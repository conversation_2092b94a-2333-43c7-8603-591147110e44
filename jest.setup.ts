import '@testing-library/jest-dom'; // Adds custom matchers for DOM assertions
import {cleanup} from '@testing-library/react'; // Cleans up the DOM after each test

// Clears mocks before each test
beforeEach(() => {
  jest.clearAllMocks();
});

afterEach(() => {
  cleanup();
});

// beforeAll(() => {
//   jest.spyOn(console, 'warn').mockImplementation(() => {});
//   jest.spyOn(console, 'error').mockImplementation(() => {});
// });

// afterAll(() => {
//   (console.warn as jest.Mock).mockRestore();
//   (console.error as jest.Mock).mockRestore();
// });


// jest.setup.js
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});
