{"lighthouseVersion": "12.2.1", "requestedUrl": "https://app.datoms.io/enterprise/1/datoms-x/customer-management/", "mainDocumentUrl": "https://app.datoms.io/accounts/login?rd=https://app.datoms.io/enterprise/1/datoms-x/customer-management/", "finalDisplayedUrl": "https://app.datoms.io/accounts/login?rd=https://app.datoms.io/enterprise/1/datoms-x/customer-management/", "finalUrl": "https://app.datoms.io/accounts/login?rd=https://app.datoms.io/enterprise/1/datoms-x/customer-management/", "fetchTime": "2024-11-04T11:12:13.733Z", "gatherMode": "navigation", "runWarnings": ["The page may not be loading as expected because your test URL (https://app.datoms.io/enterprise/1/datoms-x/customer-management/) was redirected to https://app.datoms.io/accounts/login?rd=https://app.datoms.io/enterprise/1/datoms-x/customer-management/. Try testing the second URL directly."], "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/130.0.0.0 Safari/537.36", "environment": {"networkUserAgent": "Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "hostUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/130.0.0.0 Safari/537.36", "benchmarkIndex": 3043.5, "credits": {}}, "audits": {"viewport": {"id": "viewport", "title": "Has a `<meta name=\"viewport\">` tag with `width` or `initial-scale`", "description": "A `<meta name=\"viewport\">` not only optimizes your app for mobile screen sizes, but also prevents [a 300 millisecond delay to user input](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Learn more about using the viewport meta tag](https://developer.chrome.com/docs/lighthouse/pwa/viewport/).", "score": 1, "scoreDisplayMode": "metricSavings", "warnings": [], "metricSavings": {"INP": 0}, "details": {"type": "debugdata", "viewportContent": "width=device-width, initial-scale=1.0"}, "guidanceLevel": 3}, "first-contentful-paint": {"id": "first-contentful-paint", "title": "First Contentful Paint", "description": "First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).", "score": 0.87, "scoreDisplayMode": "numeric", "numericValue": 977.632, "numericUnit": "millisecond", "displayValue": "1.0 s", "scoringOptions": {"p10": 934, "median": 1600}}, "largest-contentful-paint": {"id": "largest-contentful-paint", "title": "Largest Contentful Paint", "description": "Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": 0.84, "scoreDisplayMode": "numeric", "numericValue": 1377.676, "numericUnit": "millisecond", "displayValue": "1.4 s", "scoringOptions": {"p10": 1200, "median": 2400}}, "first-meaningful-paint": {"id": "first-meaningful-paint", "title": "First Meaningful Paint", "description": "First Meaningful Paint measures when the primary content of a page is visible. [Learn more about the First Meaningful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/).", "score": null, "scoreDisplayMode": "notApplicable"}, "speed-index": {"id": "speed-index", "title": "Speed Index", "description": "Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).", "score": 0.97, "scoreDisplayMode": "numeric", "numericValue": 971, "numericUnit": "millisecond", "displayValue": "1.0 s", "scoringOptions": {"p10": 1311, "median": 2300}}, "screenshot-thumbnails": {"id": "screenshot-thumbnails", "title": "Screenshot Thumbnails", "description": "This is what the load of your site looked like.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "filmstrip", "scale": 3000, "items": [{"timing": 375, "timestamp": 955153257866, "data": "data:image/jpeg;base64,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"}, {"timing": 750, "timestamp": 955153632866, "data": "data:image/jpeg;base64,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"}, {"timing": 1125, "timestamp": 955154007866, "data": "data:image/jpeg;base64,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"}, {"timing": 1500, "timestamp": 955154382866, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAFcAfQDASIAAhEBAxEB/8QAHAABAAEFAQEAAAAAAAAAAAAAAAECAwQFBgcI/8QAUxAAAQMCAgMLBwcKAwYGAwAAAQACAwQRBSESMZEGExQyQVFSVGFxkgcVIlOBobEWMzRCVXLRCBcjNmJkk8HC4TVzshgkJjdD8CVWY3SCg0R18f/EABsBAQADAQEBAQAAAAAAAAAAAAABAgMEBQYH/8QANxEAAgECAwQIBQQCAgMAAAAAAAECAxEEEjEUIUFRBRMVMlJxkbE0YYGh0SIzQlMjJPDxBnLB/9oADAMBAAIRAxEAPwDYqVKL5u5+oBAgUhAEUopIFkRSgKkRFICIiAIiIQSEQIhFwiIguEREIuWJOOVSqn5vKpUGqCIiEiyWREAslkRALJZEQCyIiAIiIAiIgCIiAIiILhERAEREAREQBERAEREAREQBERAEREAREQBERASFIQKRrUXKkopRSQUXS6v8JDvnomO7QLFTo07+K90Z5nC42qvkRmtqjHup0leNLJa7NGQfsm6sOaWmzgQe0JvRKaehOl2JpdipRLk7irS7FOn2KhEuNxXp9iafYqES43Fen2JvnYqES4sivfOxN87FQiXFkXN87E3zsVtEuLIub52Jvh5lbRLiyLm+HmUGQkcyoRLiyCIii5IREQBERLgWSyIpuBZLIiXAslkRLgWSyIouBZLIiXAsiIlwEREuAiIlwEREAREQBERAEREAREQBERAEREAREQBERAVKQoUhSiCUU2RSQW7KHaX1bKbpdUJLJklYbtBB5wquHVFrPcHjmcLq5dX6KNktZCx7Q5pcAQeVWT4FJpWuzEbPG7jNLTztVxui7iPaew5FdfVbmaCa5Y10R/ZOWxamq3JTNuaadj+xwstXSkuBxwxdN8beZpnNLeMCEV6bCsTo73hkLednpBYpmc11pogHcuWiVk421OqNXNpvLlksqRJG7U8tPM4fzUGVocQT7RqUWLqSZXZLKA5p1EKbqCwssptIw0HCOER6eno719bvWLdLoVkm9HYqLQNdysmKr3uimpt5jcJCDpkekO4rFDiOVTpA6xsR2YlFS1FgdRt3qC0hTYHUdqek3PMKSSmyWVWkDrGxLA8UqCSmyWUkEaxZRdALJZLpdALJZLpdALIl1KAhFKICEUogIsllKICLJZSiAiyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6kICVI1qFUFYglERCCiyiylFUsRYLJw4f7/B98LHWTh30+D74Ux1RSp3WegIiL0T5sKzPSU9QLTQxvH7TVeRCU2tDSVO5qgmuWNfETysP8lqarcnM25pp2P7Hi3vXYqCqOlF8DeOKqx0Z5zU4NX02b6d7hzs9JYRdJG6zrgjkIXqRWNUU0E7bTQxvH7TbrKWHXBnTDHv8AkjzgTc4VYkaeW3eutqtz1DJmxjoj+wVqanc3<PERSON>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*********************************************************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"}, {"timing": 1875, "timestamp": 955154757866, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAFcAfQDASIAAhEBAxEB/8QAHAABAAEFAQEAAAAAAAAAAAAAAAECAwQFBgcI/8QAUxAAAQMCAgMLBwcKAwYGAwAAAQACAwQRBSESMZEGExQyQVFSVGFxkgcVIlOBobEWMzRCVXLRCBcjNmJkk8HC4TVzshgkJjdD8CVWY3SCg0R18f/EABsBAQADAQEBAQAAAAAAAAAAAAABAgMEBQYH/8QANxEAAgECAwQIBQQCAgMAAAAAAAECAxEEEjEUIUFRBRMVMlJxkbE0YYGh0SIzQlMjJPDxBnLB/9oADAMBAAIRAxEAPwDYqVKL5u5+oBAgUhAEUopIFkRSgKkRFICIiAIiIQSEQIhFwiIguEREIuWJOOVSqn5vKpUGqCIiEiyWREAslkRALJZEQCyIiAIiIAiIgCIiAIiILhERAEREAREQBERAEREAREQBERAEREAREQBERASFIQKRrUXKkopRSQUXS6v8JDvnomO7QLFTo07+K90Z5nC42qvkRmtqjHup0leNLJa7NGQfsm6sOaWmzgQe0JvRKaehOl2JpdipRLk7irS7FOn2KhEuNxXp9iafYqES43Fen2JvnYqES4sivfOxN87FQiXFkXN87E3zsVtEuLIub52Jvh5lbRLiyLm+HmUGQkcyoRLiyCIii5IREQBERLgWSyIpuBZLIiXAslkRLgWSyIouBZLIiXAsiIlwEREuAiIlwEREAREQBERAEREAREQBERAEREAREQBERAVKQoUhSiCUU2RSQW7KHaX1bKbpdUJLJklYbtBB5wquHVFrPcHjmcLq5dX6KNktZCx7Q5pcAQeVWT4FJpWuzEbPG7jNLTztVxui7iPaew5FdfVbmaCa5Y10R/ZOWxamq3JTNuaadj+xwstXSkuBxwxdN8beZpnNLeMCEV6bCsTo73hkLednpBYpmc11pogHcuWiVk421OqNXNpvLlksqRJG7U8tPM4fzUGVocQT7RqUWLqSZXZLKA5p1EKbqCwssptIw0HCOER6eno719bvWLdLoVkm9HYqLQNdysmKr3uimpt5jcJCDpkekO4rFDiOVTpA6xsR2YlFS1FgdRt3qC0hTYHUdqek3PMKSSmyWVWkDrGxLA8UqCSmyWUkEaxZRdALJZLpdALJZLpdALIl1KAhFKICEUogIsllKICLJZSiAiyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6kICVI1qFUFYglERCCiyiylFUsRYLJw4f7/B98LHWTh30+D74Ux1RSp3WegIiL0T5sKzPSU9QLTQxvH7TVeRCU2tDSVO5qgmuWNfETysP8lqarcnM25pp2P7Hi3vXYqCqOlF8DeOKqx0Z5zU4NX02b6d7hzs9JYRdJG6zrgjkIXqRWNUU0E7bTQxvH7TbrKWHXBnTDHv8AkjzgTc4VYkaeW3eutqtz1DJmxjoj+wVqanc3<PERSON>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*********************************************************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"}, {"timing": 2250, "timestamp": 955155132866, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAFcAfQDASIAAhEBAxEB/8QAHAABAAEFAQEAAAAAAAAAAAAAAAECAwQFBgcI/8QAUxAAAQMCAgMLBwcKAwYGAwAAAQACAwQRBSESMZEGExQyQVFSVGFxkgcVIlOBobEWMzRCVXLRCBcjNmJkk8HC4TVzshgkJjdD8CVWY3SCg0R18f/EABsBAQADAQEBAQAAAAAAAAAAAAABAgMEBQYH/8QANxEAAgECAwQIBQQCAgMAAAAAAAECAxEEEjEUIUFRBRMVMlJxkbE0YYGh0SIzQlMjJPDxBnLB/9oADAMBAAIRAxEAPwDYqVKL5u5+oBAgUhAEUopIFkRSgKkRFICIiAIiIQSEQIhFwiIguEREIuWJOOVSqn5vKpUGqCIiEiyWREAslkRALJZEQCyIiAIiIAiIgCIiAIiILhERAEREAREQBERAEREAREQBERAEREAREQBERASFIQKRrUXKkopRSQUXS6v8JDvnomO7QLFTo07+K90Z5nC42qvkRmtqjHup0leNLJa7NGQfsm6sOaWmzgQe0JvRKaehOl2JpdipRLk7irS7FOn2KhEuNxXp9iafYqES43Fen2JvnYqES4sivfOxN87FQiXFkXN87E3zsVtEuLIub52Jvh5lbRLiyLm+HmUGQkcyoRLiyCIii5IREQBERLgWSyIpuBZLIiXAslkRLgWSyIouBZLIiXAsiIlwEREuAiIlwEREAREQBERAEREAREQBERAEREAREQBERAVKQoUhSiCUU2RSQW7KHaX1bKbpdUJLJklYbtBB5wquHVFrPcHjmcLq5dX6KNktZCx7Q5pcAQeVWT4FJpWuzEbPG7jNLTztVxui7iPaew5FdfVbmaCa5Y10R/ZOWxamq3JTNuaadj+xwstXSkuBxwxdN8beZpnNLeMCEV6bCsTo73hkLednpBYpmc11pogHcuWiVk421OqNXNpvLlksqRJG7U8tPM4fzUGVocQT7RqUWLqSZXZLKA5p1EKbqCwssptIw0HCOER6eno719bvWLdLoVkm9HYqLQNdysmKr3uimpt5jcJCDpkekO4rFDiOVTpA6xsR2YlFS1FgdRt3qC0hTYHUdqek3PMKSSmyWVWkDrGxLA8UqCSmyWUkEaxZRdALJZLpdALJZLpdALIl1KAhFKICEUogIsllKICLJZSiAiyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6kICVI1qFUFYglERCCiyiylFUsRYLJw4f7/B98LHWTh30+D74Ux1RSp3WegIiL0T5sKzPSU9QLTQxvH7TVeRCU2tDSVO5qgmuWNfETysP8lqarcnM25pp2P7Hi3vXYqCqOlF8DeOKqx0Z5zU4NX02b6d7hzs9JYRdJG6zrgjkIXqRWNUU0E7bTQxvH7TbrKWHXBnTDHv8AkjzgTc4VYkaeW3eutqtz1DJmxjoj+wVqanc3<PERSON>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*********************************************************+QVpEuRbmVuleRa9hzNyCozREuErDNM0RCRmmarZG92oZc5V9lOBxjfsCi5VySMUAk2AuVeZA48azQslrQ0WaAFKXKOo+BbZCxvJc9quIigo22LJZEQgWSyIgFksiIBZLIiAWSyIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIArlP88zvVtXaf55nepWpD0M9ERbnMShUIgF0uoRQSQVCkohBSiIoLGCiq0H9F2xNB/RdsWBvcpUa9ar0H9F2xNB/RdsQXMd8DXcXIqw+NzNYy5ws/Qf0XbE0H9F2xLssp2NaizX02l9RwPYFYfTStz0HEdgVrmikmWUVWg/oO2JoP6DtiFtxSiq0H9B2xNB/QdsQbilFVoP6DtiaD+g7Yg3FKKrQf0HbFWynld9RwHaEIukWkAJNgCVlspCOM1x9ivCJwFgwj2KLlXNcDEZTuPGNlebExvJc9qvaD+i7Ymg/ou2KCjlcosllXoP6LtiaD+i7YhW5RZLKvQf0XbE0H9F2xBcosllXoP6LtiaD+i7YguUWSyr0H9F2xNB/RdsQXKLJZV6D+i7Ymg/ou2ILlFkVeg/ou2JoP6LtiC5Siq0H9F2xNB/RdsQXKVCr0H9F2xNB/RdsQXKLJZV6D+i7Ymg/ou2ILlFksq9B/RdsTQf0XbEFyiyWVeg/ou2JoP6LtiC5RZLKvQf0XbE0H9F2xBcosllXoP6LtiaD+i7YguUoqtB/RdsTQf0XbEFylFVoP6LtiaD+i7YguUoqtB/RdsTQf0XbEFylXKf55neqdB/RdsVynY/fmei7XzKVqRJ7jORToO6J2JoO6J2Lc5rohQqtB3ROxNB3ROxBcosoVeg7ou2KNB3ROxCblJUKssd0TsUaDuidigXKUU6DuidiID2ngVL1eHwBOBUvV4fAFGKQy1OG1UNNJvU0kbmsf0XEZFc7JhmMNkHAHCjp9H5ls2lnZ18zyn0bHksvr8q5HweZ8zo+BUvV4fAE4FS9Xh8AXInBN0mjOW4mAXlzmZ+ky7QBnqJ1Z/it1udpMVpZJ/OlU6oYQNAucLj2BMq5DMzacCperw+AJwKl6vD4AshEyrkMzMfgVL1eHwBOBUvV4fAFkImVchmZjcApOrQ+AJwCk6tD4AslEyoZnzMbgFJ1aHwBOAUnVofAFkomVDM+ZjcApOrQ+AJwCk6tD4AslEyoZnzMbgFJ1aHwBTwKl6vD4AshEyrkMz5mPwKl6vD4AnAqXq8PgCyETKuQzMx+BUvV4fAE4FS9Xh8AWQiZVyGZmPwKl6vD4AnAqXq8PgCyETKuQzMx+BUvV4fAE4FS9Xh8AWQiZVyGZmPwKl6vD4AnAqXq8PgCyETKuQzMx+BUvV4fAE4FS9Xh8AWQiZVyGZmPwKl6vD4AnAqXq8PgCyETKuQzMx+BUvV4fAE4FS9Xh8AWQiZVyGZmPwKl6vD4AnAqXq8PgCyETKuQzMx+BUvV4fAE4FS9Xh8AWQiZVyGZmPwKl6vD4AnAqXq8PgCyETKuQzMx+BUvV4fAE4FS9Xh8AWQiZVyGZmPwKl6vD4AnAqXq8PgCyETKuQzMx+BUvV4fAE4FS9Xh8AWQiZVyGZmPwKl6vD4AnAqXq8PgCyETKuQzMx+BUvV4fAE4FS9Xh8AWQiZVyGZmPwKl6vD4AnAqXq8PgCyETKuQzMx+BUvV4fAE4FS9Xh8AWQiZVyGZmPwKl6vD4Ap4FS9Xh8AV9EyrkMzLPBKf1EXhCcEp/UReEK8iWQuyzwSn9RF4QnBKf1EXhCvIlkLss8Ep/UReEJwSn9RF4QryJZC7LHBKb1EXhC1dVU0VNOyKWOBr3mzAWD0swMtoW7WjxHBTW1tLUueWupn6cYFsicifa249qZULs2UNLTvja408Vz+wEV+FpZE1p1hEshdlaKieMTQSROJAe0tJHatdDhk8c0cjq17yHAuBGThrttLvYbcikg2iLVTUVTUaZiry0ZgFovbM9uv8FM1DPIGNZWkFrC0jPXz60BtEWp82VRkBdXP0B9UAj+aeapgZSyreC8AZ3ysNetAbYG4uNSXC1hw6YQxMZUn0IwzO+ZBuTkeVWzhdQSwurZPQcHXzudeRz7fcgNul1qqWjlgqYnvrt8aXOIafrAg2Az7NgUeapWMIiqntJ15mxyaAdesWv7UBtkWuOHyhkTWVLm6L3POvO79Ln5slMNI9rpozVOdchwF82hAbBFp5cOqxG3RrSCHXLjcWG1Vx4fMYrCue55dpl3ZrtrQG1BBJAOYRaWWg3tw32tbHI+UPbckXs21sznrV+egqZId7bVuYdAt023uCb5jPkvyqSDZotSzDZSCRWPLHcxOrM8/d7BZVtw6cTxP4U4NY8uc0X9Li9vYdpUEmzRauWkq5gd8qRDZ50DGTfRyt7TY371VUUFRLCxprpIyNLTLMg4E39lhkCgNlcZ56kBBAINwVqqXDCymY0VT3gxaGkCbOGiRfX23ViDDpKSmhpnV4Dt70GXJGpvJn2XUIlm8JABJNgOUoDcXGpYUVI7e3B82m1zQNIXv8VZgw6ZmmX1ksl7aFybCwshBs0WvfQzOic1lRoONvSbfLLWM1hYBRviGl524fGAGF2lpG+g0HO/KQXe1TYG9QEOAIIIOYIWt83zvpJop6pz5HhwDxcBuk2xyvzkkc2pYkGFy0WGRUjsScwt3lsbz6JOha4156VrHsQG9Rc+cEqnU8MAxWYSQsLTJc6b7tsHON9dxf2LZxU0zqcsmcA4PJBDibj8fcoe7QkzUWv4DNoSsFXIA5mi063NN73udiwsUwuvqHUZpa0xCKVplaC4B7LG4135lK3g3qLBqKOWR+k2a3oBtjpa768isLD8HqaUxmSufM5sbI3F9/SIJJdr1m6Mhb9TdOc1jS5xDQOUmyMc17dJjg4c4N1Yipi2N8b3acbtQdmb8ua09HgFRS1ZkixGTeDJJLvFvRu+Z0hPPqcG9wKkhHQEgAkkADlKDPUtG7BaiWmrYZ697xUQuiGVgy4AyF+S3vKu1+GVdU4GPEJKci4G9jk0geXlsCPallzF2bdFp5MJndXQ1DKxzAxhY6MX0XEnXrvksiSgkc9xbUvDXMc0g3OZ5VBJsEJAtcgXyC19PQSQsibwhz9EAOLic7C1xmsXEMIqax1O41pjMLw8FoPICOflvmhPE3RIFrnWi1c+FvmFOXVLxJEOMCcyARf3+5VU1BUw029mtdI/T0i9wNyNG3Pr5e9CDZItXHhs4cwSVj3MAAIzz9K5zvyjIqajD6mSvdOytcyIkERWy7eX/u5QGzRakYZU6BDqwk3B1HZr1K7Bh80VO5hq5XyEN9NxOscqA2KAgi4NwtQMKnAkBrHm5Bbe+VvbyqimwaaGGKN1W5wY7S5c9eWvVmgN0oBBJAIJGscy1lPhtRFIHurHvIIIaSbauXPlVyagkfVGVlQ9jXa2gnmtln/wB2UA2CLW1GHzyVLpI6x8bCAA0clrfG3vKtswqezxJWym7iRouIt6IHPzi/eVINtcXtyqNJulo6Q0tdr5rTyYVVSS75w1zHAusBe1i6/P2DVzBXK3DZpavhUUoErY9FozAvY9vagNqi1r6KpkawuqNF9vSAva9+TPUrcuFSvIdwyQOseU8vt5EBtkWr821A0mitk0DfI3uDbvVDcOqRVOBqHOp3Rubm43BIsMuzNAbdFr4aKeJstqkkuto3ubWN+f2KiHD6lk8T31rnMa4kttxtXLfs96A2dxzotdDhgYZS6aQ6cjn5OIsCdWtEBnVEQngkicSA9paSNYutR5hZokCd/wA4Hj0G6rg21d/Yt0iA1TMEhYwNEspAvxg06+8KluBQNFhLKMyTxc7+xbdEBrZsKG8b3TTOiu65Nge9TDhUcUwkbNL93IA6+Ydq2KIDUjA4QXnfpPTtcWbYW5sv/wCqG4HE2cSCaQguLiCAbkkHLLIZLbogNXLglPLHCxzngxRiNrha4s1zQdWv0ye+yNwaJtG2mE0+g1xcXaQubtLSCbcxW0RAa2nweKAktlmJMZjzI1X7u1VswuFkcjQ513gAusOQk6rW5dSz0QGrkwWF8bmGWSztdg2+2yoZgNO1zSJpzoiwu4HktzLbolga+XCopYIopZZnCMWab2PJzdyx5MBgexjDNMAx2lf0bnO+u2rsW4RAamowKnnbGHSzNMcYiaWkAgAOF9Wv0r94CqGCQClMBklIJHpEjSFgOW2d7Z3W0RAYFVhkVRK+Rz3AuABFmnUQeUdit02Dx08RjjqJy02DtIhxcM8iSLnWtmiA1JwOAwwxiaYNijZGMwcmauTl5edW6nc9T1EkL3zTh0V9HRIGtujnln7VukROwe81GHYFBQNjbDPUODMrvcCTlbM2Wa+hjdHIwEt083EAG558xYrKRBY1lNg0VPWNqRNM97A8NDyCBpG//fetPSbm6+jEnBq2jY58YjL+DPLsgQHX3zXYjwjmXVotI1ZRvZ6kWRzLcDxVroyMQoiGOY4aVGXE6IIzcXknXrJurseDYjDSxQR19O9jALmaBxLiAG56L2i1gMrc66FFLrTf/SIyrU5al3P4nTxSRjEaItkc1zr0XNbUNOw1cy6CliqY3u3+obKwlxA0LEZ5C9+QX5FkoonVlPveyLBERZgIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiIAiIgCIiAIiID//2Q=="}, {"timing": 2625, "timestamp": 955155507866, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAFcAfQDASIAAhEBAxEB/8QAHAABAAEFAQEAAAAAAAAAAAAAAAECAwQFBgcI/8QAUxAAAQMCAgMLBwcKAwYGAwAAAQACAwQRBSESMZEGExQyQVFSVGFxkgcVIlOBobEWMzRCVXLRCBcjNmJkk8HC4TVzshgkJjdD8CVWY3SCg0R18f/EABsBAQADAQEBAQAAAAAAAAAAAAABAgMEBQYH/8QANxEAAgECAwQIBQQCAgMAAAAAAAECAxEEEjEUIUFRBRMVMlJxkbE0YYGh0SIzQlMjJPDxBnLB/9oADAMBAAIRAxEAPwDYqVKL5u5+oBAgUhAEUopIFkRSgKkRFICIiAIiIQSEQIhFwiIguEREIuWJOOVSqn5vKpUGqCIiEiyWREAslkRALJZEQCyIiAIiIAiIgCIiAIiILhERAEREAREQBERAEREAREQBERAEREAREQBERASFIQKRrUXKkopRSQUXS6v8JDvnomO7QLFTo07+K90Z5nC42qvkRmtqjHup0leNLJa7NGQfsm6sOaWmzgQe0JvRKaehOl2JpdipRLk7irS7FOn2KhEuNxXp9iafYqES43Fen2JvnYqES4sivfOxN87FQiXFkXN87E3zsVtEuLIub52Jvh5lbRLiyLm+HmUGQkcyoRLiyCIii5IREQBERLgWSyIpuBZLIiXAslkRLgWSyIouBZLIiXAsiIlwEREuAiIlwEREAREQBERAEREAREQBERAEREAREQBERAVKQoUhSiCUU2RSQW7KHaX1bKbpdUJLJklYbtBB5wquHVFrPcHjmcLq5dX6KNktZCx7Q5pcAQeVWT4FJpWuzEbPG7jNLTztVxui7iPaew5FdfVbmaCa5Y10R/ZOWxamq3JTNuaadj+xwstXSkuBxwxdN8beZpnNLeMCEV6bCsTo73hkLednpBYpmc11pogHcuWiVk421OqNXNpvLlksqRJG7U8tPM4fzUGVocQT7RqUWLqSZXZLKA5p1EKbqCwssptIw0HCOER6eno719bvWLdLoVkm9HYqLQNdysmKr3uimpt5jcJCDpkekO4rFDiOVTpA6xsR2YlFS1FgdRt3qC0hTYHUdqek3PMKSSmyWVWkDrGxLA8UqCSmyWUkEaxZRdALJZLpdALJZLpdALIl1KAhFKICEUogIsllKICLJZSiAiyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6kICVI1qFUFYglERCCiyiylFUsRYLJw4f7/B98LHWTh30+D74Ux1RSp3WegIiL0T5sKzPSU9QLTQxvH7TVeRCU2tDSVO5qgmuWNfETysP8lqarcnM25pp2P7Hi3vXYqCqOlF8DeOKqx0Z5zU4NX02b6d7hzs9JYRdJG6zrgjkIXqRWNUU0E7bTQxvH7TbrKWHXBnTDHv8AkjzgTc4VYkaeW3eutqtz1DJmxjoj+wVqanc3<PERSON>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*********************************************************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"}, {"timing": 3000, "timestamp": 955155882866, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAFcAfQDASIAAhEBAxEB/8QAHAABAAEFAQEAAAAAAAAAAAAAAAECAwQFBgcI/8QAUxAAAQMCAgMLBwcKAwYGAwAAAQACAwQRBSESMZEGExQyQVFSVGFxkgcVIlOBobEWMzRCVXLRCBcjNmJkk8HC4TVzshgkJjdD8CVWY3SCg0R18f/EABsBAQADAQEBAQAAAAAAAAAAAAABAgMEBQYH/8QANxEAAgECAwQIBQQCAgMAAAAAAAECAxEEEjEUIUFRBRMVMlJxkbE0YYGh0SIzQlMjJPDxBnLB/9oADAMBAAIRAxEAPwDYqVKL5u5+oBAgUhAEUopIFkRSgKkRFICIiAIiIQSEQIhFwiIguEREIuWJOOVSqn5vKpUGqCIiEiyWREAslkRALJZEQCyIiAIiIAiIgCIiAIiILhERAEREAREQBERAEREAREQBERAEREAREQBERASFIQKRrUXKkopRSQUXS6v8JDvnomO7QLFTo07+K90Z5nC42qvkRmtqjHup0leNLJa7NGQfsm6sOaWmzgQe0JvRKaehOl2JpdipRLk7irS7FOn2KhEuNxXp9iafYqES43Fen2JvnYqES4sivfOxN87FQiXFkXN87E3zsVtEuLIub52Jvh5lbRLiyLm+HmUGQkcyoRLiyCIii5IREQBERLgWSyIpuBZLIiXAslkRLgWSyIouBZLIiXAsiIlwEREuAiIlwEREAREQBERAEREAREQBERAEREAREQBERAVKQoUhSiCUU2RSQW7KHaX1bKbpdUJLJklYbtBB5wquHVFrPcHjmcLq5dX6KNktZCx7Q5pcAQeVWT4FJpWuzEbPG7jNLTztVxui7iPaew5FdfVbmaCa5Y10R/ZOWxamq3JTNuaadj+xwstXSkuBxwxdN8beZpnNLeMCEV6bCsTo73hkLednpBYpmc11pogHcuWiVk421OqNXNpvLlksqRJG7U8tPM4fzUGVocQT7RqUWLqSZXZLKA5p1EKbqCwssptIw0HCOER6eno719bvWLdLoVkm9HYqLQNdysmKr3uimpt5jcJCDpkekO4rFDiOVTpA6xsR2YlFS1FgdRt3qC0hTYHUdqek3PMKSSmyWVWkDrGxLA8UqCSmyWUkEaxZRdALJZLpdALJZLpdALIl1KAhFKICEUogIsllKICLJZSiAiyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6kICVI1qFUFYglERCCiyiylFUsRYLJw4f7/B98LHWTh30+D74Ux1RSp3WegIiL0T5sKzPSU9QLTQxvH7TVeRCU2tDSVO5qgmuWNfETysP8lqarcnM25pp2P7Hi3vXYqCqOlF8DeOKqx0Z5zU4NX02b6d7hzs9JYRdJG6zrgjkIXqRWNUU0E7bTQxvH7TbrKWHXBnTDHv8AkjzgTc4VYkaeW3eutqtz1DJmxjoj+wVqanc3<PERSON>25gna7seLLF0ZI64YuEjVjPUtnS0sU1M0uFnc4WvkwurhPpMPe3NbXDWSNpm6TuU5EKIx37y9SpeN4ssSYaf+m8HsKxn088Wtrrc4zW8RWcEZqvJanPXB4zdiaIOp21b2SCKTjsB7ViyYcw/NuLew5qrgzWNeL13Gt9NuvMbQo9E6xbuWU+jnjzA0h+yVjuu02e2x7RYqtrGqknoUlh5M+5U2VdgdRt3qo6VsxpDn1qLE3LKK5Zp1G3eoLSBe2XOlhcoRVIlhcpRTZLKCSEU2SyAhFNksgIRSpU2IuUoqkSwuQllIUoSU2KWVSICmyWVSICmyWVSICmyWVSICmxSxVSICmym3YpRALJZEQCyiwUogIsEsFKICLBTZEQEhSECka1JBKKUQgtoiKhIWTh30+D74WMsnDfp8H3wpjqis+6zv0U2Sy9M+bIRTZLICEKkqEBSVQ7UqyFQ7UoLItP1LHkWQ7UrEiqzWBhzcqqjaDTC4B1qJtaqi+jD2rPibvQx5mNbGHAZlxHwVlZE/zLfvH4BWFR6mkHdEIiKC4UOY14s5oI7QpRAYslDC7igtPYVjPoJG5xuDvcVs1ebMNENkiY4DlGR2qMiZdVpx+ZzskcjPnIz3kfzVIt9Ulq6XRgfqe5h5ni42hWJcNbICWsa8c8ZVXTfAusSv5KxoiL62g9rVTog6iPbkthLhxB/RvIPM5WDDNE7SfC2UZ67ke7NZtNG6qRejMUtINjkosrrZXNBDbWIIsQDrVtRcvvIsllKKLkkWSylEAslgiIBYJYIiAWREQBERAEREAREQBERAEREAREQBERAEREAREQBEUoCVI1qApCm5BKKQikgt2SymytSMdrYT3KhJcssnDvp8H3wtYJXjl2rJw+ocK2AkA+mFaOqKTf6WekosJte36zD7FcbWxHWSPYvSufO2MlFaFREfrhXA9p1OG1ASVClCgKXK27UrhVsoSi09WJFkOWPIqM1iYkqri+jj2qiblVcP0ce1Z8ToeiLbmb4I2Xtd5+AWbBTxw5tF3EEXPMRYrFbx4f8w/ALYK0TObdrGrq6UxHSZmz4LGst4RcWIuCtfVUjmHSjBLTyDWFWUeKNKdW+6Rh2SyrDHHU07FWKaXQc4tsGi5vkqWNnJIsqFUiElKkEg3BIPOFKIC4KiS1n2kH7Yv71JdA8HSY6M87TcbFaUHUVN2Vyrgafgwf8zLG/svY7CrUkUkZs9jm94VKusnljFmvOjzHMbFy3R6H6kWUWTv0b/noBfnZ6JTeoX/NzaJ5pBb3pbkM3NGMivvppWC+jpN525hWbKN6LJp6EWSylEJIsur3A7mocfqKh1XI5sEAF2sNi4nV7Mlyq9K8jvExPvj/AKl04SKnVUZaHndK1Z0sLOcHZ7vc3H5vMF/ePGPwT83mC/vHjH4LsEXt7PS8KPje0MV/Y/U4/wDN5gv7x4x+Cfm8wX948Y/BdgqXyMj472t7zZNnpeFDb8V/Y/VnI/m8wX948Y/BPzeYL+8eMfguvY5rxdpBHOEc4NF3EAc5TZ6XhQ2/Ff2P1ZyH5vMF/ePGPwT83mC/vHjH4Lr3ODRdxAHaqd9j6bdqbPS8KG34r+x+rOS/N5gv7x4x+Cfm8wX948Y/Bde57WN0nOAbzkqGPbI3SY4OHODdNnpeFDb8V/Y/VnI/m8wX948Y/BPzeYL+8eMfguvDgSQCLjkRzg3jEDvTZ6XhQ2/Ff2P1ZyH5vMF/ePGPwT83mC/vHjH4Lr3vaxuk9wa3nJsqWSxvaXMe1zRyg3TZ6XhQ2/Ff2P1ZyX5vMF/ePGPwT83mC/vHjH4LsAQRcG4RNnpeFDtDFf2P1Zx/5vMF/ePGPwUHyeYLbXUeMfguxRNnpeFDtDFf2P1PAN02FtwjGqijY8vYwgtcddjzrV2XT+UUf8WVXc34LmbLwKyUakkuZ91g5upQhOWrSIsllNkssjpJQa1FkU3ILiK2iXIsSiIqklqWIPzGRVFG0trYQR9cLIV6ijbJWwBw+uFaL3opPus6RFsjQMOpzgqHUHRftC9Kx89mRgJms00D+R4KoNFN+ztUWJujHEjxqc7aqxUzD65PeqjSzD6hVt0MjTmx2xAXRWyjXYqeHH6zNixixw1tI9iocobJSRl8MY45ghVPIIBGYWucojndF2t5lVs1ijImVcP0ce1WTI2Rt2lX4vo49qotTV6FDePD98/ALYrXt40X+YfgFsFaJlNaBS0lpBaSCNRChFa5SwUEAgg5gqUQWNPUxGKUj6usK0ttWRb7EbcYZhatrC92i0ElZSVmdcJ3W8pRZc1JvdO12Zff0uYBYtlD3FoyUtCFB1Kqyg6ioLGgREXKeibSGEzNa1rQ425VTNQ6N9KNzPYrlOAWtu0nLkWS2VzcmzPb2OzC6oxi1vOCc5xl+n/n/PI1W8PZfe5CPbZWXRPbrbsW7fN6OlJHDIDlcZFWyaV/JJGezMKrpLgy8cRLjE0hFtaLcOpGSfNzROvnZ2RWNLQStF96dbnbmPcqOjNcDSOJg9zZgL0ryPcTE++P+pedOhI7+1ejeSBpazE788f9S2wX78f+cDi6ZaeDn9PdHoyIi+gPhguZ3TYRHimLUQmlkbGAQWsNrrplzW6WfEabE6OaiojUwtBLw3XtXLjFF0v1q6uvc6cI5KpeDs7P5cDUVED9yu6PDxTTyuoat29uje7SsdWW0LK8oM0tU6kwmkc4TSh8zg3XZrSQPaR7lEFFiOO4/TV2I0ppKOlzZG43Lnc//fMqX4FW4vukr62plnoWR2jgcy13N5fhf2rz8k3TlCnF5ZPctN1t+uifA9FTgqkalSSzRjveu++7TVq6v5GfhTId1G5aj4XJKHNykMb9E6bbg3+K5zc/gNPW4/i9JNPVb1SvaI7Sm+d9exbzcZhtZguIYlQyxvdRucJIZjaxPL7bW2FXdzVBVU26THJ54XMhme0xuOp2tbKm6qpOpHfo/omU61UeujSn+mya382vvbU1WKwuxvdhHgz5pY6ClhDnNa6xebcp2Kmihdua3a09DTTSuoaqP5t7r2PYs3H6DEMO3TRY3hlOapjmb3NE02Pf/wB8yowujxHGN1MeL4hSGkpoGaMcbzdxPOpyPPo82bX5fixoqi6rvLJl0uu95a3vx5Gjr8VqcK3d1lU1z3UsbmtmZfINOV1O7jG5MQxOnjopXCjp5Yw57TbTc4jLYt7T4HLU7qcbNZTuFFVQhjXnUe5YeO7lpKPBMPo8MifUPZUiSV+VznrKOnVyyto2/c2p18MqlNvvJJfLTV/Nb1/0UeUUytqcLfVCoOEAfpxCSDft9y2u5bDsGlw6q8yVUskc7Q1wc8ks9nIszHsSrKExRRYRJX07memWkZHmsVqdwuFVcGL4jiE1HwCmnsI6e/vXRlXW6Xv8tN3M5VNvCWby203rfv4rW/z5HXYdTcDpI4NIu0Ra5WSiLqSsrI8ltyd2ERFJB4n5Rf1squ5vwXNLpfKL+tlV3N+C5pfNYj92XmfoeA+Gp+S9giIsTrCIiAIiIAilFUELJw76fB98LHWRh306D74Vo6opPus79EReofNhERAFBUqCgRS4A61acxpyLRsV1yoKgujGkgiP1G7FjS0sR+rbuWc/WrEiozWJrX07WOuwuae9ZMP0ce1USq5D9HHtWa1N33ShvHh/zD/JbBYDePD/AJh/ks9XiZVOAREVjMIiIAo0Wg3axreewspRACARY6lrKun3p128Q6uxbNUvaHtLXC4KrJXLwm4s01lBGRV6eIwvsdXIedWjqKyZ1p33o55ERcZ6Rtqewa0kkZawsjSuMpGm/I8LFj+bb3K4wtHGaXe1dUZWVjhnG7uS5zNIh8bcuVpVJER1Oc09ouqxA5xG9guadRVgqHLmiVFcGUvABIBuOdQyR8Zux7m25ipKoOpUvbejS11ZkucZJQXkuJIuSvQPJMbtxLIDNmr2rz5nHb3r0HyS8XEu9n9S6sG71l9fY8/pZWwk15e6PQkRF7p8WEREAREQGDjbK1+GyDDC0VWkwgOdo6TQ8FwvY2JbpC/Jdc8/Dt1D4p2jEY2SN096eM9P0X6BItlYll+fRJ5bLr0QHIVVDuqdNCaeugjYWl0gcb2fkcss26229qtyYTujkrIZ21gY2zNJjpdLRycHWy15tz7F2aIDjcNbuofW18dQdCJsWjDI9ws59xmAOYaWZ1qupoN07N5FLWRvFyJNN+YzyINuQXy5V16IDjX4Vukms6SsY12gWEb5cEnSBNrfdNu9Q3Dt1bW07G18TRYb6QR6J0W6rjMXDhbtuuzRAYOCMrI8Mhbibmuq7HT0XaQGeQvy5LOREAREQHiflF/Wuq7m/Bc0um8ov611Xc34LmrL5jEfuy82foeA+Gp+S9iEU2RYnWQilEBCKUQBFNilihBCycO+nQffCx7FZGHA8Og++FMdUUn3Wd8iIvWPnAiIgCgqVSeVQwQ5UlVFUlQWRaerEmpZD1jyalRm0TDlVyH6OPaqJVXF9HCzWps+6Q3jw/5h/ks+ywG8eH/MP8lsFeJnU4EWSymyWVjIiyWU2SyApRSiEkIpRAW5ohLGWn2HmWqnifEdGRpFxcXGsc4W5WtxL54fd/FZzW65tRk72OVslkRcB7RtI/m29yu6dgdFrW5c1yrcfzbe4KordHI1vJEjg4uvcnXfNTICLaV97eLtJCt3Us9Ib2Ta+YJOopci1t5TvTyHejbRF88tnOrbWOcCQMtVzkNqu74WOBFnSA30zmfwVEhdLdxJNhn2KNxZNlpnzje9eg+SXi4l3s/qXn7PnG969A8k3FxLvZ/UunBfvR+vscPS/wAJP6e56EiIvePigiIgCIiAK1BUwVD52QTMkfA/e5Q11yx1gdE8xsQbcxHOrq4KkxKqpK/dXDRUFea6sxEGkdJQzCF1qeCPTMmjohocx2d87ZawgO9VqaphhlhilkaySYlsbSc3EC5A9gXmeIVWKUmGQ1MUuLwQmlihrGVDZXSPqTKwWZYF1yDIC6IHW2wOSljX8IhqqN+JvfUVc++thiqWyRRmIiNkt2h+TswXgW0rDIID1BFwm4w1kOJ4e2UYxvM+G6U3DBO4CYOGsycV1r5ZXXdoAiIgCIiAIiIDxXyifrXVdzfguZXTeUQf8V1Xc34LmrFfMYj92XmfoWB+Gp+S9iEU2KWKxOshFNiligIRTYogJREUC4Vynk3mdklr6JvbnVtFOhD3qzOiG6U8tKPH/ZVN3Ss+tTOHc665tFrtFTmczwdHwnUDdJT8sEvst+Kn5SU3qZvd+K5ZFO0zK7FS5HVN3R0pOcUw9g/FXBj9EdZkH/xXIop2mZGw0vmdh57ofWu8BUeeqH1p8BXIIm0zI2Gn8zrTi1G7VNtBVLq+lcMp2e0rlEUbRInY4LRnRy1VOdU0fiCvQSxupxovadeorlll09ZvMQZoXty3SNbfvEsNu3G7c9vFcC5t7ixsglYNQlH/ANn9lqfOI9Wdqnzi3oHardainUS5G0Mx5HSj/wCalsvPLL8f5rV+cWdByecI+g5OsXMdRLkbgVFv+tL4B+Kg1L+SV/taFqfOEfQennCPoPU9auZGzvl7G3bUyetb7Wq4Kl1vnofC78FpPOEfQennCPoPU9auZDwzfD2N0at4PHiPsKltW++qM+2y0nnCPoP9yecI+i9OtXMbM+RvuEu6MX8ULDrHmSS5DR6NsnXWu4fFzOTh8VtTtiOqmI4dxd7GrREXIelc2sABjF3Bvo8vKr7adzm3EkI75Gj+a1LaiQAAEWCnhUn7Oxaqa4nNKnLgzOLCHWBBPYbq46kqGt0jBIBz6JWt4VJzN2KOEv5gmeI6ufAyi11yLG6N043B7QQRnmMljsq5GG7LtPO02Vb6+d7dF8kjm8xeSozIlwlpYlvzje8L0HyTAhuJXFs2f1LzczE8nvXo3kjdpMxLLlZ/UurAv/PH6+x5/S6awk/p7o9DRQ++g7RtpWyvzrDp5K8gCoghBtmWSXzv3BfQHxRmotdXT4jFC91JSRzyWOix0gbc8gv2qud2IaVK6FkIabb+1xuRlnb22UN2JsZyLEElZpgby3Rte+kMjdZMJeYmGUNbJb0g03APYidw1YqREUkGPiFFT4hTOp6uPfIiQ61y0gg3BBGYIOYIzCUNFBQw73Th9uV0kjpHHvc4kn2lZCIAiIgCIiAIiIAiIgPFfKJ+tVV3N+C5tdL5RP1qqu5vwXNL5fEfuy82foWBf+tT8l7BERYnXcIiILhERBclEWyosM4TAJN80b8llMYuTsjOc1BXka1FuvMzeWU7FPmaP1rtiv1UzLaafM0iLd+Zo/Wu2J5mj9a/YE6qY2mnzNIi3fmaP1z9gTzNH65+wJ1UxtNPmaRFu/M0frn7AnmaP1z9gTqpjaafM0iLd+Zo/Wv2BPM0frX7E6qY2mnzNIi3gwaL1j1IweHle9OqkNppmiRb7zRB0n7UOEQBpN36udOpkRtVM0Ku00BneWtIBAvmrbhZxCzMK+kO+4fiFSKu7G03aLaJ82v6bPenm1/TZ71s1K1yROXrpGr82v6bPenm1/TZ71tNEqFORDr5Gs82v6bPenm1/TZ71s0UZIjrpGs82v6bPenm1/TZ71s0TJEddI1nm1/Tb7082v6bfetminIh10jWebX9NvvTza/pt962aJkQ66RrPNr+m33rBc3RcQeQ2XQrQS/Ov7ys5pLQ2pTcr3KQLmwuSqt6f0HbFfw76dD95dTUSRQROlnc1sbdbjyK9Kk6u5alK+I6l6HH70/oO2JvT+g7YugOK4Wf/wAuIdxKp854b12NdXZtfwv0ZzdpQ+XqaHe39B2xej+SJpazE9IEZx6x95aKnEVTC2WCUPjdqcOVdpuBj0G1md7lv81fCUHTrq+qucXSeK63CySWtvdHSYlUGkoZp2gOLG3AKo3ms64z+D/dUY+CcGqwMzofzWevbPkzD3is64z+D/dN4rOuM/g/3WYiAw94rOuM/g/3TeKzrjP4P91mIgMPeKzrjP4P903is64z+D/dZiIDD3is64z+D/dN4rOuM/g/3WYiAw94rOuM/g/3TeKzrjP4P91mIgMPeKzrjP4P903is64z+D/dZiIDD3is64z+D/dN4rOuM/g/3WYiAw95rBmath/+n+6yonacTHHLSAKqdxT3K3TfR4vuD4IDxryh/rVVdzfgubXSeUP9aqrub8Fza+WxH7svNn6DgfhqfkvYIiLE6giIgCIiAmy6LB/oDO8rnl0WDH/cGd5WtF/qObFdwzb86W5s1N0uuq55xSiquEJslwUoqrpdLgpRVC51AlVaD+g/Ym8XLaK7vcnq3+FVCnmOqNyneRmRYRZPBZvVn3IaaYAkssB2pZ8iM0eZjgbFS/iu7lXpKl59B3cVFyxyLh6R71l4UP8AeHfd/mFiO4x71mYV9Id93+YXDF/qPWqdxm0sqmC7296hV04vPGP2gupanC9C+6PIrEstw+KzHdy1C0qxy2M6csxFkspQZkLI0Islll7ysVS42ITTIsllKKCSLJZSiAiy0Evzj+8roFz8nzj+8rKrwOjD8S9h306H7y3W6b/AqvuH+oLTYd9Oh+8Fud03+BVfcP8AUF3dFfvw/wDZe6OTpPuvyZ5yrEk+ibNAJ7VfVl9O17rlfpWJhUqU3GlLLLnyPlcPUhTqKdSOaK1Wlze4TukrqSijp4KIStZezg1xvc35F6p5IcVqsTZiZq6beN7Mej6JF76XOuQ3I0zBgVObC93f6ivSNwbAxtZYay3+a+CyShi5KcrtN33antY7EU6mEahSUU7cW+KOrc0OaQ4Ag6wVjHD6Um+9DaVlKmWVkTdKV7WDncbLvPmTH830vqhtKeb6X1Q2lV8NpesQ+MJw2m6xF4wouicrKPN9L6obSnm+l9UNpVfDabrEXjCcMpusReMJdCzKPN9L6obSnm+l9UNpVfDKbrEXjCcMpusReMJdCzKPN9L6obSnm+l9UNpVfDKbrEXjCcMpusReMJdCzKPN9L6obSnm+l9UNpVfDKbrEXjCcMpvXxeMJdCzKPN9L6obSnm+l9UNpVfDKb18XiCcLpvXxeIJdCzKPN9L6obSnm+l9UNpVzhdP6+LxBOF0/r4vEEuhZlDaCma4ERC4z1lZOrUjSHAFpBB1EIpIPF/KGP+Kqrub8Fzdl0vlD/Wqq7m/Bc2vlcS/wDLLzZ+g4H4an5L2IsllKLG51EWSylEuCLIpRLgldFg30FveVzy6HBvoLe8rWh3jlxXcM2yWUouo88g5BWaWoDq+KIgEF1ledxStfR/4zB95XgrszqyaW46wRRjUxo9iq0RzDYpRdByXFhzBERCAiIgCon+Zf3FVqib5l/cUehK1NHZQ8eg7uVSpfxHdy5DvOSdxj3rLwv6Q77v8wsU8Y96y8L+kO+6fiFwwf6j1qndZs1doxeqiH7QVtX6AXrYB+2PiuuPeR583aLN7LF+if3Fc20XcAuznhtBJl9UrjoheRg7QurFKzRy4SeZMyDBYHJYzPnG9630lOAxxtyFaOIfpmD9ofFZVYZWjalUzJm53jLUtEdZXZCn9H2Lj3cY960xMctjPCzzXKUWzFHlqWteLSOHMSFhKDjqdEZqWhUInEalQRY2XQQ0elCw21tBWimGjNIOZxHvVqlPIkylOqptooWhk+cd3lb5aGT5x3eVyVXoduH1Zew/6bD94LfY7TyVWFVEMDQ6R4AA1coWhoS1tXE5xAAdmSV0T8RpGGxnaT+yCVvg63UyVRapp+hz42m6jypcDkI9yle4jSfA0cvpEke5Zke443G+VYI5Q1n91u341Ttvoskce4ALHkxxxH6OAA87nXXs1P8AyPEvSaXkjzodErw/c2WG0TKCjZTxuc5rb2LteZuu13D8Wr72/wA15hJjFU7iljO5v4ruvJXUS1AxIzPLiCy3ZrXBhsT12Iu97dx0hhZUsLJvRW90dtiNU2ioJ6l/FiYXn2L5+xbFqzFa2Spq5nuc85AGwaOQAL6FqImzwSRSC7HtLT3FeMY3uFxehq5BSU7qqmv6EjCL25iNd11Y+NSSWXQw6BrUKcpda0pcG/ucsHv6btqkPf03bVtxuWxv7NqPCp+S+N/ZtR4V5bp1PC/Q+m2vD+OPqjUh7+m7aqg9/TdtW2G5jGvs2o8Kn5MY19m1HhVXSqeF+hG14fxr1RqQ9/TdtUh7+m7atsNzGNfZtR4VPyZxr7OqPCq9VU8L9Cu1Yfxr1Rgx01W9jnAODW2uS62vVrKiSOeIEvJFjokaYuD3XW0+T+PFgaaGqLRqBGpHbnMbcSX0FSSczca1EqU+EWZ7VSvvnH1X5NQHv6btqq039J21bX5NYx9nVHhUjc1jH2dP4Vm6NXwv0ZbaqHjXqjVB7+m7aqg9/TdtW0G5vGPs+fwqfk5jH2fP4VV0avhfoyu1UPGvVGr039J21VB7+k7atn8nMX+z59iqG5zF/s+fwqjo1fC/RkbVQ8a9UZ24rGamgxeCEyPfTzvDHMJuLnIFeurzrchuRq48QirMTjEMcR0mMJBc53Jq1Beir6DoyFWFK1T6HyvS9SjOvely325njPlC/Wmq7m/Bc3ZdJ5Qf1pqu5vwXOLxsS/8ANLzZ9XgfhqfkvYiyWUosbnURZLKUS4IRSiXBNl0GDfQW95XProMH+gt7ytaHeObFdwzkRF1nnEO1Fa+kH/jEH3lsDqKwKT/GIPvK9PUyq6HXIiLoOQIiIAiIgCom+Zf3FVqib5l/cUehK1NIqX8V3cqlD+I7uXIegjkyPSPesrDPpDvun4hYruMe9ZeF/SHfdPxC4Id5Hq1O4zZrKwwXxGmH/qN+KxrLLwgXxSkH/qt+K7KfeR51V2hLyZ2tTFank+6VwVOLzxj9ofFel1cVqWb7pXmtIRwuEX+uPiu7Gq0onndHTvGZ2M9PaGQ2+qVx0A/3mMftj4r0eqgtTSn9g/Bea0z71sQt/wBQfFMZGziTgamaMz0UQDQGXIvOn/OO716wIQI7nmXkMh/TOv0j8VOPVspToypmzfT/AOneChyGS4mqcG1UrQNTyPeu7qN0WEwM9GV0rh9VjT8dS4CpkEtVLI0ENe8uAPaVTFuFkou5pgXUbbmmj0Who9KigNtcbT7lwNYAKyoHNI4e8rOfuoxJsMccc0cLI2ho0WDV7brRS1TC9z3v0nOJJIzuVnia8JpKPA1weHq05Nz4mVZaGT5x/eVmvxFrdQJ+9ktbJOwucb3JN8l5tSSeh7NCEle6K0WO6oP1W7VbdLIeW3cszoUWZhIGsqh00beW/csM3JzN0DSTYAkpYtk5l91R0W7V6T5HZC9mKaVtcf8AUvM95IF3lrB2nPYvTPI6GBmKBji7OPO1uku7o9f7Efr7Hl9M22Odvl7o9IREX0h8KEREARFEjdNjm3tcWQEosUUha8aMzxGLHRub3BHLfVl/3qWNBQVIEm/VNy/kGkQBo2Frn2lAbNFiPo7vfIyQte51725LAW19io4FPy1j9ZIsNnKgM5LjnCw20cjbA1Mjmjnve/fdVwUpYwNkfpnT072I/mgMlERAEREAREQHjXlB/Wmq7m/Bc5ZdH5Qf1pqu5vwXOL5PE/vS82foGB+Gp+S9hZLIiwOoWSyIgFkREAXQ4P8AQW95XProMH+gt7ytqHeOXFdwzURF2HnkHUsCk/xiD7yzzqWBSf4xB95Xp6mVXQ6+yWUoug5CLJZSiAiyWUogIsqJh+hf3FXFanc0RPBcL251De4lamkUO4ru5UPEn1bKxLvmib31LkbPQRzjyA458qysLeOEOtn6J+IWtdxiszCfpLvuH4hcMO8epU7jNyZOYLMwNxOM0V8hvrfisBQZAw30tEjluuuMsrTPPnFzi48z1nFKqkgpJRLURNLmkC7hnkvKqZ4iqIpHAkNcHG3YVivqowc3Fx7FadW9Fm1a4jGKq09LGGE6PlQi1e9/od7XbsmSRvjp6N2i5pF3utbYuOa8skD2mzgdIHmK1j6x/K4NWNJVXvdznLCrjJVXdnVh+jo0VaK1Okq8Zq5iTPWym+sB9hsC1r6uMaruWndOfqjaqHSPdrOxYyrzlqddPBQhuRtJK4gZBre9Y8lc439M+xYBz1oqOTerOiNCCLz6lzjkPaSrbpXu5bdypVTWOdxWkqDS0UUE31qFe3treO8DsGZTTY3isuedyDNyLbWucfRBKr3q3zjmt7NZR0r3C17DmGSoTcN7K7xt1NLjzuyCGV5FgQ0czclQpDS7UCUuTZFK9N8jXExTvj/qXnLICeMbL0zyRMDI8S0Rys/qXZ0e/wDYj9fY8npqS2Oa8vdHoiIuJ8rXlCovJzucZildTy1LpZRFFDGQC52vWV9KfCnbIvmJv5VtM8nQ3M1DrC5tKDYKWflVwSNc5m5ipcGi7iJQbd6A+nEXzE38qymcbN3M1BPZKE/2rKYgkbmaiwFz+lGSA+nUXzE38qyncbN3MVJPMJQjPyrqLTbvm5uoDb52mF7ID6dRazcxjVPui3PYfi9EHCnrYWzMDtYB5CuC8rvlhwzybV1BR1uH1VbUVbHPaISBogEDl70B6gi+b5fyqsGheWS7nMTY8aw5zAfiqqf8qfCKh2jBubxN5HI17PxQH0ci+eZPym6CNrnP3K4sA21zpM5fb2qzP+VJhVOxr59zOKxscSGuc5oBtzID6MRfNn+1fgX/AJfxHxs/FP8AavwL/wAv4j42figPpNFyXkv3cUXlB3KsxvDoJqeMyuhdFLbSa5tubsIXWoDxryg/rRU9zfgucXSeUH9aKnub8Fzi+SxX70/Nn3+B+Gp+S9iEUosDqIRSiAhFKICV0GD/AEFveVz66DB/oLe8rah3jmxXcM1ERdh55B1Fa+l/xiD7y2J1LXNDoq5kwYXBpvbnV4OzMqqujsEWjfilU8fo4mM7TmVYfLWS3053AHkbktXNIwVOTOhe9jOO5re82WJLiVJHrlDjzNzWl4NpZvc5x7TdVtgjbqCq6pdUXxM6TGWZiKF7uYnJY78TrJOIxjPeqQ0DUFKq6jLqii091VKP0k77cwyVbGlozJJ5yVTJNHHx3tb3lYsuJ07OKS89gWUqi4s2hRf8UZ9+dUu4p7lpZsaIvoNa3vNysCfFZZLgyOseQZBZOtHgdMcLUZbe0FxuAqoXGF5dHkSLLEdUOOoAK26R7tbiuTeej1d9zNhJUuPGk96sOqG9pKxEU66llTSLzqg8gAVDpHnW4qhFBayQRFWInkXI0RzuyUi5Qiu6MTeM4uPM1Rvuj82xre3WUsLkNie7U2w5zkp0GN477nmaqHPc4+kSVCDeXN8a3iMHe7NUukc7W425lSiXFgilrHO4oJV9lP0z7AouG0jHVxkLnclh2rJaxrdQVaXKOfIssgaONmVdAA1BSii5VtsL0TyS8TEu9n9S87Xovkl4mJd7P6l29HfER+vseX0x8JP6e6PQV88flqfqLg3/AL4/6CvodeYflAeTys8om5KChwyoihrKaffmCXJrsrEE8i+nPijifJj5HtyVT5MqbFaeGesrq7D3GUibREriL6HZmAF84bhsFqqzFccpItGlZE0tIc3fLShx3uP/AORBF16zhXkY8ruE4c2gw3H4aajbpWhjrHBo0teVlqqT8nfyj0hJpq6giJkbMS2pIJe03BOWsIDlaOh3UR0slRG7BoY2UjJpg5jQYY3t0ml2WsglRhtHWz0+O4XXOpo62GXeRDTQNLnlpMlwTrbdoyGdivSvzQ+Vn0bYjhgDadtM0b7kGgNF+LmfRGfetXT+Qfyo08lZJFilC2SrcXTu4QbvJvc30deZQHEP3O7opsUp6einwuSTfN7g0Ghm+EmFzsraml8evtXP7t8Hxc0NPjWJOw4wu0YmClc0Egl5a7RHIdE59y9ci8h/lWiaGx4xRtAcx4tUm4LNHRPF5NBnhCw5vyePKNV0VPQVdfh7qOJ12MM5IZr7O07UB9KeQ7/lJuX/APZt+JXgf5Xn/Mfcl/lH/WF9L7hMCduZ3HYRgr5RM+ip2xOkAsHEayPavMvL15H8R8ouMYTiOFYpDRS0UbmESsJvcggi3cpi7NMhq6seRY7hG5PEIJ37qJmUugwlkzcnX5u3uXlO4nDYpqrEpaerquDRSMjZvLBpuDnEBzrg2H4r2TEvyY91mJTb7XbpqKZ/O6N34q3R/kvbqaJ5fSbpqSFxFiY2PFxtWSg+unVb3S/jwXkXyUaUI0qC3Li9X+PI82rdwu6yCWdjamnEFMRKSZr6Dcg1xsM9Q1X1LLg8nW6zGqefD6+qiLqSDhFPAJBYOc6MWJtYAtcTr5F6K38mrdkzR0d1sA0SSOPrPtWxn8gG7iXCafDxuooI4YSXaccT2vfq4zr3OpaFTxvc35O6Gqc2mx3E3Uda/FHYa3ey1zNICPVfNxJksLd61WM+TTH8JoXVtUymFK2HhDniYEtZZxFxyEluj95zRyr2J35MG6t8rZHbp6QyNk30OLX3D8vS168hn2LLd+Thuzfh9VRybqaSSGpDGyabHklrTpBt76r2JHOBzID0D8jv/lG7/wDYTf6WL3FcD5Etwk3k83EMwWqq2Vc5nfO+RjdFt3WFh7Au+QHjflA/Wip7m/Bc6uv3bxMdujqC5oJs34LQbzH0AvlcTG9aXmz7vBVEsPBfJGvRZ+9M6ATeo+gFhkZ09YjARZ+9R9AJvTOiEysdYjARZ29M6IRRlHWIwl0GDD/cW95Wgsugwb6EPvFXod4yxXcM2wSymyXXYefciwUaIveytSVcEfHlaDzA3WJLi0LeI1zvcqupFas0jTlLRGxsEsFoJsak+oWM7sysCbEZZOM97vbYLJ148DeOEm9TqZKiCPjyNHZdYkuK07L6Ac87AuYdO88wVtznO1uJVHXk9Dojgl/Jm9nxp2YYGN95WBPicsl7yPI5hkFr7JZZucnqzeOHhHRF19Q9xVtz3O1uKiyWVDZJIiyWU2SykkiyWVTWlxs0EnsCr3kjjuazvOexCG0i1ZALmwFyrv6JuoOee3IIZXWs2zB+yLIL8iN5da7rNH7RS0TeVzz2ZBUHPWUspuLcyvfSOIGs7gqCS45klLJZRcWSIslkVTI3P1A250JKbJa+oLJZTj659gV5rGt4oAUXKOa4GKyBzteQ7VeZAxuvM9quoouUc2yLDkSylEKiyWUogIsllKICLL0LyTOaPOLLjSOgQL52zXn1lfo6qoophLSzPik1aTDYrfDVlRqqo+ByY3DvE0JUk7NnvqLxH5R4v9oT+JPlHi/2hP4l7Ha9Lws+e7Br+Jff8HtyLxH5R4v9oT+JPlHi/wBoT+JO16XhY7Br+Jff8HtyLxH5R4v9oT+JPlHi/wBoT+JO16XhY7Br+Jff8HtyLxH5R4v9oT+JPlHi/wBoT+JO16XhY7Br+Jff8HtyLxH5R4v9oT+JPlHi/wBoT+JO16XhY7Br+Jff8HtyLxH5R4v9oT+JPlHi/wBoT+JO16XhY7Br+Jff8HtyLxH5R4v9oT+JPlHi/wBoVHiTtel4WOwa/iX3/B7ci8S+UeL/AGhUeJVw7osWMrQa+ci/SU9r0vCw+ga6/kvv+D2pF5F5+xTrs3iTz7idvps/iVu06fJmfYtbxL7/AIMndmQd0VTY3tYe5aMhVve6R7nvcXOcbkk3JVJXkVJ55uXM+ho0+rpxhyRbRV2UWWZqUqFUVBQXIsiIoJNetlR4i2mpgzey51yddgtahAIsRcLKMnF3RtOCmrSM2bGpTxXMZ3C5WBNiEknGe93eclbfTg8U27FYfG5nGClzctWXp0qa0RW6d51WCtuc52sk+*********************************************************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"}]}}, "final-screenshot": {"id": "final-screenshot", "title": "Final Screenshot", "description": "The last screenshot captured of the pageload.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "screenshot", "timing": 1161, "timestamp": 955154043890, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAFcAfQDASIAAhEBAxEB/8QAHAABAAEFAQEAAAAAAAAAAAAAAAECAwQFBgcI/8QAUxAAAQMCAgMLBwcKAwYGAwAAAQACAwQRBSESMZEGExQyQVFSVGFxkgcVIlOBobEWMzRCVXLRCBcjNmJkk8HC4TVzshgkJjdD8CVWY3SCg0R18f/EABsBAQADAQEBAQAAAAAAAAAAAAABAgMEBQYH/8QANxEAAgECAwQIBQQCAgMAAAAAAAECAxEEEjEUIUFRBRMVMlJxkbE0YYGh0SIzQlMjJPDxBnLB/9oADAMBAAIRAxEAPwDYqVKL5u5+oBAgUhAEUopIFkRSgKkRFICIiAIiIQSEQIhFwiIguEREIuWJOOVSqn5vKpUGqCIiEiyWREAslkRALJZEQCyIiAIiIAiIgCIiAIiILhERAEREAREQBERAEREAREQBERAEREAREQBERASFIQKRrUXKkopRSQUXS6v8JDvnomO7QLFTo07+K90Z5nC42qvkRmtqjHup0leNLJa7NGQfsm6sOaWmzgQe0JvRKaehOl2JpdipRLk7irS7FOn2KhEuNxXp9iafYqES43Fen2JvnYqES4sivfOxN87FQiXFkXN87E3zsVtEuLIub52Jvh5lbRLiyLm+HmUGQkcyoRLiyCIii5IREQBERLgWSyIpuBZLIiXAslkRLgWSyIouBZLIiXAsiIlwEREuAiIlwEREAREQBERAEREAREQBERAEREAREQBERAVKQoUhSiCUU2RSQW7KHaX1bKbpdUJLJklYbtBB5wquHVFrPcHjmcLq5dX6KNktZCx7Q5pcAQeVWT4FJpWuzEbPG7jNLTztVxui7iPaew5FdfVbmaCa5Y10R/ZOWxamq3JTNuaadj+xwstXSkuBxwxdN8beZpnNLeMCEV6bCsTo73hkLednpBYpmc11pogHcuWiVk421OqNXNpvLlksqRJG7U8tPM4fzUGVocQT7RqUWLqSZXZLKA5p1EKbqCwssptIw0HCOER6eno719bvWLdLoVkm9HYqLQNdysmKr3uimpt5jcJCDpkekO4rFDiOVTpA6xsR2YlFS1FgdRt3qC0hTYHUdqek3PMKSSmyWVWkDrGxLA8UqCSmyWUkEaxZRdALJZLpdALJZLpdALIl1KAhFKICEUogIsllKICLJZSiAiyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6XQCyWS6kICVI1qFUFYglERCCiyiylFUsRYLJw4f7/B98LHWTh30+D74Ux1RSp3WegIiL0T5sKzPSU9QLTQxvH7TVeRCU2tDSVO5qgmuWNfETysP8lqarcnM25pp2P7Hi3vXYqCqOlF8DeOKqx0Z5zU4NX02b6d7hzs9JYRdJG6zrgjkIXqRWNUU0E7bTQxvH7TbrKWHXBnTDHv8AkjzgTc4VYkaeW3eutqtz1DJmxjoj+wVqanc3<PERSON>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*********************************************************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"}}, "total-blocking-time": {"id": "total-blocking-time", "title": "Total Blocking Time", "description": "Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "0 ms", "scoringOptions": {"p10": 150, "median": 350}}, "max-potential-fid": {"id": "max-potential-fid", "title": "Max Potential First Input Delay", "description": "The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 16, "numericUnit": "millisecond", "displayValue": "20 ms"}, "cumulative-layout-shift": {"id": "cumulative-layout-shift", "title": "Cumulative Layout Shift", "description": "Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 0.000672417303624145, "numericUnit": "unitless", "displayValue": "0.001", "scoringOptions": {"p10": 0.1, "median": 0.25}, "details": {"type": "debugdata", "items": [{"cumulativeLayoutShiftMainFrame": 0.000672417303624145, "newEngineResult": {"cumulativeLayoutShift": 0.000672417303624145, "cumulativeLayoutShiftMainFrame": 0.000672417303624145}, "newEngineResultDiffered": false}]}}, "server-response-time": {"id": "server-response-time", "title": "Initial server response time was short", "description": "Keep the server response time for the main document short because all other requests depend on it. [Learn more about the Time to First Byte metric](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 140.749, "numericUnit": "millisecond", "displayValue": "Root document took 140 ms", "metricSavings": {"FCP": 50, "LCP": 50}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "responseTime", "valueType": "timespanMs", "label": "Time Spent"}], "items": [{"url": "https://app.datoms.io/accounts/login?rd=https://app.datoms.io/enterprise/1/datoms-x/customer-management/", "responseTime": 140.749}], "overallSavingsMs": 40.***************}, "guidanceLevel": 1}, "interactive": {"id": "interactive", "title": "Time to Interactive", "description": "Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 977.632, "numericUnit": "millisecond", "displayValue": "1.0 s"}, "user-timings": {"id": "user-timings", "title": "User Timing marks and measures", "description": "Consider instrumenting your app with the User Timing API to measure your app's real-world performance during key user experiences. [Learn more about User Timing marks](https://developer.chrome.com/docs/lighthouse/performance/user-timings/).", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "critical-request-chains": {"id": "critical-request-chains", "title": "Avoid chaining critical requests", "description": "The Critical Request Chains below show you what resources are loaded with a high priority. Consider reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load. [Learn how to avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/).", "score": 1, "scoreDisplayMode": "informative", "displayValue": "5 chains found", "details": {"type": "criticalrequestchain", "chains": {"86D27D6904A0AF97C6D2478EFEE5210B": {"request": {"url": "https://app.datoms.io/enterprise/1/datoms-x/customer-management/", "startTime": 955152.884311, "endTime": 955153.142879, "responseReceivedTime": 955153.141551, "transferSize": 1940}, "children": {"86D27D6904A0AF97C6D2478EFEE5210B:redirect": {"request": {"url": "https://app.datoms.io/accounts/login?rd=https://app.datoms.io/enterprise/1/datoms-x/customer-management/", "startTime": 955153.**********, "endTime": 955153.360397, "responseReceivedTime": 955153.**********, "transferSize": 65445}, "children": {"28384.2": {"request": {"url": "https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/css/intlTelInput.css", "startTime": 955153.292011, "endTime": 955153.523593, "responseReceivedTime": 955153.**********, "transferSize": 3490}}, "28384.3": {"request": {"url": "https://fonts.googleapis.com/css?family=Lora|Quicksand&display=swap", "startTime": 955153.2923, "endTime": 955153.**********, "responseReceivedTime": 955153.640818, "transferSize": 2005}, "children": {"28384.30": {"request": {"url": "https://fonts.gstatic.com/s/lora/v35/0QI6MX1D_JOuGQbT0gvTJPa787weuxJBkq18m9eY.woff2", "startTime": 955153.657973, "endTime": 955153.804938, "responseReceivedTime": 955153.**********, "transferSize": 21157}}, "28384.33": {"request": {"url": "https://fonts.gstatic.com/s/quicksand/v31/6xK-dSZaM9iE8KbpRA_LJ3z8mH9BOJvgkP8o58a-wjw3UD0.woff2", "startTime": 955153.658153, "endTime": 955153.795524, "responseReceivedTime": 955153.7892280001, "transferSize": 15895}}}}, "28384.13": {"request": {"url": "https://code.jquery.com/jquery-3.6.0.min.js", "startTime": 955153.542537, "endTime": 955153.799016, "responseReceivedTime": 955153.7919760001, "transferSize": 31243}}, "28384.14": {"request": {"url": "https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/js/intlTelInput.js", "startTime": 955153.55432, "endTime": 955153.641748, "responseReceivedTime": 955153.6405930001, "transferSize": 21412}}}}}}}, "longestChain": {"duration": 920.6270000934601, "length": 4, "transferSize": 21157}}, "guidanceLevel": 1}, "redirects": {"id": "redirects", "title": "Avoid multiple page redirects", "description": "Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 258.*************, "numericUnit": "millisecond", "displayValue": "Potential savings of 260 ms", "metricSavings": {"LCP": 250, "FCP": 250}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "wastedMs", "valueType": "timespanMs", "label": "Time Spent"}], "items": [{"url": "https://app.datoms.io/enterprise/1/datoms-x/customer-management/", "wastedMs": 258.*************}, {"url": "https://app.datoms.io/accounts/login?rd=https://app.datoms.io/enterprise/1/datoms-x/customer-management/", "wastedMs": 0}], "overallSavingsMs": 258.*************}, "guidanceLevel": 2}, "mainthread-work-breakdown": {"id": "mainthread-work-breakdown", "title": "Minimizes main-thread work", "description": "Consider reducing the time spent parsing, compiling and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to minimize main-thread work](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)", "score": 1, "scoreDisplayMode": "informative", "numericValue": 127.**************, "numericUnit": "millisecond", "displayValue": "0.1 s", "metricSavings": {"TBT": 0}, "details": {"type": "table", "headings": [{"key": "groupLabel", "valueType": "text", "label": "Category"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"group": "other", "groupLabel": "Other", "duration": 67.**************}, {"group": "scriptEvaluation", "groupLabel": "Script Evaluation", "duration": 24.50200000000001}, {"group": "styleLayout", "groupLabel": "Style & Layout", "duration": 19.464}, {"group": "scriptParseCompile", "groupLabel": "Script Parsing & Compilation", "duration": 6.705}, {"group": "parseHTML", "groupLabel": "Parse HTML & CSS", "duration": 6.552000000000001}, {"group": "paintCompositeRender", "groupLabel": "Rendering", "duration": 2.2929999999999997}, {"group": "garbageCollection", "groupLabel": "Garbage Collection", "duration": 0.7840000000000003}], "sortedBy": ["duration"]}, "guidanceLevel": 1}, "bootup-time": {"id": "bootup-time", "title": "JavaScript execution time", "description": "Consider reducing the time spent parsing, compiling, and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to reduce Javascript execution time](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 9.875, "numericUnit": "millisecond", "displayValue": "0.0 s", "metricSavings": {"TBT": 0}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "total", "granularity": 1, "valueType": "ms", "label": "Total CPU Time"}, {"key": "scripting", "granularity": 1, "valueType": "ms", "label": "Script Evaluation"}, {"key": "scriptParseCompile", "granularity": 1, "valueType": "ms", "label": "<PERSON><PERSON><PERSON> Parse"}], "items": [{"url": "https://app.datoms.io/accounts/login?rd=https://app.datoms.io/enterprise/1/datoms-x/customer-management/", "total": 68.889, "scripting": 8.***************, "scriptParseCompile": 1.617}], "summary": {"wastedMs": 9.875}, "sortedBy": ["total"]}, "guidanceLevel": 1}, "uses-rel-preconnect": {"id": "uses-rel-preconnect", "title": "Preconnect to required origins", "description": "Consider adding `preconnect` or `dns-prefetch` resource hints to establish early connections to important third-party origins. [Learn how to preconnect to required origins](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 223.46, "numericUnit": "millisecond", "displayValue": "Potential savings of 220 ms", "warnings": [], "metricSavings": {"LCP": 200, "FCP": 200}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "wastedMs", "valueType": "timespanMs", "label": "Potential Savings"}], "items": [{"url": "https://fonts.gstatic.com", "wastedMs": 223.46}], "overallSavingsMs": 223.46, "sortedBy": ["wastedMs"]}, "guidanceLevel": 3}, "font-display": {"id": "font-display", "title": "All text remains visible during webfont loads", "description": "Leverage the `font-display` CSS feature to ensure text is user-visible while webfonts are loading. [Learn more about `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/).", "score": 1, "scoreDisplayMode": "metricSavings", "warnings": [], "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 3}, "diagnostics": {"id": "diagnostics", "title": "Diagnostics", "description": "Collection of useful page vitals.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "debugdata", "items": [{"numRequests": 18, "numScripts": 5, "numStylesheets": 2, "numFonts": 2, "numTasks": 358, "numTasksOver10ms": 3, "numTasksOver25ms": 1, "numTasksOver50ms": 0, "numTasksOver100ms": 0, "numTasksOver500ms": 0, "rtt": 16.379999999999995, "throughput": 15603188.883113954, "maxRtt": 111.73, "maxServerLatency": 280.366, "totalByteWeight": 442065, "totalTaskTime": 127.31299999999997, "mainDocumentTransferSize": 65445}]}}, "network-requests": {"id": "network-requests", "title": "Network Requests", "description": "Lists the network requests that were made during page load.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "protocol", "valueType": "text", "label": "Protocol"}, {"key": "networkRequestTime", "valueType": "ms", "granularity": 1, "label": "Network Request Time"}, {"key": "networkEndTime", "valueType": "ms", "granularity": 1, "label": "Network End Time"}, {"key": "transferSize", "valueType": "bytes", "displayUnit": "kb", "granularity": 1, "label": "Transfer Size"}, {"key": "resourceSize", "valueType": "bytes", "displayUnit": "kb", "granularity": 1, "label": "Resource Size"}, {"key": "statusCode", "valueType": "text", "label": "Status Code"}, {"key": "mimeType", "valueType": "text", "label": "MIME Type"}, {"key": "resourceType", "valueType": "text", "label": "Resource Type"}], "items": [{"url": "https://app.datoms.io/enterprise/1/datoms-x/customer-management/", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 0, "networkRequestTime": 0.****************, "networkEndTime": 259.*************, "finished": true, "transferSize": 1940, "resourceSize": 0, "statusCode": 302, "mimeType": "text/html", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "datoms.io"}, {"url": "https://app.datoms.io/accounts/login?rd=https://app.datoms.io/enterprise/1/datoms-x/customer-management/", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 259.*************, "networkRequestTime": 259.*************, "networkEndTime": 476.**************, "finished": true, "transferSize": 65445, "resourceSize": 63649, "statusCode": 200, "mimeType": "text/html", "resourceType": "Document", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "datoms.io"}, {"url": "https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/css/intlTelInput.css", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 407.*************, "networkRequestTime": 408.**************, "networkEndTime": 639.*************, "finished": true, "transferSize": 3490, "resourceSize": 25254, "statusCode": 200, "mimeType": "text/css", "resourceType": "Stylesheet", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "phoenixrobotix.com"}, {"url": "https://fonts.googleapis.com/css?family=Lora|Quicksand&display=swap", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 407.85900008678436, "networkRequestTime": 408.6779999732971, "networkEndTime": 757.9690001010895, "finished": true, "transferSize": 2005, "resourceSize": 6113, "statusCode": 200, "mimeType": "text/css", "resourceType": "Stylesheet", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "Google Fonts"}, {"url": "https://prstatic.phoenixrobotix.com/imgs/logo/datoms-full-logo-regietsred.png", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 412.63699996471405, "networkRequestTime": 412.9340000152588, "networkEndTime": 658.375, "finished": true, "transferSize": 50694, "resourceSize": 50175, "statusCode": 200, "mimeType": "image/png", "resourceType": "Image", "priority": "Medium", "experimentalFromMainFrame": true, "entity": "phoenixrobotix.com"}, {"url": "https://static.datoms.io/images/left-arrow.png", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 412.7450000047684, "networkRequestTime": 413.08900010585785, "networkEndTime": 633.4460000991821, "finished": true, "transferSize": 4002, "resourceSize": 3507, "statusCode": 200, "mimeType": "image/png", "resourceType": "Image", "priority": "Medium", "experimentalFromMainFrame": true, "entity": "datoms.io"}, {"url": "https://static.datoms.io/images/ios-app-store-badge.png", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 412.8140000104904, "networkRequestTime": 634.1600000858307, "networkEndTime": 652.6900000572205, "finished": true, "transferSize": 6471, "resourceSize": 5978, "statusCode": 200, "mimeType": "image/png", "resourceType": "Image", "priority": "Medium", "experimentalFromMainFrame": true, "entity": "datoms.io"}, {"url": "https://static.datoms.io/images/google-play-badge.png", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 412.86100006103516, "networkRequestTime": 653.1119999885559, "networkEndTime": 669.9240000247955, "finished": true, "transferSize": 5396, "resourceSize": 4904, "statusCode": 200, "mimeType": "image/png", "resourceType": "Image", "priority": "Medium", "experimentalFromMainFrame": true, "entity": "datoms.io"}, {"url": "https://static.datoms.io/images/icons/datoms_white-01.svg", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 412.91799998283386, "networkRequestTime": 760.7829999923706, "networkEndTime": 779.8069999217987, "finished": true, "transferSize": 1770, "resourceSize": 2585, "statusCode": 200, "mimeType": "image/svg+xml", "resourceType": "Image", "priority": "High", "experimentalFromMainFrame": true, "entity": "datoms.io"}, {"url": "https://code.jquery.com/jquery-3.6.0.min.js", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 412.99000000953674, "networkRequestTime": 658.9150000810623, "networkEndTime": 915.3940000534058, "finished": true, "transferSize": 31243, "resourceSize": 89501, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Medium", "experimentalFromMainFrame": true, "entity": "jQuery <PERSON>"}, {"url": "https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/js/intlTelInput.js", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 413.07099997997284, "networkRequestTime": 670.6980000734329, "networkEndTime": 758.12600004673, "finished": true, "transferSize": 21412, "resourceSize": 89336, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Medium", "experimentalFromMainFrame": true, "entity": "phoenixrobotix.com"}, {"url": "https://static.datoms.io/images/login-back.png", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 763.4359999895096, "networkRequestTime": 763.768000125885, "networkEndTime": 888.5030000209808, "finished": true, "transferSize": 147452, "resourceSize": 146822, "statusCode": 200, "mimeType": "image/png", "resourceType": "Image", "priority": "High", "experimentalFromMainFrame": true, "entity": "datoms.io"}, {"url": "https://fonts.gstatic.com/s/lora/v35/0QI6MX1D_JOuGQbT0gvTJPa787weuxJBkq18m9eY.woff2", "sessionTargetType": "page", "protocol": "h3", "rendererStartTime": 764.0980000495911, "networkRequestTime": 774.3509999513626, "networkEndTime": 921.3160001039505, "finished": true, "transferSize": 21157, "resourceSize": 21128, "statusCode": 200, "mimeType": "font/woff2", "resourceType": "Font", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "Google Fonts"}, {"url": "https://fonts.gstatic.com/s/quicksand/v31/6xK-dSZaM9iE8KbpRA_LJ3z8mH9BOJvgkP8o58a-wjw3UD0.woff2", "sessionTargetType": "page", "protocol": "h3", "rendererStartTime": 764.210000038147, "networkRequestTime": 774.5310000181198, "networkEndTime": 911.9019999504089, "finished": true, "transferSize": 15895, "resourceSize": 15868, "statusCode": 200, "mimeType": "font/woff2", "resourceType": "Font", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "Google Fonts"}, {"url": "https://ipinfo.io/?callback=jQuery36001465317437292597_1730718734783&_=*************", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 939.2560000419617, "networkRequestTime": 939.6959999799728, "networkEndTime": 1477.1230000257492, "finished": true, "transferSize": 589, "resourceSize": 628, "statusCode": 200, "mimeType": "text/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "ipinfo.io"}, {"url": "https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/js/utils.js", "sessionTargetType": "page", "protocol": "h2", "rendererStartTime": 1481.9510000944138, "networkRequestTime": 1482.426999926567, "networkEndTime": 1575.1050000190735, "finished": true, "transferSize": 57577, "resourceSize": 252155, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "phoenixrobotix.com"}, {"url": "https://static.datoms.io/images/icons/favicon.ico", "sessionTargetType": "page", "protocol": "", "rendererStartTime": 1483.7400000095367, "networkRequestTime": 1483.7400000095367, "networkEndTime": 1692.0610001087189, "finished": true, "transferSize": 0, "resourceSize": 0, "statusCode": -1, "mimeType": "", "resourceType": "Other", "priority": "High", "experimentalFromMainFrame": true, "entity": "datoms.io"}], "debugData": {"type": "debugdata", "networkStartTimeTs": 955152883622}}}, "network-rtt": {"id": "network-rtt", "title": "Network Round Trip Times", "description": "Network round trip times (RTT) have a large impact on performance. If the RTT to an origin is high, it's an indication that servers closer to the user could improve performance. [Learn more about the Round Trip Time](https://hpbn.co/primer-on-latency-and-bandwidth/).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 111.73, "numericUnit": "millisecond", "displayValue": "110 ms", "details": {"type": "table", "headings": [{"key": "origin", "valueType": "text", "label": "URL"}, {"key": "rtt", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"origin": "https://fonts.gstatic.com", "rtt": 111.73}, {"origin": "https://app.datoms.io", "rtt": 34.59899999999999}, {"origin": "https://ipinfo.io", "rtt": 24.109999999999985}, {"origin": "https://prstatic.phoenixrobotix.com", "rtt": 19.022999999999996}, {"origin": "https://code.jquery.com", "rtt": 18.55300000000001}, {"origin": "https://fonts.googleapis.com", "rtt": 18.50800000000001}, {"origin": "https://static.datoms.io", "rtt": 16.379999999999995}], "sortedBy": ["rtt"]}}, "network-server-latency": {"id": "network-server-latency", "title": "Server Backend Latencies", "description": "Server latencies can impact web performance. If the server latency of an origin is high, it's an indication the server is overloaded or has poor backend performance. [Learn more about server response time](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 280.366, "numericUnit": "millisecond", "displayValue": "280 ms", "details": {"type": "table", "headings": [{"key": "origin", "valueType": "text", "label": "URL"}, {"key": "serverResponseTime", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"origin": "https://ipinfo.io", "serverResponseTime": 280.366}, {"origin": "https://fonts.googleapis.com", "serverResponseTime": 121.03799999999995}, {"origin": "https://app.datoms.io", "serverResponseTime": 70.42600000000003}, {"origin": "https://prstatic.phoenixrobotix.com", "serverResponseTime": 34.465500000000006}, {"origin": "https://static.datoms.io", "serverResponseTime": 0}, {"origin": "https://code.jquery.com", "serverResponseTime": 0}, {"origin": "https://fonts.gstatic.com", "serverResponseTime": 0}], "sortedBy": ["serverResponseTime"]}}, "main-thread-tasks": {"id": "main-thread-tasks", "title": "Tasks", "description": "Lists the toplevel main thread tasks that executed during page load.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "startTime", "valueType": "ms", "granularity": 1, "label": "Start Time"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "End Time"}], "items": [{"duration": 6.711, "startTime": 405.235}, {"duration": 11.414, "startTime": 762.814}, {"duration": 32.083, "startTime": 794.49}, {"duration": 7.535, "startTime": 919.456}, {"duration": 12.964, "startTime": 928.531}, {"duration": 9.782, "startTime": 941.606}]}}, "metrics": {"id": "metrics", "title": "Metrics", "description": "Collects all available metrics.", "score": 1, "scoreDisplayMode": "informative", "numericValue": 978, "numericUnit": "millisecond", "details": {"type": "debugdata", "items": [{"firstContentfulPaint": 978, "firstContentfulPaintTs": 955153860498, "firstContentfulPaintAllFrames": 978, "firstContentfulPaintAllFramesTs": 955153860498, "largestContentfulPaint": 1378, "largestContentfulPaintTs": 955154260542, "largestContentfulPaintAllFrames": 1378, "largestContentfulPaintAllFramesTs": 955154260542, "interactive": 978, "interactiveTs": 955153860498, "speedIndex": 971, "speedIndexTs": 955153853866, "totalBlockingTime": 0, "maxPotentialFID": 16, "cumulativeLayoutShift": 0.000672417303624145, "cumulativeLayoutShiftMainFrame": 0.000672417303624145, "timeToFirstByte": 402, "timeToFirstByteTs": 955153284409, "observedTimeOrigin": 0, "observedTimeOriginTs": 955152882866, "observedNavigationStart": 0, "observedNavigationStartTs": 955152882866, "observedFirstPaint": 978, "observedFirstPaintTs": 955153860498, "observedFirstContentfulPaint": 978, "observedFirstContentfulPaintTs": 955153860498, "observedFirstContentfulPaintAllFrames": 978, "observedFirstContentfulPaintAllFramesTs": 955153860498, "observedLargestContentfulPaint": 1378, "observedLargestContentfulPaintTs": 955154260542, "observedLargestContentfulPaintAllFrames": 1378, "observedLargestContentfulPaintAllFramesTs": 955154260542, "observedTraceEnd": 12531, "observedTraceEndTs": 955165413988, "observedLoad": 1483, "observedLoadTs": 955154365852, "observedDomContentLoaded": 942, "observedDomContentLoadedTs": 955153824940, "observedCumulativeLayoutShift": 0.000672417303624145, "observedCumulativeLayoutShiftMainFrame": 0.000672417303624145, "observedFirstVisualChange": 844, "observedFirstVisualChangeTs": 955153726866, "observedLastVisualChange": 1161, "observedLastVisualChangeTs": 955154043866, "observedSpeedIndex": 971, "observedSpeedIndexTs": 955153853510}, {"lcpInvalidated": false}]}}, "resource-summary": {"id": "resource-summary", "title": "Resources Summary", "description": "Aggregates all network requests and groups them by type", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "label", "valueType": "text", "label": "Resource Type"}, {"key": "requestCount", "valueType": "numeric", "label": "Requests"}, {"key": "transferSize", "valueType": "bytes", "label": "Transfer Size"}], "items": [{"resourceType": "total", "label": "Total", "requestCount": 17, "transferSize": 442065}, {"resourceType": "image", "label": "Image", "requestCount": 6, "transferSize": 215785}, {"resourceType": "script", "label": "<PERSON><PERSON><PERSON>", "requestCount": 5, "transferSize": 116348}, {"resourceType": "document", "label": "Document", "requestCount": 1, "transferSize": 65445}, {"resourceType": "font", "label": "Font", "requestCount": 2, "transferSize": 37052}, {"resourceType": "stylesheet", "label": "Stylesheet", "requestCount": 2, "transferSize": 5495}, {"resourceType": "other", "label": "Other", "requestCount": 1, "transferSize": 1940}, {"resourceType": "media", "label": "Media", "requestCount": 0, "transferSize": 0}, {"resourceType": "third-party", "label": "Third-party", "requestCount": 10, "transferSize": 209589}]}}, "third-party-summary": {"id": "third-party-summary", "title": "Minimize third-party usage", "description": "Third-party code can significantly impact load performance. Limit the number of redundant third-party providers and try to load third-party code after your page has primarily finished loading. [Learn how to minimize third-party impact](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/).", "score": 1, "scoreDisplayMode": "informative", "displayValue": "Third-party code blocked the main thread for 0 ms", "metricSavings": {"TBT": 0}, "details": {"type": "table", "headings": [{"key": "entity", "valueType": "text", "label": "Third-Party", "subItemsHeading": {"key": "url", "valueType": "url"}}, {"key": "transferSize", "granularity": 1, "valueType": "bytes", "label": "Transfer Size", "subItemsHeading": {"key": "transferSize"}}, {"key": "blockingTime", "granularity": 1, "valueType": "ms", "label": "Main-Thread Blocking Time", "subItemsHeading": {"key": "blockingTime"}}], "items": [{"mainThreadTime": 10.207999999999998, "blockingTime": 0, "transferSize": 133173, "tbtImpact": 0, "entity": "phoenixrobotix.com", "subItems": {"type": "subitems", "items": [{"url": "https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/js/utils.js", "mainThreadTime": 4.078999999999999, "blockingTime": 0, "transferSize": 57577, "tbtImpact": 0}, {"url": "https://prstatic.phoenixrobotix.com/imgs/logo/datoms-full-logo-regietsred.png", "mainThreadTime": 0, "blockingTime": 0, "transferSize": 50694, "tbtImpact": 0}, {"url": "https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/js/intlTelInput.js", "mainThreadTime": 5.43, "blockingTime": 0, "transferSize": 21412, "tbtImpact": 0}, {"url": "https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/css/intlTelInput.css", "mainThreadTime": 0.6990000000000001, "blockingTime": 0, "transferSize": 3490, "tbtImpact": 0}]}}, {"mainThreadTime": 0.33199999999999996, "blockingTime": 0, "transferSize": 39057, "tbtImpact": 0, "entity": "Google Fonts", "subItems": {"type": "subitems", "items": [{"url": "https://fonts.gstatic.com/s/lora/v35/0QI6MX1D_JOuGQbT0gvTJPa787weuxJBkq18m9eY.woff2", "mainThreadTime": 0, "blockingTime": 0, "transferSize": 21157, "tbtImpact": 0}, {"url": "https://fonts.gstatic.com/s/quicksand/v31/6xK-dSZaM9iE8KbpRA_LJ3z8mH9BOJvgkP8o58a-wjw3UD0.woff2", "mainThreadTime": 0, "blockingTime": 0, "transferSize": 15895, "tbtImpact": 0}, {"url": "https://fonts.googleapis.com/css?family=Lora|Quicksand&display=swap", "mainThreadTime": 0.33199999999999996, "blockingTime": 0, "transferSize": 2005, "tbtImpact": 0}]}}, {"mainThreadTime": 11.796000000000001, "blockingTime": 0, "transferSize": 31243, "tbtImpact": 0, "entity": "jQuery <PERSON>", "subItems": {"type": "subitems", "items": [{"url": "https://code.jquery.com/jquery-3.6.0.min.js", "mainThreadTime": 11.796000000000001, "blockingTime": 0, "transferSize": 31243, "tbtImpact": 0}]}}, {"mainThreadTime": 0.647, "blockingTime": 0, "transferSize": 589, "tbtImpact": 0, "entity": "ipinfo.io", "subItems": {"type": "subitems", "items": [{"url": "https://ipinfo.io/?callback=jQuery36001465317437292597_1730718734783&_=*************", "mainThreadTime": 0.647, "blockingTime": 0, "transferSize": 589, "tbtImpact": 0}]}}], "summary": {"wastedBytes": 209589, "wastedMs": 0}, "isEntityGrouped": true}, "guidanceLevel": 1}, "third-party-facades": {"id": "third-party-facades", "title": "Lazy load third-party resources with facades", "description": "Some third-party embeds can be lazy loaded. Consider replacing them with a facade until they are required. [Learn how to defer third-parties with a facade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"TBT": 0}, "guidanceLevel": 3}, "largest-contentful-paint-element": {"id": "largest-contentful-paint-element", "title": "Largest Contentful Paint element", "description": "This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": 0, "scoreDisplayMode": "metricSavings", "displayValue": "1,380 ms", "metricSavings": {"LCP": 200}, "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "node", "valueType": "node", "label": "Element"}], "items": [{"node": {"type": "node", "lhId": "page-0-P", "path": "1,HTML,1,BODY,1,DIV,1,DIV,1,P", "selector": "body.iti-mobile > div#cookieNotice > div#cookiePopup > p", "boundingRect": {"top": 881, "bottom": 920, "left": 18, "right": 1368, "width": 1350, "height": 39}, "snippet": "<p>", "nodeLabel": "We use essential cookies that are required to provide you with basic functions …"}}]}, {"type": "table", "headings": [{"key": "phase", "valueType": "text", "label": "Phase"}, {"key": "percent", "valueType": "text", "label": "% of LCP"}, {"key": "timing", "valueType": "ms", "label": "Timing"}], "items": [{"phase": "TTFB", "timing": 401.5429998779297, "percent": "29%"}, {"phase": "<PERSON>ad <PERSON>", "timing": 0, "percent": "0%"}, {"phase": "Load Time", "timing": 0, "percent": "0%"}, {"phase": "Render Delay", "timing": 976.1330001220703, "percent": "71%"}]}]}, "guidanceLevel": 1}, "lcp-lazy-loaded": {"id": "lcp-lazy-loaded", "title": "Largest Contentful Paint image was not lazily loaded", "description": "Above-the-fold images that are lazily loaded render later in the page lifecycle, which can delay the largest contentful paint. [Learn more about optimal lazy loading](https://web.dev/articles/lcp-lazy-loading).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"LCP": 0}, "guidanceLevel": 3}, "layout-shifts": {"id": "layout-shifts", "title": "Avoid large layout shifts", "description": "These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)", "score": 1, "scoreDisplayMode": "informative", "displayValue": "2 layout shifts found", "metricSavings": {"CLS": 0.001}, "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "extra"}, "label": "Element"}, {"key": "score", "valueType": "numeric", "subItemsHeading": {"key": "cause", "valueType": "text"}, "granularity": 0.001, "label": "Layout shift score"}], "items": [{"node": {"type": "node", "lhId": "page-1-DIV", "path": "1,HTML,1,BODY,0,DIV,1,DIV,0,DIV,0,DIV", "selector": "div.container > div.form > div.click > div.front", "boundingRect": {"top": 223, "bottom": 717, "left": 810, "right": 1215, "width": 405, "height": 495}, "snippet": "<div class=\"front\">", "nodeLabel": "Login\nEmail or Mobile\nNext\nForgot Password ?"}, "score": 0.0005461807089171886}, {"node": {"type": "node", "lhId": "page-2-DIV", "path": "1,<PERSON><PERSON><PERSON>,1,BODY,0,DIV,3,DIV", "selector": "body.iti-mobile > div.container > div.datoms-logo", "boundingRect": {"top": 867, "bottom": 910, "left": 1118, "right": 1320, "width": 203, "height": 43}, "snippet": "<div class=\"datoms-logo\">", "nodeLabel": "body.iti-mobile > div.container > div.datoms-logo"}, "score": 0.00012623659470695634, "subItems": {"type": "subitems", "items": [{"extra": {"type": "url", "value": "https://fonts.gstatic.com/s/lora/v35/0QI6MX1D_JOuGQbT0gvTJPa787weuxJBkq18m9eY.woff2"}, "cause": "Web font loaded"}, {"extra": {"type": "url", "value": "https://fonts.gstatic.com/s/quicksand/v31/6xK-dSZaM9iE8KbpRA_LJ3z8mH9BOJvgkP8o58a-wjw3UD0.woff2"}, "cause": "Web font loaded"}, {"extra": {"type": "url", "value": "https://code.jquery.com/jquery-3.6.0.min.js"}, "cause": "A late network request adjusted the page layout"}, {"extra": {"type": "url", "value": "https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/js/intlTelInput.js"}, "cause": "A late network request adjusted the page layout"}, {"extra": {"type": "url", "value": "https://fonts.googleapis.com/css?family=Lora|Quicksand&display=swap"}, "cause": "A late network request adjusted the page layout"}, {"extra": {"type": "url", "value": "https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/css/intlTelInput.css"}, "cause": "A late network request adjusted the page layout"}]}}]}, "guidanceLevel": 2}, "long-tasks": {"id": "long-tasks", "title": "Avoid long main-thread tasks", "description": "Lists the longest tasks on the main thread, useful for identifying worst contributors to input delay. [Learn how to avoid long main-thread tasks](https://web.dev/articles/optimize-long-tasks)", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"TBT": 0}, "details": {"type": "table", "headings": [], "items": [], "debugData": {"type": "debugdata", "urls": [], "tasks": []}}, "guidanceLevel": 1}, "non-composited-animations": {"id": "non-composited-animations", "title": "Avoid non-composited animations", "description": "Animations which are not composited can be janky and increase CLS. [Learn how to avoid non-composited animations](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "unsized-images": {"id": "unsized-images", "title": "Image elements do not have explicit `width` and `height`", "description": "Set an explicit width and height on image elements to reduce layout shifts and improve CLS. [Learn how to set image dimensions](https://web.dev/articles/optimize-cls#images_without_dimensions)", "score": 0.5, "scoreDisplayMode": "metricSavings", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "label": ""}, {"key": "url", "valueType": "url", "label": "URL"}], "items": [{"url": "https://prstatic.phoenixrobotix.com/imgs/logo/datoms-full-logo-regietsred.png", "node": {"type": "node", "lhId": "1-2-<PERSON><PERSON>", "path": "1,HTML,1,BODY,0,DIV,1,DIV,0,DIV,0,DIV,0,DIV,0,DIV,0,IMG", "selector": "div.front > div.form-sec > div.logo-container > img#company_logo", "boundingRect": {"top": 243, "bottom": 331, "left": 883, "right": 1143, "width": 260, "height": 89}, "snippet": "<img id=\"company_logo\" class=\"datoms-r-logo\" src=\"https://prstatic.phoenixrobotix.com/imgs/logo/datoms-full-logo-regietsred.…\">", "nodeLabel": "div.front > div.form-sec > div.logo-container > img#company_logo"}}, {"url": "https://static.datoms.io/images/google-play-badge.png", "node": {"type": "node", "lhId": "1-6-<PERSON><PERSON>", "path": "1,HTML,1,BODY,0,DIV,1,DIV,0,DIV,0,DIV,0,DIV,2,FORM,13,DIV,1,A,0,IMG", "selector": "form#login_form > div#play_ios_store_icons > a#android > img", "boundingRect": {"top": 627, "bottom": 694, "left": 1013, "right": 1185, "width": 173, "height": 67}, "snippet": "<img src=\"https://static.datoms.io/images/google-play-badge.png\">", "nodeLabel": "form#login_form > div#play_ios_store_icons > a#android > img"}}, {"url": "https://static.datoms.io/images/icons/datoms_white-01.svg", "node": {"type": "node", "lhId": "1-8-<PERSON><PERSON>", "path": "1,<PERSON><PERSON><PERSON>,1,BODY,0,DIV,3,DIV,0,IMG", "selector": "body.iti-mobile > div.container > div.datoms-logo > img", "boundingRect": {"top": 867, "bottom": 906, "left": 1118, "right": 1320, "width": 203, "height": 39}, "snippet": "<img src=\"https://static.datoms.io/images/icons/datoms_white-01.svg\">", "nodeLabel": "body.iti-mobile > div.container > div.datoms-logo > img"}}, {"url": "https://static.datoms.io/images/ios-app-store-badge.png", "node": {"type": "node", "lhId": "1-5-<PERSON><PERSON>", "path": "1,HTML,1,BODY,0,DIV,1,DIV,0,DIV,0,DIV,0,DIV,2,FORM,13,DIV,0,A,0,IMG", "selector": "form#login_form > div#play_ios_store_icons > a#apple > img", "boundingRect": {"top": 637, "bottom": 684, "left": 855, "right": 998, "width": 143, "height": 47}, "snippet": "<img src=\"https://static.datoms.io/images/ios-app-store-badge.png\">", "nodeLabel": "form#login_form > div#play_ios_store_icons > a#apple > img"}}]}, "guidanceLevel": 4}, "prioritize-lcp-image": {"id": "prioritize-lcp-image", "title": "Preload Largest Contentful Paint image", "description": "If the LCP element is dynamically added to the page, you should preload the image in order to improve LCP. [Learn more about preloading LCP elements](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"LCP": 0}, "guidanceLevel": 4}, "script-treemap-data": {"id": "script-treemap-data", "title": "Script Treemap Data", "description": "Used for treemap app", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "treemap-data", "nodes": [{"name": "https://app.datoms.io/accounts/login?rd=https://app.datoms.io/enterprise/1/datoms-x/customer-management/", "resourceBytes": 42610, "unusedBytes": 38702, "children": [{"name": "(inline) !function(){\"us…", "resourceBytes": 837, "unusedBytes": 91}, {"name": "(inline) // set cookie a…", "resourceBytes": 1093, "unusedBytes": 254}, {"name": "(inline) var inputMobile…", "resourceBytes": 40680, "unusedBytes": 38357}]}, {"name": "https://code.jquery.com/jquery-3.6.0.min.js", "resourceBytes": 89501, "unusedBytes": 51231}, {"name": "https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/js/intlTelInput.js", "resourceBytes": 88650, "unusedBytes": 29457}, {"name": "https://ipinfo.io/?callback=jQuery36001465317437292597_1730718734783&_=*************", "resourceBytes": 628, "unusedBytes": 0}, {"name": "https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/js/utils.js", "resourceBytes": 252155, "unusedBytes": 13435}]}}, "uses-long-cache-ttl": {"id": "uses-long-cache-ttl", "title": "Serve static assets with an efficient cache policy", "description": "A long cache lifetime can speed up repeat visits to your page. [Learn more about efficient cache policies](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/).", "score": 0.5, "scoreDisplayMode": "metricSavings", "numericValue": 6116, "numericUnit": "byte", "displayValue": "2 resources found", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "cacheLifetimeMs", "valueType": "ms", "label": "<PERSON><PERSON>", "displayUnit": "duration"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size", "displayUnit": "kb", "granularity": 1}], "items": [{"url": "https://ipinfo.io/?callback=jQuery36001465317437292597_1730718734783&_=*************", "cacheLifetimeMs": 0, "cacheHitProbability": 0, "totalBytes": 589, "wastedBytes": 589}], "summary": {"wastedBytes": 6116}, "sortedBy": ["totalBytes"], "skipSumming": ["cacheLifetimeMs"]}, "guidanceLevel": 3}, "total-byte-weight": {"id": "total-byte-weight", "title": "Avoids enormous network payloads", "description": "Large network payloads cost users real money and are highly correlated with long load times. [Learn how to reduce payload sizes](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 442065, "numericUnit": "byte", "displayValue": "Total size was 432 KiB", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}], "items": [{"url": "https://static.datoms.io/images/login-back.png", "totalBytes": 147452}, {"url": "https://app.datoms.io/accounts/login?rd=https://app.datoms.io/enterprise/1/datoms-x/customer-management/", "totalBytes": 65445}, {"url": "https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/js/utils.js", "totalBytes": 57577}, {"url": "https://prstatic.phoenixrobotix.com/imgs/logo/datoms-full-logo-regietsred.png", "totalBytes": 50694}, {"url": "https://code.jquery.com/jquery-3.6.0.min.js", "totalBytes": 31243}, {"url": "https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/js/intlTelInput.js", "totalBytes": 21412}, {"url": "https://fonts.gstatic.com/s/lora/v35/0QI6MX1D_JOuGQbT0gvTJPa787weuxJBkq18m9eY.woff2", "totalBytes": 21157}, {"url": "https://fonts.gstatic.com/s/quicksand/v31/6xK-dSZaM9iE8KbpRA_LJ3z8mH9BOJvgkP8o58a-wjw3UD0.woff2", "totalBytes": 15895}, {"url": "https://static.datoms.io/images/ios-app-store-badge.png", "totalBytes": 6471}], "sortedBy": ["totalBytes"]}, "guidanceLevel": 1}, "offscreen-images": {"id": "offscreen-images", "title": "Defer offscreen images", "description": "Consider lazy-loading offscreen and hidden images after all critical resources have finished loading to lower time to interactive. [Learn how to defer offscreen images](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/).", "score": 0.5, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "Potential savings of 3 KiB", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [{"key": "node", "valueType": "node", "label": ""}, {"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Resource Size"}, {"key": "wastedBytes", "valueType": "bytes", "label": "Potential Savings"}], "items": [{"node": {"type": "node", "lhId": "1-3-<PERSON><PERSON>", "path": "1,HTML,1,BODY,0,DIV,1,DIV,0,DIV,0,DIV,0,DIV,2,FORM,7,DIV,0,DIV,0,IMG", "selector": "form#login_form > div#otp_butn > div#back_butn1 > img", "boundingRect": {"top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "snippet": "<img src=\"https://static.datoms.io/images/left-arrow.png\">", "nodeLabel": "form#login_form > div#otp_butn > div#back_butn1 > img"}, "url": "https://static.datoms.io/images/left-arrow.png", "requestStartTime": 955153296.7110001, "totalBytes": 3507, "wastedBytes": 3507, "wastedPercent": 100}], "overallSavingsMs": 0, "overallSavingsBytes": 3507, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "render-blocking-resources": {"id": "render-blocking-resources", "title": "Eliminate render-blocking resources", "description": "Resources are blocking the first paint of your page. Consider delivering critical JS/CSS inline and deferring all non-critical JS/styles. [Learn how to eliminate render-blocking resources](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 706, "numericUnit": "millisecond", "displayValue": "Potential savings of 710 ms", "metricSavings": {"FCP": 700, "LCP": 700}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedMs", "valueType": "timespanMs", "label": "Potential Savings"}], "items": [{"url": "https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/css/intlTelInput.css", "totalBytes": 3490, "wastedMs": 792}, {"url": "https://fonts.googleapis.com/css?family=Lora|Quicksand&display=swap", "totalBytes": 2005, "wastedMs": 877}], "overallSavingsMs": 706}, "guidanceLevel": 2}, "unminified-css": {"id": "unminified-css", "title": "Minify CSS", "description": "Minifying CSS files can reduce network payload sizes. [Learn how to minify CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "unminified-javascript": {"id": "unminified-javascript", "title": "Minify JavaScript", "description": "Minifying JavaScript files can reduce payload sizes and script parse time. [Learn how to minify JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/).", "score": 0.5, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "Potential savings of 10 KiB", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedBytes", "valueType": "bytes", "label": "Potential Savings"}], "items": [{"url": "inline: \n\tvar inputMobile = document.querySelec…", "totalBytes": 40680, "wastedBytes": 9899, "wastedPercent": 24.333824975417894}], "overallSavingsMs": 0, "overallSavingsBytes": 9899, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "unused-css-rules": {"id": "unused-css-rules", "title": "Reduce unused CSS", "description": "Reduce unused rules from stylesheets and defer CSS not used for above-the-fold content to decrease bytes consumed by network activity. [Learn how to reduce unused CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 1}, "unused-javascript": {"id": "unused-javascript", "title": "Reduce unused JavaScript", "description": "Reduce unused JavaScript and defer loading scripts until they are required to decrease bytes consumed by network activity. [Learn how to reduce unused JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 30, "numericUnit": "millisecond", "displayValue": "Potential savings of 37 KiB", "metricSavings": {"FCP": 50, "LCP": 50}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "source", "valueType": "code"}, "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "subItemsHeading": {"key": "sourceBytes"}, "label": "Transfer Size"}, {"key": "wastedBytes", "valueType": "bytes", "subItemsHeading": {"key": "sourceWastedBytes"}, "label": "Potential Savings"}], "items": [{"url": "https://app.datoms.io/accounts/login?rd=https://app.datoms.io/enterprise/1/datoms-x/customer-management/", "totalBytes": 40680, "wastedBytes": 38357, "wastedPercent": 94.**************}], "overallSavingsMs": 30, "overallSavingsBytes": 38357, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 30, "LCP": 30}}}, "guidanceLevel": 1}, "modern-image-formats": {"id": "modern-image-formats", "title": "Serve images in next-gen formats", "description": "Image formats like WebP and AVIF often provide better compression than PNG or JPEG, which means faster downloads and less data consumption. [Learn more about modern image formats](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "uses-optimized-images": {"id": "uses-optimized-images", "title": "Efficiently encode images", "description": "Optimized images load faster and consume less cellular data. [Learn how to efficiently encode images](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "uses-text-compression": {"id": "uses-text-compression", "title": "Enable text compression", "description": "Text-based resources should be served with compression (gzip, deflate or brotli) to minimize total network bytes. [Learn more about text compression](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 30, "numericUnit": "millisecond", "displayValue": "Potential savings of 51 KiB", "metricSavings": {"FCP": 50, "LCP": 50}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedBytes", "valueType": "bytes", "label": "Potential Savings"}], "items": [{"url": "https://app.datoms.io/accounts/login?rd=https://app.datoms.io/enterprise/1/datoms-x/customer-management/", "totalBytes": 63649, "wastedBytes": 52731}], "overallSavingsMs": 30, "overallSavingsBytes": 52731, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 30, "LCP": 30}}}, "guidanceLevel": 3}, "uses-responsive-images": {"id": "uses-responsive-images", "title": "Properly size images", "description": "Serve images that are appropriately-sized to save cellular data and improve load time. [Learn how to size images](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/).", "score": 0.5, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "Potential savings of 4 KiB", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [{"key": "node", "valueType": "node", "label": ""}, {"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Resource Size"}, {"key": "wastedBytes", "valueType": "bytes", "label": "Potential Savings"}], "items": [{"node": {"type": "node", "lhId": "1-6-<PERSON><PERSON>", "path": "1,HTML,1,BODY,0,DIV,1,DIV,0,DIV,0,DIV,0,DIV,2,FORM,13,DIV,1,A,0,IMG", "selector": "form#login_form > div#play_ios_store_icons > a#android > img", "boundingRect": {"top": 627, "bottom": 694, "left": 1013, "right": 1185, "width": 173, "height": 67}, "snippet": "<img src=\"https://static.datoms.io/images/google-play-badge.png\">", "nodeLabel": "form#login_form > div#play_ios_store_icons > a#android > img"}, "url": "https://static.datoms.io/images/google-play-badge.png", "totalBytes": 4904, "wastedBytes": 4552, "wastedPercent": 92.82290978893037}], "overallSavingsMs": 0, "overallSavingsBytes": 4552, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "efficient-animated-content": {"id": "efficient-animated-content", "title": "Use video formats for animated content", "description": "Large GIFs are inefficient for delivering animated content. Consider using MPEG4/WebM videos for animations and PNG/WebP for static images instead of GIF to save network bytes. [Learn more about efficient video formats](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "duplicated-javascript": {"id": "duplicated-javascript", "title": "Remove duplicate modules in JavaScript bundles", "description": "Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity. ", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "legacy-javascript": {"id": "legacy-javascript", "title": "Avoid serving legacy JavaScript to modern browsers", "description": "Polyfills and transforms enable legacy browsers to use new JavaScript features. However, many aren't necessary for modern browsers. For your bundled JavaScript, adopt a modern script deployment strategy using module/nomodule feature detection to reduce the amount of code shipped to modern browsers, while retaining support for legacy browsers. [Learn how to use modern JavaScript](https://web.dev/articles/publish-modern-javascript)", "score": 0.5, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "location", "valueType": "source-location"}, "label": "URL"}, {"key": null, "valueType": "code", "subItemsHeading": {"key": "signal"}, "label": ""}, {"key": "wastedBytes", "valueType": "bytes", "label": "Potential Savings"}], "items": [{"url": "https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/js/intlTelInput.js", "wastedBytes": 0, "subItems": {"type": "subitems", "items": [{"signal": "@babel/plugin-transform-classes", "location": {"type": "source-location", "url": "https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/js/intlTelInput.js", "urlProvider": "network", "line": 41, "column": 37}}]}, "totalBytes": 0}], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "dom-size": {"id": "dom-size", "title": "Avoids an excessive DOM size", "description": "A large DOM will increase memory usage, cause longer [style calculations](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations), and produce costly [layout reflows](https://developers.google.com/speed/articles/reflow). [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 105, "numericUnit": "element", "displayValue": "105 elements", "metricSavings": {"TBT": 0}, "details": {"type": "table", "headings": [{"key": "statistic", "valueType": "text", "label": "Statistic"}, {"key": "node", "valueType": "node", "label": "Element"}, {"key": "value", "valueType": "numeric", "label": "Value"}], "items": [{"statistic": "Total DOM Elements", "value": {"type": "numeric", "granularity": 1, "value": 105}}, {"node": {"type": "node", "lhId": "1-0-DIV", "path": "1,HTML,1,BODY,0,DIV,1,DIV,0,DIV,0,DIV,0,DIV,2,FORM,1,DIV,1,DIV,0,DIV,0,DIV,0,DIV", "selector": "div.iti > div.iti__flag-container > div.iti__selected-flag > div.iti__flag", "boundingRect": {"top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "snippet": "<div class=\"iti__flag iti__in\">", "nodeLabel": "div.iti > div.iti__flag-container > div.iti__selected-flag > div.iti__flag"}, "statistic": "Maximum DOM Depth", "value": {"type": "numeric", "granularity": 1, "value": 12}}, {"node": {"type": "node", "lhId": "1-1-FORM", "path": "1,HTML,1,BODY,0,DIV,1,DIV,0,DIV,0,DIV,0,DIV,2,FORM", "selector": "div.click > div.front > div.form-sec > form#login_form", "boundingRect": {"top": 413, "bottom": 697, "left": 840, "right": 1185, "width": 345, "height": 284}, "snippet": "<form id=\"login_form\" action=\"/\" method=\"post\">", "nodeLabel": "Email or Mobile\nNext\nForgot Password ?"}, "statistic": "Maximum Child Elements", "value": {"type": "numeric", "granularity": 1, "value": 13}}]}, "guidanceLevel": 1}, "no-document-write": {"id": "no-document-write", "title": "Avoids `document.write()`", "description": "For users on slow connections, external scripts dynamically injected via `document.write()` can delay page load by tens of seconds. [Learn how to avoid document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/).", "score": 1, "scoreDisplayMode": "metricSavings", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "uses-http2": {"id": "uses-http2", "title": "Use HTTP/2", "description": "HTTP/2 offers many benefits over HTTP/1.1, including binary headers and multiplexing. [Learn more about HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "metricSavings": {"LCP": 0, "FCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0}, "guidanceLevel": 3}, "uses-passive-event-listeners": {"id": "uses-passive-event-listeners", "title": "Uses passive listeners to improve scrolling performance", "description": "Consider marking your touch and wheel event listeners as `passive` to improve your page's scroll performance. [Learn more about adopting passive event listeners](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/).", "score": 1, "scoreDisplayMode": "metricSavings", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 3}, "bf-cache": {"id": "bf-cache", "title": "Page didn't prevent back/forward cache restoration", "description": "Many navigations are performed by going back to a previous page, or forwards again. The back/forward cache (bfcache) can speed up these return navigations. [Learn more about the bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)", "score": 1, "scoreDisplayMode": "binary", "guidanceLevel": 4}}, "configSettings": {"output": ["json"], "maxWaitForFcp": 30000, "maxWaitForLoad": 45000, "pauseAfterFcpMs": 5250, "pauseAfterLoadMs": 5250, "networkQuietThresholdMs": 5250, "cpuQuietThresholdMs": 5250, "formFactor": "desktop", "throttling": {"rttMs": 150, "throughputKbps": 1638.4, "requestLatencyMs": 562.5, "downloadThroughputKbps": 1474.5600000000002, "uploadThroughputKbps": 675, "cpuSlowdownMultiplier": 4}, "throttlingMethod": "provided", "screenEmulation": {"mobile": false, "width": 1350, "height": 940, "deviceScaleFactor": 1, "disabled": false}, "emulatedUserAgent": "Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "auditMode": false, "gatherMode": false, "clearStorageTypes": ["file_systems", "shader_cache", "service_workers", "cache_storage"], "disableStorageReset": false, "debugNavigation": false, "channel": "node", "usePassiveGathering": false, "disableFullPageScreenshot": false, "skipAboutBlank": false, "blankPage": "about:blank", "ignoreStatusCode": false, "locale": "en-US", "blockedUrlPatterns": null, "additionalTraceCategories": null, "extraHeaders": null, "precomputedLanternData": null, "onlyAudits": null, "onlyCategories": ["performance"], "skipAudits": null}, "categories": {"performance": {"title": "Performance", "supportedModes": ["navigation", "timespan", "snapshot"], "auditRefs": [{"id": "first-contentful-paint", "weight": 10, "group": "metrics", "acronym": "FCP"}, {"id": "largest-contentful-paint", "weight": 25, "group": "metrics", "acronym": "LCP"}, {"id": "total-blocking-time", "weight": 30, "group": "metrics", "acronym": "TBT"}, {"id": "cumulative-layout-shift", "weight": 25, "group": "metrics", "acronym": "CLS"}, {"id": "speed-index", "weight": 10, "group": "metrics", "acronym": "SI"}, {"id": "interactive", "weight": 0, "group": "hidden", "acronym": "TTI"}, {"id": "max-potential-fid", "weight": 0, "group": "hidden"}, {"id": "first-meaningful-paint", "weight": 0, "acronym": "FMP", "group": "hidden"}, {"id": "render-blocking-resources", "weight": 0, "group": "diagnostics"}, {"id": "uses-responsive-images", "weight": 0, "group": "diagnostics"}, {"id": "offscreen-images", "weight": 0, "group": "diagnostics"}, {"id": "unminified-css", "weight": 0, "group": "diagnostics"}, {"id": "unminified-javascript", "weight": 0, "group": "diagnostics"}, {"id": "unused-css-rules", "weight": 0, "group": "diagnostics"}, {"id": "unused-javascript", "weight": 0, "group": "diagnostics"}, {"id": "uses-optimized-images", "weight": 0, "group": "diagnostics"}, {"id": "modern-image-formats", "weight": 0, "group": "diagnostics"}, {"id": "uses-text-compression", "weight": 0, "group": "diagnostics"}, {"id": "uses-rel-preconnect", "weight": 0, "group": "diagnostics"}, {"id": "server-response-time", "weight": 0, "group": "diagnostics"}, {"id": "redirects", "weight": 0, "group": "diagnostics"}, {"id": "uses-http2", "weight": 0, "group": "diagnostics"}, {"id": "efficient-animated-content", "weight": 0, "group": "diagnostics"}, {"id": "duplicated-javascript", "weight": 0, "group": "diagnostics"}, {"id": "legacy-javascript", "weight": 0, "group": "diagnostics"}, {"id": "prioritize-lcp-image", "weight": 0, "group": "diagnostics"}, {"id": "total-byte-weight", "weight": 0, "group": "diagnostics"}, {"id": "uses-long-cache-ttl", "weight": 0, "group": "diagnostics"}, {"id": "dom-size", "weight": 0, "group": "diagnostics"}, {"id": "critical-request-chains", "weight": 0, "group": "diagnostics"}, {"id": "user-timings", "weight": 0, "group": "diagnostics"}, {"id": "bootup-time", "weight": 0, "group": "diagnostics"}, {"id": "mainthread-work-breakdown", "weight": 0, "group": "diagnostics"}, {"id": "font-display", "weight": 0, "group": "diagnostics"}, {"id": "third-party-summary", "weight": 0, "group": "diagnostics"}, {"id": "third-party-facades", "weight": 0, "group": "diagnostics"}, {"id": "largest-contentful-paint-element", "weight": 0, "group": "diagnostics"}, {"id": "lcp-lazy-loaded", "weight": 0, "group": "diagnostics"}, {"id": "layout-shifts", "weight": 0, "group": "diagnostics"}, {"id": "uses-passive-event-listeners", "weight": 0, "group": "diagnostics"}, {"id": "no-document-write", "weight": 0, "group": "diagnostics"}, {"id": "long-tasks", "weight": 0, "group": "diagnostics"}, {"id": "non-composited-animations", "weight": 0, "group": "diagnostics"}, {"id": "unsized-images", "weight": 0, "group": "diagnostics"}, {"id": "viewport", "weight": 0, "group": "diagnostics"}, {"id": "bf-cache", "weight": 0, "group": "diagnostics"}, {"id": "network-requests", "weight": 0, "group": "hidden"}, {"id": "network-rtt", "weight": 0, "group": "hidden"}, {"id": "network-server-latency", "weight": 0, "group": "hidden"}, {"id": "main-thread-tasks", "weight": 0, "group": "hidden"}, {"id": "diagnostics", "weight": 0, "group": "hidden"}, {"id": "metrics", "weight": 0, "group": "hidden"}, {"id": "screenshot-thumbnails", "weight": 0, "group": "hidden"}, {"id": "final-screenshot", "weight": 0, "group": "hidden"}, {"id": "script-treemap-data", "weight": 0, "group": "hidden"}, {"id": "resource-summary", "weight": 0, "group": "hidden"}], "id": "performance", "score": 0.94}}, "categoryGroups": {"metrics": {"title": "Metrics"}, "diagnostics": {"title": "Diagnostics", "description": "More information about the performance of your application. These numbers don't [directly affect](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) the Performance score."}, "a11y-best-practices": {"title": "Best practices", "description": "These items highlight common accessibility best practices."}, "a11y-color-contrast": {"title": "Contrast", "description": "These are opportunities to improve the legibility of your content."}, "a11y-names-labels": {"title": "Names and labels", "description": "These are opportunities to improve the semantics of the controls in your application. This may enhance the experience for users of assistive technology, like a screen reader."}, "a11y-navigation": {"title": "Navigation", "description": "These are opportunities to improve keyboard navigation in your application."}, "a11y-aria": {"title": "ARIA", "description": "These are opportunities to improve the usage of ARIA in your application which may enhance the experience for users of assistive technology, like a screen reader."}, "a11y-language": {"title": "Internationalization and localization", "description": "These are opportunities to improve the interpretation of your content by users in different locales."}, "a11y-audio-video": {"title": "Audio and video", "description": "These are opportunities to provide alternative content for audio and video. This may improve the experience for users with hearing or vision impairments."}, "a11y-tables-lists": {"title": "Tables and lists", "description": "These are opportunities to improve the experience of reading tabular or list data using assistive technology, like a screen reader."}, "seo-mobile": {"title": "Mobile Friendly", "description": "Make sure your pages are mobile friendly so users don’t have to pinch or zoom in order to read the content pages. [Learn how to make pages mobile-friendly](https://developers.google.com/search/mobile-sites/)."}, "seo-content": {"title": "Content Best Practices", "description": "Format your HTML in a way that enables crawlers to better understand your app’s content."}, "seo-crawl": {"title": "Crawling and Indexing", "description": "To appear in search results, crawlers need access to your app."}, "best-practices-trust-safety": {"title": "Trust and Safety"}, "best-practices-ux": {"title": "User Experience"}, "best-practices-browser-compat": {"title": "Browser Compatibility"}, "best-practices-general": {"title": "General"}, "hidden": {"title": ""}}, "stackPacks": [], "entities": [{"name": "datoms.io", "origins": ["https://app.datoms.io", "https://static.datoms.io"], "isFirstParty": true, "isUnrecognized": true}, {"name": "phoenixrobotix.com", "origins": ["https://prstatic.phoenixrobotix.com"], "isUnrecognized": true}, {"name": "Google Fonts", "homepage": "https://fonts.google.com/", "origins": ["https://fonts.googleapis.com", "https://fonts.gstatic.com"], "category": "cdn"}, {"name": "jQuery <PERSON>", "homepage": "https://code.jquery.com/", "origins": ["https://code.jquery.com"], "category": "cdn"}, {"name": "ipinfo.io", "origins": ["https://ipinfo.io"], "category": "utility"}], "fullPageScreenshot": {"screenshot": {"data": "data:image/webp;base64,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", "width": 1350, "height": 940}, "nodes": {"page-0-P": {"id": "", "top": 881, "bottom": 920, "left": 18, "right": 1368, "width": 1350, "height": 39}, "page-1-DIV": {"id": "", "top": 223, "bottom": 717, "left": 810, "right": 1215, "width": 405, "height": 495}, "page-2-DIV": {"id": "", "top": 867, "bottom": 910, "left": 1118, "right": 1320, "width": 203, "height": 43}, "1-0-DIV": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-1-FORM": {"id": "login_form", "top": 413, "bottom": 697, "left": 840, "right": 1185, "width": 345, "height": 284}, "1-2-IMG": {"id": "company_logo", "top": 243, "bottom": 331, "left": 883, "right": 1143, "width": 260, "height": 89}, "1-3-IMG": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-4-IMG": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-5-IMG": {"id": "", "top": 637, "bottom": 684, "left": 855, "right": 998, "width": 143, "height": 47}, "1-6-IMG": {"id": "", "top": 627, "bottom": 694, "left": 1013, "right": 1185, "width": 173, "height": 67}, "1-7-IMG": {"id": "company_logo", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-8-IMG": {"id": "", "top": 867, "bottom": 906, "left": 1118, "right": 1320, "width": 203, "height": 39}, "1-9-DIV": {"id": "", "top": 0, "bottom": 940, "left": 0, "right": 1350, "width": 1350, "height": 940}, "1-10-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-11-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-12-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-13-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-14-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}}}, "timing": {"entries": [{"startTime": 1328.87, "name": "lh:config", "duration": 484.15, "entryType": "measure"}, {"startTime": 1331.66, "name": "lh:config:resolveArtifactsToDefns", "duration": 57.33, "entryType": "measure"}, {"startTime": 1813.18, "name": "lh:runner:gather", "duration": 17420.25, "entryType": "measure"}, {"startTime": 1881.71, "name": "lh:driver:connect", "duration": 7.49, "entryType": "measure"}, {"startTime": 1889.51, "name": "lh:driver:navigate", "duration": 7.26, "entryType": "measure"}, {"startTime": 1897.39, "name": "lh:gather:getBenchmarkIndex", "duration": 1004.58, "entryType": "measure"}, {"startTime": 2902.15, "name": "lh:gather:getVersion", "duration": 0.88, "entryType": "measure"}, {"startTime": 2903.85, "name": "lh:prepare:navigationMode", "duration": 55.23, "entryType": "measure"}, {"startTime": 2918.37, "name": "lh:storage:clearDataForOrigin", "duration": 11.53, "entryType": "measure"}, {"startTime": 2930.09, "name": "lh:storage:clearBrowserCaches", "duration": 26.9, "entryType": "measure"}, {"startTime": 2957.88, "name": "lh:gather:prepareThrottlingAndNetwork", "duration": 1.1, "entryType": "measure"}, {"startTime": 3024.47, "name": "lh:driver:navigate", "duration": 12532.57, "entryType": "measure"}, {"startTime": 15762.75, "name": "lh:computed:NetworkRecords", "duration": 1, "entryType": "measure"}, {"startTime": 15764.33, "name": "lh:gather:getArtifact:DevtoolsLog", "duration": 0.04, "entryType": "measure"}, {"startTime": 15764.39, "name": "lh:gather:getArtifact:Trace", "duration": 0.03, "entryType": "measure"}, {"startTime": 15764.52, "name": "lh:gather:getArtifact:RootCauses", "duration": 397.93, "entryType": "measure"}, {"startTime": 15764.59, "name": "lh:computed:TraceEngineResult", "duration": 383.14, "entryType": "measure"}, {"startTime": 15764.66, "name": "lh:computed:ProcessedTrace", "duration": 17.18, "entryType": "measure"}, {"startTime": 16162.48, "name": "lh:gather:getArtifact:ConsoleMessages", "duration": 0.05, "entryType": "measure"}, {"startTime": 16162.54, "name": "lh:gather:getArtifact:CSSUsage", "duration": 10.14, "entryType": "measure"}, {"startTime": 16172.7, "name": "lh:gather:getArtifact:DOMStats", "duration": 7.78, "entryType": "measure"}, {"startTime": 16180.51, "name": "lh:gather:getArtifact:ImageElements", "duration": 66.69, "entryType": "measure"}, {"startTime": 16247.24, "name": "lh:gather:getArtifact:JsUsage", "duration": 0.18, "entryType": "measure"}, {"startTime": 16247.45, "name": "lh:gather:getArtifact:LinkElements", "duration": 5.18, "entryType": "measure"}, {"startTime": 16252.38, "name": "lh:computed:MainResource", "duration": 0.22, "entryType": "measure"}, {"startTime": 16252.65, "name": "lh:gather:getArtifact:MetaElements", "duration": 17.37, "entryType": "measure"}, {"startTime": 16270.09, "name": "lh:gather:getArtifact:NetworkUserAgent", "duration": 0.1, "entryType": "measure"}, {"startTime": 16270.27, "name": "lh:gather:getArtifact:OptimizedImages", "duration": 1356.23, "entryType": "measure"}, {"startTime": 17626.57, "name": "lh:gather:getArtifact:ResponseCompression", "duration": 4.37, "entryType": "measure"}, {"startTime": 17630.97, "name": "lh:gather:getArtifact:Scripts", "duration": 0.09, "entryType": "measure"}, {"startTime": 17631.08, "name": "lh:gather:getArtifact:SourceMaps", "duration": 0.05, "entryType": "measure"}, {"startTime": 17631.14, "name": "lh:gather:getArtifact:Stacks", "duration": 8.75, "entryType": "measure"}, {"startTime": 17631.23, "name": "lh:gather:collectStacks", "duration": 8.63, "entryType": "measure"}, {"startTime": 17639.95, "name": "lh:gather:getArtifact:Stylesheets", "duration": 11.42, "entryType": "measure"}, {"startTime": 17651.44, "name": "lh:gather:getArtifact:TraceElements", "duration": 101.49, "entryType": "measure"}, {"startTime": 17652.26, "name": "lh:computed:ProcessedNavigation", "duration": 0.74, "entryType": "measure"}, {"startTime": 17653.12, "name": "lh:computed:CumulativeLayoutShift", "duration": 90.71, "entryType": "measure"}, {"startTime": 17744.81, "name": "lh:computed:Responsiveness", "duration": 0.16, "entryType": "measure"}, {"startTime": 17752.95, "name": "lh:gather:getArtifact:ViewportDimensions", "duration": 0.96, "entryType": "measure"}, {"startTime": 17753.95, "name": "lh:gather:getArtifact:devtoolsLogs", "duration": 0.05, "entryType": "measure"}, {"startTime": 17754.04, "name": "lh:gather:getArtifact:traces", "duration": 0.03, "entryType": "measure"}, {"startTime": 17754.08, "name": "lh:gather:getArtifact:FullPageScreenshot", "duration": 1186.83, "entryType": "measure"}, {"startTime": 18940.97, "name": "lh:gather:getArtifact:BFCacheFailures", "duration": 278.7, "entryType": "measure"}, {"startTime": 19233.86, "name": "lh:runner:audit", "duration": 397.03, "entryType": "measure"}, {"startTime": 19233.96, "name": "lh:runner:auditing", "duration": 396.62, "entryType": "measure"}, {"startTime": 19235.27, "name": "lh:audit:viewport", "duration": 2.74, "entryType": "measure"}, {"startTime": 19236.06, "name": "lh:computed:ViewportMeta", "duration": 0.42, "entryType": "measure"}, {"startTime": 19238.64, "name": "lh:audit:first-contentful-paint", "duration": 3.07, "entryType": "measure"}, {"startTime": 19239.43, "name": "lh:computed:First<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 0.31, "entryType": "measure"}, {"startTime": 19242.29, "name": "lh:audit:largest-contentful-paint", "duration": 2.99, "entryType": "measure"}, {"startTime": 19243.64, "name": "lh:computed:Largest<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 0.09, "entryType": "measure"}, {"startTime": 19245.91, "name": "lh:audit:first-meaningful-paint", "duration": 1.65, "entryType": "measure"}, {"startTime": 19248, "name": "lh:audit:speed-index", "duration": 173.55, "entryType": "measure"}, {"startTime": 19248.4, "name": "lh:computed:SpeedIndex", "duration": 172.01, "entryType": "measure"}, {"startTime": 19248.48, "name": "lh:computed:Speedline", "duration": 171.88, "entryType": "measure"}, {"startTime": 19421.58, "name": "lh:audit:screenshot-thumbnails", "duration": 0.49, "entryType": "measure"}, {"startTime": 19422.08, "name": "lh:audit:final-screenshot", "duration": 1.08, "entryType": "measure"}, {"startTime": 19422.18, "name": "lh:computed:Screenshots", "duration": 0.93, "entryType": "measure"}, {"startTime": 19423.58, "name": "lh:audit:total-blocking-time", "duration": 3.23, "entryType": "measure"}, {"startTime": 19424, "name": "lh:computed:TotalBlockingTime", "duration": 1.56, "entryType": "measure"}, {"startTime": 19424.66, "name": "lh:computed:Interactive", "duration": 0.85, "entryType": "measure"}, {"startTime": 19427.26, "name": "lh:audit:max-potential-fid", "duration": 1.92, "entryType": "measure"}, {"startTime": 19427.73, "name": "lh:computed:MaxPotentialFID", "duration": 0.15, "entryType": "measure"}, {"startTime": 19429.84, "name": "lh:audit:cumulative-layout-shift", "duration": 1.63, "entryType": "measure"}, {"startTime": 19431.88, "name": "lh:audit:server-response-time", "duration": 2.26, "entryType": "measure"}, {"startTime": 19434.46, "name": "lh:audit:interactive", "duration": 1.22, "entryType": "measure"}, {"startTime": 19436.3, "name": "lh:audit:user-timings", "duration": 1.81, "entryType": "measure"}, {"startTime": 19436.67, "name": "lh:computed:UserTimings", "duration": 0.53, "entryType": "measure"}, {"startTime": 19438.48, "name": "lh:audit:critical-request-chains", "duration": 7.15, "entryType": "measure"}, {"startTime": 19439.2, "name": "lh:computed:CriticalRequest<PERSON><PERSON>ns", "duration": 3.53, "entryType": "measure"}, {"startTime": 19439.32, "name": "lh:computed:PageDependencyGraph", "duration": 2.67, "entryType": "measure"}, {"startTime": 19445.91, "name": "lh:audit:redirects", "duration": 14.83, "entryType": "measure"}, {"startTime": 19446.98, "name": "lh:computed:LanternInteractive", "duration": 12.37, "entryType": "measure"}, {"startTime": 19447.07, "name": "lh:computed:LanternLargestContentfulPaint", "duration": 9.22, "entryType": "measure"}, {"startTime": 19447.12, "name": "lh:computed:Lantern<PERSON><PERSON>t<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 6.09, "entryType": "measure"}, {"startTime": 19447.29, "name": "lh:computed:LoadSimulator", "duration": 1.61, "entryType": "measure"}, {"startTime": 19447.33, "name": "lh:computed:NetworkAnalysis", "duration": 1.31, "entryType": "measure"}, {"startTime": 19461.19, "name": "lh:audit:mainthread-work-breakdown", "duration": 7.21, "entryType": "measure"}, {"startTime": 19461.88, "name": "lh:computed:MainThreadTasks", "duration": 4.61, "entryType": "measure"}, {"startTime": 19468.88, "name": "lh:audit:bootup-time", "duration": 5.32, "entryType": "measure"}, {"startTime": 19470.53, "name": "lh:computed:TBTImpactTasks", "duration": 1.43, "entryType": "measure"}, {"startTime": 19474.5, "name": "lh:audit:uses-rel-preconnect", "duration": 2.08, "entryType": "measure"}, {"startTime": 19476.93, "name": "lh:audit:font-display", "duration": 1.96, "entryType": "measure"}, {"startTime": 19478.91, "name": "lh:audit:diagnostics", "duration": 0.5, "entryType": "measure"}, {"startTime": 19479.43, "name": "lh:audit:network-requests", "duration": 2.19, "entryType": "measure"}, {"startTime": 19479.63, "name": "lh:computed:EntityClassification", "duration": 1.64, "entryType": "measure"}, {"startTime": 19481.85, "name": "lh:audit:network-rtt", "duration": 0.91, "entryType": "measure"}, {"startTime": 19483.01, "name": "lh:audit:network-server-latency", "duration": 0.85, "entryType": "measure"}, {"startTime": 19483.87, "name": "lh:audit:main-thread-tasks", "duration": 0.18, "entryType": "measure"}, {"startTime": 19484.09, "name": "lh:audit:metrics", "duration": 2.32, "entryType": "measure"}, {"startTime": 19484.22, "name": "lh:computed:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duration": 2.06, "entryType": "measure"}, {"startTime": 19484.46, "name": "lh:computed:FirstContentfulPaintAllFrames", "duration": 0.05, "entryType": "measure"}, {"startTime": 19484.54, "name": "lh:computed:LargestContentfulPaintAllFrames", "duration": 0.05, "entryType": "measure"}, {"startTime": 19484.7, "name": "lh:computed:LCPBreakdown", "duration": 0.96, "entryType": "measure"}, {"startTime": 19484.79, "name": "lh:computed:TimeToFirstByte", "duration": 0.08, "entryType": "measure"}, {"startTime": 19484.88, "name": "lh:computed:LCPImageRecord", "duration": 0.76, "entryType": "measure"}, {"startTime": 19486.43, "name": "lh:audit:resource-summary", "duration": 1.1, "entryType": "measure"}, {"startTime": 19486.54, "name": "lh:computed:ResourceSummary", "duration": 0.33, "entryType": "measure"}, {"startTime": 19487.91, "name": "lh:audit:third-party-summary", "duration": 2.88, "entryType": "measure"}, {"startTime": 19491, "name": "lh:audit:third-party-facades", "duration": 1.43, "entryType": "measure"}, {"startTime": 19492.59, "name": "lh:audit:largest-contentful-paint-element", "duration": 1.25, "entryType": "measure"}, {"startTime": 19494.05, "name": "lh:audit:lcp-lazy-loaded", "duration": 0.92, "entryType": "measure"}, {"startTime": 19495.36, "name": "lh:audit:layout-shifts", "duration": 1.55, "entryType": "measure"}, {"startTime": 19497.1, "name": "lh:audit:long-tasks", "duration": 1.61, "entryType": "measure"}, {"startTime": 19498.88, "name": "lh:audit:non-composited-animations", "duration": 0.83, "entryType": "measure"}, {"startTime": 19499.94, "name": "lh:audit:unsized-images", "duration": 1.16, "entryType": "measure"}, {"startTime": 19501.27, "name": "lh:audit:prioritize-lcp-image", "duration": 0.6, "entryType": "measure"}, {"startTime": 19501.88, "name": "lh:audit:script-treemap-data", "duration": 13.65, "entryType": "measure"}, {"startTime": 19502.1, "name": "lh:computed:<PERSON><PERSON><PERSON><PERSON>", "duration": 0.08, "entryType": "measure"}, {"startTime": 19502.22, "name": "lh:computed:ModuleDuplication", "duration": 0.18, "entryType": "measure"}, {"startTime": 19502.44, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.25, "entryType": "measure"}, {"startTime": 19502.76, "name": "lh:computed:UnusedJavascriptSummary", "duration": 5.4, "entryType": "measure"}, {"startTime": 19508.23, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.49, "entryType": "measure"}, {"startTime": 19508.81, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.08, "entryType": "measure"}, {"startTime": 19508.97, "name": "lh:computed:UnusedJavascriptSummary", "duration": 3.36, "entryType": "measure"}, {"startTime": 19512.4, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.28, "entryType": "measure"}, {"startTime": 19512.77, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.1, "entryType": "measure"}, {"startTime": 19512.96, "name": "lh:computed:UnusedJavascriptSummary", "duration": 2.52, "entryType": "measure"}, {"startTime": 19515.88, "name": "lh:audit:uses-long-cache-ttl", "duration": 2.24, "entryType": "measure"}, {"startTime": 19518.35, "name": "lh:audit:total-byte-weight", "duration": 1.15, "entryType": "measure"}, {"startTime": 19519.67, "name": "lh:audit:offscreen-images", "duration": 6.75, "entryType": "measure"}, {"startTime": 19526.6, "name": "lh:audit:render-blocking-resources", "duration": 5.18, "entryType": "measure"}, {"startTime": 19527.06, "name": "lh:computed:UnusedCSS", "duration": 2.39, "entryType": "measure"}, {"startTime": 19529.48, "name": "lh:computed:NavigationInsights", "duration": 0.08, "entryType": "measure"}, {"startTime": 19529.61, "name": "lh:computed:First<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 0.88, "entryType": "measure"}, {"startTime": 19529.7, "name": "lh:computed:Lantern<PERSON><PERSON>t<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 0.78, "entryType": "measure"}, {"startTime": 19529.78, "name": "lh:computed:LoadSimulator", "duration": 0.07, "entryType": "measure"}, {"startTime": 19531.93, "name": "lh:audit:unminified-css", "duration": 7.33, "entryType": "measure"}, {"startTime": 19539.42, "name": "lh:audit:unminified-javascript", "duration": 31.34, "entryType": "measure"}, {"startTime": 19570.96, "name": "lh:audit:unused-css-rules", "duration": 2.01, "entryType": "measure"}, {"startTime": 19573.14, "name": "lh:audit:unused-javascript", "duration": 3.38, "entryType": "measure"}, {"startTime": 19576.69, "name": "lh:audit:modern-image-formats", "duration": 2.96, "entryType": "measure"}, {"startTime": 19579.98, "name": "lh:audit:uses-optimized-images", "duration": 1.72, "entryType": "measure"}, {"startTime": 19581.96, "name": "lh:audit:uses-text-compression", "duration": 3.19, "entryType": "measure"}, {"startTime": 19585.51, "name": "lh:audit:uses-responsive-images", "duration": 3.05, "entryType": "measure"}, {"startTime": 19586.15, "name": "lh:computed:ImageRecords", "duration": 0.47, "entryType": "measure"}, {"startTime": 19588.74, "name": "lh:audit:efficient-animated-content", "duration": 2.02, "entryType": "measure"}, {"startTime": 19590.92, "name": "lh:audit:duplicated-javascript", "duration": 2.02, "entryType": "measure"}, {"startTime": 19593.19, "name": "lh:audit:legacy-javascript", "duration": 29.29, "entryType": "measure"}, {"startTime": 19622.98, "name": "lh:audit:dom-size", "duration": 2, "entryType": "measure"}, {"startTime": 19625.3, "name": "lh:audit:no-document-write", "duration": 0.98, "entryType": "measure"}, {"startTime": 19626.49, "name": "lh:audit:uses-http2", "duration": 2.14, "entryType": "measure"}, {"startTime": 19628.86, "name": "lh:audit:uses-passive-event-listeners", "duration": 0.75, "entryType": "measure"}, {"startTime": 19629.82, "name": "lh:audit:bf-cache", "duration": 0.76, "entryType": "measure"}, {"startTime": 19630.6, "name": "lh:runner:generate", "duration": 0.29, "entryType": "measure"}], "total": 17817.28}, "i18n": {"rendererFormattedStrings": {"calculatorLink": "See calculator.", "collapseView": "Collapse view", "crcInitialNavigation": "Initial Navigation", "crcLongestDurationLabel": "Maximum critical path latency:", "dropdownCopyJSON": "Copy JSON", "dropdownDarkTheme": "Toggle Dark Theme", "dropdownPrintExpanded": "Print Expanded", "dropdownPrintSummary": "Print Summary", "dropdownSaveGist": "Save as Gist", "dropdownSaveHTML": "Save as HTML", "dropdownSaveJSON": "Save as JSON", "dropdownViewer": "Open in Viewer", "dropdownViewUnthrottledTrace": "View Unthrottled Trace", "errorLabel": "Error!", "errorMissingAuditInfo": "Report error: no audit information", "expandView": "Expand view", "firstPartyChipLabel": "1st party", "footerIssue": "File an issue", "hide": "<PERSON>de", "labDataTitle": "Lab Data", "lsPerformanceCategoryDescription": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analysis of the current page on an emulated mobile network. Values are estimated and may vary.", "manualAuditsGroupTitle": "Additional items to manually check", "notApplicableAuditsGroupTitle": "Not applicable", "openInANewTabTooltip": "Open in a new tab", "opportunityResourceColumnLabel": "Opportunity", "opportunitySavingsColumnLabel": "Estimated Savings", "passedAuditsGroupTitle": "Passed audits", "runtimeAnalysisWindow": "Initial page load", "runtimeAnalysisWindowSnapshot": "Point-in-time snapshot", "runtimeAnalysisWindowTimespan": "User interactions timespan", "runtimeCustom": "Custom throttling", "runtimeDesktopEmulation": "Emulated Desktop", "runtimeMobileEmulation": "Emulated Moto G Power", "runtimeNoEmulation": "No emulation", "runtimeSettingsAxeVersion": "Axe version", "runtimeSettingsBenchmark": "Unthrottled CPU/Memory Power", "runtimeSettingsCPUThrottling": "CPU throttling", "runtimeSettingsDevice": "<PERSON><PERSON>", "runtimeSettingsNetworkThrottling": "Network throttling", "runtimeSettingsScreenEmulation": "Screen emulation", "runtimeSettingsUANetwork": "User agent (network)", "runtimeSingleLoad": "Single page session", "runtimeSingleLoadTooltip": "This data is taken from a single page session, as opposed to field data summarizing many sessions.", "runtimeSlow4g": "Slow 4G throttling", "runtimeUnknown": "Unknown", "show": "Show", "showRelevantAudits": "Show audits relevant to:", "snippetCollapseButtonLabel": "Collapse snippet", "snippetExpandButtonLabel": "Expand snippet", "thirdPartyResourcesLabel": "Show 3rd-party resources", "throttlingProvided": "Provided by environment", "toplevelWarningsMessage": "There were issues affecting this run of Lighthouse:", "unattributable": "Unattributable", "varianceDisclaimer": "Values are estimated and may vary. The [performance score is calculated](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) directly from these metrics.", "viewTraceLabel": "View Trace", "viewTreemapLabel": "View Treemap", "warningAuditsGroupTitle": "Passed audits but with warnings", "warningHeader": "Warnings: "}, "icuMessagePaths": {"core/gather/driver/navigation.js | warningRedirected": [{"values": {"requested": "https://app.datoms.io/enterprise/1/datoms-x/customer-management/", "final": "https://app.datoms.io/accounts/login?rd=https://app.datoms.io/enterprise/1/datoms-x/customer-management/"}, "path": "runWarnings[0]"}], "core/audits/viewport.js | title": ["audits.viewport.title"], "core/audits/viewport.js | description": ["audits.viewport.description"], "core/lib/i18n/i18n.js | firstContentfulPaintMetric": ["audits[first-contentful-paint].title"], "core/audits/metrics/first-contentful-paint.js | description": ["audits[first-contentful-paint].description"], "core/lib/i18n/i18n.js | seconds": [{"values": {"timeInMs": 977.632}, "path": "audits[first-contentful-paint].displayValue"}, {"values": {"timeInMs": 1377.676}, "path": "audits[largest-contentful-paint].displayValue"}, {"values": {"timeInMs": 971}, "path": "audits[speed-index].displayValue"}, {"values": {"timeInMs": 977.632}, "path": "audits.interactive.displayValue"}, {"values": {"timeInMs": 127.**************}, "path": "audits[mainthread-work-breakdown].displayValue"}, {"values": {"timeInMs": 9.875}, "path": "audits[bootup-time].displayValue"}], "core/lib/i18n/i18n.js | largestContentfulPaintMetric": ["audits[largest-contentful-paint].title"], "core/audits/metrics/largest-contentful-paint.js | description": ["audits[largest-contentful-paint].description"], "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": ["audits[first-meaningful-paint].title"], "core/audits/metrics/first-meaningful-paint.js | description": ["audits[first-meaningful-paint].description"], "core/lib/i18n/i18n.js | speedIndexMetric": ["audits[speed-index].title"], "core/audits/metrics/speed-index.js | description": ["audits[speed-index].description"], "core/lib/i18n/i18n.js | totalBlockingTimeMetric": ["audits[total-blocking-time].title"], "core/audits/metrics/total-blocking-time.js | description": ["audits[total-blocking-time].description"], "core/lib/i18n/i18n.js | ms": [{"values": {"timeInMs": 0}, "path": "audits[total-blocking-time].displayValue"}, {"values": {"timeInMs": 16}, "path": "audits[max-potential-fid].displayValue"}, {"values": {"timeInMs": 111.73}, "path": "audits[network-rtt].displayValue"}, {"values": {"timeInMs": 280.366}, "path": "audits[network-server-latency].displayValue"}, {"values": {"timeInMs": 1377.676}, "path": "audits[largest-contentful-paint-element].displayValue"}], "core/lib/i18n/i18n.js | maxPotentialFIDMetric": ["audits[max-potential-fid].title"], "core/audits/metrics/max-potential-fid.js | description": ["audits[max-potential-fid].description"], "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": ["audits[cumulative-layout-shift].title"], "core/audits/metrics/cumulative-layout-shift.js | description": ["audits[cumulative-layout-shift].description"], "core/audits/server-response-time.js | title": ["audits[server-response-time].title"], "core/audits/server-response-time.js | description": ["audits[server-response-time].description"], "core/audits/server-response-time.js | displayValue": [{"values": {"timeInMs": 140.749}, "path": "audits[server-response-time].displayValue"}], "core/lib/i18n/i18n.js | columnURL": ["audits[server-response-time].details.headings[0].label", "audits.redirects.details.headings[0].label", "audits[bootup-time].details.headings[0].label", "audits[uses-rel-preconnect].details.headings[0].label", "audits[network-rtt].details.headings[0].label", "audits[network-server-latency].details.headings[0].label", "audits[unsized-images].details.headings[1].label", "audits[uses-long-cache-ttl].details.headings[0].label", "audits[total-byte-weight].details.headings[0].label", "audits[offscreen-images].details.headings[1].label", "audits[render-blocking-resources].details.headings[0].label", "audits[unminified-javascript].details.headings[0].label", "audits[unused-javascript].details.headings[0].label", "audits[uses-text-compression].details.headings[0].label", "audits[uses-responsive-images].details.headings[1].label", "audits[legacy-javascript].details.headings[0].label"], "core/lib/i18n/i18n.js | columnTimeSpent": ["audits[server-response-time].details.headings[1].label", "audits.redirects.details.headings[1].label", "audits[mainthread-work-breakdown].details.headings[1].label", "audits[network-rtt].details.headings[1].label", "audits[network-server-latency].details.headings[1].label"], "core/lib/i18n/i18n.js | interactiveMetric": ["audits.interactive.title"], "core/audits/metrics/interactive.js | description": ["audits.interactive.description"], "core/audits/user-timings.js | title": ["audits[user-timings].title"], "core/audits/user-timings.js | description": ["audits[user-timings].description"], "core/audits/critical-request-chains.js | title": ["audits[critical-request-chains].title"], "core/audits/critical-request-chains.js | description": ["audits[critical-request-chains].description"], "core/audits/critical-request-chains.js | displayValue": [{"values": {"itemCount": 5}, "path": "audits[critical-request-chains].displayValue"}], "core/audits/redirects.js | title": ["audits.redirects.title"], "core/audits/redirects.js | description": ["audits.redirects.description"], "core/lib/i18n/i18n.js | displayValueMsSavings": [{"values": {"wastedMs": 258.*************}, "path": "audits.redirects.displayValue"}, {"values": {"wastedMs": 223.46}, "path": "audits[uses-rel-preconnect].displayValue"}, {"values": {"wastedMs": 706}, "path": "audits[render-blocking-resources].displayValue"}], "core/audits/mainthread-work-breakdown.js | title": ["audits[mainthread-work-breakdown].title"], "core/audits/mainthread-work-breakdown.js | description": ["audits[mainthread-work-breakdown].description"], "core/audits/mainthread-work-breakdown.js | columnCategory": ["audits[mainthread-work-breakdown].details.headings[0].label"], "core/audits/bootup-time.js | title": ["audits[bootup-time].title"], "core/audits/bootup-time.js | description": ["audits[bootup-time].description"], "core/audits/bootup-time.js | columnTotal": ["audits[bootup-time].details.headings[1].label"], "core/audits/bootup-time.js | columnScriptEval": ["audits[bootup-time].details.headings[2].label"], "core/audits/bootup-time.js | columnScriptParse": ["audits[bootup-time].details.headings[3].label"], "core/audits/uses-rel-preconnect.js | title": ["audits[uses-rel-preconnect].title"], "core/audits/uses-rel-preconnect.js | description": ["audits[uses-rel-preconnect].description"], "core/lib/i18n/i18n.js | columnWastedBytes": ["audits[uses-rel-preconnect].details.headings[1].label", "audits[offscreen-images].details.headings[3].label", "audits[render-blocking-resources].details.headings[2].label", "audits[unminified-javascript].details.headings[2].label", "audits[unused-javascript].details.headings[2].label", "audits[uses-text-compression].details.headings[2].label", "audits[uses-responsive-images].details.headings[3].label", "audits[legacy-javascript].details.headings[2].label"], "core/audits/font-display.js | title": ["audits[font-display].title"], "core/audits/font-display.js | description": ["audits[font-display].description"], "core/audits/network-rtt.js | title": ["audits[network-rtt].title"], "core/audits/network-rtt.js | description": ["audits[network-rtt].description"], "core/audits/network-server-latency.js | title": ["audits[network-server-latency].title"], "core/audits/network-server-latency.js | description": ["audits[network-server-latency].description"], "core/lib/i18n/i18n.js | columnResourceType": ["audits[resource-summary].details.headings[0].label"], "core/lib/i18n/i18n.js | columnRequests": ["audits[resource-summary].details.headings[1].label"], "core/lib/i18n/i18n.js | columnTransferSize": ["audits[resource-summary].details.headings[2].label", "audits[third-party-summary].details.headings[1].label", "audits[uses-long-cache-ttl].details.headings[2].label", "audits[total-byte-weight].details.headings[1].label", "audits[render-blocking-resources].details.headings[1].label", "audits[unminified-javascript].details.headings[1].label", "audits[unused-javascript].details.headings[1].label", "audits[uses-text-compression].details.headings[1].label"], "core/lib/i18n/i18n.js | totalResourceType": ["audits[resource-summary].details.items[0].label"], "core/lib/i18n/i18n.js | imageResourceType": ["audits[resource-summary].details.items[1].label"], "core/lib/i18n/i18n.js | scriptResourceType": ["audits[resource-summary].details.items[2].label"], "core/lib/i18n/i18n.js | documentResourceType": ["audits[resource-summary].details.items[3].label"], "core/lib/i18n/i18n.js | fontResourceType": ["audits[resource-summary].details.items[4].label"], "core/lib/i18n/i18n.js | stylesheetResourceType": ["audits[resource-summary].details.items[5].label"], "core/lib/i18n/i18n.js | otherResourceType": ["audits[resource-summary].details.items[6].label"], "core/lib/i18n/i18n.js | mediaResourceType": ["audits[resource-summary].details.items[7].label"], "core/lib/i18n/i18n.js | thirdPartyResourceType": ["audits[resource-summary].details.items[8].label"], "core/audits/third-party-summary.js | title": ["audits[third-party-summary].title"], "core/audits/third-party-summary.js | description": ["audits[third-party-summary].description"], "core/audits/third-party-summary.js | displayValue": [{"values": {"timeInMs": 0}, "path": "audits[third-party-summary].displayValue"}], "core/audits/third-party-summary.js | columnThirdParty": ["audits[third-party-summary].details.headings[0].label"], "core/lib/i18n/i18n.js | columnBlockingTime": ["audits[third-party-summary].details.headings[2].label"], "core/audits/third-party-facades.js | title": ["audits[third-party-facades].title"], "core/audits/third-party-facades.js | description": ["audits[third-party-facades].description"], "core/audits/largest-contentful-paint-element.js | title": ["audits[largest-contentful-paint-element].title"], "core/audits/largest-contentful-paint-element.js | description": ["audits[largest-contentful-paint-element].description"], "core/lib/i18n/i18n.js | columnElement": ["audits[largest-contentful-paint-element].details.items[0].headings[0].label", "audits[layout-shifts].details.headings[0].label", "audits[dom-size].details.headings[1].label"], "core/audits/largest-contentful-paint-element.js | columnPhase": ["audits[largest-contentful-paint-element].details.items[1].headings[0].label"], "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": ["audits[largest-contentful-paint-element].details.items[1].headings[1].label"], "core/audits/largest-contentful-paint-element.js | columnTiming": ["audits[largest-contentful-paint-element].details.items[1].headings[2].label"], "core/audits/largest-contentful-paint-element.js | itemTTFB": ["audits[largest-contentful-paint-element].details.items[1].items[0].phase"], "core/audits/largest-contentful-paint-element.js | itemLoadDelay": ["audits[largest-contentful-paint-element].details.items[1].items[1].phase"], "core/audits/largest-contentful-paint-element.js | itemLoadTime": ["audits[largest-contentful-paint-element].details.items[1].items[2].phase"], "core/audits/largest-contentful-paint-element.js | itemRenderDelay": ["audits[largest-contentful-paint-element].details.items[1].items[3].phase"], "core/audits/lcp-lazy-loaded.js | title": ["audits[lcp-lazy-loaded].title"], "core/audits/lcp-lazy-loaded.js | description": ["audits[lcp-lazy-loaded].description"], "core/audits/layout-shifts.js | title": ["audits[layout-shifts].title"], "core/audits/layout-shifts.js | description": ["audits[layout-shifts].description"], "core/audits/layout-shifts.js | displayValueShiftsFound": [{"values": {"shiftCount": 2}, "path": "audits[layout-shifts].displayValue"}], "core/audits/layout-shifts.js | columnScore": ["audits[layout-shifts].details.headings[1].label"], "core/audits/layout-shifts.js | rootCauseFontChanges": ["audits[layout-shifts].details.items[1].subItems.items[0].cause", "audits[layout-shifts].details.items[1].subItems.items[1].cause"], "core/audits/layout-shifts.js | rootCauseRenderBlockingRequest": ["audits[layout-shifts].details.items[1].subItems.items[2].cause", "audits[layout-shifts].details.items[1].subItems.items[3].cause", "audits[layout-shifts].details.items[1].subItems.items[4].cause", "audits[layout-shifts].details.items[1].subItems.items[5].cause"], "core/audits/long-tasks.js | title": ["audits[long-tasks].title"], "core/audits/long-tasks.js | description": ["audits[long-tasks].description"], "core/audits/non-composited-animations.js | title": ["audits[non-composited-animations].title"], "core/audits/non-composited-animations.js | description": ["audits[non-composited-animations].description"], "core/audits/unsized-images.js | failureTitle": ["audits[unsized-images].title"], "core/audits/unsized-images.js | description": ["audits[unsized-images].description"], "core/audits/prioritize-lcp-image.js | title": ["audits[prioritize-lcp-image].title"], "core/audits/prioritize-lcp-image.js | description": ["audits[prioritize-lcp-image].description"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": ["audits[uses-long-cache-ttl].title"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": ["audits[uses-long-cache-ttl].description"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": [{"values": {"itemCount": 2}, "path": "audits[uses-long-cache-ttl].displayValue"}], "core/lib/i18n/i18n.js | columnCacheTTL": ["audits[uses-long-cache-ttl].details.headings[1].label"], "core/audits/byte-efficiency/total-byte-weight.js | title": ["audits[total-byte-weight].title"], "core/audits/byte-efficiency/total-byte-weight.js | description": ["audits[total-byte-weight].description"], "core/audits/byte-efficiency/total-byte-weight.js | displayValue": [{"values": {"totalBytes": 442065}, "path": "audits[total-byte-weight].displayValue"}], "core/audits/byte-efficiency/offscreen-images.js | title": ["audits[offscreen-images].title"], "core/audits/byte-efficiency/offscreen-images.js | description": ["audits[offscreen-images].description"], "core/lib/i18n/i18n.js | displayValueByteSavings": [{"values": {"wastedBytes": 3507}, "path": "audits[offscreen-images].displayValue"}, {"values": {"wastedBytes": 9899}, "path": "audits[unminified-javascript].displayValue"}, {"values": {"wastedBytes": 38357}, "path": "audits[unused-javascript].displayValue"}, {"values": {"wastedBytes": 52731}, "path": "audits[uses-text-compression].displayValue"}, {"values": {"wastedBytes": 4552}, "path": "audits[uses-responsive-images].displayValue"}], "core/lib/i18n/i18n.js | columnResourceSize": ["audits[offscreen-images].details.headings[2].label", "audits[uses-responsive-images].details.headings[2].label"], "core/audits/byte-efficiency/render-blocking-resources.js | title": ["audits[render-blocking-resources].title"], "core/audits/byte-efficiency/render-blocking-resources.js | description": ["audits[render-blocking-resources].description"], "core/audits/byte-efficiency/unminified-css.js | title": ["audits[unminified-css].title"], "core/audits/byte-efficiency/unminified-css.js | description": ["audits[unminified-css].description"], "core/audits/byte-efficiency/unminified-javascript.js | title": ["audits[unminified-javascript].title"], "core/audits/byte-efficiency/unminified-javascript.js | description": ["audits[unminified-javascript].description"], "core/audits/byte-efficiency/unused-css-rules.js | title": ["audits[unused-css-rules].title"], "core/audits/byte-efficiency/unused-css-rules.js | description": ["audits[unused-css-rules].description"], "core/audits/byte-efficiency/unused-javascript.js | title": ["audits[unused-javascript].title"], "core/audits/byte-efficiency/unused-javascript.js | description": ["audits[unused-javascript].description"], "core/audits/byte-efficiency/modern-image-formats.js | title": ["audits[modern-image-formats].title"], "core/audits/byte-efficiency/modern-image-formats.js | description": ["audits[modern-image-formats].description"], "core/audits/byte-efficiency/uses-optimized-images.js | title": ["audits[uses-optimized-images].title"], "core/audits/byte-efficiency/uses-optimized-images.js | description": ["audits[uses-optimized-images].description"], "core/audits/byte-efficiency/uses-text-compression.js | title": ["audits[uses-text-compression].title"], "core/audits/byte-efficiency/uses-text-compression.js | description": ["audits[uses-text-compression].description"], "core/audits/byte-efficiency/uses-responsive-images.js | title": ["audits[uses-responsive-images].title"], "core/audits/byte-efficiency/uses-responsive-images.js | description": ["audits[uses-responsive-images].description"], "core/audits/byte-efficiency/efficient-animated-content.js | title": ["audits[efficient-animated-content].title"], "core/audits/byte-efficiency/efficient-animated-content.js | description": ["audits[efficient-animated-content].description"], "core/audits/byte-efficiency/duplicated-javascript.js | title": ["audits[duplicated-javascript].title"], "core/audits/byte-efficiency/duplicated-javascript.js | description": ["audits[duplicated-javascript].description"], "core/audits/byte-efficiency/legacy-javascript.js | title": ["audits[legacy-javascript].title"], "core/audits/byte-efficiency/legacy-javascript.js | description": ["audits[legacy-javascript].description"], "core/audits/dobetterweb/dom-size.js | title": ["audits[dom-size].title"], "core/audits/dobetterweb/dom-size.js | description": ["audits[dom-size].description"], "core/audits/dobetterweb/dom-size.js | displayValue": [{"values": {"itemCount": 105}, "path": "audits[dom-size].displayValue"}], "core/audits/dobetterweb/dom-size.js | columnStatistic": ["audits[dom-size].details.headings[0].label"], "core/audits/dobetterweb/dom-size.js | columnValue": ["audits[dom-size].details.headings[2].label"], "core/audits/dobetterweb/dom-size.js | statisticDOMElements": ["audits[dom-size].details.items[0].statistic"], "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": ["audits[dom-size].details.items[1].statistic"], "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": ["audits[dom-size].details.items[2].statistic"], "core/audits/dobetterweb/no-document-write.js | title": ["audits[no-document-write].title"], "core/audits/dobetterweb/no-document-write.js | description": ["audits[no-document-write].description"], "core/audits/dobetterweb/uses-http2.js | title": ["audits[uses-http2].title"], "core/audits/dobetterweb/uses-http2.js | description": ["audits[uses-http2].description"], "core/audits/dobetterweb/uses-passive-event-listeners.js | title": ["audits[uses-passive-event-listeners].title"], "core/audits/dobetterweb/uses-passive-event-listeners.js | description": ["audits[uses-passive-event-listeners].description"], "core/audits/bf-cache.js | title": ["audits[bf-cache].title"], "core/audits/bf-cache.js | description": ["audits[bf-cache].description"], "core/config/default-config.js | performanceCategoryTitle": ["categories.performance.title"], "core/config/default-config.js | metricGroupTitle": ["categoryGroups.metrics.title"], "core/config/default-config.js | diagnosticsGroupTitle": ["categoryGroups.diagnostics.title"], "core/config/default-config.js | diagnosticsGroupDescription": ["categoryGroups.diagnostics.description"], "core/config/default-config.js | a11yBestPracticesGroupTitle": ["categoryGroups[a11y-best-practices].title"], "core/config/default-config.js | a11yBestPracticesGroupDescription": ["categoryGroups[a11y-best-practices].description"], "core/config/default-config.js | a11yColorContrastGroupTitle": ["categoryGroups[a11y-color-contrast].title"], "core/config/default-config.js | a11yColorContrastGroupDescription": ["categoryGroups[a11y-color-contrast].description"], "core/config/default-config.js | a11yNamesLabelsGroupTitle": ["categoryGroups[a11y-names-labels].title"], "core/config/default-config.js | a11yNamesLabelsGroupDescription": ["categoryGroups[a11y-names-labels].description"], "core/config/default-config.js | a11yNavigationGroupTitle": ["categoryGroups[a11y-navigation].title"], "core/config/default-config.js | a11yNavigationGroupDescription": ["categoryGroups[a11y-navigation].description"], "core/config/default-config.js | a11yAriaGroupTitle": ["categoryGroups[a11y-aria].title"], "core/config/default-config.js | a11yAriaGroupDescription": ["categoryGroups[a11y-aria].description"], "core/config/default-config.js | a11yLanguageGroupTitle": ["categoryGroups[a11y-language].title"], "core/config/default-config.js | a11yLanguageGroupDescription": ["categoryGroups[a11y-language].description"], "core/config/default-config.js | a11yAudioVideoGroupTitle": ["categoryGroups[a11y-audio-video].title"], "core/config/default-config.js | a11yAudioVideoGroupDescription": ["categoryGroups[a11y-audio-video].description"], "core/config/default-config.js | a11yTablesListsVideoGroupTitle": ["categoryGroups[a11y-tables-lists].title"], "core/config/default-config.js | a11yTablesListsVideoGroupDescription": ["categoryGroups[a11y-tables-lists].description"], "core/config/default-config.js | seoMobileGroupTitle": ["categoryGroups[seo-mobile].title"], "core/config/default-config.js | seoMobileGroupDescription": ["categoryGroups[seo-mobile].description"], "core/config/default-config.js | seoContentGroupTitle": ["categoryGroups[seo-content].title"], "core/config/default-config.js | seoContentGroupDescription": ["categoryGroups[seo-content].description"], "core/config/default-config.js | seoCrawlingGroupTitle": ["categoryGroups[seo-crawl].title"], "core/config/default-config.js | seoCrawlingGroupDescription": ["categoryGroups[seo-crawl].description"], "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": ["categoryGroups[best-practices-trust-safety].title"], "core/config/default-config.js | bestPracticesUXGroupTitle": ["categoryGroups[best-practices-ux].title"], "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": ["categoryGroups[best-practices-browser-compat].title"], "core/config/default-config.js | bestPracticesGeneralGroupTitle": ["categoryGroups[best-practices-general].title"]}}}