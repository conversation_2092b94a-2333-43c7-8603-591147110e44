const gulp = require("gulp"),
  fs = require("fs"),
  string_replace = require("gulp-replace"),
  rename = require("gulp-rename"),
  gutil = require("gulp-util"),
  jsonEdit = require("gulp-json-editor"),
  ftp = require("vinyl-ftp"),
  packageJson = require("./package.json"),
  xmlpoke = require("gulp-xmlpoke"),
  less = require("gulp-less"),
  LessAutoprefix = require("less-plugin-autoprefix"),
  autoprefix = new LessAutoprefix({ browsers: ["last 2 versions"] }),
  sourcemaps = require("gulp-sourcemaps");

require("regenerator-runtime/runtime");

const { configSdk, updateWebApp } = require("@datoms/js-sdk");

/*gulp.task('insert_error_tracking', () => {
  return gulp
    .src('src/index.js')
    .pipe(editFile((content, fileInfo) => {
      console.log('fileInfo_',fileInfo);
      console.log('content_',content);
      // Edit file content here, eg:
      const newContent = `import React from 'react';\nimport ReactDOM from 'react-dom';\nimport App from './App.js';\nimport { GlobalConfig } from './GlobalConfig';\nimport * as Sentry from "@sentry/react";\nimport { BrowserTracing } from "@sentry/tracing";\nimport * as serviceWorker from './serviceWorker';\nSentry.init({
  dsn: "https://<EMAIL>/4504631556964352",
  integrations: [new BrowserTracing()],
  tracesSampleRate: 1.0,
});\nReactDOM.render(<App t={GlobalConfig}/>, document.getElementById('root'));\nserviceWorker.unregister();`;
  console.log('new_content_', newContent);
      return newContent;
    }))
    .pipe(gulp.dest('src/'));
});*/

gulp.task("insert_error_tracking", () => {
  let src = "./src/index.js";
  return gulp
    .src(src)
    .pipe(
      string_replace(
        "/* ##PR_INSERT_ERROR_TRACKING## */",
        `import * as Sentry from "@sentry/react";\nimport { BrowserTracing } from "@sentry/tracing";\nSentry.init({
  dsn: "https://<EMAIL>/4504631556964352",
  integrations: [new BrowserTracing()],
  tracesSampleRate: 1.0,
});`,
      ),
    )
    .pipe(gulp.dest("src/"));
});

gulp.task("insert_mix_panel_init", () => {
  let src = "./src/index.js";
  return gulp
    .src(src)
    .pipe(
      string_replace(
        "/* ##PR_INSERT_MIX_PANEL_INIT## */",
        `window.mixpanel.init("448c1893fd8657d024dea174b99759a2", {'debug': true, 'track_pageview': true, 'persistence': 'localStorage' });`,
      ),
    )
    .pipe(gulp.dest("src/"));
});

gulp.task("remove_app_version", function () {
  return gulp
    .src("./mobile_app/config.xml")
    .pipe(
      xmlpoke({
        replacements: [
          {
            xpath: "/w:widget/@version",
            value: "",
            namespaces: {
              w: "http://www.w3.org/ns/widgets",
              cdv: "http://cordova.apache.org/ns/1.0",
            },
          },
        ],
      }),
    )
    .pipe(gulp.dest("./mobile_app/"));
});

gulp.task("set_app_version", function () {
  return gulp
    .src("./mobile_app/config.xml")
    .pipe(
      xmlpoke({
        replacements: [
          {
            xpath: "/w:widget/@version",
            value: packageJson.version,
            namespaces: {
              w: "http://www.w3.org/ns/widgets",
              cdv: "http://cordova.apache.org/ns/1.0",
            },
          },
        ],
      }),
    )
    .pipe(gulp.dest("./mobile_app/"));
});

gulp.task(
  "update_mobile_app_version",
  gulp.series("remove_app_version", "set_app_version"),
);

// gulp.task("update_homepage", () => {
//   const homepage =
//     "https://" +
//     process.env.DATOMS_WEBAPP_BUILD_STATIC_SERVER +
//     process.env.DATOMS_WEBAPP_BUILD_APP_SLUG +
//     "v" +
//     packageJson.version +
//     "/";
//   console.log("homepage -> ", homepage);
//   console.log("gulp_process_env -> ", process.env);
//   return gulp
//     .src("./package.json")
//     .pipe(
//       jsonEdit({
//         homepage: homepage,
//       }),
//     )
//     .pipe(gulp.dest("./"));
// });

// updates the homepage for review deployment to production environment (next.datoms.io)
// gulp.task("update_review_production_homepage", () => {
//   const homepage =
//     "https://" +
//     process.env.DATOMS_WEBAPP_BUILD_STATIC_SERVER +
//     process.env.DATOMS_WEBAPP_BUILD_APP_SLUG +
//     "next/";
//   console.log("homepage -> ", homepage);
//   return gulp
//     .src("./package.json")
//     .pipe(
//       jsonEdit({
//         homepage: homepage,
//       }),
//     )
//     .pipe(gulp.dest("./"));
// });

// gulp.task("update_review_homepage", () => {
//   const homepage =
//     "https://" +
//     process.env.DATOMS_WEBAPP_BUILD_STATIC_SERVER +
//     process.env.DATOMS_WEBAPP_BUILD_APP_SLUG +
//     process.env.CI_COMMIT_REF_NAME +
//     "/";
//   console.log("homepage -> ", homepage);
//   return gulp
//     .src("./package.json")
//     .pipe(
//       jsonEdit({
//         homepage: homepage,
//       }),
//     )
//     .pipe(gulp.dest("./"));
// });

// gulp.task("update_cypress_url", () => {
//   const updatedUrl =
//     "https://" +
//     process.env.CI_COMMIT_REF_NAME +
//     ".review.datoms.io/enterprise/895/dg-monitoring/dashboard/?type=all_things";
//   console.log("updatedUrl -> ", updatedUrl);
//   return gulp
//     .src("./cypress.json")
//     .pipe(
//       jsonEdit({
//         env: { url: updatedUrl },
//       }),
//     )
//     .pipe(gulp.dest("./build"));
// });

gulp.task("update_version", () => {
  const version = packageJson.version + "-staging";
  console.log("version -> ", version);
  return gulp
    .src("./package.json")
    .pipe(
      jsonEdit({
        version: version,
      }),
    )
    .pipe(gulp.dest("./"));
});

/*gulp.task('edit_homepage_for_staging', () => {
	const homepage =
		'https://' +
		process.env.DATOMS_WEBAPP_BUILD_STATIC_SERVER +
		process.env.DATOMS_WEBAPP_BUILD_APP_SLUG +
		'v' +
		packageJson.version +
		'/';
	return gulp
		.src('./package.json')
		.pipe(
			jsonEdit({
				homepage: homepage,
			})
		)
		.pipe(gulp.dest('./'));
});

gulp.task('edit_homepage_for_webapp', () => {
	return gulp
		.src('./package.json')
		.pipe(
			jsonEdit({
				homepage:
					'https://prstatic.phoenixrobotix.com/-/static/datoms/dg-monitoring/v0.1.0/',
			})
		)
		.pipe(gulp.dest('./'));
});*/

// gulp.task("edit_homepage_for_mobile", () => {
//   return gulp
//     .src("./package.json")
//     .pipe(
//       jsonEdit({
//         homepage: ".",
//       }),
//     )
//     .pipe(gulp.dest("./"));
// });

// add necessary cordova scripts for mobile build
gulp.task("add_cordova_script_to_index_file", () => {
  let src = "./build/index.html";
  return gulp
    .src(src)
    .pipe(
      string_replace(
        "<!-- ##PR_REPLACE_BY_CORDOVA_FRAMEWORK_FILES## -->",
        '<script type="text/javascript" src="cordova.js"></script><script type="text/javascript" src="js/jquery-2.1.3.min.js"></script><script type="text/javascript" src="js/stacktrace.min.js"></script><script type="text/javascript" src="js/index.js"></script>',
      ),
    )
    .pipe(gulp.dest("./build"));
});

gulp.task("remove_mixpanel_in_public_index", () => {
  let src = "./public/index.html";
  return gulp
    .src(src)
    .pipe(
      string_replace(
        '<script type="text/javascript">(function(f,b){if(!b.__SV){var e,g,i,h;window.mixpanel=b;b._i=[];b.init=function(e,f,c){function g(a,d){var b=d.split(".");2==b.length&&(a=a[b[0]],d=b[1]);a[d]=function(){a.push([d].concat(Array.prototype.slice.call(arguments,0)))}}var a=b;"undefined"!==typeof c?a=b[c]=[]:c="mixpanel";a.people=a.people||[];a.toString=function(a){var d="mixpanel";"mixpanel"!==c&&(d+="."+c);a||(d+=" (stub)");return d};a.people.toString=function(){return a.toString(1)+".people (stub)"};i="disable time_event track track_pageview track_links track_forms track_with_groups add_group set_group remove_group register register_once alias unregister identify name_tag set_config reset opt_in_tracking opt_out_tracking has_opted_in_tracking has_opted_out_tracking clear_opt_in_out_tracking start_batch_senders people.set people.set_once people.unset people.increment people.append people.union people.track_charge people.clear_charges people.delete_user people.remove".split(" ");for(h=0;h<i.length;h++)g(a,i[h]);var j="set set_once union unset remove delete".split(" ");a.get_group=function(){function b(c){d[c]=function(){call2_args=arguments;call2=[c].concat(Array.prototype.slice.call(call2_args,0));a.push([e,call2])}}for(var d={},e=["get_group"].concat(Array.prototype.slice.call(arguments,0)),c=0;c<j.length;c++)b(j[c]);return d};b._i.push([e,f,c])};b.__SV=1.2;e=f.createElement("script");e.type="text/javascript";e.async=!0;e.src="undefined"!==typeof MIXPANEL_CUSTOM_LIB_URL?MIXPANEL_CUSTOM_LIB_URL:"file:"===f.location.protocol&&"//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js".match(/^///)?"https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js":"//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js";g=f.getElementsByTagName("script")[0];g.parentNode.insertBefore(e,g)}})(document,window.mixpanel||[]);</script>',
        "",
      ),
    )
    .pipe(gulp.dest("./public"));
});
gulp.task("add_mixpanel_in_index", () => {
  let src = "./build/index.html";
  return gulp
    .src(src)
    .pipe(
      string_replace(
        "<!-- ##PR_REPLACE_BY_MIXPANEL_SCRIPT## -->",
        '<script type="text/javascript">(function(f,b){if(!b.__SV){var e,g,i,h;window.mixpanel=b;b._i=[];b.init=function(e,f,c){function g(a,d){var b=d.split(".");2==b.length&&(a=a[b[0]],d=b[1]);a[d]=function(){a.push([d].concat(Array.prototype.slice.call(arguments,0)))}}var a=b;"undefined"!==typeof c?a=b[c]=[]:c="mixpanel";a.people=a.people||[];a.toString=function(a){var d="mixpanel";"mixpanel"!==c&&(d+="."+c);a||(d+=" (stub)");return d};a.people.toString=function(){return a.toString(1)+".people (stub)"};i="disable time_event track track_pageview track_links track_forms track_with_groups add_group set_group remove_group register register_once alias unregister identify name_tag set_config reset opt_in_tracking opt_out_tracking has_opted_in_tracking has_opted_out_tracking clear_opt_in_out_tracking start_batch_senders people.set people.set_once people.unset people.increment people.append people.union people.track_charge people.clear_charges people.delete_user people.remove".split(" ");for(h=0;h<i.length;h++)g(a,i[h]);var j="set set_once union unset remove delete".split(" ");a.get_group=function(){function b(c){d[c]=function(){call2_args=arguments;call2=[c].concat(Array.prototype.slice.call(call2_args,0));a.push([e,call2])}}for(var d={},e=["get_group"].concat(Array.prototype.slice.call(arguments,0)),c=0;c<j.length;c++)b(j[c]);return d};b._i.push([e,f,c])};b.__SV=1.2;e=f.createElement("script");e.type="text/javascript";e.async=!0;e.src="undefined"!==typeof MIXPANEL_CUSTOM_LIB_URL?MIXPANEL_CUSTOM_LIB_URL:"file:"===f.location.protocol&&"//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js".match(/^\\/\\//)?"https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js":"//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js";g=f.getElementsByTagName("script")[0];g.parentNode.insertBefore(e,g)}})(document,window.mixpanel||[]);</script>',
      ),
    )
    .pipe(gulp.dest("./build"));
});
/*gulp.task("remove_mixpanel_in_index", () => {
  let src = "./build/index.html";
  return gulp
    .src(src)
    .pipe(
      string_replace(
        '<script type="text/javascript">(function(f,b){if(!b.__SV){var e,g,i,h;window.mixpanel=b;b._i=[];b.init=function(e,f,c){function g(a,d){var b=d.split(".");2==b.length&&(a=a[b[0]],d=b[1]);a[d]=function(){a.push([d].concat(Array.prototype.slice.call(arguments,0)))}}var a=b;"undefined"!==typeof c?a=b[c]=[]:c="mixpanel";a.people=a.people||[];a.toString=function(a){var d="mixpanel";"mixpanel"!==c&&(d+="."+c);a||(d+=" (stub)");return d};a.people.toString=function(){return a.toString(1)+".people (stub)"};i="disable time_event track track_pageview track_links track_forms track_with_groups add_group set_group remove_group register register_once alias unregister identify name_tag set_config reset opt_in_tracking opt_out_tracking has_opted_in_tracking has_opted_out_tracking clear_opt_in_out_tracking start_batch_senders people.set people.set_once people.unset people.increment people.append people.union people.track_charge people.clear_charges people.delete_user people.remove".split(" ");for(h=0;h<i.length;h++)g(a,i[h]);var j="set set_once union unset remove delete".split(" ");a.get_group=function(){function b(c){d[c]=function(){call2_args=arguments;call2=[c].concat(Array.prototype.slice.call(call2_args,0));a.push([e,call2])}}for(var d={},e=["get_group"].concat(Array.prototype.slice.call(arguments,0)),c=0;c<j.length;c++)b(j[c]);return d};b._i.push([e,f,c])};b.__SV=1.2;e=f.createElement("script");e.type="text/javascript";e.async=!0;e.src="undefined"!==typeof MIXPANEL_CUSTOM_LIB_URL?MIXPANEL_CUSTOM_LIB_URL:"file:"===f.location.protocol&&"//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js".match(/^///)?"https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js":"//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js";g=f.getElementsByTagName("script")[0];g.parentNode.insertBefore(e,g)}})(document,window.mixpanel||[]);</script>',
        "",
      ),
    )
    .pipe(gulp.dest("./build"));
});
*/

/* Upload files through FTP Connection */
// Primary Server FTP Connection
function getPrimaryServerFtpConnection() {
  return ftp.create({
    host: process.env.FTP_PRIMARY_SERVER_HOST,
    port: process.env.FTP_PRIMARY_SERVER_PORT,
    user: process.env.FTP_PRIMARY_SERVER_USER,
    password: process.env.FTP_PRIMARY_SERVER_PASSWORD,
    parallel: 5,
    log: gutil.log,
  });
}
// Secondary Server FTP Connection
function getSecondaryServerFtpConnection() {
  return ftp.create({
    host: process.env.FTP_SECONDARY_SERVER_HOST,
    port: process.env.FTP_SECONDARY_SERVER_PORT,
    user: process.env.FTP_SECONDARY_SERVER_USER,
    password: process.env.FTP_SECONDARY_SERVER_PASSWORD,
    parallel: 5,
    log: gutil.log,
  });
}

// Upload the PHP files to primary server
gulp.task("upload_php_files_to_primary_server", () => {
  let conn = getPrimaryServerFtpConnection(),
    localFiles = ["./build/app_dg_monitoring.php"],
    serverPath = "/datoms/app";
  return gulp
    .src(localFiles, { buffer: false })
    .pipe(conn.newer(serverPath))
    .pipe(conn.dest(serverPath));
});
// Upload the PHP files to secondary server
// gulp.task('upload_php_files_to_secondary_server', () => {
// 	let conn = getSecondaryServerFtpConnection(),
// 		localFiles = ['./build/app_dg_monitoring.php'],
// 		serverPath = '/datoms/app';
// 	return gulp
// 		.src(localFiles, { buffer: false })
// 		.pipe(conn.newer(serverPath))
// 		.pipe(conn.dest(serverPath));
// });

// Upload php files to deploy servers
gulp.task(
  "upload_php_files_to_deploy_servers",
  gulp.parallel(
    "upload_php_files_to_primary_server",
    // 'upload_php_files_to_secondary_server'
  ),
);

gulp.task("update_webapp_version", async () => {
  // set auth
  configSdk({
    authentication: {
      method: "private_key",
      access_name: "datoms-service-webapp-deployment",
      private_key: Buffer.from(
        process.env.DATOMS_WEBAPP_DEPLOYMENT_AUTH_PRIVATE_KEY,
        "base64",
      ).toString(),
    },
  });

  // update web app
  await updateWebApp(
    process.env.DATOMS_WEBAPP_BUILD_APP_SERVICE_ID,
    "v" + packageJson.version,
  );
});

gulp.task("less", () => {
  return gulp
    .src("./src/**/*.less")
    .pipe(sourcemaps.init())
    .pipe(
      less({
        plugins: [autoprefix],
        javascriptEnabled: true,
        modifyVars: { "@primary-color": "#f58740" },
      }),
    )
    .pipe(sourcemaps.write())
    .pipe(gulp.dest("./build_library"));
});

gulp.task("build-misc", () => {
  return gulp
    .src(["src/**/*.{gif,jpg,png,svg,css,json}"])
    .pipe(sourcemaps.init())
    .pipe(sourcemaps.write())
    .pipe(gulp.dest("./build_library"));
});

gulp.task("update_library_name", () => {
  const name = "@datoms/dg-monitoring-views";
  return gulp
    .src("./package.json")
    .pipe(
      jsonEdit({
        name: name,
      }),
    )
    .pipe(gulp.dest("./"));
});
