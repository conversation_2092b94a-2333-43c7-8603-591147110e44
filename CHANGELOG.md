## [3.245.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.245.2...v3.245.3) (2025-07-21)


### Bug Fixes

* **mobile-app-version:** mobile app version updated ([ea6dbea](https://github.com/Datoms-IoT/datoms-webapp/commit/ea6dbeae36edd6bf33ee33f89997ae0c3559ddca))

## [3.245.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.245.1...v3.245.2) (2025-07-21)


### Bug Fixes

* **device page:** fixes device page blank out when searching for firmware version ([7289e9b](https://github.com/Datoms-IoT/datoms-webapp/commit/7289e9bc38b56c3285e2c326a53cbb76497bb56a))

## [3.245.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.245.0...v3.245.1) (2025-07-18)


### Bug Fixes

* **tml-mobile-login:** fixed 'tml branding not showing on login page in mobile' issue ([e0b9ad1](https://github.com/Datoms-IoT/datoms-webapp/commit/e0b9ad106e4f216a32879c6519b1c4f9b796e8c1))

# [3.245.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.244.1...v3.245.0) (2025-07-18)


### Features

* **customer-specific config:** daily, run and multi asset report config condition added for DHL ([bd4afe4](https://github.com/Datoms-IoT/datoms-webapp/commit/bd4afe4908690e46b7294e6ef9b9b93ee07d777f))

## [3.244.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.244.0...v3.244.1) (2025-07-18)


### Bug Fixes

* **gridgensetenergy/logic.ts:** updates site consumption logic ([aca339d](https://github.com/Datoms-IoT/datoms-webapp/commit/aca339dd154e1ead8240b733977b311f00033043))

# [3.244.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.243.17...v3.244.0) (2025-07-17)


### Features

* **site-view:** added chart and graph for load ([c3098ad](https://github.com/Datoms-IoT/datoms-webapp/commit/c3098adfd577a96dba19f7dbed086c15e3aa968b))

## [3.243.17](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.243.16...v3.243.17) (2025-07-16)


### Bug Fixes

* **layout-branding:** fixed 'brand name not showing' issue for tata motors and its customers ([e95f4f3](https://github.com/Datoms-IoT/datoms-webapp/commit/e95f4f3a8312e4329ca98231b49a26e3a35d21ba))

## [3.243.16](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.243.15...v3.243.16) (2025-07-14)


### Bug Fixes

* **asset configuration:** added pin code edit option in thing configuration for customer id 14500 ([debdb5b](https://github.com/Datoms-IoT/datoms-webapp/commit/debdb5b96bd3ada900363b1dd95021ef7173156b))

## [3.243.15](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.243.14...v3.243.15) (2025-07-14)


### Bug Fixes

* **new-map-view:** implemented loading in map and fixed some bugs ([e54a99a](https://github.com/Datoms-IoT/datoms-webapp/commit/e54a99a5d1716d4169a026c3e97121df680b6f59))

## [3.243.14](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.243.13...v3.243.14) (2025-07-11)


### Bug Fixes

* **daily-report:** fixed a bug 'on changing date-range, wrong asset got selected automaticaly' ([4e16f30](https://github.com/Datoms-IoT/datoms-webapp/commit/4e16f30bacee853759ca789fc5217efa3fbbda46))

## [3.243.13](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.243.12...v3.243.13) (2025-07-11)


### Bug Fixes

* **production:** added support for new sensor type for alphasense ([7bc0e0f](https://github.com/Datoms-IoT/datoms-webapp/commit/7bc0e0fbfb9264e46444d69bc2ae13a6ef942648))

## [3.243.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.243.11...v3.243.12) (2025-07-09)


### Bug Fixes

* **custom-report:** fixed data not coming in site-wise report for some asset ([cc02af3](https://github.com/Datoms-IoT/datoms-webapp/commit/cc02af3531fb9291b7e9e52308807fe26f12e71b))

## [3.243.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.243.10...v3.243.11) (2025-07-08)


### Bug Fixes

* **custom-report:** fixed 'page crashing when param is searched in site-wise report' ([55d6286](https://github.com/Datoms-IoT/datoms-webapp/commit/55d62867c05d49382d3601c288e90d1fd4fcb53b))

## [3.243.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.243.9...v3.243.10) (2025-07-07)


### Bug Fixes

* **custom-report:** fixed marker not showing in aqi graph for aurassure ([d3cc4df](https://github.com/Datoms-IoT/datoms-webapp/commit/d3cc4df9e9abd5e186146bc37427c18652dad2f9))

## [3.243.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.243.8...v3.243.9) (2025-07-04)


### Bug Fixes

* **custom-report:** optimized download time for custom report by around 20 percent ([1ad54fa](https://github.com/Datoms-IoT/datoms-webapp/commit/1ad54faa11f9526799d372a7c3833e7a492c2e57))

## [3.243.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.243.7...v3.243.8) (2025-07-04)


### Bug Fixes

* **custom-report:** fixed some custom report bugs e.g. grid-graph switch not working ([97fd777](https://github.com/Datoms-IoT/datoms-webapp/commit/97fd777af6ffd0d4bc0ab42ad97a8717c1e7992f))

## [3.243.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.243.6...v3.243.7) (2025-07-03)


### Bug Fixes

* **custom-report:** reduced time for downloading the custom report ([d50a00c](https://github.com/Datoms-IoT/datoms-webapp/commit/d50a00cb3cf9d85b2f3eba3a90787c843923f8f0))

## [3.243.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.243.5...v3.243.6) (2025-07-03)


### Bug Fixes

* **detailed-view:** fixed 'on switching to yesterday in date range for graph, new data was coming' ([9bfc733](https://github.com/Datoms-IoT/datoms-webapp/commit/9bfc733928085ef41d7c84dd40b9474d462ac081))

## [3.243.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.243.4...v3.243.5) (2025-07-02)


### Bug Fixes

* **custom-report:** fixed loading issue in custom report graph and changed max time range to 3 days ([84135ee](https://github.com/Datoms-IoT/datoms-webapp/commit/84135eee41e74bc4c0e7046807a5a16f32a72b9c))

## [3.243.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.243.3...v3.243.4) (2025-07-02)


### Bug Fixes

* **custom-report:** fixed `page not loading` for large dataset even when data comes from api ([c1fb560](https://github.com/Datoms-IoT/datoms-webapp/commit/c1fb560f665250333de305a76c155642847c8aa5))

## [3.243.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.243.2...v3.243.3) (2025-07-01)


### Bug Fixes

* **custom-report:** reduced custom report rendering time for large datasets ([97fb9e5](https://github.com/Datoms-IoT/datoms-webapp/commit/97fb9e5ec09c75891014f5c5841bd05a3811230b))

## [3.243.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.243.1...v3.243.2) (2025-06-26)


### Bug Fixes

* **new-map-view:** fixed some bugs in the main page flow and modified dg params config ([fa8c43b](https://github.com/Datoms-IoT/datoms-webapp/commit/fa8c43b0245c5f8a3164e8f67952d8f8390603ce))

## [3.243.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.243.0...v3.243.1) (2025-06-25)


### Bug Fixes

* **detailed-view:** fixed '[object Object]' showing instead of x-axis title in detailed-view ([2b3a0c6](https://github.com/Datoms-IoT/datoms-webapp/commit/2b3a0c6aaff9500bfca92581473fd1aa2331174c))

# [3.243.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.242.2...v3.243.0) (2025-06-25)


### Features

* **map-view:** implemented KPI update for device kpi in map view when any filter is selected ([0f1ca6e](https://github.com/Datoms-IoT/datoms-webapp/commit/0f1ca6e6698dc94fbd23f041a848f8d3da2bb940))

## [3.242.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.242.1...v3.242.2) (2025-06-25)


### Bug Fixes

* **report download:** fixes issue for report download in data availability reports ([aae8ad2](https://github.com/Datoms-IoT/datoms-webapp/commit/aae8ad2946bd1552bfc5eff0db08845d2f92fcda))

## [3.242.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.242.0...v3.242.1) (2025-06-24)


### Bug Fixes

* **map-view:** implemented 'operational' as default in 'operational status' filter for zepto ([9ef9528](https://github.com/Datoms-IoT/datoms-webapp/commit/9ef9528582bb929a152a686ba84dfdc58708932d))

# [3.242.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.241.0...v3.242.0) (2025-06-24)


### Features

* **thing config:** added 3rd party push configuration option for iaqms ([f5cabdc](https://github.com/Datoms-IoT/datoms-webapp/commit/f5cabdc72542ffe4f2cfcbfc631c2aa132ef3868))

# [3.241.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.240.2...v3.241.0) (2025-06-24)


### Features

* **new-map-view:** added cold room panel-view UI in map view ([66aa1bd](https://github.com/Datoms-IoT/datoms-webapp/commit/66aa1bd81b653a4b55bb7b87fb0eb695a5729433))

## [3.240.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.240.1...v3.240.2) (2025-06-23)


### Bug Fixes

* **site-list:** fixed KPIs for Datoms users ([f500176](https://github.com/Datoms-IoT/datoms-webapp/commit/f5001768d54e40cb8e7859a7c850b868decb5769))

## [3.240.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.240.0...v3.240.1) (2025-06-23)


### Bug Fixes

* **list-view:** fixed issue with site list view for datoms-x ([57ad0b6](https://github.com/Datoms-IoT/datoms-webapp/commit/57ad0b60130cbf93d162ad6ded5d8f18c9878732))

# [3.240.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.239.2...v3.240.0) (2025-06-18)


### Features

* **detailed view:** added parameters in detailed view ([2d314ef](https://github.com/Datoms-IoT/datoms-webapp/commit/2d314ef957bacfe203f8bfd8da808800db2ce392))

## [3.239.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.239.1...v3.239.2) (2025-06-18)


### Bug Fixes

* **site view:** fixed some issue in site view status header ([86d1e70](https://github.com/Datoms-IoT/datoms-webapp/commit/86d1e70f00935975ba792888204bd15aef8a6425))

## [3.239.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.239.0...v3.239.1) (2025-06-17)


### Bug Fixes

* **battery panel:** fixes battery panel view and map view ([3506cc9](https://github.com/Datoms-IoT/datoms-webapp/commit/3506cc961c10d2a75dc6357b0a9122ff61d1c2f0))

# [3.239.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.238.5...v3.239.0) (2025-06-17)


### Features

* **site-view:** added header component in site-view, added StatusKeys to Config and fixed drawer ([2b2efa9](https://github.com/Datoms-IoT/datoms-webapp/commit/2b2efa9110fa016f463369ef2d5b0c47bb94cb57))

## [3.238.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.238.4...v3.238.5) (2025-06-17)


### Bug Fixes

* **asset configuration:** fixes syncing of protocol config params for magmon protocol ([4d2dd82](https://github.com/Datoms-IoT/datoms-webapp/commit/4d2dd82d8d9fda5659be63bd3f953bce232e4145))

## [3.238.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.238.3...v3.238.4) (2025-06-17)


### Bug Fixes

* **detailed-view:** removed a parameter 'eff_curr' from detailed view ([8aa2b38](https://github.com/Datoms-IoT/datoms-webapp/commit/8aa2b386fad2a2e4780a86f5153491229abefc6f))

## [3.238.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.238.2...v3.238.3) (2025-06-16)


### Bug Fixes

* **configurable summary:** fix condition for summary info, and add check to handle empty description ([b50fd55](https://github.com/Datoms-IoT/datoms-webapp/commit/b50fd5506c7c179a18be57e2562f883f2325a85c))

## [3.238.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.238.1...v3.238.2) (2025-06-16)


### Bug Fixes

* **detailed-view:** fixed 'parameter trends not showing for solar system in detailed view' ([83af311](https://github.com/Datoms-IoT/datoms-webapp/commit/83af311fc9a2166a5f8bae548c140f30244b61aa))

## [3.238.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.238.0...v3.238.1) (2025-06-13)


### Bug Fixes

* **mri-views:** fixed analog view scrolling error ([ce1ffd0](https://github.com/Datoms-IoT/datoms-webapp/commit/ce1ffd07df4f79ac899d67befe9d942001c4f045))

# [3.238.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.237.0...v3.238.0) (2025-06-13)


### Features

* **asset type config:** added mri room, mri panel in cold room asset type ([6f1c4c0](https://github.com/Datoms-IoT/datoms-webapp/commit/6f1c4c00db3be343f7a689637f4242ad55f4bfdb))

# [3.237.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.236.9...v3.237.0) (2025-06-13)


### Bug Fixes

* **device list:** fixes device add, unassign bugs ([a81f986](https://github.com/Datoms-IoT/datoms-webapp/commit/a81f98676848965c9143e4a7b97ab78aad87d3ea))
* **event-view:** changed the default date range from 'last 7 days' to 'today' ([07f7f42](https://github.com/Datoms-IoT/datoms-webapp/commit/07f7f423c2a605e359d4fc2cfc78634729c48338))
* **new-map-view:** changed the filter query key for asset/site list filtering for map-data ([0362c7b](https://github.com/Datoms-IoT/datoms-webapp/commit/0362c7b5bcdd18d46d1844a8962b45f4c87c49bc))
* **new-map-view:** fixed page going blank in map view page rendering ([a0854cb](https://github.com/Datoms-IoT/datoms-webapp/commit/a0854cbef0dda45bb243ae098ad9ab816e9331af))
* **new-map-view:** fixed the quick link not working for detailed/analog view ([fb9d862](https://github.com/Datoms-IoT/datoms-webapp/commit/fb9d862248ef4e93e66e6ced202613e4e7fdd514))


### Features

* **mri-machine:** added map, panel, detailed, analog, list views for mri machine asset category ([10ac3d6](https://github.com/Datoms-IoT/datoms-webapp/commit/10ac3d6640b416beacf23a7b25b97eda5d953d75))

## [3.236.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.236.8...v3.236.9) (2025-06-13)


### Bug Fixes

* **event-view:** changed the default date range from 'last 7 days' to 'today' ([6876df4](https://github.com/Datoms-IoT/datoms-webapp/commit/6876df4fa2fb1bfe4bcc85aef9e21010078d2a0d))

## [3.236.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.236.7...v3.236.8) (2025-06-10)


### Bug Fixes

* **new-map-view:** fixed page going blank in map view page rendering ([dbf6f79](https://github.com/Datoms-IoT/datoms-webapp/commit/dbf6f795bfaba02a2185d76ec89acef60796033c))

## [3.236.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.236.6...v3.236.7) (2025-06-09)


### Bug Fixes

* **new-map-view:** changed the filter query key for asset/site list filtering for map-data ([b783315](https://github.com/Datoms-IoT/datoms-webapp/commit/b7833159932b798a281114e17b1862c94febcb83))

## [3.236.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.236.5...v3.236.6) (2025-06-09)


### Bug Fixes

* **device list:** fixes device add, unassign bugs ([ab7f287](https://github.com/Datoms-IoT/datoms-webapp/commit/ab7f28773945d02a5e93ebecdff7bf9728882e2e))

## [3.236.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.236.4...v3.236.5) (2025-06-09)


### Bug Fixes

* **new-map-view:** fixed the quick link not working for detailed/analog view ([0443975](https://github.com/Datoms-IoT/datoms-webapp/commit/0443975d494988dcd26ff939401e2a7541c84b0e))

## [3.236.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.236.3...v3.236.4) (2025-06-09)


### Bug Fixes

* **device health report:** fixes download and removed sorting from table ([41caac3](https://github.com/Datoms-IoT/datoms-webapp/commit/41caac308798299668d9000b19e9a5a3bffa7be4))

## [3.236.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.236.2...v3.236.3) (2025-06-06)


### Bug Fixes

* **new-map-view:** fixed map marker and drawer issues in new map view ([402a86a](https://github.com/Datoms-IoT/datoms-webapp/commit/402a86aade9b2aa0c4748b2ab324ebebcf63b6b7))

## [3.236.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.236.1...v3.236.2) (2025-06-06)


### Bug Fixes

* **fix:** device qr list call ([5ba839b](https://github.com/Datoms-IoT/datoms-webapp/commit/5ba839b96c99a321c464d5beef04005f158edbc2))

## [3.236.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.236.0...v3.236.1) (2025-06-06)


### Bug Fixes

* **fix:** fixes pagination and filter ([d3b64bd](https://github.com/Datoms-IoT/datoms-webapp/commit/d3b64bdbf1f2c7ebf13694e1b5963baf94b7d89e))

# [3.236.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.235.3...v3.236.0) (2025-06-05)


### Features

* **new-map-view:** implemented real time updates (socket) in map view ([a9d0523](https://github.com/Datoms-IoT/datoms-webapp/commit/a9d0523166a01195707dca33d85222fd13a589c7))

## [3.235.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.235.2...v3.235.3) (2025-06-05)


### Bug Fixes

* **style-issue:** some style issues fixed ([527eac7](https://github.com/Datoms-IoT/datoms-webapp/commit/527eac7eb6980dc6c5cab28ec516df56cfa05e79))

## [3.235.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.235.1...v3.235.2) (2025-06-05)


### Bug Fixes

* **gravity report:** fixes table api call ([66472a8](https://github.com/Datoms-IoT/datoms-webapp/commit/66472a81646b374301ac7c4cab89155f4b74d1f7))

## [3.235.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.235.0...v3.235.1) (2025-06-04)


### Bug Fixes

* **device health report:** fix ([7eec14a](https://github.com/Datoms-IoT/datoms-webapp/commit/7eec14a32a6c40fd978a8bcecffca611b336245f))
* **gravity-reports:** fixed the issue of page no persisting when filter changed ([605a99c](https://github.com/Datoms-IoT/datoms-webapp/commit/605a99cecf849c0e969cc430fe0e807186f902f2))

# [3.235.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.234.1...v3.235.0) (2025-06-04)


### Features

* **device health report:** added device health report ([6acb732](https://github.com/Datoms-IoT/datoms-webapp/commit/6acb732fce77ee68b797d32b2eadb91fec274225))

## [3.234.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.234.0...v3.234.1) (2025-06-04)


### Bug Fixes

* **detailed-view:** fixed the issue- showing offline instead of disconnected in detailed-view header ([807cb12](https://github.com/Datoms-IoT/datoms-webapp/commit/807cb12e30d1d6290f1d5e7ad5dfdc5434ed5508))

# [3.234.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.233.0...v3.234.0) (2025-06-03)


### Features

* **map-view:** added site details and customer,partner filters for datoms-x in new map view ([e8630d9](https://github.com/Datoms-IoT/datoms-webapp/commit/e8630d9823aa7846e6ab6e22404c3f2be6762a7f))

# [3.233.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.232.1...v3.233.0) (2025-05-30)


### Features

* **map-view:** developed a new map view that supports sites along with assets ([077ca7a](https://github.com/Datoms-IoT/datoms-webapp/commit/077ca7a53e70640bc9e546ace46641b267b9b350))

## [3.232.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.232.0...v3.232.1) (2025-05-28)


### Bug Fixes

* **mixpanel:** user name and email sent to user properties and condition added for customer name ([511210e](https://github.com/Datoms-IoT/datoms-webapp/commit/511210e6f1548a3f905c1e34acd5650edb3690a4))

# [3.232.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.231.0...v3.232.0) (2025-05-28)


### Features

* **ant-d:** ant-d package version updated and style issues fixed ([58d4c35](https://github.com/Datoms-IoT/datoms-webapp/commit/58d4c35bfa0c0c5ad96ce2454c87d2a7466dc69c))

# [3.231.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.230.1...v3.231.0) (2025-05-26)


### Features

* **map-view:** added operational status filter in map view for zepto ([6888ba4](https://github.com/Datoms-IoT/datoms-webapp/commit/6888ba405a5a3d1d3014e71ab3448fb126895f89))

## [3.230.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.230.0...v3.230.1) (2025-05-23)


### Bug Fixes

* **asset-configuration:** fixed the issue of asset template name not showing in view mode ([f7dff63](https://github.com/Datoms-IoT/datoms-webapp/commit/f7dff6399958b5698358232b9f160e3cd866f282))

# [3.230.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.229.0...v3.230.0) (2025-05-23)


### Features

* **custom-report:** added custom range for y-axis in graphs for customer 'pooja sponge pvt ltd' ([ab570ff](https://github.com/Datoms-IoT/datoms-webapp/commit/ab570ff9299a08150260bbd51dbf5cfb6737abc2))

# [3.229.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.228.1...v3.229.0) (2025-05-23)


### Features

* **contact support:** for vendor when customer not selected all sites shown ([3c10dfe](https://github.com/Datoms-IoT/datoms-webapp/commit/3c10dfe9e915f8bdee717e909575c6e1436c2041))
* **contact support:** site type and site selection added in create ticket ([f31f1a5](https://github.com/Datoms-IoT/datoms-webapp/commit/f31f1a5c6492137ecae4b5c52a6b5a3a219299e4))

## [3.228.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.228.0...v3.228.1) (2025-05-20)


### Bug Fixes

* **asset config:** added bihar pcb ([91f532d](https://github.com/Datoms-IoT/datoms-webapp/commit/91f532de223303f8e788a146e9660d5f007e5b61))

# [3.228.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.227.10...v3.228.0) (2025-05-19)


### Features

* **site list:** added site operational status ([54b86b0](https://github.com/Datoms-IoT/datoms-webapp/commit/54b86b03f09785fba4b635a513a06839fd8b8d6b))

## [3.227.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.227.9...v3.227.10) (2025-05-19)


### Bug Fixes

* **contact support:** added new key support for assets field ([ac79b04](https://github.com/Datoms-IoT/datoms-webapp/commit/ac79b04f5b5b0364ba4c6f57187524d33be97211))

## [3.227.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.227.8...v3.227.9) (2025-05-19)


### Bug Fixes

* **mix-panel:** customer name sent to user properties ([7ba3e91](https://github.com/Datoms-IoT/datoms-webapp/commit/7ba3e91bf9039ff3089767f037af6952d5c29c17))

## [3.227.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.227.7...v3.227.8) (2025-05-16)


### Bug Fixes

* **site list view:** re-establish polling interval on filters change ([59b0260](https://github.com/Datoms-IoT/datoms-webapp/commit/59b0260b384d372250bf6c083086bc58d6c3b52d))

## [3.227.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.227.6...v3.227.7) (2025-05-15)


### Bug Fixes

* **site list view:** misc bug fixes ([6eebffb](https://github.com/Datoms-IoT/datoms-webapp/commit/6eebffb4ca290714c33333ee1bfc8465646804c5))

## [3.227.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.227.5...v3.227.6) (2025-05-14)


### Bug Fixes

* **reports:** fixed mahindra condition for their resellers' end customers ([fe0d39f](https://github.com/Datoms-IoT/datoms-webapp/commit/fe0d39f86fec48d7b5914e003a82fd6b83006255))

## [3.227.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.227.4...v3.227.5) (2025-05-13)


### Bug Fixes

* **site list view:** cells with NA values made grey ([92820d9](https://github.com/Datoms-IoT/datoms-webapp/commit/92820d9a83070e77a9bcad806b504e233720ffde))

## [3.227.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.227.3...v3.227.4) (2025-05-13)


### Bug Fixes

* **site list view:** added filter by kpi ([7c222b6](https://github.com/Datoms-IoT/datoms-webapp/commit/7c222b619ff3348fac08f992ad76cadf59f400c8))

## [3.227.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.227.2...v3.227.3) (2025-05-13)


### Bug Fixes

* **site list view:** updated last data packet col path ([54a314d](https://github.com/Datoms-IoT/datoms-webapp/commit/54a314d9dfd2a57a3c3119b3e40f565d7ea92005))

## [3.227.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.227.1...v3.227.2) (2025-05-13)


### Bug Fixes

* **site list view:** key updated for parameter compliance ([c4b5902](https://github.com/Datoms-IoT/datoms-webapp/commit/c4b59026ec3f49bedd602f36bae04cc515a5237e))

## [3.227.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.227.0...v3.227.1) (2025-05-13)


### Bug Fixes

* **site list view:** added device_issues update ([9de6c91](https://github.com/Datoms-IoT/datoms-webapp/commit/9de6c91a74103021dcae06bbf3fb8b77139f58f1))

# [3.227.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.226.0...v3.227.0) (2025-05-12)


### Bug Fixes

* **report list:** dg reports renamed as "DG Set Reports" from "Reports"& included Summary Reports ([df57506](https://github.com/Datoms-IoT/datoms-webapp/commit/df57506e49457c60c9151181d398e540f175c548))
* **site list view:** filter component to take full width when no buttons available in the right side ([7db6f41](https://github.com/Datoms-IoT/datoms-webapp/commit/7db6f417c25701b74b95a5de59ecb8c945f9bdcd))


### Features

* **site list view:** added realtime update for events ([2b8531b](https://github.com/Datoms-IoT/datoms-webapp/commit/2b8531bd7264f41ae4508d15856d644d9b2cfb43))

# [3.226.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.225.0...v3.226.0) (2025-05-09)


### Features

* **site list view:** added KPI, status icons & alerts column ([3e09b2e](https://github.com/Datoms-IoT/datoms-webapp/commit/3e09b2e48da2eb9d5da1d9ccc5296ebb21bbed7e))

# [3.225.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.224.14...v3.225.0) (2025-05-08)


### Bug Fixes

* **site list view:** loading two times on filter/sorting change - fixed ([bb56045](https://github.com/Datoms-IoT/datoms-webapp/commit/bb5604586fde198158602d82fd1f2487ef795883))
* **site list view:** territory name sorting fixed ([1bceee6](https://github.com/Datoms-IoT/datoms-webapp/commit/1bceee692d062067b60c273e2e334fdfbe7f78f9))


### Features

* **table:** implemented grouped column customisation ([b682d31](https://github.com/Datoms-IoT/datoms-webapp/commit/b682d31b93e8e9b5e174e80796266b2002c6f0f1))

## [3.224.14](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.224.13...v3.224.14) (2025-05-07)


### Bug Fixes

* **site list view:** fixed site type wise preferred column download ([e3dabdb](https://github.com/Datoms-IoT/datoms-webapp/commit/e3dabdbb168e784a071b2141c1325523d647b4c7))
* **site type:** added new site type in asset config for cold room ([b2e24c0](https://github.com/Datoms-IoT/datoms-webapp/commit/b2e24c0843e0b7e53da085f7337567cc7b7657f5))

## [3.224.13](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.224.12...v3.224.13) (2025-05-07)


### Bug Fixes

* **site list view:** parameter column config made dynamic based on site type ([57641fd](https://github.com/Datoms-IoT/datoms-webapp/commit/57641fd1b5b75a21617877d6f39d2d69db3d9ce1))

## [3.224.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.224.11...v3.224.12) (2025-05-07)


### Bug Fixes

* **map view:** asset name will occupy all the blank space available in drawer list ([317f45f](https://github.com/Datoms-IoT/datoms-webapp/commit/317f45fa2985249b98fe466981fe438669cf5e28))

## [3.224.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.224.10...v3.224.11) (2025-05-06)


### Bug Fixes

* **asset list view:** added door status column ([ed2dfd8](https://github.com/Datoms-IoT/datoms-webapp/commit/ed2dfd87b2b916a19736e2bf5f00e7bbfb285b95))
* **user add:** add button going out of screen fixed ([356714b](https://github.com/Datoms-IoT/datoms-webapp/commit/356714bd96b0bbeb124e46815e105f9d2584ca36))

## [3.224.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.224.9...v3.224.10) (2025-05-06)


### Bug Fixes

* **door status:** handled no data condition ([554ab54](https://github.com/Datoms-IoT/datoms-webapp/commit/554ab542ddeedbbfb0369c1f62e56dae8ca3c70a))

## [3.224.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.224.8...v3.224.9) (2025-05-05)


### Bug Fixes

* **iot views:** added grid energy meter support ([93ce325](https://github.com/Datoms-IoT/datoms-webapp/commit/93ce325c12a1697bce6c7c48dc26692e2dd5efa0))
* **site list view:** selected filters in download & customize option fixed ([e861397](https://github.com/Datoms-IoT/datoms-webapp/commit/e861397b17fe14abe423c34e75fe870d44d9659c))
* **views:** added site list view in datomsx ([ed260ec](https://github.com/Datoms-IoT/datoms-webapp/commit/ed260ecb779d383e76cad9bcd155739066884e51))

## [3.224.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.224.7...v3.224.8) (2025-05-05)


### Bug Fixes

* **cgwa role:** removed detailed view and analog view for cgwa role type ([71015a5](https://github.com/Datoms-IoT/datoms-webapp/commit/71015a510fddb0a4d4b5c99132da8d7383404c24))

## [3.224.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.224.6...v3.224.7) (2025-05-02)


### Bug Fixes

* **site list view:** removed color for door open status ([cd90bca](https://github.com/Datoms-IoT/datoms-webapp/commit/cd90bca69c447faf8fcd511fe5adfa0462d92cfa))

## [3.224.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.224.5...v3.224.6) (2025-05-02)


### Bug Fixes

* **site list view:** door status, link to site view added ([b8efd9e](https://github.com/Datoms-IoT/datoms-webapp/commit/b8efd9e6bff9e860dc92a3db935cac8465ed0a88))

## [3.224.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.224.4...v3.224.5) (2025-05-02)


### Bug Fixes

* **lisitng page:** supported download via pagination in table component & listing controller ([aea8965](https://github.com/Datoms-IoT/datoms-webapp/commit/aea8965bddc761ba7862897e29c6f7207cd082fe))

## [3.224.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.224.3...v3.224.4) (2025-05-02)


### Bug Fixes

* **list view:** fixed same columns repeating multiple times ([81c7670](https://github.com/Datoms-IoT/datoms-webapp/commit/81c7670e25b79e951f8239dfd8ce3d5852d5b4a9))

## [3.224.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.224.2...v3.224.3) (2025-05-02)


### Bug Fixes

* **site list view:** default active sites fetch ([a42a293](https://github.com/Datoms-IoT/datoms-webapp/commit/a42a293a9e15373f7062566fca8fadbb2a98f12e))

## [3.224.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.224.1...v3.224.2) (2025-05-02)


### Bug Fixes

* **cgwa-role-conditions:** removed a parameter from panel view, kept only asset-specific report ([9882585](https://github.com/Datoms-IoT/datoms-webapp/commit/988258536b16b32acd111f1d7c6659e2b3bef500))

## [3.224.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.224.0...v3.224.1) (2025-05-02)


### Bug Fixes

* **panel view:** panel view as landing page fixed ([6a8caff](https://github.com/Datoms-IoT/datoms-webapp/commit/6a8caffbaf1bbf6082d04f48589f59c96b269e04))

# [3.224.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.21...v3.224.0) (2025-04-30)


### Features

* **component:** added a base component SegmentedTab ([266a5cd](https://github.com/Datoms-IoT/datoms-webapp/commit/266a5cd00d390e1f0a49279671088040c945f3ba))
* **list view:** added site list view ([df24a80](https://github.com/Datoms-IoT/datoms-webapp/commit/df24a80cb1c5e940d4369b09b467a91e2e1b4e6e))
* **views:** list view separated from panel view ([5d7633d](https://github.com/Datoms-IoT/datoms-webapp/commit/5d7633d53f6b8b297379b1557b4301984cbb4a39))

## [3.223.21](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.20...v3.223.21) (2025-04-30)


### Bug Fixes

* **map-common-header:** added tooltip for the asset name in map common header ([b215bbc](https://github.com/Datoms-IoT/datoms-webapp/commit/b215bbcaf724225c185f1e07495b436a192aa14a))

## [3.223.20](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.19...v3.223.20) (2025-04-29)


### Bug Fixes

* **listing page:** fixes listing page ([a4c3096](https://github.com/Datoms-IoT/datoms-webapp/commit/a4c309612c112681c57c2f7c3368d127fffa2bd7))

## [3.223.19](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.18...v3.223.19) (2025-04-29)


### Bug Fixes

* **device_list:** added search & highlighting for searched text in table ([e1c8603](https://github.com/Datoms-IoT/datoms-webapp/commit/e1c8603cecee5429e4f1c569a230bffb4055465c))
* **unit tests:** fixes unit tests for listing page logic ([5dc1029](https://github.com/Datoms-IoT/datoms-webapp/commit/5dc1029412d11e5d7e99004f941e19327b4d7de5))

## [3.223.18](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.17...v3.223.18) (2025-04-29)


### Bug Fixes

* **detailed-view:** added export active energy parameter in detailed view for grid energy meter ([c3e6516](https://github.com/Datoms-IoT/datoms-webapp/commit/c3e65166eb03dcfbaa074a84654275c7d48431f5))

## [3.223.17](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.16...v3.223.17) (2025-04-28)


### Bug Fixes

* **site-view:** handled load-data and only-genset ui for site view ([6949c90](https://github.com/Datoms-IoT/datoms-webapp/commit/6949c90fb826f2a709d63a97d9e8602f593b7315))

## [3.223.16](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.15...v3.223.16) (2025-04-28)


### Bug Fixes

* **analog-view:** fixed the ranges of ll-voltage in analog view ([f7a715a](https://github.com/Datoms-IoT/datoms-webapp/commit/f7a715a25d1068a407e7ad88e0b8718fedd37cd4))

## [3.223.15](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.14...v3.223.15) (2025-04-25)


### Bug Fixes

* **notifications:** asset events update via socket only when the user has access to the asset ([5d0f9a2](https://github.com/Datoms-IoT/datoms-webapp/commit/5d0f9a243aee5fa5fadcc1da42bebd2f0983e5ea))

## [3.223.14](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.13...v3.223.14) (2025-04-24)


### Bug Fixes

* **detailed-view:** removed the phase parameters shown in detailed view if not configured for asset ([fcdbd4c](https://github.com/Datoms-IoT/datoms-webapp/commit/fcdbd4cde923f6d8e0587c5fe3525a265b612aee))

## [3.223.13](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.12...v3.223.13) (2025-04-23)


### Bug Fixes

* **calibration:** added calibration support for fuel tank ([00660c5](https://github.com/Datoms-IoT/datoms-webapp/commit/00660c5610a6069e67a08dfc8d8cf9bc58288492))

## [3.223.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.11...v3.223.12) (2025-04-23)


### Bug Fixes

* **reports:** multi asset report bug fix ([96958cc](https://github.com/Datoms-IoT/datoms-webapp/commit/96958ccd2cee2e9699f89e9d1db30e9d1d95761d))

## [3.223.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.10...v3.223.11) (2025-04-22)


### Bug Fixes

* **report:** multi asset report changes ([5451452](https://github.com/Datoms-IoT/datoms-webapp/commit/54514523b9b65af3ba973cada81207ef3b483dd5))
* **reports:** added support for no aggregation period ([c72928d](https://github.com/Datoms-IoT/datoms-webapp/commit/c72928d2369e67507488627a6a74963dd32552b8))

## [3.223.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.9...v3.223.10) (2025-04-21)


### Bug Fixes

* **mobile-view:** fixed status and legends shown in mobile view for sistema bio ([e3e5d1b](https://github.com/Datoms-IoT/datoms-webapp/commit/e3e5d1b6fa9451299a0c0ca9024734d08dd9fc70))

## [3.223.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.8...v3.223.9) (2025-04-18)


### Bug Fixes

* **produciton module:** added sensor type ([1e21ea0](https://github.com/Datoms-IoT/datoms-webapp/commit/1e21ea0500fdb20a4962174096382757472ab1b9))

## [3.223.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.7...v3.223.8) (2025-04-18)


### Bug Fixes

* **asset config:** data push config not sent correctly - fixed ([5db909f](https://github.com/Datoms-IoT/datoms-webapp/commit/5db909faf316e519396750d5e2c9f528379299df))
* **reports:** removed mains runhour for mahindra ([b3283b5](https://github.com/Datoms-IoT/datoms-webapp/commit/b3283b5162d6c4fd6c8971c4a2cc5a6d4cf22166))

## [3.223.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.6...v3.223.7) (2025-04-17)


### Bug Fixes

* **flow-meter:** new parameter added in map and panel view ([c7c2693](https://github.com/Datoms-IoT/datoms-webapp/commit/c7c26934dfbc2b5ca81fbb08a9ac2e7f24c43cbd))

## [3.223.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.5...v3.223.6) (2025-04-16)


### Bug Fixes

* **site:** config icon style fix ([997078b](https://github.com/Datoms-IoT/datoms-webapp/commit/997078bffbfcf0b15de0d1e3a09fa21326dcd42b))

## [3.223.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.4...v3.223.5) (2025-04-16)


### Bug Fixes

* **site:** open site config in a new tab in desktop screen ([6ba4d71](https://github.com/Datoms-IoT/datoms-webapp/commit/6ba4d71408682d87ca31a10f7b64512d322e527b))

## [3.223.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.3...v3.223.4) (2025-04-15)


### Bug Fixes

* **panel view:** nA data while loading more assets fixed ([e9ccb3b](https://github.com/Datoms-IoT/datoms-webapp/commit/e9ccb3b1bb31159d6c1d7eb6897daf035bcb1774))

## [3.223.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.2...v3.223.3) (2025-04-15)


### Bug Fixes

* **unit-testing:** added unit-testing for listing page logic and fixed additional data handling ([0cddcc4](https://github.com/Datoms-IoT/datoms-webapp/commit/0cddcc46a3a1d298a7b3e00500c0e28609d86ce4))

## [3.223.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.1...v3.223.2) (2025-04-15)


### Bug Fixes

* **reports:** fixed asset status report ([fd315bf](https://github.com/Datoms-IoT/datoms-webapp/commit/fd315bf414141020d07eaf7948b2b7f345ca565b))

## [3.223.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.223.0...v3.223.1) (2025-04-15)


### Bug Fixes

* **production module:** added condition for trade wifi similar to trade lte ([27893a6](https://github.com/Datoms-IoT/datoms-webapp/commit/27893a6a3ea3c6218e473d19b3ca879db5b7e3b5))

# [3.223.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.222.8...v3.223.0) (2025-04-14)


### Bug Fixes

* **unit-testing:** fixes failing test-cases ([f4d281a](https://github.com/Datoms-IoT/datoms-webapp/commit/f4d281aa50c1772ab8ef74a4ca2f965a0f988f82))


### Features

* **device-listing:** added actions for add, bulk add for device list ([18dd981](https://github.com/Datoms-IoT/datoms-webapp/commit/18dd9819130b5848fb75b18ce41abb4bc9a709d0))
* **device-listing:** added actions for sim add edit and 3rd party connection details ([0a6f84c](https://github.com/Datoms-IoT/datoms-webapp/commit/0a6f84c9f1aa34237996153c45afb3457ce72618))
* **device-listing:** added all actions for device page listing ([8987c3e](https://github.com/Datoms-IoT/datoms-webapp/commit/8987c3e015a24b4a6d7883f559140d4c817d3fed))
* **device-listing:** added firmware update option ([e7a9858](https://github.com/Datoms-IoT/datoms-webapp/commit/e7a9858238f87a5f89083ccf88edfaa0da1e9e46))

## [3.222.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.222.7...v3.222.8) (2025-04-11)


### Bug Fixes

* **detailed-view:** added a status parameter (egr_indu) for genset ([f4f869b](https://github.com/Datoms-IoT/datoms-webapp/commit/f4f869b5d5dad114ee2307449dbbbaa5e7b2691b))

## [3.222.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.222.6...v3.222.7) (2025-04-11)


### Bug Fixes

* **reports:** added genset serial number in asset status report for cooper ([30ec6c7](https://github.com/Datoms-IoT/datoms-webapp/commit/30ec6c7c28e670a784213dc795f64bf6b0ced994))
* **site view:** door status made NA for no data condition ([9eaaab1](https://github.com/Datoms-IoT/datoms-webapp/commit/9eaaab1ffadd76039aab167b58779062c40f5342))

## [3.222.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.222.5...v3.222.6) (2025-04-10)


### Bug Fixes

* **tracking:** zapscale tracking removed ([879715b](https://github.com/Datoms-IoT/datoms-webapp/commit/879715b5666f7143e9dc6b6675f9cdde948e69ab))

## [3.222.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.222.4...v3.222.5) (2025-04-09)


### Bug Fixes

* modified status and filter for different views for sistema bio end customer ([d24d0e2](https://github.com/Datoms-IoT/datoms-webapp/commit/d24d0e2ccbf55ccf46a6e51c8892c3ac0928b34c))

## [3.222.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.222.3...v3.222.4) (2025-04-09)


### Bug Fixes

* **site view:** fixed site view ([ad0fae1](https://github.com/Datoms-IoT/datoms-webapp/commit/ad0fae1afa6bebcd2f5ca7849802b348527ca657))

## [3.222.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.222.2...v3.222.3) (2025-04-08)


### Bug Fixes

* **translation:** translation issues fixed for table customization tab ([6440a29](https://github.com/Datoms-IoT/datoms-webapp/commit/6440a293640f22984680185ef26f44ec2ed62f55))

## [3.222.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.222.1...v3.222.2) (2025-04-04)


### Bug Fixes

* **listing-page:** handles dependent filters and sorting, pagination in url ([cd11656](https://github.com/Datoms-IoT/datoms-webapp/commit/cd11656084344014a779e8168ee52e34099ee3a5))

## [3.222.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.222.0...v3.222.1) (2025-04-04)


### Bug Fixes

* **asset config:** added extra fields in quick config ([fcdc86d](https://github.com/Datoms-IoT/datoms-webapp/commit/fcdc86dfd4386e10db4ae7da059dd4ad0b681cf5))

# [3.222.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.221.1...v3.222.0) (2025-04-03)


### Bug Fixes

* **event-view:** added a column for average power in the event view of gas genset ([325ebf0](https://github.com/Datoms-IoT/datoms-webapp/commit/325ebf00a82e2cedc96dd1985ee4da6f6404304b))


### Features

* **device listing:** added filters and download option ([b73fcba](https://github.com/Datoms-IoT/datoms-webapp/commit/b73fcba3f7acbf88d44903f95eba06488aa7b6b2))

## [3.221.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.221.0...v3.221.1) (2025-04-03)


### Bug Fixes

* **mix panel:** session start and end function added and session replay text masking removed ([484b1f0](https://github.com/Datoms-IoT/datoms-webapp/commit/484b1f0ce5fc75eeb205a6b9687b46b5f3fc74fc))

# [3.221.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.220.1...v3.221.0) (2025-04-03)


### Features

* **listing-page:** device-list first release ([380710b](https://github.com/Datoms-IoT/datoms-webapp/commit/380710b3169d3cd3b28234477cf88637bb0dac20))

## [3.220.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.220.0...v3.220.1) (2025-04-02)


### Bug Fixes

* **store:** fixed initial app load ([e19c3d8](https://github.com/Datoms-IoT/datoms-webapp/commit/e19c3d8c786e8427135d0b5805ed6b6ab1ff27cb))

# [3.220.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.219.4...v3.220.0) (2025-04-02)


### Bug Fixes

* **map view:** optimized asset list rendering ([a880fb8](https://github.com/Datoms-IoT/datoms-webapp/commit/a880fb86187b83fae8c179530bac82538cc8f043))
* **map view:** socket update in backyard for improved performance ([9e0b921](https://github.com/Datoms-IoT/datoms-webapp/commit/9e0b92196a0d0e3846c5cffa9edfc7ba7eca8cc2))


### Features

* **event-view:** added event view for gas genset ([5a33d46](https://github.com/Datoms-IoT/datoms-webapp/commit/5a33d46fce31060263be25e13a1afd63555e9418))

## [3.219.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.219.3...v3.219.4) (2025-04-02)


### Bug Fixes

* **mix panel:** mix panel session replay enabled ([befbd95](https://github.com/Datoms-IoT/datoms-webapp/commit/befbd955143a33c6732ff36afb6afeda610abb16))

## [3.219.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.219.2...v3.219.3) (2025-04-02)


### Bug Fixes

* **views:** modified machine info ui in panel view and fixed 'disconnected' asset status not showing ([547b51c](https://github.com/Datoms-IoT/datoms-webapp/commit/547b51c476e8d1ea6c8c08432ee2173ae379ab66))

## [3.219.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.219.1...v3.219.2) (2025-04-02)


### Bug Fixes

* **detailed-view:** added remaining status parameters for solar pump and fixed machine info shown ([f11b8ba](https://github.com/Datoms-IoT/datoms-webapp/commit/f11b8bafaf081e5785692d54a374a23b4b39f138))

## [3.219.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.219.0...v3.219.1) (2025-04-01)


### Bug Fixes

* **thing configuration:** sent fault register, param_type and eval expr for parameter handling ([549ec14](https://github.com/Datoms-IoT/datoms-webapp/commit/549ec149d22d257920f7490f00ff98eb888d5c6a))

# [3.219.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.218.0...v3.219.0) (2025-04-01)


### Features

* **mix-panel:** user session tracking added for mixpanel ([34fef8f](https://github.com/Datoms-IoT/datoms-webapp/commit/34fef8fe1f779e1521c73c7332d0c0dd440c2ce4))

# [3.218.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.217.0...v3.218.0) (2025-03-31)


### Features

* **solar-pump:** added real-time view and event view for solar pump & fixed runhour format in graph ([7bd89c8](https://github.com/Datoms-IoT/datoms-webapp/commit/7bd89c89a0399936e863dadbef5497afc5c70b86))

# [3.217.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.216.0...v3.217.0) (2025-03-28)


### Features

* **reports:** data availability reports added for end customers ([59907a7](https://github.com/Datoms-IoT/datoms-webapp/commit/59907a78fd1f78ad10c89235c4d0c733ec6d1b4c))

# [3.216.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.215.0...v3.216.0) (2025-03-28)


### Bug Fixes

* org preference unchecked columns shown ([3a09e29](https://github.com/Datoms-IoT/datoms-webapp/commit/3a09e29c426cf9f478d1482dd9810fa5611f3b4d))
* **reports:** column duplicating while customization in lifetime reports ([2089a23](https://github.com/Datoms-IoT/datoms-webapp/commit/2089a23097800cb3cd595b7dec5af9e8375d61ee))


### Features

* **reports:** added customization to summary component ([b62883a](https://github.com/Datoms-IoT/datoms-webapp/commit/b62883a7f975a3b97cc6e6cdefc610bd1d84bad4))
* **reports:** customize table/summary new ui ([16a9bc8](https://github.com/Datoms-IoT/datoms-webapp/commit/16a9bc80a1a611f3ee6fa6101dc88dafa92a9f9f))

# [3.215.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.214.0...v3.215.0) (2025-03-28)


### Features

* **reports added:** predefied reports added for solar pump asset type ([71073c5](https://github.com/Datoms-IoT/datoms-webapp/commit/71073c5e046061a0fe390e6588e4f9069e22f416))

# [3.214.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.213.0...v3.214.0) (2025-03-27)


### Features

* **solar-pump-views:** added map view, panel view and detailed view for solar pump ([c351ade](https://github.com/Datoms-IoT/datoms-webapp/commit/c351aded70f9bd197b3feacb978bd5d55cc4df34))

# [3.213.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.212.1...v3.213.0) (2025-03-27)


### Features

* **grid-energy-meter:** added earth-neutral voltage parameter in the detailed view and analog view ([076f029](https://github.com/Datoms-IoT/datoms-webapp/commit/076f029f875f5bb807ba54bbd6bdd794a5e69f65))

## [3.212.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.212.0...v3.212.1) (2025-03-24)


### Bug Fixes

* **dashboard:** fixed asset status on socket update ([b7912c8](https://github.com/Datoms-IoT/datoms-webapp/commit/b7912c8fb651b65b096ebe38f540928f0f614d92))

# [3.212.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.211.6...v3.212.0) (2025-03-20)


### Features

* **users:** added different status/info for user verification email request ([b818bb9](https://github.com/Datoms-IoT/datoms-webapp/commit/b818bb9c816dfaa3b910c504d9d4488d4dce4356))

## [3.211.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.211.5...v3.211.6) (2025-03-19)


### Bug Fixes

* **translation:** translation issue fixed for mobile app ([1995737](https://github.com/Datoms-IoT/datoms-webapp/commit/1995737624ce25b35b7e9d7150393eea838ce857))

## [3.211.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.211.4...v3.211.5) (2025-03-19)


### Bug Fixes

* **views:** iot views enabled for gas genset ([fb11096](https://github.com/Datoms-IoT/datoms-webapp/commit/fb110964a45bfe9317954a39e660acdcb5907b63))

## [3.211.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.211.3...v3.211.4) (2025-03-18)


### Bug Fixes

* **reports:** added selected filters in pdf download ([f788e3a](https://github.com/Datoms-IoT/datoms-webapp/commit/f788e3a5151fb15aa470081930be79ad4085c925))
* **reports:** customize option enabled for partners ([d5dc285](https://github.com/Datoms-IoT/datoms-webapp/commit/d5dc2857f36ee0891086e5f7744601c2a8439870))
* **reports:** new reports routes added ([95745f6](https://github.com/Datoms-IoT/datoms-webapp/commit/95745f6612c389c8ca305340b0bb087d04f93e58))
* **reports:** old multi asset report download header fix ([2314317](https://github.com/Datoms-IoT/datoms-webapp/commit/231431731a497f820eaf89897640b527594ad233))

## [3.211.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.211.2...v3.211.3) (2025-03-18)


### Bug Fixes

* **report:** fixed door open count value in sensor summary report ([021a8cc](https://github.com/Datoms-IoT/datoms-webapp/commit/021a8cca2fce4c40c5fbfd5e5fa679505c2044b6))

## [3.211.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.211.1...v3.211.2) (2025-03-17)


### Bug Fixes

* **site:** updated site list limit to 500 in views and reports ([8a800ef](https://github.com/Datoms-IoT/datoms-webapp/commit/8a800ef98bffd9241481f48992ccc93ea75bf24e))

## [3.211.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.211.0...v3.211.1) (2025-03-17)


### Bug Fixes

* **translation:** some missing translation keys added ([1fac979](https://github.com/Datoms-IoT/datoms-webapp/commit/1fac979a1a4699d0e7d22b3dc0425c96d9fdcfa6))

# [3.211.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.210.4...v3.211.0) (2025-03-11)


### Features

* **language support:** adding language option in  platform to support multiple languages ([dc0ad35](https://github.com/Datoms-IoT/datoms-webapp/commit/dc0ad35eda0bc98aeab75538ecfa013b891908be))
* **language:** adding language translation in the portal ([7c730f4](https://github.com/Datoms-IoT/datoms-webapp/commit/7c730f4103b21d38398f06b72b6c75bfe78efdad))
* **translation:** protuguese language translation support added for Aurassure end customer ([f0db96b](https://github.com/Datoms-IoT/datoms-webapp/commit/f0db96bb55cc1ca82712bf94417e1fb368d009c1))

## [3.210.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.210.3...v3.210.4) (2025-03-10)


### Bug Fixes

* **customer list:** fixed combined filter of territory & partner in datomsx ([d45705e](https://github.com/Datoms-IoT/datoms-webapp/commit/d45705ec71ac7e181672bc0bad6b38b5b66e1fae))

## [3.210.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.210.2...v3.210.3) (2025-03-06)


### Bug Fixes

* **solar-panel-view:** fixed today and this month summary data not showing for solar's panel view ([cf38fe5](https://github.com/Datoms-IoT/datoms-webapp/commit/cf38fe592b92a98a41e57a5bb85299a51f5038f1))
* **views:** panel view disconnected count fixed ([829d44e](https://github.com/Datoms-IoT/datoms-webapp/commit/829d44efa74ed79b02087c16a967b274aa84ef06))

## [3.210.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.210.1...v3.210.2) (2025-03-06)


### Bug Fixes

* **real-time-view:** fixed the issue regarding runhour value not shown correctly in real-time-view ([9d331a7](https://github.com/Datoms-IoT/datoms-webapp/commit/9d331a7fec3c7e49379d611610426ea1bda72ec0))

## [3.210.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.210.0...v3.210.1) (2025-03-06)


### Bug Fixes

* **detailed view:** socket update interval changed ([71d10b0](https://github.com/Datoms-IoT/datoms-webapp/commit/71d10b05e6075662036b1a1d685fe9450a1fe80b))

# [3.210.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.209.0...v3.210.0) (2025-03-05)


### Features

* **all:** added ojus conditions for compressor partner id ([9a002c6](https://github.com/Datoms-IoT/datoms-webapp/commit/9a002c62643ddee5dfae41a7be39a64b8e63f896))

# [3.209.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.208.2...v3.209.0) (2025-03-04)


### Bug Fixes

* **remote control:** fixed remote control app_id condition for partner users ([848f7e8](https://github.com/Datoms-IoT/datoms-webapp/commit/848f7e8b2a306dd48e276eea81a07181fd9acc24))


### Features

* **all:** added condition for mobile aq monitor(102) similar to aaqms(22) ([1fa6e89](https://github.com/Datoms-IoT/datoms-webapp/commit/1fa6e8946daa0f6d02c7a7c79166fc856c541b79))

## [3.208.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.208.1...v3.208.2) (2025-03-03)


### Bug Fixes

* **real-time-view:** handled the case when some invalid data (e.g. undefined/ null) is there ([0f42382](https://github.com/Datoms-IoT/datoms-webapp/commit/0f42382e9fc904be23e100b4b51e6fef91c94752))

## [3.208.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.208.0...v3.208.1) (2025-03-03)


### Bug Fixes

* **custom-report:** show more button style issue fixed ([45b8d53](https://github.com/Datoms-IoT/datoms-webapp/commit/45b8d5365175cb26bead0d8bc761d5eaee021f75))

# [3.208.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.207.2...v3.208.0) (2025-03-03)


### Features

* **report:** fault report added for elevator type ([07dd628](https://github.com/Datoms-IoT/datoms-webapp/commit/07dd6287afb34adeb4767054c35502af84907dc9))

## [3.207.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.207.1...v3.207.2) (2025-02-28)


### Bug Fixes

* **custom-report:** fixed the issue of indivdual data points not being shown in graph ([4b83d28](https://github.com/Datoms-IoT/datoms-webapp/commit/4b83d2865583a2d2060555839000b1f7a7773600))

## [3.207.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.207.0...v3.207.1) (2025-02-28)


### Bug Fixes

* **reports:** enabled asset filter for event based multi asset report ([9c95617](https://github.com/Datoms-IoT/datoms-webapp/commit/9c95617c302ac316a95bd1efc0bd10b0dc799ef5))
* **reports:** fixed issues in gravity reports ([5b2f903](https://github.com/Datoms-IoT/datoms-webapp/commit/5b2f9033dc80aac9c3bc56fc7d97349a9c713194))

# [3.207.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.206.1...v3.207.0) (2025-02-27)


### Features

* **elevator-views:** added panel and map views for elevator asset type ([031c6d9](https://github.com/Datoms-IoT/datoms-webapp/commit/031c6d94e9345ba9af18fee1c9e4bcc8caa79a1b))
* **panel view:** added elevator asset type panel ([b2d030d](https://github.com/Datoms-IoT/datoms-webapp/commit/b2d030dc53bbce30d68180c4c779659d636e5dd6))

## [3.206.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.206.0...v3.206.1) (2025-02-24)


### Bug Fixes

* **site-view:** fixed site view page infinite-loading when the site is not present ([79cd308](https://github.com/Datoms-IoT/datoms-webapp/commit/79cd308482a8338f3df9bf1292c278cbdd0fb4f2))

# [3.206.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.205.0...v3.206.0) (2025-02-24)


### Features

* **site-view:** changed default time to 30 days for diagnostic centre site view ([2e492da](https://github.com/Datoms-IoT/datoms-webapp/commit/2e492da4049c51687a22a623f4ce609a823ea9e8))

# [3.205.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.204.0...v3.205.0) (2025-02-21)


### Features

* **site-view:** modified site-view for cold storage site, rice/millet mills, solar site ([b5f2cb4](https://github.com/Datoms-IoT/datoms-webapp/commit/b5f2cb41b03fd6402ed47623450a43a848b464ad))

# [3.204.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.203.2...v3.204.0) (2025-02-18)


### Features

* **reports:** added trip & fault gravity reports ([efe8359](https://github.com/Datoms-IoT/datoms-webapp/commit/efe835982c13fd063ef589b771161206152fe9a2))
* **reports:** handled api pagination in report ([0312f09](https://github.com/Datoms-IoT/datoms-webapp/commit/0312f0964dae7a2ae27a3b1bb9ac7ebffbfc1e50))

## [3.203.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.203.1...v3.203.2) (2025-02-18)


### Bug Fixes

* **detailed view:** going blank when no phase params available fixed ([850c394](https://github.com/Datoms-IoT/datoms-webapp/commit/850c394d258fdb86c0c17f2c20ed74cafcadb4d1))

## [3.203.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.203.0...v3.203.1) (2025-02-17)


### Bug Fixes

* **solar-system-parameters:** fixed required parameters not showing in detailed view of solar system ([2d4f20c](https://github.com/Datoms-IoT/datoms-webapp/commit/2d4f20cc2f9201b65146ac707e4881172cac882e))

# [3.203.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.202.6...v3.203.0) (2025-02-17)


### Features

* **solar-inverter-view:** added map view and detailed view for solar inverter ([622c5ba](https://github.com/Datoms-IoT/datoms-webapp/commit/622c5ba65dabbc5ea21460680e146ce7627d1f45))
* **solar-inverter-views:** created the panel view for solar inverter ([02db537](https://github.com/Datoms-IoT/datoms-webapp/commit/02db537bb1d49028d2d43222d17993fbb878b34a))

## [3.202.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.202.5...v3.202.6) (2025-02-14)


### Bug Fixes

* **gravity reports:** dynamic unit handled in graph ([a6a45cb](https://github.com/Datoms-IoT/datoms-webapp/commit/a6a45cb7e2118bccf891f78936cc68ab00767de9))

## [3.202.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.202.4...v3.202.5) (2025-02-13)


### Bug Fixes

* **reports:** gas genset report fixes ([ab91d6c](https://github.com/Datoms-IoT/datoms-webapp/commit/ab91d6c9955dd0739fe52c3684139937def5fc14))

## [3.202.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.202.3...v3.202.4) (2025-02-13)


### Bug Fixes

* **deps:** update dependency @ant-design/icons to v5.6.1 ([64ffab6](https://github.com/Datoms-IoT/datoms-webapp/commit/64ffab6d5721ae27429b31505d3cebf8e420cc77))
* **deps:** update dependency file-type-checker to v1.1.4 ([00d689e](https://github.com/Datoms-IoT/datoms-webapp/commit/00d689ec3ab9802bc50d90df4eb7e8d8ca2f61fc))
* **deps:** update dependency i18next-browser-languagedetector to v7.2.2 ([dd0a51e](https://github.com/Datoms-IoT/datoms-webapp/commit/dd0a51ead3cf9a85f032f3a45aaf345f8bc4613a))
* **deps:** update dependency json2csv to v5.0.7 ([6dcb4d0](https://github.com/Datoms-IoT/datoms-webapp/commit/6dcb4d05839981381ea374f187d42a7b1f78ed16))
* **deps:** update dependency moment-timezone to v0.5.47 ([388ccc1](https://github.com/Datoms-IoT/datoms-webapp/commit/388ccc11fcd70fd8501ae02a6cc3c79e48847b03))
* **deps:** update dependency react-error-boundary to v4.1.2 ([9ae8e8d](https://github.com/Datoms-IoT/datoms-webapp/commit/9ae8e8da1cc2fd5a8803e69ad5ec8b5b22ecd83e))
* **unused:** removed unused files ([28a8b01](https://github.com/Datoms-IoT/datoms-webapp/commit/28a8b019ac77025e11b9a54dd3af5245b8551a86))

## [3.202.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.202.2...v3.202.3) (2025-02-13)

## [3.202.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.202.1...v3.202.2) (2025-02-12)


### Bug Fixes

* **real-time-view:** handled wrong ranges of ll-voltage in generator tab for gas-genset ([88c237b](https://github.com/Datoms-IoT/datoms-webapp/commit/88c237bfd01a26560b350900e842101897c74882))

## [3.202.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.202.0...v3.202.1) (2025-02-12)


### Bug Fixes

* **site-view:** handled ui for no genset case ([c23df5d](https://github.com/Datoms-IoT/datoms-webapp/commit/c23df5da554297d2f9be7f8d83e96a0d4658c476))

# [3.202.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.201.0...v3.202.0) (2025-02-11)


### Features

* **views:** grid views implemented ([3e505e3](https://github.com/Datoms-IoT/datoms-webapp/commit/3e505e394825a5cb4adf86eae9feb3f1436f8c3c))

# [3.201.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.200.1...v3.201.0) (2025-02-06)


### Features

* **reports:** added multi asset reports for dg,gas genset,ac energy,ac electrical,exhaust fan,solar ([6dd9160](https://github.com/Datoms-IoT/datoms-webapp/commit/6dd916090a91451cef5b1fe7f0b0e466cd16d5c7))
* **table:** added support for grouped column in configurable modal ([e98553f](https://github.com/Datoms-IoT/datoms-webapp/commit/e98553fa379b34f2ec6e964436e373794ae3ee2b))

## [3.200.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.200.0...v3.200.1) (2025-02-05)


### Bug Fixes

* **reports:** gas genset daily reports route fixed ([76cb1a7](https://github.com/Datoms-IoT/datoms-webapp/commit/76cb1a7f88ee95bfc4d2ae769449ba804dbaa2d3))

# [3.200.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.199.0...v3.200.0) (2025-02-04)


### Features

* **view:** handled frequency widget in real time view for single/three phase gensets ([55bc361](https://github.com/Datoms-IoT/datoms-webapp/commit/55bc36115f26f4b1aad2eac8be0db63eff186097))

# [3.199.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.198.0...v3.199.0) (2025-02-03)


### Features

* **views:** handled Single Phase DG Parameters in Detailed and Real Time View ([2954cf5](https://github.com/Datoms-IoT/datoms-webapp/commit/2954cf51e461872dfffd369132f1cfdd7408722a))

# [3.198.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.197.3...v3.198.0) (2025-02-03)


### Features

* **site view:** new design for diagnostic site type ([293d82e](https://github.com/Datoms-IoT/datoms-webapp/commit/293d82ef2faa45375dfc6a98943b837de1791ff4))

## [3.197.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.197.2...v3.197.3) (2025-01-30)


### Bug Fixes

* **aurassure view changes:** fixed the workflow and map view issue with the Air View customer ([fc3c303](https://github.com/Datoms-IoT/datoms-webapp/commit/fc3c3032c90ee7117d06c874268490cc09239a99))

## [3.197.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.197.1...v3.197.2) (2025-01-29)


### Bug Fixes

* **report and title size:** reduced the size of widget's title to 15 and added sort info in Reports ([3417b12](https://github.com/Datoms-IoT/datoms-webapp/commit/3417b12af39d24f1e96da566d560fbbbdae07305))

## [3.197.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.197.0...v3.197.1) (2025-01-29)


### Bug Fixes

* **zepto runhour issue:** fixed zepto runhour issue in sensor report ([1b844d0](https://github.com/Datoms-IoT/datoms-webapp/commit/1b844d084f0a82e57cdeca2654557059f667d37f))

# [3.197.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.196.2...v3.197.0) (2025-01-28)


### Features

* **reports:** added additonal daily reports ([8627213](https://github.com/Datoms-IoT/datoms-webapp/commit/86272136820620fed4d4fa465b0d2e4cca648f88))

## [3.196.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.196.1...v3.196.2) (2025-01-28)


### Bug Fixes

* **zepto sensor summary report:** fixed door open duration in the sensor summary report for Zepto ([6452bc4](https://github.com/Datoms-IoT/datoms-webapp/commit/6452bc42a71bb7d50c800e81229442699c309b33))

## [3.196.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.196.0...v3.196.1) (2025-01-28)

# [3.196.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.195.1...v3.196.0) (2025-01-27)


### Features

* **configurable filters:** added 5 new filters for asset status report ([05b3cce](https://github.com/Datoms-IoT/datoms-webapp/commit/05b3cced286993ef5b99bc350bc4e86d526fdcf6))
* **configurable graph:** upgraded Congfigurable Graph component to support all chart types ([208b770](https://github.com/Datoms-IoT/datoms-webapp/commit/208b770e5467215a5a2b6af777591f320ee777d4))

## [3.195.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.195.0...v3.195.1) (2025-01-23)


### Bug Fixes

* **report download:** enabled and fixed download of reports ([c491072](https://github.com/Datoms-IoT/datoms-webapp/commit/c491072addb8fdfd6badf35743d0b76f8e44e9ca))

# [3.195.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.194.4...v3.195.0) (2025-01-22)


### Bug Fixes

* **calibration:** added auto tag in calibration list ([aa042dd](https://github.com/Datoms-IoT/datoms-webapp/commit/aa042dd6b30cc5cbcceaa2cea90c803c90285875))


### Features

* **reports:** added Site Report ([359cf42](https://github.com/Datoms-IoT/datoms-webapp/commit/359cf420b086ebdfaa09a0154caaf45f422d1842))

## [3.194.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.194.3...v3.194.4) (2025-01-22)


### Bug Fixes

* **calibration:** enabled parameter selection for zero type ([7f45883](https://github.com/Datoms-IoT/datoms-webapp/commit/7f45883b3e13865381133fb4214daf3a4cbc019c))

## [3.194.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.194.2...v3.194.3) (2025-01-22)


### Bug Fixes

* **calibration:** enabled calibration manage for datomsx & partner users in end customer portal ([1e9b4ac](https://github.com/Datoms-IoT/datoms-webapp/commit/1e9b4ac74a81fe7d215f115d377b87b1b6a209a9))

## [3.194.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.194.1...v3.194.2) (2025-01-20)


### Bug Fixes

* **reports:** dg status report download fixed ([fa27be3](https://github.com/Datoms-IoT/datoms-webapp/commit/fa27be3a5e826026da1fb8152f2875e55563eb37))

## [3.194.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.194.0...v3.194.1) (2025-01-16)


### Bug Fixes

* **cooper analog view:** the range for the generator L-L Voltage was increased to 500 ([32bddf2](https://github.com/Datoms-IoT/datoms-webapp/commit/32bddf2399a75009120190cb88bc1c7207a738a6))

# [3.194.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.193.5...v3.194.0) (2025-01-16)


### Features

* **assets:** added onboarding status filter and column in datomsx ([6343e0f](https://github.com/Datoms-IoT/datoms-webapp/commit/6343e0ffece5956e26e65493dd64516e72c6be14))

## [3.193.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.193.4...v3.193.5) (2025-01-14)


### Bug Fixes

* **iot views:** map view filter issue fixed in views ([69c847e](https://github.com/Datoms-IoT/datoms-webapp/commit/69c847ee41e3d1eb8e500681ce21dc29323996d1))
* **trip view:** new columns & filters added ([6c3ba4c](https://github.com/Datoms-IoT/datoms-webapp/commit/6c3ba4cf6ce3ebdcb7f2ea95afd43a7df4ecaf22))

## [3.193.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.193.3...v3.193.4) (2025-01-14)


### Bug Fixes

* **firmware:** accept .gz/.tar.gz files for firmware upload ([ee6196f](https://github.com/Datoms-IoT/datoms-webapp/commit/ee6196f000395c6628e1bbb6962b0d129b0e2475))
* **iot views:** blank page while navigating to views from customer details fixed ([c46748d](https://github.com/Datoms-IoT/datoms-webapp/commit/c46748d460f583cc35871f91c19a30c075bb2abe))

## [3.193.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.193.2...v3.193.3) (2025-01-10)


### Bug Fixes

* **change password link:** fixed the change password link not working for aurassure ([e243f5f](https://github.com/Datoms-IoT/datoms-webapp/commit/e243f5f5ae67c0e6b2051dfd68aecb826e63e2b0))

## [3.193.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.193.1...v3.193.2) (2025-01-10)


### Bug Fixes

* **logout url:** fixed the logout url ([629de85](https://github.com/Datoms-IoT/datoms-webapp/commit/629de857efd1ee0424818693284bb1748b4b4c84))

## [3.193.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.193.0...v3.193.1) (2025-01-09)


### Bug Fixes

* **mahindra daily report:** added the columns in download data and added the param in api call ([dfe03e9](https://github.com/Datoms-IoT/datoms-webapp/commit/dfe03e95fbd4c2d4fff2518412635198a0db6678))

# [3.193.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.192.3...v3.193.0) (2025-01-09)


### Features

* **mahindra daily report:** added fuel filled% and fuel consumed% in daily report for mahindra ([34ce921](https://github.com/Datoms-IoT/datoms-webapp/commit/34ce921ac6077dbef87c021827b51238ac419ebb))

## [3.192.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.192.2...v3.192.3) (2025-01-04)


### Bug Fixes

* **detailed view:** socket update made immediate & fault socket update fixed ([783abf2](https://github.com/Datoms-IoT/datoms-webapp/commit/783abf288e5d1e486b59ab7e022ba7af3d13e838))
* **workflows:** added calibration scheduling for end customers ([2542ebf](https://github.com/Datoms-IoT/datoms-webapp/commit/2542ebfce8f0a384c9696c797dfac5da9abf5f71))

## [3.192.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.192.1...v3.192.2) (2025-01-02)

## [3.192.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.192.0...v3.192.1) (2025-01-02)


### Bug Fixes

* **cooper analog view:** no red zone for cooper dg with less than 50 KVA ([f479717](https://github.com/Datoms-IoT/datoms-webapp/commit/f47971766ee04bc132edcc2d493d5e6fa482f780))

# [3.192.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.191.0...v3.192.0) (2024-12-31)


### Features

* **asset config:** added madhya pradesh spcb in 3rd party data push servers ([1acc95f](https://github.com/Datoms-IoT/datoms-webapp/commit/1acc95f313b9a4c1dd0f104cee67997351ebdad7))

# [3.191.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.190.1...v3.191.0) (2024-12-30)


### Features

* **panel view:** added new table format panel with 15 min avg & last 24 hr min/max data ([956c1ce](https://github.com/Datoms-IoT/datoms-webapp/commit/956c1ce72ce099fb795260ed1a1abbaef22892cb))

## [3.190.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.190.0...v3.190.1) (2024-12-30)


### Bug Fixes

* **asset config:** added new unit ([26dd439](https://github.com/Datoms-IoT/datoms-webapp/commit/26dd43998e0d962edb9febd236df38eee7b0c7b2))

# [3.190.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.189.0...v3.190.0) (2024-12-26)


### Features

* **added e2e testing:** page open testing (page load time, API response time measure) for all pages ([0ebcb69](https://github.com/Datoms-IoT/datoms-webapp/commit/0ebcb696351fbf495fc8a60fd66e265cbe1b0cca))

# [3.189.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.188.0...v3.189.0) (2024-12-26)


### Features

* **header menu:** added API Docs in the header menu for DatomsX ([738b1ee](https://github.com/Datoms-IoT/datoms-webapp/commit/738b1ee0244db13c471331ffe04fbe5e5b4417af))

# [3.188.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.20...v3.188.0) (2024-12-23)


### Features

* **authentication:** added page to manage api credentials of customers ([9d8bb3d](https://github.com/Datoms-IoT/datoms-webapp/commit/9d8bb3dceabef22758f28ae55a905d2beabe3608))

## [3.187.20](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.19...v3.187.20) (2024-12-23)

## [3.187.19](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.18...v3.187.19) (2024-12-20)


### Bug Fixes

* **detailed view:** energy data mismatch for 1 day fix & socket update stopped for past custom range ([e682781](https://github.com/Datoms-IoT/datoms-webapp/commit/e6827810cf06daf4e568d307531797beb5ccee78))

## [3.187.18](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.17...v3.187.18) (2024-12-19)


### Bug Fixes

* **asset config:** added analyzer addition for aurassure ([e93fc22](https://github.com/Datoms-IoT/datoms-webapp/commit/e93fc220af80ec5467db6ecc6934973a14934b93))

## [3.187.17](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.16...v3.187.17) (2024-12-19)


### Bug Fixes

* **views:** implemented territory filter in views & map view issues solved ([1af4492](https://github.com/Datoms-IoT/datoms-webapp/commit/1af449290d8850c2d7c4add7960d96b66df5e479))

## [3.187.16](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.15...v3.187.16) (2024-12-17)


### Bug Fixes

* **custom reports:** site wise report not opening fixed ([dd56a75](https://github.com/Datoms-IoT/datoms-webapp/commit/dd56a75ff45087f8186048b95f0ea6a43bfaa7f9))
* **daily reports:** added solar capacity util in solar daily reports ([41cf947](https://github.com/Datoms-IoT/datoms-webapp/commit/41cf947bd033f298973eb6742a138a1d77be0aaf))
* **detailed view:** added hourly aggregation graph for charging & discharging energy of battery ([82230b2](https://github.com/Datoms-IoT/datoms-webapp/commit/82230b2463f6f882dec691193b9fe9c61b45a4cc))
* **mapview:** added customer name in map view for each asset ([8ed6a78](https://github.com/Datoms-IoT/datoms-webapp/commit/8ed6a78d36b5760db7d2a2c7574162c07d3c58ba))
* **mobile:** style fixes in mobile screen for different pages ([7085baf](https://github.com/Datoms-IoT/datoms-webapp/commit/7085baf66d5ffa146fa1617a03ac137cb3d11477))
* **site view:** added ellipsis in asset name ([64b8a4a](https://github.com/Datoms-IoT/datoms-webapp/commit/64b8a4a0454b42ab59d8425bf3a3c510b3a94fab))
* **trip view:** door open/closing time shown in seconds ([935ddf5](https://github.com/Datoms-IoT/datoms-webapp/commit/935ddf528c081bee8818278b9378d1d179f179f1))

## [3.187.15](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.14...v3.187.15) (2024-12-16)


### Bug Fixes

* **fixed page break:** fixed white screen issue on landing page ([e9701fc](https://github.com/Datoms-IoT/datoms-webapp/commit/e9701fcf2a0df23a14b867337cf6dda2b97b70c6))

## [3.187.14](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.13...v3.187.14) (2024-12-13)


### Bug Fixes

* **event view:** door status graph upper limit set to 1 ([3f76e4a](https://github.com/Datoms-IoT/datoms-webapp/commit/3f76e4acf855d979132b0d889481a8b53f2aa360))

## [3.187.13](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.12...v3.187.13) (2024-12-13)


### Bug Fixes

* **event view:** remove graph from pdf download ([4105dcf](https://github.com/Datoms-IoT/datoms-webapp/commit/4105dcf48e729a31b92e1c2b0e00489f15c02b91))

## [3.187.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.11...v3.187.12) (2024-12-13)


### Bug Fixes

* **event view:** door status graph label fixed ([c8518c9](https://github.com/Datoms-IoT/datoms-webapp/commit/c8518c9f766f3b7833a1326fc69e92eb1569bbcd))

## [3.187.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.10...v3.187.11) (2024-12-13)


### Bug Fixes

* **views:** updated cold rooms parameter names and order ([f863c29](https://github.com/Datoms-IoT/datoms-webapp/commit/f863c29652bffc57119e3361eb3dcfe1862985ba))

## [3.187.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.9...v3.187.10) (2024-12-13)


### Bug Fixes

* **event view:** added additional date range in filters ([1e4e463](https://github.com/Datoms-IoT/datoms-webapp/commit/1e4e4636f9a27be72345fe34cc7300e377852e0a))

## [3.187.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.8...v3.187.9) (2024-12-12)


### Bug Fixes

* **asset config:** added gujarat spcb in third party data push ([453058e](https://github.com/Datoms-IoT/datoms-webapp/commit/453058eed989d179e9a2ad6885333b516367336e))

## [3.187.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.7...v3.187.8) (2024-12-12)


### Bug Fixes

* **event view:** added door open duration filter & total event in summary for cold rooms ([2443884](https://github.com/Datoms-IoT/datoms-webapp/commit/24438843576622f70a3527909964d04a51a2bde1))

## [3.187.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.6...v3.187.7) (2024-12-12)


### Bug Fixes

* **alerts:** timeout zero provision for timeout alerts ([028ecf8](https://github.com/Datoms-IoT/datoms-webapp/commit/028ecf8f2437cd676699ea9a86ecaa0c33c6bfc4))

## [3.187.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.5...v3.187.6) (2024-12-12)


### Bug Fixes

* **alerts:** added threshold value update along with timeout rules ([17439bd](https://github.com/Datoms-IoT/datoms-webapp/commit/17439bdffa483a3ea9513fdf84545bbaa02fbad2))
* **generic views:** socket interval reduced, and added direct update for active assets ([1b22765](https://github.com/Datoms-IoT/datoms-webapp/commit/1b22765196e9f3e0c58200ea4fa6a66e4fb6a1f3))

## [3.187.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.4...v3.187.5) (2024-12-11)


### Bug Fixes

* **ojus mobile:** specific mobile view interaction for ojus ([e108803](https://github.com/Datoms-IoT/datoms-webapp/commit/e108803a62261441fe24b4258284281336f35461))

## [3.187.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.3...v3.187.4) (2024-12-11)


### Bug Fixes

* **detailed view:** yaxis scale updated as per data points for battery soc parameter ([0169596](https://github.com/Datoms-IoT/datoms-webapp/commit/016959625bfe9bc030089980faa117f18305d108))

## [3.187.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.2...v3.187.3) (2024-12-11)


### Bug Fixes

* **detail view graph:** fixed the scaling of detail view graph ([750a2c5](https://github.com/Datoms-IoT/datoms-webapp/commit/750a2c5de38487829279a16bfd52348ec5a6c8af))

## [3.187.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.1...v3.187.2) (2024-12-10)


### Bug Fixes

* **store:** logout url fixed for end customers ([694b50f](https://github.com/Datoms-IoT/datoms-webapp/commit/694b50fc640a116f8fab2b0c30f6aa5ddb752763))

## [3.187.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.187.0...v3.187.1) (2024-12-10)


### Bug Fixes

* **all:** fix in reports and detailed view for battery asset type ([6959a31](https://github.com/Datoms-IoT/datoms-webapp/commit/6959a318eacd9ed5acee0c89df2a7995fe16e85b))

# [3.187.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.186.8...v3.187.0) (2024-12-09)


### Bug Fixes

* **detailed view:** changes for battery asset type ([2cfffbe](https://github.com/Datoms-IoT/datoms-webapp/commit/2cfffbe10a4434680add376b92027bd2fd3037bb))


### Features

* **reports:** added daily report for battery asset type ([7933b9e](https://github.com/Datoms-IoT/datoms-webapp/commit/7933b9e080815991aca829d14ebc7f57f3894b55))

## [3.186.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.186.7...v3.186.8) (2024-12-09)


### Bug Fixes

* **realtime:** added gas consume parameter in realtime pages of all templates ([821f68c](https://github.com/Datoms-IoT/datoms-webapp/commit/821f68c2a200e131f9cfc69943b91d03bb40a6df))

## [3.186.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.186.6...v3.186.7) (2024-12-09)


### Bug Fixes

* **customer edit:** template id condition for mahindra fixed ([53e70f4](https://github.com/Datoms-IoT/datoms-webapp/commit/53e70f416fe3ad6f5bc29c4663564544de95e14f))

## [3.186.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.186.5...v3.186.6) (2024-12-09)


### Bug Fixes

* **kpi filter:** removed total fault count from kpi filter for perfect generators ([0845196](https://github.com/Datoms-IoT/datoms-webapp/commit/084519635893c30ee8a0e368047523c8e63876af))

## [3.186.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.186.4...v3.186.5) (2024-12-06)


### Bug Fixes

* **all:** fixed issues in map-view, site-view & asset list ([5c235db](https://github.com/Datoms-IoT/datoms-webapp/commit/5c235dbf4815f5e8971e02a6ac488e96b47cde25))

## [3.186.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.186.3...v3.186.4) (2024-12-06)


### Bug Fixes

* **mapview:** fixed parameter order in mapview for cold room ([72a645b](https://github.com/Datoms-IoT/datoms-webapp/commit/72a645bc87dca2c0da3a5efcd6f439605ea01cbf))

## [3.186.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.186.2...v3.186.3) (2024-12-06)

## [3.186.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.186.1...v3.186.2) (2024-12-06)


### Bug Fixes

* **all:** changes related to cold rooms & fixed bugs in mobile version ([52ff695](https://github.com/Datoms-IoT/datoms-webapp/commit/52ff695fd28e9c01e9e90ada7ecd84a2c8d717a3))

## [3.186.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.186.0...v3.186.1) (2024-12-05)


### Bug Fixes

* **page name in header:** fixed page name not coming in header ([28e1b81](https://github.com/Datoms-IoT/datoms-webapp/commit/28e1b81ba774bb8745ecc1f82356085fd43315c4))

# [3.186.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.185.4...v3.186.0) (2024-12-05)


### Features

* **options api:** integrated new options API ([2f6e4a1](https://github.com/Datoms-IoT/datoms-webapp/commit/2f6e4a10c9d34613bd6285462f1e8497ffaf14c0))

## [3.185.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.185.3...v3.185.4) (2024-12-04)


### Bug Fixes

* **trip view:** disabled graph for non supported asset categories ([90140f7](https://github.com/Datoms-IoT/datoms-webapp/commit/90140f7d62ca94d8993e336fa2bd37b5081db150))

## [3.185.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.185.2...v3.185.3) (2024-12-04)


### Bug Fixes

* **ojus map view:** parameters and detailed view link removed from compressor of ojus end customer ([0566527](https://github.com/Datoms-IoT/datoms-webapp/commit/05665276d62baed1834b36fec7f3c567b6c7fb1c))

## [3.185.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.185.1...v3.185.2) (2024-12-04)


### Bug Fixes

* **new asset list:** implemented new asset list api ([ead2aa7](https://github.com/Datoms-IoT/datoms-webapp/commit/ead2aa7bc6356cc0ca29c25c6fe7da8357752fb7))

## [3.185.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.185.0...v3.185.1) (2024-12-03)


### Bug Fixes

* **reports:** some site reports not opening issue fixed ([7c7658a](https://github.com/Datoms-IoT/datoms-webapp/commit/7c7658afc90bd77dd8da3c5b82aff95dc0b07deb))

# [3.185.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.184.14...v3.185.0) (2024-12-03)


### Features

* **asset config:** added mhpcb in 3rd party data push option ([70c32de](https://github.com/Datoms-IoT/datoms-webapp/commit/70c32de2dfa6f53a03037d1735d057c9269130f3))

## [3.184.14](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.184.13...v3.184.14) (2024-12-03)


### Bug Fixes

* **panel data:** fixed the incosistency in the retained value among different views ([47fde97](https://github.com/Datoms-IoT/datoms-webapp/commit/47fde976f457ab42d49693ea76bd35a1d62204a0))

## [3.184.13](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.184.12...v3.184.13) (2024-12-02)


### Bug Fixes

* **custom reports:** data for incorrect date range showing ([651fd10](https://github.com/Datoms-IoT/datoms-webapp/commit/651fd107d995edba3d7d71639eae3da75a4a4a8b))

## [3.184.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.184.11...v3.184.12) (2024-11-29)


### Bug Fixes

* **asset config:** added new parameter units for asset config ([b45cbc0](https://github.com/Datoms-IoT/datoms-webapp/commit/b45cbc0559e5ebdb66cb346cfc9ffbe9b019bf30))

## [3.184.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.184.10...v3.184.11) (2024-11-29)


### Bug Fixes

* **asset config:** device list api changed to lite version to improve performance ([2b8985d](https://github.com/Datoms-IoT/datoms-webapp/commit/2b8985dd852c27ca2bacaa59362aaa1d9de6fb96))

## [3.184.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.184.9...v3.184.10) (2024-11-29)


### Bug Fixes

* **asset config:** added additional data push servers for aurassure ([3a74381](https://github.com/Datoms-IoT/datoms-webapp/commit/3a74381e197f968cd9cf89cf7c0b5505df954b39))

## [3.184.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.184.8...v3.184.9) (2024-11-28)

## [3.184.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.184.7...v3.184.8) (2024-11-28)


### Bug Fixes

* **kpi filter:** fixed Fault KPI filter not working in map view ([529f55c](https://github.com/Datoms-IoT/datoms-webapp/commit/529f55cf636bc6a67c33833aad6c437c073469d1))

## [3.184.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.184.6...v3.184.7) (2024-11-28)

## [3.184.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.184.5...v3.184.6) (2024-11-28)


### Bug Fixes

* **assets list:** fixed subscribed on data disappering after asset deactivation in asset list ([5754629](https://github.com/Datoms-IoT/datoms-webapp/commit/5754629545c1d9989e3af927ef0f2e869a4293d9))

## [3.184.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.184.4...v3.184.5) (2024-11-25)


### Bug Fixes

* **mps ui issues:** fixed Asset and Customer Filter in the Views ([0131712](https://github.com/Datoms-IoT/datoms-webapp/commit/0131712d16cc23831e1f0f2fed8d6c91dc841c9f))

## [3.184.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.184.3...v3.184.4) (2024-11-25)


### Bug Fixes

* **cooper mobile view:** fixed white screen issue on closing analog view in the mobile for Cooper ([a01a7fd](https://github.com/Datoms-IoT/datoms-webapp/commit/a01a7fdb0a60f5d6341f6036e18192ba1ddd1915))

## [3.184.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.184.2...v3.184.3) (2024-11-25)


### Bug Fixes

* **aurassure new reports:** made some enhancements and fixed bugs in aurassure new reports ([03fa583](https://github.com/Datoms-IoT/datoms-webapp/commit/03fa583a4831c98ce2f750cb61256d8f38f919d4))

## [3.184.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.184.1...v3.184.2) (2024-11-25)


### Bug Fixes

* **asset config:** page not opening issue fixed ([9051715](https://github.com/Datoms-IoT/datoms-webapp/commit/905171524c79bd46109e228c61c7db9e30213831))

## [3.184.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.184.0...v3.184.1) (2024-11-25)


### Bug Fixes

* **pagefilter:** added support for asset category api controlled filter options ([81679a5](https://github.com/Datoms-IoT/datoms-webapp/commit/81679a57279a4ee5a27857b54e92cb5d02854908))

# [3.184.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.183.0...v3.184.0) (2024-11-22)


### Features

* **aurassure new reports:** added hourly, daily and monthly reports for Aurassure ([30ca617](https://github.com/Datoms-IoT/datoms-webapp/commit/30ca617880aecae414d07ad08d64ef57696e6fc9))

# [3.183.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.182.9...v3.183.0) (2024-11-22)


### Features

* **assets:** new assets list added for datoms-x ([1edc8ac](https://github.com/Datoms-IoT/datoms-webapp/commit/1edc8acc1b1f1de8eb475cf0d67ed085565f2ec2))

## [3.182.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.182.8...v3.182.9) (2024-11-21)


### Bug Fixes

* **reports:** multi asset report download fix ([8c100b5](https://github.com/Datoms-IoT/datoms-webapp/commit/8c100b535c021eb1647524479c392573b14e208e))

## [3.182.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.182.7...v3.182.8) (2024-11-21)


### Bug Fixes

* **asset config:** fixed param configs for fault parameters ([1847665](https://github.com/Datoms-IoT/datoms-webapp/commit/18476654e15c9e1743f8fd241df3836dc579fe67))

## [3.182.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.182.6...v3.182.7) (2024-11-20)


### Bug Fixes

* **reports:** multi asset report download name fix ([d478cee](https://github.com/Datoms-IoT/datoms-webapp/commit/d478cee71566c8ad6d8923bedf74a6a1555216de))

## [3.182.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.182.5...v3.182.6) (2024-11-20)


### Bug Fixes

* **reports:** multi asset report auto download fix ([0fc5aa4](https://github.com/Datoms-IoT/datoms-webapp/commit/0fc5aa4f52c47e83f7ba62e6d841c1672431ea4a))

## [3.182.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.182.4...v3.182.5) (2024-11-20)


### Bug Fixes

* **reports:** all assets dg daily report auto download if things key not present ([2222a32](https://github.com/Datoms-IoT/datoms-webapp/commit/2222a32f5a208ebd743722949eeedb02695a7da2))

## [3.182.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.182.3...v3.182.4) (2024-11-20)


### Bug Fixes

* **reports:** added auto report download for multi asset report ([446b9f1](https://github.com/Datoms-IoT/datoms-webapp/commit/446b9f1f086589519817e97fea0638e727275094))

## [3.182.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.182.2...v3.182.3) (2024-11-20)


### Bug Fixes

* **trip report:** increased the results per page for trip report to 2000 ([12ebf3e](https://github.com/Datoms-IoT/datoms-webapp/commit/12ebf3eb4ddc839e0cac91c62493b1f945e17878))

## [3.182.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.182.1...v3.182.2) (2024-11-19)


### Bug Fixes

* **real time data:** fixed the decimal value not coming in the real time flip component ([b5f307f](https://github.com/Datoms-IoT/datoms-webapp/commit/b5f307f8145c5891c8a774c10f944ef982b0775f))

## [3.182.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.182.0...v3.182.1) (2024-11-18)


### Bug Fixes

* **real time default value:** fixed the default value in real time if the device is offline ([599e933](https://github.com/Datoms-IoT/datoms-webapp/commit/599e93367f46beb5d3a391883748c0fbe0f8c663))

# [3.182.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.181.3...v3.182.0) (2024-11-18)


### Features

* **asset config:** save asset config & parameters without device ([2006e52](https://github.com/Datoms-IoT/datoms-webapp/commit/2006e52444ded7e3d17b3a16e309ee471c6b31a0))

## [3.181.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.181.2...v3.181.3) (2024-11-18)


### Bug Fixes

* **fault section:** fixed the fault section to include OK in the fault name ([bd79a48](https://github.com/Datoms-IoT/datoms-webapp/commit/bd79a4875d59a780fa77f83ca553e1e29b06ad54))

## [3.181.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.181.1...v3.181.2) (2024-11-15)


### Bug Fixes

* **views:** page invalid issue fix ([eff4667](https://github.com/Datoms-IoT/datoms-webapp/commit/eff466759f3293b8dc3250bfe40926f20b8d26e5))

## [3.181.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.181.0...v3.181.1) (2024-11-14)


### Bug Fixes

* **asset config:** serial port select option for rs232 for device type ecu1051 ([735839e](https://github.com/Datoms-IoT/datoms-webapp/commit/735839ee7f6671cb0d3a08bc0b2de0ddfa5ef38f))
* **reports:** custom report not opening fixed ([58f0c79](https://github.com/Datoms-IoT/datoms-webapp/commit/58f0c79d48d9d70fba92731f906756cb5de0693f))

# [3.181.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.180.2...v3.181.0) (2024-11-11)


### Features

* **assets:** added page to show third party data push logs ([3030ce1](https://github.com/Datoms-IoT/datoms-webapp/commit/3030ce16474b7a20cd16641db13328dbb5c9b52e))

## [3.180.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.180.1...v3.180.2) (2024-11-11)


### Bug Fixes

* **runhour format:** fixed the format of lifetime runhour in panel and detail view ([141cd77](https://github.com/Datoms-IoT/datoms-webapp/commit/141cd773a5c66dd4b06e00871bf371c95f5a969f))

## [3.180.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.180.0...v3.180.1) (2024-11-11)


### Bug Fixes

* **flow and motor:** added water flow in the panel view of flow and motor ([78befa5](https://github.com/Datoms-IoT/datoms-webapp/commit/78befa54807e3a77dbef28362bbb2aef53cacfe5))

# [3.180.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.179.8...v3.180.0) (2024-11-07)


### Features

* **asset config:** added option for data push to dpcc ([bd387cb](https://github.com/Datoms-IoT/datoms-webapp/commit/bd387cb9f2787fcf24c751977864e0c6c25e883e))

## [3.179.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.179.7...v3.179.8) (2024-11-06)


### Bug Fixes

* **list view:** fixed the render issue of list view ([717f53b](https://github.com/Datoms-IoT/datoms-webapp/commit/717f53bf7635603d1455878dfde129294c3d70ad))

## [3.179.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.179.6...v3.179.7) (2024-11-06)


### Bug Fixes

* **aurassure report:** fixed the site not coming in csv report and header row getting sorted ([466033f](https://github.com/Datoms-IoT/datoms-webapp/commit/466033f180e49ff4f56ebcfd8b11442ad501e12d))

## [3.179.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.179.5...v3.179.6) (2024-11-06)


### Bug Fixes

* **aurassure aaqms report:** sorted the AAQMS Report data according to the site name ([00088e5](https://github.com/Datoms-IoT/datoms-webapp/commit/00088e5f7b86d2754f7538c924eb963ca5159367))

## [3.179.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.179.4...v3.179.5) (2024-11-05)


### Bug Fixes

* **views in mobile app:** fixed the issue of views not coming in the mobile app ([8042c09](https://github.com/Datoms-IoT/datoms-webapp/commit/8042c099e2d7c8ab77b9702936b0d89962a4f55b))

## [3.179.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.179.3...v3.179.4) (2024-11-05)


### Bug Fixes

* **custom report:** fixed the order of the custom report data points in the table ([7273da8](https://github.com/Datoms-IoT/datoms-webapp/commit/7273da887caf4ea6db68c9b3a7302161a8602be3))

## [3.179.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.179.2...v3.179.3) (2024-11-04)


### Bug Fixes

* **analog faults:** fixed the faults not coming in the analog view ([f890c1e](https://github.com/Datoms-IoT/datoms-webapp/commit/f890c1e74b181c4dd30175da38ef9029b85f7c89))

## [3.179.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.179.1...v3.179.2) (2024-11-04)


### Bug Fixes

* **life time runhour:** fixed the incorrect value of lifetime runhour in detail view and panel view ([75a72e3](https://github.com/Datoms-IoT/datoms-webapp/commit/75a72e3cfe45e63e7e39e59952d4031a99eb699d))

## [3.179.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.179.0...v3.179.1) (2024-10-30)


### Bug Fixes

* **gravity report:** changed the url for api calling in the gravity reports ([64a8f58](https://github.com/Datoms-IoT/datoms-webapp/commit/64a8f58f01b81fc8b55d951f3a1901111ac8ffae))

# [3.179.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.178.6...v3.179.0) (2024-10-30)


### Features

* **configurable report & component:** added configurable table, chart and added multi-asset report ([1c64abc](https://github.com/Datoms-IoT/datoms-webapp/commit/1c64abc5b30c25ee0f4ad953fd2bc9dea7fd8270))
* **gravity reports:** added report for gravity framework ([abea0d1](https://github.com/Datoms-IoT/datoms-webapp/commit/abea0d1320cc5de2d49f1ef363d9c64d1a38a622))

## [3.178.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.178.5...v3.178.6) (2024-10-29)


### Bug Fixes

* **assets:** asset deactivate column not showing fixed ([d6b4111](https://github.com/Datoms-IoT/datoms-webapp/commit/d6b41112efe8a34089c8ff210a9ecf29324e386b))

## [3.178.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.178.4...v3.178.5) (2024-10-29)


### Bug Fixes

* **aurassure report:** fixed the same value coming for all the parameters in the report ([f399873](https://github.com/Datoms-IoT/datoms-webapp/commit/f39987355e383aace8f184547af55218fbfe560a))

## [3.178.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.178.3...v3.178.4) (2024-10-29)


### Bug Fixes

* **dg new template:** added Lifetime Runhour in the new DG Template and added lifetime runhour ([1dffc66](https://github.com/Datoms-IoT/datoms-webapp/commit/1dffc66a07e52633a7f2bf4c902d6510af6e5e8b))

## [3.178.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.178.2...v3.178.3) (2024-10-28)


### Bug Fixes

* **aurassure report:** aurassure Threshold report ([19b05dc](https://github.com/Datoms-IoT/datoms-webapp/commit/19b05dcd83d8dc4cf7c4bb58ecd3b816d8ad3ab9))

## [3.178.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.178.1...v3.178.2) (2024-10-27)


### Bug Fixes

* **asset config:** asset template not loaded correctly fixed ([37bac44](https://github.com/Datoms-IoT/datoms-webapp/commit/37bac44cd4265b2743fceda0e18c297900f6c56c))

## [3.178.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.178.0...v3.178.1) (2024-10-25)


### Bug Fixes

* **aurassure report:** added aurassure daily threshold report ([5e3ac3b](https://github.com/Datoms-IoT/datoms-webapp/commit/5e3ac3b524e8cc20499f9939a8e46d7f68e0ab82))

# [3.178.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.20...v3.178.0) (2024-10-25)


### Features

* **multi asset report:** added Multi Asset Report for Aurassure - Jindal ([535dbaa](https://github.com/Datoms-IoT/datoms-webapp/commit/535dbaa6d784595577ee89b6599419a1a69c043c))

## [3.177.20](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.19...v3.177.20) (2024-10-24)


### Bug Fixes

* **global store:** added default page title/favicon for whitelabelled customers ([31845f8](https://github.com/Datoms-IoT/datoms-webapp/commit/31845f8a783abfbe4b183364dac788b22cfaaac8))

## [3.177.19](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.18...v3.177.19) (2024-10-24)


### Bug Fixes

* **global store:** zapscale tracking condition updated to track only direct datomsx customers ([e85e3b6](https://github.com/Datoms-IoT/datoms-webapp/commit/e85e3b6e6a07cc433e9c83a9629d64deb6e2fbe2))
* **production module:** added new sensor properties and sensor type support ([90c0117](https://github.com/Datoms-IoT/datoms-webapp/commit/90c0117d91de41c824979206356800d41c6f59b8))

## [3.177.18](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.17...v3.177.18) (2024-10-24)


### Bug Fixes

* **elgi analog view:** fIxed the ranges of Analog View for Elgi ([6862ee5](https://github.com/Datoms-IoT/datoms-webapp/commit/6862ee5e18345b0cdd8d539a4361266c6e81353e))

## [3.177.17](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.16...v3.177.17) (2024-10-23)


### Bug Fixes

* **help section:** fixed the help section not coming for resellers ([4902755](https://github.com/Datoms-IoT/datoms-webapp/commit/4902755e46038aa0b57ed961542399fe849fa87c))

## [3.177.16](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.15...v3.177.16) (2024-10-23)


### Bug Fixes

* **subscription:** updated condition for subscription upgrade ([e77a97b](https://github.com/Datoms-IoT/datoms-webapp/commit/e77a97ba355b95ab9bf2b41bb75ca789f12ae039))

## [3.177.15](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.14...v3.177.15) (2024-10-23)


### Bug Fixes

* **analog view:** analog view not opening ([4f1c638](https://github.com/Datoms-IoT/datoms-webapp/commit/4f1c6385e96885c2eac3c198f152b9111a22287c))

## [3.177.14](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.13...v3.177.14) (2024-10-23)


### Bug Fixes

* **analog view:** analog view not opening issue ([d637b47](https://github.com/Datoms-IoT/datoms-webapp/commit/d637b4724e20ce0d19858aa9d0111658944eae17))

## [3.177.13](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.12...v3.177.13) (2024-10-22)


### Bug Fixes

* **cooper analog view:** fixed the error coming in analog view for cooper ([a457043](https://github.com/Datoms-IoT/datoms-webapp/commit/a457043bb8d6dff1724fb0c109f8d94e3f5c2f97))

## [3.177.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.11...v3.177.12) (2024-10-22)


### Bug Fixes

* **aurassure icons:** changed the icons for detail, panel and list view for aurassure ([867a81e](https://github.com/Datoms-IoT/datoms-webapp/commit/867a81e710925a3ccaee23b13e95ee29aeb35c3d))

## [3.177.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.10...v3.177.11) (2024-10-21)

## [3.177.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.9...v3.177.10) (2024-10-21)


### Bug Fixes

* **custom report:** fixed the custom report not opening when all param in one graph selected ([44853e9](https://github.com/Datoms-IoT/datoms-webapp/commit/44853e98d913b63690d20e5f6dfb08f08e919906))

## [3.177.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.8...v3.177.9) (2024-10-21)


### Bug Fixes

* **elgi analog view:** changed the max value and the danger zone values for elgi in analog ([8b2b876](https://github.com/Datoms-IoT/datoms-webapp/commit/8b2b8768ae57059004bbbdffec41f8324c0e113c))

## [3.177.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.7...v3.177.8) (2024-10-18)


### Bug Fixes

* **dg new template:** migrated murari power customers to new dg template and for partner views ([15aa9aa](https://github.com/Datoms-IoT/datoms-webapp/commit/15aa9aa8d12d79bde8c795cc99a4bed299b23454))

## [3.177.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.6...v3.177.7) (2024-10-18)


### Bug Fixes

* **fuel tank status:** fixed the asset status of fuel tank on hover ([024bcbb](https://github.com/Datoms-IoT/datoms-webapp/commit/024bcbb86b6c2938bb184d94f36edada3c168043))

## [3.177.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.5...v3.177.6) (2024-10-18)


### Bug Fixes

* **device and asset status:** fixed the asset and device status of Calcom Assets ([3d49b6c](https://github.com/Datoms-IoT/datoms-webapp/commit/3d49b6cff5d2762f4d47fbbae10266c9d469bf07))

## [3.177.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.4...v3.177.5) (2024-10-18)


### Bug Fixes

* **asset config:** handled aurassure specific conditions for thier resellers ([05ca341](https://github.com/Datoms-IoT/datoms-webapp/commit/05ca341aadd9fc8cb34018092a0205262f8ecd06))

## [3.177.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.3...v3.177.4) (2024-10-18)


### Bug Fixes

* **elgi analog view:** changed the range for Elgi Analog View according to the kVA ([3b9f41a](https://github.com/Datoms-IoT/datoms-webapp/commit/3b9f41a24358a4aa00af6fd0dff86bab3d29801f))
* **elgi analog view:** min Max and Danger zones for different rated DG ([55a9b9f](https://github.com/Datoms-IoT/datoms-webapp/commit/55a9b9f47d940502f279a4c6b7d67b9d7e820f0b))

## [3.177.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.2...v3.177.3) (2024-10-17)


### Bug Fixes

* **hotspot report:** adding hotspots for non-aqi parameters ([a9493a3](https://github.com/Datoms-IoT/datoms-webapp/commit/a9493a3cf3aa77879c35560e88f5072af63f88ec))

## [3.177.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.1...v3.177.2) (2024-10-17)


### Bug Fixes

* **asset config:** added network address field in protocol config ([26f30d3](https://github.com/Datoms-IoT/datoms-webapp/commit/26f30d3ebef6af08941a9cb6f83ab7e3652a95de))

## [3.177.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.177.0...v3.177.1) (2024-10-17)


### Bug Fixes

* **devices:** show both assigned and unassined devices in customer section for partners ([f5c54fb](https://github.com/Datoms-IoT/datoms-webapp/commit/f5c54fbafd510ce4ddd396c71cce51605fb43459))

# [3.177.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.176.5...v3.177.0) (2024-10-16)


### Features

* asset transfer from goem to other resellers of same partner ([a757d85](https://github.com/Datoms-IoT/datoms-webapp/commit/a757d858b65f5fb56591cf344bbf38b5b53152a2))

## [3.176.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.176.4...v3.176.5) (2024-10-16)


### Bug Fixes

* **cooper activities in real time:** fixed the issue of  activities not coming in the real time view ([64e636e](https://github.com/Datoms-IoT/datoms-webapp/commit/64e636ea1b32f56e88afd90b483ef440f1ffa2ca))

## [3.176.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.176.3...v3.176.4) (2024-10-15)


### Bug Fixes

* **selco site report:** fixed the issue of selco site report not getting downloaded ([4ba5a32](https://github.com/Datoms-IoT/datoms-webapp/commit/4ba5a326fb7ec98d501e1ab25e2ac29a3b978fd7))

## [3.176.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.176.2...v3.176.3) (2024-10-14)


### Bug Fixes

* **dg status:** fixed the discrepancy of the DG Status between map view and detailed view ([88f8499](https://github.com/Datoms-IoT/datoms-webapp/commit/88f8499ff717464f9d21269570cf064a03a3bd85))

## [3.176.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.176.1...v3.176.2) (2024-10-14)


### Bug Fixes

* **mahindra map view:** fixed the map view not opening for mahindra ([cf87097](https://github.com/Datoms-IoT/datoms-webapp/commit/cf87097b89c141dea0f3081ebd05b72417111b7e))

## [3.176.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.176.0...v3.176.1) (2024-10-14)


### Bug Fixes

* **platform opening issue:** platform opening issue fixed for cooper ([57a10d3](https://github.com/Datoms-IoT/datoms-webapp/commit/57a10d3518fe26ff6d0aa429ef130845f1f68d24))

# [3.176.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.175.8...v3.176.0) (2024-10-10)


### Features

* **aurassure custom report:** aurassure custom report changes ([9714ccb](https://github.com/Datoms-IoT/datoms-webapp/commit/9714ccb38d3d290a4177617e95827fabf8095a8f))

## [3.175.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.175.7...v3.175.8) (2024-10-10)


### Bug Fixes

* template id for mahindra fixed ([6ef7a4a](https://github.com/Datoms-IoT/datoms-webapp/commit/6ef7a4a3b79758738ffff06f8613b3f9cf6dbc57))

## [3.175.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.175.6...v3.175.7) (2024-10-10)


### Bug Fixes

* **ojus analog view:** fixed the needle at the end if the value exceeds the maximum value ([20aa086](https://github.com/Datoms-IoT/datoms-webapp/commit/20aa086c86851f03c1f6b267bb4af4638a591e69))

## [3.175.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.175.5...v3.175.6) (2024-10-10)


### Bug Fixes

* **asset:** partner list wrong in datomsx asset add ([db4be25](https://github.com/Datoms-IoT/datoms-webapp/commit/db4be25d2f461b196e8a52d77e9f1958cb7e034e))

## [3.175.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.175.4...v3.175.5) (2024-10-10)


### Bug Fixes

* **ojus analog view:** changing the parameter and adding the conversion factor accordingly ([3b7c4dd](https://github.com/Datoms-IoT/datoms-webapp/commit/3b7c4dd6ffc87d0673a36aba57802c417280bcc4))

## [3.175.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.175.3...v3.175.4) (2024-10-09)


### Bug Fixes

* organization profile after demo changes ([72cea1a](https://github.com/Datoms-IoT/datoms-webapp/commit/72cea1ab2ee5374cd3a42ae093275859b168a0dd))

## [3.175.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.175.2...v3.175.3) (2024-10-09)


### Bug Fixes

* **trip report:** offline Run in Trip Report ([bd046b2](https://github.com/Datoms-IoT/datoms-webapp/commit/bd046b29e7c41f3a593fdc447a9d635691817e4c))

## [3.175.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.175.1...v3.175.2) (2024-10-09)


### Bug Fixes

* **territory:** territory link issue from customer details ([fa7e654](https://github.com/Datoms-IoT/datoms-webapp/commit/fa7e65439a32b32ca8d6b9dffa437eab7191d5f4))

## [3.175.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.175.0...v3.175.1) (2024-10-09)


### Bug Fixes

* **territory:** link to territory page from datomsx customer details ([4afdd01](https://github.com/Datoms-IoT/datoms-webapp/commit/4afdd018da0292687f877486e77106f35df8cea1))

# [3.175.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.174.11...v3.175.0) (2024-10-08)


### Features

* **all:** granular feature control for customer,assets,devices & users module ([5cc8bfd](https://github.com/Datoms-IoT/datoms-webapp/commit/5cc8bfd3d60e0239988dba1002db2f33e57d5af6))
* **customers:** partner wise organization profile implemented ([429be62](https://github.com/Datoms-IoT/datoms-webapp/commit/429be62491104be5ca9b28113a0954df3b9b30df))
* **territory:** oem territory for resellers conditions handled ([9b287ea](https://github.com/Datoms-IoT/datoms-webapp/commit/9b287ea4965bc45f5826272ab183afbe4e2ee712))

## [3.174.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.174.10...v3.174.11) (2024-10-08)


### Bug Fixes

* **ojus real time:** fixed the parameter order in Real Time Gauge 3 for Ojus ([7b2f000](https://github.com/Datoms-IoT/datoms-webapp/commit/7b2f000a5bc4e5cea1e3b1ec528c23f0d8852007))

## [3.174.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.174.9...v3.174.10) (2024-10-08)


### Bug Fixes

* **production module:** infra model updated ([3f4f4c9](https://github.com/Datoms-IoT/datoms-webapp/commit/3f4f4c9178c773bab2523ec0449244e8595b86f5))

## [3.174.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.174.8...v3.174.9) (2024-10-07)


### Bug Fixes

* **mahindra detailed view:** changed fuel fill value of DG in Detailed View for Mahindra from L to % ([5532a53](https://github.com/Datoms-IoT/datoms-webapp/commit/5532a53ce4fa6d9339c89fd07fc82754e8af76ad))

## [3.174.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.174.7...v3.174.8) (2024-10-07)


### Bug Fixes

* **assets:** asset add/config access to datomsx users in partner portal ([66defde](https://github.com/Datoms-IoT/datoms-webapp/commit/66defdeb96a212daae69c8e730d69e7274fc35d6))

## [3.174.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.174.6...v3.174.7) (2024-10-04)


### Bug Fixes

* **daily report:** different columns for same report in partner and end customer portal issue fix ([205ac1a](https://github.com/Datoms-IoT/datoms-webapp/commit/205ac1aec1f8f001be27b37f431fdf2832a0151c))

## [3.174.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.174.5...v3.174.6) (2024-10-04)


### Bug Fixes

* **analog view:** hid the tab when no parameter configured ([0271c66](https://github.com/Datoms-IoT/datoms-webapp/commit/0271c66105ddef143b68cf85a124fc4ffa52c9be))

## [3.174.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.174.4...v3.174.5) (2024-10-03)

## [3.174.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.174.3...v3.174.4) (2024-10-03)


### Bug Fixes

* **analog view:** added fault section in real time view for mahindra (template 24) and template 17 ([bb9aaa6](https://github.com/Datoms-IoT/datoms-webapp/commit/bb9aaa63c973ae6004be79369b22cf88d30f4dd7))

## [3.174.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.174.2...v3.174.3) (2024-10-03)


### Bug Fixes

* **reports:** data availability report calendar usability issue fixed ([4f96b33](https://github.com/Datoms-IoT/datoms-webapp/commit/4f96b3380dab1e6e430358e02209d55d9e9d2ff8))

## [3.174.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.174.1...v3.174.2) (2024-10-01)


### Bug Fixes

* **ojus real time:** adjusted the UI for Ojus Analog View according to the mobile ([d377f81](https://github.com/Datoms-IoT/datoms-webapp/commit/d377f81393cd0cca983b1b279e4388b1e339ee0d))

## [3.174.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.174.0...v3.174.1) (2024-10-01)


### Bug Fixes

* **panel view:** fixed the panel view runhour value coming in decimal points ([d6c64b9](https://github.com/Datoms-IoT/datoms-webapp/commit/d6c64b96b7ee350492c7cd8238ebd3bdf62616c2))

# [3.174.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.173.6...v3.174.0) (2024-10-01)


### Features

* **alerts:** maintenance add/edit ui change, provision for description ([da12ed1](https://github.com/Datoms-IoT/datoms-webapp/commit/da12ed10bf1b621091c4e17705e214051a962a18))
* **service module:** maintenance description added in ticket details ([25096a6](https://github.com/Datoms-IoT/datoms-webapp/commit/25096a6096d2488a29c33122f634f16e51d210b0))

## [3.173.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.173.5...v3.173.6) (2024-09-30)


### Bug Fixes

* **asset add:** search fixed for partner field ([7138320](https://github.com/Datoms-IoT/datoms-webapp/commit/7138320871f0aeb2ceb5f116a055b4bd6ef2130c))

## [3.173.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.173.4...v3.173.5) (2024-09-30)


### Bug Fixes

* **devices:** firmware updated when not assigned to customer ([161eea4](https://github.com/Datoms-IoT/datoms-webapp/commit/161eea466dddd937153ded246a7517d9a17c2e0a))

## [3.173.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.173.3...v3.173.4) (2024-09-30)


### Bug Fixes

* **ojus realtime:** scroll issue in mobile fixed ([7470a0b](https://github.com/Datoms-IoT/datoms-webapp/commit/7470a0b1c035b5a7a7eee01b4ba17c113066daa7))

## [3.173.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.173.2...v3.173.3) (2024-09-27)


### Bug Fixes

* on/off component email autofill fixed ([493ccc6](https://github.com/Datoms-IoT/datoms-webapp/commit/493ccc6113b6697135a646db541a4fe4d374c6cf))

## [3.173.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.173.1...v3.173.2) (2024-09-27)


### Bug Fixes

* autocomplete disabled for filter component ([8540df2](https://github.com/Datoms-IoT/datoms-webapp/commit/8540df253a31e2a259ef31016c753329e1e3e53d))

## [3.173.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.173.0...v3.173.1) (2024-09-27)


### Bug Fixes

* **ojus:** fixed the minor UI bug in Ojus Real Time View ([e102400](https://github.com/Datoms-IoT/datoms-webapp/commit/e102400e7846cb59145dd36718664ef280151ac8))

# [3.173.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.172.0...v3.173.0) (2024-09-27)


### Features

* **analog view:** added the new UI of the analog view for OJUS Power ([0e2f154](https://github.com/Datoms-IoT/datoms-webapp/commit/0e2f15408f8fd45329f0ff11d042db11c7712075))

# [3.172.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.171.10...v3.172.0) (2024-09-26)


### Features

* **hotspot report:** hotspot report implemented ([c55d499](https://github.com/Datoms-IoT/datoms-webapp/commit/c55d499ef4c925af0ff8e0aa41d5b86a99edb199))

## [3.171.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.171.9...v3.171.10) (2024-09-26)


### Bug Fixes

* **assets:** preserve existing asset details when configuring an asset ([24d4393](https://github.com/Datoms-IoT/datoms-webapp/commit/24d4393e46583b51c872dda7225a9e243dc64c4e))

## [3.171.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.171.8...v3.171.9) (2024-09-25)


### Bug Fixes

* aurassure reseller condition ([42c38db](https://github.com/Datoms-IoT/datoms-webapp/commit/42c38db2c40f93b5eca258e23386f6ba5bef3582))

## [3.171.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.171.7...v3.171.8) (2024-09-25)


### Bug Fixes

* handled aurassure condition for resellers of aurassure ([33c8e12](https://github.com/Datoms-IoT/datoms-webapp/commit/33c8e12d3389bb4f2150f8a8d9595993b4910ef2))

## [3.171.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.171.6...v3.171.7) (2024-09-23)


### Bug Fixes

* **cooper:** cooper DG not coming issue fixed ([d4570b4](https://github.com/Datoms-IoT/datoms-webapp/commit/d4570b42f770dd888e64e8be50d5bacce279603a))

## [3.171.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.171.5...v3.171.6) (2024-09-23)


### Bug Fixes

* **customer:** page blank in customer add fixed ([0a26960](https://github.com/Datoms-IoT/datoms-webapp/commit/0a26960c7512d63c393e0ab63344efd453473075))

## [3.171.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.171.4...v3.171.5) (2024-09-20)


### Bug Fixes

* **datoms-x:** rental partner template id handled in datoms-x ([d7577a4](https://github.com/Datoms-IoT/datoms-webapp/commit/d7577a4d2c34cf41ebe166dacb5344f7ce42b0c4))

## [3.171.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.171.3...v3.171.4) (2024-09-20)


### Bug Fixes

* **customer:** error in edit fixed ([de10081](https://github.com/Datoms-IoT/datoms-webapp/commit/de10081852c7b538b8c64e806bff3205f9bd28d7))

## [3.171.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.171.2...v3.171.3) (2024-09-19)


### Bug Fixes

* **dg:** template id updated to 29 ([f543435](https://github.com/Datoms-IoT/datoms-webapp/commit/f543435f18d48900579aead7feab32ab130cf14e))

## [3.171.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.171.1...v3.171.2) (2024-09-18)


### Bug Fixes

* **dg:** mahin=dra partner view fix ([443522f](https://github.com/Datoms-IoT/datoms-webapp/commit/443522f2ebfd17377466c3a0ab1868d6ed8e6e1a))

## [3.171.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.171.0...v3.171.1) (2024-09-18)


### Bug Fixes

* **app:** timezone issue fix ([1ae21b8](https://github.com/Datoms-IoT/datoms-webapp/commit/1ae21b8dab7bc45e71765b70df3c458f15c3da7a))

# [3.171.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.170.0...v3.171.0) (2024-09-18)


### Features

* **sonarqube:** adding repo to SonarQube ([4b364ee](https://github.com/Datoms-IoT/datoms-webapp/commit/4b364eea9e24fa23f9f79f2ed03a4e7de5c0dd09))

# [3.170.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.169.11...v3.170.0) (2024-09-18)


### Features

* app.js restructuring using store ([c2d6f35](https://github.com/Datoms-IoT/datoms-webapp/commit/c2d6f354cc183f25a260e781d59514b6e910c1ee))

## [3.169.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.169.10...v3.169.11) (2024-09-17)

## [3.169.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.169.9...v3.169.10) (2024-09-16)


### Bug Fixes

* **param-list:** added data-sheets column in system templates list ([9eba335](https://github.com/Datoms-IoT/datoms-webapp/commit/9eba335a4c6735d0c1e2b862a185e9b4b033080e))

## [3.169.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.169.8...v3.169.9) (2024-09-16)


### Bug Fixes

* **dg:** cooper inducement time param change ([281b53d](https://github.com/Datoms-IoT/datoms-webapp/commit/281b53d1eb719b5806ac46adc0ed47f693da98e7))

## [3.169.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.169.7...v3.169.8) (2024-09-14)


### Bug Fixes

* **cooper:** copper Analog View Fix ([55356cf](https://github.com/Datoms-IoT/datoms-webapp/commit/55356cf953a6d18f524af829cef124eca20935e1))

## [3.169.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.169.6...v3.169.7) (2024-09-13)


### Bug Fixes

* **report:** summary report removed from other thing types report ([4f8c1b5](https://github.com/Datoms-IoT/datoms-webapp/commit/4f8c1b533b12dd69cb4884f09f4164a46d2c57b1))

## [3.169.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.169.5...v3.169.6) (2024-09-13)


### Bug Fixes

* **routes:** added condition of pages to show for public role ([edffdff](https://github.com/Datoms-IoT/datoms-webapp/commit/edffdffa372eccaa8b8cf91b00ebfff24c8a4373))

## [3.169.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.169.4...v3.169.5) (2024-09-13)


### Bug Fixes

* **report:** data availability report for all the customers for datoms-x ([282a37d](https://github.com/Datoms-IoT/datoms-webapp/commit/282a37db19acf829d926d8f30e69708a95135ff6))

## [3.169.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.169.3...v3.169.4) (2024-09-12)


### Bug Fixes

* **aurassure:** aurassure data interval canged ([078e3c3](https://github.com/Datoms-IoT/datoms-webapp/commit/078e3c3bebede1813c6164b48474705e5676852e))

## [3.169.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.169.2...v3.169.3) (2024-09-12)


### Bug Fixes

* **assets:** show data availability column for all users of aurassure ([b5c6836](https://github.com/Datoms-IoT/datoms-webapp/commit/b5c68363b3a67777a1ee70a5692d4e3e61a4e52a))

## [3.169.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.169.1...v3.169.2) (2024-09-12)


### Bug Fixes

* **dg:** cooper rpm limit updated ([7630ef3](https://github.com/Datoms-IoT/datoms-webapp/commit/7630ef3f1ee7a4b6aca616511510a1755ecb3243))

## [3.169.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.169.0...v3.169.1) (2024-09-12)


### Bug Fixes

* **dg:** last fuel filled in % and fuel filled drained removed from report for mahindra ([786b057](https://github.com/Datoms-IoT/datoms-webapp/commit/786b057333daff73113a0180a3c03e5dc8a56fb7))

# [3.169.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.168.3...v3.169.0) (2024-09-11)


### Features

* **assets:** oem asset linking to resellers without end customers ([c012ecb](https://github.com/Datoms-IoT/datoms-webapp/commit/c012ecb08a81f2199167af83fb290a605fbbac2a))

## [3.168.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.168.2...v3.168.3) (2024-09-11)


### Bug Fixes

* **dg:** cooper realtime reactive and apprent power limit changes ([72a0a73](https://github.com/Datoms-IoT/datoms-webapp/commit/72a0a73e1f9f7b61cbbdd66b138024b922105b4a))

## [3.168.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.168.1...v3.168.2) (2024-09-11)


### Bug Fixes

* **assets:** raw log from asset list fixed ([3c7c5ff](https://github.com/Datoms-IoT/datoms-webapp/commit/3c7c5ffbe3163b40ad1af4a83ca702e59fb27d2d))

## [3.168.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.168.0...v3.168.1) (2024-09-11)


### Bug Fixes

* **report:** asset status report vendor id condition updated ([7565d1d](https://github.com/Datoms-IoT/datoms-webapp/commit/7565d1d07f29e1e4417d2e70ad1d5e8309caad87))

# [3.168.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.167.6...v3.168.0) (2024-09-11)


### Features

* **login page:** mobile login page for tata motors ([46b3595](https://github.com/Datoms-IoT/datoms-webapp/commit/46b3595d52795368e2c5c798927d39d243676308))

## [3.167.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.167.5...v3.167.6) (2024-09-10)


### Bug Fixes

* **things:** asset & machine templates fetched for clients instead of partner ([a6d4957](https://github.com/Datoms-IoT/datoms-webapp/commit/a6d4957c740cdabdb013367e21fd7edb612ce2d8))

## [3.167.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.167.4...v3.167.5) (2024-09-10)


### Bug Fixes

* **report:** asset status report territory for reseller updated ([9c703a0](https://github.com/Datoms-IoT/datoms-webapp/commit/9c703a0455e27e043f3125ce4b87e42d3c1faca9))

## [3.167.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.167.3...v3.167.4) (2024-09-09)


### Bug Fixes

* **asset:** commissioning date field moved to quick add section for dg asset type ([079e183](https://github.com/Datoms-IoT/datoms-webapp/commit/079e183d5556e584b66da528ee8d9878ca2253d9))

## [3.167.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.167.2...v3.167.3) (2024-09-09)


### Bug Fixes

* **device:** ota and raw log made feature controlled ([a3ad006](https://github.com/Datoms-IoT/datoms-webapp/commit/a3ad006d9ee078691ae0ee99697c80e065304775))

## [3.167.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.167.1...v3.167.2) (2024-09-09)


### Bug Fixes

* **dg:** cooper dial updated in real time view ([d555303](https://github.com/Datoms-IoT/datoms-webapp/commit/d5553033f363c41cfa77788ffd900737e2462e20))

## [3.167.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.167.0...v3.167.1) (2024-09-09)


### Bug Fixes

* **customers:** given oem user access to add end customer under reseller if add access is available ([36dda52](https://github.com/Datoms-IoT/datoms-webapp/commit/36dda52c89a7170d326e43fdcc6652a8982e0bae))
* **menu:** reports page added for cpcb user of datomsx ([7472636](https://github.com/Datoms-IoT/datoms-webapp/commit/7472636be12d1322392c01acfba6a269a120288e))

# [3.167.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.166.11...v3.167.0) (2024-09-09)


### Features

* **dg:** mains runhour implemented for DG ([c074f43](https://github.com/Datoms-IoT/datoms-webapp/commit/c074f43d4ff9545c45c9dd097c3730d119859b8e))

## [3.166.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.166.10...v3.166.11) (2024-09-07)


### Bug Fixes

* **contact support:** white screen in contact support page ([9afadcc](https://github.com/Datoms-IoT/datoms-webapp/commit/9afadcc5f841faa0a087450e718f4029f6c4c5a0))

## [3.166.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.166.9...v3.166.10) (2024-09-06)


### Bug Fixes

* **menu:** user specific page condition added ([04e821a](https://github.com/Datoms-IoT/datoms-webapp/commit/04e821a4b53d11ac55be2f4546c85d8f340b4a90))

## [3.166.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.166.8...v3.166.9) (2024-09-06)


### Bug Fixes

* **dashboard:** dG Dashboard kpi change ([1f9421a](https://github.com/Datoms-IoT/datoms-webapp/commit/1f9421a3e562437b61d788129de724928cc22631))

## [3.166.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.166.7...v3.166.8) (2024-09-06)


### Bug Fixes

* **dashboard:** fuel tank dashboard changes ([a052f82](https://github.com/Datoms-IoT/datoms-webapp/commit/a052f82071e6ced6d676605e30d96c959dc30688))

## [3.166.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.166.6...v3.166.7) (2024-09-05)


### Bug Fixes

* **reports:** reports download flow updated ([72b507d](https://github.com/Datoms-IoT/datoms-webapp/commit/72b507d6b5a1f6acfc45eb9b0be1768a098f44f3))

## [3.166.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.166.5...v3.166.6) (2024-09-05)


### Bug Fixes

* **firmware:** bin file download issue fixed ([d3e1c8e](https://github.com/Datoms-IoT/datoms-webapp/commit/d3e1c8e8be46f38bc8a2955ccee42b186f10eb53))
* **tickets:** all tickets filtering added from api ([21719e2](https://github.com/Datoms-IoT/datoms-webapp/commit/21719e2b953248b575cd37e11217786077dede9d))

## [3.166.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.166.4...v3.166.5) (2024-09-05)


### Bug Fixes

* **reports:** invalid date removed from min and max at ([5c39b37](https://github.com/Datoms-IoT/datoms-webapp/commit/5c39b37494af58f4a40fe31ce83a1999644fa094))

## [3.166.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.166.3...v3.166.4) (2024-09-05)


### Bug Fixes

* **aurassure:** reports avg data min at and max at updated ([0c4e28d](https://github.com/Datoms-IoT/datoms-webapp/commit/0c4e28d6a705caebc5e3d168a835929309ac33f5))

## [3.166.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.166.2...v3.166.3) (2024-09-05)


### Bug Fixes

* **report:** fuel tank report start fuel and remaining fuel from fuel_litre ([e9c2c20](https://github.com/Datoms-IoT/datoms-webapp/commit/e9c2c20f0662f2a103544d24eb752a43d251038d))

## [3.166.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.166.1...v3.166.2) (2024-09-04)


### Bug Fixes

* **asset report:** asset status report download fixes ([20745b3](https://github.com/Datoms-IoT/datoms-webapp/commit/20745b3420a60ad207f32a8e6420ccd663c331c7))

## [3.166.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.166.0...v3.166.1) (2024-09-04)


### Bug Fixes

* **dg monitoring:** oCL Mobile issue fixes, download flow change in report ([89b34d2](https://github.com/Datoms-IoT/datoms-webapp/commit/89b34d2e1770efc831997a1fd5aadde0c66f5ec4))

# [3.166.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.165.3...v3.166.0) (2024-09-03)


### Bug Fixes

* **device:** bulk firmware update fixed ([3912937](https://github.com/Datoms-IoT/datoms-webapp/commit/391293720b939cab115e38cbbbdcf5759a6c1c58))


### Features

* **parameters:** added option to view thing-category wise onboarded parameters and system templates ([95103ec](https://github.com/Datoms-IoT/datoms-webapp/commit/95103ecf07082bbce9b9eefcb6e209760e2ab64b))

## [3.165.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.165.2...v3.165.3) (2024-09-03)


### Bug Fixes

* **dg:** dG multi asset report data not coming for gas genset fixes ([2d216f4](https://github.com/Datoms-IoT/datoms-webapp/commit/2d216f4a5f21c592f7d7c8e76f0fb0a22d3aaa24))

## [3.165.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.165.1...v3.165.2) (2024-09-03)


### Bug Fixes

* **generic:** new template id changed ([7d8c36c](https://github.com/Datoms-IoT/datoms-webapp/commit/7d8c36c324ea9a1ea84722e6956a78b24bb76e4f))

## [3.165.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.165.0...v3.165.1) (2024-09-02)


### Bug Fixes

* **panel def visualization changes:** panel def visualization changes ([de4dce7](https://github.com/Datoms-IoT/datoms-webapp/commit/de4dce73783fbb7d90508396e8efa8b20fe2c129))

# [3.165.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.164.12...v3.165.0) (2024-09-02)


### Features

* **template:** template selection feature added ([7eea2fa](https://github.com/Datoms-IoT/datoms-webapp/commit/7eea2fa1322d93ba58fd18e9e26078f682d50590))

## [3.164.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.164.11...v3.164.12) (2024-09-02)


### Bug Fixes

* **search filter:** adding search filter in contact support ([4d7a055](https://github.com/Datoms-IoT/datoms-webapp/commit/4d7a055d869b4eed5458cf0ba66efda9996b79cf))

## [3.164.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.164.10...v3.164.11) (2024-09-02)


### Bug Fixes

* **customers:** customer edit issue fixed ([8117f16](https://github.com/Datoms-IoT/datoms-webapp/commit/8117f166d56d0dd557e13903ce47899d573d31cb))

## [3.164.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.164.9...v3.164.10) (2024-08-28)


### Bug Fixes

* **report:** asset status report loading fixed ([9ce12e2](https://github.com/Datoms-IoT/datoms-webapp/commit/9ce12e2f0590ad5c9e25443660cd253b78c6e3a3))

## [3.164.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.164.8...v3.164.9) (2024-08-28)


### Bug Fixes

* **dg status report:** dg status report download fixes for large things ([fe81019](https://github.com/Datoms-IoT/datoms-webapp/commit/fe810199dec892f3fa5d78e1339deffc276d26b3))

## [3.164.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.164.7...v3.164.8) (2024-08-28)


### Bug Fixes

* **drawer close issue:** drawer wont be closed on clicking outside ([249e787](https://github.com/Datoms-IoT/datoms-webapp/commit/249e787e6099ecfe05f4a6d175dc4e32c67750e7))

## [3.164.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.164.6...v3.164.7) (2024-08-28)


### Bug Fixes

* **mobile header:** contact Support Mobile Header ([0afa5c1](https://github.com/Datoms-IoT/datoms-webapp/commit/0afa5c1aff20fb3d82f86c161927f7da05f181cf))

## [3.164.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.164.5...v3.164.6) (2024-08-28)


### Bug Fixes

* **contact support:** contact Support Changes - New fields added in Table, CSM Filter ([6f9c94d](https://github.com/Datoms-IoT/datoms-webapp/commit/6f9c94d737ff111af0677a9cc1e300e9c6cc1a87))

## [3.164.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.164.4...v3.164.5) (2024-08-27)


### Bug Fixes

* **dashboard:** datoms-x views not opening error fixed ([4172b08](https://github.com/Datoms-IoT/datoms-webapp/commit/4172b08162dc51f8bf9d3a946508f54c5484632e))

## [3.164.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.164.3...v3.164.4) (2024-08-23)


### Bug Fixes

* **ip camera:** ip camera status issue fixed ([abf0fcc](https://github.com/Datoms-IoT/datoms-webapp/commit/abf0fcc30eece441c2f8b38d7a7f3b1970ed8679))

## [3.164.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.164.2...v3.164.3) (2024-08-22)


### Bug Fixes

* **dg:** offline status removed when thing is disconnected but device online ([63bc1af](https://github.com/Datoms-IoT/datoms-webapp/commit/63bc1af62380f3f3fe172bf123860200386e660d))

## [3.164.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.164.1...v3.164.2) (2024-08-22)


### Bug Fixes

* **trip report:** mahindra trip report fuel litre to percentage ([908920d](https://github.com/Datoms-IoT/datoms-webapp/commit/908920d76c3708bd74842f1bcf886963568468e1))

## [3.164.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.164.0...v3.164.1) (2024-08-22)


### Bug Fixes

* **dg:** mahindra bug fixes for start and end fuel ([92be26a](https://github.com/Datoms-IoT/datoms-webapp/commit/92be26aaea5ad2e3c4368bfe1bfe869a9890f12a))

# [3.164.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.163.2...v3.164.0) (2024-08-22)


### Bug Fixes

* **asset:** disabled dashboard link from asset list if asset is not assigned to any customer ([ab3a178](https://github.com/Datoms-IoT/datoms-webapp/commit/ab3a1781a5de52545f9e1cc26b6bf36aba0c19d3))
* **asset:** partner non admin users asset data view no data issue fixed ([fbd1f28](https://github.com/Datoms-IoT/datoms-webapp/commit/fbd1f28ea1faf2f0098e34b8e68c88a55dbe3755))


### Features

* **customers:** class type field added in list,details,add,edit pages ([b6ed340](https://github.com/Datoms-IoT/datoms-webapp/commit/b6ed3402ca202a67d375c31bc2109f93dd1ed841))

## [3.163.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.163.1...v3.163.2) (2024-08-21)


### Bug Fixes

* **fault status:** fault status updated in map view ([be15015](https://github.com/Datoms-IoT/datoms-webapp/commit/be15015c1c8f6ef776aaf7145f50254d9e599485))

## [3.163.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.163.0...v3.163.1) (2024-08-21)


### Bug Fixes

* **generic thing category:** generic param order updated ([d826c3c](https://github.com/Datoms-IoT/datoms-webapp/commit/d826c3c505a7e4a8f5c4343f25f2ce5060d24767))

# [3.163.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.162.2...v3.163.0) (2024-08-21)


### Features

* **mahindra:** mahindra fuel param  name changed to Indicative Fuel Level and fuel's L val removed ([7b9cfcd](https://github.com/Datoms-IoT/datoms-webapp/commit/7b9cfcdb490f5abecd33f9048177c291636f3b65))
* **mahindra:** mahindra fuel param name changed and server derived param removed ([5c07ce7](https://github.com/Datoms-IoT/datoms-webapp/commit/5c07ce7867ff57265a33fe28caff82654b8cf36d))

## [3.162.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.162.1...v3.162.2) (2024-08-21)


### Bug Fixes

* **style changes:** fixed the style issues in panel header, panel body and mobile detail view ([79adb68](https://github.com/Datoms-IoT/datoms-webapp/commit/79adb68754b1d42bc06bd96246c0709a0e71ad32))

## [3.162.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.162.0...v3.162.1) (2024-08-19)


### Bug Fixes

* **mobile:** exhaust fan mobile view landing fixed ([9aa4546](https://github.com/Datoms-IoT/datoms-webapp/commit/9aa45463522039637aa69d76c6e827eec95daf14))

# [3.162.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.161.6...v3.162.0) (2024-08-16)


### Features

* **dg:** force mtu status added for dg ([b61a7df](https://github.com/Datoms-IoT/datoms-webapp/commit/b61a7dfc83dd6f030912c66fbb37f3d19159642f))

## [3.161.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.161.5...v3.161.6) (2024-08-14)


### Bug Fixes

* **mobile issue fix:** real time page break issue and default detail view ([260e1da](https://github.com/Datoms-IoT/datoms-webapp/commit/260e1da229f11e7a8701a4e68ebff95a92769fdd))

## [3.161.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.161.4...v3.161.5) (2024-08-14)


### Bug Fixes

* **daily report:** daily report energy sum value fixed ([20a92bd](https://github.com/Datoms-IoT/datoms-webapp/commit/20a92bda70631f5361f20122a2ce34874759a6c1))

## [3.161.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.161.3...v3.161.4) (2024-08-14)


### Bug Fixes

* **exhaust fan view changes:** made the demo changes for exhaust fan ([ac050fa](https://github.com/Datoms-IoT/datoms-webapp/commit/ac050fa08b0fe29267dfc871816cc33d2c9f5ab0))

## [3.161.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.161.2...v3.161.3) (2024-08-14)


### Bug Fixes

* **produciton module:** composite item addition aplhanumeric condition removed for some device types ([b588fd3](https://github.com/Datoms-IoT/datoms-webapp/commit/b588fd3e45e4d8754c0914be6d8c9d2f6e1fdb3b))

## [3.161.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.161.1...v3.161.2) (2024-08-14)


### Bug Fixes

* **asset config:** page going blank ([9b34b59](https://github.com/Datoms-IoT/datoms-webapp/commit/9b34b59659260da7357378bce0cf94799ad8a8e2))

## [3.161.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.161.0...v3.161.1) (2024-08-13)


### Bug Fixes

* **exhaust fan mobile view:** demo changes for exhaust fan mobile view ([9cbc55c](https://github.com/Datoms-IoT/datoms-webapp/commit/9cbc55c24a699ec784d12911a6184fb974916ca1))

# [3.161.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.160.3...v3.161.0) (2024-08-13)


### Bug Fixes

* **mobile view and socket:** exhaust Fan Mobile View and Socket Duration for Switch ([7d2ee8e](https://github.com/Datoms-IoT/datoms-webapp/commit/7d2ee8e6479a962e5aa111e0c1c8f8ab1ac34c66))


### Features

* **exhaust fan mobile view:** exhaust Fan mobile view ([446cd35](https://github.com/Datoms-IoT/datoms-webapp/commit/446cd35d6231ccf492f60eec7f2d6c8324cf38b9))

## [3.160.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.160.2...v3.160.3) (2024-08-13)


### Bug Fixes

* **dg:** cooper bug fixes ([56825f7](https://github.com/Datoms-IoT/datoms-webapp/commit/56825f7a953a906a20e0c9cf967d88151c81575e))

## [3.160.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.160.1...v3.160.2) (2024-08-13)


### Bug Fixes

* **device:** raw log page going blank ([4621309](https://github.com/Datoms-IoT/datoms-webapp/commit/46213092bf6e77cc736c2f31d7ae9e0835180e31))

## [3.160.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.160.0...v3.160.1) (2024-08-13)


### Bug Fixes

* **asset config:** select all fault params when no fault key is present in asset template ([f4a5994](https://github.com/Datoms-IoT/datoms-webapp/commit/f4a5994f33bc6e5c01c7edc6d8f523dffca5a6a4))

# [3.160.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.159.8...v3.160.0) (2024-08-12)


### Features

* **dg:** cooper dg implemented ([260614b](https://github.com/Datoms-IoT/datoms-webapp/commit/260614b55106dcf000b8f37ee6dd271fe5420b73))

## [3.159.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.159.7...v3.159.8) (2024-08-12)


### Bug Fixes

* **customer filter:** customer Filter for Datoms X in Contact Support ([191ef92](https://github.com/Datoms-IoT/datoms-webapp/commit/191ef9211c633d5c50c7bdf5b6eeec75b47c80fb))

## [3.159.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.159.6...v3.159.7) (2024-08-12)


### Bug Fixes

* **fuel tank:** fuel litre parameters implemented in dg ([732e5e4](https://github.com/Datoms-IoT/datoms-webapp/commit/732e5e413611fe58f5d335d59162e16900448144))

## [3.159.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.159.5...v3.159.6) (2024-08-09)


### Bug Fixes

* **switch issues:** switch issues ([131aba8](https://github.com/Datoms-IoT/datoms-webapp/commit/131aba8e790d17d38d4a04eb1db06ef022b0d2ed))

## [3.159.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.159.4...v3.159.5) (2024-08-09)


### Bug Fixes

* **customers:** territory field in customer add made mandatory for mahindra ([3cba639](https://github.com/Datoms-IoT/datoms-webapp/commit/3cba639dc2aaba83f2c90323d373a73af0b54189))

## [3.159.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.159.3...v3.159.4) (2024-08-09)


### Bug Fixes

* **customer filter:** adding customer filter for datoms-x in contact support ([c6a7136](https://github.com/Datoms-IoT/datoms-webapp/commit/c6a71367fa3529ab6b53c9a99f01b24b849e26e6))

## [3.159.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.159.2...v3.159.3) (2024-08-09)


### Bug Fixes

* **mix panel:** mixpanel enabled for all customers ([4729d62](https://github.com/Datoms-IoT/datoms-webapp/commit/4729d626a390267c0865c305909e152b2d651c84))

## [3.159.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.159.1...v3.159.2) (2024-08-08)


### Bug Fixes

* **production:** removed 8 digit qr code condition for certain composite items ([17093af](https://github.com/Datoms-IoT/datoms-webapp/commit/17093afdd9227b8bd9c0a543601d6bf685caf73d))

## [3.159.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.159.0...v3.159.1) (2024-08-07)


### Bug Fixes

* **switch issue:** fixed switch issue update in real time with sockets ([dd012b0](https://github.com/Datoms-IoT/datoms-webapp/commit/dd012b064fcfb48742f43f5e33bd79f5ec3e3439))

# [3.159.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.158.0...v3.159.0) (2024-08-06)


### Features

* **exhaust fan:** greese Buster Exhaust Fan Asset ([154c2f8](https://github.com/Datoms-IoT/datoms-webapp/commit/154c2f88e3ad796829fb9b3cee0a5641500d2ea6))

# [3.158.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.157.6...v3.158.0) (2024-08-06)


### Features

* **data availability report:** data avl report data availability ebnabled ([d682f2c](https://github.com/Datoms-IoT/datoms-webapp/commit/d682f2c1b20858daca68d903e7571ed727387699))

## [3.157.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.157.5...v3.157.6) (2024-08-06)


### Bug Fixes

* **dg:** fuel keys are updated to determine fuel ([6fa5a71](https://github.com/Datoms-IoT/datoms-webapp/commit/6fa5a71ba7d128dd3a875116f084e9f853c314e4))

## [3.157.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.157.4...v3.157.5) (2024-08-05)


### Bug Fixes

* **report:** daily report issue fixes ([9d9f00e](https://github.com/Datoms-IoT/datoms-webapp/commit/9d9f00e91ea2687c009370f330e151904ed89216))

## [3.157.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.157.3...v3.157.4) (2024-08-01)


### Bug Fixes

* **reports:** lifetime report enabled for gas genset ([7f4bef1](https://github.com/Datoms-IoT/datoms-webapp/commit/7f4bef106cfb87b0d4cff14b0230eb0ee3a373e1))

## [3.157.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.157.2...v3.157.3) (2024-07-31)


### Bug Fixes

* **dg:** mobile view status changed ([4a93a60](https://github.com/Datoms-IoT/datoms-webapp/commit/4a93a60d237fa35da5eda6b40d042314a54c7179))

## [3.157.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.157.1...v3.157.2) (2024-07-31)


### Bug Fixes

* **asset:** asset list table horizontal scroll added ([140e1d8](https://github.com/Datoms-IoT/datoms-webapp/commit/140e1d814db04bdef3742a699a98040543912780))
* **reports:** report configure icon removed for datomsx and partners(when end customer is selected) ([b553597](https://github.com/Datoms-IoT/datoms-webapp/commit/b5535971cd27379db541fbca19a5e41ba86826f4))

## [3.157.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.157.0...v3.157.1) (2024-07-30)


### Bug Fixes

* **ui changes:** uI changes ([3bee006](https://github.com/Datoms-IoT/datoms-webapp/commit/3bee006934b2998861b193f0566f0826404237ce))

# [3.157.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.156.4...v3.157.0) (2024-07-29)


### Features

* **gas genset:** gas genset thing type implemented ([2c28398](https://github.com/Datoms-IoT/datoms-webapp/commit/2c28398573c3c98fb4c3580dd2973eef1a1f10d8))

## [3.156.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.156.3...v3.156.4) (2024-07-29)


### Bug Fixes

* **ticket reply prompt:** adding prompt when a user replies to a ticket ([610c103](https://github.com/Datoms-IoT/datoms-webapp/commit/610c1038dc3ac3049c1a4c9c810f23d4cacb0e51))

## [3.156.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.156.2...v3.156.3) (2024-07-29)


### Bug Fixes

* **aurassure:** aurassure offline status fixed ([08a7324](https://github.com/Datoms-IoT/datoms-webapp/commit/08a7324fba49cd4b32f4b251c898c1f6a2231ee6))

## [3.156.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.156.1...v3.156.2) (2024-07-25)


### Bug Fixes

* **reports:** report custom time duration for 36 months ([0f9da35](https://github.com/Datoms-IoT/datoms-webapp/commit/0f9da35c83ce21c077255412041b94340ac2388d))

## [3.156.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.156.0...v3.156.1) (2024-07-25)


### Bug Fixes

* **polling duration:** changed the polling duration from 2 minutes to 3 minutes ([7ca1b80](https://github.com/Datoms-IoT/datoms-webapp/commit/7ca1b807fbfeb352633c99c290c92e7542fa0dd4))

# [3.156.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.155.14...v3.156.0) (2024-07-24)


### Bug Fixes

* **territory:** dealers added in territory tree structure ([2d0fe39](https://github.com/Datoms-IoT/datoms-webapp/commit/2d0fe3904b5b5bf52fa4247928981f098072a98a))


### Features

* **assets:** new customer addition from assets add page & asset customer linking ([7b3927d](https://github.com/Datoms-IoT/datoms-webapp/commit/7b3927d6210c22a33be63a006f9383eb65defb7a))
* **filters:** dealers in territory filter tree in customer/device/assets list ([9f80b90](https://github.com/Datoms-IoT/datoms-webapp/commit/9f80b909e3a3bed884f6201d2e3e36e59e958f24))

## [3.155.14](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.155.13...v3.155.14) (2024-07-24)


### Bug Fixes

* **dg:** connected status implemented for things ([6c94c5d](https://github.com/Datoms-IoT/datoms-webapp/commit/6c94c5d80e45b8131bee6a85102bc5ad06d08f6b))

## [3.155.13](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.155.12...v3.155.13) (2024-07-24)


### Bug Fixes

* **ui fixes:** uI Fixes ([5c13809](https://github.com/Datoms-IoT/datoms-webapp/commit/5c13809c90879677e18a95ba37700507bb585dd9))

## [3.155.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.155.11...v3.155.12) (2024-07-22)


### Bug Fixes

* **alerts:** group alert duplicates issue ([35ac7c7](https://github.com/Datoms-IoT/datoms-webapp/commit/35ac7c7d29d2eff7468c057f7f1bf7fdaf69d9e6))

## [3.155.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.155.10...v3.155.11) (2024-07-19)


### Bug Fixes

* **asset list:** dD and ip camera icon changed ([2cf5e4a](https://github.com/Datoms-IoT/datoms-webapp/commit/2cf5e4ae39ab86a1dd462777d705a9c37a0b4ce8))

## [3.155.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.155.9...v3.155.10) (2024-07-18)


### Bug Fixes

* **report:** report excel format fix, territory filter search fix in asset status report ([db30686](https://github.com/Datoms-IoT/datoms-webapp/commit/db3068696b45603d2bfe7497bc5f1e679e17f9e5))

## [3.155.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.155.8...v3.155.9) (2024-07-18)


### Bug Fixes

* **customer search:** customer search not working ([7ebee8d](https://github.com/Datoms-IoT/datoms-webapp/commit/7ebee8db6449f99ab879f902dd6b3888ede767c8))

## [3.155.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.155.7...v3.155.8) (2024-07-17)


### Bug Fixes

* **alerts:** alerts group enable issue fixed ([37bd9a9](https://github.com/Datoms-IoT/datoms-webapp/commit/37bd9a9c7cda64389aeb2f790145887770dec41b))

## [3.155.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.155.6...v3.155.7) (2024-07-16)


### Bug Fixes

* **report:** report routes fixed for iot ([179a2cc](https://github.com/Datoms-IoT/datoms-webapp/commit/179a2cca7a9494e0b957f5a6c04c5f24cd026c09))

## [3.155.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.155.5...v3.155.6) (2024-07-16)


### Bug Fixes

* **portable compressor:** portable compressor design changes and real time view modifications ([e28f56f](https://github.com/Datoms-IoT/datoms-webapp/commit/e28f56fa51878655dedfbf365fd4efedeed03869))

## [3.155.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.155.4...v3.155.5) (2024-07-16)


### Bug Fixes

* **mobile app:** mobile app version updated ([39ea87b](https://github.com/Datoms-IoT/datoms-webapp/commit/39ea87bee1751f808b3ffda4e0bb8391e0ff56a6))
* **zap scale:** beacket missed issue fixed ([c38a577](https://github.com/Datoms-IoT/datoms-webapp/commit/c38a577877ded1695069171e89e58ed7f7fa915d))

## [3.155.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.155.3...v3.155.4) (2024-07-16)


### Bug Fixes

* **zap scale tracking:** zap scale tracking condition updated for datoms-x users ([9766170](https://github.com/Datoms-IoT/datoms-webapp/commit/976617026c075c92e3a097d0abe561a1c7d27491))

## [3.155.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.155.2...v3.155.3) (2024-07-16)


### Bug Fixes

* **multi asset reports:** multi asset reports no data "-" implemented ([9073e5c](https://github.com/Datoms-IoT/datoms-webapp/commit/9073e5c58b7a1682869cf790b77b0e68622f39ce))

## [3.155.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.155.1...v3.155.2) (2024-07-16)


### Bug Fixes

* **renaming the support:** renaming the support to Contact support and changed the icon ([069e68d](https://github.com/Datoms-IoT/datoms-webapp/commit/069e68d6ecf6f0e11e0d205e605a8db276fdffff))
* **support module:** renaming Tickets module to Support module ([8e28b6d](https://github.com/Datoms-IoT/datoms-webapp/commit/8e28b6df8a8ac3be3f463073cc80674d7b39f4cf))

## [3.155.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.155.0...v3.155.1) (2024-07-15)


### Bug Fixes

* **data availability report:** data availability report current date removed ([80cce1e](https://github.com/Datoms-IoT/datoms-webapp/commit/80cce1ee618ddf4936e9653a3a054a43736f91b6))

# [3.155.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.154.5...v3.155.0) (2024-07-12)


### Features

* **report:** data availability report introduced ([a623c39](https://github.com/Datoms-IoT/datoms-webapp/commit/a623c3946a5671dee8ad74942fbeb45605f85d72))

## [3.154.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.154.4...v3.154.5) (2024-07-12)


### Bug Fixes

* **zapscale:** zapscale tracking added for datoms-x ([19d6147](https://github.com/Datoms-IoT/datoms-webapp/commit/19d61479afec216ca266cc63565abef6e6eca2da))

## [3.154.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.154.3...v3.154.4) (2024-07-12)


### Bug Fixes

* **dg:** mahindra multi asset report remived ([9e5a202](https://github.com/Datoms-IoT/datoms-webapp/commit/9e5a202de551ce9fcf143d907f9a7ac06b914587))

## [3.154.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.154.2...v3.154.3) (2024-07-11)


### Bug Fixes

* **ticket support:** seaparate header option and vendor id 1 ([12a8196](https://github.com/Datoms-IoT/datoms-webapp/commit/12a8196f50a28d88855ecd729613331daddc1fff))
* **ui fixes:** uI Fixes and Vendor 1 access ([8a36ae2](https://github.com/Datoms-IoT/datoms-webapp/commit/8a36ae26a334f44ca79f92a1c1fba3e10754e9af))

## [3.154.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.154.1...v3.154.2) (2024-07-11)


### Bug Fixes

* **store:** added kms restriction info for e-bikes ([c43d48c](https://github.com/Datoms-IoT/datoms-webapp/commit/c43d48c93bf4cd209e22768c4a437cb136c049dd))
* **territory:** territory button enabled for datomsx in customer details ([4c02b88](https://github.com/Datoms-IoT/datoms-webapp/commit/4c02b8838b22468093abb4e125bbe50f0392d9a9))

## [3.154.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.154.0...v3.154.1) (2024-07-11)


### Bug Fixes

* **asset:** unassign device of partner during configuration giving error ([aac1873](https://github.com/Datoms-IoT/datoms-webapp/commit/aac187377645a870965a32466b9da53839cf289f))

# [3.154.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.153.6...v3.154.0) (2024-07-10)


### Features

* **dg:** cpcb 4 parameters added to dg mobile view ([93c0fe2](https://github.com/Datoms-IoT/datoms-webapp/commit/93c0fe2d210ce4009062e90405b6d621acf92a15))

## [3.153.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.153.5...v3.153.6) (2024-07-10)


### Bug Fixes

* **mobile version fix:** aPI fix for mobile version ([7fdf94f](https://github.com/Datoms-IoT/datoms-webapp/commit/7fdf94f2fe06c8c8022f0c2ecac8d2ec2e7d27fb))
* **mobile version:** fixed issues with mobile build ([1c5e60f](https://github.com/Datoms-IoT/datoms-webapp/commit/1c5e60f840a01a21c7cde2ae2ddc4ac30a9d58c3))

## [3.153.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.153.4...v3.153.5) (2024-07-10)


### Bug Fixes

* **asset:** customer linking button fix ([25b555d](https://github.com/Datoms-IoT/datoms-webapp/commit/25b555df526748e0c90bcdd8f0b374fa8b93f494))

## [3.153.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.153.3...v3.153.4) (2024-07-09)


### Bug Fixes

* **email validation:** adding email validation ([5aa1aaa](https://github.com/Datoms-IoT/datoms-webapp/commit/5aa1aaa0c7360ad28c91db317cd9a4378201b8f1))

## [3.153.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.153.2...v3.153.3) (2024-07-09)


### Bug Fixes

* **ui enhancements and bug fixes:** uI Enhancements and fixed bugs related to upload alerts ([28ea22e](https://github.com/Datoms-IoT/datoms-webapp/commit/28ea22ef604cad666975dfb032107a636702c099))

## [3.153.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.153.1...v3.153.2) (2024-07-09)


### Bug Fixes

* **mobile view:** mobile view design changes for new map view ([b92cfd7](https://github.com/Datoms-IoT/datoms-webapp/commit/b92cfd7343e288845acbe8dc91fa8fb91b334c5b))

## [3.153.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.153.0...v3.153.1) (2024-07-09)


### Bug Fixes

* **zapscale tracking:** zapscale tracking script add issue fixed ([3bafee4](https://github.com/Datoms-IoT/datoms-webapp/commit/3bafee4b84e718595d8bc23b408d5006820d5788))

# [3.153.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.152.0...v3.153.0) (2024-07-09)


### Features

* **tracking:** zapscale tracking implemented for direct end customers ([0f34ae1](https://github.com/Datoms-IoT/datoms-webapp/commit/0f34ae1e624f088ab834b79599160dccda07ca87))

# [3.152.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.151.7...v3.152.0) (2024-07-08)


### Features

* **freshdesk tickets:** integrating Freshdesk Ticketing service ([9f574e0](https://github.com/Datoms-IoT/datoms-webapp/commit/9f574e035f461cedc528a318e32cbb955fa04a64))

## [3.151.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.151.6...v3.151.7) (2024-07-05)


### Bug Fixes

* **aurassure:** status report time iussue fixed ([9136d15](https://github.com/Datoms-IoT/datoms-webapp/commit/9136d15e2c9614ec0bea6283dfa3589a38a81d12))

## [3.151.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.151.5...v3.151.6) (2024-07-05)


### Bug Fixes

* **dg:** device status with asset status implemented ([c050b9a](https://github.com/Datoms-IoT/datoms-webapp/commit/c050b9a1035a2567ff2c7b0a3bf91b271c0cb001))

## [3.151.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.151.4...v3.151.5) (2024-07-05)


### Bug Fixes

* **asset:** selected system template not showing issue fixed ([2586dc3](https://github.com/Datoms-IoT/datoms-webapp/commit/2586dc34f206cf59b9b981e7b4219f52f689d146))
* **users:** whatsapp number label changed to mobile number ([87060a8](https://github.com/Datoms-IoT/datoms-webapp/commit/87060a865d69147c6d1d810637fb900705476b4d))

## [3.151.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.151.3...v3.151.4) (2024-07-02)


### Bug Fixes

* **report:** daily report nan fixes ([f34c63c](https://github.com/Datoms-IoT/datoms-webapp/commit/f34c63c47d84512761a8895154706bb71955bf60))

## [3.151.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.151.2...v3.151.3) (2024-07-02)


### Bug Fixes

* **detailed view:** detailed view graph timeout condition upated ([f32721d](https://github.com/Datoms-IoT/datoms-webapp/commit/f32721d3d9dd399f5371b9278aa9a05d697adab9))

## [3.151.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.151.1...v3.151.2) (2024-07-01)


### Bug Fixes

* **multi asset reports:** fuel filled and drained added to multi assets report ([7ee9351](https://github.com/Datoms-IoT/datoms-webapp/commit/7ee9351735a7cdb7dd258e70950d2ac6169614f0))

## [3.151.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.151.0...v3.151.1) (2024-06-28)


### Bug Fixes

* **reports:** multi asset reports ([12b5753](https://github.com/Datoms-IoT/datoms-webapp/commit/12b575394c60698f7af8413d386bd95bea4ce0ad))

# [3.151.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.150.5...v3.151.0) (2024-06-27)


### Features

* **generic:** generic asset time added to the portal for poc purpose ([5817a60](https://github.com/Datoms-IoT/datoms-webapp/commit/5817a6020dae79231364f3df0fc590875071ff49))

## [3.150.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.150.4...v3.150.5) (2024-06-27)


### Bug Fixes

* **report:** unprocessed data condition handled for average ([738027b](https://github.com/Datoms-IoT/datoms-webapp/commit/738027b709ccdce62fed0cb9284955d457801359))

## [3.150.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.150.3...v3.150.4) (2024-06-26)


### Bug Fixes

* **reports:** weekly option enabled in multi asset report ([fc453c2](https://github.com/Datoms-IoT/datoms-webapp/commit/fc453c28094081a2d89267c0d05260d079508f16))

## [3.150.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.150.2...v3.150.3) (2024-06-26)


### Bug Fixes

* **reports:** fault Report added for Process Analyzer ([ed20b5d](https://github.com/Datoms-IoT/datoms-webapp/commit/ed20b5d1f21dfe272628d8d88f7f8253caa9bed5))

## [3.150.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.150.1...v3.150.2) (2024-06-26)


### Bug Fixes

* **custoim report:** custom report raw data unprocessed condition failure-fixed ([bec5c6a](https://github.com/Datoms-IoT/datoms-webapp/commit/bec5c6a211930b6d647ae2abb29eb9f4953ee573))

## [3.150.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.150.0...v3.150.1) (2024-06-25)


### Bug Fixes

* **assets:** dashboard link from list for aaqms thing category updated ([e70dc5b](https://github.com/Datoms-IoT/datoms-webapp/commit/e70dc5bbf7e39c536a9e86ec6ed30a127c132a87))
* **users:** edit form showing incorrect values ([42855b8](https://github.com/Datoms-IoT/datoms-webapp/commit/42855b8e7e7f035661886fd95f6223231721c7ee))

# [3.150.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.149.0...v3.150.0) (2024-06-24)


### Features

* **aurassure:** aurassure unprocessed data and organization settings ([7af961e](https://github.com/Datoms-IoT/datoms-webapp/commit/7af961e6004f2a6301f39c843d62ef8f0400259b))

# [3.149.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.148.10...v3.149.0) (2024-06-24)


### Bug Fixes

* **config:** added configs for typescript support ([d2cd736](https://github.com/Datoms-IoT/datoms-webapp/commit/d2cd736043c213ce5eea738734bfdd0d5ef95e03))


### Features

* **assets:** dmd configuration added in asset config for Display thing type ([ac6aec3](https://github.com/Datoms-IoT/datoms-webapp/commit/ac6aec36a515edebe9c9755d228ac9ebb937c3c2))

## [3.148.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.148.9...v3.148.10) (2024-06-24)


### Bug Fixes

* **asset:** analyzer param edit issue in configuration ([b84c1d3](https://github.com/Datoms-IoT/datoms-webapp/commit/b84c1d3e1831c6f0ec2645b6f6149213d9112f7f))

## [3.148.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.148.8...v3.148.9) (2024-06-24)


### Bug Fixes

* **asset icon:** online offline status fix ([d00e6d4](https://github.com/Datoms-IoT/datoms-webapp/commit/d00e6d47c23d27dc0c4c7862d070ddc437a0386e))

## [3.148.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.148.7...v3.148.8) (2024-06-21)


### Bug Fixes

* **dg:** estimated rnhr calculation fix ([f802451](https://github.com/Datoms-IoT/datoms-webapp/commit/f80245193440a61a3fbe9c6cfe9a32362ab004fd))

## [3.148.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.148.6...v3.148.7) (2024-06-21)


### Bug Fixes

* **asset config:** page going blank on spcb protocol change ([8dca150](https://github.com/Datoms-IoT/datoms-webapp/commit/8dca15065c41b1dca2c81727030fd94a61c05f06))

## [3.148.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.148.5...v3.148.6) (2024-06-21)


### Bug Fixes

* **assets:** asset data view realtime update fix ([259a5ac](https://github.com/Datoms-IoT/datoms-webapp/commit/259a5ac1a966ad51370ad38476c819227a66ed06))

## [3.148.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.148.4...v3.148.5) (2024-06-21)


### Bug Fixes

* **asset list:** asset data view realtime update ([a827c52](https://github.com/Datoms-IoT/datoms-webapp/commit/a827c529af514d7277294a90eefb742ebb0379e2))

## [3.148.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.148.3...v3.148.4) (2024-06-20)


### Bug Fixes

* **filter:** territory filter added in customer, assets & devices ([2f80782](https://github.com/Datoms-IoT/datoms-webapp/commit/2f807820a422c3695f8557aac9b9c23dd47ac8c5))

## [3.148.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.148.2...v3.148.3) (2024-06-20)


### Bug Fixes

* **assets:** asset online style fix ([4641812](https://github.com/Datoms-IoT/datoms-webapp/commit/4641812099730ef2edece9c9af4de7cb162be757))

## [3.148.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.148.1...v3.148.2) (2024-06-20)


### Bug Fixes

* **assets:** partner name hide in vendors for customer free assets ([90a9e47](https://github.com/Datoms-IoT/datoms-webapp/commit/90a9e472a4c036adced69cace4bae6c5d6a4887c))

## [3.148.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.148.0...v3.148.1) (2024-06-20)


### Bug Fixes

* **goem:** issues fixed ([748751d](https://github.com/Datoms-IoT/datoms-webapp/commit/748751d6336419e517b4da649fbb1f379f6056eb))

# [3.148.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.147.4...v3.148.0) (2024-06-19)


### Features

* **assets:** asset addition without customers ([8497d32](https://github.com/Datoms-IoT/datoms-webapp/commit/8497d32d9e61aa4ac47cda4db6578ae388d8e596))

## [3.147.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.147.3...v3.147.4) (2024-06-19)


### Bug Fixes

* **detailed view:** detailed view timeformat fixing ([52b4156](https://github.com/Datoms-IoT/datoms-webapp/commit/52b4156202ccc7877b81fa32d0cf082e85927c17))

## [3.147.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.147.2...v3.147.3) (2024-06-18)


### Bug Fixes

* **devices:** device delete added for test customer ([674ffd8](https://github.com/Datoms-IoT/datoms-webapp/commit/674ffd8f235e846ed57799e68c92399b7a0a5375))

## [3.147.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.147.1...v3.147.2) (2024-06-18)


### Bug Fixes

* **devices:** enabled unassigned devices delete ([55076bb](https://github.com/Datoms-IoT/datoms-webapp/commit/55076bb17ba42680cbc32eeb7da6475245b3659e))

## [3.147.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.147.0...v3.147.1) (2024-06-18)


### Bug Fixes

* **reports:** dG status report issye fixes and snapshot report name change ([d780d47](https://github.com/Datoms-IoT/datoms-webapp/commit/d780d47a82bebc59d1a8128f641fb4688ece93e6))

# [3.147.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.146.2...v3.147.0) (2024-06-18)


### Bug Fixes

* **process analyzer:** process Analyzer Report actuation data to int ([d15e495](https://github.com/Datoms-IoT/datoms-webapp/commit/d15e495a647fb586dc694d0ae07641c3864e8ec1))


### Features

* **desktop:** added conditions for desktop app build ([f8e123b](https://github.com/Datoms-IoT/datoms-webapp/commit/f8e123b90daa0fcbb0bf8efa2df633063ef83af3))

## [3.146.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.146.1...v3.146.2) (2024-06-13)


### Bug Fixes

* **panel view:** panel View Faults ([96f7b70](https://github.com/Datoms-IoT/datoms-webapp/commit/96f7b70ef94f384594f79438562133d4ba9f4762))

## [3.146.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.146.0...v3.146.1) (2024-06-13)


### Bug Fixes

* **reports:** process Analyzer Reports Routes Fix ([d74b039](https://github.com/Datoms-IoT/datoms-webapp/commit/d74b0391979c9fd5c410eca5831ee17d2c71744d))

# [3.146.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.145.2...v3.146.0) (2024-06-13)


### Features

* **process analyzer:** process analyzer onboarded ([221c525](https://github.com/Datoms-IoT/datoms-webapp/commit/221c52549bd17d36aac8e8eb1a5872256d912cf6))
* **process analyzer:** uI for different views of Process Analyzer ([f3e75fa](https://github.com/Datoms-IoT/datoms-webapp/commit/f3e75faafa692aa4455cd29969c42a92369f4265))

## [3.145.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.145.1...v3.145.2) (2024-06-12)


### Bug Fixes

* **reports:** snapshot report as per date and time selection ([b1f807c](https://github.com/Datoms-IoT/datoms-webapp/commit/b1f807c771fc4a70b3c48137e56d4f5c8f459414))

## [3.145.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.145.0...v3.145.1) (2024-06-11)


### Bug Fixes

* **asset dashboard:** enabling asset dashboard from features ([17af2bc](https://github.com/Datoms-IoT/datoms-webapp/commit/17af2bcf9f4945e7bcacee220a6e480f191011a2))

# [3.145.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.144.8...v3.145.0) (2024-06-11)


### Features

* **snapshot report:** snapshot report added ([606fd1c](https://github.com/Datoms-IoT/datoms-webapp/commit/606fd1c29a600ac75c3fc93755e1e88c35a3a567))

## [3.144.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.144.7...v3.144.8) (2024-06-11)


### Bug Fixes

* **detailed view:** genset status detailed view genset status parameters added ([db9f736](https://github.com/Datoms-IoT/datoms-webapp/commit/db9f736300708991dc9741fa61d8f2afce05dcbc))

## [3.144.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.144.6...v3.144.7) (2024-06-10)


### Bug Fixes

* **custom report:** custom report partner filter bug fix ([82dc90a](https://github.com/Datoms-IoT/datoms-webapp/commit/82dc90a734a094d530f7c4428460972de338ae4a))

## [3.144.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.144.5...v3.144.6) (2024-06-10)


### Bug Fixes

* **predefined reports:** predefined reports partner selection fixes ([6412c10](https://github.com/Datoms-IoT/datoms-webapp/commit/6412c10e2c67650bf08b65a8a20d3980d498933b))

## [3.144.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.144.4...v3.144.5) (2024-06-10)


### Bug Fixes

* **reports:** reports partner selection issue fixed ([e88fae8](https://github.com/Datoms-IoT/datoms-webapp/commit/e88fae8779d973ebb3a0bd2ac084b064adfa9a37))

## [3.144.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.144.3...v3.144.4) (2024-06-10)


### Bug Fixes

* **assets:** city, province, country code edit fixed ([64575e2](https://github.com/Datoms-IoT/datoms-webapp/commit/64575e2e2c3728b363d4f6549baacaff8240b967))
* **reports:** reports download user access and partners filter added ([f8ef516](https://github.com/Datoms-IoT/datoms-webapp/commit/f8ef51624d1368fd76681afa08f38f61804dc9da))

## [3.144.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.144.2...v3.144.3) (2024-06-07)


### Bug Fixes

* **layout:** layout header left and right value updated ([79d9954](https://github.com/Datoms-IoT/datoms-webapp/commit/79d99543613bad6e6a2c468c17f154446d9ad6d8))

## [3.144.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.144.1...v3.144.2) (2024-06-05)


### Bug Fixes

* **devices:** device status condition updated ([781ef3d](https://github.com/Datoms-IoT/datoms-webapp/commit/781ef3d334669cc59db70e4f7040d7192f3a01f5))

## [3.144.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.144.0...v3.144.1) (2024-06-03)


### Bug Fixes

* **multiassetreport:** multi Asset report date selection condition updated ([2d4bc59](https://github.com/Datoms-IoT/datoms-webapp/commit/2d4bc598c8856e576c7d4030df63a6a9c3a04382))

# [3.144.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.143.1...v3.144.0) (2024-06-03)


### Features

* **dg reports:** dg multi assets report implemented ([0ec07e6](https://github.com/Datoms-IoT/datoms-webapp/commit/0ec07e67c0147e048fa4bd12df9f1c6c4cf1fcdd))

## [3.143.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.143.0...v3.143.1) (2024-05-31)


### Bug Fixes

* **assets:** added device last online time in assets list ([9f57144](https://github.com/Datoms-IoT/datoms-webapp/commit/9f5714475ec112b26adb4ea17b1ed53fbc05da4e))

# [3.143.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.142.3...v3.143.0) (2024-05-31)


### Features

* **reports:** reports excel download multiple sheets implemented ([14c61e1](https://github.com/Datoms-IoT/datoms-webapp/commit/14c61e13f575af9909b0227fec0ee0eb8b00d543))

## [3.142.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.142.2...v3.142.3) (2024-05-31)


### Bug Fixes

* **devices:** last online time added in additional places ([216b1d8](https://github.com/Datoms-IoT/datoms-webapp/commit/216b1d8f8f54a515da0bd9c506a0e3788bd7bebe))

## [3.142.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.142.1...v3.142.2) (2024-05-31)


### Bug Fixes

* **devices:** online time implemented in devices ([22fbf2a](https://github.com/Datoms-IoT/datoms-webapp/commit/22fbf2ac687f9e31e60fd7ac9208d92e1c6f0aa1))

## [3.142.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.142.0...v3.142.1) (2024-05-30)


### Bug Fixes

* **assets:** fix asset config error ([b2c293c](https://github.com/Datoms-IoT/datoms-webapp/commit/b2c293cce50253840709cfa91a1b7d055439752e))

# [3.142.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.141.1...v3.142.0) (2024-05-29)


### Features

* **event view:** cold storent asset time added in event view ([a07cc68](https://github.com/Datoms-IoT/datoms-webapp/commit/a07cc68549d372eaec30b6d951cf2f31efd73608))

## [3.141.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.141.0...v3.141.1) (2024-05-29)


### Bug Fixes

* **asset:** asset config reset issue ([19be8b4](https://github.com/Datoms-IoT/datoms-webapp/commit/19be8b4ed815615e5ffc34e9dda67064d92fbfce))

# [3.141.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.140.3...v3.141.0) (2024-05-28)


### Features

* **generic:** description added to the parameters ([4798d66](https://github.com/Datoms-IoT/datoms-webapp/commit/4798d66d8a80aa7d50eaa6afc255746d906c30e8))

## [3.140.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.140.2...v3.140.3) (2024-05-28)


### Bug Fixes

* **assets:** machine info date fields edit issue ([c1f1540](https://github.com/Datoms-IoT/datoms-webapp/commit/c1f15401a3b360b0d6a3ba2969b05422cb686605))

## [3.140.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.140.1...v3.140.2) (2024-05-28)


### Bug Fixes

* **asset dashboard:** asset dashboard customers filter ([9e67bf2](https://github.com/Datoms-IoT/datoms-webapp/commit/9e67bf24214016fc987342195e68b6a8afdb54cf))

## [3.140.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.140.0...v3.140.1) (2024-05-27)


### Bug Fixes

* **panel view:** pnel view na while loading the data fixed ([d8eaccc](https://github.com/Datoms-IoT/datoms-webapp/commit/d8eaccc37719aec0f8b267907a0077c54b1f4c68))

# [3.140.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.139.2...v3.140.0) (2024-05-27)


### Features

* **detailed view:** detailed view download implemented ([a86e92f](https://github.com/Datoms-IoT/datoms-webapp/commit/a86e92f04a014ebea00b2892fa2bb6bbec11bf6d))

## [3.139.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.139.1...v3.139.2) (2024-05-24)


### Bug Fixes

* **assets:** update address field on lat long fields change ([27fd579](https://github.com/Datoms-IoT/datoms-webapp/commit/27fd579d8e8ed8a50da20f689b1e2b602adea2cb))
* **production module:** added condition for trb142 composite item ([0611f87](https://github.com/Datoms-IoT/datoms-webapp/commit/0611f87bf1c90ca58c453c9b2ce719f40938dbc8))

## [3.139.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.139.0...v3.139.1) (2024-05-24)

# [3.139.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.138.2...v3.139.0) (2024-05-23)


### Bug Fixes

* **assets:** device protocol fields issue fixed ([a7f1d2f](https://github.com/Datoms-IoT/datoms-webapp/commit/a7f1d2f32c6ee1216121a6fec241969ff2503a5f))
* **menu:** handled workflow page showing without giving feature in enterprise plan ([d225cab](https://github.com/Datoms-IoT/datoms-webapp/commit/d225cab001b330ab6e0d825f2ff4ec6b5cf14b57))


### Features

* **assets:** added parameter description field in asset configuration ([905ef49](https://github.com/Datoms-IoT/datoms-webapp/commit/905ef495dd32c669da55e92d80c0f452639c3cab))

## [3.138.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.138.1...v3.138.2) (2024-05-23)


### Bug Fixes

* **mix panel:** added condition for datoms-x ([a6552ef](https://github.com/Datoms-IoT/datoms-webapp/commit/a6552efb8ee75a02702ed3bbd6bf90e8e90d977e))
* **mix panel:** mix panel condition added for mobile ([b8747e1](https://github.com/Datoms-IoT/datoms-webapp/commit/b8747e191d61b8cb65aeeebe58ba341e2af84ea9))
* **mix panel:** mix panel init statement in app js ([4187189](https://github.com/Datoms-IoT/datoms-webapp/commit/4187189aa4606a576c5bf2764c456553957c90e3))
* **mix panel:** otw ([140907e](https://github.com/Datoms-IoT/datoms-webapp/commit/140907edc15cc2de414916d1787783b7249afe37))
* **mix panel:** otw ([457857b](https://github.com/Datoms-IoT/datoms-webapp/commit/457857bb68f5bcde71621b0dddb9f253b11fbd3e))
* **mix panel:** vendor condition added in init ([71c0673](https://github.com/Datoms-IoT/datoms-webapp/commit/71c06733440dd267993fa418d4961ddd46b9af30))
* **mobile app version:** mobile app version updated ([4a9a229](https://github.com/Datoms-IoT/datoms-webapp/commit/4a9a2290bfa1fc01c9139e95663fb867cfe6baf3))

## [3.138.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.138.0...v3.138.1) (2024-05-23)


### Bug Fixes

* **custom report:** site custom report bug fixes ([dc901d8](https://github.com/Datoms-IoT/datoms-webapp/commit/dc901d8ae1a687db481cd7f6c8c5a000224a8c49))

# [3.138.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.137.3...v3.138.0) (2024-05-22)


### Features

* **site custom report:** site custom report form ([8969e20](https://github.com/Datoms-IoT/datoms-webapp/commit/8969e2081890403e549c84ed5b8fb6b9bc540c8d))

## [3.137.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.137.2...v3.137.3) (2024-05-21)


### Bug Fixes

* **tripview:** offline trips show in tripview table ([eba83bc](https://github.com/Datoms-IoT/datoms-webapp/commit/eba83bce89cbd4451f715f3e22a5e3ddee3d51c5))

## [3.137.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.137.1...v3.137.2) (2024-05-20)


### Bug Fixes

* **assets:** added gcp push for aurassure ([1ce1ca2](https://github.com/Datoms-IoT/datoms-webapp/commit/1ce1ca2b5027b639563d261a0925ddf23f72cbf5))

## [3.137.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.137.0...v3.137.1) (2024-05-17)


### Bug Fixes

* **assets:** handle condition for date input in asset machine info ([3ef4e6a](https://github.com/Datoms-IoT/datoms-webapp/commit/3ef4e6a847afb2cfab5fd0407482505573d6b532))

# [3.137.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.136.13...v3.137.0) (2024-05-16)


### Features

* **sensor report:** sensor report for cold storage site type implemented ([53cf596](https://github.com/Datoms-IoT/datoms-webapp/commit/53cf5960b518786cec0f75e80db0759fddf90788))

## [3.136.13](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.136.12...v3.136.13) (2024-05-16)


### Bug Fixes

* **site view:** site view data calling fix ([4df8f6a](https://github.com/Datoms-IoT/datoms-webapp/commit/4df8f6af269e5ec475647f9dc3ee704b3707d2f3))

## [3.136.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.136.11...v3.136.12) (2024-05-15)


### Bug Fixes

* **things:** added commissioning date & capacity in quick config for tata ([1d6f36c](https://github.com/Datoms-IoT/datoms-webapp/commit/1d6f36c83b0346e7135a4828daf7418918edb33c))

## [3.136.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.136.10...v3.136.11) (2024-05-14)


### Bug Fixes

* **site view:** door status open and close fixed in graph ([5dcb7b8](https://github.com/Datoms-IoT/datoms-webapp/commit/5dcb7b854f35272e110d3ec3b65fdfddc6f2a07a))

## [3.136.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.136.9...v3.136.10) (2024-05-13)


### Bug Fixes

* **compressor:** compressor added to generic ([99377a9](https://github.com/Datoms-IoT/datoms-webapp/commit/99377a98a0e5649bbe11a77eaed26c754b7117a5))

## [3.136.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.136.8...v3.136.9) (2024-05-13)


### Bug Fixes

* **site view:** site view energy graph implemented ([51d79e9](https://github.com/Datoms-IoT/datoms-webapp/commit/51d79e91f2dde80638f87cededd8275f25a1798c))

## [3.136.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.136.7...v3.136.8) (2024-05-13)


### Bug Fixes

* **dg status report:** dG Status report download file name updated ([8a97944](https://github.com/Datoms-IoT/datoms-webapp/commit/8a97944178fd0436275e40cb64d5e89a319dc276))

## [3.136.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.136.6...v3.136.7) (2024-05-13)


### Bug Fixes

* **generic:** compressor views handled in Generic ([413dafb](https://github.com/Datoms-IoT/datoms-webapp/commit/413dafb85ba9e7f48fa733f7125f439e13d72520))

## [3.136.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.136.5...v3.136.6) (2024-05-10)


### Bug Fixes

* **dg status report:** dG status report download file name fix ([0c2f02a](https://github.com/Datoms-IoT/datoms-webapp/commit/0c2f02a6fac9ea46b558542ec5cdad75abcdc885))

## [3.136.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.136.4...v3.136.5) (2024-05-10)


### Bug Fixes

* **daily yesterday report:** daily yesterday report auto download fixed ([72d7f48](https://github.com/Datoms-IoT/datoms-webapp/commit/72d7f4832e00d58007bef39c6b4a936aa2b3b8c3))

## [3.136.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.136.3...v3.136.4) (2024-05-10)


### Bug Fixes

* **dg status report:** dg status report auto download implemented ([9eca58e](https://github.com/Datoms-IoT/datoms-webapp/commit/9eca58e73a7972658f8fcddf891e32a6b9570a8c))

## [3.136.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.136.2...v3.136.3) (2024-05-10)


### Bug Fixes

* **map view:** map view for cold storage, temp & humid, battery implemented ([342842a](https://github.com/Datoms-IoT/datoms-webapp/commit/342842a93a1a7d836c05e6a5937d1ce2510e6aed))

## [3.136.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.136.1...v3.136.2) (2024-05-10)


### Bug Fixes

* **alerts:** offline alerts order ([08e7433](https://github.com/Datoms-IoT/datoms-webapp/commit/08e74333c6547a9cfd95b6bbd212dff1457fb102))

## [3.136.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.136.0...v3.136.1) (2024-05-09)


### Bug Fixes

* **site view:** site view bug and design fixes ([3c8627f](https://github.com/Datoms-IoT/datoms-webapp/commit/3c8627f8b2c9a3d2d0a49af4579e0f83de032289))

# [3.136.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.135.1...v3.136.0) (2024-05-09)


### Features

* **site view:** siteview added ([91156a4](https://github.com/Datoms-IoT/datoms-webapp/commit/91156a46e01d4e4adfbebe53ef390a0b5d969000))

## [3.135.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.135.0...v3.135.1) (2024-05-08)


### Bug Fixes

* dummy commit to trigger release ([bd8e2f8](https://github.com/Datoms-IoT/datoms-webapp/commit/bd8e2f8f6eb39ddb5a6668415e1c86022e26c861))

# [3.135.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.134.3...v3.135.0) (2024-05-08)


### Features

* **territory:** territory management for sites and assets ([f94d185](https://github.com/Datoms-IoT/datoms-webapp/commit/f94d185f706673d03c660ba217c15ca51d4c0d0a))

## [3.134.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.134.2...v3.134.3) (2024-05-08)


### Bug Fixes

* **asset:** location field not showing in configuration ([3a7945d](https://github.com/Datoms-IoT/datoms-webapp/commit/3a7945d851301b55098a48ebde4ec537f00c159b))

## [3.134.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.134.1...v3.134.2) (2024-05-07)


### Bug Fixes

* **panel view:** panel view optimization with pagination implementation ([204e91d](https://github.com/Datoms-IoT/datoms-webapp/commit/204e91dd2c1b6bf7411ba5ff5cc4cfb57c24bfe3))

## [3.134.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.134.0...v3.134.1) (2024-05-06)


### Bug Fixes

* **alerts:** offline alerts for selco enabled in end customers ([5b57751](https://github.com/Datoms-IoT/datoms-webapp/commit/5b57751d307229a1a3452632c36b476b2a0540ac))

# [3.134.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.133.2...v3.134.0) (2024-05-03)


### Bug Fixes

* **alerts:** offline alerts made editable for selco ([fe64ce4](https://github.com/Datoms-IoT/datoms-webapp/commit/fe64ce4dc8ba1d79e5143794b645594b0fedb0da))
* **trip view:** linked customer assets filter condition in datomsx ([492457a](https://github.com/Datoms-IoT/datoms-webapp/commit/492457acb226e289d969c90afa200bbf7f8ddbf8))


### Features

* **site:** control to apply site location to associated assets ([d82c94e](https://github.com/Datoms-IoT/datoms-webapp/commit/d82c94e2ad6e21d0789d049d3fde64722cda091f))

## [3.133.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.133.1...v3.133.2) (2024-05-02)


### Bug Fixes

* **gentek logo:** gentek logo updated ([f704582](https://github.com/Datoms-IoT/datoms-webapp/commit/f704582c7ec381d7c1d73ab85ab491db0465d297))

## [3.133.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.133.0...v3.133.1) (2024-05-01)


### Bug Fixes

* **reports:** no reports found in custom report - fixed ([01c392d](https://github.com/Datoms-IoT/datoms-webapp/commit/01c392d9c3ef13f2e93c67c40879d000cd086452))

# [3.133.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.132.13...v3.133.0) (2024-04-30)


### Features

* **energy, solar, electrical machines:** energy, solar, electrical machines view onboarding ([7d27955](https://github.com/Datoms-IoT/datoms-webapp/commit/7d27955390a41c8184a51056227b6ed08c33c79d))

## [3.132.13](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.132.12...v3.132.13) (2024-04-30)


### Bug Fixes

* **environment monitoring:** flushsync added in environment monitoring ([69e0ec4](https://github.com/Datoms-IoT/datoms-webapp/commit/69e0ec41c29d126015a41fd3caf263fc069d5c2d))

## [3.132.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.132.11...v3.132.12) (2024-04-29)


### Bug Fixes

* **devices:** firmware upload circuit version exception added for few device ids ([74ea75f](https://github.com/Datoms-IoT/datoms-webapp/commit/74ea75f9cd6f36c0b4e63f8ad267b0f47e5e6050))

## [3.132.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.132.10...v3.132.11) (2024-04-29)


### Bug Fixes

* **device:** firmware update for devices with application id as null ([43074a4](https://github.com/Datoms-IoT/datoms-webapp/commit/43074a4d55cdb0a2bb94b6c2472baf98999dc27a))
* **trip view:** zomato blinkit condition in customer filter options ([bbcf1b1](https://github.com/Datoms-IoT/datoms-webapp/commit/bbcf1b196bf3d2d26011b2edab471a073a22a969))

## [3.132.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.132.9...v3.132.10) (2024-04-29)


### Bug Fixes

* **gd status report:** genset state added in dg status report ([d55760f](https://github.com/Datoms-IoT/datoms-webapp/commit/d55760f0155a3c2540df949f3366fcf9ae693902))

## [3.132.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.132.8...v3.132.9) (2024-04-29)


### Bug Fixes

* **asset:** multiplier field issue fixed ([26fe2b2](https://github.com/Datoms-IoT/datoms-webapp/commit/26fe2b23e78b82d1831c9fdbb4c786242e4451f8))

## [3.132.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.132.7...v3.132.8) (2024-04-29)


### Bug Fixes

* **site:** max things condition updated ([a2274ec](https://github.com/Datoms-IoT/datoms-webapp/commit/a2274ecbaf787fa6444f3478ced636b6a10d3071))

## [3.132.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.132.6...v3.132.7) (2024-04-26)


### Bug Fixes

* **assets:** prevented value change on scroll for some fields ([46915f8](https://github.com/Datoms-IoT/datoms-webapp/commit/46915f8482fc2bac4e5999a4df05c68d74f321fa))

## [3.132.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.132.5...v3.132.6) (2024-04-25)


### Bug Fixes

* **trip view:** tags column changes ([d59c8c8](https://github.com/Datoms-IoT/datoms-webapp/commit/d59c8c80e7469d543346e6f2096a04965016419e))

## [3.132.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.132.4...v3.132.5) (2024-04-25)


### Bug Fixes

* **dg status report:** dG status report territory added ([908d709](https://github.com/Datoms-IoT/datoms-webapp/commit/908d709699dff8c9394d36e7d24ddf36891ec801))

## [3.132.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.132.3...v3.132.4) (2024-04-25)


### Bug Fixes

* **menu:** trip view made feature controlled for end customers ([df9883a](https://github.com/Datoms-IoT/datoms-webapp/commit/df9883a84ded6597d7c4d0989327dd44bfb16944))

## [3.132.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.132.2...v3.132.3) (2024-04-24)


### Bug Fixes

* **assets configuration:** location params active if device is location enabled ([f389548](https://github.com/Datoms-IoT/datoms-webapp/commit/f3895489400a48db7abf86b24781e460febb161e))

## [3.132.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.132.1...v3.132.2) (2024-04-23)


### Bug Fixes

* **site:** pagination limit set to 100 for site list ([57869d2](https://github.com/Datoms-IoT/datoms-webapp/commit/57869d2622c72daa1c481ea787d0fe5a99180d52))

## [3.132.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.132.0...v3.132.1) (2024-04-23)


### Bug Fixes

* **users:** site user access ([6d309e5](https://github.com/Datoms-IoT/datoms-webapp/commit/6d309e571dce3230657e84cce097ef86ec570815))

# [3.132.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.131.2...v3.132.0) (2024-04-23)


### Bug Fixes

* require not defined ([60f74e8](https://github.com/Datoms-IoT/datoms-webapp/commit/60f74e83ffbeed0325919cec719a34c19f7e1dc6))


### Features

* removed CRA and use VITE as build tool ([f875818](https://github.com/Datoms-IoT/datoms-webapp/commit/f87581889f657158d051413732fb04830a353642))

## [3.131.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.131.1...v3.131.2) (2024-04-22)


### Bug Fixes

* **users:** user edit page blank issue fix ([2f5f4fc](https://github.com/Datoms-IoT/datoms-webapp/commit/2f5f4fc17a787e9cc1c61357bf3e4c0dd4daecc3))

## [3.131.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.131.0...v3.131.1) (2024-04-22)


### Bug Fixes

* **energy meter report:** energy meter id added to report ([804d0e9](https://github.com/Datoms-IoT/datoms-webapp/commit/804d0e976afa3ecd578c955eee61656fd3c607f1))

# [3.131.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.130.7...v3.131.0) (2024-04-19)


### Features

* **users:** site access for end customer users ([9a16851](https://github.com/Datoms-IoT/datoms-webapp/commit/9a16851d010665e67bb78c9bd909da838fbfc69c))

## [3.130.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.130.6...v3.130.7) (2024-04-19)


### Bug Fixes

* **energy meter report:** energy meter report download width removed ([d520fb7](https://github.com/Datoms-IoT/datoms-webapp/commit/d520fb7d912b9f536f675b6a5a76456c4fd6991f))

## [3.130.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.130.5...v3.130.6) (2024-04-18)


### Bug Fixes

* **energy meter report:** energy meter daily report export and net energy implemented ([22c2b52](https://github.com/Datoms-IoT/datoms-webapp/commit/22c2b522721585475eda6b2cba767c9af383c7a5))

## [3.130.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.130.4...v3.130.5) (2024-04-18)


### Bug Fixes

* **production module:** new care models updated ([72b84db](https://github.com/Datoms-IoT/datoms-webapp/commit/72b84db84734d81d1b372a6198b7abcffe0d482d))

## [3.130.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.130.3...v3.130.4) (2024-04-17)


### Bug Fixes

* **filters:** things & devices filter support for linked end customer ([98a9efc](https://github.com/Datoms-IoT/datoms-webapp/commit/98a9efcfd58bf8f4f4c5ae0798d67c735bd49bef))

## [3.130.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.130.2...v3.130.3) (2024-04-17)


### Bug Fixes

* **production module:** aurassure infra gt ltt conditions ([853bfdf](https://github.com/Datoms-IoT/datoms-webapp/commit/853bfdf19a05fe806ae612edbc4936c358281940))

## [3.130.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.130.1...v3.130.2) (2024-04-16)


### Bug Fixes

* **aurassure:** aurassure avg data calculation from hourly avg ([bc811ef](https://github.com/Datoms-IoT/datoms-webapp/commit/bc811ef82bc9e944b4a889ec2249682e541130d5))

## [3.130.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.130.0...v3.130.1) (2024-04-16)


### Bug Fixes

* **production module:** product qr print condition fix ([3bb25c1](https://github.com/Datoms-IoT/datoms-webapp/commit/3bb25c11a8a107f626e49572b9f1b63ebf365a80))

# [3.130.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.129.1...v3.130.0) (2024-04-16)


### Features

* **production module:** added new product support - Aurassure Infra GT ([e0e7edb](https://github.com/Datoms-IoT/datoms-webapp/commit/e0e7edb02099c109537018ea9d197f5131b35e62))

## [3.129.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.129.0...v3.129.1) (2024-04-15)


### Bug Fixes

* **assets:** customer list search in asset add/config fix ([c7e2e02](https://github.com/Datoms-IoT/datoms-webapp/commit/c7e2e029eaede0e9413224e2fd5aafba9fe3af1c))

# [3.129.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.128.2...v3.129.0) (2024-04-12)


### Features

* **rental:** rental partner portal for yaana ([600a45d](https://github.com/Datoms-IoT/datoms-webapp/commit/600a45d405743c2d76a1600816c0681ad9e7198d))

## [3.128.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.128.1...v3.128.2) (2024-04-12)


### Bug Fixes

* **report:** lifetime report values not coming fixed ([f0fa35c](https://github.com/Datoms-IoT/datoms-webapp/commit/f0fa35c2b6bbb8f86e3db3c43ad5f914b56f8c1f))

## [3.128.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.128.0...v3.128.1) (2024-04-12)


### Bug Fixes

* **dg:** dG dashboard optimized ([288a956](https://github.com/Datoms-IoT/datoms-webapp/commit/288a956664d03488b70bf2131956d01d757b5ddd))

# [3.128.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.127.6...v3.128.0) (2024-04-11)


### Features

* **asset management:** added parameter settings update options from system template ([6517e76](https://github.com/Datoms-IoT/datoms-webapp/commit/6517e760775846d2408d412477689a9f063f4be5))

## [3.127.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.127.5...v3.127.6) (2024-04-11)


### Bug Fixes

* **dg:** lumen energy logo fix for end customer ([ef57f04](https://github.com/Datoms-IoT/datoms-webapp/commit/ef57f04d17142e57e27536d280752ac8c25a8583))

## [3.127.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.127.4...v3.127.5) (2024-04-11)


### Bug Fixes

* **pomo:** pomo calibration menu item fixes and .xls to .xlsx in download modal ([80175bb](https://github.com/Datoms-IoT/datoms-webapp/commit/80175bb1c2bfba8ce6a75be20f49e3a8b88132ab))

## [3.127.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.127.3...v3.127.4) (2024-04-11)


### Bug Fixes

* **dg:** events api restricted for not to be called as per rental condition ([8dbddba](https://github.com/Datoms-IoT/datoms-webapp/commit/8dbddba994e6d7bf9346871cc925194ab5eb839a))

## [3.127.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.127.2...v3.127.3) (2024-04-10)


### Bug Fixes

* **subscription management:** added start date column in to be billed tab ([64b14ea](https://github.com/Datoms-IoT/datoms-webapp/commit/64b14ea8d4c4ef8dda50697e04eb5120517d1d51))

## [3.127.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.127.1...v3.127.2) (2024-04-10)


### Bug Fixes

* **device list:** page optimization ([4c15538](https://github.com/Datoms-IoT/datoms-webapp/commit/4c1553826015a0759d50ec32dc7f7954a84e0ce2))
* **things list:** configure icon block when customer is inactive ([b306302](https://github.com/Datoms-IoT/datoms-webapp/commit/b306302b63dc00d74c95ed78b1100e91f536fadf))

## [3.127.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.127.0...v3.127.1) (2024-04-10)


### Bug Fixes

* **warehouse site report:** warehouse site report changes for view ([8c8d559](https://github.com/Datoms-IoT/datoms-webapp/commit/8c8d559215406cb81893d845c8cff6b48e650c98))

# [3.127.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.126.1...v3.127.0) (2024-04-09)


### Features

* **site report:** warehouse Site report implementation ([7ed9cad](https://github.com/Datoms-IoT/datoms-webapp/commit/7ed9cad1c682fc7463a54ef980141158687043b0))

## [3.126.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.126.0...v3.126.1) (2024-04-09)


### Bug Fixes

* **lumen energy:** lumen energy logo updated ([aab517f](https://github.com/Datoms-IoT/datoms-webapp/commit/aab517fa14c3a8bdbc2c71d301bd2f65d1569fc7))

# [3.126.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.125.11...v3.126.0) (2024-04-09)


### Features

* **billing management:** added to be billed tab in subscription ([9d03141](https://github.com/Datoms-IoT/datoms-webapp/commit/9d03141e89ac1e80e9582cf014be75b119587344))

## [3.125.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.125.10...v3.125.11) (2024-04-05)


### Bug Fixes

* **asset configuration:** provision for clearing parameter data unit ([b2b07cf](https://github.com/Datoms-IoT/datoms-webapp/commit/b2b07cfb1d03ed44d4a01d6ea8945e9bfbb080fe))

## [3.125.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.125.9...v3.125.10) (2024-04-05)


### Bug Fixes

* **asset configuration:** added additional units ([efa1c99](https://github.com/Datoms-IoT/datoms-webapp/commit/efa1c9919c6c55b0ad0f4e7987a9e98109cc1c26))

## [3.125.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.125.8...v3.125.9) (2024-04-05)


### Bug Fixes

* **asset configuration:** no_unit removed ([466c396](https://github.com/Datoms-IoT/datoms-webapp/commit/466c3962a293d1501712151e90b819207dd88ee9))

## [3.125.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.125.7...v3.125.8) (2024-04-05)


### Bug Fixes

* **asset configuration:** added additional data units ([fcbf4ee](https://github.com/Datoms-IoT/datoms-webapp/commit/fcbf4ee463016bf62ba73fe044a4a985a2a2d6cf))

## [3.125.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.125.6...v3.125.7) (2024-04-04)


### Bug Fixes

* **report:** solar Daily Report initial and snapshot removed ([0479158](https://github.com/Datoms-IoT/datoms-webapp/commit/047915864ecae092ef50c2d28d1b0162181b66c0))

## [3.125.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.125.5...v3.125.6) (2024-04-04)


### Bug Fixes

* **services:** pagination implemented in services page ([6dcd32e](https://github.com/Datoms-IoT/datoms-webapp/commit/6dcd32edefd09e9d6bcdf31c158e2ba9048d540d))

## [3.125.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.125.4...v3.125.5) (2024-04-04)


### Bug Fixes

* **map:** lat long updated ([3cca79a](https://github.com/Datoms-IoT/datoms-webapp/commit/3cca79a77061b5508dfba91a5f446f3a6cce3964))

## [3.125.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.125.3...v3.125.4) (2024-04-04)


### Bug Fixes

* **zooming out map for single thing:** zooming out map for single thing ([c674163](https://github.com/Datoms-IoT/datoms-webapp/commit/c674163f3742fdf42d49a266a7f86869397c9091))

## [3.125.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.125.2...v3.125.3) (2024-04-03)


### Bug Fixes

* **map:** mapbox implemented in application end platform ([bb89298](https://github.com/Datoms-IoT/datoms-webapp/commit/bb892983614b0da24b35f97e5cdda6151fda7751))

## [3.125.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.125.1...v3.125.2) (2024-04-02)


### Bug Fixes

* **routes/menu:** oem dashboard hidden if access not available ([ef226a5](https://github.com/Datoms-IoT/datoms-webapp/commit/ef226a5eaff4d1b8d12c182ea8b80bab3cf7c3c2))

## [3.125.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.125.0...v3.125.1) (2024-04-01)


### Bug Fixes

* **asset management:** fixed - device assign issue if device id is zero ([388b48d](https://github.com/Datoms-IoT/datoms-webapp/commit/388b48dc68feb3b830a08a113334b0e1d47ea136))

# [3.125.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.124.6...v3.125.0) (2024-04-01)


### Features

* **header:** added link to datoms status page ([c01cfb7](https://github.com/Datoms-IoT/datoms-webapp/commit/c01cfb72ae96063a80add06afe3ffa62347c8375))

## [3.124.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.124.5...v3.124.6) (2024-03-27)


### Bug Fixes

* **report:** data availability report download api call pageSize decreased ([b15cad7](https://github.com/Datoms-IoT/datoms-webapp/commit/b15cad73c8e0dc80c6c501d3c07f5b75089c1c01))

## [3.124.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.124.4...v3.124.5) (2024-03-26)


### Bug Fixes

* **mobile app:** iOS App - security enhancements ([3d3fb81](https://github.com/Datoms-IoT/datoms-webapp/commit/3d3fb8144b2aad34597b0d907b6d571ecbf74b17))

## [3.124.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.124.3...v3.124.4) (2024-03-22)


### Bug Fixes

* **mobile app:** security enhancements and removed workflow page for mobile ([19b52ac](https://github.com/Datoms-IoT/datoms-webapp/commit/19b52acdce600daeebf7d10a4ff826d1d17b7cfd))

## [3.124.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.124.2...v3.124.3) (2024-03-21)


### Bug Fixes

* **mobile menu:** calibration mobile menu hidden temporarily ([e13c388](https://github.com/Datoms-IoT/datoms-webapp/commit/e13c388e2e960a9f7c21501c24275434a6b20290))

## [3.124.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.124.1...v3.124.2) (2024-03-20)


### Bug Fixes

* **customers:** applications edit fix ([a160158](https://github.com/Datoms-IoT/datoms-webapp/commit/a1601588620de487c109ea76bafe9296ff8e343d))

## [3.124.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.124.0...v3.124.1) (2024-03-20)


### Bug Fixes

* **asset management:** fixed - fuel sensor system templates are not selected for old configurations ([7eec549](https://github.com/Datoms-IoT/datoms-webapp/commit/7eec549f7053b8826d1f55c65425ef66f755e39c))

# [3.124.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.123.8...v3.124.0) (2024-03-19)


### Features

* **workflow:** added remote calibration feature ([bed17e8](https://github.com/Datoms-IoT/datoms-webapp/commit/bed17e816567058d1fc939ef7a13050a29694362))

## [3.123.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.123.7...v3.123.8) (2024-03-19)


### Bug Fixes

* **routes:** iot landing page fix ([27ffdc1](https://github.com/Datoms-IoT/datoms-webapp/commit/27ffdc1dc2faddb49800c84105bc49b4ebf1c65a))

## [3.123.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.123.6...v3.123.7) (2024-03-18)


### Bug Fixes

* **reports:** fuel fll drain report fuel end value key updated ([6f3c7f0](https://github.com/Datoms-IoT/datoms-webapp/commit/6f3c7f050127bd49132eb4d2e7e4b59fabd6eed1))

## [3.123.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.123.5...v3.123.6) (2024-03-13)


### Bug Fixes

* **reports:** dg status report customizattion issue fixed & Pre defined report select all removed ([e6ffbe8](https://github.com/Datoms-IoT/datoms-webapp/commit/e6ffbe833a24b80927d3a979976c0675051f7d8d))

## [3.123.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.123.4...v3.123.5) (2024-03-12)


### Bug Fixes

* **mobile -version:** mobile app version updated ([2400127](https://github.com/Datoms-IoT/datoms-webapp/commit/2400127430acc40165bcf86b6f00a94cf29b2fa2))

## [3.123.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.123.3...v3.123.4) (2024-03-09)


### Bug Fixes

* **aurassure:** aurassure report download error fixed ([6bd5d29](https://github.com/Datoms-IoT/datoms-webapp/commit/6bd5d29b437b40fc91e92dfff9d9b15c255536f6))

## [3.123.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.123.2...v3.123.3) (2024-03-07)


### Bug Fixes

* **pollution monitoring:** pollution monitoring thing limit and download column limit removed ([faf4740](https://github.com/Datoms-IoT/datoms-webapp/commit/faf47401dd7820e835d5a72d88e498baf6d20c96))

## [3.123.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.123.1...v3.123.2) (2024-03-05)


### Bug Fixes

* **report:** genset fuel tank site type updated ([123b26f](https://github.com/Datoms-IoT/datoms-webapp/commit/123b26fab2cd144b581c6df6990d11aa3d65d7b1))

## [3.123.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.123.0...v3.123.1) (2024-03-05)


### Bug Fixes

* **reports:** reports improvisation [download format updated, pdf column break removed] ([8d60da8](https://github.com/Datoms-IoT/datoms-webapp/commit/8d60da817ec1a77953bf1b50c6e6f7356a02ee94))
* **routes:** routes restriction condition updated ([9d35bfd](https://github.com/Datoms-IoT/datoms-webapp/commit/9d35bfda68231e608abe36045e11785a199d42a4))

# [3.123.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.122.2...v3.123.0) (2024-03-05)


### Features

* **routes:** blocked restricted routes ([b97640e](https://github.com/Datoms-IoT/datoms-webapp/commit/b97640ebf52dca83d1348022a194e4e692ec98a9))

## [3.122.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.122.1...v3.122.2) (2024-03-04)


### Bug Fixes

* **asset management:** fixed - issue location params are added non gps devices ([54bcf0a](https://github.com/Datoms-IoT/datoms-webapp/commit/54bcf0ae168c53a5b5fea5a35419142cf756b99c))

## [3.122.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.122.0...v3.122.1) (2024-03-02)


### Bug Fixes

* **asset dashboard:** asset dashboard by default last 30 days ([7f7df7e](https://github.com/Datoms-IoT/datoms-webapp/commit/7f7df7e7d3a2034af5864fe5c72042ab883c1682))

# [3.122.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.121.3...v3.122.0) (2024-03-01)


### Features

* **genset site report:** genset & Fuel Tank site report added ([c6e210f](https://github.com/Datoms-IoT/datoms-webapp/commit/c6e210f7c09e6a711589b52b693d97386cfd0c6e))

## [3.121.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.121.2...v3.121.3) (2024-03-01)


### Bug Fixes

* **selectwithrangepicker:** custom date select issue ([69ba48e](https://github.com/Datoms-IoT/datoms-webapp/commit/69ba48ed5b575498635858d20da4f44e389b1c07))

## [3.121.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.121.1...v3.121.2) (2024-02-29)


### Bug Fixes

* **image cropping:** cordova blob to file fix ([0ca6493](https://github.com/Datoms-IoT/datoms-webapp/commit/0ca6493d325bf90a197e748a18a400b8cf4d9b5c))

## [3.121.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.121.0...v3.121.1) (2024-02-28)


### Bug Fixes

* **image upload:** preview url format change ([70c838d](https://github.com/Datoms-IoT/datoms-webapp/commit/70c838dd899db30bb6502b3f0de6fccba4b7aaa4))

# [3.121.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.120.1...v3.121.0) (2024-02-28)


### Features

* **image upload:** cropping enabled for images before upload ([eb98a33](https://github.com/Datoms-IoT/datoms-webapp/commit/eb98a33862ed9acd9b1e215a56cae5bb78016f4a))

## [3.120.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.120.0...v3.120.1) (2024-02-27)


### Bug Fixes

* **mixpanel-events:** detailed view asset list drawer click track added ([f87a70b](https://github.com/Datoms-IoT/datoms-webapp/commit/f87a70b59a9f768486cfd83772d3fd099836d140))

# [3.120.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.119.12...v3.120.0) (2024-02-26)


### Features

* **webapp:** added mix panel tracking for android and web ([a873fc2](https://github.com/Datoms-IoT/datoms-webapp/commit/a873fc2cf40c906277b2749ed3fa0db85d6e119c))

## [3.119.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.119.11...v3.119.12) (2024-02-26)


### Bug Fixes

* **fuel tank:** fuel Tank daily report repeating data issue fixed ([45d299d](https://github.com/Datoms-IoT/datoms-webapp/commit/45d299d7be069324d7db478747483979ddf3f809))

## [3.119.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.119.10...v3.119.11) (2024-02-26)


### Bug Fixes

* **report:** report custom date selection issue fixed & map unnecessary geo-code fix ([7edf46e](https://github.com/Datoms-IoT/datoms-webapp/commit/7edf46ea42aa35f740e48d0ac91b36a5296a513c))

## [3.119.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.119.9...v3.119.10) (2024-02-26)


### Bug Fixes

* **pomo:** poMo detailed view going blank issue fixed ([920923f](https://github.com/Datoms-IoT/datoms-webapp/commit/920923f0710f84acfb82cbe238a74fb4207aff5b))

## [3.119.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.119.8...v3.119.9) (2024-02-23)


### Bug Fixes

* **dg status report:** dg status report asset add time filter added ([e2fd668](https://github.com/Datoms-IoT/datoms-webapp/commit/e2fd6683269eea4ec9b92d5a6b5cbd52f332ede6))

## [3.119.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.119.7...v3.119.8) (2024-02-23)


### Bug Fixes

* **ev:** e bike page not opening fix ([7f38047](https://github.com/Datoms-IoT/datoms-webapp/commit/7f3804718198850a433d1b1be6f6e7a1a1704669))

## [3.119.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.119.6...v3.119.7) (2024-02-23)


### Bug Fixes

* **asset management:** performance improvement changes ([26dd751](https://github.com/Datoms-IoT/datoms-webapp/commit/26dd751e2302be4717a78b1a936c77c8f9d78995))

## [3.119.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.119.5...v3.119.6) (2024-02-23)


### Bug Fixes

* **pomo range report:** poMo range report improvisation ([09f4973](https://github.com/Datoms-IoT/datoms-webapp/commit/09f49736e079505a44b216d43547c79e89fc204d))

## [3.119.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.119.4...v3.119.5) (2024-02-21)


### Bug Fixes

* **pomo:** poMo range report fixes ([b320bc5](https://github.com/Datoms-IoT/datoms-webapp/commit/b320bc55cba79e9d90d147edddec3da7a8fcbf51))

## [3.119.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.119.3...v3.119.4) (2024-02-21)


### Bug Fixes

* **pomo report:** pomo param report modal fix ([6ffeea5](https://github.com/Datoms-IoT/datoms-webapp/commit/6ffeea531988716e10ef9c095348f1a932cbe950))

## [3.119.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.119.2...v3.119.3) (2024-02-21)


### Bug Fixes

* **datoms policy:** mobile app package name issue ([a579702](https://github.com/Datoms-IoT/datoms-webapp/commit/a579702ae3ed79d1f433b39767ab906f744f60d4))

## [3.119.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.119.1...v3.119.2) (2024-02-20)


### Bug Fixes

* **report:** report design changes and preference updated for range report ([1230f0e](https://github.com/Datoms-IoT/datoms-webapp/commit/1230f0e3fd67973915c2f3d596103b330ff0009c))

## [3.119.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.119.0...v3.119.1) (2024-02-20)


### Bug Fixes

* **datoms policy:** datoms policy added for mobile app ([34a5adf](https://github.com/Datoms-IoT/datoms-webapp/commit/34a5adf1c009d0a6c4970c28c81a468e2de55377))

# [3.119.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.118.1...v3.119.0) (2024-02-20)


### Features

* **pollution range comparison report:** poMo parameters range comparison report ([5ff2f5f](https://github.com/Datoms-IoT/datoms-webapp/commit/5ff2f5f3c65701b68d561232354d1e08037f8a38))

## [3.118.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.118.0...v3.118.1) (2024-02-20)


### Bug Fixes

* **datoms policy:** page redirection after policy acceptance issue ([fba15f1](https://github.com/Datoms-IoT/datoms-webapp/commit/fba15f1539bb6145d9366af7105ed61babde3dfa))

# [3.118.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.117.3...v3.118.0) (2024-02-19)


### Features

* **datoms policy:** terms of Service and Privacy Policy Acceptance Page added ([04782f0](https://github.com/Datoms-IoT/datoms-webapp/commit/04782f0a4d04f41231801486fd4a5f3e4dd58d68))

## [3.117.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.117.2...v3.117.3) (2024-02-16)


### Bug Fixes

* **site consumption:** site Consumption Report for all Customers ([6e9a7b2](https://github.com/Datoms-IoT/datoms-webapp/commit/6e9a7b28777c36352c78264b8ad764dab30b1632))

## [3.117.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.117.1...v3.117.2) (2024-02-15)


### Bug Fixes

* **flow meter daily report:** flowmeter daily report total volume chnaged ([3db4394](https://github.com/Datoms-IoT/datoms-webapp/commit/3db43947b722696ec5e1b28c5fd399d6cad01cd9))

## [3.117.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.117.0...v3.117.1) (2024-02-15)


### Bug Fixes

* **js-sdk:** added error handling for cordova http plugin ([8114313](https://github.com/Datoms-IoT/datoms-webapp/commit/8114313df27003ce5df647cd22bc91fac10f3402))

# [3.117.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.116.1...v3.117.0) (2024-02-15)


### Features

* **users management:** new column added to show security details ([35abf7c](https://github.com/Datoms-IoT/datoms-webapp/commit/35abf7c84f215de8497908afd9d1c96032945bdc))

## [3.116.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.116.0...v3.116.1) (2024-02-15)


### Bug Fixes

* **site consumption:** site consumption report style fixes ([fd50843](https://github.com/Datoms-IoT/datoms-webapp/commit/fd50843991fd5ba7154a74cc741777ff794d3264))

# [3.116.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.115.1...v3.116.0) (2024-02-14)


### Features

* **site consumption report:** site Consumption Report added for DATOMS-X users ([2e5643d](https://github.com/Datoms-IoT/datoms-webapp/commit/2e5643dec7500ac446058935c684a4011152c5e2))

## [3.115.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.115.0...v3.115.1) (2024-02-13)


### Bug Fixes

* **software-download-link:** link copy button given for software download ([42dc7bd](https://github.com/Datoms-IoT/datoms-webapp/commit/42dc7bd4d5e70bc3986ae7bfee3c98ab3dc1656c))

# [3.115.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.114.0...v3.115.0) (2024-02-09)

### Features

- **trip view:** single view for both trips and fuel transactions ([83cfd56](https://github.com/Datoms-IoT/datoms-webapp/commit/83cfd565fe5005c68fe75bb9ca04c8e1d5d6052d))

# [3.114.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.113.13...v3.114.0) (2024-02-09)

### Features

- **remote calibration:** remote calibration structure change and module updated ([ec84282](https://github.com/Datoms-IoT/datoms-webapp/commit/ec84282eccff6d331b6ffa28ce418532c31741d9))

## [3.113.13](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.113.12...v3.113.13) (2024-02-08)

### Bug Fixes

- **routes:** landing page update based on role ([1574edf](https://github.com/Datoms-IoT/datoms-webapp/commit/1574edfe15a80cfb547197bd218371e074134252))
- **settings:** button provided to reauthenticate in security page ([795a5a8](https://github.com/Datoms-IoT/datoms-webapp/commit/795a5a87727b6ed722158da506849a9943d0c39a))
- **user sessions:** session status filter restricted to datoms and aurassure users ([a7afb5b](https://github.com/Datoms-IoT/datoms-webapp/commit/a7afb5b54bff7b4583d942607400940cf902d9ae))

## [3.113.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.113.11...v3.113.12) (2024-02-08)

### Bug Fixes

- **mobile app:** fixed - mobile app login api throwing erorr when opened from backgrounf ([8545814](https://github.com/Datoms-IoT/datoms-webapp/commit/854581445dd9de08fdabdd3a1c6e76266efd01d7))

## [3.113.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.113.10...v3.113.11) (2024-02-08)

### Bug Fixes

- **mobile app:** removed android:allowbackup flag ([ee84f61](https://github.com/Datoms-IoT/datoms-webapp/commit/ee84f61a21cf67537e5ad2b4affb3f6944393078))

## [3.113.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.113.9...v3.113.10) (2024-02-07)

### Bug Fixes

- **energy meter:** energy meter l-n voltage and freq range updated ([795a144](https://github.com/Datoms-IoT/datoms-webapp/commit/795a144fad59ace303cf5f645a5cf603281959de))

## [3.113.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.113.8...v3.113.9) (2024-02-06)

### Bug Fixes

- **asset management:** fixed - hardware interfacevalues are not shown on system template selection ([6f153de](https://github.com/Datoms-IoT/datoms-webapp/commit/6f153de3b853ea22776b7ee2d64383edab861b1a))

## [3.113.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.113.7...v3.113.8) (2024-02-06)

### Bug Fixes

- **alerts:** faults & constant alerts grouping ([f2ffceb](https://github.com/Datoms-IoT/datoms-webapp/commit/f2ffceba20c371be106bf8221de3560da7c9c645))

## [3.113.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.113.6...v3.113.7) (2024-02-05)

### Bug Fixes

- **asset dashboard:** vendor id key in url updated for task api ([0eb136d](https://github.com/Datoms-IoT/datoms-webapp/commit/0eb136d52c3394319a8148f1047148889d648e48))

## [3.113.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.113.5...v3.113.6) (2024-02-05)

### Bug Fixes

- **asset dashboard:** asset dashboard optimization ([6300c18](https://github.com/Datoms-IoT/datoms-webapp/commit/6300c18bdd22530d2bf8e2294e38c9407138e32e))
- **asset dashboard:** asset Dashboard optimization ([d7481f7](https://github.com/Datoms-IoT/datoms-webapp/commit/d7481f770a9e961186cdbb1af2c4b64c7c35951a))

## [3.113.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.113.4...v3.113.5) (2024-02-05)

### Bug Fixes

- **asset management:** fixed - device re-assign error when asset addition fails ([006d86a](https://github.com/Datoms-IoT/datoms-webapp/commit/006d86af3776ec2bbb27c8916e923a599527be2c))

## [3.113.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.113.3...v3.113.4) (2024-02-05)

### Bug Fixes

- **mobile app:** added allowbackup false in mobile application ([8c37dc5](https://github.com/Datoms-IoT/datoms-webapp/commit/8c37dc5280c767a8c2c3ca4473fb13466b2a3683))

## [3.113.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.113.2...v3.113.3) (2024-02-05)

### Bug Fixes

- **dg views:** fixed - fuel tank icon is not showing in map view page in android ([d0016e2](https://github.com/Datoms-IoT/datoms-webapp/commit/d0016e2a0189b34fde7c0a0e4ff46548503337df))

## [3.113.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.113.1...v3.113.2) (2024-02-05)

### Bug Fixes

- **battery:** battery thing type summary section & detailed view raw graph min range changed to one ([b462a61](https://github.com/Datoms-IoT/datoms-webapp/commit/b462a61ee439553ea115b4cdb58d1286e9530abc))

## [3.113.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.113.0...v3.113.1) (2024-02-02)

### Bug Fixes

- **map component:** map component bug fix ([c259f4f](https://github.com/Datoms-IoT/datoms-webapp/commit/c259f4f30250d0c1da9e159c8e0d7f78c351cc37))

# [3.113.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.112.4...v3.113.0) (2024-02-02)

### Features

- **map view:** geofence feature added ([2bd0c77](https://github.com/Datoms-IoT/datoms-webapp/commit/2bd0c77da791b1cbd844c2587dbfb30c62202957))

## [3.112.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.112.3...v3.112.4) (2024-02-02)

### Bug Fixes

- **settings:** 2FA page enabled for all customers in webapp ([4758b8d](https://github.com/Datoms-IoT/datoms-webapp/commit/4758b8d1787b8b8fa558ddda4f6aa2ed87d851b0))

## [3.112.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.112.2...v3.112.3) (2024-02-01)

### Bug Fixes

- **alerts:** timeout alerts violation timeout edit enabled ([c360285](https://github.com/Datoms-IoT/datoms-webapp/commit/c3602852e3b2e961b5d5bf928f7983be5814a8f5))

## [3.112.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.112.1...v3.112.2) (2024-02-01)

### Bug Fixes

- **asset management:** added asset type blacklist for parameter settings ([d918b81](https://github.com/Datoms-IoT/datoms-webapp/commit/d918b81ecbb44a1755bbb50470262f445875ed93))

## [3.112.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.112.0...v3.112.1) (2024-01-31)

### Bug Fixes

- **settings:** security page added for aurassure ([c428f07](https://github.com/Datoms-IoT/datoms-webapp/commit/c428f070f73a5bde299db6aa9a7a179363541f43))

# [3.112.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.20...v3.112.0) (2024-01-31)

### Features

- **settings:** user sessions & 2FA authentication added ([9a7095a](https://github.com/Datoms-IoT/datoms-webapp/commit/9a7095a61d311ade766933fa6e36700e93cff9a4))

## [3.111.20](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.19...v3.111.20) (2024-01-31)

### Bug Fixes

- **ip camera:** ip camera thing type added ([61dab52](https://github.com/Datoms-IoT/datoms-webapp/commit/61dab526f634b8058c3d0c4145ddb31f1fed1f4e))

## [3.111.19](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.18...v3.111.19) (2024-01-31)

### Bug Fixes

- **battery panel:** summary hidden from battery panel ([6b9707f](https://github.com/Datoms-IoT/datoms-webapp/commit/6b9707fd9b9557c2b697d8a64ba240544c3b1c93))

## [3.111.18](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.17...v3.111.18) (2024-01-30)

### Bug Fixes

- **site:** site not coming issue fixed in panel page ([8f70a5f](https://github.com/Datoms-IoT/datoms-webapp/commit/8f70a5ffcde65e5dfaa3f4f4974c260cfabe9042))

## [3.111.17](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.16...v3.111.17) (2024-01-30)

### Bug Fixes

- **asset management:** added "Rented To" options for all rental asset types ([657c935](https://github.com/Datoms-IoT/datoms-webapp/commit/657c935f38d674b3070a72fa9f68a8deed8b8d73))

## [3.111.16](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.15...v3.111.16) (2024-01-30)

### Bug Fixes

- **pollution monitoring:** pollution monitoring all parameters enabled for views ([660b57e](https://github.com/Datoms-IoT/datoms-webapp/commit/660b57e79deb66678c5aeec5b558b7f0d3d96184))

## [3.111.15](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.14...v3.111.15) (2024-01-29)

### Bug Fixes

- **reports:** dC daily report remove ([fa0d821](https://github.com/Datoms-IoT/datoms-webapp/commit/fa0d82192721ba6afc4511af4958944dbe2ab2e2))

## [3.111.14](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.13...v3.111.14) (2024-01-25)

### Bug Fixes

- **aurassure:** aurassure scale fixed for aqi parameters ([255750e](https://github.com/Datoms-IoT/datoms-webapp/commit/255750e53bf43edadb746a51730631a3bbd461e1))

## [3.111.13](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.12...v3.111.13) (2024-01-24)

### Bug Fixes

- **real time:** energy meter real time updated with all params ([8b68891](https://github.com/Datoms-IoT/datoms-webapp/commit/8b6889107566a650f070b25915bf5ebb28738fd8))

## [3.111.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.11...v3.111.12) (2024-01-24)

### Bug Fixes

- **assetdashboard:** asset dashboard load wise rnhr graph filtering fixed ([21c2353](https://github.com/Datoms-IoT/datoms-webapp/commit/21c235340a255ae106d15421cf7d24437de26bf7))

## [3.111.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.10...v3.111.11) (2024-01-24)

### Bug Fixes

- **site management:** fixed - sites user access issue' ([b7971bb](https://github.com/Datoms-IoT/datoms-webapp/commit/b7971bb85d25319c6d72ba0aa0296af28f71d36f))

## [3.111.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.9...v3.111.10) (2024-01-24)

### Bug Fixes

- **asset management:** added parameter settings for cold storage asset type ([67674b7](https://github.com/Datoms-IoT/datoms-webapp/commit/67674b7f4497b9761881fcff2fae0ae0f2466ee0))

## [3.111.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.8...v3.111.9) (2024-01-24)

### Bug Fixes

- **energy meter:** energy meter raw and avg graph should come ([c013c9c](https://github.com/Datoms-IoT/datoms-webapp/commit/c013c9c3eb20e55be1056e1dc7c35576ee109206))

## [3.111.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.7...v3.111.8) (2024-01-23)

### Bug Fixes

- **site:** site option added to map view and panel view ([3a9a3da](https://github.com/Datoms-IoT/datoms-webapp/commit/3a9a3dad31c5d383adc1977ab489a414d2c3e3f0))

## [3.111.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.6...v3.111.7) (2024-01-22)

### Bug Fixes

- **site management:** bug fixes and improvements ([84563f0](https://github.com/Datoms-IoT/datoms-webapp/commit/84563f0fc99d824e653cfa9dcd688041e68216fe))

## [3.111.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.5...v3.111.6) (2024-01-22)

### Bug Fixes

- **realtime to analog:** realtime lebel changed to analog throughout the platform ([a80e93d](https://github.com/Datoms-IoT/datoms-webapp/commit/a80e93d0ca04edecbd8c20d4b050473dcd5edd6f))
- **remove device delete:** device delete option removed form device management ([dc78940](https://github.com/Datoms-IoT/datoms-webapp/commit/dc78940e436ef10ff62cd802a3f73ef7d46675ca))

## [3.111.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.4...v3.111.5) (2024-01-19)

### Bug Fixes

- **site management:** added pupose for each asset type category ([a85437b](https://github.com/Datoms-IoT/datoms-webapp/commit/a85437baf70f08d10a3d07d3e64d447df1cd65da))

## [3.111.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.3...v3.111.4) (2024-01-19)

### Bug Fixes

- **generic:** energy meter map view data fixed ([4e6e55a](https://github.com/Datoms-IoT/datoms-webapp/commit/4e6e55a428e48cc8628ed36f9b0c507a279ba45b))

## [3.111.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.2...v3.111.3) (2024-01-19)

### Bug Fixes

- **aurassure:** aurassure parameters scale updated ([d6eb4af](https://github.com/Datoms-IoT/datoms-webapp/commit/d6eb4af2c931639f08e5c3d86e60f9391912c1eb))

## [3.111.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.1...v3.111.2) (2024-01-19)

### Bug Fixes

- **reports:** inverter report seperated from energy meter ([c2c8c42](https://github.com/Datoms-IoT/datoms-webapp/commit/c2c8c42100c86211422777f80d20d11b9c364cd0))

## [3.111.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.111.0...v3.111.1) (2024-01-18)

### Bug Fixes

- **reports:** tempHumid and Inverter report updated ([a4b6e3f](https://github.com/Datoms-IoT/datoms-webapp/commit/a4b6e3f1bb934ebbeca1c17ff680f002f92fcf4f))

# [3.111.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.110.5...v3.111.0) (2024-01-18)

### Features

- **site:** battery and inverter type added ([63d62c6](https://github.com/Datoms-IoT/datoms-webapp/commit/63d62c6e077013d1e767695daa610c4faf9c57d2))

## [3.110.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.110.4...v3.110.5) (2024-01-18)

### Bug Fixes

- **selco:** temperature key changed for temp&humid category ([c42032a](https://github.com/Datoms-IoT/datoms-webapp/commit/c42032a5d8e8167e3ad50b6725d0eacef7d1c3f7))

## [3.110.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.110.3...v3.110.4) (2024-01-18)

### Bug Fixes

- **aurassure:** aurassure all views for single thing ([b0d4620](https://github.com/Datoms-IoT/datoms-webapp/commit/b0d4620e13dd7e5d3b19828f56f744b01a2da79a))

## [3.110.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.110.2...v3.110.3) (2024-01-18)

### Bug Fixes

- **water leve:** wather level added to generic ([44c4d4f](https://github.com/Datoms-IoT/datoms-webapp/commit/44c4d4f7288c1c49fb3da750ed6b1e020cc35e17))

## [3.110.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.110.1...v3.110.2) (2024-01-18)

### Bug Fixes

- **selco:** category id changed for solar and temperature and humidity ([b235105](https://github.com/Datoms-IoT/datoms-webapp/commit/b235105cea0dbafd31312bc7741a2732784accae))

## [3.110.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.110.0...v3.110.1) (2024-01-17)

### Bug Fixes

- **asset management:** removed default asset info form for aurassure ([a36238c](https://github.com/Datoms-IoT/datoms-webapp/commit/a36238c212dc43bbf8bb44bc12c34ab11f06595a))
- **aurassure:** street thing category name changed to Water Level Monitor ([d84a7a2](https://github.com/Datoms-IoT/datoms-webapp/commit/d84a7a237d81ee26a29be8b6fd2ce9eccd38be69))

# [3.110.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.109.7...v3.110.0) (2024-01-17)

### Features

- **selco:** cold storage, temp & humid, energy and solar power deployed for selco ([f31ac1d](https://github.com/Datoms-IoT/datoms-webapp/commit/f31ac1de187685a28ae617daf4cd2446d8745374))

## [3.109.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.109.6...v3.109.7) (2024-01-17)

### Bug Fixes

- **aurassure:** street View Added ([3107ed1](https://github.com/Datoms-IoT/datoms-webapp/commit/3107ed181586d72613f6022111c01cd37fe84321))

## [3.109.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.109.5...v3.109.6) (2024-01-16)

### Bug Fixes

- **asset dashboard:** access added for Asset dashboard ([edc1c96](https://github.com/Datoms-IoT/datoms-webapp/commit/edc1c96e08931c3a34147cbfc903874518b60f06))

## [3.109.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.109.4...v3.109.5) (2024-01-12)

### Bug Fixes

- **aurassure:** aurassure major pollutant value update and AQI value in detailed view ([43b282e](https://github.com/Datoms-IoT/datoms-webapp/commit/43b282e812542ffd4776c3bd8eeafbbe15f164e5))

## [3.109.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.109.3...v3.109.4) (2024-01-12)

### Bug Fixes

- **js-sdk:** fixed - update preferences is not working ([631084f](https://github.com/Datoms-IoT/datoms-webapp/commit/631084fc0a5f89757b8e8d2cd96fddce30c8d37b))

## [3.109.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.109.2...v3.109.3) (2024-01-12)

### Bug Fixes

- **billing management:** fixed - customer name not showing in subscription edit ([1be9747](https://github.com/Datoms-IoT/datoms-webapp/commit/1be97472444fed87e34e8e755a5ca34a0ebf6d52))

## [3.109.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.109.1...v3.109.2) (2024-01-12)

### Bug Fixes

- **aurasssure:** aqi last hour data update for online and offline ([8e4b584](https://github.com/Datoms-IoT/datoms-webapp/commit/8e4b5848aa9eeab1576e0a45fdb2861572486cce))

## [3.109.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.109.0...v3.109.1) (2024-01-12)

### Bug Fixes

- **aurassure:** aurassure last hour aqi for offline and online things ([435213c](https://github.com/Datoms-IoT/datoms-webapp/commit/435213c4b576aaeceb5372b68fcf259d8795ade7))

# [3.109.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.108.3...v3.109.0) (2024-01-11)

### Features

- **site management:** added Site Management As Feature ([2dfddf3](https://github.com/Datoms-IoT/datoms-webapp/commit/2dfddf305752f7e28155218b96c4350646f16a1e))

## [3.108.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.108.2...v3.108.3) (2024-01-11)

### Bug Fixes

- **asset dashboard:** asset dashboard fuel filled graph should show non zero when only active ([461c5e1](https://github.com/Datoms-IoT/datoms-webapp/commit/461c5e1bda5766decc266ad003d656854ac2a402))

## [3.108.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.108.1...v3.108.2) (2024-01-10)

### Bug Fixes

- **aurassure:** aurassure detailed view hourly graph time fixed ([7ce5234](https://github.com/Datoms-IoT/datoms-webapp/commit/7ce52348d371551ce3b1c5ff089f0b423159f155))

## [3.108.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.108.0...v3.108.1) (2024-01-10)

### Bug Fixes

- **thingstatus:** online Offline dynamic from static in things status ([ccb75e4](https://github.com/Datoms-IoT/datoms-webapp/commit/ccb75e499a339cf081159d9440819e170d335439))

# [3.108.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.107.0...v3.108.0) (2024-01-09)

### Features

- **dg asset dashboard:** dG Asset Dashboard deployment for datoms-x ([7160d17](https://github.com/Datoms-IoT/datoms-webapp/commit/7160d17455c3b0ca591d340906be3701379c4931))

# [3.107.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.106.2...v3.107.0) (2024-01-09)

### Features

- **alerts:** recurring maintenance alerts ([ca3494e](https://github.com/Datoms-IoT/datoms-webapp/commit/ca3494eb8d08693887fd020b7f692ac7f12d1b40))

## [3.106.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.106.1...v3.106.2) (2024-01-09)

### Bug Fixes

- **customreport:** custom Report IAQMS enabled ([1eb6eb3](https://github.com/Datoms-IoT/datoms-webapp/commit/1eb6eb3996b1fcfe98771e0f098a99809919afda))

## [3.106.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.106.0...v3.106.1) (2024-01-08)

### Bug Fixes

- **aurassure:** aurassure all parameters added to panel view with order ([3cb5e7b](https://github.com/Datoms-IoT/datoms-webapp/commit/3cb5e7b72834d2ea4b8f0e5cf9d407f3d556a9a5))

# [3.106.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.105.4...v3.106.0) (2024-01-04)

### Features

- **customers:** dealer management feature added ([9022331](https://github.com/Datoms-IoT/datoms-webapp/commit/9022331960b8605d16797eea3cbe4388d5dce087))

## [3.105.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.105.3...v3.105.4) (2024-01-04)

### Bug Fixes

- **label change:** thing label changed to Asset ([2fc3321](https://github.com/Datoms-IoT/datoms-webapp/commit/2fc3321d6737dd932d202c14de834e992d7c2130))

## [3.105.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.105.2...v3.105.3) (2024-01-03)

### Bug Fixes

- **detailed view:** energy monitoring detailed view all parameters enabled ([19f1e26](https://github.com/Datoms-IoT/datoms-webapp/commit/19f1e26312bee4a49b10be8d87df179e873a9a23))

## [3.105.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.105.1...v3.105.2) (2024-01-02)

### Bug Fixes

- **view dropdown:** white screen issue fixed for view dropdown in iot platform ([8744221](https://github.com/Datoms-IoT/datoms-webapp/commit/87442215a05d73c3b2a9f518845f1bed052fd19f))

## [3.105.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.105.0...v3.105.1) (2023-12-23)

### Bug Fixes

- **asset management:** asset tagging (cpcb4 & 2) bugfixes and improvements ([119a451](https://github.com/Datoms-IoT/datoms-webapp/commit/119a451ec6fe33980124dc03164f1655d857b2d7))

# [3.105.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.104.1...v3.105.0) (2023-12-22)

### Features

- **asset management:** added cpcb2 and cpcb4 filters in thing list and asset status report ([aaea97a](https://github.com/Datoms-IoT/datoms-webapp/commit/aaea97abbd36e33e03af18e11b6e064ae8653a26))

## [3.104.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.104.0...v3.104.1) (2023-12-22)

### Bug Fixes

- **asset management:** fixed - asset addition throws error when fuel tank capacity is zero ([b74b57f](https://github.com/Datoms-IoT/datoms-webapp/commit/b74b57fc9de9d312ec2391b7d84b8d452fad48d4))

# [3.104.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.103.4...v3.104.0) (2023-12-22)

### Features

- **customers:** migration of end customers from Partners(non rental) to Phoenix Robotix ([08f2d1d](https://github.com/Datoms-IoT/datoms-webapp/commit/08f2d1dfc44391d287de807720a420527457eac0))

## [3.103.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.103.3...v3.103.4) (2023-12-21)

### Bug Fixes

- **ipcamera:** iP camera direction not changing - fixed ([981a24a](https://github.com/Datoms-IoT/datoms-webapp/commit/981a24adbd687a7e1dbb3248c976575eab3cece7))

## [3.103.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.103.2...v3.103.3) (2023-12-21)

### Bug Fixes

- **file validation:** block upload of file with multiple extension ([f2bbddb](https://github.com/Datoms-IoT/datoms-webapp/commit/f2bbddb3cd66b6316118f54e9a07c0e7b19701c2))

## [3.103.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.103.1...v3.103.2) (2023-12-20)

### Bug Fixes

- **webapp:** changed - fetching socket token from js-sdk ([cba60fd](https://github.com/Datoms-IoT/datoms-webapp/commit/cba60fdf9c0c17522346e2bbaffb4b646d0c64ec))

## [3.103.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.103.0...v3.103.1) (2023-12-20)

### Bug Fixes

- **viewppages-menu:** view pages menu issue fixed ([0ec40d2](https://github.com/Datoms-IoT/datoms-webapp/commit/0ec40d2b0af86fc7f48fed19eed8b35755115624))

# [3.103.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.102.0...v3.103.0) (2023-12-20)

### Features

- **billing management:** added support for multi-select in generic filter ([41edb2f](https://github.com/Datoms-IoT/datoms-webapp/commit/41edb2fe956245ff59ecc54a0707a5eea24724f0))

# [3.102.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.101.10...v3.102.0) (2023-12-19)

### Features

- **assetdashboard:** asset dashboard flowmeter mobile version ([63e917b](https://github.com/Datoms-IoT/datoms-webapp/commit/63e917b5c52a2af77463e4d9c274d73d088d76c6))

## [3.101.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.101.9...v3.101.10) (2023-12-19)

### Bug Fixes

- **assetdashboard:** asset dashboard available for things dashboard in datoms-x ([e6cdc35](https://github.com/Datoms-IoT/datoms-webapp/commit/e6cdc35792ebdd2db4a5a94ae47fe01900736b28))

## [3.101.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.101.8...v3.101.9) (2023-12-19)

### Bug Fixes

- **assetdashboard:** asset dashboard flow meter summary ([993e57a](https://github.com/Datoms-IoT/datoms-webapp/commit/993e57a06f3eacf3b6bcdae3c3a128ddbe5506cb))

## [3.101.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.101.7...v3.101.8) (2023-12-18)

### Bug Fixes

- **assetdashboard:** flow Meter asset dashboard design fixes, api calling changes ([333bafa](https://github.com/Datoms-IoT/datoms-webapp/commit/333bafa85af0c68300b7ee30fd16454f57b9fc2c))

## [3.101.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.101.6...v3.101.7) (2023-12-18)

### Bug Fixes

- **trip view:** multi parameter trend & customer, partner columns added ([5ab1fed](https://github.com/Datoms-IoT/datoms-webapp/commit/5ab1fed731ee01d5bee6387ca66b0ddb7844db97))
- **trip view:** parameter order defined for DG in parameter trend page ([1cc6f59](https://github.com/Datoms-IoT/datoms-webapp/commit/1cc6f59ee555673a6e411e0a8a836e47d9e8ac8b))

## [3.101.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.101.5...v3.101.6) (2023-12-15)

### Bug Fixes

- **reports:** daily reports unnecessary function calling removed ([2f20127](https://github.com/Datoms-IoT/datoms-webapp/commit/2f20127ed45c9d821ca607ae228fc4ef628f9da7))

## [3.101.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.101.4...v3.101.5) (2023-12-15)

### Bug Fixes

- **reports:** reports download modal not opening issue fixed ([6e5bd38](https://github.com/Datoms-IoT/datoms-webapp/commit/6e5bd3883e4ea8b9e45703a6cd977eca896feb7b))

## [3.101.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.101.3...v3.101.4) (2023-12-13)

### Bug Fixes

- **js-sdk:** fixed - window not defined error in roll-out ([5941af1](https://github.com/Datoms-IoT/datoms-webapp/commit/5941af1aa17ec8bdc7b9d8c686daa5216849f066))

## [3.101.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.101.2...v3.101.3) (2023-12-13)

### Bug Fixes

- **js-sdk:** fixed - window is not defined error in pipeline ([b927d98](https://github.com/Datoms-IoT/datoms-webapp/commit/b927d989ec681bf26c0bebdfe5868e5a6b7202f0))

## [3.101.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.101.1...v3.101.2) (2023-12-13)

### Bug Fixes

- **mobile app:** added cordova plugin http code ([b4cf5aa](https://github.com/Datoms-IoT/datoms-webapp/commit/b4cf5aa2a3cf3359bf44e46c6488e8438a75069f))

## [3.101.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.101.0...v3.101.1) (2023-12-13)

### Bug Fixes

- **dg monitoring:** dg monitoring map re centering issue fixed ([19b8556](https://github.com/Datoms-IoT/datoms-webapp/commit/19b85564c34322d6ec9a56a3d7eab80897f24481))

# [3.101.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.100.4...v3.101.0) (2023-12-13)

### Features

- **mobile app:** routed mobile app api calls through cordova-http plugin ([ac89924](https://github.com/Datoms-IoT/datoms-webapp/commit/ac899249495d291569966de91313c36d462ea29b))

## [3.100.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.100.3...v3.100.4) (2023-12-12)

### Bug Fixes

- **generic detailed:** generic detailed view average calculation updated ([38bdb9d](https://github.com/Datoms-IoT/datoms-webapp/commit/38bdb9d5b99d32f96422754f3a2349fe716d1f6a))

## [3.100.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.100.2...v3.100.3) (2023-12-12)

### Bug Fixes

- **electricalmachines:** electrical machines panel design change for flip enery meter ([3f8c17c](https://github.com/Datoms-IoT/datoms-webapp/commit/3f8c17c2c342874e7cf08b2eaf3533b4c29f5d0a))

## [3.100.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.100.1...v3.100.2) (2023-12-12)

### Bug Fixes

- **generic:** electrical machines config updated in categories ([8a35f67](https://github.com/Datoms-IoT/datoms-webapp/commit/8a35f679b5cb64afa7932f3d2b4006fb4de6c4f3))

## [3.100.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.100.0...v3.100.1) (2023-12-12)

### Bug Fixes

- **assetdashboard:** asset dashboard enable for only datoms-x ([0fb01bf](https://github.com/Datoms-IoT/datoms-webapp/commit/0fb01bff37bcd731029a3e282cb44d8e4549717b))

# [3.100.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.99.3...v3.100.0) (2023-12-11)

### Features

- **assetdashboard:** asset dashboard implemented ([44ecaf3](https://github.com/Datoms-IoT/datoms-webapp/commit/44ecaf3cdcc84c42bd7e975cdd563594b6acdecf))

## [3.99.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.99.2...v3.99.3) (2023-12-11)

### Bug Fixes

- **generic:** generic template 7 days not working issue fixed ([d4c1abd](https://github.com/Datoms-IoT/datoms-webapp/commit/d4c1abd79f1884c25d4f01b77fcc089b63082d6f))

## [3.99.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.99.1...v3.99.2) (2023-12-11)

### Bug Fixes

- **asset management:** added parameter order while saving ([7d35a00](https://github.com/Datoms-IoT/datoms-webapp/commit/7d35a007c25985e5dcd74b9e39d911c73c7904dc))

## [3.99.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.99.0...v3.99.1) (2023-12-06)

### Bug Fixes

- **asset management:** fixed - thing addition failing in some cases ([dcb2273](https://github.com/Datoms-IoT/datoms-webapp/commit/dcb2273e0dc648f21b35fa9e6c1457ef9939f658))

# [3.99.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.98.1...v3.99.0) (2023-12-05)

### Features

- **trip view:** parameter trend of a trip ([c3c6f8f](https://github.com/Datoms-IoT/datoms-webapp/commit/c3c6f8f7c43fe34972f82b533b35c33e4c73ba51))

## [3.98.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.98.0...v3.98.1) (2023-12-05)

### Bug Fixes

- **environmentmonitoring:** aurassure socket calling fix for raw ([6cd97c7](https://github.com/Datoms-IoT/datoms-webapp/commit/6cd97c706c344fd9c5de0b6133b2bd9c3b9919cf))

# [3.98.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.97.3...v3.98.0) (2023-12-04)

### Features

- **billing management:** added subscription basis (device & thing) ([a671f43](https://github.com/Datoms-IoT/datoms-webapp/commit/a671f4310725197ac1f0d49af00449283d0e5d83))

## [3.97.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.97.2...v3.97.3) (2023-12-04)

### Bug Fixes

- **custom report:** custom report customize loading fix ([ca8bb0f](https://github.com/Datoms-IoT/datoms-webapp/commit/ca8bb0f00cb5f7c4fa287437fed46c6f5e08e81b))

## [3.97.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.97.1...v3.97.2) (2023-12-04)

### Bug Fixes

- **customreport:** custom report parameter order as per order set ([acfc9c7](https://github.com/Datoms-IoT/datoms-webapp/commit/acfc9c7b1a9cce271d310752397e3a631553358c))

## [3.97.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.97.0...v3.97.1) (2023-12-04)

### Bug Fixes

- **asset management:** added supported units for aurassure in parameter configuration ([dc9f3c2](https://github.com/Datoms-IoT/datoms-webapp/commit/dc9f3c2683ac4bff1ab24f9acf589564fbb96c8e))

# [3.97.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.96.3...v3.97.0) (2023-12-02)

### Features

- **generictemplate:** param order defined for aurassure panel,generic detailed view, custom report ([de5ef03](https://github.com/Datoms-IoT/datoms-webapp/commit/de5ef03d3b5f237e24731c525c2652f8f8a0cf3b))

## [3.96.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.96.2...v3.96.3) (2023-11-30)

### Bug Fixes

- **mahindra:** mahindra real time current and act. power updated as per KVA ([e1f7738](https://github.com/Datoms-IoT/datoms-webapp/commit/e1f7738885376eb6e9344ce3cdd23a32b16792e3))

## [3.96.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.96.1...v3.96.2) (2023-11-30)

### Bug Fixes

- **account type:** additional points fixed ([69a7617](https://github.com/Datoms-IoT/datoms-webapp/commit/69a76170fa7452bfd55dee4269063aac578b6d18))

## [3.96.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.96.0...v3.96.1) (2023-11-30)

### Bug Fixes

- **head.js:** removed customer details api call for multi application ([1c1b129](https://github.com/Datoms-IoT/datoms-webapp/commit/1c1b12971bf4cce577b0f90f6c5c5486b4e3403f))

# [3.96.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.95.4...v3.96.0) (2023-11-29)

### Features

- account type & end customer visibility ([436ffff](https://github.com/Datoms-IoT/datoms-webapp/commit/436ffffd02e7372a1f0342dc8fd3d70350ff7d2b))

## [3.95.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.95.3...v3.95.4) (2023-11-29)

### Bug Fixes

- **asset management:** added multi-system support in asset templates ([b1c9397](https://github.com/Datoms-IoT/datoms-webapp/commit/b1c939713de08a4e8241a7759b1608ee53fca0e2))

## [3.95.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.95.2...v3.95.3) (2023-11-29)

### Bug Fixes

- **app.js:** production engineer role access issue fixed ([75f46f3](https://github.com/Datoms-IoT/datoms-webapp/commit/75f46f36db2536744cf5efe120c0820158127248))

## [3.95.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.95.1...v3.95.2) (2023-11-28)

### Bug Fixes

- **billing management:** bug fixes and improvements ([5eaedfc](https://github.com/Datoms-IoT/datoms-webapp/commit/5eaedfccb2531ac6fe2206b0a1094cb98ad876f2))

## [3.95.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.95.0...v3.95.1) (2023-11-28)

### Bug Fixes

- **subscription management:** added loading for export data & removed unused imports (asset config) ([59734d2](https://github.com/Datoms-IoT/datoms-webapp/commit/59734d2954f54d47e0e76bf0effaea4570f0c6b3))

# [3.95.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.94.3...v3.95.0) (2023-11-27)

### Features

- **subscription management:** added Advaced Filters and Export to Excel ([71203a7](https://github.com/Datoms-IoT/datoms-webapp/commit/71203a77463441ceca071444372089658e93fea0))

## [3.94.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.94.2...v3.94.3) (2023-11-23)

### Bug Fixes

- **user access:** user access conditions updated in add, edir, delete, deactivate ([c247c80](https://github.com/Datoms-IoT/datoms-webapp/commit/c247c80252f6e4587d691013dda0586fbe515466))

## [3.94.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.94.1...v3.94.2) (2023-11-23)

### Bug Fixes

- **firmware:** bin file validation fixed ([b83ff84](https://github.com/Datoms-IoT/datoms-webapp/commit/b83ff840a918db58351927c9b945a25b70b8f004))

## [3.94.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.94.0...v3.94.1) (2023-11-22)

### Bug Fixes

- **aurassure:** temperature and humidity key changed for aurassure ([4df4b41](https://github.com/Datoms-IoT/datoms-webapp/commit/4df4b414d285368e3a8252a0c0f7965d4298c45f))

# [3.94.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.93.7...v3.94.0) (2023-11-22)

### Features

- **asset management:** released new asset configuration for partners and end customers ([bdd3ec1](https://github.com/Datoms-IoT/datoms-webapp/commit/bdd3ec1937ef5af6a82a8290d6db7a899b5b5d5b))

## [3.93.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.93.6...v3.93.7) (2023-11-21)

### Bug Fixes

- **aurassure:** aQI enabled for aurassure detailed view ([d274745](https://github.com/Datoms-IoT/datoms-webapp/commit/d2747457e1e0555040088c51be88f48e50d6d532))

## [3.93.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.93.5...v3.93.6) (2023-11-21)

### Bug Fixes

- **lifetime reports:** energy key updated in lifetime reports ([cb69ec0](https://github.com/Datoms-IoT/datoms-webapp/commit/cb69ec0765daef04398aa44e47152cf2ac5cd9bc))

## [3.93.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.93.4...v3.93.5) (2023-11-21)

### Bug Fixes

- **asset management:** added meter(m),feet(ft) units and bug fixes ([3148e62](https://github.com/Datoms-IoT/datoms-webapp/commit/3148e6217ff5bc8676efffef651022e05a9b92a8))

## [3.93.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.93.3...v3.93.4) (2023-11-21)

### Bug Fixes

- **mahindra:** real time graph config fixed for mahindra ([e3e42d8](https://github.com/Datoms-IoT/datoms-webapp/commit/e3e42d8e2579511a0ec3dfb43253977564171c01))

## [3.93.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.93.2...v3.93.3) (2023-11-20)

### Bug Fixes

- **generic:** flow meter design changes and real time fixed ([5424793](https://github.com/Datoms-IoT/datoms-webapp/commit/5424793effe37f147086a983b00703a792a210f6))

## [3.93.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.93.1...v3.93.2) (2023-11-17)

### Bug Fixes

- **role add access:** role addition access given to aurassure vendor ([79b5b01](https://github.com/Datoms-IoT/datoms-webapp/commit/79b5b0106a0e0bea3a576ae31865adadff8ccb37))
- **role list:** created by system text removed from role list ([87b49d3](https://github.com/Datoms-IoT/datoms-webapp/commit/87b49d3489a56f675538c8f1a07f55ac6849b4a4))

## [3.93.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.93.0...v3.93.1) (2023-11-17)

### Bug Fixes

- **asset management:** aCL changes and end customer addition imporvements ([da55d12](https://github.com/Datoms-IoT/datoms-webapp/commit/da55d1267a8fc1e63674c106a29100b979ce365e))

# [3.93.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.92.11...v3.93.0) (2023-11-17)

### Features

- **generic:** borewell mapview ([b4bf29c](https://github.com/Datoms-IoT/datoms-webapp/commit/b4bf29c67556ee0b2940eee5da0ab0257fff50d7))

## [3.92.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.92.10...v3.92.11) (2023-11-16)

### Bug Fixes

- **aurassure:** aurassure icon change for humidity and temperature ([3f9e957](https://github.com/Datoms-IoT/datoms-webapp/commit/3f9e9574e88f08fc30ac34a9ce8d307860a33fe1))

## [3.92.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.92.9...v3.92.10) (2023-11-15)

### Bug Fixes

- **role-edit:** role edit issue fixed ([74a713f](https://github.com/Datoms-IoT/datoms-webapp/commit/74a713f817ef13dbf559fde0104c6f1c01745da8))

## [3.92.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.92.8...v3.92.9) (2023-11-14)

### Bug Fixes

- **aurassure:** aurassure colour scheme changed ([ae72380](https://github.com/Datoms-IoT/datoms-webapp/commit/ae72380419a827f7f1d4e5de8f3f55b5804ad68e))

## [3.92.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.92.7...v3.92.8) (2023-11-13)

### Bug Fixes

- **help-link:** vendor portal help url updated ([40d7451](https://github.com/Datoms-IoT/datoms-webapp/commit/40d74518954dce32805b6389e8f838e4fd29ca0d))

## [3.92.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.92.6...v3.92.7) (2023-11-13)

### Bug Fixes

- **package:** react highcharts removed ([390d3bf](https://github.com/Datoms-IoT/datoms-webapp/commit/390d3bf565eb95445a19cb5bf7d91ca7ef03e103))

## [3.92.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.92.5...v3.92.6) (2023-11-13)

### Bug Fixes

- **trip view:** removed order purpose, added tags functionality for vehicle trips ([833198d](https://github.com/Datoms-IoT/datoms-webapp/commit/833198d0c1c19da98d463c97d07dbae5502681ae))

## [3.92.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.92.4...v3.92.5) (2023-11-10)

### Bug Fixes

- **user-access:** user access key updated ([eaa46e6](https://github.com/Datoms-IoT/datoms-webapp/commit/eaa46e6574c16a1a14902e1e4e1595778f72b57a))

## [3.92.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.92.3...v3.92.4) (2023-11-10)

### Bug Fixes

- **overall:** changed cookies for active thing/fault & essential cookie banner to local storage ([90931e3](https://github.com/Datoms-IoT/datoms-webapp/commit/90931e3383a61cccf8e8347ac723febf333b7754))

## [3.92.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.92.2...v3.92.3) (2023-11-10)

## [3.92.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.92.1...v3.92.2) (2023-11-10)

### Bug Fixes

- **reports:** reports moment version updated ([890d384](https://github.com/Datoms-IoT/datoms-webapp/commit/890d384344a8bfa0e7239e36d346e5285138f815))

## [3.92.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.92.0...v3.92.1) (2023-11-10)

### Bug Fixes

- **production module:** composite addition sensor key structure change ([3adb98d](https://github.com/Datoms-IoT/datoms-webapp/commit/3adb98d570e78caec075dac83867325953b5db71))

# [3.92.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.91.0...v3.92.0) (2023-11-09)

### Features

- **production module:** new items addition ([4069e6c](https://github.com/Datoms-IoT/datoms-webapp/commit/4069e6c2c66ca4c22b081898a292076d6996cb46))

# [3.91.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.90.2...v3.91.0) (2023-11-09)

### Features

- **asset management:** onboarded aurassure system templates ([8f0be22](https://github.com/Datoms-IoT/datoms-webapp/commit/8f0be224c525846eac275855c549a5711a918c8e))

## [3.90.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.90.1...v3.90.2) (2023-11-09)

### Bug Fixes

- **aurassure:** ubreathe list view updated ([3e92611](https://github.com/Datoms-IoT/datoms-webapp/commit/3e926117c4696cafcd93dde8d7e4173513eb3ace))

## [3.90.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.90.0...v3.90.1) (2023-11-09)

### Bug Fixes

- **aurassure:** ubreathe aqi removed ([c7e6694](https://github.com/Datoms-IoT/datoms-webapp/commit/c7e669493fdc782b5e13b71474fccd8777099856))

# [3.90.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.89.2...v3.90.0) (2023-11-08)

### Features

- **flow & motor:** flow & motor onboard to datoms ([04e32ef](https://github.com/Datoms-IoT/datoms-webapp/commit/04e32efc9ba75417cbe49eec4672945d0487c13f))

## [3.89.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.89.1...v3.89.2) (2023-11-08)

### Bug Fixes

- **customers:** partner list in customer add of mobile app - fixed ([5a4f895](https://github.com/Datoms-IoT/datoms-webapp/commit/5a4f895df1d5c9a63d7744fdbaf1a9489d2a6ff0))

## [3.89.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.89.0...v3.89.1) (2023-11-07)

### Bug Fixes

- **dg monitorin:** fuel tank factor removed, daily report multiple data fix, date selection fix ([3e02125](https://github.com/Datoms-IoT/datoms-webapp/commit/3e021251e67d239dadca05c734d59cd3ce7531d5))

# [3.89.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.88.1...v3.89.0) (2023-11-07)

### Features

- **trip view:** "tags" column & filter added for trips ([1c7ef73](https://github.com/Datoms-IoT/datoms-webapp/commit/1c7ef73f5c5fe8c56ae3ef02c84bcb3cc12e2d24))

## [3.88.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.88.0...v3.88.1) (2023-11-06)

### Bug Fixes

- **generic:** mobile version for generic app ([d451452](https://github.com/Datoms-IoT/datoms-webapp/commit/d4514525d06b83196cb78d68cd9fb8fd66a7ec11))

# [3.88.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.87.3...v3.88.0) (2023-11-06)

### Features

- **asset management:** added Device - location Linking and improvements ([c5e90a5](https://github.com/Datoms-IoT/datoms-webapp/commit/c5e90a58c2036f82b862d9c996d9c85c8e5af526))

## [3.87.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.87.2...v3.87.3) (2023-11-03)

### Bug Fixes

- **file upload:** validation for upload file type ([42b0efa](https://github.com/Datoms-IoT/datoms-webapp/commit/42b0efae1fc49df26e9f7bd37068ac81f6cfeeec))

## [3.87.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.87.1...v3.87.2) (2023-11-03)

### Bug Fixes

- **product-report-download:** product report download issue fixed ([c8af487](https://github.com/Datoms-IoT/datoms-webapp/commit/c8af48787941d5a2a02909c8b4c074330812231e))

## [3.87.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.87.0...v3.87.1) (2023-11-02)

### Bug Fixes

- **product-delete:** product delete and test reset blocked if assigned to customer ([4eb0bbb](https://github.com/Datoms-IoT/datoms-webapp/commit/4eb0bbb760cfff9cd66a4d5f2715849bb078b2fb))

# [3.87.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.86.1...v3.87.0) (2023-11-01)

### Bug Fixes

- **customers:** reset edited values of application edit if form is closed without saving ([5c39dbd](https://github.com/Datoms-IoT/datoms-webapp/commit/5c39dbdeb0b80202b922255f03eb2e9d51dff000))

### Features

- **new feature:** device and thing status and custom command feature control for vendors ([42a02d5](https://github.com/Datoms-IoT/datoms-webapp/commit/42a02d5d89187987eaecce696f859e4d45c35731))

## [3.86.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.86.0...v3.86.1) (2023-11-01)

### Bug Fixes

- **asset management:** fixed - old asset configuration is not opening in some cases ([1c6a90f](https://github.com/Datoms-IoT/datoms-webapp/commit/1c6a90f2a7ec286b84888917f4f05dcf19d304d9))

# [3.86.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.85.1...v3.86.0) (2023-10-31)

### Features

- **generic:** aurassure and borewell embeded to generic ([2eab67b](https://github.com/Datoms-IoT/datoms-webapp/commit/2eab67b18261ccd6efeea41c7e116a3e062d5f0d))

## [3.85.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.85.0...v3.85.1) (2023-10-31)

### Bug Fixes

- **asset management:** fixed - unable to add asset with empty configuratioon ([40e6a9f](https://github.com/Datoms-IoT/datoms-webapp/commit/40e6a9fa1c84133815e309c67a369dc21b872846))

# [3.85.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.84.15...v3.85.0) (2023-10-30)

### Features

- **production-firmware:** production and firmware module access given to vendors via feature ([f774432](https://github.com/Datoms-IoT/datoms-webapp/commit/f774432b8cf8f7b6b5d6cd5cad4b1c3e47b9991d))

## [3.84.15](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.84.14...v3.84.15) (2023-10-27)

### Bug Fixes

- **aurassure:** aurassure no aqi for certain things ([464ba0d](https://github.com/Datoms-IoT/datoms-webapp/commit/464ba0d25548d107d583a05a714c0c73b11ff72a))

## [3.84.14](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.84.13...v3.84.14) (2023-10-27)

### Bug Fixes

- **report:** report avg value condition updated for fuel tank ([c66e70b](https://github.com/Datoms-IoT/datoms-webapp/commit/c66e70bc502f4460e005f9f4838feaaa5ce994c2))

## [3.84.13](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.84.12...v3.84.13) (2023-10-27)

### Bug Fixes

- **asset management:** released new asset config UI for client - Uniphos(id 887) ([1c10684](https://github.com/Datoms-IoT/datoms-webapp/commit/1c10684a7bd9b9a33f689c0104a97895c5e6fe94))

## [3.84.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.84.11...v3.84.12) (2023-10-26)

## [3.84.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.84.10...v3.84.11) (2023-10-26)

### Bug Fixes

- **report:** report changes ([2bd60f3](https://github.com/Datoms-IoT/datoms-webapp/commit/2bd60f3a3d27489386f898f9a48fd8035dceb9b2))

## [3.84.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.84.9...v3.84.10) (2023-10-26)

## [3.84.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.84.8...v3.84.9) (2023-10-26)

### Bug Fixes

- **report:** report fuel level condition added ([72b2b18](https://github.com/Datoms-IoT/datoms-webapp/commit/72b2b1870d6da9ae6960eb9a5ab08d3d4d4329ca))

## [3.84.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.84.7...v3.84.8) (2023-10-20)

### Bug Fixes

- **da report:** data availability report made feature controlled for partners ([d81bc5a](https://github.com/Datoms-IoT/datoms-webapp/commit/d81bc5ae29203027c05fee653675b13ca79f6e95))

## [3.84.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.84.6...v3.84.7) (2023-10-20)

### Bug Fixes

- **asset management:** 3rd party data transmission bug fixes and improvements ([68b4c09](https://github.com/Datoms-IoT/datoms-webapp/commit/68b4c09745c160cb550a6cb9b5ee6b13065f589a))

## [3.84.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.84.5...v3.84.6) (2023-10-19)

### Bug Fixes

- mobile app version updated ([f654a16](https://github.com/Datoms-IoT/datoms-webapp/commit/f654a166056a545fb6236ebfacec7cf716101de0))

## [3.84.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.84.4...v3.84.5) (2023-10-19)

### Bug Fixes

- **assign-device:** assign device cases handled ([d0b68d1](https://github.com/Datoms-IoT/datoms-webapp/commit/d0b68d1e69b73a56c130b77ac9e81bf8137dd1e2))
- **end customer views:** fuel level multiplier for fuel tank id: 11569 ([e00ae49](https://github.com/Datoms-IoT/datoms-webapp/commit/e00ae49afa1011575c2ddb5a512a220a6e9a6113))

## [3.84.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.84.3...v3.84.4) (2023-10-18)

### Bug Fixes

- **asset management:** corrected wrong device id for Advantech-ECU-1051 in hardware interface ([b759999](https://github.com/Datoms-IoT/datoms-webapp/commit/b7599991efd498b2c715a41dff987fc33e54a885))

## [3.84.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.84.2...v3.84.3) (2023-10-18)

### Bug Fixes

- **asset management:** changed hardware interface form for Advantech device type ([7010000](https://github.com/Datoms-IoT/datoms-webapp/commit/7010000896c4cf5f4096bcc42ce828e1ebbe1c2c))

## [3.84.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.84.1...v3.84.2) (2023-10-17)

### Bug Fixes

- **asset management:** restricted fuel calibration to (pressure & capacitive) fuel sensor & bugfixes ([99d2eee](https://github.com/Datoms-IoT/datoms-webapp/commit/99d2eee17ff85e8bd46b0404e133cccd2062a122))

## [3.84.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.84.0...v3.84.1) (2023-10-17)

### Bug Fixes

- **customers:** merge blocked for non active customers ([6c166c3](https://github.com/Datoms-IoT/datoms-webapp/commit/6c166c3cd5934160dd63c6d8b345c4297af73429))

# [3.84.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.83.1...v3.84.0) (2023-10-16)

### Features

- **customers:** customer merging feature for datoms-x ([fa0b75f](https://github.com/Datoms-IoT/datoms-webapp/commit/fa0b75f0f285b7ca9967318404c1d066769b7ba8))

## [3.83.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.83.0...v3.83.1) (2023-10-16)

### Bug Fixes

- **asset management:** asset configuration view - bug fixes and changed sl_port key to serial_port ([d82442d](https://github.com/Datoms-IoT/datoms-webapp/commit/d82442d5dc74360cbc240849857a5e6160403b51))

# [3.83.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.82.0...v3.83.0) (2023-10-13)

### Features

- **customer details:** applications view new design ([12c51de](https://github.com/Datoms-IoT/datoms-webapp/commit/12c51de66215d3bba5bc0d9e1411572a06170818))

# [3.82.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.18...v3.82.0) (2023-10-13)

### Features

- **asset management:** added asset configuration view page ([d6aec87](https://github.com/Datoms-IoT/datoms-webapp/commit/d6aec8789ea2ed343d28b3dfa5c9d8286466d229))

## [3.81.18](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.17...v3.81.18) (2023-10-13)

### Bug Fixes

- **asset management:** updated hardware interface form in advanced device config ([a6971a8](https://github.com/Datoms-IoT/datoms-webapp/commit/a6971a831958e86f1b3f53cbe18817542a174e6c))

## [3.81.17](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.16...v3.81.17) (2023-10-13)

### Bug Fixes

- **map:** google map onoad api condition added ([9833caa](https://github.com/Datoms-IoT/datoms-webapp/commit/9833caab2559e797e2c0f3d52c99da8fb9d95fb7))

## [3.81.16](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.15...v3.81.16) (2023-10-12)

### Bug Fixes

- **raw-log:** rawlog shown to aurassure vendor portal ([874c401](https://github.com/Datoms-IoT/datoms-webapp/commit/874c4014161885cc1527a5956cc41838c40f5b2f))

## [3.81.15](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.14...v3.81.15) (2023-10-12)

### Bug Fixes

- **tripview:** trip view page made feature controlled ([a6ee8ba](https://github.com/Datoms-IoT/datoms-webapp/commit/a6ee8ba7dd6fd10c1af7a22f0b32853d582c9f65))

## [3.81.14](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.13...v3.81.14) (2023-10-11)

### Bug Fixes

- **map:** map zooming as per filter ([7086395](https://github.com/Datoms-IoT/datoms-webapp/commit/708639586e2c1ee523a437bae4b0ffdf350a3442))

## [3.81.13](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.12...v3.81.13) (2023-10-10)

### Bug Fixes

- **trip view:** tagging and filter trips based on order purpose ([8e6f0f9](https://github.com/Datoms-IoT/datoms-webapp/commit/8e6f0f98b9adaa40d9ec957bf2b106e74adddc6a))

## [3.81.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.11...v3.81.12) (2023-10-10)

### Bug Fixes

- **mahindra:** mahindra mobile goto links fixed ([6623892](https://github.com/Datoms-IoT/datoms-webapp/commit/66238922e13fa55d28e9d74272bba65a26fdca1e))

## [3.81.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.10...v3.81.11) (2023-10-10)

### Bug Fixes

- **device-assignment:** device assignment application selection handled for all customer types ([1a2206d](https://github.com/Datoms-IoT/datoms-webapp/commit/1a2206db43de5d8f404f984546c99ba9324bec3a))

## [3.81.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.9...v3.81.10) (2023-10-09)

### Bug Fixes

- **report:** vioaltion report column as per type ([153d312](https://github.com/Datoms-IoT/datoms-webapp/commit/153d3121b484edc9fcae986dd5dc60aeeb2ca311))

## [3.81.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.8...v3.81.9) (2023-10-09)

### Bug Fixes

- **asset management:** fixed - asset type options are not coming for some users with limited access ([c25c03d](https://github.com/Datoms-IoT/datoms-webapp/commit/c25c03da6e992ae9e723c1fc5e0a4c4e7d4291f9))

## [3.81.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.7...v3.81.8) (2023-10-06)

### Bug Fixes

- **generic filter:** props for disabling all filter fields/buttons ([50a1127](https://github.com/Datoms-IoT/datoms-webapp/commit/50a1127eefb6d73e12a24360ab2091eda08b5323))

## [3.81.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.6...v3.81.7) (2023-10-06)

### Bug Fixes

- **device-debug:** offline trend graph data shown upto current time if current date selected ([cda7306](https://github.com/Datoms-IoT/datoms-webapp/commit/cda7306bbe9f3a502791158b449633a3f0996351))

## [3.81.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.5...v3.81.6) (2023-10-06)

### Bug Fixes

- **asset management:** asset configuration page mobile version improvements ([400dd0b](https://github.com/Datoms-IoT/datoms-webapp/commit/400dd0bfb6864ed25041edd43ba5903958e0f02b))

## [3.81.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.4...v3.81.5) (2023-10-06)

### Bug Fixes

- **asset management:** fixed - rented to option is not working in new configuration page ([ab7b35f](https://github.com/Datoms-IoT/datoms-webapp/commit/ab7b35f3b644f4569b2255322bec5147201201d7))

## [3.81.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.3...v3.81.4) (2023-10-06)

### Bug Fixes

- **detailed view:** flow meter graph fix ([d29e5d9](https://github.com/Datoms-IoT/datoms-webapp/commit/d29e5d93d6e4918cfa69823b99443dba7d30899b))

## [3.81.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.2...v3.81.3) (2023-10-05)

### Bug Fixes

- **calibration:** calibration 0 value coming null fixed ([5508668](https://github.com/Datoms-IoT/datoms-webapp/commit/5508668cc866170798fa819ecaa71a840ef4990a))

## [3.81.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.1...v3.81.2) (2023-10-05)

### Bug Fixes

- **trip view:** added expected fuel consumption and expected fuel consumption per hour ([a05ac59](https://github.com/Datoms-IoT/datoms-webapp/commit/a05ac59f95608551847f36089218fdf7ed814d5c))

## [3.81.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.81.0...v3.81.1) (2023-10-04)

### Bug Fixes

- **data availability report:** end date for this month range fixed ([16c11f5](https://github.com/Datoms-IoT/datoms-webapp/commit/16c11f5b98d79769ed54a5a1cf3c9d06bb2197ee))

# [3.81.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.80.0...v3.81.0) (2023-10-04)

### Features

- **trip view:** fuel fill & drain columns, row highlight for filled, theft ([1f03895](https://github.com/Datoms-IoT/datoms-webapp/commit/1f03895c307c784ffc6c4dbab2f1683df76f19f5))

# [3.80.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.79.6...v3.80.0) (2023-10-04)

### Features

- **schedule reports:** schedule feature added for Data Availability Report ([3a0ca4a](https://github.com/Datoms-IoT/datoms-webapp/commit/3a0ca4a441663cea2f65e47f724cd695f6624cd5))

## [3.79.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.79.5...v3.79.6) (2023-10-04)

### Bug Fixes

- **dc availability report:** dc energy meter availability report ([3a1e559](https://github.com/Datoms-IoT/datoms-webapp/commit/3a1e559becb7e4f47b80d414240038314e9ee3eb))

## [3.79.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.79.4...v3.79.5) (2023-10-04)

### Bug Fixes

- **asset management:** fixed - system template showing undefined for fuel_sensor_name "0" ([a4506ba](https://github.com/Datoms-IoT/datoms-webapp/commit/a4506ba348edd2c1dd16b672d38e2cd000414292))

## [3.79.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.79.3...v3.79.4) (2023-10-04)

### Bug Fixes

- **violation report:** violation report repeated data fixed ([58dfaea](https://github.com/Datoms-IoT/datoms-webapp/commit/58dfaea8fff45f3311052e5557eaeb6c6d46c2b7))

## [3.79.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.79.2...v3.79.3) (2023-10-04)

### Bug Fixes

- **pomo:** pomo avg graph limit fixed ([70fbcf0](https://github.com/Datoms-IoT/datoms-webapp/commit/70fbcf064e341a353f334f9655512b2b083c9733))

## [3.79.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.79.1...v3.79.2) (2023-09-30)

### Bug Fixes

- **generic:** avl percentage should not greater than 100 for dc energy meter ([c078fc5](https://github.com/Datoms-IoT/datoms-webapp/commit/c078fc5268224687057f0651f1a4f6b504d99fb2))

## [3.79.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.79.0...v3.79.1) (2023-09-29)

### Bug Fixes

- **generic:** availability added to dc energy meter ([a60007f](https://github.com/Datoms-IoT/datoms-webapp/commit/a60007f5e289bc43bb050f5d794611437b7312dd))

# [3.79.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.78.3...v3.79.0) (2023-09-29)

### Features

- **customers:** customer activation feature ([10f7958](https://github.com/Datoms-IoT/datoms-webapp/commit/10f79584c228ffb5eef550db2249cbbf6438da86))
- **customers:** reactivation of deactivated and archived customers ([061b177](https://github.com/Datoms-IoT/datoms-webapp/commit/061b17785f4c2d95f2cf2077cfc3e9a6eb789086))

## [3.78.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.78.2...v3.78.3) (2023-09-29)

### Bug Fixes

- **customreports:** custom reports 15 min aggr time fixed ([fc98e2b](https://github.com/Datoms-IoT/datoms-webapp/commit/fc98e2bbbe7654aad714754d42df7415f0519458))

## [3.78.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.78.1...v3.78.2) (2023-09-29)

### Bug Fixes

- **asset management:** fixed - parameters are not showing for custom template in device config ([4c41417](https://github.com/Datoms-IoT/datoms-webapp/commit/4c41417fc4681e90f34d5b6b17521ff11664355d))

## [3.78.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.78.0...v3.78.1) (2023-09-28)

### Bug Fixes

- **asset management:** fixed - advanced device config not opening in asset addition ([df1adaf](https://github.com/Datoms-IoT/datoms-webapp/commit/df1adaf458c77fe4dce7471877d3401c27d52d96))

# [3.78.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.77.8...v3.78.0) (2023-09-28)

### Features

- **customers:** customer status filter added ([da71f42](https://github.com/Datoms-IoT/datoms-webapp/commit/da71f42f8fb4a4b3b3551144fd6e147a52c1daea))

## [3.77.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.77.7...v3.77.8) (2023-09-28)

### Bug Fixes

- **reports:** fuel fill drain report scheduling download ([461db92](https://github.com/Datoms-IoT/datoms-webapp/commit/461db925c407167c175ab715944cc13a60775345))

## [3.77.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.77.6...v3.77.7) (2023-09-28)

### Bug Fixes

- **customers:** retain selected filters after customer deactivation/archive ([15fd069](https://github.com/Datoms-IoT/datoms-webapp/commit/15fd069d5fd9ce16daaf6c4d5de865afce71bbce))

## [3.77.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.77.5...v3.77.6) (2023-09-27)

### Bug Fixes

- **reports:** reports download filename extension added for scheduling ([c96470c](https://github.com/Datoms-IoT/datoms-webapp/commit/c96470cf3929f9ca09a06b0d31f8495ba38d2d11))

## [3.77.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.77.4...v3.77.5) (2023-09-27)

### Bug Fixes

- **reports:** 5 years time duration enabled for cems, eqms, aaqms ([49da651](https://github.com/Datoms-IoT/datoms-webapp/commit/49da6510ad73b92898d3adc572162612df368dd2))

## [3.77.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.77.3...v3.77.4) (2023-09-27)

### Bug Fixes

- **reports:** data availability report additional columns added ([dcd5742](https://github.com/Datoms-IoT/datoms-webapp/commit/dcd574206104feadf37e0899acdd78b091ceb407))

## [3.77.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.77.2...v3.77.3) (2023-09-27)

### Bug Fixes

- **asset management:** removed dashboard opening restriction for some categories and bug fixes ([7984151](https://github.com/Datoms-IoT/datoms-webapp/commit/798415153b5950d4c2d934f0b00c3b4a62f8504e))

## [3.77.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.77.1...v3.77.2) (2023-09-26)

### Bug Fixes

- **schedulereport:** schedule report fixes for new api ([a8db451](https://github.com/Datoms-IoT/datoms-webapp/commit/a8db45187aae83dab18d95e9f02067e03672528e))

## [3.77.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.77.0...v3.77.1) (2023-09-25)

### Bug Fixes

- **mixpanel:** new user properties sent and asset type sent to event when asset added or configured ([2bb1db2](https://github.com/Datoms-IoT/datoms-webapp/commit/2bb1db22697731f0a3bce9d64251a08e3bb5fe14))

# [3.77.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.76.0...v3.77.0) (2023-09-25)

### Features

- **asset management:** added rental asset addition from datoms-x ([f1988af](https://github.com/Datoms-IoT/datoms-webapp/commit/f1988af208b3f5cdcec95627354f03398707a4df))

# [3.76.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.75.5...v3.76.0) (2023-09-22)

### Features

- **partner reports:** data availability report in DatomsX ([3068a84](https://github.com/Datoms-IoT/datoms-webapp/commit/3068a84f8b6b2f0a29a87f88fa6e68444dc3743c))

## [3.75.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.75.4...v3.75.5) (2023-09-22)

### Bug Fixes

- **asset management:** fuel sensor onboarding bug fixes and improvements ([c146984](https://github.com/Datoms-IoT/datoms-webapp/commit/c1469847c8f354c0caaefce3d9ed44a11601fa83))

## [3.75.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.75.3...v3.75.4) (2023-09-21)

### Bug Fixes

- **trip-view:** graph going empty on page no/page size change - fixed ([b68972c](https://github.com/Datoms-IoT/datoms-webapp/commit/b68972c666808241cdd4dd517c2584e502c3783a))

## [3.75.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.75.2...v3.75.3) (2023-09-21)

### Bug Fixes

- **mixpanel-event:** track events added for thing add open and thing add ([d6c75df](https://github.com/Datoms-IoT/datoms-webapp/commit/d6c75df952fd9d2932ac39e7d61be7ea29008891))

## [3.75.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.75.1...v3.75.2) (2023-09-21)

### Bug Fixes

- **detailed view:** genset status parameters added ([07b22dd](https://github.com/Datoms-IoT/datoms-webapp/commit/07b22ddb642b972971d5ecc68e193b2160907f82))

## [3.75.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.75.0...v3.75.1) (2023-09-20)

### Bug Fixes

- **report disabled date fixed:** report disabled date fixed ([7fac232](https://github.com/Datoms-IoT/datoms-webapp/commit/7fac2327b150f1ddde35894daeb5ec3514582d46))

# [3.75.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.74.5...v3.75.0) (2023-09-20)

### Features

- **asset management:** on boarded fuel sensors as system templates ([56b2f45](https://github.com/Datoms-IoT/datoms-webapp/commit/56b2f45469738689462ebb66d06ae3fb02446b26))

## [3.74.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.74.4...v3.74.5) (2023-09-20)

### Bug Fixes

- **trip view:** enabled trip view for kalyani motors ([5d3861f](https://github.com/Datoms-IoT/datoms-webapp/commit/5d3861fb3d5f58ddb584ad71fabe9d441e36d9fd))

## [3.74.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.74.3...v3.74.4) (2023-09-19)

### Bug Fixes

- **asset management:** removed default "data_type" value for pollution categories ([c283c42](https://github.com/Datoms-IoT/datoms-webapp/commit/c283c42a6e8acb71cc9fc87e4638f2b4bdaee202))

## [3.74.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.74.2...v3.74.3) (2023-09-19)

### Bug Fixes

- **mix-panel:** mix panel implementation updated ([74f2b6c](https://github.com/Datoms-IoT/datoms-webapp/commit/74f2b6c1d763fa39e251b749dddc7433f52f3315))

## [3.74.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.74.1...v3.74.2) (2023-09-18)

### Bug Fixes

- **asset management:** fixed - configuration page is rendered before loading configuration ([c7d6f5c](https://github.com/Datoms-IoT/datoms-webapp/commit/c7d6f5c07146361c84cac4c8a7719cc764266735))

## [3.74.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.74.0...v3.74.1) (2023-09-18)

### Bug Fixes

- **asset management:** fixed - api calling issue in iot-platform after asset type restriction ([cf0aab2](https://github.com/Datoms-IoT/datoms-webapp/commit/cf0aab22638de317579d64a97179c53c46d411f9))

# [3.74.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.73.8...v3.74.0) (2023-09-15)

### Features

- **customers:** allowed thing category ids for a customer ([467046a](https://github.com/Datoms-IoT/datoms-webapp/commit/467046a0a33c0fbd28b4116142798195791c4515))

## [3.73.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.73.7...v3.73.8) (2023-09-15)

### Bug Fixes

- **car reort:** car report avg data NaN fixed ([f9ffcde](https://github.com/Datoms-IoT/datoms-webapp/commit/f9ffcdebc3f16391a61e0dfab60fe74300d66107))

## [3.73.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.73.6...v3.73.7) (2023-09-14)

### Bug Fixes

- **subscription management:** fixed - Subscription status is hidden in some cases ([49b8fec](https://github.com/Datoms-IoT/datoms-webapp/commit/49b8feccd17dd92ed632e84dbdcd3431b753870e))

## [3.73.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.73.5...v3.73.6) (2023-09-14)

### Bug Fixes

- **asset management:** blocked access dashboard button in asset list for assets with no device ([093cde1](https://github.com/Datoms-IoT/datoms-webapp/commit/093cde139263f6edc3ca031f58fce22391c68a00))

## [3.73.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.73.4...v3.73.5) (2023-09-13)

### Bug Fixes

- **generic:** car list status as per map ([34ae52f](https://github.com/Datoms-IoT/datoms-webapp/commit/34ae52ffaacc18f28a72d306f48268cc8994df54))

## [3.73.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.73.3...v3.73.4) (2023-09-13)

### Bug Fixes

- **subscription managament:** fixed price and auto reneeal bug fixes and improvements ([e3722f5](https://github.com/Datoms-IoT/datoms-webapp/commit/e3722f57b213cb4e6e0bf49e9cf2759196e30e6b))
- **subscription management:** subscription fixed price bug fixes and improvements ([ad503c0](https://github.com/Datoms-IoT/datoms-webapp/commit/ad503c0ed8d4cd333bfbd9d0dae0dd843e6515ac))

## [3.73.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.73.2...v3.73.3) (2023-09-12)

### Bug Fixes

- **asset management:** added default system template for car thing type and location bug fixes ([7614037](https://github.com/Datoms-IoT/datoms-webapp/commit/7614037a2cbf190b0e65d119da0335c46ec60635))

## [3.73.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.73.1...v3.73.2) (2023-09-11)

### Bug Fixes

- **rc:** rc changes ([e432284](https://github.com/Datoms-IoT/datoms-webapp/commit/e432284d9c3b5f0eda6484b929359c7146b8321a))

## [3.73.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.73.0...v3.73.1) (2023-09-11)

### Bug Fixes

- **asset management:** fixed - asset configuration error on vendor change ([686d876](https://github.com/Datoms-IoT/datoms-webapp/commit/686d8765a85a8749c92f4efa2eef50c2ef263117))

# [3.73.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.72.2...v3.73.0) (2023-09-08)

### Features

- **subscription management:** fixed price column and adding subscription from template & free-trial ([c4008e6](https://github.com/Datoms-IoT/datoms-webapp/commit/c4008e6b99877a0e5934c39ccdea437201c72912))

## [3.72.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.72.1...v3.72.2) (2023-09-08)

### Bug Fixes

- **asset management:** added missing data handling values in asset configuration ([781339f](https://github.com/Datoms-IoT/datoms-webapp/commit/781339f37128a465a1290f9ff4c93df3568a71ce))

## [3.72.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.72.0...v3.72.1) (2023-09-08)

### Bug Fixes

- **rc:** rc changes ([5acb44f](https://github.com/Datoms-IoT/datoms-webapp/commit/5acb44ff75469b401819800ab76d3d0a3f33c6f8))

# [3.72.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.71.1...v3.72.0) (2023-09-07)

### Features

- **customer management:** deactivate and archive of customers ([89481ac](https://github.com/Datoms-IoT/datoms-webapp/commit/89481ac4fdcd3cc13d54deef561fbbf8db14019d))

## [3.71.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.71.0...v3.71.1) (2023-09-07)

### Bug Fixes

- **fuel filled and drain report:** fuel filled and drain report event api call fix ([2d8be1c](https://github.com/Datoms-IoT/datoms-webapp/commit/2d8be1ca390659ae560208ce64ad560c198b93b3))

# [3.71.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.70.0...v3.71.0) (2023-09-07)

### Features

- **mobile app:** added android 13 support ([c018a9c](https://github.com/Datoms-IoT/datoms-webapp/commit/c018a9cd3e5261ebe88874207b8e18f093fe2bb7))

# [3.70.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.69.5...v3.70.0) (2023-09-06)

### Bug Fixes

- **asset management:** bugfixes and improvements ([e6a4493](https://github.com/Datoms-IoT/datoms-webapp/commit/e6a449338b7bd28575aa9dfe565912ba2677d03f))

### Features

- **asset management:** added Device QR scan ([bb47204](https://github.com/Datoms-IoT/datoms-webapp/commit/bb47204f4c53a623d2e6d5d424ebe98d120c4f34))

## [3.69.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.69.4...v3.69.5) (2023-09-05)

### Bug Fixes

- **rc:** rc change ([de1686f](https://github.com/Datoms-IoT/datoms-webapp/commit/de1686fdf5cff1670ff75b9c649a42fb145a1a1f))

## [3.69.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.69.3...v3.69.4) (2023-09-04)

### Bug Fixes

- **mix-panel:** mix panel init statement corrected ([8f89b07](https://github.com/Datoms-IoT/datoms-webapp/commit/8f89b07b2ba5e208c7dc0a51a2e885550afd6729))

## [3.69.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.69.2...v3.69.3) (2023-09-04)

### Bug Fixes

- **asset management:** added wrd details for flow meter in asset configuration ([9780a41](https://github.com/Datoms-IoT/datoms-webapp/commit/9780a413bcc6c7009d5e4268b7e14939fd7b1839))

## [3.69.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.69.1...v3.69.2) (2023-09-04)

### Bug Fixes

- **build-script:** production build script updated ([859d5b3](https://github.com/Datoms-IoT/datoms-webapp/commit/859d5b3ba697f6c30e4b635f190a973403477605))

## [3.69.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.69.0...v3.69.1) (2023-09-04)

# [3.69.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.18...v3.69.0) (2023-09-01)

### Features

- **schedule report:** schedule report implemented ([4bc72a0](https://github.com/Datoms-IoT/datoms-webapp/commit/4bc72a09e0a4c9481443ea17334076e33a93b36d))

## [3.68.18](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.17...v3.68.18) (2023-08-31)

### Bug Fixes

- **mobile app:** reverted android 13 changes ([be4ede8](https://github.com/Datoms-IoT/datoms-webapp/commit/be4ede8ba73ed65578948fc0c261545bee141b1f))

## [3.68.17](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.16...v3.68.17) (2023-08-31)

### Bug Fixes

- **mobile app:** updated mobile app to android 13 ([28d52d4](https://github.com/Datoms-IoT/datoms-webapp/commit/28d52d47154b43ee5dbb2d094ced3a76632aa30f))

## [3.68.16](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.15...v3.68.16) (2023-08-30)

### Bug Fixes

- **alerts:** category 86 exception added for offline alerts in users ([3912c10](https://github.com/Datoms-IoT/datoms-webapp/commit/3912c1014d21123d85e1f44481fecd6434db4d51))

## [3.68.15](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.14...v3.68.15) (2023-08-30)

### Bug Fixes

- **alerts:** category id 86 added to exception for offline alerts in end customer ([8e69748](https://github.com/Datoms-IoT/datoms-webapp/commit/8e6974859f0649d506c82b98abd12272e55edc59))

## [3.68.14](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.13...v3.68.14) (2023-08-29)

### Bug Fixes

- **notifications:** mobile app client id issue fixed ([e4def49](https://github.com/Datoms-IoT/datoms-webapp/commit/e4def49049a899fc1da3c71967529ba95267048d))

## [3.68.13](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.12...v3.68.13) (2023-08-28)

### Bug Fixes

- **notifications:** notification page optimized ([453dddc](https://github.com/Datoms-IoT/datoms-webapp/commit/453dddc84240f24c107855ed1bb7a0c76b884eb3))

## [3.68.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.11...v3.68.12) (2023-08-25)

### Bug Fixes

- **violationsummary:** violation type added to violation summary report ([a0f1d8d](https://github.com/Datoms-IoT/datoms-webapp/commit/a0f1d8d0b8648c5c6565f510330a4327e39b96d2))

## [3.68.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.10...v3.68.11) (2023-08-25)

### Bug Fixes

- **asset management:** added dependent params for fuel voltage and fuel height ([cdd8807](https://github.com/Datoms-IoT/datoms-webapp/commit/cdd88074c48c1cab73873a4132cb86730dcc75f9))

## [3.68.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.9...v3.68.10) (2023-08-24)

### Bug Fixes

- **asset management:** fixed - showing wrong thing list in customer management ([07c0129](https://github.com/Datoms-IoT/datoms-webapp/commit/07c01296e9388c95c44df5f42e60cc668bf85800))

## [3.68.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.8...v3.68.9) (2023-08-24)

### Bug Fixes

- **dds:** dDS api integration ([284d3a7](https://github.com/Datoms-IoT/datoms-webapp/commit/284d3a7ef67fddce4dbaef3c3dcb65390efeca4e))

## [3.68.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.7...v3.68.8) (2023-08-23)

### Bug Fixes

- **asset management:** changed data availability label in asset list table ([66ae25e](https://github.com/Datoms-IoT/datoms-webapp/commit/66ae25e1cbf175108c1d5b38fa450d17d3f3e26a))

## [3.68.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.6...v3.68.7) (2023-08-23)

### Bug Fixes

- **asset management:** bug fixes and improvements ([40d43ef](https://github.com/Datoms-IoT/datoms-webapp/commit/40d43efc384b9f356923cb420d76b6364ac77286))

## [3.68.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.5...v3.68.6) (2023-08-22)

### Bug Fixes

- **asset management:** fixed - thing list loading error and moved asset info below basic (quick add) ([c2fee37](https://github.com/Datoms-IoT/datoms-webapp/commit/c2fee374d62cc0c13093fdd4f06e765fb37dcaaf))

## [3.68.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.4...v3.68.5) (2023-08-22)

### Bug Fixes

- **asset management:** asset configuration optimisation ([a47710f](https://github.com/Datoms-IoT/datoms-webapp/commit/a47710fb5f9431c2d0131a4f65fed3826bd9c9af))

## [3.68.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.3...v3.68.4) (2023-08-21)

### Bug Fixes

- **asset management:** added Nm3/h unit in paramter settings ([366c5e6](https://github.com/Datoms-IoT/datoms-webapp/commit/366c5e67042377795c0bfbc2db07e2734f2a0405))

## [3.68.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.2...v3.68.3) (2023-08-21)

### Bug Fixes

- **calibration:** calibration filter options changed ([e8ca620](https://github.com/Datoms-IoT/datoms-webapp/commit/e8ca620094cb55522190cfc94ce807539296bf5b))

## [3.68.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.1...v3.68.2) (2023-08-21)

### Bug Fixes

- **rc:** cylinder number key change for rc ([b4925e3](https://github.com/Datoms-IoT/datoms-webapp/commit/b4925e32f76a7e9f16fd7ed6a16625654796ef62))

## [3.68.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.68.0...v3.68.1) (2023-08-21)

### Bug Fixes

- **thing management:** added parallel api loading in thing list ([239447c](https://github.com/Datoms-IoT/datoms-webapp/commit/239447c12261f1d96b7ac74c31b61b4c20c32bd0))

# [3.68.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.18...v3.68.0) (2023-08-20)

### Features

- **remotecalibration:** remote calibration implemented ([010e80e](https://github.com/Datoms-IoT/datoms-webapp/commit/010e80ed01ed8e3f21c73016016fccfbe06dd5e2))

## [3.67.18](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.17...v3.67.18) (2023-08-18)

### Bug Fixes

- **asset management:** fixed - clinder duration is wrong in calibration settings ([396d25e](https://github.com/Datoms-IoT/datoms-webapp/commit/396d25e62aa5d1d187b4b95b230e5b87599537c3))

## [3.67.17](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.16...v3.67.17) (2023-08-18)

### Bug Fixes

- **asset management:** added remote control to old thing configuration ([cbae483](https://github.com/Datoms-IoT/datoms-webapp/commit/cbae483536a3481eb36ee536de3065a2e4110097))

## [3.67.16](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.15...v3.67.16) (2023-08-18)

### Bug Fixes

- **asset management:** added advanced device config for dg with system template ([9266827](https://github.com/Datoms-IoT/datoms-webapp/commit/92668279afa20b51d576962007baa5560fa7c5ad))

## [3.67.15](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.14...v3.67.15) (2023-08-18)

### Bug Fixes

- **customer list api:** changes for optimization of customer list api ([fa64df3](https://github.com/Datoms-IoT/datoms-webapp/commit/fa64df344ec52849410d9990d0172e7b8d556bdb))

## [3.67.14](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.13...v3.67.14) (2023-08-17)

### Bug Fixes

- **custom report:** customize report cancel go back ([d3612c7](https://github.com/Datoms-IoT/datoms-webapp/commit/d3612c76b0e7259ee1811751a8fbe607ccc3bd7b))

## [3.67.13](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.12...v3.67.13) (2023-08-17)

### Bug Fixes

- **subscription management:** fixed - invalid billing cycle error in free-trial ([bd58c26](https://github.com/Datoms-IoT/datoms-webapp/commit/bd58c26503cb95b203144defbcb2a164639d6694))

## [3.67.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.11...v3.67.12) (2023-08-16)

### Bug Fixes

- **subscription management:** added decimal support in subscription price ([1093cb6](https://github.com/Datoms-IoT/datoms-webapp/commit/1093cb647ece589d74ccb96d7e9c84dd06d5959e))

## [3.67.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.10...v3.67.11) (2023-08-16)

### Bug Fixes

- **asset management:** fixed - dervied params are added twice when device is removed and added again ([3f9478c](https://github.com/Datoms-IoT/datoms-webapp/commit/3f9478c16e217b03c4b6a680104b3355a5517a50))

## [3.67.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.9...v3.67.10) (2023-08-16)

### Bug Fixes

- **asset management:** added custom system configuration option for DG ([9becea0](https://github.com/Datoms-IoT/datoms-webapp/commit/9becea032315a6634f63fa5da04672ee596398eb))

## [3.67.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.8...v3.67.9) (2023-08-14)

### Bug Fixes

- **asset management:** added new fuel sensor options ([8269e31](https://github.com/Datoms-IoT/datoms-webapp/commit/8269e31b67ad5c3c7df3062b2fc02fc44456e054))

## [3.67.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.7...v3.67.8) (2023-08-14)

### Bug Fixes

- **asset management:** added new protocols and system templates ([5a96f69](https://github.com/Datoms-IoT/datoms-webapp/commit/5a96f69e3219f06a5fa8553fa714cb384e0f8ecd))

## [3.67.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.6...v3.67.7) (2023-08-14)

### Bug Fixes

- **alerts:** maintenance alerts bug fix ([0e5a81a](https://github.com/Datoms-IoT/datoms-webapp/commit/0e5a81a82fbc0e11af795211beea5d32b5ecf7ec))

## [3.67.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.5...v3.67.6) (2023-08-11)

### Bug Fixes

- **asset management:** fixed - handled customer name field width for longer customer names ([5288638](https://github.com/Datoms-IoT/datoms-webapp/commit/5288638370db7e20eb00df7410c0602116119093))

## [3.67.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.4...v3.67.5) (2023-08-11)

### Bug Fixes

- **iot views:** detailed view infinite loading issue fixed ([c868dc8](https://github.com/Datoms-IoT/datoms-webapp/commit/c868dc8ada70f84085df5617e1ad61e3761e4625))

## [3.67.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.3...v3.67.4) (2023-08-11)

### Bug Fixes

- **asset management:** added partner name in quick add and pollution monitoring asset bug fixes ([d39db65](https://github.com/Datoms-IoT/datoms-webapp/commit/d39db653185048054489391121f5f7f02e2ca969))

## [3.67.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.2...v3.67.3) (2023-08-11)

### Bug Fixes

- **subscription management:** added user and partner icon for payee in subscription addition ([8b767d5](https://github.com/Datoms-IoT/datoms-webapp/commit/8b767d533d215e9660621e0db48195fe16cfa241))

## [3.67.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.1...v3.67.2) (2023-08-10)

### Bug Fixes

- **asset management:** asset management v2 - bug fixes ([0fe04e7](https://github.com/Datoms-IoT/datoms-webapp/commit/0fe04e7dcffc7db180f41610db50e427d73f4edc))

## [3.67.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.67.0...v3.67.1) (2023-08-10)

### Bug Fixes

- **asset management:** asset management v2 - bug fixes and improvements ([8fd4dbe](https://github.com/Datoms-IoT/datoms-webapp/commit/8fd4dbe7dc155749711aeac80d93c1bf0ba8cd27))

# [3.67.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.66.3...v3.67.0) (2023-08-09)

### Features

- **order details:** test drive report ([c5e0fb4](https://github.com/Datoms-IoT/datoms-webapp/commit/c5e0fb482633032a5105b10455ec13d9f398832b))

## [3.66.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.66.2...v3.66.3) (2023-08-09)

### Bug Fixes

- **asset management:** added param settings to enviromnent monitoring and remote control section ([055be2f](https://github.com/Datoms-IoT/datoms-webapp/commit/055be2f389228fb36f976d8757120d72c51c93e5))

## [3.66.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.66.1...v3.66.2) (2023-08-09)

### Bug Fixes

- **asset management:** added parameter settings for flow meter anf bug fixes ([a093ebe](https://github.com/Datoms-IoT/datoms-webapp/commit/a093ebedc54fb6ececaf0c098d958a61265f710e))

## [3.66.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.66.0...v3.66.1) (2023-08-08)

### Bug Fixes

- **asset management:** added socket implementation in things v2 ([a6b552f](https://github.com/Datoms-IoT/datoms-webapp/commit/a6b552fb137444d591be2e9d74a2ece091544957))

# [3.66.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.65.4...v3.66.0) (2023-08-08)

### Features

- **asset management:** deployment - Things V2 for Datoms-x users ([b523e10](https://github.com/Datoms-IoT/datoms-webapp/commit/b523e1018125eaeb68bfc60920ff73d628db2e23))

## [3.65.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.65.3...v3.65.4) (2023-08-08)

### Bug Fixes

- **thing management:** thing management v2- bug fixes and improvements ([f3f3a94](https://github.com/Datoms-IoT/datoms-webapp/commit/f3f3a94823c38b912e4026ccceefcfa59407e9a2))

## [3.65.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.65.2...v3.65.3) (2023-08-07)

### Bug Fixes

- **billing management:** fixed - Sorting is not working in partner templates ([ec98304](https://github.com/Datoms-IoT/datoms-webapp/commit/ec98304f2a72e957000be00957a03ebe94a911d1))

## [3.65.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.65.1...v3.65.2) (2023-08-07)

### Bug Fixes

- **subscription management:** fixed - price per thing column not showing ([94ac5ea](https://github.com/Datoms-IoT/datoms-webapp/commit/94ac5ea528e6c8a69ca1024f43662db7616c9b41))

## [3.65.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.65.0...v3.65.1) (2023-08-04)

### Bug Fixes

- **subscsription management:** subscription management table bug fixes and improvements ([d74d9a8](https://github.com/Datoms-IoT/datoms-webapp/commit/d74d9a8b45b7bd39a397b4dc4db9191c70c9f05a))

# [3.65.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.64.1...v3.65.0) (2023-08-03)

### Features

- **subscription management:** added customise table, filters and sorting from backend ([dbf362b](https://github.com/Datoms-IoT/datoms-webapp/commit/dbf362be69ea662f4dcc6f076b284c6d2a7124b1))

## [3.64.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.64.0...v3.64.1) (2023-08-02)

### Bug Fixes

- **dg realtime:** dG real time current limit changed for OJUS ([49c91d7](https://github.com/Datoms-IoT/datoms-webapp/commit/49c91d75be8299de42338a5d4b74928218052370))

# [3.64.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.22...v3.64.0) (2023-08-01)

### Features

- **help-link:** help link added for end customer ([3914300](https://github.com/Datoms-IoT/datoms-webapp/commit/391430028ca9669b82d27a113ec8c1b7a5cc9536))

## [3.63.22](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.21...v3.63.22) (2023-07-28)

### Bug Fixes

- **trip-view:** date picker issue fixed ([87ab7fc](https://github.com/Datoms-IoT/datoms-webapp/commit/87ab7fca1255fdea8e4125fdf096fdbe0501754f))

## [3.63.21](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.20...v3.63.21) (2023-07-28)

### Bug Fixes

- **trip-view:** summary calculations and advanced filters added ([3b6e4bb](https://github.com/Datoms-IoT/datoms-webapp/commit/3b6e4bb02a1e540820fd05a5757a306ef3268844))

## [3.63.20](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.19...v3.63.20) (2023-07-27)

### Bug Fixes

- **thing management:** fixed - scrolling issues in thing management v2 ([804a7e6](https://github.com/Datoms-IoT/datoms-webapp/commit/804a7e602cd8fc33de4a9d11d496ee0232840416))

## [3.63.19](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.18...v3.63.19) (2023-07-27)

### Bug Fixes

- **generic:** raw graph threshold updated ([5675ccd](https://github.com/Datoms-IoT/datoms-webapp/commit/5675ccdeb2e586ed22060a8460d78e15fd3bb0ab))

## [3.63.18](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.17...v3.63.18) (2023-07-26)

### Bug Fixes

- **thing management:** thing management v2 - mobile - bug fixes and improvements ([f1848c4](https://github.com/Datoms-IoT/datoms-webapp/commit/f1848c4a6093b34900fd32f9c3d4309a7299165b))

## [3.63.17](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.16...v3.63.17) (2023-07-26)

### Bug Fixes

- **report:** report rows with no data removed ([872c6b7](https://github.com/Datoms-IoT/datoms-webapp/commit/872c6b780e5dddef3dbcbfffd036a6d9f57adf14))

## [3.63.16](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.15...v3.63.16) (2023-07-25)

### Bug Fixes

- **layout:** page contain height fix ([b1dd5a3](https://github.com/Datoms-IoT/datoms-webapp/commit/b1dd5a3c500a0cdbf29f855b51511c529eacf0c6))

## [3.63.15](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.14...v3.63.15) (2023-07-25)

### Bug Fixes

- **generic template:** pollution monitoring bug fix ([cd5e678](https://github.com/Datoms-IoT/datoms-webapp/commit/cd5e678436626283ec1de10b522e435c9c9f7014))

## [3.63.14](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.13...v3.63.14) (2023-07-25)

### Bug Fixes

- **thing management:** thing management v2 - bug fixes and improvements ([9aca701](https://github.com/Datoms-IoT/datoms-webapp/commit/9aca7019d2d5b893afc970f0deabe24e194f4f18))

## [3.63.13](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.12...v3.63.13) (2023-07-24)

### Bug Fixes

- **thing management:** thing management v2 - bug fixes and improvements ([5a4af3b](https://github.com/Datoms-IoT/datoms-webapp/commit/5a4af3b4245e408fa7cbc57a984a587d8ac1b959))

## [3.63.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.11...v3.63.12) (2023-07-24)

### Bug Fixes

- **report:** report column name fixed for small number of columns ([5234de0](https://github.com/Datoms-IoT/datoms-webapp/commit/5234de0667c0b2be8ea1a40001f3665c8a206d12))

## [3.63.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.10...v3.63.11) (2023-07-21)

### Bug Fixes

- **report:** report pdf design fixes ([922f1d8](https://github.com/Datoms-IoT/datoms-webapp/commit/922f1d85abf9c02b89c058b00aa0e5f743c48573))

## [3.63.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.9...v3.63.10) (2023-07-21)

### Bug Fixes

- **thing management:** bug fixes and improvements for new thing config - development testing ([493aa42](https://github.com/Datoms-IoT/datoms-webapp/commit/493aa42b9e06abbac7a87c67ff844b6992f865b8))

## [3.63.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.8...v3.63.9) (2023-07-20)

### Bug Fixes

- **report pdf design fix:** report pdf design fix ([74c56ba](https://github.com/Datoms-IoT/datoms-webapp/commit/74c56ba7bbcafe4a5fb2c58d21ced664bc1327ea))

## [3.63.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.7...v3.63.8) (2023-07-20)

### Bug Fixes

- **thing management:** bug fixes and improvements ([87ef431](https://github.com/Datoms-IoT/datoms-webapp/commit/87ef4310e0c637d10d24510558199d6bf2672d3b))

## [3.63.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.6...v3.63.7) (2023-07-19)

### Bug Fixes

- **report:** report fixes ([951a29a](https://github.com/Datoms-IoT/datoms-webapp/commit/951a29aa0defc1fda27e4bc19255f1a33d5d0426))

## [3.63.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.5...v3.63.6) (2023-07-19)

### Bug Fixes

- **thing management:** added device config new UI and bug fixes ([7af5e99](https://github.com/Datoms-IoT/datoms-webapp/commit/7af5e99d64385c0e747daab32f99b1a1eed88a60))

## [3.63.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.4...v3.63.5) (2023-07-18)

### Bug Fixes

- **thing management:** added new protocols in thing configuration ([0fa5622](https://github.com/Datoms-IoT/datoms-webapp/commit/0fa5622a1f787aefa81b8ed5275bb9c5c5043060))

## [3.63.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.3...v3.63.4) (2023-07-18)

### Bug Fixes

- **trip-view:** table & filters - style and functionality fixes, max time limit increased to 2 years ([68d908c](https://github.com/Datoms-IoT/datoms-webapp/commit/68d908cfc721ac759ecf54af56def88494adfe27))

## [3.63.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.2...v3.63.3) (2023-07-18)

## [3.63.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.1...v3.63.2) (2023-07-17)

## [3.63.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.63.0...v3.63.1) (2023-07-17)

### Bug Fixes

- **subscription management:** added currency selection in subscription management ([86597c4](https://github.com/Datoms-IoT/datoms-webapp/commit/86597c42047f2b078b8cf4c503452c7ba1489933))

# [3.63.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.62.4...v3.63.0) (2023-07-17)

### Features

- **trip-view:** trip view in datomsx & api pagination, filters, customizable table in trip view ([93d9768](https://github.com/Datoms-IoT/datoms-webapp/commit/93d97685e94a3b6500683e14945007436e43b3a8))

## [3.62.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.62.3...v3.62.4) (2023-07-14)

### Bug Fixes

- **thing management:** thing management v2 bug fixes and improvements ([662725a](https://github.com/Datoms-IoT/datoms-webapp/commit/662725ac028bb50feeefd85d8cc294580bcce455))

## [3.62.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.62.2...v3.62.3) (2023-07-14)

### Bug Fixes

- **dg:** fixed issue with daily summary report ([3ecb55e](https://github.com/Datoms-IoT/datoms-webapp/commit/********************7e135af09619abfd1125))

## [3.62.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.62.1...v3.62.2) (2023-07-14)

### Bug Fixes

- **workorders:** status filter placeholder change ([983eafe](https://github.com/Datoms-IoT/datoms-webapp/commit/983eafef7617d6fa7d8b7883d4982ccd6716d291))

## [3.62.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.62.0...v3.62.1) (2023-07-13)

### Bug Fixes

- **thing management:** added new thing management to customer 2696 ([fe3c776](https://github.com/Datoms-IoT/datoms-webapp/commit/fe3c776355a56e411d28830e421df002b6bfdd44))

# [3.62.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.61.9...v3.62.0) (2023-07-12)

### Features

- **report:** yesterday daily and fault report added ([0058088](https://github.com/Datoms-IoT/datoms-webapp/commit/0058088d985388efc7c62d23c5c62c99adbd222a))

## [3.61.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.61.8...v3.61.9) (2023-07-12)

### Bug Fixes

- **thing management:** fixed - unabled to get geolocation in ios devices ([375477e](https://github.com/Datoms-IoT/datoms-webapp/commit/375477e2516e89ccc631c98964039a6a427b90f9))

## [3.61.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.61.7...v3.61.8) (2023-07-12)

### Bug Fixes

- **workorders:** fuel management tickets remarks on approval/rejection ([611407f](https://github.com/Datoms-IoT/datoms-webapp/commit/611407f91dda66b156da5158f6bdce0f2a26708b))

## [3.61.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.61.6...v3.61.7) (2023-07-12)

### Bug Fixes

- **subscription management:** fixed - filter values reset after adding or editing subscription ([df125d3](https://github.com/Datoms-IoT/datoms-webapp/commit/df125d35470e05fe82d8572240346201f0b7ea95))

## [3.61.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.61.5...v3.61.6) (2023-07-12)

### Bug Fixes

- **subscription management:** fixed - table is struck in loading state after ediitng subscription ([1beea29](https://github.com/Datoms-IoT/datoms-webapp/commit/1beea299861624b778c71aae929b5f2db093b573))

## [3.61.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.61.4...v3.61.5) (2023-07-11)

### Bug Fixes

- **thing management:** remote calibration bug fixes and improvements ([262ffcd](https://github.com/Datoms-IoT/datoms-webapp/commit/262ffcd0d889ad110605dbd869416ca66b78e569))

## [3.61.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.61.3...v3.61.4) (2023-07-11)

### Bug Fixes

- **thing management:** remote calibration bug fixes ([ff60861](https://github.com/Datoms-IoT/datoms-webapp/commit/ff60861f39f4071492a8c9ac9cf02263972ce8a2))

## [3.61.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.61.2...v3.61.3) (2023-07-11)

### Bug Fixes

- **subscription management:** added total MRR heading and improvements ([b6d7d66](https://github.com/Datoms-IoT/datoms-webapp/commit/b6d7d660c6ce90949f973266884fa94ef5698bbe))

## [3.61.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.61.1...v3.61.2) (2023-07-10)

### Bug Fixes

- **generic routes:** fixed - routes for thing management ([3293439](https://github.com/Datoms-IoT/datoms-webapp/commit/329343938accda05c0c9bb6cecf66328271659d0))

## [3.61.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.61.0...v3.61.1) (2023-07-10)

### Bug Fixes

- **subscription management:** added New Column MRR in subscrption list ([83a1773](https://github.com/Datoms-IoT/datoms-webapp/commit/83a1773ae7e2a019f2a4da57e965b1eefd575bb5))

# [3.61.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.60.11...v3.61.0) (2023-07-10)

### Features

- **thing management:** added New Thing Management for client id 1820 ([3a4f150](https://github.com/Datoms-IoT/datoms-webapp/commit/3a4f15028f40ee0d0b983738f82fa9ad962fe505))

## [3.60.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.60.10...v3.60.11) (2023-07-10)

### Bug Fixes

- **user-edit:** user edit territory issue fixed ([c2d77e2](https://github.com/Datoms-IoT/datoms-webapp/commit/c2d77e2fb8bd0fde93568487f0d16b96087215a5))

## [3.60.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.60.9...v3.60.10) (2023-07-10)

### Bug Fixes

- **report:** report mobile view thing selection fix ([52e8056](https://github.com/Datoms-IoT/datoms-webapp/commit/52e80566e3110953557a0f2658831d717bbb4e11))

## [3.60.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.60.8...v3.60.9) (2023-07-10)

### Bug Fixes

- **firmware-update:** device firmware update case handled for circuit check with version ([b37f017](https://github.com/Datoms-IoT/datoms-webapp/commit/b37f017a393a8081521a5caf9ab06d5fc2b6165c))

## [3.60.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.60.7...v3.60.8) (2023-07-07)

### Bug Fixes

- **report:** report customized modal placeolder ([d8edbca](https://github.com/Datoms-IoT/datoms-webapp/commit/d8edbca80520a79b102f42a96b80bf78af28f325))

## [3.60.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.60.6...v3.60.7) (2023-07-06)

### Bug Fixes

- **firmware-add:** new firmware upload circuit version shown only for some specific device types ([2968345](https://github.com/Datoms-IoT/datoms-webapp/commit/2968345e6dd0af4dd54fcff7a06d570de40107d3))

## [3.60.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.60.5...v3.60.6) (2023-07-06)

### Bug Fixes

- **report:** report custom drawer design change ([1a99a09](https://github.com/Datoms-IoT/datoms-webapp/commit/1a99a09abd82652017826d7f2d0ccd7572958547))

## [3.60.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.60.4...v3.60.5) (2023-07-06)

### Bug Fixes

- **things list:** things list count difference in different views pages ([d23981e](https://github.com/Datoms-IoT/datoms-webapp/commit/d23981e84a89607cffcbdead1c8c85cacd7ebee6))

## [3.60.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.60.3...v3.60.4) (2023-07-05)

### Bug Fixes

- **ip camera:** ip camera detailed view available in mobile ([91d49d3](https://github.com/Datoms-IoT/datoms-webapp/commit/91d49d34ac5b614738f5bc95a717a24a27c24c2e))

## [3.60.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.60.2...v3.60.3) (2023-07-03)

### Bug Fixes

- **thing management:** updated data bit and stop bit keys in system configuration ([66736be](https://github.com/Datoms-IoT/datoms-webapp/commit/66736bebd76b9dc650b16191f435219a7efc2d5f))

## [3.60.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.60.1...v3.60.2) (2023-07-03)

### Bug Fixes

- **thing management:** fixed - some data and keys are missing in system config of uniphos protocol ([d40d9b4](https://github.com/Datoms-IoT/datoms-webapp/commit/d40d9b4d71bf6dce4e590e4acf472e48a8e66b8b))

## [3.60.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.60.0...v3.60.1) (2023-07-01)

### Bug Fixes

- **custom report:** custom report 15 min average graph bug fix ([01f762a](https://github.com/Datoms-IoT/datoms-webapp/commit/01f762ac58a96ca38fe106202b931361b4b6684c))

# [3.60.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.59.3...v3.60.0) (2023-06-30)

### Features

- **rental:** test drive feedback feature ([0f60d55](https://github.com/Datoms-IoT/datoms-webapp/commit/0f60d55ae2f940c41ee0d0a80b99b5d03a64bbaf))

## [3.59.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.59.2...v3.59.3) (2023-06-29)

### Bug Fixes

- **rental:** total order count temp fix ([a10b187](https://github.com/Datoms-IoT/datoms-webapp/commit/a10b187f34498c40b85f298e0e69acae1dbaf1a3))

## [3.59.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.59.1...v3.59.2) (2023-06-28)

### Bug Fixes

- **thing management:** changed data_type key in parameter configuration to src_data_type ([fb1c4f7](https://github.com/Datoms-IoT/datoms-webapp/commit/fb1c4f72d8885d2503fc050d68c3eefd9a732cf1))

## [3.59.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.59.0...v3.59.1) (2023-06-28)

### Bug Fixes

- **generic:** dc energy meter available in panel ([c34904f](https://github.com/Datoms-IoT/datoms-webapp/commit/c34904f12487e3b3c421119a6f4977e1a9f4b8d4))

# [3.59.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.58.8...v3.59.0) (2023-06-28)

### Features

- **rental:** test drive version 2, customer manager flow ([8d51741](https://github.com/Datoms-IoT/datoms-webapp/commit/8d517413e18109517577ef878e086c47328b401e))

## [3.58.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.58.7...v3.58.8) (2023-06-28)

### Bug Fixes

- **dg:** maintenance due calculation change for all portal ([9d61650](https://github.com/Datoms-IoT/datoms-webapp/commit/9d6165042b6e5aebefd8c47e5d006bcd897a8bbf))

## [3.58.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.58.6...v3.58.7) (2023-06-27)

### Bug Fixes

- **thing management:** changed sampling time for pollution thing types ([a8c3efb](https://github.com/Datoms-IoT/datoms-webapp/commit/a8c3efba17d7a5779bcd9ec2e8bcb43cfba342b0))

## [3.58.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.58.5...v3.58.6) (2023-06-27)

### Bug Fixes

- **thing management:** updated modbus configuration fields ans keys ([bed8eb6](https://github.com/Datoms-IoT/datoms-webapp/commit/bed8eb65cbf3c33617f8f9928db283b3ad0851fa))

## [3.58.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.58.4...v3.58.5) (2023-06-27)

### Bug Fixes

- **google-map:** google map api key updated ([23b1b74](https://github.com/Datoms-IoT/datoms-webapp/commit/23b1b74f5515366d55d4efcce7dbbfd41b493181))

## [3.58.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.58.3...v3.58.4) (2023-06-26)

### Bug Fixes

- **cookie-banner:** sometime cookie banner flicker issue fixed ([85c7f47](https://github.com/Datoms-IoT/datoms-webapp/commit/85c7f47ef80282f3a2fde2da1ac30a18874332c1))

## [3.58.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.58.2...v3.58.3) (2023-06-26)

### Bug Fixes

- **thing management:** updated vendor filtering condition for rental things in thing list ([820d23a](https://github.com/Datoms-IoT/datoms-webapp/commit/820d23a76cc5b8342dd324fb8449c0dfae4c5c99))

## [3.58.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.58.1...v3.58.2) (2023-06-23)

### Bug Fixes

- **dg:** fuel key replaced with fuel raw in dg monitoring ([8af98f9](https://github.com/Datoms-IoT/datoms-webapp/commit/8af98f950f2c7e8802c5f24510aa7b1cb45da806))

## [3.58.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.58.0...v3.58.1) (2023-06-22)

### Bug Fixes

- **app js:** trip view in old dg routes ([457d87d](https://github.com/Datoms-IoT/datoms-webapp/commit/457d87d2bf1af1fb3b423724ed834fae489475ec))

# [3.58.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.57.2...v3.58.0) (2023-06-22)

### Features

- **trip view:** trip view page added(for Sales Demo and Development Testing) ([35ddcce](https://github.com/Datoms-IoT/datoms-webapp/commit/35ddccedd96908bc0328c4887f9a74a09e0c7ccf))

## [3.57.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.57.1...v3.57.2) (2023-06-21)

### Bug Fixes

- **url-fix:** thing and user application name url issue fixed ([57cfeea](https://github.com/Datoms-IoT/datoms-webapp/commit/57cfeea8a6506c8c1294a98f611f452699662c97))

## [3.57.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.57.0...v3.57.1) (2023-06-21)

### Bug Fixes

- **dummy:** dummy commit ([311a384](https://github.com/Datoms-IoT/datoms-webapp/commit/311a384588786114eba25c96d61088c8a8c66082))

# [3.57.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.56.5...v3.57.0) (2023-06-21)

### Features

- **app:** url should not consist dg-monitoring ([e328de7](https://github.com/Datoms-IoT/datoms-webapp/commit/e328de72a5d86f31a4adba5ec1d471514b80ac4a))

## [3.56.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.56.4...v3.56.5) (2023-06-21)

### Bug Fixes

- **revert-access-webapp:** access webapp and thing dashboard link reverted to open in end cus portal ([d125036](https://github.com/Datoms-IoT/datoms-webapp/commit/d1250366e0f22661b74f61cbb4e148d90afbbd3f))

## [3.56.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.56.3...v3.56.4) (2023-06-20)

### Bug Fixes

- **camerapomo:** pomo camera thumbnail for no streamlink ([1ca23a9](https://github.com/Datoms-IoT/datoms-webapp/commit/1ca23a99c3513723d814f7e9fbaf0d9cc7073ca2))

## [3.56.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.56.2...v3.56.3) (2023-06-19)

### Bug Fixes

- **cookie-consent:** cookie path set to / ([240703c](https://github.com/Datoms-IoT/datoms-webapp/commit/240703c1ea175df237782cc695c2b54fb76e9f2b))

## [3.56.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.56.1...v3.56.2) (2023-06-19)

### Bug Fixes

- **cookie-consent:** added expire to cookie to retain value after browser close ([13f2532](https://github.com/Datoms-IoT/datoms-webapp/commit/13f253212bcdc17f3775df0195cfd49c2a8a1305))

## [3.56.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.56.0...v3.56.1) (2023-06-19)

### Bug Fixes

- **user add:** preserve selected things on switching roles(except to/from admin) ([953e3b6](https://github.com/Datoms-IoT/datoms-webapp/commit/953e3b67e21eed01b578ac6685c145b3cf0b3c43))

# [3.56.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.55.8...v3.56.0) (2023-06-19)

### Features

- **cookie-consent:** cookie consent banner shown to user ([54434ae](https://github.com/Datoms-IoT/datoms-webapp/commit/54434ae0575e0e4dc4685d91b276aa131383ac73))

## [3.55.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.55.7...v3.55.8) (2023-06-16)

### Bug Fixes

- **app:** task api parametrs false attribute added to dg nd generic ([94cb548](https://github.com/Datoms-IoT/datoms-webapp/commit/94cb548603b37e4da2a8ecd1f51fb8344a879cc0))

## [3.55.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.55.6...v3.55.7) (2023-06-16)

### Bug Fixes

- **custom report:** custom report 15 min aggr title change ([a9c921d](https://github.com/Datoms-IoT/datoms-webapp/commit/a9c921d5117aa0440b1cf751b9b4a1845793bcdd))

## [3.55.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.55.5...v3.55.6) (2023-06-16)

### Bug Fixes

- **customers:** max users field show for customer id 392 ([a6b62ab](https://github.com/Datoms-IoT/datoms-webapp/commit/a6b62abc6cae07738a14474e88f572011bcda99f))
- **user:** for all users email and mobile resend and verify/unverify shown ([3bd1d33](https://github.com/Datoms-IoT/datoms-webapp/commit/3bd1d33c88dbc8c8c25b59a238d07bad363497d9))

## [3.55.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.55.4...v3.55.5) (2023-06-16)

### Bug Fixes

- **alerts:** pomo alerts issue fix ([4dfb576](https://github.com/Datoms-IoT/datoms-webapp/commit/4dfb576fa94f9a7e9a0d4a754f45a7ea3922ad20))

## [3.55.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.55.3...v3.55.4) (2023-06-15)

### Bug Fixes

- **ipcamera:** ip camera thmbnail and status time added ([4f23936](https://github.com/Datoms-IoT/datoms-webapp/commit/4f23936d2cc6e05413f8ce289cfd12d2abe8ebe0))

## [3.55.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.55.2...v3.55.3) (2023-06-15)

### Bug Fixes

- **custom report:** custom report dynamic aggr intervals ([3770f80](https://github.com/Datoms-IoT/datoms-webapp/commit/3770f80cec42e48091b0d62075b73126b27114cb))

## [3.55.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.55.1...v3.55.2) (2023-06-15)

### Bug Fixes

- **smssummary:** violation report api integration ([a5d3b7e](https://github.com/Datoms-IoT/datoms-webapp/commit/a5d3b7e8b8ba3f9eb24eddf3ffdfa87eba33c48f))

## [3.55.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.55.0...v3.55.1) (2023-06-14)

### Bug Fixes

- **custom report:** custom report data isnan checked. Single thing detailed view open ([3389709](https://github.com/Datoms-IoT/datoms-webapp/commit/338970988307435ae5af8ca96c5eecfb8df26994))

# [3.55.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.54.14...v3.55.0) (2023-06-14)

### Features

- new dg views implemented in iot views ([52f6680](https://github.com/Datoms-IoT/datoms-webapp/commit/52f668087b558497af9582aaae66018c9921ef1d))

## [3.54.14](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.54.13...v3.54.14) (2023-06-14)

### Bug Fixes

- **pomo:** pomo param order ([91c1491](https://github.com/Datoms-IoT/datoms-webapp/commit/91c149113e29a87aff0c952edc7949486d666c02))

## [3.54.13](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.54.12...v3.54.13) (2023-06-14)

### Bug Fixes

- **user-management:** deactive text changed to inactive ([fd0de64](https://github.com/Datoms-IoT/datoms-webapp/commit/fd0de6444f4eaf0236dc3e2499fc5713013fb570))

## [3.54.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.54.11...v3.54.12) (2023-06-13)

### Bug Fixes

- **zero-date:** zero date case handled in lists and deactive tag shown in user list ([e45a114](https://github.com/Datoms-IoT/datoms-webapp/commit/e45a11481ca9c51e408ef5cd15b0f7edfa70dabf))

## [3.54.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.54.10...v3.54.11) (2023-06-13)

### Bug Fixes

- **genset status:** genset status parameters added ([85531e0](https://github.com/Datoms-IoT/datoms-webapp/commit/85531e074a80ca36f42b5fe40006270aa6f2c323))

## [3.54.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.54.9...v3.54.10) (2023-06-13)

### Bug Fixes

- **workflow:** workflow mobile design fixed ([eaed64b](https://github.com/Datoms-IoT/datoms-webapp/commit/eaed64b7ca222df98cf11f53142fcaad29df2c50))

## [3.54.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.54.8...v3.54.9) (2023-06-12)

### Bug Fixes

- **ip camera:** ip camera logic and design changes ([e5ad1da](https://github.com/Datoms-IoT/datoms-webapp/commit/e5ad1da8e39f0c1cbde2fb8c9adb5784f838112f))
- **ip-cam:** enabled ip camera streaming in detailed view ([810a31e](https://github.com/Datoms-IoT/datoms-webapp/commit/810a31e5db27ba0cde08310f57b1c5de2df83652))

## [3.54.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.54.7...v3.54.8) (2023-06-12)

### Bug Fixes

- **generic:** workflow fixes ([217d543](https://github.com/Datoms-IoT/datoms-webapp/commit/217d543400b81646c82df6ccc12e2285e3399e17))

## [3.54.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.54.6...v3.54.7) (2023-06-12)

### Bug Fixes

- **threshold limit fixed for parameters:** threshold limit fixed for parameters ([e41d642](https://github.com/Datoms-IoT/datoms-webapp/commit/e41d6425b93faa09b30377ffe473bfad4ea80617))

## [3.54.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.54.5...v3.54.6) (2023-06-09)

### Bug Fixes

- **dg:** fault param name tooltip ([6a3d95e](https://github.com/Datoms-IoT/datoms-webapp/commit/6a3d95ec375d72ca2f52b66a7384502eddb050c9))

## [3.54.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.54.4...v3.54.5) (2023-06-09)

### Bug Fixes

- **customers:** pomo email id update, mahindra cus add features list fix ([243044e](https://github.com/Datoms-IoT/datoms-webapp/commit/243044e1d2a6c11243342afe2f18f77fd88e9336))

## [3.54.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.54.3...v3.54.4) (2023-06-09)

### Bug Fixes

- **generic:** pomo detailed view margin fix ([44275b2](https://github.com/Datoms-IoT/datoms-webapp/commit/44275b2d9b55ef835d30eee9ab678298448bbccd))

## [3.54.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.54.2...v3.54.3) (2023-06-08)

### Bug Fixes

- **thing management:** fixed - Thing Addition is giving error for mahindra in some cases ([1748454](https://github.com/Datoms-IoT/datoms-webapp/commit/174845464c1765a7f8ea358dd0fd655dbc211399))

## [3.54.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.54.1...v3.54.2) (2023-06-08)

### Bug Fixes

- **workorders:** system & technician filled columns added ([696c38e](https://github.com/Datoms-IoT/datoms-webapp/commit/696c38e404dd0ef7b1d05e5a8d2d9ff282b4cdd2))

## [3.54.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.54.0...v3.54.1) (2023-06-08)

### Bug Fixes

- **customer-filter:** customer filters disabled when data is loading ([65a6d67](https://github.com/Datoms-IoT/datoms-webapp/commit/65a6d675db0a3796a4d320b7e5e5a0cdad1125aa))

# [3.54.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.53.4...v3.54.0) (2023-06-07)

### Features

- **detailed view:** genset status updated to detailed view ([f4f3e83](https://github.com/Datoms-IoT/datoms-webapp/commit/f4f3e831f8661bed20a49f3c82a3e28017098e01))

## [3.53.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.53.3...v3.53.4) (2023-06-07)

### Bug Fixes

- **customer edit:** order of pomo fields changed, 2 new fields added ([a39f6e8](https://github.com/Datoms-IoT/datoms-webapp/commit/a39f6e8025bbfd607cf600815e7cece48db631bc))

## [3.53.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.53.2...v3.53.3) (2023-06-05)

### Bug Fixes

- **generic:** pollution monitoring workflow page added ([5299bbc](https://github.com/Datoms-IoT/datoms-webapp/commit/5299bbc5e58a9a3d71cd94e9d84b0118be74c02e))

## [3.53.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.53.1...v3.53.2) (2023-06-05)

### Bug Fixes

- **generic:** fleet data fixed ([f5fffe8](https://github.com/Datoms-IoT/datoms-webapp/commit/f5fffe8e1bbf3058b1cf8ef82af48365d0e11966))

## [3.53.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.53.0...v3.53.1) (2023-06-05)

### Bug Fixes

- **generic:** tanker truck client id undefined fixed ([8a4362c](https://github.com/Datoms-IoT/datoms-webapp/commit/8a4362c7b98ce63a397a123cdd042ece89460d95))

# [3.53.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.52.0...v3.53.0) (2023-06-05)

### Bug Fixes

- **rental:** test drive otp start ([2a2541a](https://github.com/Datoms-IoT/datoms-webapp/commit/2a2541aaed924f9b0eab8e21671f90252584301b))

### Features

- **rental:** order start using otp for test drive ([210336b](https://github.com/Datoms-IoT/datoms-webapp/commit/210336b28649f0d757e6810e9235c71e8ff16617))

# [3.52.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.51.8...v3.52.0) (2023-06-02)

### Features

- **customers:** cpcb & spcb info in customer details of customers with pomo things(cat: 21,22,23) ([9f934ae](https://github.com/Datoms-IoT/datoms-webapp/commit/9f934ae7e48097393e057ced6960b1c93a10105b))

## [3.51.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.51.7...v3.51.8) (2023-06-02)

### Bug Fixes

- **marquee fixed:** marquee fixed ([8e1dd34](https://github.com/Datoms-IoT/datoms-webapp/commit/8e1dd34457656b279434d646c49b21bd4e9ec55c))

## [3.51.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.51.6...v3.51.7) (2023-06-01)

### Bug Fixes

- **pomo:** pOMO threshold enable for active alerts ([8413aa5](https://github.com/Datoms-IoT/datoms-webapp/commit/8413aa51721d84844d887827ede621df0ce454d3))

## [3.51.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.51.5...v3.51.6) (2023-06-01)

### Bug Fixes

- **alert-rule:** alert rule delivery media switch show/hide condition updated ([4aad20a](https://github.com/Datoms-IoT/datoms-webapp/commit/4aad20a4a19cfb3fc0568263dcad2b9d11483b64))

## [3.51.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.51.4...v3.51.5) (2023-06-01)

### Bug Fixes

- **generic pomo:** pomo param name fixed with threshold key change ([9a72548](https://github.com/Datoms-IoT/datoms-webapp/commit/9a725483a24f6865ec92524277bb3bd7b7eaa43e))

## [3.51.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.51.3...v3.51.4) (2023-06-01)

### Bug Fixes

- **pomo-alert:** alert ruel edit issue fixed for polution monitoring ([43e7a7f](https://github.com/Datoms-IoT/datoms-webapp/commit/43e7a7f18ad5db3e0a8e37c0b9c9091acc61a1fa))

## [3.51.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.51.2...v3.51.3) (2023-05-31)

### Bug Fixes

- **pomo alerts:** removed complex expression and added 'Threshold' as label ([dc79847](https://github.com/Datoms-IoT/datoms-webapp/commit/dc79847280d599c7844be5cbcd98c727de0eba45))

## [3.51.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.51.1...v3.51.2) (2023-05-31)

### Bug Fixes

- **customers:** style fix for ios ([97a8095](https://github.com/Datoms-IoT/datoms-webapp/commit/97a809506d8c06ed17125428061b4c69c6ee7cd8))

## [3.51.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.51.0...v3.51.1) (2023-05-30)

### Bug Fixes

- **app:** pollution monitoring banner added ([f409b7c](https://github.com/Datoms-IoT/datoms-webapp/commit/f409b7c5091a6335f4ae1c47463c7f9c65be6e26))

# [3.51.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.50.2...v3.51.0) (2023-05-30)

### Features

- **alerts:** alerts condition handle for PoMo ([68c554c](https://github.com/Datoms-IoT/datoms-webapp/commit/68c554cbecdfcd1197ccd3d88de848719223144f))

## [3.50.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.50.1...v3.50.2) (2023-05-29)

### Bug Fixes

- **custome report:** custom report categoy fixed ([7a9bfff](https://github.com/Datoms-IoT/datoms-webapp/commit/7a9bffffc0ee7a3eaff368acfdafdf91f7401aaa))

## [3.50.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.50.0...v3.50.1) (2023-05-26)

### Bug Fixes

- **generic:** pomo changes ([9c89cce](https://github.com/Datoms-IoT/datoms-webapp/commit/9c89cce83bdeb55a2c27a45e229be04c74aaae64))

# [3.50.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.49.8...v3.50.0) (2023-05-26)

### Features

- **customer-list:** customer inactive status shown in list ([27f735f](https://github.com/Datoms-IoT/datoms-webapp/commit/27f735fe2e7cdeea0a19a7c1210f137b5cbe6a1e))

## [3.49.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.49.7...v3.49.8) (2023-05-26)

### Bug Fixes

- **thing management:** fixed - status icon size in thing list ([f224059](https://github.com/Datoms-IoT/datoms-webapp/commit/f224059df4c1af880ed54c95158977a657bf7d74))

## [3.49.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.49.6...v3.49.7) (2023-05-26)

### Bug Fixes

- **rental:** test drive details reload fix ([94fbf96](https://github.com/Datoms-IoT/datoms-webapp/commit/94fbf96a73b797fe35189218bf09d423a4351156))

## [3.49.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.49.5...v3.49.6) (2023-05-26)

### Bug Fixes

- **rental:** map zoom in bangalore ([1f1f547](https://github.com/Datoms-IoT/datoms-webapp/commit/1f1f5471001b7619b2b5fe1c211f1d8a5a2d7dac))

## [3.49.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.49.4...v3.49.5) (2023-05-25)

### Bug Fixes

- **rental-test drive:** location of testdrive & display order added by user ([a9d23c1](https://github.com/Datoms-IoT/datoms-webapp/commit/a9d23c19d6e1349799afe1b782c927c96bddb9be))

## [3.49.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.49.3...v3.49.4) (2023-05-25)

### Bug Fixes

- **generic:** pomo detailed view graph sync ([1333219](https://github.com/Datoms-IoT/datoms-webapp/commit/1333219cb30a614fb90a689b76caf19f2d7ce9e1))

## [3.49.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.49.2...v3.49.3) (2023-05-25)

### Bug Fixes

- **thing management:** thing management bug fixes and improvements ([ac7b3de](https://github.com/Datoms-IoT/datoms-webapp/commit/ac7b3de0ccef7c7ef48e222207554e63bc7ee27b))

## [3.49.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.49.1...v3.49.2) (2023-05-25)

### Bug Fixes

- **thing management:** fixed - Location picker styling issue ([7feb52e](https://github.com/Datoms-IoT/datoms-webapp/commit/7feb52e6e5a64e2a71d76d54009cc94806ce0f91))

## [3.49.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.49.0...v3.49.1) (2023-05-24)

### Bug Fixes

- **pomo:** pomo design changes and generic enabled for all thing types ([7de4d7c](https://github.com/Datoms-IoT/datoms-webapp/commit/7de4d7c89962262e234c7f953446775406d842ca))

# [3.49.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.48.11...v3.49.0) (2023-05-24)

### Features

- **self-edit:** user self edit enabled ([2117493](https://github.com/Datoms-IoT/datoms-webapp/commit/21174938a453134cda63b86a91e16913084a79b9))

## [3.48.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.48.10...v3.48.11) (2023-05-23)

### Bug Fixes

- **mobile login:** fixed - menu icons are not changing after logout and login with different account ([87f7843](https://github.com/Datoms-IoT/datoms-webapp/commit/87f78432e172284a48a0028fd1d4a5c7c0f74ff6))

## [3.48.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.48.9...v3.48.10) (2023-05-23)

### Bug Fixes

- **pomo:** poMo bug fix and design changes ([ceea8ea](https://github.com/Datoms-IoT/datoms-webapp/commit/ceea8eaacf33e2ad50725e409a6955da8b68b912))

## [3.48.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.48.8...v3.48.9) (2023-05-23)

### Bug Fixes

- **iot views:** datomsx views issue fix ([117becf](https://github.com/Datoms-IoT/datoms-webapp/commit/117becf92de2e2ad19e6a85d2209923de6fb26e4))

## [3.48.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.48.7...v3.48.8) (2023-05-23)

### Bug Fixes

- **iot routes:** hidden unsupported iot views for vendors ([3205e7e](https://github.com/Datoms-IoT/datoms-webapp/commit/3205e7e6328330fe3610853da288db303ed624ad))

## [3.48.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.48.6...v3.48.7) (2023-05-23)

### Bug Fixes

- **thing management:** show inactive things only in thing management page only ([9acdeef](https://github.com/Datoms-IoT/datoms-webapp/commit/9acdeef2ee3b62003e22c4ec3e58cabd29382d55))

## [3.48.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.48.5...v3.48.6) (2023-05-19)

### Bug Fixes

- **generic template:** pollution monitoring detailed and panel view design changes ([9dc2e58](https://github.com/Datoms-IoT/datoms-webapp/commit/9dc2e581a5dbe88633583dc6bba0338d2da69b13))

## [3.48.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.48.4...v3.48.5) (2023-05-19)

### Bug Fixes

- **rental:** customer addition from orders ([afb3859](https://github.com/Datoms-IoT/datoms-webapp/commit/afb3859dea5da99b58f0b99ebb04ab236cd1b485))

## [3.48.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.48.3...v3.48.4) (2023-05-19)

### Bug Fixes

- **parsemakename:** form item make not coming case handled ([b608615](https://github.com/Datoms-IoT/datoms-webapp/commit/b60861516a929ffa09e563d16f659bd9aac4c342))

## [3.48.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.48.2...v3.48.3) (2023-05-18)

### Bug Fixes

- **thingmanagement:** fixed - thing configuration error - specify parameter only once ([c57a5b0](https://github.com/Datoms-IoT/datoms-webapp/commit/c57a5b083f20bd801cf0256eca27180b71e9bec7))

## [3.48.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.48.1...v3.48.2) (2023-05-18)

### Bug Fixes

- **mobile design for pomo:** poMo mobile design ([0d59106](https://github.com/Datoms-IoT/datoms-webapp/commit/0d59106d09bc0261ca8657c4f9b4cdeb0079aa86))

## [3.48.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.48.0...v3.48.1) (2023-05-17)

### Bug Fixes

- **rental:** test drive issue fixed ([ffc845e](https://github.com/Datoms-IoT/datoms-webapp/commit/ffc845e699392ca4f5ad62e0f9e6b16974294eb9))

# [3.48.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.47.5...v3.48.0) (2023-05-16)

### Features

- **rental/customers:** test Drive Order Management feature added ([8e58a29](https://github.com/Datoms-IoT/datoms-webapp/commit/8e58a29c819ee94648685c58e7c57a02283e8a13))

## [3.47.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.47.4...v3.47.5) (2023-05-16)

### Bug Fixes

- **user-add:** atleast one admin in user list condition removed for all customers ([4b93b41](https://github.com/Datoms-IoT/datoms-webapp/commit/4b93b41a4f01cd67041f835133aab74d2eacc18d))

## [3.47.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.47.3...v3.47.4) (2023-05-15)

### Bug Fixes

- **generic template:** generic Template Pollution monitoring table data unavailability fix ([aadbdc0](https://github.com/Datoms-IoT/datoms-webapp/commit/aadbdc0b2cb0fd06fb5af0a650044fe6faa75e3a))

## [3.47.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.47.2...v3.47.3) (2023-05-12)

### Bug Fixes

- **pollution monitoring detailed view:** threshold removed from pollution monitoring detailed view ([030e5ef](https://github.com/Datoms-IoT/datoms-webapp/commit/030e5ef6c4ac3f07a80f1d775e3aca37de628712))

## [3.47.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.47.1...v3.47.2) (2023-05-12)

### Bug Fixes

- **generic template:** pollution monitoring design change ([c7eb9c8](https://github.com/Datoms-IoT/datoms-webapp/commit/c7eb9c8f61eea8b50ef1ad71e184600a3dab6ca7))

## [3.47.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.47.0...v3.47.1) (2023-05-12)

### Bug Fixes

- **thingmanagement:** multi system configuration imporvements and bug fixes ([fd60b49](https://github.com/Datoms-IoT/datoms-webapp/commit/fd60b491ab8be92df95dbd37094d8db23d2bd5b0))

# [3.47.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.46.5...v3.47.0) (2023-05-11)

### Features

- **customers/workorders:** fuel management workorders added, enterprise plan in customers add/edit ([363f435](https://github.com/Datoms-IoT/datoms-webapp/commit/363f435a41d53ce9d96e7b7f18b41e9786595399))

## [3.46.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.46.4...v3.46.5) (2023-05-11)

### Bug Fixes

- **generic template:** yuDash customer added in generic template ([5173d4f](https://github.com/Datoms-IoT/datoms-webapp/commit/5173d4fae963d6d3a65985b47921188ceb2d2c12))

## [3.46.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.46.3...v3.46.4) (2023-05-11)

### Bug Fixes

- **user-add:** atleast one admin in user list case handled for rental end-customer ([97e6ff7](https://github.com/Datoms-IoT/datoms-webapp/commit/97e6ff70fa5b8dd745a1243b1572f188692be40c))

## [3.46.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.46.2...v3.46.3) (2023-05-11)

### Bug Fixes

- **thingmanagement:** changed multisystem structure ([44d3888](https://github.com/Datoms-IoT/datoms-webapp/commit/44d3888765bd5397060c2fb419d63a7196628fd8))

## [3.46.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.46.1...v3.46.2) (2023-05-10)

### Bug Fixes

- **generic template:** map view removed from flow meter ([a8b1309](https://github.com/Datoms-IoT/datoms-webapp/commit/a8b130910a7db5be444d9e34be275a1b55523f35))

## [3.46.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.46.0...v3.46.1) (2023-05-09)

### Bug Fixes

- **cold-storage:** cold storage in generic ([1b86e1c](https://github.com/Datoms-IoT/datoms-webapp/commit/1b86e1ca6a488ca37c5dd7880623d6457413ddec))

# [3.46.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.45.5...v3.46.0) (2023-05-09)

### Features

- **user-management:** users search with email and mobile number also ([103b538](https://github.com/Datoms-IoT/datoms-webapp/commit/103b5385aa75f8a7312fb74728357151a59e8820))

## [3.45.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.45.4...v3.45.5) (2023-05-09)

### Bug Fixes

- **lifetime report:** lifetime report error fix for repeatations of values when no data ([2ae1eab](https://github.com/Datoms-IoT/datoms-webapp/commit/2ae1eab80da0eee02c2d0487341449ad3bb14218))

## [3.45.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.45.3...v3.45.4) (2023-05-08)

### Bug Fixes

- **mobile login page:** styling Corrections for custom login pages ([7b4b571](https://github.com/Datoms-IoT/datoms-webapp/commit/7b4b571640a9dc46c7ff0a420ddc3417f3b03aac))

## [3.45.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.45.2...v3.45.3) (2023-05-08)

### Bug Fixes

- **alerts:** rental thing alert template value change issue fixed ([5384d51](https://github.com/Datoms-IoT/datoms-webapp/commit/5384d51b9048b44c4b969b67a0f7668aed98b9b7))

## [3.45.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.45.1...v3.45.2) (2023-05-04)

### Bug Fixes

- **generic routes:** site dashboard limited to swiggy if coming from feature ([6d10c25](https://github.com/Datoms-IoT/datoms-webapp/commit/6d10c251216e5074f4202ceb0881562eeaf5bdc1))

## [3.45.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.45.0...v3.45.1) (2023-05-04)

### Bug Fixes

- **thingmanagement:** fixed - Device configuration not opening if template api fails ([8296764](https://github.com/Datoms-IoT/datoms-webapp/commit/8296764525ef528fbee0ce4434f90fed7d111e79))

# [3.45.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.44.0...v3.45.0) (2023-05-01)

### Bug Fixes

- **yoyorydes:** terms and conditions added for yoyo and datoms, alerts bugfix ([ce86140](https://github.com/Datoms-IoT/datoms-webapp/commit/ce861404ba4899dfc3ba24d33012850c32fbec84))

### Features

- terms & conditions and privacy policy added for datoms and yoyorydes ([89d0815](https://github.com/Datoms-IoT/datoms-webapp/commit/89d0815b2b27fbf795dad92263febdf0811f4bf7))

# [3.44.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.43.4...v3.44.0) (2023-04-28)

### Features

- **rental store:** notifications for rental store yoyorydes/few modifications ([5ac8d33](https://github.com/Datoms-IoT/datoms-webapp/commit/5ac8d33198cec612fe540204c9c6a6c59e372ef7))

## [3.43.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.43.3...v3.43.4) (2023-04-27)

### Bug Fixes

- **thingmanagement:** fixed - Device is assgined to datoms-x for rental things in datoms-x ([93823cf](https://github.com/Datoms-IoT/datoms-webapp/commit/93823cfb0848c10b88d613fdf1818a03f40ad4af))

## [3.43.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.43.2...v3.43.3) (2023-04-26)

### Bug Fixes

- **fuel -buddy:** fuel buddy icon removed from header for all customer ([ffd92c1](https://github.com/Datoms-IoT/datoms-webapp/commit/ffd92c1d33dc21b9998152176d49de1d07355712))

## [3.43.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.43.1...v3.43.2) (2023-04-25)

### Bug Fixes

- **rental alerts:** rental alerts mobile app notification medium enabled ([cfbc4be](https://github.com/Datoms-IoT/datoms-webapp/commit/cfbc4beef39e451cc5a68373e852ea99e008f4e3))

## [3.43.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.43.0...v3.43.1) (2023-04-25)

### Bug Fixes

- **generic template:** list optimized and map removed from chiller ([c71d005](https://github.com/Datoms-IoT/datoms-webapp/commit/c71d005c773c9994484132c0969241b7069c20e0))

# [3.43.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.42.2...v3.43.0) (2023-04-24)

### Features

- **thingmanagement:** added multisystem support and replacing device without losing configuration ([933544b](https://github.com/Datoms-IoT/datoms-webapp/commit/933544b26905e117f68cf429bea1d06f5742ebdd))

## [3.42.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.42.1...v3.42.2) (2023-04-24)

### Bug Fixes

- issues fixed for tecnosoft vendor ([3ca4a02](https://github.com/Datoms-IoT/datoms-webapp/commit/3ca4a02578068836270af1d91d1e5d66f7d479dd))

## [3.42.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.42.0...v3.42.1) (2023-04-20)

### Bug Fixes

- **mobile no label:** whatsapp no text changed to mobile no for tecnosoft ([69014e1](https://github.com/Datoms-IoT/datoms-webapp/commit/69014e14982e4840c09982ea4acb16551830b8af))
- **placeholder-text:** placeholder text for mobile number issue fixed ([632f7d9](https://github.com/Datoms-IoT/datoms-webapp/commit/632f7d99c970738c585c43c3205ab28e7b963e61))

# [3.42.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.41.9...v3.42.0) (2023-04-20)

### Features

- **rental:** yoyorydes rental order management from vendor portal ([e03b80b](https://github.com/Datoms-IoT/datoms-webapp/commit/e03b80bf692b1ec8a14b13bec75f10a3316d03d5))

## [3.41.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.41.8...v3.41.9) (2023-04-20)

### Bug Fixes

- **real time:** real time view added to flow meter ([d918033](https://github.com/Datoms-IoT/datoms-webapp/commit/d91803345395e02f84447059b658222d0540433f))

## [3.41.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.41.7...v3.41.8) (2023-04-19)

### Bug Fixes

- **fleet and tanker truck:** status fixed for fleet and tanker truck ([dc24bee](https://github.com/Datoms-IoT/datoms-webapp/commit/dc24beedd47c9f7e4e79cd5933c0bb47f2bcc4e6))

## [3.41.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.41.6...v3.41.7) (2023-04-18)

### Bug Fixes

- **generic template:** pollution monitoring added to generic template ([b7dac4f](https://github.com/Datoms-IoT/datoms-webapp/commit/b7dac4f899d3e8e2ddab49a7ff1507fa344d032b))

## [3.41.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.41.5...v3.41.6) (2023-04-17)

### Bug Fixes

- **help-for-vendor:** help link shown for all vendors ([61c834a](https://github.com/Datoms-IoT/datoms-webapp/commit/61c834aefd71ecc9d8a78678d1a97e569a9c21df))

## [3.41.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.41.4...v3.41.5) (2023-04-17)

### Bug Fixes

- **reports:** reports added for chiller ([2af0ef0](https://github.com/Datoms-IoT/datoms-webapp/commit/2af0ef0d27e4220b64d7d3ab2db57d95e4b84d15))

## [3.41.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.41.3...v3.41.4) (2023-04-17)

### Bug Fixes

- **app js:** cold storage demo customer added to generic app ([d48d191](https://github.com/Datoms-IoT/datoms-webapp/commit/d48d19173c8d2e6897735a31d95a6cace5b88768))

## [3.41.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.41.2...v3.41.3) (2023-04-14)

### Bug Fixes

- **thingmanagement:** fixed - parameter settings in system configuration are not shown in some cases ([16ac9f0](https://github.com/Datoms-IoT/datoms-webapp/commit/16ac9f0b03e770813123501ca278703ff7c3c180))

## [3.41.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.41.1...v3.41.2) (2023-04-13)

### Bug Fixes

- **user-add:** user-add role select handled for basic plan ([6377ec9](https://github.com/Datoms-IoT/datoms-webapp/commit/6377ec9ca2644a511a720113153a16bdbcf50706))

## [3.41.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.41.0...v3.41.1) (2023-04-12)

### Bug Fixes

- **generic template:** things with no real time should not come to the list - Fix ([1b6b365](https://github.com/Datoms-IoT/datoms-webapp/commit/1b6b365d54157e312b0d0071b0c5a6a2f787bf1b))

# [3.41.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.40.2...v3.41.0) (2023-04-12)

### Bug Fixes

- **gmmco-case:** dg online and dg offline alerts are hidden for gmmco end customer in alerts ([2c972b8](https://github.com/Datoms-IoT/datoms-webapp/commit/2c972b8b943814d9079424132a9d3a7f142fdbb1))
- **gmmnco changes:** gmmnco changes for dg-monitoring ([8f36224](https://github.com/Datoms-IoT/datoms-webapp/commit/8f3622436604077977699b1e79373874ff76cf1f))
- **lebel-change:** device AC power text changed to Device Power ([9ef58d7](https://github.com/Datoms-IoT/datoms-webapp/commit/9ef58d7d87b439877ac5e191fdf87582b1dba4ef))
- **rule-template:** thing online/offline rule template hidden for gmmco all end customer portal ([91d75c9](https://github.com/Datoms-IoT/datoms-webapp/commit/91d75c944eef3d26925435f035a6d07b6cd4ee10))

### Features

- **dg status report:** dG Status reports added fot iot and runnhour unit to SMU for GMMCO ([13bdfde](https://github.com/Datoms-IoT/datoms-webapp/commit/13bdfde0ee29c56cbc45faf211a2d8164aadf072))

## [3.40.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.40.1...v3.40.2) (2023-04-12)

### Bug Fixes

- **order details:** order details going blank issue fixed ([8e94374](https://github.com/Datoms-IoT/datoms-webapp/commit/8e94374345771e89708224b2a15e545854f22c7c))

## [3.40.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.40.0...v3.40.1) (2023-04-07)

### Bug Fixes

- **package-json:** yoyorydes mobile build domain name updated ([9357d31](https://github.com/Datoms-IoT/datoms-webapp/commit/9357d315793b79a44a096519cf2c6331865fab48))

# [3.40.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.39.6...v3.40.0) (2023-04-07)

### Features

- **rental-store:** rental store for bike rentals(yoyorydes) end user app v1 ([5c18369](https://github.com/Datoms-IoT/datoms-webapp/commit/5c183691a19e435261228743fcadd99126349a31))

## [3.39.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.39.5...v3.39.6) (2023-04-05)

### Bug Fixes

- **detailed view:** load percentage calculation change in dg monitoring ([045910f](https://github.com/Datoms-IoT/datoms-webapp/commit/045910f0fc616fc2dc9521dc6de8def69acc111e))

## [3.39.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.39.4...v3.39.5) (2023-04-04)

### Bug Fixes

- **thingmanagement:** system Configuration Bugfixes and Changed AC Power label to AC Power in Debug ([6a42923](https://github.com/Datoms-IoT/datoms-webapp/commit/6a4292373db01def6afff6cf2c86bf529022d1f1))

## [3.39.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.39.3...v3.39.4) (2023-04-04)

### Bug Fixes

- **aurassure custom report white labelling:** aurassure custom report white labelling ([d1f3189](https://github.com/Datoms-IoT/datoms-webapp/commit/d1f31890280105c1f814737b1a02081f2b81aa71))

## [3.39.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.39.2...v3.39.3) (2023-04-04)

### Bug Fixes

- **timezone:** user last login timezone shone as per customer time zone ([96e75a5](https://github.com/Datoms-IoT/datoms-webapp/commit/96e75a5c2b0dc4f27926ae5dabf6e89aa3555c16))

## [3.39.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.39.1...v3.39.2) (2023-04-04)

### Bug Fixes

- **generic template:** generic template changes for flow meter ([21f3d57](https://github.com/Datoms-IoT/datoms-webapp/commit/21f3d578e576e960f487d84be24f1cfac9477720))

## [3.39.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.39.0...v3.39.1) (2023-04-01)

### Bug Fixes

- **custom report:** default machine info added if machine info not available from thing categories ([21bb1e5](https://github.com/Datoms-IoT/datoms-webapp/commit/21bb1e5c9d22ae28bd1681441d366107cd5a0e71))

# [3.39.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.38.5...v3.39.0) (2023-03-31)

### Features

- **generic template:** dc energy meter added to generic template ([932ca59](https://github.com/Datoms-IoT/datoms-webapp/commit/932ca59921e6df6c82a211639256959e1e4eb201))

## [3.38.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.38.4...v3.38.5) (2023-03-30)

### Bug Fixes

- **custom report:** custom report data missing on timeout fixed ([ba38fd9](https://github.com/Datoms-IoT/datoms-webapp/commit/ba38fd99c94cf79f2c5fa3e42f91b271b82a88ed))

## [3.38.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.38.3...v3.38.4) (2023-03-30)

### Bug Fixes

- **flow meter changes:** flow meter reports and unit format updated ([2bdc2a3](https://github.com/Datoms-IoT/datoms-webapp/commit/2bdc2a3407270ee417e656ac31e6e29268379567))
- **flow meter:** flow meter unit change and report updated ([f7730a5](https://github.com/Datoms-IoT/datoms-webapp/commit/f7730a5ddb87d85e0b320ab8255b6ea9b6d8713d))

## [3.38.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.38.2...v3.38.3) (2023-03-28)

### Bug Fixes

- **user-add:** user add case handled for multiple applications ([91ac427](https://github.com/Datoms-IoT/datoms-webapp/commit/91ac4277f09104bb4c06396099a3cdee53962078))

## [3.38.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.38.1...v3.38.2) (2023-03-27)

### Bug Fixes

- **thingmangement:** added Latitude and Longitude fields in General form ([b5c7f86](https://github.com/Datoms-IoT/datoms-webapp/commit/b5c7f86257ca5a9471b7beb7ca4cd7f4df228ffb))

## [3.38.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.38.0...v3.38.1) (2023-03-24)

### Bug Fixes

- **pollutionmonitoring:** added Digital display thing category to pollution monitoring routing ([9026b55](https://github.com/Datoms-IoT/datoms-webapp/commit/9026b55c80e3ba81f2254f733b70dcfb63ed5a56))

# [3.38.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.37.3...v3.38.0) (2023-03-22)

### Features

- **flow meter daily report:** flow meter daily report added ([7f5d25e](https://github.com/Datoms-IoT/datoms-webapp/commit/7f5d25e3f1178adf7b1ab5d7bab946a0de2c5302))

## [3.37.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.37.2...v3.37.3) (2023-03-21)

### Bug Fixes

- **generic template:** generic template bug fixes ([564880c](https://github.com/Datoms-IoT/datoms-webapp/commit/564880ce53646614fa7d7b25372aa2eaa2fa28af))

## [3.37.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.37.1...v3.37.2) (2023-03-21)

### Bug Fixes

- **daily report header for plan:** daily reports header fix for plan ([8db1d88](https://github.com/Datoms-IoT/datoms-webapp/commit/8db1d88341ecff70bde05c0ee0674425e93fdccb))

## [3.37.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.37.0...v3.37.1) (2023-03-21)

### Bug Fixes

- **app js:** client id added to app js ([a1754a1](https://github.com/Datoms-IoT/datoms-webapp/commit/a1754a1313b4cf9c71db56e9f499930305b26e7e))

# [3.37.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.36.7...v3.37.0) (2023-03-21)

### Features

- **generic template:** fuel tank, tanker truck and car view added to generic ([b9fb729](https://github.com/Datoms-IoT/datoms-webapp/commit/b9fb72927051d3f78adfb5ad1a401b2a30123f44))

## [3.36.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.36.6...v3.36.7) (2023-03-17)

### Bug Fixes

- **packagejson:** REACT*APP* prefix added to custom host name env variable ([2586449](https://github.com/Datoms-IoT/datoms-webapp/commit/2586449f3b8a18013b3d66bfa5ce910cd6ee42a7))

## [3.36.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.36.5...v3.36.6) (2023-03-17)

### Bug Fixes

- **dg real time:** dg real time current limit change for care diesel partner ([87f86a8](https://github.com/Datoms-IoT/datoms-webapp/commit/87f86a88f035f9823eee507d8bf9037eea7e72c1))

## [3.36.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.36.4...v3.36.5) (2023-03-17)

### Bug Fixes

- **workflow:** mobile build added for yoyo rides ([3c3d68d](https://github.com/Datoms-IoT/datoms-webapp/commit/3c3d68d98497708b290cbab7bfdaa3b5a64d3853))

## [3.36.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.36.3...v3.36.4) (2023-03-16)

### Bug Fixes

- **aurassure dashboard:** detailed graph loading error fix ([78b244a](https://github.com/Datoms-IoT/datoms-webapp/commit/78b244a60cd4e5ecc61ee3e608dbd2ab5bf7dec7))

## [3.36.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.36.2...v3.36.3) (2023-03-15)

### Bug Fixes

- **alerts-issue:** handled case for rental and oem alert management ([533fa6d](https://github.com/Datoms-IoT/datoms-webapp/commit/533fa6dd9eae959365042c1ddafe8700822b23d8))

## [3.36.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.36.1...v3.36.2) (2023-03-15)

### Bug Fixes

- **fuel filled and drained report:** end fuel key changed for fuel filled and drained report ([14f3a51](https://github.com/Datoms-IoT/datoms-webapp/commit/14f3a512a24a469fab02b8f454d25782ec7d8c5e))
- **fuelfilldrainreport:** end fuel key changed ([266b4e0](https://github.com/Datoms-IoT/datoms-webapp/commit/266b4e004bc80795bcfa71c264d9d2f122167b25))

## [3.36.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.36.0...v3.36.1) (2023-03-15)

### Bug Fixes

- **environment monitoring dashboard:** environment monitoring dashboard graph timeout implemented ([7b97e99](https://github.com/Datoms-IoT/datoms-webapp/commit/7b97e99cec2b334a438b0e9ce6d8164ddd5e0746))

# [3.36.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.35.3...v3.36.0) (2023-03-15)

### Features

- **generic template:** new Filter design for gewneric filter and new filter component ([71cc9a2](https://github.com/Datoms-IoT/datoms-webapp/commit/71cc9a2571c3b39341efa8b8b8d1b11bb2541870))

## [3.35.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.35.2...v3.35.3) (2023-03-14)

### Bug Fixes

- **custom report:** custom reports graph timeout fix for aurassure ([4afd27e](https://github.com/Datoms-IoT/datoms-webapp/commit/4afd27ee1e6d21d333bf27807ef9d860549654b6))

## [3.35.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.35.1...v3.35.2) (2023-03-13)

### Bug Fixes

- **device-wifi-icon:** wifi icon view issue fixed in device list ([483ada1](https://github.com/Datoms-IoT/datoms-webapp/commit/483ada10b16b62227c9171cdff646cae217c638f))
- **user-add-cases:** first user should be admin and to admin always all things access ([c9ce9e3](https://github.com/Datoms-IoT/datoms-webapp/commit/c9ce9e367e1339094b31ca2931f0f04985728b60))

## [3.35.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.35.0...v3.35.1) (2023-03-10)

### Bug Fixes

- **thingmanagement:** bug fixes in system configuration and address selection ([d593f74](https://github.com/Datoms-IoT/datoms-webapp/commit/d593f7499e47d5afe3f79e70bb87fad00047959e))

# [3.35.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.34.1...v3.35.0) (2023-03-10)

### Bug Fixes

- customer id sent to rules api case handled ([23e4a7d](https://github.com/Datoms-IoT/datoms-webapp/commit/23e4a7d7dcadf7f4cc4233c0e685f3a6e2901621))

### Features

- rental things enabled for end customers ([f1e00ab](https://github.com/Datoms-IoT/datoms-webapp/commit/f1e00ab71520afa1e40fd717e59b033648e360fa))

## [3.34.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.34.0...v3.34.1) (2023-03-09)

### Bug Fixes

- **thingmanagement:** fixed - Values are not chaning in scale modal of system configuration ([cd4f3a8](https://github.com/Datoms-IoT/datoms-webapp/commit/cd4f3a88aa6bb642131f366c0bb29a3834e5cd4b))

# [3.34.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.33.2...v3.34.0) (2023-03-09)

### Features

- **thingmanagement:** added Modbus system configuration in thing configuration ([a91a5b8](https://github.com/Datoms-IoT/datoms-webapp/commit/a91a5b80c0a434ce8b2ef8d97678ee38e25a4703))

## [3.33.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.33.1...v3.33.2) (2023-03-09)

### Bug Fixes

- **custom reports:** aqi value attribute fix in custom reports ([efb1b6c](https://github.com/Datoms-IoT/datoms-webapp/commit/efb1b6cc144fb0ba30c7663fb15350780fda7095))

## [3.33.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.33.0...v3.33.1) (2023-03-07)

### Bug Fixes

- **header-logo-style-fix:** company logo style issue fixed ([84f9410](https://github.com/Datoms-IoT/datoms-webapp/commit/84f9410171ec9d5b6c4e0d6c9457528320949f96))

# [3.33.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.32.1...v3.33.0) (2023-03-06)

### Features

- **generic component:** flow meter design added to generic map and panel view ([f5805cf](https://github.com/Datoms-IoT/datoms-webapp/commit/f5805cf42e968a3b638186a89031c2277ad6981f))

## [3.32.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.32.0...v3.32.1) (2023-03-06)

### Bug Fixes

- **customers:** constraints for enabling fuel delivery feature ([c2fb4b8](https://github.com/Datoms-IoT/datoms-webapp/commit/c2fb4b8d410ab3a9913329014980f53670f4d5ff))
- **partner reports:** fuel delivery summary report for datomsx ([02ea1d7](https://github.com/Datoms-IoT/datoms-webapp/commit/02ea1d79b08b00e12149751971d3697143215181))

# [3.32.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.31.1...v3.32.0) (2023-03-03)

### Features

- **thingmanagement:** added Feature to select Address from Map ([ff757d8](https://github.com/Datoms-IoT/datoms-webapp/commit/ff757d87b5e3fd547627fd4e3d0d7266b3fda36e))

## [3.31.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.31.0...v3.31.1) (2023-03-03)

### Bug Fixes

- **reports:** fuelfilldrain reports date select issue fix ([3e2540d](https://github.com/Datoms-IoT/datoms-webapp/commit/3e2540d4757b4dcbbb29ea41a2566a2e146b1196))

# [3.31.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.30.9...v3.31.0) (2023-03-02)

### Features

- **customers and users:** fuel delivery add-ons in customers & new role of fuel delivery in users ([e4a4b8d](https://github.com/Datoms-IoT/datoms-webapp/commit/e4a4b8ddd91b001e963c26bc41a6ad4189d63ffe))

## [3.30.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.30.8...v3.30.9) (2023-03-02)

### Bug Fixes

- **user-setting-loading-issue:** initial loading issue fixed in user settings of alert management ([0634b37](https://github.com/Datoms-IoT/datoms-webapp/commit/0634b374893f0970eb811c06218c65b372fbf3cd))

## [3.30.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.30.7...v3.30.8) (2023-03-02)

### Bug Fixes

- **thingconfiguration:** fixed - Thing config is not loading in customer managemnt for rental things ([3244da0](https://github.com/Datoms-IoT/datoms-webapp/commit/3244da01f82ca2645240ab13eae1a5d8a60ee198))

## [3.30.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.30.6...v3.30.7) (2023-03-01)

### Bug Fixes

- **thinglist:** fixed - wrong thing configuration link from thing list in customer management ([b36172a](https://github.com/Datoms-IoT/datoms-webapp/commit/b36172a70d005e083020f963d19dfb0c8b49e3d1))

## [3.30.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.30.5...v3.30.6) (2023-03-01)

### Bug Fixes

- **views:** fueltank fuelfill reports routes added in iot views ([d4998d7](https://github.com/Datoms-IoT/datoms-webapp/commit/d4998d7884c436dbaf6306a7a6cad51c4e598083))

## [3.30.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.30.4...v3.30.5) (2023-02-28)

### Bug Fixes

- **reports:** fuel drained removed from fuel tank reports ([9908b30](https://github.com/Datoms-IoT/datoms-webapp/commit/9908b30237daf9f131dedf04fcca3e856826c8c8))

## [3.30.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.30.3...v3.30.4) (2023-02-28)

### Bug Fixes

- **thinglist:** fixed - thing list page breaking in mobile and changed thing category icon ([bc63892](https://github.com/Datoms-IoT/datoms-webapp/commit/bc6389213277a5a1f3fec5fbd2f88932d5989a14))

## [3.30.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.30.2...v3.30.3) (2023-02-27)

### Bug Fixes

- **time format:** users, things, notification page time formatting ([07a8c14](https://github.com/Datoms-IoT/datoms-webapp/commit/07a8c14cefe7a51c14f8120c676599a64a782247))

## [3.30.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.30.1...v3.30.2) (2023-02-27)

### Bug Fixes

- **reactcomponents:** added ImageComponent for Thing Category Icons ([1491e89](https://github.com/Datoms-IoT/datoms-webapp/commit/1491e899331b77fb6a06d954aacb8dcc1142b50e))

## [3.30.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.30.0...v3.30.1) (2023-02-25)

# [3.30.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.19...v3.30.0) (2023-02-24)

### Features

- **customers:** client time format preference ([56e5815](https://github.com/Datoms-IoT/datoms-webapp/commit/56e58159da57ad3433ec1437e8efbb47f57d8345))
- **dg views:** time format in DG portal ([f362db1](https://github.com/Datoms-IoT/datoms-webapp/commit/f362db1f7a77d82bee2cc70ee425134b8b3aabd8))

## [3.29.19](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.18...v3.29.19) (2023-02-24)

### Bug Fixes

- **pollutionmonitoring:** added option to switch stations in pollution monitoring dashboard mobile ([0c81832](https://github.com/Datoms-IoT/datoms-webapp/commit/0c818324fd4f9cecbfd22a51e87cf76ab73ad914))

## [3.29.18](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.17...v3.29.18) (2023-02-24)

### Bug Fixes

- **dg events api calling:** events Api called for selected thing in DG and Generic View ([4ee5ef3](https://github.com/Datoms-IoT/datoms-webapp/commit/4ee5ef3c8b30f41a605bd3f4892a6abb0dca4c2e))

## [3.29.17](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.16...v3.29.17) (2023-02-23)

### Bug Fixes

- **dg views & reports:** dg views & Reports task api calling reverted ([c4dc8b5](https://github.com/Datoms-IoT/datoms-webapp/commit/c4dc8b56e29d709d59bdb7efc8975ef047f5ffef))

## [3.29.16](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.15...v3.29.16) (2023-02-23)

### Bug Fixes

- **thingconfiguration:** fixed - Partner Name is not coming in thing configuration for rental things ([28fd3b9](https://github.com/Datoms-IoT/datoms-webapp/commit/28fd3b9b5aa74f7e788b968d8841cb4af513857d))

## [3.29.15](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.14...v3.29.15) (2023-02-22)

### Bug Fixes

- **billingmanagement:** fixed UI issues,Added option to edit startdate before invoice generatation ([c79819d](https://github.com/Datoms-IoT/datoms-webapp/commit/c79819dcd4a57cd29669a6ccb4492333438a5051))

## [3.29.14](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.13...v3.29.14) (2023-02-22)

### Bug Fixes

- **dg detailed view:** dg detailed view todays data aggregation period change ([58af464](https://github.com/Datoms-IoT/datoms-webapp/commit/58af464756571237c9ab22bedd69156d3e69a354))
- **dg reports and pages:** trip , fault and events api logic change as per rental things ([ba92fdc](https://github.com/Datoms-IoT/datoms-webapp/commit/ba92fdcc79735239eeed572cb583b6c79ee78183))

## [3.29.13](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.12...v3.29.13) (2023-02-21)

### Bug Fixes

- **dg dashboard and detailed view:** dG monitoring Dashboard and Detailed view code optimization ([efb34d7](https://github.com/Datoms-IoT/datoms-webapp/commit/efb34d78aa18fc8003405443bf2522ee190a1d95))

## [3.29.12](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.11...v3.29.12) (2023-02-21)

### Bug Fixes

- **thingmanagement:** changed dashboard url of rental things in iot-platform ([7d3cd64](https://github.com/Datoms-IoT/datoms-webapp/commit/7d3cd644744274b0401f852b3fc3b21b0a0987e3))

## [3.29.11](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.10...v3.29.11) (2023-02-16)

### Bug Fixes

- **electrical machines realtime:** electrical machines real-time page power limit changed to 1000 ([ce7e0cb](https://github.com/Datoms-IoT/datoms-webapp/commit/ce7e0cb2ca10d80f7af9d1e865f69a1710090527))

## [3.29.10](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.9...v3.29.10) (2023-02-16)

### Bug Fixes

- **maintenance alerts:** ticket generation type added in rule actions while maintenance addition ([1b5ddde](https://github.com/Datoms-IoT/datoms-webapp/commit/1b5ddde7c8f87ec86c898aee25620c92a818812c))

## [3.29.9](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.8...v3.29.9) (2023-02-15)

### Bug Fixes

- **rental machine alerts:** client id and activity filter issue fixed in things alerts ([7555da6](https://github.com/Datoms-IoT/datoms-webapp/commit/7555da60cb02771eaccac326fe37fc16be15ae38))

## [3.29.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.7...v3.29.8) (2023-02-15)

## [3.29.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.6...v3.29.7) (2023-02-14)

## [3.29.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.5...v3.29.6) (2023-02-14)

### Bug Fixes

- **generic app:** flow meter added to Generic App ([1c8b49a](https://github.com/Datoms-IoT/datoms-webapp/commit/1c8b49a432f32f4cb0d3578a5791fbaf4d5c0100))

## [3.29.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.4...v3.29.5) (2023-02-14)

### Bug Fixes

- **deploy:** fixed issue with deploy ([0e29db1](https://github.com/Datoms-IoT/datoms-webapp/commit/0e29db18dff22659b5f696cd6cafea6313681132))

## [3.29.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.3...v3.29.4) (2023-02-14)

## [3.29.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.2...v3.29.3) (2023-02-14)

## [3.29.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.1...v3.29.2) (2023-02-14)

## [3.29.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.29.0...v3.29.1) (2023-02-14)

# [3.29.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.28.8...v3.29.0) (2023-02-14)

### Features

- **thingconfiguration:** added Rajasthan in Protocol Options for SPCB Data push Configuration ([f581766](https://github.com/Datoms-IoT/datoms-webapp/commit/f581766af7f916208ebfbdf296c61b65d91de327))

## [3.28.8](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.28.7...v3.28.8) (2023-02-14)

## [3.28.7](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.28.6...v3.28.7) (2023-02-14)

## [3.28.6](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.28.5...v3.28.6) (2023-02-14)

## [3.28.5](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.28.4...v3.28.5) (2023-02-14)

## [3.28.4](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.28.3...v3.28.4) (2023-02-14)

## [3.28.3](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.28.2...v3.28.3) (2023-02-14)

## [3.28.2](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.28.1...v3.28.2) (2023-02-14)

## [3.28.1](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.28.0...v3.28.1) (2023-02-14)

### Bug Fixes

- **release:** fixed issue with release ([fa96f1b](https://github.com/Datoms-IoT/datoms-webapp/commit/fa96f1bac9eaeadf499894d5c2847d5f02612600))

# [3.28.0](https://github.com/Datoms-IoT/datoms-webapp/compare/v3.27.1...v3.28.0) (2023-02-13)

### Features

- **test:** testing release ([9cfce5b](https://github.com/Datoms-IoT/datoms-webapp/commit/9cfce5b28bd02bbba21b8ac6f4ad71177c1a61f0))

## v3.27.2 (13-02-2023)

### Bugfix

1. Iot views customer list not updating - bugfix

## v3.27.1 (10-02-2023)

### Feature

1. Dg mapview changes.

## v3.27.0 (10-02-2023)

### Feature

1. Environment monitoring dashboard in iot views.
2. Iot views enabled for rental partners in datoms-x.
3. Customer and Things quick add.

## v3.26.0 (10-02-2023)

### Feature

1. Enable rental partners to assign rental things without order to customers.

## v3.25.2 (10-02-2023)

### Feature

1. Panel filter fix.

## v3.25.1 (10-02-2023)

### Feature

1. Swiggy application changes.

## v3.25.0 (09-02-2023)

### Feature

1. Help link added in header for sales demo partner.

## v3.24.10 (08-02-2023)

### Hotfix

1. Added thing-details page for generic app.
2. modified thing icon style in thing list

## v3.24.9 (08-02-2023)

### Hotfix

1. Site Dashboard calculation changes.

## v3.24.8 (08-02-2023)

### Hotfix

1. logged_in_user_role_type props sent to user management of environment.

## v3.24.7 (07-02-2023)

### Hotfix

1. AQI scale and color changed for non-aqi parameters.

## v3.24.6 (07-02-2023)

### Hotfix

1. Changed General Form Json in thing configuration for iot-platform and
2. subscription bug fixes
3. Removed Subscription Date editing feature

## v3.24.5 (07-02-2023)

### Hotfix

1. Work orders page bugfix.

## v3.24.4 (06-02-2023)

### Hotfix

1. Replace string added in index for error tracking.

## v3.24.3 (06-02-2023)

### Hotfix

1. Code replaced for error tracking.

## v3.24.2 (06-02-2023)

### Hotfix

1. Fixed - Customer's Vendor Name not coming in thing addition
2. removed script for error tracking code insertion

## v3.24.1 (06-02-2023)

### Hotfix

1. Code replaced for error tracking.

## v3.24.0 (06-02-2023)

### Feature

1. Error tracking code added in index.js only for production.

## v3.23.8 (06-02-2023)

### Hotfix

1. Replaced Customer list api with lite version in thing management.

## v3.23.7 (04-02-2023)

### Hotfix

1. Territory page bugfix.

## v3.23.6 (03-02-2023)

### Hotfix

1. Environment monitoring pc parameters changes.

## v3.23.5 (03-02-2023)

### Hotfix

1. Thing Details page optimization.

## v3.23.4 (03-02-2023)

### Hotfix

1. Delivery tracking beacon category final deployment.

## v3.23.3 (02-02-2023)

### Hotfix

1. Panel View loading in summary data section and fault section.

## v3.23.2 (02-02-2023)

### Hotfix

1. Fixed - Unable to create Customer in some cases

## v3.23.1 (02-02-2023)

### Hotfix

1. Logo change for swiggy.

## v3.23.0 (01-02-2023)

### Hotfix

1. Map view, Panel View and DG Dashboard added for swiggy.

## v3.22.3 (31-01-2023)

### Hotfix

1. Energy parameter key change in site dashboard.

## v3.22.2 (30-01-2023)

### Hotfix

1. Site Dashboard fixes.

## v3.22.1 (30-01-2023)

### Hotfix

1. Site things from id fixed.

## v3.22.0 (30-01-2023)

### Hotfix

1. Dashboard and work order for swiggy.

## v3.21.9 (27-01-2023)

### Hotfix

1. Warehouse view change for 80 category.

## v3.21.8 (27-01-2023)

### Hotfix

1. Warehouse things added for 69 and 80 category.

## v3.21.7 (26-01-2023)

### Hotfix

1. Package.json issue fixed.

## v3.21.6 (25-01-2023)

### Hotfix

1. calculation fixed for trip report energy generation.
   > > > > > > > CHANGELOG.md

## v3.21.5 (25-01-2023)

### Hotfix

1. Subscription Management Improvements & bugfixes.

## v3.21.4 (24-01-2023)

### Hotfix

1. Fixed - Pollution Dashboard not opening in some cases.

## v3.21.3 (24-01-2023)

### Hotfix

1. Subscription Management Improvements & bugfixes.

## v3.21.2 (23-01-2023)

### Hotfix

1. Warehouse zoom in graph not coming issue fixed.

## v3.21.1 (20-01-2023)

### Hotfix

1. Subscription Management bugfixes.

## v3.21.0 (19-01-2023)

### Feature

1. Rental Alpha Changes.

## v3.20.0 (18-01-2023)

### Feature

1. Added Subscription management.

## v3.19.3 (13-01-2023)

### Hotfix

1. `FuelFillDrain` report made configurable.
2. `FuelFillDrain` report made available for `Fuel Tank` thing type.
3. `Location` column added in `FuelFillDrain` report.

## v3.19.2 (12-01-2023)

### Feature

1. Fridge parameters to be updated.

## v3.19.1 (11-01-2023)

### Feature

1. Fridge in panel implemented.

## v3.19.0 (11-01-2023)

### Feature

1. Component added to open google maps for a thing's location.
2. Open in maps feature added in panel view of map.

## v3.18.2 (11-01-2023)

### Bugfix

1. Fridge availble in warehouse.

## v3.18.1 (06-01-2023)

### Bugfix

1. Rental E-bike config type id updated.

## v3.18.0 (05-01-2023)

### Feature

1. Back button position changed in mobile app login page.

## v3.17.5 (05-01-2023)

### BugFix

1. Environment monitoring optimization.
2. Maintenance rnable in panel.

## v3.17.4 (05-01-2023)

### BugFix

1. User addition error handle in customer addition.

## v3.17.3 (03-01-2023)

### BugFix

1. Page blank issue fixed.

## v3.17.2 (03-01-2023)

### BugFix

1. Back icon changed for login page.

## v3.17.1 (03-01-2023)

### BugFix

1. (Thing Addition) Added thing type automatic selection for rental partners if only one thing type is present.

## v3.17.0 (03-01-2023)

### Features

1. Back button added in mobile app login page.
2. Shown text in user profile and on edit input box shown.

## v3.16.2 (24-12-2022)

### Bugfix

1. Rental things dashboard link from things page enabled.

## v3.16.1 (23-12-2022)

### Feature

1. Fuel dispenser mobile view.

## v3.16.0 (23-12-2022)

### Feature

1. User profile email and mobile edit flow added.

## v3.15.15 (22-12-2022)

### Hotfix

1. Service module mobile design.
2. Service module partner filter in datoms-x

## v3.15.14 (22-12-2022)

### Hotfix

1. Fixed - machine info report bugs.

## v3.15.13 (21-12-2022)

### Hotfix

1. Views enabled even if no customer for a rental partner.

## v3.15.12 (21-12-2022)

### Hotfix

1. Unit added to fuel dispenser.

## v3.15.11 (21-12-2022)

### Hotfix

1. User edit mobile number + case handled.

## v3.15.10 (21-12-2022)

### Hotfix

1. Removed pre alerts count from service module.

## v3.15.9 (20-12-2022)

### Hotfix

1. Fixed - IP camera isn't loading in some cases.

## v3.15.8 (20-12-2022)

### Hotfix

1. Alerts User setting things filter logic updated.

## v3.15.7 (20-12-2022)

### Bugfix

1. Reports Header DATOMS removed.

## v3.15.6 (20-12-2022)

### Bugfix

1. fixed - pollution monitoring dashboard is not opening in some cases
2. Added Ip camera machineinfo from formcontroller

## v3.15.5 (20-12-2022)

### Bugfix

1. Electrical machines current phase values updated.

## v3.15.4 (20-12-2022)

### Bugfix

1. Service module style fix.

## v3.15.3 (20-12-2022)

### Hotfix

1. Maintenance alerts user settings changes.

## v3.15.2 (19-12-2022)

### Hotfix

1. fixed - Machine Info Report bugs.

## v3.15.1 (19-12-2022)

### Hotfix

1. Service module enabled for datomsx.
2. Pagination added in notifications(main) page.

## v3.15.0 (19-12-2022)

### Feature

1. `Machine info report` added for rental customers.
2. fixed - show ComplianceBanner only in pollution customer portal
3. Updated Js-Sdk

## v3.14.1 (16-12-2022)

### BugFix

1. Fuel dispencer employee id added.

## v3.14.0 (16-12-2022)

### Feature

1. `Service Module` added.

## v3.13.0 (16-12-2022)

### Feature

1. Pollution monitoring workflow details added.

## v3.12.2 (16-12-2022)

### Bug fix

1. Environment monitoring graoh fixes.

## v3.12.1 (15-12-2022)

### Bug fix

1. Thing Management changes for Rental alpha release.

## v3.12.0 (14-12-2022)

### Feature

1. Tracking id and employee id fields added in user add/edit.

## v3.11.1 (12-12-2022)

### Feature

1. Fuel dispenser parameters changed.

## v3.11.0 (09-12-2022)

### Feature

1. Delivery tracking added to datoms.

## v3.10.0 (08-12-2022)

### Feature

1. Added Thing Details and User Details Page for web.

## v3.9.6 (07-12-2022)

### Hotfix

1. Asset Lock button removed when order is not started or completed.

## v3.9.5 (06-12-2022)

### Hotfix

1. Electrical Machines added in Views pages.
2. Rental details graph point-width decreased.

## v3.9.4 (06-12-2022)

### Hotfix

1. Added DLC1001 & Sozi (RS485) fuel sensor name options (thing management)

## v3.9.3 (05-12-2022)

### Hotfix

1. Electrical machines map view added.

## v3.9.2 (05-12-2022)

### Hotfix

1. Fixed - iOS styling issues.

## v3.9.1 (02-12-2022)

### Hotfix

1. Rental improvements.

## v3.9.0 (02-12-2022)

### Feature

1. Removed console logs from production.

## v3.8.0 (01-12-2022)

### Feature

1. Custom report changes.

## v3.7.0 (12-01-2022)

### Feature

1. Zoho customer link in datoms-x.

## v3.6.1 (30-11-2022)

### Bugfix

1. Hide add new maintenance button when thing is empty.

## v3.6.0 (30-11-2022)

### Feature

1. New login structure for mobile app login page.

## v3.5.2 (30-11-2022)

### Bugfix

1. Delete confirm for maintenance alerts.

## v3.5.1 (29-11-2022)

### Bugfix

1. yml reverted.

## v3.5.0 (29-11-2022)

### Feature

1. Maintenance alerts basic version added.
2. Fuel Fill and Fuel Drain columns added in daily reports.

## v3.4.1 (28-11-2022)

### Bugfix

1. android-targetSdkVersion updated to 31.

## v3.4.0 (28-11-2022)

### Feature

1. Mobile app login page new flow added.

## v3.3.5 (23-11-2022)

### Bugfix

1. Electrical Machines implemented.

## v3.3.4 (23-11-2022)

### Bugfix

1. DG run report download issue fixed.

## v3.3.3 (23-11-2022)

### Bugfix

1. Realtime tab selection issue fixed.

## v3.3.2 (23-11-2022)

### Bugfix

1. Mobile and email fields made optional in user addition of customer addition.
2. Partner customer addition from datoms-x partner list empty sometimes - fixed.
3. Two things removed in warehouse.
4. Rental kpi removed from things list page of rental partner.

## v3.3.1 (18-11-2022)

### Bugfix

1. Device assigned/unassigned list table column width adjusted.

## v3.3.0 (18-11-2022)

### Features

1. Resend verification for email and mobile separated.
2. Either mandatory field added for email and mobile in user add/edit.
3. Mobile number changed to whatsapp number.
4. Mobile number sent as string to api instead of array in user add/edit and customer add.

## v3.2.1 (17-11-2022)

### Bugfix

1. Removed lazy loading for Structure.

## v3.2.0 (17-11-2022)

### Feature

1. Created date column added in assigned/unassigned device list.

## v3.1.0 (17-11-2022)

### Features

1. Rental made compatible for E-Bike.
2. Bug fixes (thing management)

## v3.0.0 (16-11-2022)

### Feature

1. Monorepo implemented.

## v2.7.17 (15-11-2022)

### BugFix

1. Panel style changes.

## v2.7.16 (14-11-2022)

### BugFix

1. Site todays energy factor calculation change.

## v2.7.15 (11-11-2022)

### BugFix

1. Site Socket optimized.

## v2.7.14 (10-11-2022)

### BugFix

1. Site features changes.

## v2.7.13 (09-11-2022)

### BugFix

1. Site Average value na fixed.

## v2.7.12 (09-11-2022)

### BugFix

1. reverted yarn.lock

## v2.7.11 (09-11-2022)

### BugFix

1. yarn updated.

## v2.7.10 (09-11-2022)

### BugFix

1. Site panel view added.

## v2.7.9 (07-11-2022)

### BugFix

1. Reverted highcharts and js-pdf versions

## v2.7.8 (04-11-2022)

### BugFix

1. Updated packages and removed unused imports

## v2.7.7 (03-11-2022)

### BugFixes

1. Configuration of reports params - Daily & Dg Run Reports (JN Machineries)
2. `FuelFillDrainReport` enabled for all customers(except Mahindra).

## v2.7.6 (02-11-2022)

### BugFix

1. Updated Thing management Version.

## v2.7.5 (01-11-2022)

### BugFix

1. Graph large to small point ratio fixed in warehouse.

## v2.7.4 (01-11-2022)

### BugFixes

1. Rental updated(manual runhour addition).
2. Alerts, users, iot & layout updated(bugfixes).

## v2.7.3 (01-11-2022)

### BugFix

1. Site secton style fixing.

## v2.7.2 (31-10-2022)

### BugFix

1. Site secton added to warehouse.

## v2.7.1 (28-10-2022)

### BugFix

1. Site warehouse style fixes.

## v2.7.0 (27-10-2022)

### Feature

1. Site warehouse implemented.

## v2.6.0 (27-10-2022)

### Feature

1. Non iot things.

## v2.5.10 (27-10-2022)

### BugFix

1. Updated Reports Version.
2. Pollution Monitoring Bug fixes.
3. Added New Reports Route for client id 1748

## v2.5.9 (21-10-2022)

### BugFixes

1. Rental, Users, Alerts, Things, DG views and Iot library updated.
2. FuelFillDrainReport routes added.

## v2.5.8 (20-10-2022)

### Bugfix

1. DG views and iot library updated.

## v2.5.7 (20-10-2022)

### Bugfix

1. Tanker truck daily reports routes added.

## v2.5.6 (19-10-2022)

### Bugfix

1. gitlab.yml file pipelines updated.

## v2.5.5 (19-10-2022)

### Bugfix

1. Thing management version updated (thing add issue fixed).

## v2.5.4 (14-10-2022)

### Bugfix

1. yml file updated - environment removed from production.

## v2.5.3 (14-10-2022)

### Bugfix

1. DG and Iot library updated.

## v2.5.2 (14-10-2022)

### Bugfix

1. Enabled Thing Management for DG.

## v2.5.1 (13-10-2022)

### Feature

1. DG and Iot library updated.

## v2.5.0 (13-10-2022)

### Feature

1. Energy meter routes added.

## v2.4.4 (11-10-2022)

### Bugfixes

1. Notification added in menu.
2. Static file redirection update in config-overrides.

## v2.4.3 (11-10-2022)

### Bugfix

1. Users, layout, rental, alerts & iot views updated (bugfix and improvements).

## v2.4.2 (11-10-2022)

### Bugfix

1. Aurassure user-management routing fix.

## v2.4.1 (10-10-2022)

### Bugfix

1. Users & alerts updated (single app improvements/fixes).
2. layout updated (aurassure header logo for mobile app).

## v2.4.0 (10-10-2022)

### Features

1. Partner customer addition from datoms-x.
2. Customer type edit.
3. Customer single app improvements/fixes.
4. Things, users, alerts, device, rental updated.(single app improvements/fixes).
5. Desktop menu order made similar to mobile.

## v2.3.3 (07-10-2022)

### Feature

1. Logout and change password url change.

## v2.3.2 (07-10-2022)

### Feature

1. Logout and change password url change.

## v2.3.1 (06-10-2022)

### Feature

1. Logout and change password url change.

## v2.3.0 (06-10-2022)

### Feature

1. Environment monitoring onboarding in mobile.

## v2.2.14 (29-09-2022)

### Feature

1. DG and Iot library updated.

## v2.2.13 (28-09-2022)

### Feature

1. DG and Iot library updated.

## v2.2.12 (28-09-2022)

### Feature

1. DG and Iot library updated.

## v2.2.11 (28-09-2022)

### Feature

1. DG and Iot library updated.

## v2.2.10 (26-09-2022)

### Feature

1. DG and Iot library updated.

## v2.2.9 (26-09-2022)

### Feature

1. DG and Iot library updated.

## v2.2.8 (20-09-2022)

### Feature

1. DG, layout and Iot library updated.

## v2.2.6 (20-09-2022)

### Feature

1. DG, alerts and Iot library updated.

## v2.2.6 (20-09-2022)

### Feature

1. DG and Iot library updated.

## v2.2.5 (20-09-2022)

### Bugfix

1. Layout version updated.

## v2.2.4 (19-09-2022)

### Features

1. Alert management version updated.

## v2.2.3 (19-09-2022)

### Features

1. Alert management version updated.

## v2.2.2 (19-09-2022)

### Features

1. Alert management version updated.

## v2.2.1 (19-09-2022)

### Features

1. Ev Style Fixes.

## v2.2.0 (16-09-2022)

### Features

1. Ev implemented.

## v2.1.0 (16-09-2022)

### Features

1. Alert subscription reports added for partner & end customers.
2. Whatsapp & sms add-ons added in customer addition/edit for datoms-X.
3. Single app bug fixes.

## v2.0.2 (12-09-2022)

### Bugfix

1. Single App Bug Fixes.
2. Iot views, Thing, User, React Components & Js Utils updated.

## v2.0.1 (12-09-2022)

### Bugfix

1. Dashboard and reports clubbing in desktop.
2. IoT views updated. (Dashboard and reports clubbing in desktop)
3. Thing management updated. (bugfix)
4. Layout updated. (menu selection issue when submenu is present at the top)

## v2.0.0 (09-09-2022)

### Feature

1. Datoms Single App.

## v1.43.10 (07-09-2022)

### Bugfix

1. White labelling default page title removed.

## v1.43.9 (06-09-2022)

### Bugfix

1. White labelling for page title.

## v1.43.8 (30-08-2022)

### Bugfix

1. Pollution monitoring reports stations as per user access.

## v1.43.7 (26-08-2022)

### Bugfix

1. Updated alert management version.

## v1.43.6 (26-08-2022)

### Bugfix

1. Added Alerts for pollution monitoring.

## v1.43.5 (25-08-2022)

### Bugfix

1. Responsive design changes for pollution.
2. Removed fuel buddy for pollution
3. added missing pages

## v1.43.4 (23-08-2022)

### Bugfix

1. Chamber fuel start and end time added.

## v1.43.3 (23-08-2022)

### Bugfix

1.Added first thing type as app view decider for pollution.

## v1.43.2 (23-08-2022)

### Bugfix

1. Removed hard-coded camera details.

## v1.43.1 (23-08-2022)

### Bugfix

1. Bug fixes & Changed Routes from pollution to dg.

## v1.43.0 (23-08-2022)

### Feature

1. On boarded pollution into dg-monitoring.

## v1.42.9 (16-08-2022)

### Bugfix

1. Improvise daily reports with load calculation from trip and event api changes.

## v1.42.8 (11-08-2022)

### Bugfix

1. Compressor style fixes.

## v1.42.7 (09-08-2022)

### Bugfix

1. Thing management updated.

## v1.42.6 (09-08-2022)

### Feature

1. GMMNCO parameters range and limit changes in real time page.

## v1.42.5 (04-08-2022)

### Feature

1. Tanker truck trip details added.

## v1.42.4 (04-08-2022)

### Feature

1. getActivityDataFunction removed from map-view.

## v1.42.3 (04-08-2022)

### Feature

1. Map view removed from mahindra customer.

## v1.42.2 (03-08-2022)

### Feature

1. Menu options rearranged and detailed view calculated energy graph implemented.

## v1.42.1 (01-08-2022)

### Feature

1. Tanker truck current volume implemented and Mobile view and icon changed for map view.

## v1.42.0 (01-08-2022)

### Feature

1. Dg monitoring Map View implemented -> Dg sets, compressor, fuel tank.

## v1.41.0 (01-08-2022)

### Feature

1. Dg monitoring made as a library.
2. Unwanted/conflicting styles removed.

## v1.40.11 (29-07-2022)

### Bugfix

1. Fuel tank landing page change.

## v1.40.10 (28-07-2022)

### Bugfix

1. Fleet UI changes.

## v1.40.9 (27-07-2022)

### Bugfix

1. Rental event removed from activity & violation.

## v1.40.8 (26-07-2022)

### Bugfix

1. Trip report not last two columns not coming issue fix.

## v1.40.7 (22-07-2022)

### Bugfix

1. switch change by default first tab.

## v1.40.6 (22-07-2022)

### Bugfix

1. Fixed - android app sometimes struck on splashscreen if login page is not loaded.

## v1.40.5 (21-07-2022)

### Bugfix

1. Fleet monitoring change done.

## v1.40.4 (21-07-2022)

### Bugfix

1. fixed - dashboard not loading after login and logout in dg ap

## v1.40.3 (20-07-2022)

### Bugfix

1. Tanker truck panel design fix..

## v1.40.2 (19-07-2022)

### Bugfix

1. Package max space size updated.

## v1.40.1 (19-07-2022)

### Bugfix

1. Panel design fix for detailed view.

## v1.40.0 (18-07-2022)

### Bugfix

1. Tanker truck feature added.

## v1.39.2 (15-07-2022)

### Bugfix

1. added code splitting for different routes.

## v1.39.0 (06-07-2022)

### Feature

1. Mechanical DG reports updated.

## v1.39.0 (06-07-2022)

### Feature

1. Fleet monitoring daily and trip report added.

## v1.38.11 (29-06-2022)

### Bugfix

1. Updated layout,utils,sdk and thing management.

## v1.38.10 (29-06-2022)

### Bugfix

1. Calculation change for fuel and energy composite parameters.

## v1.38.9 (29-06-2022)

### Bugfix

1. Control Button Bugfix(components library updated).

## v1.38.8 (28-06-2022)

### Bugfix

1. Changes for compressor.

## v1.38.7 (27-06-2022)

### Bugfix

1. Report module updated.

## v1.38.6 (24-06-2022)

### Bugfix

1. Report tab selection issue fixed.

## v1.38.5 (22-06-2022)

### Bugfix

1. Updated layout,thing and user management.

## v1.38.4 (22-06-2022)

### Bugfix

1. Compressor detailed view runhour data fixed.

## v1.38.3 (21-06-2022)

### Bugfix

1. Total fuel used added to compressor detailed view.

## v1.38.2 (21-06-2022)

### Bugfix

1. Mobile app version updated.

## v1.38.1 (21-06-2022)

### Bugfix

1. Updated layout version (fixed - re-login issue for mobile app).

## v1.38.0 (17-06-2022)

### Feature

1. OJUS conditions added to DG MONITORING.

## v1.37.0 (16-06-2022)

### Feature

1. Fleet Routes added to dg monitoring.

## v1.36.4 (15-06-2022)

### BugFix

1. Updated thing management version (Added machine info form JSON fetching from backend.)

## v1.36.3 (14-06-2022)

### BugFix

1. Updated thing management version (Added machine info template selection option for predefined templates)

## v1.36.2 (11-06-2022)

### BugFix

1. Real time engine view dynamic.

## v1.36.1 (11-06-2022)

### BugFix

1. Dashboard KPI name changed for compressor.

## v1.36.0 (10-06-2022)

### Feature

1. Compressor added to dg monitoring.

## v1.35.4 (09-06-2022)

### Bugfix

1. Panel view trip calculated runhour not coming case handled.

## v1.35.3 (08-06-2022)

### Bugfix

1. Updated thing management (fixed new things flow bugs).

## v1.35.2 (08-06-2022)

### Feature

1. Fuel tank composite parameters graph removed from dashboard.

## v1.35.1 (08-06-2022)

### Feature

1. Real time view fixed for mahindra for mobile deployment.

## v1.35.0 (03-06-2022)

### Feature

1. Plan feature added.

## v1.34.1 (02-06-2022)

### Feature

1. Operational mode condition checked.

## v1.34.0 (02-06-2022)

### Feature

1. Gemmnco changes.

## v1.33.3 (02-06-2022)

### Bugfix

1. Hide summary reports for Mahindra.

## v1.33.2 (01-06-2022)

### Bugfix

1. Daily report summary fuel drain fix.

## v1.33.1 (26-05-2022)

### Feature

1. initial parameter attribute added .

## v1.33.0 (26-05-2022)

### Feature

1. Summary and fuel tank report added.

## v1.32.1 (12-05-2022)

### Bugfix

1. Few optimization to reduce api calling.

## v1.32.0 (10-05-2022)

### Bugfix

1. Fuel tank feature updated.

## v1.31.14 (05-05-2022)

### Bugfix

1. Updated layout version (Changed document title from layout).

## v1.31.13 (04-05-2022)

### Bugfix

1. Updated layout version (Changed document title from layout).

## v1.31.12 (03-05-2022)

### Bugfix

1. Updated layout version (Changed tagline for ojus login page in mobile app).

## v1.31.11 (03-05-2022)

### Bugfix

1. Updated layout version (Changed tagline for ojus login page in mobile app).

## v1.31.10 (29-04-2022)

### Bugfix

1. Updated layout version (Changed webapp icon size for white label).
2. Title icon change.

## v1.31.9 (29-04-2022)

### Bugfix

1. Updated layout version (Changed background color for ojus and added forgot password link to open in browser).

## v1.31.8 (28-04-2022)

### Bugfix

1. Updated layout version (Change position for white label icon).

## v1.31.7 (28-04-2022)

### Bugfix

1. Updated layout version (changed API key for ojus app).

## v1.31.6 (27-04-2022)

### Bugfix

1. Ttile logo change.

## v1.31.5 (26-04-2022)

### Bugfix

1. Updated layout version (removed welcome page for ojus).

## v1.31.4 (26-04-2022)

### Bugfix

1. Updated layout version (showing old version on logout page).

## v1.31.3 (26-04-2022)

### Bugfix

1. Reports datoms header changes as per white labeling .

## v1.31.2 (26-04-2022)

### Bugfix

1. Updated layout version .

## v1.31.1 (26-04-2022)

### Bugfix

1. White labeling logout and change password link changed.

## v1.31.0 (21-04-2022)

### Bugfix

1. White labeling to remove datoms logo.
2. Add vendor name instead of Datoms in reports.

## v1.30.9 (15-04-2022)

### Bugfix

1. Make name corrected in thing details section.
2. Thing & Device management version updated.

## v1.30.8 (12-04-2022)

### Bugfix

1. Detailed view graph date change issue fix.

## v1.30.7 (11-04-2022)

### Bugfix

1. Added getvalidname function for predefined reports.
2. moving dg fixes

## v1.30.6 (04-04-2022)

### Bugfix

1. Energy unit condition added for no 'enrg' key.

## v1.30.5 (01-04-2022)

### Bugfix

1. window.getInputDetails function returns empty object.

## v1.30.4 (31-03-2022)

### Bugfix

1. Condition updated for summary report.

## v1.30.3 (31-03-2022)

### Bugfix

1. Added callback after generating report.

## v1.30.2 (31-03-2022)

### Bugfix

1. Summary report page going blank for some customers

## v1.30.1 (31-03-2022)

### Bugfix

1. is_moving condition changed

## v1.30.0 (30-03-2022)

### Features

1. Added new Summary Report

## v1.29.2 (30-03-2022)

### Bugfix

1. Lng changed to Long in data api value response.

## v1.29.1 (28-03-2022)

### Features

1. Auto test implemented.
2. Reports updated for custom options.

## v1.29.0 (25-03-2022)

### Features

1. Functionality auto test implemented.
2. Custom report generation time optimized and report view detailed table order changed to descending.

## v1.28.2 (23-03-2022)

### Bugfix

1. lat/long condition changed.

## v1.28.1 (23-03-2022)

### Bugfix

1. DG pointer removed from map if lat/long value is `-1` .

## v1.28.0 (22-03-2022)

### Features

1. Detailed view today's section changed for starsight.

## v1.27.3 (21-03-2022)

### Features

1. Usersmanagement updated (table loading made false only after setting table data)

## v1.27.2 (21-03-2022)

### Features

1. Lat/long values taken from parameters if available irrespective of its moving status.
2. DG shown on map only if both lat/long values are valid.

## v1.27.1 (17-03-2022)

### Features

1. Report machine info not changing in mobile issue fixed.

## v1.27.0 (15-03-2022)

### Features

1. Lock feature according to mode.

## v1.26.1 (15-03-2022)

### Bugfix

1. Longitude parameter key changed to `long`.

## v1.26.0 (15-03-2022)

### Features

1. Moving DG Feature Added.

## v1.25.14 (04-03-2022)

### Bugfix

1. User-management updated(Admin option from role selection and all option from things select removed when logged in user is not admin).

## v1.25.13 (24-02-2022)

### Bugfix

1. Updated react-components, layout and notifications.

## v1.25.12 (21-02-2022)

### Bugfix

1. Mobile version updated.

## v1.25.11 (21-02-2022)

### Bugfix

1. Fault report responsive.

## v1.25.10 (16-02-2022)

### Bugfix

1. React component version reverted.

## v1.25.9 (16-02-2022)

### Bugfix

1. Last fault data invalid date issue fix.

## v1.25.8 (16-02-2022)

### Bugfix

1. Fuel buddy issue fix for mobile.
2. Splash screen design fix.

## v1.25.7 (12-02-2022)

### Bugfix

1. End customer access to fuel buddy.
2. Fuel buddy will only show for indian customer.

## v1.25.6 (07-02-2022)

### Bugfix

1.Added fuelbuddy desktop integration

## v1.25.5 (07-02-2022)

### Bugfix

1. Mobile app screenshots updated.

## v1.25.4 (04-02-2022)

### Bugfixes

1. Added close button for fuelbuddy modal.
2. updated reports (removed pdf download option for mobile)

## v1.25.3 (01-02-2022)

### Bugfix

1. Low fuel level fixed fuel buddy.

## v1.25.2 (31-01-2022)

### Bugfixes

1. changed Fuelbuddy flow
2. changed mobile app go to links logic
3. changed map height in mobile
4. update layout version

## v1.25.1 (31-01-2022)

### Bugfix

1. Reports component updated with mobile view fixed.

## v1.25.0 (22-01-2022)

### Feature

1. Integrated fuelbuddy (mobile app) for indian customers.

## v1.24.6 (21-01-2022)

### Bugfix

1. Updated layout and notifications

## v1.24.5 (18-01-2022)

### Bugfixes

1. Report version update.
2. Functionality update for reports form.
3. Reports graph improvized.

## v1.24.4 (17-01-2022)

### Bugfix

1. Added heap analytics for web and mobile.

## v1.24.3 (12-01-2022)

### Bugfix

1. Custom report added to fuel only template.

## v1.24.2 (10-01-2022)

### Bugfixes

1. Realtime link not opening in mobile view fixed.
2. Without Fuel condition for Advanced template restored.
3. Removed heap in local and staging.
4. Without Fuel Condition for Basic Template added.
5. Fuel filled and Fuel drained added for basic templated.

## v1.24.1 (10-01-2022)

### Bugfix

1. Added heap.io integration for ios mobile app

## v1.24.0 (07-01-2022)

### Features

1. Mahindra additional changes.
2. Basic Template added.

## v1.23.1 (05-01-2022)

### Bugfix

1. Removed alert for storage location in android and ios.

## v1.23.0 (03-01-2022)

### Features

1. Email tracking for heap analytics.
2. In mobile app customized splash screen added for mahindra.

## v1.22.0 (03-01-2022)

### Feature

1. Added Reports downloading feature in mobile.

## v1.21.0 (01-01-2022)

### Feature

1. New report component added for custom reports.

## v1.20.3 (30-12-2021)

### Bugfix

1. Maintenance runhour and days value changed.

## v1.20.2 (28-12-2021)

### Bugfix

1. Mains L-L voltage key updated.

## v1.20.1 (24-12-2021)

### Bugfix

1. Heap code enabled.

## v1.20.0 (24-12-2021)

### Features

1. Alert management added.
2. Thing management version updated (Some issues fixed).

## v1.19.1 (23-12-2021)

### Bugfix

1. Custom report form graph view type option texts changed.

## v1.19.0 (23-12-2021)

### Features

1. Custom report multigraph implemented.
2. Custom report pdf graph timezone updated.
3. Energy per fuel NA removed.

## v1.18.4 (21-12-2021)

### Bugfixes

1. Loading added to graph on clicking of list, map and kpi items in Dashboard.
2. Violation option removed from all over the platform.

## v1.18.3 (20-12-2021)

### Bugfix

1. user management and thing management version revert.

## v1.18.2 (20-12-2021)

### Bugfix

1. Added window.heap

## v1.18.1 (20-12-2021)

### Bugfixes

1. Updated thing management and user management version.
2. Mahindra icons update.
3. Reports issue fixed.

## v1.18.0 (20-12-2021)

### Feature

1. Heap analytics implemented.

## v1.17.6 (14-12-2021)

### BugFix

1. All selected parameters added to detailed view.

## v1.17.5 (14-12-2021)

### BugFix

1. Panel view ISO format date handled.

## v1.17.4 (14-12-2021)

### BugFix

1. Parameter name change for real time.
2. maintenance time format chenge.
3. Graph label added with detailed view graphs.

## v1.17.3 (14-12-2021)

### BugFix

1. Deduct runhour from maintenance.

## v1.17.2 (14-12-2021)

### BugFix

1. Detailed view composite graph parameters unit added as prefix.

## v1.17.1 (13-12-2021)

### Feature

1. Real time phase value name changed.

## v1.17.0 (13-12-2021)

### Feature

1. Short names implemented to real time.

### BugFix

1. Maintenance calculation check.
2. Fuel consumption per hour normalized.

## v1.16.0 (11-12-2021)

### Feature

1. Changes for Mahindra.

## v1.15.2 (07-12-2021)

### Bugfix

1. Updated package versions

## v1.15.1 (06-12-2021)

### Bugfix

1. Dashboard List view design break issue fixed.

## v1.15.0 (04-12-2021)

### Features

1. Lock/unlock button added.
2. Mobile App not opening issue fixed.

## v1.14.4 (30-11-2021)

### Bugfix

1. Event socket data retrieval process changed.

## v1.14.3 (30-11-2021)

### Bugfix

1. Detailed view from and upto fuel filled level calculation change.

## v1.14.2 (30-11-2021)

### Bugfix

1. Detailed view from and upto fuel filled level added.

## v1.14.1 (25-11-2021)

### Bugfix

1. Layout library version updated (User notification ui issues fixed)

## v1.14.0 (23-11-2021)

### Feature

1. Added lazy loading for DG web app

## v1.13.0 (23-11-2021)

### Feature

1. Layout library version updated (User notification added)

## v1.12.0 (23-11-2021)

### Feature

1. Only fuel parameter case handled in template 2.

## v1.11.12 (22-11-2021)

### Bugfix

1. Added android target sdk version updated to 30.

## v1.11.11 (18-11-2021)

### Bugfix

1. Served production app from S3 instead of ec2 server.

## v1.11.10 (15-11-2021)

### Bugfix

1. Styling Fix for Dg monitoring.

## v1.11.9 (15-11-2021)

### Bugfixes

1. All datoms libraries updated.
2. latest trip data received time improvized.
3. control button condition added for customer 326.

## v1.11.8 (10-11-2021)

### Bugfix

1. Added manual trigger for publish to npm and publish to npm staging.

## v1.11.7 (10-11-2021)

### Bugfix

1. User management added and rental page removed from mobile app.

## v1.11.6 (10-11-2021)

### Bugfix

1. Real time NA value should be there instead of 0 if parameter key is not there.

## v1.11.5 (09-11-2021)

### Bugfix

1. Real time phase value duplication fixed.

## v1.11.4 (03-11-2021)

### Bugfixes

1. Panel last trip details parameters name on hover.
2. '%' in case of load percentage.
3. Trip view calculation check for energy per fuel.

## v1.11.3 (02-11-2021)

### Bugfixes

1. 0 should reflect for both energy key.
2. critical-trends report download condition changed.
3. getRemoteAccess function added.

## v1.11.2 (01-11-2021)

### Bugfixes

1. DG remote on/off button added for only customer id - 1101.
2. js sdk and user management version updated (user active/deactive api issue fixed).

## v1.11.1 (29-10-2021)

### Bugfix

1. Activity and violation socket details key updated.

## v1.11.0 (28-10-2021)

### Feature

1. Updated flow and design for dg remote on/off button (for only customer id - 1075) for others old flow will show.

## v1.10.6 (25-10-2021)

### Bugfixes

1. Dashboard graph style issue fixed.
2. Fault report sorting issue fixed.
3. Status text updated in filter of panel view.

## v1.10.5 (25-10-2021)

### Bugfix

1. Some fixes regarding infinity elimination and filter.

## v1.10.4 (22-10-2021)

### Bugfix

1. Dashboard status fixed.

## v1.10.3 (21-10-2021)

### Bugfix

1. Some style issues fixed.

## v1.10.2 (21-10-2021)

### Bugfix

1. Publish to npm step disabled.

## v1.10.1 (21-10-2021)

### Bugfixes

1. Some style issues fixed in dashboard and panel view page.
2. Yarn lock file updated.

## v1.10.0 (21-10-2021)

### Feature

1. No fuel parameter case handled.

### Bugfixes

1. Some style and functionality issues fixed in all pages.
2. Some bugs fixed in user and thing management.

## v1.9.6 (19-10-2021)

### Bugfix

1. Updated menu condition in app js.

## v1.9.5 (11-10-2021)

### Bugfixes

1. Rental management version updated (handled no data case for summary page)
2. Page name updated for real time and summary page.

## v1.9.4 (07-10-2021)

### Bugfix

1. Removed condition for rental management in app js.

## v1.9.3 (07-10-2021)

### Bugfix

1. Removed publish to npm step as it is blocking deployment by failing.

## v1.9.2 (06-10-2021)

### Bugfix

1. package-lock updated.

## v1.9.1 (06-10-2021)

### Bugfix

1. Added new task for npm publish for staging.

## v1.9.0 (04-10-2021)

### Feature

1. Rental pages added.

## v1.8.43 (30-09-2021)

### Bugfix

1. npm install, version updated and redeployed

## v1.8.42 (29-09-2021)

### Bugfix

1. Test pipelines removed.

## v1.8.41 (29-09-2021)

### Bugfix

1. Scroll issue fixed for fuel only details view.

## v1.8.40 (23-09-2021)

### Bugfixes

1. Husky installed.
2. Layout library updated.

## v1.8.39 (23-09-2021)

### Bugfixes

1. License file added and huskey added for app version update.
2. Deploy app files pipeline changed to manual trigger.

## v1.8.38 (23-09-2021)

### Bugfix

1. Redeployment for mobile app.

## v1.8.37 (22-09-2021)

### Bugfix

1. xml version update.

## v1.8.36 (21-09-2021)

### Bugfix

1. Remove pagesense from mobile build.

## v1.8.35 (21-09-2021)

### Bugfix

1. Layout package update for logo.

## v1.8.34 (21-09-2021)

### Bugfix

1. Socket implemented with fixes & auto-updated version in mobile build.

## v1.8.33 (20-09-2021)

### Bugfix

1. The websocket connection not working properly in mobile apps.

## v1.8.32 (20-09-2021)

### Bugfix

1. Corrected issues with Socket connection not working properly in Mobile Apps.

## v1.8.31 (16-09-2021)

### Bugfix

1. Enable switch in detailed view and real-time.

## v1.8.30 (14-09-2021)

### Bugfix

1. Last fuel filled value to be fixed to 2.

## v1.8.29 (14-09-2021)

### Bugfix

1. layout package update.

## v1.8.28 (13-09-2021)

### Bugfix

1. Logos style fix for webapp.

## v1.8.27 (13-09-2021)

### Bugfix

1. Logos style change.

## v1.8.26 (13-09-2021)

### Bugfix

1. Menu.less change.

## v1.8.25 (13-09-2021)

### Bugfix

1. Vendor logo added.

## v1.8.24 (10-09-2021)

### Bugfix

1. Welcome page added.

## v1.8.23 (09-09-2021)

### Bugfix

1. React component version updated.

## v1.8.22 (09-09-2021)

### Bugfixes

1. Reports module version updated.
2. Dynamic deploy path added and home path updated.

## v1.8.21 (08-09-2021)

### Bugfix

1. Test report added.

## v1.8.20 (07-09-2021)

### Bugfix

1. Version reverted.

## v1.8.19 (07-09-2021)

### Bugfix

1. Raw data report template added.

## v1.8.18 (01-09-2021)

### Bugfix

1. Updated getViewAccess props pass format.

## v1.8.17 (01-09-2021)

### Bugfix

1. Updated console in main app file.

## v1.8.16 (01-09-2021)

### Bugfixes

1. Real time view responsive issue fix.

## v1.8.15 (31-08-2021)

### Bugfixes

1. Sent getViewAccess function to all pages.
2. js-sdk version updated.

## v1.8.14 (31-08-2021)

### Bugfix

1. App name sent from state.

## v1.8.13 (27-08-2021)

### Bugfix

1. default app name added and layout version updated.

## v1.8.12 (27-08-2021)

### Bugfix

1. real time value overlapping.

## v1.8.11 (25-08-2021)

### Bugfix

1. js-sdk updated for trigercommand.

## v1.8.10 (24-08-2021)

### Bugfix

1. Added condition to get client id from local storage for mobile build.

## v1.8.9 (23-08-2021)

### Bugfixes

1. load percentage calculation removed.

## v1.8.8 (17-08-2021)

### Bugfixes

1. Estimated runhour calculation change.

## v1.8.7 (11-08-2021)

### Bugfixes

1. Script created for stage and for webapp static home path added.
2. Report reverted to old.

## v1.8.6 (11-08-2021)

### Bugfix

1. gulp file updated with variable name.

## v1.8.5 (11-08-2021)

### Bugfix

1. gulp file changed.
2. gitlab-ci file changed.

## v1.8.4 (10-08-2021)

### Bugfix

1. gulp file changed.

## v1.8.3 (10-08-2021)

### Bugfix

1. New on/off feature implemented.

## v1.8.2 (06-08-2021)

### Bugfix

1. Dashboard page optimized for Fuel only.

## v1.8.1 (05-08-2021)

### Feature

1. real time null value indicator handled.
2. report graph labels point format to 2 decimals.

## v1.8.0 (05-08-2021)

### Feature

1. DG fuel only all pages added.

## v1.7.0 (27-07-2021)

### Feature

1. Alerts management added and all datoms libraries version updated to latest.

## v1.6.10 (26-07-2021)

### Bugfix

1. Runhour format in HH:mm.

## v1.6.9 (22-07-2021)

### Bugfix

1. Mileage - when calculation value is blank.

## v1.6.8 (21-07-2021)

### Bugfix

1. Fuel Maximum level limit.
2. trip 0 is data has come in that day.

## v1.6.8 (20-07-2021)

### Bugfix

1. Detailed view table no data should show na and not 0.

## v1.6.6 (20-07-2021)

### Bugfix

1. 1536 screen real time view design fix.

## v1.6.5 (19-07-2021)

### Bugfix

1. Change load percentage calculation for summary.
2. Change pagination option for summary table.
3. Issue fix with parsefloat in real time.

## v1.6.3 (12-07-2021)

### Bugfix

1. Portal not opening issue fix.
2. Real time lifetime runhour format change.

## v1.6.2 (09-07-2021)

### Bugfix

1. Removed energy data from fuel only page.

## v1.6.1 (08-07-2021)

### Bugfix

1. Fuel only page bug fixed.

## v1.6.0 (07-07-2021)

### Feature

1. Fuel only template added.

## v1.5.21 (05-07-2021)

### Bugfixes

1. Added % value in fuel tank.

## v1.5.20 (01-07-2021)

### Bugfixes

1. Added ^ in all datoms dependencies.
2. Removed duplicate condition for thing management in app.js file.

## v1.5.19 (28-06-2021)

### Bugfixes

1. Activity violation and fault no data icon changed.

## v1.5.18 (25-06-2021)

### Bugfixes

1. Device management menu item added for mobile.
2. js sdk, js utils, react components, thing and device management library version updated.
3. Antd library version set to 4.6.6

## v1.5.17 (24-06-2021)

### Bugfixes

1. No fault, activity and violation icon change.

## v1.5.16 (21-06-2021)

### Bugfixes

1. gulpfile changes to deploy to primary server.

## v1.5.15 (21-06-2021)

### Bugfixes

1. No data widget for activity and fault

## v1.5.14 (18-06-2021)

### Bugfixes

1. Thing management version increased.
2. Device management version increased and paths added for all templates.

## v1.5.13 (16-06-2021)

### Bugfixes

1. Condition added for thing management menu and paths added.
2. Thing management version updated.

## v1.5.12 (15-06-2021)

### Bugfix

1. Remove dummy data from real time.

## v1.5.11 (11-06-2021)

### Bugfix

1. 1440 screen height reduce in real time.

## v1.5.10 (02-06-2021)

### Bugfix

1. Bucket implemented in real-time.

## v1.5.9 (26-05-2021)

### Bugfix

1. Real time responsive issue fix.

## v1.5.8 (24-05-2021)

### Bugfix

1. Trip report new react-components version issue solved.

## v1.5.7 (20-05-2021)

### Bugfix

1. max old space size increased.

## v1.5.6 (20-05-2021)

### Bugfix

1. More script added for npm publish.

## v1.5.5 (20-05-2021)

### Bugfix

1. Remove unnecessary files and document.getElementById.

## v1.5.4 (20-05-2021)

### Bugfixes

1. Made setup to publish to npm after every successfuly deployment so that the mobile app can be published as well.
2. Added setup for gitpod.

## v1.5.3 (19-05-2021)

### Bugfix

1. Pagesense implemented in html file.

## v1.5.2 (18-05-2021)

### Bugfix

1. Styling changes and real time data page added.

## v1.5.1 (17-05-2021)

### Bugfix

1. Fuel level value change.

## v0.6.3 (12-05-2021)

### Bugfix

1. Mechanical panel view responsive.

## v0.6.2 (11-05-2021)

### Bugfix

1. Graph changes and runhour implementation in mechanical panel view.

## v0.6.1 (04-05-2021)

### Bugfix

1. Thing management component update and url change.

## v0.6.0 (30-04-2021)

### Feature

1. Mechanical panel implemented.

## v0.5.0 (28-04-2021)

### Feature

1. Fault parameter report added.

## v0.4.38 (23-04-2021)

### Bugfix

1. Dashboard calculated energy value implemented.

## v0.4.37 (22-04-2021)

### Bugfix

1. Dashboard switched off value change to blank string.

## v0.4.36 (22-04-2021)

### Bugfix

1. Energy calculation change in dashboard.

## v0.4.35 (22-04-2021)

### Bugfix

1. Energy generated and other energy meter readings name with (Meter) string.

## v0.4.34 (21-04-2021)

### Bugfix

1. Energy generated name with (Meter) string.

## v0.4.33 (20-04-2021)

### Bugfix

1. Calculated runhour and load percentage calculation change.

## v0.4.32 (15-04-2021)

### Bugfix

1. js sdk, js utils, layout and user management library version updated.

## v0.4.31 (13-04-2021)

### Bugfix

1. js sdk library version updated and options api imported from js sdk.

## v0.4.30 (13-04-2021)

### Bugfix

1. New load and energy parameters added.

## v0.4.29 (13-04-2021)

### Bugfix

1. Remove energy parameter from generator parameters.

## v0.4.28 (13-04-2021)

### Bugfix

1. Dashboard estimated runhr calculation change.

## v0.4.27 (06-04-2021)

### Bugfix

1. Activity fuel filled and drain data to show.
2. Graph all data not showing issue fix.

## v0.4.26 (06-04-2021)

### Bugfix

1. New fault parameter added.

## v0.4.25 (31-03-2021)

### Bugfix

1. Dynamic kva value.

## v0.4.24 (26-03-2021)

### Bugfix

1. Activity real time events.

## v0.4.23 (25-03-2021)

### Bugfix

1. Fault parameter default icon change.

## v0.4.22 (25-03-2021)

### Bugfix

1. Daily report data for long duraion fixed

## v0.4.21 (24-03-2021)

### Bugfix

1. Trip View issue fix

## v0.4.20 (22-03-2021)

### Bugfix

1. Trip View available for all customers

## v0.4.19 (19-03-2021)

### Bugfix

1. report access handled

## v0.4.18 (19-03-2021)

### Bugfix

1. DG monitoring daily report fuel remaining calculation change to snapshot data.

## v0.4.17 (18-03-2021)

### Bugfix

1. DG monitoring Trip View Fixes.

## v0.4.16 (12-03-2021)

### Bugfix

1. DG monitoring Trip View Unicell Customer.

## v0.4.15 (12-03-2021)

### Bugfix

1. DG monitoring events time in descending format.

## v0.4.14 (11-03-2021)

### Bugfix

1. DG monitoring events time as per timezone.

## v0.4.13 (03-03-2021)

### Bugfix

1. Detailed view runhour parameter hidden from graph.

## v0.4.12 (27-02-2021)

### Bugfix

1. Detailed view as landing page if there is only single thing.

## v0.4.11 (26-02-2021)

### Bugfix

1. Style fixes in detailed view

## v0.4.10 (25-02-2021)

### Bugfix

1. New template-2 design change in dg monitoring App file

## v0.4.9 (24-02-2021)

### Bugfix

1. Cell tower monitoring detailed view graph with visible marker.
2. Cell tower monitoring Last data received text change.

## v0.4.8 (19-02-2021)

### Bugfix

1. DG template-2 and cell tower monitoring DG Status implemented.
2. DG template 2 and cell tower no fault data render implemented

## v0.4.7 (18-02-2021)

### Bugfix

1. DG template-2 load% calculation implemented.
2. DG template 2 critical report time interval changed to last 30 days

## v0.4.6 (16-02-2021)

### Bugfix

1. DG template-2 changes implemented

## v0.4.5 (12-02-2021)

### Bugfix

1. DG template-2 debug parameters implemented
2. Changes in cell tower monitoring

## v0.4.4 (06-02-2021)

### Bugfix

1. Undefined issue fix for socket

## v0.4.3 (05-02-2021)

### Bugfix

1. debug param enabled
2. mc_st changes
3. new params added
4. tested

## v0.4.2 (03-02-2021)

### Bugfix

1. route changes for cell monitoring.

## v0.4.1 (03-02-2021)

### Bugfix

1. Api integrated for cell monitoring.

## v0.4.0 (29-01-2021)

### Features

1. Cell tower monitoring template design implementated.

## v0.3.5 (19-01-2021)

### Bugfixes

1. Detailed view drawer full text search filter implemented.

## v0.3.4 (19-01-2021)

### Bugfixes

1. Custom report bug fix.

## v0.3.3 (18-01-2021)

### Bugfixes

1. Template report events api not calling issue fix.

## v0.3.2 (18-01-2021)

### Bugfixes

1. Report responsive done.
2. Custom report issue fixed.

## v0.3.1 (08-01-2021)

### Features

1. dg detailed view url change for single station

## v0.3.0 (08-01-2021)

### Features

1. Implemented changes for template - 1.
2. Responsiveness of the app enhanced.
3. Changes made as for the latest Report.
4. Unnecessary files removed from the repo.

### Bugfixes

1. DG was shown as "Offline" in the portal when no Machine Status data was received from the DG.
2. Minor functionality related issues fixed.

## v0.2.1 (06-01-2021)

### Bugfix

1. Missing script added for test job.

## v0.2.0 (06-01-2021)

### Feature

1. Mobile App build added.

## v0.1.4 (22-12-2020)

### Bugfixes

1. Showing NA while no real time data is coming in detailed view.

## v0.1.3 (18-12-2020)

### Bugfixes

1. Dg monitoring graph timezone from user preferance.

## v0.1.2 (18-12-2020)

### Bugfixes

1. Detailed view real time multiple data coming issue fix.

## v0.1.1 (17-12-2020)

### Bugfixes

1. Removed unnecessary Dynatrace code.

## v0.1.0 (17-12-2020)

### Features

1. Separate repo setup for DG Monitoring.
2. Antd v4 update.
3. Separate Mobile view implemented for dg detailed view & dashboard.
