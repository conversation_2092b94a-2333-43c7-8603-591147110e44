import type { Config } from 'jest';

const config: Config = {
  preset: 'ts-jest',          // Use ts-jest to process TypeScript files
  testEnvironment: 'jsdom',   // Simulates a browser-like environment

  transform: {
    "^.+\\.tsx?$": [
      "ts-jest",
      {
        diagnostics: false,
      },
    ],
  },

  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'], // Points to the setup file for additional configurations
  moduleNameMapper: {
    // '\\.(css|less|scss|sass)$': 'identity-obj-proxy', // Mock style imports,
    "^@datoms/dg-monitoring-views/(.*)$": "<rootDir>/src/packages/dg-monitoring-views/$1",
    "^@datoms/react-components/(.*)$": "<rootDir>/src/packages/react-components/$1",
    "^@datoms/webapp-component-user-management/(.*)$": "<rootDir>/src/packages/webapp-component-user-management/$1",
    "^@datoms/js-utils/(.*)$": "<rootDir>/src/packages/js-utils/$1",
    ".*\\.(css|less|svg|png)$": "<rootDir>/lessToJsConverterForJest.js",
  },

  testMatch: [
    // '<rootDir>/src/components/configurable/baseComponents/ConfigurableChart/*.test.{ts,tsx}',
    // '<rootDir>/src/components/configurable/baseComponents/*/configs/*.test.{ts,tsx}',
    // '<rootDir>/src/components/configurable/baseComponents/ConfigurableTable/ConfigurableTable.test.{ts,tsx}',
    // '<rootDir>/src/components/configurable/baseComponents/ConfigurableSummary/ConfigurableSummary.test.{ts,tsx}',
    // '<rootDir>/src/components/configurable/baseComponents/ConfigurableFilters/ConfigurableFilters.test.{ts,tsx}',
    // '<rootDir>/src/components/configurable/baseComponents/ConfigurableParameterList/ConfigurableParameterList.test.{ts,tsx}',
    // '<rootDir>/src/components/configurable/StatusIcon/**/*.test.{ts,tsx}',
    '<rootDir>/src/components/base/SearchBar/*.test.{ts,tsx}',
    // '<rootDir>/src/containers/views/MapView/components/MapComponent/**/*.test.{ts,tsx}',
    // '<rootDir>/src/containers/views/MapView/components/Legend/**/*.test.{ts,tsx}',
    // '<rootDir>/src/containers/views/MapView/components/MapListDrawer/**/*.test.{ts,tsx}',
    // '<rootDir>/src/components/configurable/Kpi/*.test.{ts,tsx}',
    // '<rootDir>/src/packages/dg-monitoring-views/src/js/GenericTemplate/component/MRIPanelBody/MRIPanelBody.test.{ts,tsx}',
    // '<rootDir>/src/packages/dg-monitoring-views/src/js/GenericTemplate/pages/DetailedView/components/MRIMachineComponents/MRIMachineStatusHeader/MRIMachineStatusHeader.test.tsx',
  ],

  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'], // Allowing .js and .jsx imports
  verbose: true, // Display individual test results with the test suite hierarchy

};

export default config;
