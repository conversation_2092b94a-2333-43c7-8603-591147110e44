# Freshdesk API Integration

 - Every single piece of information - be it a customer's ID or the priority of a specific ticket - can be identified by its own unique identifier or "URI".
 - All URIs follow a specific format and that format is:
 - `https://your_helpdesk_domain_name/api/v2/resource_name`
 - Eg: `https://thelocker.freshdesk.com/api/v2/tickets`
 - Before we can set the priority of a ticket or change a customer's name or use any of the APIs, we need to "authenticate our ID" or "log in" in the same way as we log in to helpdesk's web portal.

## Authentication
 - Example, if your API key is abcdefghij1234567890, the curl command to use:
 - `curl -v -u abcdefghij1234567890:X -H "Content-Type: application/json" -X GET https://domain.freshdesk.com/helpdesk/tickets.json`

## Rate Limit
 - The number of API calls per hour is restricted to 1000. 
 - If the API request is received after the limit has been reached, Freshdesk will give an error response. The "retry-after" value will tell how long to wait before sending another API request.

## Pagination
 - Responses for API that return a list, like ticket listing and contact listing, are paginated. 
 - To scroll through the responses, add GET request parameter page=[number] to the request.
 
Example: 
 - `https://domain.freshdesk.com/api/v2/contacts?page=2`
 - `https://domain.freshdesk.com/api/v2/contacts?per_page=10&page=2 `

## Codes
 - Status- Open: 2 | Pending : 3 | Resolved : 4 | Closed : 5
 - Priority- Low: 1 | Medium: 2 | High: 3 | Urgent : 4

## Chat Support 
 - Users would be given options to either Create, Update or Reply to a ticket, or Update a conversation.
 - Upon the selection of an option, the specific and relevant API will be called after receiving the appropriate input.
 - As the user selects an option, some fields can be show accordingly for which the data needs to be filled by the user.
 - User will enter the details for the tickets such as: Description, Subject, Email, Priority, Status, CC Mails, etc.
 - Upon filling the details, appropriate API will be called and the results will be shown to the user according to the response received.
 - User can view a specific ticket, the conversation related to the ticket and reply or update the ticket accordingly.
 - User can see a list of all the tickets and will have the option to filter the tickets based on the fields such as Status, Date and Custom String.

## Fetching and Displaying existing tickets on Platform
 - Based on the user, List All Tickets API can be called to list all the tickets related with the user.
 - Some filters can be provided to filter and fetch the specific tickets as per the requirement.
 - The fetched data can be shown in a tabular form, along with filters, pagination can also be provided to the user using the Pagination API.

## API Features
There are different operations that can be performed using the API, all the CRUD operations to Create, View, Update and Delete Tickets.

### Creating a ticket
API:
 - Method: Post
 - URI: /api/v2/tickets
 - Sample Code: `curl -v -u yourapikey:X -H "Content-Type: application/json" -d '{ "description": "Details about the issue...", "subject": "Support Needed...", "email": "<EMAIL>", "priority": 1, "status": 2, "cc_emails": ["<EMAIL>","<EMAIL>"] }' -X POST 'https://domain.freshdesk.com/api/v2/tickets'`
 -  Other custom fields can also be added as : `"custom_fields" : { "category" : "Primary" }`
 - Users can add attachments too using: `"attachments" : [ ]`
 - Sample Code: `curl -v -u yourapikey:X -F "attachments[]=@/path/to/attachment1.ext" -F "attachments[]=@/path/to/attachment2.ext" -F "email=<EMAIL>" -F "subject=Ticket Title" -F "description=this is a sample ticket" -X POST 'https://domain.freshdesk.com/api/v2/tickets'`

### Creating a Child Ticket
 - This API request must have parent_id.
 - It is the ID of the parent ticket under which the child ticket needs to be created.
 - Sample Code: `curl -v -u yourapikey:X -H "Content-Type: application/json" -d '{ "description": "Details about the issue...", "subject": "Support Needed...", "email": "<EMAIL>", "parent_id": 1, priority": 1, "status": 2, "cc_emails": ["<EMAIL>","<EMAIL>"] }' -X POST 'https://domain.freshdesk.com/api/v2/tickets'`

### Viewing a Ticket
API
 - Method: GET
 - URI: /api/v2/tickets/[id]
 - Sample Code: `curl -v -u yourapikey:X -H "Content-Type: application/json" -X GET 'https://domain.freshdesk.com/api/v2/tickets/20'`

Getting the associated conversations along with the ticket response.
`curl -v -u yourapikey:X -X GET 'https://domain.freshdesk.com/api/v2/tickets/20?include=conversations'`

Getting the associated company and requester information along with the ticket response.
`curl -v -u yourapikey:X -X GET 'https://domain.freshdesk.com/api/v2/tickets/20?include=company,requester'`

Getting the associated stats information along with the ticket response.
`curl -v -u yourapikey:X -X GET 'https://domain.freshdesk.com/api/v2/tickets/20?include=stats'`

View a Ticket with Association
`curl -v -u yourapikey:X -H "Content-Type: application/json" -X GET 'https://domain.freshdesk.com/api/v2/tickets/23'`

### Get Associated Tickets
API:
 - Method: GET
 - URI: /api/v2/tickets/[id]/associated_tickets
 - Sample Code: `curl -v -u yourapikey:X -H "Content-Type: application/json" -X GET 'https://domain.freshdesk.com/api/v2/tickets/20/associated_tickets'`

### Replying to a Ticket/Creating a Reply
API:
 - Method: POST
 - URI: /api/v2/tickets/[id]/reply
 - Sample Code: `curl -v -u yourapikey:X -H "Content-Type: application/json" -X POST -d '{ "body":"We are working on this issue. Will keep you posted." }' 'https://domain.freshdesk.com/api/v2/tickets/141/reply'`

We can reply to a ticket with Attachment as well:
 - Sample Code: `curl -v -u yourapikey:X -F "attachments[]=@/path/to/attachment1.txt" -F "body=this is a sample reply" -X POST 'https://domain.freshdesk.com/api/v2/tickets/27/reply'`


### Updating a Conversation
API:
 - Method: PUT
 - URI: /api/v2/conversations/[id]
 - Sample Code: `curl -v -u yourapikey:X -H "Content-Type: application/json" -X PUT -d '{ "body":"Can you provide some screenshots?" }' 'https://domain.freshdesk.com/api/v2/conversations/5'`

Updating a conversation with Attachments
 - Sample Code: `curl -v -u yourapikey:X -F "attachments[]=@/path/to/attachment1.txt" -F "body=updated conversation" -X PUT 'https://domain.freshdesk.com/api/v2/conversations/6's`

### Update a Ticket
API:
 - Method: PUT
 - URI: /api/v2/tickets/[id]
 - Sample Code: `curl -v -u yourapikey:X -H "Content-Type: application/json" -X PUT -d '{ "priority":2, "status":3 }' 'https://domain.freshdesk.com/api/v2/tickets/1'`

### Update Multiple Tickets
API:
 - Method: POST
 - URI: /api/v2/tickets/bulk_update
 - Sample Code: `curl -v -u yourapikey:X -H "Content-Type: application/json" -X POST -d '{"bulk_action": {"ids": [20,21,22],"properties":{"from_email":"<EMAIL>","status":2,"group_id":1234,"type":"Question","priority":4},"reply":{"body":"Please check this ticket"}}}' 'https://domain.freshdesk.com/api/v2/tickets/bulk_update'` 
 
<!-- ### Delete a Ticket
API:
 - Method: DELETE
 - URI: /api/v2/tickets/[id]
 - Sample Code: `curl -v -u yourapikey:X -X DELETE 'https://domain.freshdesk.com/api/v2/tickets/1'`

When deleted, tickets can be retrieved them using the Restore Ticket API.

### Delete Multiple Tickets
API:
 - Method: POST
 - URI: /api/v2/tickets/bulk_delete
 - Sample Code: `curl -v -u yourapikey:X -H "Content-Type: application/json" -X POST -d '{"bulk_action": {"ids": [20,21,22]}}' 'https://domain.freshdesk.com/api/v2/tickets/bulk_delete'`

### Restore a Ticket
API:
 - Method: PUT
 - URI: /api/v2/tickets/[id]/restore
 - Sample Code: `curl -v -u yourapikey:X -H "Content-Type: application/json" -X PUT -d '' 'https://domain.freshdesk.com/api/v2/tickets/1/restore'` 
 -->

### List All Tickets
API:
 - Methods: GET
 - URI: /api/v2/tickets
 - Sample Code: `curl -v -u yourapikey:X -X GET 'https://domain.freshdesk.com/api/v2/tickets'`
 - Filters can be used to view only specific tickets
 
### Filter Tickets
API:
 - Method: GET
 - URI: /api/v2/search/tickets?query=[query]
 - Sample Code: `curl -v -u yourapikey:X -X GET 'https://domain.freshdesk.com/api/v2/search/tickets?query="priority:3"'`

Filtering Tickets with Custom Fields:
 - Single line text	: string | Number : integer | Checkbox : boolean | Dropdown : string
 - Sample Code: `curl -v -u yourapikey:X -X GET 'https://domain.freshdesk.com/api/v2/search/tickets?query="custom_string:theactualkeywordone%OR%20custom_string:theactualkeywordtwo"'`

Other Examples:
Get the second page of Open and Pending tickets.
 - "status:3 OR status:4"
 - Sample Code: `curl -v -u yourapikey:X -X GET 'https://domain.freshdesk.com/api/v2/search/tickets?query="status:3%20OR%20status:4"&page=2'`

Get the list of tickets created on a particular day.
 - created_at:'2017-01-01'"
 - Sample Code: `curl -v -u yourapikey:X -X GET 'https://domain.freshdesk.com/api/v2/search/tickets?query="created_at:%272017-01-01%27"'`

Get the list of tickets whose type is 'Question' or 'Problem' and response due on first week of October 2017.
 - "(type:'Question' OR type:'Problem') AND (due_by:>'2017-10-01' AND due_by:<'2017-10-07')"
 - Sample Code: `curl -v -u yourapikey:X -X GET 'https://domain.freshdesk.com/api/v2/search/tickets?query="(type:%27Question%27%20OR%20type:%27Problem%27)%20AND%20(due_by:>%272017-10-01%27%20AND%20due_by:<%272017-10-07%27)"'`

### Sample Codes:
[Github Link for Samples in NodeJS](https://github.com/freshworks/fresh-samples/tree/master/NodeJs)