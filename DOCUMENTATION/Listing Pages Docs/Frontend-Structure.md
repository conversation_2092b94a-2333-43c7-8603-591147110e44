## 🌲 Root Structure

```
src
 ┓ assets               # Global assets (icons, images, svgs, etc.)
 ┓ configs              # Global configuration
 ┓ utils                # Global utility functions (formatters, parsers, helpers)
 ┓ types                # Global TypeScript types/interfaces
 ┓ components           # Global reusable UI components
 ┓ containers           # Feature or domain-specific grouped views
 ┓ layout               # App shell/layouts (sidebars, headers, wrappers)
 ┓ routes               # App routes definition
 ┓ app.ts               # Entry point for initializing app, routes, providers
 ┓ store                # Context global state management
 ┗ style.less           # Global styling (base styles, variables, resets)
```

## `assets`
- Keep all files at the root level of the `assets` folder.
- Naming convention: kabab-case. (all lowercase, separated by -)
- Example: `my-icon.svg`, `my-image.png`, `my-logo.svg`, `my-icon.svg`, `my-image.png`, `my-logo.svg`

## `configs`
- Now we dont really have anything to store here.
- We will not use this to store config for asset category, as it should be defined from backend.
- We will also not define any config related to any specific component, as it should be defined at that component level.
- Instead of vendor specific conditions, we can keep feature specific conditions.

## `utils`
- Migrate all files of js-utils here
- We can keep any commonly used utility functions here.
- Component specific utils won't be placed here.

## `types`
- Shared accross  the app.

## `components`
- base (shift the compmonents from react-components folder here)
- configurable (can place Table, Graph etc. It can use base components)
- composites (Report controller- It can use both base & configurable components)

## `styles`
- Remove global styles.
- Component/Page styles should be placed in the same folder as the component/page.
- We will only use `.less` files.
- We will not use static color codes, we will use variables.
- theme wise variable definition.
- Keep style for html & body (need to evaluate this)

---

## 📦 `containers`  
Group-level folders for major features or domains

```
containers
 ┓ managements          # Domain-specific modules (Customers, Assets, Users etc.)
 ┓ views                # Visualization-heavy views like maps, dashboards, trends
 ┓ reports              # Structured reports (daily, multi-asset, faults etc.)
 ┓ settings             # Configuration settings like user profile, sessions, mfa
 ┓ authentication       # Login, signup, access control flows
 ┗ yoyo-rydes           # Rental App
```

---

## 📁 `managements`

```
managements
 ┓ customers
 ┓ devices
 ┓ assets
 ┓ users
 ┓ subscriptions
 ┓ firmwares
 ┓ rental
 ┓ production-inventory
 ┓ workorders
 ┓ fuel-caliration
 ┗ changelog
```

Each folder here handles a specific **management domain** and may contain pages, modals, or workflows.

---

## 👁️ `views`

```
views
 ┓ MapView
 ┓ PanelView
 ┓ Realtime
 ┓ DetailedView
 ┓ TripView
 ┓ SiteView
 ┓ DGDashboard
 ┓ AssetDashboard
 ┗ SiteDashboard
```

---

## 📊 `reports`

```
reports
 ┓ DailyReports
    ┓ DGDailyReports.tsx
    ┗ GasGensetDailyReports.tsx
 ┓ MultiAssetReports
    ┓ DGMultiAssetReports.tsx
    ┗ GasGensetMultiAssetReports.tsx
 ┗ FaultReports
    ┓ DGFaultReports.tsx
    ┗ GasGensetFaultReports.tsx
```

Each file here typically represents a standalone report UI with logic tied to a data type.

---

## 🔍 Per Page Folder Structure

```
Realtime
 ┓ index.tsx
 ┓ index.test.tsx
 ┓ logic.tsx
 ┓ utils.ts
 ┓ types.ts
 ┓ style.less
 ┓ configs/
 ┓ assets/
 ┓ components/
    ┓ HeaderComponent/
    ┃   ┓ index.tsx
    ┃   ┓ index.test.tsx
    ┃   ┓ logic.tsx
    ┃   ┓ types.ts
    ┃   ┓ style.less
    ┃   ┓ configs/
    ┃   ┗ assets/
    ┗ ParamTrendComponent/
        ┓ index.tsx
        ┓ index.test.tsx
        ┓ logic.tsx
        ┓ types.ts
        ┓ style.less
        ┓ configs/
        ┗ assets/
```

📌 **Explanation**:

1. `index.tsx`:  
   - Main React component (UI structure, JSX, hooks usage).
   - Form rendering, layout, conditional logic.
   - API calls via hooks (e.g., `useEffect`, `useQuery`, `useMutation`) are initiated here.

2. `logic.tsx`:  
   - Business logic separated from UI.
   - Data processing, derived state, form validation logic.
   - Often broken into hooks:
     - `useGraph.tsx`, `useSummary.tsx`, `useTable.tsx`, etc.

3. `utils.ts`:  
   - Page-specific helper functions (calculations, formatting, etc.)

4. `types.ts`:  
   - Local TS types/interfaces for props, form schemas, API responses.

5. `style.less`:  
   - Scoped styles specific to the page.
   - 

6. `configs/`:  
   - Constants and configuration specific to the page or its logic.

7. `assets/`:  
   - Local icons, images, SVGs used by this page.

8. `components/`:  
   - UI components used **only in this page**.
   - Each component gets its own subfolder with:
     - `index.tsx` (main UI)
     - `logic.tsx` (optional, if it has logic)
     - `types.ts`, `style.less`, `assets/`, `configs/`
     - `index.test.tsx` (unit tests)
     - `style.less`
     - `assets/`
     - `configs/`

---


📌 **Detailed Flow Example**: Summary + Filter + Table + Add Button

Let’s say we have a page called `Customers` that has:
- A **summary** bar showing total customers, active ones, etc.
- A **filter** to search customers by name/status.
- A **table** listing customer records.
- An **Add button** that opens a modal with a **form** to add a new customer.

```
customers
 ┓ index.tsx              # Main component rendering layout and UI blocks
 ┓ logic.tsx              # Business logic: table transforms, filters, API integration
 ┓ types.ts               # Local types for Customer, filter schema, form state
 ┓ utils.ts               # Small helper functions (e.g., status formatting)
 ┓ style.less             # Scoped styles for layout and page UI
 ┓ configs/               # Filter options, status mappings, dropdown enums
 ┓ assets/                # Page-specific icons/images
 ┓ components/
    ┓ SummaryComponent/         # Shows total/active stats
    ┓ FilterComponent/          # Search + dropdowns
    ┓ CustomerTable/      # Data table
    ┓ AddCustomerModal/   # Form modal
    ┗ ParameterTrend/     # Parameter Trend component
```

### File Responsibilities:
- `index.tsx`: 
  - Arranges the layout and renders `SummaryComponent`, `FilterComponent`, `CustomerTable`, and `AddCustomerModal`.
  - Uses hooks from `logic.tsx` to get props/data, which is used to render the UI and passes required props to the child components.

- `logic.tsx`:
  - Handles API calls using `useEffect` or custom hooks.
  - States for filter, pagination, form state, etc. are stored here.
  - API call is done to fetch summary data & paginated table data at the start or whenver filter/pagination changes/new items are added.
  - Handles form submission logic (`onSubmit`, validation, toast handling).
  - `onChange functions for filter`/`onSubmit for form` are defined here then passed to the respective components.

- `FilterComponent` component:
  - Based on filter config passed as props from main page, renders the filter UI.
  - Calls API to fetch filter options based on filter config if api is enabled for a particular filter.
  - Emits filter changes event to the main page.

- `CustomerTable`:
  - Accepts filtered customer list via props and renders using Ant Design's `Table`.
  - Handles pagination, sorting events by calling `onChange` event passed via props from main page.

- `AddCustomerModal`:
  - Contains Form component.
  - Initial values can be sent via props from `index.tsx`.
  - calls onSubmit function passed via props from `index.tsx`.

- `SummaryComponent`:
  - Displays quick stats like total, active, inactive customers.
  - Summary data is passed via props from `index.tsx`.

- `ParameterTrend`:
  - There can be some component which are only partly dependent on the main page for its data.
  - This component simply accepts the asset id from props.
  - It then fetches the asset data from the API and renders the chart.
  - api call is made in `\components\ParameterTrend\logic.tsx` file to fetch the data, 
  - which is then used in `\components\ParameterTrend\index.tsx` file to render the chart.

This structure ensures that UI layout stays in `index.tsx`, logic stays modular in `logic.tsx`, and all UI components are well-separated and testable.

---

💡 **Best Practices**:
- Keep **domain logic** in `logic.tsx` (or custom hooks) and avoid cluttering `index.tsx`.
- API integration via hooks like `useFetchData()` should live in `logic.tsx`.
- All forms should delegate state management and validation logic to `logic.tsx` or custom `useFormLogic.tsx`.
- **Tests** (`index.test.tsx`) should live next to the component/page they test.
- Reusable pieces across pages should go into `src/components/` or `src/utils/`.




## Naming and letter case conventions

- 5 types of casings are used:
 - camelCase
  - example: `myVariable`
 - PascalCase (UpperCamelCase)
  - example: `MyVariable`
 - kebab-case
  - example: `my-file`
 - snake_case
  - example: `my_variable`
 - UPPER_SNAKE_CASE
  - example: `MY_CONSTANT`

- **camelCase** is used for variables, functions, and components.
        -  for config keys in file if you're accessing them like regular object properties.
- **PascalCase** is used for class names, interfaces, and types.
- **kebab-case** is used for asset file(images, icons) names and folders.
- **snake_case** is used for keys used in communication with rest api.
- **UPPER_SNAKE_CASE** is used for constants.

## ✅ Casing Summary

| Type                        | Casing            | Example                     |
|-----------------------------|-------------------|-----------------------------|
| Folder name                 | `kebab-case`      | `user-profile/`             |
| Component file              | `PascalCase`      | `LoginForm.tsx`             |
| Utility/config/hook/type file    | `kebab-case`      | `use-auth.ts`               |
| Stylesheet file             | `kebab-case`      | `login-form.module.scss`    |
| Variable/function names     | `camelCase`       | `getUserData`               |
| Internal config object keys | `camelCase`       | `maxRetryCount`             |
| JSON config keys            | `snake_case`      | `api_endpoint`              |


## How to

1. How to create a component which is functionally similar but visually very different from ANTD components?
- We can create composite/configurable components using antd component inside it.
- We can then give a class name to it, and override the styles under that classname accordingly.

2. How to change the style configuration of antd components?
- First preference: Using ANTD `ConfigProvider`, `Design Tokens`
- If not possible then we will customize through `.less`, but always scope the style within that component classname
- While doing either of the above two, never hard-code the colors, font-sizes, etc.
- We can define css variables and use those in style files.

3. How to get the variables inside css/less files passed in the andt provider ?



