# **Asset List Page Implementation Guide**

## **Writing Configuration JSON**
- Define the filters and columns.
- Define add button.
- Define the api config for each action button.
- Write proper conditions to show/hide columns or filters or bulk action buttons/single action buttons.

## **Component/Logic separation**
### 1. `ThingDebug` Component
- Need to add a separate route for this component.
- thingDetails was passed as props, now need to call thing details api to get values like devices, cusotmer, application etc.

### 2. `AssetDataView Modal` Component
- Logic is already seperated.
- Is internally routed, as it is a modal.
- Need to remove it from route.
- Need to pass customer_id and asset_id as props (currently taking from url params)
- Handle via predefined actions through config.

### 3. `LinkCustomer Modal` Component
- Component is already seperated.
- But some details like customer list, resellers list are sent from parent component, 
it needs to be handled inside the component itself.
- Handle via predefined actions through config.

### 4. `Customer Details LinkCustomer Modal` Component
- Component is already seperated.
- Send required props to the component.
- Handle via predefined actions through config.

### 5. Functional Status Update
- Can be handled via config, for details refer to `Action-Handling.md`

### 6. Activate/Inactivate Asset
- Can be handled via config, for details refer to `Action-Handling.md`

### 7. Delete Asset
- Can be handled via config, for details refer to `Action-Handling.md`


### Logic Conditions

1. If opened for end customer:
    - `retriveThingsList` is called
    - with query params: `?status=all&without_device=true`
2. If opened for partner/datoms-x:
    - `retriveVendorThingsList` is called
3. Provision for delears node in territory filter if dealer management is enabled.

## Add Buttons
### Add Asset Button
- If feature/access includes "ThingManagement:Add"
- On clicking, opens `AddDevice` component.
### Bulk Add Asset Button
- If feature/access includes "ThingManagement:Add"
- On clicking, opens `BulkAddDevice` component.

## 6. Filters:
    - Account Type:
      - only show to applicaiton_id 12
      - defualt value: 'production'
    - Territories: 
      - If territory feature is enabled or application_id is 12
      - default value: NA
    - Partners:
      - If partners are available
      - default value: NA
    - Customers:
      - If customers are available
      - default value: NA
    - Asset Type:
      - If multiple asset types are available
    - Asset Status
      - Always show
    - Onboarding Status
        - show only for application_id 12
    - Search By Asset Name

## 7. Table Columns

### **Asset Name**
- **Column Display Condition:** Always visible (No condition restricting its visibility).
- **Sorting:** 
  - Alphabetically sorts `thing_name` values.
  - If `thing_name` exists and is a string, converts it to lowercase for case-insensitive sorting.

- **Rendering Logic:**
  - Displays the **Asset Name** along with an **icon representing its category**.
  - Clicking on the asset redirects to **Asset Details** (`goToThingDetails(row_value)`).

- **Icon Display:**
  - **If `category_id` is 44 or 85**:
    - Uses `ImageComponent` with:
      - **Category icon**
      - **Tooltip:** `"Category: {type_name}"`
      - **Background:** White
      - **Status Indicator:** Based on `active_status`
  - **For all other categories**:
    - Uses `ImageComponent` with:
      - **Category icon**
      - **Tooltip:** `"Category: {type_name}"`
      - **Background:** White
      - **Status Indicator:** Based on `is_iot_enabled`
      - **Device Status:**  
        - `"Online"` if `devices[0].online_status === 1`
        - `"Offline"` otherwise

- **Status Handling:**
  - **If `status === "inactive"`**:
    - Displays the asset name inside a tooltip.
    - Adds a **red "Inactive" status icon** (`StopOutlined`).
  - **If `status !== "inactive"`**:
    - Displays asset name with a tooltip.
    - If IoT-enabled (`is_iot_enabled`):
      - Shows a tooltip with:
        - **Asset Name**
        - **"Last data received time: {last_data_received_time}"**

- **Additional Details:**
  - **Added Date:** Displays formatted `created_on` date (`HH:mm, DD MMM YYYY`).
  - **Rental Tag:**  
    - If `is_rental === true` and `showRentalTag()` returns `true`, shows `"Rent"` tag with a tooltip.
  - **Non-IoT Tag:**  
    - If `is_iot_enabled === 0`, displays `"Non-IoT"` tag.

**✔ Ensures proper visualization of asset details, status, and associated metadata.**

### **Devices**
- **Column Display Condition:** Always visible (No condition restricting its visibility).

- **Rendering Logic:**
  - Extracts **up to 2 device QR codes** for display.
  - If there are **more than 2 devices**, displays a `" + X more"` tag.
  - Clicking `" + X more"` tag **shows a tooltip** with a list of additional devices and their last online time.

- **Displayed Device Information:**
  - **Device QR Code**
  - **Online Status**:
    - Green tag if `online_status === 1` (Online)
    - Default tag if `online_status !== 1` (Offline)
  - **Last Online Time**:
    - Formatted as `"DD MMM YYYY, HH:mm"` using user preferences.
    - Shows `"-"` if no online time available.
  - **Last Data Received Time**:
    - Formatted as `"DD MMM YYYY, HH:mm"` using user preferences.
    - Shows `"-"` if no data received.

- **Tooltip Information:**
  - **For individual devices**:
    - `"Device QR: {device.name}"`
    - `"Last Online Time: {device.last_online_time}"`
    - `"Last Data Received: {device.last_data_received_time}"`
  - **For `+ X more` tag**:
    - Shows a list of remaining devices with their last online time.

- **If no devices exist**, displays `"-"`.

✔ Ensures **clear visibility** of associated devices while **maintaining readability** for multiple linked devices.

### **Customer Name**

- **Rendering Logic:**
  - **If the asset is not linked to a customer (`assetWithoutCustomer === true`)**:
    - **If `thing_goem_id === client_id`** → Displays `"-"` (No customer assigned).
    - **If `thing_goem_id` exists AND `vendor_id !== client_id`** → Displays `"-"` (No edit permissions: ThingManagement:EditCustomer).
    - **If `enabled_features` does not include `"ThingManagement:EditCustomer"`** → Displays `""` (No add option).
    - Otherwise, displays an `"Add Customer"` link:
      - Clicking it **opens a modal to link a customer**.

  - **If the asset is linked to a customer**:
    - Displays `customer_name`.

- **Vendor Tag Display Conditions:**
  - **If `vendor_id !== client_id`, `client_id === 1`, or `enabled_features` include `"IndustryManagement:Dealers"`**:
    - Displays a **Vendor Tag (`AntTag`)**.
    - **Tag Color:**
      - `"Orange"` → If `client_id !== 1` AND `client_id !== vendor_id` (Different vendor).
      - `"Geekblue"` → Otherwise.
    - **Tag Text:**
      - **If `primary_vendor_name` exists**, it is displayed.
      - Otherwise:
        - If `thing_goem_id !== client_id` → Displays `goem_name`.
        - Otherwise, displays `vendor_name`.

- **Hierarchy Popover Display Conditions:**
  - Displays a `HeirarchyPopover` **if**:
    - `primary_vendor_name` exists, **or**
    - `(thing_goem_id !== client_id && goem_name exists)`.
  - **Popover Includes:**
    - **Customer Name** (If `assetWithoutCustomer === false`, otherwise `"-"`).
    - **Vendor Name**.
    - **GOEM Name**.
    - **Primary Vendor Name**.

✔ Ensures **customer name display follows business logic** while allowing **customer linking where applicable**.

### **Partner Name**
- **Column Display Condition:**  
  - **Shown only for end customers.**

- **Rendering Logic:**
  - Displays the **vendor name** (`vendor_name`) associated with the asset.

✔ Ensures **partner details are visible only to end customers**, providing clarity on asset ownership.

### **Data Availability (%) (Last 24 Hours)**
- **Column Display Condition:**  
  - **Show if**:
    - `application_id === 12` **or**
    - `application_id === 17` **and** `client_id === 1819`
    - **Unless** the URL path includes `"/customer-management"`.

- **Sorting:**
  - Sorts numerically based on `data_availability`.

- **Rendering Logic:**
  - Displays a **circular progress indicator** (`AntProgress`) representing **data availability percentage** over the last 24 hours.
  - **Tooltip:**  
    - `"Last 24 Hours Data Availability - {data_availability}%"`  
    - If `data_availability` is `null`, displays `"Last 24 Hours Data Availability"` without a percentage.

- **Progress Bar Properties:**
  - **Value:**  
    - If `data_availability` exists, use its value.
    - Otherwise, default to **`0%`**.
  - **Size:** `40px`
  - **Color:** `#f58740` (Orange)
  - **Format:** Displays the percentage inside the circle.

✔ Ensures **real-time visibility of data availability** while dynamically adjusting visibility based on `application_id` and URL path.

### **Debug**
- **Column Display Condition:** Always visible (No condition restricting its visibility).

- **Rendering Logic:**
  - **If `row_value.devices` exist and contain at least one device**:
    - Displays the **Asset Debug Icon** (`DebugIcon`).
    - Clicking it **opens Debug mode** for the asset.
    - **If `application_id === 12` OR `client_id === 1819`**:
      - Displays an additional **Raw Log Icon** (`raw_log_icon`).
      - Clicking it **opens Debug mode with raw logs enabled**.
    - **If `client_id === 1` OR `enabled_features` include `"ThingManagement:AssetData"`**:
      - Displays an **Asset Data Icon** (`FileTextOutlined`).
      - Clicking it **opens Asset Data Modal** for the respective asset.

  - **If `row_value.devices` is empty or missing**:
    - Displays **disabled icons** with tooltip `"No Device Available"`.
    - **If `application_id === 12` OR `client_id === 1819`**, also shows a disabled **Raw Log Icon**.

✔ Ensures **debugging and raw log access** based on `application_id` and `client_id`, while restricting actions for assets with no linked devices.

### **Configure**
- **Column Display Condition:** Always visible (No condition restricting its visibility).

- **Rendering Logic:**
  - **If `customer_status === "inactive"`**:
    - Shows a **disabled configuration icon** (`SettingOutlined`).
    - Tooltip: `"Customer Inactive"`.

  - **If `customer_status !== "inactive"`**:
    - Displays a **Configurable Asset Icon** based on different conditions:
    
    #### **1. User Has Configuration Access** `Feature Key- ThingManagement:Edit`
    - **If the asset is not linked to a customer (`assetWithoutCustomer === true`)**
      - **And** `customer_id === vendor_id`:
        - Shows a **disabled configuration icon**.
        - Tooltip: `"Please link customer first!"`.
        - Icon displayed depends on:
          - **If mandatory fields are missing:** `ErrorConfigureIcon`
          - **If latitude/longitude is missing:** `LatLongConfigure`
          - **Otherwise:** `ConfigureIcon`

    - **If `is_application_filter === true`**:
      - Displays a **clickable configuration icon** that links to:
        ```
        /customer-management/{customer_id}/applications/{selected_app}/things/{thing_id}/configuration/general
        ```
      - Icon displayed depends on:
        - **If mandatory fields are missing:** `ErrorConfigureIcon`
        - **If latitude/longitude is missing:** `LatLongConfigure`
        - **Otherwise:** `ConfigureIcon`

    - **For all other cases**:
      - Displays a **clickable configuration icon** that links to:
        ```
        /customers/{customer_id}/applications/{application_id}/things/{thing_id}/configuration/general
        ```
      - Icon displayed depends on:
        - **If mandatory fields are missing:** `ErrorConfigureIcon`
        - **If latitude/longitude is missing:** `LatLongConfigure`
        - **Otherwise:** `ConfigureIcon`
      - Additionally, displays **Sync Image** (`getSyncImage(row_data)`).

    #### **2. User Does Not Have Configuration Access**
    - Shows a **disabled configuration icon** with tooltip `"Access Denied"`.
    - Icon displayed depends on:
      - **If mandatory fields are missing:** `ErrorConfigureIcon`
      - **If latitude/longitude is missing:** `LatLongConfigure`
      - **Otherwise:** `ConfigureIcon`

✔ Ensures **access-controlled configuration** while visually indicating issues such as **missing customer links, mandatory fields, or location data**.

### **Onboarding Status (%)**
- **Column Display Condition:**  
  - **Hidden if** `application_id !== 12`.

- **Sorting:**
  - Sorts numerically based on `onboarding_percentage`.

- **Rendering Logic:**
  - **If `category_id === 18`**:
    - Displays a **circular progress bar** (`AntProgress`) representing **onboarding completion percentage**.
    - **Size:** `40px`
    - **Color:** `#f58740` (Orange)
    - **Format:** Displays the percentage inside the circle.

  - **Tooltip (`AntTooltip`)**:
    - Shows a breakdown of **onboarding configuration status**.
    - Each field is displayed with a **green checkmark (✔) if configured**, or a **red cross (❌) if missing**.
    - **Fields Checked:**
      1. **KVA** (`'kva'`)
      2. **Fuel Tank Capacity** (`'capacity'`)
      3. **Phase** (`'dg_type'`)
      4. **Fuel Sensor Type** (`'fuel_sensor_type'`)
      5. **Min. Fuel Fill** (`'min_fuel_fill'`)
      6. **Location** (`'location'`)

  - **If `category_id !== 18`**, nothing is displayed (`''`).

✔ Ensures **clear tracking of onboarding progress** and highlights **missing configurations with visual indicators**.

### **Dashboard**
- **Column Display Condition:**  
  - **Hidden if** `logged_in_user_role_type === 10` (restricted for specific role type).

- **Rendering Logic:**
  - **Dashboard access is allowed (`checkAccessDashboard(row_data)`) if:**
    - `category_id` is in `[44, 85, 21, 22, 23]`, OR
    - The asset has **at least one associated device** (`devices.length > 0`).

  #### **1. If `thing.thing_customer_type === 1`**
  - **Dashboard access is disabled** (No `push_url` or `url` available).
  - Displays a **disabled icon**.
  - Tooltip: `"Device Not Configured"`.

  #### **2. If `url` exists (valid)**
  - Displays a **clickable icon**.
  - Clicking **opens the analytics dashboard** in a **new tab** (`target="_blank"`).
  - Tooltip: `"View Analytics"`.

  #### **3. If `push_url` exists**
  - Displays a **clickable icon**.
  - Clicking **opens the dashboard in the same tab or a new tab** based on `getTarget()`.
  - Tooltip: `"Open Dashboard"`.


  #### **4. If `push_url` and `url` are both missing or invalid**
  - Displays a **disabled icon**.
  - Tooltip: `"Device Not Configured"`.

  #### **5. `url` Logic (`get_url(clientSlug, applicationSlug, data)`)**
  - Generates the **base URL** for the dashboard:
    - If running **locally**, uses `"https://app.datoms.io"`.
    - If running on a **deployed environment**, uses `window.location.protocol + "//" + window.location.host"`.
  - **URL Mapping Logic**:
    - **If `applicationSlug === "pollution-monitoring"`**, returns `"https://taru.aurassure.com/"`.
    - **If `clientSlug` or `applicationSlug` is missing**, returns `""` (Invalid URL).
    - **If `category === 18` (Specific type of asset) and `vendor_id !== 1280`**,  
      - Generates a **detailed view URL**:  
        ```
        {basepath}/enterprise/{customer_id}/{applicationSlug}/detailed-view/?thing_id={data.id}
        ```
    - **For all other cases**, returns:
      ```
      {basepath}/enterprise/{customer_id}/{applicationSlug}
      ```
  - If `url` is valid, `push_url` is set to `""`.

  #### **6. `push_url` Logic (`get_push_url(thing, url)`)**
  - **Determines whether the asset supports dashboard push access:**
    - Supported if:
      - **Category ID** is in `[21, 22, 23, 44, 85, 67, 74, 76, 80, 77, 79, 83, 69, 45, 86, 18, 71, 73, 78, 42]`.
      - **Enabled features** include `"AccessThingData:AccessThingData"` OR the platform is `"datoms-x"`.
      - The asset is **IoT-enabled** (`is_iot_enabled`).
      - `client_id !== 1819`.
      - `vendor_id !== 1074` (if `url` is empty).
  - **Generates the appropriate push URL based on category and view type:**
    - If `category === 18 OR 73` and `vendor_id !== 1280`, returns a **detailed view URL**:
      ```
      {host_app_name}/dg-monitoring/detailed-view{query_string}
      ```
    - If the category supports **map view**, returns:
      ```
      {host_app_name}/dg-monitoring/map-view{query_string}
      ```
    - If the category supports **panel view**, returns:
      ```
      {host_app_name}/dg-monitoring/panel-view{query_string}
      ```

✔ Ensures **controlled access to the dashboard**, dynamically enabling/disabling access based on **user permissions, device configuration, and valid URLs**.

### **Functional Status**
- **Column Display Condition:**  
  - **Hidden if** `is_rental === false` OR `application_id === 12`.

- **Rendering Logic:**
  - **If `is_rental === true`**:
    - Displays the current **functional status** from `functional_status[config]`.
    - Includes a **dropdown menu (`AntDropdown`)** for selecting a different functional status.

  - **Dropdown (`AntDropdown`) Behavior:**
    - Lists all available functional statuses **except the current one**.
    - Clicking a new status triggers `toggleFunc(func, row_data)` to update the functional status.
    - **Dropdown is enabled** (`disabled={false}`) → Users can select a different status.

  - **Dropdown Icon (`CaretDownOutlined`) Behavior:**
    - If access is granted (`1` as a placeholder for access logic):
      - Shows an **active dropdown icon**.
    - Otherwise:
      - Shows a **disabled dropdown icon** with a tooltip `"No access"`.

  - **If `is_rental === false`**, displays `"-"` (No functional status available).

✔ Ensures **rental assets have a selectable functional status**, while non-rental assets do not display this column.

### **Rental Status**
- **Column Display Condition:**  
  - **Hidden if** `is_rental === false` OR `application_id === 12`.

- **Rendering Logic:**
  - **If `rental_status` exists**, displays the value in **uppercase** using `upperCase(config)`.
  - **If `rental_status` is missing or empty**, displays `"-"`.

✔ Ensures **rental assets display their rental status**, while non-rental assets do not show this column.

### **Subscribed On**
- **Column Display Condition:**  
  - **Hidden if** `application_id === 12`.

- **Rendering Logic:**
  - Displays the **subscription date (`subscribed_on`)**.
  - **If hovered**, shows a tooltip with:
    - `"First Data Received on {row_data.first_data_recieved}"`.

✔ Ensures **only relevant applications display subscription details**, while also providing **first data received information** in a tooltip.

### **Status**
- **Column Display Condition:**  
  - **Hidden if** user does not have **deactivation access** (`Access Key- ThingManagement:Deactivate && Feature Key- ThingManagement:ThingDeactivate are not present`).

- **Rendering Logic:**
  - Displays a **toggle switch (`AntSwitch`)** for setting asset status.
  - **Tooltip**:
    - Shows `"Active"` if `status !== "inactive"`.
    - Shows `"Inactive"` if `status === "inactive"`.

- **Switch Behavior:**
  - **If `status === "inactive"`**, the switch is **unchecked**.
  - **If `status !== "inactive"`**, the switch is **checked**.
  - **On status change**, triggers `onUpdateStatus(checked, event, row_data)` to update the asset's status.

✔ Ensures **only users with deactivation access** can modify asset status while providing **clear visual feedback**.

### **Other Status**
- **Column Display Condition:**  
  - **Displayed only if** `category_id` of atleast one asset belongs to a pollution-monitoring application (`1, 21, 22, 23, 102`).

- **Rendering Logic:**  
  - **Displayed only if** `category_id` belongs to a pollution-monitoring application (`1, 21, 22, 23, 102`).
  - Displays **pollution data push status** for:
    1. **DPCC (`D`)**
    2. **SPCB (`S`)**
    3. **CPCB (`C`)**
    4. **DMD (`DMD Icon`)**
  
  #### **1. DPCC (`D`)**
  - **Displayed if** `spcb_data_push_details.server_type === 13` (DPCC Push Enabled).
  - Clicking it **opens DPCC logs**.

  #### **2. SPCB (`S`)**
  - **Displayed if `spcb_data_push_details.server_type > 0` (excluding 13)**.
  - Clicking it **opens SPCB logs**.
  - The **status class** depends on `spcb_status`:
    - `"greena"` → **Active**
    - `"reda"` → **Inactive**
    - `"yellowa"` → **Pending**
    - `"greya"` → **Disabled**

  #### **3. CPCB (`C`)**
  - **Displayed if** `cpcb_data_push_details.push_data === true`.
  - Clicking it **opens CPCB logs**.
  - The **status class** depends on `cpcb_status`:
    - `"greena"` → **Active**
    - `"reda"` → **Inactive**
    - `"yellowa"` → **Pending**
    - `"greya"` → **Disabled**

  #### **4. DMD (`DMD Icon`)**
  - **Displayed for all pollution devices.**
  - Tooltip:
    - `"DMD Not Installed"` if `dmd_status` is missing.
    - `"DMD Status: ONLINE"` if `dmd_status === 1`.
    - `"DMD Status: OFFLINE"` if `dmd_status === 0`.

✔ Ensures **pollution-monitoring devices display SPCB, CPCB, DPCC, and DMD statuses** while dynamically enabling/disabling them based on data availability.

### **Delete**
- **Column Display Condition:**  
  - **Hidden if** user does not have **delete access** (`ThingManagement:Delete is not present`).

- **Rendering Logic:**
  - **If user has delete access (`ThingManagement:Delete is present`) AND `row_data.devices.length === 0`**:
    - Displays a **clickable delete icon (`DeleteOutlined`)**.
    - Clicking it **triggers `onDeleteThing(row_data)`** to delete the asset.
    - Tooltip: `"Delete"`.

  - **If delete conditions are not met, displays a disabled delete icon with a tooltip explaining the restriction:**
    - **If user lacks delete access** → `"You don't have access to it"`.
    - **If `devices.length > 0`** → `"Please delink device to delete"`.
    - **If `status === "active"` AND `devices.length > 0`** → `"Please delink device and deactivate asset to delete"`.

✔ Ensures **only authorized users can delete assets**, while preventing deletion of **active or linked assets**.


### Columns to hide in different portals (Additional conditions).
- **Devices** hide in customer details drawer.
- **Customer Name** hide in customer details drawer and for end customers.
- **Debug** --> dide for end customers.
- **Debug --> Asset Data** hide in customer details drawer.
- **Configure --> Sync Status** hide in customer details drawer and for end customers.
- **Onboarding Status** hide in customer details drawer and for end customers.
- **Subscribed On** hide in customer details drawer.
- **Status** hide in customer details drawer and for end customers.
- **Other Status** hide in customer details drawer.
- **Delete** hide in customer details drawer.


### **Socket Connection Details**

#### **1. `dashboard_successfully_connected`**
- **Triggered when** the socket successfully connects to the dashboard.
- **Behavior:**
  - If `currently_viewing_device_ids` exists, it **subscribes to device updates** by calling:
    ```
    subscribeForDeviceUpdates(true)
    ```
  - Ensures real-time updates for the currently viewed devices.

---

#### **2. `update_device_details`**
- **Triggered when** device details are updated.
- **Processes the received payload (`device_data`)**:
  - Parses the JSON payload.
  - Retrieves `sync_data` from the component state.
  - **If `sync_data[device_id]` has changed** (compared to `device_sync_status` in the payload):
    - Updates the `sync_data` object.
    - Calls `setState({ sync_data })` to update the UI.
- **Purpose:** Keeps the UI in sync with the latest device synchronization statuses.

---

#### **3. `unsubscribe_from_devices_successful`**
- **Triggered when** the socket successfully unsubscribes from devices.
- **Behavior:**
  - Automatically **resubscribes** to the latest list of devices by emitting:
    ```
    subscribe_to_devices
    ```
  - Ensures that the user **continuously receives updates for the currently viewed devices**.

---

#### **4. `subscribeForDeviceUpdates(forceSubscribe, viewing_device_ids)`**
- **Manages device subscriptions** dynamically.
- **Behavior:**
  - **Handles sorting and pagination**:
    - Retrieves `filtered_table_data` (list of assets).
    - Applies **sorting** (if enabled) based on:
      - `"thing_name"` (Asset Name)
      - `"customer_name"`
      - `"data_availability"`
    - Applies **pagination** (if enabled) to determine:
      - `view_start_index` → First visible asset.
      - `view_end_index` → Last visible asset.
  - **Extracts `currently_viewing_device_ids`** from paginated `filtered_things`:
    - If an asset has linked devices, adds **device IDs** to `currently_viewing_device_ids`.
  - **Subscribes only if the device list has changed** (prevents unnecessary emissions):
    - If `forceSubscribe === true` OR `currently_viewing_device_ids` has changed:
      - **Unsubscribes from old devices** (`unsubscribe_from_devices`).
      - Updates `this.currently_viewing_device_ids` with the new list.

---

#### **5. Summary of Actions**
1. **Dashboard Connection (`dashboard_successfully_connected`)**  
   - Triggers subscription if devices are being viewed.
2. **Device Details Update (`update_device_details`)**  
   - Updates synchronization status for each device.
3. **Device Unsubscription (`unsubscribe_from_devices_successful`)**  
   - Ensures the system resubscribes to the correct devices.
4. **Sorting & Filtering (`subscribeForDeviceUpdates`)**  
   - Converts relevant fields to lowercase, removes archived stations.
   - Sorts based on active sorting preferences.
5. **Pagination (`subscribeForDeviceUpdates`)**  
   - Determines the visible devices based on current pagination.
6. **Subscription Logic (`subscribeForDeviceUpdates`)**  
   - Subscribes only if the device list changes.
   - Unsubscribes from outdated devices before subscribing to new ones.

✔ Ensures **real-time device tracking**, minimizes unnecessary socket emissions, and keeps the **dashboard updated with accurate device statuses**.


