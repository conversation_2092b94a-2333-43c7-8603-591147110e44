# Listing Page Flow Architecture

## Initialize State
- Full Page Loading: `true`
- Table Loading: `true`
- Pagination State: Initialized from URL or default to 1
- Sorting State: Initialized from URL or default to none

## Page Configuration
- Calls API to fetch base page config in `useEffect<props.pageId>`
- Page config includes `dynamicComponentConfig` flag to determine flow path
- Full page Loading stops

```javascript
// Example page config structure
{
  dynamicComponentConfig: {
    enabled: true,
    dependent_filter_keys: ["client_id", "region"]  // Filters that trigger config refresh
  },
  filters: [
    // filter configurations
  ],
  // other page settings
}
```

## Filter Component Rendering
- Calls API to fetch filter options in `useEffect<[]>`
- Checks for any selected values in URL or default value as per config
- Sets the values for `filters`
- `filterCallback` function is called in `useEffect<[filters]>`

## Filter Callback Processing
- In the `filterCallback` function, `page_no` is set as per the value in URL or default to 1
- Get all filter values using `filterRef?.current?.getFilters()`
- Check if `dynamicComponentConfig.enabled` flag is true in page config
- If enabled, compare current values of `dependent_filter_keys` with previous filter state

## Conditional Flow Branching
- If `dynamicComponentConfig.enabled === true`:
  - Check if any values in `dependent_filter_keys` have changed:
    - If changed:
      - Set `componentConfigLoading` to `true`
      - Fetch component configs based on selected filters
      - Set `componentConfigLoading` to `false`
      - Cache component configs using dependent filter values as key
      - Store component configs in state
    - If not changed:
      - Use cached component configs from last fetch
  - Proceed to data loading
- If `dynamicComponentConfig.enabled === false`:
  - Use component configs already present in page config
  - Proceed directly to data loading

## Data Loading
- Calls API to fetch table data with appropriate configs
- Table Loading: `false`

## Rendering
- Table and other components render with their respective configurations

## Flow Diagram

```mermaid
flowchart TD
    Start([Start]) --> InitLoading[Initialize Loading States]
    InitLoading --> FetchPageConfig[Fetch Page Config]
    FetchPageConfig --> PageLoaded{Config Loaded?}
    PageLoaded -->|Yes| StopPageLoading[Stop Full Page Loading]
    PageLoaded -->|No| FetchPageConfig
    
    StopPageLoading --> RenderFilters[Render Filter Component]
    RenderFilters --> FetchFilterOptions[Fetch Filter Options]
    FetchFilterOptions --> CheckUrlParams[Check URL Parameters]
    CheckUrlParams --> SetFilters[Set Filter Values]
    SetFilters --> CallFilterCallback[Call Filter Callback]
    
    CallFilterCallback --> SetPagination[Set Pagination]
    SetPagination --> IsDynamicEnabled{Dynamic Config Enabled?}
    
    IsDynamicEnabled -->|No| UseExistingConfig[Use Configs from Page Config]
    IsDynamicEnabled -->|Yes| CheckDependentFilters[Check If Dependent Filters Changed]
    
    CheckDependentFilters -->|No Change| UseCache[Use Cached Component Configs]
    CheckDependentFilters -->|Changed| StartCompConfigLoading[Start Component Config Loading]
    
    StartCompConfigLoading --> FetchComponentConfigs[Fetch Component Configs Based on Filters]
    FetchComponentConfigs --> CompConfigLoaded{Configs Loaded?}
    CompConfigLoaded -->|Yes| CacheComponentConfigs[Cache Component Configs]
    CompConfigLoaded -->|No| FetchComponentConfigs
    
    CacheComponentConfigs --> StopCompConfigLoading[Stop Component Config Loading]
    UseCache --> StartTableLoading[Start Table Data Loading]
    UseExistingConfig --> StartTableLoading
    StopCompConfigLoading --> StartTableLoading
    
    StartTableLoading --> FetchTableData[Fetch Table Data With Configs]
    FetchTableData --> TableDataLoaded{Data Loaded?}
    TableDataLoaded -->|Yes| StopTableLoading[Stop Table Loading]
    TableDataLoaded -->|No| FetchTableData
    
    StopTableLoading --> RenderComponents[Render Table and Components]
    RenderComponents --> End([End])
    
    %% When filters change
    SetFilters -->|Filter Changes| CallFilterCallback
    
    %% When pagination changes
    StopTableLoading -->|Pagination/Sorting Changes| StartTableLoading
```
This unified architecture:

1. Works for both your original and new flow requirements
2. Uses a single flag `dynamicComponentConfig` in the page config to determine which path to take
3. Handles the conditional component configuration fetching step
4. Maintains proper loading states for each phase
5. Reuses the same flow pattern for both scenarios with minimal branching


```json
{
  "dynamicComponentConfig": {
    "enabled": true,
    "dependent_filter_keys": ["site_type"],
    "config_api": "site_type_detials"
  },
  "page_url": "list-view/sites",
  "filters": [
    {
      "url_name": "site_type",
      "label": "Site Type",
      "filter_api": "site_type",
      "placeholder": "Site Type",
      "showSearch": true,
      "allowClear": false,
      "no_outside_label": true,
      "showSingleOption": true,
      "is_outside_filter_drawer": true,
      "is_inside_filter_drawer": false,
      "select_first_option": true,
      "dependent_filters": ["site_status"]
    },
    {
      "url_name": "site_status",
      "label": "Site Status",
      "filter_api": "site_status",
      "placeholder": "Site Status",
      "showSearch": true,
      "allowClear": false,
      "no_outside_label": true,
      "showSingleOption": true,
      "is_outside_filter_drawer": true,
      "is_inside_filter_drawer": false
    },
    {
      "type": "tree_select",
      "feature_key": "UserManagement:Territory",
      "label": "Territory",
      "url_name": "territories",
      "key": "territories",
      "query_key": "info.territory_id",
      "filter_api": "territories",
      "is_options_dynamic": true,
      "component_props": {
        "treeData": [],
        "value": [],
        "treeDefaultExpandAll": true,
        "treeCheckable": true,
        "showCheckedStrategy": "SHOW_PARENT",
        "treeCheckStrictly": true,
        "maxTagCount": 0,
        "maxTagPlaceholder": "function(omittedValues: number[]) { return omittedValues.length + ` territor${omittedValues.length === 1 ? 'y' : 'ies'} selected`}",
        "placeholder": "Select territories",
        "filterTreeNode": "function(search: string, item: { title: string }) { return item.title.toLowerCase().indexOf(search.toLowerCase()) >= 0;}"
      },
      "no_outside_label": true,
      "is_outside_filter_drawer": true,
      "is_inside_filter_drawer": false
    },
    {
      "type": "search",
      "placeholder": "Search Sites",
      "size": "default"
    }
  ],
  "actions": [
    {
      "key": "site_asset_switch",
      "component_props": {
        "options": [
          {
            "value": "site",
            "label": "Site"
          },
          {
            "value": "asset",
            "label": "Asset",
            "link": "list-view/assets"
          }
        ],
        "value": "site"
      },
      "component": "SegmentedTabs",
      "position": "left"
    },
    {
      "key": "download",
      "title": "Download"
    }
  ]
}
```