# **Customer List Page Implementation Guide**

## **Writing Configuration JSON**
- Define the filters and columns.
- Define add button.
- Define the api config for each action button.
- Write proper conditions to show/hide columns or filters or bulk action buttons/single action buttons.

## **Component/Logic separation**
### 1. `AddCustomer` Component
- Separate the route.
- Details passed as props needs to be fetched inside the component.

### 2. `Customer Details` Component
- Separate the route.
- Details passed as props needs to be fetched inside the component.

### 3. `Merge Customers` Component
- Copy the jsx snippet and functions required for the component.
- Provision to pass required values via props.
- Handle via predefined actions through config.

### 4. `Migrate Customers` Component
- Copy the jsx snippet and functions required for the component.
- Provision to pass required values via props.
- Handle via predefined actions through config.

### 5. Deactivate/Activate Customer
- Can be handled via config, for details refer to `Action-Handling.md`

### 6. Archive Customer
- Can be handled via config, for details refer to `Action-Handling.md`

### 7. Delete Customer
- Can be handled via config, for details refer to `Action-Handling.md`


### Logic Conditions

1. Provision for delears node in territory filter if dealer management is enabled.

## Add Buttons
### Add Customer Button
- If feature/access includes "IndustryManagement:Add"
- On clicking, opens `AddCustomer` component.
### Bulk Add Customer Button
- If feature/access includes "IndustryManagement:Add"
- On clicking, opens `AddCustomer` component.


## Filters:
    - Account Type:
      - only show to applicaiton_id 12
      - defualt value: 'production'
    - Territories: 
      - If territory feature is enabled or application_id is 12
      - default value: NA
    - Partners:
      - If partners are available
      - default value: NA
    - Applications:
      - If multiple applications are available
      - default value: NA
    - Customers:
      - If customers are available
      - default value: NA
    - Customer Status:
      - only for datoms-x
    - Customer Type:
      - If dealer management is enabled or for datoms-x
    - Customer Success:
      - only for datoms-x
    - Sales Owner:
      - only for datoms-x


## Table Columns

### **Customer Name**
- **Column Display Conditions:**
  - Always visible.

- **Sorting:**
  - Alphabetically sorts `name` in **ascending or descending order**.

- **Rendering Logic:**
  - Displays the **customer name (`name`)**.
  - **If `is_vendor === 1`**, appends:
    - `" [R]"` if `vendor_id !== 1`
    - `" *"` otherwise.

  - **Vendor Tag Display Conditions:**
    - **If `is_vendor !== 1` OR `vendor_id !== client_id`**, displays a **Vendor Tag (`AntTag`)**:
      - **Tag Text:**
        - **If `client_id === 1` AND `parent_vendor_id` exists**, uses `customerNameMap[parent_vendor_id]`.
        - Otherwise, displays `vendor_name`.
      - **Hierarchy Popover (`HeirarchyPopover`)**:
        - **If `client_id === 1` AND `parent_vendor_id` exists**, displays:
          - `Customer Name`
          - `Vendor Name`
          - `Parent Vendor Name`

  - **Timezone Display:**
    - Uses **customer's `client_time_zone`** if available, otherwise defaults to `"Asia/Kolkata"`.

  - **Status Indicators:**
    - **If `status === "inactive"`**, displays a **red inactive icon (`StopOutlined`)** with tooltip `"Inactive"`.
    - **If `status === "archived"`**, displays an **archive icon (`archive_image_red`)** with tooltip `"Archived"`.

  - **Date Added Display:**
    - If `date_added_at !== 0`, shows `"Date added {D MMM, YYYY}"`.

  - **Application-Specific Display:**
    - **If `application_id !== 17` OR feature `"IndustryManagement:Dealers"` is enabled**, displays the **Vendor Tag (`AntTag`)**.

  - **Clickable Interaction:**
    - Clicking the customer name **opens the Customer Drawer in "view" mode**.

✔ Ensures **customer hierarchy is properly displayed**, while including **status indicators, timestamps, and vendor relations**.

### **Rating**
- **Column Display Conditions:**
  - Always visible.

- **Sorting:**
  - **Sorting is disabled** (`sorter` is commented out).
  - If needed, sorting could be enabled using `a.rating - b.rating`.

- **Rendering Logic:**
  - **If `rating > 0`**:
    - Displays **a star icon (`Star`) for each rating point**.
    - The number of stars **matches the `rating` value**.
  - **If `rating === 0` or missing**, displays `"-"`.

✔ Ensures **visual representation of ratings using star icons**, while handling cases where no rating is available.

### **Mobile Number**
- **Column Display Condition:**  
  - **Shown only if** `isRentalStore === true`.

- **Rendering Logic:**
  - **If `user_lists[0].dcc_mobiles` exists**, displays the **mobile number**.
  - **If `dcc_mobiles` is missing or empty**, displays an empty string (`""`).

✔ Ensures **mobile numbers are displayed only in rental store contexts**, avoiding unnecessary columns in other views.

### **Mobile Number**
- **Column Display Condition:**  
  - **Shown only if** `isTestDrive === true`.

- **Rendering Logic:**
  - **If `mobile_no` exists**, displays the **mobile number**.
  - **If `mobile_no` is missing or empty**, displays `"-"`.

✔ Ensures **mobile numbers are displayed only in test drive contexts**, avoiding unnecessary columns in other views.

### **Account Type & Class Type**
- **Column Display Condition:**  
  - **Shown only if** `client_id === 1`.

- **Rendering Logic:**
  - **Account Type (`account_type`)**:
    - Displays the **account type name** based on `accountTypeName[val]`.
    - If `val` is missing or undefined, displays `"-"`.

  - **Class Type (`class_type`)**:
    - Displays the **class type name** based on `classTypeName[val]`.
    - If `val` is missing or undefined, displays `"-"`.

✔ Ensures **account and class type details are shown only to the main client (`client_id === 1`)**, preventing unnecessary display for other users.

### **Users**
- **Column Display Condition:**  
  - Always visible.

- **Sorting:**
  - Sorts numerically based on `user` count (`a.user - b.user`).

- **Rendering Logic:**
  - Displays a **badge (`AntBadge`)** showing the number of users.
  - **Badge Behavior:**
    - **Always visible**, even if `user === 0` (`showZero=true`).
    - **If `user > 99999`**, caps the display at `99999+`.
    - **Dynamic Badge Sizing:**
      - `"multiple-5"` → If `user > 9999`
      - `"multiple-4"` → If `user > 999`
  - **User Image:**
    - Displays an **icon (`user_image`)** representing users (`25x25` size).
  - **On Click Behavior:**
    - Opens the **Customer Drawer** in `"view"` mode, specifically in `"user_view"`.

✔ Ensures **clear user count representation with an interactive badge**, making it easy to view user details.

### **Assets**
- **Column Display Condition:**  
  - **Hidden if** `client_id === 392`.

- **Sorting:**
  - Sorts numerically based on `thing_count` (`a.thing_count - b.thing_count`).

- **Rendering Logic:**
  - Displays a **badge (`AntBadge`)** showing the number of assets.
  - **Badge Behavior:**
    - **Always visible**, even if `thing_count === 0` (`showZero=true`).
    - **If `thing_count > 99999`**, caps the display at `99999+`.
    - **Dynamic Badge Sizing:**
      - `"multiple-5"` → If `thing_count > 9999`
      - `"multiple-4"` → If `thing_count > 999`
  - **Asset Image:**
    - Displays an **icon (`thing_image`)** representing assets (`25x25` size).
  - **On Click Behavior:**
    - Opens the **Customer Drawer** in `"view"` mode, specifically in `"things_view"`.

✔ Ensures **interactive asset count representation with a clear display**, while excluding it for client `392`.

### **Devices**
- **Column Display Condition:**  
  - Always visible.

- **Sorting:**
  - Sorts numerically based on `devices` (`a.devices - b.devices`).

- **Rendering Logic:**
  - Displays a **badge (`AntBadge`)** showing the number of devices.
  - **Badge Behavior:**
    - **Always visible**, even if `devices === 0` (`showZero=true`).
    - **If `devices > 99999`**, caps the display at `99999+`.
    - **Dynamic Badge Sizing:**
      - `"multiple-5"` → If `devices > 9999`
      - `"multiple-4"` → If `devices > 999`
  - **Device Image:**
    - Displays an **icon (`device_image`)** representing devices (`25x25` size).
  - **On Click Behavior:**
    - Opens the **Customer Drawer** in `"view"` mode, specifically in `"device_view"`.

✔ Ensures **clear and interactive device count representation**, making it easy to access device details.

### **Software License**
- **Column Display Condition:**  
  - **Shown only if any subscribed application by the partner has software license enabled.**

- **Sorting:**
  - Sorts numerically based on `sw_license_count` (`a.sw_license_count - b.sw_license_count`).

- **Rendering Logic:**
  - Displays a **badge (`AntBadge`)** showing the number of software licenses.
  - **Badge Behavior:**
    - **If `sw_license_count === 0`**, displays `"N/A"` instead of a number.
    - **If `sw_license_count > 99999`**, caps the display at `99999+`.
    - **Dynamic Badge Sizing:**
      - `"multiple-5"` → If `sw_license_count > 9999`
      - `"multiple-4"` → If `sw_license_count > 999`
  - **Styling Behavior:**
    - **If `sw_license_count === 0`**, applies `"soft-license-outer-disable"` and `"soft-license-inner-disable"` styles to indicate **disabled state**.
  - **Software License Image:**
    - Displays an **icon (`software_license_image`)** representing software licenses (`25x25` size).
  - **On Click Behavior:**
    - If `sw_license_count > 0`, clicking the badge **opens the Customer Drawer** in `"view"` mode, specifically in `"software_license"`.

✔ Ensures **software license details are displayed only if enabled for the partner**, while handling cases with no licenses.

### **Customer Success**
- **Column Display Condition:**  
  - **Shown only if** the selected **Account Type** in the filter is `"Production"`.

- **Rendering Logic:**
  - **If `customer_success_users` list is empty or undefined**, displays `"-"` (No users assigned).
  - **If `customer_success_users` contains users**:
    - Displays **each user as a tag (`AntTag`)**.
    - The tag text is retrieved from `userNameMap[user_id]`.
    - **Users are arranged in a flexible, centered layout** (`flex-wrap: wrap`, `rowGap: 10px`).

✔ Ensures **customer success representatives are displayed only for Production accounts**, while handling cases with no assigned users.

### **Sales Owner**
- **Column Display Condition:**  
  - **Shown only if** the selected **Account Type** in the filter is `"Production"`.

- **Rendering Logic:**
  - **If `sales_owner_users` list is empty or undefined**, displays `"-"` (No users assigned).
  - **If `sales_owner_users` contains users**:
    - Displays **each user as a tag (`AntTag`)**.
    - The tag text is retrieved from `userNameMap[user_id]`.
    - **Users are arranged in a flexible, centered layout** (`flex-wrap: wrap`, `rowGap: 10px`).

✔ Ensures **sales owner information is displayed only for Production accounts**, while handling cases with no assigned users.

### **Actions**
- **Column Display Condition:**  
  - Always visible.

- **Column Width:**
  - If `application_id === 12` → Width is `15%` or `160px`.
  - Otherwise → Width is `10%` or `110px`.

- **Rendering Logic:**
  - Displays a **set of conditional icons** (Activate, Deactivate, Archive, Migrate, Delete) based on:
    - **User permissions (`getViewAccess`)**
    - **Application ID**
    - **User role (`user_id`)**
    - **Record status**
    - **Customer type & partner mapping**

#### **Icons & Conditions**

- ✅ **Activate**
  - Shown if:
    - `application_id === 12`
    - `record.status === "inactive"` **OR** (`status === "archived"` **AND** `user_id ∈ [41, 2222]`)
    - User has **`IndustryManagement:Deactivate`** permission.
  - Clicking triggers: `confirmReActiveIndustry(record)`

- 🚫 **Deactivate**
  - Shown if:
    - `application_id === 12`
    - `record.status === "active"`
    - User has **`IndustryManagement:Deactivate`** permission.
  - Clicking triggers: `confirmDeactivateIndustry(record)`

- 📦 **Archive**
  - Shown if:
    - `application_id === 12`
    - `record.status ∈ ["active", "inactive"]`
    - `user_id ∈ [41, 2222]`
    - User has **`IndustryManagement:Archive`** permission.
  - Clicking triggers: `confirmArchiveIndustry(record)`

- 🔁 **Migrate**
  - Shown if:
    - `application_id === 12`
    - `record.status === "active"`
    - `record.customer_type` includes `5`
    - `record.vendor_id !== 1`
    - `partners_customer_type_map[record.vendor_id]` is an array and does **not** include `4`
    - User has **`IndustryManagement:Migrate`** permission.
  - Clicking triggers: `confirmMigrateIndustry(record)`

- 🗑️ **Delete**
  - Shown if:
    - `record.status === "active"`
  - Clicking triggers:
    - `confirmDeleteIndustryGroup(record)` if `record.category === "Group"`
    - Otherwise, `confirmDeleteIndustry(record)`

✔ This column provides **dynamic access-based controls** for customer/partner records, ensuring **only relevant actions are shown** based on user role and record state.

## Special Columns Conditions

### Test Drive
1. Customer Name
2. Rating
3. Mobile Number
4. Actions

### Rental Store
1. Customer Name
2. Rating
3. Mobile Number: 
4. Actions
