
---

## **Step 1: Define J<PERSON>N Structure for Socket Updates**
Your JSON should include:
- **`socketConfig`** → Defines which socket event to listen to.
- **`updateMapping`** → Maps payload keys to column data for updates.

### **Updated JSON (Stored in Database)**
```json
{
  "pageTitle": "Device Dashboard",
  "filters": [
    { "label": "Status", "type": "dropdown", "options": ["Active", "Inactive"] }
  ],
  "addButtons": [
    { "text": "Add Device", "key": "add_device", "popupType": "custom", "popupComponent": "AddDevicePopup" }
  ],
  "tableConfig": {
    "columns": [
      {
        "title": "Device Name",
        "dataIndex": "deviceName",
        "key": "deviceName"
      },
      {
        "title": "Status",
        "dataIndex": "status",
        "key": "status",
        "socketConfig": {
          "event": "device_status_update",
          "updateMapping": {
            "status": "status",
            "lastUpdated": "last_updated"
          }
        }
      },
      {
        "title": "Firmware Version",
        "dataIndex": "firmwareVersion",
        "key": "firmwareVersion",
        "socketConfig": {
          "event": "firmware_update",
          "updateMapping": {
            "firmwareVersion": "firmware_version"
          }
        }
      },
      {
        "title": "Actions",
        "dataIndex": "actions",
        "key": "actions",
        "rowActions": [
          {
            "text": "Delete",
            "key": "delete_device",
            "popupType": "confirm",
            "popupConfig": {
              "title": "Are you sure you want to delete this device?",
              "onOkFunction": "function_deleteDevice"
            },
            "apiConfig": {
              "apiFunction": "function_deleteDevice",
              "args": ["row.deviceId"]
            },
            "icon": "🗑️"
          }
        ]
      }
    ]
  }
}
```

---
## **Step 2: Implement the Functional Component with Socket Integration**
- **Uses socket to listen for events**.
- **Updates table data dynamically when the socket emits data**.
- **Handles API calls for row actions**.
- **Uses the same JSON for bulk actions and row actions**.

### **React Functional Component (`Dashboard.tsx`)**
```tsx
import React, { useEffect, useState } from "react";
import { Table, Modal, Button } from "antd";
import { io } from "socket.io-client";

const SOCKET_URL = "your-socket-url-here"; // Replace with actual socket URL

interface Device {
  deviceId: string;
  deviceName: string;
  status: string;
  firmwareVersion: string;
  lastUpdated?: string;
}

const Dashboard: React.FC<{ config: any }> = ({ config }) => {
  const [data, setData] = useState<Device[]>([]);
  const [socket, setSocket] = useState<any>(null);

  // Function to update table data from socket payload
  const updateTableData = (event: string, payload: any) => {
    setData(prevData =>
      prevData.map(row => {
        if (row.deviceId === payload.deviceId) {
          const columnToUpdate = config.tableConfig.columns.find(
            (col: any) => col.socketConfig?.event === event
          );
          if (columnToUpdate) {
            const { updateMapping } = columnToUpdate.socketConfig;
            Object.keys(updateMapping).forEach(payloadKey => {
              const columnKey = updateMapping[payloadKey];
              (row as any)[columnKey] = payload[payloadKey];
            });
          }
        }
        return row;
      })
    );
  };

  // Function to handle row actions
  const handleRowAction = (actionConfig: any, row: Device) => {
    showPopup(actionConfig, [row], {});
  };

  // Function to show confirm popup
  const showPopup = (actionConfig: any, selectedRows: Device[], context: any) => {
    if (actionConfig.popupType === "confirm") {
      Modal.confirm({
        title: actionConfig.popupConfig.title,
        content: actionConfig.popupConfig.content || "",
        onOk: () => executeApiAction(actionConfig, selectedRows, context)
      });
    }
  };

  // Function to execute API action
  const executeApiAction = async (actionConfig: any, selectedRows: Device[], context: any) => {
    if (!actionConfig.apiConfig) return;

    const { apiFunction, args } = actionConfig.apiConfig;
    const evaluatedArgs = args.map(arg =>
      new Function("selectedRows", "context", `return ${arg};`)(selectedRows, context)
    );

    // Simulated API function call
    console.log(`Calling API function: ${apiFunction}`, evaluatedArgs);
    // await apiFunctions[apiFunction](...evaluatedArgs);
  };

  useEffect(() => {
    const newSocket = io(SOCKET_URL);
    setSocket(newSocket);

    newSocket.on("connect", () => {
      newSocket.emit("connect_dashboard_to_socket");
      console.log("Connected to socket");
    });

    newSocket.on("dashboard_successfully_connected", () => {
      console.log("Dashboard connected to socket");
    });

    // Listen to socket events dynamically from JSON
    config.tableConfig.columns.forEach((col: any) => {
      if (col.socketConfig) {
        newSocket.on(col.socketConfig.event, (payload: any) => {
          updateTableData(col.socketConfig.event, payload);
        });
      }
    });

    return () => {
      newSocket.disconnect();
    };
  }, [config]);

  // Parse columns from JSON
  const parsedColumns = config.tableConfig.columns.map((col: any) => {
    if (col.rowActions) {
      return {
        ...col,
        render: (_: any, row: Device) => (
          <div>
            {col.rowActions.map((action: any) => (
              <Button key={action.key} onClick={() => handleRowAction(action, row)}>
                {action.icon}
              </Button>
            ))}
          </div>
        )
      };
    }
    return col;
  });

  return <Table columns={parsedColumns} dataSource={data} rowKey="deviceId" />;
};

export default Dashboard;
```

---

## **How It Works**
### **1. JSON Defines the Page Structure**
- **Filters, Buttons, Table Columns** are all stored in the database.
- Each **column can have a `socketConfig`** that:
  - Defines the **socket event name** (`"event": "device_status_update"`).
  - Maps **socket payload keys to table columns** (`"updateMapping": { "status": "status" }`).

### **2. Component Reads JSON and Subscribes to Sockets**
- **Listens to socket events dynamically** based on JSON.
- When a **socket event arrives**, it:
  - Finds the column that has `socketConfig.event`.
  - Updates the mapped column values in the table.

### **3. Supports Row Actions (Delete, Edit, etc.)**
- The **"Actions"** column has a **delete button** stored in JSON.
- When clicked, it **shows a confirmation popup** and **executes the API action**.

---

## **Why This Approach is Ideal**
✅ **Dynamic & Configurable** – No hardcoded API calls or table structures.  
✅ **Socket Integration via JSON** – Updates table in real time.  
✅ **Reusable Components** – Bulk actions and row actions use the same logic.  
✅ **Scalable** – Easily add more socket updates, columns, and actions.  

---

## **Next Steps**
1. **Store this JSON in your database** and fetch it dynamically.
2. **Replace `SOCKET_URL` with your actual WebSocket URL**.
3. **Implement real API calls** in `apiFunctions`.

Would this fully solve your requirements? 🚀 Let me know if you need any tweaks!