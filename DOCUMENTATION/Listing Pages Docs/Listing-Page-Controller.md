
# **Listing Page Implementation Guide**

## **1. Create `ListingPage` Controller**
**Similar to `GravityReport` - report controller**
- Create a controller: **`ListingPage`** (*for all listing pages*).
- Refer to the code of **`GravityReport`**.
- Remove unwanted states/functions from **`GravityReport`**, as they are not required in **`ListingPage`**.
  
#### **Steps:**
1. **Copy** the entire code of `index.tsx`.
2. **Remove** all functions and states.
3. **Keep only** the `.tsx` render code (this will be modified as needed).
4. **Copy** the `style.less` file.
5. **Update the main page ID** in both `.tsx` and `.less` files → **`listing_page_controller`**.

---

## **2. Components Required in `ListingPage`**
### **➡️ `PageFilter` Component [Not Implemented in `GravityReport`]**
- **Define a key** in `page_config.json` for it.
- Example structure:
  ```json
  "filters": [{}]
  ```
- Send this as props to **`PageFilter`**.
- Implement a **callback function** and pass it to `PageFilter` to handle filter changes (e.g., calling the device listing API based on selected filters).

### **➡️ `Add Button` Component [New]**
- **Define a key** in `page_config.json` for it.
- Example structure:
  ```json
  "add_button": [{}]
  ```
- Each button item will be a **link to a page**, as defined in `page_config.json`.

### **➡️ `ConfigurableTable` Component [Used in `GravityReport`]**
- **Copy the render code snippet** from `GravityReport`.

---

## **3. Define Required States in `ListingPage`**
- `filters`
- `add_button`
- `table_config`
- `page_config`
- `pagination`

---

## **4. Write Logic & Functions**
### **➡️ `getPageConfig` Function** (*Refer to `GravityReport`*)  
- Fetch `pageConfig` and set it to the `page_config` state.
- Use `pageConfig` to set:
  - `filters` state.
  - `add_button` state.

### **➡️ Function to Fetch Page Data**
- Based on `page_config`, fetch data for the table (*refer to `GravityReport`*).
- Set the fetched data to `table_config` state, along with table configurations.
- Set page loading to `false`.

---

## **5. Define `page_config.json`**
- Similar to `GravityReport`, but with unwanted keys removed.

---

## **6. Table Configuration Changes**
### **`showIf` Key**
- Added to each column item to conditionally show/hide the column.
- After fetching `page_config`, filter the table config based on `showIf` conditions.
### **`Handling Actions`**
- Detailed explanation for both bulk and single row actions are provided in the file `Action-Handling.md`.

### **➡️ Example Column with `showIf` Condition**
```json
{
  "align": "left",
  "fixed": "left",
  "title": "Asset",
  "width": 200,
  "ellipsis": true,
  "dataIndex": "asset",
  "pdf_title": "Asset",
  "colPathExp": "name",
  "showIf": {
    "and": [
      { "application_id": 12 },
      {
        "or": [{ "user_id": 42 }, { "client_id": 5 }]
      },
      { "user_access_keys": "AccessControl:Delete" }
    ]
  }
}
```

---

## **7. `filterColumns` Function**
### **➡️ Example Code Snippet**
```js
const evaluateCondition = (condition, context) => {
  if (Array.isArray(condition.and)) {
    return condition.and.every(subCondition => evaluateCondition(subCondition, context));
  }

  if (Array.isArray(condition.or)) {
    return condition.or.some(subCondition => evaluateCondition(subCondition, context));
  }

  const [[key, value]] = Object.entries(condition);

  // Check in context dynamically
  if (Array.isArray(context[key])) {
    return context[key].includes(value); // Handles arrays like `user_access_keys`
  }

  return context[key] === value;
};

// Generalized Filtering Function
const filterColumns = (columns, context) => {
  return columns.filter(column => {
    if (!column.showIf) return true;
    return evaluateCondition(column.showIf, context);
  });
};

// Example Usage
const globalContext = { application_id: 12, client_id: 5, user_id: 42, user_access_keys: ["AccessControl:Delete", "SomeOtherKey"] };

const filteredColumns = filterColumns(columns, globalContext);
console.log(filteredColumns);
```

---


---

## **8. Full `page_config.json` Example**
````json
{
  "table": [
    {
      "title": "",
      "apiType": "device-list",
      "colSpan": 24,
      "apiConfig": {
        "api_query": {
          "parameters": "true",
          "thing_category": "18"
        }
      },
      "resizable": false,
      "tableProps": {
        "size": "middle",
        "columns": [
          {
            "align": "left",
            "fixed": "left",
            "title": "Asset",
            "width": 200,
            "ellipsis": true,
            "dataIndex": "asset",
            "pdf_title": "Asset",
            "colPathExp": "name",
            "showIf": {
              "and": [
                { "application_id": 12 },
                {
                  "or": [{ "user_id": 42 }, { "client_id": 5 }]
                },
                { "user_access_keys": "AccessControl:Delete" }
              ]
            }
          },
          {
            "key": "eng_sl_no",
            "align": "left",
            "title": "Engine Serial No",
            "checked": false,
            "ellipsis": false,
            "dataIndex": "eng_sl_no",
            "pdf_title": "Engine Serial No",
            "colPathExp": "thing_details.engine_sl_no"
          }
        ],
        "expandable": false,
        "pagination": {
          "pageSize": 50,
          "hideOnSinglePage": false
        },
        "rowSelection": false
      },
      "dataPathExp": "devices",
      "downloadable": false,
      "preferenceKeys": ["listing", "devices"],
      "defaultFixedCount": 1,
      "searchableColumns": [],
      "totalCountPathExp": "",
      "disableCustomization": false
    }
  ],
  "title": "Lifetime Report"
}
````