
## Action Types
1. **Show a simple confirmation modal** for `popupType: "confirm"`.
2. **Render a pre-defined complex component** for `popupType: "custom"`, mapped via a component registry.

---

## **JSON Configuration**
- `"popupType": "confirm"` → Standard confirmation modal.
- `"popupType": "custom"` → Uses a predefined complex UI component.
- `"popupComponent": "FirmwareUpload"` → Maps to a pre-registered React component.

```json
[
  {
    "text": "Unassign Device",
    "key": "unassign_device",
    "popupType": "confirm",
    "popupConfig": {
      "title": "Do you want to un-assign all selected devices?",
      "content": "",
      "onOkFunction": "unassignDevice"
    },
    "apiConfig": {
      "apiFunction": "unassignDevice",
      "args": [
        "selectedRows.map(row => row.deviceId)", 
        "context.clientId"
      ]
    },
    "type": "ghost",
    "disabled": false
  },
  {
    "text": "Upload Firmware",
    "key": "firmware_upload",
    "popupType": "custom",
    "popupComponent": "FirmwareUpload",
    "apiConfig": {
      "apiFunction": "assignUserRole",
      "args": [
        "selectedRows.map(row => row.userId)",
        "formValues.role",
        "context.organizationId"
      ]
    },
    "type": "ghost",
    "disabled": false,
    "showIf": {
      "and": [
        { "application_id": 12 },
        {
          "or": [{ "user_id": 42 }, { "client_id": 5 }]
        },
        { "user_access_keys": "AccessControl:Delete" }
      ]
    }
  }
]
```

---

## **Step 1: Define a Component Registry**
We'll define a **map of pre-registered UI components** for `popupType: "custom"`.

```javascript
import { Modal } from "antd";
import FirmwareUpload from "./FirmwareUpload";

const popupComponents = {
  FirmwareUpload: FirmwareUpload
};
```

---

## **Step 2: Handle Popups (Confirmation + Custom UI)**
```javascript
const showPopup = (actionConfig, selectedRows, context) => {
  if (!actionConfig.popupType) {
    executeApiAction(actionConfig, selectedRows, {}, context);
    return;
  }

  const { popupType, popupConfig, popupComponent } = actionConfig;

  if (popupType === "confirm") {
    Modal.confirm({
      title: popupConfig.title,
      content: popupConfig.content || "",
      onOk: () => executeApiAction(actionConfig, selectedRows, {}, context)
    });
  } else if (popupType === "custom" && popupComponent) {
    const CustomComponent = popupComponents[popupComponent];
    if (CustomComponent) {
      Modal.confirm({
        title: "Custom Popup",
        content: <CustomComponent onSubmit={(formValues) => executeApiAction(actionConfig, selectedRows, formValues, context)} />,
        footer: null // Custom component handles its own actions
      });
    } else {
      console.error(`Popup component "${popupComponent}" not found.`);
    }
  }
};
```

---

## **Step 3: Execute API Call After Popup Confirmation**
```javascript
const executeApiAction = async (actionConfig, selectedRows, formValues, context) => {
  if (!actionConfig.apiConfig) {
    console.warn("No API config defined for this action.");
    return;
  }

  const { apiFunction, args } = actionConfig.apiConfig;

  if (!apiFunctions[apiFunction]) {
    console.error(`API function "${apiFunction}" not found.`);
    return;
  }

  const evaluatedArgs = args.map(arg => {
    try {
      return new Function("selectedRows", "formValues", "context", `return ${arg};`)(selectedRows, formValues, context);
    } catch (error) {
      console.error(`Error evaluating argument: ${arg}`, error);
      return undefined;
    }
  });

  try {
    await apiFunctions[apiFunction](...evaluatedArgs);
  } catch (error) {
    console.error(`Error executing API function "${apiFunction}"`, error);
  }
};
```

---

## **Predefined Custom Component Example (`FirmwareUpload`)**
```javascript
import React, { useState } from "react";
import { Modal, Select, Button } from "antd";

const FirmwareUpload = ({ onSubmit }) => {
  const [selectedRole, setSelectedRole] = useState(null);

  return (
    <div>
      <Select
        placeholder="Select Version"
        onChange={setSelectedRole}
        style={{ width: "100%", marginBottom: 16 }}
      >
        <Select.Option value="Admin">v1.0.0</Select.Option>
        <Select.Option value="Editor">v0.8.9</Select.Option>
        <Select.Option value="Viewer">v0.9.0</Select.Option>
      </Select>
      <Button
        type="primary"
        disabled={!selectedRole}
        onClick={() => onSubmit({ role: selectedRole })}
      >
        Upload Firmware
      </Button>
    </div>
  );
};

export default FirmwareUpload;
```

---

## **How It Works**
1. **Click Action Button**
   - Fetch `actionConfig` based on button `key`.
   - Calls `showPopup(actionConfig, selectedRows, context)`.

2. **Popup Opens**
   - If `popupType === "confirm"`, shows a simple confirmation modal.
   - If `popupType === "custom"`, renders a predefined React component.

3. **User Confirms / Submits**
   - Calls `executeApiAction(actionConfig, selectedRows, formValues, context)`.

4. **API Function is Called**
   - Extracts API function name.
   - Evaluates arguments dynamically.
   - Calls the correct function.

---

## **Why This Works Well**
✅ **Handles both confirmations & complex popups**.  
✅ **Predefined UI components** for custom popups.  
✅ **No hardcoded logic** – Just map new UI components in JSON.  
✅ **Scalable** – Easily add new popups & API calls.  

# Implementation in Page
## **1. Table Bulk Actions**
  - Table actions can be defined as mentioned below
  - **showIf** key can be present similar to table columns
  - If API calls are required, define the apiConfig
  - apiFunction will have the js-sdk function names
  - args will have the arguments for the js-sdk function
  - context is the global context
  - apiConfig should be used to generate the actual function, refer **showPopup**
  - To manipulate the action buttons, loop items.
  - If **showIf** is present, then evaluate the condition and show/hide the action button
  - if, apiConfig is present, attach a new key **onClickFunction** in each action item
  - then call the corresponding **showPopup** in the table actions, inside **onClickFunction**
  - **onClickFunction** will look something like: pls refer **onClickFunction** below

- **onClickFunction** [example code snippet]
```js
 function onClickFunction (selectedRows: any) {
   const context = { clientId: 5, userId: 42 }; // this is the global context, will be available in main page
   // actionConfig --> each table actions items
   showPopup(actionConfig, selectedRows, context); 
  }
```

Yes, since your **columns array is stored as JSON in the database** and any key starting with `"function"` is treated as a function, we can **define `rowActionsConfig` directly inside the column configuration**.

---
## **2. Table Single Row Actions**
### **How to Define Actions Inside the Column JSON**
Modify your **column JSON stored in the database** to include `rowActions` **inside the column definition itself**.

```json
[
  {
    "title": "Device Name",
    "dataIndex": "deviceName",
    "key": "deviceName"
  },
  {
    "title": "Actions",
    "dataIndex": "actions",
    "key": "actions",
    "rowActions": [
      {
        "text": "Delete",
        "key": "delete_item",
        "popupType": "confirm",
        "popupConfig": {
          "title": "Are you sure you want to delete this item?",
          "content": "This action cannot be undone.",
          "onOkFunction": "function_deleteDevice"
        },
        "apiConfig": {
          "apiFunction": "function_deleteDevice",
          "args": ["row.deviceId"]
        },
        "icon": "🗑️"
      }
    ]
  }
]
```

---

## **How to Render Actions in the Ant Design Table**
Now, when parsing your JSON, extract `rowActions` from the column definition and render buttons inside the `render` function.

```javascript
const handleRowAction = (actionConfig, row) => {
  showPopup(actionConfig, [row], {}); // Reuse popup function
};

const parsedColumns = columns.map(col => {
  if (col.rowActions) {
    return {
      ...col,
      render: (_, row) => (
        <div>
          {col.rowActions.map(action => (
            <button key={action.key} onClick={() => handleRowAction(action, row)}>
              {action.icon}
            </button>
          ))}
        </div>
      )
    };
  }
  return col;
});
```
---