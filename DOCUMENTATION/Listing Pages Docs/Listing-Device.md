# **Device List Page Implementation Guide**

## **Writing Configuration JSON**
- Define the filters and columns.
- Filter for assigned, unassigned devices?
- Define add button.
- Define the api config for each action button.
- Write proper conditions to show/hide columns or filters or bulk action buttons/single action buttons.

## Routes
/devices/list
/devices/add
/devices/22189/debug


## **Component/Logic separation**
### 1. `FirmwareUpload` Component
- Copy the jsx snippet and functions required for the component.
- Make it generic to support both single and multiple device update.
- Provision to pass required values via props.
- Handle via predefined actions through config.

### 2. `SimAddEdit` Component
- Copy the jsx snippet and functions required for the component.
- Provision to pass required values via props.
- Handle via predefined actions through config.

### 3. `ThirdPartyConnectionDetails` Component
- Copy the jsx snippet and functions required for the component.
- Provision to pass required values via props.
- Handle via predefined actions through config.

### 4. `AddDevice` Component
- Copy the jsx snippet and functions required for the component.
- Provision to pass required values via props.
- Handle via predefined actions through config.

### 5. `BulkAddDevice` Component
- Copy the jsx snippet and functions required for the component.
- Provision to pass required values via props.
- Handle via predefined actions through config.

### 6. `DeviceDebug` Component
- Need to add a separate route for this component.
- Required details like device_id, device_qr_code which were passed as props to the component in `DeviceList` component needs to be passed as query params.

### 7. Assign Device
- Can be handled via config, for details refer to `Action-Handling.md`

### 8. UnAssign Device
- Can be handled via config, for details refer to `Action-Handling.md`

### 9. Activate/Inactivate Device
- Can be handled via config, for details refer to `Action-Handling.md`



### Logic Conditions

[`Not Required`] 1. If opened for end customer:
    - `devicesList` is called
[`Not Required`] 2. If opened for partner/datoms-x:
    - `deviceLists` is called
    - In assigned tab- `type=assigned&goem_devices=true` is passed in the query params.
    - In unassigned tab- `type=unassigned` is passed in the query params.
[`Not Required`] 3. Device list is ordered by ["created_at"], ["desc"]
[`On Hold`] 4. Provision for delears node in territory filter if dealer management is enabled.
    - handle in filter component
    - dealers with territories shown come in one api
[`Not Required`] 5. Handle values in api:
     - device_status:
              st.status_code && st.status_code !== 7 ? "active" : "inactive"
     - connectivity_status:
              st.status_code && st.status_code === 1 ? "online" : "offline"

## Add Buttons
### Addd Device Button
- If feature/access includes "DeviceManagement:AddDevice"
- On clicking, opens `AddDevice` component.
### Bulk Add Device Button
- If feature/access includes "DeviceManagement:BulkAddDevice"
- On clicking, opens `BulkAddDevice` component.


## 6. Filters:
    - Account Type:
      - API implementation required
      - only show to applicaiton_id 12
      - only show for assigned devices
      - defualt value: 'production'
    - Territories:
      - If territory feature is enabled or application_id is 12
      - only show for assigned devices
      - default value: NA
    - Partners:
      - API implementation required
      - If partners are available
      - only show for assigned devices
      - default value: NA
    - Customers:
      - API implementation required
      - If customers are available
      - only show for assigned devices
      - default value: NA
    - Device Type:
      - API implementation required
      - If device types are available
    - Connectivity Status
      - API implementation required
      - Always show
    - Device Status
        - API implementation required
        - show only for application_id 12
    - Search By Device QR

## 7. Table Columns

### Device Serial ID
- **QR Code**e
- **Last Online Time**
  - On hover:
    - **Last Online Time:** `12:00 AM`
    - **Last Data Received:** `12:00 AM`
  - If no Last Online Time is available, show **"Never"**
- **Third-Party Connection Details**
  - Show button if `type === 17 || 52`

### Created Date
- Format: `DD MMM YYYY, HH:mm`

### Firmware
- Show **Firmware Version** if available
- If features includes "DeviceManagement:OTA" or DatomsX -
  - If **latest**, show `"Up to Date"` icon
  - If **not latest**, show `"Update"` icon
    - Clicking `"Update"` icon opens **Firmware Upload Modal**

### Assets
- Display linked assets as:
  - `Asset 1, + 2 more`
  - On hover: Show **remaining assets**

### Customer Name
- Display **Customer Name**
- If **Reseller exists**, show **Customer Hierarchy**:
  - `[Customer Name, Reseller Name, Vendor Name]`
- Show **Application Name**

### Network Status
- **If `device_config` exists**:
  - **If `device_modem_type` is `gprs`**:
    - Show **GPRS** icon
    - If `device_sim_slot` exists, display **Sim Slot** (e.g., `"Sim 1"`)
    - **If `connectivity_status` is online**:
      - Signal Strength:
        - **>20** → `"Strength: Excellent"` (`GprsSignalRounded5`)
        - **13-20** → `"Strength: Good"` (`GprsSignalRounded4`)
        - **6-12** → `"Strength: Poor"` (`GprsSignalRounded2`)
        - **<6** → `"No Signal"`
    - **If `connectivity_status` is offline**:
      - Show `"Last Reported Strength"` with respective signal category

  - **If `device_modem_type` is `wifi`**:
    - Show **Wi-Fi** icon
    - **If `connectivity_status` is online**:
      - Signal Strength:
        - **>20** → `"Strength: Excellent"` (`WifiSignalRounded3`)
        - **13-20** → `"Strength: Good"` (`WifiSignalRounded2`)
        - **6-12** → `"Strength: Poor"` (`WifiSignalRounded1`)
        - **<6** → `"No Signal"`
    - **If `connectivity_status` is offline**:
      - Show `"Last Reported Strength"` with respective signal category

  - **If `device_modem_type` is `ethernet`**:
    - **If `connectivity_status` is online**, show **Ethernet Active** icon
    - **If `connectivity_status` is offline**, show **Ethernet Inactive** icon

### Power Status
- **Power Indicator**
  - If `device_power_status` is **not null or false**:
    - **If `connectivity_status` is online**, show **Power On** icon
      - Tooltip: `"Power Status: On"`
    - Otherwise, show **Power Inactive** icon
      - Tooltip: `"Last Reported Status: On"`
  - If `device_power_status` is **false**:
    - **If `connectivity_status` is online**, show **Power Off** icon
      - Tooltip: `"Power Status: Off"`
    - Otherwise, show **Power Inactive** icon
      - Tooltip: `"Last Reported Status: Off"`
  - If `device_power_status` is **null**:
    - Show **Power Inactive** icon
      - Tooltip:
        - `"Power Status: Unknown"` (if `connectivity_status` is **online**)
        - `"Last Reported Status: Unknown"` (otherwise)

- **Battery Display**
  - If `device_battery_percent` exists:
    - Show **Battery Bar** with active levels based on `device_battery_percent`
      - (Only active if `connectivity_status` is **online**)
    - Display **Battery Percentage** next to the bar
      - (Only if `connectivity_status` is **online**)

- **Charging Status**
  - If `device_charging_status` exists:
    - **If `device_charging_status === true` and `connectivity_status` is online**, show **Charging Active** icon
    - Otherwise, show **Charging Inactive** icon

### SIM
- **If screen width < 576px (Mobile View)**:
  - Show **Edit Icon** (`EditOutlined`)
    - Clicking opens **SimAddEdit Drawer**
  - Display SimAddEdit:
    - **If `sim_details[0].serial_no` exists**:
      - Show `"SIM1: {serial_no} ({operator})"`
    - **If `sim_details[1].serial_no` exists**:
      - Show `", SIM2: {serial_no} ({operator})"`
    - Otherwise, show **"NA"**

- **If screen width ≥ 576px (Desktop View)**:
  - Show **SIM Icon** with tooltip
    - Tooltip contains:
      - **If `sim_details[0].serial_no` exists**:
        - `"SIM1: {serial_no} ({operator})"`
      - **If `sim_details[1].serial_no` exists**:
        - `"SIM2: {serial_no} ({operator})"`
      - Otherwise, show **"NA"**
  - Clicking the SIM icon opens **SimAddEdit Drawer**

### Online Percentage
- Displays **This Month's Online %** inside a circular progress bar.
- **Tooltip:** `"This month online %"`
- **If `percent` exists and is not `null`**, display it.
- **If `percent` is `null` or missing**, display `0%`.
- **Progress bar settings**:
  - **Type:** Circle
  - **Size:** `35px`
  - **Stroke Color:** `#21A1DB`

### Debug & Configure
- **Debug**
  - Shows a **Debug Icon** (`debug`)
  - Clicking it **opens Device Debug** for the selected device.

- if features includes "DeviceManagement:Rawlog" or DatomsX- **Raw Log**
  - Shows a **Raw Log Icon** (`raw_log_icon`)
  - Clicking it **opens Device Debug** with raw log mode enabled.

- **Configure Device**
  - Shown only if `type_id` is **11, 12, or 51**.
  - Clicking it **opens Device Configuration** at a dynamically generated route:
    - Base Path:
      `/enterprise/{client_id}/{platform_slug}/devices/assigned/{device_id}/communication?type_id={type_id}`
    - If `is_application_filter` is enabled, the route changes to:
      `/enterprise/{client_id}/{platform_slug}/customer-management/{customer_id}/applications/{selected_app}/devices/{device_id}/communication?type_id={type_id}`

- if features includes "DeviceManagement:CustomCommand" or DatomsX- **Custom Command**
  - Shown if:
    - `application_id` is **12**, or
    - `enabled_features` includes `"DeviceManagement:CustomCommand"`
  - Clicking it **opens Custom Command** for the selected device.

### Device Status
- **Toggle Switch (`AntSwitch`)**
  - **Checked (`Active`)** if `status_code` is **not 7**.
  - **Unchecked (`Inactive`)** if `status_code` is **7**.
  - **Tooltip** displays `"Active"` or `"Inactive"` based on `status_code`.

- **On Toggle (`onChange` event)**
  - Opens a **confirmation modal (`AntConfirmModal`)** before changing status.
  - **Modal Content:**
    - **Title:**
      - Shows **Active** or **Inactive** status with an **icon**:
        - **If `status_code` is 7** → Show **Active Icon**.
        - **Else** → Show **Inactive Icon**.
    - **Message:**
      - `"Are you sure you want to make {qr} Active/Inactive?"`
    - **Buttons:**
      - `"Active"` / `"Inactive"` (based on `status_code`)
      - `"Cancel"`
  - On confirmation, calls `updateStatusOnOk(key, [row_data.id])`:
    - `key = "active"` if switching **to Active**.
    - `key = "block"` if switching **to Inactive**.

### Columns to hide in different portals.
- **Customer Name** hide while opening inside customer details drawer and also for unassigned devices.
- **Device Status** only show in DatomsX (both in main page and inside customer details drawer).



### Table Columns for 392

#### **Device QR**
- Displays the **Device QR Code** with a tooltip.
- Shows the **Device Type Name** below the QR code.
- **Sorting:** Alphabetically (`localeCompare`).

#### **Customer Name**
- Displays the **Customer Name**.
- If `customer` is empty or `null`, shows **"-"**.
- **Sorting:** Alphabetically (`localeCompare`).

#### **Status**
- **Tooltip:** `"Last data received"`
- **Determines online/offline status based on timestamp**:
  - Calculates the difference between **current time** and `timestamp`.
  - **If `timestamp` is within the last 15 minutes**:
    - Show `"Online"` status with **green indicator**.
  - **If `timestamp` is older than 15 minutes**:
    - Show `"Offline"` status with **red indicator**.
  - **If `timestamp` is missing (`null`, `""`, `undefined`, `0`)**:
    - Show `"Never"` (meaning no data was ever received).
- **Displays the last recorded timestamp** in format:
  `"DD MMM YYYY, HH:mm"` (`Asia/Kolkata` timezone).
- **Sorting:** By timestamp.

#### **Device Status**
- If `status_code == 7`, show **"Blocked"**.
- Otherwise, show **"Active"**.

#### **Description**
- Displays the device description.





## Socket Updates

#### **1. `device_firmware_updated`** [`Only htis is required`]
- **Triggered when** a device firmware update is successfully queued.
- Calls `openNotification` to display a **success notification**:
  - Message: `"Devices firmware update queued successfully."`
- **Purpose:** Notifies the user that the firmware update process has begun.

---

#### **2. `update_location_details_in_dashboard`**
- **Triggered when** location details of a device change in the dashboard.
- **Processes the received payload (`data_status`)**:
  - Parses the JSON data.
  - If `this.device_details` exists:
    - Iterates through all device details.
    - **If `device_id` matches an existing device**, updates its properties except `device_id`.
    - Updates the stored `device_details`.
    - Calls `updateFilter()` to refresh the displayed data.
- **Purpose:** Ensures that location details in the dashboard remain up to date.

---

#### **3. `new_station_added_successfully`**
- **Triggered when** a new device (station) is added successfully.
- **Processes the received payload (`add_new_device`)**:
  - Parses the JSON data.
  - Appends the new device details to `this.device_details`.
  - Calls `filterTableData()` to refresh the table display.
- **Purpose:** Dynamically updates the device list when a new device is added.

---

#### **4. `unsubscribe_from_devices_successful`**
- **Triggered when** the socket successfully unsubscribes from old device updates.
- **Automatically subscribes to the updated list of devices**:
  - Emits `"subscribe_to_devices"` with the latest `currently_viewing_device_ids`.
- **Purpose:** Ensures that only relevant devices remain subscribed for real-time updates.

---

#### **5. Dashboard Connection & Device Subscription**
- **When `"dashboard_successfully_connected"` is received:**
  - If `currently_viewing_device_ids` **contains devices**, it calls `subscribeForDeviceUpdates(true)` to refresh device subscriptions.

- **`subscribeForDeviceUpdates(forceSubscribe)` Logic:**
  - **Prepares device list**:
    - Retrieves `filtered_stations` (list of available devices).
    - Converts **station names** and **QR codes** to **lowercase** for case-insensitive comparison.
    - **Removes archived stations**.

  - **Applies sorting and pagination**:
    - **Sorting:** Uses `table_view_options.sorter` to sort devices by:
      - `"station"` → `name`
      - `"qr"` → `qr_code`
      - `"active"` → `last_data_receive_time`
      - `"percent"` → `online_percentage`
      - `"availability"` → `data_availability`
    - **Pagination:** Uses `table_view_options.pagination` to determine:
      - `view_start_index` → First visible device.
      - `view_end_index` → Last visible device.
    - If pagination settings are missing, defaults are applied.

  - **Extracts `currently_viewing_device_ids`** from paginated `filtered_stations`.

  - **Subscribes to devices only if the list has changed** (to prevent redundant emissions):
    - If `forceSubscribe` is `true` OR the new `currently_viewing_device_ids` differs from the previous list:
      - **Unsubscribes from old devices** by emitting `"unsubscribe_from_devices"` with the previous `device_ids`.
      - Updates `currently_viewing_device_ids` with the new list.









