import path from "path";
import react from "@vitejs/plugin-react-swc";
import {
  loadEnv,
  defineConfig,
  transformWithEsbuild,
  // splitVendorChunkPlugin,
} from "vite";
// import EnvironmentPlugin from "vite-plugin-environment";
// import commonjs from "vite-plugin-commonjs";
import { visualizer } from "rollup-plugin-visualizer";
import { nodePolyfills } from "vite-plugin-node-polyfills";
import packageJson from "./package.json";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };
  let homepage = "";
  let addAnalyticsScript = false;
  if (process.env.VITE_MOBILE) {
    homepage = "/";
  } else if (process.env.VITE_REVIEW_PROD) {
    homepage =
      "https://" +
      process.env.DATOMS_WEBAPP_BUILD_STATIC_SERVER +
      process.env.DATOMS_WEBAPP_BUILD_APP_SLUG +
      "next/";
      addAnalyticsScript = true; // revert
  } else if (mode === "production") {
    homepage =
      "https://" +
      process.env.DATOMS_WEBAPP_BUILD_STATIC_SERVER +
      process.env.DATOMS_WEBAPP_BUILD_APP_SLUG +
      "v" +
      packageJson.version +
      "/";
    addAnalyticsScript = true;
  }
  console.log("vite mode -> ", mode);
  console.log("vite packageJson -> ", packageJson.version);
  console.log("vite process.env.VITE_REVIEW_PROD -> ", process.env.VITE_REVIEW_PROD);
  console.log("vite homepage -> ", homepage);
  console.log("vite addAnalyticsScript -> ", addAnalyticsScript);
  return {
    plugins: [
      react(),
      visualizer({
        gzipSize: true,
      }),
      // splitVendorChunkPlugin(),
      nodePolyfills(),
      {
        name: "load+transform-js-files-as-jsx",
        async transform(code, id) {
          if (!id.match(/src\/.*\.js$/)) {
            return null;
          }

          // Use the exposed transform from vite, instead of directly
          // transforming with esbuild
          return transformWithEsbuild(code, id, {
            loader: "jsx",
            jsx: "automatic", // 👈 this is important
          });
        },
      },
      {
        name: "add-analytics-script",
        /* This will run only for prod build for web (npm run build:prod) */
        apply: () => addAnalyticsScript,
        async transform(code, id) {
          if (
            !id.includes("node_modules") &&
            id.replace(/\\/g, "/") ===
              path.resolve(__dirname, "./src/index.js").replace(/\\/g, "/")
          ) {
            console.log("Adding analytics script to index.js");
            return code
              .replace(
                `"##PR_INSERT_ERROR_TRACKING##"`,
                `import * as Sentry from "@sentry/react";\nimport { BrowserTracing } from "@sentry/tracing";\nSentry.init({
                dsn: "https://<EMAIL>/4504631556964352",
                integrations: [new BrowserTracing()],
                tracesSampleRate: 1.0,
              });`,
              )
              /*.replace(
                `"##PR_INSERT_MIX_PANEL_INIT##"`,
                `window.mixpanel.init("448c1893fd8657d024dea174b99759a2", {'debug': true, 'track_pageview': true, 'persistence': 'localStorage' });`,
              );*/
          }
        },
      },
    ],
    test: {
      environment: "jsdom",
      globals: true,
      setupFiles: "src/setupTests.ts",
    },
    base: homepage,
    build: {
      outDir: "build",
      assetsDir: "static",
      commonjsOptions: {
        include: "node_modules/**",
        extensions: [".js"],
        transformMixedEsModules: true,
      },
    },
    esbuild: {
      drop: mode === "production" ? ["console", "debugger"] : [],
    },
    css: {
      preprocessorOptions: {
        less: {
          math: "always",
          relativeUrls: true,
          javascriptEnabled: true,
        },
      },
    },
    optimizeDeps: {
      esbuildOptions: {
        loader: {
          ".js": "jsx",
        },
        // Node.js global to browser globalThis
        define: {
          global: "globalThis",
        },
      },
    },
    server: {
      port: 3000,
      proxy: {
        "/-/api": {
          target: "https://app.datoms.io",
          // target: "https://api.stage.datoms.io",
          // target: "http://localhost:4000",
          changeOrigin: true,
          secure: false,
          headers: {
            Cookie:
              // "PHPSESSID=DnrLprGTbEnXUkvt7NDJ6wWpvkrlECOEuofDZ7fkWeYt4qxCWGo0oCTI7b1QoSCH",
              "PHPSESSID=IvqkvdmoVn2PFn1RtnVscSPo5PU1ArgEYJW5fm6uSHnP69QhbRq5XIkvCu2HqViA",
          },
        },
        "/v1.3": {
          // target: "https://api-testing.datoms.io",
          target: "https://api.datoms.io",
          // target: "http://localhost:4000",
          changeOrigin: true,
          secure: false,
          headers: {
            Cookie:
              // "PHPSESSID=065DBUkszsaIZlRsn3W8akjaxUNHNd8lmzmNtaBd28Md3CODCuOnurdljObipokg",
              // "PHPSESSID=QjfyeACKgj9QBsEm7qGjNGfWJY9YDdDmJYBF7EjcPwpBkbJsR11SUg6xBTPodiWz"// 2696
              // "PHPSESSID=rIkdc0DJp7BPEiNKmh1A0xVMfvp1QKPgv74Pj7PhzSZX8147I01OvOGmyPV7S0W5"// 10685
              // "PHPSESSID=0g36DNgdnS6DvKrvWSGmS943VEyOQxCMqTCvTmgwGcf6s7cX1vzK8cNzPyXzkbAA" // 1
              // "PHPSESSID=s:yd-66iTY7LaJo8f6fjnd87t29JZ-7KdY.lcczYkKqAfYtWcnkQsG2J44eVLlxYQ0C9VP9TRzoE5w", // 13108
              "PHPSESSID=IvqkvdmoVn2PFn1RtnVscSPo5PU1ArgEYJW5fm6uSHnP69QhbRq5XIkvCu2HqViA", 
          },
        },
      },
    },
    resolve: {
      alias: {
        "@datoms/webapp-component-device-management": path.resolve(
          __dirname,
          "./src/packages/webapp-component-device-management",
        ),
        "@datoms/webapp-component-thing-management": path.resolve(
          __dirname,
          "./src/packages/webapp-component-thing-management",
        ),
        "@datoms/webapp-component-user-management": path.resolve(
          __dirname,
          "./src/packages/webapp-component-user-management",
        ),
        "@datoms/webapp-component-rental-management": path.resolve(
          __dirname,
          "./src/packages/webapp-component-rental-management",
        ),
        "@datoms/webapp-component-software-license-management": path.resolve(
          __dirname,
          "./src/packages/webapp-component-software-license-management",
        ),
        "@datoms/webapp-component-billing-management": path.resolve(
          __dirname,
          "./src/packages/webapp-component-billing-management",
        ),
        "@datoms/webapp-component-alerts-management": path.resolve(
          __dirname,
          "./src/packages/webapp-component-alerts-management",
        ),
        "@datoms/js-utils": path.resolve(__dirname, "./src/packages/js-utils"),
        "@datoms/iot-platform-views": path.resolve(
          __dirname,
          "./src/packages/iot-platform-views",
        ),
        "@datoms/webapp-component-user-notifications": path.resolve(
          __dirname,
          "./src/packages/webapp-component-user-notifications",
        ),
        "@datoms/webapp-component-reports": path.resolve(
          __dirname,
          "./src/packages/webapp-component-reports",
        ),
        "@datoms/webapp-component-changelog": path.resolve(
          __dirname,
          "./src/packages/webapp-component-changelog",
        ),
        "@datoms/react-components": path.resolve(
          __dirname,
          "./src/packages/react-components",
        ),
        "@datoms/webapp-component-layout": path.resolve(
          __dirname,
          "./src/packages/webapp-component-layout",
        ),
        "@datoms/dg-monitoring-views": path.resolve(
          __dirname,
          "./src/packages/dg-monitoring-views",
        ),
        "@datoms/datoms-store-front": path.resolve(
          __dirname,
          "./src/packages/datoms-store-front",
        ),
        "@datoms/datoms-webapp-product-inventory-system": path.resolve(
          __dirname,
          "./src/packages/datoms-webapp-product-inventory-system",
        ),
        "@datoms/datoms-settings": path.resolve(
          __dirname,
          "./src/packages/datoms-settings",
        ),
        "@components": path.resolve(__dirname, "./src/components"),
      },
    },
  };
});
