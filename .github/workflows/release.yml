name: Release

# Controls when the workflow will run
on:
  # Triggers the workflow on push to main / master branch in the repository
  push:
    branches:
      - master

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # vulnerability-assessment:
  #   # The type of runner that the job will run on
  #   runs-on: self-hosted
  #   # Docker image
  #   container: node:20.11.1
  #   # Steps represent a sequence of tasks that will be executed as part of the job
  #   steps:
  #     - name: Checkout
  #       uses: actions/checkout@v3
  #     - name: Audit
  #       env:
  #         # Setting this NPM_TOKEN is required here, otherwise yarn gives error
  #         NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
  #       run: yarn audit --groups dependencies
  #       continue-on-error: true

  # code-style-check:
  #   runs-on: self-hosted
  #   container: node:20.11.1
  #   steps:
  #     - name: Checkout
  #       uses: actions/checkout@v3
  #     - run: npx prettier --check .
  #       continue-on-error: true

  # code-syntax-check:
  #   runs-on: self-hosted
  #   container: node:20.11.1
  #   steps:
  #     - name: Checkout
  #       uses: actions/checkout@v3
  #     # - name: Get yarn cache directory path
  #     #   id: yarn-cache-dir-path
  #     #   run: echo "::set-output name=dir::$(yarn cache dir)"
  #     # - name: Setup Yarn Cache
  #     #   uses: actions/cache@v3
  #     #   id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
  #     #   with:
  #     #     path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
  #     #     key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
  #     #     restore-keys: |
  #     #       ${{ runner.os }}-yarn-
  #     - name: NPM Install
  #       env:
  #         # Setting this NPM_TOKEN is required here, otherwise yarn gives error
  #         NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
  #       run: yarn
  #     - name: Check Syntax
  #       env:
  #         # Setting this NPM_TOKEN is required here, otherwise yarn gives error
  #         NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
  #       run: yarn test:syntax
  #       continue-on-error: true

  unit-testing:
    runs-on: ubuntu-latest
    container: node:20.11.1
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn cache dir)" >> $GITHUB_OUTPUT
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      - name: Setup Yarn Cache
        uses: actions/cache@v4
        id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-
      - name: Install Dependencies
        run: yarn
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      - name: Run tests
        run: |
          yarn test --ci
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          CI: true

  release:
    runs-on: ubuntu-latest
    # needs: [unit-testing]
    # needs: [vulnerability-assessment, code-style-check, code-syntax-check]
    container: node:20.11.1
    steps:
      - name: Set Git Safe Directory
        run: git config --system --add safe.directory '*'
      - name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn cache dir)" >> $GITHUB_OUTPUT
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      - name: Setup Yarn Cache
        uses: actions/cache@v4
        id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-
      - name: NPM Install
        run: yarn
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      - name: Release
        env:
          # Setting this NPM_TOKEN is required here, otherwise yarn gives error
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          GITHUB_TOKEN: ${{ secrets.DATOMS_GITHUB_TOKEN }}
        run: yarn semantic-release
      - name: SonarQube Scan
        uses: sonarsource/sonarqube-scan-action@master
        with:
          args: >
            -Dsonar.projectVersion=${{ steps.datoms-set-app-version.outputs.version }}
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
