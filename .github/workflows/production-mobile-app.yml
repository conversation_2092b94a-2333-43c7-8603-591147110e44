name: Production Mobile App

# Controls when the workflow will run
on:
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  production-mobile-build:
    environment: production
    runs-on: ubuntu-latest
    # needs: [vulnerability-assessment, code-style-check, code-syntax-check]
    container: node:20.11.1
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      # - name: Get yarn cache directory path
      #   id: yarn-cache-dir-path
      #   run: echo "::set-output name=dir::$(yarn cache dir)"
      # - name: Setup Yarn Cache
      #   uses: actions/cache@v3
      #   id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
      #   with:
      #     path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
      #     key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
      #     restore-keys: |
      #       ${{ runner.os }}-yarn-
      - name: NPM Install
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: yarn
      - name: Build Library
        env:
          # Setting this NPM_TOKEN is required here, otherwise yarn gives error
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: yarn build:mobile
      - name: Upload build files to artifacts
        uses: actions/upload-artifact@v4
        with:
          name: production-mobile-build-files
          path: build
