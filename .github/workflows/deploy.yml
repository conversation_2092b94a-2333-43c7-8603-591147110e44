name: Production Deploy

# Controls when the workflow will run
on:
  # Triggers the workflow on release tag creation in the repository
  release:
    types: [released]

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  production-deploy:
    environment: production
    runs-on: ubuntu-latest
    container: nikolaik/python-nodejs:python3.11-nodejs20-slim
    steps:
      - name: <PERSON><PERSON> repo
        uses: actions/checkout@v4
      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn cache dir)" >> $GITHUB_OUTPUT
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      - name: Setup Yarn Cache
        uses: actions/cache@v4
        id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-
      - name: NPM Install
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: yarn
      - name: Build
        env:
          # Setting this NPM_TOKEN is required here, otherwise yarn gives error
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          DATOMS_WEBAPP_BUILD_APP_SLUG: ${{ vars.DATOMS_WEBAPP_BUILD_APP_SLUG }}
          DATOMS_WEBAPP_BUILD_STATIC_SERVER: ${{ vars.DATOMS_WEBAPP_BUILD_STATIC_SERVER }}
        run: yarn build:prod
      - name: Install awscli
        run: pip install awscli
      - name: Upload to S3
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        run: aws s3 cp ./build s3://prstatic.phoenixrobotix.com/-/static/datoms/dg-monitoring/${GITHUB_REF_NAME}/ --recursive
      - name: Rollout
        env:
          # Setting this NPM_TOKEN is required here, otherwise yarn gives error
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          DATOMS_WEBAPP_DEPLOYMENT_AUTH_PRIVATE_KEY: ${{ secrets.DATOMS_WEBAPP_DEPLOYMENT_AUTH_PRIVATE_KEY }}
          DATOMS_WEBAPP_BUILD_APP_SERVICE_ID: ${{ vars.DATOMS_WEBAPP_BUILD_APP_SERVICE_ID }}
          DATOMS_DEPLOYMENT: ${{ vars.DATOMS_DEPLOYMENT }}
        run: yarn update_webapp_version
      - name: Build StoryBook
        env:
          # Setting this NPM_TOKEN is required here, otherwise yarn gives error
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: yarn build:storybook
      - name: Upload StoryBook to S3
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        run: aws s3 cp ./storybook-static s3://prstatic.phoenixrobotix.com/-/static/datoms/static-site-storybook/ --recursive
      - name: Invalidate Cloudfront cache
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        run: aws cloudfront create-invalidation --distribution-id E1Q5ZB32ZZ8N4C --paths "/-/static/datoms/static-site-storybook/*"
