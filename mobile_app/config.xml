<?xml version='1.0' encoding='utf-8'?>
<widget id="io.datoms.dgmonitoring" version="3.245.2" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0" xmlns:android="http://schemas.android.com/apk/res/android">
    <name>Datoms</name>
    <description>
        A smart way to manage and monitor machines
    </description>
    <author email="<EMAIL>" href="https://phoenixrobotix.com">
        Phoenix Robotix
    </author>
    <content src="index.html" />
    <access origin="*" />
    <allow-navigation href="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    <allow-intent href="maps:*" />
    <preference name="scheme" value="http" />
    <platform name="android">
        <allow-intent href="market:*" />
        <preference name="AndroidWindowSplashScreenAnimationDuration" value="2000" />
        <preference name="AndroidWindowSplashScreenAnimatedIcon" value="res/screen/android/datoms.png" />
        <preference name="AndroidWindowSplashScreenBackground" value="#FFFFFF" />
        <preference name="AndroidExtraFilesystems" value="files,files-external,documents,sdcard,cache,cache-external,assets,root" />
        <icon density="ldpi" src="res/icon/android/icon-ldpi.png" />
        <icon density="hdpi" src="res/icon/android/icon-hdpi.png" />
        <icon density="mdpi" src="res/icon/android/icon-mdpi.png" />
        <icon density="xhdpi" src="res/icon/android/icon-xhdpi.png" />
        <icon density="xxhdpi" src="res/icon/android/icon-xxhdpi.png" />
        <icon density="xxxhdpi" src="res/icon/android/icon-xxxhdpi.png" />
        <resource-file src="res/android/drawable-mdpi/notification_icon.png" target="app/src/main/res/drawable-mdpi/notification_icon.png" />
        <resource-file src="res/android/drawable-hdpi/notification_icon.png" target="app/src/main/res/drawable-hdpi/notification_icon.png" />
        <resource-file src="res/android/drawable-xhdpi/notification_icon.png" target="app/src/main/res/drawable-xhdpi/notification_icon.png" />
        <resource-file src="res/android/drawable-xxhdpi/notification_icon.png" target="app/src/main/res/drawable-xxhdpi/notification_icon.png" />
        <resource-file src="res/android/drawable-xxxhdpi/notification_icon.png" target="app/src/main/res/drawable-xxxhdpi/notification_icon.png" />
        <config-file target="AndroidManifest.xml" parent="/manifest/application">
            <meta-data android:name="com.google.firebase.messaging.default_notification_icon" android:resource="@drawable/notification_icon" />
        </config-file>
        <edit-config file="app/src/main/AndroidManifest.xml" mode="merge" target="/manifest/application">
            <application android:allowBackup="false" />
        </edit-config>
        <edit-config file="app/src/main/AndroidManifest.xml" mode="merge" target="/manifest/application/activity">
            <activity android:taskAffinity="" />
        </edit-config>
    </platform>
    <platform name="ios">
        <allow-intent href="itms:*" />
        <allow-intent href="itms-apps:*" />
        <edit-config target="NSAppTransportSecurity" file="*-Info.plist" mode="overwrite">
            <dict>
                <key>NSAllowsArbitraryLoads</key>
                <false/>
            </dict>
        </edit-config>
        <edit-config target="NSLocationWhenInUseUsageDescription" file="*-Info.plist" mode="merge">
            <string>need location access to find things nearby</string>
        </edit-config>
        <edit-config target="NSLocationAlwaysAndWhenInUseUsageDescription" file="*-Info.plist" mode="merge">
            <string>need location access to find things nearby</string>
        </edit-config>
        <edit-config target="NSLocationAlwaysUsageDescription" file="*-Info.plist" mode="merge">
            <string>need location access to find things nearby</string>
        </edit-config>
        <preference name="StatusBarOverlaysWebView" value="false" />
        <preference name="StatusBarBackgroundColor" value="#000000" />
        <preference name="iosPersistentFileLocation" value="Library" />
        <preference name="iosExtraFilesystems" value="library,library-nosync,documents,documents-nosync,cache,bundle,root" />
        <splash src="res/screen/ios/Default@2x~universal~anyany.png" />
        <splash src="res/screen/ios/Default@2x~universal~comany.png" />
        <splash src="res/screen/ios/Default@2x~universal~comcom.png" />
        <splash src="res/screen/ios/Default@3x~universal~anyany.png" />
        <splash src="res/screen/ios/Default@3x~universal~anycom.png" />
        <splash src="res/screen/ios/Default@3x~universal~comany.png" />
        <icon height="20" src="res/icon/ios/20.jpg" width="20" />
        <icon height="29" src="res/icon/ios/29.jpg" width="29" />
        <icon height="40" src="res/icon/ios/40.jpg" width="40" />
        <icon height="50" src="res/icon/ios/50.jpg" width="50" />
        <icon height="57" src="res/icon/ios/57.jpg" width="57" />
        <icon height="58" src="res/icon/ios/58.jpg" width="58" />
        <icon height="60" src="res/icon/ios/60.jpg" width="60" />
        <icon height="72" src="res/icon/ios/72.jpg" width="72" />
        <icon height="76" src="res/icon/ios/76.jpg" width="76" />
        <icon height="80" src="res/icon/ios/80.jpg" width="80" />
        <icon height="87" src="res/icon/ios/87.jpg" width="87" />
        <icon height="100" src="res/icon/ios/100.jpg" width="100" />
        <icon height="114" src="res/icon/ios/114.jpg" width="114" />
        <icon height="120" src="res/icon/ios/120.jpg" width="120" />
        <icon height="144" src="res/icon/ios/144.jpg" width="144" />
        <icon height="152" src="res/icon/ios/152.jpg" width="152" />
        <icon height="167" src="res/icon/ios/167.jpg" width="167" />
        <icon height="180" src="res/icon/ios/180.jpg" width="180" />
        <icon height="216" src="res/icon/ios/216.jpg" width="216" />
        <icon height="1024" src="res/icon/ios/1024.jpg" width="1024" />
    </platform>
    <preference name="fullscreen" value="false" />
    <preference name="AndroidXEnabled" value="true" />
    <preference name="Orientation" value="portrait" />
    <preference name="AutoHideSplashScreen" value="false" />
    <preference name="SplashScreenDelay" value="5000" />
    <preference name="FadeSplashScreen" value="true" />
    <preference name="FadeSplashScreenDuration" value="750" />
    <preference name="ShowSplashScreenSpinner" value="false" />
    <preference name="SplashMaintainAspectRatio" value="true" />
    <preference name="android-targetSdkVersion" value="34" />
</widget>
