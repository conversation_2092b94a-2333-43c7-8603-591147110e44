{"name": "@datoms/dg-monitoring-webapp", "version": "3.245.3", "description": "dg_monitoring_webapp", "private": true, "license": "SEE LICENSE IN /LICENSE", "homepage": ".", "repository": {"type": "git", "url": "git+ssh://**************/Datoms-IoT/datoms-webapp.git"}, "scripts": {"commit": "cz", "semantic-release": "semantic-release", "prepare": "husky install", "start": "vite --force", "preview": "vite preview", "start:mobile": "VITE_MOBILE=true vite", "build": "vite build", "build:prod": "CI=false node --max-old-space-size=16384 ./node_modules/vite/bin/vite.js build && npm run add_mixpanel_web", "build:review-prod": "CI=false VITE_REVIEW_PROD=true node --max-old-space-size=16384 ./node_modules/vite/bin/vite.js build && npm run add_mixpanel_web", "build:mobile": "CI=false VITE_MOBILE=true node --max-old-space-size=16384 ./node_modules/vite/bin/vite.js build && npm run add_script", "build:mobile:yoyorides": "CI=false VITE_CUSTOM_HOST_NAME=yoyorydes.com VITE_MOBILE=true vite build && npm run add_script", "build:mobile:stage": "CI=false VITE_MOBILE=true vite build --mode staging && npm run add_script", "build:staging:mobile": "npm run update_version && npm run build:mobile:stage", "build:staging:mobile:yoyorides": "npm run update_version && CI=false VITE_CUSTOM_HOST_NAME=yoyorydes.stage.datoms.io VITE_MOBILE=true vite build --mode staging && npm run add_script", "review_build": "CI=false node --max-old-space-size=16384 ./node_modules/vite/bin/vite.js build --mode staging", "build:review": "npm run review_build", "add_script": "gulp add_cordova_script_to_index_file", "analyze": "source-map-explorer 'build/static/js/*.js'", "analyze2": "node --max_old_space_size=16384 analyze.js", "build:staging": "node --max-old-space-size=16384 ./node_modules/vite/bin/vite.js build --mode staging", "add_mixpanel_web": "gulp add_mixpanel_in_index", "docs": "jsdoc -c jsdoc.json", "test": "node --max-old-space-size=8192 ./node_modules/jest/bin/jest.js --coverage", "eject": "react-app-rewired eject", "versions": "npmvet", "update_version": "gulp update_version", "install": "npm run versions", "deploy": "aws s3 cp ./build s3://prstatic.phoenixrobotix.com/-/static/datoms/dg-monitoring/v0.1.0 --recursive && npm run deploy:php_file", "deploy:php_file": "gulp upload_php_files_to_deploy_servers", "update_webapp_version": "gulp update_webapp_version", "build_package": "npm run update_library_name && npm run build_package:js && npm run build_package:less && npm run build_package:misc", "build_package:js": "babel src -d build_library", "build_package:less": "gulp less", "build_package:misc": "gulp build-misc", "test:syntax": "yarn run eslint --ext .js src/", "update_library_name": "gulp update_library_name", "storybook": "storybook dev -p 6006", "build:storybook": "cross-env NODE_OPTIONS=\"--max-old-space-size=8192\" storybook build", "test-storybook": "test-storybook", "cypress:run:page-open": "cypress run --browser chrome --spec cypress/e2e/PlatformWisePageOpen/**/*.cy.ts"}, "cypress-cucumber-preprocessor": {"nonGlobalStepDefinitions": true, "cucumberJson": {"generate": true, "outputFolder": "cypress/cucumber-json", "filePrefix": "", "fileSuffix": ".cucumber"}}, "author": {"name": "Phoenix Robotix Pvt. Ltd.", "email": "<EMAIL>", "url": "https://phoenixrobotix.com"}, "engines": {"node": "10 || >=12.16.1"}, "dependencies": {"@ant-design/compatible": "1.0.5", "@ant-design/icons": "5.6.1", "@babel/standalone": "7.27.0", "@datoms/js-sdk": "1.94.7", "@highcharts/map-collection": "2.1.0", "@react-google-maps/api": "2.19.3", "@sentry/react": "7.36.0", "@sentry/tracing": "7.36.0", "@types/react-virtualized-auto-sizer": "1.0.8", "@vis.gl/react-google-maps": "1.5.2", "@vitejs/plugin-react": "4.3.4", "antd": "5.25.2", "chrome-launcher": "1.1.2", "convert-seconds": "^1.0.1", "cypress-mailslurp": "1.10.0", "env-cmd": "10.1.0", "enzyme": "3.11.0", "enzyme-adapter-react-16": "1.15.8", "exceljs": "4.4.0", "file-saver": "^2.0.5", "file-type-checker": "1.1.4", "gulp": "4.0.2", "gulp-less": "5.0.0", "gulp-rename": "2.0.0", "gulp-replace": "1.1.4", "gulp-sourcemaps": "3.0.0", "gulp-util": "3.0.8", "gulp-xmlpoke": "0.2.1", "highcharts": "11.3.0", "highcharts-more": "0.1.7", "highcharts-react-official": "3.2.1", "humanize-duration": "3.31.0", "i18next": "23.10.0", "i18next-browser-languagedetector": "7.2.2", "jmespath": "0.16.0", "jquery": "3.7.1", "json2csv": "5.0.7", "jspdf": "2.5.1", "jspdf-autotable": "3.8.2", "less": "4.2.0", "less-plugin-autoprefix": "2.0.0", "lodash": "^4.17.21", "memoize-one": "6.0.0", "moment-duration-format": "2.3.2", "moment-timezone": "0.5.47", "prop-types": "15.8.1", "query-string": "9.0.0", "react": "18.2.0", "react-beautiful-dnd": "13.1.1", "react-circle": "1.1.1", "react-color": "2.19.3", "react-cropper": "2.3.3", "react-d3-heatmap": "1.1.5", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-dom": "18.2.0", "react-draggable": "4.4.6", "react-error-boundary": "4.1.2", "react-fast-compare": "3.2.2", "react-fast-marquee": "1.6.4", "react-google-maps": "9.4.5", "react-grid-layout": "1.4.4", "react-highlight-words": "0.20.0", "react-i18next": "14.0.5", "react-image-gallery": "1.3.0", "react-intersection-observer": "9.16.0", "react-intl-tel-input": "8.2.0", "react-multi-email": "1.0.23", "react-phone-input-2": "2.15.1", "react-qr-code": "2.0.12", "react-remark": "2.1.0", "react-resizable": "3.0.5", "react-resize-detector": "10.0.1", "react-router-dom": "5.1.2", "react-router-dom-v5-compat": "6.22.2", "react-star-ratings": "2.3.0", "react-thermometer-component": "1.0.1", "react-to-print": "2.15.1", "react-virtualized": "9.22.5", "react-window": "1.8.11", "reactcss": "1.2.3", "regenerator-runtime": "0.14.1", "safe-eval": "0.4.1", "slugify": "1.6.6", "socket.io-client": "2.3.0", "tableexport": "5.2.0", "ts-node": "10.9.2", "uuid": "10.0.0", "vinyl-ftp": "0.6.1", "yarn": "1.22.21"}, "resolutions": {"moment-timezone": "0.5.47", "xlsx": "0.18.5"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "devDependencies": {"@babel/cli": "7.23.9", "@babel/core": "7.26.0", "@babel/plugin-proposal-class-properties": "7.10.1", "@babel/preset-env": "7.26.0", "@babel/preset-react": "7.26.3", "@chromatic-com/storybook": "3.2.2", "@neuralegion/cypress-har-generator": "5.17.0", "@semantic-release/changelog": "6.0.3", "@semantic-release/exec": "6.0.3", "@semantic-release/git": "10.0.1", "@storybook/addon-actions": "8.4.7", "@storybook/addon-docs": "8.4.7", "@storybook/addon-essentials": "8.4.7", "@storybook/addon-interactions": "8.4.7", "@storybook/addon-onboarding": "8.4.7", "@storybook/blocks": "8.4.7", "@storybook/jest": "0.2.3", "@storybook/react": "8.4.7", "@storybook/react-vite": "8.4.7", "@storybook/test": "8.4.7", "@storybook/testing-library": "0.2.2", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.1.0", "@testing-library/user-event": "14.5.2", "@types/cypress": "1.1.6", "@types/highcharts": "7.0.0", "@types/jest": "29.5.14", "@types/node": "22.7.9", "@types/react-resizable": "3.0.8", "@types/react-router-dom": "5.3.3", "@vitejs/plugin-react-swc": "3.6.0", "babel-jest": "29.7.0", "babel-plugin-transform-rename-import": "2.3.0", "better-docs": "2.7.3", "commitizen": "4.3.0", "cross-env": "7.0.3", "cypress": "13.15.1", "cz-conventional-changelog": "3.3.0", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-react": "7.33.2", "eslint-plugin-storybook": "0.11.1", "eslint-plugin-unused-imports": "3.1.0", "gulp": "4.0.2", "gulp-json-editor": "2.6.0", "gulp-rename": "2.0.0", "gulp-util": "3.0.8", "husky": "9.0.11", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jsdoc": "4.0.2", "jsdom": "24.0.0", "lighthouse": "12.2.1", "lint-staged": "15.2.2", "npmvet": "0.2.1", "prettier": "3.2.5", "puppeteer": "23.6.1", "rollup-plugin-visualizer": "5.12.0", "semantic-release": "23.1.1", "source-map-explorer": "2.5.3", "storybook": "8.4.7", "ts-jest": "29.2.5", "ts-node": "10.9.2", "typescript": "5.6.3", "vinyl-ftp": "0.6.1", "vite": "5.4.12", "vite-plugin-node-polyfills": "0.21.0", "vitest": "1.6.1"}, "husky": {"hooks": {"pre-commit": "lint-staged && gulp update_mobile_app_version", "post-commit": "git add ./mobile_app/config.xml && git commit -m \"updated mobile app version\""}}, "lint-staged": {"*.{js,jsx,json,css,less,scss,md}": "prettier --write"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}