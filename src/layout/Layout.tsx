/* Libs */
import React, { Suspense, useState } from "react";
import AntLayout from "@datoms/react-components/src/components/AntLayout";
import Loading from "@datoms/react-components/src/components/Loading";
import Error404 from "../containers/authentication/Error404";
import LoginPage from "../containers/authentication/LoginPage";
import DatomsPolicy from "@datoms/datoms-settings/src/pages/DatomsPolicy";
import WelcomePage from "../containers/authentication/WelcomePage";
import UserNotification from "../packages/webapp-component-user-notifications/src/components/UserNotification";
import { Switch, Route } from "react-router-dom";
import { CompatRouter } from "react-router-dom-v5-compat";
import { BrowserRouter as WebRouter } from "react-router-dom";
import MobileRouter from "react-router-dom/HashRouter";
import Menu from "./components/Menu";
import { LayoutProps } from "./types";

import "./style.less";

let Router = WebRouter;
if (import.meta.env.VITE_MOBILE || import.meta.env.VITE_DESKTOP) {
  console.log("process_ 1");
  Router = MobileRouter;
}

const Layout: React.FC<LayoutProps> = (props) => {
  const [login_finish, setLogin_finish] = useState<boolean>(false);
  const [view_welcome, setViewWelcome] = useState<boolean>(true);
  const [user_notification_drawer_status, setUserNotificationDrawerStatus] =
    useState<boolean>(false);

  let baseName : string = "";
  let clientId : number = props.client_id;

  if (import.meta.env.VITE_BUILD_MODE !== "development") {
    let client_slug = window.location.pathname.split("/")[2];
    if (client_slug && typeof client_slug === "string") {
      baseName = "/enterprise/" + client_slug;
    }
  }

  const setLogoutState = () => {
    setLogin_finish(false);
    window.location.hash = "/";
  };

  const updatePageType = () => {
    let page_type = "device",
      page_path = "";

    if (window.location.pathname.split("/")[2]) {
      page_path = window.location.pathname.split("/")[2];
      if (import.meta.env.VITE_BUILD_MODE !== "development") {
        page_path = window.location.pathname.split("/")[4];
      }
    }
    if (page_path == "production-inventory" || page_path == "sim-management") {
      page_type = "production";
    } else if (page_path == "client-management") {
      page_type = "client";
    }
  };

  const setLoginFinish = () => {
    try {
      props.setStoreLogin();
    } catch (err) {}
    setLogin_finish(true);
  };

  const loginClick = () => {
    setViewWelcome(true);
  };

  const disableWelcomePage = () => {
    setViewWelcome(false);
  };

  const handleUserNotificationClickButton = () => {
    setUserNotificationDrawerStatus(true);
  };

  const handleUserNotificationDrawerClose = () => {
    setUserNotificationDrawerStatus(false);
  };

  const getExtraHeaderComps = () => {
    return <></>;
  };

  if (window.localStorage.getItem("Client-Id")) {
    clientId = parseInt(window.localStorage.getItem("Client-Id") || '');
  }

  if (window.localStorage.getItem("Client-Id")) {
    clientId = parseInt(window.localStorage.getItem("Client-Id") || '');
  }

  if (import.meta.env.VITE_MOBILE && (!login_finish || props.store_logout)) {
    return (
      <Router basename={baseName}>
        <CompatRouter>
          <Switch>
            <Route
              path="/"
              exact
              render={(routeProps) => (
                <LoginPage
                  loginClickMain={loginClick}
                  {...routeProps}
                  application_id={props.application_id}
                  app_name={props.app_name}
                  home_page={props.home_page}
                  execute={props.getUserAccess} 
                  setLoginFinish={setLoginFinish}
                  mobileAppPackageName={props.mobileAppPackageName}
                />
              )}
            />
          </Switch>
        </CompatRouter>
      </Router>
    );
  }

  if (props.showPolicyPage) {
    return (
      <Router basename={baseName}>
        <Switch>
          <Route
            path="/"
            render={(routeProps) => (
              <DatomsPolicy
                {...routeProps}
                isEdit
                callback={props.getUserAccess}
                policyTypeStatus={props.showPolicyPage || {}}
              />
            )}
          />
        </Switch>
      </Router>
    );
  }

  if (
    props.enabled_features &&
    props.head_side_object_data &&
    Object.keys(props.head_side_object_data).length
  ) {
    if (
      (parseInt(props.vendor_id) === 1048 ||
        parseInt(props.vendor_id) === 1140 ||
        parseInt(props.vendor_id) === 10786 ||
        parseInt(props.vendor_id) === 1062) &&
      import.meta.env.VITE_MOBILE
    ) {
      if (login_finish && view_welcome) {
        return (
          <WelcomePage
            disableWelcomePage={() => disableWelcomePage()}
            {...props}
          />
        );
      } else {
        return (
          <Router basename={baseName}>
            <CompatRouter>
              <Menu
                is_mobile_page_name={props.is_mobile_page_name}
                t={props.t}
                app_name={props.app_name}
                i18n={props.i18n}
                user_preferences={props.user_preferences}
                user_id={props.user_id}
                user_name={props.user_name}
                client_id={clientId}
                application_id={props.application_id}
                new_head={props.head_side_object_data}
                location_path={window.location.href}
                fuel_buddy={props.fuel_buddy}
                collapsed={props.collapsed}
                getExtraHeaderComps={getExtraHeaderComps}
                showComplianceBanner={props.showComplianceBanner}
                onCollapseSider={props.onCollapseSider}
                onmenuClick={props.onmenuClick}
                active_page_type={props.page_type}
                updatePageType={updatePageType}
                setLogoutState={setLogoutState}
                vendor_logo={props.vendor_logo}
                handleUserNotificationClick={handleUserNotificationClickButton}
                handleUserNotificationDrawerClose={() =>
                  handleUserNotificationDrawerClose()
                }
                is_white_label={props.is_white_label}
                white_label_icon={props.white_label_icon}
                isFixedLogoForWhitelabel={props.isFixedLogoForWhitelabel}
                logged_in_user_email={props.logged_in_user_email}
                logged_in_user_full_name={props.logged_in_user_full_name}
                logged_in_user_mobile={props.logged_in_user_mobile}
                isRentalStore={props.isRentalStore}
                customer_type={props.customer_type}
                structure_style={props.structure_style}
                logged_in_user_client_id={props.logged_in_user_client_id}
                deskTopLogout={props.deskTopLogout}
                vendor_id={props.vendor_id}
                logged_in_user_role_type={props.logged_in_user_role_type}
              />
              <AntLayout
                className={
                  "page-contains" +
                  (props.showComplianceBanner
                    ? " page-contains-with-banner "
                    : "") +
                  (props.collapsed ? " collapsed-side" : "") +
                  (props.isRentalStore ? " page-contains-rental-store" : "")
                }
              >
                <Suspense
                  fallback={
                    <Loading show_logo={props.loading_logo} ta={true} />
                  }
                >
                  <Switch>
                    {props.children}
                    <Route
                      render={() => (
                        <Error404
                          t={props.t}
                          is_white_label={props.is_white_label}
                          customizations={props.customizations}
                        />
                      )}
                    />
                  </Switch>
                </Suspense>
                {(() => {
                  if (user_notification_drawer_status) {
                    return (
                      <UserNotification
                        t={props.t}
                        client_id={clientId}
                        application_id={props.application_id}
                        app_name={props.app_name}
                        handleUserNotificationAppDrawerClose={
                          handleUserNotificationDrawerClose
                        }
                        user_preferences={props.user_preferences}
                        globalConfig={props.globalConfig}
                      />
                    );
                  }
                })()}
              </AntLayout>
            </CompatRouter>
          </Router>
        );
      }
    } else {
      return (
        <Router basename={baseName}>
          <CompatRouter>
            <Menu
              is_mobile_page_name={props.is_mobile_page_name}
              t={props.t}
              app_name={props.app_name}
              i18n={props.i18n}
              user_preferences={props.user_preferences}
              user_id={props.user_id}
              user_name={props.user_name}
              client_id={clientId}
              application_id={props.application_id}
              new_head={props.head_side_object_data}
              location_path={window.location.href}
              fuel_buddy={props.fuel_buddy}
              collapsed={props.collapsed}
              getExtraHeaderComps={props.getExtraHeaderComps}
              showComplianceBanner={props.showComplianceBanner}
              onCollapseSider={props.onCollapseSider}
              onmenuClick={props.onmenuClick}
              active_page_type={props.page_type}
              updatePageType={updatePageType}
              setLogoutState={setLogoutState}
              vendor_logo={props.vendor_logo}
              handleUserNotificationClick={handleUserNotificationClickButton}
              handleUserNotificationDrawerClose={() =>
                handleUserNotificationDrawerClose()
              }
              is_white_label={props.is_white_label}
              white_label_icon={props.white_label_icon}
              isFixedLogoForWhitelabel={props.isFixedLogoForWhitelabel}
              logged_in_user_email={props.logged_in_user_email}
              logged_in_user_full_name={props.logged_in_user_full_name}
              logged_in_user_mobile={props.logged_in_user_mobile}
              isRentalStore={props.isRentalStore}
              customer_type={props.customer_type}
              structure_style={props.structure_style}
              logged_in_user_client_id={props.logged_in_user_client_id}
              deskTopLogout={props.deskTopLogout}
              vendor_id={props.vendor_id}
              logged_in_user_role_type={props.logged_in_user_role_type}
            />
            <AntLayout
              className={
                "page-contains" +
                (props.showComplianceBanner
                  ? " page-contains-with-banner "
                  : "") +
                (props.collapsed ? " collapsed-side" : "") +
                (props.isRentalStore ? " page-contains-rental-store" : "")
              }
            >
              <Suspense
                fallback={<Loading show_logo={props.loading_logo} ta={true} />}
              >
                <Switch>
                  {props.children}
                  <Route
                    render={() => (
                      <Error404
                        t={props.t}
                        is_white_label={props.is_white_label}
                        customizations={props.customizations}
                      />
                    )}
                  />
                  {/*<Route component={Error404} />*/}
                </Switch>
              </Suspense>
              {(() => {
                if (user_notification_drawer_status) {
                  return (
                    <UserNotification
                      t={props.t}
                      client_id={clientId}
                      application_id={props.application_id}
                      app_name={props.app_name}
                      handleUserNotificationAppDrawerClose={
                        handleUserNotificationDrawerClose
                      }
                      user_preferences={props.user_preferences}
                      globalConfig={props.globalConfig}
                      logged_in_user_role_type={props.logged_in_user_role_type}
                    />
                  );
                }
              })()}
            </AntLayout>
          </CompatRouter>
        </Router>
      );
    }
  } else {
    return <Loading show_logo={props.loading_logo} />;
  }
};

export default Layout;
