import React, { useEffect, useState } from "react";
import Head from "./Head";
import Sider from "./Sider";
import MobileMenu from "./MobileMenu";
import _find from "lodash/find";
import AntLayout from "@datoms/react-components/src/components/AntLayout";
import "./menu.less";
import { useLocation } from "react-router-dom";
import { MenuProps } from "../types";

const Menu: React.FC<MenuProps> = (props) => {
  const {
    is_mobile_page_name,
    t,
    i18n,
    app_name,
    user_preferences,
    user_id,
    user_name,
    getExtraHeaderComps,
    showComplianceBanner,
    client_id,
    application_id,
    new_head,
    location_path,
    collapsed,
    onCollapseSider,
    active_page_type,
    updatePageType,
    setLogoutState,
    handleUserNotificationClick,
    handleUserNotificationDrawerClose,
    is_white_label,
    white_label_icon,
    isFixedLogoForWhitelabel,
    customizations,
    isRentalStore,
    customer_type,
    structure_style,
    logged_in_user_client_id,
    deskTopLogout,
    vendor_id,
    vendor_logo,
    onmenuClick,
    logged_in_user_role_type,
  } = props;

  const location = useLocation();

  const [active_mobile_menu_key, setActiveMobileMenuKey] = useState<string>("");
  const [active_menu_key, setActiveMenuKey] = useState<string>("");
  const [menuKeys, setMenuKeys] = useState<string[]>([]);
  const [active_submenu_key, setActiveSubMenuKey] = useState<string>("");
  const [menuOptions, setMenuOptions] = useState([]);
  const [mobileMenuOptions, setMobileMenuOptions] = useState([]);
  const [currentLocationsHref, setCurrentLocationHref] = useState<any>(null);
  const [first_key, setFirstKey] = useState<string>("");

  let menu_options = new_head?.header_component?.sider?.menu_items || [];
  let mobile_menu_options = new_head?.header_component?.mobile_menu || [];
  let selectedKey = "no value";
  let selectedMobileKey = "no value";

  if (new_head?.header_component?.sider?.menu_items?.length) {
    new_head.header_component.sider.menu_items.map(
      (menuDet: { key: string; url: any }) => {
        if (
          window.location.href &&
          window.location.href.includes(menuDet.url)
        ) {
          selectedKey = menuDet.key;
        }
      },
    );
  }

  if (new_head?.header_component?.mobile_menu?.length) {
    new_head.header_component.mobile_menu.map(
      (menuDet: { key: string; url: any }) => {
        if (
          window.location.href &&
          window.location.href.includes(menuDet.url)
        ) {
          selectedMobileKey = menuDet.key;
        }
      },
    );
  }

  const onCollapse = () => {
    onCollapseSider();
    setTimeout(() => {
      window.dispatchEvent(new Event("resize"));
    }, 500);
  };

  const mobileMenuClick = (key: string) => {
    setActiveMenuKey(key);
  };

  const subMenuActive = (submenu_items: { url: any }[]) => {
    let found = false;
    submenu_items.find((submenuData: { url: any }) => {
      console.log("gdaiActive: ", window.location.pathname, submenuData.url);
      if (window.location.pathname.includes(submenuData.url)) {
        found = true;
        return;
      }
    });
    return found;
  };

  const currentMenuData = (activeMenuData: {
    key: string;
    submenu_items: { url: string; key: string }[];
  }) => {
    if (activeMenuData) {
      let activeSubMenuData = { url: "", key: "" };
      if (activeMenuData?.submenu_items?.length) {
        activeMenuData.submenu_items.map((submenuData, index) => {
          if (window.location.pathname.includes(submenuData.url)) {
            activeSubMenuData = submenuData;
          }
        });
      }
      setActiveMenuKey(activeMenuData?.key);
      setActiveSubMenuKey(activeSubMenuData?.key || "");
      setMenuKeys(activeMenuData?.alternativeKeys || []);
      if (onmenuClick) onmenuClick();
    }
  };

  const currentSubMenuData = (activeSubMenuData: {
    url: string;
    key: string;
  }) => {
    setActiveMenuKey("");
    setActiveSubMenuKey(activeSubMenuData?.key || "");
    if (onmenuClick) onmenuClick();
  };

  const isAlternativekey = (alternativeKeys: string[]) => {
    if (Array.isArray(alternativeKeys)) {
      return alternativeKeys.find((key) => window.location.href.includes(key));
    }
    return false;
  };

  const onUrlChange = () => {
    if (window.location.href !== currentLocationsHref) {
      let menu_options = new_head?.header_component?.sider?.menu_items || [];
      let mobile_menu_options = new_head?.header_component?.mobile_menu || [];

      setMenuOptions(menu_options);
      setMobileMenuOptions(mobile_menu_options);
      setCurrentLocationHref(window.location.href);

      let currentPageDetails = _find(
        new_head.header_component.sider.menu_items,
        (o: {
          url: string;
          submenu_items: { url: any }[];
          key: string;
          alternativeKeys: string[];
        }) => {
          return (
            (o?.submenu_items?.length &&
              (window.location.href.includes(o.key) ||
                isAlternativekey(o.alternativeKeys)) &&
              subMenuActive(o.submenu_items)) ||
            (o.url && window.location.href.includes(o.url))
          );
        },
      );

      if (currentPageDetails) {
        currentMenuData(currentPageDetails);
      }

      let currentMobilePage = _find(
        new_head.header_component.mobile_menu,
        (o) => {
          return window.location.href.includes(o.url);
        },
      );

      if (currentMobilePage) {
        mobileMenuClick(currentMobilePage.key);
      }
    }
  };

  useEffect(() => {
    onUrlChange();
  }, [location]);

  useEffect(() => {
    onUrlChange();
    setMenuOptions(menu_options);
    setMobileMenuOptions(mobile_menu_options);
    setActiveMenuKey(selectedKey);
    setActiveSubMenuKey("");
    setActiveMobileMenuKey(selectedMobileKey);
    setFirstKey(
      props?.new_head?.header_component?.sider?.menu_items[0]?.key || "",
    );
  }, []);

  return (
    <AntLayout className="menu-component">
      {isRentalStore ? (
        ""
      ) : (
        <Head
          t={t}
					i18n={i18n}
          app_name={app_name}
          user_preferences={user_preferences}
          client_id={client_id}
          logged_in_user_client_id={logged_in_user_client_id}
          user_id={user_id}
          user_name={user_name}
          application_id={application_id}
          showComplianceBanner={showComplianceBanner}
          SiderOptions={props?.new_head?.header_component?.sider}
          HeaderOptions={new_head?.header_component?.header}
          getExtraHeaderComps={getExtraHeaderComps}
          location_path={window.location.href}
          collapsed={collapsed}
          currentMenuData={(menu_value) => currentMenuData(menu_value)}
          active_menu_key={active_menu_key}
          currentSubMenuData={currentSubMenuData}
          active_page_type={active_page_type ? active_page_type : ""}
          onCollapse={onCollapse}
          active_submenu_key={active_submenu_key}
          menuOptions={menuOptions}
          updatePageType={updatePageType}
          setLogoutState={setLogoutState}
          vendor_logo={vendor_logo}
          handleNotificationIconClick={handleUserNotificationClick}
          is_white_label={is_white_label}
          white_label_icon={white_label_icon}
          isFixedLogoForWhitelabel={isFixedLogoForWhitelabel}
          customizations={customizations}
          customer_type={customer_type}
          deskTopLogout={deskTopLogout}
          vendor_id={vendor_id}
          onCollapseSider={onCollapseSider}
          logged_in_user_role_type={logged_in_user_role_type}
        />
      )}

      <Sider
        t={t}
        collapsed={collapsed}
        onCollapseSider={onCollapseSider}
        SiderOptions={props?.new_head.header_component?.sider}
        location_path={window.location.href}
        onCollapse={onCollapse}
        currentMenuData={(menu_value) => currentMenuData(menu_value)}
        currentSubMenuData={currentSubMenuData}
        active_menu_key={active_menu_key}
        active_submenu_key={active_submenu_key}
        menuOptions={menuOptions}
        first_key={first_key}
        handleUserNotificationDrawerClose={() => {
          if (typeof handleUserNotificationDrawerClose === "function") {
            handleUserNotificationDrawerClose();
          }
        }}
        is_white_label={is_white_label}
        white_label_icon={white_label_icon}
        isFixedLogoForWhitelabel={isFixedLogoForWhitelabel}
        customizations={customizations}
        sider_style={structure_style?.sider_style}
        menuKeys={menuKeys}
      />

      <MobileMenu
        isRentalStore={isRentalStore}
        is_mobile_page_name={is_mobile_page_name}
        location_path={window.location.href}
        mobileMenuOptions={mobileMenuOptions}
        mobileMenuClick={mobileMenuClick}
        active_menu_key={active_mobile_menu_key}
        handleUserNotificationDrawerClose={() => {
          if (typeof handleUserNotificationDrawerClose === "function") {
            handleUserNotificationDrawerClose();
          }
        }}
        is_white_label={is_white_label}
        white_label_icon={white_label_icon}
        isFixedLogoForWhitelabel={isFixedLogoForWhitelabel}
        application_id={application_id}
        active_menu_color={
          structure_style?.sider_style?.active_icon_background_color ?? ""
        }
      />
    </AntLayout>
  );
};

export default Menu;
