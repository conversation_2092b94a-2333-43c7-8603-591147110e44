import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { getBaseUrl } from "@datoms/js-utils/src/base-url-logic";
import { mixPanelResetUser } from "@datoms/js-utils/src/mix-panel";
import LogoutOutlined from "@ant-design/icons/LogoutOutlined";
import UnorderedListOutlined from "@ant-design/icons/UnorderedListOutlined";
import RocketOutlined from "@ant-design/icons/RocketOutlined";
import ThunderboltOutlined from "@ant-design/icons/ThunderboltOutlined";
import QuestionCircleOutlined from "@ant-design/icons/QuestionCircleOutlined";
import AntLayout from "@datoms/react-components/src/components/AntLayout";
import AntMenu from "@datoms/react-components/src/components/AntMenu";
import AntDivider from "@datoms/react-components/src/components/AntDivider";
import AntDrawer from "@datoms/react-components/src/components/AntDrawer";
import AntHeader from "@datoms/react-components/src/components/AntHeader";
import AntBadge from "@datoms/react-components/src/components/AntBadge";
import AntNotification from "@datoms/react-components/src/components/AntNotification";
import Sider from "./Sider";
import Marquee from "react-fast-marquee";
import {
  logout,
  retriveEventsData,
  subscribeForEventsUpdates,
  disconnectSocketConnection,
  establishSocketConnection,
} from "@datoms/js-sdk";
import moment from "moment-timezone";
import "react-intl-tel-input/dist/main.css";
import _filter from "lodash/filter";
import _orderBy from "lodash/orderBy";
import _find from "lodash/find";
import {
  setClientLocalStorage,
  getClientLocalStorage,
} from "@datoms/js-utils/src/local-data-storage";
import Aurassure_logo from "../../images/ClientLogo/aurassure_logo.png";
import { CodeOutlined, CustomerServiceOutlined } from "@ant-design/icons";
import BellIcon from "../../images/ClientLogo/bell.svg";
import { useGlobalContext } from "../../store/globalStore";
import { BodyObject, HeadProps } from "../types";

let iotBasePath = "https://app.datoms.io";
let notificationInterval: string | number | NodeJS.Timeout | null | undefined =
  null;
let socket: any;

if (
  !import.meta.env.VITE_MOBILE &&
  typeof window !== undefined &&
  !window.location.href.includes("localhost")
) {
  iotBasePath = window.location.protocol + "//" + window.location.host;
}

const Head: React.FC<HeadProps> = (props) => {
  const cookieName = "DatomsEssentialCookies";
  const cookieValue = "accept";

  const [visible, setVisible] = useState(false);
  const [notification_count, setNotificationCount] = useState(0);
  const package_name = window.localStorage.getItem("package_name") || "";
  const [cookie_banner_visible, setCookieBannerVisible] = useState(true);

  const { client_id, application_id, bannerToBeShown } = useGlobalContext();

  const getCookie = () => {
    const isEssentialAccepted = getClientLocalStorage(cookieName);
    if (
      isEssentialAccepted ||
      typeof window?.localStorage?.getItem !== "function" ||
      window?.location?.host.includes(".datoms.io")
    ) {
      setCookieBannerVisible(false);
    }
  };

  const notificationPolling = (interval = 10000) => {
    if (import.meta.env.VITE_DESKTOP) {
      if (notificationInterval !== null) {
        clearInterval(notificationInterval);
      }
      notificationInterval = setInterval(() => {
        fetchNotifications();
      }, interval);
    }
  };

  const openNotification = (type: string, msg: string) => {
    AntNotification({
      type: type,
      message: msg,
      placement: "bottomLeft",
      className: "alert-" + type,
    });
  };

  const fetchNotifications = async () => {
    let data = {
      client_id: client_id,
      application_id: application_id,
    };
    let url_string = "";
    url_string =
      url_string +
      "?get_details=true" +
      "&generated_after=" +
      (moment().unix() - 86400) +
      "&generated_before=" +
      moment().unix();

    let e_data = await retriveEventsData(data, url_string);

    console.log("e_data->", e_data);
    if (e_data.response.status === 403) {
    } else if (e_data.response.status === "success") {
      let total_count = 0;
      if (e_data.response.events && e_data.response.events.length) {
        e_data.response.events.map(
          (ne: { tags: string; entity_type: string }) => {
            if (
              ne.entity_type == "thing" &&
              (ne.tags.includes("Fault") ||
                ne.tags.includes("Fault OK") ||
                ne.tags.includes("Activity") ||
                ne.tags.includes("Maintenance") ||
                ne.tags.includes("Violation"))
            ) {
              total_count = total_count + 1;
            }
          },
        );
        notificationPolling(86400);
      } else {
        notificationPolling();
      }
      setNotificationCount(total_count);
    } else {
      openNotification("error", e_data.response.message);
    }
  };

  const initialCall = async () => {
    socket = establishSocketConnection();
    getCookie();

    if (parseInt(application_id) !== 12 && parseInt(application_id) !== 17) {
      if (!props?.no_api_call) {
        await fetchNotifications();
      }
      socket.on("connect", () => {
        subscribeForEventsUpdates(socket, client_id, application_id);
      });
      socket.on(
        "new_event_generated",
        (payload: { tags: string; entity_type: string }) => {
          if (
            payload?.entity_type == "thing" &&
            (payload.tags.includes("Fault") ||
              payload.tags.includes("Fault OK") ||
              payload.tags.includes("Activity") ||
              payload.tags.includes("Maintenance") ||
              payload.tags.includes("Violation"))
          ) {
            setNotificationCount(notification_count + 1);
          }
        },
      );
    }
  };

  const showDrawer = () => {
    setVisible(true);
  };

  const closeDrawer = () => {
    setVisible(false);
  };

  const menuClicked = (menu_value: string) => {
    closeDrawer();
    props?.currentMenuData(menu_value);
  };

  const subMenuClicked = () => {
    closeDrawer();
    props?.currentSubMenuData();
  };

  const logoutClicked = async () => {
    let bodyObject: BodyObject = {};

    if (import.meta.env.VITE_DESKTOP) {
      if (props?.deskTopLogout) {
        props.deskTopLogout();
      }
      return;
    }

    bodyObject["applicationId"] = localStorage.getItem("applicationId");
    bodyObject["MobileDeviceId"] = localStorage.getItem("mobileDeviceId");
    bodyObject["userId"] = localStorage.getItem("User-Id");
    let logoutResponse = await logout({}, bodyObject);
    if (logoutResponse.Status === "Success") {
      mixPanelResetUser();
      localStorage.removeItem("Auth-Token");
      localStorage.removeItem("Refresh-Token");
      localStorage.removeItem("User-Id");
      localStorage.removeItem("User-Name");
      localStorage.removeItem("Client-Id");
      localStorage.removeItem("Application-Id");
      if (props?.setLogoutState) {
        props.setLogoutState();
      }
    }
  };

  const setCookie = () => {
    setClientLocalStorage(cookieName, cookieValue);
    setCookieBannerVisible(false);
  };

  const headerMenuItems = [];
  let userName = props?.user_name ? props?.user_name : "Username";
  let pageName = "";
  let currentPageDetails = _find(
    props?.SiderOptions?.menu_items,
    (o: { url: string }) => {
      return (props?.location_path ?? "").includes(o.url);
    },
  );

  let cookieBanner: JSX.Element | null = null;
  if (
    cookie_banner_visible &&
    !import.meta.env.VITE_MOBILE &&
    !import.meta.env.VITE_DESKTOP
  ) {
    cookieBanner = (
      <div id="cookieNotice" className="light display-right">
        <div className="button-container">
          <div id="acceptCookie" onClick={() => setCookie()}>
            X
          </div>
        </div>
        <div id="cookie_Pop_up">
          <div className="title">Cookies</div>
          <p>
            We use essential cookies that are required to provide you with basic
            functions while using the application. These functions include the
            user session validation, for example. By continuing to use this
            application you accept the use of cookies. Read more about cookies
            in our{" "}
            <a href="https://datoms.io/privacy-policy-datoms/" target="_blank">
              Privacy Policy.
            </a>
          </p>
        </div>
        ;
      </div>
    );
  }

  if (currentPageDetails) {
  if (currentPageDetails?.submenu_items?.length) {
      let name = "";
      currentPageDetails.submenu_items.map(
        (sub_menu: { url: string; name: string }) => {
          if ((props?.location_path ?? "").includes(sub_menu.url)) {
            name = sub_menu.name;
            return;
          }
        },
      );
      if (name !== "") {
        pageName = name;
      }
    } else {
      pageName = currentPageDetails.page_name;
    }
  }

  let preferanceModal = <div />;

  let mobileHeaderIcon =
    props?.HeaderOptions &&
    props?.HeaderOptions.vendor_logo &&
    props?.HeaderOptions.vendor_logo != "" ? (
      <div
        className={
          "logos mobile-screen-header-logo" +
          (props?.is_white_label ? " white-label" : "")
        }
      >
        <img
          src={props?.HeaderOptions.vendor_logo}
          className={
            "company-logo" + (props?.is_white_label ? " white-label" : "")
          }
        />
      </div>
    ) : package_name === "com.aurassure.app" ? (
      <div className="mobile-screen-header-logo">
        <img alt="logo" src={Aurassure_logo} className="logo-img" />
      </div>
    ) : props?.isFixedLogoForWhitelabel ? (
      <div className="mobile-screen-header-logo fixed-logo">
        <img
          alt="logo"
          src={props?.white_label_icon.large_icon}
          className="logo-img"
        />
      </div>
    ) : props?.HeaderOptions?.logo != "" ? (
      <div
        className={
          "logos mobile-screen-header-logo" +
          (props?.is_white_label ? " white-label" : "")
        }
      >
        <img
          src={props?.HeaderOptions?.logo}
          className={
            "company-logo" + (props?.is_white_label ? " white-label" : "")
          }
        />
      </div>
    ) : (
      <div className="mobile-screen-header-logo">
        <img
          src="https://prstatic.phoenixrobotix.com/imgs/cold_storage_monitoring/datoms_logo.svg"
          className="logo-img"
        />
      </div>
    );

  if(props?.location_path &&
    props?.location_path.includes("/datoms-x") &&
    window.innerWidth > 576) {
    headerMenuItems.push({
      key: "api-docs",
      label: (
        <a
          target="_blank"
          href="https://docs.datoms.io/api/"
          rel="noreferrer"
        >
          API Docs
        </a>
      ),
      icon: <CodeOutlined />,
    })
  }

  if (
    props?.location_path &&
    props?.location_path.includes("/datoms-x") &&
    window.innerWidth > 576
  ) {
    headerMenuItems.push(
      {
        key: "impact",
        label: (
          <a
            target="_blank"
            href={
              window.location.origin + "/enterprise/1/datoms-x/impact-dashboard"
            }
            rel="noreferrer"
          >
            Impact
          </a>
        ),
        icon: <ThunderboltOutlined />,
      },
      {
        key: "release",
        label: (
          <a
            target="_blank"
            href="https://releases.datoms.io/"
            rel="noreferrer"
          >
            Release Notes
          </a>
        ),
        icon: <RocketOutlined />,
      },
    );
  }

  let notificationIcon = (
    <div
      className={
        "user-notification-count-inner-container" +
        ((parseInt(props.application_id) === 12 ||
          parseInt(props.application_id) === 17) &&
        window.location.href.includes("/user-notifications")
          ? " user-notification-count-inner-container-disable"
          : "")
      }
      onClick={() =>
        props?.handleNotificationIconClick &&
        props.handleNotificationIconClick()
      }
    >
      <AntBadge dot={notification_count > 0} className={"notification-count"}>
        <img src={BellIcon} />
      </AntBadge>
    </div>
  );

  if (
    props?.application_id === 17 &&
    window.innerWidth > 576 &&
    !import.meta.env.VITE_DESKTOP
  ) {
    headerMenuItems.push({
      key: "help",
      label: (
        <a
          target="_blank"
          href={"https://docs.datoms.io/docs/datoms/Customers/overview"}
          rel="noreferrer"
        >
          {props?.t ? props?.t("help") : "Help"}
        </a>
      ),
      icon: <QuestionCircleOutlined />,
    });
  }

  if (props?.vendor_id === 1 || props?.application_id === 12) {
    if (window.innerWidth > 576) {
      headerMenuItems.push({
        key: "tickets",
        label: <Link to={getBaseUrl(props, "tickets")}>Contact Support</Link>,
        icon: (
          <CustomerServiceOutlined
            style={{
              fontSize: 16,
              marginTop: 10,
            }}
          />
        ),
      });
    } else {
      headerMenuItems.push({
        key: "tickets",
        label: (
          <Link to={getBaseUrl(props, "tickets")}>
            <CustomerServiceOutlined
              style={{
                fontSize: 20,
                marginTop: 10,
              }}
            />
          </Link>
        ),
      });
    }
  }

  if (
    props?.application_id === 16 &&
    window.innerWidth > 576 &&
    !import.meta.env.VITE_DESKTOP
  ) {
    headerMenuItems.push({
      key: "blank",
      label: (
        <a
          target="_blank"
          href={"https://iot-docs.datoms.io/docs/datoms/login"}
          rel="noreferrer"
        >
          {props?.t ? props?.t("help") : "Help"}
        </a>
      ),
      icon: <QuestionCircleOutlined />,
    });
  }

  if (
    (props?.logged_in_user_role_type !== 7 &&
      parseInt(props.application_id) !== 12 &&
      parseInt(props.application_id) !== 17) ||
    window.innerWidth < 576
  ) {
    headerMenuItems.push({
      key: "notification-bell-icon",
      label: notificationIcon,
    });
  }

  const profileSubMenuItems = [];
  if (!import.meta.env.VITE_MOBILE && !import.meta.env.VITE_DESKTOP) {
    profileSubMenuItems.push({
      key: "change-password",
      label: (
        <a
          href={props?.HeaderOptions.change_pass_link}
          target="_blank"
          rel="noreferrer"
        >{props?.t ? props?.t("change_password") : "Change Password"}
          {/* Change Password */}
        </a>
      ),
    });
  }

  if (!import.meta.env.VITE_DESKTOP) {
    profileSubMenuItems.push({
      key: "settings",
      label: (
        <Link to={getBaseUrl(props, "settings/user-profile")}>
          {props?.t ? props?.t("settings") : "Settings"}
        </Link>
      ),
    });
  }
  profileSubMenuItems.push({
    key: "privacy-policy",
    label: (
      <Link to={getBaseUrl(props, "privacy-policy")}>
        {props?.t ? props?.t("privacy_policy") : "Privacy Policy"}
        {/* Privacy Policy */}
      </Link>
    ),
  });

  if (!import.meta.env.VITE_DESKTOP) {
    profileSubMenuItems.push({
      key: "status",
      label: (
        <div
          onClick={() => window.open("https://datoms.statuspage.io/", "_blank")}
        >
          {props?.t ? props?.t("status") : "Status"}
          {/* Status */}
        </div>
      ),
    });
  }

  profileSubMenuItems.push({
    key: "logout",
    label:
      import.meta.env.VITE_MOBILE || import.meta.env.VITE_DESKTOP ? (
        <a href="javascript:void(0)" onClick={() => logoutClicked()}>
          <LogoutOutlined style={{ marginRight: 10 }} />
          {props?.t ? props?.t("logout") : "Logout"}
        </a>
      ) : (
        <a href={props?.HeaderOptions.logout}>
          <LogoutOutlined style={{ marginRight: 10 }} />
          {props?.t ? props?.t("logout") : "Logout"}
        </a>
      ),
  });

  headerMenuItems.push({
    key: "user-profile",
    label: (
      <Link to="#">
        <span className="user-icon notranslate">
          {userName.charAt(0).toUpperCase()}
        </span>
        {window.innerWidth <= 1024 ? "" : <span>{userName}</span>}
      </Link>
    ),
    children: profileSubMenuItems,
  });

  useEffect(() => {
    initialCall();
    return () => {
      if (parseInt(application_id) !== 12 && parseInt(application_id) !== 17) {
        disconnectSocketConnection(socket);
      }
      if (notificationInterval) {
        clearInterval(notificationInterval);
      }
    };
  }, []);
  return (
    <AntLayout>
      <AntHeader
        t={props.t}
        style={{
          position: "fixed",
          zIndex: 10,
          width: "100%",
          overflow: "hidden",
        }}
      >
        <div style={{ display: "flex" }}>
          <div
            className={
              "logos mobile-drawer-container" +
              (props?.collapsed ? " collapsed-side" : "")
            }
          >
            <UnorderedListOutlined
              className="menu-icon"
              onClick={() => showDrawer()}
            />
            <AntDrawer
              title=""
              width={256}
              placement="left"
              closable={true}
              onClose={() => closeDrawer()}
              visible={visible}
              rootClassName="mobile-drawer menu-sider"
              destroyOnClose={true}
            >
              <Sider
                t={props?.t}
                collapsed={props?.collapsed}
                onCollapseSider={props?.onCollapseSider}
                SiderOptions={props?.SiderOptions}
                location_path={props?.location_path}
                onCollapse={props?.onCollapse}
                currentMenuData={(menu_value: string) =>
                  menuClicked(menu_value)
                }
                currentSubMenuData={(sub_menu_value: any) =>
                  subMenuClicked(sub_menu_value)
                }
                active_menu_key={props?.active_menu_key}
                active_submenu_key={props?.active_submenu_key}
                menuOptions={props?.menuOptions}
              />
            </AntDrawer>
          </div>
          {mobileHeaderIcon}
          {import.meta.env.VITE_MOBILE ? (
            mobileHeaderIcon
          ) : (
            <div
              className={
                "logos mobile-hidden-content" +
                (props?.collapsed ? " collapsed-side" : "") +
                " datoms-header-title"
              }
            >
              {(() => {
                if (props?.HeaderOptions.vendor_logo != "") {
                  return (
                    <img
                      src={props?.HeaderOptions.vendor_logo}
                      className={
                        "company-logo" +
                        (props?.is_white_label ? " white-label" : "")
                      }
                    />
                  );
                }
              })()}
              {(() => {
                if (props?.HeaderOptions.vendor_logo != "") {
                  return <AntDivider type="vertical" />;
                }
              })()}
              {(() => {
                if (props?.HeaderOptions.logo != "") {
                  return (
                    <img
                      src={props?.HeaderOptions.logo}
                      className={
                        "company-logo" +
                        (props?.is_white_label ? " white-label" : "")
                      }
                    />
                  );
                }
              })()}
              <span className="bold company-name">
                {props?.HeaderOptions.company_name}
              </span>
              <AntDivider type="vertical" />
              <span>
                {props.t? props.t(pageName): pageName}
                {/* {props.t
                  ? props.t(pageName?.toLowerCase().replace(/\s+/g, '_'))
                  : pageName?.toLowerCase().replace(/\s+/g, '_')} */}
              </span>
            </div>
          )}
          {window.innerWidth <= 1024 ? (
            <AntMenu
              className="mobile-show-content"
              theme="light"
              mode="horizontal"
              style={{
                lineHeight: "64px",
                display: "flex",
                flex: 1,
                justifyContent: "flex-end",
                alignItems: "center",
              }}
              items={headerMenuItems}
            />
          ) : (
            <AntMenu
              className="mobile-hidden-content"
              theme="light"
              mode="horizontal"
              style={{
                lineHeight: "64px",
                display: "flex",
                flex: 1,
                justifyContent: "flex-end",
                alignItems: "center",
              }}
              items={headerMenuItems}
            />
          )}
        </div>
        {bannerToBeShown() ? (
          <div className={"compliance-banner-container"}>
            <Marquee className="malarquee-padding" pauseOnHover={true}>
              Data available at this portal is as per CPCB prescribed procedure
              published at{" "}
              <a href="http://cpcb.nic.in" target="_blank" rel="noreferrer">
                cpcb.nic.in
              </a>
            </Marquee>
          </div>
        ) : (
          ""
        )}
      </AntHeader>
      <div style={{ display: "none" }} id="google_translate_element" />
      {preferanceModal}
      {cookieBanner}
    </AntLayout>
  );
};

export default Head;
