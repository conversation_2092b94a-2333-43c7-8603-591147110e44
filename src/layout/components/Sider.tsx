import React from "react";
import { Link } from "react-router-dom";
import { Icon as LegacyIcon } from "@ant-design/compatible";
import { mixPanelTrackUser } from "@datoms/js-utils/src/mix-panel";
import AntLayout from "@datoms/react-components/src/components/AntLayout";
import AntSider from "@datoms/react-components/src/components/AntSider";
import AntMenu from "@datoms/react-components/src/components/AntMenu";
import AntSubMenu from "@datoms/react-components/src/components/AntSubMenu";
import AntMenuItem from "@datoms/react-components/src/components/AntMenuItem";
import { MenuOptionDataProps, SiderProps, SubmenuDataProps } from "../types";

const Sider: React.FC<SiderProps> = (props) => {
  const {
    sider_style,
    SiderOptions,
    collapsed,
    onCollapse,
    white_label_icon,
    isFixedLogoForWhitelabel,
    active_menu_key,
    menuOptions,
    currentSubMenuData,
    active_submenu_key,
    first_key,
    custom_menu,
    param_value,
    handleUserNotificationDrawerClose,
    currentMenuData,
    onMenuClick,
    menuKeys,
  } = props;

  return (
    <AntLayout>
      <AntSider
        style={{
          "background-color": sider_style?.background_color ?? "",
        }}
        trigger={
          sider_style ? (
            <div
              style={{
                backgroundColor: "#fff",
                color: "#002140",
              }}
            >
              {">"}
            </div>
          ) : (
            ""
          )
        }
        default_type={true}
        collapsible={SiderOptions?.collapsible ? true : false}
        className="side-menu"
        collapsed={window.innerWidth <= 1024 ? false : collapsed}
        onCollapse={() => onCollapse()}
      >
        <div className="logo" />
        {(() => {
          if (SiderOptions?.non_collapse_logo || SiderOptions?.collapse_logo) {
            let collapseLogo = white_label_icon?.small_icon?.length
              ? white_label_icon?.small_icon
              : SiderOptions?.collapse_logo;
            let nonCollapseLogo = white_label_icon?.large_icon?.length
              ? white_label_icon?.large_icon
              : SiderOptions?.non_collapse_logo;
            return (
              <div
                className={
                  "imgs imgs-logo-container" + (collapsed ? " collapsed" : "")
                }
              >
                {(() => {
                  if (SiderOptions?.non_collapse_logo) {
                    return (
                      <img
                        className={
                          "big-logo" +
                          (isFixedLogoForWhitelabel ? " fixed-big-logo" : "") +
                          (!collapsed ? " show" : " hide")
                        }
                        src={nonCollapseLogo}
                      />
                    );
                  }
                })()}
                {(() => {
                  if (window.innerWidth <= 1024) {
                    return (
                      <img
                        className={
                          "big-logo" +
                          (isFixedLogoForWhitelabel ? " fixed-big-logo" : "") +
                          (collapsed ? " show" : " hide")
                        }
                        src={nonCollapseLogo}
                      />
                    );
                  }
                  if (SiderOptions?.collapse_logo) {
                    return (
                      <img
                        className={
                          "small-logo" +
                          (isFixedLogoForWhitelabel
                            ? " fixed-small-logo"
                            : "") +
                          (!collapsed ? " hide" : " show")
                        }
                        src={collapseLogo}
                      />
                    );
                  }
                })()}
              </div>
            );
          }
        })()}
        <AntMenu
          theme="dark"
          mode="inline"
          defaultSelectedKeys={
            window.location.href ? [window.location.href.split("/")[2]] : []
          }
          style={{
            "background-color": sider_style?.background_color ?? "",
          }}
          defaultOpenKeys={
            window.location.href ? [window.location.href.split("/")[2]] : []
          }
          selectedKeys={active_menu_key}
        >
          {(() => {
            if (menuOptions) {
              return menuOptions.map(
                (data: MenuOptionDataProps, index: any) => {
                  let openTarget = "_self";
                  if (data.new_tab) {
                    openTarget = "_blank";
                  }
                  if (data.submenu_items && data.submenu_items.length) {
                    return (
                      <AntSubMenu
                        key={data.key}
                        onClick={() => {
                          currentSubMenuData(data);
                          mixPanelTrackUser("Nav Menu");
                        }}
                        title={
                          <span>
                            {(() => {
                              if (data.icon_type) {
                                return <LegacyIcon type={data.icon_type} />;
                              } else if (data.component) {
                                return (
                                  <LegacyIcon component={data.component} />
                                );
                              }
                            })()}
                            <span>{data.page_name}</span>
                          </span>
                        }
                      >
                        {(() => {
                          if (data.submenu_items && data.submenu_items.length) {
                            return data.submenu_items.map(
                              (submenu_data: SubmenuDataProps) => {
                                const className = window.location.href.includes(
                                  submenu_data.url?.toLowerCase(),
                                )
                                  ? "ant-menu-item-selected"
                                  : "";
                                return (
                                  <AntMenuItem
                                    className={className}
                                    style={{
                                      color:
                                        submenu_data.key == active_submenu_key
                                          ? ""
                                          : sider_style?.icon_color ?? "",
                                    }}
                                    key={submenu_data.key}
                                  >
                                    <Link
                                      to={submenu_data.url}
                                      target={openTarget}
                                    >
                                      {/* {props.t? props.t(submenu_data.name?.toLowerCase().replace(/\s+/g, '_')): submenu_data.name} */}
                                      {props.t? props.t(submenu_data.name): submenu_data.name}
                                    </Link>
                                  </AntMenuItem>
                                );
                              },
                            );
                          }
                        })()}
                      </AntSubMenu>
                    );
                  } else {
                    return (
                      <AntMenuItem
                        style={{
                          color:
                            data.key == active_menu_key
                              ? ""
                              : sider_style?.icon_color ?? "",
                          "background-color":
                            data.key == active_menu_key
                              ? sider_style?.active_icon_background_color ?? ""
                              : "",
                        }}
                        className={
                          menuKeys?.some((key) =>
                            data?.alternativeKeys?.includes(key.toLowerCase()),
                          )
                            ? "ant-menu-item-selected"
                            : data.key &&
                                window.location.href &&
                                active_menu_key
                              ? data.key == first_key &&
                                active_menu_key == "no value"
                                ? "ant-menu-item-selected"
                                : window.location.href.includes(
                                      active_menu_key?.toLowerCase(),
                                    ) && data.key == active_menu_key
                                  ? "ant-menu-item-selected"
                                  : active_menu_key == data.key
                                    ? "ant-menu-item-selected"
                                    : ""
                              : custom_menu && data.menu_value && param_value
                                ? parseInt(active_menu_key) ===
                                  parseInt(param_value)
                                  ? "ant-menu-item-selected"
                                  : ""
                                : active_menu_key == data.key
                                  ? "ant-menu-item-selected"
                                  : ""
                        }
                        key={data.key}
                        onClick={() => {
                          handleUserNotificationDrawerClose();
                          currentMenuData(data);
                          mixPanelTrackUser("Nav Menu");
                        }}
                      >
                        {(() => {
                          if (data.url) {
                            return (
                              <Link to={data.url} target={openTarget}>
                                {(() => {
                                  if (data.icon_type) {
                                    return <LegacyIcon type={data.icon_type} />;
                                  } else if (data.component) {
                                    return (
                                      <LegacyIcon component={data.component} />
                                    );
                                  }
                                })()}
                                <span>
                                  {props.t? props.t(data.page_name): data.page_name}
                                  {/* {props.t
                                    ? props.t(
                                        ['map_view'].includes(
                                          data.page_name?.toLowerCase().replace(/-/g, '_').replace(/\s+/g, '_')
                                        )
                                          ? data.page_name.toLowerCase().replace(/-/g, '_').replace(/\s+/g, '_')
                                          : data.page_name?.toLowerCase().replace(/-/g, '_') || ''
                                      )
                                    : data.page_name} */}
                                </span>
                              </Link>
                            );
                          }
                          if (onMenuClick && data.menu_value) {
                            return (
                              <div
                                onClick={() => {
                                  handleUserNotificationDrawerClose();
                                  onMenuClick(data.menu_value);
                                }}
                              >
                                <span>{data.page_name}</span>
                              </div>
                            );
                          }
                        })()}
                      </AntMenuItem>
                    );
                  }
                },
              );
            }
          })()}
        </AntMenu>
      </AntSider>
    </AntLayout>
  );
};

export default Sider;
