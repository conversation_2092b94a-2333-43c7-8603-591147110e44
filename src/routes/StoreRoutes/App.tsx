import React from "react";
import { Route } from "react-router-dom";
import { getRoutesMapping, non_exact_paths } from "./routes";
import { JSX } from "react/jsx-runtime";

const Home = React.lazy(
  () => import("@datoms/datoms-store-front/src/pages/Home"),
);
const Bookings = React.lazy(
  () => import("@datoms/datoms-store-front/src/pages/Bookings"),
);
const AccountDetails = React.lazy(
  () => import("@datoms/datoms-store-front/src/pages/AccountDetails"),
);
const HelpDesk = React.lazy(
  () => import("@datoms/datoms-store-front/src/pages/HelpDesk"),
);

export function StoreRoutes(
  client_id: number,
  head_side_object_data: any,
  AlertObjectData: {
    buttons: { text: string; value: string; primary: boolean }[];
    status_btn: { text: string; value: string; primary: boolean }[];
    show_menu: boolean;
    application_menu: {
      head_text: string[];
      menu_items: { key: string; page_name: string; menu_value: number }[];
      bg_color: string;
      selected_menu_color: string;
    };
    thing_category_select: {
      label: string[];
      select_option: {
        show_arrow: boolean;
        default_value: string;
        options: never[];
      }[];
    };
    alert_table: {
      action_button: {
        icon_type: string;
        text: string;
        show_action_button: boolean;
      };
      config: {
        pagination_data: {
          size: string;
          total: number;
          pageSize: number;
          showSizeChanger: boolean;
          showQuickJumper: boolean;
          hideOnSinglePage: boolean;
        };
        bordered: boolean;
        size: string;
        showHeader: boolean;
        rowSelect: boolean;
        scroll: { y: number };
        loading: boolean;
        locale: {
          filterTitle: string;
          filterConfirm: string;
          filterReset: string;
          emptyText: string;
        };
      };
      head_data: (
        | {
            title: JSX.Element;
            dataIndex: string;
            key: string;
            render: (value: any, row_data: any, index: any) => JSX.Element;
            width: string;
            align?: undefined;
          }
        | {
            title: JSX.Element;
            key: string;
            align: string;
            render: (value: any, row_data: any, index: any) => JSX.Element;
            width: string;
            dataIndex?: undefined;
          }
      )[];
      row_data: never[];
    };
  },
  template_id: string | number,
  getViewAccess: any,
  getRemoteAccess: any,
  getRemoteLockAccess: any,
  enabled_features: any,
  plan_description: {},
  props: any,
  application_id: number,
  application_name: string,
  client_name: string,
  client_logo: string,
  vendor_logo: string,
  user_id: string | number,
  user_preferences: any,
  collapsed: boolean,
  onCollapseSider: any,
  t: string,
  globalConfig: any,
  app_name: string | number,
  vendor_id: string | number,
  logged_in_user_client_id: string,
  logged_in_user_role_type: string,
  authToken: any,
  checkCustomerFuelBuddy: boolean,
  is_white_label: string,
  vendor_name: boolean,
  loadingLogo: any,
  firstThingCat: any,
  setMenuUrl: string,
  user_name: any,
  setStoreLogout: any,
  allApps: any,
) {
  const final_routes: JSX.Element[] = [];
  const page_name_map = {
    Home,
    Bookings,
    AccountDetails,
    HelpDesk,
  };
  const common_props = {
    client_id,
    head_side_object_data,
    AlertObjectData,
    template_id,
    getViewAccess,
    getRemoteAccess,
    getRemoteLockAccess,
    enabled_features,
    plan_description,
    props,
    application_id,
    application_name,
    client_name,
    client_logo,
    vendor_logo,
    user_id,
    user_preferences,
    collapse: collapsed,
    onCollapseSider,
    t,
    globalConfig,
    app_name,
    vendor_id,
    logged_in_user_client_id,
    logged_in_user_role_type,
    authToken,
    checkCustomerFuelBuddy,
    is_white_label,
    vendor_name,
    loadingLogo,
    firstThingCat,
    setMenuUrl,
    user_name,
    setStoreLogout,
    allApps,
  };
  const route_mapping = getRoutesMapping();
  Object.keys(route_mapping).map((page) => {
    const page_routes = route_mapping[page];
    const PageName = page_name_map[page];
    page_routes.map((path: unknown) => {
      final_routes.push(
        <Route
          exact={non_exact_paths.includes(page) ? false : true}
          path={path}
          render={(props) => <PageName {...common_props} {...props} />}
        />,
      );
    });
  });
  return final_routes;
}
