import React from "react";
import { Route } from "react-router-dom";
import MachineInfoProps from "../MachineInfoProps";
import {
  getBasePathComponent,
  getRoutesMapping,
  non_exact_paths,
} from "./routes";
import { JSX } from "react/jsx-runtime";

const MorePage = React.lazy(
  () =>
    import(
      "@datoms/dg-monitoring-views/src/js/GenericTemplate/component/MorePage"
    ),
);
const SiteView = React.lazy(
  () =>
    import("@datoms/dg-monitoring-views/src/js/GenericTemplate/pages/SiteView"),
);
const RemoteCalibration = React.lazy(
  () =>
    import(
      "@datoms/dg-monitoring-views/src/js/GenericTemplate/pages/RemoteCalibration"
    ),
);
const QueryStringInUrlGeneric = React.lazy(
  () =>
    import(
      "@datoms/dg-monitoring-views/src/js/GenericTemplate/pages/QuerystringInUrlGeneric/"
    ),
);
const GenericPanelView = React.lazy(
  () =>
    import("@datoms/dg-monitoring-views/src/js/GenericTemplate/pages/Panel"),
);
const GenericDetailedView = React.lazy(
  () =>
    import(
      "@datoms/dg-monitoring-views/src/js/GenericTemplate/pages/DetailedView"
    ),
);
const GenericRealTime = React.lazy(
  () =>
    import("@datoms/dg-monitoring-views/src/js/GenericTemplate/pages/RealTime"),
);
const GenericMapView = React.lazy(
  () =>
    import(
      "@datoms/dg-monitoring-views/src/js/GenericTemplate/pages/MapView/pages"
    ),
);
const DGDashboard = React.lazy(
  () =>
    import(
      "@datoms/dg-monitoring-views/src/js/DGMonitoring/Template-17/Dashboard"
    ),
);
const GenericAssetDashboard = React.lazy(
  () =>
    import(
      "@datoms/dg-monitoring-views/src/js/GenericTemplate/pages/AssetDashboard"
    ),
);
const SiteDashboard = React.lazy(
  () => import("../../packages/dg-monitoring-views/src/js/SiteDashboard"),
);

const ReportTemplates = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/ReportTemplates"
    ),
);
const CustomReport = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-reports/src/components/CustomReport"
    ),
);
const HotspotReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/components/HotspotReport"
    ),
);

const AurassureHourlyReport = React.lazy(
  () => import("../../containers/reports/AurassureReports/HourlyReport"),
);
const AurassureDailyReport = React.lazy(
  () => import("../../containers/reports/AurassureReports/DailyReport"),
);
const AurassureMonthlyReport = React.lazy(
  () => import("../../containers/reports/AurassureReports/MonthlyReport"),
);
const JindalMultiAssetReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/aurassure-reports/JindalMultiAssetReport"
    ),
);
const DataAvailabilityReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/DataAvailabilityReport"
    ),
);
const DataAvailability = React.lazy(
  () =>
    import(
      "../../containers/reports/DataAvailabilityReport"
    ),
);
const DgStatusReport = React.lazy(
  () =>
    import("../../packages/iot-platform-views/src/js/reports/DgStatusReport"),
);
const SummaryReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/SummaryReports"
    ),
);
const TripView = React.lazy(
  () =>
    import("@datoms/dg-monitoring-views/src/js/GenericTemplate/pages/TripView"),
);
/*Dg Set */
const DgDailyReport = React.lazy(
  () =>
    import(
      "../../containers/reports/DailyReports/DgDailyReport"
    ),
);
const GasGensetDailyReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/DGReport/pages/DailyReports"
    ),
);
const GasGensetMultiAssetReports = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/GasGensetMultiAssetReport"
    ),
);
const CriticalReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/CriticalTrendsReport"
    ),
);
const DGMultiAssetReport = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/DgMultiAssetReport"
    ),
);
const DGSnapshotReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/DGReport/pages/SnapshotReport"
    ),
);
const DgRunReports = React.lazy(
  () =>
    import(
      "../../containers/reports/TripReports/DgTripReport"
    ),
);
const GasGensetTripReport = React.lazy(
  () =>
    import(
      "../../containers/reports/TripReports/GasGensetTripReport"
    ),
);
const DGFaultReport = React.lazy(
  () =>
    import(
      "../../containers/reports/FaultReports/DgFaultReport"
    ),
);
const GasGensetFaultReport = React.lazy(
  () =>
    import(
      "../../containers/reports/FaultReports/GasGensetFaultReport"
    ),
);
const FuelFillDrainReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/dg-new-reports/pages/FuelFillDrainReport"
    ),
);
/*summary reports */
const LifetimeReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/summary-reports/pages/LifetimeReports"
    ),
);
const YesterdayDailyFaultReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/YesterdayDailyFaultReport"
    ),
);
const ParameterReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/summary-reports/pages/ParameterReports"
    ),
);
/*Fuel Tank */
const FuelTankDailyReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fuel-tank-reports/pages/DailyReports"
    ),
);
const FuelTankSnapShotReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fuel-tank-reports/pages/SnapshotReports"
    ),
);
const FuelTankFillReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fuel-tank-reports/pages/FuelTankFillReport"
    ),
);
/*Compressor */
const CompDailyReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/compressor-reports/DailyReports"
    ),
);
const CompDgRunReports = React.lazy(
  () =>
    import(
      "../../containers/reports/TripReports/CompressorTripReport"
    ),
);
const CompFaultReports = React.lazy(
  () =>
    import(
      "../../containers/reports/FaultReports/CompressorFaultReport"
    ),
);
const CompressorMultiAssetReport = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/CompressorMultiAssetReport"
    ),
);
/*Fleet */
const FleetDailyReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/FleetReports/DailyReports"
    ),
);
const FleetTripReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/FleetReports/TripReports"
    ),
);
const FleetTripDetails = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/FleetReports/TripDetails"
    ),
);

/*Tanker Truck*/
const TankerTruckFuelTankerDailyReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/TankerTruckDailyReports/FuelTankerDailyReports"
    ),
);

const TankerTruckVehicleDailyReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/TankerTruckDailyReports/VehicleReports"
    ),
);

const TankerTruckFuelFilledDispenseReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/TankerTruckDailyReports/FuelFilledDispenseReports"
    ),
);
const TankerTruckTripReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/TankerTruckDailyReports/TripReports"
    ),
);
const TankerTruckTripDetails = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/TankerTruckDailyReports/TripDetails"
    ),
);

const AlertSubscriptionThingsReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/alerSubscription/pages"
    ),
);
/*Energy Meter */
const EnergyMeterDailyReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/energy-meter-reports/pages/DailyReports"
    ),
);

const inverterDailyReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/energy-meter-reports/pages/DailyReports"
    ),
);

const ProcessAnalyzerDailyReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/process-analyzer-reports/pages/DailyReports"
    ),
);

const ProcessAnalyzerFaultReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/process-analyzer-reports/pages/FaultReport"
    ),
);

const SolarPowerDailyReport = React.lazy(
  () =>
    import(
      "../../containers/reports/DailyReports/SolarPowerDailyReport"
    ),
);

const SolarPowerTripReport = React.lazy(
  () =>
    import(
      "../../containers/reports/TripReports/SolarPowerTripReport"
    ),
);

const SolarPowerMultiAssetReport = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/SolarPowerMultiAssetReport"
    ),
);

const SolarPumpDailyReport = React.lazy(
  () =>
    import(
      "../../containers/reports/DailyReports/SolarPumpDailyReport"
    ),
);

const SolarPumpTripReport = React.lazy(
  () =>
    import(
      "../../containers/reports/TripReports/SolarPumpTripReport"
    ),
);

const SolarPumpMultiAssetReport = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/SolarPumpMultiAssetReport"
    ),
);

const ElevatorFaultReport = React.lazy(
  () =>
    import(
      "../../containers/reports/FaultReports/ElevatorFaultReport"
    ),
);

const SolarPumpFaultReport = React.lazy(
  () =>
    import(
      "../../containers/reports/FaultReports/SolarPumpFaultReport"
    ),
);

const AcEnergyDailyReport = React.lazy(
  () =>
    import(
      "../../containers/reports/DailyReports/AcEnergyMeterDailyReport"
    ),
);

const GridEnergyMeterDailyReport = React.lazy(
  () =>
    import(
      "../../containers/reports/DailyReports/GridEnergyMeterDailyReport"
    ),
);

const AcEnergyMultiAsserReport = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/AcEnergyMeterMultiAssetReport"
    ),
);

const GridEnergyMeterMultiAssetReport = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/GridEnergyMeterMultiAssetReport"
    ),
);

const AcElectricalMachinesDailyReport = React.lazy(
  () =>
    import(
      "../../containers/reports/DailyReports/AcElectricalMachineDailyReport"
    ),
);

const AcElectricalMachinesTripReport = React.lazy(
  () =>
    import(
      "../../containers/reports/TripReports/AcElectricalMachineTripReport"
    ),
);

const AcElectricalMachinesMultiAsserReport = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/AcElectricalMachineMultiAssetReport"
    ),
);

const ExhaustFanDailyReport = React.lazy(
  () =>
    import(
      "../../containers/reports/DailyReports/ExhaustFanDailyReport"
    ),
);
const ExhaustFanTripReport = React.lazy(
  () =>
    import(
      "../../containers/reports/TripReports/ExhaustFanTripReport"
    ),
);
const ExhaustFanMultiAsserReport = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/ExhaustFanMultiAssetReport"
    ),
);

const TempHumidDailyReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/temp-humid-report/pages/DailyReports"
    ),
);

const EnergyMeterAvailabilityReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/energy-meter-reports/pages/AvailabilityReport"
    ),
);

const BatteryDailyReport = React.lazy(
  () =>
    import(
      "../../containers/reports/DailyReports/BatteryDailyReport"
    ),
);

const BatteryMultiAssetReport = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/BatteryMultiAssetReport"
    ),
);


const DcEnergyMeterDailyReport = React.lazy(
  () =>
    import(
      "../../containers/reports/DailyReports/DcEnergyMeterDailyReport"
    ),
);

const DcEnergyMeterMultiAssetReport = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/DcEnergyMeterMultiAssetReport"
    ),
);

const AssignedDevice = React.lazy(
  () =>
    import(
      /* webpackChunkName: "AssignedDevice"*/ "../../packages/webapp-component-device-management/src/components/AssignedDevice"
    ),
);
const UnassignedDevice = React.lazy(
  () =>
    import(
      /* webpackChunkName: "UnassignedDevice"*/ "../../packages/webapp-component-device-management/src/components/UnassignedDevice"
    ),
);
const DeviceCalibration = React.lazy(
  () =>
    import(
      /* webpackChunkName: "DeviceCalibration"*/ "../../packages/webapp-component-device-management/src/components/DeviceCalibration"
    ),
);
const DeviceCommunication = React.lazy(
  () =>
    import(
      /* webpackChunkName: "DeviceCommunication"*/ "../../packages/webapp-component-device-management/src/components/DeviceCommunication"
    ),
);
const DeviceCustomCommand = React.lazy(
  () =>
    import(
      /* webpackChunkName: "DeviceCustomCommand"*/ "../../packages/webapp-component-device-management/src/components/DeviceCustomCommand"
    ),
);
const DeviceConfiguration = React.lazy(
  () =>
    import(
      /* webpackChunkName: "DeviceConfiguration"*/ "../../packages/webapp-component-device-management/src/components/DeviceConfiguration"
    ),
);
const DeviceHealth = React.lazy(
  () =>
    import(
      /* webpackChunkName: "DeviceHealth"*/ "../../packages/webapp-component-device-management/src/components/DeviceHealth"
    ),
);

const ThingList = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-thing-management/src/components/ThingList"
    ),
);

const ThingConfigV2 = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-thing-management/src/components/ThingAddV2"
    ),
);
const ThingDetails = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-thing-management/src/components/ThingDetails/ThingDetails"
    ),
);

const SiteList = React.lazy(
  () =>
    import(
      /* webpackChunkName: "SiteList"*/ "../../packages/webapp-component-thing-management/src/components/SiteList"
    ),
);
const SiteConfiguration = React.lazy(
  () =>
    import(
      /* webpackChunkName: "SiteConfiguration"*/ "../../packages/webapp-component-thing-management/src/components/SiteConfiguration"
    ),
);
const TerritoryPage = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-user-management/src/components/TerritoryPage"
    ),
);
const UsersManagement = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-user-management/src/components/UsersManagement"
    ),
);

const UserFullNotification = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-user-notifications/src/components/UserFullNotification"
    ),
);

const RuleManagement = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-alerts-management/src/components/RuleManagement"
    ),
);
const ServiceModule = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-alerts-management/src/components/ServiceModule"
    ),
);
const WorkFlow = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/GenericTemplate/pages/Workflow"
    ),
);

const DDS = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/GenericTemplate/pages/DDSDisplay"
    ),
);

const FlowMeterDailyReport = React.lazy(
  () =>
    import(
      "../../containers/reports/DailyReports/FlowMeterDailyReport"
    ),
);

const FlowMeterHourlyReport = React.lazy(
  () =>
    import(
      "../../containers/reports/HourlyReports/FlowMeterHourlyReport"
    ),
);

const FlowMeterMultiAssetReport = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/FlowMeterMultiAssetReport"
    ),
);

const FlowMeterSiteConsumptionReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/flow-meter-reports/pages/SiteConsumption"
    ),
);

const GensetFuelTankSiteReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/DGReport/pages/GensetFuelTankSiteReport"
    ),
);

const EnergySummaryReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/WarehouseSiteReport/EnergySummaryReport"
    ),
);

const SensorSummaryReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/WarehouseSiteReport/SensorSummaryReport"
    ),
);

const SMSSummaryReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/SMSSummaryReport"
    ),
);

const RiceMilletEnergyReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/WarehouseSiteReport/RiceMilletEnergyReport"
    ),
);

const PollutionParamRangeSummaryReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/PollutionMonitoringReports/pages/ParamRangeReport"
    ),
);

const Settings = React.lazy(
  () => import("@datoms/datoms-settings/src/index.js"),
);

const DatomsPolicy = React.lazy(
  () => import("@datoms/datoms-settings/src/pages/DatomsPolicy"),
);
const TicketingSupport = React.lazy(
  () => import("../../containers/TicketingSupport"),
);

const GravityReportDemo = React.lazy(
  () => import("../../containers/reports/GravityReports/DemoReport"),
)

const ListView = React.lazy(
  () => import("../../containers/views/ListView"),
)

const MapView = React.lazy(
  () => import("../../containers/views/MapView"),
);

export const GenericRoutes = (
  client_id: number,
  head_side_object_data: any,
  menuUrlArray: any,
  AlertObjectData: {
    buttons: { text: string; value: string; primary: boolean }[];
    status_btn: { text: string; value: string; primary: boolean }[];
    show_menu: boolean;
    application_menu: {
      head_text: string[];
      menu_items: { key: string; page_name: string; menu_value: number }[];
      bg_color: string;
      selected_menu_color: string;
    };
    thing_category_select: {
      label: string[];
      select_option: {
        show_arrow: boolean;
        default_value: string;
        options: never[];
      }[];
    };
    alert_table: {
      action_button: {
        icon_type: string;
        text: string;
        show_action_button: boolean;
      };
      config: {
        pagination_data: {
          size: string;
          total: number;
          pageSize: number;
          showSizeChanger: boolean;
          showQuickJumper: boolean;
          hideOnSinglePage: boolean;
        };
        bordered: boolean;
        size: string;
        showHeader: boolean;
        rowSelect: boolean;
        scroll: { y: number };
        loading: boolean;
        locale: {
          filterTitle: string;
          filterConfirm: string;
          filterReset: string;
          emptyText: string;
        };
      };
      head_data: (
        | {
            title: JSX.Element;
            dataIndex: string;
            key: string;
            render: (value: any, row_data: any, index: any) => JSX.Element;
            width: string;
            align?: undefined;
          }
        | {
            title: JSX.Element;
            key: string;
            align: string;
            render: (value: any, row_data: any, index: any) => JSX.Element;
            width: string;
            dataIndex?: undefined;
          }
      )[];
      row_data: never[];
    };
  },
  template_id: string | number,
  getViewAccess: any,
  getRemoteAccess: any,
  getRemoteLockAccess: any,
  enabled_features: string | string[],
  plan_description: {},
  props: any,
  application_id: number,
  application_name: string,
  client_name: string,
  client_logo: string,
  vendor_logo: string,
  user_id: string | number,
  user_preferences: { timezone: any },
  collapsed: boolean,
  onCollapseSider: any,
  t: any,
  globalConfig: any,
  app_name: string,
  vendor_id: string | number,
  logged_in_user_client_id: string | number,
  logged_in_user_role_type: string,
  authToken: string,
  checkCustomerFuelBuddy: any,
  is_white_label: boolean,
  vendor_name: string,
  loadingLogo: boolean,
  firstThingCat: any,
  setMenuUrl: any,
  thing_list: any,
  bannerToBeShown: any,
  logged_in_user_name: string,
  allApps: string[],
  currentUserPreferences: any,
  updateCurrentUserPreference: any,
  parent_vendor_id: any,
) => {
  const final_routes: JSX.Element[] = [];
  let page_name_map = {
    SiteView: SiteView,
    SiteDashboard: SiteDashboard,
    GenericMapView: GenericMapView,
    DGDashboard: DGDashboard,
    GenericAssetDashboard: GenericAssetDashboard,
    GenericRealTime: GenericRealTime,
    GenericDetailedView: GenericDetailedView,
    GenericPanelView: GenericPanelView,
    QueryStringInUrlGeneric: QueryStringInUrlGeneric,
    ReportTemplates: ReportTemplates,
    CustomReport: CustomReport,
    HotspotReport: HotspotReport,
    AurassureHourlyReport: AurassureHourlyReport,
    AurassureDailyReport: AurassureDailyReport,
    AurassureMonthlyReport: AurassureMonthlyReport,
    JindalMultiAssetReport: JindalMultiAssetReport,
    DataAvailabilityReport: DataAvailabilityReport,
    DataAvailability: DataAvailability,
    DgStatusReport: DgStatusReport,
    SummaryReports: SummaryReports,
    DgDailyReport: DgDailyReport,
    GasGensetDailyReports: GasGensetDailyReports,
    GasGensetMultiAssetReports: GasGensetMultiAssetReports,
    CriticalReports: CriticalReports,
    DgRunReports: DgRunReports,
    GasGensetTripReport: GasGensetTripReport,
    DGMultiAssetReport: DGMultiAssetReport,
    DGSnapshotReport: DGSnapshotReport,
    DGFaultReport: DGFaultReport,
    GasGensetFaultReport:GasGensetFaultReport,
    FuelFillDrainReport: FuelFillDrainReport,
    LifetimeReports: LifetimeReports,
    YesterdayDailyFaultReport: YesterdayDailyFaultReport,
    ParameterReports: ParameterReports,
    FuelTankDailyReports: FuelTankDailyReports,
    FuelTankSnapShotReports: FuelTankSnapShotReports,
    FuelTankFillReport: FuelTankFillReport,
    CompDailyReports: CompDailyReports,
    CompDgRunReports: CompDgRunReports,
    CompFaultReports: CompFaultReports,
    CompressorMultiAssetReport: CompressorMultiAssetReport,
    FleetDailyReports: FleetDailyReports,
    FleetTripReports: FleetTripReports,
    FleetTripDetails: FleetTripDetails,
    TankerTruckFuelTankerDailyReports: TankerTruckFuelTankerDailyReports,
    TankerTruckVehicleDailyReports: TankerTruckVehicleDailyReports,
    FlowMeterDailyReport: FlowMeterDailyReport,
    FlowMeterHourlyReport: FlowMeterHourlyReport,
    FlowMeterMultiAssetReport: FlowMeterMultiAssetReport,
    FlowMeterSiteConsumptionReport: FlowMeterSiteConsumptionReport,
    GensetFuelTankSiteReport: GensetFuelTankSiteReport,
    SolarPowerDailyReport: SolarPowerDailyReport,
    SolarPumpDailyReport: SolarPumpDailyReport,
    SolarPowerTripReport: SolarPowerTripReport,
    SolarPumpTripReport: SolarPumpTripReport,
    EnergySummaryReport: EnergySummaryReport,
    SensorSummaryReport: SensorSummaryReport,
    SMSSummaryReport: SMSSummaryReport,
    RiceMilletEnergyReport: RiceMilletEnergyReport,
    PollutionParamRangeSummaryReport: PollutionParamRangeSummaryReport,
    TankerTruckFuelFilledDispenseReports: TankerTruckFuelFilledDispenseReports,
    TankerTruckTripReports: TankerTruckTripReports,
    TankerTruckTripDetails: TankerTruckTripDetails,
    AlertSubscriptionThingsReport: AlertSubscriptionThingsReport,
    EnergyMeterDailyReport: EnergyMeterDailyReport,
    inverterDailyReport: inverterDailyReport,
    ProcessAnalyzerDailyReport: ProcessAnalyzerDailyReport,
    ProcessAnalyzerFaultReport: ProcessAnalyzerFaultReport,
    SolarPowerMultiAssetReport: SolarPowerMultiAssetReport,
    SolarPumpMultiAssetReport: SolarPumpMultiAssetReport,
    ElevatorFaultReport: ElevatorFaultReport,
    SolarPumpFaultReport: SolarPumpFaultReport,
    AcEnergyDailyReport: AcEnergyDailyReport,
    GridEnergyMeterDailyReport: GridEnergyMeterDailyReport,
    AcEnergyMultiAsserReport: AcEnergyMultiAsserReport,
    GridEnergyMeterMultiAssetReport: GridEnergyMeterMultiAssetReport,
    AcElectricalMachinesDailyReport: AcElectricalMachinesDailyReport,
    AcElectricalMachinesTripReport: AcElectricalMachinesTripReport,
    AcElectricalMachinesMultiAsserReport: AcElectricalMachinesMultiAsserReport,
    ExhaustFanDailyReport: ExhaustFanDailyReport,
    ExhaustFanTripReport: ExhaustFanTripReport,
    ExhaustFanMultiAsserReport: ExhaustFanMultiAsserReport,
    TempHumidDailyReport: TempHumidDailyReport,
    EnergyMeterAvailabilityReport: EnergyMeterAvailabilityReport,
    RuleManagement: RuleManagement,
    ServiceModule: ServiceModule,
    WorkFlow: WorkFlow,
    DDS: DDS,
    AssignedDevice: AssignedDevice,
    UnassignedDevice: UnassignedDevice,
    DeviceCalibration: DeviceCalibration,
    DeviceCommunication: DeviceCommunication,
    DeviceConfiguration: DeviceConfiguration,
    DeviceCustomCommand: DeviceCustomCommand,
    TerritoryPage: TerritoryPage,
    DeviceHealth: DeviceHealth,
    ThingAdd: ThingConfigV2,
    ThingConfigure: ThingConfigV2,
    ThingList: ThingList,
    SiteConfiguration: SiteConfiguration,
    SiteList: SiteList,
    ThingDetails: window.innerWidth > 576 ? ThingList : ThingDetails,
    UsersManagement: UsersManagement,
    UserFullNotification: UserFullNotification,
    MorePage: MorePage,
    TripView: TripView,
    RemoteCalibration: RemoteCalibration,
    Settings: Settings,
    DatomsPolicy: DatomsPolicy,
    TicketingSupport: TicketingSupport,
    BatteryDailyReport: BatteryDailyReport,
    BatteryMultiAssetReport: BatteryMultiAssetReport,
    DcEnergyMeterDailyReport: DcEnergyMeterDailyReport,
    DcEnergyMeterMultiAssetReport: DcEnergyMeterMultiAssetReport,
    GravityReportDemo: GravityReportDemo,
    ListView: ListView,
    MapView: MapView,
  };
  const common_props = {
    client_id,
    head_side_object_data,
    menuUrlArray,
    AlertObjectData,
    template_id,
    getViewAccess,
    getRemoteAccess,
    getRemoteLockAccess,
    enabled_features,
    plan_description,
    props,
    application_id,
    application_name,
    client_name,
    client_logo,
    vendor_logo,
    user_id,
    user_preferences,
    collapse: collapsed,
    onCollapseSider,
    t,
    globalConfig,
    app_name,
    vendor_id,
    logged_in_user_client_id,
    logged_in_user_role_type,
    authToken,
    checkCustomerFuelBuddy,
    is_white_label,
    vendor_name,
    loadingLogo,
    firstThingCat,
    setMenuUrl,
    thing_list,
    bannerToBeShown,
    logged_in_user_name,
    allApps,
    currentUserPreferences,
    updateCurrentUserPreference,
    isGenericView: true,
    parent_vendor_id,
  };

  const route_mapping = getRoutesMapping(common_props);
  const baseComponentName = getBasePathComponent(common_props);
  route_mapping.forEach((routeItem) => {
    const PageName = page_name_map[routeItem.component];
    if (!PageName) return; // Page component not available

    const pageRoutes = routeItem.getPath(routeItem.path, common_props);
    if (!pageRoutes.length) return; // Page route access not available
    if (routeItem.component === baseComponentName) {
      pageRoutes.push("/", "/dg-monitoring", "/dg-monitoring/generic/");
    }
    let isScheduledReport = enabled_features?.includes("Reports:Scheduled");
    final_routes.push(
      <Route
        exact={non_exact_paths.includes(routeItem.component) ? false : true}
        path={pageRoutes}
        render={(props) => (
          <PageName
            {...common_props}
            {...props}
            timeZone={user_preferences?.timezone}
            data_api={["data"]}
            report_disabled_cats={[44, 85]}
            type={"custom"}
            isScheduledReport={isScheduledReport}
            {...MachineInfoProps}
            application_name={
              logged_in_user_client_id === client_id
                ? undefined
                : "dg-monitoring"
            }
          />
        )}
      />,
    );
  });
  return final_routes;
};
