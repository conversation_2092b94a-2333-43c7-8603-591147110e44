import _find from "lodash/find";
import { getFinalThingCat } from "../../packages/dg-monitoring-views/src/js/GenericTemplate/logic/getThingsCats";
import { isAurassure } from "../../packages/dg-monitoring-views/src/js/GenericTemplate/logic/isAurassure";
import { availableThingCategory, checkMenuAvailability } from "../routeHandler";
import {
  GetBasePathComponentProps,
  RouteMappingProps,
  ThingHasMapProps,
} from "./types";

export const getRoutesMapping = (props: RouteMappingProps) => {
  // const {
  //   dgSetType,
  //   fuelTankType,
  //   compressorType,
  //   fleetType,
  //   tankerType,
  //   solarPowerType,
  //   acEnergyType,
  //   tempHumidType,
  //   inverterType,
  //   processAnalyzerType,
  //   dcEnergyMeterType,
  //   electricalMachinesType,
  //   exhaustFanType,
  //   flowMeterMachinesType,
  //   complianceMachinesType,
  // } = availableThingCategory(props.thing_list?.things);
  let genericRoutes = [
    {
      component: "Settings",
      getPath: (path = []) => {
        return path;
      },
      path: ["/dg-monitoring/settings/:page_name", "/settings/:page_name"],
    },
    {
      component: "DatomsPolicy",
      getPath: (path = []) => {
        return path;
      },
      path: ["/dg-monitoring/privacy-policy", "/privacy-policy"],
    },
    {
      component: "TicketingSupport",
      getPath: (path = []) => {
        return path;
      },
      path: ["/dg-monitoring/tickets", "/tickets"],
    },
    {
      component: "DGDashboard",
      getPath: (path = []) => {
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: ["/dg-monitoring/dashboard", "/dashboard"],
    },
    {
      component: "SiteView",
      getPath: (path = []) => {
        // menuPages
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: ["/dg-monitoring/site-view", "/site-view"],
    },
    {
      component: "SiteDashboard",
      getPath: (path = []) => {
        // menuPages
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: ["/dg-monitoring/site-dashboard", "/site-dashboard"],
    },
    {
      component: "GenericAssetDashboard",
      getPath: (path = []) => {
        // menuPages
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/generic/asset-dashboard",
        "/asset-dashboard",
        "/dg-monitoring/asset-dashboard",
      ],
    },
    {
      component: "GenericRealTime",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/generic/real-time",
        "/real-time",
        "/dg-monitoring/real-time",
      ],
    },
    {
      component: "GenericDetailedView",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/generic/detailed-view",
        "/detailed-view",
        "/dg-monitoring/detailed-view",
      ],
    },
    {
      component: "QueryStringInUrlGeneric",
      getPath: (path = []) => {
        // menuPages
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/generic/map-view",
        "/dg-monitoring/generic/panel-view",
        "/map-view",
        "/panel-view",
        "/dg-monitoring/map-view",
        "/dg-monitoring/panel-view",
        "/generic/map-view",
      ],
    },
    {
      component: "GenericMapView",
      getPath: (path = []) => {
        // menuPages
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/generic/map-view",
        "/map-view",
        "/dg-monitoring/map-view",
        "/generic/map-view",
      ],
    },
    {
      component: "MapView",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/views/map-view", 
        "/views/map-view",

        "/dg-monitoring/views/map-view/list",
        "/views/map-view/list",
        "/dg-monitoring/views/map-view/list/:entityType/:id",
        "/views/map-view/list/:entityType/:id",
        "/dg-monitoring/views/map-view/:entityType/:id",
        "/views/map-view/:entityType/:id",
      ],
    },
    {
      component: "GenericPanelView",
      getPath: (path = []) => {
        // menuPages
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/generic/panel-view",
        "/panel-view",
        "/dg-monitoring/panel-view"
      ],
    },
    {
      component: "ListView",
      getPath: (path = []) => {
        // menuPages
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/generic/list-view",
        "/list-view",
        "/dg-monitoring/list-view",
        "/dg-monitoring/generic/list-view/assets",
        "/list-view/assets",
        "/dg-monitoring/list-view/assets",
        "/dg-monitoring/generic/list-view/sites",
        "/list-view/sites",
        "/dg-monitoring/list-view/sites"
      ],
    },
    {
      component: "ServiceModule",
      getPath: (path = []) => {
        // menuPages
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/work-orders",
        "/dg-monitoring/work-orders/:id/details",
        "/work-orders",
        "/work-orders/:id/details",
      ],
    },
    {
      component: "WorkFlow",
      getPath: (path = []) => {
        // menuPages
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/workflow/compliance",
        "/dg-monitoring/workflow/compliance/details",
        "/dg-monitoring/workflow/compliance/add",
        "/workflow/compliance",
        "/workflow/compliance/details",
        "/workflow/compliance/add",
        "/dg-monitoring/workflow/calibration",
        "/dg-monitoring/workflow/calibration/details",
        "/workflow/calibration",
        "/workflow/calibration/details",
        "/dg-monitoring/workflow/calibration/schedules",
        "/workflow/calibration/schedules",
      ],
    },
    {
      component: "DDS",
      getPath: (path = []) => {
        // menuPages
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: ["/dg-monitoring/dds", "/dds"],
    },
    {
      component: "UserFullNotification",
      getPath: (path = []) => {
        // menuPages
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: ["/dg-monitoring/user-notifications", "/user-notifications"],
    },
    {
      component: "ReportTemplates",
      getPath: (path = []) => {
        // menuPages
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: ["/dg-monitoring/reports/", "/reports/"],
    },
    {
      component: "CustomReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/custom-reports",
        "/reports/custom-reports",
      ],
    },
    {
      component: "HotspotReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/hotspot-report",
        "/reports/hotspot-report",
      ],
    },
    {
      component: "AurassureHourlyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/aurassure/hourly-report",
        "/reports/aurassure/hourly-report",
      ],
    },
    {
      component: "AurassureDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/aurassure/daily-report",
        "/reports/aurassure/daily-report",
      ],
    },
    {
      component: "AurassureMonthlyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/aurassure/monthly-report",
        "/reports/aurassure/monthly-report",
      ],
    },
    {
      component: "JindalMultiAssetReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/aaqms/multi-asset-report",
        "/reports/aaqms/multi-asset-report",
      ],
    },
    {
      component: "DataAvailabilityReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/data-availability-report",
        "/reports/data-availability-report",
      ],
    },
    {
      component: "DataAvailability",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/data-availability",
        "/reports/data-availability",
      ],
    },
    {
      component: "DgStatusReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/asset-status-report",
        "/reports/asset-status-report",
      ],
    },
    {
      component: "SummaryReports",
      getPath: (path = []) => {
        // not used
        return path;
      },
      path: [
        "/dg-monitoring/reports/summary-reports",
        "/reports/summary-reports",
      ],
    },
    {
      component: "YesterdayDailyFaultReport",
      getPath: (path = []) => {
        // not used
        return path;
      },
      path: [
        "/dg-monitoring/reports/yesterday-data-reports",
        "/reports/yesterday-data-reports",
      ],
    },
    {
      component: "DgDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/template-reports",
        "/reports/template-reports",
      ],
    },
    {
      component: "GasGensetDailyReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/gas-genset/daily-report",
        "/reports/gas-genset/daily-report",
      ],
    },
    {
      component: "GasGensetMultiAssetReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/gas-genset/multi-asset-reports",
        "/reports/gas-genset/multi-asset-reports",
      ],
    },
    {
      component: "CriticalReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/critical-trends-reports",
        "/reports/critical-trends-reports",
      ],
    },
    {
      component: "DgRunReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/dg-run-reports",
        "/reports/dg-run-reports",
      ],
    },
    {
      component: "GasGensetTripReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/reports/gas-genset/run-reports",
        "/dg-monitoring/reports/gas-genset/run-reports",
      ],
    },
    {
      component: "DGMultiAssetReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/dg-multi-assets-report",
        "/reports/dg-multi-assets-report",
      ],
    },
    {
      component: "DGSnapshotReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/dg-snapshot-report",
        "/reports/dg-snapshot-report",
      ],
    },
    {
      component: "DGFaultReport",
      getPath: (path = []) => {
        return path;
      },
      path: ["/dg-monitoring/reports/fault-reports", "/reports/fault-reports"],
    },
    {
      component: "GasGensetFaultReport",
      getPath: (path = []) => {
        return path;
      },
      path: ["reports/gas-genset/fault-reports", "/dg-monitoring/reports/gas-genset/fault-reports"],
    },
    {
      component: "ElevatorFaultReport",
      getPath: (path = []) => {
        return path;
      },
      path: ["/dg-monitoring/reports/elevator/fault-reports", "/reports/elevator/fault-reports"],
    },
    {
      component: "SolarPumpFaultReport",
      getPath: (path = []) => {
        return path;
      },
      path: ["/dg-monitoring/reports/solar-pump/fault-reports", "/reports/solar-pump/fault-reports"],
    },
    {
      component: "FuelFillDrainReport",
      getPath: (path = []) => {
        return parseInt(props.vendor_id) !== 1140 ? path : [];
      },
      path: [
        "/dg-monitoring/reports/fuel-fill-drain",
        "/reports/fuel-fill-drain",
      ],
    },
    {
      component: "LifetimeReports",
      getPath: (path = []) => {
        return parseInt(props.vendor_id) !== 1140 ? path : [];
      },
      path: [
        "/dg-monitoring/reports/lifetime-reports",
        "/reports/lifetime-reports",
      ],
    },
    {
      component: "ParameterReports",
      getPath: (path = []) => {
        return parseInt(props.vendor_id) !== 1140 ? path : [];
      },
      path: [
        "/dg-monitoring/reports/parameter-reports",
        "/reports/parameter-reports",
      ],
    },
    {
      component: "FuelTankDailyReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/fuel-tank/daily-reports",
        "/reports/fuel-tank/daily-reports",
      ],
    },
    {
      component: "FuelTankSnapShotReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/fuel-tank/snapshot-reports",
        "/reports/fuel-tank/snapshot-reports",
      ],
    },
    {
      component: "FuelTankFillReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/fuel-tank-fill",
        "/reports/fuel-tank-fill",
      ],
    },
    {
      component: "CompDailyReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/compressor/daily-reports",
        "/reports/compressor/daily-reports",
      ],
    },
    {
      component: "CompDgRunReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/compressor/run-reports",
        "/reports/compressor/run-reports",
      ],
    },
    {
      component: "CompFaultReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/compressor/fault-reports",
        "/reports/compressor/fault-reports",
      ],
    },
    {
      component: "CompressorMultiAssetReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/compressor/multi-asset-reports",
        "/reports/compressor/multi-asset-reports",
      ],
    },
    {
      component: "FleetDailyReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/fleet/daily-reports",
        "/reports/fleet/daily-reports",
      ],
    },
    {
      component: "FleetTripReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/fleet/trip-reports",
        "/reports/fleet/trip-reports",
      ],
    },
    {
      component: "FleetTripDetails",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/fleet/trip-reports/details",
        "/reports/fleet/trip-reports/details",
      ],
    },
    {
      component: "TankerTruckFuelTankerDailyReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/tanker-truck/daily-reports",
        "/reports/tanker-truck/daily-reports",
      ],
    },
    {
      component: "TankerTruckVehicleDailyReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/vehicle/daily-reports",
        "/reports/vehicle/daily-reports",
      ],
    },
    {
      component: "TankerTruckFuelFilledDispenseReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/fuel-fill-dispense",
        "/reports/fuel-fill-dispense",
      ],
    },
    {
      component: "TankerTruckTripReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/tanker-truck/trip-reports",
        "/reports/tanker-truck/trip-reports",
      ],
    },
    {
      component: "TankerTruckTripDetails",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/tanker-truck/trip-reports/details",
        "/reports/tanker-truck/trip-reports/details",
      ],
    },
    {
      component: "AlertSubscriptionThingsReport",
      getPath: (path = []) => {
        // revisit
        return path;
      },
      path: [
        "/dg-monitoring/reports/alert-delivery-summary-reports",
        "/reports/alert-delivery-summary-reports",
      ],
    },
    {
      component: "inverterDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/inverter/daily-reports",
        "/reports/inverter/daily-reports",
      ],
    },
    {
      component: "ProcessAnalyzerDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/process-analyzer/daily-reports",
        "/reports/process-analyzer/daily-reports",
      ],
    },
    {
      component: "ProcessAnalyzerFaultReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/process-analyzer/fault-reports",
        "/reports/process-analyzer/fault-reports",
      ],
    },
    {
      component: "SolarPowerDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/solar-power/daily-reports",
        "/reports/solar-power/daily-reports",
      ],
    },
    {
      component: "SolarPowerTripReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/solar-power/trip-reports",
        "/reports/solar-power/trip-reports",
      ],
    },
    {
      component: "SolarPowerMultiAssetReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/solar-power/multi-asset-reports",
        "/reports/solar-power/multi-asset-reports",
      ],
    },
    {
      component: "SolarPumpDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/solar-pump/daily-reports",
        "/reports/solar-pump/daily-reports",
      ],
    },
    {
      component: "SolarPumpTripReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/solar-pump/trip-reports",
        "/reports/solar-pump/trip-reports",
      ],
    },
    {
      component: "SolarPumpMultiAssetReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/solar-pump/multi-asset-reports",
        "/reports/solar-pump/multi-asset-reports",
      ],
    },
    {
      component: "AcEnergyDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/ac-energy-meter/daily-reports",
        "/reports/ac-energy-meter/daily-reports",
      ],
    },
    {
      component: "GridEnergyMeterDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/grid-energy-meter/daily-reports",
        "/reports/grid-energy-meter/daily-reports",
      ],
    },
    {
      component: "AcEnergyMultiAsserReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/ac-energy-meter/multi-asset-reports",
        "/reports/ac-energy-meter/multi-asset-reports",
      ],
    },
    {
      component: "GridEnergyMeterMultiAssetReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/grid-energy-meter/multi-asset-reports",
        "/reports/grid-energy-meter/multi-asset-reports",
      ],
    },
    {
      component: "AcElectricalMachinesDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/ac-electrical-machines/daily-reports",
        "/reports/ac-electrical-machines/daily-reports",
      ],
    },
    {
      component: "AcElectricalMachinesTripReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/ac-electrical-machines/trip-reports",
        "/reports/ac-electrical-machines/trip-reports",
      ],
    },
    {
      component: "AcElectricalMachinesMultiAsserReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/ac-electrical-machines/multi-asset-reports",
        "/reports/ac-electrical-machines/multi-asset-reports",
      ],
    },
    {
      component: "ExhaustFanDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/exhaust-fan/daily-reports",
        "/reports/exhaust-fan/daily-reports",
      ],
    },
    {
      component: "ExhaustFanTripReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/exhaust-fan/trip-reports",
        "/reports/exhaust-fan/trip-reports",
      ],
    },
    {
      component: "ExhaustFanMultiAsserReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/exhaust-fan/multi-asset-reports",
        "/reports/exhaust-fan/multi-asset-reports",
      ],
    },
    {
      component: "RiceMilletEnergyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/site-reports/rice-millet-energy-report",
        "/reports/site-reports/rice-millet-energy-report",
      ],
    },
    {
      component: "BatteryDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/battery/daily-reports",
        "/reports/battery/daily-reports",
      ],
    },
    {
      component: "BatteryMultiAssetReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/battery/multi-asset-reports",
        "/reports/battery/multi-asset-reports",
      ],
    },
    {
      component: "DcEnergyMeterDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/dc-energy-meter/daily-reports",
        "/reports/dc-energy-meter/daily-reports",
      ],
    },
    {
      component: "DcEnergyMeterMultiAssetReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/dc-energy-meter/multi-asset-reports",
        "/reports/dc-energy-meter/multi-asset-reports",
      ],
    },
    {
      component: "TempHumidDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/temp-humid/daily-reports",
        "/reports/temp-humid/daily-reports",
      ],
    },
    {
      component: "EnergyMeterAvailabilityReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/energy-meter/availability-reports",
        "/reports/energy-meter/availability-reports",
      ],
    },
    {
      component: "FlowMeterDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/flow-meter/daily-reports",
        "/reports/flow-meter/daily-reports",
      ],
    },
    {
      component: "FlowMeterHourlyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/flow-meter/hourly-reports",
        "/reports/flow-meter/hourly-reports",
      ],
    },
     {
      component: "FlowMeterMultiAssetReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/flow-meter/multi-asset-reports",
        "/reports/flow-meter/multi-asset-reports",
      ],
    },
    {
      component: "FlowMeterSiteConsumptionReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/flow-meter/site-consumption-reports",
        "/reports/flow-meter/site-consumption-reports",
      ],
    },
    {
      component: "SMSSummaryReport",
      getPath: (path = []) => {
        return !isAurassure(props.vendor_id) ? path : [];
      },
      path: [
        "/dg-monitoring/reports/violation-summary-report",
        "/reports/violation-summary-report",
      ],
    },
    {
      component: "PollutionParamRangeSummaryReport",
      getPath: (path = []) => {
        return !isAurassure(props.vendor_id) ? path : [];
      },
      path: [
        "/dg-monitoring/reports/pollution-monitoring/param-range-report",
        "/reports/pollution-monitoring/param-range-report",
      ],
    },
    {
      component: "GensetFuelTankSiteReport",
      getPath: (path = []) => {
        // revisit
        return path;
      },
      path: [
        "/dg-monitoring/reports/genset-fuel-tank-site-report",
        "/reports/genset-fuel-tank-site-report",
      ],
    },
    {
      component: "EnergySummaryReport",
      getPath: (path = []) => {
        // revisit
        return path;
      },
      path: [
        "/dg-monitoring/reports/energy-summary-report",
        "/reports/energy-summary-report",
      ],
    },
    {
      component: "SensorSummaryReport",
      getPath: (path = []) => {
        // revisit
        return path;
      },
      path: [
        "/dg-monitoring/reports/sensor-summary-report",
        "/reports/sensor-summary-report",
      ],
    },
    {
      component: "AssignedDevice",
      getPath: (path = []) => {
        // menuPages
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/devices",
        "/dg-monitoring/devices/assigned",
        "/dg-monitoring/devices/assigned/:device_id/debug",
        "/devices",
        "/devices/assigned",
        "/devices/assigned/:device_id/debug",
      ],
    },
    {
      component: "DeviceConfiguration",
      getPath: (path = []) => {
        // menuPages remove
        if (
          checkMenuAvailability(
            ["/dg-monitoring/devices", "/devices"],
            props.menuUrlArray,
          )
        ) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/devices/assigned/:station_id/configuration",
        // "/dg-monitoring/devices/unassigned/:station_id/configuration",
        "/devices/assigned/:station_id/configuration",
        // "/devices/unassigned/:station_id/configuration",
      ],
    },
    {
      component: "DeviceCommunication",
      getPath: (path = []) => {
        // menuPages remove
        if (
          checkMenuAvailability(
            ["/dg-monitoring/devices", "/devices"],
            props.menuUrlArray,
          )
        ) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/devices/assigned/:device_id/communication",
        // "/dg-monitoring/devices/unassigned/:device_id/communication",
        "/devices/assigned/:device_id/communication",
        // "/devices/unassigned/:device_id/communication",
      ],
    },
    {
      component: "DeviceHealth",
      getPath: (path = []) => {
        // menuPages remove
        if (
          checkMenuAvailability(
            ["/dg-monitoring/devices", "/devices"],
            props.menuUrlArray,
          )
        ) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/devices/assigned/:device_id/health",
        // "/dg-monitoring/devices/unassigned/:device_id/health",
        "/devices/assigned/:device_id/health",
        // "/devices/unassigned/:device_id/health",
      ],
    },
    {
      component: "DeviceCustomCommand",
      getPath: (path = []) => {
        // menuPages remove
        if (
          checkMenuAvailability(
            ["/dg-monitoring/devices", "/devices"],
            props.menuUrlArray,
          ) &&
          props.enabled_features?.includes("DeviceManagement:CustomCommand")
        ) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/devices/assigned/:device_id/custom-command",
        // "/dg-monitoring/devices/unassigned/:device_id/custom-command",
        "/devices/assigned/:device_id/custom-command",
        // "/devices/unassigned/:device_id/custom-command",
      ],
    },
    {
      component: "DeviceCalibration",
      getPath: (path = []) => {
        // menuPages remove
        if (
          checkMenuAvailability(
            ["/dg-monitoring/devices", "/devices"],
            props.menuUrlArray,
          )
        ) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/devices/assigned/:device_id/calibration",
        "/dg-monitoring/devices/assigned/:device_id/calibration/edit",
        "/dg-monitoring/devices/assigned/:device_id/calibration/add",
        // "/dg-monitoring/devices/unassigned/:device_id/calibration",
        // "/dg-monitoring/devices/unassigned/:device_id/calibration/edit",
        // "/dg-monitoring/devices/unassigned/:device_id/calibration/add",
        "/devices/assigned/:device_id/calibration",
        "/devices/assigned/:device_id/calibration/edit",
        "/devices/assigned/:device_id/calibration/add",
        // "/devices/unassigned/:device_id/calibration",
        // "/devices/unassigned/:device_id/calibration/edit",
        // "/devices/unassigned/:device_id/calibration/add",
      ],
    },
    {
      component: "UsersManagement",
      getPath: (path = []) => {
        // menuPages
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/user-management/users/view",
        "/dg-monitoring/user-management/users/add",
        "/dg-monitoring/user-management/users/:user_id/edit",
        "/dg-monitoring/user-management/users/:user_id/view",
        "/dg-monitoring/user-management/user-groups/view",
        "/dg-monitoring/user-management/user-groups/add",
        "/dg-monitoring/user-management/user-groups/:group_id/edit",
        "/dg-monitoring/user-management/roles/view",
        "/dg-monitoring/user-management/roles/add",
        "/dg-monitoring/user-management/roles/:role_id/edit",
        "/user-management/users/view",
        "/user-management/users/add",
        "/user-management/users/:user_id/edit",
        "/user-management/users/:user_id/view",
        "/user-management/user-groups/view",
        "/user-management/user-groups/add",
        "/user-management/user-groups/:group_id/edit",
        "/user-management/roles/view",
        "/user-management/roles/add",
        "/user-management/roles/:role_id/edit",
      ],
    },
    {
      component: "TerritoryPage",
      getPath: (path = [], props: any) => {
        if (
          props.enabled_features?.includes("UserManagement:Territory") &&
          props.getViewAccess(["TerritoryManagement:View"]) &&
          window.innerWidth >= 1080
        ) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/user-management/territory/view",
        "/user-management/territory/view",
      ],
    },
    {
      component: "ThingList",
      getPath: (path = []) => {
        // menuPages
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/things/",
        "/dg-monitoring/things/:thing_id/devices/:device_id/debug",
        "/things/",
        "/things/:thing_id/devices/:device_id/debug",
      ],
    },
    {
      component: "ThingAdd",
      getPath: (path = []) => {
        if (
          checkMenuAvailability(
            ["/things/", "/dg-monitoring/things/"],
            props.menuUrlArray,
          ) &&
          props.getViewAccess(["ThingManagement:Add"])
        ) {
          return path;
        }
        return [];
      },
      path: ["/dg-monitoring/things/add", "/things/add"],
    },
    {
      component: "ThingDetails",
      getPath: (path = []) => {
        // linked condition
        if (
          checkMenuAvailability(
            ["/things/", "/dg-monitoring/things/"],
            props.menuUrlArray,
          )
        ) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/customers/:customer_id/applications/:application_id/things/:thing_id/thing-details",
        "/customers/:customer_id/applications/:application_id/things/:thing_id/thing-details",
      ],
    },
    {
      component: "ThingConfigure",
      getPath: (path = []) => {
        if (
          checkMenuAvailability(
            ["/things/", "/dg-monitoring/things/"],
            props.menuUrlArray,
          ) &&
          props.getViewAccess(["ThingManagement:Edit"])
        ) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/customers/:customer_id/applications/:application_id/things/:thing_id/configuration/general",
        "/dg-monitoring/customers/:customer_id/applications/:application_id/things/:thing_id/configuration/configuration",
        "/dg-monitoring/customers/:customer_id/applications/:application_id/things/:thing_id/configuration/info",
        "/customers/:customer_id/applications/:application_id/things/:thing_id/configuration/general",
        "/customers/:customer_id/applications/:application_id/things/:thing_id/configuration/configuration",
        "/customers/:customer_id/applications/:application_id/things/:thing_id/configuration/info",
      ],
    },
    {
      component: "SiteConfiguration",
      getPath: (path = []) => {
        if (
          !checkMenuAvailability(
            ["/dg-monitoring/sites", "/sites"],
            props.menuUrlArray,
          )
        ) {
          return [];
        }
        const filteredRoutes = path.filter((pathUrl) => {
          if (pathUrl === "/dg-monitoring/sites/add") {
            return props.getViewAccess(["SiteManagement:Add"]);
          }
          if (
            pathUrl ===
            "/dg-monitoring/customers/:customer_id/sites/:site_id/configuration"
          ) {
            return props.getViewAccess(["SiteManagement:Edit"]);
          }
          return true;
        });
        return filteredRoutes;
      },
      path: [
        "/dg-monitoring/sites/add",
        "/dg-monitoring/customers/:customer_id/sites/:site_id/configuration",
        "/dg-monitoring/customers/:customer_id/sites/:site_id/details",
      ],
    },
    {
      component: "SiteList",
      getPath: (path = []) => {
        // menuPages
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: ["/dg-monitoring/sites", "/sites"],
    },
    {
      component: "RuleManagement",
      getPath: (path = []) => {
        // menuPages
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: ["/dg-monitoring/alerts", "/alerts"],
    },
    {
      component: "MorePage",
      getPath: (path = []) => {
        // menuPages
        if (window.innerWidth < 576) {
          return path;
        }
        return [];
      },
      path: ["/dg-monitoring/more", "/more"],
    },
    {
      component: "TripView",
      getPath: (path = []) => {
        // menuPages
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/generic/trip-view/:pageType",
        "/dg-monitoring/trip-view/:pageType",
        "/dg-monitoring/trip-view",
        "/dg-monitoring/generic/trip-view/:pageType/:trip_id/details",
        "/trip-view/:pageType",
        "/trip-view",
        "/trip-view/:pageType/:trip_id/details",
      ],
    },
    {
      component: "GravityReportDemo",
      getPath: (path = []) => {
        return path;
      },
      path: ["/dg-monitoring/gravity-report-demo", "/gravity-report-demo"],
    },
  ];
  if (props.logged_in_user_role_type === 6) {
    genericRoutes = [
      {
        component: "ServiceModule",
        getPath: (path = []) => {
          // menuPages
          if (checkMenuAvailability(path, props.menuUrlArray)) {
            return path;
          }
          return [];
        },
        path: [
          "/dg-monitoring/work-orders",
          "/dg-monitoring/work-orders/:id/details",
          "/work-orders",
          "/work-orders/:id/details",
        ],
      },
    ];
  }
  return genericRoutes;
};

export const getBasePathComponent = (props: GetBasePathComponentProps) => {
  const {
    client_id,
    enabled_features,
    thing_list,
    logged_in_user_role_type,
    vendor_id,
    head_side_object_data,
    user_id,
  } = props;

  let totalThings = thing_list;
  totalThings["things_categories"] = getFinalThingCat(
    isAurassure(parseInt(vendor_id)),
  );
  // let findThingHasMap = thingHasMap({ client_id, vendor_id, thing_list });
  const findThingHasMap =
    head_side_object_data?.header_component?.sider?.menu_items.some(
      (menuItem) => menuItem.key === "map-view",
    );

  let thingLenOne =
    totalThings?.things?.length === 1 &&
    !isAurassure(parseInt(vendor_id)) &&
    _find(totalThings?.things, (o: { category: number }) => {
      return ![71, 67, 74, 76, 77, 73, 18, 99].includes(o.category);
    });

  if (logged_in_user_role_type === 6) {
    return "ServiceModule";
  }  else if (logged_in_user_role_type === 11) {
    return window.innerWidth <= 576 ? "ReportTemplates" : "GenericPanelView";
  }  else if (thingLenOne) {
    return "GenericDetailedView";
  } else if (
    enabled_features.includes("AccessData:SiteDashboard") &&
    client_id === 1381
  ) {
    return "SiteDashboard";
  } else {
    if (findThingHasMap || window.innerWidth <= 576) {
      // "MapView" can be added here if needed
      return "GenericMapView";
    } else {
      return "GenericPanelView";
    }
  }
};

const thingHasMap = ({
  client_id,
  vendor_id,
  thing_list,
}: ThingHasMapProps) => {
  if (import.meta.env.VITE_DESKTOP) return false;
  const pollutionThingCat = isAurassure(parseInt(vendor_id))
    ? [21, 23, 44, 85]
    : [21, 22, 102, 23, 44, 85];
  const findThingHasMap =
    _find(thing_list?.things, function (o: { category: number }) {
      return (
        _find(thing_list.things_categories, { id: o.category })?.pages?.map &&
        !pollutionThingCat.includes(o.category)
      );
    }) && ![2156].includes(client_id);
  return findThingHasMap ? true : false;
};

export const non_exact_paths = [];
