export function getMenuUrlArray(head_side_object_data: {
  header_component: { sider: { menu_items: any[] } };
}) {
  const urlArray: any[] = [];
  try {
    head_side_object_data.header_component.sider.menu_items.forEach(
      (item: { submenu_items: any[]; url: any }) => {
        if (Array.isArray(item.submenu_items) && item.submenu_items.length) {
          item.submenu_items.forEach((subItem: { url: any }) => {
            if (subItem.url) {
              urlArray.push(trimUrl(subItem.url));
            }
          });
        } else if (item.url) {
          urlArray.push(trimUrl(item.url));
        }
      },
    );
  } catch (err) {}
  return urlArray;
}

export function checkMenuAvailability(pageRoutes: any[], menuUrlArray = []) {
  if (window.innerWidth <= 576) {
    return true;
  }
  // return (
  //   menuUrlArray.length &&
  //   pageRoutes.find((pageRoute) => {
  //     return menuUrlArray.includes(trimUrl(pageRoute));
  //   })
  // );
  return (
    menuUrlArray.length &&
    pageRoutes.some((pageRoute: string) => {
      if (!pageRoute.includes("/:")) {
        return menuUrlArray.includes(trimUrl(pageRoute));
      }
      const pageRouteSegments = trimUrl(pageRoute).split("/");
      return menuUrlArray.some((menuUrl) => {
        const menuUrlSegments = menuUrl.split("/");
        if (pageRouteSegments.length !== menuUrlSegments.length) {
          return false;
        }
        for (let i = 0; i < pageRouteSegments.length; i++) {
          if (pageRouteSegments[i].startsWith(":")) {
            continue;
          }
          if (pageRouteSegments[i] !== menuUrlSegments[i]) {
            return false;
          }
        }
        return true;
      });
    })
  );
}

function trimUrl(path: string) {
  let finalPath = path.includes("?") ? path.split("?")[0] : path;
  return finalPath.endsWith("/") ? finalPath.slice(0, -1) : finalPath;
}

export function availableThingCategory(thing_list = []) {
  const types = {
    dgSetType: null,
    fuelTankType: null,
    compressorType: null,
    fleetType: null,
    tankerType: null,
    energyMeterType: null,
    solarPowerType: null,
    acEnergyType: null,
    tempHumidType: null,
    inverterType: null,
    processAnalyzerType: null,
    dcEnergyMeterType: null,
    electricalMachinesType: null,
    flowMeterMachinesType: null,
    complianceMachinesType: null,
    chillerType: null,
    cemsType: null,
    aaqmsType: null,
    eqmsType: null,
    ipCamType: null,
    digitalDisplayType: null,
    coldStorageType: null,
    exhaustFanType: null,
  };

  for (let thing of thing_list) {
    switch (thing.category) {
      case 18:
      case 96: //for gas genset
        types.dgSetType = true;
        break;
      case 71:
        types.fuelTankType = true;
        break;
      case 73:
        types.compressorType = true;
        break;
      case 67:
      case 76:
        types.fleetType = true;
        break;
      case 74:
        types.tankerType = true;
        break;
      case 79:
        types.acEnergyType = true;
        break;
      case 91:
        types.solarPowerType = true;
        break;
      case 90:
        types.tempHumidType = true;
        break;
      case 93:
        types.inverterType = true;
        break;
      case 94:
        types.processAnalyzerType = true;
        break;
      case 99:
        types.exhaustFanType = true;
        break;
      case 77:
        types.dcEnergyMeterType = true;
        break;
      case 78:
        types.electricalMachinesType = true;
        break;
      case 86:
        types.flowMeterMachinesType = true;
        break;
      case 21:
      case 22:
      case 23:
      case 44:
      case 85:
        types.complianceMachinesType = types.complianceMachinesType || true;
        break;
      case 83:
        types.chillerType = true;
        break;
      case 45:
        types.coldStorageType = true;
        break;
    }
    if (Object.values(types).every(Boolean)) {
      break;
    }
  }
  //modify the code of this function to make it more readable and in single loop
  return types;
}
