export const getRoutesMapping = (props: { application_id: number }) => {
  return props.application_id === 12 ? datomsx_routes : iot_platform_routes;
};
export function getBasePathComponent(props: {
  logged_in_user_role_type: number;
  enabled_features: string[];
  application_id: number;
  user_id: number;
  viewDataAccess: () => boolean;
}) {
  if (
    props.logged_in_user_role_type === 7 &&
    props.enabled_features.includes("ThingsRental:ThingsRental")
  ) {
    return "OrdersList";
  }
  if (props.application_id === 12) {
    if (props.logged_in_user_role_type === 1) {
      if (window.innerWidth < 576) {
        return "DashboardClubbed";
      } else {
        return "Dashboard";
      }
    } else if (props.user_id === 10169) {
      return "ThingList";
    } else {
      return "CustomerManagement";
    }
  }
  if (props.application_id === 17) {
    if (
      (props.logged_in_user_role_type === 1 ||
        props.enabled_features?.includes("ThingsRental:ThingsRental")) &&
      changeIotHomePath(
        props.enabled_features,
        props.viewDataAccess,
        props.logged_in_user_role_type,
      )
    ) {
      if (window.innerWidth < 576) {
        return props.logged_in_user_role_type === 1 ||
          props.enabled_features.includes("ThingsRental:ThingsRental")
          ? "DashboardClubbed"
          : "CustomerManagement";
      } else {
        if (props.enabled_features.includes("ThingsRental:ThingsRental")) {
          return "RentalDashboard";
        } else if (props.enabled_features.includes("Dashboard:Dashboard")) {
          return "Dashboard";
        } else {
          return "CustomerManagement";
        }
      }
    } else {
      return "CustomerManagement";
    }
  }
}

function changeIotHomePath(
  enabled_features: string | string[],
  viewDataAccess: { (): boolean; (): any },
  logged_in_user_role_type: number,
) {
  let dashboard_enabled = false;
  if (
    (enabled_features &&
      enabled_features.includes("ThingsRental:ThingsRental")) ||
    (enabled_features.includes("Dashboard:Dashboard") &&
      logged_in_user_role_type === 1) ||
    viewDataAccess()
  ) {
    dashboard_enabled = true;
  }
  return dashboard_enabled;
}
const datomsx_routes = [
  {
    component: "Settings",
    getPath: (path = []) => {
      return path;
    },
    path: ["/datoms-x/settings/:page_name"],
  },
  {
    component: "DatomsPolicy",
    getPath: (path = []) => {
      return path;
    },
    path: ["/datoms-x/privacy-policy"],
  },
  {
    component: "NewReportTemplates",
    getPath: (path = []) => {
      return path;
    },
    path: ["/datoms-x/reports", "/datoms-x/reports/:page_name"],
  },
  {
    component: "FuelDeliveryReport",
    getPath: (path = []) => {
      return path;
    },
    path: ["/datoms-x/partner-reports/fuel-delivery-report"],
  },
  {
    component: "DgStatusReport",
    getPath: (path = []) => {
      return path;
    },
    path: ["/datoms-x/partner-reports/asset-status-report"],
  },
  {
    component: "DataAvailabilityReport",
    getPath: (path = []) => {
      return path;
    },
    path: ["/datoms-x/partner-reports/data-availability-report"],
  },
  {
    component: "DeviceHealthReport",
    getPath: (path = []) => {
      return path;
    },
    path: ["/datoms-x/partner-reports/device-health-report"],
  },
  {
    component: "Dashboard",
    getPath: (path = []) => {
      return path;
    },
    path: ["/datoms-x/dashboard"],
  },
  {
    component: "DashboardClubbed",
    getPath: (path = []) => {
      if (window.innerWidth > 576) {
        return [];
      }
      return path;
    },
    path: [
      "/datoms-x/iot-dashboard",
      "/datoms-x/rent-dashboard",
      "/datoms-x/asset-dashboard",
    ],
  },
  {
    component: "UserFullNotification",
    getPath: (path = []) => {
      return path;
    },
    path: ["/datoms-x/user-notifications"],
  },
  {
    component: "RemoteCalibration",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/datoms-x/workflow/calibration",
      "/datoms-x/workflow/calibration/details",
    ],
  },
  {
    component: "FirmwareManagement",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/datoms-x/firmware-management",
      "/datoms-x/firmware-management/:device_type",
      "/datoms-x/firmware-management/:device_type/:version/bug",
      "/datoms-x/firmware-management/:device_type/:version/release-log",
      "/datoms-x/firmware-management/:device_type/:version/dev-log",
      "/datoms-x/firmware-management/:device_type/upload",
    ],
  },
  {
    component: "CustomerManagement",
    getPath: (
      path = [],
      props: { getViewAccess: (arg0: string[]) => unknown },
    ) => {
      const filteredRoutes = path.filter((pathUrl) => {
        if (["/datoms-x/customer-management/contacts/new"].includes(pathUrl)) {
          return props.getViewAccess(["IndustryManagement:Add"]);
        }
        if (
          [
            "/datoms-x/customer-management/:customer_id/applications/edit",
          ].includes(pathUrl)
        ) {
          return props.getViewAccess(["IndustryManagement:Edit"]);
        }
        return true;
      });
      return filteredRoutes;
    },
    path: [
      "/datoms-x/customer-management",
      "/datoms-x/customer-management/contacts",
      "/datoms-x/customer-management/contacts/edit",
      "/datoms-x/customer-management/contacts/new",
      "/datoms-x/customer-management/customer-group/add",
      "/datoms-x/customer-management/contacts/view",
      "/datoms-x/customer-management/applications",
      "/datoms-x/customer-management/customer-group/:customer_id/details/view",
      "/datoms-x/customer-management/customer-group/:customer_id/details/edit",
      "/datoms-x/customer-management/:customer_id/details/edit",
      "/datoms-x/customer-management/:customer_id/details/address/edit",
      "/datoms-x/customer-management/:customer_id/details/contact/edit",
      "/datoms-x/customer-management/:customer_id/details/other/edit",
      "/datoms-x/customer-management/:customer_id/details",
      "/datoms-x/customer-management/:customer_id/details/view",
      "/datoms-x/customer-management/applications",
      "/datoms-x/customer-management/:customer_id/applications/view",
      "/datoms-x/customer-management/:customer_id/applications/edit",
      "/datoms-x/customer-management/applications/new",
      "/datoms-x/customer-management/applications/view",
      "/datoms-x/customer-management/devices",
      "/datoms-x/customer-management/devices/view",
      "/datoms-x/customer-management/alerts",
      "/datoms-x/customer-management/alerts/view",
      "/datoms-x/customer-management/:customer_id/devices",
      "/datoms-x/customer-management/:customer_id/devices/view",
      "/datoms-x/customer-management/:customer_id/devices/edit",
      "/datoms-x/customer-management/:customer_id/devices",
      "/datoms-x/customer-management/:customer_id/alerts",
      "/datoms-x/customer-management/:customer_id/roles",
      "/datoms-x/customer-management/:customer_id/roles/view",
      "/datoms-x/customer-management/:customer_id/roles/add",
      "/datoms-x/customer-management/:customer_id/roles/:role_id/edit",
      "/datoms-x/customer-management/:customer_id/roles/:role_id/delete",
      "/datoms-x/customer-management/:customer_id/users",
      "/datoms-x/customer-management/:customer_id/users/edit",
      "/datoms-x/customer-management/:customer_id/users/view",
      "/datoms-x/customer-management/:customer_id/applications/:app_id/users/view",
      "/datoms-x/customer-management/:customer_id/applications/:app_id/users/:user_id/edit",
      "/datoms-x/customer-management/:customer_id/applications/:app_id/users/add",
      "/datoms-x/customer-management/:customer_id/applications/:app_id/roles/view",
      "/datoms-x/customer-management/:customer_id/applications/:app_id/roles/:role_id/edit",
      "/datoms-x/customer-management/:customer_id/applications/:app_id/roles/add",
      "/datoms-x/customer-management/:customer_id/users/add",
      "/datoms-x/customer-management/:customer_id/users/:user_id/edit",
      "/datoms-x/customer-management/:customer_id/users/:user_id/delete",
      "/datoms-x/customer-management/:customer_id/user-groups/:group_id/delete",
      "/datoms-x/customer-management/:customer_id/applications/:app_id/user-groups/view",
      "/datoms-x/customer-management/:customer_id/applications/:app_id/user-groups/add/",
      "/datoms-x/customer-management/:customer_id/applications/:app_id/user-groups/:group_id/edit",
      "/datoms-x/customer-management/:customer_id/alerts/application/",
      "/datoms-x/customer-management/:customer_id/alerts/application/:application_id",
      "/datoms-x/customer-management/:customer_id/alerts/application/:application_id/category/:category_id",
      "/datoms-x/customer-management/:customer_id/alerts/application/:application_id/category/:category_id/add",
      "/datoms-x/customer-management/:customer_id/alerts/application/category/:category_id/:application_id/edit/:template_id",
      "/datoms-x/customer-management/:customer_id/applications/:application_id/things/list",
      "/datoms-x/customer-management/:customer_id/applications/:application_id/things/add",
      "/datoms-x/customer-management/:customer_id/applications/:application_id/things/:thing_id/configuration/general",
      "/datoms-x/customer-management/:customer_id/applications/:application_id/things/:thing_id/configuration/configuration",
      "/datoms-x/customer-management/:customer_id/applications/:application_id/things/:thing_id/configuration/info",
      "/datoms-x/customer-management/:customer_id/things/:thing_id/devices/:device_id/debug",
      "/datoms-x/customer-management/:customer_id/applications/:application_id/devices/list",
      "/datoms-x/customer-management/:customer_id/applications/:application_id/devices/:device_id/calibration",
      "/datoms-x/customer-management/:customer_id/applications/:application_id/devices/:device_id/calibration/add",
      "/datoms-x/customer-management/:customer_id/applications/:application_id/devices/:device_id/calibration/edit",
      "/datoms-x/customer-management/:customer_id/applications/:application_id/devices/:device_id/configuration",
      "/datoms-x/customer-management/:customer_id/applications/:application_id/devices/:device_id/custom-command",
      "/datoms-x/customer-management/:customer_id/applications/:application_id/devices/:device_id/health",
      "/datoms-x/customer-management/:customer_id/applications/:application_id/devices/:device_id/communication",
      "/datoms-x/customer-management/:customer_id/change-logs",
      "/datoms-x/customer-management/:customer_id/billing-management",
      "/datoms-x/customer-management/:customer_id/api-credentials",
      "/datoms-x/customer-management/:customer_id/subscription-management",
      "/datoms-x/customer-management/roles",
      "/datoms-x/customer-management/devices",
      "/datoms-x/customer-management/devices/add",
      "/datoms-x/customer-management/devices/edit",
      "/datoms-x/customer-management/roles/add",
      "/datoms-x/customer-management/roles/:role_id/edit",
      "/datoms-x/customer-management/roles/:role_id/delete",
      "/datoms-x/customer-management/users",
      "/datoms-x/customer-management/users/add",
      "/datoms-x/customer-management/users/view",
      "/datoms-x/customer-management/users/:user_id/edit",
      "/datoms-x/customer-management/users/:user_id/delete",
      "/datoms-x/customer-management/users/:user_id/set-status",
      "/datoms-x/customer-management/user-groups/view",
      "/datoms-x/customer-management/user-groups/add",
      "/datoms-x/customer-management/user-groups/:group_id/edit",
      "/datoms-x/customer-management/user-groups/:group_id/delete",
      "/datoms-x/customer-management/:customer_id/alerts",
      "/datoms-x/customer-management/:customer_id/alerts/application/",
      "/datoms-x/customer-management/alerts/application/:application_id",
      "/datoms-x/customer-management/:customer_id/alerts/application/:application_id/category/:category_id",
      "/datoms-x/customer-management/:customer_id/alerts/application/:application_id/category/:category_id/add",
      "/datoms-x/customer-management/:customer_id/alerts/application/:application_id/category/:category_id/edit/:template_id",
      "/datoms-x/customer-management/:customer_id/alerts-management",
      "/datoms-x/customer-management/:customer_id/software-license",
    ],
  },
  {
    component: "AssignedDevice",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/datoms-x/customer-management/:customer_id/applications/:application_id/devices/:device_id/debug",
      "/datoms-x/devices",
      "/datoms-x/devices/assigned",
      "/datoms-x/devices/assigned/:device_id/debug",
    ],
  },
  {
    component: "DeviceDebug",
    getPath: (path = []) => {
      return path;
    },
    path: ["/datoms-x/devices/assigned/:device_id/device-debug"],
  },
  {
    component: "TerritoryPage",
    getPath: (path = [], props: { getViewAccess: (arg0: string[]) => any }) => {
      if (
        props.getViewAccess(["TerritoryManagement:View"]) &&
        window.innerWidth >= 1080
      ) {
        return path;
      }
      return [];
    },
    path: [
      "/datoms-x/customer-management/:customer_id/territory/view",
      "/datoms-x/user-management/territory/view",
    ],
  },
  {
    component: "UnassignedDevice",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/datoms-x/devices/unassigned",
      "/datoms-x/devices/unassigned/add",
      "/datoms-x/devices/unassigned/:device_id/edit",
      "/datoms-x/devices/unassigned/:device_id/debug",
    ],
  },
  {
    component: "DeviceConfiguration",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/datoms-x/devices/assigned/:station_id/configuration",
      "/datoms-x/devices/unassigned/:station_id/configuration",
    ],
  },
  {
    component: "DeviceCommunication",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/datoms-x/devices/assigned/:device_id/communication",
      "/datoms-x/devices/unassigned/:device_id/communication",
    ],
  },
  {
    component: "DeviceHealth",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/datoms-x/devices/assigned/:device_id/health",
      "/datoms-x/devices/unassigned/:device_id/health",
    ],
  },
  {
    component: "DeviceCustomCommand",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/datoms-x/devices/assigned/:device_id/custom-command",
      "/datoms-x/devices/unassigned/:device_id/custom-command",
    ],
  },
  {
    component: "DeviceCalibration",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/datoms-x/devices/assigned/:device_id/calibration",
      "/datoms-x/devices/assigned/:device_id/calibration/edit",
      "/datoms-x/devices/assigned/:device_id/calibration/add",
      "/datoms-x/devices/unassigned/:device_id/calibration",
      "/datoms-x/devices/unassigned/:device_id/calibration/edit",
      "/datoms-x/devices/unassigned/:device_id/calibration/add",
    ],
  },
  {
    component: "ThingAdd",
    getPath: (path = [], props: { getViewAccess: (arg0: string[]) => any }) => {
      if (props.getViewAccess(["ThingManagement:Add"])) {
        return path;
      }
      return [];
    },
    path: [
      "/datoms-x/things/add",
      "/datoms-x/customer/:customer_id/things/add",
    ],
  },
  {
    component: "ThingConfigure",
    getPath: (path = [], props: { getViewAccess: (arg0: string[]) => any }) => {
      if (props.getViewAccess(["ThingManagement:Edit"])) {
        return path;
      }
      return [];
    },
    path: [
      "/datoms-x/customers/:customer_id/applications/:application_id/things/:thing_id/configuration/general",
      "/datoms-x/customers/:customer_id/applications/:application_id/things/:thing_id/configuration/configuration",
      "/datoms-x/customers/:customer_id/applications/:application_id/things/:thing_id/configuration/info",
    ],
  },
  {
    component: "ThingList",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/datoms-x/things/",
      "/datoms-x/things/:asset_id/customers/:customer_id/asset-data",
      "/datoms-x/things/:thing_id/devices/:device_id/debug",
    ],
  },
  {
    component: "DataPushLogs",
    getPath: (path = []) => {
      return path;
    },
    path: ["/datoms-x/things/:asset_id/customers/:customer_id/data-push-logs"],
  },
  {
    component: "SiteConfiguration",
    getPath: (
      path = [],
      props: { getViewAccess: (arg0: string[]) => unknown },
    ) => {
      const filteredRoutes = path.filter((pathUrl) => {
        if (
          pathUrl === "/datoms-x/customers/:customer_id/sites/:site_id/details"
        ) {
          return props.getViewAccess(["SiteManagement:View"]);
        }
        if (pathUrl === "/datoms-x/sites/add") {
          return props.getViewAccess(["SiteManagement:Add"]);
        }
        if (
          pathUrl ===
          "/datoms-x/customers/:customer_id/sites/:site_id/configuration"
        ) {
          return props.getViewAccess(["SiteManagement:Edit"]);
        }
        return true;
      });
      return filteredRoutes;
    },
    path: [
      "/datoms-x/sites/add",
      "/datoms-x/customers/:customer_id/sites/:site_id/configuration",
      "/datoms-x/customers/:customer_id/sites/:site_id/details",
    ],
  },
  {
    component: "SiteList",
    getPath: (path = [], props: { getViewAccess: (arg0: string[]) => any }) => {
      if (props.getViewAccess(["SiteManagement:View"])) {
        return path;
      }
      return [];
    },
    path: ["/datoms-x/sites"],
  },
  {
    component: "UsersManagement",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/datoms-x/user-management/users/view",
      "/datoms-x/user-management/users/:user_id/view",
      "/datoms-x/user-management/users/add",
      "/datoms-x/user-management/users/:user_id/edit",
      "/datoms-x/user-management/user-groups/view",
      "/datoms-x/user-management/user-groups/add",
      "/datoms-x/user-management/user-groups/:group_id/edit",
      "/datoms-x/user-management/roles/view",
      "/datoms-x/user-management/roles/add",
      "/datoms-x/user-management/roles/:role_id/edit",
    ],
  },
  {
    component: "ServiceModule",
    getPath: (path = []) => {
      return path;
    },
    path: ["/datoms-x/work-orders", "/datoms-x/work-orders/:id/details"],
  },
  {
    component: "ChangeLogView",
    getPath: (path = []) => {
      return path;
    },
    path: ["/datoms-x/change-logs"],
  },
  {
    component: "SubscriptionList",
    getPath: (path = [], props: { getViewAccess: (arg0: string[]) => any }) => {
      if (
        props.getViewAccess([
          "SubscriptionManagement:SubscriptionManagement",
          "SubscriptionManagement:View",
          "SubscriptionTemplateManagement:SubscriptionTemplateManagement",
          "SubscriptionTemplateManagement:View",
        ])
      ) {
        return path;
      }
      return [];
    },
    path: ["/datoms-x/subscriptions"],
  },
  {
    component: "DgInIotPages",
    getPath: (path = []) => {
      return path;
    },
    path: ["/datoms-x/dg-monitoring/:page_name"],
  },
  {
    component: "MorePage",
    getPath: (path = []) => {
      if (window.innerWidth <= 576) {
        return path;
      }
      return [];
    },
    path: ["/datoms-x/more"],
  },
  {
    component: "ThingQuickAdd",
    getPath: (path = [], props: { getViewAccess: (arg0: string[]) => any }) => {
      if (props.getViewAccess(["ThingManagement:Add"])) {
        return path;
      }
      return [];
    },
    path: ["/datoms-x/things/quick-add"],
  },
  {
    component: "ThingDetails",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/datoms-x/customers/:customer_id/applications/:application_id/things/:thing_id/thing-details",
    ],
  },
  {
    component: "TripView",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/datoms-x/trip-view/:pageType",
      "/datoms-x/trip-view/:pageType/:trip_id/details",
    ],
  },
  {
    component: "AllMapViews",
    getPath: (path = []) => {
      return path;
    },
    path: ["/datoms-x/map-view"],
  },
  {
    component: "AssetDashboard",
    getPath: (path = []) => {
      if (window.innerWidth < 576) {
        return [];
      }
      return path;
    },
    path: ["/datoms-x/asset-dashboard"],
  },
  {
    component: "TicketingSupport",
    getPath: (path = []) => {
      return path;
    },
    path: ["/datoms-x/tickets", "/tickets"],
  },
  {
    component: "MapView",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/datoms-x/views/map-view",
      "/views/map-view",
      "/datoms-x/views/map-view/list",
      "/views/map-view/list",
      "/datoms-x/views/map-view/:entityType/:id",
      "/views/map-view/:entityType/:id",
      "/datoms-x/views/map-view/list/:entityType/:id",
      "/views/map-view/list/:entityType/:id",
    ],
  },
  {
    component: "ProductInventory",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/datoms-x/hardware-production/",
      "/datoms-x/hardware-production/production-inventory",
      "/datoms-x/hardware-production/production-inventory/circuits",
      "/datoms-x/hardware-production/production-inventory/composites",
      "/datoms-x/hardware-production/production-inventory/products",
      "/datoms-x/hardware-production/production-inventory/sensors",
      "/datoms-x/hardware-production/production-inventory/circuits/test/:type_id/:ent_type_id/:ent_id",
      "/datoms-x/hardware-production/production-inventory/composites/test/:type_id/:ent_type_id/:ent_id",
      "/datoms-x/hardware-production/production-inventory/products/test/:type_id/:ent_type_id/:ent_id",
      "/datoms-x/hardware-production/production-inventory/circuits/log/:type_id/:ent_id",
      "/datoms-x/hardware-production/production-inventory/composites/log/:type_id/:ent_id",
      "/datoms-x/hardware-production/production-inventory/products/log/:type_id/:ent_id",
      "/datoms-x/hardware-production/production-inventory/composites/details/:type_id/:ent_id",
      "/datoms-x/hardware-production/production-inventory/products/details/:type_id/:ent_id",
      "/datoms-x/hardware-production/production-inventory/sensors/details/:type_id/:ent_id",
    ],
  },
  {
    component: "ParameterList",
    getPath: (path = [], props: { getViewAccess: (arg0: string[]) => any }) => {
      if (props.getViewAccess(["AccessData:ParameterView"])) {
        return path;
      }
      return [];
    },
    path: ["/datoms-x/parameter-list", "/parameter-list"],
  },
  {
    component: "DeviceList",
    getPath: (path = []) => {
      return path;
    },
    path: ["/datoms-x/devices/list", "/devices/list"],
  },
  {
    component: "AssetList",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/datoms-x/assets/list",
      "/assets/list",
      "/datoms-x/assets/:asset_id/customers/:customer_id/asset-data",
    ],
  },
];
const iot_platform_routes = [
  {
    component: "AssetList",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/iot-platform/assets/list",
      "/assets/list",
      "/iot-platform/assets/:asset_id/customers/:customer_id/asset-data",
    ],
  },
  {
    component: "Settings",
    getPath: (path = []) => {
      return path;
    },
    path: ["/iot-platform/settings/:page_name"],
  },
  {
    component: "DatomsPolicy",
    getPath: (path = []) => {
      return path;
    },
    path: ["/iot-platform/privacy-policy"],
  },
  {
    component: "NewReportTemplates",
    getPath: (path = [], props: { client_id: number }) => {
      if (parseInt(props.client_id) !== 392) {
        return path;
      }
      return [];
    },
    path: ["/iot-platform/reports", "/iot-platform/reports/:page_name"],
  },
  {
    component: "AlertsSubscriptionCustomers",
    getPath: (path = [], props: { client_id: number }) => {
      if (parseInt(props.client_id) !== 392) {
        return path;
      }
      return [];
    },
    path: ["/iot-platform/partner-reports/alert-delivery-report"],
  },
  {
    component: "MachineInforeport",
    getPath: (
      path = [],
      props: { client_id: number; customer_type: number[] },
    ) => {
      if (
        parseInt(props.client_id) !== 392 &&
        props.customer_type &&
        Array.isArray(props.customer_type) &&
        props.customer_type.indexOf(4) > -1
      ) {
        return path;
      }
      return [];
    },
    path: ["/iot-platform/partner-reports/machine-info-report"],
  },
  {
    component: "DgStatusReport",
    getPath: (path = [], props: { client_id: number }) => {
      if (parseInt(props.client_id) !== 392) {
        return path;
      }
      return [];
    },
    path: ["/iot-platform/partner-reports/asset-status-report"],
  },
  {
    component: "DataAvailabilityReport",
    getPath: (path = [], props: { enabled_features: string | string[] }) => {
      if (props.enabled_features?.includes("Reports:DataAvailability")) {
        return path;
      }
      return [];
    },
    path: ["/iot-platform/partner-reports/data-availability-report"],
  },
  {
    component: "CustomerManagement",
    getPath: (
      path = [],
      props: { getViewAccess: (arg0: string[], arg1: boolean) => unknown },
    ) => {
      const filteredRoutes = path.filter((pathUrl) => {
        if (
          ["/iot-platform/customer-management/contacts/new"].includes(pathUrl)
        ) {
          return props.getViewAccess(["IndustryManagement:Add"], true);
        }
        if (
          [
            "/iot-platform/customer-management/:customer_id/applications/edit",
          ].includes(pathUrl)
        ) {
          return props.getViewAccess(["IndustryManagement:Edit"], true);
        }
        return true;
      });
      return filteredRoutes;
    },
    path: [
      "/iot-platform/customer-management",
      "/iot-platform/customer-management/contacts",
      "/iot-platform/customer-management/contacts/edit",
      "/iot-platform/customer-management/contacts/new",
      "/iot-platform/customer-management/customer-group/add",
      "/iot-platform/customer-management/contacts/view",
      "/iot-platform/customer-management/applications",
      "/iot-platform/customer-management/customer-group/:customer_id/details/view",
      "/iot-platform/customer-management/customer-group/:customer_id/details/edit",
      "/iot-platform/customer-management/:customer_id/details/edit",
      "/iot-platform/customer-management/:customer_id/details/address/edit",
      "/iot-platform/customer-management/:customer_id/details/contact/edit",
      "/iot-platform/customer-management/:customer_id/details/other/edit",
      "/iot-platform/customer-management/:customer_id/details",
      "/iot-platform/customer-management/:customer_id/details/view",
      "/iot-platform/customer-management/applications",
      "/iot-platform/customer-management/:customer_id/applications/view",
      "/iot-platform/customer-management/:customer_id/applications/edit",
      "/iot-platform/customer-management/applications/new",
      "/iot-platform/customer-management/applications/view",
      "/iot-platform/customer-management/devices",
      "/iot-platform/customer-management/devices/view",
      "/iot-platform/customer-management/alerts",
      "/iot-platform/customer-management/alerts/view",
      "/iot-platform/customer-management/:customer_id/devices",
      "/iot-platform/customer-management/:customer_id/devices/view",
      "/iot-platform/customer-management/:customer_id/devices/edit",
      "/iot-platform/customer-management/:customer_id/devices",
      "/iot-platform/customer-management/:customer_id/alerts",
      "/iot-platform/customer-management/:customer_id/roles",
      "/iot-platform/customer-management/:customer_id/roles/view",
      "/iot-platform/customer-management/:customer_id/roles/add",
      "/iot-platform/customer-management/:customer_id/roles/:role_id/edit",
      "/iot-platform/customer-management/:customer_id/roles/:role_id/delete",
      "/iot-platform/customer-management/:customer_id/users",
      "/iot-platform/customer-management/:customer_id/users/edit",
      "/iot-platform/customer-management/:customer_id/users/view",
      "/iot-platform/customer-management/:customer_id/alerts",
      "/iot-platform/customer-management/:customer_id/alerts/application/:application_id",
      "/iot-platform/customer-management/:customer_id/alerts/application/:application_id/category/:category_id",
      "/iot-platform/customer-management/:customer_id/applications/:application_id/things/add",
      "/iot-platform/customer-management/:customer_id/applications/:application_id/things/:thing_id/configuration/general",
      "/iot-platform/customer-management/:customer_id/applications/:application_id/things/:thing_id/configuration/configuration",
      "/iot-platform/customer-management/:customer_id/applications/:application_id/things/:thing_id/configuration/info",
      "/iot-platform/customer-management/:customer_id/alerts/application/:application_id/category/:category_id/add",
      "/iot-platform/customer-management/:customer_id/alerts/application/:application_id/category/:category_id/edit/:template_id",
      "/iot-platform/customer-management/:customer_id/alerts-management",
      "/iot-platform/customer-management/:customer_id/alerts-management/users/:user_id/things/:thing_id/alert-rules",
      "/iot-platform/customer-management/:customer_id/applications/:app_id/users/view",
      "/iot-platform/customer-management/:customer_id/applications/:app_id/users/:user_id/edit",
      "/iot-platform/customer-management/:customer_id/applications/:app_id/users/add",
      "/iot-platform/customer-management/:customer_id/applications/:app_id/roles/view",
      "/iot-platform/customer-management/:customer_id/applications/:app_id/roles/:role_id/edit",
      "/iot-platform/customer-management/:customer_id/applications/:app_id/roles/add",
      "/iot-platform/customer-management/:customer_id/users/add",
      "/iot-platform/customer-management/:customer_id/users/:user_id/edit",
      "/iot-platform/customer-management/:customer_id/users/:user_id/delete",
      "/iot-platform/customer-management/:customer_id/user-groups/:group_id/delete",
      "/iot-platform/customer-management/:customer_id/applications/:app_id/user-groups/view",
      "/iot-platform/customer-management/:customer_id/applications/:app_id/user-groups/add/",
      "/iot-platform/customer-management/:customer_id/applications/:app_id/user-groups/:group_id/edit",
      "/iot-platform/customer-management/:customer_id/applications/:application_id/things/list",
      "/iot-platform/customer-management/:customer_id/applications/:application_id/things/:thing_id/configuration/general",
      "/iot-platform/customer-management/:customer_id/applications/:application_id/things/:thing_id/configuration/configuration",
      "/iot-platform/customer-management/:customer_id/applications/:application_id/things/:thing_id/configuration/info",
      "/iot-platform/customer-management/:customer_id/things/:thing_id/devices/:device_id/debug",
      "/iot-platform/customer-management/:customer_id/applications/:application_id/devices/list",
      "/iot-platform/customer-management/:customer_id/applications/:application_id/devices/:device_id/calibration",
      "/iot-platform/customer-management/:customer_id/applications/:application_id/devices/:device_id/calibration/add",
      "/iot-platform/customer-management/:customer_id/applications/:application_id/devices/:device_id/calibration/edit",
      "/iot-platform/customer-management/:customer_id/applications/:application_id/devices/:device_id/configuration",
      "/iot-platform/customer-management/:customer_id/applications/:application_id/devices/:device_id/custom-command",
      "/iot-platform/customer-management/:customer_id/applications/:application_id/devices/:device_id/health",
      "/iot-platform/customer-management/:customer_id/applications/:application_id/devices/:device_id/communication",
      "/iot-platform/customer-management/roles",
      "/iot-platform/customer-management/devices",
      "/iot-platform/customer-management/devices/add",
      "/iot-platform/customer-management/devices/edit",
      "/iot-platform/customer-management/roles/add",
      "/iot-platform/customer-management/roles/:role_id/edit",
      "/iot-platform/customer-management/roles/:role_id/delete",
      "/iot-platform/customer-management/users",
      "/iot-platform/customer-management/users/add",
      "/iot-platform/customer-management/users/view",
      "/iot-platform/customer-management/users/:user_id/edit",
      "/iot-platform/customer-management/users/:user_id/delete",
      "/iot-platform/customer-management/users/:user_id/set-status",
      "/iot-platform/customer-management/user-groups/view",
      "/iot-platform/customer-management/user-groups/add",
      "/iot-platform/customer-management/user-groups/:group_id/edit",
      "/iot-platform/customer-management/user-groups/:group_id/delete",
      "/iot-platform/customer-management/:customer_id/software-license",
      "/iot-platform/customer-management/:customer_id/api-credentials",
    ],
  },
  {
    component: "SiteConfiguration",
    getPath: (
      path = [],
      props: {
        enabled_features: string | string[];
        getViewAccess: (arg0: string[]) => unknown;
      },
    ) => {
      if (
        !(
          props.enabled_features.includes("SiteManagement:SiteManagement") &&
          props.getViewAccess(["SiteManagement:View"])
        )
      ) {
        return [];
      }
      const filteredRoutes = path.filter((pathUrl) => {
        if (pathUrl === "/iot-platform/sites/add") {
          return props.getViewAccess(["SiteManagement:Add"]);
        }
        if (
          pathUrl ===
          "/iot-platform/customers/:customer_id/sites/:site_id/configuration"
        ) {
          return props.getViewAccess(["SiteManagement:Edit"]);
        }
        return true;
      });
      return filteredRoutes;
    },
    path: [
      "/iot-platform/sites/add",
      "/iot-platform/customers/:customer_id/sites/:site_id/configuration",
      "/iot-platform/customers/:customer_id/sites/:site_id/details",
    ],
  },
  {
    component: "RemoteCalibration",
    getPath: (
      path = [],
      props: {
        enabled_features: string | string[];
      },
    ) => {
      if (props.enabled_features.includes("Workflow:RemoteCalibration")) {
        return path;
      }

      return [];
    },
    path: [
      "/iot-platform/workflow/calibration",
      "/iot-platform/workflow/calibration/details",
    ],
  },
  {
    component: "SiteList",
    getPath: (
      path = [],
      props: {
        enabled_features: string | string[];
        getViewAccess: (arg0: string[]) => any;
      },
    ) => {
      if (
        props.enabled_features.includes("SiteManagement:SiteManagement") &&
        props.getViewAccess(["SiteManagement:View"])
      ) {
        return path;
      }
      return [];
    },
    path: ["/iot-platform/sites"],
  },
  {
    component: "Dashboard",
    getPath: (
      path = [],
      props: {
        enabled_features: string | string[];
        logged_in_user_role_type: number;
      },
    ) => {
      if (
        props.enabled_features.includes("Dashboard:Dashboard") &&
        props.logged_in_user_role_type === 1
      ) {
        return path;
      }
      return [];
    },
    path: ["/iot-platform/dashboard"],
  },
  {
    component: "DashboardClubbed",
    getPath: (
      path = [],
      props: {
        enabled_features: string | string[];
        logged_in_user_role_type: number;
        supportedViewsPage: (arg0: string) => unknown;
      },
    ) => {
      if (window.innerWidth > 576) {
        return [];
      }
      const filteredRoutes = path.filter((pathUrl) => {
        if (pathUrl === "/iot-platform/iot-dashboard") {
          return (
            props.enabled_features?.includes("Dashboard:Dashboard") &&
            props.logged_in_user_role_type === 1
          );
        }
        if (pathUrl === "/iot-platform/rent-dashboard") {
          return props.enabled_features?.includes("ThingsRental:ThingsRental");
        }
        if (pathUrl === "/iot-platform/application-dashboard") {
          return (
            props.enabled_features?.includes(
              "AccessThingData:AccessThingData",
            ) && props.supportedViewsPage("dashboard")
          );
        }
        if (pathUrl === "/iot-platform/asset-dashboard") {
          return props.supportedViewsPage("asset-dashboard");
        }
        return true;
      });

      return filteredRoutes;
    },
    path: [
      "/iot-platform/iot-dashboard",
      "/iot-platform/rent-dashboard",
      "/iot-platform/asset-dashboard",
      "/iot-platform/application-dashboard",
    ],
  },
  {
    component: "UserFullNotification",
    getPath: (path = [], props: { client_id: number }) => {
      if (parseInt(props.client_id) !== 392) {
        return path;
      }
      return [];
    },
    path: ["/iot-platform/user-notifications"],
  },
  {
    component: "AssignedDevice",
    getPath: (path = [], props: { enabled_features: string | string[] }) => {
      if (
        props.enabled_features.includes("DeviceManagement:View") ||
        props.enabled_features.includes("DeviceManagement:DeviceManagement")
      ) {
        return path;
      }
      return [];
    },
    path: [
      "/iot-platform/customer-management/:customer_id/applications/:application_id/devices/:device_id/debug",
      "/iot-platform/devices/assigned",
      "/iot-platform/devices/assigned/:device_id/debug",
    ],
  },
  {
    component: "UnassignedDevice",
    getPath: (
      path = [],
      props: {
        enabled_features: string | string[];
        getViewAccess: (arg0: string[], arg1: boolean) => any;
        vendor_id: number;
        customer_type: number[];
      },
    ) => {
      if (
        !props.enabled_features.includes("DeviceManagement:View") &&
        !props.enabled_features.includes("DeviceManagement:DeviceManagement")
      ) {
        return [];
      }
      const filteredRoutes = path.filter((pathUrl) => {
        if (pathUrl === "/iot-platform/devices/unassigned/add") {
          return props.getViewAccess(["DeviceManagement:Add"], true);
        }
        return true;
      });
      return filteredRoutes;
    },
    path: [
      "/iot-platform/devices/unassigned",
      "/iot-platform/devices/unassigned/add",
      "/iot-platform/devices/unassigned/:device_id/edit",
      "/iot-platform/devices/unassigned/:device_id/debug",
    ],
  },
  {
    component: "DeviceConfiguration",
    getPath: (path = [], props: { enabled_features: string | string[] }) => {
      if (
        props.enabled_features.includes("DeviceManagement:View") ||
        props.enabled_features.includes("DeviceManagement:DeviceManagement")
      ) {
        return path;
      }
      return [];
    },
    path: [
      "/iot-platform/devices/assigned/:station_id/configuration",
      "/iot-platform/devices/unassigned/:station_id/configuration",
    ],
  },
  {
    component: "DeviceCommunication",
    getPath: (path = [], props: { enabled_features: string | string[] }) => {
      if (
        props.enabled_features.includes("DeviceManagement:View") ||
        props.enabled_features.includes("DeviceManagement:DeviceManagement")
      ) {
        return path;
      }
      return [];
    },
    path: [
      "/iot-platform/devices/assigned/:device_id/communication",
      "/iot-platform/devices/unassigned/:device_id/communication",
    ],
  },
  {
    component: "DeviceHealth",
    getPath: (path = [], props: { enabled_features: string | string[] }) => {
      if (
        props.enabled_features.includes("DeviceManagement:View") ||
        props.enabled_features.includes("DeviceManagement:DeviceManagement")
      ) {
        return path;
      }
      return [];
    },
    path: [
      "/iot-platform/devices/assigned/:device_id/health",
      "/iot-platform/devices/unassigned/:device_id/health",
    ],
  },
  {
    component: "DeviceCustomCommand",
    getPath: (path = [], props: { enabled_features: string | string[] }) => {
      if (
        (props.enabled_features.includes("DeviceManagement:View") ||
          props.enabled_features.includes(
            "DeviceManagement:DeviceManagement",
          )) &&
        props.enabled_features.includes("DeviceManagement:CustomCommand")
      ) {
        return path;
      }
      return [];
    },
    path: [
      "/iot-platform/devices/assigned/:device_id/custom-command",
      "/iot-platform/devices/unassigned/:device_id/custom-command",
    ],
  },
  {
    component: "DeviceCalibration",
    getPath: (path = [], props: { enabled_features: string | string[] }) => {
      if (
        props.enabled_features.includes("DeviceManagement:View") ||
        props.enabled_features.includes("DeviceManagement:DeviceManagement")
      ) {
        return path;
      }
      return [];
    },
    path: [
      "/iot-platform/devices/assigned/:device_id/calibration",
      "/iot-platform/devices/assigned/:device_id/calibration/edit",
      "/iot-platform/devices/assigned/:device_id/calibration/add",
      "/iot-platform/devices/unassigned/:device_id/calibration",
      "/iot-platform/devices/unassigned/:device_id/calibration/edit",
      "/iot-platform/devices/unassigned/:device_id/calibration/add",
    ],
  },
  {
    component: "UsersManagement",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/iot-platform/user-management/users/view",
      "/iot-platform/user-management/users/:user_id/view",
      "/iot-platform/user-management/users/add",
      "/iot-platform/user-management/users/:user_id/edit",
      "/iot-platform/user-management/user-groups/view",
      "/iot-platform/user-management/user-groups/add",
      "/iot-platform/user-management/user-groups/:group_id/edit",
      "/iot-platform/user-management/roles/view",
      "/iot-platform/user-management/roles/add",
      "/iot-platform/user-management/roles/:role_id/edit",
    ],
  },
  {
    component: "TerritoryPage",
    getPath: (
      path = [],
      props: {
        enabled_features: string | string[];
        getViewAccess: (arg0: string[]) => any;
      },
    ) => {
      if (
        props.enabled_features?.includes("UserManagement:Territory") &&
        props.getViewAccess(["TerritoryManagement:View"]) &&
        window.innerWidth >= 1080
      ) {
        return path;
      }
      return [];
    },
    path: [
      "/iot-platform/customer-management/:customer_id/territory/view",
      "/iot-platform/user-management/territory/view",
    ],
  },
  {
    component: "ThingList",
    getPath: (path = [], props: { enabled_features: string | string[] }) => {
      if (
        props.enabled_features?.includes("ThingManagement:ThingAdd") ||
        props.enabled_features?.includes("ThingManagement:ThingList") ||
        props.enabled_features?.includes("ThingManagement:ThingManagement") ||
        props.enabled_features?.includes("ThingManagement:View") ||
        props.enabled_features?.includes("ThingManagement:View")
      ) {
        return path;
      }
      return [];
    },
    path: [
      "/iot-platform/things/",
      "/iot-platform/things/:asset_id/customers/:customer_id/asset-data",
      "/iot-platform/things/:thing_id/devices/:device_id/debug",
    ],
  },
  {
    component: "DataPushLogs",
    getPath: (path = []) => {
      return path;
    },
    path: [
      "/iot-platform/things/:asset_id/customers/:customer_id/data-push-logs",
    ],
  },
  {
    component: "ThingAdd",
    getPath: (
      path = [],
      props: {
        logged_in_user_client_id: number;
        getViewAccess: (arg0: string[], arg1: boolean) => any;
      },
    ) => {
      if (
        props.getViewAccess(["ThingManagement:Add"], true) ||
        props.logged_in_user_client_id === 1
      ) {
        return path;
      }
      return [];
    },
    path: [
      "/iot-platform/things/add",
      "/iot-platform/customer/:customer_id/things/add",
    ],
  },
  {
    component: "ThingConfigure",
    getPath: (
      path = [],
      props: {
        logged_in_user_client_id: number;
        getViewAccess: (arg0: string[], arg1: boolean) => any;
      },
    ) => {
      if (
        props.getViewAccess(["ThingManagement:Edit"], true) ||
        props.logged_in_user_client_id === 1
      ) {
        return path;
      }
      return [];
    },
    path: [
      "/iot-platform/customers/:customer_id/applications/:application_id/things/:thing_id/configuration/general",
      "/iot-platform/customers/:customer_id/applications/:application_id/things/:thing_id/configuration/configuration",
      "/iot-platform/customers/:customer_id/applications/:application_id/things/:thing_id/configuration/info",
    ],
  },
  {
    component: "RentalInventory",
    getPath: (
      path = [],
      props: { customer_type: number[]; enabled_features: string | string[] },
    ) => {
      if (
        props.customer_type?.includes(4) &&
        props.enabled_features?.includes(
          "InventoryManagement:InventoryManagement",
        )
      ) {
        return path;
      }
      return [];
    },
    path: ["/iot-platform/rental-inventory"],
  },
  {
    component: "OrdersList",
    getPath: (
      path = [],
      props: { customer_type: number[]; enabled_features: string | string[] },
    ) => {
      if (
        props.customer_type?.includes(4) &&
        props.enabled_features?.includes("ThingsRental:ThingsRental")
      ) {
        return path;
      }
      return [];
    },
    path: [
      "/iot-platform/rental-orders",
      "/iot-platform/rental-orders/:order_id/details",
      "/iot-platform/rental-orders/:order_id/doc-verification",
      "/iot-platform/rental-orders/:order_id/asset-assign",
      "/iot-platform/rental-orders/:order_id/edit",
    ],
  },
  {
    component: "RentalDashboard",
    getPath: (path = [], props: { enabled_features: string | string[] }) => {
      if (props.enabled_features?.includes("ThingsRental:ThingsRental")) {
        return path;
      }
      return [];
    },
    path: ["/iot-platform/order-dashboard"],
  },
  {
    component: "IotRuleManagement",
    getPath: (
      path = [],
      props: { customer_type: number[]; enabled_features: string | string[] },
    ) => {
      if (
        props.customer_type?.includes(4) &&
        props.enabled_features?.includes("AlertManagement:AlertManagement") &&
        props.enabled_features?.includes("ThingsRental:ThingsRental")
      ) {
        return path;
      }
      return [];
    },
    path: ["/iot-platform/alerts-management"],
  },
  {
    component: "ServiceModule",
    getPath: (path = [], props: { enabled_features: string | string[] }) => {
      if (
        props.enabled_features?.includes("ServiceManagement:ServiceManagement")
      ) {
        return path;
      }
      return [];
    },
    path: [
      "/iot-platform/work-orders",
      "/iot-platform/work-orders/:id/details",
    ],
  },
  {
    component: "DgInIotPages",
    getPath: (path = [], props: { enabled_features: string | string[] }) => {
      if (props.enabled_features.includes("AccessThingData:AccessThingData")) {
        return path;
      }
      return [];
    },
    path: ["/iot-platform/dg-monitoring/:page_name"],
  },
  {
    component: "MorePage",
    getPath: (path = []) => {
      if (window.innerWidth <= 576) {
        return path;
      }
      return [];
    },
    path: ["/iot-platform/more"],
  },
  {
    component: "ThingQuickAdd",
    getPath: (path = [], props: { getViewAccess: (arg0: string[]) => any }) => {
      if ((props.getViewAccess(["ThingManagement:Add"]), true)) {
        return path;
      }
      return [];
    },
    path: ["/iot-platform/things/quick-add"],
  },
  {
    component: "ThingDetails",
    getPath: (path = [], props: { enabled_features: string | string[] }) => {
      if (
        props.enabled_features?.includes("ThingManagement:ThingAdd") ||
        props.enabled_features?.includes("ThingManagement:ThingList") ||
        props.enabled_features?.includes("ThingManagement:ThingManagement") ||
        props.enabled_features?.includes("ThingManagement:View")
      ) {
        return path;
      }
      return [];
    },
    path: [
      "/iot-platform/customers/:customer_id/applications/:application_id/things/:thing_id/thing-details",
    ],
  },
  {
    component: "TripView",
    getPath: (path = [], props: { enabled_features: string | string[] }) => {
      if (props.enabled_features?.includes("AccessThingData:TripView")) {
        return path;
      }
      return [];
    },
    path: [
      "/iot-platform/trip-view/:pageType",
      "/iot-platform/trip-view/:pageType/:trip_id/details",
    ],
  },
  {
    component: "AllMapViews",
    getPath: (path = []) => {
      return path;
    },
    path: ["/iot-platform/map-view"],
  },
  {
    component: "AssetDashboard",
    getPath: (
      path = [],
      props: { supportedViewsPage: (arg0: string) => any },
    ) => {
      if (window.innerWidth < 576) {
        return [];
      }
      if (props.supportedViewsPage("asset-dashboard")) {
        return path;
      }
      return [];
    },
    path: ["/iot-platform/asset-dashboard"],
  },
  {
    component: "TicketingSupport",
    getPath: (path = []) => {
      return path;
    },
    path: ["/iot-platform/tickets", "/tickets"],
  },
  {
    component: "ProductInventory",
    getPath: (path = [], props: { enabled_features: string | string[] }) => {
      if (
        props.enabled_features.includes("InventoryManagement:Circuits") ||
        props.enabled_features.includes("InventoryManagement:Composites") ||
        props.enabled_features.includes("InventoryManagement:Products") ||
        props.enabled_features.includes("InventoryManagement:Sensors")
      ) {
        return path;
      }
      return [];
    },
    path: [
      "/iot-platform/hardware-production/",
      "/iot-platform/hardware-production/production-inventory",
      "/iot-platform/hardware-production/production-inventory/circuits",
      "/iot-platform/hardware-production/production-inventory/composites",
      "/iot-platform/hardware-production/production-inventory/products",
      "/iot-platform/hardware-production/production-inventory/sensors",
      "/iot-platform/hardware-production/production-inventory/circuits/test/:type_id/:ent_type_id/:ent_id",
      "/iot-platform/hardware-production/production-inventory/composites/test/:type_id/:ent_type_id/:ent_id",
      "/iot-platform/hardware-production/production-inventory/products/test/:type_id/:ent_type_id/:ent_id",
      "/iot-platform/hardware-production/production-inventory/circuits/log/:type_id/:ent_id",
      "/iot-platform/hardware-production/production-inventory/composites/log/:type_id/:ent_id",
      "/iot-platform/hardware-production/production-inventory/products/log/:type_id/:ent_id",
      "/iot-platform/hardware-production/production-inventory/composites/details/:type_id/:ent_id",
      "/iot-platform/hardware-production/production-inventory/products/details/:type_id/:ent_id",
      "/iot-platform/hardware-production/production-inventory/sensors/details/:type_id/:ent_id",
    ],
  },
  {
    component: "FirmwareManagement",
    getPath: (path = [], props: { enabled_features: string | string[] }) => {
      if (
        props.enabled_features.includes("FirmwareManagement:FirmwareManagement")
      ) {
        return path;
      }
      return [];
    },
    path: [
      "/iot-platform/firmware-management",
      "/iot-platform/firmware-management/:device_type",
      "/iot-platform/firmware-management/:device_type/:version/bug",
      "/iot-platform/firmware-management/:device_type/:version/release-log",
      "/iot-platform/firmware-management/:device_type/:version/dev-log",
      "/iot-platform/firmware-management/:device_type/upload",
    ],
  },
];

export const non_exact_paths = ["DgInIotPages", "NewReportTemplates"];
