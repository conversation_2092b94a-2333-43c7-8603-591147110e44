import React from "react";
import { Route } from "react-router-dom";
import {
  getBasePathComponent,
  getRoutesMapping,
  non_exact_paths,
} from "./routes";
import { JSX } from "react/jsx-runtime";

const MorePage = React.lazy(
  () => import("../../packages/iot-platform-views/src/js/components/MorePage"),
);
const Dashboard = React.lazy(
  () =>
    import(
      "../../packages/iot-platform-views/src/js/DatomsX/Template-1/Dashboard"
    ),
);
const DashboardClubbed = React.lazy(
  () => import("../../packages/iot-platform-views/src/js/DashboardClubbed"),
);
const DgInIotPages = React.lazy(
  () => import("../../packages/iot-platform-views/src/js/DGMonitoring"),
);
const TerritoryPage = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-user-management/src/components/TerritoryPage"
    ),
);
const FirmwareManagement = React.lazy(
  () =>
    import(
      "../../packages/iot-platform-views/src/js/DatomsX/Template-1/FirmwareManagement"
    ),
);
const CustomerManagement = React.lazy(
  () =>
    import(
      "../../packages/iot-platform-views/src/js/DatomsX/Template-1/CustomerManagement"
    ),
);

const AssignedDevice = React.lazy(
  () =>
    import(
      /* webpackChunkName: "AssignedDevice"*/ "../../packages/webapp-component-device-management/src/components/AssignedDevice"
    ),
);
const UnassignedDevice = React.lazy(
  () =>
    import(
      /* webpackChunkName: "UnassignedDevice"*/ "../../packages/webapp-component-device-management/src/components/UnassignedDevice"
    ),
);
const DeviceCalibration = React.lazy(
  () =>
    import(
      /* webpackChunkName: "DeviceCalibration"*/ "../../packages/webapp-component-device-management/src/components/DeviceCalibration"
    ),
);
const DeviceCommunication = React.lazy(
  () =>
    import(
      /* webpackChunkName: "DeviceCommunication"*/ "../../packages/webapp-component-device-management/src/components/DeviceCommunication"
    ),
);
const DeviceCustomCommand = React.lazy(
  () =>
    import(
      /* webpackChunkName: "DeviceCustomCommand"*/ "../../packages/webapp-component-device-management/src/components/DeviceCustomCommand"
    ),
);
const DeviceConfiguration = React.lazy(
  () =>
    import(
      /* webpackChunkName: "DeviceConfiguration"*/ "../../packages/webapp-component-device-management/src/components/DeviceConfiguration"
    ),
);
const DeviceHealth = React.lazy(
  () =>
    import(
      /* webpackChunkName: "DeviceHealth"*/ "../../packages/webapp-component-device-management/src/components/DeviceHealth"
    ),
);

const ThingAddNew = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-thing-management/src/components/ThingAddV2"
    ),
);
const ThingList = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-thing-management/src/components/ThingList"
    ),
);
const ThingDetails = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-thing-management/src/components/ThingDetails/ThingDetails"
    ),
);
const ThingQuickAdd = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-thing-management/src/components/QuickAdd/ThingQuickAdd"
    ),
);

const SiteConfiguration = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-thing-management/src/components/SiteConfiguration"
    ),
);
const SiteList = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-thing-management/src/components/SiteList"
    ),
);

const ChangeLogView = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-changelog/src/components/ChangeLogView"
    ),
);

const UsersManagement = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-user-management/src/components/UsersManagement"
    ),
);
const UserFullNotification = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-user-notifications/src/components/UserFullNotification"
    ),
);
const BillingManagement = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-billing-management/src/components/BillingManagement"
    ),
);
const OrdersList = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-rental-management/src/components/OrdersList"
    ),
);
const RentalInventory = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-rental-management/src/components/RentalInventory"
    ),
);
const RentalDashboard = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-rental-management/src/components/RentalDashboard"
    ),
);

const IotRuleManagement = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-alerts-management/src/components/IotRuleManagement"
    ),
);
const ServiceModule = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-alerts-management/src/components/ServiceModule"
    ),
);

const ReportTemplates = React.lazy(
  () =>
    import("../../packages/iot-platform-views/src/js/reports/ReportTemplate"),
);

const NewReportTemplates = React.lazy(
  () =>
    import(
      "../../packages/iot-platform-views/src/js/reports/NewReportTemplate"
    ),
);

const AlertsSubscriptionCustomers = React.lazy(
  () =>
    import(
      "../../packages/iot-platform-views/src/js/reports/alerSubscriptionCustomers/pages"
    ),
);
const MachineInforeport = React.lazy(
  () =>
    import(
      "../../packages/iot-platform-views/src/js/reports/MachineInfoReport/MachineInforeport"
    ),
);

const SubscriptionList = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-subscription-management/src/components/SubscriptionList"
    ),
);
const FuelDeliveryReport = React.lazy(
  () =>
    import(
      "../../packages/iot-platform-views/src/js/reports/FuelDeliveryReport"
    ),
);

const DgStatusReport = React.lazy(
  () =>
    import("../../packages/iot-platform-views/src/js/reports/DgStatusReport"),
);
const TripView = React.lazy(
  () =>
    import("@datoms/dg-monitoring-views/src/js/GenericTemplate/pages/TripView"),
);
const AllMapViews = React.lazy(
  () =>
    import(
      "../../packages/iot-platform-views/src/js/DatomsX/Template-1/AllMapViews"
    ),
);
const RemoteCalibration = React.lazy(
  () =>
    import(
      "@datoms/dg-monitoring-views/src/js/GenericTemplate/pages/RemoteCalibration"
    ),
);
const AssetDashboard = React.lazy(
  () =>
    import(
      "@datoms/dg-monitoring-views/src/js/GenericTemplate/pages/AssetDashboard"
    ),
);

const DataAvailabilityReport = React.lazy(
  () =>
    import(
      "../../packages/iot-platform-views/src/js/reports/DataAvailabilityReport"
    ),
);

const DeviceHealthReport = React.lazy(
  () =>
    import(
      "../../packages/iot-platform-views/src/js/reports/DeviceHealthReport"
    ),
);

const ProductInventory = React.lazy(
  () =>
    import(
      "../../packages/datoms-webapp-product-inventory-system/src/js/DatomsX/Template-1/ProductInventory"
    ),
);

const TicketingSupport = React.lazy(
  () => import("../../containers/TicketingSupport"),
);

const MapView = React.lazy(
  () => import("../../containers/views/MapView"),
);

const ParameterList = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-thing-management/src/components/ParameterList/ParameterList"
    ),
);

const DeviceList = React.lazy(
  () =>
    import(
      "../../containers/managements/Devices/Devices"
    ),
);

const DeviceDebug = React.lazy(
  () =>
    import(
      "../../containers/managements/Devices/DeviceDebug"
    ),
);

const DataPushLogs = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-thing-management/src/components/DataPushLogs"
    ),
);

const AssetList = React.lazy(
  () => import("../../containers/managements/Assets/AssetList"),
);

const Settings = React.lazy(
  () => import("@datoms/datoms-settings/src/index.js"),
);
const DatomsPolicy = React.lazy(
  () => import("@datoms/datoms-settings/src/pages/DatomsPolicy"),
);

export function iotPlatformDatomsXRoutes(
  client_id: number,
  head_side_object_data: any,
  AlertObjectData: {
    buttons: { text: string; value: string; primary: boolean }[];
    status_btn: { text: string; value: string; primary: boolean }[];
    show_menu: boolean;
    application_menu: {
      head_text: string[];
      menu_items: { key: string; page_name: string; menu_value: number }[];
      bg_color: string;
      selected_menu_color: string;
    };
    thing_category_select: {
      label: string[];
      select_option: {
        show_arrow: boolean;
        default_value: string;
        options: never[];
      }[];
    };
    alert_table: {
      action_button: {
        icon_type: string;
        text: string;
        show_action_button: boolean;
      };
      config: {
        pagination_data: {
          size: string;
          total: number;
          pageSize: number;
          showSizeChanger: boolean;
          showQuickJumper: boolean;
          hideOnSinglePage: boolean;
        };
        bordered: boolean;
        size: string;
        showHeader: boolean;
        rowSelect: boolean;
        scroll: { y: number };
        loading: boolean;
        locale: {
          filterTitle: string;
          filterConfirm: string;
          filterReset: string;
          emptyText: string;
        };
      };
      head_data: (
        | {
            title: JSX.Element;
            dataIndex: string;
            key: string;
            render: (value: any, row_data: any, index: any) => JSX.Element;
            width: string;
            align?: undefined;
          }
        | {
            title: JSX.Element;
            key: string;
            align: string;
            render: (value: any, row_data: any, index: any) => JSX.Element;
            width: string;
            dataIndex?: undefined;
          }
      )[];
      row_data: never[];
    };
  },
  getViewAccess: any,
  enabled_features: any,
  props: any,
  application_id: number,
  application_name: string,
  client_name: string,
  client_logo: string,
  vendor_logo: string,
  user_id: string | number,
  user_preferences: any,
  collapsed: boolean,
  onCollapseSider: any,
  t: any,
  globalConfig: any,
  getRemoteAccess: any,
  vendor_id: string | number,
  logged_in_user_role_type: string | number,
  logged_in_user_client_id: string | number,
  app_name: string,
  customer_type: number[],
  is_iot_enabled: boolean,
  viewDataAccess: any,
  setMenuUrl: any,
  supportedViewsPage: any,
  allApps: string[],
  currentUserPreferences: any,
  updateCurrentUserPreference: any,
  availableThingCats: any,
  is_calibration_supported: boolean,
  translationUrls: [],
) {
  const final_routes: JSX.Element[] = [];
  let page_name_map = {
    Dashboard: Dashboard,
    DashboardClubbed: DashboardClubbed,
    FirmwareManagement: FirmwareManagement,
    CustomerManagement: CustomerManagement,
    AssignedDevice: AssignedDevice,
    UnassignedDevice: UnassignedDevice,
    DeviceCalibration: DeviceCalibration,
    DeviceCommunication: DeviceCommunication,
    DeviceConfiguration: DeviceConfiguration,
    DeviceCustomCommand: DeviceCustomCommand,
    DeviceHealth: DeviceHealth,
    ThingAdd: ThingAddNew,
    ThingConfigure: ThingAddNew,
    SiteConfiguration: SiteConfiguration,
    SiteList: SiteList,
    ThingList: ThingList,
    OrdersList: OrdersList,
    UsersManagement: UsersManagement,
    RentalInventory: RentalInventory,
    RentalDashboard: RentalDashboard,
    IotRuleManagement: IotRuleManagement,
    ChangeLogView: ChangeLogView,
    UserFullNotification: UserFullNotification,
    BillingManagement: BillingManagement,
    TerritoryPage: TerritoryPage,
    MorePage: MorePage,
    DgInIotPages: DgInIotPages,
    RemoteCalibration: RemoteCalibration,
    ThingQuickAdd: ThingQuickAdd,
    ThingDetails: window.innerWidth > 576 ? ThingList : ThingDetails,
    NewReportTemplates: NewReportTemplates,
    AlertsSubscriptionCustomers: AlertsSubscriptionCustomers,
    MachineInforeport: MachineInforeport,
    ServiceModule: ServiceModule,
    SubscriptionList: SubscriptionList,
    FuelDeliveryReport: FuelDeliveryReport,
    DgStatusReport: DgStatusReport,
    DataAvailabilityReport: DataAvailabilityReport,
    DeviceHealthReport: DeviceHealthReport,
    AssetDashboard: AssetDashboard,
    TripView: TripView,
    AllMapViews: AllMapViews,
    ProductInventory: ProductInventory,
    TicketingSupport: TicketingSupport,
    MapView: MapView,
    ParameterList: ParameterList,
    DeviceList: DeviceList,
    DeviceDebug: DeviceDebug,
    Settings: Settings,
    DatomsPolicy: DatomsPolicy,
    DataPushLogs: DataPushLogs,
    AssetList: AssetList,
  };
  if (logged_in_user_role_type === 7) {
    page_name_map = {
      OrdersList: OrdersList,
    };
  }
  const common_props = {
    client_id,
    head_side_object_data,
    AlertObjectData,
    getViewAccess,
    enabled_features,
    props,
    application_id,
    application_name,
    client_name,
    client_logo,
    vendor_logo,
    user_id,
    user_preferences,
    collapse: collapsed,
    onCollapseSider,
    t,
    globalConfig,
    getRemoteAccess,
    vendor_id,
    logged_in_user_role_type,
    logged_in_user_client_id,
    app_name,
    customer_type,
    is_iot_enabled,
    viewDataAccess,
    setMenuUrl,
    supportedViewsPage,
    allApps,
    currentUserPreferences,
    updateCurrentUserPreference,
    availableThingCats,
    is_calibration_supported,
    translationUrls
  };
  const route_mapping = getRoutesMapping(common_props);
  const baseComponentName = getBasePathComponent(common_props);
  route_mapping.forEach((routeItem) => {
    const PageName = page_name_map[routeItem.component];
    if (!PageName) return; // Page component not available

    const pageRoutes = routeItem.getPath(routeItem.path, common_props);
    if (!pageRoutes.length) return; // Page route access not available
    if (routeItem.component === baseComponentName) {
      pageRoutes.push(
        application_id === 12 ? ("/", "/datoms-x") : ("/", "/iot-platform"),
      );
    }
    final_routes.push(
      <Route
        exact={non_exact_paths.includes(routeItem.component) ? false : true}
        path={pageRoutes}
        render={(props) => <PageName {...common_props} {...props} />}
      />,
    );
  });
  return final_routes;
}
