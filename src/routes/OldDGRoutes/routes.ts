import { availableThingCategory, checkMenuAvailability } from "../routeHandler";
import { GetRouteMappingProps } from "./types";

export const getRoutesMapping = (props: GetRouteMappingProps) => {
  // const {
  //   dgSetType,
  //   fuelTankType,
  //   compressorType,
  //   fleetType,
  //   tankerType,
  //   energyMeterType,
  //   solarPowerType,
  //   processAnalyzerType,
  //   tempHumidType,
  //   inverterType,
  //   dcEnergyMeterType,
  //   electricalMachinesType,
  //   exhaustFanType,
  //   flowMeterMachinesType,
  //   complianceMachinesType,
  // } = availableThingCategory(props.thing_list?.things);
  let routes = [
    {
      component: "Settings",
      getPath: (path = []) => {
        return path;
      },
      path: ["/dg-monitoring/settings/:page_name", "/settings/:page_name"],
    },
    {
      component: "DatomsPolicy",
      getPath: (path = []) => {
        return path;
      },
      path: ["/dg-monitoring/privacy-policy", "/privacy-policy"],
    },
    {
      component: "TicketingSupport",
      getPath: (path = []) => {
        return path;
      },
      path: ["/dg-monitoring/tickets", "/tickets"],
    },
    {
      component: "SiteDashboard",
      getPath: (path = []) => {
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: ["/dg-monitoring/site-dashboard", "/site-dashboard"],
    },
    {
      component: "Warehouse",
      getPath: (path = []) => {
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: ["/dg-monitoring/warehouse", "/warehouse"],
    },
    {
      component: "UserNotification",
      getPath: (path = []) => {
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: ["/dg-monitoring/user-notifications", "/user-notifications"],
    },
    {
      component: "TerritoryPage",
      getPath: (
        path = [],
        props: {
          enabled_features: string | string[];
          getViewAccess: (arg0: string[]) => any;
        },
      ) => {
        if (
          props.enabled_features?.includes("UserManagement:Territory") &&
          props.getViewAccess(["TerritoryManagement:View"]) &&
          window.innerWidth >= 1080
        ) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/user-management/territory/view",
        "/user-management/territory/view",
      ],
    },
    {
      component: "UsersManagement",
      getPath: (path = []) => {
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/user-management/users/view",
        "/user-management/users/view",
        "/dg-monitoring/user-management/users/:user_id/view",
        "/user-management/users/:user_id/view",
        "/dg-monitoring/user-management/users/add",
        "/user-management/users/add",
        "/dg-monitoring/user-management/users/:user_id/edit",
        "/user-management/users/:user_id/edit",
        "/dg-monitoring/user-management/user-groups/view",
        "/user-management/user-groups/view",
        "/dg-monitoring/user-management/user-groups/add",
        "/user-management/user-groups/add",
        "/dg-monitoring/user-management/user-groups/:group_id/edit",
        "/user-management/user-groups/:group_id/edit",
        "/dg-monitoring/user-management/roles/view",
        "/user-management/roles/view",
        "/dg-monitoring/user-management/roles/add",
        "/user-management/roles/add",
        "/dg-monitoring/user-management/roles/:role_id/edit",
        "/user-management/roles/:role_id/edit",
      ],
    },
    {
      component: "CustomReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/custom-reports",
        "/reports/custom-reports",
      ],
    },
    {
      component: "DataAvailabilityReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/data-availability-report",
        "/reports/data-availability-report",
      ],
    },
    {
      component: "DgStatusReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/asset-status-report",
        "/reports/asset-status-report",
      ],
    },
    {
      component: "ReportTemplates",
      getPath: (path = []) => {
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: ["/dg-monitoring/reports", "/reports"],
    },
    {
      component: "SummaryReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/summary-reports",
        "/reports/summary-reports",
      ],
    },
    {
      component: "YesterdayDailyFaultReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/yesterday-data-reports",
        "/reports/yesterday-data-reports",
      ],
    },
    {
      component: "LifetimeReports",
      getPath: (path = []) => {
        return parseInt(props.vendor_id) !== 1140 ? path : [];
      },
      path: [
        "/dg-monitoring/reports/lifetime-reports",
        "/reports/lifetime-reports",
      ],
    },
    {
      component: "FleetDailyReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/fleet/daily-reports",
        "/reports/fleet/daily-reports",
      ],
    },
    {
      component: "DgDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/template-reports",
        "/reports/template-reports",
      ],
    },
    {
      component: "GasGensetDailyReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/gas-genset/daily-report",
        "/reports/gas-genset/daily-report",
      ],
    },
    {
      component: "GasGensetMultiAssetReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/gas-genset/multi-asset-reports",
        "/reports/gas-genset/multi-asset-reports",
      ],
    },
    {
      component: "FleetTripReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/fleet/trip-reports",
        "/reports/fleet/trip-reports",
      ],
    },
    {
      component: "TankerTruckTripReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/tanker-truck/trip-reports",
        "/reports/tanker-truck/trip-reports",
      ],
    },
    {
      component: "FleetTripDetails",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/fleet/trip-reports/details",
        "/reports/fleet/trip-reports/details",
      ],
    },
    {
      component: "TankerTruckTripDetails",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/tanker-truck/trip-reports/details",
        "/reports/tanker-truck/trip-reports/details",
      ],
    },
    {
      component: "AlertSubscriptionThingsReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/alert-delivery-summary-reports",
        "/reports/alert-delivery-summary-reports",
      ],
    },
    {
      component: "EnergyMeterDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/energy-meter/daily-reports",
        "/reports/energy-meter/daily-reports",
        "/dg-monitoring/reports/inverter/daily-reports",
        "/reports/inverter/daily-reports",
      ],
    },
    {
      component: "SolarPowerDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/solar-power/daily-reports",
        "/reports/solar-power/daily-reports",
      ],
    },
    {
      component: "ProcessAnalyzerDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/process-analyzer/daily-reports",
        "/reports/process-analyzer/daily-reports",
      ],
    },
    {
      component: "ProcessAnalyzerFaultReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/process-analyzer/fault-reports",
        "/reports/process-analyzer/fault-reports",
      ],
    },
    {
      component: "SolarPowerTripReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/solar-power/trip-reports",
        "/reports/solar-power/trip-reports",
      ],
    },
    {
      component: "SolarPowerMultiAssetReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/solar-power/multi-asset-reports",
        "/reports/solar-power/multi-asset-reports",
      ],
    },
    {
      component: "AcEnergyDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/ac-energy-meter/daily-reports",
        "/reports/ac-energy-meter/daily-reports",
      ],
    },
    {
      component: "GridEnergyMeterDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/grid-energy-meter/daily-reports",
        "/reports/grid-energy-meter/daily-reports",
      ],
    },
    {
      component: "AcEnergyMultiAsserReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/ac-energy-meter/multi-asset-reports",
        "/reports/ac-energy-meter/multi-asset-reports",
      ],
    },
    {
      component: "GridEnergyMeterMultiAssetReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/grid-energy-meter/multi-asset-reports",
        "/reports/grid-energy-meter/multi-asset-reports",
      ],
    },
    {
      component: "AcElectricalMachinesDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/ac-electrical-machines/daily-reports",
        "/reports/ac-electrical-machines/daily-reports",
      ],
    },
    {
      component: "AcElectricalMachinesTripReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/ac-electrical-machines/trip-reports",
        "/reports/ac-electrical-machines/trip-reports",
      ],
    },
    {
      component: "AcElectricalMachinesMultiAsserReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/ac-electrical-machines/multi-asset-reports",
        "/reports/ac-electrical-machines/multi-asset-reports",
      ],
    },
    {
      component: "ExhaustFanDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/exhaust-fan/daily-reports",
        "/reports/exhaust-fan/daily-reports",
      ],
    },
    {
      component: "ExhaustFanTripReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/exhaust-fan/trip-reports",
        "/reports/exhaust-fan/trip-reports",
      ],
    },
    {
      component: "ExhaustFanMultiAsserReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/exhaust-fan/multi-asset-reports",
        "/reports/exhaust-fan/multi-asset-reports",
      ],
    },
    {
      component: "TempHumidDailyReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/temp-humid/daily-reports",
        "/reports/temp-humid/daily-reports",
      ],
    },
    {
      component: "EnergyMeterAvailabilityReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/energy-meter/availability-reports",
        "/reports/energy-meter/availability-reports",
      ],
    },
    {
      component: "TankerTruckFuelTankerDailyReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/tanker-truck/daily-reports",
        "/reports/tanker-truck/daily-reports",
      ],
    },
    {
      component: "TankerTruckVehicleDailyReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/vehicle/daily-reports",
        "/reports/vehicle/daily-reports",
      ],
    },
    {
      component: "TankerTruckFuelFilledDispenseReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/fuel-fill-dispense",
        "/reports/fuel-fill-dispense",
      ],
    },
    {
      component: "CompDailyReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/compressor/daily-reports",
        "/reports/compressor/daily-reports",
      ],
    },
    {
      component: "CompDgRunReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/compressor/run-reports",
        "/reports/compressor/run-reports",
      ],
    },
    {
      component: "DGFaultReport",
      getPath: (path = []) => {
        return path;
      },
      path: ["/dg-monitoring/reports/fault-reports", "/reports/fault-reports"],
    },
    {
      component: "GasGensetFaultReport",
      getPath: (path = []) => {
        return path;
      },
      path: ["reports/gas-genset/fault-reports", "/dg-monitoring/reports/gas-genset/fault-reports"],
    },
    {
      component: "CriticalReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/critical-trends-reports",
        "/reports/critical-trends-reports",
      ],
    },
    {
      component: "DgRunReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/dg-run-reports",
        "/reports/dg-run-reports",
      ],
    },
    {
      component: "GasGensetTripReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/reports/gas-genset/run-reports",
        "/dg-monitoring/reports/gas-genset/run-reports",
      ],
    },
    {
      component: "DGMultiAssetReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/dg-multi-assets-report",
        "/reports/dg-multi-assets-report",
      ],
    },
    {
      component: "DGSnapshotReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/dg-snapshot-report",
        "/reports/dg-snapshot-report",
      ],
    },
    {
      component: "DgStatusReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/asset-status-report",
        "/reports/asset-status-report",
      ],
    },
    {
      component: "FuelFillDrainReport",
      getPath: (path = []) => {
        return parseInt(props.vendor_id) !== 1140 ? path : [];
      },
      path: [
        "/dg-monitoring/reports/fuel-fill-drain",
        "/reports/fuel-fill-drain",
      ],
    },
    {
      component: "FuelTankFillReport",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/fuel-tank-fill",
        "/reports/fuel-tank-fill",
      ],
    },
    {
      component: "FuelTankDailyReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/fuel-tank/daily-reports",
        "/reports/fuel-tank/daily-reports",
      ],
    },
    {
      component: "FuelTankSnapShotReports",
      getPath: (path = []) => {
        return path;
      },
      path: [
        "/dg-monitoring/reports/fuel-tank/snapshot-reports",
        "/reports/fuel-tank/snapshot-reports",
      ],
    },
    {
      component: "ParameterReports",
      getPath: (path = []) => {
        return parseInt(props.vendor_id) !== 1140 ? path : [];
      },
      path: [
        "/dg-monitoring/reports/parameter-reports",
        "/reports/parameter-reports",
      ],
    },
    {
      component: "GensetFuelTankSiteReport",
      getPath: (path = []) => {
        // revisit
        return path;
      },
      path: [
        "/dg-monitoring/reports/genset-fuel-tank-site-report",
        "/reports/genset-fuel-tank-site-report",
      ],
    },
    {
      component: "AssignedDevice",
      getPath: (path = []) => {
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/devices",
        "/devices",
        "/dg-monitoring/devices/assigned",
        "/devices/assigned",
        "/dg-monitoring/devices/assigned/:device_id/debug",
        "/devices/assigned/:device_id/debug",
      ],
    },
    {
      component: "DeviceConfiguration",
      getPath: (path = []) => {
        //remove
        if (
          checkMenuAvailability(
            ["/dg-monitoring/devices", "/devices"],
            props.menuUrlArray,
          )
        ) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/devices/assigned/:station_id/configuration",
        "/devices/assigned/:station_id/configuration",
        // "/dg-monitoring/devices/unassigned/:station_id/configuration",
        // "/devices/unassigned/:station_id/configuration",
      ],
    },
    {
      component: "DeviceCommunication",
      getPath: (path = []) => {
        //remove
        if (
          checkMenuAvailability(
            ["/dg-monitoring/devices", "/devices"],
            props.menuUrlArray,
          )
        ) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/devices/assigned/:device_id/communication",
        "/devices/assigned/:device_id/communication",
        // "/dg-monitoring/devices/unassigned/:device_id/communication",
        // "/devices/unassigned/:device_id/communication",
      ],
    },
    {
      component: "DeviceHealth",
      getPath: (path = []) => {
        if (
          checkMenuAvailability(
            ["/dg-monitoring/devices", "/devices"],
            props.menuUrlArray,
          )
        ) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/devices/assigned/:device_id/health",
        "/devices/assigned/:device_id/health",
        // "/dg-monitoring/devices/unassigned/:device_id/health",
        // "/devices/unassigned/:device_id/health",
      ],
    },
    {
      component: "DeviceCustomCommand",
      getPath: (path = []) => {
        if (
          checkMenuAvailability(
            ["/dg-monitoring/devices", "/devices"],
            props.menuUrlArray,
          )
        ) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/devices/assigned/:device_id/custom-command",
        "/devices/assigned/:device_id/custom-command",
        // "/dg-monitoring/devices/unassigned/:device_id/custom-command",
        // "/devices/unassigned/:device_id/custom-command",
      ],
    },
    {
      component: "DeviceCalibration",
      getPath: (path = []) => {
        if (
          checkMenuAvailability(
            ["/dg-monitoring/devices", "/devices"],
            props.menuUrlArray,
          )
        ) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/devices/assigned/:device_id/calibration",
        "/devices/assigned/:device_id/calibration",
        "/dg-monitoring/devices/assigned/:device_id/calibration/edit",
        "/devices/assigned/:device_id/calibration/edit",
        "/dg-monitoring/devices/assigned/:device_id/calibration/add",
        "/devices/assigned/:device_id/calibration/add",
        // "/dg-monitoring/devices/unassigned/:device_id/calibration",
        // "/devices/unassigned/:device_id/calibration",
        // "/dg-monitoring/devices/unassigned/:device_id/calibration/edit",
        // "/devices/unassigned/:device_id/calibration/edit",
        // "/dg-monitoring/devices/unassigned/:device_id/calibration/add",
        // "/devices/unassigned/:device_id/calibration/add",
      ],
    },
    {
      component: "FinalThingAdd",
      getPath: (path = []) => {
        if (
          checkMenuAvailability(
            ["/things/", "/dg-monitoring/things/"],
            props.menuUrlArray,
          ) &&
          props.getViewAccess(["ThingManagement:Add"])
        ) {
          return path;
        }
        return [];
      },
      path: ["/dg-monitoring/things/add", "/things/add"],
    },
    {
      component: "FinalThingConfigure",
      getPath: (path = []) => {
        if (
          checkMenuAvailability(
            ["/things/", "/dg-monitoring/things/"],
            props.menuUrlArray,
          ) &&
          props.getViewAccess(["ThingManagement:Edit"])
        ) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/customers/:customer_id/applications/:application_id/things/:thing_id/configuration/general",
        "/customers/:customer_id/applications/:application_id/things/:thing_id/configuration/general",
        "/dg-monitoring/customers/:customer_id/applications/:application_id/things/:thing_id/configuration/configuration",
        "/customers/:customer_id/applications/:application_id/things/:thing_id/configuration/configuration",
        "/dg-monitoring/customers/:customer_id/applications/:application_id/things/:thing_id/configuration/info",
        "/customers/:customer_id/applications/:application_id/things/:thing_id/configuration/info",
      ],
    },
    {
      component: "SiteConfiguration",
      getPath: (path = []) => {
        if (
          !checkMenuAvailability(
            ["/dg-monitoring/sites", "/sites"],
            props.menuUrlArray,
          )
        ) {
          return [];
        }
        const filteredRoutes = path.filter((pathUrl) => {
          if (
            pathUrl === "/dg-monitoring/sites/add" ||
            pathUrl === "/sites/add"
          ) {
            return props.getViewAccess(["SiteManagement:Add"]);
          }
          if (
            pathUrl ===
              "/dg-monitoring/customers/:customer_id/sites/:site_id/configuration" ||
            pathUrl === "/customers/:customer_id/sites/:site_id/configuration"
          ) {
            return props.getViewAccess(["SiteManagement:Edit"]);
          }
          return true;
        });
        return filteredRoutes;
      },
      path: [
        "/dg-monitoring/sites/add",
        "/sites/add",
        "/dg-monitoring/customers/:customer_id/sites/:site_id/configuration",
        "/customers/:customer_id/sites/:site_id/configuration",
        "/dg-monitoring/customers/:customer_id/sites/:site_id/details",
        "/customers/:customer_id/sites/:site_id/details",
      ],
    },
    {
      component: "SiteList",
      getPath: (path = []) => {
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: ["/dg-monitoring/sites", "/sites"],
    },
    {
      component: "FinalThingDetails",
      getPath: (path = []) => {
        if (
          checkMenuAvailability(
            ["/things/", "/dg-monitoring/things/"],
            props.menuUrlArray,
          )
        ) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/customers/:customer_id/applications/:application_id/things/:thing_id/thing-details/",
        "/customers/:customer_id/applications/:application_id/things/:thing_id/thing-details/",
      ],
    },
    {
      component: "ThingList",
      getPath: (path = []) => {
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/customers/:customer_id/applications/:application_id/things/:thing_id/thing-details/",
        "/customers/:customer_id/applications/:application_id/things/:thing_id/thing-details/",
        "/dg-monitoring/things/",
        "/things",
        "/dg-monitoring/things/:thing_id/devices/:device_id/debug",
        "/things/:thing_id/devices/:device_id/debug",
      ],
    },
    {
      component: "RuleManagement",
      getPath: (path = []) => {
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: ["/dg-monitoring/alerts", "/alerts"],
    },
    {
      component: "ServiceModule",
      getPath: (path = []) => {
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/work-orders",
        "/work-orders",
        "/dg-monitoring/work-orders/:id/details",
        "/work-orders/:id/details",
      ],
    },
    {
      component: "RemoteCalibration",
      getPath: (path = []) => {
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring/workflow/calibration",
        "/dg-monitoring/workflow/calibration/details",
        "/workflow/calibration",
        "/workflow/calibration/details",
      ],
    },
  ];
  if (props.firstThingCat && parseInt(props.firstThingCat.category) === 42) {
    routes.push(
      {
        component: "EnvDashboard",
        getPath: (path = []) => {
          if (checkMenuAvailability(path, props.menuUrlArray)) {
            return path;
          }
          return [];
        },
        path: ["/environment-monitoring/", "/environment-monitoring/dashboard"],
      },
      {
        component: "UsersManagement",
        getPath: (path = []) => {
          if (checkMenuAvailability(path, props.menuUrlArray)) {
            return path;
          }
          return [];
        },
        path: [
          "/environment-monitoring/user-management/users/view",
          "/environment-monitoring/user-management/users/add",
          "/environment-monitoring/user-management/users/:user_id/edit",
          "/environment-monitoring/user-management/user-groups/view",
          "/environment-monitoring/user-management/user-groups/add",
          "/environment-monitoring/user-management/user-groups/:group_id/edit",
          "/environment-monitoring/user-management/roles/view",
          "/environment-monitoring/user-management/roles/add",
          "/environment-monitoring/user-management/roles/:role_id/edit",
        ],
      },
      {
        component: "CustomReport",
        getPath: (path = []) => {
          if (checkMenuAvailability(path, props.menuUrlArray)) {
            return path;
          }
          return [];
        },
        path: ["/environment-monitoring/reports/custom-reports"],
      },
      {
        component: "RuleManagement",
        getPath: (path = []) => {
          if (checkMenuAvailability(path, props.menuUrlArray)) {
            return path;
          }
          return [];
        },
        path: ["/dg-monitoring/alerts", "/alerts"],
      },
      {
        component: "DgStatusReport",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/asset-status-report",
          "/reports/asset-status-report",
        ],
      },
      {
        component: "UserNotification",
        getPath: (path = []) => {
          if (checkMenuAvailability(path, props.menuUrlArray)) {
            return path;
          }
          return [];
        },
        path: ["/environment-monitoring/user-notifications"],
      },
    );
  } else if (
    props.firstThingCat &&
    parseInt(props.firstThingCat.category) === 78
  ) {
    routes.push(
      {
        component: "ElectricalMachinesMapView",
        getPath: (path = []) => {
          if (checkMenuAvailability(path, props.menuUrlArray)) {
            return path;
          }
          return [];
        },
        path: ["/dg-monitoring", "/", "/dg-monitoring/map-view", "/map-view"],
      },
      {
        component: "ElectricalMachinesRealTime",
        getPath: (path = []) => {
          if (checkMenuAvailability(path, props.menuUrlArray)) {
            return path;
          }
          return [];
        },
        path: ["/dg-monitoring/real-time", "/real-time"],
      },
    );
  } else if (
    props.firstThingCat &&
    (parseInt(props.firstThingCat.category) === 67 ||
      parseInt(props.firstThingCat.category) === 76)
  ) {
    routes.push(
      {
        component: "FleetMapView",
        getPath: (path = []) => {
          if (checkMenuAvailability(path, props.menuUrlArray)) {
            return path;
          }
          return [];
        },
        path: ["/dg-monitoring", "/", "/dg-monitoring/map-view", "/map-view"],
      },
      {
        component: "LifetimeReports",
        getPath: (path = []) => {
          return parseInt(props.vendor_id) !== 1140 ? path : [];
        },
        path: [
          "/dg-monitoring/reports/lifetime-reports",
          "/reports/lifetime-reports",
        ],
      },
      {
        component: "FleetDailyReports",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/fleet/daily-reports",
          "/reports/fleet/daily-reports",
        ],
      },
      {
        component: "FleetTripReports",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/fleet/trip-reports",
          "/reports/fleet/trip-reports",
        ],
      },
      {
        component: "FleetTripDetails",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/fleet/trip-reports/details",
          "/reports/fleet/trip-reports/details",
        ],
      },
    );
  } else if (
    props.firstThingCat &&
    parseInt(props.firstThingCat.category) === 77
  ) {
    routes.push(
      {
        component: "EnergyMeterMapView",
        getPath: (path = []) => {
          if (checkMenuAvailability(path, props.menuUrlArray)) {
            return path;
          }
          return [];
        },
        path: ["/dg-monitoring", "/", "/dg-monitoring/map-view", "/map-view"],
      },
      {
        component: "EnergyMeterDailyReport",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/energy-meter/daily-reports",
          "/reports/energy-meter/daily-reports",
          "/dg-monitoring/reports/inverter/daily-reports",
          "/reports/inverter/daily-reports",
        ],
      },
      {
        component: "SolarPowerDailyReport",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/solar-power/daily-reports",
          "/reports/solar-power/daily-reports",
        ],
      },
      {
        component: "ProcessAnalyzerDailyReport",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/process-analyzer/daily-reports",
          "/reports/process-analyzer/daily-reports",
        ],
      },
      {
        component: "ProcessAnalyzerFaultReport",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/process-analyzer/fault-reports",
          "/reports/process-analyzer/fault-reports",
        ],
      },
      {
        component: "SolarPowerTripReport",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/solar-power/trip-reports",
          "/reports/solar-power/trip-reports",
        ],
      },
      {
        component: "SolarPowerMultiAssetReport",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/solar-power/multi-asset-reports",
          "/reports/solar-power/multi-asset-report",
        ],
      },
      {
        component: "AcEnergyDailyReport",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/ac-energy-meter/daily-reports",
          "/reports/ac-energy-meter/daily-reports",
        ],
      },
      {
        component: "GridEnergyMeterDailyReport",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/grid-energy-meter/daily-reports",
          "/reports/grid-energy-meter/daily-reports",
        ],
      },
      {
        component: "AcEnergyMultiAsserReport",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/ac-energy-meter/multi-asset-reports",
          "/reports/ac-energy-meter/multi-asset-reports",
        ],
      },
      {
        component: "GridEnergyMeterMultiAssetReport",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/grid-energy-meter/multi-asset-reports",
          "/reports/grid-energy-meter/multi-asset-reports",
        ],
      },
      {
        component: "AcElectricalMachinesDailyReport",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/ac-energy-meter/daily-reports",
          "/reports/ac-energy-meter/daily-reports",
        ],
      },
      {
        component: "AcElectricalMachinesTripReport",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/ac-energy-meter/trip-reports",
          "/reports/ac-energy-meter/trip-reports",
        ],
      },
      {
        component: "AcElectricalMachinesMultiAsserReport",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/ac-energy-meter/multi-asset-reports",
          "/reports/ac-energy-meter/multi-asset-reports",
        ],
      },
      {
        component: "ExhaustFanDailyReport",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/exhaust-fan/daily-reports",
          "/reports/exhaust-fan/daily-reports",
        ],
      },
      {
        component: "ExhaustFanTripReport",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/exhaust-fan/trip-reports",
          "/reports/exhaust-fan/trip-reports",
        ],
      },
      {
        component: "ExhaustFanMultiAsserReport",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/exhaust-fan/multi-asset-reports",
          "/reports/exhaust-fan/multi-asset-reports",
        ],
      },
      {
        component: "TempHumidDailyReport",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/temp-humid/daily-reports",
          "/reports/temp-humid/daily-reports",
        ],
      },
      {
        component: "EnergyMeterAvailabilityReport",
        getPath: (path = []) => {
          return dcpath;
        },
        path: [
          "/dg-monitoring/reports/energy-meter/availability-reports",
          "/reports/energy-meter/availability-reports",
        ],
      },
    );
  } else if (
    props.firstThingCat &&
    parseInt(props.firstThingCat.category) === 74
  ) {
    routes.push(
      {
        component: "TankerMapView",
        getPath: (path = []) => {
          if (checkMenuAvailability(path, props.menuUrlArray)) {
            return path;
          }
          return [];
        },
        path: ["/dg-monitoring", "/", "/dg-monitoring/map-view", "/map-view"],
      },
      {
        component: "FleetDailyReports",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/fleet/daily-reports",
          "/reports/fleet/daily-reports",
        ],
      },
      {
        component: "FleetTripReports",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/fleet/trip-reports",
          "/reports/fleet/trip-reports",
        ],
      },
      {
        component: "FleetTripDetails",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/fleet/trip-reports/details",
          "/reports/fleet/trip-reports/details",
        ],
      },
      {
        component: "TankerTruckFuelTankerDailyReports",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/tanker-truck/daily-reports",
          "/reports/tanker-truck/daily-reports",
        ],
      },
      {
        component: "TankerTruckVehicleDailyReports",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/vehicle/daily-reports",
          "/reports/vehicle/daily-reports",
        ],
      },
      {
        component: "TankerTruckFuelFilledDispenseReports",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/fuel-fill-dispense",
          "/reports/fuel-fill-dispense",
        ],
      },
      {
        component: "TankerTruckTripReports",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/tanker-truck/trip-reports",
          "/reports/tanker-truck/trip-reports",
        ],
      },
      {
        component: "TankerTruckTripDetails",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/tanker-truck/trip-reports/details",
          "/reports/tanker-truck/trip-reports/details",
        ],
      },
    );
  } else if (
    props.firstThingCat &&
    parseInt(props.firstThingCat.category) === 73
  ) {
    routes.push(
      {
        component: "DgMapView",
        getPath: (path = []) => {
          return path;
        },
        path: ["/dg-monitoring", "/", "/dg-monitoring/map-view", "/map-view"],
      },
      {
        component: "CompDashboard",
        getPath: (path = []) => {
          return path;
        },
        path: ["/dg-monitoring/dashboard", "/dashboard"],
      },
      {
        component: "CompDetailedView",
        getPath: (path = []) => {
          return path;
        },
        path: ["/dg-monitoring/detailed-view", "/detailed-view"],
      },
      {
        component: "CompDailyReports",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/compressor/daily-reports",
          "/reports/compressor/daily-reports",
        ],
      },
      {
        component: "CompDgRunReports",
        getPath: (path = []) => {
          return path;
        },
        path: [
          "/dg-monitoring/reports/compressor/run-reports",
          "/reports/compressor/run-reports",
        ],
      },
      {
        component: "DGFaultReport",
        getPath: (path = []) => {
          return path;
        },
        path: ["/dg-monitoring/reports/fault-reports", "/reports/fault-reports"],
      },
      {
        component: "GasGensetFaultReport",
        getPath: (path = []) => {
          return path;
        },
        path: ["reports/gas-genset/fault-reports", "/dg-monitoring/reports/gas-genset/fault-reports"],
      },
    );
  } else if (
    props.firstThingCat &&
    (parseInt(props.firstThingCat.category) === 82 ||
      parseInt(props.firstThingCat.category) === 84)
  ) {
    routes.push({
      component: "DeliveryDashboard",
      getPath: (path = []) => {
        if (checkMenuAvailability(path, props.menuUrlArray)) {
          return path;
        }
        return [];
      },
      path: [
        "/dg-monitoring",
        "/",
        "/dg-monitoring/dashboard",
        "/dashboard",
        "/dg-monitoring/dashboard/:id/details",
      ],
    });
  } else {
    if (parseInt(props.template_id) === 19) {
      routes.push(
        {
          component: "DGDashboard",
          getPath: (path = []) => {
            if (checkMenuAvailability(path, props.menuUrlArray)) {
              return path;
            }
            return [];
          },
          path: [
            "/dg-monitoring",
            "/",
            "/dg-monitoring/dashboard",
            "/dashboard",
          ],
        },
        {
          component: "CellTowerDetailedView",
          getPath: (path = []) => {
            if (checkMenuAvailability(path, props.menuUrlArray)) {
              return path;
            }
            return [];
          },
          path: ["/dg-monitoring/detailed-view", "/detailed-view"],
        },
        {
          component: "DGPanel",
          getPath: (path = []) => {
            if (checkMenuAvailability(path, props.menuUrlArray)) {
              return path;
            }
            return [];
          },
          path: ["/dg-monitoring/panel-view", "/panel-view"],
        },
        {
          component: "DgDailyReport",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/template-reports",
            "/reports/template-reports",
          ],
        },
        {
          component: "GasGensetDailyReports",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/gas-genset/daily-report",
            "/reports/gas-genset/daily-report",
          ],
        },
        {
          component: "GasGensetMultiAssetReports",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/gas-genset/multi-asset-reports",
            "/reports/gas-genset/multi-asset-reports",
          ],
        },
        {
          component: "DGFaultReport",
          getPath: (path = []) => {
            return path;
          },
          path: ["/dg-monitoring/reports/fault-reports", "/reports/fault-reports"],
        },
        {
          component: "GasGensetFaultReport",
          getPath: (path = []) => {
            return path;
          },
          path: ["reports/gas-genset/fault-reports", "/dg-monitoring/reports/gas-genset/fault-reports"],
        },
        {
          component: "CriticalReports",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/critical-trends-reports",
            "/reports/critical-trends-reports",
          ],
        },
        {
          component: "DgRunReports",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/dg-run-reports",
            "/reports/dg-run-reports",
          ],
        },
        {
          component: "GasGensetTripReport",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/reports/gas-genset/run-reports",
            "/dg-monitoring/reports/gas-genset/run-reports",
          ],
        },
        {
          component: "DGMultiAssetReport",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/dg-multi-assets-report",
            "/reports/dg-multi-assets-report",
          ],
        },
        {
          component: "DGSnapshotReport",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/dg-snapshot-report",
            "/reports/dg-snapshot-report",
          ],
        },
      );
    } else if (parseInt(props.template_id) === 24) {
      routes.push(
        {
          component: "WithoutFuelDashboard",
          getPath: (path = []) => {
            if (checkMenuAvailability(path, props.menuUrlArray)) {
              return path;
            }
            return [];
          },
          path: [
            "/dg-monitoring",
            "/",
            "/dg-monitoring/dashboard",
            "/dashboard",
          ],
        },
        {
          component: "WithoutFuelDetailedView",
          getPath: (path = []) => {
            if (checkMenuAvailability(path, props.menuUrlArray)) {
              return path;
            }
            return [];
          },
          path: ["/dg-monitoring/detailed-view", "/detailed-view"],
        },
        {
          component: "WithoutFuelPanel",
          getPath: (path = []) => {
            if (checkMenuAvailability(path, props.menuUrlArray)) {
              return path;
            }
            return [];
          },
          path: ["/dg-monitoring/panel-view", "/panel-view"],
        },
        {
          component: "WithoutFuelRealTime",
          getPath: (path = []) => {
            if (checkMenuAvailability(path, props.menuUrlArray)) {
              return path;
            }
            return [];
          },
          path: ["/dg-monitoring/real-time", "/real-time"],
        },
        {
          component: "DGFaultReport",
          getPath: (path = []) => {
            return path;
          },
          path: ["/dg-monitoring/reports/fault-reports", "/reports/fault-reports"],
        },
        {
          component: "GasGensetFaultReport",
          getPath: (path = []) => {
            return path;
          },
          path: ["reports/gas-genset/fault-reports", "/dg-monitoring/reports/gas-genset/fault-reports"],
        },
        {
          component: "DgDailyReport",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/template-reports",
            "/reports/template-reports",
          ],
        },
        {
          component: "GasGensetDailyReports",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/gas-genset/daily-report",
            "/reports/gas-genset/daily-report",
          ],
        },
        {
          component: "GasGensetMultiAssetReports",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/gas-genset/multi-asset-reports",
            "/reports/gas-genset/multi-asset-reports",
          ],
        },
        {
          component: "CriticalReports",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/critical-trends-reports",
            "/reports/critical-trends-reports",
          ],
        },
        {
          component: "DgRunReports",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/dg-run-reports",
            "/reports/dg-run-reports",
          ],
        },
        {
          component: "GasGensetTripReport",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/reports/gas-genset/run-reports",
            "/dg-monitoring/reports/gas-genset/run-reports",
          ],
        },
        {
          component: "DGMultiAssetReport",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/dg-multi-assets-report",
            "/reports/dg-multi-assets-report",
          ],
        },
        {
          component: "DGSnapshotReport",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/dg-snapshot-report",
            "/reports/dg-snapshot-report",
          ],
        },
        {
          component: "DGMorePage",
          getPath: (path = []) => {
            // menuPages
            if (window.innerWidth < 576) {
              return path;
            }
            return [];
          },
          path: ["/dg-monitoring/more", "/more"],
        },
      );
    } else if (parseInt(props.template_id) === 21) {
      routes.push(
        {
          component: "FuelSensorDashboard",
          getPath: (path = []) => {
            if (checkMenuAvailability(path, props.menuUrlArray)) {
              return path;
            }
            return [];
          },
          path: [
            "/dg-monitoring",
            "/",
            "/dg-monitoring/dashboard",
            "/dashboard",
          ],
        },
        {
          component: "FuelSensorDetailedView",
          getPath: (path = []) => {
            if (checkMenuAvailability(path, props.menuUrlArray)) {
              return path;
            }
            return [];
          },
          path: ["/dg-monitoring/detailed-view", "/detailed-view"],
        },
        {
          component: "FuelSensorPanel",
          getPath: (path = []) => {
            if (checkMenuAvailability(path, props.menuUrlArray)) {
              return path;
            }
            return [];
          },
          path: ["/dg-monitoring/panel-view", "/panel-view"],
        },
        {
          component: "DGFaultReport",
          getPath: (path = []) => {
            return path;
          },
          path: ["/dg-monitoring/reports/fault-reports", "/reports/fault-reports"],
        },
        {
          component: "GasGensetFaultReport",
          getPath: (path = []) => {
            return path;
          },
          path: ["reports/gas-genset/fault-reports", "/dg-monitoring/reports/gas-genset/fault-reports"],
        },
        {
          component: "DgDailyReport",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/template-reports",
            "/reports/template-reports",
          ],
        },
        {
          component: "GasGensetDailyReports",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/gas-genset/daily-report",
            "/reports/gas-genset/daily-report",
          ],
        },
        {
          component: "GasGensetMultiAssetReports",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/gas-genset/multi-asset-reports",
            "/reports/gas-genset/multi-asset-reports",
          ],
        },
        {
          component: "CriticalReports",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/critical-trends-reports",
            "/reports/critical-trends-reports",
          ],
        },
        {
          component: "DgRunReports",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/dg-run-reports",
            "/reports/dg-run-reports",
          ],
        },
        {
          component: "GasGensetTripReport",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/reports/gas-genset/run-reports",
            "/dg-monitoring/reports/gas-genset/run-reports",
          ],
        },
        {
          component: "DGMultiAssetReport",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/dg-multi-assets-report",
            "/reports/dg-multi-assets-report",
          ],
        },
        {
          component: "DGSnapshotReport",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/dg-snapshot-report",
            "/reports/dg-snapshot-report",
          ],
        },
      );
    } else {
      routes.push(
        {
          component: "DgMapView",
          getPath: (path = []) => {
            if (checkMenuAvailability(path, props.menuUrlArray)) {
              return path;
            }
            return [];
          },
          path: ["/dg-monitoring", "/", "/dg-monitoring/map-view", "/map-view"],
        },
        {
          component: "DGDashboard",
          getPath: (path = []) => {
            if (checkMenuAvailability(path, props.menuUrlArray)) {
              return path;
            }
            return [];
          },
          path: ["/dg-monitoring/dashboard", "/dashboard"],
        },
        {
          component: "AssetDashboard",
          getPath: (path = []) => {
            if (checkMenuAvailability(path, props.menuUrlArray)) {
              return path;
            }
            return [];
          },
          path: ["/dg-monitoring/asset-dashboard", "/asset-dashboard"],
        },
        {
          component: "DGDetailedView",
          getPath: (path = []) => {
            if (checkMenuAvailability(path, props.menuUrlArray)) {
              return path;
            }
            return [];
          },
          path: ["/dg-monitoring/detailed-view", "/detailed-view"],
        },
        {
          component: "FinalPanelView",
          getPath: (path = []) => {
            if (checkMenuAvailability(path, props.menuUrlArray)) {
              return path;
            }
            return [];
          },
          path: ["/dg-monitoring/panel-view", "/panel-view"],
        },
        {
          component: "DGRealTime",
          getPath: (path = []) => {
            if (checkMenuAvailability(path, props.menuUrlArray)) {
              return path;
            }
            return [];
          },
          path: ["/dg-monitoring/real-time", "/real-time"],
        },
        {
          component: "DGFaultReport",
          getPath: (path = []) => {
            return path;
          },
          path: ["/dg-monitoring/reports/fault-reports", "/reports/fault-reports"],
        },
        {
          component: "GasGensetFaultReport",
          getPath: (path = []) => {
            return path;
          },
          path: ["reports/gas-genset/fault-reports", "/dg-monitoring/reports/gas-genset/fault-reports"],
        },
        {
          component: "FuelFillDrainReport",
          getPath: (path = []) => {
            return parseInt(props.vendor_id) !== 1140 ? path : [];
          },
          path: [
            "/dg-monitoring/reports/fuel-fill-drain",
            "/reports/fuel-fill-drain",
          ],
        },
        {
          component: "FuelTankFillReport",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/fuel-tank-fill",
            "/reports/fuel-tank-fill",
          ],
        },
        {
          component: "DailyReports",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/template-reports",
            "/reports/template-reports",
          ],
        },
        {
          component: "FuelTankDailyReports",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/fuel-tank/daily-reports",
            "/reports/fuel-tank/daily-reports",
          ],
        },
        {
          component: "FuelTankSnapShotReports",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/fuel-tank/snapshot-reports",
            "/reports/fuel-tank/snapshot-reports",
          ],
        },
        {
          component: "CriticalReports",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/critical-trends-reports",
            "/reports/critical-trends-reports",
          ],
        },
        {
          component: "DgRunReports",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/dg-run-reports",
            "/reports/dg-run-reports",
          ],
        },
        {
          component: "GasGensetTripReport",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/reports/gas-genset/run-reports",
            "/dg-monitoring/reports/gas-genset/run-reports",
          ],
        },
        {
          component: "DGMultiAssetReport",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/dg-multi-assets-report",
            "/reports/dg-multi-assets-report",
          ],
        },
        {
          component: "DGSnapshotReport",
          getPath: (path = []) => {
            return path;
          },
          path: [
            "/dg-monitoring/reports/dg-snapshot-report",
            "/reports/dg-snapshot-report",
          ],
        },
        {
          component: "ParameterReports",
          getPath: (path = []) => {
            return parseInt(props.vendor_id) !== 1140 ? path : [];
          },
          path: [
            "/dg-monitoring/reports/parameter-reports",
            "/reports/parameter-reports",
          ],
        },
        {
          component: "LifetimeReports",
          getPath: (path = []) => {
            return parseInt(props.vendor_id) !== 1140 ? path : [];
          },
          path: [
            "/dg-monitoring/reports/lifetime-reports",
            "/reports/lifetime-reports",
          ],
        },
        {
          component: "TripView",
          getPath: (path = []) => {
            if (checkMenuAvailability(path, props.menuUrlArray)) {
              return path;
            }
            return [];
          },
          path: [
            "/dg-monitoring/trip-view/:pageType",
            "/dg-monitoring/trip-view",
            "/dg-monitoring/trip-view/:pageType/:trip_id/details",
            "/trip-view/:pageType",
            "/trip-view",
            "/trip-view/:pageType/:trip_id/details",
          ],
        },
        {
          component: "DGMorePage",
          getPath: (path = []) => {
            // menuPages
            if (window.innerWidth < 576) {
              return path;
            }
            return [];
          },
          path: ["/dg-monitoring/more", "/more"],
        },
      );
    }
    if (props.logged_in_user_role_type === 6) {
      routes = [
        {
          component: "ServiceModule",
          getPath: (path = []) => {
            if (checkMenuAvailability(path, props.menuUrlArray)) {
              return path;
            }
            return [];
          },
          path: ["/dg-monitoring/work-orders", "/work-orders"],
        },
      ];
    }
  }
  return routes;
};
