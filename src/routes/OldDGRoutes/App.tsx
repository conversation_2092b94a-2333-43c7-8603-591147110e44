import React from "react";
import { Route } from "react-router-dom";
import MachineInfoProps from "../MachineInfoProps";
import { getRoutesMapping } from "./routes";
import { JSX } from "react/jsx-runtime";

const TripView = React.lazy(
  () =>
    import("@datoms/dg-monitoring-views/src/js/GenericTemplate/pages/TripView"),
);
const TerritoryPage = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-user-management/src/components/TerritoryPage"
    ),
);
const UsersManagement = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-user-management/src/components/UsersManagement"
    ),
);
const AssignedDevice = React.lazy(
  () =>
    import(
      /* webpackChunkName: "AssignedDevice"*/ "../../packages/webapp-component-device-management/src/components/AssignedDevice"
    ),
);
const UnassignedDevice = React.lazy(
  () =>
    import(
      /* webpackChunkName: "UnassignedDevice"*/ "../../packages/webapp-component-device-management/src/components/UnassignedDevice"
    ),
);
const DeviceCalibration = React.lazy(
  () =>
    import(
      /* webpackChunkName: "DeviceCalibration"*/ "../../packages/webapp-component-device-management/src/components/DeviceCalibration"
    ),
);
const DeviceCommunication = React.lazy(
  () =>
    import(
      /* webpackChunkName: "DeviceCommunication"*/ "../../packages/webapp-component-device-management/src/components/DeviceCommunication"
    ),
);
const DeviceCustomCommand = React.lazy(
  () =>
    import(
      /* webpackChunkName: "DeviceCustomCommand"*/ "../../packages/webapp-component-device-management/src/components/DeviceCustomCommand"
    ),
);
const DeviceConfiguration = React.lazy(
  () =>
    import(
      /* webpackChunkName: "DeviceConfiguration"*/ "../../packages/webapp-component-device-management/src/components/DeviceConfiguration"
    ),
);
const DeviceHealth = React.lazy(
  () =>
    import(
      /* webpackChunkName: "DeviceHealth"*/ "../../packages/webapp-component-device-management/src/components/DeviceHealth"
    ),
);
const RuleManagement = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-alerts-management/src/components/RuleManagement"
    ),
);
const ServiceModule = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-alerts-management/src/components/ServiceModule"
    ),
);
const ThingList = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-thing-management/src/components/ThingList"
    ),
);
const ThingConfigurationV2 = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-thing-management/src/components/ThingAddV2/index"
    ),
);
const SiteList = React.lazy(
  () =>
    import(
      /* webpackChunkName: "SiteList"*/ "../../packages/webapp-component-thing-management/src/components/SiteList"
    ),
);
const SiteConfiguration = React.lazy(
  () =>
    import(
      /* webpackChunkName: "SiteConfiguration"*/ "../../packages/webapp-component-thing-management/src/components/SiteConfiguration"
    ),
);
const ThingDetails = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-thing-management/src/components/ThingDetails/ThingDetails"
    ),
);
const CustomReport = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-reports/src/components/CustomReport"
    ),
);
const DataAvailabilityReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/DataAvailabilityReport"
    ),
);
const DgStatusReport = React.lazy(
  () =>
    import("../../packages/iot-platform-views/src/js/reports/DgStatusReport"),
);
/*DG*/
const DGDetailedView = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/DGMonitoring/Template-17/DetailedView"
    ),
);
const DGDashboard = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/DGMonitoring/Template-17/Dashboard"
    ),
);
const AssetDashboard = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/GenericTemplate/pages/AssetDashboard"
    ),
);
const RemoteCalibration = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/GenericTemplate/pages/RemoteCalibration"
    ),
);
const DGRealTime = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/DGMonitoring/Template-17/RealTime"
    ),
);
const DGPanel = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/DGMonitoring/Template-17/Panel"
    ),
);
const DgMapView = React.lazy(
  () => import("../../packages/dg-monitoring-views/src/js/components/MapView"),
);
/*Cell Tower*/
const CellTowerDetailedView = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/DGMonitoring/Template-19/DetailedView"
    ),
);
/*fuel Sensor Monitoring*/
const FuelSensorDashboard = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/DGMonitoring/Template 21/Dashboard"
    ),
);
const FuelSensorDetailedView = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/DGMonitoring/Template 21/DetailedView"
    ),
);
const FuelSensorPanel = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/DGMonitoring/Template 21/Panel"
    ),
);
/*without fuel Monitoring*/
const WithoutFuelDetailedView = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/DGMonitoring/Template-24/DetailedView"
    ),
);
const WithoutFuelDashboard = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/DGMonitoring/Template-24/Dashboard"
    ),
);
const WithoutFuelRealTime = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/DGMonitoring/Template-24/Realtime"
    ),
);
const WithoutFuelPanel = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/DGMonitoring/Template-24/Panel"
    ),
);
/*Compressor */
const CompDetailedView = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/DGMonitoring/Template-25/pages/DetailedView"
    ),
);
const CompDashboard = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/DGMonitoring/Template-25/pages/Dashboard"
    ),
);
/*Electrical Machines*/
const ElectricalMachinesRealTime = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/ElectricalMachines/js/Realtime"
    ),
);
const ElectricalMachinesMapView = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/ElectricalMachines/js/MapView"
    ),
);
/*Fleet*/
const FleetMapView = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/Fleet/js/VehicleTracking/MapView"
    ),
);
/*Tanker*/
const TankerMapView = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/Fleet/js/FuelTanker/MapView"
    ),
);
/*Energy Meter*/
const EnergyMeterMapView = React.lazy(
  () =>
    import("../../packages/dg-monitoring-views/src/js/EnergyMeter/js/MapView"),
);
/*Reports */
const ReportTemplates = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/ReportTemplates"
    ),
);
const SummaryReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/SummaryReports"
    ),
);
const YesterdayDailyFaultReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/YesterdayDailyFaultReport"
    ),
);
/*Dg Set */
const DgDailyReport = React.lazy(
  () =>
    import(
      "../../containers/reports/DailyReports/DgDailyReport"
    ),
);
const GasGensetDailyReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/DGReport/pages/DailyReports"
    ),
);
const GasGensetMultiAssetReports = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/GasGensetMultiAssetReport"
    ),
);
const CriticalReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/CriticalTrendsReport"
    ),
);
const DGMultiAssetReport = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/DgMultiAssetReport"
    ),
);
const DGSnapshotReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/DGReport/pages/SnapshotReport"
    ),
);
const DgRunReports = React.lazy(
  () =>
    import(
      "../../containers/reports/TripReports/DgTripReport"
    ),
);
const GasGensetTripReport = React.lazy(
  () =>
    import(
      "../../containers/reports/TripReports/GasGensetTripReport"
    ),
);
const DGFaultReport = React.lazy(
  () =>
    import(
      "../../containers/reports/FaultReports/DgFaultReport"
    ),
);
const GasGensetFaultReport = React.lazy(
  () =>
    import(
      "../../containers/reports/FaultReports/GasGensetFaultReport"
    ),
);
const FuelFillDrainReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/dg-new-reports/pages/FuelFillDrainReport"
    ),
);
/*summary reports */
const LifetimeReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/summary-reports/pages/LifetimeReports"
    ),
);
const ParameterReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/summary-reports/pages/ParameterReports"
    ),
);
/*Fuel Tank */
const FuelTankDailyReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fuel-tank-reports/pages/DailyReports"
    ),
);
const FuelTankFillReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fuel-tank-reports/pages/FuelTankFillReport"
    ),
);
const FuelTankSnapShotReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fuel-tank-reports/pages/SnapshotReports"
    ),
);
/*Compressor */
const CompDailyReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/compressor-reports/DailyReports"
    ),
);
const CompDgRunReports = React.lazy(
  () =>
    import(
      "../../containers/reports/TripReports/CompressorTripReport"
    ),
);
/*Fleet */
const FleetDailyReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/FleetReports/DailyReports"
    ),
);
const FleetTripReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/FleetReports/TripReports"
    ),
);
const FleetTripDetails = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/FleetReports/TripDetails"
    ),
);

/*Tanker Truck*/
const TankerTruckFuelTankerDailyReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/TankerTruckDailyReports/FuelTankerDailyReports"
    ),
);

const TankerTruckVehicleDailyReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/TankerTruckDailyReports/VehicleReports"
    ),
);

const TankerTruckFuelFilledDispenseReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/TankerTruckDailyReports/FuelFilledDispenseReports"
    ),
);
const TankerTruckTripReports = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/TankerTruckDailyReports/TripReports"
    ),
);
const TankerTruckTripDetails = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/TankerTruckDailyReports/TripDetails"
    ),
);

const AlertSubscriptionThingsReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/alerSubscription/pages"
    ),
);
/*Energy Meter */
const EnergyMeterDailyReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/energy-meter-reports/pages/DailyReports"
    ),
);
const EnergyMeterAvailabilityReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/energy-meter-reports/pages/AvailabilityReport"
    ),
);
const SolarPowerDailyReport = React.lazy(
  () =>
    import(
      "../../containers/reports/DailyReports/SolarPowerDailyReport"
    ),
);

const ProcessAnalyzerDailyReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/process-analyzer-reports/pages/DailyReports"
    ),
);

const ProcessAnalyzerFaultReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/process-analyzer-reports/pages/FaultReport"
    ),
);

const SolarPowerTripReport = React.lazy(
  () =>
    import(
      "../../containers/reports/TripReports/SolarPowerTripReport"
    ),
);
const SolarPowerMultiAssetReport = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/SolarPowerMultiAssetReport"
    ),
);
const AcEnergyDailyReport = React.lazy(
  () =>
    import(
      "../../containers/reports/DailyReports/AcEnergyMeterDailyReport"
    ),
);
const AcEnergyMultiAsserReport = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/AcEnergyMeterMultiAssetReport"
    ),
);
const GridEnergyMeterDailyReport = React.lazy(
  () =>
    import(
      "../../containers/reports/DailyReports/GridEnergyMeterDailyReport"
    ),
);
const GridEnergyMeterMultiAssetReport = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/GridEnergyMeterMultiAssetReport"
    ),
);
const AcElectricalMachinesDailyReport = React.lazy(
  () =>
    import(
      "../../containers/reports/DailyReports/AcElectricalMachineDailyReport"
    ),
);
const AcElectricalMachinesTripReport = React.lazy(
  () =>
    import(
      "../../containers/reports/TripReports/AcElectricalMachineTripReport"
    ),
);
const AcElectricalMachinesMultiAsserReport = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/AcElectricalMachineMultiAssetReport"
    ),
);
const ExhaustFanDailyReport = React.lazy(
  () =>
    import(
      "../../containers/reports/DailyReports/ExhaustFanDailyReport"
    ),
);
const ExhaustFanTripReport = React.lazy(
  () =>
    import(
      "../../containers/reports/TripReports/ExhaustFanTripReport"
    ),
);
const ExhaustFanMultiAsserReport = React.lazy(
  () =>
    import(
      "../../containers/reports/MultiAssetReports/ExhaustFanMultiAssetReport"
    ),
);
const TempHumidDailyReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/temp-humid-report/pages/DailyReports"
    ),
);
const GensetFuelTankSiteReport = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/features/Reports/DGReport/pages/GensetFuelTankSiteReport"
    ),
);
// /*Environment */
// const EnvDashboard = React.lazy(() =>
//   import("../EnvironmentMonitoring/Template-20/Dashboard")
// );
const UserNotification = React.lazy(
  () =>
    import(
      "../../packages/webapp-component-user-notifications/src/components/UserFullNotification"
    ),
);
/*Warehouse */
const Warehouse = React.lazy(
  () => import("../../packages/dg-monitoring-views/src/js/Warehouse"),
);
const SitePanel = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/Warehouse/components/SitePanel"
    ),
);
const DeliveryDashboard = React.lazy(
  () =>
    import(
      "../../packages/dg-monitoring-views/src/js/DeliveryTracking/DashboardComponent/page"
    ),
);
const SiteDashboard = React.lazy(
  () => import("../../packages/dg-monitoring-views/src/js/SiteDashboard"),
);
/* Settings */
const Settings = React.lazy(
  () => import("@datoms/datoms-settings/src/index.js"),
);
const DatomsPolicy = React.lazy(
  () => import("@datoms/datoms-settings/src/pages/DatomsPolicy"),
);

const TicketingSupport = React.lazy(
  () => import("../../containers/TicketingSupport"),
);

const DGMorePage = React.lazy(
  () => import("@datoms/dg-monitoring-views/src/js/components/DGMorePage"),
);

function singleAppProjectRoutes(
  client_id: number,
  head_side_object_data: any,
  menuUrlArray: any,
  AlertObjectData: {
    buttons: { text: string; value: string; primary: boolean }[];
    status_btn: { text: string; value: string; primary: boolean }[];
    show_menu: boolean;
    application_menu: {
      head_text: string[];
      menu_items: { key: string; page_name: string; menu_value: number }[];
      bg_color: string;
      selected_menu_color: string;
    };
    thing_category_select: {
      label: string[];
      select_option: {
        show_arrow: boolean;
        default_value: string;
        options: never[];
      }[];
    };
    alert_table: {
      action_button: {
        icon_type: string;
        text: string;
        show_action_button: boolean;
      };
      config: {
        pagination_data: {
          size: string;
          total: number;
          pageSize: number;
          showSizeChanger: boolean;
          showQuickJumper: boolean;
          hideOnSinglePage: boolean;
        };
        bordered: boolean;
        size: string;
        showHeader: boolean;
        rowSelect: boolean;
        scroll: { y: number };
        loading: boolean;
        locale: {
          filterTitle: string;
          filterConfirm: string;
          filterReset: string;
          emptyText: string;
        };
      };
      head_data: (
        | {
            title: JSX.Element;
            dataIndex: string;
            key: string;
            render: (value: any, row_data: any, index: any) => JSX.Element;
            width: string;
            align?: undefined;
          }
        | {
            title: JSX.Element;
            key: string;
            align: string;
            render: (value: any, row_data: any, index: any) => JSX.Element;
            width: string;
            dataIndex?: undefined;
          }
      )[];
      row_data: never[];
    };
  },
  template_id: number,
  getViewAccess: any,
  getRemoteAccess: any,
  getRemoteLockAccess: any,
  enabled_features: string | string[],
  plan_description: {} | undefined,
  props: any,
  application_id: number,
  application_name: string,
  client_name: string,
  client_logo: string,
  vendor_logo: string,
  user_id: string | number,
  user_preferences: { timezone: any },
  collapsed: boolean,
  onCollapseSider: any,
  t: any,
  globalConfig: any,
  app_name: string,
  vendor_id: string | number,
  logged_in_user_client_id: string | number,
  logged_in_user_role_type: string,
  authToken: string,
  checkCustomerFuelBuddy: any,
  is_white_label: boolean,
  vendor_name: string,
  loadingLogo: boolean,
  firstThingCat: any,
  allApps: string[],
  currentUserPreferences: any,
  updateCurrentUserPreference: any,
  thing_list: any,
) {
  const FinalThingAdd = ThingConfigurationV2,
    FinalThingDetails = window.innerWidth > 576 ? ThingList : ThingDetails,
    isScheduledReport = enabled_features?.includes("Reports:Scheduled"),
    FinalThingConfigure = ThingConfigurationV2;
  const FinalPanelView = client_id === 1381 ? SitePanel : DGPanel;

  const page_name_map = {
    Settings,
    DatomsPolicy,
    SiteDashboard,
    Warehouse,
    UserNotification,
    TerritoryPage,
    UsersManagement,
    CustomReport,
    DataAvailabilityReport,
    DgStatusReport,
    ReportTemplates,
    SummaryReports,
    YesterdayDailyFaultReport,
    LifetimeReports,
    FleetDailyReports,
    DgDailyReport,
    GasGensetDailyReports,
    GasGensetMultiAssetReports,
    DGMultiAssetReport,
    DGSnapshotReport,
    FleetTripReports,
    TankerTruckTripReports,
    FleetTripDetails,
    TankerTruckTripDetails,
    AlertSubscriptionThingsReport,    
    EnergyMeterDailyReport,
    SolarPowerDailyReport,
    ProcessAnalyzerDailyReport,
    ProcessAnalyzerFaultReport,
    SolarPowerTripReport,
    SolarPowerMultiAssetReport,
    AcEnergyDailyReport,
    GridEnergyMeterDailyReport,
    AcEnergyMultiAsserReport,
    GridEnergyMeterMultiAssetReport,
    AcElectricalMachinesDailyReport,
    AcElectricalMachinesTripReport,
    AcElectricalMachinesMultiAsserReport,
    ExhaustFanDailyReport,
    ExhaustFanTripReport,
    ExhaustFanMultiAsserReport,
    TempHumidDailyReport,
    GensetFuelTankSiteReport,
    EnergyMeterAvailabilityReport,
    TankerTruckFuelTankerDailyReports,
    TankerTruckVehicleDailyReports,
    TankerTruckFuelFilledDispenseReports,
    CompDailyReports,
    CompDgRunReports,
    DGFaultReport,
    GasGensetFaultReport,
    CriticalReports,
    DgRunReports,
    GasGensetTripReport,
    FuelFillDrainReport,
    FuelTankFillReport,
    FuelTankDailyReports,
    FuelTankSnapShotReports,
    ParameterReports,
    UnassignedDevice,
    AssignedDevice,
    DeviceConfiguration,
    DeviceCommunication,
    DeviceHealth,
    DeviceCustomCommand,
    DeviceCalibration,
    FinalThingAdd,
    FinalThingConfigure,
    SiteConfiguration,
    SiteList,
    FinalThingDetails,
    ThingList,
    RuleManagement,
    ServiceModule,
    // EnvDashboard,
    ElectricalMachinesMapView,
    ElectricalMachinesRealTime,
    FleetMapView,
    EnergyMeterMapView,
    TankerMapView,
    DeliveryDashboard,
    DGDashboard,
    CellTowerDetailedView,
    RemoteCalibration,
    DGPanel,
    WithoutFuelDashboard,
    WithoutFuelDetailedView,
    WithoutFuelPanel,
    WithoutFuelRealTime,
    FuelSensorDashboard,
    FuelSensorDetailedView,
    FuelSensorPanel,
    DgMapView,
    AssetDashboard,
    DGDetailedView,
    FinalPanelView,
    DGRealTime,
    TripView,
    CompDashboard,
    CompDetailedView,
    TicketingSupport,
    DGMorePage,
  };

  const common_props = {
    client_id,
    head_side_object_data,
    menuUrlArray,
    AlertObjectData,
    template_id,
    getViewAccess,
    getRemoteAccess,
    getRemoteLockAccess,
    enabled_features,
    plan_description,
    props,
    application_id,
    application_name,
    client_name,
    client_logo,
    vendor_logo,
    user_id,
    user_preferences,
    collapsed,
    onCollapseSider,
    t,
    globalConfig,
    app_name,
    vendor_id,
    logged_in_user_client_id,
    logged_in_user_role_type,
    authToken,
    checkCustomerFuelBuddy,
    is_white_label,
    vendor_name,
    loadingLogo,
    firstThingCat,
    allApps,
    currentUserPreferences,
    updateCurrentUserPreference,
    thing_list,
  };
  const routes: JSX.Element[] = [];
  const route_mapping = getRoutesMapping(common_props);
  route_mapping.forEach((routeItem) => {
    const PageName = page_name_map[routeItem.component];
    if (!PageName) return; // Page component not available

    const pageRoutes = routeItem.getPath(routeItem.path, common_props);
    if (!pageRoutes.length) return; // Page route access not available
    routes.push(
      <Route
        exact
        path={pageRoutes}
        render={(props) => (
          <PageName
            {...common_props}
            {...props}
            timeZone={user_preferences?.timezone}
            data_api={["data"]}
            report_disabled_cats={[44, 85]}
            type={"custom"}
            isScheduledReport={isScheduledReport}
            {...MachineInfoProps}
            application_name={
              logged_in_user_client_id === client_id
                ? undefined
                : "dg-monitoring"
            }
          />
        )}
      />,
    );
  });
  return routes;
}

export { singleAppProjectRoutes };
