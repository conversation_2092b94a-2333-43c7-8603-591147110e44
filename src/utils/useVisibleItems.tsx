import { useState, useEffect, useRef, RefObject } from 'react';

interface UseVisibleItemsOptions {
  /**
   * Optional class names for temporary test elements
   */
  itemClassName?: string;
  
  /**
   * Approximate width to reserve for "more items" indicator
   */
  moreItemsWidth?: number;

  /**
   * Gap between items
   */
  itemsGap?: number;
  
  /**
   * Function to render an item as HTML string
   * This is used for accurate width measurement
   */
  renderItem?: (item: any) => string;
}

/**
 * Custom hook to calculate how many items can fit in a container
 * and keep track of visible items and hidden items count
 */
function useVisibleItems<T, E extends HTMLElement = HTMLElement>(
  items: T[] | undefined, 
  containerRef: RefObject<E | null>,
  options: UseVisibleItemsOptions = {}
) {
  const [visibleItems, setVisibleItems] = useState<T[]>([]);
  const [hiddenItemsCount, setHiddenItemsCount] = useState(0);
  
  const { 
    itemClassName = '', 
    moreItemsWidth = 100,
    renderItem,
    itemsGap = 6
  } = options;
  
  useEffect(() => {
    if (!items?.length || !containerRef?.current) return;

    let timeoutId: number | undefined;
    
    const calculateVisibleItems = () => {
      const container = containerRef?.current;
      if (!container) return;
      
      const containerWidth = container.clientWidth;
      
      // Handle zero width case (when the container is not rendered yet)
      if (containerWidth === 0) {
        // Schedule another measurement after render completes
        timeoutId = window.setTimeout(calculateVisibleItems, 0);
        return;
      }

      // Create temporary elements to measure width
      const tempDiv = document.createElement('div');
      tempDiv.style.position = 'absolute';
      tempDiv.style.visibility = 'hidden';
      tempDiv.style.display = 'flex';
      document.body.appendChild(tempDiv);
      
      // Store measurements of each item (including gaps)
      const itemMeasurements: number[] = [];
      let totalWidthAllItems = 0;
      
      // For each item, measure its width
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        const itemElement = document.createElement('div');
        
        if (itemClassName) {
          itemElement.className = itemClassName;
        }
        
        // Use the renderItem function if provided, otherwise convert to string
        if (renderItem) {
          // Use the custom render function to get HTML representation
          itemElement.innerHTML = renderItem(item);
        } else {
          // Default for primitive values
          itemElement.textContent = String(item);
        }
        
        tempDiv.appendChild(itemElement);
        const itemWidth = itemElement.offsetWidth;
        
        // Add gap to width calculation if it's not the first item
        const gapWidth = i > 0 ? itemsGap : 0;
        
        // Store the width including gap
        itemMeasurements.push(itemWidth + gapWidth);
        totalWidthAllItems += itemWidth + gapWidth;
      }
      
      document.body.removeChild(tempDiv);
      
      // First check: Do all items fit without "More" indicator?
      if (totalWidthAllItems <= containerWidth) {
        // All items fit, show them all
        setVisibleItems([...items]);
        setHiddenItemsCount(0);
      } else {
        // Not all items fit, determine how many fit with "More" indicator
        let runningWidth = 0;
        let visibleCount = 0;
        
        for (let i = 0; i < itemMeasurements.length; i++) {
          const remainingItems = items.length - i - 1;
          const needsMoreTag = remainingItems > 0;
          const reserveWidth = needsMoreTag ? moreItemsWidth : 0;
          
          // Check if adding this item would exceed container width
          if (runningWidth + itemMeasurements[i] + reserveWidth <= containerWidth || i === 0) {
            runningWidth += itemMeasurements[i];
            visibleCount++;
          } else {
            break;
          }
        }
        
        setVisibleItems(items.slice(0, visibleCount));
        setHiddenItemsCount(Math.max(0, items.length - visibleCount));
      }
    };
    
    calculateVisibleItems();
    
    // Recalculate on window resize
    window.addEventListener('resize', calculateVisibleItems);
    return () => {
        window.removeEventListener('resize', calculateVisibleItems);
        if (timeoutId) window.clearTimeout(timeoutId);
    }
  }, [items, containerRef, itemClassName, moreItemsWidth, renderItem, itemsGap]);
  
  return { visibleItems, hiddenItemsCount };
}

export default useVisibleItems; 