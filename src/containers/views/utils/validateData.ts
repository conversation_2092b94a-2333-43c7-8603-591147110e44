export const validateData = (data: any, defaultValue: any, dataType: string) => {
    if(!data) return defaultValue;
    switch(dataType) {
        case "array":
            if(Array.isArray(data)) {
                return data;
            }
            return defaultValue;
        case "object":
            if(typeof data === "object") {
                return data;
            }
            return defaultValue;
        default:
            return data;
    }
};


export const getMapValidData = (mapData: any) => {
    let markers = validateData(mapData?.data?.mapMarkers, [], "array");
    let listItems = validateData(mapData?.data?.listDrawerItems, [], "array");
    let legendData = validateData(mapData?.data?.legendData, {}, "object");

    const validMarkers = markers.map((marker: any) => {
        return {
            ...marker,
            lat: Number(marker.lat),
            long: Number(marker.long) 
        }
    });

    const validListItems = listItems.map((item: any) => {
        return {
            ...item,
            lat: Number(item.lat),
            long: Number(item.long)
        }
    });

    return {
        markers: validMarkers,
        listItems: validListItems,
        legendData
    }
}
