import React from 'react';
import ActionButtons from '../components/ActionButtons/ActionButtons';
import RemoteControl from '../components/RemoteControl/RemoteControl';
// Import types from the components/types file
import { type DynamicComponentConfig } from '../components/types';

/**
 * Type definition for component registry entries
 */
type ComponentRegistryEntry = React.ComponentType<any>;

/**
 * Registry of components that can be dynamically rendered
 * Each key maps to a React component that will receive props
 */
const componentRegistry: Record<string, ComponentRegistryEntry> = {
  'action-buttons': ActionButtons,
  'remote-control': RemoteControl,
};

/**
 * Renders a dynamic component based on the provided configuration
 * 
 * @param dynamicComponent - Configuration for the component to render
 * @returns React element or null if component not found
 */
export const renderDynamicComponent = (
  dynamicComponent: DynamicComponentConfig | undefined
): React.ReactNode => {
  if (!dynamicComponent) return null;
  
  const { key, config } = dynamicComponent;
  const Component = componentRegistry[key];
  
  if (!Component) {
    console.warn(`Dynamic component with key "${key}" not found in registry`);
    return null;
  }
  
  // Create element using React.createElement instead of JSX
  return React.createElement(Component, config);
};

export default componentRegistry; 