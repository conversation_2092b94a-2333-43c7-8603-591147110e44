{"kpi": [{"data": [{"key": "total_sites", "size": "medium", "title": "Sites", "divider": true, "clickable": false, "precision": 0, "valPathExp": "[?id=='total_sites'].value | [0]"}, {"key": "compliant_sites", "size": "medium", "title": "Compliant", "divider": true, "clickable": true, "precision": 0, "valPathExp": "[?id=='compliant_sites'].value | [0]", "description": "Sites that have no active alerts and no device issues (e.g., device offline or other device errors)", "filter_query": {"key": "site_status", "value": "true", "operator": "eq", "query_key": "status.compliance"}}, {"key": "non_compliant_sites", "size": "medium", "title": "Non-Compliant", "divider": true, "clickable": true, "precision": 0, "valPathExp": "[?id=='non_compliant_sites'].value | [0]", "description": "Sites that have active alerts or device issues (e.g., device offline or other device errors)", "filter_query": {"key": "site_status", "value": "false", "operator": "eq", "query_key": "status.compliance"}}, {"key": "alert_sites", "size": "medium", "title": "<PERSON><PERSON><PERSON>", "divider": true, "clickable": true, "precision": 0, "valPathExp": "[?id=='alert_sites'].value | [0]", "description": "Number of sites with active alerts. (e.g., temperature Violations)", "filter_query": {"key": "site_alerts", "value": "true", "operator": "eq", "query_key": "status.alerts"}}], "config": {"state": "valueFirst", "itemsClickable": true}, "apiType": "sites_kpi", "apiConfig": {"kpi_queries": [{"id": "total_sites", "search": null, "filters": [{"field": "status.active", "value": "active", "operator": "eq"}]}, {"id": "compliant_sites", "search": null, "filters": [{"field": "status.active", "value": "active", "operator": "eq"}, {"field": "status.compliance", "value": true, "operator": "eq"}]}, {"id": "non_compliant_sites", "search": null, "filters": [{"field": "status.active", "value": "active", "operator": "eq"}, {"field": "status.compliance", "value": false, "operator": "eq"}]}, {"id": "device_issues_sites", "search": null, "filters": [{"field": "status.active", "value": "active", "operator": "eq"}, {"field": "status.device_issues", "value": true, "operator": "eq"}]}, {"id": "alert_sites", "search": null, "filters": [{"field": "status.active", "value": "active", "operator": "eq"}, {"field": "status.alerts", "value": true, "operator": "eq"}]}]}, "dataPathExp": "", "includeFilters": ["site_type", "territories"]}], "table": [{"apiType": "sites", "colSpan": 24, "filters": [], "apiConfig": {"api_query": {"filters": {"status.active": "active"}}}, "resizable": false, "tableProps": {"size": "middle", "columns": [{"key": "site", "align": "left", "fixed": "left", "title": "Site", "width": 320, "render": "function(site, row_value) { const siteTypeIconMap = {'8': 'https://static.datoms.io/images/icons/site-category/warehouses.svg', '7': 'https://static.datoms.io/images/icons/site-category/supermarket-and-dark-stores.svg'}; const statusConfig = {entityType: 'site', iconType: 'normal', statusColor: row_value.compliance === false ? '#964B00' : '#008C3F', categoryIcon: siteTypeIconMap[row_value.site_type] || 'https://static.datoms.io/images/icons/site-category/supermarket-and-dark-stores.svg', leftIcons: [], rightIcons: [], actionIcon: row_value.action_icon || '', tooltipItems: [{'title': 'Status', 'value': row_value.compliance === false ? 'Non-Compliant' : 'Compliant'}]}; if(row_value.device_issues === true) {statusConfig.rightIcons.push('https://static.datoms.io/images/icons/devices-warn.svg');}; if(row_value.alerts === true) {statusConfig.tooltipItems.push({'title': 'Active Alerts'}); statusConfig.rightIcons.push('https://static.datoms.io/images/icons/notifications-warn.svg');}; return (<div style={{ display: 'flex', alignItems: 'center', gap: '12px', width: '100%', padding: '0 8px' }}><div style={{ display: 'flex', alignItems: 'center', gap: '12px', minWidth: 0, flex: 1 }}><StatusIcon config={statusConfig} /><div style={{ minWidth: 0, flex: 1 }}><AntTooltip title={row_value.site}><Highlighter style={{ fontSize: '13px', display: 'block', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', width: '100%' }} searchWords={[table_search]} autoEscape={true} textToHighlight={row_value.site} /></AntTooltip></div></div><div style={{ flexShrink: 0, margin: '0 8px' }}>{!props.dg_in_iot_mode && <Link target='_blank' to={baseUrl + 'site-view?site_id=' + row_value.site_id}><img src=\"https://static.datoms.io/images/icons/detailed-view.png\" /></Link>}</div></div>);}", "sorter": true, "ellipsis": false, "dataIndex": "site", "pdf_title": "Site", "colPathExp": "info.name", "api_sort_key": "info.name", "not_customizable": true}, {"key": "territory", "align": "center", "title": "Territory", "width": 150, "sorter": true, "ellipsis": false, "dataIndex": "territory", "pdf_title": "Territory", "colPathExp": "info.territory_name", "api_sort_key": "info.territory_name"}, {"key": "active_alerts_count", "align": "center", "title": "<PERSON><PERSON><PERSON>", "width": 120, "render": "function(count, row_value) { return count !== '-' && count > 0 ? (<AntPopover content={!Array.isArray(row_value.active_alerts_names) ? 'No alerts' : (<div style={{ maxWidth: '300px', maxHeight: '200px', overflow: 'auto' }}><ul style={{ margin: 0, padding: '0 0 0 16px' }}>{row_value.active_alerts_names.map((alert, index) => (<li key={index} style={{ marginBottom: '4px', textAlign: 'left' }}>{alert}</li>))}</ul></div>)} trigger='click' placement='top' overlayStyle={{ maxWidth: '400px' }}><div style={{ display: 'inline-flex', alignItems: 'center', justifyContent: 'center', padding: '2px 10px', borderRadius: '20px', border: '1px solid #ff4d4f', color: '#ff4d4f', fontSize: '12px', fontWeight: 500, cursor: 'pointer' }}><WarningOutlined style={{ marginRight: '5px' }} /> <span style={{lineHeight: 'normal'}}>{count} Alerts</span></div></AntPopover>) : '-'; }", "sorter": true, "ellipsis": false, "dataIndex": "active_alerts_count", "pdf_title": "<PERSON><PERSON><PERSON>", "colPathExp": "status.active_alerts_count", "api_sort_key": "status.active_alerts_count"}], "expandable": false, "pagination": {"pageSize": 12, "hideOnSinglePage": false}, "rowSelection": false}, "dataPathExp": "data", "downloadable": true, "additionalData": [{"dataIndex": "site_id", "colPathExp": "info.id"}, {"dataIndex": "site_type", "colPathExp": "info.category_id"}, {"dataIndex": "site_type_name", "colPathExp": "info.category_name"}, {"dataIndex": "compliance", "colPathExp": "status.compliance"}, {"dataIndex": "device_issues", "colPathExp": "status.device_issues"}, {"dataIndex": "alerts", "colPathExp": "status.alerts"}, {"dataIndex": "active_alerts_names", "colPathExp": "status.active_alerts_names"}, {"dataIndex": "parameters_compliance", "colPathExp": "status.parameters_compliance"}], "api_pagination": true, "preferenceKeys": ["list_view", "site_list"], "defaultFixedCount": 1, "searchableColumns": [], "totalCountPathExp": "pagination.total", "disableCustomization": false}], "title": "Site List View", "actions": [{"key": "site_asset_switch", "position": "left", "component": "SegmentedTabs", "component_props": {"value": "site", "options": [{"label": "Site", "value": "site"}, {"link": "list-view/assets", "label": "<PERSON><PERSON>", "value": "asset"}]}}], "filters": [{"label": "Site Type", "url_name": "site_type", "query_key": "info.category_id", "allowClear": false, "filter_api": "site_type", "showSearch": true, "placeholder": "Site Type", "no_outside_label": true, "showSingleOption": true, "select_first_option": true, "is_inside_filter_drawer": false, "is_outside_filter_drawer": true}, {"key": "territories", "type": "tree_select", "label": "Territory", "url_name": "territories", "query_key": "info.territory_id", "filter_api": "territories", "feature_key": "UserManagement:Territory", "component_props": {"value": [], "treeData": [], "allowClear": true, "maxTagCount": 0, "placeholder": "Select territories", "treeCheckable": true, "treeDefaultExpandAll": true}, "no_outside_label": true, "is_options_dynamic": true, "is_inside_filter_drawer": false, "is_outside_filter_drawer": true}, {"key": "site_status", "label": "Site Status", "url_name": "site_status", "query_key": "status.compliance", "allowClear": true, "optionData": [{"title": "Compliant", "value": "true"}, {"title": "Non-Compliant", "value": "false"}], "showSearch": true, "placeholder": "Site Status", "no_outside_label": true, "showSingleOption": true, "is_inside_filter_drawer": false, "is_outside_filter_drawer": true}, {"size": "default", "type": "search", "placeholder": "Search Sites"}], "page_url": "list-view/sites", "realtimeConfig": {"key": "site_id", "enabled": true}, "dynamicComponentConfig": {"enabled": true, "config_api": "site_type_detials", "dependent_filter_keys": ["site_type"]}}