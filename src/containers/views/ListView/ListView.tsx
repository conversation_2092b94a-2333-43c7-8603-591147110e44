import React, { useEffect } from "react";
import ListingPage from "../../../components/composites/ListingPage";
import Panel from "@datoms/dg-monitoring-views/src/js/GenericTemplate/pages/Panel";
import { getBaseUrl } from "@datoms/js-utils/src/base-url-logic";
import { useGlobalContext } from "../../../store/globalStore";
// import pageConfig from "./page-config.json";

interface ListViewProps {
  enabled_features?: string[];
  history: {
    push: (path: string) => void;
  };
  location: {
    pathname: string;
  };
  [key: string]: any; // for other props
}

const ListView: React.FC<ListViewProps> = (props) => {
  const context = useGlobalContext();
  useEffect(() => {
    const hasSiteManagement = props.enabled_features?.includes(
      "SiteManagement:SiteManagement",
    );
    
    if (hasSiteManagement && props.client_id === 13853 && !props.location.pathname.includes("/assets")) {
      !props.location.pathname.includes("/sites") &&
        props.history.push(getBaseUrl(props, "list-view/sites"));
    } else if (!props.location.pathname.includes("/assets")) {
      props.history.push(getBaseUrl(props, "list-view/assets"));
    }
  }, []);

  const renderComponent = () => {
    if (props.location.pathname.includes("/sites")) {
      return <ListingPage {...props} /*pageConfig={pageConfig}*/ page_id={context.user_id === 41 ? 55 : 53}/>;
    }
    return <Panel {...props} />;
  };

  return renderComponent();
};

export default ListView;
