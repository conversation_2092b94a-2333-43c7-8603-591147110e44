export const siteKpiFullConfig = [
    {
        "data": [
            {
                "key": "total_sites",
                "size": "medium",
                "title": "Sites",
                "divider": true,
                "clickable": false,
                "precision": 0,
                "valPathExp": "[?id=='total_sites'].value | [0]"
            },
            {
                "key": "compliant_sites",
                "size": "medium",
                "title": "Compliant",
                "divider": true,
                "clickable": true,
                "precision": 0,
                "valPathExp": "[?id=='compliant_sites'].value | [0]",
                "description": "Sites that have no active alerts and no device issues (e.g., device offline or other device errors)",
                "filter_query": {
                    "key": "site_status",
                    "value": "true",
                    "operator": "eq",
                    "query_key": "status.compliance"
                }
            },
            {
                "key": "non_compliant_sites",
                "size": "medium",
                "title": "Non-Compliant",
                "divider": true,
                "clickable": true,
                "precision": 0,
                "valPathExp": "[?id=='non_compliant_sites'].value | [0]",
                "description": "Sites that have active alerts or device issues (e.g., device offline or other device errors)",
                "filter_query": {
                    "key": "site_status",
                    "value": "false",
                    "operator": "eq",
                    "query_key": "status.compliance"
                }
            },
            {
                "key": "alert_sites",
                "size": "medium",
                "title": "Alerts",
                "divider": true,
                "clickable": true,
                "precision": 0,
                "valPathExp": "[?id=='alert_sites'].value | [0]",
                "description": "Number of sites with active alerts. (e.g., temperature Violations)",
                "filter_query": {
                    "key": "site_alerts",
                    "value": "true",
                    "operator": "eq",
                    "query_key": "status.alerts"
                }
            }
        ],
        "config": {
            "state": "valueFirst",
            "itemsClickable": true,
            "isBoxShadow": true,
            "backgroundColor": "#FFF"
        },
        "apiType": "sites_kpi",
        "apiConfig": {
            "kpi_queries": [
                {
                    "id": "total_sites",
                    "search": null,
                    "filters": [
                        {
                            "field": "status.active",
                            "value": "active",
                            "operator": "eq"
                        }
                    ]
                },
                {
                    "id": "compliant_sites",
                    "search": null,
                    "filters": [
                        {
                            "field": "status.active",
                            "value": "active",
                            "operator": "eq"
                        },
                        {
                            "field": "status.compliance",
                            "value": true,
                            "operator": "eq"
                        }
                    ]
                },
                {
                    "id": "non_compliant_sites",
                    "search": null,
                    "filters": [
                        {
                            "field": "status.active",
                            "value": "active",
                            "operator": "eq"
                        },
                        {
                            "field": "status.compliance",
                            "value": false,
                            "operator": "eq"
                        }
                    ]
                },
                {
                    "id": "device_issues_sites",
                    "search": null,
                    "filters": [
                        {
                            "field": "status.active",
                            "value": "active",
                            "operator": "eq"
                        },
                        {
                            "field": "status.device_issues",
                            "value": true,
                            "operator": "eq"
                        }
                    ]
                },
                {
                    "id": "alert_sites",
                    "search": null,
                    "filters": [
                        {
                            "field": "status.active",
                            "value": "active",
                            "operator": "eq"
                        },
                        {
                            "field": "status.alerts",
                            "value": true,
                            "operator": "eq"
                        }
                    ]
                }
            ]
        },
        "dataPathExp": "",
        "includeFilters": [
            "site_type",
            "territories",
            "operational_status",
            "partners",
            "customers"
        ]
    }
]

export const assetKpiFullConfig = [
    {
        "data": [
            {
                "key": "total_assets",
                "size": "medium",
                "title": "Assets",
                "divider": true,
                "clickable": false,
                "precision": 0,
                "valPathExp": "[?id=='total_assets'].value | [0]"
            },
            {
                "key": "running_assets",
                "size": "medium",
                "title": "Running",
                "divider": true,
                "clickable": true,
                "precision": 0,
                "valPathExp": "[?id=='running_assets'].value | [0]",
                "filter_query": {
                    "key": "asset_status",
                    "value": "Running",
                    "operator": "eq",
                    "query_key": "status.activity"
                }
            },
            {
                "key": "stopped_assets",
                "size": "medium",
                "title": "Stopped",
                "divider": true,
                "clickable": true,
                "precision": 0,
                "valPathExp": "[?id=='stopped_assets'].value | [0]",
                "filter_query": {
                    "key": "asset_status",
                    "value": "Stopped",
                    "operator": "eq",
                    "query_key": "status.activity"
                }
            },
        ],
        "config": {
            "state": "valueFirst",
            "itemsClickable": true,
            "isBoxShadow": true,
            "backgroundColor": "#FFF"
        },
        "apiType": "assets_kpi",
        "apiConfig": {
            "kpi_queries": [
                {
                    "id": "total_assets",
                    "search": null,
                    "filters": [
                        {
                            "field": "status.active",
                            "value": "active",
                            "operator": "eq"
                        }
                    ]
                },
                {
                    "id": "running_assets",
                    "search": null,
                    "filters": [
                        {
                            "field": "status.active",
                            "value": "active",
                            "operator": "eq"
                        },
                        {
                            "field": "status.activity",
                            "value": "Running",
                            "operator": "eq"
                        }
                    ]
                },
                {
                    "id": "stopped_assets",
                    "search": null,
                    "filters": [
                        {
                            "field": "status.active",
                            "value": "active",
                            "operator": "eq"
                        },
                        {
                            "field": "status.activity",
                            "value": "Stopped",
                            "operator": "eq"
                        }
                    ]
                }
            ]
        },
        "dataPathExp": "",
        "includeFilters": [
            "asset_type",
            "territories",
            "partners",
            "customers"
        ]
    }
]

export const getKpiFullConfig = (selectedMapType: string = "assets") => [
    ...(selectedMapType === "sites" ? siteKpiFullConfig : assetKpiFullConfig)
]
