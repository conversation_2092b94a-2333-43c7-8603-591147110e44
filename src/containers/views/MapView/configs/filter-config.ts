
const isBigScreen = window.innerWidth > 1366;
const isMediumScreen = window.innerWidth > 1024;

export const additionalFilters = [
  {
    "key": "partners",
    "label": "Partner",
    "sorted": false,
    "url_name": "partners",
    "query_key": "info.partner_org_id",
    "allowClear": true,
    "filter_api": "partners",
    "showSearch": true,
    "feature_key": "UserManagement:Partner",
    "multiSelect": true,
    "placeholder": "Partners",
    "filterOption": false,
    "api_pagination": true,
    "showEmptyOption": true,
    "no_outside_label": true,
    "showSingleOption": true,
    "dependent_filters": [
        "customers"
    ],
    "is_options_dynamic": true,
    "autoClearSearchValue": false,
    "is_inside_filter_drawer": false,
    "is_outside_filter_drawer": true
  },
  {
    "key": "customers",
    "label": "Customer",
    "sorted": false,
    "url_name": "customers",
    "query_key": "info.end_user_org_id",
    "allowClear": true,
    "filter_api": "customers",
    "showSearch": true,
    "feature_key": "UserManagement:Customer",
    "multiSelect": true,
    "placeholder": "Customers",
    "filterOption": false,
    "api_pagination": true,
    "showEmptyOption": true,
    "no_outside_label": true,
    "showSingleOption": true,
    "is_options_dynamic": true,
    "autoClearSearchValue": false,
    "is_inside_filter_drawer": false,
    "is_outside_filter_drawer": true
  }
]

export const siteFilterData = (isDatomsX: boolean = false) => [
    {
      "label": "Site Type",
      "url_name": "site_type",
      "placeholder": "Site Type",
      "query_key": "info.category_id",
      "allowClear": true,
      "filter_api": "site_type",
      "showSearch": true,
      "no_outside_label": true,
      "showSingleOption": true,
      // "select_first_option": true,
      "is_inside_filter_drawer": false,
      "is_outside_filter_drawer": true
    },
    {
      "key": "operational_status",
      "label": "Operational Status",
      "url_name": "operational_status",
      "query_key": "status.operational",
      "allowClear": true,
      "optionData": [
          {
              "title": "Operational",
              "value": "true"
          },
          {
              "title": "Non-Operational",
              "value": "false"
          }
      ],
      "showSearch": true,
      "placeholder": "Operational Status",
      // "defaultValue": "true",
      "no_outside_label": true,
      "showSingleOption": true,
      "is_inside_filter_drawer": isDatomsX,
      "is_outside_filter_drawer": !isDatomsX
    },
    {
      "key": "territories",
      "type": "tree_select",
      "label": "Territory",
      "url_name": "territories",
      "query_key": "info.territory_id",
      "filter_api": "territories",
      "feature_key": "UserManagement:Territory",
      "component_props": {
          "value": [],
          "treeData": [],
          "allowClear": true,
          "maxTagCount": 0,
          "placeholder": "Select territories",
          "treeCheckable": true,
          "treeDefaultExpandAll": true
      },
      "no_outside_label": true,
      "is_options_dynamic": true,
      "is_inside_filter_drawer": false,
      "is_outside_filter_drawer": true
    },
    {
      "key": "site_status",
      "label": "Site Status",
      "url_name": "site_status",
      "query_key": "status.compliance",
      "allowClear": true,
      "optionData": [
          {
              "title": "Compliant",
              "value": "true"
          },
          {
              "title": "Non-Compliant",
              "value": "false"
          }
      ],
      "showSearch": true,
      "placeholder": "Site Status",
      "no_outside_label": true,
      "showSingleOption": true,
      "is_inside_filter_drawer": isDatomsX,
      "is_outside_filter_drawer": !isDatomsX
    },
    // {
    //   "url_name": "device_status",
    //   "key": "device_status",
    //   "label": "Device Status",
    //   "placeholder": "Device Status",
    //   "showSearch": true,
    //   "allowClear": true,
    //   "no_outside_label": true,
    //   "showSingleOption": true,
    //   "is_outside_filter_drawer": true,
    //   "is_inside_filter_drawer": false,
    //   "optionData": [
    //     {
    //       "title": "Online",
    //       "value": "online"
    //     },
    //     {
    //       "title": "Offline",
    //       "value": "offline"
    //     }
    //   ]
    // },
    
  ]

export const assetFilterData = (isDatomsX: boolean = false) => [
  {
    "label": "Asset Category",
    "url_name": "thing_category",
    "placeholder": "Asset Types",
    "key": "thing_category",
    "query_key": "info.category_id",
    "filter_api": "thing_category",
    "showSearch": true,
    "allowClear": true,
    "no_outside_label": true,
    "showSingleOption": true,
    "is_outside_filter_drawer": true,
    "is_inside_filter_drawer": false,
    // "dependent_filters": ["asset_status"]
  },
  {
    "key": "territories",
    "type": "tree_select",
    "label": "Territory",
    "url_name": "territories",
    "query_key": "info.territory_id",
    "filter_api": "territories",
    "feature_key": "UserManagement:Territory",
    "component_props": {
        "value": [],
        "treeData": [],
        "allowClear": true,
        "maxTagCount": 0,
        "placeholder": "Select territories",
        "treeCheckable": true,
        "treeDefaultExpandAll": true
    },
    "no_outside_label": true,
    "is_options_dynamic": true,
    "is_inside_filter_drawer": false,
    "is_outside_filter_drawer": true
  },
  {
      "url_name": "asset_status",
      "key": "asset_status",
      "label": "Asset Status",
      "placeholder": "Asset Status",
      "query_key": "status.activity",
      "showSearch": true,
      "allowClear": true,
      "no_outside_label": true,
      "showSingleOption": true,
      "is_outside_filter_drawer": !isDatomsX,
      "is_inside_filter_drawer": isDatomsX,
      "optionData": [
        {
          "title": "Running",
          "value": "Running"
        },
        {
          "title": "Stopped",
          "value": "Stopped"
        },
      ]
    },
]

export const getFilterData = (selectedMapType: string = "assets", applicationId?: number) => {
  const isDatomsX = applicationId === 12;
  let filters = selectedMapType === "sites" ? siteFilterData(isDatomsX) : assetFilterData(isDatomsX);
  if(isDatomsX) {
    filters = [...additionalFilters, ...filters];
  }
  return filters;
}
