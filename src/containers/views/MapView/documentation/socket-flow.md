# Socket Data Update Flow

```mermaid
flowchart TD
    SocketData[Data received from socket] --> CheckLastUpdateTime{Is last update time set?}
    
    CheckLastUpdateTime -->|No| SetInitialTime[Update last update time = now] --> ImmediateUpdate[/Make API call to update data/]
    
    CheckLastUpdateTime -->|Yes| CheckElapsedTime{Has 15s passed since last update?}
    CheckElapsedTime -->|Yes| MakeAPICall[/Make API call to update data/] --> UpdateTimeAfterAPICall[Update last update time = now]
    
    CheckElapsedTime -->|No| CheckTimerRunning{Is a timeout already scheduled?}
    CheckTimerRunning -->|Yes| ContinueWaiting[Continue waiting for scheduled timeout]
    CheckTimerRunning -->|No| ScheduleTimeout[Schedule timeout for 15s - elapsedTime]
    
    ScheduleTimeout --> TimeoutFires[Timeout fires after delay] --> DelayedUpdate[/Make API call to update data/] --> UpdateTimeAfterTimeout[Update last update time = now] --> NullifyTimeout[Set timeout ref to null]
    
    MinuteInterval[Interval running every 60s] --> CheckIntervalTime{Has 15s passed since last update?}
    CheckIntervalTime -->|Yes| UpdateTimeBeforeInterval[Update last update time = now] --> IntervalUpdate[/Make API call to update data/]
    CheckIntervalTime -->|No| SkipUpdate[Skip update, wait for next interval]
    
    ComponentUnmount[Component unmount] --> CleanupTimeouts[Clear all timeouts and intervals]
    
    DirectAPICall[API call made directly from useMapData] --> ClearExistingTimeout[Clear any scheduled timeout] --> DirectUpdate[/Make API call to update data/] --> UpdateTimeAfterDirectCall[Update last update time = now]
```

## Implementation Notes

1. Socket Data Reception:
   - When new data arrives from socket, check the last update time
   - If last update time is unknown, first set it to current time, then make API calls.
   - If last update time is known and 15s has passed, make API call first, then update the last update time
   - Otherwise, check if a timeout is already running

2. Update Timing Logic:
   - If elapsed time > 15 seconds: Make API call first, then update the last update time afterward
   - If elapsed time < 15 seconds: Schedule a timeout for 15s - elapsedTime
   - Avoid scheduling multiple timeouts (check if one is already running)
   - When timeout fires, make API call first, then update the last update time
   - After updating the last update time, set the timeout reference to null

3. Periodic Check:
   - Every 60 seconds, check if last update was more than 15 seconds ago
   - If yes, first update the last update time, then trigger an API update
   - If no, do nothing until next interval
   
4. Component Cleanup:
   - When component unmounts, clear all timeouts and intervals
   - Reset all buffers and references

5. Direct API Calls:
   - When API calls are made directly from main page (useMapData.ts)
   - Clear any scheduled timeouts (set timeout reference to null)
   - Make the API call
   - Update the last update time after API call completes