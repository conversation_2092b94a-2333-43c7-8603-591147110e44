# Url Structure

## Click Functionalities

- when list drawer is opened:   /list
- when site details drawer is opened:  /site/:siteId
- when asset details drawer is opened:  /asset/:assetId

- clustering ??
- legend ?

## Filters (including KPIs)

- Site Type => site_type=:id
- Asset Type => thing_category=:id
- Territory =>  territories=:id
- Site Status =>  site_status=:status
- Device Status => device_status=:status

- Asset Status => asset_status_:id=:status

## Search

- when something is searched: ?search=
