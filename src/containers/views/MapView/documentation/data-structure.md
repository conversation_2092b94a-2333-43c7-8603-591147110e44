## List Drawer: Entity

```
interface ListDrawerEntityProps {
  id: number;
  name: string;
  entityType: "asset" | "site";
  lastDataReceivedTime?: string | Date | null;
  customerName?: string;
  statusIconConfig: StatusIconConfig;
  showDeviceOfflineTag?: boolean;
  siteInfo?: {
    id: number;
    name: string;
  };
  assetCount?: number;
  dynamicComponent?: {
    key: string;
    config: any
  };
  onClick?: (id: number) => void;
}
```


## Map Component: Marker

```
interface IconArrayItemType {
  lat: number;
  long: number;
  entityType: "asset" | "site";
  entityId: number;
  statusIconConfig: StatusIconConfig;
}
```



## Status Icon Config:

```
interface StatusIconConfig {
  statusColor?: string;
  deviceStatusColor?: string;
  entityType?: 'site' | 'asset';
  categoryIcon?: string;
  leftIcons?: string[];
  rightIcons?: string[];
  actionIcon?: string;
  tooltipItems?: {title: string, value: string}[];
}
```

* In tooltipItems
  - categoryName
  - entityName
  - entityStatus
  - deviceStatus
  - faultTypes


## Legend: 

```
{
    assetStatusIcons: [
        {
          categoryIcon: dgCategoryIcon,
          status: [
            {
              statusColor: "#008C3F",
              title: "Running"
            },{
              statusColor: "#F65F54",
              title: "Stopped"
            },
            {
              customIcon: LockIcon,
              title: "Locked"
            }
          ]
        },
        {
          categoryIcon: solarPumpCategoryIcon,
          status: [
            {
              statusColor: "#22CF61",
              title: "Connected"
            },{
              statusColor: "#A6A6A6",
              title: "Disconnected"
            }
          ]
        }
    ],
    siteStatusIcons: {
        categoryIcon: solarPumpCategoryIcon,
        status: [
            {
                statusColor: "#008C3F",
                title: "Compliant"
            },
            {
                statusColor: "#964B00",
                title: "Non-Compliant"
            }
        ]
    },
    deviceStatus: [
        {
            color: "#BAEBC7",
            title: "Online"
        },
        {
            color: "#EED960",
            title: "Partially Online"
        },
        {
            color: "#D2D2D2",
            title: "Offline"
        }
    ],
    leftIconsSite: [
        {
            icon: DeviceErrorIcon,
            title: "Device Error"
        },
        {
            icon: SystemErrorIcon,  
            title: "System Error"
        }
    ],
    rightIconsSite: [
        {
            icon: TripFaultIcon,
            title: "Trip Fault"
        }
    ],
    leftIconsAsset: [
        {
            icon: DeviceErrorIcon,
            title: "Device Error"
        }
    ],
    rightIconsAsset: [
        {
            icon: TripFaultIcon,
            title: "Trip Fault"
        },
        {
            icon: MaintenanceDueIcon,
            title: "Maintenance Due"
        },
        {
            icon: ViolationIcon,
            title: "Violation"
        }
    ]
}
```


# Required Data : 

## Main Page :

 - Filter Data
 - KPI Data
 - Legend
  - siteStatusIcons / assetStatusIcons & deviceStatusColor
  - when *View More* is clicked
    - assetStatusIcons
    - siteStatusIcons
    - deviceStatus
    - error/ alert indicator icons for site and asset (which are enabled)

 - Map 
   - list of site/asset, each item having lat, long, entityType, entityId, StatusIconConfig
   - In the start we don't know which markers would be clustered and which not; otherwise we won't need statusIconConfig for clustered ones in the start.

 - List Drawer
  - search button clicked or it is in the url: list of site/asset each item having 
      - StatusIconConfig, 
      - Asset/Site name, 
      - toShowOfflineDeviceTag,  (deviceStatus = offline & device layer enabled)
      - asset count / last data received time, 
      - customer name (for partner/ datoms-x portal)  
      - siteName for assets connected to site (when some asset type filter will  be selected)
      - dynamic component data (remote control icons)

 - Details Drawer
  - some entity is clicked from the list OR some marker is clicked from the map or it is there in the url.
    for asset: 
      - details from the list drawer (except customer name) 
      - dynamic component data (remote control icons) / asset status
      - asset info (e.g. make, model etc.)
      - quick links to show
      - Alerts (list of name of the alerts)
      - critical status (if enabled)
      - parameter data
    for site: 
      - details from the list drawer (except customer name)
      - site status
      - quick links to show
      - Alerts (list of name of the alerts)
      - Device Issues (list of device issues)
      - asset list 
        - asset category name (& id)
        - list of assets
          - same as list drawer data (except customer name, site name)
          - dynamic component data (remote control icons) / asset status
          - asset info
          -------when any asset's collapse arrow is clicked-------
          - quick links to show
          - Alerts (list of name of the alerts)
          - critical status (if enabled)
          - parameter data


# Final json:

### StatusIconConfig: 
{
  statusColor?: string;
  deviceStatusColor?: string;
  entityType?: 'site' | 'asset';
  categoryIcon?: string;
  leftIcons?: string[];
  rightIcons?: string[];
  actionIcon?: string;
  tooltipItems?: {title: string, value: string}[];
}

* tooltipItems = [
  {
    title: categoryName,
    value: entityName
  },
  {
    title: Site Status
    value: entityStatus
  },
  {
    title: Device Status,
    value: deviceStatus
  },
  {
    title: Fault types,
    value: faultTypes
  }
]

## Map:
[
  {
    lat: number;
    long: number;
    entityId: number;
    entityType: "asset" | "site";
    statusIconConfig: StatusIconConfig;
  },
  {

  }
]

## List Drawer:
[
  {
    lat: number;
    long: number;
    id: number;
    entityType: "asset" | "site";
    statusIconConfig: StatusIconConfig;

    name: string;
    showDeviceOfflineTag?: boolean;
    assetCount?: number;
  },
  {

  }
]

## Site Details:
{
  lat?: number;
  long?: number;
  id?: string | number;
  entityType?: string;
  statusIconConfig?: any;
  name?: string;
  showDeviceOfflineTag?: boolean;
  assetCount?: number;

  status?: string;
  alerts?: string[];
  deviceIssues?: string[];
}

  assetList: [
    categoryId: number;
    categoryName: string;
    assets: [
      {
        entityConfig: {
          id?: string | number;
          name?: string;
          entityType?: string;
          lastDataReceivedTime?: string;
          showDeviceOfflineTag?: boolean;
          statusIconConfig?: any;
          dynamicComponent?: React.ReactNode;
          status?: string;
          assetInfo?: Array<{ title: string; value: string | number }>;
          lat?: number;
          long?: number;
        },
        extendedInfo: {
          alerts: [],
          criticalStatus: []
        }
        paramData: {}
      }
    ]
  ]
