## Details Drawer and Map Pointer

1. map zoom doesn't change when drawer is closed (sometimes)
2. socket update renders the map again which changes the zoom/center (specially when an entity was selected-> in details drawer)
3. On filter change, closing the drawer and resetting the map to default zoom/center  ----- confirm it
4. When details drawer is opened, should the user be able to change the zoom/pan ??

5. when 2 entities are at same lat-long and one of them is clicked, it is possible that the marker of the other one is above this one in the map, then it will look as if the clicked entity didn't even appear. Should we do something about it ?

## Socket

1. connect once per page load, subscribe-unsubscribe when new data comes (entities array changes)


## Re-rendering bugs

1. In urlDrawerToggle: drawer state changes again and again when api is called via socket (each 15 sec.)
2. In map component also, it renders multiple times initially ...


## URL

1. Change URL to `/list/sites/:id` instead of `/list/site/:id`    (`site`/`asset` will become `sites`/`assets`)


## Alerts strip

1. It contains both faults and violation

## Entity Basic Info

1. Show a placeholder text when asset count is zero for a site.


## Overall Changes

1. Keeping `types` at one place (non-repetitive): It can be in multiple files but one `type` should not be in multiple files.
2. Add loading for map.
3. Modify UI (specially those common in panel-view and map-view : panel top header/ category specific views etc.)
4. on socket update, api call is being made twice ? (check)

## Category Specific Views

1. Should we handle data validation in frontend as well    ------- confirm it
2. Confirm about the single source of truth for parameters. And accordingly handle name, unit etc. & existence of parameters & when to show "NA" or "-"
