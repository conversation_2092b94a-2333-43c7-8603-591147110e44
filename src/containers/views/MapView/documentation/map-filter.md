## Filters

- To render filters according to site type availability
  - site type and status filter will be available only if site feature is enabled and sites are created.
  - If no site is there and only 1 asset type is there, then it won't be shown
  - If one site type and one asset types are there, site and asset both filters will be available

- Filter Functionality:
  - If an asset type filter is selected, assets will be shown in the map. If a site type is also selected, assets will get filtered based on site type as well.
  - If asset status is selected then corresponding asset type will also get selected.
  - If an asset type is selected, other asset type's status filters will be disabled.
  - If a site type is selected, sites for that type will be shown but when asset is also selected then sites won't be shown, only assets will be shown.
  - If a site status is selected along with asset type, assets will be filtered based on that site staus as well. (Similarly other combinations will also show data in the same way)

## KPI

- KPI Render and Functionality:
  - If single/multiple assets types are there (no site), single/multiple Asset Type KPIs will be shown along with Device KPI
  - If multiple site types are there, KPIs for each site type will be there.
  - If there is a site then KPIs only for sites will be shown unless an asset type is filtered.
  - If site is there and any asset type is filtered then KPIs for that asset type will be shown.
  - Faults/Violation etc. KPIs will be shown only if they are present.
  - This may change according to customer as well (e.g. for a customer even when site is present, asset type KPI might need to be shown)
  - Clicking any KPI will select the corresponding filters if available (e.g. WareHouse -> Compliant KPI is selected then it will select site_type: WareHouse and site_status: Compliant)
  - Selecting a filter will select the corresponding KPI (e.g. site_staus: Compliant is selected, it will select the 'Compliant' in all site type KPIs)     ?? Confirm


## URL 

  - URL should work as single source of truth
  - Initially, after rendering of Filter and KPIs, the selected data will be taken from URL (if any)
  - Any update to filter or KPI (any selection) should update the URL accordingly and vice versa.


## Approach:

- Fetch all page definitions (filter, KPI config) through API call
- Parse Url and initialize the `selectedFilters` state (once we get the config)
- Render Filters & KPIs (with loading)
- Fetch main page data and KPI data (with `selectedFilters` as filter for both ). For KPI, we can remove some filters (based on config) from `selectedFilters`.
- Display data and update KPIs (their value)
- Handle User Action


## Step by Step :

- Step-1: Creating Entity Availability Service 
  - It determines what entities (site / assets) are available
  - Makes a call to a lightweight API that returns just counts/type.
  - It returns entity availability (hasSite, hasAsset, assetCount, siteCount) ?? Or only entity count
  
  - Need to write API in the backend as well

- Step-2: Rendering Strategy
  - 

### Handling Site and Asset Filters together:

- We may create two different keys as `assetFilter` and `siteFilter`