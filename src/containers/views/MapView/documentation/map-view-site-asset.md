

## Switch: Asset-Site
- If site feature is there, show switch else by default 'asset' is selected and no switch.
- If site feature is there, by default 'site' will be selected    ??

## Filter and KPIs
- On the basis of `selectedMapType` (`site` or `asset`), render filters / KPIs
- Filters will be rendered from config defined in frontend
- KPIs will be rendered from frontend config (will need to update some keys according to that defined in Api)

- Now, for site/asset, filter should be rendered properly.
- Filter-KPI linking should work

## Data API Call
- In the payload, this `selectedMapType` is passed and based on that API will result into 'only asset data' or 'only site data'


# IMPLEMENTATIONS:

- Getting the `selectedMapType` value from URL

- Provide a switch and manage `selectedMapType` stat
- Update filter, KPI configs.
- Filters Rendering: on the basis of this state (create a function for that)
- KPIs Rendering: Entity Status KPIs will be rendered on the basis of this state. Other KPIs   ?? 

- When switched (`selectedMapType` state changes)
  - Clear the selected filters, KPIs state
  - Change the Filter, KPI (Render them again)
  - Fetch the map data, KPI data.
  - Set in the URL
  - Close details drawer (if opened)

- Check if filters are working properly
- Check Filter-KPI linking
- Add details for panel view of DG, Cold Room


Cases: 
- On switch, should we close list drawer as well ? 

Flow: 
- Check for site feature: Yes => switch is shown, `selectedMapType` state is taken from the url (if not in url, then a default will be set)
- 'useMapData' hook is called, it renders Filter/KPI based on `selectedMapType`.
- Make calls to apis with this config

- state switched
  - close details drawer (if open)
  - set in the url
  - filter, kpi, map data (handled via dependency)
  - reset all filter, kpi states 
