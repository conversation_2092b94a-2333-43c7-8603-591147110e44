# Components
```
  MapView Component
    |- Map Header
      |- Filter Section (includes Drawer)
    |- KPI Section
      |- Configurable KPI
    |- Asset/Site List Drawer
      |- Search bar
      |- Asset/Site List **
        |- Image Component (can be made common for other views also in future)
        |- Remote Control (lock and on/off icon) (included Popup for password)
        -----------------------------
    |- Asset/Site Details Drawer
      |- Common Header
      |- Site Details
      |- Asset Details
    |- Google Map Component
    |- Legend Section
```


// showDrawer (list drawer) : states (assumed) to handle the drawer visibility


# Functionality:

## MapView Component
  - Conditional Rendering of Asset/Site List Drawer and Search Icon.
    - Clicking on Search Icon will open List Drawer.
    - Clicking on Cross Icon (in List Drawer) will close List Drawer.
    - If no drawer is opened, search icon will be there.

  - Cluster View: when cluster view will be enabled (handling via state), it will show accordingly in the google map component.

  - Device Layer : Enabled / Disabled (accodingly it will be passed to the child components)

  - List Drawer
    - It will be rendered if showDrawer is true. 
    - It will be passed the site and asset list data, drawerToggle function.
    - When it is opened, some query parameter might be added in the url.

## Map Header
  - Filter Section
    - show the filters (asset type/ site type/ territory etc.) as they are coming from backend (it will be passed from the MapView component)
    - when any filter is selected, it will accordingly change the url.
    - In filter drawer, asset status filters would be there for the selected asset types.
  - Cluster View Switch
    - clicking on it will call a function passed as callback from MapView Component
    - it will also change the url to include the cluster view parameter.

## KPI Section
  - It will be passed the kpi widgets to be shown and data to be displayed in each widget.
  - That data will be modified in a form which can be passed to Configurable KPI component.
  - Clicking on any KPI will change the url according to the key defined for that KPI.

## List Drawer
  - consists of: Search bar, Cross Icon and List of Assets/Sites
  - Search bar (Can use a common Search bar component)
    - It will pass the search value back to its parent component (e.g. List drawer here)
    - This value will be set in the url as search value parameter
    - hover and active ui will be handled as required.
  - Cross Icon : It will toggle the showDrawer value in the MapView component.

  ### List of Assets/Sites
    - If No Data to be shown (according to filters), it will show a "No Data" message.
    - If Data to be shown (according to filters), it will show the list of assets/sites.
    - Image Component
      - It will be passed the image url or the asset/site-type
      - By default, there will be a tooltip showing asset/site and device status
      - If device layer is disabled, device status will not be shown in the tooltip.
      - If tooltip is disabled for List, tooltip won't be shown
      - It will add the fault/violation etc. icons with the category icon. Also, if device layer is disabled, device errors won't be shown in the icon.
    - Name Part
      - Name will have 'ellipsis' if it overflows
      - Right padding will depend on if there is remote control icon or not
      - other data will be shown as required (asset: last data received time, site: asset count)
      - *Click*
        - asset/site Details drawer will open (may be handled using some state in MapView component)
        - url will be updated with the asset/site id (in query parameter)
    - Remote Control Icon
      - there will be provision for two icons (lock icon, remote on/off icon)
      - if enabled, it will be displayed.

      -----------------
    - Implement Infinite scrolling pagination
    - when asset/site details drawer will open, this list drawer will remain there (with a lower z-index) so that user does not lose the scroll progress when he comes back.

## Asset/Site Details Drawer
  - will get the 'selected asset/site id' from the url (logic can be defined in logic file or utils file)
  - accordingly show the details of the asset/site.
  - Clicking on Close button will close the drawer.

  render: 
    - Common Header
      - Back Button
        - Clicking on back button will take to the previous page.
      - Image Component
      - Asset/site Name (along with asset count for site & last data received time for asset)
      - Cross Button to toggle the showDrawer (and showDetailsDrawer)
      - Quick Links Section (site: Site view, For asset: Detailed View, Analog View)
      - Machine Info (for Asset)
      - Location
    - Asset/Site Details
      - Site Details: 
        - List of Assets linked with that site, for each asset detailed /analog view icons will be there (if present)
        - Assets will be listed category-wise (asset-category wise)
        - Clicking on any asset will open the asset details and update the url to include the asset id
        - tooltip in the image component can be disabled for this drawer

      - Asset Details
        - If it is linked with a site, the name of that site will be shown in the header.
          

## Google Map Component
  - Data passed
    - pointers (having lat/long and other details to be shown in tooltip) , cluster view (enabled/disabled)
    - if device layer enabled, data related to it will be sent for 'hoverText'. If tooltip is disabled for map view, tooltip data will not be passed to it.
  - For each pointer
    - if tooltip passed: a tooltip will be shown with the required data.
    - Clicking on any pointer will call a callback function which will change the url and open the details drawer.

## Legend Section
  - Will receive legend data from backend and convert it in the format required to show it in the page (and then probably pass to google map component)


## Url Change: 
  - url change will change the states (e.g. selected asset types, site types, searchValue etc.)
  - whenever url change will occur, if there are states associated with it, the states will be updated.

## Filter : 
  - Depending on the states (changed by url change or inherited from the url initially), api call will be there to fetch the data.






* Doubt: partner specific configuration will be handled in the backend or it will be checked in frontend ?
* Doubt: Should we keep the list scrolled to the point user did when user goes back from details page ? (also for filter drawer)
* Doubt: What are the functionalities (apart from filters) that will also be managed in url? (list drawer opened, site/asset details)

# Functions
  - Set Url for any parameter (add/update/remove) ( ~ 4 Hrs)
  - Get parameter values from url ( ~ 2 Hrs)

# Filters
  - Removing selected filters tags & implementing info (tooltip) for drawer filters ( ~ 45 Minutes) : changes in base layers (1, 2)
  - Handling Asset Status Filters based on asset types selected ( ~ 3 Hrs)  : change in layer 3

# Search bar
  - Create a component for search bar (~ 1 Hr)

# Image Component (Asset Status Image)
  - Create a component for thing-category icon with status (~ 4 Hr)

# Map Common Header
  - create a new component to be used for site details and asset details (~ 5 Hr)

# Map View Component
  - Map Header
    - Header Component (~ 45 Mins)
    - Filter Config (~ 1.5 Hr)

  - KPI Section (~ 1.5 Hrs)

  - Asset/ Site List Drawer (~ 3 Hrs)

  - Asset/ Site Details Drawer
    - Site Details Component ( ~ 5 Hr)
    - Asset Details Component (~ 30 Mins)      (* Doubt: How will this work (different ui for different asset types) ?)

  - Legend Section (30 Min)      (* Doubt: Just to show as coming from backend or some extra handling needed in frontend)

  - Google Map Component
    - Legend is handled inside the google map component. Should we define a new prop and conditionally render via prop or we need to handle it via props passed for older version as well?
    - It calls the callback function (onMapClick) with the asset id. Should call with the id and specification of entity (asset/site)



## Type of Data
  - Entity List
  - Entity Details (static data)
  - Parameter Data (real time)
  - Parameter Data (aggregated)


## Legend Data Structure
  
  ```
  assetStatusIcons: [
    {
      categoryIcon: "",
      status: [
        {
          statusColor: 
          title: 
        },{
          statusColor:
          title:
        },
        {
          customIcon: 
          title: 
        }
      ]
    },
    {
      categoryIcon: "",
      status: [

      ]
    }
  ]

  ```

  ```
  siteStatusIcons: {
    categoryIcon: "",
    status: [{statusColor, title}]
  }
  ```
