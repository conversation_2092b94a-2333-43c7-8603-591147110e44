## Legend :

siteTypeIconMap = {
  8: 'https://static.datoms.io/images/icons/site-category/warehouses.svg', 
  7: 'https://static.datoms.io/images/icons/site-category/supermarket-and-dark-stores.svg'
}

indicatorIconsMap = {
  alerts: 'https://static.datoms.io/images/icons/notifications-warn.svg',
}

siteStatusColorMap = {
  compliant : "#008C3F",
  non-compliant: "#964B00"
}

assetStatusColorMap = {
  connected: "#72c264",
  disconnected: "#b8b1b1"
}

{
    siteStatusIcons: {
        categoryIcon: siteTypeIconMap[8],
        status: [
            {
                title: "Compliant"
                statusColor: siteStatusColorMap["compliant"],
            },
            {
                title: "Non-Compliant"
                statusColor: siteStatusColorMap["non-compliant"],
            }
        ]
    },
    rightIconsSite: [
        {
            icon: indicatorIconsMap['alerts'],
            title: "Alerts"
        }
    ],
}


## StatusIconConfig:

// about asset (right icons) => (maintenance due, violation, fault)  &  (actionIcon) => ?

siteStatus = status.compliance ? Compliant : Non-Compliant
if(status.alerts) {
  rightIcons.push(indicatorIconsMap['alerts']);
}

statusIconConfig = {
    statusColor: siteStatusColorMap[siteStatus.toLowerCase()]
    entityType: 'site'
    categoryIcon: siteTypeIconMap[info.category_id]
    rightIcons: rightIcons
    actionIcon: 
    tooltipItems: [
        {
            title: info.category_name,
            value: info.name,
        },
        {
            title: Site Status
            value: siteStatus
        },
        {
            title: Fault type,
            value: 
        },
        {
            title: Faults,
            value:
        }
    ]
}


## Map :

mapMarkers = [
    {
        lat: info.location.lat;
        long: info.location.long;
        entityId: info.id;
        entityType: "site";
        statusIconConfig: StatusIconConfig;
    }
]


## List Drawer:
[
  {
    lat: info.location.lat;
    long: info.location.long;
    entityId: info.id;
    entityType: "site";
    statusIconConfig: StatusIconConfig;

    name: info.name;
    showDeviceOfflineTag: status.connectivity  ???
    assetCount: assets.length;    
  },
  {

  }
]


## Site Details:
{
    entityConfig: {
      lat: info.location.lat;
      long: info.location.long;
      entityId: info.id;
      entityType: "site";
      statusIconConfig: StatusIconConfig;
      name: info.name;
      showDeviceOfflineTag: status.connectivity  ???
      assetCount: assets.length;
      status: siteStatus;
    }
    extendedInfo: {
      quickLinks: []
      alerts: status.active_alerts_names;
    }
    assetList
}

  assetList: [
    categoryId: info.category_id;
    categoryName: info.category_name;
    assets: [
      {
        entityConfig: {
          id: info.id;
          name: info.name;
          lastDataReceivedTime: time.last_data_receive;
          showDeviceOfflineTag: status.connectivity  ???
          statusIconConfig: statusIconConfig,
          dynamicComponent: {
            key: 'remote-control',
            config: 
          }
          status: 
          assetInfo: info.assetInfo     [{title, value}] => (case conversion of snake case to human readable)
          lat: info.location.lat;
          long: info.location.long;
        },
        extendedInfo: {
          alerts: status.active_alerts_names,
          criticalStatus: 
        }
        paramData: {}
      }
    ]
  ]
