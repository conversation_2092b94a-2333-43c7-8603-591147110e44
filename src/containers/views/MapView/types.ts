export interface Coordinates {
  lat: number;
  long: number;
}

export interface QuickLink {
  key: string;
  title?: string;
  url?: string;
  icon?: string;
}

export interface StatusIconConfig {
  iconType?: 'map' | 'normal';
  statusColor?: string;
  deviceStatusColor?: string;
  entityType?: 'site' | 'asset';
  categoryIcon?: string;
  leftIcons?: string[];
  rightIcons?: string[];
  actionIcon?: string;
  tooltipItems?: {title: string, value: string}[];
}

export interface EntityBase {
  id: number;
  name: string;
  entityType: "asset" | "site";
  coordinates: Coordinates;
  statusIconConfig: StatusIconConfig;
  customerName?: string;
}

export interface Site extends EntityBase {
  entityType: "site";
  assetCount: number;
  siteInfo: {
    status: string;
  };
  quickLinks: QuickLink[];
  alerts: string[];
  deviceIssues: string[];
  assetCategories: {
    id: number;
    name: string;
    assets: number[];
  }[];
}

export interface Asset extends EntityBase {
  entityType: "asset";
  lastDataReceivedTime?: string | Date | null;
  showDeviceOfflineTag?: boolean;
  siteInfo?: {
    id: number;
    name: string;
  };
  assetInfo: {
    title: string;
    value: string;
  }[];
  dynamicComponent?: {
    key: string;
    config: any;
  };
  quickLinks: QuickLink[];
  alerts: string[];
  criticalStatus: {
    title: string;
    icon: string;
  }[];
  parameters: {
    name: string;
    value: string;
  }[];
}

export interface MapMarker {
  entityId: number;
  entityType: "asset" | "site";
  lat: number;
  long: number;
}

export interface ListDrawerItem {
  id: number;
  name: string;
  entityType: "asset" | "site";
  statusIconConfig: StatusIconConfig;
  lastDataReceivedTime?: string | Date | null;
  customerName?: string;
  showDeviceOfflineTag?: boolean;
  assetCount?: number;
  siteInfo?: {
    id: number;
    name: string;
  };
  onClick?: (id: number) => void;
}

export interface EntityDetails {
  basic: ListDrawerItem;
  siteStatus?: string;
  assetInfo?: {
    title: string;
    value: string;
  }[];
  dynamicComponent?: {
    key: string;
    config: any;
  };
  quickLinks: QuickLink[];
  alerts: string[];
  deviceIssues?: string[];
  criticalStatus?: {
    title: string;
    icon: string;
  }[];
  parameters?: {
    name: string;
    value: string;
  }[];
}

export interface AssetCategory {
  id: number;
  name: string;
  assets: {
    basic: {
      id: number;
      name: string;
      entityType: "asset";
      statusIconConfig: StatusIconConfig;
      lastDataReceivedTime?: string | Date | null;
      showDeviceOfflineTag?: boolean;
    };
    assetInfo: {
      make: string;
      model: string;
      serialNumber: string;
    };
    dynamicComponent?: {
      key: string;
      config: any;
    };
  }[];
}

export interface ExpandedAssetDetails {
  quickLinks: QuickLink[];
  alerts: string[];
  criticalStatus: {
    title: string;
    icon: string;
  }[];
  parameters: {
    name: string;
    value: string;
  }[];
} 