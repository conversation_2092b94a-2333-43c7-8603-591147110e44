import { useState, useEffect, useCallback } from 'react';
import { useHistory, useLocation } from 'react-router-dom';

type MapType = 'sites' | 'assets';

const useMapTypeUrl = (defaultMapType: MapType = 'assets') => {
  const history = useHistory();
  const location = useLocation();
  const [mapType, setMapType] = useState<MapType | undefined>(undefined);

  // Initialize mapType from URL on mount, or set default if missing
  useEffect(() => {
    const currentParams = new URLSearchParams(location.search);
    const urlMapType = currentParams.get('map_type') as MapType;
    
    if (urlMapType && (urlMapType === 'sites' || urlMapType === 'assets')) {
      // MapType exists in URL and is valid
      setMapType(urlMapType);
    } else {
      // MapType doesn't exist or is invalid, update URL
      setMapType(defaultMapType);
      
      const newParams = new URLSearchParams();
      
      // Always add mapType first
      newParams.set('map_type', defaultMapType);
      
      // Then add all other existing parameters
      currentParams.forEach((value, key) => {
        if (key !== 'map_type') {
          newParams.set(key, value);
        }
      });
      
      // Update URL with map_type added
      history.replace(`${location.pathname}?${newParams.toString()}`);
    }
  }, []); // Only run on mount

  // Update URL when mapType changes
  const updateMapType = useCallback((newMapType: MapType) => {
    setMapType(newMapType);
    
    const newParams = new URLSearchParams();
    newParams.set('map_type', newMapType);
    
    const getBasePath = (pathname: string): string => {
      // Match patterns like: /anything/views/map-view or /anything/views/map-view/list
      const mapViewPattern = /^(.*\/views\/map-view)(?:\/list)?(?:\/.*)?$/;
      const match = pathname.match(mapViewPattern);
      
      if (match) {
        const baseMapViewPath = match[1]; // e.g., "/some/path/views/map-view"
        
        // Check if the original path had /list after map-view
        const hasListSegment = pathname.includes(`${baseMapViewPath}/list`);
        
        if (hasListSegment) {
          return `${baseMapViewPath}/list`;
        } else {
          return baseMapViewPath;
        }
      }
      
      // Fallback: return original pathname if pattern doesn't match
      return pathname;
    };
    
    const basePath = getBasePath(location.pathname);
    history.push(`${basePath}?${newParams.toString()}`);

  }, [history, location.pathname, location.search]);

  return [mapType, updateMapType] as const;
};

export default useMapTypeUrl;
