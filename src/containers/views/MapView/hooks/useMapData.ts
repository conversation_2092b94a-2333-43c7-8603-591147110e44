import { useEffect, useState, useRef, useMemo } from "react";
import { getMapDataV4, getSummaryDataV4 } from "@datoms/js-sdk";
import _cloneDeep from "lodash/cloneDeep";
import { useGlobalContext } from "../../../../store/globalStore";
import {getKpiFullConfig } from "../configs/kpi-config";
import { getFilterData } from "../configs/filter-config";
import { useKPIFilter } from "./useKPIFilter";
import { useSocketUpdate } from "./useSocketUpdate";
import { getMapValidData, validateData } from "../../utils/validateData";

const useMapData = (entityId?: number, entityType?: string, selectedMapType?: string, dataUpdatedFromSocketRef?: any) => {
  const [mapMarkers, setMapMarkers] = useState<any[]>([]);
  const [listItems, setListItems] = useState<any[]>([]);
  const [entityDetails, setentityDetails] = useState<any>(null);
  const [legendConfig, setLegendConfig] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [entityDetailsLoading, setentityDetailsLoading] = useState(true);
  const [kpiData, setKpiData] = useState<any[]>([]);
  const [filterMap, setFilterMap] = useState<any>({});
  const [filters, setFilters] = useState<any[]>([]);
  const [dependentFilters, setDependentFilters] = useState<any>({});
  const [searchText, setSearchText] = useState<string | undefined>(undefined);
  const [selectedFilters, setSelectedFilters] = useState<any>({});
  const filterRef = useRef<any>(null);

  const [isFilterLoaded, setIsFilterLoaded] = useState(false);

  const context = useGlobalContext();
  const {
    enabled_features,
    currentUserPreferences: user_preferences,
    application_id,
    client_id,
  } = context;


  const filterDataRef = useMemo(() => {
    return getFilterData(selectedMapType, application_id);
  }, [selectedMapType, application_id]);

  const kpiFullConfig = useMemo(() => {
    return getKpiFullConfig(selectedMapType);
  }, [selectedMapType]);

  const {
    handleKPIClick,
    updateKpiSelection,
    getSelectedFilters,
    selectedKpiKey,
  } = useKPIFilter(filterRef, filterDataRef, setLoading, setFilterMap, kpiFullConfig);

  const additionalQuery = useMemo(() => {
    let query = {};
    if (client_id !== 1) {
      query = {
        "info.end_user_org_id": client_id,
      }
    }
    return query;
  }, [client_id]);

  const processFilters = () => {
    const depFilters: any = {};
    const filters: any = [];
    const filterQuery: any = {};

    const filterData = filterDataRef; 
    filterData.forEach((filter: any) => {
      const isFeatureEnabled =
        !filter.feature_key ||
        enabled_features.find(
          (feature: any) => feature.feature_key === filter.feature_key || true,
        );
      if (!isFeatureEnabled) return;

      filters.push(filter);
      if (filter.url_name && filter.dependent_filters) {
        depFilters[filter.url_name] = filter.dependent_filters;
      }
      if (filter.url_name && filter.query_key) {
        filterQuery[filter.url_name] = filter.query_key;
      }
    });

    setFilterMap(filterQuery);
    setFilters(filters);
    setDependentFilters(depFilters);
  };

  const getFinalFilters = (
    selectedFilters: any,
    modified = false,
    includeFilters: any[] = [],
  ) => {
    if (!filterRef.current || !selectedFilters) {
      console.log("FilterRef not ready, returning empty filters");
      return {};
    }

    const finalFilters = Object.fromEntries(
      Object.entries(selectedFilters || {})
        .filter(
          ([key, value]) =>
            value !== "" &&
            !(includeFilters.length > 0 && !includeFilters.includes(key)),
        )
        .map(([key, value]) => {
          // Parse string booleans to actual boolean values
          if (value === "true") return [filterMap[key] || key, true];
          if (value === "false") return [filterMap[key] || key, false];
          return [filterMap[key] || key, value];
        }),
    );

    if (modified) {
      const modifiedFilters: Record<string, any> = {};
      for (const key in finalFilters) {
        const value = finalFilters[key];
        if (Array.isArray(value)) {
          if (value.length > 1) {
            modifiedFilters[key] = { in: value };
          } else if (value.length === 1) {
            modifiedFilters[key] = value[0];
          }
        } else {
          modifiedFilters[key] = value;
        }
      }
      return modifiedFilters;
    }
    return finalFilters;
  };

  const getKpiData = async (selectedFilters = getSelectedFilters()) => {
    // Process each KPI configuration in the array
    const kpiPromises = kpiFullConfig.map(async (kpiItem) => {
      const finalFilters = getFinalFilters(
        selectedFilters,
        false,
        kpiItem.includeFilters,
      );

      const response = await getSummaryDataV4(client_id, {
        apiType: kpiItem.apiType,
        dataPathExp: kpiItem.dataPathExp,
        timezone: user_preferences.timezone,
        apiConfig: {
          ...kpiItem.apiConfig,
          kpi_queries: getKpiPayload(
            kpiItem.apiConfig.kpi_queries,
            finalFilters,
            searchText,
          ),
        },
        data: kpiItem.data,
      });

      return {
        config: kpiItem.config,
        data: response.data,
      };
    });

    // Wait for all KPI requests to complete and return results
    return Promise.all(kpiPromises);
  };

  const fetchMapData = async (showLoading = true) => {
    try {
      if (showLoading) {
        setLoading(true);
      }

      const finalFilters = getFinalFilters(getSelectedFilters(), true);

      const mapDataPromise = getMapDataV4(client_id, {
        api_config: {
          map_type: selectedMapType === "sites" ? "site" : "asset",
          api_query: {
            limit: 1000,
            filter: { ...finalFilters, ...additionalQuery },  // TODO: This can be removed in future
            search: searchText,
          },
        },
      });

      // Run both API calls in parallel
      const [mapData, kpiResults] = await Promise.all([
        mapDataPromise,
        getKpiData(),
      ]);

      // Set all state after both calls complete
      setKpiData(kpiResults);
      const { markers, listItems, legendData } = getMapValidData(mapData);
      setMapMarkers(markers);
      setListItems(listItems);
      setLegendConfig(legendData);
      
    } catch (error) {
      console.log("error in mapData: ...", error);
    } finally {
      if (showLoading) {
        setLoading(false);
      }
    }
  };

  const fetchentityDetails = async (showLoading = true) => {
    try {
      if (showLoading) {
        setentityDetailsLoading(true);
      }
      const entityDetails = await getMapDataV4(client_id, {
        entity_id: entityId,
        entity_type: entityType,
      });
      const validatedData:any = validateData(entityDetails?.data, {}, "object");
      setentityDetails(validatedData);
    } catch (error) {
      console.log("error in entityDetails: ...", error);
    } finally {
      if (showLoading) {
        setentityDetailsLoading(false);
      }
    }
  };

  // Combined fetch function for socket events
  const fetchAllData = async (showLoading = false) => {
    console.log("fetchAllData by socket abc: ");
    dataUpdatedFromSocketRef.current = true;
    await fetchMapData(showLoading);
    
    if (entityId && entityType && client_id) {
      await fetchentityDetails(showLoading);
    }
  };

  // Socket integration
  const entityIds = useMemo(() => {
    const siteIds: number[] = [];
    const assetIds: number[] = [];

    mapMarkers.forEach((item) => {
      if (item.entityType === "site") {
        siteIds.push(item.entityId);
      } else if (item.entityType === "asset") {
        assetIds.push(item.entityId);
      }
    });

    return { siteIds, assetIds };
  }, [
    mapMarkers
      .map(item => `${item.entityType}:${item.entityId}`)
      .sort()
      .join(',')
  ]);

  const { triggerDataUpdate } = useSocketUpdate({
    entityIds,
    fetchData: fetchAllData,
    client_id,
    application_id,
  });

  useEffect(() => {
    processFilters();

    if (isFilterLoaded) {
      dataUpdatedFromSocketRef.current = false;
      triggerDataUpdate(fetchMapData);
    }
  }, [selectedFilters, selectedKpiKey, isFilterLoaded]);

  useEffect(() => {
    if (entityId && entityType && client_id) {
      dataUpdatedFromSocketRef.current = false;
      triggerDataUpdate(fetchentityDetails);
    }
  }, [entityId, entityType, client_id]);

  useEffect(() => {
    if (searchText === undefined) return;

    const debouncedSearch = setTimeout(() => {
      dataUpdatedFromSocketRef.current = false;
      triggerDataUpdate(fetchMapData);
    }, 500);
    return () => clearTimeout(debouncedSearch);
  }, [searchText]);

  return {
    mapMarkers,
    listItems,
    entityDetails,
    legendConfig,
    kpiData,
    loading,
    entityDetailsLoading,
    filters,
    filterRef,
    dependentFilters,
    setSelectedFilters,
    setSearchText,
    selectedKpiKey,
    handleKPIClick,
    updateKpiSelection,
    setIsFilterLoaded
  };
};

const getKpiPayload = (
  body: any[],
  finalFilters: Record<string, any>,
  searchText: string | undefined,
) => {
  const toBeFiltered =
    (finalFilters && Object.keys(finalFilters)?.length > 0) || searchText;
  if (!body || !toBeFiltered) {
    return body;
  }

  // Create a deep clone to avoid mutating the original body
  const updatedBody = _cloneDeep(body);

  // Process each KPI item in the body
  updatedBody.forEach((kpiItem: any) => {
    // Initialize filters array if it doesn't exist
    if (!kpiItem.filters) {
      kpiItem.filters = [];
    }

    // Add each filter from finalFilters to the KPI item
    Object.entries(finalFilters).forEach(([key, value]) => {
      // Skip empty values
      if (value === undefined || value === null || value === "") {
        return;
      }

      // Handle array values (for multi-select filters)
      if (Array.isArray(value)) {
        if (value.length > 0) {
          kpiItem.filters.push({
            field: key,
            operator: "in",
            value: value,
          });
        }
      }
      // Handle regular values
      else {
        kpiItem.filters.push({
          field: key,
          operator: "eq",
          value: value,
        });
      }
    });

    // Add search if it exists in finalFilters
    if (searchText) {
      kpiItem.search = searchText.toString().trim();
    }
  });

  return updatedBody;
};

export default useMapData;
