import { useRef, useState } from "react";
import _findIndex from "lodash/findIndex";

export const useKPIFilter = (filterRef: any, filters: any, setLoading: any, setFilterMap: any, kpiFullConfig: any) => {
  const kpiChangeRef = useRef<any>(null);
  const [selectedKpiKey, setSelectedKpiKey] = useState<any>("");
  const [kpiFilters, setKpiFilters] = useState<any>(undefined);

  const handleKPIClick = (key: string | null, itemData?: any) => {
    if (!itemData?.filter_query) {
      return;
    }

    let prevKeyExistsInMainFilter: any = null;

    if (kpiFullConfig?.[0]?.data?.length) {
      const findKpi = kpiFullConfig?.[0]?.data.find((kpi: any) => {
        return selectedKpiKey === kpi.key;
      });
      if (findKpi) {
        prevKeyExistsInMainFilter = existInMainFilter(
          null,
          findKpi.filter_query,
        );
      }
    }

    setSelectedKpiKey(!key ? "" : itemData.key);
    setLoading(true);

    const mainFilterConfig: any = existInMainFilter(key, itemData.filter_query);
    console.log("mainFilterConfig", mainFilterConfig);
    if (!key) {
      if (mainFilterConfig) {
        filterRef.current?.setFilters(mainFilterConfig);
      } else {
        setKpiFilters({});
      }
      return;
    }

    const filterQuery = itemData.filter_query;
    const kpifilterMap: any = {
      [filterQuery.key]: filterQuery.query_key,
    };
    const filtersToApply = {
      [key]: filterQuery,
    };

    setFilterMap((prev: any) => ({ ...prev, ...kpifilterMap }));
    if (mainFilterConfig) {
      kpiChangeRef.current = true;
      filterRef.current?.setFilters(mainFilterConfig);
    } else {
      if (prevKeyExistsInMainFilter) {
        kpiChangeRef.current = true;
        filterRef.current?.setFilters(prevKeyExistsInMainFilter);
      }
      setKpiFilters(filtersToApply);
    }
  };

  const existInMainFilter = (key: string | null, filterQuery: any) => {
    const findIndex = _findIndex(filters, (filter: any) => filter.url_name === filterQuery.key);
    if(findIndex > -1) {
      return {
        index: findIndex,
        value: key ? filterQuery?.value : undefined
      };
    }
  };

  const updateKpiSelection = () => {
    if(kpiChangeRef?.current && kpiChangeRef.current === true) {
      kpiChangeRef.current = false;
      return;
    }
    const selectedFilters = getSelectedFilters();
    if (kpiFullConfig?.[0]?.data?.length) {
      const findKpis = kpiFullConfig?.[0]?.data.filter((kpi: any) => {
        const filterQuery = kpi.filter_query;
        if (!filterQuery) return false;
        const mainFilterConfig = existInMainFilter(kpi.key, filterQuery);
        return mainFilterConfig;
      });
      if (findKpis.length) {
        const findMatchingValue = findKpis.find((kpi: any) => {
          const filterQuery = kpi.filter_query;
          return filterQuery.value === selectedFilters?.[filterQuery.key];
        });
        // kpiRef?.current?.setSelectedKey(
        //   findMatchingValue ? findMatchingValue.key : null,
        // );
        console.log("updateKey_1", findMatchingValue ? findMatchingValue.key : null);
        setSelectedKpiKey(findMatchingValue ? findMatchingValue.key : "");
      }
    }
  };

  const getSelectedFilters = () => {
    const mainFilter = filterRef?.current?.getFilters() || {};
    let kpiFilter = {};
    if (selectedKpiKey && kpiFilters?.[selectedKpiKey]) {
      kpiFilter = {
        [kpiFilters[selectedKpiKey].query_key]: kpiFilters[selectedKpiKey].value,
      };
    }
    const filters: any = {
      ...mainFilter,
      ...kpiFilter,
    };

    return filters;
  };

  return {
    handleKPIClick,
    updateKpiSelection,
    getSelectedFilters,
    selectedKpiKey,
  }
};
