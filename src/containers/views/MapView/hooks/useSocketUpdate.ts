import { useEffect, useRef, useCallback } from "react";
import {
  establishSocketConnection,
  disconnectSocketConnection,
  subscribeForSitesUpdates,
  subscribeForThingsUpdates,
  subscribeForEntityUpdates,
  subscribeForEventsUpdates,
} from "@datoms/js-sdk";

interface UseSocketUpdateParams {
  entityIds: { siteIds: number[]; assetIds: number[] };
  fetchData: () => void;
  client_id: number;
  application_id: number;
}

export const useSocketUpdate = ({
  entityIds,
  fetchData,
  client_id,
  application_id,
}: UseSocketUpdateParams) => {
  const socketRef = useRef<any>(null);
  const lastUpdateTimeRef = useRef<number | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const fetchDataRef = useRef(fetchData);

  // Always keep the ref updated with the latest fetchData
  useEffect(() => {
    fetchDataRef.current = fetchData;
  }, [fetchData]);

  // Handle socket data updates with throttling
  const handleSocketUpdate = () => {
    const currentTime = Date.now();

    if (!lastUpdateTimeRef.current) {
      lastUpdateTimeRef.current = currentTime;
      fetchDataRef.current();
      return;
    }

    const timeElapsed = currentTime - lastUpdateTimeRef.current;

    if (timeElapsed >= 15000) {
      fetchDataRef.current();
      lastUpdateTimeRef.current = Date.now();
      return;
    }

    if (timeoutRef.current) {
      return;
    }

    timeoutRef.current = setTimeout(() => {
      fetchDataRef.current();
      lastUpdateTimeRef.current = Date.now();
      timeoutRef.current = null;
    }, 15000 - timeElapsed);
  };

  const triggerDataUpdate = useCallback((fetchPageData: () => void) => {
    // Clear any scheduled timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    // Make API call
    fetchPageData();

    // Update the last update time
    lastUpdateTimeRef.current = Date.now();
  }, []); 

  // Socket setup and subscription management
  useEffect(() => {
    const { siteIds, assetIds } = entityIds;

    // Establish socket connection
    console.log("Establishing new socket connection");
    socketRef.current = establishSocketConnection();

    // Handle socket connection
    socketRef.current.on("connect", () => {
      console.log("Socket connected");
      // TODO: connection will be established only once per page load, after that we'll subscribe and unsubscribe based on entityIds change. For that we'll need to store the entityIds for unsubscribing.

      // Subscribe for site updates
      subscribeForSitesUpdates(socketRef.current, siteIds);
      subscribeForEntityUpdates(socketRef.current, siteIds, "site");

      // Subscribe for asset updates
      subscribeForThingsUpdates(socketRef.current, assetIds); // by default for agg. period= 0
      subscribeForEventsUpdates(socketRef.current, client_id, application_id);
    });

    // Handle raw data updates
    socketRef.current.on("new_data_generated_for_site", () => {
      console.log("Received raw data update for sites");
      handleSocketUpdate();
    });
    socketRef.current.on("new_data_generated_for_station", () => {
      console.log("Received raw data update for assets");
      handleSocketUpdate();
    });

    // Handle event updates (e.g. alerts)
    socketRef.current.on("update", () => {
      console.log("Received entity update for sites");
      handleSocketUpdate();
    });
    socketRef.current.on("new_event_generated", () => {
      console.log("Received event update for assets");
      handleSocketUpdate();
    });

    // Cleanup function
    return () => {
      // Disconnect socket
      if (socketRef.current) {
        console.log("Disconnecting socket on cleanup");
        disconnectSocketConnection(socketRef.current);
        socketRef.current = null;
      }

      // Clear timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }

      if (lastUpdateTimeRef.current) {
        lastUpdateTimeRef.current = null;
      }
    };
  }, [entityIds]); 

  // Separate useEffect for the periodic interval
  useEffect(() => {
    intervalRef.current = setInterval(() => {
      if (
        lastUpdateTimeRef.current &&
        Date.now() - lastUpdateTimeRef.current < 15000
      ) {
        return;
      }
      lastUpdateTimeRef.current = Date.now();
      fetchDataRef.current();
    }, 60000);

    return () => {
      // Clear interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, []); // Empty dependency array is fine here since we use fetchDataRef

  return { triggerDataUpdate };
};
