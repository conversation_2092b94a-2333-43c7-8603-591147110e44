import { useEffect, useState, useRef, useCallback } from "react";
import { useHistory, useLocation, useParams } from "react-router-dom";

interface UrlDrawerState {
  isListDrawerOpen: boolean;
  isDetailsDrawerOpen: boolean;
  entityType?: "asset" | "site";
  entityId?: number;
  currentPath: string;
  selectedPointer: SelectedPointer | null;
}

type SelectedPointer = {
  lat: number;
  long: number;
  entityId?: number;
  entityType?: "asset" | "site";
}

const useUrlDrawerToggle = (dataUpdatedFromSocketRef: any): [
  UrlDrawerState,
  {
    openListDrawer: () => void;
    closeListDrawer: () => void;
    openDetailsDrawer: (entityType: "asset" | "site", entityId: number, lat?: number, long?: number) => void;
    closeDetailsDrawer: () => void;
    closeAllDrawers: () => void;
    afterDetailsDrawerClose: (open: boolean) => void;
    updateSelectedPointer: (pointer: SelectedPointer | null) => void;
  }
] => {
  const history = useHistory();
  const location = useLocation();
  const params = useParams<{ entityType?: "asset" | "site"; id?: string }>();
  
  // Flag to track update source
  const isUpdatingFromMap = useRef(false);
  
  const [drawerState, setDrawerState] = useState<UrlDrawerState>({
    isListDrawerOpen: false,
    isDetailsDrawerOpen: false,
    entityType: undefined,
    entityId: undefined,
    currentPath: location.pathname,
    selectedPointer: null
  });

  // Get the base URL path for the map view
  const getBaseMapViewPath = () => {
    // Extract the base path up to "views/map-view"
    const fullPath = location.pathname;
    const regex = /(.*\/views\/map-view)/;
    const match = fullPath.match(regex);
    return match ? match[1] : "/views/map-view";
  };

  // Preserve query parameters when changing URLs
  const preserveQueryParams = (newPathname: string) => {
    return `${newPathname}${location.search}`;
  };

  // Calculate path based on drawer state
  const calculatePath = (state: {
    isListDrawerOpen: boolean;
    isDetailsDrawerOpen: boolean;
    entityType?: "asset" | "site";
    entityId?: number;
  }) => {
    const baseUrl = getBaseMapViewPath();
    
    if (state.isListDrawerOpen) {
      if (state.isDetailsDrawerOpen && state.entityType && state.entityId) {
        return `${baseUrl}/list/${state.entityType}/${state.entityId}`;
      }
      return `${baseUrl}/list`;
    } else if (state.isDetailsDrawerOpen && state.entityType && state.entityId) {
      return `${baseUrl}/${state.entityType}/${state.entityId}`;
    }
    
    return baseUrl;
  };

  // Update URL without waiting for it to complete
  const updateUrl = useCallback((newPath: string) => {
    const fullUrl = preserveQueryParams(newPath);
    if (fullUrl !== location.pathname + location.search) {
      history.push(fullUrl);
    }
  }, [history, location.search, location.pathname]);

  // Update drawer state and URL
  const updateDrawerState = useCallback((newState: Partial<UrlDrawerState>) => {
    const hasPointerChanged = newState.selectedPointer !== undefined && 
      JSON.stringify(newState.selectedPointer) !== JSON.stringify(drawerState.selectedPointer);
    
    const hasEntityChanged = 
      (newState.entityId !== undefined && newState.entityId !== drawerState.entityId) ||
      (newState.entityType !== undefined && newState.entityType !== drawerState.entityType);
      
    const hasDrawerStateChanged =
      (newState.isDetailsDrawerOpen !== undefined && newState.isDetailsDrawerOpen !== drawerState.isDetailsDrawerOpen) ||
      (newState.isListDrawerOpen !== undefined && newState.isListDrawerOpen !== drawerState.isListDrawerOpen);
    
    if (!hasPointerChanged && !hasEntityChanged && !hasDrawerStateChanged) {
      return;
    }
    
    setDrawerState(prevState => {
      const updatedState = { ...prevState, ...newState };
      const newPath = calculatePath(updatedState);
      
      if (!isUpdatingFromMap.current) {
        updateUrl(newPath);
      }
      
      return {
        ...updatedState,
        currentPath: newPath
      };
    });
  }, [drawerState, updateUrl]);

  // Handle URL changes - must properly sync URL to state
  useEffect(() => {
    const pathname = location.pathname;
    const entityType = params.entityType;
    const entityId = params.id ? parseInt(params.id, 10) : undefined;
    
    const isListPath = pathname.includes('/list');
    const hasEntityParams = !!(entityType && entityId);
    
    isUpdatingFromMap.current = true;
    
    setDrawerState(prevState => ({
      ...prevState,
      isListDrawerOpen: isListPath,
      isDetailsDrawerOpen: hasEntityParams,
      entityType: entityType as "asset" | "site",
      entityId: entityId,
      currentPath: pathname,
      selectedPointer: hasEntityParams ? {
        lat: prevState.selectedPointer?.lat ?? 0, 
        long: prevState.selectedPointer?.long ?? 0,
        entityId: entityId,
        entityType: entityType as "asset" | "site"
      } : null
    }));
    
    const timer = setTimeout(() => {
      isUpdatingFromMap.current = false;
    }, 100); // A small delay to ensure state updates from URL don't trigger URL updates
    
    return () => clearTimeout(timer);
  }, [location.pathname, params.entityType, params.id]);

  const openListDrawer = useCallback(() => {
    updateDrawerState({ isListDrawerOpen: true });
  }, [updateDrawerState]);

  const closeListDrawer = useCallback(() => {
    updateDrawerState({ isListDrawerOpen: false });
  }, [updateDrawerState]);

  const openDetailsDrawer = useCallback((entityType: "asset" | "site", entityId: number, lat?: number, long?: number) => {
    const pointer: SelectedPointer = {
      lat: lat ?? 0,
      long: long ?? 0,
      entityId,
      entityType
    };
    
    updateDrawerState({
      isDetailsDrawerOpen: true,
      entityType: entityType,
      entityId,
      selectedPointer: pointer
    });
  }, [updateDrawerState]);

  const closeDetailsDrawer = useCallback(() => {
    dataUpdatedFromSocketRef.current = false;
    updateDrawerState({ 
      isDetailsDrawerOpen: false,
      selectedPointer: null
    });
  }, [updateDrawerState]);

  const closeAllDrawers = useCallback(() => {
    dataUpdatedFromSocketRef.current = false;
    updateDrawerState({
      isListDrawerOpen: false,
      isDetailsDrawerOpen: false,
      selectedPointer: null
    });
  }, [updateDrawerState]);

  const afterDetailsDrawerClose = useCallback((open: boolean) => {
    if(!open) {
      updateDrawerState({
        entityType: undefined,
        entityId: undefined
      });
    }
  }, [updateDrawerState]);

  const updateSelectedPointer = useCallback((pointer: SelectedPointer | null) => {
    updateDrawerState({ selectedPointer: pointer });
  }, [updateDrawerState]);

  console.log("drawerState: ", drawerState);

  return [
    drawerState,
    {
      openListDrawer,
      closeListDrawer,
      openDetailsDrawer,
      closeDetailsDrawer,
      closeAllDrawers,
      afterDetailsDrawerClose,
      updateSelectedPointer
    },
  ];
};

export default useUrlDrawerToggle;
