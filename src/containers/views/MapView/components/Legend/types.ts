// Status item within asset/site categories
interface StatusItem {
  statusColor?: string; // Optional for items using customIcon
  title: string;
  customIcon?: string; // e.g., LockIcon
}

// Structure for asset status categories
interface AssetStatusCategory {
  categoryIcon: string;
  status: StatusItem[];
}

// Structure for site status information
interface SiteStatusInfo {
  categoryIcon: string;
  status: StatusItem[];
}

// Structure for device status items
interface DeviceStatusItem {
  color: string;
  title: string;
}

// Structure for general legend icons (left/right)
interface LegendIconItem {
  icon: string; // Path or URL
  title: string;
}

// Props for the Legend component, matching legendConfig
export interface LegendProps {
  assetStatusIcons?: AssetStatusCategory[];
  siteStatusIcons?: SiteStatusInfo;
  deviceStatus?: DeviceStatusItem[];
  leftIconsSite?: LegendIconItem[];
  rightIconsSite?: LegendIconItem[];
  leftIconsAsset?: LegendIconItem[];
  rightIconsAsset?: LegendIconItem[];
  loading?: boolean;
}

// Internal display structure for rendering legend items
export interface LegendDisplayItem {
  statusColor: string;
  deviceStatusColor?: string;
  title: string;
  entityType: 'asset' | 'site';
  customIcon?: string; // Optional custom icon for special status items
}

// Props for LegendDetailsSection component
export interface LegendDetailsSectionProps {
  onClose: () => void;
  assetStatusIcons?: AssetStatusCategory[];
  siteStatusIcons?: SiteStatusInfo;
  deviceStatus?: DeviceStatusItem[];
  leftIconsSite?: LegendIconItem[];
  rightIconsSite?: LegendIconItem[];
  leftIconsAsset?: LegendIconItem[];
  rightIconsAsset?: LegendIconItem[];
}

// Props for LegendDetailsBox component
export interface LegendDetailsBoxProps {
  title: string;
  primaryStatusIcons: any;
  deviceStatus: any[] | undefined;
  leftIcons: any[] | undefined;
  rightIcons: any[] | undefined;
}

// Props for IconExplainingText component
export interface IconExplainingTextProps {
  text: React.ReactNode;
  lineType: 'upper' | 'lower' | 'straight';
  lineWidth?: number;
  className?: string;
}

// Props for Line sub-components in IconExplainingText
export interface LineProps {
  lineWidth?: number;
}

// Props for IconWithExplanation component
export interface IconWithExplanationProps {
  entityType?: 'site' | 'asset';
  statusColor?: string;
  deviceStatusColor?: string;
  categoryIcon?: string;
  sectionTitle?: string;
  structure?: string;
  text?: string;
  customIcon?: string;
}

// Type for line types in IconWithExplanation
export type LineType = 'upper' | 'straight' | 'lower';

// Type for structure map in IconWithExplanation
export type StructureMap = {
  [key: string]: {
    text: React.ReactNode;
    lineWidth?: number;
  };
};
