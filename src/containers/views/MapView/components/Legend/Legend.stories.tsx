import React from 'react';
import Legend from './Legend';
import { action } from '@storybook/addon-actions';
import {
  Canvas,
  Source,
  Stories,
  Subtitle,
  Title,
  Controls,
} from "@storybook/blocks";
import { Table } from "@storybook/components";

interface RowProps {
  key: string;
  required: string;
  allowedValues: string;
  description: string;
  defaultValue: string;
}

const CustomPropsTable = ({ rows }: { rows: RowProps[] }) => (
  <Table>
    <thead>
      <tr>
        <th>Key</th>
        <th>Required</th>
        <th>Allowed Values</th>
        <th>Default Value</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      {rows?.map((row: RowProps) => (
        <tr key={row.key}>
          <td>{row.key}</td>
          <td>{row.required}</td>
          <td>{row.allowedValues}</td>
          <td>{row.defaultValue}</td>
          <td>{row.description}</td>
        </tr>
      ))}
    </tbody>
  </Table>
);

/**
 * The Legend component provides a visual explanation of the various icons and colors used on the map.
 * It shows a condensed view initially and can expand to a detailed view with more information.
 */
export default {
  title: 'Views/MapView/Legend',
  component: Legend,
  tags: ["autodocs"],
  parameters: {
    componentSubtitle: "A legend component for displaying map status icons and indicators",
    docs: {
      page: () => (
        <>
          <Title>Legend</Title>
          <p>
            This component wraps status icons for sites and assets on the map. It uses the IconFrame component to display status indicators.
            Shows a condensed view initially and expands to a detailed view with more information.
          </p>
          <CustomPropsTable
            rows={[
              {
                key: "siteStatusIcons",
                required: "N",
                allowedValues: "object",
                defaultValue: "undefined",
                description: "Configuration for site status icons with a categoryIcon and status array",
              },
              {
                key: "siteStatusIcons.categoryIcon",
                required: "Y*",
                allowedValues: "string (URL)",
                defaultValue: "-",
                description: "URL of the category icon to display. Required if siteStatusIcons is provided",
              },
              {
                key: "siteStatusIcons.status",
                required: "Y*",
                allowedValues: "Array<StatusItem>",
                defaultValue: "[]",
                description: "Array of status items with statusColor and title. Required if siteStatusIcons is provided",
              },
              {
                key: "assetStatusIcons",
                required: "N",
                allowedValues: "Array<AssetStatusCategory>",
                defaultValue: "undefined",
                description: "Array of asset status categories, each with categoryIcon and status array",
              },
              {
                key: "assetStatusIcons[].categoryIcon",
                required: "Y*",
                allowedValues: "string (URL)",
                defaultValue: "-",
                description: "URL of the category icon to display. Required for each asset category",
              },
              {
                key: "assetStatusIcons[].status",
                required: "Y*",
                allowedValues: "Array<StatusItem>",
                defaultValue: "[]",
                description: "Array of status items with statusColor/customIcon and title. Required for each asset category",
              },
              {
                key: "deviceStatus",
                required: "N",
                allowedValues: "Array<DeviceStatusItem>",
                defaultValue: "undefined",
                description: "Configuration for device status indicators",
              },
              {
                key: "deviceStatus[].color",
                required: "Y*",
                allowedValues: "string (hex color)",
                defaultValue: "-",
                description: "Color for the device status indicator. Required for each device status item",
              },
              {
                key: "deviceStatus[].title",
                required: "Y*",
                allowedValues: "string",
                defaultValue: "-",
                description: "Title for the device status. Required for each device status item",
              },
              {
                key: "leftIconsSite",
                required: "N",
                allowedValues: "Array<LegendIconItem>",
                defaultValue: "undefined",
                description: "Left indicator icons for sites",
              },
              {
                key: "rightIconsSite",
                required: "N",
                allowedValues: "Array<LegendIconItem>",
                defaultValue: "undefined",
                description: "Right indicator icons for sites",
              },
              {
                key: "leftIconsAsset",
                required: "N",
                allowedValues: "Array<LegendIconItem>",
                defaultValue: "undefined",
                description: "Left indicator icons for assets",
              },
              {
                key: "rightIconsAsset",
                required: "N",
                allowedValues: "Array<LegendIconItem>",
                defaultValue: "undefined",
                description: "Right indicator icons for assets",
              },
            ]}
          />
          <Subtitle>Data Structure Examples</Subtitle>
          <Source
            language="javascript"
            code={`
// Site Status Icons
const siteStatusIcons = {
  categoryIcon: "icon-url.svg",
  status: [
    { statusColor: "#008C3F", title: "Compliant" },
    { statusColor: "#964B00", title: "Non-Compliant" }
  ]
};

// Asset Status Icons
const assetStatusIcons = [
  {
    categoryIcon: "dg-icon.svg",
    status: [
      { statusColor: "#008C3F", title: "Running" },
      { statusColor: "#F65F54", title: "Stopped" },
      { customIcon: "lock-icon.svg", title: "Locked" }
    ]
  }
];

// Device Status
const deviceStatus = [
  { color: "#BAEBC7", title: "Online" },
  { color: "#EED960", title: "Partially Online" },
  { color: "#D2D2D2", title: "Offline" }
];

// Indicator Icons
const leftIconsSite = [
  { icon: "device-error.svg", title: "Device Error" }
];
            `}
          />
          <Stories />
        </>
      ),
    },
  },
  argTypes: {
    siteStatusIcons: {
      description: 'Configuration for site status icons. If provided, the component will display site status icons.',
      control: 'object',
    },
    assetStatusIcons: {
      description: 'Configuration for asset status icons. If provided, the component will display asset status icons.',
      control: 'object',
    },
    deviceStatus: {
      description: 'Configuration for device status indicators.',
      control: 'object',
    },
    leftIconsSite: {
      description: 'Left indicator icons for sites.',
      control: 'object',
    },
    rightIconsSite: {
      description: 'Right indicator icons for sites.',
      control: 'object',
    },
    leftIconsAsset: {
      description: 'Left indicator icons for assets.',
      control: 'object',
    },
    rightIconsAsset: {
      description: 'Right indicator icons for assets.',
      control: 'object',
    },
  },
};

// Mock icons for stories
const dgCategoryIcon = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiNmZmZmZmYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLXNldHRpbmdzIj48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIj48L2NpcmNsZT48cGF0aCBkPSJNMTkuNCAxNWExLjY1IDEuNjUgMCAwIDAgLjMzIDEuODJsLjA2LjA2YTIgMiAwIDAgMSAwIDIuODMgMiAyIDAgMCAxLTIuODMgMGwtLjA2LS4wNmExLjY1IDEuNjUgMCAwIDAtMS44Mi0uMzMgMS42NSAxLjY1IDAgMCAwLTEgMS41MVYyMWEyIDIgMCAwIDEtMiAyIDIgMiAwIDAgMS0yLTJ2LS4wOUExLjY1IDEuNjUgMCAwIDAgOSAxOS40YTEuNjUgMS42NSAwIDAgMC0xLjgyLjMzbC0uMDYuMDZhMiAyIDAgMCAxLTIuODMgMCAyIDIgMCAwIDEgMC0yLjgzbC4wNi0uMDZhMS42NSAxLjY1IDAgMCAwIC4zMy0xLjgyIDEuNjUgMS42NSAwIDAgMC0xLjUxLTFIM2EyIDIgMCAwIDEtMi0yIDIgMiAwIDAgMSAyLTJoLjA5QTEuNjUgMS42NSAwIDAgMCA0LjYgOWExLjY1IDEuNjUgMCAwIDAtLjMzLTEuODJsLS4wNi0uMDZhMiAyIDAgMCAxIDAtMi44MyAyIDIgMCAwIDEgMi44MyAwbC4wNi4wNmExLjY1IDEuNjUgMCAwIDAgMS44Mi4zM0g5YTEuNjUgMS42NSAwIDAgMCAxLTEuNTFWM2EyIDIgMCAwIDEgMi0yIDIgMiAwIDAgMSAyIDJ2LjA5YTEuNjUgMS42NSAwIDAgMCAxIDEuNTEgMS42NSAxLjY1IDAgMCAwIDEuODItLjMzbC4wNi0uMDZhMiAyIDAgMCAxIDIuODMgMCAyIDIgMCAwIDEgMCAyLjgzbC0uMDYuMDZhMS42NSAxLjY1IDAgMCAwLS4zMyAxLjgyVjlhMS42NSAxLjY1IDAgMCAwIDEuNTEgMUgyMWEyIDIgMCAwIDEgMiAyIDIgMiAwIDAgMS0yIDJoLS4wOWExLjY1IDEuNjUgMCAwIDAtMS41MSAxeiI+PC9wYXRoPjwvc3ZnPg==";
const solarPumpCategoryIcon = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiNmZmZmZmYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLWhvbWUiPjxwYXRoIGQ9Ik0zIDlsOS03IDkgN3YxMWEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnoiPjwvcGF0aD48cG9seWxpbmUgcG9pbnRzPSI5IDIyIDkgMTIgMTUgMTIgMTUgMjIiPjwvcG9seWxpbmU+PC9zdmc+";
const LockIcon = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiNmZmZmZmYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLWxvY2siPjxyZWN0IHg9IjMiIHk9IjExIiB3aWR0aD0iMTgiIGhlaWdodD0iMTEiIHJ4PSIyIiByeT0iMiI+PC9yZWN0PjxwYXRoIGQ9Ik03IDExVjdhNSA1IDAgMCAxIDEwIDB2NCI+PC9wYXRoPjwvc3ZnPg==";
const DeviceErrorIcon = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiM5ZTllOWUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLXNsYXNoIj48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCI+PC9jaXJjbGU+PGxpbmUgeDE9IjQuOTMiIHkxPSI0LjkzIiB4Mj0iMTkuMDciIHkyPSIxOS4wNyI+PC9saW5lPjwvc3ZnPg==";
const SystemErrorIcon = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiMyMTk2ZjMiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLWluZm8iPjxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjEwIj48L2NpcmNsZT48bGluZSB4MT0iMTIiIHkxPSIxNiIgeDI9IjEyIiB5Mj0iMTIiPjwvbGluZT48bGluZSB4MT0iMTIiIHkxPSI4IiB4Mj0iMTIuMDEiIHkyPSI4Ij48L2xpbmU+PC9zdmc+";
const TripFaultIcon = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiNmNDQzMzYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLWFjdGl2aXR5Ij48cG9seWxpbmUgcG9pbnRzPSIyMiAxMiAxOCAxMiAxNSAyMSA5IDMgNiAxMiAyIDEyIj48L3BvbHlsaW5lPjwvc3ZnPg==";
const MaintenanceDueIcon = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiNmZjk4MDAiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLWFsZXJ0LXRyaWFuZ2xlIj48cGF0aCBkPSJNMTAuMjkgMy44NkwxLjgyIDE4YTIgMiAwIDAgMCAxLjcxIDNoMTYuOTRhMiAyIDAgMCAwIDEuNzEtM0wxMy43MSAzLjg2YTIgMiAwIDAgMC0zLjQyIDB6Ij48L3BhdGg+PGxpbmUgeDE9IjEyIiB5MT0iOSIgeDI9IjEyIiB5Mj0iMTMiPjwvbGluZT48bGluZSB4MT0iMTIiIHkxPSIxNyIgeDI9IjEyLjAxIiB5Mj0iMTciPjwvbGluZT48L3N2Zz4=";
const ViolationIcon = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiNmNDQzMzYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0iZmVhdGhlciBmZWF0aGVyLXgtY2lyY2xlIj48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCI+PC9jaXJjbGU+PGxpbmUgeDE9IjE1IiB5MT0iOSIgeDI9IjkiIHkyPSIxNSI+PC9saW5lPjxsaW5lIHgxPSI5IiB5MT0iOSIgeDI9IjE1IiB5Mj0iMTUiPjwvbGluZT48L3N2Zz4=";

// Sample data for asset status
const assetStatusIcons = [
  {
    categoryIcon: dgCategoryIcon,
    status: [
      { statusColor: "#008C3F", title: "Running" },
      { statusColor: "#F65F54", title: "Stopped" },
      { customIcon: LockIcon, title: "Locked" }
    ]
  },
  {
    categoryIcon: solarPumpCategoryIcon,
    status: [
      { statusColor: "#22CF61", title: "Connected" },
      { statusColor: "#A6A6A6", title: "Disconnected" }
    ]
  }
];

// Sample data for site status
const siteStatusIcons = {
  categoryIcon: solarPumpCategoryIcon,
  status: [
    { statusColor: "#008C3F", title: "Compliant" },
    { statusColor: "#964B00", title: "Non-Compliant" }
  ]
};

// Sample data for device status
const deviceStatus = [
  { color: "#BAEBC7", title: "Online" },
  { color: "#EED960", title: "Partially Online" },
  { color: "#D2D2D2", title: "Offline" }
];

// Sample data for site indicator icons
const leftIconsSite = [
  { icon: DeviceErrorIcon, title: "Device Error" },
  { icon: SystemErrorIcon, title: "System Error" }
];

const rightIconsSite = [
  { icon: TripFaultIcon, title: "Trip Fault" }
];

// Sample data for asset indicator icons
const leftIconsAsset = [
  { icon: DeviceErrorIcon, title: "Device Error" }
];

const rightIconsAsset = [
  { icon: TripFaultIcon, title: "Trip Fault" },
  { icon: MaintenanceDueIcon, title: "Maintenance Due" },
  { icon: ViolationIcon, title: "Violation" }
];

export const SiteLegend = () => {
  return (
    <div style={{ padding: "20px", background: "#f0f2f5", height: "300px", position: "relative" }}>
      <Legend
        siteStatusIcons={siteStatusIcons}
        deviceStatus={deviceStatus}
        leftIconsSite={leftIconsSite}
        rightIconsSite={rightIconsSite}
      />
    </div>
  );
};

export const AssetLegend = () => {
  return (
    <div style={{ padding: "20px", background: "#f0f2f5", height: "300px", position: "relative" }}>
      <Legend
        assetStatusIcons={assetStatusIcons}
        deviceStatus={deviceStatus}
        leftIconsAsset={leftIconsAsset}
        rightIconsAsset={rightIconsAsset}
      />
    </div>
  );
};

export const WighoutDeviceLegend = () => {
  return (
    <div style={{ padding: "20px", background: "#f0f2f5", height: "300px", position: "relative" }}>
      <Legend
        siteStatusIcons={siteStatusIcons}
      />  
    </div>
  );
};

export const CombinedLegend = () => {
  return (
    <div style={{ padding: "20px", background: "#f0f2f5", height: "300px", position: "relative" }}>
      <Legend
        siteStatusIcons={siteStatusIcons}
        assetStatusIcons={assetStatusIcons}
        deviceStatus={deviceStatus}
        leftIconsSite={leftIconsSite}
        rightIconsSite={rightIconsSite}
        leftIconsAsset={leftIconsAsset}
        rightIconsAsset={rightIconsAsset}
      />
    </div>
  );
};
