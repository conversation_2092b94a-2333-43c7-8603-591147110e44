import React from 'react';
import { ArrowLeftOutlined } from '@ant-design/icons';
import './style.less'; // Correct path now
import { Button } from 'antd';
import LegendDetailsBox from '../LegendDetailsBox';
import { LegendDetailsSectionProps } from '../../types';

/**
 * Displays the detailed legend section, including site and/or asset details.
 *
 * @param props - The properties passed to the component.
 * @param props.onClose - Callback function to close the details section.
 * @param props.assetStatusIcons - Configuration for asset status icons.
 * @param props.siteStatusIcons - Configuration for site status icons.
 * @param props.deviceStatus - Configuration for device status.
 * @param props.leftIconsSite - Left indicator icons for sites.
 * @param props.rightIconsSite - Right indicator icons for sites.
 * @param props.leftIconsAsset - Left indicator icons for assets.
 * @param props.rightIconsAsset - Right indicator icons for assets.
 * @returns The rendered legend details section.
 */
const LegendDetailsSection: React.FC<LegendDetailsSectionProps> = (props) => {
  const isSite = props.siteStatusIcons?.status?.length > 0;
  const isAsset = props.assetStatusIcons?.[0]?.status?.length > 0;

  const legendBoxSiteConfig = {
    primaryStatusIcons: props.siteStatusIcons,  
    deviceStatus: props.deviceStatus,
    leftIcons: props.leftIconsSite,
    rightIcons: props.rightIconsSite,
  }

  const legendBoxAssetConfig = {
    primaryStatusIcons: props.assetStatusIcons,
    deviceStatus: props.deviceStatus,
    leftIcons: props.leftIconsAsset,
    rightIcons: props.rightIconsAsset,
  }

  return (
    <div id="legend-details-section">
      <div className="details-header">
        <Button shape='circle' className="back-button" onClick={() => props.onClose()} aria-label="Go back">
            <ArrowLeftOutlined />
        </Button>
        <span className="details-title">Legends</span>
      </div>
      <div className="details-content-area">
        {isSite && <LegendDetailsBox title="Site" {...legendBoxSiteConfig}/>}
        {isAsset && <LegendDetailsBox title="Asset" {...legendBoxAssetConfig}/>}
      </div>
    </div>
  );
};

export default LegendDetailsSection; 