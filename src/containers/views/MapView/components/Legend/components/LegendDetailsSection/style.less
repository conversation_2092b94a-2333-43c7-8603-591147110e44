#legend-details-section {
  height: 100%;
  background-color: #ffffff; /* Replaced @component-background */
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px 24px 36px 20px;

    .ant-btn {
        height: 30px;
        min-width: 30px;
    }
}

.details-header {
  display: flex;
  align-items: center;
}

.back-button {
  margin-right: 15px;
}

.details-title {
  font-size: 14px;
  font-weight: 500;
  color: #000000; /* Replaced @heading-color */
}

.details-content-area {
  flex-grow: 1;
  display: flex;
  gap: 20px;
  overflow: auto;
}