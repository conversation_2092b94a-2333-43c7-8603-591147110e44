import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import LegendDetailsSection from './LegendDetailsSection';
import { LegendDetailsSectionProps } from '../../types';

// Mock the LegendDetailsBox component
jest.mock('../LegendDetailsBox', () => ({
  __esModule: true,
  default: (props: any) => (
    <div data-testid="legend-details-box" data-title={props.title}>
      {props.title} Details Box
    </div>
  ),
}));

describe('LegendDetailsSection Component', () => {
  const onCloseMock = jest.fn();
  
  const siteStatusIcons = {
    categoryIcon: 'site-icon.svg',
    status: [
      { statusColor: '#FF0000', title: 'Critical' },
      { statusColor: '#00FF00', title: 'Normal' },
    ],
  };

  const assetStatusIcons = [
    {
      categoryIcon: 'asset-icon.svg',
      status: [
        { statusColor: '#FF0000', title: 'Critical' },
        { statusColor: '#00FF00', title: 'Normal' },
      ],
    },
  ];

  const deviceStatus = [
    { color: '#AABBCC', title: 'Online' },
  ];

  const leftIconsSite = [
    { icon: 'left-icon1.svg', title: 'Left Icon 1' },
  ];

  const rightIconsSite = [
    { icon: 'right-icon1.svg', title: 'Right Icon 1' },
  ];

  const leftIconsAsset = [
    { icon: 'left-asset-icon1.svg', title: 'Left Asset Icon 1' },
  ];

  const rightIconsAsset = [
    { icon: 'right-asset-icon1.svg', title: 'Right Asset Icon 1' },
  ];

  beforeEach(() => {
    onCloseMock.mockClear();
  });

  it('should render with site status icons', () => {
    const props: LegendDetailsSectionProps = {
      onClose: onCloseMock,
      siteStatusIcons,
      deviceStatus,
      leftIconsSite,
      rightIconsSite,
    };

    render(<LegendDetailsSection {...props} />);
    
    expect(screen.getByText('Legends')).toBeInTheDocument();
    expect(screen.getByTestId('legend-details-box')).toBeInTheDocument();
    expect(screen.getByTestId('legend-details-box')).toHaveAttribute('data-title', 'Site');
    expect(screen.queryByText('Asset Details Box')).not.toBeInTheDocument();
  });

  it('should render with asset status icons', () => {
    const props: LegendDetailsSectionProps = {
      onClose: onCloseMock,
      assetStatusIcons,
      deviceStatus,
      leftIconsAsset,
      rightIconsAsset,
    };

    render(<LegendDetailsSection {...props} />);
    
    expect(screen.getByText('Legends')).toBeInTheDocument();
    expect(screen.getByTestId('legend-details-box')).toBeInTheDocument();
    expect(screen.getByTestId('legend-details-box')).toHaveAttribute('data-title', 'Asset');
    expect(screen.queryByText('Site Details Box')).not.toBeInTheDocument();
  });

  it('should render with both site and asset status icons', () => {
    const props: LegendDetailsSectionProps = {
      onClose: onCloseMock,
      siteStatusIcons,
      assetStatusIcons,
      deviceStatus,
      leftIconsSite,
      rightIconsSite,
      leftIconsAsset,
      rightIconsAsset,
    };

    render(<LegendDetailsSection {...props} />);
    
    const detailsBoxes = screen.getAllByTestId('legend-details-box');
    expect(detailsBoxes).toHaveLength(2);
    expect(detailsBoxes[0]).toHaveAttribute('data-title', 'Site');
    expect(detailsBoxes[1]).toHaveAttribute('data-title', 'Asset');
  });

  it('should not render site section when no site status icons', () => {
    const props: LegendDetailsSectionProps = {
      onClose: onCloseMock,
      siteStatusIcons: { categoryIcon: 'site-icon.svg', status: [] },
      assetStatusIcons,
    };

    render(<LegendDetailsSection {...props} />);
    
    const detailsBoxes = screen.getAllByTestId('legend-details-box');
    expect(detailsBoxes).toHaveLength(1);
    expect(detailsBoxes[0]).toHaveAttribute('data-title', 'Asset');
  });

  it('should not render asset section when no asset status icons', () => {
    const props: LegendDetailsSectionProps = {
      onClose: onCloseMock,
      siteStatusIcons,
      assetStatusIcons: [{ categoryIcon: 'asset-icon.svg', status: [] }],
    };

    render(<LegendDetailsSection {...props} />);
    
    const detailsBoxes = screen.getAllByTestId('legend-details-box');
    expect(detailsBoxes).toHaveLength(1);
    expect(detailsBoxes[0]).toHaveAttribute('data-title', 'Site');
  });

  it('should call onClose when back button is clicked', () => {
    const props: LegendDetailsSectionProps = {
      onClose: onCloseMock,
      siteStatusIcons,
    };

    render(<LegendDetailsSection {...props} />);
    
    fireEvent.click(screen.getByRole('button', { name: /go back/i }));
    expect(onCloseMock).toHaveBeenCalledTimes(1);
  });
});
