import React from 'react';
import { render, screen } from '@testing-library/react';
import IconWithExplanation from './IconWithExplanation';
import { IconWithExplanationProps } from '../../types';

// Mock the StatusIcon component
jest.mock('../../../../../../../components/configurable/StatusIcon', () => ({
  __esModule: true,
  default: (props: any) => (
    <div 
      data-testid="status-icon" 
      data-config={JSON.stringify(props.config)}
    >
      Status Icon
    </div>
  ),
}));

// Mock the IconExplainingText component
jest.mock('../IconExplainingText', () => ({
  __esModule: true,
  default: (props: any) => (
    <div 
      data-testid="icon-explaining-text" 
      data-line-type={props.lineType}
      data-line-width={props.lineWidth}
      data-class-name={props.className}
    >
      {props.text}
    </div>
  ),
}));

describe('IconWithExplanation Component', () => {
  const baseProps: IconWithExplanationProps = {
    entityType: 'site',
    statusColor: '#00FF00',
    deviceStatusColor: '#AABBCC',
    categoryIcon: 'category-icon.svg',
    sectionTitle: 'Site',
  };

  it('should render with full structure for site', () => {
    const props: IconWithExplanationProps = {
      ...baseProps,
      structure: 'full',
    };

    render(<IconWithExplanation {...props} />);
    
    expect(screen.getByTestId('status-icon')).toBeInTheDocument();
    
    const explanationTexts = screen.getAllByTestId('icon-explaining-text');
    expect(explanationTexts).toHaveLength(3); // upper, straight, lower
    
    // Check that the correct line types are used
    expect(explanationTexts[0]).toHaveAttribute('data-line-type', 'upper');
    expect(explanationTexts[1]).toHaveAttribute('data-line-type', 'straight');
    expect(explanationTexts[2]).toHaveAttribute('data-line-type', 'lower');
    
    // Check that the line widths are set correctly for site
    expect(explanationTexts[1]).toHaveAttribute('data-line-width', '50');
    expect(explanationTexts[2]).toHaveAttribute('data-line-width', '45');
    
    // Check that the class names are set correctly
    expect(explanationTexts[0]).toHaveAttribute('data-class-name', 'structure-upper entity-site');
    expect(explanationTexts[1]).toHaveAttribute('data-class-name', 'structure-straight entity-site');
    expect(explanationTexts[2]).toHaveAttribute('data-class-name', 'structure-lower entity-site');
  });

  it('should render with full structure for asset', () => {
    const props: IconWithExplanationProps = {
      ...baseProps,
      entityType: 'asset',
      sectionTitle: 'Asset',
      structure: 'full',
    };

    render(<IconWithExplanation {...props} />);
    
    const explanationTexts = screen.getAllByTestId('icon-explaining-text');
    
    // Check that the line widths are set correctly for asset
    expect(explanationTexts[1]).toHaveAttribute('data-line-width', '53');
    expect(explanationTexts[2]).toHaveAttribute('data-line-width', '47');
    
    // Check that the class names are set correctly
    expect(explanationTexts[0]).toHaveAttribute('data-class-name', 'structure-upper entity-asset');
    expect(explanationTexts[1]).toHaveAttribute('data-class-name', 'structure-straight entity-asset');
    expect(explanationTexts[2]).toHaveAttribute('data-class-name', 'structure-lower entity-asset');
  });

  it('should render with primary structure', () => {
    const props: IconWithExplanationProps = {
      ...baseProps,
      structure: 'primary',
      text: 'Primary Status',
    };

    render(<IconWithExplanation {...props} />);
    
    expect(screen.getByTestId('status-icon')).toBeInTheDocument();
    
    const explanationTexts = screen.getAllByTestId('icon-explaining-text');
    expect(explanationTexts).toHaveLength(1); // Only one line for primary
    
    expect(explanationTexts[0]).toHaveAttribute('data-line-type', 'upper');
    expect(screen.getByText('Primary Status')).toBeInTheDocument();
  });

  it('should render with device structure', () => {
    const props: IconWithExplanationProps = {
      ...baseProps,
      structure: 'device',
      text: 'Device Status',
    };

    render(<IconWithExplanation {...props} />);
    
    expect(screen.getByTestId('status-icon')).toBeInTheDocument();
    
    const explanationTexts = screen.getAllByTestId('icon-explaining-text');
    expect(explanationTexts).toHaveLength(1); // Only one line for device
    
    expect(explanationTexts[0]).toHaveAttribute('data-line-type', 'straight');
    expect(explanationTexts[0]).toHaveAttribute('data-class-name', 'entity-site');
    expect(screen.getByText('Device Status')).toBeInTheDocument();
  });

  it('should render custom icon when provided', () => {
    const props: IconWithExplanationProps = {
      ...baseProps,
      statusColor: undefined,
      customIcon: 'custom-icon.svg',
      structure: 'primary',
      text: 'Custom Icon',
    };

    render(<IconWithExplanation {...props} />);
    
    // StatusIcon should not be rendered
    expect(screen.queryByTestId('status-icon')).not.toBeInTheDocument();
    
    // Custom icon should be rendered
    const customIcon = screen.getByAltText('Custom Icon');
    expect(customIcon).toBeInTheDocument();
    expect(customIcon).toHaveAttribute('src', 'custom-icon.svg');
  });

  it('should handle missing deviceStatusColor', () => {
    const props: IconWithExplanationProps = {
      ...baseProps,
      deviceStatusColor: undefined,
      structure: 'full',
    };

    render(<IconWithExplanation {...props} />);
    
    const explanationTexts = screen.getAllByTestId('icon-explaining-text');
    expect(explanationTexts).toHaveLength(2); // Only straight and lower, no upper
    
    // Check that the correct line types are used
    expect(explanationTexts[0]).toHaveAttribute('data-line-type', 'straight');
    expect(explanationTexts[1]).toHaveAttribute('data-line-type', 'lower');
  });
});
