import React from "react";
import StatusIcon from "../../../../../../../components/configurable/StatusIcon";  
import "./style.less";
import IconExplainingText from "../IconExplainingText";
import { IconWithExplanationProps, LineType, StructureMap } from "../../types";

/**
 * Displays a StatusIcon along with explanatory text and lines pointing to different parts of the icon.
 * Used within the LegendDetailsBox to illustrate icon meanings.
 *
 * @param props - Component properties.
 * @param props.entityType - The type of entity ('site' or 'asset').
 * @param props.statusColor - The primary status color for the icon.
 * @param props.deviceStatusColor - The color for the device status part of the icon.
 * @param props.categoryIcon - The icon representing the category (e.g., dg-set, solar-pump).
 * @param props.sectionTitle - The title of the section ('Site' or 'Asset') used in explanations.
 * @param props.structure - Defines which explanations to show ('full', 'primary', 'device').
 * @param props.text - Specific text to show for 'primary' or 'device' structures.
 * @param props.customIcon - Optional path to a custom icon instead of using StatusIcon.
 * @returns The rendered icon with explanations.
 */
const IconWithExplanation: React.FC<IconWithExplanationProps> = ({
  entityType,
  statusColor,
  deviceStatusColor,
  categoryIcon,
  sectionTitle,
  structure,
  text,
  customIcon,
}) => {
  let structureMap: StructureMap = {};

  if(deviceStatusColor) {
    structureMap = {
      upper: {
        text: (
          <>
            Color represents <strong>Device Status</strong>
          </>
        ),
      },
    };
  } 
  structureMap = {
    ...structureMap,
    
    straight: {
      text: (
        <>
          Icon represents <strong>{sectionTitle} Type</strong>
        </>
      ),
      lineWidth: entityType === "site" ? 50 : 53,
    },
    lower: {
      text: (
        <>
          Color represents <strong>{sectionTitle} Status</strong>
        </>
      ),
      lineWidth: entityType === "site" ? 45 : 47,
    },
  }

  return (
    <div className="icon-explanation-wrapper">
      <div className="icon-container">
        {statusColor ? (
          <StatusIcon
            config={{
              entityType,
              statusColor,
              deviceStatusColor,
              categoryIcon,
            }}
          />
        ) : (
          customIcon && <img src={customIcon} alt="Custom Icon" />
        )}
      </div>

      <div className={`line-text ${structure}`}>
        {structure === "full" &&
          Object.keys(structureMap).map((key, index) => (
            <IconExplainingText
              key={index}
              text={structureMap[key as LineType].text}
              lineType={key as LineType}
              lineWidth={structureMap[key as LineType].lineWidth}
              className={`structure-${key} entity-${entityType}`}
            />
          ))
        }

        {structure === "primary" && (
          <IconExplainingText text={text} lineType={"upper"} />
        )}

        {structure === "device" && (
          <IconExplainingText
            text={text}
            lineType={"straight"}
            className={`entity-${entityType}`}
          />
        )}
      </div>
    </div>
  );
};

export default IconWithExplanation;
