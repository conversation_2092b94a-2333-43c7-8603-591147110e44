
.icon-explanation-wrapper {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding-left: 8px;
  width: 100%;

  &:has(.full) {
    margin-top: -8px;
  }
}

.icon-container {
  position: relative;
  transform: scale(1.35);
}

.line-text {
    display: flex;
    flex-direction: column;
    margin-left: -17px;
    z-index: 5;

    &.full {
        height: 65px;
        justify-content: flex-end;
    }

    .structure-upper {
        margin-left: 18px;

        &.entity-site {
            margin-left: 13px;
        }
    }

    .structure-lower {
        margin-left: 6px;
        &.entity-site {
            margin-left: 5px;
        }
    }

    &.device {
      margin-left: 1px;
      margin-top: -19px;

        .entity-site {
          margin-left: -2px;
        }
    }

    &.primary {
        margin-top: -28px;
        margin-left: -7px;
    }
}

