import React from 'react';
import { render, screen } from '@testing-library/react';
import IconExplainingText from './IconExplainingText';
import { IconExplainingTextProps } from '../../types';

describe('IconExplainingText Component', () => {
  it('should render upper line with default width', () => {
    const props: IconExplainingTextProps = {
      text: 'Upper Line Text',
      lineType: 'upper',
    };

    const { container } = render(<IconExplainingText {...props} />);
    
    expect(screen.getByText('Upper Line Text')).toBeInTheDocument();
    
    const svgElement = container.querySelector('svg');
    expect(svgElement).toBeInTheDocument();
    expect(svgElement).toHaveAttribute('width', '35');
    expect(svgElement).toHaveAttribute('height', '12');
    
    // Check that the container has the correct class
    const lineContainer = container.querySelector('.line-text-container');
    expect(lineContainer).toHaveClass('upper-line');
  });

  it('should render lower line with default width', () => {
    const props: IconExplainingTextProps = {
      text: 'Lower Line Text',
      lineType: 'lower',
    };

    const { container } = render(<IconExplainingText {...props} />);
    
    expect(screen.getByText('Lower Line Text')).toBeInTheDocument();
    
    const svgElement = container.querySelector('svg');
    expect(svgElement).toBeInTheDocument();
    expect(svgElement).toHaveAttribute('width', '42');
    expect(svgElement).toHaveAttribute('height', '13');
    
    // Check that the container has the correct class
    const lineContainer = container.querySelector('.line-text-container');
    expect(lineContainer).toHaveClass('lower-line');
  });

  it('should render straight line with default width', () => {
    const props: IconExplainingTextProps = {
      text: 'Straight Line Text',
      lineType: 'straight',
    };

    const { container } = render(<IconExplainingText {...props} />);
    
    expect(screen.getByText('Straight Line Text')).toBeInTheDocument();
    
    const svgElement = container.querySelector('svg');
    expect(svgElement).toBeInTheDocument();
    expect(svgElement).toHaveAttribute('width', '24');
    expect(svgElement).toHaveAttribute('height', '2');
    
    // Check that the container has the correct class
    const lineContainer = container.querySelector('.line-text-container');
    expect(lineContainer).toHaveClass('straight-line');
  });

  it('should render upper line with custom width', () => {
    const props: IconExplainingTextProps = {
      text: 'Custom Width Upper Line',
      lineType: 'upper',
      lineWidth: 50,
    };

    const { container } = render(<IconExplainingText {...props} />);
    
    const svgElement = container.querySelector('svg');
    expect(svgElement).toHaveAttribute('width', '50');
  });

  it('should render lower line with custom width', () => {
    const props: IconExplainingTextProps = {
      text: 'Custom Width Lower Line',
      lineType: 'lower',
      lineWidth: 60,
    };

    const { container } = render(<IconExplainingText {...props} />);
    
    const svgElement = container.querySelector('svg');
    expect(svgElement).toHaveAttribute('width', '60');
  });

  it('should render straight line with custom width', () => {
    const props: IconExplainingTextProps = {
      text: 'Custom Width Straight Line',
      lineType: 'straight',
      lineWidth: 40,
    };

    const { container } = render(<IconExplainingText {...props} />);
    
    const svgElement = container.querySelector('svg');
    expect(svgElement).toHaveAttribute('width', '40');
  });

  it('should render with custom className', () => {
    const props: IconExplainingTextProps = {
      text: 'Custom Class Text',
      lineType: 'upper',
      className: 'custom-class',
    };

    const { container } = render(<IconExplainingText {...props} />);
    
    const outerDiv = container.firstChild;
    expect(outerDiv).toHaveClass('custom-class');
  });

  it('should render with React node as text', () => {
    const props: IconExplainingTextProps = {
      text: <span data-testid="react-node">React Node Text</span>,
      lineType: 'upper',
    };

    render(<IconExplainingText {...props} />);
    
    expect(screen.getByTestId('react-node')).toBeInTheDocument();
    expect(screen.getByText('React Node Text')).toBeInTheDocument();
  });

  describe('Snapshot tests', () => {
    it('should match snapshot with upper line type', () => {
      const props: IconExplainingTextProps = {
        text: 'Upper Line Text',
        lineType: 'upper',
      };

      const { container } = render(<IconExplainingText {...props} />);
      expect(container).toMatchSnapshot();
    });

    it('should match snapshot with lower line type', () => {
      const props: IconExplainingTextProps = {
        text: 'Lower Line Text',
        lineType: 'lower',
      };

      const { container } = render(<IconExplainingText {...props} />);
      expect(container).toMatchSnapshot();
    });

    it('should match snapshot with straight line type', () => {
      const props: IconExplainingTextProps = {
        text: 'Straight Line Text',
        lineType: 'straight',
      };

      const { container } = render(<IconExplainingText {...props} />);
      expect(container).toMatchSnapshot();
    });
  });
});
