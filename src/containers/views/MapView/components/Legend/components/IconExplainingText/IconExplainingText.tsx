import "./style.less";
import { IconExplainingTextProps, LineProps } from "../../types";

const DEFAULT_UPPER_WIDTH = 35;
const DEFAULT_LOWER_WIDTH = 42;
const DEFAULT_STRAIGHT_WIDTH = 24;

const UpperLine = ({ lineWidth = DEFAULT_UPPER_WIDTH }: LineProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={lineWidth}
      height="12"
      viewBox={`0 0 ${lineWidth} 12`}
      fill="none"
    >
      <path
        d={`M7.33316 0.836813L${lineWidth - (DEFAULT_UPPER_WIDTH - 34.5204)} 0.836813M0.970947 10.6436L7.76174 0.586914`}
        stroke="#4F4F4F"
      />
      <circle cx="1.1797" cy="10.4475" r="0.998791" fill="#4F4F4F" />
    </svg>
  );
};

const LowerLine = ({ lineWidth = DEFAULT_LOWER_WIDTH }: LineProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={lineWidth}
      height="13"
      viewBox={`0 0 ${lineWidth} 13`}
      fill="none"
    >
      <path
        d={`M8.01085 11.5401H${lineWidth - (DEFAULT_LOWER_WIDTH - 41.9449)}M1.66821 1.73071L8.4381 11.79`}
        stroke="#4F4F4F"
      />
      <circle
        cx="0.997295"
        cy="0.997295"
        r="0.997295"
        transform="matrix(1 0 0 -1 0.880859 2.92249)"
        fill="#4F4F4F"
      />
    </svg>
  );
};

const StraightLine = ({ lineWidth = DEFAULT_STRAIGHT_WIDTH }: LineProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={lineWidth}
      height="2"
      viewBox={`0 0 ${lineWidth} 2`}
      fill="none"
    >
      <path d={`M0.0668945 0.848755H${lineWidth - 0.0668945}`} stroke="#4F4F4F" />
    </svg>
  );
};

/**
 * Renders a line (upper, lower, or straight) with corresponding text for explaining parts of an icon.
 *
 * @param props - Component properties.
 * @param props.text - The text content to display next to the line.
 * @param props.lineType - The type of line to render ('upper', 'lower', or 'straight').
 * @param props.lineWidth - Optional width for the line SVG.
 * @param props.className - Optional additional CSS class names.
 * @returns The rendered explaining text component with a line.
 */
const IconExplainingText = ({ text, lineType, lineWidth, className }: IconExplainingTextProps) => {
  return (
    <div className={className}>
      <div className={`line-text-container ${lineType}-line`}>
        {lineType === "upper" && <UpperLine lineWidth={lineWidth} />}
        {lineType === "lower" && <LowerLine lineWidth={lineWidth} />}
        {lineType === "straight" && <StraightLine lineWidth={lineWidth} />}
        <span>{text}</span>
      </div>
    </div>
  );
};

export default IconExplainingText;
