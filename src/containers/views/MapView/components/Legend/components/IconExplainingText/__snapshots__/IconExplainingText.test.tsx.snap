// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`IconExplainingText Component Snapshot tests should match snapshot with lower line type 1`] = `
<div>
  <div>
    <div
      class="line-text-container lower-line"
    >
      <svg
        fill="none"
        height="13"
        viewBox="0 0 42 13"
        width="42"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M8.01085 11.5401H41.9449M1.66821 1.73071L8.4381 11.79"
          stroke="#4F4F4F"
        />
        <circle
          cx="0.997295"
          cy="0.997295"
          fill="#4F4F4F"
          r="0.997295"
          transform="matrix(1 0 0 -1 0.880859 2.92249)"
        />
      </svg>
      <span>
        Lower Line Text
      </span>
    </div>
  </div>
</div>
`;

exports[`IconExplainingText Component Snapshot tests should match snapshot with straight line type 1`] = `
<div>
  <div>
    <div
      class="line-text-container straight-line"
    >
      <svg
        fill="none"
        height="2"
        viewBox="0 0 24 2"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0.0668945 0.848755H23.9331055"
          stroke="#4F4F4F"
        />
      </svg>
      <span>
        Straight Line Text
      </span>
    </div>
  </div>
</div>
`;

exports[`IconExplainingText Component Snapshot tests should match snapshot with upper line type 1`] = `
<div>
  <div>
    <div
      class="line-text-container upper-line"
    >
      <svg
        fill="none"
        height="12"
        viewBox="0 0 35 12"
        width="35"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.33316 0.836813L34.5204 0.836813M0.970947 10.6436L7.76174 0.586914"
          stroke="#4F4F4F"
        />
        <circle
          cx="1.1797"
          cy="10.4475"
          fill="#4F4F4F"
          r="0.998791"
        />
      </svg>
      <span>
        Upper Line Text
      </span>
    </div>
  </div>
</div>
`;
