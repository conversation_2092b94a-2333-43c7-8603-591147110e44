import "./style.less";
import IconWithExplanation from "../IconWithExplanation/IconWithExplanation";
import StatusIcon from "../../../../../../../components/configurable/StatusIcon";
import { LegendDetailsBoxProps } from "../../types";

/**
 * Renders a detailed box within the legend, explaining icons for either a Site or an Asset.
 *
 * @param props - The properties passed to the component.
 * @param props.title - The title of the box ('Site' or 'Asset').
 * @param props.primaryStatusIcons - Configuration for the primary status icons (SiteStatusIcons or AssetStatusIcons).
 * @param props.deviceStatus - Configuration for device status icons.
 * @param props.leftIcons - Configuration for left indicator icons.
 * @param props.rightIcons - Configuration for right indicator icons.
 * @returns The rendered legend details box.
 */
const LegendDetailsBox = (props: LegendDetailsBoxProps) => {
  const entityType = props.title.toLowerCase() === "site" ? "site" : "asset";
  const deviceStatusArray = props.deviceStatus || [];

  const statusColor = "#008C3F";
  const deviceStatusColor = deviceStatusArray.length > 0 ? "#BAEBC7" : "";

  const finalIconsArray =
    entityType === "site"
      ? props.primaryStatusIcons?.status?.map((status: any) => ({
          color: status.statusColor,
          title: status.title,
          categoryIcon: props.primaryStatusIcons?.categoryIcon,
        }))
      : props.primaryStatusIcons?.flatMap((group: any) =>
          group?.status?.map((statusItem: any) => ({
            color: statusItem.statusColor,
            title: statusItem.title,
            categoryIcon: group.categoryIcon,
            customIcon: statusItem.customIcon,
          })),
        );

  const categoryIcon = finalIconsArray[0].categoryIcon;

  const indicatorIconsArray = [...props.leftIcons || [], ...props.rightIcons || []];
  const leftIconsArray = props.leftIcons?.map((item) => item.icon) || [];
  const rightIconsArray = props.rightIcons?.map((item) => item.icon) || [];

  return (
    <div className="legend-details-box">
      <span className="ldb-title">{props.title}</span>
      <div className="ldb-content">
        <div className="ldb-item">
          <span className="ldb-item-title">{props.title} Structure</span>
          <IconWithExplanation
            entityType={entityType}
            statusColor={statusColor}
            deviceStatusColor={deviceStatusColor}
            categoryIcon={categoryIcon}
            sectionTitle={props.title}
            structure="full"
          />
        </div>

        {/* Primary Status Icons */}
        {finalIconsArray.length > 0 && (
          <div className="ldb-item">
            <span className="ldb-item-title">{props.title} Status</span>
            <div className="status-examples">
              {finalIconsArray.map((icon: any, index: number) => (
                <div className="status-example" key={index}>
                  <IconWithExplanation
                    entityType={entityType}
                    statusColor={icon.color}
                    deviceStatusColor={deviceStatusColor}
                    categoryIcon={icon.categoryIcon}
                    sectionTitle={props.title}
                    structure="primary"
                    text={icon.title}
                    customIcon={icon.customIcon}
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Device Status Icons */}
        {deviceStatusArray.length > 0 && (
          <div className="ldb-item">
            <span className="ldb-item-title">Device Status</span>
            <div className="status-examples">
              {deviceStatusArray.map((status, index) => (
                <div key={index} className="status-example">
                  <IconWithExplanation
                    entityType={entityType}
                    statusColor={statusColor}
                    deviceStatusColor={status.color}
                    categoryIcon={categoryIcon}
                    structure="device"
                    text={status.title}
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Indicators */}
        {indicatorIconsArray.length > 0 && (
          <div className="ldb-item">
            <span className="ldb-item-title">{props.title} Indicators</span>
            <p className="ldb-item-description">
              {props.title} indicators are kept on the marker to signal various
              statuses such as maintenance due, violations, trip faults, system
              errors, or device errors.
            </p>
            <div className="ldb-indicators-icon">
              <StatusIcon
                config={{
                  entityType,
                  statusColor,
                  deviceStatusColor,
                  categoryIcon,
                  leftIcons: leftIconsArray,
                  rightIcons: rightIconsArray,
                }}
              />
            </div>
            <div className="ldb-indicators-container">
              {indicatorIconsArray.map((indicator, index) => (
                <div key={index} className="ldb-indicator-item">
                  <img src={indicator.icon} alt={indicator.title} />
                  <span>{indicator.title}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LegendDetailsBox;
