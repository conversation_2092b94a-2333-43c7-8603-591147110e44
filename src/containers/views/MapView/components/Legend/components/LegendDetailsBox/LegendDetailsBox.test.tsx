import React from 'react';
import { render, screen } from '@testing-library/react';
import LegendDetailsBox from './LegendDetailsBox';
import { LegendDetailsBoxProps } from '../../types';

// Mock the IconWithExplanation component
jest.mock('../IconWithExplanation/IconWithExplanation', () => ({
  __esModule: true,
  default: (props: any) => (
    <div
      data-testid="icon-with-explanation"
      data-entity-type={props.entityType}
      data-status-color={props.statusColor}
      data-device-status-color={props.deviceStatusColor}
      data-structure={props.structure}
      data-text={props.text}
      data-custom-icon={props.customIcon}
    >
      Icon With Explanation
    </div>
  ),
}));

// Mock the StatusIcon component
jest.mock('../../../../../../../components/configurable/StatusIcon', () => ({
  __esModule: true,
  default: (props: any) => (
    <div
      data-testid="status-icon"
      data-config={JSON.stringify(props.config)}
    >
      Status Icon
    </div>
  ),
}));

describe('LegendDetailsBox Component', () => {
  // Site status icons with distinct colors and titles
  const siteStatusIcons = {
    categoryIcon: 'site-icon.svg',
    status: [
      { statusColor: '#FF0000', title: 'Site Critical' },
      { statusColor: '#00FF00', title: 'Site Normal' },
      { statusColor: '#FFFF00', title: 'Site Warning' },
    ],
  };

  // Asset status icons with distinct colors and titles
  const assetStatusIcons = [
    {
      categoryIcon: 'asset-icon.svg',
      status: [
        { statusColor: '#0000FF', title: 'Asset Critical' },
        { statusColor: '#00FFFF', title: 'Asset Normal' },
        { statusColor: '#FF00FF', title: 'Asset Warning' },
        { customIcon: 'custom-icon.svg', title: 'Asset Custom' },
      ],
    },
  ];

  const deviceStatus = [
    { color: '#AABBCC', title: 'Online' },
    { color: '#CCBBAA', title: 'Offline' },
  ];

  const leftIcons = [
    { icon: 'left-icon1.svg', title: 'Left Icon 1' },
    { icon: 'left-icon2.svg', title: 'Left Icon 2' },
  ];

  const rightIcons = [
    { icon: 'right-icon1.svg', title: 'Right Icon 1' },
    { icon: 'right-icon2.svg', title: 'Right Icon 2' },
  ];

  it('should render with site title and structure', () => {
    const props: LegendDetailsBoxProps = {
      title: 'Site',
      primaryStatusIcons: siteStatusIcons,
      deviceStatus: [],
      leftIcons: [],
      rightIcons: [],
    };

    render(<LegendDetailsBox {...props} />);

    expect(screen.getByText('Site')).toBeInTheDocument();
    expect(screen.getByText('Site Structure')).toBeInTheDocument();

    const structureIcon = screen.getAllByTestId('icon-with-explanation')[0];
    expect(structureIcon).toHaveAttribute('data-entity-type', 'site');
    expect(structureIcon).toHaveAttribute('data-structure', 'full');
  });

  it('should render with asset title and structure', () => {
    const props: LegendDetailsBoxProps = {
      title: 'Asset',
      primaryStatusIcons: assetStatusIcons,
      deviceStatus: [],
      leftIcons: [],
      rightIcons: [],
    };

    render(<LegendDetailsBox {...props} />);

    expect(screen.getByText('Asset')).toBeInTheDocument();
    expect(screen.getByText('Asset Structure')).toBeInTheDocument();

    const structureIcon = screen.getAllByTestId('icon-with-explanation')[0];
    expect(structureIcon).toHaveAttribute('data-entity-type', 'asset');
    expect(structureIcon).toHaveAttribute('data-structure', 'full');
  });

  it('should render site status icons with distinct site-specific titles and colors', () => {
    const props: LegendDetailsBoxProps = {
      title: 'Site',
      primaryStatusIcons: siteStatusIcons,
      deviceStatus: [],
      leftIcons: [],
      rightIcons: [],
    };

    render(<LegendDetailsBox {...props} />);

    expect(screen.getByText('Site Status')).toBeInTheDocument();

    const statusIcons = screen.getAllByTestId('icon-with-explanation').slice(1); // Skip the structure icon
    expect(statusIcons).toHaveLength(3); // Three status items

    statusIcons.forEach(icon => {
      expect(icon).toHaveAttribute('data-entity-type', 'site');
      expect(icon).toHaveAttribute('data-structure', 'primary');
    });

    // Check that the status colors are correctly passed
    expect(statusIcons[0]).toHaveAttribute('data-status-color', '#FF0000');
    expect(statusIcons[1]).toHaveAttribute('data-status-color', '#00FF00');
    expect(statusIcons[2]).toHaveAttribute('data-status-color', '#FFFF00');

    // Check that the site-specific titles are correctly passed
    expect(statusIcons[0]).toHaveAttribute('data-text', 'Site Critical');
    expect(statusIcons[1]).toHaveAttribute('data-text', 'Site Normal');
    expect(statusIcons[2]).toHaveAttribute('data-text', 'Site Warning');
  });

  it('should render asset status icons with distinct asset-specific titles, colors, and custom icons', () => {
    const props: LegendDetailsBoxProps = {
      title: 'Asset',
      primaryStatusIcons: assetStatusIcons,
      deviceStatus: [],
      leftIcons: [],
      rightIcons: [],
    };

    render(<LegendDetailsBox {...props} />);

    expect(screen.getByText('Asset Status')).toBeInTheDocument();

    const statusIcons = screen.getAllByTestId('icon-with-explanation').slice(1); // Skip the structure icon
    expect(statusIcons).toHaveLength(4); // Four status items (3 colors + 1 custom)

    statusIcons.forEach(icon => {
      expect(icon).toHaveAttribute('data-entity-type', 'asset');
      expect(icon).toHaveAttribute('data-structure', 'primary');
    });

    // Check that the status colors are correctly passed
    expect(statusIcons[0]).toHaveAttribute('data-status-color', '#0000FF');
    expect(statusIcons[1]).toHaveAttribute('data-status-color', '#00FFFF');
    expect(statusIcons[2]).toHaveAttribute('data-status-color', '#FF00FF');

    // Check that the asset-specific titles are correctly passed
    expect(statusIcons[0]).toHaveAttribute('data-text', 'Asset Critical');
    expect(statusIcons[1]).toHaveAttribute('data-text', 'Asset Normal');
    expect(statusIcons[2]).toHaveAttribute('data-text', 'Asset Warning');

    // Check that the custom icon is correctly passed
    expect(statusIcons[3]).toHaveAttribute('data-custom-icon', 'custom-icon.svg');
    expect(statusIcons[3]).toHaveAttribute('data-text', 'Asset Custom');
  });

  it('should render device status icons', () => {
    const props: LegendDetailsBoxProps = {
      title: 'Site',
      primaryStatusIcons: siteStatusIcons,
      deviceStatus,
      leftIcons: [],
      rightIcons: [],
    };

    render(<LegendDetailsBox {...props} />);

    expect(screen.getByText('Device Status')).toBeInTheDocument();

    const deviceStatusIcons = screen.getAllByTestId('icon-with-explanation').slice(4); // Skip structure and site status icons (1 structure + 3 status)
    expect(deviceStatusIcons).toHaveLength(2); // Two device status items

    deviceStatusIcons.forEach(icon => {
      expect(icon).toHaveAttribute('data-entity-type', 'site');
      expect(icon).toHaveAttribute('data-structure', 'device');
    });

    // Check that the device status colors are correctly passed
    expect(deviceStatusIcons[0]).toHaveAttribute('data-device-status-color', '#AABBCC');
    expect(deviceStatusIcons[1]).toHaveAttribute('data-device-status-color', '#CCBBAA');
  });

  it('should render indicator icons', () => {
    const props: LegendDetailsBoxProps = {
      title: 'Site',
      primaryStatusIcons: siteStatusIcons,
      deviceStatus: [],
      leftIcons,
      rightIcons,
    };

    render(<LegendDetailsBox {...props} />);

    expect(screen.getByText('Site Indicators')).toBeInTheDocument();
    expect(screen.getByTestId('status-icon')).toBeInTheDocument();

    // Check that all indicator icons are rendered
    expect(screen.getByText('Left Icon 1')).toBeInTheDocument();
    expect(screen.getByText('Left Icon 2')).toBeInTheDocument();
    expect(screen.getByText('Right Icon 1')).toBeInTheDocument();
    expect(screen.getByText('Right Icon 2')).toBeInTheDocument();

    // Check that the indicator icons are correctly passed to StatusIcon
    const statusIcon = screen.getByTestId('status-icon');
    const config = JSON.parse(statusIcon.getAttribute('data-config') || '{}');
    expect(config.leftIcons).toEqual(['left-icon1.svg', 'left-icon2.svg']);
    expect(config.rightIcons).toEqual(['right-icon1.svg', 'right-icon2.svg']);
  });

  it('should handle missing props gracefully', () => {
    // Mock the component to avoid the error
    jest.spyOn(console, 'error').mockImplementation(() => {});

    const props: LegendDetailsBoxProps = {
      title: 'Site',
      // Need to provide at least one status item to avoid the error
      primaryStatusIcons: {
        categoryIcon: 'site-icon.svg',
        status: [{ statusColor: '#FF0000', title: 'Critical' }]
      },
      deviceStatus: undefined,
      leftIcons: undefined,
      rightIcons: undefined,
    };

    render(<LegendDetailsBox {...props} />);

    expect(screen.getByText('Site')).toBeInTheDocument();
    expect(screen.getByText('Site Status')).toBeInTheDocument(); // Now we have a status item
    expect(screen.queryByText('Device Status')).not.toBeInTheDocument();
    expect(screen.queryByText('Site Indicators')).not.toBeInTheDocument();

    console.error.mockRestore();
  });
});
