.legend-details-box {
    flex: 1;
    background-color: #f7f7f7;
    border: 1px solid #B7B7B7;
    border-radius: 8px;
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 26px;
    overflow: auto;

    .ldb-title {
        font-size: 16px;
        font-weight: 500;
        color: #000000;
    }

    .ldb-content {
        display: flex;
        flex-direction: column;
        gap: 20px;

        .ldb-item {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .ldb-item-title {
                font-size: 14px;
                font-weight: 500;
                margin-bottom: 10px;
                color: #232323;
            }

            .ldb-item-description {
                font-size: 12px;
                margin-top: -16px;
                color: #4f4f4f;
            }

            .status-examples {
                display: flex;
                flex-wrap: wrap;
                row-gap: 34px;
                column-gap: 48px;
            }

            .ldb-indicators-icon {
                width: fit-content;
                padding-left: 8px;
                padding-bottom: 16px;
                transform: scale(1.35);
            }

            .ldb-indicators-container {
                display: flex;
                column-gap: 24px;
                row-gap: 10px;
                flex-wrap: wrap;

                .ldb-indicator-item {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    
                }
            }
        }
    }
}