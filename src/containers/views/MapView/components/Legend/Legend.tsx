import React, { useState } from "react";
import "./style.less";
import { LegendProps, LegendDisplayItem } from "./types";
import IconFrame from "../../../../../components/configurable/StatusIcon/components/IconFrame";
import LegendDetailsSection from "./components/LegendDetailsSection";
import { LoadingOutlined } from "@ant-design/icons";

/**
 * Legend component for displaying map status icons.
 * Shows a condensed view initially and expands to a detailed view.
 *
 * @param props - The properties passed to the component.
 * @param props.siteStatusIcons - Configuration for site status icons.
 * @param props.assetStatusIcons - Configuration for asset status icons.
 * @param props.deviceStatus - Configuration for device status.
 * @param props.leftIconsSite - Left indicator icons for sites.
 * @param props.rightIconsSite - Right indicator icons for sites.
 * @param props.leftIconsAsset - Left indicator icons for assets.
 * @param props.rightIconsAsset - Right indicator icons for assets.
 * @param props.loading - Whether the legend is loading.
 * @returns The rendered Legend component or null if no icons are available.
 */
const Legend: React.FC<LegendProps> = (props) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const iconsToDisplay: LegendDisplayItem[] = React.useMemo(() => {
    const isSite = props.siteStatusIcons?.status;
    const sourceStatusItems = isSite
      ? props.siteStatusIcons?.status
      : props.assetStatusIcons?.[0]?.status || [];

    if(!Array.isArray(sourceStatusItems)) {
      return [];
    }

    return sourceStatusItems
      .filter((item) => item.statusColor)
      .map((item) => ({
        statusColor: item.statusColor ?? "",
        deviceStatusColor: props.deviceStatus?.[0]?.color,
        entityType: isSite ? "site" : "asset",
        title: item.title,
        customIcon: item.customIcon,
      }));
  }, [props.siteStatusIcons, props.assetStatusIcons, props.deviceStatus]);

  // Handle cases where there might be no icons to display
  if (iconsToDisplay.length === 0) {
    return null;
  }

  const handleClose = () => {
    setIsExpanded(false);
  };

  return !isExpanded ? (
    <div id="map-legend-container">
      {props.loading ? (
        <LoadingOutlined className="legend-loading-icon" />
      ) : (
        <>
          {iconsToDisplay.map((item, index) => (
            <div key={index} className="legend-item">
              <div className="status-indicator">
                <IconFrame
                  color={{ inner: item.statusColor, outer: item.deviceStatusColor || item.statusColor }}
                  entityType={item.entityType}
                  iconType="normal"
                  fullFrame={true}
                />
              </div>
              <span className="legend-label">{item.title}</span>
            </div>
          ))}
          <span className="view-more-link" onClick={() => setIsExpanded(true)}>
            View More
          </span>
        </>
      )}
    </div>
  ) : (
    <div id="map-legend-details">
      <LegendDetailsSection onClose={handleClose} {...props} />
    </div>
  );
};

export default Legend;
