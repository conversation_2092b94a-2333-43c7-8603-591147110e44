@blue-color: #374375;

#map-legend-container {
  display: flex;
  align-items: center;
  background-color: white;
  padding: 6px 24px;
  border-radius: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  gap: 16px;
  height: 36px;
  width: fit-content;
  min-width: 100px;
  font-size: 12px;
  margin: 24px;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
  justify-content: center;

  .status-indicator {
    transform: scale(0.375);
    margin-inline: -15px;
  }
  
  .legend-loading-icon {
    font-size: 16px;
    color: @blue-color;
    animation: spin 1s infinite linear;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-label {
  color: #333;
}

.view-more-link {
  color: @blue-color;
  text-decoration: none;
  font-style: italic;
  font-weight: 400;
  white-space: nowrap;

  &:hover {
    cursor: pointer;
    text-decoration: underline;
  }
}

.legend-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

#map-legend-details {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1500;
}
