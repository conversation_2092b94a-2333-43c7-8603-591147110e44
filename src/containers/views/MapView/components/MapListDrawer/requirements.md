# MapListDrawer Component Requirements

## Overview
The MapListDrawer component displays a searchable, scrollable list of entities (sites and assets) in a sidebar drawer for the map view. It uses virtualized rendering for performance optimization when displaying large lists.

## UI Components
- **Header**
  - Search bar for filtering entities
  - Close icon button to dismiss the drawer
- **Content Area**
  - Virtualized list of entities (using react-window)
  - Loading skeleton when data is loading
  - Empty state message when no results are found

## Data Structure
The component receives an array of entities with the following structure:

```typescript
interface ListDrawerEntityProps {
  id: number;                         // Id of the entity ('asset' or 'site')
  name: string;                       // Display name of the entity
  entityType: "asset" | "site";       // Type of entity - determines display format
  lastDataReceivedTime?: string | Date | null;        // Last data received time (for assets)
  customerName?: string;              // Name of the customer associated with this entity
  statusIconConfig: StatusIconConfig; // Configuration for the status icon
  showDeviceOfflineTag?: boolean;     // Flag to show "Offline" tag for devices
  siteInfo?: {                        // Information about the site an asset belongs to
    id: number;
    name: string;
  };
  assetCount?: number;                // Count of assets for site entities
  dynamicComponent?: {                // Optional dynamic component to render
    key: string;
    config: any
  };
  onClick?: (id: number) => void;     // Callback function when entity is clicked
}
```

## Props

```typescript
interface MapListDrawerProps {
  onListDrawerClose: () => void;              // Callback when 'Close Icon' is clicked
  onSearch: (searchText: any) => void;        // Callback when search is performed
  onEntityClick: (entityId: number) => void;  // Callback when entity is clicked
  items?: ListDrawerEntityProps[];            // Array of entity items to display
  loading?: boolean;                          // Flag indicating loading state
}
```

## Functionality / Logic
- The drawer shows a list of entities (sites and assets)
- Close icon will dismiss the drawer by triggering `onListDrawerClose` callback
- Search input triggers the `onSearch` callback, which should filter the list at the parent component level
- Entities are rendered with appropriate information based on their type:
  - **Sites**: Show name, asset count, customer (if available)
  - **Assets**: Show name, last data received time, site info (if connected), customer (if available)
- Each entity is clickable and triggers the `onEntityClick` callback
- The list supports virtualization for performance with variable height rows

## Behavior
- When loading, a skeleton UI is displayed
- When the list is empty, a "No Results Found!" message is shown
- Entity height is dynamically calculated based on content (using tags, additional lines)
- The list recalculates heights when items change

## Filter Logic (Handled by Parent Component)
- **Default**: List shows both sites and individual assets (not within a site)
- **Asset Type Filter**: When an asset type is selected, all assets of that type are shown (including those within sites)
- **Site Type + Asset Type Filter**: When both a site type and an asset type are selected, only assets of the specified type belonging to the selected site type are shown