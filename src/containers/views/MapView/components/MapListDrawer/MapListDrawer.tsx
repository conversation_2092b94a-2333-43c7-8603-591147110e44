/**
 * MapListDrawer component displays a searchable, scrollable list of entities in a sidebar drawer
 * for the map view, with virtualized rendering for performance.
 *
 * @module MapListDrawer
 */
import React, { useRef, useEffect } from "react";
import "./style.less";
import SearchBar from "../../../../../components/base/SearchBar";
import CloseIcon from "../../../../../components/base/CloseIcon";
import ListDrawerEntity from "./components/ListDrawerEntity";
import ListDrawerSkeleton from "./components/ListDrawerSkeleton";
import { VariableSizeList as List } from "react-window";
import AutoSizer from "react-virtualized/dist/commonjs/AutoSizer";
import { MapListDrawerProps } from "./types";
import ListNoDataIcon from "./assets/list-no-data.svg";

/** Height constants for virtualized list row calculations */
// Approximate heights - these should be tuned based on actual CSS and content
const BASE_ROW_HEIGHT = 39.5; // Height for name, lastDataReceivedTime/assetCount line
const TAG_LINE_HEIGHT = 26.5; // Approx height for one line of tags (including some margin)
const DIVIDER_HEIGHT = 1; // Height of the <hr />
const ROW_PADDING = 32;
/** Default minimum heights by entity type */
const DEFAULT_HEIGHT = {
  asset: 48,
  site: 42,
};

/**
 * MapListDrawer component displays entities in a searchable, scrollable list
 * with virtualized rendering for performance optimization.
 *
 * @param props - Component props
 * @returns React component
 */
const MapListDrawer: React.FC<MapListDrawerProps> = ({
  onListDrawerClose,
  onSearch,
  onEntityClick,
  items = [],
  loading = false,
}) => {
  const listRef = useRef<List>(null);

  // Reset list measurements when items change
  useEffect(() => {
    if (listRef.current) {
      listRef.current.resetAfterIndex(0);
    }
  }, [items]);

  /**
   * Calculates the height for each row in the virtualized list based on content
   *
   * @param index - Index of the item in the items array
   * @returns Calculated height in pixels
   */
  const calculateItemHeight = (index: number): number => {
    const item = items[index];
    if (!item) return BASE_ROW_HEIGHT + ROW_PADDING;

    let height = BASE_ROW_HEIGHT;

    if (item.entityType === "asset" && item.siteInfo) {
      height += TAG_LINE_HEIGHT;
    }

    if (item.customerName) {
      height += TAG_LINE_HEIGHT;
    }

    if (index < items.length - 1) {
      height += DIVIDER_HEIGHT;
    }

    height = Math.max(DEFAULT_HEIGHT[item.entityType], height) + ROW_PADDING;
    return height;
  };

  /**
   * Row component for the virtualized list
   *
   * @param props - Row component props
   * @returns React component
   */
  const Row = ({
    index,
    style,
  }: {
    index: number;
    style: React.CSSProperties;
  }) => {
    const entityProps = items[index];
    // const isLastItem = index === items.length - 1;

    return (
      <div key={index} style={style}>
        <ListDrawerEntity {...entityProps} onClick={onEntityClick} />
        <hr className="list-drawer-divider" />
        {/* {!isLastItem && <hr className="list-drawer-divider" />} */}
      </div>
    );
  };

  return (
    <div id="map-list-drawer">
      <div className="list-header">
        <SearchBar placeholder="Search" onSearch={onSearch} />
        <CloseIcon onClose={onListDrawerClose} />
      </div>
      <div className="list-container">
        {loading ? (
          <ListDrawerSkeleton />
        ) : items.length > 0 ? (
          <AutoSizer>
            {({ height, width }) => (
              <List
                ref={listRef}
                height={height}
                width={width}
                itemCount={items.length}
                itemSize={calculateItemHeight} // Use the calculation function
              >
                {Row}
              </List>
            )}
          </AutoSizer>
        ) : (
          <div className="list-empty">
            <img src={ListNoDataIcon} alt="No Data" />
            <span>No Results Found!</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default MapListDrawer;
