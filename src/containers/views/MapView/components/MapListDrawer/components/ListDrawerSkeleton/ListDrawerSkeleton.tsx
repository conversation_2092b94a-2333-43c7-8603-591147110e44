/**
 * ListDrawerSkeleton component provides loading placeholder UI for the MapListDrawer
 * with responsive layout that adjusts to the viewport size.
 * 
 * @module ListDrawerSkeleton
 */
import React, { useEffect, useState } from "react";
import { Skeleton } from "antd";
import "./style.less";
import { ListDrawerSkeletonProps } from "../../types";

/**
 * ListDrawerSkeleton displays loading placeholder UI while list data is being fetched
 * with dynamically calculated number of items based on viewport height.
 * 
 * @param props - Component props
 * @returns React component
 */
const ListDrawerSkeleton: React.FC<ListDrawerSkeletonProps> = ({ count }) => {
  /** State to track number of skeleton items to display */
  const [itemCount, setItemCount] = useState(count || 8);
  
  useEffect(() => {
    if (!count) {
      /**
       * Calculates the number of skeleton items to display based on viewport height
       * for a responsive loading experience
       */
      const calculateItemsForScreen = () => {
        const itemHeight = 99; // Approximate height of each skeleton item in pixels
        const availableHeight = window.innerHeight - 120; // Subtract header and some padding
        const calculatedCount = Math.floor(availableHeight / itemHeight);
        setItemCount(Math.max(3, calculatedCount)); // Ensure at least 3 items
      };
      
      calculateItemsForScreen();
      window.addEventListener('resize', calculateItemsForScreen);
      
      return () => {
        window.removeEventListener('resize', calculateItemsForScreen);
      };
    }
  }, [count]);

  return (
    <div className="map-list-drawer-skeleton">
      {Array.from({ length: itemCount }).map((_, index) => (
        <div key={index} className="skeleton-item">
          <Skeleton 
            active 
            avatar={{ 
              shape: "circle", 
              size: 44,
              className: index % 2 === 0 ? 'active-avatar' : 'inactive-avatar'
            }} 
            paragraph={{ 
              rows: 2,
              width: ['80%', '50%']
            }} 
            title={false}
          />
          {index < itemCount - 1 && <div className="skeleton-divider" />}
        </div>
      ))}
    </div>
  );
};

export default ListDrawerSkeleton;
