import React from 'react';
import { render, screen, act } from '@testing-library/react';
import ListDrawerSkeleton from './ListDrawerSkeleton';
import { ListDrawerSkeletonProps } from '../../types';

// Mock the Skeleton component from antd
jest.mock('antd', () => ({
  Skeleton: ({ active, avatar, paragraph }: any) => (
    <div 
      data-testid="skeleton" 
      data-active={active ? 'true' : 'false'}
      data-avatar-shape={avatar?.shape}
      data-avatar-size={avatar?.size}
      data-avatar-class={avatar?.className}
      data-paragraph-rows={paragraph?.rows}
    >
      Skeleton
    </div>
  )
}));

describe('ListDrawerSkeleton Component', () => {
  // Save original window.innerHeight
  const originalInnerHeight = window.innerHeight;
  
  beforeEach(() => {
    // Reset window.innerHeight before each test
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: 800
    });
    
    // Mock addEventListener and removeEventListener
    window.addEventListener = jest.fn();
    window.removeEventListener = jest.fn();
  });
  
  afterEach(() => {
    // Restore original window.innerHeight
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: originalInnerHeight
    });
    
    jest.clearAllMocks();
  });

  it('should render with default count of skeleton items', () => {
    render(<ListDrawerSkeleton />);
    
    // With window.innerHeight = 800, and the calculation in the component:
    // Math.floor((800 - 120) / 99) = Math.floor(680 / 99) = 6 items
    const skeletonItems = screen.getAllByTestId('skeleton');
    expect(skeletonItems).toHaveLength(6);
    
    // Check that addEventListener was called for resize
    expect(window.addEventListener).toHaveBeenCalledWith('resize', expect.any(Function));
  });

  it('should render with specified count of skeleton items', () => {
    const props: ListDrawerSkeletonProps = {
      count: 4
    };
    
    render(<ListDrawerSkeleton {...props} />);
    
    // Should render exactly 4 items as specified
    const skeletonItems = screen.getAllByTestId('skeleton');
    expect(skeletonItems).toHaveLength(4);
    
    // Should not add event listener when count is provided
    expect(window.addEventListener).not.toHaveBeenCalled();
  });

  it('should recalculate item count on window resize', () => {
    render(<ListDrawerSkeleton />);
    
    // Get the resize handler
    const resizeHandler = (window.addEventListener as jest.Mock).mock.calls[0][1];
    
    // Change window height and trigger resize
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: 500
    });
    
    act(() => {
      resizeHandler();
    });
    
    // With window.innerHeight = 500, and the calculation in the component:
    // Math.floor((500 - 120) / 99) = Math.floor(380 / 99) = 3 items
    const skeletonItems = screen.getAllByTestId('skeleton');
    expect(skeletonItems).toHaveLength(3);
  });

  it('should ensure minimum of 3 items even with small window', () => {
    render(<ListDrawerSkeleton />);
    
    // Get the resize handler
    const resizeHandler = (window.addEventListener as jest.Mock).mock.calls[0][1];
    
    // Change window height to a very small value
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: 200
    });
    
    act(() => {
      resizeHandler();
    });
    
    // With window.innerHeight = 200, the calculation would give:
    // Math.floor((200 - 120) / 99) = Math.floor(80 / 99) = 0
    // But the component ensures a minimum of 3 items
    const skeletonItems = screen.getAllByTestId('skeleton');
    expect(skeletonItems).toHaveLength(3);
  });

  it('should remove event listener on unmount', () => {
    const { unmount } = render(<ListDrawerSkeleton />);
    
    unmount();
    
    // Check that removeEventListener was called
    expect(window.removeEventListener).toHaveBeenCalledWith('resize', expect.any(Function));
  });

  it('should render skeleton items with correct properties', () => {
    render(<ListDrawerSkeleton />);
    
    const skeletonItems = screen.getAllByTestId('skeleton');
    
    // Check the first skeleton item
    expect(skeletonItems[0]).toHaveAttribute('data-active', 'true');
    expect(skeletonItems[0]).toHaveAttribute('data-avatar-shape', 'circle');
    expect(skeletonItems[0]).toHaveAttribute('data-avatar-size', '44');
    expect(skeletonItems[0]).toHaveAttribute('data-paragraph-rows', '2');
    
    // Check that alternating items have different avatar classes
    expect(skeletonItems[0]).toHaveAttribute('data-avatar-class', 'active-avatar');
    expect(skeletonItems[1]).toHaveAttribute('data-avatar-class', 'inactive-avatar');
    expect(skeletonItems[2]).toHaveAttribute('data-avatar-class', 'active-avatar');
  });

  it('should render dividers between skeleton items except for the last one', () => {
    render(<ListDrawerSkeleton />);
    
    const skeletonItems = screen.getAllByTestId('skeleton');
    const dividers = document.querySelectorAll('.skeleton-divider');
    
    // There should be one less divider than the number of skeleton items
    expect(dividers.length).toBe(skeletonItems.length - 1);
  });
});
