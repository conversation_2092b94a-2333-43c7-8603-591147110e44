@tag-font-color: #4f4f4f;

.map-list-drawer-entity {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  cursor: pointer;
  position: relative;
  gap: 24px;

  .lde-left-section {
    display: flex;
    overflow: hidden;
    gap: 16px;
    align-items: flex-start;
    flex-grow: 1;
    min-width: 0;
  }

  .lde-details {
    flex-grow: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-width: 0; // Prevents text overflow issues with flex

    .lde-title {
      font-size: 14px;
      line-height: 1.3;
      margin-bottom: 4px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
      .text-ellipsis();
    }

    .text-ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .lde-secondary-row {
      display: flex;
      align-items: center;
      height: 17px;
      
      .lde-secondary-text {
        font-size: 12px;
        margin-right: 8px;
        color: #808080;
        line-height: normal;
      }
      
      .offline-tag {
        line-height: 1;
        padding: 2px 6px;
        font-size: 11px;
        margin: 0;
      }
    }

    .ant-tag {
        margin-top: 5px;
        width: fit-content;
        color: @tag-font-color;
        .text-ellipsis();
    }

    .lde-customer {
      margin-bottom: 4px;
      font-size: 14px;
      
      .ant-typography {
        color: rgba(0, 0, 0, 0.65);
      }
    }

    .lde-asset-count {
      font-size: 12px;
      margin-top: 2px;
    }
  }

  .lde-dynamic-component {
    margin-left: 12px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    height: 100%;
  }
}
