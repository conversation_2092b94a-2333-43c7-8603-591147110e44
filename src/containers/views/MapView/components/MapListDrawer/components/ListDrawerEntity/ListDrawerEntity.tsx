/**
 * ListDrawerEntity component renders a single entity (asset or site) in the MapListDrawer
 * with appropriate styling, status icons, and metadata displayed in a consistent format.
 *
 * @module ListDrawerEntity
 */
import React from "react";
import { Tag, Tooltip } from "antd";
import StatusIcon from "../../../../../../../components/configurable/StatusIcon/StatusIcon";
import "./style.less";
import { ListDrawerEntityProps } from "../../types";
import { renderDynamicComponent } from "../../../../../utils/dynamic-component-config";
import EntityBasicInfo from "../../../../../components/EntityBasicInfo";

/** Background color for tags */
const tagBgColor = "#F2F2F2";

/**
 * ListDrawerEntity displays a single entity in the map list drawer
 * with appropriate status indicators, name, and relevant metadata.
 *
 * @param props - Component props
 * @returns React component
 */
const ListDrawerEntity: React.FC<ListDrawerEntityProps> = ({
  entityId,
  name,
  lat,
  long,
  entityType,
  lastDataReceivedTime,
  customerName,
  statusIconConfig,
  onClick,
  siteInfo,
  assetCount,
  showDeviceOfflineTag = false,
  dynamicComponent,
}) => {
  /**
   * Formats lastDataReceivedTime values to a consistent string format
   *
   * @param d - Date value to format (string, Date object, or null)
   * @returns Formatted lastDataReceivedTime string or undefined if no lastDataReceivedTime provided
   */

  const handleClick = () => {
    onClick?.(entityId, entityType, lat, long);
  };

  return (
    <div className="map-list-drawer-entity" onClick={handleClick}>
      <div className="lde-left-section">
        <StatusIcon config={statusIconConfig} />
        <div className="lde-details">
          <EntityBasicInfo
            name={name}
            entityType={entityType}
            lastDataReceivedTime={lastDataReceivedTime}
            assetCount={assetCount}
            showDeviceOfflineTag={showDeviceOfflineTag}
          />

          {entityType === "asset" && siteInfo && (
            <Tooltip title={siteInfo.name}>
              <Tag>{siteInfo.name}</Tag>
            </Tooltip>
          )}

          {customerName && (
            <Tooltip title={customerName}>
              <Tag color={tagBgColor}>
                Customer: <b>{customerName}</b>
              </Tag>
            </Tooltip>
          )}
        </div>
      </div>

      {dynamicComponent && <div className="lde-dynamic-component">{renderDynamicComponent(dynamicComponent)}</div>}
    </div>
  );
};

export default ListDrawerEntity;
