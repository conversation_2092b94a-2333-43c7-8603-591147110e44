import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import ListDrawerEntity from './ListDrawerEntity';
import { ListDrawerEntityProps } from '../../types';

// Mock the StatusIcon component
jest.mock('../../../../../../../components/configurable/StatusIcon/StatusIcon', () => ({
  __esModule: true,
  default: ({ config }: any) => (
    <div data-testid="status-icon" data-status-color={config.statusColor}>
      Status Icon
    </div>
  )
}));

// Mock the renderDynamicComponent function
jest.mock('../../../../../utils/dynamic-component-config', () => ({
  __esModule: true,
  renderDynamicComponent: (component: any) => (
    <div data-testid="dynamic-component" data-key={component.key}>
      Dynamic Component
    </div>
  )
}));

// Mock moment for consistent lastDataReceivedTime formatting
jest.mock('moment', () => {
  const mockMoment = {
    unix: () => mockMoment,
    format: jest.fn().mockReturnValue('01 Jan 2023, 12:00')
  };
  return mockMoment;
});

describe('ListDrawerEntity Component', () => {
  // Sample props for testing
  const defaultProps: ListDrawerEntityProps = {
    id: 1,
    name: 'Test Entity',
    entityType: 'asset',
    statusIconConfig: { statusColor: '#00FF00' },
    onClick: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render an asset entity with basic information', () => {
    render(<ListDrawerEntity {...defaultProps} />);

    // Check that the main elements are rendered
    expect(screen.getByText('Test Entity')).toBeInTheDocument();
    expect(screen.getByTestId('status-icon')).toBeInTheDocument();

    // Check that the status icon has the correct color
    expect(screen.getByTestId('status-icon')).toHaveAttribute('data-status-color', '#00FF00');

    // For assets without a lastDataReceivedTime, it should show "No Data Received"
    expect(screen.getByText('No Data Received')).toBeInTheDocument();
  });

  it('should render an asset entity with lastDataReceivedTime information', () => {
    const props = {
      ...defaultProps,
      lastDataReceivedTime: new Date('2023-01-01T12:00:00')
    };

    render(<ListDrawerEntity {...props} />);

    // Check that the lastDataReceivedTime is formatted and displayed
    expect(screen.getByText('01 Jan 2023, 12:00')).toBeInTheDocument();
    expect(screen.queryByText('No Data Received')).not.toBeInTheDocument();
  });

  it('should render an asset entity with string lastDataReceivedTime', () => {
    const props = {
      ...defaultProps,
      lastDataReceivedTime: '01 Jan 2023, 12:00'
    };

    render(<ListDrawerEntity {...props} />);

    // Check that the lastDataReceivedTime string is displayed as-is
    expect(screen.getByText('01 Jan 2023, 12:00')).toBeInTheDocument();
  });

  it('should render a site entity with asset count', () => {
    const props = {
      ...defaultProps,
      entityType: 'site',
      assetCount: 5
    };

    render(<ListDrawerEntity {...props} />);

    // Check that the asset count is displayed
    expect(screen.getByText('5 Assets')).toBeInTheDocument();
  });

  it('should render a site entity without asset count', () => {
    const props = {
      ...defaultProps,
      entityType: 'site',
      // No assetCount provided
    };

    render(<ListDrawerEntity {...props} />);

    // Check that no secondary text is displayed
    expect(screen.queryByText(/Assets/)).not.toBeInTheDocument();

    // The secondary row should still be rendered but empty
    const secondaryRow = document.querySelector('.lde-secondary-row');
    expect(secondaryRow).toBeInTheDocument();
    expect(secondaryRow).toBeEmptyDOMElement();
  });

  it('should render site information for an asset', () => {
    const props = {
      ...defaultProps,
      siteInfo: { id: 101, name: 'Test Site' }
    };

    render(<ListDrawerEntity {...props} />);

    // Check that the site name is displayed as a tag
    expect(screen.getByText('Test Site')).toBeInTheDocument();
  });

  it('should render customer information when provided', () => {
    const props = {
      ...defaultProps,
      customerName: 'Test Customer'
    };

    render(<ListDrawerEntity {...props} />);

    // Check that the customer name is displayed
    expect(screen.getByText('Customer:')).toBeInTheDocument();
    expect(screen.getByText('Test Customer')).toBeInTheDocument();
  });

  it('should render offline tag when showDeviceOfflineTag is true', () => {
    const props = {
      ...defaultProps,
      showDeviceOfflineTag: true
    };

    render(<ListDrawerEntity {...props} />);

    // Check that the offline tag is displayed
    expect(screen.getByText('Offline')).toBeInTheDocument();
  });

  it('should call onClick with the entity ID when clicked', () => {
    render(<ListDrawerEntity {...defaultProps} />);

    // Click on the entity
    fireEvent.click(screen.getByText('Test Entity').closest('.map-list-drawer-entity')!);

    // Check that onClick was called with the correct ID
    expect(defaultProps.onClick).toHaveBeenCalledWith(1);
  });

  it('should not call onClick when onClick is not provided', () => {
    const props = { ...defaultProps };
    delete props.onClick;

    render(<ListDrawerEntity {...props} />);

    // Click on the entity
    fireEvent.click(screen.getByText('Test Entity').closest('.map-list-drawer-entity')!);

    // No error should occur
  });

  it('should render tooltips for long text', () => {
    const props = {
      ...defaultProps,
      name: 'Very Long Entity Name That Should Be Truncated',
      customerName: 'Very Long Customer Name',
      siteInfo: { id: 101, name: 'Very Long Site Name' }
    };

    render(<ListDrawerEntity {...props} />);

    // Check that tooltips are rendered for long text
    // Note: We can't easily test the tooltip content in this test setup
    // but we can check that the text is displayed
    expect(screen.getByText('Very Long Entity Name That Should Be Truncated')).toBeInTheDocument();
    expect(screen.getByText('Very Long Customer Name')).toBeInTheDocument();
    expect(screen.getByText('Very Long Site Name')).toBeInTheDocument();
  });

  it('should render dynamic component when provided', () => {
    const props = {
      ...defaultProps,
      dynamicComponent: { key: 'test-dynamic', config: { someConfig: 'value' } }
    };

    render(<ListDrawerEntity {...props} />);

    // Check that the dynamic component is rendered
    const dynamicComponent = screen.getByTestId('dynamic-component');
    expect(dynamicComponent).toBeInTheDocument();
    expect(dynamicComponent).toHaveAttribute('data-key', 'test-dynamic');
  });
});
