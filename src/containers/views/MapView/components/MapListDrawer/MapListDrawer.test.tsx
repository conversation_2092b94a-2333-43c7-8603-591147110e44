import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import MapListDrawer from './MapListDrawer';
import { MapListDrawerProps, ListDrawerEntityProps } from './types';
import '@testing-library/jest-dom';
import { expect, jest } from '@jest/globals';

// Track rendered heights in this variable
const mockRenderedHeights: Record<number, number> = {};

// Mock the react-window and react-virtualized components
jest.mock('react-window', () => {
  const resetAfterIndexMock = jest.fn();

  // Create a class component that can receive refs
  class MockVariableSizeList extends React.Component<any> {
    resetAfterIndex = resetAfterIndexMock;

    render() {
      const { children, itemCount, itemSize, height, width } = this.props;
      // Reset the heights before rendering
      Object.keys(mockRenderedHeights).forEach(key => delete mockRenderedHeights[Number(key)]);
      
      const items = Array.from({ length: itemCount }).map((_, index) => {
        const size = typeof itemSize === 'function' ? itemSize(index) : itemSize;
        // Store the calculated height for testing
        mockRenderedHeights[index] = size;
        return children({ index, style: { height: size, width } });
      });

      return (
        <div
          data-testid="virtualized-list"
          style={{ height, width }}
        >
          {items}
        </div>
      );
    }
  }

  return {
    VariableSizeList: MockVariableSizeList,
    resetAfterIndexMock
  };
});

jest.mock('react-virtualized/dist/commonjs/AutoSizer', () => ({
  __esModule: true,
  default: ({ children }: any) => children({ height: 500, width: 300 })
}));

// Mock the child components
jest.mock('./components/ListDrawerEntity', () => ({
  __esModule: true,
  default: (props: any) => (
    <div data-testid="list-drawer-entity" data-entity-id={props.id} onClick={() => props.onClick?.(props.id)}>
      {props.name}
    </div>
  )
}));

jest.mock('./components/ListDrawerSkeleton', () => ({
  __esModule: true,
  default: () => <div data-testid="list-drawer-skeleton">Loading...</div>
}));

jest.mock('../../../../../components/base/SearchBar', () => ({
  __esModule: true,
  default: ({ onSearch, placeholder }: any) => (
    <div data-testid="search-bar" data-placeholder={placeholder}>
      <input
        type="text"
        placeholder={placeholder}
        onChange={(e) => onSearch?.(e.target.value)}
        data-testid="search-input"
      />
    </div>
  )
}));

jest.mock('../../../../../components/base/CloseIcon', () => ({
  __esModule: true,
  default: ({ onClose }: any) => (
    <button data-testid="close-icon" onClick={onClose}>
      Close
    </button>
  )
}));

describe('MapListDrawer Component', () => {
  // Sample data for testing
  const mockItems: ListDrawerEntityProps[] = [
    {
      id: 1,
      name: 'Asset 1',
      entityType: 'asset',
      lastDataReceivedTime: '2023-01-01',
      statusIconConfig: { statusColor: '#00FF00' },
      siteInfo: { id: 101, name: 'Site A' },
      customerName: 'Customer X'
    },
    {
      id: 2,
      name: 'Asset 2',
      entityType: 'asset',
      lastDataReceivedTime: '2023-01-02',
      statusIconConfig: { statusColor: '#FFFF00' }
    },
    {
      id: 3,
      name: 'Site 1',
      entityType: 'site',
      statusIconConfig: { statusColor: '#FF0000' },
      assetCount: 5
    }
  ];

  const defaultProps: MapListDrawerProps = {
    onListDrawerClose: jest.fn(),
    onSearch: jest.fn(),
    onEntityClick: jest.fn(),
    items: mockItems,
    loading: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render the component with items', () => {
    render(<MapListDrawer {...defaultProps} />);

    // Check that the main container is rendered
    expect(screen.getByTestId('virtualized-list')).toBeInTheDocument();

    // Check that all items are rendered
    expect(screen.getByText('Asset 1')).toBeInTheDocument();
    expect(screen.getByText('Asset 2')).toBeInTheDocument();
    expect(screen.getByText('Site 1')).toBeInTheDocument();

    // Check that the search bar and close icon are rendered
    expect(screen.getByTestId('search-bar')).toBeInTheDocument();
    expect(screen.getByTestId('close-icon')).toBeInTheDocument();
  });

  it('should render the skeleton loader when loading is true', () => {
    render(<MapListDrawer {...defaultProps} loading={true} />);

    // Check that the skeleton loader is rendered
    expect(screen.getByTestId('list-drawer-skeleton')).toBeInTheDocument();

    // Check that the virtualized list is not rendered
    expect(screen.queryByTestId('virtualized-list')).not.toBeInTheDocument();
  });

  it('should call onListDrawerClose when close icon is clicked', () => {
    render(<MapListDrawer {...defaultProps} />);

    // Click the close icon
    fireEvent.click(screen.getByTestId('close-icon'));

    // Check that onListDrawerClose was called
    expect(defaultProps.onListDrawerClose).toHaveBeenCalledTimes(1);
  });

  it('should call onSearch when search input changes', () => {
    render(<MapListDrawer {...defaultProps} />);

    // Type in the search input
    fireEvent.change(screen.getByTestId('search-input'), { target: { value: 'test search' } });

    // Check that onSearch was called with the correct value
    expect(defaultProps.onSearch).toHaveBeenCalledWith('test search');
  });

  it('should call onEntityClick when an entity is clicked', () => {
    render(<MapListDrawer {...defaultProps} />);

    // Click on the first entity
    fireEvent.click(screen.getByText('Asset 1'));

    // Check that onEntityClick was called with the correct ID
    expect(defaultProps.onEntityClick).toHaveBeenCalledWith(1);
  });

  it('should render with empty items array', () => {
    render(<MapListDrawer {...defaultProps} items={[]} />);

    // Check that the "No Results Found!" message is displayed
    expect(screen.getByText('No Results Found!')).toBeInTheDocument();

    // Check that the list-drawer-entity is not rendered
    expect(screen.queryByTestId('list-drawer-entity')).not.toBeInTheDocument();
  });

  it('should handle undefined items prop', () => {
    const { onListDrawerClose, onSearch, onEntityClick } = defaultProps;
    render(<MapListDrawer
      onListDrawerClose={onListDrawerClose}
      onSearch={onSearch}
      onEntityClick={onEntityClick}
    />);

    // Should render the "No Results Found!" message
    expect(screen.getByText('No Results Found!')).toBeInTheDocument();
  });

  it('should handle different item types with varying properties', () => {
    const itemsWithVariedProperties: ListDrawerEntityProps[] = [
      // Asset with siteInfo and customerName
      {
        id: 1,
        name: 'Asset with all info',
        entityType: 'asset',
        statusIconConfig: { statusColor: '#00FF00' },
        siteInfo: { id: 101, name: 'Site A' },
        customerName: 'Customer X'
      },
      // Asset with siteInfo but no customerName
      {
        id: 2,
        name: 'Asset with site info',
        entityType: 'asset',
        statusIconConfig: { statusColor: '#FFFF00' },
        siteInfo: { id: 102, name: 'Site B' }
      },
      // Asset with customerName but no siteInfo
      {
        id: 3,
        name: 'Asset with customer',
        entityType: 'asset',
        statusIconConfig: { statusColor: '#FF0000' },
        customerName: 'Customer Y'
      },
      // Site with assetCount
      {
        id: 4,
        name: 'Site with assets',
        entityType: 'site',
        statusIconConfig: { statusColor: '#00FFFF' },
        assetCount: 10
      }
    ];

    render(
      <MapListDrawer
        {...defaultProps}
        items={itemsWithVariedProperties}
      />
    );

    // Verify all items render correctly
    expect(screen.getByText('Asset with all info')).toBeInTheDocument();
    expect(screen.getByText('Asset with site info')).toBeInTheDocument();
    expect(screen.getByText('Asset with customer')).toBeInTheDocument();
    expect(screen.getByText('Site with assets')).toBeInTheDocument();
  });

  it('should update when item list changes', () => {
    const { rerender } = render(<MapListDrawer {...defaultProps} />);
    
    // Initially renders with original items
    expect(screen.getByText('Asset 1')).toBeInTheDocument();
    
    // Create a new set of items
    const newItems = [
      ...mockItems,
      {
        id: 4,
        name: 'New Item',
        entityType: 'site' as const,
        statusIconConfig: { statusColor: '#0000FF' }
      }
    ];
    
    // Re-render with new items
    rerender(<MapListDrawer {...defaultProps} items={newItems} />);
    
    // Check that the new item is rendered
    expect(screen.getByText('New Item')).toBeInTheDocument();
  });

  it('should handle sparse arrays and calculate appropriate heights for different item types', () => {
    // Create a sparse array with different item types and properties
    const sparseItems: ListDrawerEntityProps[] = [];
    
    // Full asset with siteInfo and customerName
    sparseItems[0] = {
      id: 1,
      name: 'Asset with everything',
      entityType: 'asset',
      statusIconConfig: { statusColor: '#00FF00' },
      siteInfo: { id: 101, name: 'Site A' },
      customerName: 'Customer X'
    };
    
    // Skip index 1 to test sparse array handling
    
    // Basic asset with minimal properties
    sparseItems[2] = {
      id: 3,
      name: 'Basic asset',
      entityType: 'asset',
      statusIconConfig: { statusColor: '#FF0000' }
    };
    
    // Site type
    sparseItems[3] = {
      id: 4,
      name: 'Site entity',
      entityType: 'site',
      statusIconConfig: { statusColor: '#0000FF' }
    };
    
    // Render with our sparse array
    render(
      <MapListDrawer
        {...defaultProps}
        items={sparseItems}
      />
    );
    
    // Verify that the component rendered the items (this demonstrates non-sparse indices work)
    expect(screen.getByText('Asset with everything')).toBeInTheDocument();
    expect(screen.getByText('Basic asset')).toBeInTheDocument();
    expect(screen.getByText('Site entity')).toBeInTheDocument();
    
    // Verify all expected indices rendered something
    // The fact that we can click on all of them means the sparse array didn't break rendering
    fireEvent.click(screen.getByText('Asset with everything'));
    expect(defaultProps.onEntityClick).toHaveBeenCalledWith(1);
    
    fireEvent.click(screen.getByText('Basic asset'));
    expect(defaultProps.onEntityClick).toHaveBeenCalledWith(3);
    
    fireEvent.click(screen.getByText('Site entity'));
    expect(defaultProps.onEntityClick).toHaveBeenCalledWith(4);
    
    // If we got here without errors, the test passes - the component properly handled the sparse array
    // and properly rendered each item
  });
});
