/**
 * Type definitions for the MapListDrawer component and its child components
 * 
 * @module MapListDrawerTypes
 */

import type { StatusIconConfig } from "../../../../../components/configurable/StatusIcon/StatusIcon";

/**
 * Props for the ListDrawerEntity component
 * 
 * @interface ListDrawerEntityProps
 */
export interface ListDrawerEntityProps {
  /** Unique identifier for the entity */
  entityId: number; 
  /** Display name of the entity */
  name: string;
  /** Latitude of the entity */
  lat: number;
  /** Longitude of the entity */
  long: number;
  /** Type of entity - determines how information is displayed */
  entityType: "asset" | "site";
  /** Date information (for assets) or null if not available */
  lastDataReceivedTime?: string;
  /** Name of the customer associated with this entity */
  customerName?: string;
  /** Configuration for the status icon */
  statusIconConfig: StatusIconConfig;
  /** Callback function when entity is clicked */
  onClick?: (entityId: number, entityType: "asset" | "site", lat: number, long: number) => void;
  /** Flag to show "Offline" tag for devices that are offline */
  showDeviceOfflineTag?: boolean;

  /** Information about the site an asset belongs to */
  siteInfo?: {
    id: number;
    name: string;
  };

  /** Count of assets for site entities */
  assetCount?: number;

  /** Optional dynamic component to render */
  dynamicComponent?: { key: string; config: any };
}

/**
 * Props for the MapListDrawer component
 *
 * @interface MapListDrawerProps
 */
export interface MapListDrawerProps {
  /** Callback function when the drawer is closed */
  onListDrawerClose: () => void;
  /** Callback function when search is performed */
  onSearch: (searchText: any) => void;
  /** Callback function when an entity is clicked */
  onEntityClick: (entityId: number, entityType: "asset" | "site", lat: number, long: number) => void;
  /** Array of entity items to display in the list */
  items?: ListDrawerEntityProps[];
  /** Flag indicating if content is in loading state */
  loading?: boolean;
}

/**
 * Props for the ListDrawerSkeleton component
 * 
 * @interface ListDrawerSkeletonProps
 */
export interface ListDrawerSkeletonProps {
  /** Optional number of skeleton items to display, otherwise calculated based on viewport */
  count?: number;
} 