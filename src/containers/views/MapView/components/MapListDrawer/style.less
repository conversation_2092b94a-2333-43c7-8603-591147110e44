@grey-color: #808080;

#map-list-drawer {
    height: 100%;
    display: flex;
    flex-direction: column;

    .list-header {
        padding: 16px;
        display: flex;
        gap: 12px;
        justify-content: space-between;
        align-items: center;
    }

    .list-drawer-divider {
        border-color: #f0f0f0;
        border-width: 0.5px;
        border-style: solid;
        margin: 0;
    }

    .list-container {
        flex: 1;
        overflow: hidden auto;
        min-height: 0;
    }

    .list-empty {
        padding-bottom: 16px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 12px;
        height: 100%;

        span {
            font-size: 14px;
            color: @grey-color;
        }
    }
}
