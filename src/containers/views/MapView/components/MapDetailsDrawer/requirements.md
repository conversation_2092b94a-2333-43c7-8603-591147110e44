# Asset/Site Details 

## Common Header
 - Status Icon => (spacing, size)
 - Asset name => tooltip (ellipsis)
 - Date (last data) => No data/invalid data case handling
 - Offline tag
 - Dynamic Component (/ status indicator)                                          

 - site name (to which asset is linked) => max-width: 1/3, tooltip                  (OPTIONAL)
 - asset info => all data is shown when clicked on three dots


 - onClose (show a close icon to the right and when clicked, it will be called)
 - onBackClick (show a back arrow icon in the start when clicked, callback)
 - collapsibleComponent (show an expand icon and when clicked, that icon will change and will append this component)
 - styleKey (panel/ sitePanel/ mapHeader)
 - styleObject (object can be passed to style it) => optional 


### Extended
- Quick Links (icon, title)
- Alerts (Count, alert names)

- customer name
- location 



Quick Links: will be feature-controlled

 FOR asset info:  
 - make separate hook or function to make it take the rest of the space OR may keep it there only if any issue
 - if info values are not coming, don't show that info.


## Details Drawer
 - Common Header
 - Site Details / Asset Details

### Site Details
 - Asset List (category-wise)
 - Structure: 
   [
    {
        categoryId: 18,
        categoryName: DG Set,
        assets: [
            {
                entityConfig,
                extendedInfo,
                paramData
            }
        ]
    }
   ]

### Asset Details
- Asset Category Id
- Param data


### Common Header
 - entityConfig
    - id
    - name
    - entityType
    - lastDataReceivedTime
    - assetCount
    - showOfflineTag
    - statusIconConfig
    - dynamicComponent
    - siteInfo
    - status
    - assetInfo  (For ASSETS only)


 - onClose
 - onBackClick

 - asssetList   (for sites only)


 - collapsibleComponent
 - styleKey
 - styleObject    (OPTIONAL)