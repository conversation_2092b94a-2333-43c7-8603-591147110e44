import {type MapDetailsDrawerProps } from './types'
import './style.less'
import MapSiteData from './components/MapSiteData'
import MapAssetData from './components/MapAssetData'
import PanelTopHeader from '../../../components/PanelTopHeader'
import { Skeleton } from 'antd'

const MapDetailsDrawer = (props: MapDetailsDrawerProps) => {
  const { assetList, paramData, loading, ...headerProps } = props;

  return (
    <div id='map-details-drawer'>
      {
        loading ? (
          <Skeleton active style={{padding: '20px'}} />
        ): (
          <>
            <PanelTopHeader 
              {...headerProps}
            />
            {headerProps?.entityConfig?.entityType === 'site' && (
              <MapSiteData assetList={assetList || []} />
            )}
            {headerProps?.entityConfig?.entityType === 'asset' && (
              <MapAssetData categoryId={headerProps?.entityConfig?.categoryId} paramData={paramData} />
            )}
          </>
        )
      }
    </div>
  )
}

export default MapDetailsDrawer