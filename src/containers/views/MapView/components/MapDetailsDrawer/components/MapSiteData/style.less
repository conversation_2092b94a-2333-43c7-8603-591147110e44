@drop-shadow: 2px 3px 1px 0px rgba(0, 0, 0, 0.05);

.map-view-site-data {
    padding: 16px;

    .no-data {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        width: 100%;
        font-style: italic;
    }

    .heading {
        font-size: 14px;
        font-weight: 500;
    }

    .assets-list {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .category-wise-assets {
            display: flex;
            flex-direction: column;
            gap: 16px;

            .asset-item {
                // border-radius: 12px;
                
                .ant-collapse {
                    .ant-collapse-header-text {
                        overflow: hidden;
                    }

                    &:has(.ant-collapse-item-active) {
                        box-shadow: @drop-shadow;
                    }

                    .ant-collapse-content-active {
                        box-shadow: @drop-shadow;
                    }

                    .ant-collapse-expand-icon {
                        border: 1px solid #7686A1;
                        width: 20px;
                        height: 20px;
                        border-radius: 50%;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        margin-right: 8px;
                        margin-top: 4px;
                        padding-inline: 6px;
                    }

                    .ant-collapse-content-box {
                        padding: 0;
                    }
                }

                .site-panel-asset-data {
                    border-radius: 0 0 12px 12px;
                    border-bottom: 1px solid #f2f2f2;
                }
            }
        }

        .category-name {
            font-size: 12px;
            padding: 20px 0 12px;
        }
    }
    
}
