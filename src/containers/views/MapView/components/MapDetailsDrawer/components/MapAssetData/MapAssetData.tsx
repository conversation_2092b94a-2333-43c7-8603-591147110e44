import './style.less'
import DGPanel from '../../../../../components/CategorySpecificView/DGPanel'
import ColdRoomPanel from '../../../../../components/CategorySpecificView/ColdRoomPanel'

const MapAssetData = ({paramData, categoryId, className}: {paramData: any, categoryId?: number, className?: string}) => {
  
  const renderPanel = () => {
    switch(categoryId) {
      case 18:
        return <DGPanel paramData={paramData} data_loading={false} />
      case 45:
        return <ColdRoomPanel paramData={paramData} data_loading={false} />
      // default:
        // return <DGPanel data_loading={false} />
    }
  }

  return (
    <div className={`map-view-asset-data ${className}`}>
        {(paramData ) ? renderPanel() : (
            <div className="no-data">
                {/* <span>No Data</span> */}
            </div>
        )}
    </div>
  )
}

export default MapAssetData