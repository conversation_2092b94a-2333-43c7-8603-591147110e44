import { useState, useEffect, useRef } from 'react';
import { useMap } from '@vis.gl/react-google-maps';

/**
 * Custom hook that tracks the interaction state of a Google Map
 *
 * @returns A boolean indicating whether the map is currently being interacted with
 * (dragged or zoomed)
 *
 * @example
 * ```tsx
 * function MapComponent() {
 *   const isMapInteracting = useMapInteractionState();
 *
 *   return (
 *     <div>
 *       <Map />
 *       {!isMapInteracting && <SomeOverlay />}
 *     </div>
 *   );
 * }
 * ```
 */
export function useMapInteractionState(): boolean {
  const [isInteracting, setIsInteracting] = useState(false);
  const map = useMap();
  const listenersRef = useRef<google.maps.MapsEventListener[]>([]);

  /**
   * Removes all registered map event listeners
   */
  const cleanupListeners = () => {
    listenersRef.current.forEach(listener => listener.remove());
    listenersRef.current = [];
  }

  useEffect(() => {
    if (!map) return;

    // Cleanup previous listeners
    cleanupListeners();

    const handleInteractionStart = () => {
      setIsInteracting(true);
    };

    // Use 'idle' event which fires *after* dragging/zooming stops
    const handleInteractionEnd = () => {
      setIsInteracting(false);
    };

    // Add listeners
    listenersRef.current.push(map.addListener('dragstart', handleInteractionStart));
    listenersRef.current.push(map.addListener('zoom_changed', handleInteractionStart));
    listenersRef.current.push(map.addListener('idle', handleInteractionEnd));

    // Cleanup on unmount
    return () => {
      cleanupListeners();
    };
  }, [map]);

  return isInteracting;
}