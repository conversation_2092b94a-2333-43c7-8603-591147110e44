import { renderHook } from '@testing-library/react';
import { useFitBounds } from './useFitBounds';

// Mock the useMap hook
const mockUseMap = jest.fn();
jest.mock('@vis.gl/react-google-maps', () => ({
  useMap: () => mockUseMap(),
}), { virtual: true });

describe('useFitBounds', () => {
  // Mock map and bounds
  const mockMap = {
    fitBounds: jest.fn(),
    setCenter: jest.fn(),
    setZoom: jest.fn(),
  };
  
  const mockBounds = {
    extend: jest.fn(),
    isEmpty: jest.fn(),
    getNorthEast: jest.fn(),
    getSouthWest: jest.fn(),
    getCenter: jest.fn(),
  };

  // Sample data for tests
  const sampleIconArray = [{ lat: 10, long: 20 }, { lat: 30, long: 40 }];
  const sampleCenter = { lat: 20, long: 30 };

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup global google object
    global.google = {
      maps: {
        LatLngBounds: jest.fn().mockImplementation(() => mockBounds),
      },
    } as any;

    // Default mock returns
    mockUseMap.mockReturnValue(mockMap);
    mockBounds.isEmpty.mockReturnValue(false);
    mockBounds.getNorthEast.mockReturnValue({ equals: jest.fn().mockReturnValue(false) });
    mockBounds.getSouthWest.mockReturnValue({});
    mockBounds.getCenter.mockReturnValue({ lat: 15, long: 25 });
  });

  // Helper function to verify no bounds operations were performed
  const verifyNoBoundsOperations = () => {
    expect(mockBounds.extend).not.toHaveBeenCalled();
    expect(mockMap.fitBounds).not.toHaveBeenCalled();
  };

  describe('when conditions prevent bounds fitting', () => {
    it('does nothing when map is not available', () => {
      mockUseMap.mockReturnValue(null);
      renderHook(() => useFitBounds(sampleIconArray, undefined, undefined));
      verifyNoBoundsOperations();
    });

    it('does nothing when iconArray is empty', () => {
      renderHook(() => useFitBounds([], undefined, undefined));
      verifyNoBoundsOperations();
    });

    it('does nothing when zoom is provided', () => {
      renderHook(() => useFitBounds(sampleIconArray, 12, undefined));
      verifyNoBoundsOperations();
    });

    it('does nothing when center is provided', () => {
      renderHook(() => useFitBounds(sampleIconArray, undefined, sampleCenter));
      verifyNoBoundsOperations();
    });

    it('does nothing when bounds is empty', () => {
      mockBounds.isEmpty.mockReturnValue(true);
      renderHook(() => useFitBounds(sampleIconArray, undefined, undefined));

      expect(mockMap.fitBounds).not.toHaveBeenCalled();
      expect(mockMap.setCenter).not.toHaveBeenCalled();
      expect(mockMap.setZoom).not.toHaveBeenCalled();
    });
  });

  describe('when bounds fitting is performed', () => {
    it('extends bounds with each location and fits map to bounds', () => {
      renderHook(() => useFitBounds(sampleIconArray, undefined, undefined));

      expect(mockBounds.extend).toHaveBeenCalledTimes(2);
      expect(mockBounds.extend).toHaveBeenCalledWith({ lat: 10, long: 20 });
      expect(mockBounds.extend).toHaveBeenCalledWith({ lat: 30, long: 40 });
      expect(mockMap.fitBounds).toHaveBeenCalledWith(mockBounds, 50);
    });

    it('sets center and zoom when bounds north east equals south west (single point case)', () => {
      mockBounds.getNorthEast.mockReturnValue({ equals: jest.fn().mockReturnValue(true) });
      renderHook(() => useFitBounds([{ lat: 10, long: 20 }], undefined, undefined));

      expect(mockMap.setCenter).toHaveBeenCalledWith({ lat: 15, long: 25 });
      expect(mockMap.setZoom).toHaveBeenCalledWith(15);
    });
  });
});
