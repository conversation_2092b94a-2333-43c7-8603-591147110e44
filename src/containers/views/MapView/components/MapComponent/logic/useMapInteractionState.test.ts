import { renderHook, act } from '@testing-library/react';
import { useMapInteractionState } from './useMapInteractionState';

// Mock the useMap hook
const mockUseMap = jest.fn();
jest.mock('@vis.gl/react-google-maps', () => ({
  useMap: () => mockUseMap(),
}), { virtual: true });

describe('useMapInteractionState', () => {
  // Mock event listeners and map
  const mockListeners: { [key: string]: Function } = {};
  const mockListenerObjects: { remove: jest.Mock }[] = [];

  const mockMap = {
    addListener: jest.fn().mockImplementation((event, callback) => {
      mockListeners[event] = callback;
      const mockListener = { remove: jest.fn() };
      mockListenerObjects.push(mockListener);
      return mockListener;
    }),
  };

  // Expected event types
  const mapEvents = ['dragstart', 'zoom_changed', 'idle'];

  beforeEach(() => {
    jest.clearAllMocks();
    Object.keys(mockListeners).forEach(key => delete mockListeners[key]);
    mockListenerObjects.length = 0;
    mockUseMap.mockReturnValue(mockMap);
  });

  describe('initialization', () => {
    it('returns false by default', () => {
      const { result } = renderHook(() => useMapInteractionState());
      expect(result.current).toBe(false);
    });

    it('does nothing when map is not available', () => {
      mockUseMap.mockReturnValue(null);
      renderHook(() => useMapInteractionState());
      expect(mockMap.addListener).not.toHaveBeenCalled();
    });

    it('adds event listeners for dragstart, zoom_changed, and idle', () => {
      renderHook(() => useMapInteractionState());

      expect(mockMap.addListener).toHaveBeenCalledTimes(mapEvents.length);

      mapEvents.forEach(event => {
        expect(mockMap.addListener).toHaveBeenCalledWith(event, expect.any(Function));
      });
    });
  });

  describe('event handling', () => {
    it('sets isInteracting to true when dragstart event is fired', () => {
      const { result } = renderHook(() => useMapInteractionState());
      expect(result.current).toBe(false);

      act(() => {
        mockListeners.dragstart();
      });

      expect(result.current).toBe(true);
    });

    it('sets isInteracting to true when zoom_changed event is fired', () => {
      const { result } = renderHook(() => useMapInteractionState());
      expect(result.current).toBe(false);

      act(() => {
        mockListeners.zoom_changed();
      });

      expect(result.current).toBe(true);
    });

    it('sets isInteracting to false when idle event is fired', () => {
      const { result } = renderHook(() => useMapInteractionState());

      // First set to true
      act(() => {
        mockListeners.dragstart();
      });
      expect(result.current).toBe(true);

      // Then set back to false
      act(() => {
        mockListeners.idle();
      });
      expect(result.current).toBe(false);
    });
  });

  describe('cleanup', () => {
    it('cleans up listeners when unmounted', () => {
      const { unmount } = renderHook(() => useMapInteractionState());

      // Verify all listeners are registered
      expect(mockListenerObjects.length).toBe(mapEvents.length);

      unmount();

      // Verify all listeners are removed
      mockListenerObjects.forEach(listener => {
        expect(listener.remove).toHaveBeenCalledTimes(1);
      });
    });
  });
});
