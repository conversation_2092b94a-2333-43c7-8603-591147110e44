import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useMap } from '@vis.gl/react-google-maps';
import { MarkerClusterer, SuperClusterAlgorithm, Cluster } from '@googlemaps/markerclusterer';
import { IconArrayItemType } from '../types';
import { LocationMarker } from './LocationMarker';
import { useFitBounds } from '../logic/useFitBounds';

/**
 * Props for the ClusteredLocationMarkers component
 *
 * @property mapClustering - Whether to enable marker clustering
 * @property onClickZoom - Zoom level to set when a marker is clicked
 * @property handleIconClick - Optional callback when a marker is clicked
 * @property iconArray - Array of location data to display as markers
 * @property zoom - Optional fixed zoom level for the map
 * @property center - Optional fixed center point for the map
 * @property selectedPointer - Optional pointer to focus on the map
 */
export type ClusteredLocationMarkersProps = {
  mapClustering?: boolean;
  onClickZoom?: number;
  handleIconClick?: (item: IconArrayItemType, map: google.maps.Map | null) => void;
  iconArray?: IconArrayItemType[];
  zoom?: number;
  center?: google.maps.LatLngLiteral;
  selectedPointer?: {lat: number, long: number, entityId: number, entityType?: "site" | "asset"} | null;
  dataUpdatedFromSocket?: boolean;
  onMarkersLoadingChange?: (loading: boolean) => void; // New prop
}

/** Type alias for Google Maps AdvancedMarkerElement */
type MarkerType = google.maps.marker.AdvancedMarkerElement;

/**
 * Component that renders location markers with optional clustering
 *
 * This component manages a collection of location markers on a Google Map and provides:
 * - Optional marker clustering for better visualization of dense marker groups
 * - Automatic bounds fitting to show all markers in the initial view
 * - Marker click handling with automatic panning and zooming
 * - Support for custom marker icons and status indicators
 * - Automatic focus on selected markers
 *
 * @example
 * ```tsx
 * <ClusteredLocationMarkers
 *   mapClustering={true}
 *   onClickZoom={15}
 *   iconArray={locationData}
 *   handleIconClick={(location, map) => showLocationDetails(location.id)}
 * />
 * ```
 */
export const ClusteredLocationMarkers = memo(({
  mapClustering,
  onClickZoom,
  handleIconClick,
  iconArray = [],
  zoom,
  center,
  selectedPointer,
  dataUpdatedFromSocket,
  onMarkersLoadingChange
}: ClusteredLocationMarkersProps) => {
  const [markers, setMarkers] = useState<{[key: string]: MarkerType}>({});
  const map = useMap();

  const onClickFocus = useCallback((lat: number, long: number) => {
    if (map) {
      map.panTo({ lat: lat, lng: long });
      map.setZoom(onClickZoom ? onClickZoom : (map.getZoom() || 0) + 2);
    }
  }, [map, onClickZoom]);

  
  // Fit map bounds to show all markers in the initial view
  useFitBounds(iconArray, zoom, center, selectedPointer, dataUpdatedFromSocket || false);
  
  if(selectedPointer && !dataUpdatedFromSocket) {
    console.log("selected pointer in clustered location markers: ", selectedPointer);
    onClickFocus(selectedPointer.lat, selectedPointer.long);
  }
  /**
   * Create the marker clusterer instance when clustering is enabled
   * and the map is available
   */
  const clusterer = useMemo(() => {
    if (!map || !mapClustering) return null;

    return new MarkerClusterer({
      map
    });
  }, [map, mapClustering]);

  /**
   * Update markers in the clusterer when markers or clustering state changes
   */
  useEffect(() => {
    const currentClusterer = clusterer;
    if (!currentClusterer) {
      // If clustering is disabled, just show all markers on the map
      Object.values(markers).forEach(marker => {
        marker.map = map;
      });
    } else {
      // When clustering is enabled, markers should not be directly on the map
      Object.values(markers).forEach(marker => {
        marker.map = null;
      });

      currentClusterer.clearMarkers();
      currentClusterer.addMarkers(Object.values(markers) as any);

      // const timerId = setTimeout(() => {
      //   Object.values(markers).forEach(marker => { marker.map = null; });
      //   currentClusterer.clearMarkers();
      //   currentClusterer.addMarkers(Object.values(markers) as any);
      // }, 200);

      // return () => clearTimeout(timerId);
    }

    return () => {
      currentClusterer?.clearMarkers();
    }
  }, [clusterer, markers, map]);

  /**
   * Handle changes to the selected pointer by panning and zooming the map
   */
  useEffect(() => {
    if(selectedPointer) {
      handleMarkerClick(selectedPointer);
    }
  }, [selectedPointer, map]);

  /**
   * Callback to track marker references for clustering
   * Efficiently updates the markers state by only changing what's needed
   */
  const setMarkerRef = useCallback((marker: MarkerType | null, key: string) => {
    setMarkers(markers => {
      if ((marker && markers[key]) || (!marker && !markers[key])) {
        return markers;
      }

      if (marker) {
        return {...markers, [key]: marker};
      } else {
        const {[key]: _, ...newMarkers} = markers;
        return newMarkers;
      }
    });
  }, []);

  /**
   * Handle marker click events - calls the provided click handler
   * and pans/zooms the map to focus on the clicked location
   */
  const handleMarkerClick = useCallback((item: IconArrayItemType) => {
    if (handleIconClick) {
      handleIconClick(item, map);
    }
    onClickFocus(item.lat, item.long);
  }, [map, handleIconClick, onClickFocus]);

  // Track marker loading state
  useEffect(() => {
    if (onMarkersLoadingChange) {
      const expectedMarkerCount = iconArray.length;
      const actualMarkerCount = Object.keys(markers).length;
      
      if (expectedMarkerCount > 0) {
        const isLoading = actualMarkerCount < expectedMarkerCount;
        onMarkersLoadingChange(isLoading);
      } else {
        onMarkersLoadingChange(false);
      }
    }
  }, [markers, iconArray.length, onMarkersLoadingChange]);

  return (
    <>
      {iconArray.map((item) => (
        <LocationMarker
          key={`${item.entityId}_${item.entityType}`}
          pointerKey={`${item.entityId}_${item.entityType}`}
          location={item}
          onClick={() => handleMarkerClick(item)}
          setMarkerRef={setMarkerRef}
          customIcon={item.customIcon}
          statusIconConfig={item.statusIconConfig}
        />
      ))}
    </>
  );
});