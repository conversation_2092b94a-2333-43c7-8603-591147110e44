import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock dependencies
const mockAdvancedMarker = jest.fn();
const mockRefCallback = jest.fn();

// Mock the AdvancedMarker component
jest.mock('@vis.gl/react-google-maps', () => {
  const React = require('react');

  // Create a forwardRef component to handle refs properly
  const MockAdvancedMarker = React.forwardRef((props, ref) => {
    mockAdvancedMarker(props);

    // Call the ref callback directly
    React.useEffect(() => {
      if (ref) {
        const mockMarker = { map: {} };
        // @ts-ignore
        ref(mockMarker);
      }
    }, [ref]);

    return React.createElement('div', {
      'data-testid': 'advanced-marker',
      onClick: props.onClick,
      children: [
        props.children,
        React.createElement('span', {
          'data-testid': 'marker-position-lat',
          key: 'lat'
        }, props.position.lat),
        React.createElement('span', {
          'data-testid': 'marker-position-long',
          key: 'long'
        }, props.position.long)
      ]
    });
  });

  return {
    AdvancedMarker: MockAdvancedMarker
  };
}, { virtual: true });

// Mock useMapInteractionState hook
const mockIsMapInteracting = jest.fn().mockReturnValue(false);
jest.mock('../logic/useMapInteractionState', () => ({
  useMapInteractionState: () => mockIsMapInteracting()
}));

// Mock StatusIcon component
const mockStatusIcon = jest.fn().mockImplementation((props) => {
  return React.createElement('div', {
    'data-testid': 'status-icon',
    children: [
      React.createElement('span', {
        'data-testid': 'status-color',
        key: 'status-color'
      }, props.config.statusColor),
      React.createElement('span', {
        'data-testid': 'category-icon',
        key: 'category-icon'
      }, props.config.categoryIcon),
      React.createElement('span', {
        'data-testid': 'icon-type',
        key: 'icon-type'
      }, props.config.iconType),
      React.createElement('span', {
        'data-testid': 'tooltip-items-count',
        key: 'tooltip-items-count'
      }, props.config.tooltipItems ? props.config.tooltipItems.length : 0)
    ]
  });
});

jest.mock('../../../../../../components/configurable/StatusIcon', () => ({
  __esModule: true,
  default: (props) => mockStatusIcon(props)
}));

// Import the component after mocking
const { LocationMarker } = require('./LocationMarker');

describe('LocationMarker', () => {
  // Common test fixtures
  const mockLocation = { lat: 10, long: 20 };
  const mockOnClick = jest.fn();
  const mockSetMarkerRef = jest.fn();
  const mockPointerKey = 'test-key';
  const customIcon = 'https://example.com/icon.png';
  const statusIconConfig = {
    statusColor: 'red',
    categoryIcon: 'building',
    tooltipItems: [
      { title: 'Item 1', value: 'Value 1' },
      { title: 'Item 2', value: 'Value 2' }
    ]
  };

  // Setup and teardown
  beforeEach(() => {
    jest.clearAllMocks();
    mockIsMapInteracting.mockReturnValue(false);
  });

  // Helper function to render the component with default props
  const renderComponent = (props = {}) => {
    return render(
      React.createElement(LocationMarker, {
        pointerKey: mockPointerKey,
        location: mockLocation,
        onClick: mockOnClick,
        setMarkerRef: mockSetMarkerRef,
        ...props
      })
    );
  };

  describe('Ref callback functionality', () => {
    it('calls setMarkerRef with marker and pointerKey when ref is set', () => {
      const mockMarker = { map: {} };
      const customPointerKey = 'test-key-direct';
      const customSetMarkerRef = jest.fn();

      render(
        React.createElement(LocationMarker, {
          pointerKey: customPointerKey,
          location: mockLocation,
          onClick: jest.fn(),
          setMarkerRef: customSetMarkerRef
        })
      );

      // Get the ref callback from the AdvancedMarker props
      const advancedMarkerProps = mockAdvancedMarker.mock.calls[0][0];

      // Call the ref function directly with a marker
      if (typeof advancedMarkerProps.ref === 'function') {
        advancedMarkerProps.ref(mockMarker);
        expect(customSetMarkerRef).toHaveBeenCalledWith(mockMarker, customPointerKey);
      }
    });

    it('calls setMarkerRef with null and pointerKey when ref is null', () => {
      const customSetMarkerRef = jest.fn();

      render(
        React.createElement(LocationMarker, {
          pointerKey: mockPointerKey,
          location: mockLocation,
          onClick: jest.fn(),
          setMarkerRef: customSetMarkerRef
        })
      );

      // Get the ref callback from the AdvancedMarker props
      const advancedMarkerProps = mockAdvancedMarker.mock.calls[0][0];

      // Call the ref function directly with null
      if (typeof advancedMarkerProps.ref === 'function') {
        advancedMarkerProps.ref(null);
        expect(customSetMarkerRef).toHaveBeenCalledWith(null, mockPointerKey);
      }
    });
  });

  describe('AdvancedMarker rendering', () => {
    it('renders AdvancedMarker with correct position', () => {
      renderComponent();

      expect(screen.getByTestId('advanced-marker')).toBeInTheDocument();
      expect(screen.getByTestId('marker-position-lat')).toHaveTextContent('10');
      expect(screen.getByTestId('marker-position-long')).toHaveTextContent('20');
    });

    it('calls onClick handler when marker is clicked', () => {
      renderComponent();

      fireEvent.click(screen.getByTestId('advanced-marker'));
      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });
  });

  describe('Child content rendering', () => {
    it('renders nothing when neither statusIconConfig nor customIcon is provided', () => {
      renderComponent();

      expect(screen.queryByTestId('status-icon')).not.toBeInTheDocument();
      expect(screen.queryByAltText('marker')).not.toBeInTheDocument();
    });

    it('renders custom icon when customIcon is provided', () => {
      renderComponent({ customIcon });

      const imgElement = screen.getByAltText('marker');
      expect(imgElement).toBeInTheDocument();
      expect(imgElement).toHaveAttribute('src', customIcon);
    });

    it('renders StatusIcon when statusIconConfig is provided', () => {
      renderComponent({ statusIconConfig });

      expect(screen.getByTestId('status-icon')).toBeInTheDocument();
      expect(screen.getByTestId('status-color')).toHaveTextContent('red');
      expect(screen.getByTestId('category-icon')).toHaveTextContent('building');
      expect(screen.getByTestId('icon-type')).toHaveTextContent('map');
    });

    it('renders StatusIcon with default tooltipItems when map is not interacting', () => {
      renderComponent({ statusIconConfig });

      // Verify StatusIcon was called with the correct config
      expect(mockStatusIcon).toHaveBeenCalledWith(
        expect.objectContaining({
          config: expect.objectContaining({
            tooltipItems: expect.any(Array)
          })
        })
      );
    });

    it('renders StatusIcon with empty tooltipItems when map is interacting', () => {
      mockIsMapInteracting.mockReturnValue(true);
      renderComponent({ statusIconConfig });

      // Verify StatusIcon was called with empty tooltipItems
      expect(mockStatusIcon).toHaveBeenCalledWith(
        expect.objectContaining({
          config: expect.objectContaining({
            tooltipItems: []
          })
        })
      );
    });

    it('prioritizes statusIconConfig over customIcon when both are provided', () => {
      renderComponent({ statusIconConfig, customIcon });

      expect(screen.getByTestId('status-icon')).toBeInTheDocument();
      expect(screen.queryByAltText('marker')).not.toBeInTheDocument();
    });
  });

  describe('Integration tests', () => {
    it('handles prop changes correctly', () => {
      // Start with basic props
      const { rerender } = renderComponent();

      // Update to include customIcon
      rerender(
        React.createElement(LocationMarker, {
          pointerKey: mockPointerKey,
          location: mockLocation,
          onClick: mockOnClick,
          setMarkerRef: mockSetMarkerRef,
          customIcon: customIcon
        })
      );

      expect(screen.getByAltText('marker')).toBeInTheDocument();

      // Update to include statusIconConfig
      rerender(
        React.createElement(LocationMarker, {
          pointerKey: mockPointerKey,
          location: mockLocation,
          onClick: mockOnClick,
          setMarkerRef: mockSetMarkerRef,
          statusIconConfig: statusIconConfig
        })
      );

      expect(screen.getByTestId('status-icon')).toBeInTheDocument();
      expect(screen.queryByAltText('marker')).not.toBeInTheDocument();

      // Update to include both (statusIconConfig should take precedence)
      rerender(
        React.createElement(LocationMarker, {
          pointerKey: mockPointerKey,
          location: mockLocation,
          onClick: mockOnClick,
          setMarkerRef: mockSetMarkerRef,
          statusIconConfig: statusIconConfig,
          customIcon: customIcon
        })
      );

      expect(screen.getByTestId('status-icon')).toBeInTheDocument();
      expect(screen.queryByAltText('marker')).not.toBeInTheDocument();

      // Test with map interaction (should hide tooltip items)
      mockIsMapInteracting.mockReturnValue(true);

      rerender(
        React.createElement(LocationMarker, {
          pointerKey: mockPointerKey,
          location: mockLocation,
          onClick: mockOnClick,
          setMarkerRef: mockSetMarkerRef,
          statusIconConfig: statusIconConfig
        })
      );

      // Verify StatusIcon was called with empty tooltipItems
      expect(mockStatusIcon).toHaveBeenCalledWith(
        expect.objectContaining({
          config: expect.objectContaining({
            tooltipItems: []
          })
        })
      );
    });
  });
});
