import { useCallback } from 'react';
import { AdvancedMarker } from '@vis.gl/react-google-maps';
import { LocationMarkerPropsType } from '../types';
import StatusIcon from '../../../../../../components/configurable/StatusIcon';
import { useMapInteractionState } from '../logic/useMapInteractionState';

/**
 * A customizable marker component for displaying locations on a Google Map
 *
 * This component wraps Google Maps' AdvancedMarker and provides additional functionality:
 * - Can display either a StatusIcon or a custom image as the marker
 * - Handles marker references for parent components
 * - Disables tooltips during map interactions to improve performance
 * - Supports click events for interactive maps
 *
 * @param props - Component properties
 * @param props.pointerKey - Unique identifier for this marker
 * @param props.location - Geographic coordinates (lat/long) for marker placement
 * @param props.onClick - Optional callback function triggered when marker is clicked
 * @param props.setMarkerRef - Function to store reference to the marker element
 * @param props.customIcon - Optional URL to a custom marker image
 * @param props.statusIconConfig - Optional configuration for StatusIcon display
 *
 * @example
 * ```tsx
 * <LocationMarker
 *   pointerKey="location-1"
 *   location={{ lat: 37.7749, long: -122.4194 }}
 *   onClick={() => console.log('Marker clicked')}
 *   setMarkerRef={(marker, key) => saveMarkerRef(marker, key)}
 *   statusIconConfig={{ entityType: 'warning', size: 'medium' }}
 * />
 * ```
 */
export const LocationMarker = (props: LocationMarkerPropsType) => {
  const { pointerKey, location, onClick, setMarkerRef, customIcon, statusIconConfig } = props;

  // Track map interaction state to disable tooltips during dragging/zooming
  const isMapInteracting = useMapInteractionState();

  // Create callback ref to pass marker reference back to parent component
  const ref = useCallback(
    (marker: google.maps.marker.AdvancedMarkerElement | null) =>
      setMarkerRef(marker, pointerKey),
    [setMarkerRef, pointerKey]
  );

  // Disable tooltips during map interaction to improve performance
  const effectiveTooltipItems = isMapInteracting ? [] : statusIconConfig?.tooltipItems;

  return (
    <AdvancedMarker
      position={{ lat: location.lat, lng: location.long }}
      ref={ref}
      onClick={onClick}
    >
      {statusIconConfig ? (
        <StatusIcon
          config={{
            ...statusIconConfig,
            iconType: 'map',
            tooltipItems: effectiveTooltipItems
          }}
        />
      ) : customIcon ? (
        <img src={customIcon} alt="marker" />
      ) : null}
    </AdvancedMarker>
  );
};