import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ClusteredLocationMarkers } from './ClusteredLocationMarkers';

// Mock dependencies
const mockUseMap = jest.fn();
jest.mock('@vis.gl/react-google-maps', () => ({
  useMap: () => mockUseMap(),
}), { virtual: true });

// Mock MarkerClusterer functions
const mockClearMarkers = jest.fn();
const mockAddMarkers = jest.fn();

jest.mock('@googlemaps/markerclusterer', () => {
  return {
    MarkerClusterer: jest.fn().mockImplementation(() => ({
      clearMarkers: mockClearMarkers,
      addMarkers: mockAddMarkers,
    })),
  };
}, { virtual: true });

const mockUseFitBounds = jest.fn();
jest.mock('../logic/useFitBounds', () => ({
  useFitBounds: (...args) => mockUseFitBounds(...args),
}), { virtual: true });

// Mock LocationMarker with setMarkerRef functionality
jest.mock('./LocationMarker', () => {
  // Track marker references to simulate different scenarios
  const markerRefs = {};

  return {
    __markerRefs: markerRefs,
    LocationMarker: jest.fn().mockImplementation((props) => {
      // Call setMarkerRef with a mock marker when component mounts
      React.useEffect(() => {
        // If marker already exists for this key, don't create a new one
        // This helps test the early return condition in setMarkerRef
        if (!markerRefs[props.pointerKey]) {
          const mockMarker = { map: null };
          markerRefs[props.pointerKey] = mockMarker;
          props.setMarkerRef(mockMarker, props.pointerKey);
        } else {
          // Test the case where marker exists but is being updated
          props.setMarkerRef(markerRefs[props.pointerKey], props.pointerKey);
        }

        // Return cleanup function to test marker removal
        return () => {
          props.setMarkerRef(null, props.pointerKey);
          delete markerRefs[props.pointerKey];
        };
      }, [props.pointerKey]);

      return (
        <div data-testid={`location-marker-${props.pointerKey}`}>
          <span data-testid="location-lat">{props.location.lat}</span>
          <span data-testid="location-long">{props.location.long}</span>
          <button data-testid="marker-click-button" onClick={props.onClick}>Click Marker</button>
        </div>
      );
    }),
  };
}, { virtual: true });

describe('ClusteredLocationMarkers', () => {
  // Mock map instance
  const mockMap = {
    panTo: jest.fn(),
    setZoom: jest.fn(),
    getZoom: jest.fn().mockReturnValue(10),
  };

  const mockIconArray = [
    { lat: 10, long: 20, name: 'Location 1' },
    { lat: 30, long: 40, name: 'Location 2' },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseMap.mockReturnValue(mockMap);
  });

  it('renders correctly with default props', () => {
    render(<ClusteredLocationMarkers />);
    expect(mockUseFitBounds).toHaveBeenCalledWith([], undefined, undefined);
  });

  it('renders location markers for each item in iconArray', () => {
    render(<ClusteredLocationMarkers iconArray={mockIconArray} />);

    expect(screen.getByTestId('location-marker-0')).toBeInTheDocument();
    expect(screen.getByTestId('location-marker-1')).toBeInTheDocument();
  });

  it('handles marker management with clustering disabled', () => {
    // Render with clustering disabled
    const { unmount } = render(
      <ClusteredLocationMarkers
        mapClustering={false}
        iconArray={mockIconArray}
      />
    );

    // Verify MarkerClusterer was not created
    const { MarkerClusterer } = require('@googlemaps/markerclusterer');
    expect(MarkerClusterer).not.toHaveBeenCalled();

    // Unmount to test cleanup
    unmount();
  });

  it('handles marker management with clustering enabled', () => {
    // Render with clustering enabled
    const { unmount } = render(
      <ClusteredLocationMarkers
        mapClustering={true}
        iconArray={mockIconArray}
      />
    );

    // Verify MarkerClusterer was created
    const { MarkerClusterer } = require('@googlemaps/markerclusterer');
    expect(MarkerClusterer).toHaveBeenCalledWith({
      map: mockMap
    });

    // Verify markers were added to clusterer
    expect(mockAddMarkers).toHaveBeenCalled();

    // Unmount to test cleanup
    unmount();

    // Verify clusterer was cleared on unmount
    expect(mockClearMarkers).toHaveBeenCalled();
  });

  it('handles marker reference management correctly', () => {
    // Get access to the mocked LocationMarker module
    const LocationMarkerModule = require('./LocationMarker');

    // This test will trigger the setMarkerRef callback through our mocked LocationMarker
    const { rerender, unmount } = render(
      <ClusteredLocationMarkers
        iconArray={[mockIconArray[0]]}
      />
    );

    // Add a second marker to test different code paths in setMarkerRef
    rerender(
      <ClusteredLocationMarkers
        iconArray={mockIconArray}
      />
    );

    // Force a marker update to test the early return condition
    // This simulates when a marker is already in the markers state
    const existingKey = '0';
    const existingMarker = LocationMarkerModule.__markerRefs[existingKey];

    // Get access to the setMarkerRef function directly
    const { LocationMarker } = LocationMarkerModule;
    const setMarkerRefFn = (LocationMarker).mock.calls[0][0].setMarkerRef;

    // Call with same marker to test early return (marker exists and is the same)
    act(() => {
      setMarkerRefFn(existingMarker, existingKey);
    });

    // Call with null to test marker removal branch when marker exists
    act(() => {
      setMarkerRefFn(null, existingKey);
    });

    // Call with null to test marker removal branch when marker doesn't exist
    act(() => {
      setMarkerRefFn(null, '999');
    });

    // Unmount to test marker removal
    unmount();
  });

  it('handles marker click with callback', () => {
    const handleIconClickMock = jest.fn();

    render(
      <ClusteredLocationMarkers
        iconArray={mockIconArray}
        handleIconClick={handleIconClickMock}
        onClickZoom={15}
      />
    );

    fireEvent.click(screen.getAllByTestId('marker-click-button')[0]);

    expect(handleIconClickMock).toHaveBeenCalledWith(mockIconArray[0], mockMap);
    expect(mockMap.panTo).toHaveBeenCalledWith({ lat: mockIconArray[0].lat, long: mockIconArray[0].long });
    expect(mockMap.setZoom).toHaveBeenCalledWith(15);
  });

  it('handles marker click without callback', () => {
    // Test with map.getZoom() returning 0
    mockMap.getZoom.mockReturnValue(0);

    render(<ClusteredLocationMarkers iconArray={mockIconArray} />);

    fireEvent.click(screen.getAllByTestId('marker-click-button')[0]);

    expect(mockMap.panTo).toHaveBeenCalledWith({ lat: mockIconArray[0].lat, long: mockIconArray[0].long });
    expect(mockMap.setZoom).toHaveBeenCalledWith(2); // 0 + 2
  });

  it('handles marker click with undefined map', () => {
    // Mock useMap to return undefined
    mockUseMap.mockReturnValue(undefined);

    render(<ClusteredLocationMarkers iconArray={mockIconArray} />);

    // This should not throw an error
    fireEvent.click(screen.getAllByTestId('marker-click-button')[0]);

    // Restore mock
    mockUseMap.mockReturnValue(mockMap);
  });

  it('handles selected pointer with zoom matching onClickZoom', () => {
    const selectedPointer = { lat: 50, long: 60 };
    const onClickZoom = 10; // Same as mockMap.getZoom()

    mockMap.getZoom.mockReturnValue(10);

    render(
      <ClusteredLocationMarkers
        iconArray={mockIconArray}
        selectedPointer={selectedPointer}
        onClickZoom={onClickZoom}
      />
    );

    expect(mockMap.panTo).toHaveBeenCalledWith(selectedPointer);
    // setZoom is called even when current zoom equals onClickZoom
    expect(mockMap.setZoom).toHaveBeenCalledWith(onClickZoom);
  });

  it('handles selected pointer with different zoom', () => {
    const selectedPointer = { lat: 50, long: 60 };
    const onClickZoom = 16;

    mockMap.getZoom.mockReturnValue(10);

    render(
      <ClusteredLocationMarkers
        iconArray={mockIconArray}
        selectedPointer={selectedPointer}
        onClickZoom={onClickZoom}
      />
    );

    expect(mockMap.panTo).toHaveBeenCalledWith(selectedPointer);
    expect(mockMap.setZoom).toHaveBeenCalledWith(onClickZoom);
  });

  it('handles selected pointer with undefined onClickZoom', () => {
    const selectedPointer = { lat: 50, long: 60 };

    // Set getZoom to return 0 to test the fallback
    mockMap.getZoom.mockReturnValue(0);

    render(
      <ClusteredLocationMarkers
        iconArray={mockIconArray}
        selectedPointer={selectedPointer}
        // No onClickZoom provided
      />
    );

    expect(mockMap.panTo).toHaveBeenCalledWith(selectedPointer);
    expect(mockMap.setZoom).toHaveBeenCalledWith(2); // 0 + 2
  });

  it('does nothing when selectedPointer is null', () => {
    render(
      <ClusteredLocationMarkers
        iconArray={mockIconArray}
        selectedPointer={null}
      />
    );

    expect(mockMap.panTo).not.toHaveBeenCalled();
    expect(mockMap.setZoom).not.toHaveBeenCalled();
  });

  it('uses useFitBounds with correct parameters', () => {
    const zoom = 12;
    const center = { lat: 0, long: 0 };

    render(
      <ClusteredLocationMarkers
        iconArray={mockIconArray}
        zoom={zoom}
        center={center}
      />
    );

    expect(mockUseFitBounds).toHaveBeenCalledWith(mockIconArray, zoom, center);
  });
});
