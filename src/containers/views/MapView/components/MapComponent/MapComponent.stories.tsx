import React from "react";
import { <PERSON>Fn, Meta } from "@storybook/react";
import MapComponent from "./MapComponent";
import {
  Canvas,
  Source,
  Stories,
  Subtitle,
  Title,
  Controls,
} from "@storybook/blocks";
import { Table } from "@storybook/components";
import { IconArrayItemType } from "./types";

interface RowProps {
  key: string;
  required: string;
  allowedValues: string;
  description: string;
  defaultValue: string;
}

const CustomPropsTable = ({ rows }: { rows: RowProps[] }) => (
  <Table>
    <thead>
      <tr>
        <th>Key</th>
        <th>Required</th>
        <th>Allowed Values</th>
        <th>Default Value</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      {rows?.map((row: RowProps) => (
        <tr key={row.key}>
          <td>{row.key}</td>
          <td>{row.required}</td>
          <td>{row.allowedValues}</td>
          <td>{row.defaultValue}</td>
          <td>{row.description}</td>
        </tr>
      ))}
    </tbody>
  </Table>
);

// Mock API key for Storybook environment
import.meta.env.VITE_MAPS_API_KEY = "YOUR_GOOGLE_MAPS_API_KEY";

export default {
  title: "Views/MapView/MapComponent",
  component: MapComponent,
  tags: ["autodocs"],
  parameters: {
    layout: "fullscreen",
    componentSubtitle: "A Google Maps component that displays locations with optional clustering",
    docs: {
      page: () => (
        <>
          <Title>MapComponent</Title>
          <p>
            This component wraps the Google Maps API to provide a map with markers
            that can be clustered. It uses the @vis.gl/react-google-maps library.
          </p>
          <CustomPropsTable
            rows={[
              {
                key: "zoom",
                required: "N",
                allowedValues: "number (1-20)",
                defaultValue: "10",
                description: "The initial zoom level of the map",
              },
              {
                key: "center",
                required: "N",
                allowedValues: "{ lat: number, long: number }",
                defaultValue: "{ lat: 20.5937, long: 78.9629 }",
                description: "The initial center position of the map",
              },
              {
                key: "clustering",
                required: "N",
                allowedValues: "boolean",
                defaultValue: "false",
                description: "Whether to enable marker clustering",
              },
              {
                key: "onClickZoom",
                required: "N",
                allowedValues: "number (1-20)",
                defaultValue: "current zoom + 2",
                description: "The zoom level to set when a marker is clicked",
              },
              {
                key: "handleIconClick",
                required: "N",
                allowedValues: "function",
                defaultValue: "-",
                description: "Callback function when a marker is clicked",
              },
              {
                key: "iconArray",
                required: "Y",
                allowedValues: "Array",
                defaultValue: "[]",
                description: "Array of locations to display on the map",
              },
              {
                key: "iconArray.lat",
                required: "Y",
                allowedValues: "number",
                defaultValue: "-",
                description: "Latitude coordinate of the location",
              },
              {
                key: "iconArray.long",
                required: "Y",
                allowedValues: "number",
                defaultValue: "-",
                description: "Longitude coordinate of the location",
              },
              {
                key: "iconArray.statusIconConfig",
                required: "N",
                allowedValues: "object",
                defaultValue: "-",
                description: "Configuration for the status icon",
              },
              {
                key: "iconArray.statusIconConfig.statusColor",
                required: "N",
                allowedValues: "string (hex color)",
                defaultValue: "#008C3F",
                description: "Main color of the status icon. Required if statusIconConfig is provided",
              },
              {
                key: "iconArray.statusIconConfig.deviceStatusColor",
                required: "N",
                allowedValues: "string (hex color)",
                defaultValue: "",
                description: "Color for the outer part of the icon frame",
              },
              {
                key: "iconArray.statusIconConfig.categoryIcon",
                required: "Y*",
                allowedValues: "string (URL)",
                defaultValue: "-",
                description: "URL of the icon to display. Required if statusIconConfig is provided",
              },
              {
                key: "iconArray.statusIconConfig.iconType",
                required: "N",
                allowedValues: "'map' | 'normal'",
                defaultValue: "normal",
                description: "Type of the icon display",
              },
              {
                key: "iconArray.statusIconConfig.tooltipItems",
                required: "N",
                allowedValues: "Array<{ title: string, value: string }>",
                defaultValue: "[]",
                description: "Array of tooltip items to display on hover",
              },
              {
                key: "iconArray.customIcon",
                required: "N",
                allowedValues: "string (URL)",
                defaultValue: "-",
                description: "URL of a custom icon to use instead of statusIconConfig",
              },
              {
                key: "selectedPointer",
                required: "N",
                allowedValues: "{ lat: number, long: number } | null",
                defaultValue: "null",
                description: "The currently selected location",
              },
            ]}
          />
          <Subtitle />
          <Source of={BasicMap} />
          <Stories />
          <Subtitle>Interactive Examples</Subtitle>
          <Canvas of={BasicMap} />
          <Controls of={BasicMap} />
        </>
      ),
    },
  },
  argTypes: {
    zoom: {
      control: { type: "number", min: 1, max: 20 },
      description: "Initial zoom level of the map",
    },
    center: {
      control: "object",
      description: "Initial center position of the map",
    },
    clustering: {
      control: "boolean",
      description: "Enable marker clustering",
    },
    onClickZoom: {
      control: { type: "number", min: 1, max: 20 },
      description: "Zoom level when a marker is clicked",
    },
    handleIconClick: {
      action: "marker clicked",
    },
    iconArray: {
      control: "object",
      description: "Array of locations to display on the map",
    },
    selectedPointer: {
      control: "object",
      description: "Currently selected location",
    },
  },
} as Meta<typeof MapComponent>;

// Sample data for the map
const sampleLocations: IconArrayItemType[] = [
  {
    lat: 28.6139,
    long: 77.2090,
    statusIconConfig: {
      statusColor: "#008C3F",
      deviceStatusColor: "#BAEBC7",
      categoryIcon: "https://static.datoms.io/images/icons/thing-category/solar-pump-transparent.svg",
      tooltipItems: [
        { title: "Name", value: "Delhi Asset" },
        { title: "Status", value: "Active" },
      ],
    },
  },
  {
    lat: 19.0760,
    long: 72.8777,
    statusIconConfig: {
      statusColor: "#964B00",
      deviceStatusColor: "#BAEBC7",
      categoryIcon: "https://static.datoms.io/images/icons/thing-category/battery-transparent.svg",
      tooltipItems: [
        { title: "Name", value: "Mumbai Asset" },
        { title: "Status", value: "Active" },
      ],
    },
  },
  {
    lat: 12.9716,
    long: 77.5946,
    statusIconConfig: {
      statusColor: "#FF0000",
      deviceStatusColor: "#FFCCCB",
      categoryIcon: "https://static.datoms.io/images/icons/thing-category/default.svg",
      tooltipItems: [
        { title: "Name", value: "Bangalore Asset" },
        { title: "Status", value: "Error" },
      ],
    },
  },
  {
    lat: 22.5726,
    long: 88.3639,
    statusIconConfig: {
      statusColor: "#FFA500",
      deviceStatusColor: "#FFEFD5",
      categoryIcon: "https://static.datoms.io/images/icons/thing-category/solar-pump-transparent.svg",
      tooltipItems: [
        { title: "Name", value: "Kolkata Asset" },
        { title: "Status", value: "Warning" },
      ],
    },
  },
  {
    lat: 17.3850,
    long: 78.4867,
    statusIconConfig: {
      statusColor: "#008C3F",
      deviceStatusColor: "#BAEBC7",
      categoryIcon: "https://static.datoms.io/images/icons/thing-category/battery-transparent.svg",
      tooltipItems: [
        { title: "Name", value: "Hyderabad Asset" },
        { title: "Status", value: "Active" },
      ],
    },
  },
];

const Template: StoryFn<typeof MapComponent> = (args) => (
  <div style={{ width: "100%", height: "600px" }}>
    <MapComponent {...args} />
  </div>
);

export const BasicMap = Template.bind({});
BasicMap.args = {
  clustering: false,
  onClickZoom: 12,
  iconArray: sampleLocations,
  selectedPointer: null,
};

export const ClusteredMap = Template.bind({});
ClusteredMap.args = {
  clustering: true,
  onClickZoom: 12,
  iconArray: sampleLocations,
  selectedPointer: null,
};

export const ZoomedInMap = Template.bind({});
ZoomedInMap.args = {
  zoom: 12,
  center: { lat: 28.6139, long: 77.7090 },
  clustering: false,
  onClickZoom: 15,
  iconArray: sampleLocations,
  selectedPointer: null,
};

export const ZoomedInToPointer = Template.bind({});
ZoomedInToPointer.args = {
  zoom: 12,
  center: { lat: 28.6139, long: 77.2090 }, // Delhi
  clustering: false,
  onClickZoom: 15,
  iconArray: sampleLocations,
  selectedPointer: {lat: 17.3850, long: 78.4867},
};

export const EmptyMap = Template.bind({});
EmptyMap.args = {
  zoom: 5,
  center: { lat: 20.5937, long: 78.9629 }, // Center of India
  clustering: false,
  onClickZoom: 12,
  iconArray: [],
  selectedPointer: null,
};
