import React, { memo, useState, useEffect } from "react";
import { APIProvider, Map } from "@vis.gl/react-google-maps";
import { ClusteredLocationMarkers } from "./components/ClusteredLocationMarkers";
import { MapComponentPropsType } from "./types";
import { MAPS_API_KEY } from "../../../../../utils/env";
import "./style.less";
import AntSpin from "@datoms/react-components/src/components/AntSpin";
/** Google Maps libraries required by the application */
const mapLibraries = ["marker", "geometry", "places", "drawing"];

/**
 * A reusable Google Maps component with support for markers and clustering
 *
 * This component provides a complete Google Maps implementation with:
 * - Google Maps API integration with proper API key handling
 * - Support for location markers with custom icons or status indicators
 * - Optional marker clustering for better visualization of dense marker groups
 * - Customizable initial zoom level and center point
 * - Interactive marker click handling
 * - Support for programmatically selecting and focusing on specific markers
 *
 * @param props - Component properties
 * @param props.zoom - Optional custom zoom level (defaults to 10)
 * @param props.center - Optional custom center coordinates (defaults to center of India)
 * @param props.clustering - Whether to enable marker clustering
 * @param props.onClickZoom - Zoom level to set when a marker is clicked
 * @param props.handleIconClick - Optional callback when a marker is clicked
 * @param props.iconArray - Array of location data to display as markers
 * @param props.selectedPointer - Optional pointer to focus on the map
 *
 * @example
 * ```tsx
 * <MapComponent
 *   zoom={12}
 *   center={{ lat: 37.7749, lng: -122.4194 }}
 *   clustering={true}
 *   onClickZoom={15}
 *   iconArray={locationData}
 *   handleIconClick={(location) => showLocationDetails(location)}
 *   selectedPointer={selectedLocation}
 * />
 * ```
 */
const MapComponent: React.FC<MapComponentPropsType> = ({
  zoom,
  center,
  clustering,
  onClickZoom,
  handleIconClick,
  iconArray = [],
  selectedPointer,
  dataUpdatedFromSocket,
  loading
}) => {
  const [markersLoading, setMarkersLoading] = useState(true);

  const handleMarkersLoadingChange = (loading: boolean) => {
    setMarkersLoading(loading);
  };

  useEffect(() => {
    if (loading) {
      setMarkersLoading(true);
    }
  }, [loading]);

  const defaultZoom = zoom || 10;
  const defaultCenter = center || { lat: 20.5937, lng: 78.9629 };

  return (
    <div className="new-map-comp">
      <APIProvider
        apiKey={MAPS_API_KEY}
        libraries={mapLibraries}
      >
        <Map
          mapId={"42283ea186936f2"}
          defaultZoom={defaultZoom}
          defaultCenter={defaultCenter}
          gestureHandling={"greedy"} // Allows one-finger panning on touch devices
          disableDefaultUI // Removes default UI controls for a cleaner look
        >
          {!loading && <ClusteredLocationMarkers
            mapClustering={clustering}
            onClickZoom={onClickZoom}
            handleIconClick={handleIconClick}
            iconArray={iconArray}
            zoom={zoom}
            center={center}
            selectedPointer={selectedPointer}
            dataUpdatedFromSocket={dataUpdatedFromSocket}
            onMarkersLoadingChange={handleMarkersLoadingChange}
          />}
        </Map>
      </APIProvider>

      {loading && 
        <div className="map-loading-container">
          <div className="map-loading-spinner">
            <AntSpin />
          </div>
          <div className="map-loading-text">
            Loading data...
          </div>
        </div>
      }

      {!loading && markersLoading && 
        <div className="map-loading-container">
          <div className="map-loading-spinner">
            <AntSpin />
          </div>
        </div>
      }
    </div>
  );
};

export default memo(MapComponent);
