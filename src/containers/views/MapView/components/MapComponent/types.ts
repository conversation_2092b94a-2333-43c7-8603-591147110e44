export interface LocationType {
  lat: number;
  long: number;
  entityType?: "asset" | "site";
  name: string;
}

export interface MapLocationsType {
  locations: LocationType[];
}

export interface StatusIconPropsType {
  statusColor: string;
  deviceStatusColor?: string;
  categoryIcon: string;
  iconType?: string;
  tooltipItems?: Array<{
    title: string;
    value: string;
  }>;
}

export interface IconArrayItemType {
  lat: number;
  long: number;
  entityId: number;
  entityType?: "asset" | "site";
  statusIconConfig?: StatusIconPropsType;
  customIcon?: string;
}

export interface ClusteredLocationMarkersPropsType {
  mapClustering?: boolean;
  clusterRadius?: number;
  onClickZoom?: number;
  handleIconClick?: (item: IconArrayItemType) => void;
  iconArray?: IconArrayItemType[];
}

export interface LocationMarkerPropsType {
  pointerKey: string;
  location: IconArrayItemType;
  onClick: () => void;
  setMarkerRef: (marker: google.maps.marker.AdvancedMarkerElement | null, key: string) => void;
  customIcon?: string;
  statusIconConfig?: StatusIconPropsType;
}

export interface MapComponentPropsType {
  zoom?: number;
  center?: google.maps.LatLngLiteral;
  clustering?: boolean;
  onClickZoom?: number;
  handleIconClick?: (item: IconArrayItemType) => void;
  iconArray?: IconArrayItemType[];
  selectedPointer?: {lat: number, long: number, entityId: number, entityType?: "site" | "asset"} | null;
  dataUpdatedFromSocket?: boolean;
  loading?: boolean;
}
