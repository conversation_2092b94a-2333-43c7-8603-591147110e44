import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock the dependencies
jest.mock('@vis.gl/react-google-maps', () => {
  return {
    APIProvider: function MockAPIProvider(props) {
      return (
        <div
          data-testid="api-provider"
          data-api-key={props.apiKey}
          data-libraries={props.libraries.join(',')}
        >
          {props.children}
        </div>
      );
    },
    Map: function MockMap(props) {
      return (
        <div
          data-testid="google-map"
          data-map-id={props.mapId}
          data-default-zoom={props.defaultZoom}
          data-default-center={JSON.stringify(props.defaultCenter)}
          data-gesture-handling={props.gestureHandling}
          data-disable-default-ui={props.disableDefaultUI ? "true" : "false"}
        >
          {props.children}
        </div>
      );
    }
  };
}, { virtual: true });

// Mock the ClusteredLocationMarkers component
jest.mock('./components/ClusteredLocationMarkers', () => {
  return {
    ClusteredLocationMarkers: function MockClusteredLocationMarkers(props) {
      return (
        <div data-testid="clustered-markers">
          <div data-testid="clustered-markers-props">{JSON.stringify(props)}</div>
        </div>
      );
    }
  };
});

// Mock the environment variable
jest.mock('../../../../../utils/env', () => {
  return {
    MAPS_API_KEY: 'test-api-key'
  };
}, { virtual: true });

// Now import the component after mocking
const MapComponent = require('./MapComponent').default;

describe('MapComponent', () => {
  const mockProps = {
    zoom: 12,
    center: { lat: 40.7128, long: -74.006 },
    clustering: true,
    onClickZoom: 15,
    handleIconClick: jest.fn(),
    iconArray: [
      { lat: 40.7128, long: -74.006, statusIconConfig: { statusColor: 'green', categoryIcon: 'building' } },
      { lat: 40.7129, long: -74.007, customIcon: 'custom-icon.png' },
    ],
    selectedPointer: { lat: 40.7128, long: -74.006 },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders with correct configuration and passes props to child components', () => {
    // Render with default props
    render(<MapComponent />);

    // 1. Verify basic rendering
    expect(screen.getByTestId('api-provider')).toBeInTheDocument();
    expect(screen.getByTestId('google-map')).toBeInTheDocument();
    expect(screen.getByTestId('clustered-markers')).toBeInTheDocument();

    // 2. Verify API Provider configuration
    const apiProvider = screen.getByTestId('api-provider');
    expect(apiProvider).toHaveAttribute('data-api-key', 'test-api-key');
    expect(apiProvider).toHaveAttribute('data-libraries', 'marker,geometry,places,drawing');

    // 3. Verify Map component default props
    const mapElement = screen.getByTestId('google-map');
    expect(mapElement).toHaveAttribute('data-default-zoom', '10');
    const defaultCenter = JSON.parse(mapElement.getAttribute('data-default-center') || '{}');
    expect(defaultCenter).toEqual({ lat: 20.5937, long: 78.9629 });
    expect(mapElement).toHaveAttribute('data-map-id', '42283ea186936f2');
    expect(mapElement).toHaveAttribute('data-gesture-handling', 'greedy');
    expect(mapElement).toHaveAttribute('data-disable-default-ui', 'true');

    // 4. Verify ClusteredLocationMarkers default props
    const clusterProps = JSON.parse(screen.getByTestId('clustered-markers-props').textContent || '{}');
    expect(clusterProps).toMatchObject({
      iconArray: [],
    });
  });

  test('passes custom props correctly to child components', () => {
    // Render with custom props
    render(<MapComponent {...mockProps} />);

    // 1. Verify Map component custom props
    const mapElement = screen.getByTestId('google-map');
    expect(mapElement).toHaveAttribute('data-default-zoom', mockProps.zoom.toString());
    const customCenter = JSON.parse(mapElement.getAttribute('data-default-center') || '{}');
    expect(customCenter).toEqual(mockProps.center);

    // 2. Verify ClusteredLocationMarkers custom props
    const clusterProps = JSON.parse(screen.getByTestId('clustered-markers-props').textContent || '{}');
    expect(clusterProps).toMatchObject({
      iconArray: mockProps.iconArray,
      mapClustering: mockProps.clustering,
      onClickZoom: mockProps.onClickZoom,
      center: mockProps.center,
    });
  });

  test('handles partial props correctly', () => {
    // Test with only some props provided
    const partialProps = {
      clustering: true,
      onClickZoom: 15,
      handleIconClick: jest.fn(),
      iconArray: [{ lat: 10, long: 20 }],
    };

    render(<MapComponent {...partialProps} />);

    // 1. Verify default values are used for missing props
    const mapElement = screen.getByTestId('google-map');
    expect(mapElement).toHaveAttribute('data-default-zoom', '10'); // Default zoom
    const defaultCenter = JSON.parse(mapElement.getAttribute('data-default-center') || '{}');
    expect(defaultCenter).toEqual({ lat: 20.5937, long: 78.9629 }); // Default center

    // 2. Verify provided props are passed correctly
    const clusterProps = JSON.parse(screen.getByTestId('clustered-markers-props').textContent || '{}');
    expect(clusterProps).toMatchObject({
      iconArray: partialProps.iconArray,
      mapClustering: partialProps.clustering,
      onClickZoom: partialProps.onClickZoom,
    });
  });

  test('handles empty iconArray correctly', () => {
    // Test with empty iconArray
    render(<MapComponent iconArray={[]} />);

    // Verify ClusteredLocationMarkers is called with empty array
    const clusterProps = JSON.parse(screen.getByTestId('clustered-markers-props').textContent || '{}');
    expect(clusterProps).toMatchObject({
      iconArray: [],
    });
  });
});
