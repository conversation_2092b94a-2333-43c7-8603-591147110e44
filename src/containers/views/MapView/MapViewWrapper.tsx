import { useState, useCallback } from 'react';
import MapView from './MapView';
import useMapTypeUrl from './hooks/useMapTypeUrl';

const MapViewWrapper = (props: any) => {
  const [selectedMapType, setSelectedMapType] = useMapTypeUrl(undefined);
  const [mapViewKey, setMapViewKey] = useState<number>(0);

  const onMapTypeChange = useCallback((value: "sites" | "assets") => {
    // Update mapType and URL
    setSelectedMapType(value);
    
    // Force MapView to re-render from scratch by changing its key
    setMapViewKey(prev => prev + 1);

  }, [setSelectedMapType]);

  return (
    <div>
        {selectedMapType && <MapView 
          key={mapViewKey}
          onMapTypeChange={onMapTypeChange} 
          selectedMapType={selectedMapType} 
          queryParam={`?map_type=${selectedMapType}`}
          {...props} 
        />}
    </div>
  )
}

export default MapViewWrapper