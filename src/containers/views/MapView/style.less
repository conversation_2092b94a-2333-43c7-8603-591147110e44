@white-color: #fff;

#map-view-new {
	height: calc(100vh - 52px);
	width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;

    .map-view-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px 6px;
        background-color: @white-color;
        height: 51px;

        .mvh-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-section {
            min-width: 200px;
            // .generic-filter-drawer {
            //     z-index: 1200;
            // }
        }

        .cluster-switch {
            padding-right: 18px;
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
    }

    .map-view-content {
        position: relative;
        flex: 1;
        width: 100%;
    }

    .status-div {
        width: fit-content;
        display: flex;
        align-items: center;
        gap: 25px;
        background-color: @white-color;
        border-radius: 15px;
        padding: 7px 20px;
        position: absolute;
        top: 40px;
        margin: 38px 20px 0px;
        z-index: 1;
    }

    .kpi-container {
        width: 100%;
        position: absolute;
        margin-top: 18px;
        margin-left: 20px;
        z-index: 1;
    }

    .list-drawer-button {
        position: absolute;
        top: 16px;
        right: 25px;
        width: 48px;
        height: 48px;
        z-index: 1;

        svg {
            width: 18px;
            height: 18px;
        }
    }

    .ant-drawer-content-wrapper{
        box-shadow: none !important;
    }

    .ant-drawer-body {
        padding: 0px;
    }

    .map-comp {
        width: 100%;
        position: absolute;
        margin-top: 18px;
        margin-left: 20px;
        z-index: 1;
        height: calc(100vh - 18px);
    }

}
