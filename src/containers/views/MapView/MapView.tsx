import {
  useState,
  useMemo,
  useCallback,
  useEffect,
  useTransition,
  useRef,
} from "react";
import Kpi from "../../../components/configurable/Kpi/Kpi";
import "./style.less";
import MapComponent from "./components/MapComponent";
import { Button, Switch } from "antd";
import Legend from "./components/Legend/Legend";
import MapListDrawer from "./components/MapListDrawer";
import { SearchOutlined } from "@ant-design/icons";
import Drawer from "../../../components/base/Drawer";
import MapDetailsDrawer from "./components/MapDetailsDrawer";
import PageFilter from "../../../components/configurable/PageFilter";
import useUrlDrawerToggle from "./hooks/useUrlDrawerToggle";
import { IconArrayItemType } from "./components/MapComponent/types";
import useMapData from "./hooks/useMapData";
import SegmentedTabs from "../../../components/base/SegmentedTabs";
import { useGlobalContext } from "../../../store/globalStore";

const screenWidth = window.innerWidth;
const DRAWER_WIDTH =
  screenWidth > 1440 ? "25%" : screenWidth > 1280 ? "33%" : "40%";

const MapView = (props: any) => {
  const [enableClustering, setEnableClustering] = useState<boolean>(false);
  const [mapClustering, setMapClustering] = useState<boolean>(false);
  const [isMapPending, startMapTransition] = useTransition();

  const dataUpdatedFromSocketRef = useRef(false);
  const { application_id, enabled_features } = useGlobalContext();

  const [
    {
      isListDrawerOpen,
      isDetailsDrawerOpen,
      entityType,
      entityId,
      currentPath,
      selectedPointer,
    },
    {
      openListDrawer,
      closeListDrawer,
      openDetailsDrawer,
      closeDetailsDrawer,
      closeAllDrawers,
      afterDetailsDrawerClose,
      updateSelectedPointer,
    },
  ] = useUrlDrawerToggle(dataUpdatedFromSocketRef);

  const {
    mapMarkers,
    listItems,
    entityDetails,
    legendConfig,
    kpiData,
    loading,
    entityDetailsLoading,
    filters,
    filterRef,
    dependentFilters,
    setSelectedFilters,
    setSearchText,
    handleKPIClick,
    updateKpiSelection,
    selectedKpiKey,
    setIsFilterLoaded,
  } = useMapData(
    entityId,
    entityType,
    props.selectedMapType,
    dataUpdatedFromSocketRef,
  );

  useEffect(() => {
    if (selectedPointer && entityDetails) {
      const config = entityDetails.entityConfig;
      if (
        config.lat !== selectedPointer.lat ||
        config.long !== selectedPointer.long
      ) {
        updateSelectedPointer({
          lat: config.lat,
          long: config.long,
          entityId: config.id,
          entityType: config.type,
        });
      }
    }
  }, [entityDetails]);

  const isAssetSiteSwitch =
    application_id === 12 ||
    enabled_features.includes("SiteManagement:SiteManagement");

  const onClusterSwitchChange = useCallback((checked: boolean) => {
    setEnableClustering(checked);
    startMapTransition(() => {
      setMapClustering(checked);
    });
  }, []);

  const onSearch = useCallback(
    (searchText: string) => {
      setSearchText(searchText);
    },
    [setSearchText],
  );

  const onEntityClick = useCallback(
    (id: number, entityType: "asset" | "site", lat: number, long: number) => {
      openDetailsDrawer(entityType, id, lat, long);
    },
    [openDetailsDrawer],
  );

  const onFilterChange = useCallback(
    (filters: any) => {
      setSelectedFilters(filters);
      if (filters && Object.keys(filters).length > 0) {
        updateKpiSelection();
      }
    },
    [setSelectedFilters, updateKpiSelection],
  );

  // Define the extended marker type that includes our custom properties
  type ExtendedMarker = IconArrayItemType & {
    entityId?: number;
    entityType?: "asset" | "site";
    name?: string;
  };

  // Memoize the handler function to prevent recreation
  const handleIconClick = useCallback(
    (marker: ExtendedMarker) => {
      // Extract entityId and entityType from marker
      console.log("marker: ", marker);
      const { entityId, entityType, lat, long } = marker;
      if (entityId && entityType) {
        openDetailsDrawer(entityType, entityId, lat, long);
      }
    },
    [openDetailsDrawer],
  );

  const finalMapCompProps = useMemo(
    () => ({
      onClickZoom: 17,
      iconArray: mapMarkers,
      clustering: mapClustering,
      handleIconClick,
      selectedPointer,
      dataUpdatedFromSocket: dataUpdatedFromSocketRef?.current,
      loading: isMapPending || loading,
    }),
    [
      mapMarkers,
      mapClustering,
      selectedPointer,
      handleIconClick,
      dataUpdatedFromSocketRef?.current,
      // isMapPending,
      loading,
    ],
  );

  // Move useMemo to top level - don't call hooks inside JSX
  const memoizedKpiData = useMemo(
    () =>
      kpiData.map((kpi, index) => ({
        id: `kpi-${index}`,
        data: kpi.data,
        config: kpi.config,
        loading: loading,
        selectedKpiKey: selectedKpiKey,
      })),
    [kpiData, loading, selectedKpiKey],
  );

  const onMapTypeChange = useCallback((value: string | number) => {
    props.onMapTypeChange(value);
  }, []);

  return (
    <div id="map-view-new">
      <div className="map-view-header">
        <div className="mvh-left">
          {isAssetSiteSwitch && (
            <SegmentedTabs
              options={[
                { label: "Site", value: "sites" },
                { label: "Asset", value: "assets" },
              ]}
              value={props.selectedMapType}
              onChange={onMapTypeChange}
            />
          )}
          <div className="filter-section">
            {filters?.length > 0 && (
              <PageFilter
                ref={filterRef}
                history={props.history}
                filterData={filters}
                width={DRAWER_WIDTH}
                url={currentPath + props.queryParam}
                resetDependentFields={dependentFilters}
                filterCallback={onFilterChange}
                default={filters ? filters.map(() => "") : []}
                loaderRows={1}
                hideTagContainer={true}
                setIsFilterLoaded={setIsFilterLoaded}
              />
            )}
          </div>
        </div>
        <div className="cluster-switch">
          <span style={{ marginRight: "10px" }}>Clustering</span>
          <Switch
            disabled={isMapPending}
            checked={enableClustering}
            onChange={onClusterSwitchChange}
          />
        </div>
      </div>
      <div className="map-view-content">
        <div className="kpi-container">
          <Kpi kpiData={memoizedKpiData} onKPIClick={handleKPIClick} />
        </div>
        {!isListDrawerOpen && (
          <Button
            className="list-drawer-button"
            shape="circle"
            onClick={openListDrawer}
          >
            <SearchOutlined />
          </Button>
        )}

        <Drawer
          zIndex={900}
          open={isListDrawerOpen}
          width={DRAWER_WIDTH}
          onClose={closeListDrawer}
        >
          <MapListDrawer
            onListDrawerClose={closeListDrawer}
            onSearch={onSearch}
            onEntityClick={onEntityClick}
            items={listItems}
            loading={loading}
          />
        </Drawer>

        <Drawer
          zIndex={910}
          open={isDetailsDrawerOpen}
          width={DRAWER_WIDTH}
          onClose={closeDetailsDrawer}
          afterOpenChange={afterDetailsDrawerClose}
        >
          {isDetailsDrawerOpen && (
            <MapDetailsDrawer
              onClose={closeAllDrawers}
              loading={entityDetailsLoading}
              onBackClick={closeDetailsDrawer}
              {...entityDetails}
            />
          )}
        </Drawer>
        <MapComponent {...finalMapCompProps} />
      </div>
      <div className="legend-section">
        <Legend {...legendConfig} loading={loading} />
      </div>
    </div>
  );
};

export default MapView;
