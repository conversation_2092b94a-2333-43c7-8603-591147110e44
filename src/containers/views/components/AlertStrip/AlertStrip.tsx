import React, { useRef, useMemo } from 'react'
import useVisibleItems from '../../../../utils/useVisibleItems'
import ShowMorePopover from '../../../../components/base/ShowMorePopover'
import { type AlertStripProps } from '../types'
import './style.less'

const AlertStrip = ({ alerts, icon, moreItemsWidth = 135, title = '' }: AlertStripProps) => {
  // Ref for the container to measure available space
  const alertContainerRef = useRef<HTMLDivElement>(null);

  // Use the visibility hook to determine which items can be shown
  const { visibleItems, hiddenItemsCount } = useVisibleItems<string, HTMLDivElement>(
    alerts,
    alertContainerRef,
    {
      moreItemsWidth,
    }
  );

  // Include the first hidden item in visible display
  const displayItems = useMemo(() => {
    if (hiddenItemsCount > 0) {
      const firstHiddenItem = alerts[alerts.length - hiddenItemsCount];
      return [...visibleItems, firstHiddenItem];
    }
    return visibleItems;
  }, [visibleItems, alerts, hiddenItemsCount]);

  if (!alerts.length) return null;

  return (
    <div className="views-alert-strip-section">
      {icon && <img className='title-icon' src={icon} alt="alert-icon" />}
      <div className="alert-strip-items" ref={alertContainerRef}>
        <div className="visible-items">
          {displayItems.join(', ')}
        </div>
        {hiddenItemsCount > 0 && (
          <ShowMorePopover
            items={alerts}
            text={`+${hiddenItemsCount} More ${title}`}
          />
        )}
      </div>
    </div>
  )
}

export default AlertStrip