/**
 * Common types for views/components
 */

// ActionButtons component types
export interface ActionButtonsProps {
  /** Entity ID to perform actions on */
  entityId: number;
  /** Whether to show view button */
  showView?: boolean;
  /** Whether to show edit button */
  showEdit?: boolean;
  /** Whether to show delete button */
  showDelete?: boolean;
  /** Callback when view button is clicked */
  onView?: (id: number) => void;
  /** Callback when edit button is clicked */
  onEdit?: (id: number) => void;
  /** Callback when delete button is clicked */
  onDelete?: (id: number) => void;
}

// AlertStrip component types
export interface AlertStripProps {
  alerts: string[];
  icon?: string;
  moreItemsWidth?: number;
  title?: string;   // to be shown as +x more {issues/faults/alerts}
}

// Asset information types (needed for PanelTopHeader)
export interface AssetInfo {
  title: string;
  value: string | number;
}

// QuickLink types (needed for PanelHeaderExtended)
export interface QuickLink {
  key: string;
  title?: string;
  url?: string;
  icon?: string;
}

// Critical status types (needed for PanelHeaderExtended)
export interface CriticalStatus {
  title: string;
  icon: string;
}

// Location data (needed for PanelHeaderExtended)
export interface EntityData {
  lat?: number;
  long?: number;
  id?: string | number;
  type?: string;
  categoryId?: number;
  clientId?: number;
}

// Entity configuration types (needed for PanelTopHeader)
export interface EntityConfig {
  id?: number;
  categoryId?: number;
  clientId?: number;
  name?: string;
  entityType?: string;
  lastDataReceivedTime?: string;
  assetCount?: number;
  showDeviceOfflineTag?: boolean;
  statusIconConfig?: any;
  dynamicComponent?: DynamicComponentConfig;
  siteInfo?: any;
  status?: string;
  assetInfo?: AssetInfo[];
  lat?: number;
  long?: number;
}

// Extended info (needed for PanelTopHeader)
export interface ExtendedInfo {
  quickLinks?: QuickLink[];
  criticalStatus?: CriticalStatus[];
  alerts?: string[];
  deviceIssues?: string[];
}

// PanelTopHeader component types
export interface PanelTopHeaderProps {
  entityConfig: EntityConfig;
  onClose?: () => void;
  onBackClick?: () => void;
  extendedInfo?: ExtendedInfo;
  showExtendedInfo?: boolean;
  styleKey?: string;
}

// PanelHeaderExtended component types
export interface PanelHeaderExtendedProps {
  quickLinks?: QuickLink[];
  alerts?: string[];
  criticalStatus?: CriticalStatus[];
  deviceIssues?: string[];
  entityData?: EntityData;
}

// EntityBasicInfo component types
export interface EntityBasicInfoProps {
  name: string;
  secondaryText?: string;
  entityType?: string;
  lastDataReceivedTime?: string;
  assetCount?: number;
  showDeviceOfflineTag?: boolean;
  className?: string;
}

// Dynamic Component types
export interface DynamicComponentConfig {
  key: string;
  config: any;
}
