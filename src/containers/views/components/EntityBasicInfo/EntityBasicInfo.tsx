import React from "react";
import { Typography, Tag, Tooltip } from "antd";
import { type EntityBasicInfoProps } from "../types";
import "./style.less";

const { Text } = Typography;

/** Background color for tags */
const tagBgColor = "#F2F2F2";

const EntityBasicInfo: React.FC<EntityBasicInfoProps> = ({
  name,
  secondaryText,
  entityType,
  lastDataReceivedTime,
  assetCount,
  showDeviceOfflineTag = false,
  className = "",
}) => {
  // Calculate secondary text only if not directly provided
  const calculatedSecondaryText = (() => {
    // If direct secondary text is provided, use that
    if (secondaryText) return secondaryText;

    // For assets: show lastDataReceivedTime or "No Data Received"
    const displayDateText =
      entityType === "asset"
        ? lastDataReceivedTime !== "NA"
          ? lastDataReceivedTime
          : "No Data Received"
        : null;

    // For sites: show asset count
    return (
      displayDateText ??
      (assetCount ? `${assetCount} Assets` : "")  // This condition can be changed to assetCount !== undefined
    );
  })();

  return (
    <div className={`view-entity-basic-info ${className}`}>
      <Tooltip title={name}>
        <div className="entity-title">{name}</div>
      </Tooltip>

      <div className="entity-secondary-row">
        {calculatedSecondaryText && (
          <Text type="secondary" className="entity-secondary-text">
            {calculatedSecondaryText}
          </Text>
        )}

        {showDeviceOfflineTag && (
          <Tag className="offline-tag" color={tagBgColor}>
            Offline
          </Tag>
        )}
      </div>
    </div>
  );
};

export default EntityBasicInfo;
