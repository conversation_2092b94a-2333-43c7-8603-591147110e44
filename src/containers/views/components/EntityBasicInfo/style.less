@tag-font-color: #4f4f4f;

// Font size variables with semantic naming: @{element}-{size}
// Title font sizes
@title-compact: 13px;
@title-normal: 14px;
@title-spacious: 14px;

// Secondary text font sizes
@secondary-compact: 11px;
@secondary-normal: 12px;
@secondary-spacious: 13px;

// Tag font sizes
@tag-compact: 10px;
@tag-normal: 11px;
@tag-spacious: 12px;

.view-entity-basic-info {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 0;

  // Default styling (normal size)
  .entity-title {
    font-size: @title-normal;
    line-height: 1.3;
    margin-bottom: 4px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .entity-secondary-row {
    display: flex;
    align-items: center;
    height: 17px;
    
    .entity-secondary-text {
      font-size: @secondary-normal;
      margin-right: 8px;
      color: #808080;
      line-height: normal;
    }

    .offline-tag.ant-tag {
      line-height: 1;
      padding: 2px 6px;
      font-size: @tag-normal;
      margin: 0;
      color: rgba(0, 0, 0, 0.65);
    }
  }
  
  // Size variants - apply different font sizes based on class
  &.compact {
    .entity-title { font-size: @title-compact; }
    .entity-secondary-text { font-size: @secondary-compact; }
    .offline-tag.ant-tag { font-size: @tag-compact; }
  }
  
  &.spacious {
    .entity-title { font-size: @title-spacious; }
    .entity-secondary-text { font-size: @secondary-spacious; }
    .offline-tag.ant-tag { font-size: @tag-spacious; }
  }
}

