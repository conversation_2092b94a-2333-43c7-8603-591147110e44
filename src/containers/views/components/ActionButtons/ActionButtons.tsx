/**
 * ActionButtons component provides common actions for entities in the list drawer
 */
import React from 'react';
import { Button, Space, Tooltip } from 'antd';
import { EyeOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { type ActionButtonsProps } from '../types';
import './style.less';

/**
 * Renders action buttons for entities in the list drawer
 */
const ActionButtons: React.FC<ActionButtonsProps> = ({
  entityId,
  showView = true,
  showEdit = true,
  showDelete = true,
  onView,
  onEdit,
  onDelete
}) => {
  // Handle button clicks without propagating to parent
  const handleButtonClick = (
    callback?: (id: number) => void,
    e?: React.MouseEvent
  ) => {
    e?.stopPropagation();
    if (callback) callback(entityId);
  };

  return (
    <Space className="action-buttons" onClick={(e) => e.stopPropagation()}>
      {showView && (
        <Tooltip title="View">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={(e) => handleButtonClick(onView, e)}
          />
        </Tooltip>
      )}
      
      {showEdit && (
        <Tooltip title="Edit">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={(e) => handleButtonClick(onEdit, e)}
          />
        </Tooltip>
      )}
      
      {showDelete && (
        <Tooltip title="Delete">
          <Button
            type="text"
            icon={<DeleteOutlined />}
            onClick={(e) => handleButtonClick(onDelete, e)}
            danger
          />
        </Tooltip>
      )}
    </Space>
  );
};

export default ActionButtons; 