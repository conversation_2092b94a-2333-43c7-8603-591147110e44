import React from "react";
import { triggerCommand } from "@datoms/js-sdk";
import ControlButton from "@datoms/react-components/src/components/ControlButton";
import _find from "lodash/find";
import "./style.less";
import { useGlobalContext } from "../../../../store/globalStore";

interface LockCommand {
  onCommand: string;
  offCommand: string;
}

interface AssetCategoryInfo {
  id?: number;
  name?: string;
  operation_mode?: string;
  lock_command?: LockCommand;
  switch_command?: LockCommand;
  show_lock?: boolean;
}

interface RemoteControlProps {
  category_id: string | number;
  thingId: string;
  dgStatus: string;
  deviceStatus?: string;
  assetCategoryInfo?: AssetCategoryInfo;
  commandStatus: any;
  dg_lock_status: string;
  operation_mode?: string;
  isControlEnabled?: boolean;
  isLockControlEnabled?: boolean;
  socket: any;
}

const RemoteControl = (props: RemoteControlProps) => {
  const context = useGlobalContext();

  const remoteLockAccess = context.getRemoteLockAccess();
  const remoteAccess = context.getRemoteAccess();

  console.log("context: ", context);
  let array = [];
  const thingInfo = props.assetCategoryInfo;
  const operation_mode = thingInfo?.operation_mode || props.operation_mode;
  const thingCatName = thingInfo ? thingInfo.name : "DG Set";
  const lockCommand: LockCommand = thingInfo?.lock_command || {onCommand: "dg_lock", offCommand: "dg_unlock"};
  const switchCommand: LockCommand = thingInfo?.switch_command || {onCommand: "dg_start", offCommand: "dg_stop"};
  const isLockControlEnabled = thingInfo?.show_lock || props.isLockControlEnabled;

  if (remoteLockAccess && isLockControlEnabled) {
    array.push(
      <ControlButton
        dgStatus={props.dgStatus}
        socket={props.socket}
        client_id={context.client_id}
        application_id={context.application_id}
        thing_id={props.thingId}
        thingCommandStatus={props.commandStatus}
        isControlEnabled={props.isLockControlEnabled}
        operation_mode={operation_mode}
        getViewAccess={remoteLockAccess}
        {...lockCommand}
        subTitle={
          <p>
            Please enter your password to{" "}
            <span>{props.dg_lock_status === "1" ? "unlock" : "lock"}</span> the{" "}
            {thingCatName}.
          </p>
        }
        triggerCommand={triggerCommand}
        assetType={thingCatName}
        dgLockStatus={props.dg_lock_status}
        isRentalDG={true}
        rent_status={"ongoing"}
        featureLockUnlock={true}
        isOnlyLock={true}
      />,
    );
  }

  if (remoteAccess) {
    array.push(
      <ControlButton
        dgStatus={props.dgStatus}
        socket={props.socket}
        client_id={context.client_id}
        application_id={context.application_id}
        thing_id={props.thingId}
        thingCommandStatus={props.commandStatus}
        isControlEnabled={props.isControlEnabled}
        getViewAccess={remoteAccess}
        {...switchCommand}
        modalTitle={
          props.dgStatus === "1"
            ? `${thingCatName} Switch Off`
            : `${thingCatName} Switch On`
        }
        subTitle={
          <p>
            Please enter your password to{" "}
            <span>{props.dgStatus === "1" ? "switch off" : "switch on"} </span>
            the {thingCatName}.
          </p>
        }
        enableTooltip={true}
        enableSideText={false}
        triggerCommand={triggerCommand}
        assetType={thingCatName}
      />,
    );
  } else {
    array.push(
      <div
        className={
          "no-icon-switch " +
          (props.dgStatus === "2"
            ? "offline"
            : props.dgStatus === "1"
              ? "online"
              : "switch-off")
        }
      >
        {props.dgStatus === "2"
          ? props.deviceStatus === "online"
            ? "Disconnected"
            : "Offline"
          : props.dgStatus === "1"
            ? "Running"
            : "Stopped"}
      </div>,
    );
  }
  return <div className="remote-control-composite">{array}</div>;
}

export default RemoteControl;
