.flex-middle() {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex-center(@direction: row) {
    display: flex;
    flex-direction: @direction;
    align-items: center;
    justify-content: center;
}

.flex-between() {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.flex-start() {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.dg-panel-body {
    height: 100%;
    padding: 16px;
    display: flex;
    flex-direction: column;
    color: #4f4f4f;
    gap: 16px;

    // Top Section - Runhour Display
    .runhour-section {
      background-color: #fff;
      display: flex;
      align-items: center;
      gap: 16px;

      .runhour-title {
        font-size: 13px;
        font-weight: 600;
        color: #232323;
      }

      .runhour-values {
        .flex-middle();
        gap: 16px;
        font-size: 13px;
        color: #666;
        padding-left: 20px;

        .lifetime, .todays {
          .flex-middle();
          gap: 8px;

          strong {
            font-size: 13px;
            font-weight: 700;
            color: #232323;
          }
        }

        .separator {
          font-size: 18px;
          color: #ccc;
          font-weight: 300;
        }
      }
    }

    // Middle Section - Fuel/DEF + Additional Info
    .middle-section {
      display: flex;
      gap: 16px;
      align-items: flex-start;
      
      .middle-section-item {
          border-radius: 6px;
          background-color: #f6f6f6;
          height: 78px;
      }
      // Left side - Fuel and DEF
      .fuel-def-section {
        padding: 12px 14px;
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        gap: 12px;
        min-width: 140px;


        .fuel-level, .def-level {
          .flex-start();
          gap: 12px;
          cursor: pointer;

          img {
            width: 18px;
            height: 18px;
            flex-shrink: 0;
          }

          span {
            font-size: 14px;
            font-weight: 600;
            color: #232323;
          }
        }

        .fuel-level {
          img {
            filter: hue-rotate(20deg) saturate(1.2);
          }
        }

        .def-level {
          img {
            filter: hue-rotate(200deg) saturate(1.1);
          }
        }
      }

      // Right side - Additional Information
      .additional-info {
        flex: 1;
        padding: 8px;
        display: flex;
        flex-direction: column;
        gap: 6px;

        .info-item {
          display: flex;
          gap: 4px;

          .info-label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
          }

          .info-value {
            font-size: 14px;
            font-weight: 700;
            color: #232323;

            .info-time {
              font-size: 9px;
              color: #999;
              font-weight: 400;
              margin-top: -2px;
            }
          }
        }
      }
    }

    // Bottom Section - Real-time Parameters
    .realtime-section {
      background-color: #fff;
      border-radius: 12px;
      display: flex;
      flex-wrap: wrap;
      gap: 24px;
      justify-content: space-between;

      .rt-param {
        .flex-start();
        gap: 8px;
        cursor: pointer;
        min-width: 100px;

        img {
          width: 20px;
          height: 20px;
          flex-shrink: 0;
          opacity: 0.8;
        }

        .rt-value {
          font-size: 14px;
          font-weight: 600;
          color: #232323;
        }
      }
    }

    // Legacy styles for backward compatibility
    .fv {
      background-color: #fff;
      .flex-between();
      width: 100%;
      padding: 8px 16px;
      border-radius: 30px;

      .fl {
        .flex-start();
        font-size: 12px;

        img {
          width: 20px;
          height: 20px;
          margin-right: 10px;
        }

        b {
          margin-left: 10px;
          font-size: 14px;
          color: #232323;
        }
      }

      .fc {
        font-size: 12px;
      }
    }

    .pv {
      background-color: #fbfcff;
      padding: 10px 30px;
      position: absolute;
      bottom: 1px;
      left: 11px;
      width: calc(100% - 22px);
      top: 50%;
      margin-top: 8px;
      border-radius: 15px;

      .cv {
        .flex-between();

        .sub-container {
          display: flex;
          font-size: 13px;

          .name {
            margin-right: 5px;
          }

          .value {
            font-weight: 600;
            color: #232323;
          }
        }
      }

      .rtv {
        display: flex;
        margin-top: 8px;
        flex-wrap: wrap;

        .sub-container {
          .flex-start();
          font-weight: 600;
          color: #232323;
          width: 33.33%;
          margin: 5px 0;

          img {
            margin-right: 5px;
          }
        }
      }
    }

    .without-fuel-pv {
      top: 30%;
    }

    // Responsive adjustments
    @media (max-width: 768px) {
      .middle-section {
        flex-direction: column;
      }

      .realtime-section {
        .rt-param {
          min-width: 45%;
        }
      }

      .runhour-values {
        .flex-center(column);
        gap: 8px !important;

        .separator {
          display: none;
        }
      }
    }
  }
  