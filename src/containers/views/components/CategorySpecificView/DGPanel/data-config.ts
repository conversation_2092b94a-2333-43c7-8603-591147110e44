export const dummyParamData = {
  assetDetails: [
    {
      key: "capacity",
      value: "500" // 500L tank capacity
    }
  ],
  summaryParams: [
    {
      key: "calculated_runhour",
      duration: "today",
      aggregationPeriod: 86400,
      paramAttributes: ["sum"],
      value: 123360, // 34:16 in seconds (34 hours 16 minutes)
      unit: "s"
    }
  ],
  additionalRealTimeParams: [
    {
      key: "rnhr",
      name: "Lifetime Runhour",
      value: 5.3 // 48:12 in seconds (48 hours 12 minutes)
    },
    {
      key: "estimated_runhour",
      name: "Estimated Runhour",
      unit: "Hrs",
      value: "48:12"
    },
    {
      key: "fuel_raw",
      dataSource: "unprocessed",
      value: 67.23 // Raw fuel percentage
    },
    {
      key: "fuel_lt",
      dataSource: "unprocessed",
      value: 258.63 // Current fuel in litres (unprocessed)
    },
    {
      key: "fuel",
      dataSource: "processed",
      value: 65.45 // Processed fuel percentage
    },
    {
      key: "fuel_litre",
      dataSource: "processed",
      value: 259.18 // Current fuel in litres (processed)
    },
    {
      key: "def_aft_t1",
      value: 80.50 // DEF level percentage
    }
  ],
  realTimeParams: [
    {
      key: "vbat",
      unit: "V",
      name: "Battery Voltage",
      value: 118.70,
      tooltipDetails: "Generator battery voltage"
    },
    {
      key: "temp",
      unit: "°C",
      name: "Coolant  Temperature",
      value: 70.7,
      tooltipDetails: "Engine coolant temperature"
    },
    {
      key: "press",
      unit: "PSI",
      name: "Lube Oil Pressure",
      value: 250,
      tooltipDetails: "Engine oil pressure"
    },
    {
      key: "time_to_fia",
      unit: "min",
      name: "Time to FIA",
      value: 5,
      tooltipDetails: "Time to forced induction activation"
    }
  ],
  eventParams: [
    {
      key: "Fuel Filled",
      unit: "L",
      name: "Last Fuel Filled",
      value: 1365.15,
      time: "25 Jun 2025, 16:05"
    }
  ]
};


// asset_details: {
//   lifetime_runhour,
//   capacity,
// }