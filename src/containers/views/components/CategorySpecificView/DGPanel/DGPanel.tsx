import "./style.less";
import _find from "lodash/find";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import SkeletonLoader from "@datoms/react-components/src/components/SkeletonLoader";
import FuelIcon from "../../../assets/fuel-icon.svg";
import DefIcon from "../../../assets/def-icon.svg";
import BatteryIcon from "../../../assets/battery-voltage.svg";
import PressureIcon from "../../../assets/pressure.svg";
import TemperatureIcon from "../../../assets/temperature.svg";
import InducementMinuteImage from "../../../assets/inducement-minute.svg";
import React from "react";
import { dummyParamData } from "./data-config";

type AssetDetail = {
  key: string;
  value?: string | number;
};

type SummaryParam = {
  key: string;
  duration: string;
  aggregationPeriod: number;
  paramAttributes: string[];
  value?: string | number;
  unit?: string;
};

type AdditionalRealTimeParam = {
  key: string;
  value?: string | number;
};

type RealTimeParam = {
  key: string;
  unit: string;
  name: string;
  value?: string | number;
  tooltipDetails?: string;
};

type EventParam = {
  key: string;
  unit: string;
  name: string;
  value?: string | number;
  event_time?: number;
  data?: {
    fuel_filled?: string | number;
  };
};

type ParamData = {
  assetDetails: AssetDetail[];
  summaryParams: SummaryParam[];
  additionalRealTimeParams: AdditionalRealTimeParam[];
  realTimeParams: RealTimeParam[];
  eventParams: EventParam[];
};

type DGPanelBodyProps = {
  paramData?: ParamData;
  data_loading: boolean;
};

// Helper function to find parameter by key
const findParamByKey = (params: any[], key: string, objKey: string = "value") => {
  // here objKey is the key in the param object (e.g. value, time, unit etc.)
  return _find(params, { key })?.[objKey];
};

const isNumeric = (value: unknown) => {
  if (value === null || value === '') return false;
  return !isNaN(Number(value));
};

// Helper function to format fuel level
const formatValue = (processedValue: number|string, unprocessedValue: number|string, decimals: number) => {
  const value = unprocessedValue !== "NA" ? unprocessedValue : processedValue;
  if (isNumeric(value)) {
    return Number(value).toFixed(decimals);
  }
  return "NA";
};

const formatFuelLevel = (fuelLevelPercentage: number|string, fuelLevelLitre: number|string, decimals: number) => {
  if (isNumeric(fuelLevelPercentage) && isNumeric(fuelLevelLitre)) {
    return `${Number(fuelLevelLitre).toFixed(decimals)} L (${Number(fuelLevelPercentage).toFixed(decimals)}%)`;
  }
  return "NA";
};

// Helper function to format DEF value
const formatDefValue = (defParamValue: number|string, decimals: number) => {
  if (!defParamValue) return undefined;
  
  if (isNumeric(defParamValue)) {
    return Number(defParamValue).toFixed(decimals) + " %";
  }
  
  return "NA";
};

// Helper function to format runhour display
const formatRunhour = (value: number|string, unit: string = "seconds") => {
  if (!value || isNaN(Number(value))) return "NA";

  if (typeof value === "string") {
    value = Number(value);
  }
  
  let hours: number;
  let minutes: number;

  if (unit === "seconds") {
    hours = Math.floor(value / 3600);
    minutes = Math.floor((value % 3600) / 60);
  } else if (unit === "minutes") {
    hours = Math.floor(value / 60);
    minutes = value % 60;
  } else if (unit === "hours") {
    hours = Math.floor(value);
    minutes = Math.floor((value*60) % 60);
  } else {
    return "NA";
  }
  
  return (hours < 10 ? "0" + hours : hours) + ":" + (minutes < 10 ? "0" + minutes : minutes);
};

// Helper function to get icon for parameter
const getParamIcon = (key: string) => {
  switch (key) {
    case "vbat":
      return BatteryIcon;
    case "press":
      return PressureIcon;
    case "temp":
      return TemperatureIcon;
    case "time_to_fia":
      return InducementMinuteImage;
    default:
      return "";
  }
};

export default function DGPanel(props: DGPanelBodyProps) {
  const { paramData, data_loading } = props;
  
  // Use dummy data if no real data is provided
  const currentData = paramData ? paramData : dummyParamData;
  
  let fc: string = "NA",
      flf: string = "NA",
      fli: string = "NA",
      dlf: string = "NA",
      dli: string = "NA";

  const capacityParam = findParamByKey(currentData?.assetDetails, "capacity");
  fc = isNumeric(capacityParam) ? capacityParam.toString() + " L" : "NA";

  // Check if fuel parameters exist
  const findFuel = _find(currentData?.additionalRealTimeParams, function (o: any) {
    return ["fuel", "fuel_raw", "fuel_litre", "fuel_lt"].includes(o.key);
  });

  // TODO: Add customer specific offline value condition and other mechanical/fuel_only conditions

  if (currentData?.additionalRealTimeParams?.length) {
    const fuelParamUnprocessed = findParamByKey(currentData.additionalRealTimeParams, "fuel_raw");
    const fuelParamProcessed = findParamByKey(currentData.additionalRealTimeParams, "fuel");
    const fuelLitreParamUnprocessed = findParamByKey(currentData?.additionalRealTimeParams, "fuel_lt");
    const fuelLitreParamProcessed = findParamByKey(currentData?.additionalRealTimeParams, "fuel_litre");
    
    let fuelLevelPercentage = formatValue(fuelParamProcessed, fuelParamUnprocessed, 2);
    let fuelLevelLitre = formatValue(fuelLitreParamProcessed, fuelLitreParamUnprocessed, 2);

    // TODO: Add customer specific offline value condition here (props.data.on_off_moving_status == 2 && props.data.customerSpecificOfflineValue)
    flf = formatFuelLevel(fuelLevelPercentage, fuelLevelLitre, 2);
    fli = formatFuelLevel(fuelLevelPercentage, fuelLevelLitre, 0);
    
    const defParam = findParamByKey(currentData.additionalRealTimeParams, "def_aft_t1");
    dlf = formatDefValue(defParam, 2);
    dli = formatDefValue(defParam, 0);
  }

  // Get runhour data
  const lifetimeRunhourParam = findParamByKey(currentData?.additionalRealTimeParams, "rnhr");
  const todaysRunhourParam = findParamByKey(currentData?.summaryParams, "calculated_runhour");
  
  const lifetimeRunhour = formatRunhour(lifetimeRunhourParam, "hours");
  const todaysRunhour = formatRunhour(todaysRunhourParam, "seconds");

  // Get estimated runhour
  const estimatedRunhour = findParamByKey(currentData?.additionalRealTimeParams, "estimated_runhour");

  // Get last fuel filled data
  const lastFuelFilled = findParamByKey(currentData?.eventParams, "Fuel Filled");
  const lastFuelFilledTime = findParamByKey(currentData?.eventParams, "Fuel Filled", "time");

  // Build real-time parameters
  const realTimeValues: React.JSX.Element[] = [];
  if (currentData?.realTimeParams?.length) {
    currentData.realTimeParams.forEach((paramObj) => {
      if (paramObj.value !== undefined && paramObj.value !== null) {
        realTimeValues.push(
          <AntTooltip
            key={paramObj.key}
            placement="topLeft"
            title={paramObj.tooltipDetails || paramObj.name}
          >
            <div className="rt-param">
              <img src={getParamIcon(paramObj.key)} alt={paramObj.name} />
              <div className="rt-value">
                {paramObj.value !== "NA" ? `${paramObj.value} ${paramObj.unit}` : "NA"}
              </div>
            </div>
          </AntTooltip>
        );
      }
    });
  }

  return data_loading ? (
    <SkeletonLoader />
  ) : (
    <div className="dg-panel-body">
      <div className="runhour-section">
        <div className="runhour-title">Runhour (HH:MM)</div>
        <span className="separator">|</span>
        <div className="runhour-values">
          <span className="lifetime">Lifetime <strong>{lifetimeRunhour}</strong></span>
          <span className="todays">Today's <strong>{todaysRunhour}</strong></span>
        </div>
      </div>

      {/* Middle Section */}
      {findFuel && (
        <div className="middle-section">
          
          <div className="fuel-def-section middle-section-item">
            <AntTooltip
              title={
                <div>
                  <div>{`Fuel Level: ${flf}`}</div>
                  <div>{`Tank Capacity: ${fc}`}</div>
                </div>
              }
            >
              <div className="fuel-level">
                <img src={FuelIcon} alt="Fuel" />
                <span>{fli}</span>
              </div>
            </AntTooltip>
            
            {dli && (
              <AntTooltip title={`DEF: ${dlf}`}>
                <div className="def-level">
                  <img src={DefIcon} alt="DEF" />
                  <span>{dli}</span>
                </div>
              </AntTooltip>
            )}
          </div>
          <div className="additional-info middle-section-item">
            <div className="info-item">
              <span className="info-label">Estimated Runhour:</span>
              <span className="info-value">{estimatedRunhour}</span>
            </div>
            
            {lastFuelFilled !== "NA" && (
              <div className="info-item">
                <span className="info-label">Last Fuel Filled:</span>
                <span className="info-value">
                  {lastFuelFilled}
                  {lastFuelFilledTime && (
                    <div className="info-time">{lastFuelFilledTime}</div>
                  )}
                </span>
              </div>
            )}
          </div>
        
      </div>)}

      {/* Bottom Section - Real-time Parameters */}
      {realTimeValues.length > 0 && (
        <div className="realtime-section">
          {realTimeValues}
        </div>
      )}
    </div>
  );
}
