import React, { useMemo } from 'react';
import { type QuickLink, type CriticalStatus, type PanelHeaderExtendedProps } from '../types';
import AlertStrip from "../AlertStrip";
import { DeviceIssueIcon, NotificationIcon, CheckIcon } from "../../utils/icon-library";
import ShowMorePopover from "../../../../components/base/ShowMorePopover";
import "./style.less";

// Import the utils from the local directory
import { processQuickLinks } from "./utils/quick-links";
import { useGlobalContext } from '../../../../store/globalStore';
import { getBaseUrl } from '@datoms/js-utils/src/base-url-logic';
import { Link } from 'react-router-dom';

// Component for rendering individual quick link items
const QuickLinkItem: React.FC<{ link: QuickLink }> = ({ link }) => {
  // Check if the URL is external (starts with http:// or https://)
  const isExternalUrl = link.url && (link.url.startsWith('http://') || link.url.startsWith('https://'));
  
  const linkContent = (
    <>
      {link.icon && (
        <span className={`quick-link-icon ${link.key}-icon`}>
          <img src={link.icon} alt={link.title || ""} />
        </span>
      )}
      {link.title && <span className="quick-link-title">{link.title}</span>}
    </>
  );

  // Render external link as anchor tag
  if (isExternalUrl) {
    return (
      <a
        href={link.url}
        className="quick-link-item"
        target="_blank"
        rel="noopener noreferrer"
      >
        {linkContent}
      </a>
    );
  }

  // Render internal link as React Router Link
  return (
    <Link
      to={ link.url || "" }
      className="quick-link-item"
    >
      {linkContent}
    </Link>
  );
};

const NoAlerts = () => {
  return (
    <div className="alerts-container">
      <img className="title-icon" src={CheckIcon} alt="No Alerts" />
      <span>No active alerts</span>
    </div>
  );
};

const AlertCountPopover = ({ alerts }: { alerts: string[] }) => {
  return (
    <div className="alerts-container">
      <img className="title-icon" src={NotificationIcon} alt="Alerts" />
      <ShowMorePopover items={alerts} text={`${alerts.length} Alerts`} />
    </div>
  );
};

const CriticalStatusComponent = ({
  criticalStatus,
}: {
  criticalStatus: CriticalStatus[];
}) => {
  return (
    <div className="critical-status-container">
      {criticalStatus.map((alert, index) => (
        <div key={`alert-${index}`} className="critical-status-item">
          <img src={alert.icon} alt={alert.title} />
          <span>{alert.title}</span>
        </div>
      ))}
    </div>
  );
};

const PanelHeaderExtended: React.FC<PanelHeaderExtendedProps> = ({
  quickLinks = [],
  alerts = [],
  criticalStatus = [],
  deviceIssues = [],
  entityData = {},
}) => {
  const context = useGlobalContext();
  const baseUrl = getBaseUrl(context, "");
  // Memoize processed quick links
  const processedQuickLinks = useMemo(
    () => processQuickLinks(quickLinks, entityData, baseUrl, context.application_id),
    [quickLinks, entityData, baseUrl]
  );

  // Process critical status data
  const effectiveCriticalStatus = useMemo(
    () => criticalStatus?.slice(0, 2) || [],
    [criticalStatus]
  );

  // Process alerts
  const ActiveAlerts = useMemo(
    () =>
      alerts?.length > 0 ? (
        effectiveCriticalStatus.length > 0 ? (
          <AlertCountPopover alerts={alerts} />
        ) : (
          <AlertStrip alerts={alerts} icon={NotificationIcon} title="Faults" />
        )
      ) : (
        <NoAlerts />
      ),
    [alerts, effectiveCriticalStatus]
  );

  return (
    <div className="panel-header-extended-info">
      {processedQuickLinks.length > 0 && (
        <div className="quick-links">
          <div className="section-label">Quick Links</div>
          <div className="section-content">
            {processedQuickLinks.map((link) => (
              <QuickLinkItem key={link.key} link={link} />
            ))}
          </div>
        </div>
      )}

      {entityData?.type !== "asset" && <div className="alerts-section">
        {ActiveAlerts}
        {criticalStatus && criticalStatus.length > 0 && (
          <CriticalStatusComponent criticalStatus={effectiveCriticalStatus} />
        )}
      </div>}

      {deviceIssues && deviceIssues.length > 0 && (
        <div className="device-issues-section">
          <AlertStrip
            alerts={deviceIssues}
            icon={DeviceIssueIcon}
            title="Issues"
          />
        </div>
      )}
    </div>
  );
};

export default PanelHeaderExtended;