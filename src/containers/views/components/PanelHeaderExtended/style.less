@blue-color: #374375;
@neutral-text-color: #4f4f4f;
@border: 1px solid #f2f2f2;

.flex-center (@gap: 12px) {
  display: flex;
  align-items: center;
  gap: @gap;
}

.panel-header-extended-info {
  display: flex;
  flex-direction: column;
  font-size: 14px;
  border-top: @border;
  border-bottom: @border;
  background-color: #fff;

  .quick-links {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    height: 42px;
    background-color: rgba(247, 245, 243, 0.45);

    .section-label {
      font-style: italic;
      font-size: 13px;
    }

    .section-content {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;

      .quick-link-item {
        display: flex;
        align-items: center;
        text-decoration: none;
        gap: 8px;

        &:hover {
          cursor: pointer;
        }

        .quick-link-icon {
          margin-right: 6px;
          display: flex;
          align-items: center;

          &.google-map-icon {
            margin-left: 16px;
          }
        }

        .quick-link-title {
          color: @blue-color;
          font-size: 13px;
        }
      }
    }
  }

  .alerts-section,
  .device-issues-section {
    border-top: @border;
    padding: 12px 16px;

    .title-icon {
      height: 20px;
      width: 20px;
    }
  }

  .alerts-section {
    display: flex;
    justify-content: space-between;

    .alerts-container {
      .flex-center();
    }

    .critical-status-container {
      .flex-center();

      .critical-status-item {
        .flex-center(4px);
      }
    }
  }
}
