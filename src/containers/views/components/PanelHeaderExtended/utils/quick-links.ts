import { QuickLink } from "../../../MapView/components/MapDetailsDrawer/types";
import {
  LayoutIcon,
  ClockIcon,
  GoogleMapIcon,
} from "../../../utils/icon-library";

/**
 * Predefined QuickLink configurations that can be used with panel header extended info.
 */
export const QUICK_LINK_CONFIGS: QuickLink[] = [
  {
    key: "detailed-view",
    title: "Detailed View",
    url: "detailed-view?thing_id=:id",
    icon: LayoutIcon,
  },
  {
    key: "analog-view",
    title: "Analog View",
    url: "real-time?thing_id=:id",
    icon: ClockIcon,
  },
  {
    key: "site-view",
    title: "Site View",
    url: "site-view?site_id=:id",
    icon: LayoutIcon,
  },
  {
    key: "google-map",
    icon: GoogleMapIcon,
  },
];

// Helper function to process and prepare quick links
export const processQuickLinks = (
  links: QuickLink[],
  entityData: {
    lat?: number;
    long?: number;
    id?: string | number;
    categoryId?: number;
    clientId?: number;
  },
  baseUrl: string,
  applicationId: number,
): QuickLink[] => {
  if (!links?.length) return [];

  return links
    .map((link) => {
      const predefinedConfig = QUICK_LINK_CONFIGS.find(
        (item) => item.key === link.key,
      );

      // const linkUrl = `${newBaseUrl}${link.url || predefinedConfig?.url}`;
      const linkUrl = link.url || predefinedConfig?.url;

      const finalUrl = processUrl(
        link.key,
        linkUrl,
        entityData,
        applicationId,
        baseUrl,
      );

      // Short circuit if no URL available from either source
      if (!finalUrl) return null;

      // Merge the link with any available predefined properties
      return {
        ...link,
        url: finalUrl,
        title: link.title || predefinedConfig?.title,
        icon: link.icon || predefinedConfig?.icon,
      };
    })
    .filter(Boolean) as QuickLink[];
};

const processUrl = (
  key: string,
  linkUrl: string | undefined,
  entityData: {
    lat?: number;
    long?: number;
    id?: string | number;
    categoryId?: number;
    clientId?: number;
  },
  applicationId: number,
  baseUrl: string,
) => {
  if (key === "google-map") {
    return `https://maps.google.com?q=${entityData?.lat},${entityData?.long}`;
  }

  if (!linkUrl) return undefined;

  let newBaseUrl = baseUrl;
  if ([12, 17].includes(applicationId)) {
    newBaseUrl = `${baseUrl}dg-monitoring/`;
  }

  let modifiedLinkUrl = `${newBaseUrl}${linkUrl}`;
  let urlParts = linkUrl?.split("?");

  if ([12, 17].includes(applicationId) && urlParts.length > 1 && ["detailed-view", "analog-view"].includes(key)) {
    modifiedLinkUrl = `${newBaseUrl}${urlParts[0]}?thing_type=${entityData?.categoryId}&client_id=${entityData?.clientId}&end_iot_views=1&thing_id=:id`;
  }

  let finalUrl: string;
  switch (key) {
    case "detailed-view":
      finalUrl = modifiedLinkUrl.replace(":id", entityData?.id as string);
      break;
    case "analog-view":
      finalUrl = modifiedLinkUrl.replace(":id", entityData?.id as string);
      break;
    case "site-view":
      finalUrl = modifiedLinkUrl.replace(":id", entityData?.id as string);
      break;
    default:
      finalUrl = modifiedLinkUrl;
  }
  return finalUrl;
};
