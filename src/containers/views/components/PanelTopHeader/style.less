@blue-color: #374375;
@neutral-text-color: #4f4f4f;
@border: 1px solid #f2f2f2;

.flex-center (@gap: 12px) {
  display: flex;
  align-items: center;
  gap: @gap;
}

.panel-top-header {
  display: flex;
  flex-direction: column;
  border-bottom: @border;
  background-color: #fff;

  .first-row-item {
    height: 42px;
  }

  &.site-panel {
    .pth-basic-info {
      padding: 0;
    }

    .pth-main-content {
      min-height: 42px;
    }
  }

  .pth-basic-info {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    position: relative;
    overflow: hidden;
    background-color: #f9f6f4;

    .pth-main-content {
        display: flex;
        flex-direction: column;
        overflow: hidden;
        gap: 13px;
        flex: 1;
        min-height: 48px;

      .pth-main-content-top {
        display: flex;

        .pth-status-icon {
          margin-right: 12px;
          flex-shrink: 0;
        }
    
        .pth-middle-part {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
          min-width: 0;
        }
      }

      .pth-main-content-bottom {
        .flex-center();
        width: 100%;
        height: 16px;

        .site-info {
          max-width: 33%;
          height: 17px;

          .ant-tag {
            line-height: 1.3;
            padding: 0 5px 1px;
            font-size: 10px;
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .asset-info {
          .flex-center();
          overflow: hidden;
          flex: 1;

          .asset-info-item {
            font-size: 12px;
            color: #7686A1;
            
            .value {
                font-weight: 500;
            }
          }
        }
      }
    }

    .back-button {
      margin-right: 8px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
    }

    .entity-status {
      font-size: 13px;
      margin-left: 12px;
    }

    .pth-dynamic-components {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-left: 24px;
      flex-shrink: 0;

      .dynamic-component {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 8px;
        font-size: 8px;
        width: 34px;
        min-width: 34px;
      }
    }

    .pth-close-icon {
      display: flex;
      align-items: center;
      height: 20px;
      width: 20px;
      flex-shrink: 0;
      margin-left: 8px;
    }
  }
}
