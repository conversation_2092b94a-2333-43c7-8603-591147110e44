import { useMemo, useRef } from "react";
import "./style.less";
import { Tag, Tooltip } from "antd";
import BackIcon from "../../../../components/base/BackIcon";
import CloseIcon from "../../../../components/base/CloseIcon";
import StatusIcon from "../../../../components/configurable/StatusIcon";
import EntityBasicInfo from "../../components/EntityBasicInfo";
import {
  type PanelTopHeaderProps,
  type AssetInfo
} from "../types";
import ShowMorePopover from "../../../../components/base/ShowMorePopover";
import useVisibleItems from "../../../../utils/useVisibleItems";
import { validData } from "./utils/valid-data";
import PanelHeaderExtended from "../PanelHeaderExtended";
import { renderDynamicComponent } from "../../utils/dynamic-component-config";

const renderAssetInfo = (item: AssetInfo, index: number) => {
  return (
    <span className="asset-info-item" key={`asset-info-${index}`}>
      <span className="title">{item.title}: </span>
      <span className="value">{item.value}</span>
    </span>
  );
};

const PanelTopHeader = ({
  onClose,
  entityConfig = {},
  onBackClick,
  styleKey,
  extendedInfo,
  showExtendedInfo = true,
}: PanelTopHeaderProps) => {
  const validAssetInfo = useMemo(
    () => validData(entityConfig?.assetInfo) || [],
    [entityConfig?.assetInfo],
  );

  const isSiteInfo =
    entityConfig?.entityType === "asset" && entityConfig?.siteInfo?.id !== 0 && styleKey !== "site-panel";
  const isAssetInfo =
    entityConfig?.entityType === "asset" && validAssetInfo.length > 0;

  const displayAssetInfo = useMemo(
    () =>
      isAssetInfo
        ? validAssetInfo.map(
            (item: { title: string; value: string }, index: number) => {
              return renderAssetInfo(item, index);
            },
          )
        : [],
    [validAssetInfo],
  );

  const assetInfoRef = useRef<HTMLDivElement>(null);
  const {
    visibleItems: assetInfoVisibleItems,
    hiddenItemsCount: assetInfoHiddenItemsCount,
  } = useVisibleItems(displayAssetInfo, assetInfoRef, {
    itemsGap: 12,
    moreItemsWidth: 43,
  });

  return (
    <div className={`panel-top-header ${styleKey || ""}`}>
      <div className="pth-basic-info">
        {onBackClick && (
          <div className="back-button first-row-item">
            <BackIcon onBack={onBackClick} />
          </div>
        )}

        <div className="pth-main-content">
          <div className="pth-main-content-top">
            <div className="pth-status-icon first-row-item">
              {entityConfig?.statusIconConfig && (
                <StatusIcon config={entityConfig.statusIconConfig} />
              )}
            </div>

            <div className="pth-middle-part">
              <EntityBasicInfo
                name={entityConfig?.name || ""}
                entityType={entityConfig?.entityType}
                lastDataReceivedTime={entityConfig?.lastDataReceivedTime}
                assetCount={entityConfig?.assetCount}
                showDeviceOfflineTag={entityConfig?.showDeviceOfflineTag}
              />
            </div>

            {/* Dynamic components section */}
            {entityConfig?.dynamicComponent ? (
              <div className="pth-dynamic-component">{renderDynamicComponent(entityConfig?.dynamicComponent)}</div>
            ) : (
              <span
                style={{ color: entityConfig?.statusIconConfig?.statusColor }}
                className="entity-status"
              >
                {entityConfig?.status}
              </span>
            )}
          </div>
          {(isSiteInfo || isAssetInfo) && (
            <div className="pth-main-content-bottom">
              {isSiteInfo && (
                <div className="site-info">
                  <Tooltip title={entityConfig.siteInfo.name}>
                    <Tag>{entityConfig.siteInfo.name}</Tag>
                  </Tooltip>
                </div>
              )}
              {isAssetInfo && (
                <div className="asset-info" ref={assetInfoRef}>
                  {assetInfoVisibleItems}
                  {assetInfoHiddenItemsCount > 0 && (
                    <ShowMorePopover
                      items={validAssetInfo}
                      text=". . ."
                      renderItem={renderAssetInfo}
                    />
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {onClose && (
          <div className="pth-close-icon">
            <CloseIcon onClose={onClose} />
          </div>
        )}
      </div>

      {showExtendedInfo && (
        <PanelHeaderExtended 
          quickLinks={extendedInfo?.quickLinks}
          alerts={extendedInfo?.alerts}
          criticalStatus={extendedInfo?.criticalStatus}
          deviceIssues={extendedInfo?.deviceIssues}
          entityData={{
            lat: entityConfig?.lat,
            long: entityConfig?.long,
            id: entityConfig?.id,
            type: entityConfig?.entityType,
            categoryId: entityConfig?.categoryId,
            clientId: entityConfig?.clientId,
          }}
        />
      )}
    </div>
  );
};

export default PanelTopHeader;
