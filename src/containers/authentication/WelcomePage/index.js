import React from "react";
import "./styles.less";
import AntButton from "@datoms/react-components/src/components/AntButton";
import PLANT_FRONT_VIEW_UNITED_GENESIS from "../../../images/ClientLogo/PLANT_FRONT_VIEW_UNITED_GENESIS.jpg";
import COMPANY_LOGO_UNITED_GENESIS from "../../../images/ClientLogo/COMPANY_LOGO_UNITED_GENESIS.png";
import COMPANY_LOGO_MAHINDRA from "../../../images/ClientLogo/COMPANY_LOGO_MAHINDRA.png";
import COMPANY_LOGO_OJUS from "../../../images/ClientLogo/COMPANY_LOGO_OJUS.png";
import COMPANY_LOGO_LUMEN from "../../../images/ClientLogo/COMPANY_LOGO_LUMEN.png";
import VENDOR_LOGO_UNITED_GENESIS from "../../../images/ClientLogo/VENDOR_LOGO_UNITED_GENESIS.png";
import VENDOR_LOGO_MAHINDRA from "../../../images/ClientLogo/VENDOR_LOGO_MAHINDRA.png";
import VENDOR_LOGO_OJUS from "../../../images/ClientLogo/VENDOR_LOGO_OJUS.png";
import COMPANY_LOGO_GMMCO from "../../../images/ClientLogo/COMPANY_LOGO_GMMCO.png";
import VENDOR_LOGO_GMMCO from "../../../images/ClientLogo/VENDOR_LOGO_GMMCO.png";
import { relatedCustomerIds } from "../../../configs/customer-specific-config";

export default class VendorMiddlePage extends React.Component {
  constructor(props) {
    super(props);
  }

  componentDidMount() {
    setTimeout(() => {
      this.props.disableWelcomePage();
    }, 5000);
  }

  render() {
    let renderWelcome =
    relatedCustomerIds[1121].includes(this.props.vendor_id) ? (
        <div className="welcomet-page without-top-img">
          <div className="div-image">
            <img src={COMPANY_LOGO_OJUS} />
          </div>
          <div className="vendor-logo">
            <img src={VENDOR_LOGO_OJUS} />
          </div>
          <AntButton
            onClick={() => this.props.disableWelcomePage()}
            type="primary"
          >
            Continue
          </AntButton>
          <div className="delay-text">Or wait for 5 Seconds</div>
        </div>
      ) : this.props.vendor_id === 1062 ? (
        <div className="welcomet-page without-top-img gmmco">
          <div className="div-image">
            <img src={COMPANY_LOGO_GMMCO} />
          </div>
          <div className="vendor-logo">
            <img src={VENDOR_LOGO_GMMCO} />
          </div>
          <AntButton
            onClick={() => this.props.disableWelcomePage()}
            type="primary"
          >
            Continue
          </AntButton>
          <div className="delay-text">Or wait for 5 Seconds</div>
        </div>
      ) : this.props.vendor_id === 1140 ? (
        <div className="welcomet-page without-top-img">
          <div className="div-image">
            <img src={COMPANY_LOGO_MAHINDRA} />
          </div>
          <div className="vendor-logo">
            <img src={VENDOR_LOGO_MAHINDRA} />
          </div>
          <AntButton
            onClick={() => this.props.disableWelcomePage()}
            type="primary"
          >
            Continue
          </AntButton>
          <div className="delay-text">Or wait for 5 Seconds</div>
        </div>
      ) : this.props.vendor_id === 10786 ? (
        <div className="welcomet-page without-top-img">
          <div className="div-image">
            <img src={COMPANY_LOGO_LUMEN} />
          </div>
          {/*<div className="vendor-logo">*/}
          {/*	<img src={VENDOR_LOGO_MAHINDRA} />*/}
          {/*</div>*/}
          <AntButton
            onClick={() => this.props.disableWelcomePage()}
            type="primary"
          >
            Continue
          </AntButton>
          <div className="delay-text">Or wait for 5 Seconds</div>
        </div>
      ) : (
        <div className="welcomet-page">
          <div className="div-image">
            <img src={PLANT_FRONT_VIEW_UNITED_GENESIS} />
          </div>
          <div className="top-img">
            <img src={COMPANY_LOGO_UNITED_GENESIS} />
          </div>
          <div className="vendor-logo">
            <img src={VENDOR_LOGO_UNITED_GENESIS} />
          </div>
          <AntButton
            onClick={() => this.props.disableWelcomePage()}
            type="primary"
          >
            Continue
          </AntButton>
          <div className="delay-text">Or wait for 5 Seconds</div>
        </div>
      );
    return renderWelcome;
  }
}
