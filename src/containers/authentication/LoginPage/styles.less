.iti__flag {
	background-image: url("https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/img/flags.png");
}

@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
	.iti__flag {
		background-image: url("https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/img/<EMAIL>");
	}
}

#yellow-login-container {
	background: radial-gradient(#1a86e1, #0f4777);
}

.tata-login-button {
	background: #303499 !important;
	color: #fff !important;
	font-weight: bold !important;
	border-radius: 20px;
	// margin-top: 20px !important;
	//box-shadow: 5px 5px 8px 0px rgba(0,0,0,0.4);
}

.yellow-login-button {
	background: #fff !important;
	color: #0a2052 !important;
	font-weight: bold !important;
	border-radius: 20px;
	// margin-top: 20px !important;
	//box-shadow: 5px 5px 8px 0px rgba(0,0,0,0.4);
}

.white_email_id {
	color: white;
}

.aurassure-login-button {
	background: #e7effd !important;
	border: 1px solid #6d6d6d !important;
	color: #6d6d6d !important;
}

.login-container.ojus {
	background: radial-gradient(#1a86e1, #0f4777);
}

.login-container.aurassure {
	background: radial-gradient(#e0f2ee, #dafcee);
}

.login-container {
	background: linear-gradient(0deg, rgb(245, 135, 64), rgb(240, 99, 82));
	width: 100%;
	height: 100vh;
	overflow: auto;
	display: -ms-flexbox;
	display: flex;
	flex-direction: column;
	-ms-flex-align: center;
	align-items: center;

	.tag-line {
		display: flex;
		align-items: center;
		min-width: 300px;
		max-width: 450px;
		width: 50%;
		padding: 0 15px;
		margin: 20px auto 0;

		p {
			margin-bottom: 0;
			color: white;
		}

		.ojus-heading {
			font-weight: bold;
			font-size: 20px;
			margin-right: 25px;
		}
	}

	.back-img {
		position: absolute;
		height: 100vh;
		width: 100%;
		z-index: 2;
		background-image: url(https://static.datoms.io/images/login-back.png);
		background-size: cover;
	}

	.back-img.tata-back-img {
		background-image: url(	https://static.datoms.io/images/tata-motors-01-min.jpg);
		background-position: left;
		background-size: cover;
		width: 100%;
	}

	.back-img.ojus-back {
		background-image: url(https://static.datoms.io/images/ojus-mobile-login-back.png);
		background-position: center;
		background-size: cover;
		width: 100%;
	}

	.back-img.aurassure-back {
		background-image: url(../../../imgs/aurassure_bg.png);
		background-position: center;
		background-size: cover;
		width: 100%;
	}

	.ojus-img {
		width: 200px;
		z-index: 2;
	}

	.banner-img {
		width: 150px;
		z-index: 2;
	}

	.form {
		.panel {
			.front {
				.form-sec {
					.aurassure-logo {
						width: 100%;
						margin-bottom: 36px;
					}
					.tata-logo {
						margin-bottom: 36px;
					}
				}
				.form-sec.tata {
					text-align: center;
					opacity: 0.8;
				}
			}
		}
	}

	.form.ojus-form {
		.panel {
			.front {
				.form-sec {
					padding: 10px 25px;
					background-color: transparent;

					.form-container {
						.header {
							background-color: transparent;
							color: #fff;
							text-align: center;
						}

						.form-control {
							background: #1a3958;
							border-color: #1a3958;
							border-radius: 20px;
							text-align: center;
							color: #fff;
						}

						.form-control:focus {
							border: 1px solid #1a3958;
							box-shadow: none;
							outline: 0;
							background-color: #1a3958;
						}
					}

					#forgot_pswd a {
						color: #fff !important;
					}
				}
			}
		}
	}

	.form {
		width: 30%;
		max-width: 450px;
		min-width: 300px;
		position: relative;
		right: -60%;
		z-index: 3;

		.panel {
			margin: 0 auto;
			border-radius: 10px;
			position: relative;
			-webkit-perspective: 600px;
			-moz-perspective: 600px;

			.front {
				position: relative;
				top: 0;
				z-index: 900;
				transition: all 0.4s ease-in-out;
				width: 100%;

				.form-sec {
					height: 100%;
					margin: 0 auto;
					padding: 90px 50px;
					background-color: #fff;
					border-radius: 10px;

					.head {
						font-size: 30px;
						text-align: left;
						margin-bottom: 20px;
						font-family: Lora, serif;
					}

					.second-head {
						font-weight: 500;
						margin-bottom: 50px;
						font-size: 12px;
						line-height: 1.5;
						color: grey;
					}

					.form-container {
						margin-bottom: 20px;
						font-size: 14px;

						.header {
							text-align: left;
							margin-bottom: 10px;
							font-size: 13px;

							.email-mobile {
								float: right;
								cursor: pointer;
								text-decoration: underline;
							}
						}

						.form-control {
							display: block;
							width: 100%;
							padding-left: 10px;
							height: 35px;
							font-size: 14px;
							line-height: 1.42857143;
							color: grey;
							background-image: none;
							border: 1px solid grey;
							border-radius: 4px;
							box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
							transition: border-color ease-in-out 0.15s,
								box-shadow ease-in-out 0.15s;
							font-family: Quicksand, sans-serif !important;

							&:focus {
								border: 1px solid #f58740;
								box-shadow: none;
								outline: 0;
								background-color: #fff;
							}
						}

						.check {
							margin-right: 5px;
						}
					}

					#login_btn {
						display: none;
					}

					#password_form {
						display: none;
					}

					#mob_form {
						.intl-tel-input {
							width: 100%;
							border: 1px solid grey;
							border-radius: 4px;
							height: 34px;
						}

						.form-control {
							width: 100%;
						}

						#mobile_email {
							width: 100%;
							padding-left: 10px;
							margin-left: 0px;
							transition: 0.3s all;
						}

						.flag-mobile {
							display: flex;

							#country {
								display: none;
							}

							#mobile_container {
								width: 23%;
								display: none;
							}

							.mobile-code {
								display: flex;
								position: relative;
								width: 100%;

								#country_code {
									display: none;
									position: absolute;
									margin-left: 13px;
									margin-top: 0.5em;
									color: grey;
								}
							}
						}
					}

					#otp_form {
						display: none;

						#resend_timer {
							float: right;
							margin-top: 10px;
							display: none;
						}

						.resend {
							float: right;
							margin-top: 10px;
							cursor: pointer;
							text-decoration: underline;
						}
					}

					#value_form {
						display: none;
						font-size: 14px;
					}

					.next-button {
						text-align: center;
						width: 100% !important;
						background: linear-gradient(315deg, #f58740, #f06352);
						color: #fff;
						border-radius: 20px;
						padding: 10px 0;
						font-size: 13px;
						cursor: pointer;
						margin: 60px 0 -10px;
					}

					#separator {
						font-size: 13px;
						text-align: center;
						border-bottom: 1px solid;
						line-height: 0.1em;
						margin: 50px 0 30px;
						display: none;

						span {
							background: #fff;
							padding: 0 10px;
						}
					}

					#show_pass {
						font-size: 13px;
						margin: 0 auto;
						margin-top: 20px;
						width: fit-content;
						cursor: pointer;
						text-decoration: underline;
						display: none;
					}

					.form-container.mar-top-30 {
						margin-top: 30px;
					}

					.error-msg {
						font-size: 12px;
						margin-top: -10px;
						position: absolute;
						margin-left: 4px;
						color: red;
					}

					/*#login_btn {
						display: none;
					}*/

					#otp_butn {
						display: none;
						margin-top: 40px;
						margin-bottom: 120px;

						#back_butn {
							font-size: 20px;
							font-weight: bold;
							cursor: pointer;
							border-radius: 50%;
							background: #e3e3e3;
							// padding: 0px 10px;
							// padding-top: 8px;
							color: #000;
							transition: 0.3s all;
							width: 38px;
							height: 38px;
							float: left;
							display: grid;
							place-content: center;

							&:hover {
								color: #f06352;
							}
						}

						.send-button {
							text-align: center;
							width: 75%;
							background: linear-gradient(315deg, #f58740, #f06352);
							color: #fff;
							border-radius: 20px;
							padding: 10px 0;
							font-size: 13px;
							cursor: pointer;
							float: right;
						}
					}

					.btn-contain {
						text-align: center;
						margin-top: 50px;
						margin-bottom: 120px;

						#back_butn {
							font-size: 20px;
							font-weight: bold;
							cursor: pointer;
							border-radius: 50%;
							background: #e3e3e3;
							// padding: 0px 10px;
							// padding-top: 10px;
							color: #000;
							transition: 0.3s all;
							width: 38px;
							height: 38px;
							float: left;
							display: grid;
							place-content: center;

							&:hover {
								color: #f06352;
							}
						}

						.btn {
							background: linear-gradient(315deg,
									#f58740,
									#f06352);
							color: #fff;
							border-radius: 20px;
							border: none;
							box-shadow: none;
							width: 70%;
							padding: 10px;
							-webkit-font-feature-settings: 'pcap', 'c2pc';
							font-feature-settings: 'pcap', 'c2pc';
							font-variant: all-petite-caps;
							letter-spacing: 2px;
							cursor: pointer;
							font-size: 16px;
							float: right;

							&:focus {
								box-shadow: none;
								outline: 0;
							}

							&:disabled {
								cursor: not-allowed;
								opacity: 0.75;
							}
						}
					}

					#forgot_pswd {
						cursor: pointer;
						margin-top: 20px;
						text-align: center;
						font-size: 14px;

						a {
							text-decoration: none;
							color: #c4c2c2;
							transition: all 0.5s;

							&:hover {
								color: #f06352;
							}
						}
					}

					#back_login {
						cursor: pointer;
						margin-top: 20px;
						text-align: center;
						font-size: 14px;

						a {
							text-decoration: none;
							color: #c4c2c2;
							transition: all 0.5s;

							&:hover {
								color: #f06352;
							}
						}
					}
				}
			}
		}
	}

	.datoms-logo {
		position: absolute;
		bottom: 30px;
		right: 30px;
		width: 15%;
		opacity: 0.5;

		img {
			width: 100%;
		}
	}

	.copyright {
		position: absolute;
		bottom: 10px;
		color: #fff;
		opacity: 0.5;
	}

	.app-version-login {
		padding-bottom: 10px;
		position: absolute;
		bottom: 30px;
		color: #fff;
		z-index: 3;
		opacity: 0.5;
		left: 50%;
		transform: translateX(-50%);

		&.green {
			color: #5cbd91 !important;
		}
	}
}

@media (max-width: 1440px) {
	.login-container {
		.form {
			.panel {
				.front {
					.form-sec {
						padding: 70px 50px;
					}
				}
			}
		}
	}
}

@media (max-width: 1400px) {
	.login-container {
		.form {
			.panel {
				.front {
					.form-sec {
						padding: 50px;
					}
				}
			}
		}

		.datoms-logo {
			bottom: 30px;
		}
	}
}

@media (max-width: 1024px) {
	.login-container {
		.form {
			.panel {
				.front {
					.form-sec {
						.head {
							font-size: 28px;
						}
					}
				}
			}
		}
	}
}

// @media (max-width: 1024px) {
// 	.login-container {
// 		.datoms-logo {
// 			display: none;
// 		}
// 		.form {
// 			width: 40%;
// 			right: -55%;

// 		}
// 	}
// }
@media (max-width: 1024px) {
	.login-container {
		-ms-flex-pack: center;
		justify-content: center;

		.form {
			right: unset;
			width: 50%;

			.panel {
				.front {
					.form-sec {
						.head {
							font-size: 20px;
						}
					}
				}
			}
		}
	}
}

@media (max-width: 1024px) and (max-height: 500px) {
	@rules {
		0 {}

		1 {}
	}

	.login-container {
		//height: 100%;
		overflow-y: auto;

		.form {
			.panel {
				.front {
					.form-sec {
						padding: 25px;
					}
				}
			}
		}

		.copyright {
			bottom: 0;
		}
	}
}

@media (max-width: 640px) and (min-width: 600px) {
	.login-container {
		.form {
			width: 60%;
		}
	}
}

@media (max-width: 425px) {
	.login-container {
		.form {
			.panel {
				.front {
					.form-sec {
						padding: 25px;
					}
				}
			}
		}
	}
}