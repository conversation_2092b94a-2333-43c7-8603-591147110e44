import React from "react";
import AntSpin from "@datoms/react-components/src/components/AntSpin";
import "./styles.less";
import "./mfa-login-style.less";
import ArrowLeftOutlined from "@ant-design/icons/ArrowLeftOutlined";
import {
  login,
  loginMobile,
  retriveThingsList,
  sendOtp,
  checkAccount,
} from "@datoms/js-sdk";
import Aurassure_logo from "../../../images/ClientLogo/aurassure_logo.png";
import IntlTelInput from "react-intl-tel-input";
import "react-intl-tel-input/dist/main.css";
import StoreLogin from "../StoreLogin";
import {
  is_valid_email,
  mfaMethodClicked,
  sendMFAOTP,
} from "../logic/mfa-logic";
import PackageJson from "../../../../package.json";

let versionDiv = "";

if (import.meta.env.VITE_MOBILE) {
  // let pjson = require('../../../../../../../package.json');
  versionDiv = "Version " + PackageJson.version;
}

let interval,
  resendCount = 0,
  iti,
  countryObj = { dialCode: "91" },
  savedText = "",
  nextClicked = false,
  emailFilled = false,
  mobileFilled = false;

export default class LoginPage extends React.Component {
  constructor(props) {
    super(props);
    // this.login = this.login.bind(this);
    this.state = {
      login: false,
      loginClicked: false,
      package_name: window.localStorage.getItem("package_name") || "",
      version: window.localStorage.getItem("App-Version")
        ? window.localStorage.getItem("App-Version")
        : PackageJson.version,
      showStoreLogin:
        window.localStorage.getItem("package_name") === "com.yoyorydes",
      mfaMethodsArray: [],
      altMFAMethods: [],
      otpMediumConfig: {},
    };
    this.loginData = this.loginData.bind(this);
    this.showLoginForm = this.showLoginForm.bind(this);
    this.showErrorMessage = this.showErrorMessage.bind(this);
    this.hideErrorMessage = this.hideErrorMessage.bind(this);
    this.getPackageName = this.getPackageName.bind(this);
    this.mfaMethodClicked = mfaMethodClicked.bind(this);
    this.sendMFAOTP = sendMFAOTP.bind(this);
  }

  static getDerivedStateFromProps(props, state) {
    if (props.mobileAppPackageName) {
      return {
        package_name: props.mobileAppPackageName,
        showStoreLogin:
          props.mobileAppPackageName === "com.yoyorydes"
            ? state.login
              ? "show_form"
              : true
            : false,
      };
    }

    return null;
  }

  showLoginForm() {
    this.setState({
      loginClicked: false,
      login: true,
    });
  }

  closeMFAForm(message) {
    this.setState({
      mfa_enabled: false,
      mfaMethod: undefined,
      mfaToken: undefined,
      mfaMethodsArray: [],
      altMFAMethods: [],
      otpMediumConfig: {},
      loginClicked: false,
      login: true,
      error_message: message,
    });
  }

  timer() {
    let timer2 = "0:15",
      timer3 = 15,
      that = this;
    resendCount++;
    if (resendCount == 2) {
      timer2 = "0:20";
      timer3 = 20;
      document.getElementById("resend_timer").innerHTML = "Resend in : 20s";
    } else if (resendCount == 3) {
      timer2 = "0:30";
      timer3 = 30;
      document.getElementById("resend_timer").innerHTML = "Resend in : 30s";
    } else {
      document.getElementById("resend_timer").innerHTML = "Resend in : 15s";
    }
    interval = setInterval(function () {
      var timer = timer2.split(":");
      //by parsing integer, I avoid all extra string processing
      var minutes = parseInt(timer[0], 10);
      var seconds = parseInt(timer[1], 10);
      --seconds;
      minutes = seconds < 0 ? --minutes : minutes;
      if (minutes < 0) {
        clearInterval(interval);
        if (resendCount < 3) {
          document.getElementById("resend_btn").style.display = "block";
        }
        document.getElementById("resend_timer").style.display = "none";
        that.hideErrorMessage();
      }
      seconds = seconds < 0 ? timer3 : seconds;
      seconds = seconds < 10 ? "0" + seconds : seconds;
      //minutes = (minutes < 10) ?  minutes : minutes;
      document.getElementById("resend_timer").innerHTML =
        "Resend in : " + seconds + "s";
      timer2 = minutes + ":" + seconds;
    }, 1000);
  }

  check_mobile() {
    if (document.getElementById("mobile_email").value) {
      return true;
    } else {
      this.showErrorMessage("Please fill mobile number!");
      return false;
    }
  }

  check_password() {
    if (document.getElementById("password").value) {
      return true;
    } else {
      this.showErrorMessage("Please fill password!");
      return false;
    }
  }

  check_otp() {
    if (document.getElementById("otp").value) {
      return true;
    } else {
      this.showErrorMessage("Please fill OTP!");
      return false;
    }
  }

  showErrorMessage(message) {
    if (message) {
      this.setState(
        {
          error_message: message,
        },
        () => {
          document.getElementById("error_msg").style.color = "red";
        },
      );
    }
  }

  setValue(text) {
    this.setState({
      emailMobileValue: text,
    });
  }

  showSuccessMessage(message) {
    if (message) {
      this.setState(
        {
          error_message: message,
        },
        () => {
          document.getElementById("error_msg").style.color = "green";
        },
      );
    }
  }

  hideErrorMessage() {
    this.setState({
      error_message: null,
    });
  }

  autoSwitchEmailMobile() {
    // console.log('autoSwitchEmailMobile_ type', type);
    // console.log('autoSwitchEmailMobile_ value', value);

    let mobVal = document.getElementById("mobile_email").value,
      that = this;
    // let emailVal = document.getElementById('email').value;

    if (savedText !== mobVal && nextClicked) {
      document.getElementById("login_btn").style.display = "none";
      document.getElementById("password_form").style.display = "none";
      document.getElementById("otp_form").style.display = "none";
      document.getElementById("otp_butn").style.display = "none";
      document.getElementById("separator").style.display = "none";
      document.getElementById("show_pass").style.display = "none";
      document.getElementById("next_btn").style.display = "block";
      document.getElementById("password").value = "";
      document.getElementById("otp").value = "";
      this.hideErrorMessage();
    } else {
      if (mobVal && mobVal.length > 1 && mobVal % 1 == 0) {
        // document.getElementById("login_btn").style.display = "none";
        // document.getElementById("password_form").style.display = "none";
        // document.getElementById("otp_form").style.display = "none";
        // document.getElementById("otp_butn").style.display = "block";
        // document.getElementById("separator").style.display = "block";
        // document.getElementById("show_pass").style.display = "block";
        document.getElementById("country_code").style.display = "block";
        document.getElementById("mobile_container").style.display = "block";
        document.getElementById("mobile_email").style.paddingLeft = "46px";
        document.getElementById("mobile_email").style.marginLeft = "7px";
        that.hideErrorMessage();
      } else {
        document.getElementById("login_btn").style.display = "none";
        document.getElementById("password_form").style.display = "none";
        document.getElementById("otp_form").style.display = "none";
        document.getElementById("otp_butn").style.display = "none";
        document.getElementById("separator").style.display = "none";
        document.getElementById("show_pass").style.display = "none";
        document.getElementById("country_code").style.display = "none";
        document.getElementById("mobile_container").style.display = "none";
        document.getElementById("mobile_email").style.paddingLeft = "10px";
        document.getElementById("mobile_email").style.marginLeft = "0px";
        document.getElementById("next_btn").style.display = "block";
        that.hideErrorMessage();
      }
    }

    savedText = mobVal;
    nextClicked = false;
  }

  check_email() {
    let mail = /\S+@\S+\.\S+/;
    let email = document.getElementById("mobile_email").value;
    if (email.trim() == "") {
      this.showErrorMessage("Please fill email field!");
      return false;
    } else if (!mail.test(email)) {
      this.showErrorMessage("Please enter a valid email!");
      document.getElementById("mobile_email").focus();
      return false;
    } else {
      return true;
    }
  }

  setLocalStorage(key, value, setWithoutChecking = false) {
    if (value || setWithoutChecking) {
      localStorage.setItem(key, value);
    }
  }

  loginData(json) {
    // hide login form
    this.setLocalStorage("Refresh-Token", json.RefreshToken);
    this.setLocalStorage("Auth-Token", json.AuthToken);
    this.setLocalStorage("User-Id", json.UserId);
    this.setLocalStorage("User-Name", json.UserName);
    this.setLocalStorage("Client-Id", json.ClientId);
    if (json.ApplicationId) {
      this.setLocalStorage("Application-Id", json.ApplicationId);
    }

    document.getElementById("user_name").value = json.UserName;
    console.log({ json });
    this.setState(
      {
        login: false,
        loginClicked: false,
        auth_token: localStorage.getItem("Auth-Token"),
      },
      async () => {
        console.log("VITE_APP.js Login State ==> clicked");
        this.props.setLoginFinish();
        console.log("package name yoyo4", this.state.package_name);
        if (this.state.package_name === "com.yoyorydes") {
          this.props.history.push("/store/home");
          this.props.execute();
        } else {
          let client_id = json.ClientId;
          if (window.localStorage.getItem("Client-Id")) {
            client_id = window.localStorage.getItem("Client-Id");
          }
          let push_path = "";
          let client_application_id = json.ApplicationId;
          if (
            window.localStorage.getItem("Application-Id") &&
            !json.ApplicationId
          ) {
            client_application_id =
              window.localStorage.getItem("Application-Id");
          }
          let push_app_name = client_application_id
            ? client_application_id == 12
              ? "datoms-x"
              : client_application_id == 17
                ? "iot-platform"
                : "dg-monitoring"
            : "dg-monitoring";
          console.log("single app -2", client_application_id);
          if (client_application_id == 12 || client_application_id == 17) {
            console.log("single app -1", push_app_name);
            push_path = "/" + push_app_name;
          } else if (client_application_id == 6) {
            push_path = "/environment-monitoring";
          } else {
            let thingListResponse = await retriveThingsList({
              client_id,
              application_id: 16,
            });
            if (thingListResponse.status !== "success") {
              this.setState({
                unauthorised_access: true,
                unauthorised_access_msg: thingListResponse.message,
              });
              return;
            }
            if (
              thingListResponse.things &&
              thingListResponse.things.length > 0
            ) {
              // console.log('single app 0', push_app_name);
              let firstThing = thingListResponse.things[0];
              if (parseInt(firstThing.category) == 18) {
                push_path = "/" + push_app_name;
              } else if (
                parseInt(firstThing.category) == 67 ||
                parseInt(firstThing.category) == 76
              ) {
                push_path = "/" + push_app_name + "/map-view";
              } else if (parseInt(firstThing.category) == 74) {
                push_path = "/" + push_app_name + "/map-view";
              } else {
                push_path = "/" + push_app_name;
              }
            }
          }
          console.log(
            "VITE_APP.js shamik 1",
            push_path,
            this.props.location.pathname,
          );
          this.props.history.push(push_path);
          console.log("VITE_APP.js shamik 2", this.props.location.pathname);
          this.props.execute();
          console.log("VITE_APP.js shamik 3");
        }
      },
    );
  }

  async loginClick(e) {
    let that = this,
      response_status;
    const { mfa_enabled, mfaMethod, mfaToken, otpMediumConfig } = this.state;
    e.preventDefault();
    console.log("Logging in...");
    let data_to_be_posted = {};

    let validation = false;
    let mobVal = "";

    if (mfa_enabled) {
      if (mfaMethod === "auth_app") {
        validation = this.check_otp();
      } else if (mfaMethod === "auth_sms") {
        if (otpMediumConfig.otp_medium === "PASSWORD") {
          validation = this.check_password();
        } else {
          validation = this.check_otp();
        }
      } else if (mfaMethod === "recovery_codes") {
        validation = this.check_otp();
      }
    } else {
      mobVal = document.getElementById("mobile_email").value;
      if (mobVal && mobVal.length > 1 && mobVal % 1 == 0) {
        if (this.check_mobile()) {
          if (
            (getComputedStyle(document.getElementById("otp_form")).display ==
              "block" ||
              getComputedStyle(document.getElementById("otp_butn")).display ==
                "block") &&
            document.getElementById("password").value == ""
          ) {
            validation = this.check_otp();
          } else {
            validation = this.check_password();
          }
        }
      } else {
        if (this.check_email()) {
          if (
            (getComputedStyle(document.getElementById("otp_form")).display ==
              "block" ||
              getComputedStyle(document.getElementById("otp_butn")).display ==
                "block") &&
            document.getElementById("password").value == ""
          ) {
            validation = this.check_otp();
          } else {
            validation = this.check_password();
          }
        }
      }
    }
    console.log("mfaLogin1: ", validation, mfa_enabled);
    if (validation) {
      that.setState({
        loginClicked: true,
      });
      let login_contact_id;
      if (mfa_enabled) {
        data_to_be_posted = {
          token: mfaToken,
          mfa_method: ["auth_app", "auth_sms"].includes(mfaMethod)
            ? mfaMethod
            : undefined,
        };

        if (is_valid_email(that.state.loginContactId)) {
          data_to_be_posted["email_id"] = that.state.loginContactId;
        } else {
          data_to_be_posted["mobile_no"] = that.state.loginContactId;
        }

        if (mfaMethod === "auth_app") {
          data_to_be_posted["otp"] = document.getElementById("otp").value;
        } else if (mfaMethod === "auth_sms") {
          if (otpMediumConfig.otp_medium === "PASSWORD") {
            data_to_be_posted["password"] =
              document.getElementById("password").value;
          } else {
            data_to_be_posted["otp"] = parseInt(
              document.getElementById("otp").value,
            );
          }
        } else if (mfaMethod === "recovery_codes") {
          data_to_be_posted["recovery_code"] =
            document.getElementById("otp").value;
        }
        console.log("mfaLogin2: ", data_to_be_posted);
      } else {
        if (mobVal && mobVal.length > 1 && mobVal % 1 == 0) {
          if (!mobVal.includes("+" + countryObj.dialCode)) {
            mobVal = "+" + countryObj.dialCode + mobVal;
          }

          data_to_be_posted["mobile_no"] = mobVal;
          login_contact_id = data_to_be_posted["mobile_no"];
        } else {
          data_to_be_posted["email_id"] = document
            .getElementById("mobile_email")
            .value.trim();
          login_contact_id = data_to_be_posted["email_id"];
        }
        // data_to_be_posted['Username'] = document.getElementById('email').value;
        if (document.getElementById("otp").value !== "") {
          data_to_be_posted["otp"] = parseInt(
            document.getElementById("otp").value,
          );
        } else {
          data_to_be_posted["password"] =
            document.getElementById("password").value;
        }
      }
      /*data_to_be_posted['Password'] = document.getElementById(
				'password'
			).value;*/
      data_to_be_posted["source"] = "mobile";
      let mobileDeviceId = localStorage.getItem("mobileDeviceId");
      data_to_be_posted["MobileDeviceId"] = mobileDeviceId;
      data_to_be_posted["MobileDeviceOS"] =
        localStorage.getItem("mobileDeviceOS");
      // data_to_be_posted['source_id'] = this.state.package_name;
      // data_to_be_posted['MobileDeviceId'] = '100';
      // data_to_be_posted['MobileDeviceOS'] = 1;
      data_to_be_posted["source_id"] = 2;
      console.log("App Version ==> ", this.state.version);
      console.log(
        "mobileAppPackageName 1",
        this.props.mobileAppPackageName,
        this.state.package_name,
      );
      if (this.props.mobileAppPackageName === "io.datoms.dgmonitoring") {
        data_to_be_posted["app_version"] = this.state.version;
      }
      let header_data = {
        "Api-Key":
          "zGOanJnuWVkO53l10anRKixJXycRza8GpxyLOY6eLbqEgjTcUgSXUwf0c99zG7xd",
        "app-id": 2,
      };
      if (this.state.package_name === "live.ojus") {
        header_data["Api-Key"] =
          "TKn8ZTNEyf9MItB3q2kvAPpUKqhTnwSWf0FHTYjLqjmACVdm1qOahPdcSQtOzLwV";
      }

      const continueToLogin = async () => {
        let loginResponse = await loginMobile(header_data, data_to_be_posted);
        console.log("loginResponse ==> ", loginResponse);
        const mfaMethodsArray =
          /*[
					{
						key: 'auth_app',
					},
					{
						key: 'auth_sms',
						data: {
							mobile_no: '',
							otp_medium: 'SMS',
						},
						is_preferred: true,
					},
				]; //*/ loginResponse.mfa_methods;
        console.log("mfaLogin3: ", mfaMethodsArray);
        if (
          loginResponse.status === "success" &&
          Array.isArray(mfaMethodsArray) &&
          mfaMethodsArray.length
        ) {
          that.showLoginForm();
          mfaMethodsArray.push({
            key: "recovery_codes",
          });
          let preferredMethod = mfaMethodsArray.find(
            (method) => method.is_preferred,
          );
          preferredMethod = preferredMethod ?? mfaMethodsArray[0];
          this.setState(
            {
              mfa_enabled: true,
              mfaToken: loginResponse.token,
              mfaMethodsArray: mfaMethodsArray,
              loginContactId: login_contact_id,
            },
            () => this.mfaMethodClicked(preferredMethod.key),
          );
        } else if (loginResponse.Status === "Success") {
          that.loginData(loginResponse);
        } else {
          if (mfa_enabled && loginResponse.close_mfa_form) {
            this.closeMFAForm(loginResponse.message);
          } else {
            that.showErrorMessage(loginResponse.message);
            that.showLoginForm();
          }
        }
      };

      // Allow to login only if we have firebase token
      // else get a token and then allow login
      if (mobileDeviceId) {
        continueToLogin();
      } else {
        window.FirebasePlugin.getToken(
          (token) => {
            console.log("firebase Token", token);
            data_to_be_posted["MobileDeviceId"] = token;
            window.localStorage.setItem("mobileDeviceId", token);
            continueToLogin();
          },
          (err) => {
            that.showErrorMessage("Unable to Login...Please Restart App");
          },
        );
      }
    }
  }

  async componentDidMount() {
    let that = this;

    // const input = document.getElementById("mobile");
    // iti = intlTelInput(input, {
    //   initialCountry: "in",
    //   /*geoIpLookup: function(success, failure) {
    //     $.get("https://ipinfo.io", function() {}, "jsonp").always(function(resp) {
    //       let countryCode = (resp && resp.country) ? resp.country : "in";
    //       success(countryCode);
    //     });
    //   },*/
    //   utilsScript: "https://prstatic.phoenixrobotix.com/3rd-party/intl-tel-input/js/utils.js"
    // });

    console.log("REFRESH TOKEN", localStorage.getItem("Refresh-Token"));
    console.log("App version", localStorage.getItem("App-Version"));
    // window.addEventListener('message', this.getPackageName, false);
    if (localStorage.getItem("Refresh-Token") !== null) {
      let responseStatus;
      let header_data = {
        "Api-Key":
          "zGOanJnuWVkO53l10anRKixJXycRza8GpxyLOY6eLbqEgjTcUgSXUwf0c99zG7xd",
        "Refresh-Token": localStorage.getItem("Refresh-Token"),
      };
      let loginResponse = await login(header_data);
      if (loginResponse.Status === "Success") {
        that.loginData(loginResponse);
      } else {
        console.log("check package name yoyo1", that.state.package_name);
        if (that.state.package_name === "com.yoyorydes") {
          that.setState({ showStoreLogin: "show_form" });
        }
        that.showLoginForm();
      }
    } else {
      console.log("check package name yoyo2", that.state.package_name);
      if (that.state.package_name === "com.yoyorydes") {
        that.setState({ showStoreLogin: "show_form" });
      }
      this.showLoginForm();
    }
  }

  // componentWillUnmount() {
  // 	window.removeEventListener('message', this.getPackageName);
  // }

  loginWithOtp() {
    document.getElementById("password_form").style.display = "none";
    document.getElementById("otp_butn").style.display = "block";
    document.getElementById("separator").style.display = "block";
    document.getElementById("show_pass").style.display = "block";
    document.getElementById("login_btn").style.display = "none";
    document.getElementById("password").value = "";
    this.hideErrorMessage();
  }

  loginWithPassword() {
    document.getElementById("password_form").style.display = "block";
    document.getElementById("otp_form").style.display = "none";
    document.getElementById("otp_butn").style.display = "none";
    document.getElementById("separator").style.display = "none";
    document.getElementById("show_pass").style.display = "none";
    document.getElementById("login_btn").style.display = "block";
    document.getElementById("resend_timer").style.display = "none";
    document.getElementById("password").value = "";
    document.getElementById("otp").value = "";
    this.hideErrorMessage();
    clearInterval(interval);
  }

  backClicked() {
    if (emailFilled) {
      document.getElementById("value_form").style.display = "none";
      document.getElementById("mob_form").style.display = "block";
      document.getElementById("otp_form").style.display = "none";
      document.getElementById("next_btn").style.display = "block";
      document.getElementById("otp_butn").style.display = "none";
      document.getElementById("resend_btn").style.display = "none";
      document.getElementById("resend_timer").style.display = "none";
      document.getElementById("login_btn").style.display = "none";
      document.getElementById("password_form").style.display = "none";
      document.getElementById("otp_form").style.display = "none";
      document.getElementById("show_pass").style.display = "none";
      document.getElementById("separator").style.display = "none";
      document.getElementById("otp").value = "";
      document.getElementById("password").value = "";
      document.getElementById("country_code").style.display = "none";
      document.getElementById("mobile_container").style.display = "none";
      document.getElementById("mobile_email").style.paddingLeft = "10px";
      document.getElementById("mobile_email").style.marginLeft = "0px";
      this.hideErrorMessage();
      clearInterval(interval);
    } else {
      document.getElementById("value_form").style.display = "none";
      document.getElementById("mob_form").style.display = "block";
      document.getElementById("otp_form").style.display = "none";
      document.getElementById("next_btn").style.display = "block";
      document.getElementById("otp_butn").style.display = "none";
      document.getElementById("resend_btn").style.display = "none";
      document.getElementById("resend_timer").style.display = "none";
      document.getElementById("login_btn").style.display = "none";
      document.getElementById("password_form").style.display = "none";
      document.getElementById("otp_form").style.display = "none";
      document.getElementById("show_pass").style.display = "none";
      document.getElementById("separator").style.display = "none";
      document.getElementById("otp").value = "";
      document.getElementById("password").value = "";
      document.getElementById("country_code").style.display = "block";
      document.getElementById("mobile_container").style.display = "block";
      document.getElementById("mobile_email").style.paddingLeft = "46px";
      document.getElementById("mobile_email").style.marginLeft = "7px";
      this.hideErrorMessage();
      clearInterval(interval);
    }

    this.setValue("");
    emailFilled = false;
    mobileFilled = false;
  }

  async checkAccount() {
    let validation = false,
      that = this;
    resendCount = 0;
    let mobVal = document.getElementById("mobile_email").value;

    if (mobVal && mobVal.length > 1 && mobVal % 1 == 0) {
      validation = this.check_mobile();
    } else {
      validation = this.check_email();
    }

    if (validation) {
      const nextButton = document.getElementById("next_btn");
      if (nextButton) {
        nextButton.innerText = "Please wait...";
        nextButton.style.filter = "brightness(0.9)";
        nextButton.style.cursor = "not-allowed";
        nextButton.style.pointerEvents = "none";
      }
      let data_to_be_posted = {},
        response_status;
      if (mobVal && mobVal.length > 1 && mobVal % 1 == 0) {
        if (!mobVal.includes("+" + countryObj.dialCode)) {
          mobVal = "+" + countryObj.dialCode + mobVal;
        }
        that.setValue(mobVal);
        data_to_be_posted["mobile_no"] = mobVal;
      } else {
        data_to_be_posted["email_id"] = document
          .getElementById("mobile_email")
          .value.trim();
        that.setValue(document.getElementById("mobile_email").value);
      }

      document.getElementById("resend_btn").style.disabled = true;

      let otpResponse = await checkAccount(data_to_be_posted);
      if (nextButton) {
        nextButton.innerText = "Next";
        nextButton.style.filter = "none";
        nextButton.style.cursor = "pointer";
        nextButton.style.pointerEvents = "auto";
      }
      if (otpResponse.status === "success") {
        nextClicked = true;
        if (otpResponse.is_password) {
          document.getElementById("value_form").style.display = "block";
          document.getElementById("mob_form").style.display = "none";
          if (mobVal && mobVal.length > 1 && mobVal % 1 == 0) {
            mobileFilled = true;
            document.getElementById("otp_butn").style.display = "block";
            document.getElementById("separator").style.display = "block";
            document.getElementById("show_pass").style.display = "block";
            document.getElementById("next_btn").style.display = "none";
          } else {
            emailFilled = true;
            document.getElementById("password_form").style.display = "block";
            document.getElementById("login_btn").style.display = "block";
            document.getElementById("next_btn").style.display = "none";
          }
        } else {
          that.sendOtp();
        }
      } else {
        document.getElementById("resend_btn").style.disabled = false;
        that.showErrorMessage(otpResponse.message);
      }
    }
  }

  async sendOtp() {
    let validation = false,
      that = this;
    resendCount = 0;
    let mobVal = document.getElementById("mobile_email").value;

    if (mobVal && mobVal.length > 1 && mobVal % 1 == 0) {
      validation = this.check_mobile();
    } else {
      validation = this.check_email();
    }

    if (validation) {
      let data_to_be_posted = { query_text: "?purpose=LOGIN" },
        response_status;
      if (mobVal && mobVal.length > 1 && mobVal % 1 == 0) {
        if (!mobVal.includes("+" + countryObj.dialCode)) {
          mobVal = "+" + countryObj.dialCode + mobVal;
        }
        data_to_be_posted["mobile_no"] = mobVal;
      } else {
        data_to_be_posted["email_id"] = document
          .getElementById("mobile_email")
          .value.trim();
      }

      document.getElementById("resend_btn").style.disabled = true;

      let otpResponse = await sendOtp(data_to_be_posted);
      if (otpResponse.status === "success") {
        if (mobVal && mobVal.length > 1 && mobVal % 1 == 0) {
          mobileFilled = true;
        } else {
          emailFilled = true;
        }
        document.getElementById("resend_btn").style.disabled = false;
        document.getElementById("value_form").style.display = "block";
        document.getElementById("mob_form").style.display = "none";
        document.getElementById("otp_form").style.display = "block";
        document.getElementById("next_btn").style.display = "none";
        document.getElementById("otp_butn").style.display = "none";
        document.getElementById("resend_btn").style.display = "none";
        document.getElementById("resend_timer").style.display = "block";
        document.getElementById("login_btn").style.display = "block";
        document.getElementById("otp").value = "";
        that.showSuccessMessage("OTP sent successfully!");
        that.timer();
      } else {
        document.getElementById("resend_btn").style.disabled = false;
        that.showErrorMessage(otpResponse.message);
      }
    }
  }

  async resendOtp() {
    let validation = false,
      that = this;
    let mobVal = document.getElementById("mobile_email").value;

    if (mobVal && mobVal.length > 1 && mobVal % 1 == 0) {
      validation = this.check_mobile();
    } else {
      validation = this.check_email();
    }

    if (validation) {
      let data_to_be_posted = { query_text: "?purpose=LOGIN" },
        response_status;
      if (mobVal && mobVal.length > 1 && mobVal % 1 == 0) {
        if (!mobVal.includes("+" + countryObj.dialCode)) {
          mobVal = "+" + countryObj.dialCode + mobVal;
        }
        data_to_be_posted["mobile_no"] = mobVal;
      } else {
        data_to_be_posted["email_id"] = document
          .getElementById("mobile_email")
          .value.trim();
      }

      document.getElementById("resend_btn").style.disabled = true;

      let otpResponse = await sendOtp(data_to_be_posted);
      if (otpResponse.status === "success") {
        document.getElementById("resend_btn").style.disabled = false;
        document.getElementById("resend_btn").style.display = "none";
        document.getElementById("resend_timer").style.display = "block";
        document.getElementById("otp").value = "";
        that.showSuccessMessage("OTP sent successfully!");
        that.timer();
      } else {
        document.getElementById("resend_btn").style.disabled = false;
        that.showErrorMessage(otpResponse.message);
      }
    }
  }

  getPackageName(e) {
    // console.log("event-package name",e)
    if (e.data.type === "package") {
      window.localStorage.setItem("package_name", e.data.name);
      console.log("check package name yoyo3", this.state.login, e.data.name);
      this.setState({
        package_name: e.data.name,
        showStoreLogin:
          e.data.name === "com.yoyorydes"
            ? this.state.login
              ? "show_form"
              : true
            : false,
      });
    }
  }

  selectFlag(e, value) {
    console.log("selectFlag_", value);
    countryObj = value;
    document.getElementById("country_code").innerHTML = "+" + value.dialCode;
  }

  render() {
    console.log("mobileAppPackageName 2", this.props.mobileAppPackageName);
    const { mfaMethod, otpMediumConfig, showMfaVerify, altMFAMethods } =
      this.state;
    if (this.state.showStoreLogin) {
      return (
        <StoreLogin
          {...this.props}
          showStoreLogin={this.state.showStoreLogin}
        />
      );
    }
    console.log("single app 2", this.props);
    let formContainer = (
      <div>
        <AntSpin
          size="large"
          className="align-center-loading"
          style={{ color: "black" }}
        />
      </div>
    );

    let errorMessage = [];
    let buttonShow = (
      <input
        type="submit"
        className={`btn ${
          this.state.package_name === "com.tatamotors.tmlgensetrms"
            ? "tata-login-button"
            : this.state.package_name === "live.ojus"
              ? "yellow-login-button"
              : ""
        } ${
          this.state.package_name === "com.aurassure.app"
            ? "aurassure-login-button"
            : ""
        }`}
        value="Login"
        id={`form_submit_btn`}
        onClick={(e) => this.loginClick(e)}
      />
    );
    if (this.state.loginClicked) {
      buttonShow = (
        <div style={{ flex: 1 }}>
          <AntSpin />
        </div>
      );
    }

    const { error_message, emailMobileValue } = this.state;
    if (error_message) {
      errorMessage.push(
        <div id="error_msg" className="error-msg">
          {error_message}
        </div>,
      );
    }

    // let emailField = <div id="email_form" className="form-container">
    // 	<div className="header">Email or Mobile</div>
    // 	<input
    // 		id="email"
    // 		type="text"
    // 		className="form-control"
    // 		name="email"
    // 		aria-label="Email"
    // 		onChange={() => this.autoSwitchEmailMobile('email')}
    // 	/>
    // </div>;

    // let mobileField = <div id="mob_form" className="form-container">
    // 	<div className="header">Email or Mobile</div>
    // 	<input
    // 		id="mobile"
    // 		type="tel"
    // 		className="form-control"
    // 		name="mobile"
    // 		aria-label="Mobile"
    // 		placeHolder=" "
    // 		onChange={() => this.autoSwitchEmailMobile('mobile')}
    // 	/>
    // </div>;
    let valueField = (
      <div id="value_form" className="form-container mar-top-30">
        <div
          id="email_mobile_value"
          className={`${
            this.state.package_name === "live.ojus" ? "white_email_id" : ""
          }`}
        >
          {emailMobileValue}
        </div>
      </div>
    );

    let mobileField = (
      <div id="mob_form" className="form-container">
        <div className="header">Email or Mobile</div>
        <div className="flag-mobile">
          <div id="mobile_container">
            <IntlTelInput
              fieldId="country"
              fieldName="country"
              placeholder=" "
              inputClassName="form-control"
              defaultCountry="in"
              useMobileFullscreenDropdown={false}
              onSelectFlag={(e, value) => this.selectFlag(e, value)}
              /*onPhoneNumberChange={(e, value, country) => this.autoSwitchEmailMobile('mobile', country, value)}*/
            />
          </div>
          <div className="mobile-code">
            <span id="country_code">+91</span>
            <input
              id="mobile_email"
              type="text"
              className="form-control"
              name="mobile-email"
              aria-label="mobile"
              onChange={() => this.autoSwitchEmailMobile()}
            />
          </div>
        </div>
      </div>
    );

    let passwordField = (
      <div id="password_form" className="form-container mar-top-30">
        <div className="header">
          Password
          <span
            id="show_otp"
            class="email-mobile"
            onClick={() => this.loginWithOtp()}
          >
            Login with OTP
          </span>
        </div>
        <input
          id="password"
          type="password"
          className="form-control"
          name="password"
          aria-label="Password"
          onChange={this.hideErrorMessage}
        />
      </div>
    );

    let backButn = (
      <div id="back_butn" onClick={() => this.backClicked()}>
        <ArrowLeftOutlined />
      </div>
    );

    let otpField = (
      <div id="otp_form" className="form-container mar-top-30">
        <div className="header">OTP</div>
        <input
          id="otp"
          type="number"
          className="form-control"
          name="otp"
          aria-label="OTP"
          onChange={this.hideErrorMessage}
        />
        <div id="resend_btn" class="resend" onClick={() => this.resendOtp()}>
          Resend OTP
        </div>
        <div id="resend_timer"></div>
      </div>
    );

    if (this.state.login) {
      formContainer = (
        <div
          className={
            "form-sec" +
            (this.state.package_name === "com.tatamotors.tmlgensetrms"
              ? " tata"
              : "")
          }
        >
          {this.state.package_name === "com.tatamotors.tmlgensetrms" ? (
            <img
              src="https://datoms-files-storage.s3.ap-south-1.amazonaws.com/datoms/device-management/firmwares/image/1714634184654.png"
              alt="logo"
              className="tata-logo"
            />
          ) : this.state.package_name === "live.ojus" ? (
            ""
          ) : this.state.package_name === "com.aurassure.app" ? (
            <img src={Aurassure_logo} alt="logo" className="aurassure-logo" />
          ) : (
            <div className="head">
              {this.state.mfa_enabled ? "Two-factor authentication" : "Login"}
            </div>
          )}
          {this.state.mfa_enabled ? (
            <form id="login_form_mfa">
              {mfaMethod === "auth_app" && (
                <div id="mfa_totp_form" className="form-container">
                  <div className="header">Authentication code</div>
                  <input
                    id="otp"
                    type="number"
                    className="form-control mfa-otp"
                    name="otp"
                    aria-label="OTP"
                  />
                  <div className="description">
                    Open your two-factor authenticator (TOTP) app or browser
                    extension to view your authentication code.
                  </div>
                </div>
              )}
              {mfaMethod === "auth_sms" && (
                <>
                  {otpMediumConfig?.otp_medium !== "PASSWORD" ? (
                    <>
                      <div id="mfa_otp_form" className="form-container">
                        {showMfaVerify ? null : (
                          <div id="send_otp_description">
                            Click on the button below to receive a code via
                            {otpMediumConfig?.otp_medium === "EMAIL"
                              ? " email"
                              : " SMS"}
                            .
                          </div>
                        )}

                        {showMfaVerify ? (
                          <>
                            <div id="mfa_otp_fields">
                              <input
                                id="otp"
                                type="number"
                                className="form-control mfa-otp"
                                name="otp"
                                aria-label="OTP"
                              />
                              <div className="description">
                                Enter the 6 digit OTP sent to your
                                {otpMediumConfig?.otp_medium === "EMAIL"
                                  ? " email"
                                  : " phone"}
                                <span id="mfa_otp_medium_2" /> and click on
                                verify
                              </div>
                              <div
                                id="resend_btn"
                                className="resend"
                                onClick={() => this.sendMFAOTP(true)}
                              >
                                Resend OTP
                              </div>
                              <div id="resend_timer"></div>
                            </div>
                          </>
                        ) : null}
                      </div>
                    </>
                  ) : (
                    <div
                      id="mfa_password_form"
                      className="form-container mar-top-30"
                    >
                      <div className="header">Password</div>
                      <input
                        id="password"
                        type="password"
                        className="form-control"
                        name="password"
                        aria-label="Password"
                      />
                      <div className="description">
                        Enter your password to verify your account
                      </div>
                      <div></div>
                    </div>
                  )}
                </>
              )}
              {mfaMethod === "recovery_codes" && (
                <div id="mfa_recovery_form" className="form-container">
                  <div className="header">Recovery code</div>
                  <input
                    id="otp"
                    className="form-control mfa-otp"
                    name="otp"
                    aria-label="OTP"
                  />
                  <div className="description">
                    Enter one of your recovery codes to verify your identity
                  </div>
                </div>
              )}
              {errorMessage}
              {!showMfaVerify ? (
                <div
                  id="mfa_otp_butn"
                  className="otp-contain"
                  onClick={() => this.sendMFAOTP()}
                >
                  Send OTP
                </div>
              ) : (
                <div id="mfa_login_btn" className="btn-contain">
                  {this.state.loginClicked ? (
                    <AntSpin />
                  ) : (
                    <input
                      type="submit"
                      id="mfa_form_submit_btn"
                      className="btn"
                      value="Verify"
                      onClick={(e) => this.loginClick(e)}
                    />
                  )}
                </div>
              )}
              <div id="mfa_having_issues">
                <div className="mfa-mid-header">Try another method</div>
                {altMFAMethods.map((method) => (
                  <div
                    key={method.key}
                    className="mfa-method-item"
                    onClick={() => this.mfaMethodClicked(method.key)}
                  >
                    {method.title}
                  </div>
                ))}
              </div>
            </form>
          ) : (
            <form id="login_form">
              {/*{emailField}*/}
              {mobileField}
              {valueField}
              {passwordField}
              {otpField}
              {errorMessage}
              <div id="otp_butn">
                {backButn}
                <div
                  id="send_otp"
                  className={`send-button ${
                    this.state.package_name === "com.tatamotors.tmlgensetrms"
                      ? "tata-login-button"
                      : this.state.package_name === "live.ojus"
                        ? "yellow-login-button"
                        : ""
                  } ${
                    this.state.package_name === "com.aurassure.app"
                      ? "aurassure-login-button"
                      : ""
                  }`}
                  onClick={() => this.sendOtp()}
                >
                  Send OTP
                </div>
              </div>
              <div
                id="next_btn"
                className={`next-button ${
                  this.state.package_name === "com.tatamotors.tmlgensetrms"
                    ? "tata-login-button"
                    : this.state.package_name === "live.ojus"
                      ? "yellow-login-button"
                      : ""
                } ${
                  this.state.package_name === "com.aurassure.app"
                    ? "aurassure-login-button"
                    : ""
                }`}
                onClick={() => this.checkAccount()}
              >
                Next
              </div>
              <div id="separator">
                <span>OR</span>
              </div>
              <div
                id="show_pass"
                className={`${
                  this.state.package_name === "live.ojus"
                    ? "white_email_id"
                    : ""
                }`}
                onClick={() => this.loginWithPassword()}
              >
                Use password
              </div>
              <div id="login_btn" className="btn-contain">
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  {backButn}
                  {buttonShow}
                </div>
              </div>
              <div id="forgot_pswd">
                <a
                  onClick={() => {
                    let link =
                      this.state.package_name === "live.ojus"
                        ? "https://ojus.live/accounts/forgot"
                        : this.state.package_name === "com.aurassure.app"
                          ? "https://app.aurassure.com/accounts/forgot"
                          : "https://accounts.datoms.io/forgot";

                    window.open(link, "_system");
                  }}
                  target="_system"
                >
                  Forgot Password ?
                </a>
              </div>
            </form>
          )}
        </div>
      );
    }

    return (
      <div
        className={
          "login-container" +
          (this.state.package_name === "com.tatamotors.tmlgensetrms"
            ? " tata"
            : "") +
          (this.state.package_name === "live.ojus" ? " ojus" : "") +
          (this.state.package_name === "com.aurassure.app" ? " aurassure" : "")
        }
      >
        <div
          className={
            "back-img" +
            (this.state.package_name === "com.tatamotors.tmlgensetrms"
              ? " tata-back-img"
              : this.state.package_name === "live.ojus"
                ? " ojus-back"
                : this.state.package_name === "com.aurassure.app"
                  ? " aurassure-back"
                  : "")
          }
        ></div>
        {this.state.package_name === "live.ojus" ? (
          <img
            className="ojus-img"
            src="https://static.datoms.io/images/ojus-text-white.png"
          />
        ) : (
          ""
        )}
        <div
          className={
            "form" +
            (this.state.package_name === "live.ojus" ? " ojus-form" : "")
          }
        >
          <div className="click panel">
            <div className="front">{formContainer}</div>
          </div>
        </div>
        {this.state.package_name === "live.ojus" && (
          <img
            className="banner-img"
            src={"https://static.datoms.io/images/ojus-logo-white.png"}
          />
        )}
        <div
          id="app_version"
          className={
            "app-version-login " +
            (this.state.package_name === "com.aurassure.app" ? "green" : "")
          }
        >
          Version {this.state.version}
        </div>
        {this.state.package_name !== "live.ojus" &&
          this.state.package_name !== "com.tatamotors.tmlgensetrms" && (
            <div className="copyright">Powered By Datoms</div>
          )}

        <div className="datoms-logo">
          <img src="https://static.datoms.io/images/icons/datoms_white-01.svg" />
        </div>
      </div>
    );
  }
}