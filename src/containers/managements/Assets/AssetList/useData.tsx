import { useState, useCallback, useEffect, useRef } from "react";
import { message } from "antd";
import StopOutlined from "@ant-design/icons/StopOutlined";
import SettingOutlined from "@ant-design/icons/SettingOutlined";
import DeleteOutlined from "@ant-design/icons/DeleteOutlined";
import FileTextOutlined from "@ant-design/icons/FileTextOutlined";
import moment from "moment-timezone";
import {
  deleteThing,
  activateThing,
  deactivateThing,
  getVendorAssetList,
} from "@datoms/js-sdk";
import { useGlobalContext } from "../../../../store/globalStore";
import { isTerritoryEnabledFunc } from "./utility";
import AntTag from "@datoms/react-components/src/components/AntTag";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import AntProgress from "@datoms/react-components/src/components/AntProgress";
import ImageComponent from "@datoms/react-components/src/components/ImageComponent";
import HeirarchyPopover from "@datoms/react-components/src/components/HeirarchyPopover";
import { TimeFormatter } from "@datoms/js-utils/src/TimeFormatting.js";
import raw_log_icon from "@datoms/webapp-component-thing-management/src/imgs/raw_log_icon.svg";
import DebugIcon from "@datoms/webapp-component-thing-management/src/imgs/debug_icon.svg";
import DashboardIcon from "@datoms/webapp-component-thing-management/src/imgs/dashboard_icon.svg";
import ActiveIcon from "@datoms/webapp-component-thing-management/src/imgs/active_icon.svg";
import InactiveIcon from "@datoms/webapp-component-thing-management/src/imgs/inactive_icon.svg";
import ConfigureIcon from "@datoms/webapp-component-thing-management/src/imgs/configure_icon.svg";
import ErrorConfigureIcon from "@datoms/webapp-component-thing-management/src/imgs/error_configure_icon.svg";
import LatLongConfigure from "@datoms/webapp-component-thing-management/src/imgs/lat-long-configure.svg";
import { Link } from "react-router-dom";
import AntRow from "@datoms/react-components/src/components/AntRow";
import AntCol from "@datoms/react-components/src/components/AntTimelineItem";
import AntSwitch from "@datoms/react-components/src/components/AntSwitch";
import AntConfirmModal from "@datoms/react-components/src/components/AntConfirmModal";

const accountTypeOptions = [
  { value: "production", title: "Production" },
  { value: "sandbox", title: "Sandbox" },
  { value: "development", title: "Development" },
];

type UseDataReturn = {
  filterData: any[];
  pageLoading: boolean;
};

export const useData = (props): UseDataReturn => {
  const context = useGlobalContext();
  const isMobileScreen = window.innerWidth < 576;
  const filterRef = useRef<any>(null);
  const isTerritoryEnabled = isTerritoryEnabledFunc(context);
  const [pagination, setPagination] = useState({
    page_no: 1,
    page_size: 20,
    total: 0,
  });
  const [dataSource, setDataSource] = useState([]);
  const [pageLoading, setPageLoading] = useState<boolean>(true);
  const [linkCustomerData, setLinkCustomerData] = useState<any>(null);

  // useEffect(() => {
  //   fetchAssetList()
  // }, []);

  function getDashBoardURL(applicationSlug = "dg-monitoring", data) {
    let basepath = "https://app.datoms.io";
    if (
      !import.meta.env.VITE_MOBILE &&
      typeof window !== undefined &&
      !window.location.href.includes("localhost")
    ) {
      basepath = window.location.protocol + "//" + window.location.host;
    }
    if (
      data &&
      data.category &&
      parseInt(data.category) === 18 &&
      data.vendor_id !== 1280
    ) {
      return `${basepath}/enterprise/${data.customer_id}/${applicationSlug}/detailed-view/?thing_id=${data.id}`;
    } else {
      return `${basepath}/enterprise/${data.customer_id}/${applicationSlug}`;
    }
  }

  const fetchAssetList = async (
    filters: any = {},
    currentPagination = pagination,
  ) => {
    console.log("filters assetlist", filters);
    setPageLoading(true);
    const assetDataSource = [];
    let queryString = `?page_no=${currentPagination.page_no}&results_per_page=${currentPagination.page_size}`;
    if (filters.account_type) {
      queryString += `&account_type=${filters.account_type}`;
    }
    if (filters.territories?.length) {
      queryString += `&territories=${encodeURIComponent(filters.territories)}`;
    }
    if (filters.customers?.length) {
      queryString += `&customers=[${filters.customers.join(",")}]`;
    } else if (filters.vendors?.length) {
      queryString += `&vendors=[${filters.vendors.join(",")}]`;
    }
    if (filters.search) {
      queryString += `&search=${filters.search}`;
    }
    if (filters.thing_category) {
      queryString += `&thing_category=${filters.thing_category}`;
    }
    if (filters.status) {
      queryString += `&status=${filters.status}`;
    }
    if (filters.online_offline_status) {
      queryString += `&online_offline_status=${filters.online_offline_status}`;
    }

    // const response = await retriveVendorThingsList(
    //   {
    //     vendor_id: context.client_id,
    //     application_id: context.application_id,
    //   },
    //   queryString,
    // );
    const response = await getVendorAssetList(context.client_id, queryString);

    if (response.status !== "success") {
      message.error("Error in fetching asset list!");
    }

    response.things.forEach((thing: any) => {
      const time_zone = context?.user_preferences?.timezone || "Asia/Kolkata";
      const url = getDashBoardURL(undefined, thing);
      const showPrimaryVendorName =
        context?.application_id === 12 &&
        thing.vendor_id !== thing.thing_owner_id &&
        ![1, thing.customer_id].includes(thing.thing_owner_id);
      assetDataSource.push({
        thing_id: thing.id,
        thing_name: thing.name,
        vendor_id: thing.vendor_id,
        vendor_name: thing.vendor_name,
        parent_vendor_id: thing.thing_owner_id,
        primary_vendor_name: showPrimaryVendorName
          ? thing.thing_owner_name
          : false,
        goem_name: thing.goem_name,
        customer_id: thing.customer_id,
        customer_name: thing.customer_name,
        application_id: thing.application_id,
        // application: applicationDetail ? applicationDetail.name : "",
        category_id: thing.category,
        type_name: thing.category_name,
        status:
          thing.active_status && thing.active_status === "active"
            ? "active"
            : "inactive",
        data_availability: thing.curr_month_data_availability
          ? Math.round(parseFloat(thing.curr_month_data_availability))
          : 0,
        devices: thing.devices,
        active_status:
          thing.status_code && thing.status_code === 1 ? "online" : "offline",
        // customer_slug:
        // customerDetails && customerDetails.id ? customerDetails.id : "",
        // station_slug: str,
        url: thing.thing_customer_type === 1 ? "" : url,
        // push_url: thing.thing_customer_type === 1 ? "" : push_url,
        created_on: thing.added_at,
        created_by: thing.added_by,
        tags: thing.tags,
        thing_details: thing.thing_details || {},
        latitude:
          Object.keys(thing) &&
          Object.keys(thing).length &&
          Object.keys(thing).includes("latitude")
            ? thing.latitude
            : "no-keys",
        longitude:
          Object.keys(thing) &&
          Object.keys(thing).length &&
          Object.keys(thing).includes("longitude")
            ? thing.longitude
            : "no-keys",
        last_data_received_time: thing.last_data_received_time,
        functional_status: thing.functional_status,
        rental_status: thing.rental_status,
        is_rental: thing.is_rental,
        cpcb_status: thing.cpcb_status,
        is_iot_enabled: thing.is_iot_enabled,
        spcb_status: thing.spcb_status,
        dmd_status: thing.dmd_status,
        thing_category_icon: thing.thing_category_icon,
        transparent_category_icon: thing.transparent_category_icon,
        current_assigned_customer: thing.current_assigned_customer,
        subscribed_on: thing.subscription_start_time
          ? moment(thing.subscription_start_time, "X")
              .tz(time_zone)
              .format("DD MMM YYYY")
          : "NA",
        first_data_recieved: thing.first_data_time
          ? moment(thing.first_data_time, "X")
              .tz(time_zone)
              .format("DD MMM YYYY")
          : "NA",
        // customer_status: customerDetails?.status,
        assetWithoutCustomer: thing.thing_customer_type === 1,
        thing_goem_id: thing.thing_goem_id,
      });
    });

    console.log("assetDataSource", assetDataSource);
    setPagination((prev) => ({ ...prev, total: response.total_counts || 0 }));
    setDataSource(assetDataSource);
    setPageLoading(false);
  };

  const onTableChange = async (paginationData: any) => {
    const paginationState = {
      page_no: paginationData?.current || 1,
      page_size: paginationData?.pageSize || 20,
    };
    setPagination((prev) => ({ ...prev, ...paginationState }));

    fetchAssetList(filterRef?.current?.getFilters(), paginationState);
  };

  // useEffect(() => {
  //   if (filterRef?.current) {
  //     fetchAssetList();
  //   }
  // }, [filterRef?.current?.getFilters()]);

  const filterData = [
    {
      optionData: accountTypeOptions,
      showSearch: true,
      sorted: false,
      allowClear: true,
      label: "Account Type",
      placeholder: "Select Account Type",
      no_outside_label: true,
      url_name: "account_type",
      key: "account_type",
      is_outside_filter_drawer: !isMobileScreen,
      is_inside_filter_drawer: isMobileScreen,
      selectValue: undefined,
      defaultValue: "production",
    },
    {
      type: "tree_select",
      hideField: !isTerritoryEnabled,
      label: "Territory",
      url_name: "territories",
      key: "territories",
      filter_api: "territories",
      is_options_dynamic: true,
      component_props: {
        treeData: [],
        value: [],
        treeDefaultExpandAll: true,
        treeCheckable: true,
        showCheckedStrategy: "SHOW_PARENT",
        treeCheckStrictly: true,
        maxTagCount: 0,
        maxTagPlaceholder: (omittedValues: number[]) =>
          omittedValues.length +
          ` territor${omittedValues.length === 1 ? "y" : "ies"} selected`,
        placeholder: "Select territories",
        filterTreeNode: (search: string, item: { title: string }) => {
          return item.title.toLowerCase().indexOf(search.toLowerCase()) >= 0;
        },
      },
      no_outside_label: true,
      is_outside_filter_drawer: !isMobileScreen,
      is_inside_filter_drawer: false,
    },
    {
      optionData: [],
      showSearch: true,
      sorted: false,
      allowClear: true,
      label: "Partner",
      placeholder: "Partners",
      no_outside_label: true,
      multiSelect: true,
      filterType: "partner",
      url_name: "vendors",
      is_options_dynamic: true,
      filterOption: false,
      autoClearSearchValue: false,
      key: "vendors",
      showSingleOption: true,
      showEmptyOption: true,
      is_outside_filter_drawer: !isMobileScreen,
      is_inside_filter_drawer: isMobileScreen,
      filter_api: "vendors",
      api_pagination: true,
      selectValue: undefined,
    },
    {
      optionData: [],
      showSearch: true,
      sorted: false,
      allowClear: true,
      multiSelect: true,
      label: "Customer",
      placeholder: "Customers",
      filterType: "customers",
      is_options_dynamic: true,
      filterOption: false,
      no_outside_label: true,
      autoClearSearchValue: false,
      url_name: "customers",
      showSingleOption: true,
      showEmptyOption: true,
      key: "customers",
      is_outside_filter_drawer: !isMobileScreen,
      is_inside_filter_drawer: isMobileScreen,
      filter_api: "customers",
      api_pagination: true,
      selectValue: undefined,
    },
    {
      optionData: [],
      showSearch: true,
      sorted: false,
      allowClear: true,
      multiSelect: false,
      label: "Asset Category",
      placeholder: "Asset Types",
      no_outside_label: true,
      key: "thing_category",
      url_name: "thing_category",
      is_outside_filter_drawer: !isMobileScreen,
      is_inside_filter_drawer: isMobileScreen,
      filter_api: "thing_category",
      selectValue: undefined,
    },
    {
      optionData: [
        {
          title: "Active",
          value: "active",
        },
        {
          title: "Inactive",
          value: "inactive",
        },
      ],
      showSearch: true,
      allowClear: true,
      sorted: false,
      label: "Status",
      placeholder: "Asset Status",
      no_outside_label: true,
      multiSelect: true,
      key: "status",
      url_name: "status",
      is_outside_filter_drawer: !isMobileScreen,
      is_inside_filter_drawer: isMobileScreen,
      selectValue: undefined,
    },
    {
      optionData: [
        {
          title: "Online",
          value: "online",
        },
        {
          title: "Offline",
          value: "offline",
        },
      ],
      showSearch: true,
      allowClear: true,
      sorted: false,
      label: "Connectivity",
      placeholder: "Connectivity",
      no_outside_label: true,
      multiSelect: true,
      key: "online_offline_status",
      url_name: "online_offline_status",
      is_outside_filter_drawer: !isMobileScreen,
      is_inside_filter_drawer: isMobileScreen,
      selectValue: undefined,
    },
  ];

  const resetDependentFields = {
    vendors: ["customers"],
    // customer_id:["entity_category_id","entity_id"],
    // entity_category_id:["entity_id"]
  };

  function getLastUpdatedTime(last_data_received_time) {
    return last_data_received_time
      ? TimeFormatter(
          context?.user_preferences?.time_format,
          last_data_received_time,
          "DD MMM YYYY, HH:mm",
        )
      : "-";
  }

  function showRentalTag() {
    return context.client_id === 1 || context.customer_type?.includes(4);
  }

  function getDeviceNames(devices = []) {
    const device_names = [];
    let extra_names = "";
    for (let i = 0; i < devices.length; i++) {
      if (devices[i] && i < 2) {
        device_names.push({
          name: devices[i].qr_code,
          is_online:
            devices[i].online_status && devices[i].online_status === 1
              ? true
              : false,
          last_online_time: devices[i].last_online_time
            ? TimeFormatter(
                context?.user_preferences?.time_format,
                devices[i].last_online_time,
                "DD MMM YYYY, HH:mm",
              )
            : "-",
          last_data_received_time: devices[i].last_data_received_time
            ? TimeFormatter(
                context?.user_preferences?.time_format,
                devices[i].last_data_received_time,
                "DD MMM YYYY, HH:mm",
              )
            : "-",
        });
      } else {
        let receivedTime = "";
        if (devices[i].last_online_time) {
          receivedTime =
            "(Last Online: " +
            TimeFormatter(
              context?.user_preferences?.time_format,
              devices[i].last_online_time,
              "DD MMM YYYY, HH:mm",
            ) +
            ")";
        }
        if (extra_names == "") {
          extra_names = devices[i].qr_code + receivedTime;
        } else {
          extra_names += ", " + devices[i].qr_code + receivedTime;
        }
      }
    }

    if (devices.length > 2) {
      device_names.push({
        name: "+ " + (devices.length - 2) + " more",
        is_online: false,
      });
    }

    if (device_names.length) {
      return device_names.map((device, index) => {
        if (index == device_names.length - 1 && device_names.length > 2) {
          return (
            <AntTooltip
              overlayClassName="thing-list-device-tooltip"
              title={extra_names}
            >
              <AntTag
                className="vendor-tag"
                color={device.is_online ? "green" : "default"}
              >
                {device.name}
              </AntTag>
            </AntTooltip>
          );
        } else {
          return (
            <AntTooltip
              overlayClassName="thing-list-device-tooltip"
              title={
                <div>
                  <div>Device QR: {device.name}</div>
                  <div>{`Last Online Time: ${device.last_online_time}`}</div>
                  <div>{`Last Data Recieved: ${device.last_data_received_time}`}</div>
                </div>
              }
            >
              <AntTag
                className="vendor-tag"
                color={device.is_online ? "green" : "default"}
              >
                {device.name}
              </AntTag>
            </AntTooltip>
          );
        }
      });
    } else {
      return <span>-</span>;
    }
  }

  function checkAccessDashboard(row_data) {
    return (
      [44, 85, 21, 22, 102, 23].includes(row_data.category_id) ||
      (Array.isArray(row_data.devices) && row_data.devices.length > 0)
    );
  }
  function checkPollutionApplication(thingType) {
    console.log("thingType", thingType);
    return (
      parseInt(thingType) == 1 ||
      parseInt(thingType) == 21 ||
      parseInt(thingType) == 22 ||
      parseInt(thingType) == 102 ||
      parseInt(thingType) == 23
    );
  }

  function displayCustomerName(customer_name, row_value) {
    const { thing_goem_id, vendor_id, assetWithoutCustomer } = row_value;
    const { client_id, enabled_features } = context || {};

    if (thing_goem_id === client_id) {
      return assetWithoutCustomer ? "-" : "";
    }

    if (assetWithoutCustomer) {
      if (thing_goem_id && vendor_id !== client_id) {
        return "-";
      }

      if (!enabled_features?.includes("ThingManagement:EditCustomer")) {
        return "";
      }

      return (
        <span
          style={{
            color: "#374375",
            fontStyle: "italic",
            fontWeight: 500,
            cursor: "pointer",
            display: "inline-block",
            marginBottom: 6,
          }}
          onClick={() =>
            setLinkCustomerData({
              id: row_value.thing_id,
              name: row_value.thing_name,
              status: row_value.active_status,
              time: row_value.last_data_received_time,
              thing_category_icon: row_value.thing_category_icon,
              transparent_category_icon: row_value.transparent_category_icon,
              vendor_id: row_value.vendor_id,
              deviceStatus:
                row_value.devices?.[0]?.online_status === 1
                  ? "online"
                  : "offline",
            })
          }
        >
          Add Customer
        </span>
      );
    }

    return <div>{customer_name}</div>;
  }

  function checkUserAccess() {
    let {
      getViewAccess,
      logged_in_user_client_id,
      enabled_features,
      client_id,
    } = context || {};
    let accessObject = {
      addition:
        getViewAccess(["ThingManagement:Add"], true) ||
        logged_in_user_client_id === 1,
      configuration:
        getViewAccess(["ThingManagement:Edit"], true) ||
        logged_in_user_client_id === 1,
      delete: getViewAccess(["ThingManagement:Delete"], true),
      data_availability: getViewAccess(["Analytics:DataAvailability"]),
      deactivate:
        getViewAccess(["ThingManagement:Deactivate"]) &&
        (client_id === 1 ||
          enabled_features?.includes("ThingManagement:ThingDeactivate")),
    };
    return accessObject;
  }

  function renderDebugAndRawlog(row_value) {
    if (row_value.devices && row_value.devices.length) {
      return (
        <div>
          <AntTooltip title="Asset Debug">
            <Link
              to={
                "/" +
                context.app_name +
                "/things/" +
                row_value.thing_id +
                "/devices/" +
                row_value.devices[0]?.id +
                "/debug"
              }
              style={{
                verticalAlign: "middle",
                marginLeft: 10,
              }}
            >
              <img
                src={DebugIcon}
                width={25}
                className="configure-icon"
                height={23}
              />
            </Link>
          </AntTooltip>
          {(context.application_id == 12 || context.client_id == 1819) && (
            <AntTooltip title="Raw Log">
              <Link
                to={
                  "/" +
                  context.app_name +
                  "/things/" +
                  row_value.thing_id +
                  "/devices/" +
                  row_value.devices[0]?.id +
                  "/debug" +
                  "?tab=raw_log"
                }
                style={{
                  verticalAlign: "middle",
                  marginLeft: 10,
                }}
              >
                <img
                  src={raw_log_icon}
                  width={25}
                  className="configure-icon left-mar"
                  height={23}
                />
              </Link>
            </AntTooltip>
          )}
          {context.client_id === 1 ||
          context.enabled_features?.includes("ThingManagement:AssetData") ? (
            <AntTooltip title="Asset Data">
              <Link
                to={
                  "/" +
                  context.app_name +
                  "/assets/" +
                  row_value.thing_id +
                  "/customers/" +
                  row_value.customer_id +
                  "/asset-data"
                }
                style={{
                  verticalAlign: "middle",
                  marginLeft: 10,
                }}
              >
                <FileTextOutlined style={{ color: "#7686a1", fontSize: 20 }} />
              </Link>
            </AntTooltip>
          ) : (
            ""
          )}
        </div>
      );
    }
    return "";
  }

  function renderDashboardUrl(row_data) {
    const renderTooltipContent = () => {
      return checkAccessDashboard(row_data)
        ? "Open Dashboard"
        : "Device Not Configured";
    };

    const renderLink = () => {
      if (row_data.push_url && checkAccessDashboard(row_data)) {
        return (
          <Link to={row_data.push_url} target="_blank">
            <img
              src={DashboardIcon}
              className="configure-icon"
              width={25}
              height={23}
              alt="Dashboard"
            />
          </Link>
        );
      } else if (row_data.url !== "" && checkAccessDashboard(row_data)) {
        return (
          <a
            data-tip="View Analytics"
            href={row_data.url}
            target="_blank"
            rel="noopener noreferrer"
            className="proceed"
            currentitem="false"
          >
            <img
              src={DashboardIcon}
              className="configure-icon"
              width={25}
              height={23}
              alt="Analytics"
            />
          </a>
        );
      } else {
        return (
          <a
            data-tip="View Analytics"
            href="javascript:void(0);"
            className="proceed disabled"
            currentitem="false"
          >
            <img
              src={DashboardIcon}
              className="configure-icon disable"
              width={25}
              height={23}
              alt="Disabled"
            />
          </a>
        );
      }
    };

    return (
      <AntTooltip title={renderTooltipContent()}>{renderLink()}</AntTooltip>
    );
  }

  function checkMandatoryFields(details) {
    if (details && details.thing_details) {
      let thingDetails = details.thing_details;
      if (!thingDetails.kva && !thingDetails.name && !thingDetails.dg_type) {
        return true;
      }
    }
    return false;
  }

  function checkLatLong(details) {
    if (!details.longitude || !details.latitude) {
      return true;
    }
    return false;
  }

  function renderConfigureIcon(row_data) {
    const { customer_status, thing_id, customer_id, application_id } = row_data;

    // Render Inactive Customer Tooltip
    if (customer_status === "inactive") {
      return (
        <AntTooltip title="Customer Inactive">
          <SettingOutlined className="configure-icon disable" />
        </AntTooltip>
      );
    }

    // Check user access and return respective configuration options
    if (checkUserAccess() && checkUserAccess().configuration) {
      const platformPath = props.is_application_filter
        ? `/${context.app_name}/customer-management/${props.customer_id}/applications/16/things/${thing_id}/configuration/general`
        : `/${context.app_name}/customers/${customer_id}/applications/16/things/${thing_id}/configuration/general`;

      const iconSrc = checkMandatoryFields(row_data)
        ? ErrorConfigureIcon
        : checkLatLong(row_data)
          ? LatLongConfigure
          : ConfigureIcon;

      return (
        <AntTooltip title="Configure Asset">
          <Link to={platformPath} target={"_blank"}>
            <img
              src={iconSrc}
              className="configure-icon"
              width={25}
              height={23}
            />
          </Link>
        </AntTooltip>
      );
    }

    // Render Access Denied Tooltip
    const iconSrc = checkMandatoryFields(row_data)
      ? ErrorConfigureIcon
      : checkLatLong(row_data)
        ? LatLongConfigure
        : ConfigureIcon;

    return (
      <AntTooltip title="Access Denied">
        <img
          src={iconSrc}
          className="configure-icon disable"
          width={25}
          height={23}
        />
      </AntTooltip>
    );
  }

  async function setStatusThing(row_data) {
    let response;
    if (row_data.status === "inactive") {
      response = await activateThing({
        client_id: row_data.customer_id,
        application_id: row_data.application_id,
        thing_id: row_data.thing_id,
      });
    } else {
      response = await deactivateThing({
        client_id: row_data.customer_id,
        application_id: row_data.application_id,
        thing_id: row_data.thing_id,
      });
    }

    if (response.status === "success") {
      message.success("Asset status updated");
      fetchAssetList();
    } else {
      message.error(response.message);
    }
  }

  function onUpdateStatus(row_data) {
    let text = <span className="green">Active</span>,
      icon = ActiveIcon;
    if (row_data.status == "active") {
      text = <span className="red">Inactive</span>;
      icon = InactiveIcon;
    }
    AntConfirmModal({
      title: (
        <div>
          <img
            src={icon}
            width={25}
            height={row_data.status == "inactive" ? 30 : 25}
          />
          <span>{row_data.status == "inactive" ? "Active" : "Inactive"}</span>
        </div>
      ),
      content: (
        <div>
          <span>
            {"Are you sure you want to make " + row_data.thing_name + " "}
          </span>{" "}
          {text} <span>{" ?"}</span>
        </div>
      ),
      okText: row_data.status == "inactive" ? "Active" : "Inactive",
      cancelText: "Cancel",
      className: "thing-status-modal",
      onOk: () => setStatusThing(row_data),
    });
  }

  function renderAssetStatus(row_data) {
    return (
      <div>
        <AntTooltip
          title={row_data.status == "inactive" ? "Inactive" : "Active"}
        >
          <AntSwitch
            size="medium"
            checkedChildren="Active"
            unCheckedChildren="Inactive"
            checked={row_data.status == "inactive" ? false : true}
            onChange={(checked, event) =>
              onUpdateStatus(checked, event, row_data)
            }
          />
        </AntTooltip>
      </div>
    );
  }

  async function thingDelete(row_data) {
    let response = await deleteThing({
      client_id: row_data.customer_id,
      application_id: row_data.application_id,
      thing_id: row_data.thing_id,
    });

    if (response.status === "success") {
      message.success("Asset deleted successfully");
      fetchAssetList();
    } else {
      message.error(response.message);
    }
  }

  function onDeleteThing(row_data) {
    let deviceArr = [];
    if (row_data.devices && row_data.devices.length) {
      row_data.devices.map((device, index) => {
        if (index == 0) {
          deviceArr.push(
            <span className="orange">{" " + device.qr_code + " "}</span>,
          );
        } else if (index == row_data.devices.length - 1) {
          deviceArr.push(
            <span>
              {" and "} <span className="orange">{device.qr_code + " "}</span>
            </span>,
          );
        } else {
          deviceArr.push(
            <span>
              {", "}
              <span className="orange">{device.qr_code + " "}</span>
            </span>,
          );
        }
      });
    } else {
      deviceArr.push(<span> </span>);
    }
    AntConfirmModal({
      title: (
        <div>
          <span>Delete Asset</span>
        </div>
      ),
      content: (
        <div>
          <span>Are you sure you want to delete</span>
          <span>{" " + row_data.thing_name + " "}</span>
        </div>
      ),
      okText: "Delete",
      cancelText: "Cancel",
      className: "thing-delete-modal",
      onOk: () => thingDelete(row_data),
    });
  }

  function renderDeleteButton(row_data) {
    const { devices, status } = row_data;
    const deleteStyle = { fontSize: 19, color: "rgb(118, 134, 161)" };
    // Check if user has delete access and no devices are linked
    if (checkUserAccess() && checkUserAccess().delete && devices.length === 0) {
      return (
        <div>
          <AntTooltip title="Delete">
            <DeleteOutlined
              style={deleteStyle}
              className="thing-delete-butn"
              onClick={() => onDeleteThing(row_data)}
            />
          </AntTooltip>
        </div>
      );
    }

    // Determine the appropriate tooltip message
    let tooltipTitle = "You don't have access to it";

    if (devices.length > 0) {
      tooltipTitle = "Please delink device to delete";
    }

    if (status === "active" && devices.length > 0) {
      tooltipTitle = "Please delink device and deactivate asset to delete";
    }

    return (
      <div>
        <AntTooltip title={tooltipTitle}>
          <DeleteOutlined
            style={deleteStyle}
            className="thing-delete-butn disabled"
          />
        </AntTooltip>
      </div>
    );
  }

  function getDataPushPageUrl(row_data, server_type, class_name) {
    if (class_name === "greya") return "#";
    return `/${context.app_name}/things/${row_data.thing_id}/customers/${row_data.customer_id}/data-push-logs?server_type=${server_type}&asset_name=${row_data.thing_name}`;
  }

  const assetColumns = [
    {
      title: "Asset Name",
      dataIndex: "thing_name",
      key: "thing_name",
      width: "20%",
      pdf_title: "Asset Name",
      render: (_, row_value) => (
        <div className="back-color displ-flex">
          {[44, 85].includes(row_value.category_id) ? (
            <ImageComponent
              category={row_value.category_id}
              title={`Category: ${row_value.type_name}`}
              background={"white"}
              src={row_value.transparent_category_icon}
              status={row_value.active_status}
              show_status={true}
              tooltip={true}
              deviceStatus={row_value.active_status}
            />
          ) : (
            <ImageComponent
              category={row_value.category_id}
              title={`Category: ${row_value.type_name}`}
              tooltip={true}
              background={"white"}
              src={row_value.transparent_category_icon}
              show_status={true}
              status={row_value.active_status}
              deviceStatus={
                row_value.devices?.[0]?.online_status === 1
                  ? "online"
                  : "offline"
              }
            />
          )}
          <div className="wid-100">
            {(() => {
              if (row_value.status == "inactive") {
                return (
                  <div className="thing-name-container">
                    <div className="thing-name">
                      <AntTooltip title={row_value.thing_name}>
                        {row_value.thing_name + " "}
                      </AntTooltip>
                    </div>
                    <AntTooltip title="Inactive">
                      <StopOutlined className="red" />
                    </AntTooltip>
                  </div>
                );
              } else {
                return (
                  <div className="thing-name-container">
                    <div className="font-232323 thing-name">
                      <AntTooltip
                        overlayClassName={"thing_list_tooltip-1"}
                        title={
                          <div className="thing_name_tooltip_with_status-1">
                            {row_value?.is_iot_enabled ? (
                              <>
                                <span>
                                  {"Last data received time : " +
                                    getLastUpdatedTime(
                                      row_value.last_data_received_time,
                                    )}
                                </span>
                              </>
                            ) : (
                              ""
                            )}
                          </div>
                        }
                      >
                        {row_value.thing_name}
                      </AntTooltip>
                    </div>
                  </div>
                );
              }
            })()}
            <div className="font-808080">
              Added on:{" "}
              {row_value.created_on
                ? TimeFormatter(
                    context?.user_preferences?.time_format,
                    row_value.created_on,
                    "HH:mm, DD MMM YYYY",
                  )
                : "-"}
              {row_value.is_rental && showRentalTag() ? (
                <AntTooltip title="This Asset is for rent">
                  <span className="thing-rent-tag">Rent</span>
                </AntTooltip>
              ) : (
                ""
              )}
              {parseInt(row_value?.is_iot_enabled) === 0 ? (
                <span className="thing-rent-tag">Non-IoT</span>
              ) : (
                ""
              )}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "Devices",
      dataIndex: "devices",
      key: "devices",
      width: "9%",
      pdf_title: "Devices",
      render: (devices, row_value) => <div>{getDeviceNames(devices)}</div>,
    },
    {
      title: (
        <span
          className=""
          dangerouslySetInnerHTML={{
            __html: "Customer Name",
          }}
        />
      ),
      dataIndex: "customer_name",
      align: "center",
      key: "customer_name",
      width: "18%",
      render: (customer_name, row_value) => (
        <div style={{ textAlign: "center" }}>
          {displayCustomerName(customer_name, row_value)}
          {(() => {
            if (
              row_value.vendor_id !== context.client_id ||
              context.client_id === 1 ||
              context.enabled_features?.includes("IndustryManagement:Dealers")
            ) {
              return (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <AntTag
                    color={
                      context.client_id !== 1 &&
                      context.client_id !== row_value.vendor_id
                        ? "orange"
                        : "geekblue"
                    }
                    className="vendor-tag"
                  >
                    {row_value.primary_vendor_name
                      ? row_value.primary_vendor_name
                      : row_value.thing_goem_id !== context.client_id &&
                          row_value.goem_name
                        ? row_value.goem_name
                        : row_value.vendor_name}
                  </AntTag>
                  {row_value.primary_vendor_name ||
                  (row_value.thing_goem_id !== context.client_id &&
                    row_value.goem_name) ? (
                    <HeirarchyPopover
                      items={[
                        row_value.assetWithoutCustomer ? "-" : customer_name,
                        row_value.vendor_name,
                        row_value.goem_name,
                        row_value.primary_vendor_name,
                      ]}
                    />
                  ) : (
                    ""
                  )}
                </div>
              );
            }
          })()}
        </div>
      ),
    },
    {
      title: "Action",
      key: "action",
      width: "20%",
      align: "center",
      pdf_title: "Status",
      render: (_, row_data) => (
        <div style={{ display: "flex", gap: 10 }}>
          <div>{renderDebugAndRawlog(row_data)}</div>
          <div>{renderConfigureIcon(row_data)}</div>
          <div>{renderDashboardUrl(row_data)}</div>
          <div>{renderDeleteButton(row_data)}</div>
          <div>{renderAssetStatus(row_data)}</div>
        </div>
      ),
    },
    {
      title: "Data Availability (%)(Last 24 Hours)",
      dataIndex: "data_availability",
      key: "data_availability",
      width: "8%",
      align: "center",
      pdf_title: "Data Availability (%)(Last 24 Hours)",
      render: (data_availability, row_value) => (
        <AntTooltip
          title={
            "Last 24 Hours Data Availability" +
            (data_availability && data_availability != null
              ? " - " + data_availability + "%"
              : "")
          }
        >
          <AntProgress
            className="percent-icon"
            type="circle"
            percent={
              data_availability && data_availability != null
                ? data_availability
                : 0
            }
            width={40}
            strokeColor="#f58740"
            format={(percent) => `${percent}`}
          />
        </AntTooltip>
      ),
    },
    {
      title: "Subscribed On",
      dataIndex: "subscribed_on",
      key: "subscribed_on",
      width: "8%",
      align: "center",
      pdf_title: "Subscribed On",
    },
    {
      title: "Other Status",
      key: "other_status",
      width: "12%",
      align: "center",
      pdf_title: "Other Status",
      render: (a, row_data) => (
        <div className={"tm_tl_other_status_cont"}>
          {(() => {
            if (checkPollutionApplication(row_data.category_id)) {
              let cpcb_data_push_details =
                row_data?.thing_details?.cpcb_data_push_details;
              let spcb_data_push_details =
                row_data?.thing_details?.spcb_data_push_details;
              const isDpccPushEnabled =
                parseInt(spcb_data_push_details?.server_type) === 13;
              const getSpcbClass = () => {
                let cn = "greya";
                if (
                  spcb_data_push_details &&
                  parseInt(spcb_data_push_details.server_type) > 0 &&
                  parseInt(spcb_data_push_details.server_type) !== 13
                ) {
                  if (row_data.spcb_status == 1) {
                    cn = "greena";
                  } else if (row_data.spcb_status == 0) {
                    cn = "reda";
                  } else if (row_data.spcb_status == 2) {
                    cn = "yellowa";
                  }
                }
                return cn;
              };
              const getCpcbClass = () => {
                let cn = "greya";
                if (
                  cpcb_data_push_details &&
                  cpcb_data_push_details.push_data
                ) {
                  if (row_data.cpcb_status == 1) {
                    cn = "greena";
                  } else if (row_data.cpcb_status == 0) {
                    cn = "reda";
                  } else if (row_data.cpcb_status == 2) {
                    cn = "yellowa";
                  }
                }
                return cn;
              };
              const getDMDClass = () => {
                let cn = "grey";
                if (row_data.dmd_status == 1) {
                  cn = "green";
                } else if (row_data.dmd_status == 0) {
                  cn = "red";
                }
                return cn;
              };
              console.log("SPCB");
              return (
                <>
                  {isDpccPushEnabled ? (
                    <AntTooltip title="Click to view DPCC logs">
                      <Link
                        to={getDataPushPageUrl(row_data, "dpcc")}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <div
                          className={`cpcb-link`}
                          style={{
                            border: "1px solid #ff8500",
                            color: "#232323",
                            background: "white",
                          }}
                        >
                          D
                        </div>
                      </Link>
                    </AntTooltip>
                  ) : (
                    <Link
                      to={getDataPushPageUrl(row_data, "ospcb", getSpcbClass())}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <div
                        className={`spcb-link ${getSpcbClass()} ${
                          getSpcbClass() == "greya" ? "spcb_link-disabled" : ""
                        }`}
                      >
                        S{" "}
                        {getSpcbClass() !== "greya"
                          ? spcb_data_push_details?.server_type
                          : ""}
                        {getSpcbClass() !== "greya"
                          ? spcb_data_push_details?.data_push_medium == 0
                            ? "s"
                            : "d"
                          : ""}
                      </div>
                    </Link>
                  )}
                  <Link
                    to={getDataPushPageUrl(row_data, "cpcb", getCpcbClass())}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <div className={`cpcb-link ${getCpcbClass()}`}>C</div>
                  </Link>
                  <AntTooltip
                    title={
                      getDMDClass() == "grey"
                        ? "DMD Not Installed"
                        : "DMD Status: " +
                          (getDMDClass() == "green" ? "ONLINE" : "OFFLINE")
                    }
                  >
                    <div className={`dmd-link ${getDMDClass() + "-dmd"}`}>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 329 329"
                        height={20}
                        width={20}
                      >
                        <path d="M329 240V21.5H0V240h139.3v31.2h-50L75 307.4h179l-14.5-36.2h-50V240zM29.5 52.3h269.7V209H29.6V52.4z" />
                        <rect
                          className="fill"
                          fill="none"
                          width="249.5"
                          height="135.2"
                          x="40.4"
                          y="62.7"
                        />
                      </svg>
                    </div>
                  </AntTooltip>
                </>
              );
            }
          })()}
        </div>
      ),
    },
  ];

  return {
    filterData,
    pageLoading,
    resetDependentFields,
    filterRef,
    fetchAssetList,
    dataSource,
    pagination,
    assetColumns,
    onTableChange,
    linkCustomerData,
    setLinkCustomerData,
  };
};
