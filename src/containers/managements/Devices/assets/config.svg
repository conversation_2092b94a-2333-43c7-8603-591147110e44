<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="438.741" height="378.969" viewBox="0 0 438.741 378.969">
  <defs>
    <linearGradient id="linear-gradient" x1="10.702" y1="-1.634" x2="11.406" y2="-1.634" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f5841f"/>
      <stop offset="1" stop-color="#ef6052"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="-6.544" y1="-9.742" x2="-5.515" y2="-9.742" xlink:href="#linear-gradient"/>
  </defs>
  <g id="Group_6731" data-name="Group 6731" transform="translate(-10356.371 21307.969)">
    <g id="Group_6730" data-name="Group 6730" transform="translate(10214.426 -21554.539)">
      <path id="Path_3280" data-name="Path 3280" d="M141.945,724.181h49.5l-.1,1.753,111.993.2,29.225-.2-.068-2.363,57,2.347.011.922h23.587v-2.659H580.686" transform="translate(0 -178.263)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
      <g id="Group_6723" data-name="Group 6723" transform="translate(164.053 545.713)">
        <path id="Path_3281" data-name="Path 3281" d="M577.676,736.506c5.789,11.79,2.245,30.51-76.7,32.126-71.28,1.452-103.376-4.7-117.52-9.656a113.05,113.05,0,0,0-34.56-6.234c-195.2-4.941-169.6-28.509-169.6-28.509l24.618.006h2.281l-.018,1.958H318.157l.207-1.946,252.175.043h13.267l-.226.043A8.5,8.5,0,0,0,577.676,736.506Z" transform="translate(-178.19 -724.234)" fill="#bab9b9"/>
        <path id="Path_3282" data-name="Path 3282" d="M582.068,732.729c3.184,6.508-4.892,15.34-70.627,16.164-64.418.805-93.543-2.58-106.438-5.331a156.094,156.094,0,0,0-30.187-3.471c-176.082-2.666-155.643-15.511-155.088-15.847h2.281l-.018,1.958H333.974l.207-1.946,252.175.043A5.8,5.8,0,0,0,582.068,732.729Z" transform="translate(-194.006 -724.238)" fill="gray" opacity="0.56"/>
      </g>
      <path id="Path_3283" data-name="Path 3283" d="M562.626,553.186l-223.952.061-.191,1.788H226.5l.016-1.758-26.5.007a1.557,1.557,0,0,1-1.558-1.557v-.8a121.428,121.428,0,0,1,1.092-16.283,85.827,85.827,0,0,0-9.211-51.191,62.2,62.2,0,0,1,20.011-80.04,126.955,126.955,0,0,0,41.1-47,130.169,130.169,0,0,1,136.787-69.375c50.88,7.912,99.633,56.656,107.542,107.537a135.5,135.5,0,0,1,1.6,23.33,49.11,49.11,0,0,0,30.118,46.032,132.485,132.485,0,0,1,57.364,46.647C597.307,528.607,584.528,553.181,562.626,553.186Z" transform="translate(-16.134 -7.359)" fill="#fdd9b2"/>
      <path id="Path_3284" data-name="Path 3284" d="M550.294,553.181l-241.669.066-.19,1.788H196.452l.016-1.758-26.5.007a1.556,1.556,0,0,1-1.557-1.557v-.8a121.428,121.428,0,0,1,1.092-16.283,85.827,85.827,0,0,0-9.212-51.191,62.2,62.2,0,0,1,20.011-80.04,126.962,126.962,0,0,0,41.105-47A130.168,130.168,0,0,1,358.2,287.031c50.88,7.912,99.633,56.656,107.542,107.537a135.551,135.551,0,0,1,1.6,23.33,49.112,49.112,0,0,0,30.119,46.033,131.78,131.78,0,0,1,68.058,65.323A16.815,16.815,0,0,1,550.294,553.181Z" transform="translate(-4.414 -7.359)" fill="#f2f2f2"/>
      <rect id="Rectangle_2071" data-name="Rectangle 2071" width="54.416" height="1.208" rx="0.604" transform="translate(420.856 364.223)" fill="gray"/>
      <rect id="Rectangle_2072" data-name="Rectangle 2072" width="43.35" height="1.208" rx="0.604" transform="translate(400.977 356.786)" fill="gray"/>
      <rect id="Rectangle_2073" data-name="Rectangle 2073" width="90.058" height="0.986" rx="0.493" transform="translate(435.015 349.581)" fill="gray"/>
      <g id="Group_6724" data-name="Group 6724" transform="translate(164.053 328.814)">
        <path id="Path_3285" data-name="Path 3285" d="M303.118,582.456,200.712,600.632a4.038,4.038,0,0,1-4.682-4.248c1.04-14.348,2.066-55.374-16.982-81.765a4.334,4.334,0,0,1,.886-6.076c4.487-3.243,13-9.267,24.156-16.542l3.37,3.37c4.079-2.6,17.567-10.771,22.22-13.587l-2.837-4.953c15.536-9.2,37.673-19.408,56.186-27.468,6.217-2.706,13.548-1.951,15.439,4.72.7,2.459,5.034,29.081,9.634,53.014,5.046,26.251,6.528,54.245,6.931,60.005A14.447,14.447,0,0,1,303.118,582.456Z" transform="translate(-178.19 -399.553)" fill="url(#linear-gradient)"/>
        <path id="Path_3286" data-name="Path 3286" d="M225.481,443.162l-3.791,2.661-18.13-72.415,4.194-1.05Z" transform="translate(-188.085 -370.088)" fill="gray"/>
        <path id="Path_3287" data-name="Path 3287" d="M203.673,371.794l3.072-3a.093.093,0,0,0-.087-.156l-2.447.609a.092.092,0,0,0-.067.066l-.626,2.388A.093.093,0,0,0,203.673,371.794Z" transform="translate(-188.067 -368.637)" fill="#232323"/>
        <path id="Path_3288" data-name="Path 3288" d="M432.659,600.78H409.072l-.323-9.064a5.458,5.458,0,0,0-5.484-5.258l-45.783.281a5.455,5.455,0,0,0-5.423,5.52l.1,8.521H211.2s1.549-77.031-20.129-123.913c0,0,12.026-9.716,30.5-22.544a54.517,54.517,0,0,0,2.855,5.094c4.473-3.033,19.187-12.447,24.194-15.649-2.026-3.058-1.5-2.162-3.5-5.324,20.467-12.767,46.779-27.146,69.848-35.759L447.371,429.2S430.335,528.439,432.659,600.78Z" transform="translate(-183.212 -381.917)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
        <path id="Path_3289" data-name="Path 3289" d="M511.884,600.78H488.3l-.323-9.064a5.458,5.458,0,0,0-5.484-5.258l-45.783.281a5.455,5.455,0,0,0-5.423,5.52l.1,8.521H402.4c7.271-65.357-8.216-198.1-8.216-198.1L526.6,429.2S509.56,528.439,511.884,600.78Z" transform="translate(-262.438 -381.917)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
        <path id="Path_3290" data-name="Path 3290" d="M455.007,716.5v-6.725a5.932,5.932,0,0,1,4.827-5.483l46.379-.317c5.462.411,5.484,5.258,5.484,5.258l.334,9.346Z" transform="translate(-286.16 -499.433)" fill="#232323"/>
        <path id="Path_3291" data-name="Path 3291" d="M223.761,689.86l114.04-17.8,107.5,2.988" transform="translate(-195.964 -486.983)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
        <path id="Path_3292" data-name="Path 3292" d="M228.166,550.048s4.951,18.083,7.75,54.9l25.188-6.889s-2.8-48.007-8.4-61.57Z" transform="translate(-197.682 -434.105)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
        <path id="Path_3293" data-name="Path 3293" d="M232.754,555.5s4.11,15.6,6.433,47.348l20.908-5.942s-2.323-41.406-6.969-53.1Z" transform="translate(-199.472 -436.958)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
        <path id="Path_3294" data-name="Path 3294" d="M311.093,506.057s5.895,21.529,9.227,65.358l29.988-8.2s-3.332-57.155-10-73.3Z" transform="translate(-230.028 -415.939)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
        <path id="Path_3295" data-name="Path 3295" d="M316.555,512.545s4.894,18.57,7.659,56.371l24.892-7.074s-2.766-49.3-8.3-63.224Z" transform="translate(-232.158 -419.335)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
        <path id="Path_3296" data-name="Path 3296" d="M251.567,487.424l-10.5-.08,2.854,5.094Z" transform="translate(-202.716 -414.938)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
        <ellipse id="Ellipse_933" data-name="Ellipse 933" cx="4.168" cy="8.18" rx="4.168" ry="8.18" transform="translate(180.024 107.559)" fill="gray"/>
        <ellipse id="Ellipse_934" data-name="Ellipse 934" cx="4.168" cy="8.18" rx="4.168" ry="8.18" transform="translate(217.055 110.356)" fill="gray"/>
        <path id="Path_3297" data-name="Path 3297" d="M443.139,459.324s7.174-13.413,28.074-12.477v-6.239S451.561,442.48,443.139,459.324Z" transform="translate(-281.531 -396.709)" fill="gray"/>
        <path id="Path_3298" data-name="Path 3298" d="M562.264,470.011s-3.823-12.74-21.9-15.561l1.081-5.347S557.966,454.114,562.264,470.011Z" transform="translate(-319.454 -400.022)" fill="gray"/>
        <path id="Path_3299" data-name="Path 3299" d="M459.44,598.671s22.451-14.061,49.362,2.344" transform="translate(-287.889 -456.173)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="1"/>
        <line id="Line_77" data-name="Line 77" y1="0.98" transform="translate(110.276 164.908)" fill="#fff"/>
      </g>
      <g id="Group_6727" data-name="Group 6727" transform="translate(400.83 431.322)">
        <path id="Path_3300" data-name="Path 3300" d="M717.524,699.824l-.362-2.016a111.225,111.225,0,0,1,.362-41.192h0l56.7,9.418-.093,1.147a195.691,195.691,0,0,0,.093,32.644h-56.7Z" transform="translate(-624.508 -583.468)" fill="gray"/>
        <path id="Path_3301" data-name="Path 3301" d="M568.273,699.824h0a123.529,123.529,0,0,1-.3-41.344l.3-1.865h91.119l-.164.97a123.554,123.554,0,0,0,0,41.268l.164.97Z" transform="translate(-566.377 -583.468)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
        <path id="Path_3302" data-name="Path 3302" d="M663.885,647.078l16.446-13.017h96.779l-1.876.886A49.169,49.169,0,0,0,752.729,656.5h0l-.239,3.078h2.06l.847-1.46A49.171,49.171,0,0,1,777.4,638.1l3.292-1.512L779,632.163H678.223L660.3,647.078Z" transform="translate(-603.011 -573.931)" fill="#232323"/>
        <path id="Path_3303" data-name="Path 3303" d="M629.527,666.033h-5.873a18.771,18.771,0,0,1,0-9.418h5.873a18.785,18.785,0,0,0,0,9.418Z" transform="translate(-588.483 -583.468)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
        <rect id="Rectangle_2074" data-name="Rectangle 2074" width="2.549" height="11.038" transform="translate(26.559 42.178) rotate(-7.322)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
        <g id="Group_6725" data-name="Group 6725" transform="translate(20.47 34.031)">
          <path id="Path_3304" data-name="Path 3304" d="M640.6,647.413h-5.71l-1.756-13.669,5.617-.722Z" transform="translate(-612.886 -608.297)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
          <rect id="Rectangle_2075" data-name="Rectangle 2075" width="11.86" height="5.231" transform="translate(16.512 20.654) rotate(-7.322)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
          <rect id="Rectangle_2076" data-name="Rectangle 2076" width="5.868" height="15.33" transform="translate(0 6.765) rotate(-7.322)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
          <path id="Path_3305" data-name="Path 3305" d="M644.906,592.608l-6.089.783a.985.985,0,0,0-.851,1.1h0l-24.459,3.142,2.093,16.294,24.46-3.143h0a2.7,2.7,0,0,1,2.329-3.016l18.99-2.44A14.718,14.718,0,0,0,644.906,592.608Z" transform="translate(-605.229 -592.487)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
        </g>
        <path id="Path_3306" data-name="Path 3306" d="M696.97,561.021a20.806,20.806,0,0,1-16.017,16.444l-6.227,32.375H657.418l6.74-35.967a20.808,20.808,0,0,1-8.079-20.629,21.058,21.058,0,0,1,17.05-16.551l-3.749,18.615,4.582,6.814,3.177.6,6.752-4.595,3.559-18.715A20.816,20.816,0,0,1,696.97,561.021Z" transform="translate(-601.22 -536.694)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
        <g id="Group_6726" data-name="Group 6726" transform="translate(81.039 0.032)">
          <path id="Path_3307" data-name="Path 3307" d="M738.794,549.923l-.065-12.246-3.546-.931-6.856,10.475-.7,6.814a1.269,1.269,0,0,0,.94,1.357h0a.863.863,0,0,1,.615,1.054l-12.377,47.165,4.322,1.134,12.377-47.165a.863.863,0,0,1,1.054-.615h0a1.27,1.27,0,0,0,1.486-.722Z" transform="translate(-706.09 -536.746)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
          <path id="Path_3308" data-name="Path 3308" d="M724.184,639.42l-1.714,9.811-2.58-.427-1.006-.171-3.5-.579-1.013-.171-3.16-.525h-11.98l3.4-13.19a1.713,1.713,0,0,1,2.072-1.235l18.2,4.53A1.713,1.713,0,0,1,724.184,639.42Z" transform="translate(-699.237 -574.243)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
          <path id="Path_3309" data-name="Path 3309" d="M720.064,640.24l-2.214,9.9h-1.043l2.263-10.125Z" transform="translate(-706.09 -577.025)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
          <path id="Path_3310" data-name="Path 3310" d="M727.216,641.823l-2.147,9.631-1.012-.171,2.165-9.68Z" transform="translate(-708.918 -577.645)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
          <path id="Path_3311" data-name="Path 3311" d="M712.846,638.289l-2.477,11.089h-1.043l2.525-11.315Z" transform="translate(-703.172 -576.264)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
          <path id="Path_3312" data-name="Path 3312" d="M706.4,636.879l-2.665,11.949h-1.049l2.72-12.175Z" transform="translate(-700.583 -575.714)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
          <path id="Path_3313" data-name="Path 3313" d="M734.552,643.453l-2.1,9.387-1.006-.171,2.11-9.436Z" transform="translate(-711.8 -578.281)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
        </g>
      </g>
      <g id="Group_6728" data-name="Group 6728" transform="translate(285.292 266.538)">
        <path id="Path_3314" data-name="Path 3314" d="M388.475,329.715h0a4.506,4.506,0,0,1-4.914-3.939l-6.572-53.938a4.507,4.507,0,0,1,4.033-5.03l2.528-.248a4.507,4.507,0,0,1,4.934,4.15l4.044,54.186A4.506,4.506,0,0,1,388.475,329.715Z" transform="translate(-376.956 -266.538)" fill="#bab9b9"/>
        <rect id="Rectangle_2077" data-name="Rectangle 2077" width="11.765" height="11.765" rx="5.883" transform="translate(5.979 66.959) rotate(-5.608)" fill="#bab9b9"/>
      </g>
      <g id="Group_6729" data-name="Group 6729" transform="translate(308.05 301.569)">
        <path id="Path_3315" data-name="Path 3315" d="M418.03,358.49h0a2.455,2.455,0,0,1-2.417-2.436l-.238-29.608a2.456,2.456,0,0,1,2.494-2.475l1.384.021a2.455,2.455,0,0,1,2.415,2.55l-1.146,29.587A2.455,2.455,0,0,1,418.03,358.49Z" transform="translate(-414.7 -323.971)" fill="#bab9b9"/>
        <rect id="Rectangle_2078" data-name="Rectangle 2078" width="6.411" height="6.411" rx="3.206" transform="matrix(1, 0.015, -0.015, 1, 0.098, 36.226)" fill="#bab9b9"/>
      </g>
      <path id="Path_3316" data-name="Path 3316" d="M527.693,228.437c-7.7-17.987-1.92-39.012,16.067-46.717a35.431,35.431,0,0,1,46.518,18.62c7.7,17.988,1.92,39.013-16.067,46.717" transform="translate(-14.386 -107.702) rotate(21)" fill="none" stroke="#232323" stroke-linecap="round" stroke-miterlimit="10" stroke-width="0.75" stroke-dasharray="8"/>
      <path id="Path_3317" data-name="Path 3317" d="M573.214,236.967l-1.569-3.662a20.371,20.371,0,0,0,5.831-5.9l3.735,1.5c.048.022.1.041.145.058a1.636,1.636,0,0,0,2.08-1.01h0l1.794-4.483a1.375,1.375,0,0,0,.058-.145,1.635,1.635,0,0,0-1.011-2.08l-3.735-1.5a21.365,21.365,0,0,0-.149-8.29l3.661-1.568.123-.052a1.642,1.642,0,0,0,.774-2.19l-1.846-4.31-.052-.123a1.64,1.64,0,0,0-2.188-.774h0l-3.661,1.569a20.707,20.707,0,0,0-5.841-5.7l1.5-3.735c.023-.048.041-.1.058-.145a1.637,1.637,0,0,0-1.011-2.081l-4.483-1.794c-.048-.022-.1-.041-.145-.057a1.635,1.635,0,0,0-2.08,1.01l-1.5,3.735a21.37,21.37,0,0,0-8.291.152l-1.569-3.663-.053-.123a1.641,1.641,0,0,0-2.189-.774l-4.311,1.846-.123.052a1.643,1.643,0,0,0-.774,2.189l1.569,3.663a20.378,20.378,0,0,0-5.828,5.9l-3.736-1.495a1.4,1.4,0,0,0-.145-.058,1.635,1.635,0,0,0-2.08,1.011l-1.794,4.483a1.486,1.486,0,0,0-.058.145,1.636,1.636,0,0,0,1.01,2.08h0l3.735,1.5a22.258,22.258,0,0,0,.092,8.157l-3.661,1.569q-.063.024-.123.052a1.642,1.642,0,0,0-.774,2.189h0l1.846,4.311.053.123a1.641,1.641,0,0,0,2.189.774l3.662-1.569a20.384,20.384,0,0,0,5.9,5.828l-1.5,3.735a1.347,1.347,0,0,0-.057.145,1.635,1.635,0,0,0,1.01,2.081h0l4.482,1.79a1.393,1.393,0,0,0,.145.058,1.635,1.635,0,0,0,2.08-1.01h0l1.5-3.735a21.365,21.365,0,0,0,8.29-.149l1.568,3.661.053.123a1.642,1.642,0,0,0,2.189.775l4.31-1.846.123-.053a1.641,1.641,0,0,0,.773-2.186m-9.7-12.09.023-.01a9.566,9.566,0,1,1,.145-.062l-.166.071Z" transform="translate(-15.291 -109.648) rotate(21)" opacity="0.8" fill="url(#linear-gradient-2)"/>
    </g>
    <text id="Please_enter_all_the_mandatory_field_properly_to_configure_the_thing" data-name="Please enter all the mandatory field properly to configure the thing" transform="translate(10374.371 -20933)" fill="#d6c6b3" font-size="14" font-family="Roboto-Italic, Roboto" font-style="italic"><tspan x="0" y="0">Please enter all the mandatory field properly to configure the asset</tspan></text>
  </g>
</svg>
