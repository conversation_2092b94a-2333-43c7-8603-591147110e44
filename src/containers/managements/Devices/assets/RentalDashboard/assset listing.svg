<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="482.36" height="418.394" viewBox="0 0 482.36 418.394">
  <defs>
    <linearGradient id="linear-gradient" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f5841f"/>
      <stop offset="1" stop-color="#ef6052"/>
    </linearGradient>
  </defs>
  <g id="Group_6277" data-name="Group 6277" transform="translate(-697.319 -356.606)">
    <text id="No_asset_is_added_for_rent" data-name="No asset is added for rent" transform="translate(859.323 771)" fill="#9f9080" font-size="14" font-family="Roboto-Italic, Roboto" font-style="italic"><tspan x="0" y="0">No asset is added for rent</tspan></text>
    <g id="Group_6040" data-name="Group 6040" transform="translate(562.26 144.836)">
      <g id="Layer_4" data-name="Layer 4" transform="translate(203.87 293.157)">
        <g id="Group_6035" data-name="Group 6035">
          <path id="Path_2594" data-name="Path 2594" d="M239.789,406.169l-4.72,8.361-3.294-84.169,5.039-.2Z" transform="translate(-231.775 -327.318)" fill="gray"/>
          <path id="Path_2595" data-name="Path 2595" d="M231.971,329.052l4.224-2.688a.108.108,0,0,0-.063-.2l-2.938.112a.108.108,0,0,0-.093.06l-1.285,2.577A.108.108,0,0,0,231.971,329.052Z" transform="translate(-231.783 -326.164)" fill="#232323"/>
        </g>
      </g>
      <g id="Layer_5" data-name="Layer 5" transform="translate(135.064 211.77)">
        <path id="Path_2596" data-name="Path 2596" d="M154.281,673.152s-7.707,22.326,150.978,23.722,155.892,1.861,165.665,13.96,129.368,10.238,108.892-12.1,15.357-26.392,15.357-26.392l-286.282.809-.391,4.876-39.278.029-47.084-5.232Z" transform="translate(-140.591 -344.664)" fill="#bab9b9"/>
        <path id="Path_2597" data-name="Path 2597" d="M182.895,672.849s-15.525,20.172,175.45,12.975c128.663-4.849,117.9,1.023,125.291,7.672s97.839,5.625,82.354-6.649,11.615-14.5,11.615-14.5l-260.527.6-.391,3.6-39.278.021L230.323,672.7Z" transform="translate(-148.777 -344.664)" fill="gray" opacity="0.56"/>
        <path id="Path_2598" data-name="Path 2598" d="M619.011,519.991c0,.071,0,.142,0,.213a20.317,20.317,0,0,1-20.39,20.114H299.111a2.282,2.282,0,0,0-2.251,1.907l-.144.867a2.281,2.281,0,0,1-2.5,1.892l-44.806-4.979a2.166,2.166,0,0,0-.263-.014l-65.634.317a2.279,2.279,0,0,1-2.287-2.07q-.465-5.4-.465-10.925a126.726,126.726,0,0,1,73.419-114.988,2.293,2.293,0,0,0,1.111-3.071A135.841,135.841,0,0,1,242.2,353.1c-1.251-76.856,61.916-140,138.771-138.724a136.479,136.479,0,0,1,134.156,136.46q0,6.262-.555,12.385a62.234,62.234,0,0,0,22.749,53.466,120.569,120.569,0,0,1,20.517,21.67A99.853,99.853,0,0,0,587.545,464.7,64.252,64.252,0,0,1,619.011,519.991Z" transform="translate(-148.248 -212.516)" fill="#fdd9b2"/>
        <path id="Path_2599" data-name="Path 2599" d="M591.627,520.956v.213c-.036,11.154-9.193,19.355-20.346,19.355l-301.487.758-.817,4.9-47.084-5.232-67.856.327a126.9,126.9,0,0,1,27.648-92.838A148.244,148.244,0,0,0,214.8,353.357q-.009-.8-.009-1.609a136.472,136.472,0,0,1,272.944.052q0,5.009-.356,9.931c-1.552,21.564,6.863,42.752,23.426,56.647a120.524,120.524,0,0,1,19.642,20.943,99.85,99.85,0,0,0,29.71,26.346A64.25,64.25,0,0,1,591.627,520.956Z" transform="translate(-140.347 -212.794)" fill="#f2f2f2"/>
        <path id="Path_2600" data-name="Path 2600" d="M221.631,445.773l14.9-25.336v6.233l12.465-17.916-1.461-7.031,21.618-36.294s27.913,130.763,18.542,167.219a5.37,5.37,0,0,1-2.826,3.468l-46.92,23.209a2.548,2.548,0,0,1-3.679-2.366c.467-14.266.763-61.716-13.372-104.343A8.33,8.33,0,0,1,221.631,445.773Z" transform="translate(-159.709 -256.107)" fill="url(#linear-gradient)"/>
        <path id="Path_2601" data-name="Path 2601" d="M135.064,663.378l81.546-.395,47.084,5.548,39.278-.345,1.693-29.581,104.084-.37,3.167,29.871,38.871-.028-1.544-5.28,168.173-.384" transform="translate(-135.064 -334.823)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="2"/>
        <g id="Group_6036" data-name="Group 6036" transform="translate(65.19 59.5)">
          <path id="Path_2602" data-name="Path 2602" d="M480.172,668.108l-108.553-4.8,1.407-25.074,103.979.007,3.064,28.816" transform="translate(-268.902 -394.323)" fill="#232323"/>
          <path id="Path_2603" data-name="Path 2603" d="M438.355,578.566l38.871-.029-.6-2.028a208.338,208.338,0,0,1-8.4-69.969l9.007-178.51-195.094-.123L246.407,391.2l.473,5.5L240.1,406.738l-.343-3.754-13.06,23.136,7.966,33.416a371.746,371.746,0,0,1,10.283,83.338c.095,12.421-.413,23.718-1.895,30.568l47.084,5.232" transform="translate(-226.693 -304.781)" fill="none" stroke="#232323" stroke-miterlimit="10" stroke-width="1"/>
          <line id="Line_113" data-name="Line 113" y1="0.029" x2="39.278" transform="translate(63.439 273.864)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="1"/>
          <path id="Path_2604" data-name="Path 2604" d="M499.71,569.158l-.6-2.028a208.337,208.337,0,0,1-8.4-69.969l9.007-178.51h0a214.67,214.67,0,0,0-182.273-5.66l-12.821,5.537,12.271,165.624a344.041,344.041,0,0,1-3.818,82.45l-.452,2.691,39.278-.029.948-16.571a13.847,13.847,0,0,1,13.776-13.056l78.552-.279a13.847,13.847,0,0,1,13.819,12.387l1.849,17.439Z" transform="translate(-249.177 -295.401)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
          <path id="Path_2605" data-name="Path 2605" d="M252.562,641.757l49.665-19.539H476.584" transform="translate(-234.157 -389.701)" fill="none" stroke="#232323" stroke-miterlimit="10" stroke-width="1"/>
          <path id="Path_2606" data-name="Path 2606" d="M647.731,527.9" transform="translate(-348.179 -362.485)" fill="none" stroke="#232323" stroke-miterlimit="10" stroke-width="1"/>
        </g>
        <path id="Path_2607" data-name="Path 2607" d="M235.256,468.787l9.733-16.1s19.242,84.058,18.432,110.187L250.66,573.3S254.7,559.935,235.256,468.787Z" transform="translate(-163.973 -281.284)" fill="none" stroke="#232323" stroke-miterlimit="10" stroke-width="1"/>
        <path id="Path_2608" data-name="Path 2608" d="M264.994,418.631,276.555,399.5s22.858,99.851,21.9,130.889L283.291,542.78S288.092,526.9,264.994,418.631Z" transform="translate(-172.554 -265.938)" fill="none" stroke="#232323" stroke-miterlimit="10" stroke-width="1"/>
        <path id="Path_2609" data-name="Path 2609" d="M238.01,472.435l7.161-11.084s15.91,77.351,16.164,100.809l-8.426,7.475S254.433,554.263,238.01,472.435Z" transform="translate(-164.768 -283.784)" fill="none" stroke="#232323" stroke-miterlimit="10" stroke-width="1"/>
        <path id="Path_2610" data-name="Path 2610" d="M268.618,421.929l6.833-11.707s20.243,91.877,22.016,120.866l-9.417,7.682S290.342,523.055,268.618,421.929Z" transform="translate(-173.6 -269.031)" fill="none" stroke="#232323" stroke-miterlimit="10" stroke-width="1"/>
        <path id="Path_2611" data-name="Path 2611" d="M248,433.282l-2.951.106.343,3.754Z" transform="translate(-166.799 -275.685)" fill="gray"/>
        <ellipse id="Ellipse_850" data-name="Ellipse 850" cx="6.37" cy="12.051" rx="6.37" ry="12.051" transform="translate(175.714 133.565)" fill="gray"/>
        <path id="Path_2612" data-name="Path 2612" d="M324.254,362.9s14.528-18.848,58.113,17.67c0,0,11.78,4.319,14.136-4.319,0,0-7.068,13.1-23.167-6.407S336.819,347.585,324.254,362.9Z" transform="translate(-189.653 -252.538)" fill="gray"/>
        <path id="Path_2613" data-name="Path 2613" d="M523.063,391.531s-21.813-17.605-56.091,19.1a10.241,10.241,0,0,1-8.578,3.166l-5.793-.619,2.745,1.846a7.825,7.825,0,0,0,9.435-.531h0C475.226,406.02,501.8,386.831,523.063,391.531Z" transform="translate(-226.686 -262.421)" fill="gray"/>
        <path id="Path_2614" data-name="Path 2614" d="M499.126,424.531S478.813,413.5,466.905,429.7c0,0,17.648-7.148,39.745,9.676,0,0-15.819-14.4-35.071-12.3C471.579,427.071,481.474,416.97,499.126,424.531Z" transform="translate(-230.813 -272.102)" fill="gray"/>
        <path id="Path_2615" data-name="Path 2615" d="M497.629,488c-2.49-4.155-6.688-6.937-12.038-8.644-8.957-2.867-21.166-2.746-34.05-1.1-32.571,4.169-69.5,18.078-69.5,18.078" transform="translate(-206.328 -288.326)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="3"/>
        <path id="Path_2616" data-name="Path 2616" d="M513.771,471.971c-8.957-2.867-21.166-2.746-34.05-1.1a19.733,19.733,0,0,1,.406-4.667c2.07-9.861,11.227-16.292,20.454-14.357C509.431,453.708,515.13,462.558,513.771,471.971Z" transform="translate(-234.508 -280.944)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="1"/>
        <line id="Line_114" data-name="Line 114" x1="0.567" y2="12.768" transform="translate(262.894 175.997)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="3"/>
        <g id="Group_6037" data-name="Group 6037" transform="translate(78.25 73.342)">
          <path id="Path_2617" data-name="Path 2617" d="M377.649,413.895a58.015,58.015,0,1,0-83.158-1.168l-8.067,8.068a5,5,0,0,0-4.847,1.291l-35.06,35.06a5.01,5.01,0,0,0,7.086,7.085l35.059-35.059a5.005,5.005,0,0,0,1.291-4.847l8.113-8.113A58.012,58.012,0,0,0,377.649,413.895Zm-77.918-77.918a52.178,52.178,0,1,1,0,73.79A52.179,52.179,0,0,1,299.731,335.976Z" transform="translate(-245.049 -314.856)" fill="#232323"/>
          <path id="Path_2618" data-name="Path 2618" d="M319.085,371.45a41.3,41.3,0,0,1,7.569-12.951" transform="translate(-266.412 -327.449)" fill="none" stroke="#232323" stroke-miterlimit="10" stroke-width="8"/>
          <path id="Path_2619" data-name="Path 2619" d="M333.9,429.017a41.3,41.3,0,0,1-18.239-34.287q0-2.087.2-4.122" transform="translate(-265.424 -336.713)" fill="none" stroke="#232323" stroke-miterlimit="10" stroke-width="8"/>
          <path id="Path_2620" data-name="Path 2620" d="M261.354,495.211l28.378-28.185a1.249,1.249,0,0,0-1.73.028l-27.638,27.406Z" transform="translate(-249.468 -358.667)" fill="gray"/>
          <path id="Path_2621" data-name="Path 2621" d="M256.51,508.751c-.3-.239-.616-.486-.938-.738L251,512.549l-1.222,1.284a1.183,1.183,0,0,0,.021,1.652l6.751-6.7Z" transform="translate(-246.319 -370.59)" fill="gray"/>
        </g>
        <g id="Group_6038" data-name="Group 6038" transform="translate(335.472)">
          <path id="Path_2622" data-name="Path 2622" d="M623.989,240.319l-17.249-8.984c.818-1.47,1.532-2.94,2.412-4.3,5.37-8.33,13.213-12.9,22.782-14.7a38.118,38.118,0,0,1,16.766.879,54.184,54.184,0,0,1,19.7,9.274,34.248,34.248,0,0,1,13.62,22.5,25.175,25.175,0,0,1-12.043,25.823,97.279,97.279,0,0,1-16.1,7.224,95.132,95.132,0,0,0-10.8,4.369,13.211,13.211,0,0,0-6.033,6.454c-.972,2.105-1.87,4.245-2.822,6.417l-17.627-6.587c-.017-.083-.046-.136-.033-.174a59.373,59.373,0,0,1,5.256-12.152,26.535,26.535,0,0,1,11.087-9.936,120.77,120.77,0,0,1,13.573-5.332c2.622-.943,5.253-1.887,7.8-3.015a13.576,13.576,0,0,0,7.237-17.312,15.969,15.969,0,0,0-5.633-7.252,24.147,24.147,0,0,0-12.271-4.6,18.312,18.312,0,0,0-15.175,5.907c-1.388,1.427-2.545,3.079-3.8,4.632C624.409,239.719,624.222,240,623.989,240.319Z" transform="translate(-606.633 -211.77)" fill="#bab9b9"/>
          <rect id="Rectangle_2114" data-name="Rectangle 2114" width="19.302" height="19.251" rx="9.625" transform="translate(0 103.381) rotate(-69.511)" fill="#bab9b9"/>
        </g>
        <g id="Group_6039" data-name="Group 6039" transform="translate(367.338 71.755)">
          <path id="Path_2623" data-name="Path 2623" d="M677.467,322.352l-4.565-7.009c.618-.378,1.2-.778,1.813-1.1a14.2,14.2,0,0,1,11.64-.694,16.393,16.393,0,0,1,6.1,3.871,23.277,23.277,0,0,1,5.421,7.635,14.73,14.73,0,0,1,.349,11.308,10.827,10.827,0,0,1-9.967,7.132,41.831,41.831,0,0,1-7.559-.7,41.05,41.05,0,0,0-4.97-.645,5.682,5.682,0,0,0-3.624,1.144c-.809.583-1.6,1.2-2.412,1.809l-5.214-6.191c.011-.035.012-.06.025-.073a25.5,25.5,0,0,1,4.536-3.442,11.41,11.41,0,0,1,6.252-1.381,51.858,51.858,0,0,1,6.212.869c1.182.2,2.367.4,3.559.517a5.84,5.84,0,0,0,6.368-4.957,6.867,6.867,0,0,0-.579-3.907,10.384,10.384,0,0,0-3.625-4.317,7.877,7.877,0,0,0-6.934-.992c-.821.241-1.6.616-2.4.932C677.752,322.215,677.622,322.281,677.467,322.352Z" transform="translate(-655.158 -312.626)" fill="#bab9b9"/>
          <rect id="Rectangle_2115" data-name="Rectangle 2115" width="8.302" height="8.28" rx="4.14" transform="translate(0 34.097) rotate(-40.098)" fill="#bab9b9"/>
        </g>
        <rect id="Rectangle_2116" data-name="Rectangle 2116" width="79.878" height="1.774" rx="0.887" transform="translate(362.362 285.22)" fill="gray"/>
        <rect id="Rectangle_2117" data-name="Rectangle 2117" width="63.634" height="1.774" rx="0.887" transform="translate(316.821 273.416)" fill="gray"/>
        <rect id="Rectangle_2118" data-name="Rectangle 2118" width="132.198" height="1.448" rx="0.724" transform="translate(339.001 263.003)" fill="gray"/>
      </g>
    </g>
  </g>
</svg>
