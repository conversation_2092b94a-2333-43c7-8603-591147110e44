<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="649.521" height="381.505" viewBox="0 0 649.521 381.505">
  <defs>
    <linearGradient id="linear-gradient" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f5841f"/>
      <stop offset="1" stop-color="#ef6052"/>
    </linearGradient>
  </defs>
  <g id="Group_6287" data-name="Group 6287" transform="translate(-748.947 -449.692)">
    <g id="Group_6046" data-name="Group 6046" transform="translate(431.379 235.82)">
      <path id="Path_2624" data-name="Path 2624" d="M410.319,788.809H960.2s0,26.906-250.1,17.837c0,0-26.743-.952-76.69,11.892s-209.827,15.7-186.467-17.124C446.945,801.414,458.841,792.853,410.319,788.809Z" transform="translate(-46.3 -286.999)" fill="#bab9b9"/>
      <path id="Path_2625" data-name="Path 2625" d="M456.611,788.809H968.732s-15.764,22.1-232.927,14.866c0,0-24.906-.793-71.422,9.911s-195.417,13.083-173.661-14.272C490.721,799.314,501.8,792.18,456.611,788.809Z" transform="translate(-69.408 -286.999)" fill="gray"/>
      <path id="Path_2626" data-name="Path 2626" d="M1020.788,467.995q0,.178,0,.356c-.05,18.84-16.974,34.044-37.687,34.044H465.458c.863-7.576,6.4-14.373,14.876-19.339a41.125,41.125,0,0,0,20.611-35.921q-.006-.456-.006-.914c0-31.83,21.744-60.064,55.294-77.688a13.728,13.728,0,0,0,7.341-12.193v-.086c0-78.635,70.088-142.383,156.544-142.383,60.917,0,113.7,31.654,139.571,77.864a85.562,85.562,0,0,0,51.317,40.344C974.621,350.164,1020.788,404.174,1020.788,467.995Z" transform="translate(-73.824 0)" fill="#fdd9b2"/>
      <path id="Path_2627" data-name="Path 2627" d="M963.428,467.995a130.176,130.176,0,0,1-4.605,34.4H909.35v5.879H723.274v-5.879H408.1c.863-7.576,6.4-14.373,14.876-19.339a41.126,41.126,0,0,0,20.611-35.921q-.006-.456-.006-.914c0-31.8,21.708-60.018,55.212-77.645a13.833,13.833,0,0,0,7.423-12.236v-.086c0-78.635,70.088-142.383,156.544-142.383,60.917,0,113.7,31.654,139.571,77.864a85.561,85.561,0,0,0,51.316,40.344C917.261,350.164,963.428,404.174,963.428,467.995Z" transform="translate(-45.191 0)" fill="#f2f2f2"/>
      <path id="Path_2628" data-name="Path 2628" d="M967.089,788.809H864.159v6.464H678.083v-6.464H317.568" transform="translate(0 -286.999)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_2629" data-name="Path 2629" d="M1137.895,227l7.212,5.777a.268.268,0,0,0,.43-.266l-1.01-4.666a.268.268,0,0,0-.215-.207l-6.2-1.112A.268.268,0,0,0,1137.895,227Z" transform="translate(-409.443 -6.313)" fill="gray"/>
      <path id="Path_2630" data-name="Path 2630" d="M1154.314,314.123l-8.638-3.248-8.557-68.727a1.773,1.773,0,0,1,1.473-1.969l6.4-1.05Z" transform="translate(-409.1 -12.607)" fill="gray"/>
      <path id="Path_2631" data-name="Path 2631" d="M1239.736,426.947l13.165-3.688,3.543,9.707Z" transform="translate(-460.331 -104.523)" fill="gray"/>
      <path id="Path_2632" data-name="Path 2632" d="M1256.206,582.844H1118.43a1.9,1.9,0,0,1-1.9-1.9V568.995a31.808,31.808,0,0,1,4.834-16.858l8.02-12.832a16.591,16.591,0,0,0,1.822-13.558l-40.322-134.423a1.9,1.9,0,0,1,2.491-2.326l87.406,32.868a1.9,1.9,0,0,1,1.084,1.044l2.663,6.341a1.9,1.9,0,0,0,1.109,1.053l21.256,7.657a1.9,1.9,0,0,0,2.431-2.441h0a1.9,1.9,0,0,1,2.469-2.427l52.407,20.136a1.9,1.9,0,0,1,1.112,1.144l21.024,59.744a16.59,16.59,0,0,1-1.959,14.875l-19.025,27.8a1.9,1.9,0,0,0-.332,1.074v16.168A8.812,8.812,0,0,1,1256.206,582.844Z" transform="translate(-385.985 -87.358)" fill="url(#linear-gradient)"/>
      <path id="Path_2633" data-name="Path 2633" d="M1190.088,557.905H1004.012V529.119h0a87.94,87.94,0,0,0,9.659-71.875L970.492,313.3l115,43.243,4.14,9.857,32.414,11.677-3.543-9.707,71.59,27.507,26.072,74.093c2.6,7.389,3.371,10.9-2.9,22.039l-23.171,39.313Z" transform="translate(-325.929 -49.632)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_2634" data-name="Path 2634" d="M815.271,776.267l88.2-3.846V759.188a8.452,8.452,0,0,0-8.452-8.452l-67.1,4.876a10.849,10.849,0,0,0-9.78,8.36Z" transform="translate(-248.445 -267.994)" fill="#232323"/>
      <path id="Path_2635" data-name="Path 2635" d="M713.472,400.261,840.6,313.3l43.179,143.946a87.941,87.941,0,0,1-9.659,71.875h0v28.786H851.059V541.527a7.946,7.946,0,0,0-7.946-7.945L775.9,537.224A11.392,11.392,0,0,0,765.63,546l-2.772,11.9h-20.8l4.41-26.631a58.313,58.313,0,0,0-2.185-27.9l-20.1-70.7a52.707,52.707,0,0,0-10.857-20.094l-1.262-1.458A7.344,7.344,0,0,1,713.472,400.261Z" transform="translate(-196.033 -49.632)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_2636" data-name="Path 2636" d="M759.444,497.977l-4.8-3.975a1.4,1.4,0,0,0-2.213.626h0a23.2,23.2,0,0,0-.752,12.183l2.069,10.1-.06-1.571a22.13,22.13,0,0,1,3.228-12.554,19.714,19.714,0,0,1,2.6-3.341A1,1,0,0,0,759.444,497.977Z" transform="translate(-216.461 -139.676)" fill="gray"/>
      <path id="Path_2637" data-name="Path 2637" d="M880.563,427.97l-1.1-8.013a1.812,1.812,0,0,1,2.2-2.012h0a30.112,30.112,0,0,1,13.953,7.513l12.339,10.107-5.075-2.913a30.767,30.767,0,0,0-14.476-4.165,26.049,26.049,0,0,0-6.273.565A1.3,1.3,0,0,1,880.563,427.97Z" transform="translate(-280.481 -101.846)" fill="gray"/>
      <path id="Path_2638" data-name="Path 2638" d="M1167.719,528.786l-68.97-2.508-38.483-137.4,67.519,24Z" transform="translate(-370.743 -87.358)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_2639" data-name="Path 2639" d="M1186.9,665.887l-68.97-2.364-19.237,30.58,73.353,1.71Z" transform="translate(-389.926 -224.458)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_2640" data-name="Path 2640" d="M1365.354,673.722l-54.525-1.626-15.316,28.918h52.575Z" transform="translate(-488.174 -228.738)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_2641" data-name="Path 2641" d="M1343.313,565.305l-54.42-1.979-37.534-108.418,61.167,21.036Z" transform="translate(-466.133 -120.321)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_2642" data-name="Path 2642" d="M780.859,747.29s60.513-19.948,128.491-7.261l186.076,2.2" transform="translate(-231.267 -260.542)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="0.5"/>
      <path id="Path_2643" data-name="Path 2643" d="M1168.424,530.235l-60.343-2.194-36.2-127.329,59.073,21Z" transform="translate(-376.543 -93.267)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
      <ellipse id="Ellipse_851" data-name="Ellipse 851" cx="4.017" cy="7.879" rx="4.017" ry="7.879" transform="translate(610.925 380.136)" fill="gray"/>
      <ellipse id="Ellipse_852" data-name="Ellipse 852" cx="4.027" cy="7.9" rx="4.027" ry="7.9" transform="translate(562.798 402.351)" fill="gray"/>
      <path id="Path_2644" data-name="Path 2644" d="M950.448,595.267s-17.437-15.142-44.679,6.054q-5.747,4.471-11.871,8.426l-2.1,1.35" transform="translate(-286.647 -187.77)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="3"/>
      <path id="Path_2645" data-name="Path 2645" d="M530.1,681.964,646.8,675.343l103.85,3.3L641.239,690.991Z" transform="translate(-106.09 -230.359)" fill="gray"/>
      <path id="Path_2646" data-name="Path 2646" d="M860.573,743.911,750.294,762.36l.864-68.083,109.415-12.352Z" transform="translate(-216.01 -233.644)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_2647" data-name="Path 2647" d="M530.1,747.225l110.279,18.448.864-68.083L530.1,688.563Z" transform="translate(-106.09 -236.958)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_2648" data-name="Path 2648" d="M465.458,704.848l32.371-16.286,111.143,9.028L571.02,721.964Z" transform="translate(-73.824 -236.958)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
      <path id="Path_2649" data-name="Path 2649" d="M895.346,696.86l-33.912-14.936L752.02,694.276l33.773,22.11Z" transform="translate(-216.871 -233.644)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
      <line id="Line_115" data-name="Line 115" y2="15.103" transform="translate(540.713 444.984)" fill="none" stroke="#232323" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1"/>
      <path id="Path_2650" data-name="Path 2650" d="M566.495,564.04s11.59-40.685-50.486-39.123c-41.239,1.037-49.395-13.594-50.224-23.533a14.636,14.636,0,0,1,8.318-14.655c4.13-1.87,9.237-1.67,13.141,6.607,7.808,16.554-33.52,27.04-41.9,17.917-4.869-5.3-12.493-11.322-5.856-31.233s3.514-49.1-31.624-56.76" transform="translate(-45.075 -104.523)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="1" stroke-dasharray="12"/>
      <g id="Group_6043" data-name="Group 6043" transform="translate(357.381 311.901)">
        <ellipse id="Ellipse_853" data-name="Ellipse 853" cx="1.308" cy="2.064" rx="1.308" ry="2.064" transform="translate(5.417 8.32) rotate(-72.424)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
        <path id="Path_2651" data-name="Path 2651" d="M410.212,413.962a4.181,4.181,0,0,1-4.823,2.516,4.533,4.533,0,0,1-2.533-5.087c.673-2.166,2.834-1.929,4.27-1.482S410.885,411.8,410.212,413.962Z" transform="translate(-399.882 -409.61)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
        <path id="Path_2652" data-name="Path 2652" d="M404.614,428.029a4.181,4.181,0,0,0-2.171-4.988,4.534,4.534,0,0,0-5.167,2.366c-.837,2.107.993,3.281,2.39,3.836S403.776,430.136,404.614,428.029Z" transform="translate(-397.064 -416.23)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <rect id="Rectangle_2119" data-name="Rectangle 2119" width="121.396" height="1.037" rx="0.519" transform="translate(417.741 280.112)" fill="gray"/>
      <rect id="Rectangle_2120" data-name="Rectangle 2120" width="121.396" height="1.037" rx="0.519" transform="translate(491.827 267.19)" fill="gray"/>
      <rect id="Rectangle_2121" data-name="Rectangle 2121" width="96.346" height="1.037" rx="0.519" transform="translate(483.775 294.072)" fill="gray"/>
      <g id="Group_6044" data-name="Group 6044" transform="translate(510.309 415.602)">
        <line id="Line_116" data-name="Line 116" x2="4.086" y2="16.371" transform="translate(14.155 0)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="1"/>
        <line id="Line_117" data-name="Line 117" x2="13.696" y2="9.856" transform="translate(0 9.578)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="1"/>
        <line id="Line_118" data-name="Line 118" x2="11.334" y2="0.252" transform="translate(0.553 23.735)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="1"/>
      </g>
      <g id="Group_6045" data-name="Group 6045" transform="translate(552.488 414.716)">
        <line id="Line_119" data-name="Line 119" x1="4.086" y2="16.371" transform="translate(0 0)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="1"/>
        <line id="Line_120" data-name="Line 120" x1="13.696" y2="9.856" transform="translate(4.544 9.578)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="1"/>
        <line id="Line_121" data-name="Line 121" x1="11.334" y2="0.252" transform="translate(6.354 23.735)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="1"/>
      </g>
    </g>
    <text id="No_Rental_order_to_display_" data-name="No Rental order to display !!" transform="translate(989.707 827.197)" fill="#9f9080" font-size="14" font-family="Roboto-Italic, Roboto" font-style="italic"><tspan x="0" y="0">No Rental order to display !!</tspan></text>
  </g>
</svg>
