<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="206.846" height="137.505" viewBox="0 0 206.846 137.505">
  <defs>
    <linearGradient id="linear-gradient" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f5841f"/>
      <stop offset="1" stop-color="#ef6052"/>
    </linearGradient>
  </defs>
  <g id="no_rent" data-name="no rent" transform="translate(11267 3871)">
    <g id="Group_6046" data-name="Group 6046" transform="translate(-11584.568 -4084.872)">
      <path id="Path_2624" data-name="Path 2624" d="M410.319,788.809H586.8s0,8.2-80.27,5.435c0,0-8.583-.29-24.613,3.624s-67.344,4.784-59.846-5.218C422.074,792.65,425.892,790.041,410.319,788.809Z" transform="translate(-78.596 -482.709)" fill="#bab9b9"/>
      <path id="Path_2625" data-name="Path 2625" d="M456.611,788.809H621.587s-5.078,6.735-75.036,4.53c0,0-8.023-.242-23.008,3.02s-62.952,3.987-55.944-4.349C467.6,792.01,471.168,789.836,456.611,788.809Z" transform="translate(-117.823 -482.709)" fill="gray"/>
      <path id="Path_2626" data-name="Path 2626" d="M643.6,295.261q0,.057,0,.114c-.016,6.034-5.445,10.9-12.09,10.9H465.458c.277-2.426,2.053-4.6,4.772-6.194a13.167,13.167,0,0,0,6.612-11.5q0-.146,0-.293c0-10.194,6.975-19.237,17.738-24.881a4.4,4.4,0,0,0,2.355-3.905v-.028c0-25.185,22.483-45.6,50.217-45.6,19.542,0,36.475,10.138,44.773,24.938a27.44,27.44,0,0,0,16.462,12.921C628.792,257.523,643.6,274.821,643.6,295.261Z" transform="translate(-125.32)" fill="#fdd9b2"/>
      <path id="Path_2627" data-name="Path 2627" d="M586.242,295.182a41.549,41.549,0,0,1-1.477,11.007h-15.87v1.881H509.2v-1.881H408.1c.277-2.424,2.053-4.6,4.772-6.188a13.153,13.153,0,0,0,6.612-11.493q0-.146,0-.292c0-10.176,6.964-19.2,17.711-24.843a4.425,4.425,0,0,0,2.381-3.915v-.028c0-25.16,22.483-45.557,50.217-45.557,19.542,0,36.475,10.128,44.773,24.914a27.436,27.436,0,0,0,16.462,12.909C571.432,257.48,586.242,274.762,586.242,295.182Z" transform="translate(-76.714 0)" fill="#f2f2f2"/>
      <path id="Path_2628" data-name="Path 2628" d="M524.414,788.809H491.635v1.97H432.377v-1.97H317.568" transform="translate(0 -482.709)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="0.5"/>
      <path id="Path_2629" data-name="Path 2629" d="M1137.824,226.664l2.2,1.761a.082.082,0,0,0,.131-.081l-.308-1.422a.082.082,0,0,0-.066-.063l-1.89-.339A.082.082,0,0,0,1137.824,226.664Z" transform="translate(-689.34 -10.717)" fill="gray"/>
      <path id="Path_2630" data-name="Path 2630" d="M1142.35,261.981l-2.632-.99-2.608-20.943a.541.541,0,0,1,.449-.6l1.95-.32Z" transform="translate(-688.676 -21.402)" fill="gray"/>
      <path id="Path_2631" data-name="Path 2631" d="M1239.736,424.383l4.012-1.124,1.08,2.958Z" transform="translate(-774.924 -175.948)" fill="gray"/>
      <path id="Path_2632" data-name="Path 2632" d="M1141.2,447.981H1099.22a.579.579,0,0,1-.579-.579v-3.641a9.691,9.691,0,0,1,1.473-5.137l2.444-3.91a5.056,5.056,0,0,0,.555-4.132l-12.287-40.963a.579.579,0,0,1,.759-.709l26.635,10.016a.579.579,0,0,1,.33.318l.812,1.932a.58.58,0,0,0,.338.321l6.477,2.333a.58.58,0,0,0,.741-.744h0a.579.579,0,0,1,.752-.74l15.97,6.136a.579.579,0,0,1,.339.349l6.407,18.206a5.055,5.055,0,0,1-.6,4.533l-5.8,8.471a.579.579,0,0,0-.1.327V445.3A2.685,2.685,0,0,1,1141.2,447.981Z" transform="translate(-646.308 -143.808)" fill="url(#linear-gradient)"/>
      <path id="Path_2633" data-name="Path 2633" d="M1037.409,387.837h-56.7v-8.772h0a26.8,26.8,0,0,0,2.943-21.9L970.492,313.3l35.043,13.177,1.261,3,9.877,3.558-1.08-2.958,21.816,8.382,7.945,22.578c.792,2.252,1.027,3.321-.884,6.716l-7.061,11.98Z" transform="translate(-544.361 -79.767)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="0.5"/>
      <path id="Path_2634" data-name="Path 2634" d="M815.271,758.516l26.877-1.172v-4.032a2.576,2.576,0,0,0-2.576-2.575l-20.449,1.486a3.306,3.306,0,0,0-2.98,2.547Z" transform="translate(-417.787 -450.446)" fill="#232323"/>
      <path id="Path_2635" data-name="Path 2635" d="M711.249,339.8l38.738-26.5,13.158,43.865a26.8,26.8,0,0,1-2.944,21.9h0v8.772h-7.026v-4.991a2.421,2.421,0,0,0-2.421-2.421l-20.48,1.11a3.472,3.472,0,0,0-3.131,2.675l-.845,3.627H719.96l1.344-8.115a17.771,17.771,0,0,0-.666-8.5l-6.125-21.543a16.065,16.065,0,0,0-3.309-6.123l-.385-.444A2.238,2.238,0,0,1,711.249,339.8Z" transform="translate(-329.061 -79.767)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="0.5"/>
      <path id="Path_2636" data-name="Path 2636" d="M753.711,494.99l-1.461-1.211a.426.426,0,0,0-.674.191h0a7.071,7.071,0,0,0-.229,3.712l.63,3.078-.018-.479a6.743,6.743,0,0,1,.984-3.826,6,6,0,0,1,.792-1.018A.3.3,0,0,0,753.711,494.99Z" transform="translate(-364.431 -235.035)" fill="gray"/>
      <path id="Path_2637" data-name="Path 2637" d="M879.787,420.967l-.335-2.442a.552.552,0,0,1,.672-.613h0a9.175,9.175,0,0,1,4.252,2.289l3.76,3.08-1.547-.888a9.375,9.375,0,0,0-4.411-1.269,7.935,7.935,0,0,0-1.912.172A.4.4,0,0,1,879.787,420.967Z" transform="translate(-472.087 -171.405)" fill="gray"/>
      <path id="Path_2638" data-name="Path 2638" d="M1093.01,431.508l-21.017-.764-11.727-41.871,20.575,7.314Z" transform="translate(-623.233 -146.188)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="0.5"/>
      <path id="Path_2639" data-name="Path 2639" d="M1125.574,664.243l-21.017-.72-5.862,9.319,22.353.521Z" transform="translate(-655.701 -377.597)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="0.5"/>
      <path id="Path_2640" data-name="Path 2640" d="M1316.8,672.592l-16.615-.5-4.667,8.812h16.021Z" transform="translate(-819.779 -384.836)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="0.5"/>
      <path id="Path_2641" data-name="Path 2641" d="M1279.38,488.549l-16.583-.6-11.438-33.038,18.639,6.41Z" transform="translate(-782.364 -201.747)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="0.5"/>
      <path id="Path_2642" data-name="Path 2642" d="M780.859,739.306s18.44-6.078,39.155-2.213l56.7.67" transform="translate(-386.409 -437.796)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="0.5"/>
      <path id="Path_2643" data-name="Path 2643" d="M1101.3,440.182l-18.388-.669-11.03-38.8,18,6.4Z" transform="translate(-633.106 -156.197)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="0.5"/>
      <ellipse id="Ellipse_851" data-name="Ellipse 851" cx="1.224" cy="2.401" rx="1.224" ry="2.401" transform="translate(411.041 258.806)" fill="gray"/>
      <ellipse id="Ellipse_852" data-name="Ellipse 852" cx="1.227" cy="2.407" rx="1.227" ry="2.407" transform="translate(395.706 265.896)" fill="gray"/>
      <path id="Path_2644" data-name="Path 2644" d="M909.671,591.624s-5.314-4.614-13.615,1.845q-1.751,1.362-3.617,2.568l-.64.411" transform="translate(-484.492 -319.464)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="1"/>
      <path id="Path_2645" data-name="Path 2645" d="M530.1,677.361l35.564-2.017,31.646,1-33.342,3.764Z" transform="translate(-177.88 -387.712)" fill="gray"/>
      <path id="Path_2646" data-name="Path 2646" d="M783.9,700.813l-33.605,5.622.263-20.747,33.342-3.764Z" transform="translate(-363.102 -392.136)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="0.5"/>
      <path id="Path_2647" data-name="Path 2647" d="M530.1,706.439,563.7,712.06l.263-20.747L530.1,688.563Z" transform="translate(-178.33 -397.761)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="0.5"/>
      <path id="Path_2648" data-name="Path 2648" d="M465.458,693.525l9.865-4.963,33.869,2.751-11.565,7.427Z" transform="translate(-125.32 -398.61)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="0.5"/>
      <path id="Path_2649" data-name="Path 2649" d="M795.7,686.475l-10.334-4.551-33.342,3.764,10.292,6.738Z" transform="translate(-364.315 -393.023)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="0.5"/>
      <line id="Line_115" data-name="Line 115" y2="4.602" transform="translate(388.631 287.626)" fill="none" stroke="#232323" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1"/>
      <path id="Path_2650" data-name="Path 2650" d="M456.2,466.159s3.532-12.4-15.385-11.922c-12.567.316-15.052-4.143-15.3-7.171a4.46,4.46,0,0,1,2.535-4.466c1.259-.57,2.815-.509,4,2.013,2.379,5.044-10.215,8.24-12.768,5.46-1.484-1.616-3.807-3.45-1.784-9.518s1.071-14.963-9.637-17.3" transform="translate(-76.517 -174.901)" fill="none" stroke="#232323" stroke-linejoin="round" stroke-width="0.5" stroke-dasharray="4"/>
      <g id="Group_6043" data-name="Group 6043" transform="translate(329.7 245.148)">
        <ellipse id="Ellipse_853" data-name="Ellipse 853" cx="0.399" cy="0.629" rx="0.399" ry="0.629" transform="translate(1.651 2.535) rotate(-72.424)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
        <path id="Path_2651" data-name="Path 2651" d="M405,410.936a1.274,1.274,0,0,1-1.47.767,1.381,1.381,0,0,1-.772-1.55c.205-.66.864-.588,1.3-.452S405.2,410.276,405,410.936Z" transform="translate(-401.847 -409.61)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
        <path id="Path_2652" data-name="Path 2652" d="M399.365,424.443a1.274,1.274,0,0,0-.662-1.52,1.381,1.381,0,0,0-1.575.721c-.255.642.3,1,.728,1.169S399.11,425.085,399.365,424.443Z" transform="translate(-397.064 -420.847)" fill="#fff" stroke="#232323" stroke-linejoin="round" stroke-width="1"/>
      </g>
      <rect id="Rectangle_2119" data-name="Rectangle 2119" width="36.993" height="0.316" rx="0.158" transform="translate(349.785 234.97)" fill="gray"/>
      <rect id="Rectangle_2120" data-name="Rectangle 2120" width="36.993" height="0.316" rx="0.158" transform="translate(373.612 230.12)" fill="gray"/>
      <rect id="Rectangle_2121" data-name="Rectangle 2121" width="29.36" height="0.316" rx="0.158" transform="translate(370.896 239.416)" fill="gray"/>
      <g id="Group_6044" data-name="Group 6044" transform="translate(381.702 281.111)">
        <line id="Line_116" data-name="Line 116" x2="1.245" y2="4.989" transform="translate(4.313 0)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="0.5"/>
        <line id="Line_117" data-name="Line 117" x2="4.174" y2="3.003" transform="translate(0 2.919)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="0.5"/>
        <line id="Line_118" data-name="Line 118" x2="3.454" y2="0.077" transform="translate(0.168 7.233)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="0.5"/>
      </g>
      <g id="Group_6045" data-name="Group 6045" transform="translate(389.919 280.828)">
        <line id="Line_119" data-name="Line 119" x1="1.245" y2="4.989" transform="translate(0 0)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="0.5"/>
        <line id="Line_120" data-name="Line 120" x1="4.174" y2="3.003" transform="translate(1.385 2.919)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="0.5"/>
        <line id="Line_121" data-name="Line 121" x1="3.454" y2="0.077" transform="translate(1.936 7.233)" fill="none" stroke="gray" stroke-miterlimit="10" stroke-width="0.5"/>
      </g>
    </g>
    <text id="No_Rental_order_to_display_" data-name="No Rental order to display !!" transform="translate(-11241.577 -3737.495)" fill="#9f9080" font-size="13" font-family="Roboto-Regular, Roboto"><tspan x="0" y="0">No Rental order to display !!</tspan></text>
  </g>
</svg>
