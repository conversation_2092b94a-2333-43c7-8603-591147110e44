<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="260.322" height="91.619" viewBox="0 0 260.322 91.619">
  <defs>
    <clipPath id="clip-path">
      <path id="Path_3427" data-name="Path 3427" d="M-337.766,167.746v27.342c0,5.782-5.954,10.814-13.853,10.814H-528.79c-7.925,0-12.553-5.461-12.553-11.243l.48-26.913c0-5.762,6.458-10.5,14.382-10.5h174.333C-344.252,157.251-337.766,161.984-337.766,167.746Z" transform="translate(541.343 -157.251)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#aab8ff"/>
      <stop offset="1" stop-color="#544ea6"/>
    </linearGradient>
  </defs>
  <g id="Group_7762" data-name="Group 7762" transform="translate(-1437 -349.35)">
    <g id="Group_7790" data-name="Group 7790" transform="translate(1437 349.35)">
      <rect id="Rectangle_2369" data-name="Rectangle 2369" width="148.465" height="24.691" transform="translate(54.026 51.203)" fill="#45919e"/>
      <path id="Path_3425" data-name="Path 3425" d="M-337.733,168.43v32.336c0,5.781-6.51,10.494-14.437,10.494H-527.163c-7.957,0-14.438-4.713-14.438-10.494V168.43c0-5.762,6.482-10.5,14.438-10.5h174.993C-344.242,157.935-337.733,162.668-337.733,168.43Z" transform="translate(597.555 -153.83)" fill="#40757a"/>
      <path id="Path_3426" data-name="Path 3426" d="M-337.733,168.43v27.342c0,5.782-6.51,10.514-14.437,10.514H-527.163c-7.957,0-14.438-4.732-14.438-10.514V168.43c0-5.762,6.482-10.5,14.438-10.5h174.993C-344.242,157.935-337.733,162.668-337.733,168.43Z" transform="translate(597.555 -153.83)" fill="#fff" stroke="#45919e" stroke-miterlimit="10" stroke-width="1"/>
      <g id="Group_7472" data-name="Group 7472" transform="translate(63.835 0)">
        <rect id="Rectangle_2370" data-name="Rectangle 2370" width="17.448" height="2.054" transform="translate(2.416 2.052)" fill="#38aaaf"/>
        <rect id="Rectangle_2371" data-name="Rectangle 2371" width="22.28" height="2.052" transform="translate(0)" fill="#59c0d8"/>
      </g>
      <g id="Group_7470" data-name="Group 7470" transform="translate(105.183 0)">
        <g id="Group_7473" data-name="Group 7473">
          <rect id="Rectangle_2372" data-name="Rectangle 2372" width="17.448" height="2.054" transform="translate(2.417 2.052)" fill="#38aaaf"/>
          <rect id="Rectangle_2373" data-name="Rectangle 2373" width="22.28" height="2.052" fill="#59c0d8"/>
        </g>
      </g>
      <g id="Group_7471" data-name="Group 7471" transform="translate(145.806 0)">
        <rect id="Rectangle_2372-2" data-name="Rectangle 2372" width="17.448" height="2.054" transform="translate(2.417 2.052)" fill="#38aaaf"/>
        <rect id="Rectangle_2373-2" data-name="Rectangle 2373" width="22.28" height="2.052" transform="translate(0)" fill="#59c0d8"/>
      </g>
      <g id="Group_7788" data-name="Group 7788" transform="translate(186.428 0)">
        <rect id="Rectangle_2372-3" data-name="Rectangle 2372" width="17.448" height="2.054" transform="translate(2.417 2.052)" fill="#38aaaf"/>
        <rect id="Rectangle_2373-3" data-name="Rectangle 2373" width="22.28" height="2.052" fill="#59c0d8"/>
      </g>
      <g id="Group_7789" data-name="Group 7789" transform="translate(227.776 0)">
        <rect id="Rectangle_2372-4" data-name="Rectangle 2372" width="17.448" height="2.054" transform="translate(2.417 2.052)" fill="#38aaaf"/>
        <rect id="Rectangle_2373-4" data-name="Rectangle 2373" width="22.28" height="2.052" fill="#59c0d8"/>
      </g>
      <g id="Group_7463" data-name="Group 7463" transform="translate(56.246 3.331)">
        <g id="Group_7462" data-name="Group 7462" clip-path="url(#clip-path)">
          <g id="Group_7461" data-name="Group 7461" transform="translate(-1.236 18.934)">
            <path id="Union_1" data-name="Union 1" d="M-542.134,224.145V176.609s31.544-6.6,70.605,1.139c22.879,4.531,30.426,2,36.869-.535,4.558-1.79,8.565-3.583,17.056-2.871,20.5,1.711,29.076,9.644,51.5,3.406s30.5,1.993,30.5,1.993v5.411h-.078v38.992Z" transform="translate(542.134 -173.987)" fill="url(#linear-gradient)"/>
            <g id="Group_19" data-name="Group 19" transform="translate(4.635 7.455)" opacity="0.5">
              <g id="Ellipse_9" data-name="Ellipse 9" transform="translate(16.434 20.335)">
                <ellipse id="Ellipse_956" data-name="Ellipse 956" cx="1.007" cy="0.735" rx="1.007" ry="0.735" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_957" data-name="Ellipse 957" cx="0.857" cy="0.626" rx="0.857" ry="0.626" transform="translate(0.15 0.11)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_861" data-name="Ellipse 861" transform="translate(7.351 37.228)">
                <ellipse id="Ellipse_958" data-name="Ellipse 958" cx="1.007" cy="0.735" rx="1.007" ry="0.735" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_959" data-name="Ellipse 959" cx="0.857" cy="0.626" rx="0.857" ry="0.626" transform="translate(0.15 0.11)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_26" data-name="Ellipse 26" transform="translate(123.309 19.724)">
                <ellipse id="Ellipse_960" data-name="Ellipse 960" cx="0.401" cy="0.29" rx="0.401" ry="0.29" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_961" data-name="Ellipse 961" cx="0.252" cy="0.182" rx="0.252" ry="0.182" transform="translate(0.149 0.108)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_866" data-name="Ellipse 866" transform="translate(78.479 8.098)">
                <ellipse id="Ellipse_962" data-name="Ellipse 962" cx="0.401" cy="0.29" rx="0.401" ry="0.29" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_963" data-name="Ellipse 963" cx="0.252" cy="0.182" rx="0.252" ry="0.182" transform="translate(0.149 0.108)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_860" data-name="Ellipse 860" transform="translate(20.058 14.536)">
                <ellipse id="Ellipse_964" data-name="Ellipse 964" cx="0.401" cy="0.29" rx="0.401" ry="0.29" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_965" data-name="Ellipse 965" cx="0.252" cy="0.182" rx="0.252" ry="0.182" transform="translate(0.149 0.108)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_859" data-name="Ellipse 859" transform="translate(17.442)">
                <ellipse id="Ellipse_966" data-name="Ellipse 966" cx="0.401" cy="0.29" rx="0.401" ry="0.29" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_967" data-name="Ellipse 967" cx="0.252" cy="0.182" rx="0.252" ry="0.182" transform="translate(0.149 0.108)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_10" data-name="Ellipse 10" transform="translate(48.087 34.469)">
                <ellipse id="Ellipse_968" data-name="Ellipse 968" cx="0.626" cy="0.453" rx="0.626" ry="0.453" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_969" data-name="Ellipse 969" cx="0.476" cy="0.345" rx="0.476" ry="0.345" transform="translate(0.15 0.109)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_18" data-name="Ellipse 18" transform="translate(35.122 0.789)">
                <ellipse id="Ellipse_970" data-name="Ellipse 970" cx="0.626" cy="0.453" rx="0.626" ry="0.453" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_971" data-name="Ellipse 971" cx="0.476" cy="0.345" rx="0.476" ry="0.345" transform="translate(0.15 0.109)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_858" data-name="Ellipse 858" transform="translate(30.449 35.4)">
                <ellipse id="Ellipse_972" data-name="Ellipse 972" cx="0.626" cy="0.453" rx="0.626" ry="0.453" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_973" data-name="Ellipse 973" cx="0.476" cy="0.345" rx="0.476" ry="0.345" transform="translate(0.15 0.109)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_15" data-name="Ellipse 15" transform="translate(68.864 17.248)">
                <ellipse id="Ellipse_974" data-name="Ellipse 974" cx="1.389" cy="1.013" rx="1.389" ry="1.013" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_975" data-name="Ellipse 975" cx="1.238" cy="0.904" rx="1.238" ry="0.904" transform="translate(0.15 0.11)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_23" data-name="Ellipse 23" transform="translate(142.858 37.093)">
                <ellipse id="Ellipse_976" data-name="Ellipse 976" cx="2.049" cy="1.495" rx="2.049" ry="1.495" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_977" data-name="Ellipse 977" cx="1.899" cy="1.386" rx="1.899" ry="1.386" transform="translate(0.15 0.11)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_23-2" data-name="Ellipse 23" transform="translate(121.661 5.376)">
                <ellipse id="Ellipse_978" data-name="Ellipse 978" cx="2.049" cy="1.495" rx="2.049" ry="1.495" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_979" data-name="Ellipse 979" cx="1.899" cy="1.386" rx="1.899" ry="1.386" transform="translate(0.15 0.11)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_20" data-name="Ellipse 20" transform="translate(102.262 7.796)">
                <ellipse id="Ellipse_980" data-name="Ellipse 980" cx="0.782" cy="0.571" rx="0.782" ry="0.571" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_981" data-name="Ellipse 981" cx="0.632" cy="0.458" rx="0.632" ry="0.458" transform="translate(0.15 0.113)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_19" data-name="Ellipse 19" transform="translate(92.316 20.44)">
                <ellipse id="Ellipse_982" data-name="Ellipse 982" cx="1.77" cy="1.205" rx="1.77" ry="1.205" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_983" data-name="Ellipse 983" cx="1.62" cy="1.102" rx="1.62" ry="1.102" transform="translate(0.15 0.102)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_19-2" data-name="Ellipse 19" transform="translate(191.684 11.518)">
                <ellipse id="Ellipse_984" data-name="Ellipse 984" cx="1.77" cy="1.205" rx="1.77" ry="1.205" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_985" data-name="Ellipse 985" cx="1.62" cy="1.102" rx="1.62" ry="1.102" transform="translate(0.15 0.102)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_25" data-name="Ellipse 25" transform="translate(83.247 14.871)">
                <ellipse id="Ellipse_986" data-name="Ellipse 986" cx="0.677" cy="0.49" rx="0.677" ry="0.49" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_987" data-name="Ellipse 987" cx="0.526" cy="0.381" rx="0.526" ry="0.381" transform="translate(0.15 0.109)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_13" data-name="Ellipse 13" transform="translate(67.299 35.282)">
                <ellipse id="Ellipse_988" data-name="Ellipse 988" cx="0.782" cy="0.571" rx="0.782" ry="0.571" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_989" data-name="Ellipse 989" cx="0.632" cy="0.458" rx="0.632" ry="0.458" transform="translate(0.15 0.113)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_16" data-name="Ellipse 16" transform="translate(51.028 1.63)">
                <ellipse id="Ellipse_990" data-name="Ellipse 990" cx="0.782" cy="0.571" rx="0.782" ry="0.571" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_991" data-name="Ellipse 991" cx="0.632" cy="0.458" rx="0.632" ry="0.458" transform="translate(0.15 0.113)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_24" data-name="Ellipse 24" transform="translate(89.317 36.424)">
                <ellipse id="Ellipse_992" data-name="Ellipse 992" cx="1.129" cy="0.824" rx="1.129" ry="0.824" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_993" data-name="Ellipse 993" cx="0.979" cy="0.714" rx="0.979" ry="0.714" transform="translate(0.15 0.11)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_24-2" data-name="Ellipse 24" transform="translate(151.857 30.11)">
                <ellipse id="Ellipse_994" data-name="Ellipse 994" cx="1.129" cy="0.824" rx="1.129" ry="0.824" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_995" data-name="Ellipse 995" cx="0.979" cy="0.714" rx="0.979" ry="0.714" transform="translate(0.15 0.11)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_27" data-name="Ellipse 27" transform="translate(142.134 20.018)">
                <ellipse id="Ellipse_996" data-name="Ellipse 996" cx="0.723" cy="0.524" rx="0.723" ry="0.524" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_997" data-name="Ellipse 997" cx="0.573" cy="0.415" rx="0.573" ry="0.415" transform="translate(0.15 0.109)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_11" data-name="Ellipse 11" transform="translate(4.386 26.167)">
                <ellipse id="Ellipse_998" data-name="Ellipse 998" cx="0.626" cy="0.453" rx="0.626" ry="0.453" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_999" data-name="Ellipse 999" cx="0.476" cy="0.345" rx="0.476" ry="0.345" transform="translate(0.15 0.109)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_855" data-name="Ellipse 855" transform="translate(41.95 17.808)">
                <ellipse id="Ellipse_1000" data-name="Ellipse 1000" cx="0.626" cy="0.453" rx="0.626" ry="0.453" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_1001" data-name="Ellipse 1001" cx="0.476" cy="0.345" rx="0.476" ry="0.345" transform="translate(0.15 0.109)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_14" data-name="Ellipse 14" transform="translate(119.247 28.27)">
                <ellipse id="Ellipse_1002" data-name="Ellipse 1002" cx="0.461" cy="0.334" rx="0.461" ry="0.334" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_1003" data-name="Ellipse 1003" cx="0.312" cy="0.226" rx="0.312" ry="0.226" transform="translate(0.149 0.108)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_17" data-name="Ellipse 17" transform="translate(176.201 30.825)">
                <ellipse id="Ellipse_1004" data-name="Ellipse 1004" cx="1.573" cy="1.148" rx="1.573" ry="1.148" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_1005" data-name="Ellipse 1005" cx="1.423" cy="1.038" rx="1.423" ry="1.038" transform="translate(0.15 0.11)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_870" data-name="Ellipse 870" transform="translate(177.993 1.263)">
                <ellipse id="Ellipse_1006" data-name="Ellipse 1006" cx="0.811" cy="0.592" rx="0.811" ry="0.592" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_1007" data-name="Ellipse 1007" cx="0.66" cy="0.478" rx="0.66" ry="0.478" transform="translate(0.15 0.113)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_857" data-name="Ellipse 857" transform="translate(0 0.706)">
                <ellipse id="Ellipse_1008" data-name="Ellipse 1008" cx="1.573" cy="1.148" rx="1.573" ry="1.148" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_1009" data-name="Ellipse 1009" cx="1.423" cy="1.038" rx="1.423" ry="1.038" transform="translate(0.15 0.11)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_854" data-name="Ellipse 854" transform="translate(30.701 24.039)">
                <ellipse id="Ellipse_1010" data-name="Ellipse 1010" cx="1.001" cy="0.731" rx="1.001" ry="0.731" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_1011" data-name="Ellipse 1011" cx="0.851" cy="0.621" rx="0.851" ry="0.621" transform="translate(0.15 0.11)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_12" data-name="Ellipse 12" transform="translate(58.483 6.421)">
                <ellipse id="Ellipse_1012" data-name="Ellipse 1012" cx="0.747" cy="0.541" rx="0.747" ry="0.541" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_1013" data-name="Ellipse 1013" cx="0.597" cy="0.432" rx="0.597" ry="0.432" transform="translate(0.15 0.109)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_863" data-name="Ellipse 863" transform="translate(166.776 16.278)">
                <ellipse id="Ellipse_1014" data-name="Ellipse 1014" cx="0.747" cy="0.541" rx="0.747" ry="0.541" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_1015" data-name="Ellipse 1015" cx="0.597" cy="0.432" rx="0.597" ry="0.432" transform="translate(0.15 0.109)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
              <g id="Ellipse_863-2" data-name="Ellipse 863" transform="translate(142.11 6.857)">
                <ellipse id="Ellipse_1016" data-name="Ellipse 1016" cx="0.747" cy="0.541" rx="0.747" ry="0.541" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
                <ellipse id="Ellipse_1017" data-name="Ellipse 1017" cx="0.597" cy="0.432" rx="0.597" ry="0.432" transform="translate(0.15 0.109)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.25"/>
              </g>
            </g>
          </g>
        </g>
      </g>
      <g id="Group_7464" data-name="Group 7464" transform="translate(94.366 4.648)">
        <rect id="Rectangle_2374" data-name="Rectangle 2374" width="3.473" height="10.072" transform="translate(0 45.233)" fill="#82a2a3"/>
        <rect id="Rectangle_2375" data-name="Rectangle 2375" width="3.473" height="45.233" fill="#f7f7f7"/>
      </g>
      <g id="Group_7465" data-name="Group 7465" transform="translate(134.925 4.648)">
        <rect id="Rectangle_2376" data-name="Rectangle 2376" width="3.473" height="10.072" transform="translate(0 45.233)" fill="#82a2a3"/>
        <rect id="Rectangle_2377" data-name="Rectangle 2377" width="3.473" height="45.233" fill="#f7f7f7"/>
      </g>
      <g id="Group_7466" data-name="Group 7466" transform="translate(175.484 4.648)">
        <rect id="Rectangle_2378" data-name="Rectangle 2378" width="3.473" height="10.072" transform="translate(0 45.233)" fill="#82a2a3"/>
        <rect id="Rectangle_2379" data-name="Rectangle 2379" width="3.473" height="45.233" fill="#f7f7f7"/>
      </g>
      <g id="Group_7467" data-name="Group 7467" transform="translate(216.768 4.648)">
        <rect id="Rectangle_2380" data-name="Rectangle 2380" width="3.473" height="10.072" transform="translate(0 45.233)" fill="#82a2a3"/>
        <rect id="Rectangle_2381" data-name="Rectangle 2381" width="3.473" height="45.233" fill="#f7f7f7"/>
      </g>
      <path id="Path_3428" data-name="Path 3428" d="M-527.89,201.827c0-1.087,1.316-1.978,2.927-1.978H-353.43c1.61,0,2.927.89,2.927,1.978Z" transform="translate(599.355 -148.326)" fill="#7adadd"/>
      <path id="Path_3429" data-name="Path 3429" d="M-350.5,201.6h0c0,1.088-1.317,1.976-2.927,1.976H-524.963c-1.612,0-2.927-.888-2.927-1.976H-350.5Z" transform="translate(599.355 -148.097)" fill="#82a2a3"/>
      <path id="Path_3431" data-name="Path 3431" d="M-587.677,215.922v6.187a3.329,3.329,0,0,1-2.043-3.093A3.368,3.368,0,0,1-587.677,215.922Z" transform="translate(591.236 -146.216)" fill="#f5773d"/>
      <path id="Path_3432" data-name="Path 3432" d="M-537.447,230.976H-587.92V198.247l11.379-32.729h39.094Z" transform="translate(591.473 -152.834)" fill="#5ac0cf"/>
      <path id="Path_3433" data-name="Path 3433" d="M-541.953,169.09v45.579a16.811,16.811,0,0,0-7.848-1.941,16.809,16.809,0,0,0-16.521,13.732h-18.017V198.47l10.212-29.38Z" transform="translate(591.943 -152.365)" fill="#6bc8d6"/>
      <path id="Path_3434" data-name="Path 3434" d="M-567.921,219.964h-14.632v-.644h14.145a19.192,19.192,0,0,1,18.372-13.728,19.18,19.18,0,0,1,5.926.941l-.2.611a18.5,18.5,0,0,0-5.724-.908,18.543,18.543,0,0,0-17.819,13.494Z" transform="translate(592.177 -147.572)" fill="#5ac0cf"/>
      <rect id="Rectangle_2382" data-name="Rectangle 2382" width="35.204" height="0.644" transform="translate(9.624 47.807)" fill="#5ac0cf"/>
      <path id="Path_3435" data-name="Path 3435" d="M-546,172.663l-1.231,25.331h-37.116l8.633-25.331Z" transform="translate(591.942 -151.896)" fill="#3b556c"/>
      <path id="Path_3436" data-name="Path 3436" d="M-564.2,172.663l-7.3,25.331h-2.062l7.3-25.331Z" transform="translate(593.358 -151.896)" fill="#417485"/>
      <path id="Path_3437" data-name="Path 3437" d="M-570.013,197.994l7.3-25.331h-.688l-7.3,25.331Z" transform="translate(593.734 -151.896)" fill="#417485"/>
      <path id="Path_3438" data-name="Path 3438" d="M-557.858,197.994l7.3-25.331h-.687l-7.3,25.331Z" transform="translate(595.33 -151.896)" fill="#417485"/>
      <path id="Path_3439" data-name="Path 3439" d="M-573.871,170.663l-.789,2.283h-10.373l-2.852,2.832h.992v12.153h-4.166V175.778l5.1-5.075v-.04Z" transform="translate(591.06 -152.159)" fill="#417485"/>
      <path id="Path_3440" data-name="Path 3440" d="M-537.667,224.191a14.456,14.456,0,0,1-14.457,14.456,14.456,14.456,0,0,1-14.457-14.456,14.457,14.457,0,0,1,14.457-14.457A14.457,14.457,0,0,1-537.667,224.191Z" transform="translate(594.275 -147.028)" fill="#3b536d"/>
      <path id="Path_3441" data-name="Path 3441" d="M-542.7,223.667a9.947,9.947,0,0,1-9.947,9.948,9.946,9.946,0,0,1-9.946-9.948,9.946,9.946,0,0,1,9.946-9.945A9.947,9.947,0,0,1-542.7,223.667Z" transform="translate(594.798 -146.504)" fill="#ededed"/>
      <path id="Path_3442" data-name="Path 3442" d="M-545.071,223.421a7.823,7.823,0,0,1-7.823,7.823,7.822,7.822,0,0,1-7.823-7.823,7.822,7.822,0,0,1,7.823-7.821A7.823,7.823,0,0,1-545.071,223.421Z" transform="translate(595.045 -146.258)" fill="#8f9897"/>
      <path id="Path_3443" data-name="Path 3443" d="M-546.153,223.309a6.854,6.854,0,0,1-6.854,6.855,6.855,6.855,0,0,1-6.855-6.855,6.854,6.854,0,0,1,6.855-6.855A6.853,6.853,0,0,1-546.153,223.309Z" transform="translate(595.157 -146.146)" fill="#ededed"/>
      <path id="Path_3444" data-name="Path 3444" d="M-553.176,218.021a.562.562,0,0,1-.561.563.562.562,0,0,1-.562-.563.561.561,0,0,1,.562-.561A.561.561,0,0,1-553.176,218.021Z" transform="translate(595.887 -146.014)" fill="#3b536d"/>
      <path id="Path_3445" data-name="Path 3445" d="M-553.176,227.135a.562.562,0,0,1-.561.563.562.562,0,0,1-.562-.563.561.561,0,0,1,.562-.561A.561.561,0,0,1-553.176,227.135Z" transform="translate(595.887 -144.817)" fill="#3b536d"/>
      <path id="Path_3446" data-name="Path 3446" d="M-555.528,218.352a.561.561,0,0,1-.206.767.561.561,0,0,1-.768-.206.563.563,0,0,1,.2-.768A.563.563,0,0,1-555.528,218.352Z" transform="translate(595.588 -145.934)" fill="#3b536d"/>
      <path id="Path_3447" data-name="Path 3447" d="M-550.971,226.244a.562.562,0,0,1-.206.768.563.563,0,0,1-.768-.2.565.565,0,0,1,.2-.77A.566.566,0,0,1-550.971,226.244Z" transform="translate(596.187 -144.897)" fill="#3b536d"/>
      <path id="Path_3448" data-name="Path 3448" d="M-557.4,219.814a.562.562,0,0,1,.2.768.562.562,0,0,1-.767.206.564.564,0,0,1-.207-.768A.563.563,0,0,1-557.4,219.814Z" transform="translate(595.369 -145.714)" fill="#3b536d"/>
      <path id="Path_3449" data-name="Path 3449" d="M-549.51,224.371a.562.562,0,0,1,.207.768.564.564,0,0,1-.769.206.564.564,0,0,1-.2-.768A.56.56,0,0,1-549.51,224.371Z" transform="translate(596.406 -145.116)" fill="#3b536d"/>
      <path id="Path_3450" data-name="Path 3450" d="M-558.294,222.017a.562.562,0,0,1,.562.561.562.562,0,0,1-.562.563.562.562,0,0,1-.562-.563A.561.561,0,0,1-558.294,222.017Z" transform="translate(595.289 -145.415)" fill="#3b536d"/>
      <path id="Path_3451" data-name="Path 3451" d="M-549.18,222.017a.561.561,0,0,1,.561.561.562.562,0,0,1-.561.563.562.562,0,0,1-.562-.563A.561.561,0,0,1-549.18,222.017Z" transform="translate(596.486 -145.415)" fill="#3b536d"/>
      <path id="Path_3452" data-name="Path 3452" d="M-557.964,224.371a.56.56,0,0,1,.767.206.563.563,0,0,1-.2.768.565.565,0,0,1-.769-.206A.563.563,0,0,1-557.964,224.371Z" transform="translate(595.369 -145.116)" fill="#3b536d"/>
      <path id="Path_3453" data-name="Path 3453" d="M-550.073,219.814a.562.562,0,0,1,.769.206.563.563,0,0,1-.207.768.562.562,0,0,1-.767-.206A.563.563,0,0,1-550.073,219.814Z" transform="translate(596.406 -145.714)" fill="#3b536d"/>
      <path id="Path_3454" data-name="Path 3454" d="M-556.5,226.244a.565.565,0,0,1,.768-.207.565.565,0,0,1,.206.77.564.564,0,0,1-.769.2A.562.562,0,0,1-556.5,226.244Z" transform="translate(595.588 -144.897)" fill="#3b536d"/>
      <path id="Path_3455" data-name="Path 3455" d="M-551.945,218.352a.561.561,0,0,1,.768-.207.563.563,0,0,1,.206.768.563.563,0,0,1-.769.206A.561.561,0,0,1-551.945,218.352Z" transform="translate(596.187 -145.934)" fill="#3b536d"/>
      <path id="Path_3456" data-name="Path 3456" d="M-552.71,222.626a.978.978,0,0,1-.979.979.978.978,0,0,1-.977-.979.977.977,0,0,1,.977-.976A.978.978,0,0,1-552.71,222.626Z" transform="translate(595.839 -145.463)" fill="#3b536d"/>
      <path id="Path_3491" data-name="Path 3491" d="M-554.958,199.731h2.8a.716.716,0,0,0,.713-.713h0a.713.713,0,0,0-.713-.712h-2.8a.713.713,0,0,0-.713.712h0A.716.716,0,0,0-554.958,199.731Z" transform="translate(595.707 -148.529)" fill="#3b556c"/>
      <rect id="Rectangle_2383" data-name="Rectangle 2383" width="19.31" height="1.037" transform="translate(26.771 11.647)" fill="#417485"/>
      <path id="Path_3492" data-name="Path 3492" d="M-527.722,214.529a6.953,6.953,0,0,1-6.954,6.952,6.952,6.952,0,0,1-6.954-6.952,6.954,6.954,0,0,1,6.954-6.955A6.955,6.955,0,0,1-527.722,214.529Z" transform="translate(597.551 -147.312)" fill="#ededed"/>
      <rect id="Rectangle_2384" data-name="Rectangle 2384" width="100.702" height="21.588" transform="translate(71.128 59.953)" fill="#e9e9ea"/>
      <rect id="Rectangle_2385" data-name="Rectangle 2385" width="100.702" height="4.093" transform="translate(71.127 59.953)" fill="#dadadb"/>
      <rect id="Rectangle_2386" data-name="Rectangle 2386" width="100.702" height="4.093" transform="translate(71.127 77.447)" fill="#dadadb"/>
      <g id="Group_7469" data-name="Group 7469" transform="translate(176.114 57.43)">
        <path id="Path_3430" data-name="Path 3430" d="M-414.41,214.647v13.873l-67.367-.065V217.207l1.256-.536,9.347-9.686H-421.8Z" transform="translate(482.78 -204.819)" fill="#45919e"/>
        <path id="Path_3457" data-name="Path 3457" d="M-446.16,224.191a14.457,14.457,0,0,1-14.458,14.456,14.455,14.455,0,0,1-14.456-14.456,14.456,14.456,0,0,1,14.456-14.457A14.458,14.458,0,0,1-446.16,224.191Z" transform="translate(481.359 -204.458)" fill="#3b536d"/>
        <path id="Path_3458" data-name="Path 3458" d="M-451.194,223.667a9.947,9.947,0,0,1-9.948,9.948,9.947,9.947,0,0,1-9.947-9.948,9.947,9.947,0,0,1,9.947-9.945A9.946,9.946,0,0,1-451.194,223.667Z" transform="translate(481.882 -203.935)" fill="#ededed"/>
        <path id="Path_3459" data-name="Path 3459" d="M-453.564,223.421a7.823,7.823,0,0,1-7.824,7.823,7.822,7.822,0,0,1-7.822-7.823,7.822,7.822,0,0,1,7.822-7.821A7.822,7.822,0,0,1-453.564,223.421Z" transform="translate(482.129 -203.688)" fill="#8f9897"/>
        <path id="Path_3460" data-name="Path 3460" d="M-454.646,223.309a6.854,6.854,0,0,1-6.855,6.855,6.854,6.854,0,0,1-6.852-6.855,6.853,6.853,0,0,1,6.852-6.855A6.854,6.854,0,0,1-454.646,223.309Z" transform="translate(482.241 -203.576)" fill="#ededed"/>
        <path id="Path_3461" data-name="Path 3461" d="M-461.667,218.021a.563.563,0,0,1-.563.563.563.563,0,0,1-.562-.563.563.563,0,0,1,.562-.561A.562.562,0,0,1-461.667,218.021Z" transform="translate(482.971 -203.444)" fill="#3b536d"/>
        <path id="Path_3462" data-name="Path 3462" d="M-461.667,227.135a.563.563,0,0,1-.563.563.563.563,0,0,1-.562-.563.563.563,0,0,1,.562-.561A.562.562,0,0,1-461.667,227.135Z" transform="translate(482.971 -202.247)" fill="#3b536d"/>
        <path id="Path_3463" data-name="Path 3463" d="M-464.022,218.352a.561.561,0,0,1-.2.767.563.563,0,0,1-.769-.206.563.563,0,0,1,.207-.768A.56.56,0,0,1-464.022,218.352Z" transform="translate(482.672 -203.364)" fill="#3b536d"/>
        <path id="Path_3464" data-name="Path 3464" d="M-459.465,226.244a.562.562,0,0,1-.2.768.564.564,0,0,1-.769-.2.565.565,0,0,1,.207-.77A.563.563,0,0,1-459.465,226.244Z" transform="translate(483.27 -202.327)" fill="#3b536d"/>
        <path id="Path_3465" data-name="Path 3465" d="M-465.9,219.814a.562.562,0,0,1,.207.768.563.563,0,0,1-.768.206.563.563,0,0,1-.206-.768A.56.56,0,0,1-465.9,219.814Z" transform="translate(482.453 -203.145)" fill="#3b536d"/>
        <path id="Path_3466" data-name="Path 3466" d="M-458,224.371a.562.562,0,0,1,.206.768.565.565,0,0,1-.769.206.562.562,0,0,1-.2-.768A.561.561,0,0,1-458,224.371Z" transform="translate(483.49 -202.546)" fill="#3b536d"/>
        <path id="Path_3467" data-name="Path 3467" d="M-466.788,222.017a.562.562,0,0,1,.563.561.563.563,0,0,1-.563.563.563.563,0,0,1-.562-.563A.563.563,0,0,1-466.788,222.017Z" transform="translate(482.373 -202.845)" fill="#3b536d"/>
        <path id="Path_3468" data-name="Path 3468" d="M-457.674,222.017a.562.562,0,0,1,.563.561.563.563,0,0,1-.563.563.562.562,0,0,1-.562-.563A.562.562,0,0,1-457.674,222.017Z" transform="translate(483.57 -202.845)" fill="#3b536d"/>
        <path id="Path_3469" data-name="Path 3469" d="M-466.457,224.371a.561.561,0,0,1,.768.206.563.563,0,0,1-.207.768.562.562,0,0,1-.767-.206A.562.562,0,0,1-466.457,224.371Z" transform="translate(482.453 -202.546)" fill="#3b536d"/>
        <path id="Path_3470" data-name="Path 3470" d="M-458.565,219.814a.563.563,0,0,1,.769.206.563.563,0,0,1-.206.768.563.563,0,0,1-.768-.206A.561.561,0,0,1-458.565,219.814Z" transform="translate(483.49 -203.145)" fill="#3b536d"/>
        <path id="Path_3471" data-name="Path 3471" d="M-465,226.244a.566.566,0,0,1,.769-.207.565.565,0,0,1,.2.77.561.561,0,0,1-.767.2A.562.562,0,0,1-465,226.244Z" transform="translate(482.672 -202.327)" fill="#3b536d"/>
        <path id="Path_3472" data-name="Path 3472" d="M-460.439,218.352a.563.563,0,0,1,.769-.207.563.563,0,0,1,.2.768.56.56,0,0,1-.767.206A.561.561,0,0,1-460.439,218.352Z" transform="translate(483.27 -203.364)" fill="#3b536d"/>
        <path id="Path_3473" data-name="Path 3473" d="M-461.2,222.626a.978.978,0,0,1-.979.979.978.978,0,0,1-.978-.979.977.977,0,0,1,.978-.976A.978.978,0,0,1-461.2,222.626Z" transform="translate(482.923 -202.894)" fill="#3b536d"/>
        <path id="Path_3474" data-name="Path 3474" d="M-420.6,224.191a14.457,14.457,0,0,1-14.458,14.456,14.455,14.455,0,0,1-14.456-14.456,14.456,14.456,0,0,1,14.456-14.457A14.458,14.458,0,0,1-420.6,224.191Z" transform="translate(484.715 -204.458)" fill="#3b536d"/>
        <path id="Path_3475" data-name="Path 3475" d="M-425.636,223.667a9.947,9.947,0,0,1-9.948,9.948,9.947,9.947,0,0,1-9.946-9.948,9.947,9.947,0,0,1,9.946-9.945A9.946,9.946,0,0,1-425.636,223.667Z" transform="translate(485.238 -203.935)" fill="#ededed"/>
        <path id="Path_3476" data-name="Path 3476" d="M-428.006,223.421a7.823,7.823,0,0,1-7.824,7.823,7.822,7.822,0,0,1-7.822-7.823,7.822,7.822,0,0,1,7.822-7.821A7.822,7.822,0,0,1-428.006,223.421Z" transform="translate(485.485 -203.688)" fill="#8f9897"/>
        <path id="Path_3477" data-name="Path 3477" d="M-429.088,223.309a6.854,6.854,0,0,1-6.855,6.855,6.854,6.854,0,0,1-6.852-6.855,6.853,6.853,0,0,1,6.852-6.855A6.854,6.854,0,0,1-429.088,223.309Z" transform="translate(485.597 -203.576)" fill="#ededed"/>
        <path id="Path_3478" data-name="Path 3478" d="M-436.11,218.021a.563.563,0,0,1-.563.563.562.562,0,0,1-.561-.563.562.562,0,0,1,.561-.561A.562.562,0,0,1-436.11,218.021Z" transform="translate(486.327 -203.444)" fill="#3b536d"/>
        <path id="Path_3479" data-name="Path 3479" d="M-436.11,227.135a.563.563,0,0,1-.563.563.562.562,0,0,1-.561-.563.562.562,0,0,1,.561-.561A.562.562,0,0,1-436.11,227.135Z" transform="translate(486.327 -202.247)" fill="#3b536d"/>
        <path id="Path_3480" data-name="Path 3480" d="M-438.464,218.352a.561.561,0,0,1-.2.767.563.563,0,0,1-.769-.206.563.563,0,0,1,.207-.768A.56.56,0,0,1-438.464,218.352Z" transform="translate(486.028 -203.364)" fill="#3b536d"/>
        <path id="Path_3481" data-name="Path 3481" d="M-433.907,226.244a.562.562,0,0,1-.2.768.564.564,0,0,1-.769-.2.565.565,0,0,1,.207-.77A.563.563,0,0,1-433.907,226.244Z" transform="translate(486.627 -202.327)" fill="#3b536d"/>
        <path id="Path_3482" data-name="Path 3482" d="M-440.338,219.814a.562.562,0,0,1,.207.768.564.564,0,0,1-.768.206.563.563,0,0,1-.206-.768A.56.56,0,0,1-440.338,219.814Z" transform="translate(485.809 -203.145)" fill="#3b536d"/>
        <path id="Path_3483" data-name="Path 3483" d="M-432.444,224.371a.562.562,0,0,1,.206.768.565.565,0,0,1-.769.206.562.562,0,0,1-.2-.768A.561.561,0,0,1-432.444,224.371Z" transform="translate(486.846 -202.546)" fill="#3b536d"/>
        <path id="Path_3484" data-name="Path 3484" d="M-441.23,222.017a.562.562,0,0,1,.563.561.563.563,0,0,1-.563.563.562.562,0,0,1-.561-.563A.562.562,0,0,1-441.23,222.017Z" transform="translate(485.729 -202.845)" fill="#3b536d"/>
        <path id="Path_3485" data-name="Path 3485" d="M-432.116,222.017a.562.562,0,0,1,.563.561.563.563,0,0,1-.563.563.562.562,0,0,1-.561-.563A.562.562,0,0,1-432.116,222.017Z" transform="translate(486.926 -202.845)" fill="#3b536d"/>
        <path id="Path_3486" data-name="Path 3486" d="M-440.9,224.371a.562.562,0,0,1,.768.206.563.563,0,0,1-.207.768.562.562,0,0,1-.767-.206A.562.562,0,0,1-440.9,224.371Z" transform="translate(485.809 -202.546)" fill="#3b536d"/>
        <path id="Path_3487" data-name="Path 3487" d="M-433.007,219.814a.563.563,0,0,1,.769.206.563.563,0,0,1-.206.768.563.563,0,0,1-.768-.206A.561.561,0,0,1-433.007,219.814Z" transform="translate(486.846 -203.145)" fill="#3b536d"/>
        <path id="Path_3488" data-name="Path 3488" d="M-439.438,226.244a.566.566,0,0,1,.769-.207.565.565,0,0,1,.2.77.561.561,0,0,1-.767.2A.562.562,0,0,1-439.438,226.244Z" transform="translate(486.028 -202.327)" fill="#3b536d"/>
        <path id="Path_3489" data-name="Path 3489" d="M-434.881,218.352a.563.563,0,0,1,.769-.207.563.563,0,0,1,.2.768.56.56,0,0,1-.767.206A.561.561,0,0,1-434.881,218.352Z" transform="translate(486.627 -203.364)" fill="#3b536d"/>
        <path id="Path_3490" data-name="Path 3490" d="M-435.646,222.626a.978.978,0,0,1-.979.979.978.978,0,0,1-.977-.979.977.977,0,0,1,.977-.976A.978.978,0,0,1-435.646,222.626Z" transform="translate(486.279 -202.894)" fill="#3b536d"/>
        <rect id="Rectangle_2387" data-name="Rectangle 2387" width="1.432" height="46.708" transform="translate(58.552 1.44) rotate(90)" fill="#c1c1c2"/>
        <rect id="Rectangle_2388" data-name="Rectangle 2388" width="1.432" height="46.708" transform="translate(58.552 0.008) rotate(90)" fill="#dadadb"/>
        <path id="Path_3493" data-name="Path 3493" d="M-466.887,206.938l-11.842,11.841-1.013-1.012,11.423-11.423h.84Z" transform="translate(480.746 -204.904)" fill="#c1c1c2"/>
        <path id="Path_3494" data-name="Path 3494" d="M-430.653,206.938l11.842,11.841,1.013-1.012-11.423-11.423h-.84Z" transform="translate(487.192 -204.904)" fill="#c1c1c2"/>
        <path id="Path_3495" data-name="Path 3495" d="M-467.789,206.083l-.4.426h-.02l-.566.564v.02l-.142.144h-.02l-10.677,10.7-1.011-1.009.647-.647,9.019-9.039,2.164-2.165Z" transform="translate(480.629 -205.071)" fill="#dadadb"/>
        <path id="Path_3496" data-name="Path 3496" d="M-416.916,216.922l-1.011,1.009-1.012-1.009v-.02l-10.394-10.394-.425-.426,1.012-1.012,2.163,2.165,7.643,7.662Z" transform="translate(487.309 -205.071)" fill="#dadadb"/>
        <path id="Path_3497" data-name="Path 3497" d="M-420.185,217.517V232.5h.632l1.391-17.014Z" transform="translate(488.566 -203.702)" fill="#c1c1c2"/>
      </g>
    </g>
  </g>
</svg>
