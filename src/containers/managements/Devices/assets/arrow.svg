<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="38" height="38" viewBox="0 0 38 38">
  <defs>
    <filter id="Ellipse_913" x="0" y="0" width="38" height="38" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.11"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="arrow" transform="translate(9 6)">
    <g transform="matrix(1, 0, 0, 1, -9, -6)" filter="url(#Ellipse_913)">
      <circle id="Ellipse_913-2" data-name="Ellipse 913" cx="10" cy="10" r="10" transform="translate(9 6)" fill="#fff"/>
    </g>
    <g id="Icon_ionic-ios-arrow-dropup" data-name="Icon ionic-ios-arrow-dropup" transform="translate(15.239 14.086) rotate(180)">
      <path id="Path_3284" data-name="Path 3284" d="M.266,6.073a.917.917,0,0,1,0-1.292L4.795.266A.912.912,0,0,1,6.054.238l4.462,4.448A.912.912,0,1,1,9.229,5.978L5.4,2.2,1.558,6.078A.913.913,0,0,1,.266,6.073Z" fill="#7686a1"/>
    </g>
  </g>
</svg>
