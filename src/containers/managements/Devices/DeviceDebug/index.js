import React, { Suspense, lazy } from 'react';
import FileDoneOutlined from '@ant-design/icons/FileDoneOutlined';
import moment from 'moment-timezone';
import {
	deviceDebug,
	deviceErrorRawData,
	deviceDebugPieError,
	deviceDebugErrorLog,
	deviceDebugNetworkData,
	deviceDebugOfflineTrend,
	deviceDebugOfflineLog,
	deviceDebugDataFiles,
} from '@datoms/js-sdk';
import _isEmpty from 'lodash/isEmpty';
import _orderBy from 'lodash/orderBy';
import _find from 'lodash/find';
import _countBy from 'lodash/countBy';
import _filter from 'lodash/filter';

import PropTypes from 'prop-types';
import queryString from 'query-string';

/*Components*/
import AntLayout from '@datoms/react-components/src/components/AntLayout';
import AntDivider from '@datoms/react-components/src/components/AntDivider';
import AntTabs from '@datoms/react-components/src/components/AntTabs';
import AntTabPane from '@datoms/react-components/src/components/AntTabPane';
import AntAlert from '@datoms/react-components/src/components/AntAlert';
import AntSpin from '@datoms/react-components/src/components/AntSpin';
import GraphHighcharts from '@datoms/react-components/src/components/GraphHighcharts';
import Report from '@datoms/react-components/src/components/Report';
import AntRangePicker from '@datoms/react-components/src/components/AntRangePicker';
import AntNotification from '@datoms/react-components/src/components/AntNotification';
import SelectWithRangepicker from '@datoms/react-components/src/components/SelectWithRangepicker';
import SelectWithFilter from '@datoms/react-components/src/components/SelectWithFilter';

/*Configs*/
import DeviceDebugObjectData from './configuration/ThingDeviceDebugObjectData';
import DeviceDebugReportObjectData from './configuration/ThingDeviceDebugReportObjectData';
import DeviceErrorReportObjectData from './configuration/ThingDeviceErrorReportObjectData';

/*Images*/
import AcPowerIcon from '../assets/debug-icons/AcMode.svg';
import ThunderboltIcon from '../assets/thunderbolt_icon.svg';
import ThunderboltIconInactive from '../assets/thunderbolt_icon_inactive.svg';
import WifiSignalRounded from '../assets/wifi_signal_rounded.svg';
import WifiSignalRounded1 from '../assets/wifi-signal/wifi_signal_rounded_1.svg';
import WifiSignalRounded2 from '../assets/wifi-signal/wifi_signal_rounded_2.svg';
import WifiSignalRounded3 from '../assets/wifi-signal/wifi_signal_rounded_3.svg';
import GprsSignalRounded from '../assets/gprs_signal_rounded.svg';
import GprsSignalRounded2 from '../assets/gprs-signal/gprs_signal_rounded_2.svg';
import GprsSignalRounded4 from '../assets/gprs-signal/gprs_signal_rounded_4.svg';
import GprsSignalRounded5 from '../assets/gprs-signal/gprs_signal_rounded_5.svg';
import EthernetCableIcon from '../assets/ethernet_cable_icon.svg';

import './style.less';
import GraphObjectData from './configuration/GraphObjectData';
import Highcharts from 'highcharts';

import Online from '../assets/debug-icons/Online.svg';
import Offline from '../assets/debug-icons/Offline.svg';

const CustomTable = lazy(() => import('./imports/CustomTable'));
const DataFileDrawer = lazy(() => import('./imports/DataFileDrawer'));

function disabledDate(current) {
	// Can not select future dates
	return current && current >= moment().endOf('day');
}

const datePickerConfig = {
	placeholder: ['From', 'To'],
	size: 'default',
	disabledDate: disabledDate,
	showTime: false,
	separator: ' - ',
	format: 'DD-MMM-YYYY',
};

// var moment = require('moment-timezone');
moment.tz.setDefault('Asia/Kolkata');

function getRanges(upto_time) {
	let ranges = {
		'Last 3 Months': [
			moment
				.unix(upto_time)
				.startOf('month')
				.subtract(2, 'M')
				.tz('Asia/Kolkata'),
			moment(upto_time * 1000).endOf('day'),
		],
		'This Month': [
			moment.unix(upto_time).startOf('month'),
			moment(upto_time * 1000).endOf('day'),
		],
		'Last 15 Days': [
			moment((upto_time - 15 * 86400) * 1000).startOf('day'),
			moment(upto_time * 1000).endOf('day'),
		],
	};

	let offline_trend_ranges = {
		'Last 3 Days': [
			moment((upto_time - 3 * 86400) * 1000),
			moment(upto_time * 1000),
		],
	};

	return {
		ranges: ranges,
		offline_trend_ranges: offline_trend_ranges,
	};
}

export default class DeviceDebug extends React.Component {
	static defaultProps = {
		clientId: 0,
		collapsed: true,
	};

	/**
	 * @props {object} deviceDetails -  Selected thing details, like id, name, type, with devices array
	 * @props {number} clientId - Current client id
	 * @props {bool} collapsed - Sider menu is collapsed or not
	 * @props {function} closeDebug - drawer close function
	 */

	static propTypes = {
		clientId: PropTypes.number.isRequired,
		collapsed: PropTypes.bool.isRequired,
		closeDebug: PropTypes.func.isRequired,
	};

	/**
	 * @state {number} from_date - From date of selected date range for debug data
	 * @state {number} from_date_copy - Copy of from date of selected date range for debug data to show in range picker
	 * @state {number} upto_date - Upto date of selected date range for debug data
	 * @state {number} upto_date_copy - Copy of upto date of selected date range for debug data to show in range picker
	 * @state {object} device_debug_object_data - Debug page view object
	 * @state {object} device_select - Device select configuration
	 * @state {number} selected_device - Id of selected device
	 * @state {object} device_debug_data - Device debug all data
	 * @state {object} device_details - Device details like status, last data receive time etc from debug api
	 * @state {object} battery_details - Battery details like mode, percentage, charging status etc from debug api
	 * @state {object} network_details - Network details like mode, signal strength, network usage, network usage distribution etc from debug api
	 * @state {array} network_usage - Network usage details for network usage graph from debug api
	 * @state {array} device_error_logs - Device error array from debug api
	 * @state {array} device_error_filters - Device error filter types array
	 * @state {string} device_filter - Device error log selected filter type
	 * @state {number} raw_log_from_time - From time of selected date range for device error raw data
	 * @state {number} raw_log_from_time_copy - From time copy of selected date range for device error raw data to revert data if more than 24 hour
	 * @state {number} raw_log_upto_time - Upto time of selected date range for device error raw data
	 * @state {number} raw_log_upto_time_copy - Upto time copy of selected date range for device error raw data to revert data if more than 24 hour
	 * @state {array} device_error_raw_logs - Device error raw data array
	 * @state {array} sensor_error_logs - Sensor error array from debug api
	 * @state {array} data_availability - Data availability array from debug api
	 * @state {array} hourly_data_availability - 24 hour data availability array from debug api
	 * @state {number} selected_date - Selected date in monthly data
	 * @state {number} selected_time - Selected time for 24 hour data
	 * @state {array} device_error_filters - Device error array from debug api
	 * @state {string} sensor_filter - Sensor error log selected filter type
	 * @state {bool} csv_button_loading - Report should download or not
	 * @state {bool} daily_data_loading - Loading status of daily data section
	 * @state {object} device_error_raw_logs_report_object - Report controller component object
	 * @state {bool} csv_report_download - Status for report should download or not id device error raw log api call function
	 * @state {number} device_error_page_no - Device error table current page number
	 * @state {number} device_error_page_size - Device error table current per page size
	 * @state {number} device_raw_error_page_no - Device raw error table current page number
	 * @state {number} device_raw_error_page_size - Device raw error table current per page size
	 * @state {number} sensor_error_page_no - Sensor error table current page number
	 * @state {number} sensor_error_page_size - Sensor error table current per page size
	 * @state {object} thing_debug_data - Thing debug all data
	 */

	constructor(props) {
		super(props);

		this.parsed = props.location
			? queryString.parse(props.location.search)
			: '';

		this.device_id = this.parsed['device_id']? this.parsed['device_id'] : '';
		this.device_qr_code = this.parsed['qr_code']? this.parsed['qr_code'] : '';
		this.selected_range = this.parsed['selected-range']
			? this.parsed['selected-range']
			: '';

		let fromDate = this.selected_range
				? moment(this.selected_range.split('to')[0], 'DD-MM-YYYY')
						.startOf('day')
						.unix()
				: moment().subtract(3, 'M').startOf('day').unix(),
			uptoDate = this.selected_range
				? moment(this.selected_range.split('to')[1], 'DD-MM-YYYY')
						.endOf('day')
						.unix()
				: moment().endOf('day').unix();

		this.offline_trend_from_date = moment().unix() - 3 * 86400;
		this.offline_trend_upto_date = moment().unix();

		this.network_detail_from_date = moment()
			.startOf('month')
			.subtract(2, 'M')
			.unix();
		this.network_detail_upto_date = moment().endOf('day').unix();

		this.device_error_from_date = moment()
			.startOf('month')
			.subtract(2, 'M')
			.unix();
		this.device_error_upto_date = moment().endOf('day').unix();

		this.offline_log_from_date = moment()
			.startOf('month')
			.subtract(2, 'M')
			.unix();
		this.offline_log_upto_date = moment().endOf('day').unix();

		this.state = {
			graphObjectData: GraphObjectData,
			//max_signal_value: 25,
			from_date: fromDate,
			from_date_copy: fromDate,
			upto_date: uptoDate,
			upto_date_copy: uptoDate,
			device_debug_object_data: { ...DeviceDebugObjectData },
			selected_device: this.device_id
					? [this.device_id]
					: [0],
			device_details: {},
			battery_details: {},
			network_details: {},
			network_usage: [],
			device_debug_data: {},
			device_error_logs: [],
			device_error_filters: [...DeviceDebugObjectData.device_filter],
			device_filter: 'total_error',
			device_error_raw_logs: undefined,
			raw_log_from_time: 0,
			raw_log_from_time_copy: 0,
			raw_log_upto_time: 0,
			raw_log_upto_time_copy: 0,
			sensor_error_filters: [...DeviceDebugObjectData.sensor_filter],
			csv_button_loading: false,
			device_csv_button_loading: false,
			device_error_raw_logs_report_object: {},
			device_error_report_object: {},
			csv_report_download: false,
			device_csv_report_download: false,
			device_error_page_no: 1,
			device_error_page_size:
				DeviceDebugObjectData.device_error_table.config.pagination_data
					.pageSize,
			device_raw_error_page_no: 1,
			device_raw_error_page_size:
				DeviceDebugObjectData.device_error_raw_table.config
					.pagination_data.pageSize,
			device_offline_logs: undefined,
			device_offline_page_no: 1,
			device_offline_page_size:
				DeviceDebugObjectData.device_offline_table.config
					.pagination_data.pageSize,
			show_data_drawer: false,
			url_text: '',
			filter_value: {},
			error_logs: undefined,
			total_error_count: 0,
			activeKey: [],
			error_cumulative: {},
			data_files_received: 0,
			historical_data_count: 0,
			realtime_data_count: 0,
			error_sub_types: {
				network_sub_types: [],
				power_sub_types: [],
				sensor_sub_types: [],
				other_sub_types: [],
			},
			offline_trends_data: undefined,
			//offline_trends_debug_data: undefined,
			error_config: {},
			offline_graph_loading: true,
			network_loading: true,
			device_error_loading: true,
			offline_log_loading: true,
			sync_graph_data: undefined,
			is_custom_offline_trend: false,
			is_custom_network_detail: false,
			is_custom_device_error: false,
			is_custom_offline_log: false,
			selected_tab: 'offline_trend',
		};

		this.from_date_backup = fromDate;
		this.upto_date_backup = uptoDate;
		this.raw_log_from_time_copy = moment().startOf('day').unix();
		this.raw_log_upto_time_copy = moment().endOf('day').unix();

		this.platform_slug = this.props.location
			? this.props.location.pathname &&
			  this.props.location.pathname.includes('/datoms-x')
				? '/datoms-x'
				: this.props.location.pathname.includes('/iot-platform')
				? '/iot-platform'
				: '/datoms-x'
			: '';
		this.retriveDeviceDebug = this.retriveDeviceDebug.bind(this);
		this.networkGraphData = this.networkGraphData.bind(this);
		// this.handleDebugCalenderChange = this.handleDebugCalenderChange.bind(this);
		// this.handleDebugCalenderOk = this.handleDebugCalenderOk.bind(this);
		this.handleRawErrorCalenderChange = this.handleRawErrorCalenderChange.bind(
			this
		);
		this.handleRawErrorCalenderOk = this.handleRawErrorCalenderOk.bind(
			this
		);
		//this.onDeviceFilterClick = this.onDeviceFilterClick.bind(this);
		//this.onSensorFilterClick = this.onSensorFilterClick.bind(this);
		this.fetchDeviceErrorRawData = this.fetchDeviceErrorRawData.bind(this);
		this.downloadDeviceErrorReport = this.downloadDeviceErrorReport.bind(
			this
		);
		this.downloadDeviceReport = this.downloadDeviceReport.bind(this);
		this.openNotification = this.openNotification.bind(this);
		this.downloadRawReportClose = this.downloadRawReportClose.bind(this);
		this.downloadDeviceReportClose = this.downloadDeviceReportClose.bind(
			this
		);
		this.doNothing = this.doNothing.bind(this);
		this.deviceErrorPagination = this.deviceErrorPagination.bind(this);
		this.rawErrorPagination = this.rawErrorPagination.bind(this);
		//this.sensorErrorPagination = this.sensorErrorPagination.bind(this);
		// this.onDebugCalendarOpenChange = this.onDebugCalendarOpenChange.bind(this);
		this.onRawCalendarOpenChange = this.onRawCalendarOpenChange.bind(this);
		this.fetchDeviceOfflineLogs = this.fetchDeviceOfflineLogs.bind(this);
		this.offlineLogsPagination = this.offlineLogsPagination.bind(this);
		//this.networkDeviceErrorDateRange = this.networkDeviceErrorDateRange.bind(this);
		this.getPieColor = this.getPieColor.bind(this);
		this.pieChartGraphData = this.pieChartGraphData.bind(this);
		this.getOfflineTrendGraphDataset = this.getOfflineTrendGraphDataset.bind(
			this
		);
		//this.syncExtremes = this.syncExtremes.bind(this);
		this.getOfflineTrendGraphDataStructure = this.getOfflineTrendGraphDataStructure.bind(
			this
		);
		this.fetchOfflineTrend = this.fetchOfflineTrend.bind(this);
		this.onFilterChange = this.onFilterChange.bind(this);
		this.fetchPieChartData = this.fetchPieChartData.bind(this);
		this.fetchFilterErrorLogs = this.fetchFilterErrorLogs.bind(this);
		this.checkUserisEndCustomer = this.checkUserisEndCustomer.bind(this);
		this.onRangeChangeOfflineTrend = this.onRangeChangeOfflineTrend.bind(
			this
		);
		this.onRangeChangeNetworkDetail = this.onRangeChangeNetworkDetail.bind(
			this
		);
		this.onRangeChangeDeviceError = this.onRangeChangeDeviceError.bind(
			this
		);
		this.onRangeChangeOfflineLog = this.onRangeChangeOfflineLog.bind(this);
		this.getDeviceErrorCsvStructure = this.getDeviceErrorCsvStructure.bind(
			this
		);
		this.fetchDeviceNetworkSection = this.fetchDeviceNetworkSection.bind(
			this
		);
		this.fetchDataFileCount = this.fetchDataFileCount.bind(this);
		this.onOpenChangeOfflineTrend = this.onOpenChangeOfflineTrend.bind(
			this
		);
		this.onOpenChangeNetworkDetail = this.onOpenChangeNetworkDetail.bind(
			this
		);
		this.onOpenChangeDeviceError = this.onOpenChangeDeviceError.bind(this);
		this.onOpenChangeOfflineLog = this.onOpenChangeOfflineLog.bind(this);

		console.log('all_debug_props_', this.props);
	}

	async componentDidMount() {
		this.parsed_search = queryString.parse(this.props.location.search);

		let selectedTab =
			this.parsed_search &&
			Object.values(this.parsed_search).length &&
			this.parsed_search.tab
				? this.parsed_search.tab
				: 'offline_trend';

		if (this.device_id) {
			let [deviceDebugState] = await Promise.all([
				this.retriveDeviceDebug(),
			]);
			this.setState(Object.assign({}, deviceDebugState));
			// this.getOfflineTrendGraphDataStructure(
			// 	this.state.offline_trends_data
			// );
			this.fetchOfflineTrend();
			//this.fetchDeviceNetworkSection();
			//this.fetchDataFileCount();
			//this.fetchPieChartData();
			//this.fetchFilterErrorLogs();

			this.tabChange(selectedTab);
		}
	}

	/**
	 * Shows ant notification.
	 * @param {string} type - type of notification.
	 * @param {string} msg - message to be shown.
	 */
	openNotification(type, msg) {
		AntNotification({
			type: type,
			message: msg,
			// description: 'This is success notification',
			placement: 'bottomLeft',
			className: 'alert-' + type,
		});
	}

	onFilterChange(filterObj) {
		console.log('FILTER-VALUE', filterObj);

		let url_text = '';
		let error_name = [];

		if (Object.keys(filterObj).length == 0) {
			url_text = '';
		}
		// else if (Object.keys(filterObj).length == 1) {
		// 	if (Object.keys(filterObj)[0] == 'All_errors') url_text = '';
		// 	else {
		// 		if (filterObj[Object.keys(filterObj)[0]].length == 0) {
		// 			if (Object.keys(filterObj)[0] == 'Network_errors')
		// 				error_name = this.state.error_sub_types
		// 					.network_sub_types;
		// 			if (Object.keys(filterObj)[0] == 'Power_errors')
		// 				error_name = this.state.error_sub_types.power_sub_types;
		// 			if (Object.keys(filterObj)[0] == 'Sensor_errors')
		// 				error_name = this.state.error_sub_types
		// 					.sensor_sub_types;
		// 			if (Object.keys(filterObj)[0] == 'Other_errors')
		// 				error_name = this.state.error_sub_types.other_sub_types;
		// 		} else {
		// 			error_name = filterObj[Object.keys(filterObj)[0]];
		// 		}
		// 		url_text = '&error_name=[' + error_name + ']';
		// 	}
		// }
		else {
			for (let key in filterObj) {
				if (key == 'Network_errors') {
					if (filterObj[key].length != 0) {
						if (
							filterObj[key].length == 1 &&
							filterObj[key][0] == 'all'
						)
							error_name = error_name.concat(
								this.state.error_sub_types.network_sub_types
							);
						else error_name = error_name.concat(filterObj[key]);
					}
				}
				if (key == 'Power_errors') {
					if (filterObj[key].length != 0) {
						if (
							filterObj[key].length == 1 &&
							filterObj[key][0] == 'all'
						)
							error_name = error_name.concat(
								this.state.error_sub_types.power_sub_types
							);
						else error_name = error_name.concat(filterObj[key]);
					}
				}
				if (key == 'Sensor_errors') {
					if (filterObj[key].length != 0) {
						if (
							filterObj[key].length == 1 &&
							filterObj[key][0] == 'all'
						)
							error_name = error_name.concat(
								this.state.error_sub_types.sensor_sub_types
							);
						else error_name = error_name.concat(filterObj[key]);
					}
				}
				if (key == 'Other_errors') {
					if (filterObj[key].length != 0) {
						if (
							filterObj[key].length == 1 &&
							filterObj[key][0] == 'all'
						)
							error_name = error_name.concat(
								this.state.error_sub_types.other_sub_types
							);
						else error_name = error_name.concat(filterObj[key]);
					}
				}
			}

			url_text = '&error_name=[' + error_name + ']';
		}

		this.setState(
			{
				url_text: url_text,
				filter_value: filterObj,
				error_logs: undefined,
			},
			() => this.fetchFilterErrorLogs(url_text)
		);
	}

	async fetchPieChartData() {
		let error_cumulative = {};
		let network_sub_types = [],
			power_sub_types = [],
			sensor_sub_types = [],
			other_sub_types = [];
		let error_config = {
			// 'All Errors (0)': {
			// 	key: 'All_errors',
			// 	type: 'All_errors',
			// 	color: '#cacaca',
			// 	select_arr: [],
			// },
			'Network Errors (0)': {
				key: 'Network_errors',
				type: 'Network_errors',
				color: '#A9D9F7',
				select_arr: [],
			},
			'Power Errors (0)': {
				key: 'Power_errors',
				type: 'Power_errors',
				color: '#FBC7AC',
				select_arr: [],
			},
			'Sensor Errors (0)': {
				key: 'Sensor_errors',
				type: 'Sensor_errors',
				color: '#EECD96',
				select_arr: [],
			},
			'Other Errors (0)': {
				key: 'Other_errors',
				type: 'Other_errors',
				color: '#A1D7DD',
				select_arr: [],
			},
		};

		let response = await deviceDebugPieError({
			device_id: this.state.selected_device[0],
			from_date: this.device_error_from_date,
			// ? this.state.device_error_from_date
			// : this.device_error_from_date,
			upto_date: this.device_error_upto_date,
			// ? this.state.device_error_upto_date
			// : this.device_error_upto_date,
		});
		console.log('PIE RES', response);
		if (response.status === 403) {
			this.setState({
				unauthorised_access: true,
				unauthorised_access_msg: response.message,
				device_error_loading: false,
			});
		} else if (response.status === 'success') {
			if (!_isEmpty(response.error_cumulative)) {
				let total_err_count = 0,
					network_err_count = 0,
					power_err_count = 0,
					sensor_err_count = 0,
					other_err_count = 0;
				let network_select_arr = [],
					power_select_arr = [],
					sensor_select_arr = [],
					other_select_arr = [];
				let network_pie_arr = [],
					power_pie_arr = [],
					sensor_pie_arr = [],
					other_pie_arr = [];

				for (let key in response.error_cumulative) {
					if (parseInt(key) == 2) {
						network_err_count = parseInt(
							response.error_cumulative[key].total
						);

						response.error_cumulative[key].chart.map((ne) => {
							network_select_arr.push({
								id: ne.name,
								name: ne.name + ' (' + ne.value + ')',
							});
							network_pie_arr.push(ne);
							network_sub_types.push(ne.name);
						});

						error_cumulative.network = {
							total: network_err_count,
							chart: network_pie_arr,
						};
					} else if (parseInt(key) == 1) {
						power_err_count = parseInt(
							response.error_cumulative[key].total
						);

						response.error_cumulative[key].chart.map((pe) => {
							power_select_arr.push({
								id: pe.name,
								name: pe.name + ' (' + pe.value + ')',
							});
							power_pie_arr.push(pe);
							power_sub_types.push(pe.name);
						});

						error_cumulative.power = {
							total: power_err_count,
							chart: power_pie_arr,
						};
					} else if (parseInt(key) == 3) {
						sensor_err_count = parseInt(
							response.error_cumulative[key].total
						);

						response.error_cumulative[key].chart.map((se) => {
							sensor_select_arr.push({
								id: se.name,
								name: se.name + ' (' + se.value + ')',
							});
							sensor_pie_arr.push(se);
							sensor_sub_types.push(se.name);
						});

						error_cumulative.sensor = {
							total: sensor_err_count,
							chart: sensor_pie_arr,
						};
					} else {
						other_err_count =
							other_err_count +
							parseInt(response.error_cumulative[key].total);

						response.error_cumulative[key].chart.map((oe) => {
							other_select_arr.push({
								id: oe.name,
								name: oe.name + ' (' + oe.value + ')',
							});
							other_pie_arr.push(oe);
							other_sub_types.push(oe.name);
						});
					}
				}

				if (other_pie_arr.length) {
					error_cumulative.other = {
						total: other_err_count,
						chart: other_pie_arr,
					};
				}

				total_err_count =
					network_err_count +
					power_err_count +
					sensor_err_count +
					other_err_count;

				error_config = {
					// [`All Errors (${total_err_count})`]: {
					// 	key: 'All_errors',
					// 	type: 'All_errors',
					// 	color: '#cacaca',
					// 	select_arr: [],
					// },
					[`Network Errors (${network_err_count})`]: {
						key: 'Network_errors',
						type: 'Network_errors',
						color: '#A9D9F7',
						select_arr: network_select_arr,
					},
					[`Power Errors (${power_err_count})`]: {
						key: 'Power_errors',
						type: 'Power_errors',
						color: '#FBC7AC',
						select_arr: power_select_arr,
					},
					[`Sensor Errors (${sensor_err_count})`]: {
						key: 'Sensor_errors',
						type: 'Sensor_errors',
						color: '#EECD96',
						select_arr: sensor_select_arr,
					},
					[`Other Errors (${other_err_count})`]: {
						key: 'Other_errors',
						type: 'Other_errors',
						color: '#A1D7DD',
						select_arr: other_select_arr,
					},
				};
			}

			console.log('filter_value_', this.state.filter_value);

			let filterValue = {};

			Object.keys(error_config).map((key) => {
				Object.keys(this.state.filter_value).map((filterKey) => {
					if (error_config[key].type == filterKey) {
						if (this.state.filter_value[filterKey].length) {
							let filteredArray = this.state.filter_value[
								filterKey
							].filter((value) =>
								error_config[key].select_arr.includes(value)
							);
							filterValue[filterKey] = [];
							filterValue[filterKey] = filteredArray;
						} else {
							filterValue[filterKey] = [];
						}
					}
				});
			});

			console.log('filter_value_ 1', filterValue);

			this.setState({
				error_cumulative: error_cumulative,
				error_config: error_config,
				error_sub_types: {
					network_sub_types: network_sub_types,
					power_sub_types: power_sub_types,
					sensor_sub_types: sensor_sub_types,
					other_sub_types: other_sub_types,
				},
				filter_value: filterValue,
			});
		} else {
			this.openNotification('error', response.message);
			this.setState({
				unauthorised_access: false,
				error_API: true,
				error_API_msg: response.message,
				device_error_loading: false,
			});
		}
	}

	getPieColor(baseColor, count) {
		var colors = [],
			i;

		for (i = 0; i < count; i++) {
			colors.push(
				Highcharts.color(baseColor)
					.brighten(i / (count + 10))
					.get()
			);
		}
		return colors;
	}

	pieChartGraphData(chart_obj, baseColor) {
		let count = chart_obj.chart.length;

		let GraphObjectErrorPieChart = JSON.parse(
			JSON.stringify(this.state.graphObjectData)
		);

		GraphObjectErrorPieChart.graph_data.config.chart = {
			type: 'pie',
			height: window.innerWidth <= 1024 ? 250 : 200,
		};
		GraphObjectErrorPieChart.graph_data.config.legend.enabled = false;
		GraphObjectErrorPieChart.graph_data.config.legend.itemStyle.fontSize =
			'12px';
		GraphObjectErrorPieChart.graph_data.config.tooltip = {
			pointFormat: '<b>{point.y}</b>',
		};
		GraphObjectErrorPieChart.graph_data.config.responsive = {};
		GraphObjectErrorPieChart.graph_data.config.plotOptions = {
			pie: {
				colors: this.getPieColor(baseColor, count),
				allowPointSelect: false,
				cursor: '',
				dataLabels: {
					enabled: true,
					format: '<b>{point.name}</b><br>{point.y}',
					distance: 8,
					color: '#232323',
					style: {
						textShadow: false,
						textOutline: false,
						fontSize: 10,
						fontWeight: 'normal',
					},
				},
				showInLegend: false,
			},
		};
		GraphObjectErrorPieChart.graph_data.series_data = [
			{
				data: [],
			},
		];

		let pie_data = [];
		chart_obj.chart.map((ty) => {
			pie_data.push({
				name: ty.name,
				y: parseInt(ty.value),
			});
		});

		GraphObjectErrorPieChart.graph_data.series_data[0].data = pie_data;

		return GraphObjectErrorPieChart.graph_data;
	}

	getOfflineTrendGraphDataset(trend_graph_data) {
		let total_data = {
			xData: [],
			//xDataDebug: [],
			datasets: [
				{
					name: 'Device Status',
					data: [],
					type: 'area',
				},
				{
					name: 'Signal Strength',
					data: [],
					type: 'area',
				},
				{
					name: 'Device Power',
					data: [],
					type: 'area',
				},
				{
					name: 'Battery Voltage',
					data: [],
					type: 'area',
				},
			],
		};

		let sorted_trend_graph_data = _orderBy(
			trend_graph_data,
			['timestamp'],
			['asc']
		);

		sorted_trend_graph_data.map((tdata) => {
			total_data.xData.push(tdata.timestamp * 1000);

			total_data.datasets[0].data.push(tdata.status);

			total_data.datasets[1].data.push([
				tdata.network_type,
				tdata.signal_strength != null
					? parseFloat(tdata.signal_strength)
					: null,
			]);

			total_data.datasets[2].data.push(tdata.ac_power);

			total_data.datasets[3].data.push(
				tdata.battery_voltage != null
					? parseFloat(tdata.battery_voltage)
					: null
			);
		});

		console.log('TOTAL_DATA', total_data);

		return total_data;
	}

	// syncExtremes(e) {
	// 	var thisChart = this.chart;

	// 	if (e.trigger !== 'syncExtremes') {
	// 		// Prevent feedback loop
	// 		Highcharts.each(Highcharts.charts, function (chart) {
	// 			if (chart !== thisChart) {
	// 				if (chart.xAxis[0].setExtremes) {
	// 					// It is null while updating
	// 					chart.xAxis[0].setExtremes(
	// 						e.min,
	// 						e.max,
	// 						undefined,
	// 						false,
	// 						{ trigger: 'syncExtremes' }
	// 					);
	// 				}
	// 			}
	// 		});
	// 	}
	// }

	getOfflineTrendGraphDataStructure(trend_graph_data) {
		let total_data = this.getOfflineTrendGraphDataset(trend_graph_data),
			syncGraphData = [],
			that = this;

		total_data.datasets.forEach(function (dataset, i) {
			// Add X values
			let data1 = [],
				arr_wifi = [],
				arr_ethernet = [],
				arr_gprs = [];
			// let segmentColors = {
			// 	wifi: ['white', '#11A2D9'],
			// 	gprs: ['white', '#A0D911'],
			// 	ethernet: ['white', '#6E40F1'],
			// };

			// if (dataset.name === 'Device Status') {
			// 	for (let k = 0; k < dataset.data.length; k++) {
			// 		data1.push([total_data.xDataStatus[k], dataset.data[k]]);
			// 	}
			if (dataset.name === 'Signal Strength') {
				//let prev_net = dataset.data[0][0];
				// for (let k = 0; k < dataset.data.length; k++) {
				// 	if (dataset.data[k][0] == 'wifi') {
				// 		data1.push({
				// 			x: total_data.xData[k],
				// 			y: dataset.data[k][1],
				// 			// segmentColor:
				// 			// 	prev_net == 'wifi'
				// 			// 		? segmentColors['wifi'][1]
				// 			// 		: segmentColors['wifi'][0],
				// 			color: segmentColors['wifi'][1],
				// 		});
				// 	}
				// 	if (dataset.data[k][0] == 'gprs') {
				// 		data1.push({
				// 			x: total_data.xData[k],
				// 			y: dataset.data[k][1],
				// 			// segmentColor:
				// 			// 	prev_net == 'gprs'
				// 			// 		? segmentColors['gprs'][1]
				// 			// 		: segmentColors['gprs'][0],
				// 			color: segmentColors['gprs'][1],
				// 		});
				// 	}
				// 	if (dataset.data[k][0] == 'ethernet') {
				// 		data1.push({
				// 			x: total_data.xData[k],
				// 			y: dataset.data[k][1],
				// 			// segmentColor:
				// 			// 	prev_net == 'ethernet'
				// 			// 		? segmentColors['ethernet'][1]
				// 			// 		: segmentColors['ethernet'][0],
				// 			color: segmentColors['ethernet'][1],
				// 		});
				// 	}
				// 	if (dataset.data[k][0] == null) {
				// 		data1.push({
				// 			x: total_data.xData[k],
				// 			y: dataset.data[k][1],
				// 		});
				// 	}
				// 	//prev_net = dataset.data[k][0];
				// }
				//console.log('sigg', data1);
				for (let k = 0; k < dataset.data.length; k++) {
					if (dataset.data[k][0] == 'wifi') {
						arr_wifi.push([
							total_data.xData[k],
							dataset.data[k][1],
						]);
					} else {
						arr_wifi.push([total_data.xData[k], null]);
					}

					if (dataset.data[k][0] == 'ethernet') {
						arr_ethernet.push([
							total_data.xData[k],
							dataset.data[k][1],
						]);
					} else {
						arr_ethernet.push([total_data.xData[k], null]);
					}

					if (dataset.data[k][0] == 'gprs') {
						arr_gprs.push([
							total_data.xData[k],
							dataset.data[k][1],
						]);
					} else {
						arr_gprs.push([total_data.xData[k], null]);
					}
				}
			} else {
				for (let k = 0; k < dataset.data.length; k++) {
					data1.push([total_data.xData[k], dataset.data[k]]);
				}
			}

			//let pointFormat1 = '',
			let graphColor,
				pointFormat1,
				label_change = {};
			//tooltip_formatter;

			if (dataset.name === 'Device Status') {
				// tooltip_formatter = function () {
				// 	return (
				// 		moment
				// 			.unix(this.x / 1000 - 19800)
				// 			.format('dddd, MMM DD, HH:mm:ss') +
				// 		'<br />Device Status: <b>' +
				// 		this.y +
				// 		'</b>'
				// 	);
				// 	//return 'Device Status: <b>' + this.y + '</b>';
				// };
				//pointFormat1 = 'Device Status: <b>{point.y}</b>';
				pointFormat1 = function () {
					return 'Device Status: <b>' + this.y + '</b>';
				};
				graphColor = '#91D5FF';
				label_change = {
					0: 'Offline',
					1: 'Online',
				};
			}
			if (dataset.name === 'Signal Strength') {
				// tooltip_formatter = function () {
				// 	return (
				// 		moment
				// 			.unix(this.x / 1000 - 19800)
				// 			.format('dddd, MMM DD, HH:mm:ss') +
				// 		'<br />Signal Strength: <b>' +
				// 		this.y +
				// 		'</b>'
				// 	);
				// 	//return 'Signal Strength: <b>' + this.y + '</b>';
				// };
				//pointFormat1 = 'Signal Strength: <b>{point.y}</b>';
				pointFormat1 = function () {
					return 'Signal Strength: <b>' + this.y + '</b>';
				};
				if (that.max_signal_value <= 20) {
					label_change = {
						0: 'No Signal',
						6: 'Poor',
						12: 'Good',
						20: 'Excellent',
					};
				} else {
					label_change = {
						0: 'No Signal',
						6: 'Poor',
						12: 'Good',
						20: 'Excellent',
						[`${that.max_signal_value}`]: ' ',
					};
				}
			}
			if (dataset.name === 'Device Power') {
				// tooltip_formatter = function () {
				// 	if (this.y === 1)
				// 		return (
				// 			moment
				// 				.unix(this.x / 1000 - 19800)
				// 				.format('dddd, MMM DD, HH:mm:ss') +
				// 			'<br />Device Power: <b>On</b>'
				// 		);
				// 	//return 'Device Power: <b>On</b>';
				// 	else
				// 		return (
				// 			moment
				// 				.unix(this.x / 1000 - 19800)
				// 				.format('dddd, MMM DD, HH:mm:ss') +
				// 			'<br />Device Power: <b>Off</b>'
				// 		);
				// 	//else return 'Device Power: <b>Off</b>';
				// };

				graphColor = '#FAAD14';
				label_change = {
					0: 'Off',
					1: 'On',
				};
				// pointFormat1 =
				// 	'Device Power: ' + '<b>' + '{label_change[point.y]}' + '</b>';
				pointFormat1 = function () {
					return (
						'Device Power: <b>' +
						(this.y == 1 ? 'On' : 'Off') +
						'</b>'
					);
				};
			}
			if (dataset.name === 'Battery Voltage') {
				// tooltip_formatter = function () {
				// 	return (
				// 		moment
				// 			.unix(this.x / 1000 - 19800)
				// 			.format('dddd, MMM DD, HH:mm:ss') +
				// 		'<br />Battery Voltage: <b>' +
				// 		this.y +
				// 		'</b>'
				// 	);
				// 	//return 'Battery Voltage: <b>' + this.y + '</b>';
				// };
				//pointFormat1 = 'Battery Voltage: <b>{point.y}</b>';
				pointFormat1 = function () {
					return 'Battery Voltage: <b>' + this.y + '</b>';
				};
				graphColor = '#FF85C0';
			}

			let series = [
				{
					data: data1,
					name: dataset.name,
					type: dataset.type,
					color: graphColor,
					fillOpacity: 0,
					showInLegend: false,
					marker: {
						enabled: false,
					},
				},
			];

			let yAxis = {
				title: {
					text: null,
				},
				labels: {
					formatter: function () {
						return this.value;
					},
				},
				min: 0,
			};

			if (
				dataset.name === 'Device Status' ||
				dataset.name === 'Device Power'
			) {
				series = [
					{
						data: data1,
						name: dataset.name,
						type: dataset.type,
						color: graphColor,
						fillOpacity: 0.5,
						step: 'left',
						showInLegend: false,
						marker: {
							enabled: false,
						},
					},
				];
				yAxis = {
					title: {
						text: null,
					},
					labels: {
						formatter: function () {
							return label_change[this.value];
						},
					},
					tickPositions: [0, 1],
				};
			}

			if (dataset.name === 'Signal Strength') {
				let tickPosition = [0, 6, 12, 20];
				if (that.max_signal_value > 20) {
					tickPosition = [0, 6, 12, 20, that.max_signal_value];
				}
				// series = [
				// 	{
				// 		data: data1,
				// 		name: dataset.name,
				// 		type: dataset.type,
				// 		fillOpacity: 0,
				// 		showInLegend: false,
				// 		marker: {
				// 			enabled: false,
				// 		},
				// 	},
				// 	{
				// 		data: {},
				// 		name: 'Wifi',
				// 		type: 'area',
				// 		color: '#11A2D9',
				// 		marker: {
				// 			symbol: 'circle',
				// 		},
				// 	},
				// 	{
				// 		data: {},
				// 		name: 'Gprs',
				// 		type: 'area',
				// 		color: '#A0D911',
				// 		marker: {
				// 			symbol: 'circle',
				// 		},
				// 	},
				// 	{
				// 		data: {},
				// 		name: 'Ethernet',
				// 		type: 'area',
				// 		color: '#6E40F1',
				// 		marker: {
				// 			symbol: 'circle',
				// 		},
				// 	},
				// ];
				series = [
					{
						name: 'GPRS',
						color: '#A0D911',
						data: arr_gprs,
					},
					{
						name: 'WiFi',
						color: '#11A2D9',
						data: arr_wifi,
					},
					{
						name: 'Ethernet',
						color: '#6E40F1',
						data: arr_ethernet,
					},
				];
				yAxis = {
					title: {
						text: null,
					},
					labels: {
						formatter: function () {
							return label_change[this.value] == undefined
								? this.value
								: label_change[this.value];
						},
					},
					tickPositions: tickPosition,
				};
			}

			//console.log('doc-graph', document.getElementById('graph_container'));

			// var chartDiv = document.createElement('div');
			// if (document.getElementById('graph_container'))
			// 	document
			// 		.getElementById('graph_container')
			// 		.appendChild(chartDiv);

			//Highcharts.chart(chartDiv, {
			syncGraphData.push({
				chart: {
					height: 202,
					spacingTop: 20,
					spacingBottom: 20,
					marginLeft: 70,
					zoomType: 'x',
				},
				title: {
					text: dataset.name,
					align: 'left',
					x: 30,
					style: {
						fontSize: '11px',
						color: '#808080',
						marginBottom: '10px',
						marginLeft: '10px',
					},
				},
				credits: {
					enabled: false,
				},
				legend: {
					enabled: false,
				},
				xAxis: {
					type: 'datetime',
					crosshair: true,
					// events: {
					// 	setExtremes: (e) => this.syncExtremes(e),
					// },
					labels: {
						style: {
							color: 'rgba(118, 134, 161, 0.8)',
							fontSize: '10px',
						},
					},
				},
				yAxis: yAxis,
				// yAxis: {
				// 	title: {
				// 		text: null,
				// 	},
				// 	labels: {
				// 		formatter: function () {
				// 			if (dataset.name === 'Device Status') {
				// 				return label_change[this.value];
				// 			} else if (dataset.name === 'Signal Strength') {
				// 				return label_change[this.value] == undefined
				// 					? this.value
				// 					: label_change[this.value];
				// 			} else if (dataset.name === 'Device Power') {
				// 				return label_change[this.value];
				// 			} else {
				// 				return this.value;
				// 			}
				// 		},
				// 	},
				// },
				tooltip: {
					//shared: true,
					borderWidth: 0,
					backgroundColor: '#FFFFFF',
					//pointFormat: pointFormat1,
					pointFormatter: pointFormat1,
					//formatter: tooltip_formatter,
				},
				// legend: {
				// 	align: 'center',
				// 	verticalAlign: 'bottom',
				// },
				series: series,
			});
		});
		//console.log('aw', document.getElementById('graph_container'));

		this.setState(
			{
				sync_graph_data: syncGraphData,
			},
			() => {
				console.log('sync_graph_data_', this.state.sync_graph_data);
			}
		);

		// ['mousemove', 'touchmove', 'touchstart'].forEach(function (eventType) {
		// 	document
		// 		.getElementById('graph_container')
		// 		.addEventListener(eventType, function (e) {
		// 			//document.getElementById('graph_container').bind('mousemove toucchmove touchstart', function (e) {
		// 			var chart,
		// 				point,
		// 				i,
		// 				event,
		// 				points = [];

		// 			for (i = 0; i < Highcharts.charts.length; i = i + 1) {
		// 				chart = Highcharts.charts[i];
		// 				// Find coordinates within the chart
		// 				event = chart.pointer.normalize(e);

		// 				// if (i === 1) {
		// 				// 	points = [chart.series[0].searchPoint(event, true), chart.series[1].searchPoint(event, true), chart.series[2].searchPoint(event, true)];
		// 				// 	if (points) {
		// 				// 		var number = 0;
		// 				// 		Highcharts.each(points, function(p, i) {
		// 				// 		  if (!p.series.visible) {
		// 				// 			points.splice(i - number, 1);
		// 				// 			number++;
		// 				// 		  }
		// 				// 		});
		// 				// 		points.highlight(e);
		// 				// 	}
		// 				// }
		// 				// else {
		// 				// 	point = chart.series[0].searchPOint(event, true);
		// 				// 	if (point) {
		// 				// 		point.highlight(e);
		// 				// 	}
		// 				// }
		// 				// Get the hovered point
		// 				point = chart.series[0].searchPoint(event, true);

		// 				if (point) {
		// 					point.highlight(e);
		// 				}
		// 			}
		// 		});
		// });
		// Highcharts.Pointer.prototype.reset = function () {
		// 	return undefined;
		// };

		// Highcharts.Point.prototype.highlight = function (event) {
		// 	event = this.series.chart.pointer.normalize(event);
		// 	this.onMouseOver(); // Show the hover marker
		// 	this.series.chart.tooltip.refresh(this); // Show the tooltip
		// 	this.series.chart.xAxis[0].drawCrosshair(event, this); // Show the crosshair
		// };
	}

	onRangeChangeOfflineTrend(rangeArr, isCustom) {
		// let offlineTrendFromDateCopy = this.state.offline_trend_from_date_copy,
		// 	offlineTrendUptoDateCopy = this.state.offline_trend_upto_date_copy;
		// let prevCustomFlag = this.state.is_custom_offline_trend;
		console.log('isCustom1', isCustom, rangeArr);
		this.offline_trend_from_date = isCustom
			? moment(rangeArr[0] * 1000)
					.startOf('day')
					.unix()
			: rangeArr[0];
		this.offline_trend_upto_date = isCustom && moment().endOf('day').unix() !== moment(rangeArr[1] * 1000).endOf('day').unix()
			? moment(rangeArr[1] * 1000)
					.endOf('day')
					.unix()
			: rangeArr[1];
		// if (
		// 	document.getElementById('graph_container') &&
		// 	!offlineTrendFromDateCopy &&
		// 	!offlineTrendUptoDateCopy
		// )
		// 	document.getElementById('graph_container').innerHTML = '';
		// console.log('graph-doc', document.getElementById('graph_container'));

		if (isCustom) {
			this.setState(
				{
					offline_trend_from_date: moment(rangeArr[0] * 1000)
						.startOf('day')
						.unix(),
					offline_trend_upto_date: moment(rangeArr[1] * 1000)
						.endOf('day')
						.unix(),
					// offline_trend_from_date_copy: offlineTrendFromDateCopy
					// 	? offlineTrendFromDateCopy
					// 	: moment(rangeArr[0] * 1000)
					// 			.startOf('day')
					// 			.unix(),
					// offline_trend_upto_date_copy: offlineTrendUptoDateCopy
					// 	? offlineTrendUptoDateCopy
					// 	: moment(rangeArr[1] * 1000)
					// 			.endOf('day')
					// 			.unix(),
					//offline_graph_loading: true,
					// offline_graph_loading:
					// 	!offlineTrendFromDateCopy && !offlineTrendUptoDateCopy
					// 		? true
					// 		: false,
					// is_custom_offline_trend: isCustom,
				},
				() => {
					console.log(
						'isCustom112',
						this.state.offline_trend_from_date,
						this.state.offline_trend_upto_date
					);
					// if (
					// 	(!offlineTrendFromDateCopy &&
					// 		!offlineTrendUptoDateCopy) ||
					// 	prevCustomFlag == false
					// ) {
					// 	this.fetchOfflineTrend();
					// }
				}
			);
		} else {
			this.setState(
				{
					offline_trend_from_date: rangeArr[0],
					offline_trend_upto_date: rangeArr[1],
					// offline_trend_from_date_copy: offlineTrendFromDateCopy
					// 	? offlineTrendFromDateCopy
					// 	: rangeArr[0],
					// offline_trend_upto_date_copy: offlineTrendUptoDateCopy
					// 	? offlineTrendUptoDateCopy
					// 	: rangeArr[1],
					offline_graph_loading: true,
					sync_graph_data: [],
					//is_custom_offline_trend: isCustom,
				},
				() => {
					//document.getElementById('graph_container div').empty();
					this.fetchOfflineTrend();
				}
			);
		}
	}

	onOpenChangeOfflineTrend(status) {
		console.log(
			'OFFTREND_',
			status,
			this.state.offline_trend_from_date,
			this.state.offline_trend_upto_date
		);
		// if (
		// 	!status &&
		// 	(this.state.offline_trend_from_date_copy !==
		// 		this.state.offline_trend_from_date ||
		// 		this.state.offline_trend_upto_date_copy !==
		// 			this.state.offline_trend_upto_date)
		// ) {
		// if (document.getElementById('graph_container'))
		// 	document.getElementById('graph_container').innerHTML = '';
		//document.getElementById('graph_container div').empty();
		if (!status) {
			this.setState(
				{
					// offline_trend_from_date_copy: this.state
					// 	.offline_trend_from_date,
					// offline_trend_upto_date_copy: this.state
					// 	.offline_trend_upto_date,
					offline_graph_loading: true,
					sync_graph_data: [],
				},
				() => {
					//document.getElementById('graph_container div').empty();
					this.fetchOfflineTrend();
				}
			);
		}
		//}
	}

	onRangeChangeNetworkDetail(rangeArr, isCustom) {
		// let networkDetailFromDateCopy = this.state
		// 		.network_detail_from_date_copy,
		// 	networkDetailUptoDateCopy = this.state
		// 		.network_detail_upto_date_copy;
		// let prevCustomFlag = this.state.is_custom_network_detail;
		this.network_detail_from_date = moment(rangeArr[0] * 1000)
			.startOf('day')
			.unix();
		this.network_detail_upto_date = moment().endOf('day').unix() !== moment(rangeArr[1] * 1000).endOf('day').unix() ? moment(rangeArr[1] * 1000)
			.endOf('day')
			.unix() : moment(rangeArr[1] * 1000)
			.unix();

		this.dateChanged = true;
		console.log('isCustom2', isCustom);

		if (isCustom) {
			this.setState(
				{
					network_detail_from_date: moment(rangeArr[0] * 1000)
						.startOf('day')
						.unix(),
					network_detail_upto_date: moment(rangeArr[1] * 1000)
						.endOf('day')
						.unix(),
					// network_detail_from_date_copy: networkDetailFromDateCopy
					// 	? networkDetailFromDateCopy
					// 	: moment(rangeArr[0] * 1000)
					// 			.startOf('day')
					// 			.unix(),
					// network_detail_upto_date_copy: networkDetailUptoDateCopy
					// 	? networkDetailUptoDateCopy
					// 	: moment(rangeArr[1] * 1000)
					// 			.endOf('day')
					// 			.unix(),
					// network_loading:
					// 	!networkDetailFromDateCopy && !networkDetailUptoDateCopy
					// 		? true
					// 		: false,
					// is_custom_network_detail: isCustom,
				}
				// () => {
				// 	if (
				// 		(!networkDetailFromDateCopy &&
				// 			!networkDetailUptoDateCopy) ||
				// 		prevCustomFlag == false
				// 	) {
				// 		this.fetchDeviceNetworkSection();
				// 	}
				// }
			);
		} else {
			this.setState(
				{
					network_detail_from_date: moment(rangeArr[0] * 1000)
						.startOf('day')
						.unix(),
					network_detail_upto_date: moment(rangeArr[1] * 1000)
						.endOf('day')
						.unix(),
					// network_detail_from_date_copy: networkDetailFromDateCopy
					// 	? networkDetailFromDateCopy
					// 	: moment(rangeArr[0] * 1000)
					// 			.startOf('day')
					// 			.unix(),
					// network_detail_upto_date_copy: networkDetailUptoDateCopy
					// 	? networkDetailUptoDateCopy
					// 	: moment(rangeArr[1] * 1000)
					// 			.endOf('day')
					// 			.unix(),
					network_loading: true,
					//is_custom_network_detail: isCustom,
				},
				() => {
					this.fetchDeviceNetworkSection();
				}
			);
		}
	}

	onOpenChangeNetworkDetail(status) {
		console.log(
			'NET_',
			status,
			this.state.network_detail_from_date,
			this.state.network_detail_upto_date,
			this.network_detail_from_date,
			this.network_detail_upto_date
		);
		// if (
		// 	!status &&
		// 	(this.state.network_detail_from_date_copy !==
		// 		this.state.network_detail_from_date ||
		// 		this.state.network_detail_upto_date_copy !==
		// 			this.state.network_detail_upto_date)
		// ) {
		// 	console.log('OFFTREND_ 1', status);

		this.setState(
			{
				// network_detail_from_date_copy: this.state
				// 	.network_detail_from_date,
				// network_detail_upto_date_copy: this.state
				// 	.network_detail_upto_date,
				network_loading: true,
			},
			() => {
				this.fetchDeviceNetworkSection();
			}
		);
		//}
	}

	onRangeChangeDeviceError(rangeArr, isCustom) {
		// let deviceErrorFromDateCopy = this.state.device_error_from_date_copy,
		// 	deviceErrorUptoDateCopy = this.state.device_error_upto_date_copy;
		// let prevCustomFlag = this.state.is_custom_device_error;
		this.device_error_from_date = moment(rangeArr[0] * 1000)
			.startOf('day')
			.unix();
		this.device_error_upto_date = moment(rangeArr[1] * 1000)
			.endOf('day')
			.unix();

		console.log('isCustom2', isCustom);

		if (isCustom) {
			this.setState(
				{
					device_error_from_date: moment(rangeArr[0] * 1000)
						.startOf('day')
						.unix(),
					device_error_upto_date: moment(rangeArr[1] * 1000)
						.endOf('day')
						.unix(),
					// device_error_from_date_copy: deviceErrorFromDateCopy
					// 	? deviceErrorFromDateCopy
					// 	: moment(rangeArr[0] * 1000)
					// 			.startOf('day')
					// 			.unix(),
					// device_error_upto_date_copy: deviceErrorUptoDateCopy
					// 	? deviceErrorUptoDateCopy
					// 	: moment(rangeArr[1] * 1000)
					// 			.endOf('day')
					// 			.unix(),
					// device_error_loading:
					// 	!deviceErrorFromDateCopy && !deviceErrorUptoDateCopy
					// 		? true
					// 		: false,
					// is_custom_device_error: isCustom,
				}
				// () => {
				// 	if (
				// 		(!deviceErrorFromDateCopy &&
				// 			!deviceErrorUptoDateCopy) ||
				// 		prevCustomFlag == false
				// 	) {
				// 		this.fetchDataFileCount();
				// 		this.fetchDeviceNetworkSection();
				// 		this.fetchPieChartData();
				// 		this.fetchFilterErrorLogs(this.state.url_text);
				// 	}
				// }
			);
		} else {
			this.setState(
				{
					device_error_from_date: moment(rangeArr[0] * 1000)
						.startOf('day')
						.unix(),
					device_error_upto_date: moment(rangeArr[1] * 1000)
						.endOf('day')
						.unix(),
					// device_error_from_date_copy: deviceErrorFromDateCopy
					// 	? deviceErrorFromDateCopy
					// 	: moment(rangeArr[0] * 1000)
					// 			.startOf('day')
					// 			.unix(),
					// device_error_upto_date_copy: deviceErrorUptoDateCopy
					// 	? deviceErrorUptoDateCopy
					// 	: moment(rangeArr[1] * 1000)
					// 			.endOf('day')
					// 			.unix(),
					device_error_loading: true,
					//is_custom_device_error: isCustom,
				},
				() => {
					this.fetchDataFileCount();
					this.fetchDeviceNetworkSection();
					this.fetchPieChartData();
					this.fetchFilterErrorLogs(this.state.url_text);
				}
			);
		}
	}

	onOpenChangeDeviceError(status) {
		console.log('OFFTREND_', status);
		// if (
		// 	!status &&
		// 	(this.state.device_error_from_date_copy !==
		// 		this.state.device_error_from_date ||
		// 		this.state.device_error_upto_date_copy !==
		// 			this.state.device_error_upto_date)
		// ) {
		// 	console.log('OFFTREND_ 1', status);

		this.setState(
			{
				// device_error_from_date_copy: this.state
				// 	.device_error_from_date,
				// device_error_upto_date_copy: this.state
				// 	.device_error_upto_date,
				device_error_loading: true,
			},
			() => {
				this.fetchDataFileCount();
				this.fetchDeviceNetworkSection();
				this.fetchPieChartData();
				this.fetchFilterErrorLogs(this.state.url_text);
				//this.fetchDeviceOfflineLogs();
			}
		);
		//}
	}

	onRangeChangeOfflineLog(rangeArr, isCustom) {
		// let offlineLogFromDateCopy = this.state.offline_log_from_date_copy,
		// 	offlineLogUptoDateCopy = this.state.offline_log_upto_date_copy;
		// let prevCustomFlag = this.state.is_custom_offline_log;

		// console.log(
		// 	'isCustom9',
		// 	isCustom,
		// 	offlineLogFromDateCopy,
		// 	offlineLogUptoDateCopy
		// );
		this.offline_log_from_date = moment(rangeArr[0] * 1000)
			.startOf('day')
			.unix();
		this.offline_log_upto_date = moment(rangeArr[1] * 1000)
			.endOf('day')
			.unix();

		if (isCustom) {
			this.setState(
				{
					offline_log_from_date: moment(rangeArr[0] * 1000)
						.startOf('day')
						.unix(),
					offline_log_upto_date: moment(rangeArr[1] * 1000)
						.endOf('day')
						.unix(),
					// offline_log_from_date_copy: offlineLogFromDateCopy
					// 	? offlineLogFromDateCopy
					// 	: moment(rangeArr[0] * 1000)
					// 			.startOf('day')
					// 			.unix(),
					// offline_log_upto_date_copy: offlineLogUptoDateCopy
					// 	? offlineLogUptoDateCopy
					// 	: moment(rangeArr[1] * 1000)
					// 			.endOf('day')
					// 			.unix(),
					// offline_log_loading:
					// 	!offlineLogFromDateCopy && !offlineLogUptoDateCopy
					// 		? true
					// 		: false,
					// is_custom_offline_log: isCustom,
				}
				// () => {
				// 	if (
				// 		(!offlineLogFromDateCopy && !offlineLogUptoDateCopy) ||
				// 		prevCustomFlag == false
				// 	) {
				// 		this.setState(
				// 			{
				// 				device_offline_logs: undefined,
				// 			},
				// 			() => {
				// 				this.fetchDeviceOfflineLogs();
				// 			}
				// 		);
				// 	}
				// }
			);
		} else {
			this.setState(
				{
					offline_log_from_date: moment(rangeArr[0] * 1000)
						.startOf('day')
						.unix(),
					offline_log_upto_date: moment(rangeArr[1] * 1000)
						.endOf('day')
						.unix(),
					// offline_log_from_date_copy: offlineLogFromDateCopy
					// 	? offlineLogFromDateCopy
					// 	: moment(rangeArr[0] * 1000)
					// 			.startOf('day')
					// 			.unix(),
					// offline_log_upto_date_copy: offlineLogUptoDateCopy
					// 	? offlineLogUptoDateCopy
					// 	: moment(rangeArr[1] * 1000)
					// 			.endOf('day')
					// 			.unix(),
					offline_log_loading: true,
					device_offline_logs: undefined,
					//is_custom_offline_log: isCustom,
				},
				() => {
					// console.log(
					// 	'isCustom91',
					// 	this.state.offline_log_from_date_copy,
					// 	this.state.offline_log_upto_date_copy
					// );
					this.fetchDeviceOfflineLogs();
				}
			);
		}
	}

	onOpenChangeOfflineLog(status) {
		// console.log(
		// 	'OFFLOG',
		// 	status,
		// 	this.state.offline_log_from_date_copy,
		// 	this.state.offline_log_from_date,
		// 	this.state.offline_log_upto_date_copy,
		// 	this.state.offline_log_upto_date
		// );
		// if (
		// 	!status &&
		// 	(this.state.offline_log_from_date_copy !==
		// 		this.state.offline_log_from_date ||
		// 		this.state.offline_log_upto_date_copy !==
		// 			this.state.offline_log_upto_date)
		// ) {
		// 	console.log(
		// 		'OFFLOG_9',
		// 		status,
		// 		this.state.offline_log_from_date_copy,
		// 		this.state.offline_log_from_date,
		// 		this.state.offline_log_upto_date_copy,
		// 		this.state.offline_log_upto_date
		// 	);

		this.setState(
			{
				// offline_log_from_date_copy: this.state
				// 	.offline_log_from_date,
				// offline_log_upto_date_copy: this.state
				// 	.offline_log_upto_date,
				offline_log_loading: true,
				device_offline_logs: undefined,
			},
			() => {
				this.fetchDeviceOfflineLogs();
			}
		);
		//}
	}

	async getDeviceErrorCsvStructure(isReportDownload = false) {
		let network_table_data = [],
			power_table_data = [],
			sensor_table_data = [],
			other_table_data = [],
			filter_table_data = [],
			error_list_table_data = [],
			pageSize = 500;

		let err_types = [
			'Power Error',
			'Network Error',
			'Sensor Error',
			'Other Error',
		];

		let device_from_date = this.state.device_error_from_date
				? this.state.device_error_from_date
				: this.device_error_from_date,
			device_upto_date = this.state.device_error_upto_date
				? this.state.device_error_upto_date
				: this.device_error_upto_date;

		if (this.state.error_cumulative) {
			if (
				this.state.error_cumulative.network &&
				this.state.error_cumulative.network.chart
			) {
				if (this.state.error_cumulative.network.chart.length != 0) {
					this.state.error_cumulative.network.chart.map((ne) => {
						network_table_data.push({
							type: ne.name,
							count: ne.value && Number(ne.value) ? ne.value : 0,
						});
					});
				} else {
					network_table_data.push({
						type: '-',
						count: '-',
					});
				}
			} else {
				network_table_data.push({
					type: '-',
					count: '-',
				});
			}

			if (
				this.state.error_cumulative.power &&
				this.state.error_cumulative.power.chart
			) {
				if (this.state.error_cumulative.power.chart.length != 0) {
					this.state.error_cumulative.power.chart.map((pe) => {
						power_table_data.push({
							type: pe.name,
							count: pe.value && Number(pe.value) ? pe.value : 0,
						});
					});
				} else {
					power_table_data.push({
						type: '-',
						count: '-',
					});
				}
			} else {
				power_table_data.push({
					type: '-',
					count: '-',
				});
			}

			if (
				this.state.error_cumulative.sensor &&
				this.state.error_cumulative.sensor.chart
			) {
				if (this.state.error_cumulative.sensor.chart.length != 0) {
					this.state.error_cumulative.sensor.chart.map((se) => {
						sensor_table_data.push({
							type: se.name,
							count: se.value && Number(se.value) ? se.value : 0,
						});
					});
				} else {
					sensor_table_data.push({
						type: '-',
						count: '-',
					});
				}
			} else {
				sensor_table_data.push({
					type: '-',
					count: '-',
				});
			}

			if (
				this.state.error_cumulative.other &&
				this.state.error_cumulative.other.chart
			) {
				if (this.state.error_cumulative.other.chart.length != 0) {
					this.state.error_cumulative.other.chart.map((oe) => {
						other_table_data.push({
							type: oe.name,
							count: oe.value && Number(oe.value) ? oe.value : 0,
						});
					});
				} else {
					other_table_data.push({
						type: '-',
						count: '-',
					});
				}
			} else {
				other_table_data.push({
					type: '-',
					count: '-',
				});
			}
		}

		if (
			this.state.filter_value &&
			Object.keys(this.state.filter_value).length
		) {
			for (let key in this.state.filter_value) {
				if (key != 'All_errors') {
					filter_table_data.push({
						type: key,
						sub_type:
							this.state.filter_value[key].length != 0
								? '' + this.state.filter_value[key] + ''
								: '-',
					});
				}
			}
			if (filter_table_data.length == 0) {
				filter_table_data.push({
					type: '-',
					sub_type: '-',
				});
			}
		} else {
			filter_table_data = [
				{
					type: '-',
					sub_type: '-',
				},
			];
		}

		// console.log('filter_table_data_', filter_table_data);

		if (this.state.total_error_count != 0) {
			for (
				let page = 1;
				page <= Math.ceil(this.state.total_error_count / pageSize);
				page++
			) {
				let response = await deviceDebugErrorLog({
					device_id: this.state.selected_device[0],
					from_date: this.state.device_error_from_date
						? this.state.device_error_from_date
						: this.device_error_from_date,
					upto_date: this.state.device_error_upto_date
						? this.state.device_error_upto_date
						: this.device_error_upto_date,
					page: page,
					page_size: pageSize,
					filter_text: this.state.url_text,
				});

				if (response.status === 403) {
					this.setState({
						unauthorised_access: true,
						unauthorised_access_msg: response.message,
					});
				} else if (response.status === 'success') {
					let res = response.error_logs;
					res.map((data) => {
						error_list_table_data.push({
							timestamp: moment
								.unix(data.timestamp)
								.format('Do MMM YYYY, HH:mm:ss'),
							tags:
								err_types[data.error_type - 1] +
								',' +
								data.error_name,
							errors: data.error_message,
						});
					});
				} else {
					this.openNotification('error', response.message);
					this.setState({
						unauthorised_access: false,
						error_API: true,
						error_API_msg: response.message,
						device_csv_button_loading: false,
					});
				}
			}
		}

		let deviceErrorReportObject = {
			...DeviceErrorReportObjectData.report_data,
		};

		deviceErrorReportObject.body_section.body_data.map((ob) => {
			if (ob.id == 'network_error') {
				ob.table_data = network_table_data;
				ob.section_head =
					'Network Error' +
					' - ' +
					(this.state.error_cumulative.network &&
					this.state.error_cumulative.network.total
						? this.state.error_cumulative.network.total
						: 0);
			}
			if (ob.id == 'power_error') {
				ob.table_data = power_table_data;
				ob.section_head =
					'Power Error' +
					' - ' +
					(this.state.error_cumulative.power &&
					this.state.error_cumulative.power.total
						? this.state.error_cumulative.power.total
						: 0);
			}
			if (ob.id == 'sensor_error') {
				ob.table_data = sensor_table_data;
				ob.section_head =
					'Sensor Error' +
					' - ' +
					(this.state.error_cumulative.sensor &&
					this.state.error_cumulative.sensor.total
						? this.state.error_cumulative.sensor.total
						: 0);
			}
			if (ob.id == 'other_error') {
				ob.table_data = other_table_data;
				ob.section_head =
					'Other Error' +
					' - ' +
					(this.state.error_cumulative.other &&
					this.state.error_cumulative.other.total
						? this.state.error_cumulative.other.total
						: 0);
			}
			if (ob.id == 'device_error') {
				ob.table_data = filter_table_data;
			}
			if (ob.id == 'device_error_list') {
				ob.table_data = error_list_table_data;
			}
		});

		if (this.device_qr_code) {
			deviceErrorReportObject.head_section.report_head =
				deviceErrorReportObject.head_section.report_head +
				' : Device QR - ' +
				this.device_qr_code;
		}

		deviceErrorReportObject.file_name =
			deviceErrorReportObject.file_name +
			' ' +
			moment.unix(device_from_date).format('DD-MM-YYYY_HH-mm') +
			'_' +
			moment.unix(device_upto_date).format('DD-MM-YYYY_HH-mm');

		deviceErrorReportObject.time_interval = [
			device_from_date,
			device_upto_date,
		];

		let downloadReport = false;
		if (isReportDownload) {
			downloadReport = true;
		}

		this.setState({
			device_error_report_object: deviceErrorReportObject,
			device_csv_report_download: downloadReport,
		});
	}

	async fetchFilterErrorLogs(
		url_text = '',
		page = 1,
		pageSize = DeviceDebugObjectData.device_error_table.config
			.pagination_data.pageSize
	) {
		let response = await deviceDebugErrorLog({
			device_id: this.state.selected_device[0],
			from_date: this.device_error_from_date,
			// ? this.state.device_error_from_date
			// : this.device_error_from_date,
			upto_date: this.device_error_upto_date,
			// ? this.state.device_error_upto_date
			// : this.device_error_upto_date,
			page: page,
			page_size: pageSize,
			filter_text: url_text,
		});

		if (response.status === 403) {
			this.setState({
				unauthorised_access: true,
				unauthorised_access_msg: response.message,
				device_error_loading: false,
				error_logs: [],
			});
		} else if (response.status === 'success') {
			console.log('ALL-DATA-RR', response.error_logs);
			DeviceDebugObjectData.device_error_table.config.pagination_data.total =
				response.total_errors;

			this.setState(
				{
					error_logs: response.error_logs,
					total_error_count: response.total_errors,
					device_error_loading: false,
				},
				() => {
					console.log('ALL-DATA-STATE', this.state.error_logs);
				}
			);
		} else {
			this.openNotification('error', response.message);
			this.setState({
				unauthorised_access: false,
				error_API: true,
				error_API_msg: response.message,
				device_error_loading: false,
				error_logs: [],
			});
		}
	}

	async fetchDeviceNetworkSection(
		page = 1,
		pageSize = DeviceDebugObjectData.device_error_table.config
			.pagination_data.pageSize
	) {
		console.log(
			'NET_1',
			this.state.network_detail_from_date,
			this.state.network_detail_upto_date,
			this.network_detail_from_date,
			this.network_detail_upto_date
		);
		let request_object = {};
		if (this.state.selected_tab == 'network_details') {
			request_object = {
				device_id: this.state.selected_device[0],
				from_date: this.network_detail_from_date,
				// ? this.state.network_detail_from_date
				// : this.network_detail_from_date,
				upto_date: this.network_detail_upto_date,
				// ? this.state.network_detail_upto_date
				// : this.network_detail_upto_date,
			};
		}
		if (this.state.selected_tab == 'device_error') {
			request_object = {
				device_id: this.state.selected_device[0],
				from_date: this.device_error_from_date,
				// ? this.state.device_error_from_date
				// : this.device_error_from_date,
				upto_date: this.device_error_upto_date,
				// ? this.state.device_error_upto_date
				// : this.device_error_upto_date,
			};
		}
		let response = await deviceDebugNetworkData(request_object);

		if (response.status === 403) {
			let stateToUpdate1 = {
				unauthorised_access: true,
				unauthorised_access_msg: response.message,
			};
			if (this.state.selected_tab == 'network_details') {
				stateToUpdate1.network_loading = false;
			}
			if (this.state.selected_tab == 'device_error') {
				stateToUpdate1.device_error_loading = false;
			}
			this.setState(stateToUpdate1);
		} else if (response.status === 'success') {
			let stateToUpdate2 = {};
			if (this.state.selected_tab == 'network_details') {
				let graphData = this.networkGraphData(
					response.network_usage_data,
					response.network_strength
				);

				console.log('graphData_', graphData);

				let network_details = { ...this.state.network_details };
				network_details.network_usage = graphData.network_history;
				network_details.network_usage_distribution =
					graphData.network_history_pie;

				stateToUpdate2 = {
					network_details: network_details,
					network_loading: false,
				};
			}
			if (this.state.selected_tab == 'device_error') {
				stateToUpdate2 = {
					historical_data_count: response.historical_data_count,
					realtime_data_count: response.realtime_data_count,
				};
			}
			this.setState(stateToUpdate2);
		} else {
			this.openNotification('error', response.message);
			let stateToUpdate3 = {
				unauthorised_access: false,
				error_API: true,
				error_API_msg: response.message,
			};
			if (this.state.selected_tab == 'network_details') {
				stateToUpdate3.network_loading = false;
			}
			if (this.state.selected_tab == 'device_error') {
				stateToUpdate3.device_error_loading = false;
			}
			this.setState(stateToUpdate3);
		}
	}

	/**
	 * Retrives debug data of a device and updates states.
	 * @param {string} debug_type - api calling type all data or only device error
	 * @param {number} page - current page number.
	 * @param {number} pageSize - current showing rows per page.
	 */
	async retriveDeviceDebug(
		debug_type = 'all',
		page = 1,
		pageSize = DeviceDebugObjectData.device_error_table.config
			.pagination_data.pageSize
	) {
		let upto_time = moment().unix(),
			from_time = moment().unix() - 5 * 60,
			customerId = 0;

		if (
			this.props.application_id == 12 ||
			this.props.application_id == 17
		) {
			customerId = this.props.client_id;
		} else {
			customerId = this.props.vendor_id;
		}

		let response = await deviceDebug(
			customerId,
			this.state.selected_device[0],
			from_time,
			upto_time,
			debug_type,
			page,
			pageSize,
			this.state.device_filter
		);
		if (response.status === 403) {
			this.setState({
				unauthorised_access: true,
				unauthorised_access_msg: response.message,
			});
		} else if (response.status === 'success') {
			let deviceDetails = {
				device_qr_code: response.device_qr_code,
				firmware_current: response.firmware_current,
				device_is_online: response.device_is_online,
				device_last_data_received_time:
					response.device_last_data_received_time,
				config_template: response.config_template,
			};

			let batteryDetails = {
				mode_of_operation: response.mode_of_operation,
				battery_available: response.battery_available,
				battery_level: response.battery_level,
				battery_charging: response.battery_charging,
				battery_percent: response.battery_percent
					? parseFloat(response.battery_percent).toFixed(2)
					: 0,
				ac_status: response.ac_status ? response.ac_status : 'na',
			};

			let networkDetails = {
				current_operating_network: response.current_operating_network,
				current_signal_strength: response.current_signal_strength,
			};
			let stateToUpdate = {};

			if (response.device_is_online == false) {
				this.offline_trend_from_date =
					response.device_last_data_received_time == 0
						? moment().unix() - 3 * 86400
						: moment(
								response.device_last_data_received_time * 1000
						  ).unix() -
						  3 * 86400;
				this.offline_trend_upto_date =
					response.device_last_data_received_time == 0
						? moment().unix()
						: response.device_last_data_received_time;

				this.network_detail_from_date =
					response.device_last_data_received_time == 0
						? moment().startOf('month').subtract(2, 'M').unix()
						: moment(response.device_last_data_received_time * 1000)
								.startOf('month')
								.subtract(2, 'M')
								.unix();
				this.network_detail_upto_date =
					response.device_last_data_received_time == 0
						? moment().endOf('day').unix()
						: response.device_last_data_received_time;

				this.device_error_from_date =
					response.device_last_data_received_time == 0
						? moment().startOf('month').subtract(2, 'M').unix()
						: moment(response.device_last_data_received_time * 1000)
								.startOf('month')
								.subtract(2, 'M')
								.unix();
				this.device_error_upto_date =
					response.device_last_data_received_time == 0
						? moment().endOf('day').unix()
						: response.device_last_data_received_time;

				this.offline_log_from_date =
					response.device_last_data_received_time == 0
						? moment().startOf('month').subtract(2, 'M').unix()
						: moment(response.device_last_data_received_time * 1000)
								.startOf('month')
								.subtract(2, 'M')
								.unix();
				this.offline_log_upto_date =
					response.device_last_data_received_time == 0
						? moment().endOf('day').unix()
						: response.device_last_data_received_time;
			}

			if (debug_type === 'all') {
				this.raw_log_from_time_copy =
					!isNaN(response.device_last_data_received_time) &&
					response.device_last_data_received_time !== 0
						? moment(
								moment.unix(
									response.device_last_data_received_time
								)
						  )
								.startOf('day')
								.unix()
						: moment().startOf('day').unix();
				this.raw_log_upto_time_copy =
					!isNaN(response.device_last_data_received_time) &&
					response.device_last_data_received_time !== 0
						? moment(
								moment.unix(
									response.device_last_data_received_time
								)
						  )
								.endOf('day')
								.unix()
						: moment().unix();
				stateToUpdate = {
					device_debug_data: response,
					device_details: deviceDetails,
					battery_details: batteryDetails,
					network_details: networkDetails,
					device_error_logs: response.error_logs,
					device_error_filters: [
						...DeviceDebugObjectData.device_filter,
					],
					device_error_page_no: 1,
					raw_log_from_time:
						!isNaN(response.device_last_data_received_time) &&
						response.device_last_data_received_time !== 0
							? moment(
									moment.unix(
										response.device_last_data_received_time
									)
							  )
									.startOf('day')
									.unix()
							: moment().startOf('day').unix(),
					raw_log_upto_time:
						!isNaN(response.device_last_data_received_time) &&
						response.device_last_data_received_time !== 0
							? moment(
									moment.unix(
										response.device_last_data_received_time
									)
							  )
									.endOf('day')
									.unix()
							: moment().unix(),
					offline_trend_from_date:
						response.device_is_online == false &&
						response.device_last_data_received_time <
							moment().startOf('day').unix()
							? this.offline_trend_from_date
							: undefined,
					// offline_trend_from_date_copy:
					// 	response.device_is_online == false &&
					// 	response.device_last_data_received_time <
					// 		moment().startOf('day').unix()
					// 		? this.offline_trend_from_date
					// 		: undefined,
					offline_trend_upto_date:
						response.device_is_online == false &&
						response.device_last_data_received_time <
							moment().startOf('day').unix()
							? this.offline_trend_upto_date
							: undefined,
					// offline_trend_upto_date_copy:
					// 	response.device_is_online == false &&
					// 	response.device_last_data_received_time <
					// 		moment().startOf('day').unix()
					// 		? this.offline_trend_upto_date
					// 		: undefined,
					network_detail_from_date:
						response.device_is_online == false &&
						response.device_last_data_received_time <
							moment().startOf('day').unix()
							? this.network_detail_from_date
							: undefined,
					// network_detail_from_date_copy:
					// 	response.device_is_online == false &&
					// 	response.device_last_data_received_time <
					// 		moment().startOf('day').unix()
					// 		? this.network_detail_from_date
					// 		: undefined,
					network_detail_upto_date:
						response.device_is_online == false &&
						response.device_last_data_received_time <
							moment().startOf('day').unix()
							? this.network_detail_upto_date
							: undefined,
					// network_detail_upto_date_copy:
					// 	response.device_is_online == false &&
					// 	response.device_last_data_received_time <
					// 		moment().startOf('day').unix()
					// 		? this.network_detail_upto_date
					// 		: undefined,
					device_error_from_date:
						response.device_is_online == false &&
						response.device_last_data_received_time <
							moment().startOf('day').unix()
							? this.device_error_from_date
							: undefined,
					// device_error_from_date_copy:
					// 	response.device_is_online == false &&
					// 	response.device_last_data_received_time <
					// 		moment().startOf('day').unix()
					// 		? this.device_error_from_date
					// 		: undefined,
					device_error_upto_date:
						response.device_is_online == false &&
						response.device_last_data_received_time <
							moment().startOf('day').unix()
							? this.device_error_upto_date
							: undefined,
					// device_error_upto_date_copy:
					// 	response.device_is_online == false &&
					// 	response.device_last_data_received_time <
					// 		moment().startOf('day').unix()
					// 		? this.device_error_upto_date
					// 		: undefined,
					offline_log_from_date:
						response.device_is_online == false &&
						response.device_last_data_received_time <
							moment().startOf('day').unix()
							? this.offline_log_from_date
							: undefined,
					// offline_log_from_date_copy:
					// 	response.device_is_online == false &&
					// 	response.device_last_data_received_time <
					// 		moment().startOf('day').unix()
					// 		? this.offline_log_from_date
					// 		: undefined,
					offline_log_upto_date:
						response.device_is_online == false &&
						response.device_last_data_received_time <
							moment().startOf('day').unix()
							? this.offline_log_upto_date
							: undefined,
					// offline_log_upto_date_copy:
					// 	response.device_is_online == false &&
					// 	response.device_last_data_received_time <
					// 		moment().startOf('day').unix()
					// 		? this.offline_log_upto_date
					// 		: undefined,
				};
			} else if (debug_type === 'error_logs') {
				stateToUpdate = {
					device_error_logs: response.error_logs,
					device_error_filters: [
						...DeviceDebugObjectData.device_filter,
					],
				};
			}

			DeviceDebugObjectData.device_error_table.config.pagination_data.total =
				response.total_data_count;

			if (response.error_types) {
				this.state.device_error_filters.map((error, index) => {
					let filterData = _find(response.error_types, {
						type: error.key,
					});

					if (filterData) {
						stateToUpdate.device_error_filters[index].value =
							filterData.count;
					}
				});
			}
			if (debug_type === 'error_logs') {
				this.setState(Object.assign({}, stateToUpdate));
			} else {
				return stateToUpdate;
			}
		} else {
			this.openNotification('error', response.message);
			this.setState({
				unauthorised_access: false,
				error_API: true,
				error_API_msg: response.message,
			});
		}
	}

	/**
	 * Retrives raw data of device error.
	 * @param {bool} isReportDownload - report should download or not.
	 * @param {number} page - current page number.
	 * @param {number} pageSize - current showing rows per page.
	 */
	async fetchDeviceErrorRawData(
		isReportDownload = false,
		page = 1,
		pageSize = DeviceDebugObjectData.device_error_raw_table.config
			.pagination_data.pageSize
	) {
		console.log('targetURLDebug fetchDeviceErrorRawData');
		let perPageSize = pageSize;
		if (isReportDownload) {
			perPageSize = 'all';
		}

		let response = await deviceErrorRawData(
			this.props.client_id,
			this.state.selected_device[0],
			this.state.raw_log_from_time,
			this.state.raw_log_upto_time,
			page,
			perPageSize
		);
		if (response.status === 403) {
			this.setState({
				unauthorised_access: true,
				unauthorised_access_msg: response.message,
			});
		} else if (response.status === 'success') {
			let rawLogs = [];
			/*let rawLogData = [
				{
				"timestamp": 1234567890,
				"raw_log_sent_by": 1,
				"message": "qwertyuiop"
				}
			];*/

			let rawLogData = response.raw_logs;

			rawLogData.map((data, index) => {
				rawLogs.push({
					sl_no: pageSize * (page - 1) + index + 1,
					date: moment
						.unix(parseInt(data.timestamp / 1000))
						.format('Do MMM YYYY, HH:mm:ss'), // .format('Do MMM YYYY, HH:mm:ss'),
					raw_log_sent_by:
						!isNaN(data.raw_log_sent_by) &&
						data.raw_log_sent_by === 1
							? 'device'
							: !isNaN(data.raw_log_sent_by) &&
							  data.raw_log_sent_by === 2
							? 'server'
							: 'info',
					message: data.message,
				});
			});

			let deviceRawErrorReportObject = {
				...DeviceDebugReportObjectData.report_data,
			};

			if (this.device_qr_code) {
				deviceRawErrorReportObject.body_section.body_data[0].section_head =
					'Device QR - ' + this.device_qr_code;
			}

			DeviceDebugObjectData.device_error_raw_table.config.pagination_data.total =
				response.total_data_count;

			deviceRawErrorReportObject.body_section.body_data[0].table_data = rawLogs;
			deviceRawErrorReportObject.file_name =
				deviceRawErrorReportObject.file_name +
				' ' +
				moment
					.unix(this.state.raw_log_from_time)
					.format('DD-MM-YYYY_HH-mm') +
				'_' +
				moment
					.unix(this.state.raw_log_upto_time)
					.format('DD-MM-YYYY_HH-mm');
			deviceRawErrorReportObject.time_interval = [
				this.state.raw_log_from_time,
				this.state.raw_log_upto_time,
			];

			let downloadReport = false;
			if (isReportDownload) {
				downloadReport = true;
			}

			this.setState(
				{
					device_error_raw_logs: rawLogs,
					device_error_raw_logs_report_object: deviceRawErrorReportObject,
					csv_report_download: downloadReport,
				},
				() => {
					console.log('RAW', this.state.device_error_raw_logs);
				}
			);
		} else {
			this.openNotification('error', response.message);
			this.setState({
				unauthorised_access: false,
				error_API: true,
				error_API_msg: response.message,
				csv_button_loading: false,
			});
		}
	}

	async fetchDataFileCount() {
		let response = await deviceDebugDataFiles({
			client_id: this.props.client_id,
			device_id: this.state.selected_device[0],
			from_date: this.device_error_from_date,
			// ? this.state.device_error_from_date
			// : this.device_error_from_date,
			upto_date: this.device_error_upto_date,
			// ? this.state.device_error_upto_date
			// : this.device_error_upto_date,
			page: 1,
			page_size: 0,
		});

		if (response.status === 403) {
			this.setState({
				unauthorised_access: true,
				unauthorised_access_msg: response.message,
			});
		} else if (response.status === 'success') {
			this.setState({
				data_files_received: response.total_count
					? response.total_count
					: 0,
			});
		} else {
			this.openNotification('error', response.message);
			this.setState({
				unauthorised_access: false,
				error_API: true,
				error_API_msg: response.message,
			});
		}
	}

	async fetchOfflineTrend() {
		let max_signal_value = -1;
		let response = await deviceDebugOfflineTrend({
			device_id: this.state.selected_device[0],
			from_date: moment
				.unix(this.offline_trend_from_date)
				.tz('Asia/Kolkata')
				.unix(),
			upto_date: moment
				.unix(this.offline_trend_upto_date)
				.tz('Asia/Kolkata')
				.unix(),
			// from_date: this.state.offline_trend_from_date
			// 	? moment
			// 			.unix(this.state.offline_trend_from_date)
			// 			.tz('Asia/Kolkata')
			// 			.unix()
			// 	: moment
			// 			.unix(this.offline_trend_from_date)
			// 			.tz('Asia/Kolkata')
			// 			.unix(),
			// upto_date: this.state.offline_trend_upto_date
			// 	? moment
			// 			.unix(this.state.offline_trend_upto_date)
			// 			.tz('Asia/Kolkata')
			// 			.unix()
			// 	: moment
			// 			.unix(this.offline_trend_upto_date)
			// 			.tz('Asia/Kolkata')
			// 			.unix(),
		});

		if (response.status === 403) {
			this.setState({
				unauthorised_access: true,
				offline_graph_loading: false,
				unauthorised_access_msg: response.message,
			});
		} else if (response.status === 'success') {
			let offline_trends_response = [];
			if (
				response.trends_graph_data &&
				response.trends_graph_data.length
			) {
				response.trends_graph_data.map((dd) => {
					if (dd.signal_strength && dd.signal_strength != null) {
						if (parseFloat(dd.signal_strength) > max_signal_value) {
							max_signal_value = parseFloat(dd.signal_strength);
						}
					}
					if (dd.timestamp && dd.timestamp > 0) {
						offline_trends_response.push({
							timestamp: dd.timestamp,
							signal_strength: dd.signal_strength
								? dd.signal_strength
								: null,
							network_type:
								dd.network_type && dd.network_type != ''
									? dd.network_type
									: 'gprs',
							ac_power:
								dd.ac_power != null
									? parseInt(dd.ac_power)
									: null,
							status:
								dd.status != null ? parseInt(dd.status) : null,
							battery_voltage: dd.battery_voltage
								? dd.battery_voltage
								: null,
						});
					}
				});
			}
			max_signal_value = max_signal_value + 5;

			this.max_signal_value = max_signal_value;

			this.setState(
				{
					offline_trends_data: offline_trends_response,
					//offline_trends_status_data: response.status_graph_data,
					//offline_trends_debug_data: response.debug_graph_data,
					offline_graph_loading: false,
				},
				() => {
					this.getOfflineTrendGraphDataStructure(
						this.state.offline_trends_data
					);
				}
			);
		} else {
			this.openNotification('error', response.message);
			this.setState({
				unauthorised_access: false,
				error_API: true,
				error_API_msg: response.message,
				offline_graph_loading: false,
				offline_trends_data: undefined,
			});
		}
	}

	async fetchDeviceOfflineLogs(
		page = 1,
		pageSize = DeviceDebugObjectData.device_offline_table.config
			.pagination_data.pageSize
	) {
		console.log('DEVICE KEY 1', this.state.selected_device);
		let response = await deviceDebugOfflineLog({
			device_id: this.state.selected_device[0],
			from_date: this.offline_log_from_date,
			// ? this.state.offline_log_from_date
			// : this.offline_log_from_date,
			upto_date: this.offline_log_upto_date,
			// ? this.state.offline_log_upto_date
			// : this.offline_log_upto_date,
			page: page,
			page_size: pageSize,
		});
		if (response.status === 403) {
			this.setState({
				unauthorised_access: true,
				unauthorised_access_msg: response.message,
				offline_log_loading: false,
				device_offline_logs: [],
			});
		} else if (response.status === 'success') {
			let offline_logs = [];

			let offline_logs_data = response.offline_data;

			offline_logs_data.map((data, index) => {
				let offline_time_format =
					data.offline_time && Number(data.offline_time)
						? moment(data.offline_time * 1000).format(
								'Do MMM YYYY, HH:mm:ss' // 'Do MMM YYYY, HH:mm:ss'
						  )
						: '';
				let online_time_format =
					data.online_time && Number(data.online_time)
						? moment(data.online_time * 1000).format(
								'Do MMM YYYY, HH:mm:ss' // 'Do MMM YYYY, HH:mm:ss'
						  )
						: '';
				let duration_interval = data.duration;
				console.log('DUR', duration_interval);
				if (offline_time_format !== '' && online_time_format === '')
					duration_interval = moment().unix() - data.offline_time;

				let hour_value =
					(duration_interval - (duration_interval % 3600)) / 3600;
				let minute_value =
					((duration_interval % 3600) -
						((duration_interval % 3600) % 60)) /
					60;
				let second_value = (duration_interval % 3600) % 60;
				// let hour_value = moment
				// 	.duration(duration_interval, 'seconds')
				// 	.hours();
				// let minute_value = moment
				// 	.duration(duration_interval, 'seconds')
				// 	.minutes();
				// let second_value = moment
				// 	.duration(duration_interval, 'seconds')
				// 	.seconds();

				let offline_duration =
					duration_interval < 3600
						? duration_interval < 60
							? '00:00:' +
							  (second_value >= 10
									? second_value
									: '0' + second_value)
							: '00:' +
							  (minute_value >= 10
									? minute_value
									: '0' + minute_value) +
							  ':' +
							  (second_value >= 10
									? second_value
									: '0' + second_value)
						: (hour_value >= 10 ? hour_value : '0' + hour_value) +
						  ':' +
						  (minute_value >= 10
								? minute_value
								: '0' + minute_value) +
						  ':' +
						  (second_value >= 10
								? second_value
								: '0' + second_value);

				offline_logs.push({
					sl_no: pageSize * (page - 1) + index + 1,
					offline_date:
						offline_time_format !== '' ? offline_time_format : '-',
					online_date:
						online_time_format !== '' ? online_time_format : '-',
					duration:
						offline_time_format !== '' ? offline_duration : '-',
				});
			});

			DeviceDebugObjectData.device_offline_table.config.pagination_data.total =
				response.total_count;

			this.setState({
				device_offline_logs: offline_logs,
				offline_log_loading: false,
			});
		} else {
			this.openNotification('error', response.message);
			this.setState({
				unauthorised_access: false,
				error_API: true,
				error_API_msg: response.message,
				csv_button_loading: false,
				offline_log_loading: false,
				device_offline_logs: [],
			});
		}
	}

	/**
	 * Returns 2 array for pie and historical graph object.
	 * @param {array} distributionArray - network distribution array for pie graph.
	 * @param {array} usageArray - network usage array for historical graph.
	 */
	networkGraphData(distributionArray, usageArray) {
		let networkHistoryPie = {
				...DeviceDebugObjectData.network_pie_graph.graph_data_object,
			},
			networkHistory = {
				...DeviceDebugObjectData.network_usage_graph.graph_data_object,
			};

		if (distributionArray && Object.keys(distributionArray).length) {
			// console.log('network_ pie', distributionArray);
			let wifi_count = 0,
				ethernet_count = 0,
				gprs_count = 0;

			if (distributionArray.wifi) wifi_count = distributionArray.wifi;
			if (distributionArray.gprs) gprs_count = distributionArray.gprs;
			if (distributionArray.ethernet)
				ethernet_count = distributionArray.ethernet;

			networkHistoryPie.series_data = [
				{
					name: 'Network Used',
					colorByPoint: true,
					data: [
						{
							name: 'GPRS',
							y: gprs_count,
						},
						{
							name: 'WiFi',
							y: wifi_count,
						},
						{
							name: 'Ethernet',
							y: ethernet_count,
						},
					],
				},
			];
		}
		if (usageArray && Object.keys(usageArray).length) {
			let arr_wifi_main = [],
				arr_ethernet_main = [],
				arr_gprs_main = [];

			if (usageArray.wifi && usageArray.wifi.length) {
				let sorted_usage_array_wifi = _orderBy(
					usageArray.wifi,
					['timestamp'],
					['asc']
				);
				sorted_usage_array_wifi.map((w) => {
					arr_wifi_main.push([
						w.timestamp * 1000,
						parseFloat(w.strength),
					]);
				});
			}

			if (usageArray.gprs && usageArray.gprs.length) {
				let sorted_usage_array_gprs = _orderBy(
					usageArray.gprs,
					['timestamp'],
					['asc']
				);
				sorted_usage_array_gprs.map((g) => {
					arr_gprs_main.push([
						g.timestamp * 1000,
						parseFloat(g.strength),
					]);
				});
			}

			if (usageArray.ethernet && usageArray.ethernet.length) {
				let sorted_usage_array_ethernet = _orderBy(
					usageArray.ethernet,
					['timestamp'],
					['asc']
				);
				sorted_usage_array_ethernet.map((e) => {
					arr_ethernet_main.push([
						e.timestamp * 1000,
						parseFloat(e.strength),
					]);
				});
			}

			networkHistory.series_data = [
				{
					name: 'GPRS',
					data: arr_gprs_main,
				},
				{
					name: 'WiFi',
					data: arr_wifi_main,
				},
				{
					name: 'Ethernet',
					data: arr_ethernet_main,
				},
			];
		}
		console.log('nhh', networkHistoryPie, networkHistory);
		return {
			network_history_pie: networkHistoryPie,
			network_history: networkHistory,
		};
	}

	/**
	 * onOk function for device error raw data range selector.
	 * @param {bool} status - calendar dropdown open status.
	 */
	handleRawErrorCalenderChange(value) {
		console.log('handleRawErrorCalenderChange_', value);
		let that = this;
		that.setState(
			{
				raw_log_from_time: moment(value[0]).unix(),
				raw_log_upto_time: moment(value[1]).unix(),
			},
			() => {
				if (DeviceDebugObjectData.rawRangePicker.config.ranges) {
					Object.keys(
						DeviceDebugObjectData.rawRangePicker.config.ranges
					).map((range) => {
						// console.log('range_', moment(DeviceDebugObjectData.rawRangePicker.config.ranges[range][0]).unix());
						// console.log('range_ 1', moment(value[0]).unix());
						if (
							moment(
								DeviceDebugObjectData.rawRangePicker.config
									.ranges[range][0]
							).unix() === moment(value[0]).unix() &&
							moment(
								DeviceDebugObjectData.rawRangePicker.config
									.ranges[range][1]
							).unix() === moment(value[1]).unix()
						) {
							that.handleRawErrorCalenderOk(value);
						}
					});
				}
			}
		);
	}

	/**
	 * onChange function for device error raw data range selector.
	 * @param {number} value - selected date array.
	 */
	handleRawErrorCalenderOk(value) {
		console.log('handleRawErrorCalenderChange_ 1', value);

		this.setState(
			{
				device_error_raw_logs: undefined,
				device_raw_error_page_no: 1,
				raw_log_from_time: moment(value[0]).unix(),
				raw_log_upto_time: moment(value[1]).unix(),
			},
			() => {
				this.fetchDeviceErrorRawData(
					false,
					1,
					this.state.device_raw_error_page_size
				);
			}
		);
		/*if (moment(value[1]).unix() - moment(value[0]).unix() <= 86400) {
			this.raw_log_from_time_copy = moment(value[0]).unix();
			this.raw_log_upto_time_copy = moment(value[1]).unix();
		} else {
			this.setState(
				{
					raw_log_from_time: this.raw_log_from_time_copy,
					raw_log_upto_time: this.raw_log_upto_time_copy,
				},
				() => {
					this.openNotification('warning', 'Please select range between 24 hours');
				}
			);
		}*/
	}

	/**
	 * calendar open and close function callback for device error raw data range selector.
	 * @param {string} status - calendar open status.
	 */
	onRawCalendarOpenChange(status) {
		console.log('onRawCalendarOpenChange_ ', status);
		/*if (
			!status &&
			this.state.raw_log_from_time !== this.raw_log_from_time_copy
		) {
			this.setState({
				raw_log_from_time: this.raw_log_from_time_copy,
				raw_log_upto_time: this.raw_log_upto_time_copy,
			});
		}*/
	}

	/**
	 * report download function for device error raw data.
	 */
	downloadDeviceErrorReport() {
		if (
			this.state.raw_log_upto_time - this.state.raw_log_from_time <=
			86400
		) {
			this.setState(
				{
					csv_button_loading: true,
					csv_report_download: undefined,
				},
				() => {
					this.fetchDeviceErrorRawData(true);
				}
			);
		} else {
			this.openNotification(
				'warning',
				'Please select range between 24 hours'
			);
		}
	}

	/**
	 * callback function for report success download.
	 */
	downloadRawReportClose() {
		this.setState(
			{
				csv_button_loading: false,
			},
			() => {
				this.openNotification(
					'success',
					'CSV Report Downloaded for Device Error Raw Data.'
				);
			}
		);
	}

	downloadDeviceReport() {
		this.setState(
			{
				device_csv_button_loading: true,
				device_csv_report_download: undefined,
			},
			() => {
				this.getDeviceErrorCsvStructure(true);
			}
		);
	}

	downloadDeviceReportClose() {
		console.log('downloadDeviceReportClose_');
		this.setState(
			{
				device_csv_button_loading: false,
			},
			() => {
				this.openNotification(
					'success',
					'CSV Report Downloaded for Device Error Log Data.'
				);
			}
		);
	}

	/**
	 * onClick function for device error tab.
	 * @param {string} key - selected tab key.
	 */
	tabChange(key) {
		let targetUrl = '',
			that = this;
		if (that.props.history) {
			this.parsed_search = queryString.parse(that.props.location.search);

			if (
				that.props.location.pathname.includes('/assigned') ||
				that.props.location.pathname.includes('/unassigned')
			) {
				if (that.props.is_application_filter) {
					if (that.props.location.pathname.includes('/assigned')) {
						targetUrl =
							that.platform_slug +
							'/customer-management/' +
							that.props.customer_id +
							'/applications/' +
							that.props.application_id +
							'/devices/assigned/' +
							this.parsed_search.device_id +
							'/device-debug?customer_id=' +
							this.parsed_search.customer_id +
							'&application_id=' +
							this.parsed_search.application_id +
							'&device_id=' +
							this.parsed_search.device_id +
							'&device_qr_code=' +
							this.parsed_search.device_qr_code +
							'&type_name=' +
							this.parsed_search.type_name;
					} else {
						targetUrl =
							that.platform_slug +
							'/customer-management/' +
							that.props.customer_id +
							'/applications/' +
							that.props.application_id +
							'/devices/unassigned/' +
							this.parsed_search.device_id +
							'/device-debug?customer_id=' +
							this.parsed_search.customer_id +
							'&application_id=' +
							this.parsed_search.application_id +
							'&device_id=' +
							this.parsed_search.device_id +
							'&device_qr_code=' +
							this.parsed_search.device_qr_code +
							'&type_name=' +
							this.parsed_search.type_name;
					}
				} else {
					if (that.props.location.pathname.includes('/assigned')) {
						targetUrl =
							that.platform_slug +
							'/devices/assigned/' +
							this.parsed_search.device_id +
							'/device-debug?customer_id=' +
							this.parsed_search.customer_id +
							'&application_id=' +
							this.parsed_search.application_id +
							'&device_id=' +
							this.parsed_search.device_id +
							'&device_qr_code=' +
							this.parsed_search.device_qr_code +
							'&type_name=' +
							this.parsed_search.type_name;
					} else {
						targetUrl =
							that.platform_slug +
							'/devices/unassigned/' +
							this.parsed_search.device_id +
							'/device-debug?customer_id=' +
							this.parsed_search.customer_id +
							'&application_id=' +
							this.parsed_search.application_id +
							'&device_id=' +
							this.parsed_search.device_id +
							'&device_qr_code=' +
							this.parsed_search.device_qr_code +
							'&type_name=' +
							this.parsed_search.type_name;
					}
				}
			} else {
				if (that.props.is_application_filter) {
					targetUrl =
						that.platform_slug +
						'/customer-management/' +
						that.props.customer_id +
						'/things/' +
						that.props.thingDetails.thing_id +
						'/devices/' +
						that.props.selected_device[0] +
						'/device-debug';
				} else {
					targetUrl =
						that.platform_slug +
						'/things/' +
						that.props.thingDetails.thing_id +
						'/devices/' +
						that.props.selected_device[0] +
						'/device-debug';
				}
			}
		}

		that.setState(
			(state, props) => {
				let stateToUpdate = {};
				stateToUpdate.selected_tab = key;

				return stateToUpdate;
			},
			() => {
				if (that.state.selected_tab === 'network_details') {
					if (that.props.history) {
						if (
							that.props.location.pathname.includes(
								'/assigned'
							) ||
							that.props.location.pathname.includes('/unassigned')
						) {
							targetUrl = targetUrl + '&tab=network_details';
						} else {
							targetUrl = targetUrl + '?tab=network_details';
						}
						that.props.history.push(targetUrl);
					}
					that.fetchDeviceNetworkSection();
				} else if (that.state.selected_tab === 'device_error') {
					if (that.props.history) {
						if (
							that.props.location.pathname.includes(
								'/assigned'
							) ||
							that.props.location.pathname.includes('/unassigned')
						) {
							targetUrl = targetUrl + '&tab=device_error';
						} else {
							targetUrl = targetUrl + '?tab=device_error';
						}
						that.props.history.push(targetUrl);
					}
					that.fetchDataFileCount();
					that.fetchDeviceNetworkSection();
					that.fetchPieChartData();
					that.fetchFilterErrorLogs();
				} else if (that.state.selected_tab === 'raw_log') {
					if (that.props.history) {
						if (
							that.props.location.pathname.includes(
								'/assigned'
							) ||
							that.props.location.pathname.includes('/unassigned')
						) {
							targetUrl = targetUrl + '&tab=raw_log';
						} else {
							targetUrl = targetUrl + '?tab=raw_log';
						}
						that.props.history.push(targetUrl);
					}
					that.fetchDeviceErrorRawData(
						false,
						that.state.device_raw_error_page_no,
						that.state.device_raw_error_page_size
					);
				} else if (that.state.selected_tab === 'offline_logs') {
					if (that.props.history) {
						if (
							that.props.location.pathname.includes(
								'/assigned'
							) ||
							that.props.location.pathname.includes('/unassigned')
						) {
							targetUrl = targetUrl + '&tab=offline_logs';
						} else {
							targetUrl = targetUrl + '?tab=offline_logs';
						}
						that.props.history.push(targetUrl);
					}
					that.fetchDeviceOfflineLogs();
				} else if (
					that.state.selected_tab === 'offline_trend' &&
					that.props.history
				) {
					if (
						that.props.location.pathname.includes('/assigned') ||
						that.props.location.pathname.includes('/unassigned')
					) {
						targetUrl = targetUrl + '&tab=offline_trend';
					} else {
						targetUrl = targetUrl + '?tab=offline_trend';
					}
					that.props.history.push(targetUrl);
				}
				console.log('targetURLDebug', targetUrl);
			}
		);
	}

	/**
	 * onClick function for device error table pagination.
	 * @param {number} page - current page number.
	 * @param {number} pageSize - current showing rows per page.
	 */
	deviceErrorPagination(page, pageSize) {
		this.setState(
			{
				error_logs: undefined,
				device_error_page_no: page,
				device_error_page_size: pageSize,
			},
			() => {
				this.fetchFilterErrorLogs(this.state.url_text, page, pageSize);
			}
		);
	}

	/**
	 * onClick function for device raw error table pagination.
	 * @param {number} page - current page number.
	 * @param {number} pageSize - current showing rows per page.
	 */
	rawErrorPagination(page, pageSize) {
		this.setState(
			{
				device_error_raw_logs: undefined,
				device_raw_error_page_no: page,
				device_raw_error_page_size: pageSize,
			},
			() => {
				this.fetchDeviceErrorRawData(false, page, pageSize);
			}
		);
	}

	offlineLogsPagination(page, pageSize) {
		this.setState(
			{
				device_offline_logs: undefined,
				device_offline_page_no: page,
				device_offline_page_size: pageSize,
			},
			() => {
				this.fetchDeviceOfflineLogs(page, pageSize);
			}
		);
	}

	openDataFileDrawer() {
		this.setState(
			{
				show_data_drawer: true,
			},
			() => {
				console.log('openDataFileDrawer_', this.state.show_data_drawer);
			}
		);
	}

	closeDataFileDrawer() {
		this.setState({
			show_data_drawer: false,
		});
	}

	/**
	 * Disabled onclick function.
	 */
	doNothing() {}

	checkUserisEndCustomer() {
		let applicationId = this.props.application_id;

		//condition for end customer
		// return applicationId !== 17 && applicationId !== 12;

		//condition for only datoms-x
		return applicationId !== 12;
	}

	checkIsVendor() {
		let applicationId = this.props.application_id,
			clientId = this.props.client_id;

			console.log('checkIsVendor_', this.props);

		if (applicationId == 17 && this.props.enabled_features?.includes("DeviceManagement:Rawlog")) {
			return true;
		}
	}

	render() {
		// console.log('BLAH', this.state.graphObjectData);
		// console.log('DDDT', this.state.error_logs);
		const {
			device_details,
			battery_details,
			network_details,
			device_error_logs,
			device_filter,
			csv_button_loading,
			device_csv_button_loading,
			device_debug_data,
			device_error_raw_logs_report_object,
			device_error_report_object,
			raw_log_from_time,
			raw_log_upto_time,
			device_error_raw_logs,
			device_offline_logs,
			device_offline_page_no,
			unauthorised_access,
			unauthorised_access_msg,
			csv_report_download,
			device_csv_report_download,
			device_error_page_no,
			device_raw_error_page_no,
		} = this.state;

		let deviceStatusText = 'Offline';
		if (device_details.device_is_online) {
			deviceStatusText = 'Online';
		}

		let modeIcon = <img src={AcPowerIcon} width={15} height={15} />,
			modeText = <b>NA</b>;
		if (
			battery_details.mode_of_operation &&
			battery_details.mode_of_operation === 'ac'
		) {
			if (battery_details.ac_status) {
				if (battery_details.ac_status === true) modeText = <b>ON</b>;
				else if (battery_details.ac_power === false)
					modeText = <b>OFF</b>;
				else modeText = <b>NA</b>;
			}
		}

		let firmwareCurrent = (
			<div className="device-firmware">
				<span>
					{this.props.t? this.props.t('firmware') + " : ": "Firmware : "}<b>N/A</b>
				</span>
			</div>
		);
		if (device_details.firmware_current) {
			firmwareCurrent = (
				<div className="device-firmware">
					<span>{this.props.t? this.props.t('firmware') + " : ": "Firmware : "}</span>
					<span>
						<b>{device_details.firmware_current}</b>
					</span>
				</div>
			);
		}
		// let firmwareCurrent = '';
		// if (device_details.firmware_current) {
		// 	firmwareCurrent = <b>{device_details.firmware_current}</b>;
		// }
		// else {
		// 	modeIcon = (
		// 		<div className="battery">
		// 			<div className="bat-body">
		// 				<div className="bar"></div>
		// 				<div className="bar"></div>
		// 				<div className="bar"></div>
		// 				<div className="bar"></div>
		// 				<div className="bar"></div>
		// 				<div className="bar"></div>
		// 				<div className="bar"></div>
		// 				<div className="bar"></div>
		// 				<div className="bar"></div>
		// 				<div className="bar"></div>
		// 			</div>
		// 			<div className="bat-top"></div>
		// 		</div>
		// 	);
		// 	modeText = 'Battery Mode';
		// }

		let chargeStatus = 'Battery',
			chargeIcon = (
				<img src={ThunderboltIconInactive} width={15} height={15} />
			);
		if (battery_details.battery_charging) {
			chargeIcon = <img src={ThunderboltIcon} width={15} height={15} />;
			//chargeStatus = 'Charging';
		}

		let networkStatus = <div />,
			signalStrength = <div />,
			gprsIcon = <img src={GprsSignalRounded} width={15} height={15} />,
			wifiIcon = <img src={WifiSignalRounded} width={15} height={15} />;

		let networkStrengthText = '';
		if (network_details.current_signal_strength > 20) {
			networkStrengthText = 'Excellent';
			gprsIcon = <img src={GprsSignalRounded5} width={15} height={15} />;
			wifiIcon = <img src={WifiSignalRounded3} width={15} height={15} />;
		} else if (
			network_details.current_signal_strength >= 13 &&
			network_details.current_signal_strength <= 20
		) {
			networkStrengthText = 'Good';
			gprsIcon = <img src={GprsSignalRounded4} width={15} height={15} />;
			wifiIcon = <img src={WifiSignalRounded2} width={15} height={15} />;
		} else if (
			network_details.current_signal_strength >= 6 &&
			network_details.current_signal_strength <= 12
		) {
			networkStrengthText = 'Poor';
			gprsIcon = <img src={GprsSignalRounded2} width={15} height={15} />;
			wifiIcon = <img src={WifiSignalRounded1} width={15} height={15} />;
		} else if (network_details.current_signal_strength < 6) {
			networkStrengthText = 'No Signal';
		}

		if (
			network_details.current_operating_network ||
			(network_details.current_operating_network != null &&
				network_details.current_operating_network != '')
		) {
			if (network_details.current_operating_network == 'wifi') {
				networkStatus = (
					<div className="nw-mode">
						<div className="network-icon">{wifiIcon}</div>
						<div className="mode">Wi-Fi</div>
					</div>
				);
				signalStrength = (
					<div className="signal-strength">
						<div>
							{this.props.t? this.props.t('signal_strength')+ ": ": "Signal Strength: "}
							{/* Signal Strength:  */}
						</div>
						<div className="signal-value">
							<b>
								{network_details.current_signal_strength
									? network_details.current_signal_strength +
									  ' (' +
									  networkStrengthText +
									  ')'
									: this.props.t? this.props.t('no_signal'): 'No Signal'}
							</b>
						</div>
					</div>
				);
			} else if (network_details.current_operating_network == 'gprs') {
				networkStatus = (
					<div className="nw-mode">
						<div className="network-icon">{gprsIcon}</div>
						<div className="mode">GPRS</div>
					</div>
				);
				signalStrength = (
					<div className="signal-strength">
						<div>
							{this.props.t? this.props.t('signal_strength')+ ": ": "Signal Strength: "}
							{/* Signal Strength:  */}
						</div>
						<div className="signal-value">
							<b>
								{network_details.current_signal_strength
									? network_details.current_signal_strength +
									  ' (' +
									  networkStrengthText +
									  ')'
									: this.props.t? this.props.t('no_signal'): 'No Signal'}
							</b>
						</div>
					</div>
				);
			} else if (
				network_details.current_operating_network == 'ethernet'
			) {
				networkStatus = (
					<div className="nw-mode">
						<div className="network-icon">
							<img
								src={EthernetCableIcon}
								alt=""
								width={15}
								height={15}
							/>
						</div>
						<div className="mode">Ethernet</div>
					</div>
				);
				signalStrength = (
					<div className="signal-strength">
						<div>Signal Strength: </div>
						<div className="signal-value">
							<b>30</b>
						</div>
					</div>
				);
			}
		} else {
			networkStatus = (
				<div className="nw-mode no-data">
					<div className="message">
						Network Mode : <b>Unavailable</b>
					</div>
				</div>
			);
			signalStrength = (
				<div className="signal-strength">
					<div>Signal Strength: </div>
					<div className="signal-value">
						<b>
							{network_details.current_signal_strength
								? network_details.current_signal_strength +
								  ' (' +
								  networkStrengthText +
								  ')'
								: 'No Signal'}
						</b>
					</div>
				</div>
			);
		}

		let offline_trend_display = [];

		// if (this.state.offline_graph_loading) {
		// 	offline_trend_display.push(
		// 		<div className="no-data-display">
		// 			<AntSpin />
		// 		</div>
		// 	);
		// } else if (
		// 	this.state.offline_trends_data &&
		// 	this.state.offline_trends_data.length
		// ) {
		// 	offline_trend_display.push(<div id="graph_container"></div>);
		// } else {
		// 	offline_trend_display.push(
		// 		<div className="no-data-display">No Chart Data to Show</div>
		// 	);
		// }

		if (this.state.offline_graph_loading) {
			offline_trend_display.push(
				<div className="no-data-display">
					<AntSpin />
				</div>
			);
		} else if (
			this.state.sync_graph_data &&
			this.state.sync_graph_data.length &&
			this.state.offline_trends_data &&
			this.state.offline_trends_data.length
		) {
			let status_count = _countBy(
					this.state.offline_trends_data,
					function (rec) {
						return rec.status == null;
					}
				),
				signal_strength_count = _countBy(
					this.state.offline_trends_data,
					function (rec) {
						return rec.signal_strength == null;
					}
				),
				ac_power_count = _countBy(
					this.state.offline_trends_data,
					function (rec) {
						return rec.ac_power == null;
					}
				),
				battery_voltage_count = _countBy(
					this.state.offline_trends_data,
					function (rec) {
						return rec.battery_voltage == null;
					}
				);
			console.log(
				'null status',
				status_count,
				'null signal',
				signal_strength_count,
				'null ac',
				ac_power_count,
				'null battery',
				battery_voltage_count,
				'data length',
				this.state.offline_trends_data
			);
			this.state.sync_graph_data.map((graphData) => {
				let GraphObjectOfflineTrend = JSON.parse(
					JSON.stringify(this.state.graphObjectData)
				);

				GraphObjectOfflineTrend.graph_data.config.chart =
					graphData.chart;
				GraphObjectOfflineTrend.graph_data.config.title =
					graphData.title;
				GraphObjectOfflineTrend.graph_data.config.legend.enabled = false;
				GraphObjectOfflineTrend.graph_data.config.xAxis =
					graphData.xAxis;
				GraphObjectOfflineTrend.graph_data.config.yAxis =
					graphData.yAxis;
				GraphObjectOfflineTrend.graph_data.config.legend.itemStyle.fontSize =
					'12px';
				GraphObjectOfflineTrend.graph_data.config.tooltip =
					graphData.tooltip;
				GraphObjectOfflineTrend.graph_data.config.responsive = {};
				GraphObjectOfflineTrend.graph_data.config.plotOptions = {};
				GraphObjectOfflineTrend.graph_data.series_data =
					graphData.series;

				// if (graphData.title.text == 'Signal Strength') {
				// 	GraphObjectOfflineTrend.graph_data.config.yAxis.tickPositions = [
				// 		0,
				// 		6,
				// 		12,
				// 		20,
				// 		//this.max_signal_value,
				// 	];
				// }
				console.log('graph data q', graphData.title.text);
				if (
					(graphData.title.text == 'Device Status' &&
						status_count.true !=
							this.state.offline_trends_data.length) ||
					(graphData.title.text == 'Signal Strength' &&
						signal_strength_count.true !=
							this.state.offline_trends_data.length) ||
					(graphData.title.text == 'Device Power' &&
						ac_power_count.true !=
							this.state.offline_trends_data.length) ||
					(graphData.title.text == 'Battery Voltage' &&
						battery_voltage_count.true !=
							this.state.offline_trends_data.length)
				) {
					offline_trend_display.push(
						<GraphHighcharts
							graphData={GraphObjectOfflineTrend.graph_data}
						/>
					);
				}
				// 	offline_trend_display.push(
				// 		<div>
				// 			<div className="graph-data-title">
				// 				{graphData.title.text}
				// 			</div>
				// 			<div className="no-data-display">
				// 				No Chart Data to Show
				// 			</div>
				// 		</div>
				// 	);
				// } else {
				// 	offline_trend_display.push(
				// 		<GraphHighcharts
				// 			graphData={GraphObjectOfflineTrend.graph_data}
				// 		/>
				// 	);
				// }
				console.log('graphObject_', GraphObjectOfflineTrend.graph_data);
			});
		} else {
			offline_trend_display.push(
				<div className="no-data-display">No Chart Data to Show</div>
			);
		}

		let pieGraph = (
				<div className="no-data-network">No Chart Data to Show </div>
			),
			historicGraph = (
				<div className="no-data-network"> No Chart Data to Show </div>
			);
		if (network_details.network_usage_distribution) {
			pieGraph = (
				<GraphHighcharts
					ref={DeviceDebugObjectData.network_pie_graph.ref}
					graphData={network_details.network_usage_distribution}
				/>
			);
		}
		if (network_details.network_usage) {
			historicGraph = (
				<GraphHighcharts
					ref={DeviceDebugObjectData.network_usage_graph.ref}
					graphData={network_details.network_usage}
				/>
			);
		}

		let network_section;

		console.log('network_section_', this.state.network_loading);

		if (this.state.network_loading) {
			network_section = (
				<div className="no-data-display">
					<AntSpin />
				</div>
			);
		} else {
			network_section = (
				<div className="graph-section">
					<div className="history-pie-chart">
						{pieGraph}
						<div className="name">
							{(this.dateChanged
								? moment
										.unix(
											this.state.network_detail_from_date
										)
										.format('DD/MM/YYYY') +
								  ' - ' +
								  moment
										.unix(
											this.state.network_detail_upto_date
										)
										.format('DD/MM/YYYY')
								: 'Last 3 Months') + ' Network Usage'}
						</div>
					</div>
					<div className="history-graph">
						{historicGraph}
						<div className="name">
							{(this.dateChanged
								? moment
										.unix(
											this.state.network_detail_from_date
										)
										.format('DD/MM/YYYY') +
								  ' - ' +
								  moment
										.unix(
											this.state.network_detail_upto_date
										)
										.format('DD/MM/YYYY')
								: 'Last 3 Months') + ' Network Strength'}
						</div>
					</div>
				</div>
			);
		}

		console.log('network_section_ 1', network_section);
		console.log('ErrCum', this.state.error_cumulative);

		let network_error_pie_graph = (
			<div className="no-pie-data">No chart data to show</div>
		);
		if (
			this.state.error_cumulative &&
			this.state.error_cumulative.network
		) {
			network_error_pie_graph = (
				<GraphHighcharts
					graphData={this.pieChartGraphData(
						this.state.error_cumulative.network,
						'#E20F00' //'#F61B0B' // #A9D9F7
					)}
				/>
			);
		}

		let power_error_pie_graph = (
			<div className="no-pie-data">No chart data to show</div>
		);
		if (this.state.error_cumulative && this.state.error_cumulative.power) {
			power_error_pie_graph = (
				<GraphHighcharts
					graphData={this.pieChartGraphData(
						this.state.error_cumulative.power,
						'#D57A00' //'#F99004' // #FBC7AC
					)}
				/>
			);
		}

		let sensor_error_pie_graph = (
			<div className="no-pie-data">No chart data to show</div>
		);
		if (this.state.error_cumulative && this.state.error_cumulative.sensor) {
			sensor_error_pie_graph = (
				<GraphHighcharts
					graphData={this.pieChartGraphData(
						this.state.error_cumulative.sensor,
						'#0E8B49' //'#93C82E' // #EECD96
					)}
				/>
			);
		}

		let other_error_pie_graph = (
			<div className="no-pie-data">No chart data to show</div>
		);
		if (this.state.error_cumulative && this.state.error_cumulative.other) {
			other_error_pie_graph = (
				<GraphHighcharts
					graphData={this.pieChartGraphData(
						this.state.error_cumulative.other,
						'#148CC6' //'#148CC6' // #A1D7DD
					)}
				/>
			);
		}

		let deviceErrorTableData = device_error_logs;
		if (device_filter !== 'total_error') {
			deviceErrorTableData = _filter(device_error_logs, {
				error_type: device_filter,
			});
		}

		let device_error_section = [];

		if (this.state.device_error_loading) {
			device_error_section.push(
				<div className="no-data-display no-data-min-height">
					<AntSpin />
				</div>
			);
		} else {
			device_error_section.push(
				<div className="device-error">
					<div className="data-file-section">
						{/* <AntButton
							className="data-download-button"
							type="primary"
							icon={<FileTextOutlined />}
							onClick={() => this.openDataFileDrawer()}
						>
							View Historical Data Files
						</AntButton> */}
						<div className="data-count-section">
							{/* <div className="data-count">
								<div className="value">
									{this.state.data_files_received}
								</div>
								<div className="name">Data File Received</div>
							</div> */}
							<div className="data-count">
								<div className="value">
									{this.state.historical_data_count}
								</div>
								<div className="name">
									Historical Data Count
								</div>
							</div>
							<div className="data-count">
								<div className="value">
									{this.state.realtime_data_count}
								</div>
								<div className="name">Realtime Data Count</div>
							</div>
						</div>
					</div>
					<div className="pie-chart-container">
						<div className="pie-item">
							<div className="error-name">
								Network Errors -{' '}
								<span className="total-value">
									{this.state.error_cumulative.network &&
									this.state.error_cumulative.network.total
										? this.state.error_cumulative.network
												.total
										: 0}
								</span>
							</div>
							<div>{network_error_pie_graph}</div>
						</div>
						<div className="pie-item">
							<div className="error-name">
								Power Errors -{' '}
								<span className="total-value">
									{this.state.error_cumulative.power &&
									this.state.error_cumulative.power.total
										? this.state.error_cumulative.power
												.total
										: 0}
								</span>
							</div>
							<div>{power_error_pie_graph}</div>
						</div>
						<div className="pie-item">
							<div className="error-name">
								Sensor Errors -{' '}
								<span className="total-value">
									{this.state.error_cumulative.sensor &&
									this.state.error_cumulative.sensor.total
										? this.state.error_cumulative.sensor
												.total
										: 0}
								</span>
							</div>
							<div>{sensor_error_pie_graph}</div>
						</div>
						<div className="pie-item">
							<div className="error-name">
								Other Errors -{' '}
								<span className="total-value">
									{this.state.error_cumulative.other &&
									this.state.error_cumulative.other.total
										? this.state.error_cumulative.other
												.total
										: 0}
								</span>
							</div>
							<div>{other_error_pie_graph}</div>
						</div>
					</div>
					<div className="filter-error-container">
						<div className="head-text">Filter Errors</div>
						<div>
							<SelectWithFilter
								config={this.state.error_config}
								onFilterChange={(filterObj) =>
									this.onFilterChange(filterObj)
								}
							/>
						</div>
					</div>
					<div className="device-error-table">
						<CustomTable
							config_data={
								DeviceDebugObjectData.device_error_table.config
							}
							columns={
								DeviceDebugObjectData.device_error_table
									.head_data
							}
							dataSource={this.state.error_logs}
							loading={!this.state.error_logs ? true : false}
							onPaginationChange={this.deviceErrorPagination}
							current={device_error_page_no}
						/>
					</div>
				</div>
			);
		}

		let deviceErrorReportSection = <div />;
		if (device_csv_button_loading && device_csv_report_download) {
			deviceErrorReportSection = (
				<Report
					onReportDone={this.downloadDeviceReportClose}
					report_data={device_error_report_object}
				/>
			);
		}

		let csvDeviceButton = (
			<div
				className="report-button report-butn-device"
				onClick={() => this.downloadDeviceReport()}
			>
				<FileDoneOutlined />
				<span className="text">Download CSV</span>
			</div>
		);
		if (device_csv_button_loading) {
			csvDeviceButton = (
				<div
					className="report-button report-butn-device disabled"
					onClick={() => this.doNothing()}
				>
					<span className="text">
						<AntSpin />
					</span>
				</div>
			);
		}

		let rawErrorReportSection = <div />;
		if (csv_button_loading && csv_report_download) {
			rawErrorReportSection = (
				<Report
					onReportDone={this.downloadRawReportClose}
					report_data={device_error_raw_logs_report_object}
				/>
			);
		}

		let csvButton = (
			<div
				className="report-button report-button-raw"
				onClick={() => this.downloadDeviceErrorReport()}
			>
				<FileDoneOutlined />
				<span className="text">Download CSV</span>
			</div>
		);
		if (csv_button_loading) {
			csvButton = (
				<div
					className="report-button report-button-raw disabled"
					onClick={() => this.doNothing()}
				>
					<span className="text">
						<AntSpin />
					</span>
				</div>
			);
		}

		let bodySection = (
			<div className="body-section no-data">
				<AntSpin />
			</div>
		);
		console.log(
			'templatee',
			device_details,
			'vv',
			device_details.config_template
		);
		if (Object.keys(device_debug_data).length) {
			let deviceSection = (
				<div className="device-data-section">
					<AntSpin />
				</div>
			);
			if (Object.keys(device_debug_data).length) {
				deviceSection = (
					<div className="device-data-section">
						<div className="device-details">
							<div className="device-status">
								<div>
									{device_details.device_is_online ? (
										<img src={Online} />
									) : (
										<img src={Offline} />
									)}
								</div>
								<div className="status-text">
									<b>{deviceStatusText}</b>
								</div>
							</div>
							<AntDivider className="divider" type="vertical" />
							<div className="device-data-received-time">
								{this.props.t? this.props.t('last_data_received_at')+ " : ": 'Last Data Received at : '}
								{/* {'Last Data Received at : '} */}
								<b>
									{device_details.device_last_data_received_time
										? moment
												.unix(
													device_details.device_last_data_received_time
												)
												.format('Do MMM YYYY, HH:mm')
										: this.props.t? this.props.t('no_data_received'): 'No data received'}
								</b>
							</div>
							<AntDivider
								className="divider step-4"
								type="vertical"
							/>
							{firmwareCurrent}
							<AntDivider
								className="divider step-2"
								type="vertical"
							/>
							<div className="battery-mode-icon">
								{modeIcon}
								<div className="mode-text">
									{this.props.t? this.props.t('power_status') + " : ": "Power Status : "}
									{/* {'Power Status : '} */}
									{modeText}
								</div>
							</div>
							{(() => {
								if (battery_details.battery_percent) {
									return (
										<AntDivider
											className="divider break step-4"
											type="vertical"
										/>
									);
								}
							})()}
							{/* <AntDivider
								className="divider break step-4"
								type="vertical"
							/> */}
							{(() => {
								if (battery_details.battery_percent) {
									return (
										<div className="battery-section">
											<div className="battery">
												<div className="minus-icon">
													-
												</div>
												<div className="bat-body">
													<div
														className={
															'bar' +
															(battery_details.battery_percent &&
															battery_details.battery_percent >
																0
																? device_details.device_is_online
																	? ' active'
																	: ' deactive'
																: '')
														}
													></div>
													<div
														className={
															'bar' +
															(battery_details.battery_percent &&
															battery_details.battery_percent >=
																20
																? device_details.device_is_online
																	? ' active'
																	: ' deactive'
																: '')
														}
													></div>
													<div
														className={
															'bar' +
															(battery_details.battery_percent &&
															battery_details.battery_percent >=
																30
																? device_details.device_is_online
																	? ' active'
																	: ' deactive'
																: '')
														}
													></div>
													<div
														className={
															'bar' +
															(battery_details.battery_percent &&
															battery_details.battery_percent >=
																40
																? device_details.device_is_online
																	? ' active'
																	: ' deactive'
																: '')
														}
													></div>
													<div
														className={
															'bar' +
															(battery_details.battery_percent &&
															battery_details.battery_percent >=
																50
																? device_details.device_is_online
																	? ' active'
																	: ' deactive'
																: '')
														}
													></div>
													<div
														className={
															'bar' +
															(battery_details.battery_percent &&
															battery_details.battery_percent >=
																60
																? device_details.device_is_online
																	? ' active'
																	: ' deactive'
																: '')
														}
													></div>
													<div
														className={
															'bar' +
															(battery_details.battery_percent &&
															battery_details.battery_percent >=
																70
																? device_details.device_is_online
																	? ' active'
																	: ' deactive'
																: '')
														}
													></div>
													<div
														className={
															'bar' +
															(battery_details.battery_percent &&
															battery_details.battery_percent >=
																80
																? device_details.device_is_online
																	? ' active'
																	: ' deactive'
																: '')
														}
													></div>
													<div
														className={
															'bar' +
															(battery_details.battery_percent &&
															battery_details.battery_percent >=
																90
																? device_details.device_is_online
																	? ' active'
																	: ' deactive'
																: '')
														}
													></div>
													<div
														className={
															'bar' +
															(battery_details.battery_percent &&
															battery_details.battery_percent >=
																100
																? device_details.device_is_online
																	? ' active'
																	: ' deactive'
																: '')
														}
													></div>
												</div>
												<div className="bat-top"></div>
												<div className="plus-icon">
													+
												</div>
											</div>
											<div className="charging-status">
												<div className="charge-icon">
													{battery_details.battery_percent
														? chargeIcon
														: ''}
												</div>
												<div className="charge-status">
													<b>
														{battery_details.battery_percent
															? chargeStatus +
															  ' (' +
															  battery_details.battery_percent +
															  '%)'
															: ''}
													</b>
												</div>
											</div>
										</div>
									);
								}
							})()}
							{/* <div className="battery-section">
								<div className="battery">
									<div className="minus-icon">-</div>
									<div className="bat-body">
										<div
											className={
												'bar' +
												(battery_details.battery_percent &&
												battery_details.battery_percent >
													0
													? device_details.device_is_online
														? ' active'
														: ' deactive'
													: '')
											}
										></div>
										<div
											className={
												'bar' +
												(battery_details.battery_percent &&
												battery_details.battery_percent >=
													20
													? device_details.device_is_online
														? ' active'
														: ' deactive'
													: '')
											}
										></div>
										<div
											className={
												'bar' +
												(battery_details.battery_percent &&
												battery_details.battery_percent >=
													30
													? device_details.device_is_online
														? ' active'
														: ' deactive'
													: '')
											}
										></div>
										<div
											className={
												'bar' +
												(battery_details.battery_percent &&
												battery_details.battery_percent >=
													40
													? device_details.device_is_online
														? ' active'
														: ' deactive'
													: '')
											}
										></div>
										<div
											className={
												'bar' +
												(battery_details.battery_percent &&
												battery_details.battery_percent >=
													50
													? device_details.device_is_online
														? ' active'
														: ' deactive'
													: '')
											}
										></div>
										<div
											className={
												'bar' +
												(battery_details.battery_percent &&
												battery_details.battery_percent >=
													60
													? device_details.device_is_online
														? ' active'
														: ' deactive'
													: '')
											}
										></div>
										<div
											className={
												'bar' +
												(battery_details.battery_percent &&
												battery_details.battery_percent >=
													70
													? device_details.device_is_online
														? ' active'
														: ' deactive'
													: '')
											}
										></div>
										<div
											className={
												'bar' +
												(battery_details.battery_percent &&
												battery_details.battery_percent >=
													80
													? device_details.device_is_online
														? ' active'
														: ' deactive'
													: '')
											}
										></div>
										<div
											className={
												'bar' +
												(battery_details.battery_percent &&
												battery_details.battery_percent >=
													90
													? device_details.device_is_online
														? ' active'
														: ' deactive'
													: '')
											}
										></div>
										<div
											className={
												'bar' +
												(battery_details.battery_percent &&
												battery_details.battery_percent >=
													100
													? device_details.device_is_online
														? ' active'
														: ' deactive'
													: '')
											}
										></div>
									</div>
									<div className="bat-top"></div>
									<div className="plus-icon">+</div>
								</div>
								<div className="charging-status">
									<div className="charge-icon">
										{battery_details.battery_percent
											? chargeIcon
											: ''}
									</div>
									<div className="charge-status">
										<b>
											{battery_details.battery_percent
												? chargeStatus +
												  ' (' +
												  battery_details.battery_percent +
												  '%)'
												: ''}
										</b>
									</div>
								</div>
							</div> */}
							<AntDivider
								className="divider mar-top step-3"
								type="vertical"
							/>
							{networkStatus}
							<AntDivider className="divider" type="vertical" />
							{signalStrength}
							{device_details &&
							device_details.config_template ? (
								<>
									<AntDivider
										className="divider"
										type="vertical"
									/>
									<div className="nw-mode no-data">
										<div className="message">
											Template :{' '}
											<b>
												{device_details.config_template}
											</b>
										</div>
									</div>
								</>
							) : (
								''
							)}
						</div>
						{/* <div className="offline-trend-section">
							<div className="one-line">
								<div className="head-text">OFFLINE TREND</div>
								<div>
									<SelectWithRangepicker
										fromTime={
											this.state.offline_trend_from_date
										}
										uptoTime={
											this.state.offline_trend_upto_date
										}
										style_type="dotted"
										config={datePickerConfig}
										ranges={
											getRanges(moment().unix())
												.offline_trend_ranges
										}
										onRangeChange={(rangeArr, isCustom) =>
											this.onRangeChangeOfflineTrend(
												rangeArr,
												isCustom
											)
										}
										onOpenChange={
											this.onOpenChangeOfflineTrend
										}
									/>
								</div>
							</div>
							{offline_trend_display}
						</div> */}
						<div className="network-device-error-section">
							{/* <div className="one-line">
								<div className="head-text">DEVICE DETAILS</div>
								<div>
									<SelectWithRangepicker
										fromTime={
											this.state.device_detail_from_date
										}
										uptoTime={
											this.state.device_detail_upto_date
										}
										style_type="dotted"
										config={datePickerConfig}
										ranges={
											getRanges(moment().unix()).ranges
										}
										onRangeChange={(rangeArr, isCustom) =>
											this.onRangeChangeDeviceDetail(
												rangeArr,
												isCustom
											)
										}
										onOpenChange={
											this.onOpenChangeDeviceDetail
										}
									/>
								</div>
							</div>

							<div className="network-section">
								<div className="head-text">NETWORK</div>
								{network_section}
							</div> */}
							<div className="device-error-section">
								<AntTabs
									defaultActiveKey="offline_trend"
									onChange={(key) => this.tabChange(key)}
									activeKey={this.state.selected_tab}
								>
									<AntTabPane
										tab={this.props.t? this.props.t('offline_trend'): "Offline Trend"}
										key="offline_trend"
									>
										<SelectWithRangepicker
											t={this.props.t}
											fromTime={
												this.state
													.offline_trend_from_date
											}
											uptoTime={
												this.state
													.offline_trend_upto_date
											}
											style_type="dotted"
											config={datePickerConfig}
											ranges={
												getRanges(moment().unix())
													.offline_trend_ranges
											}
											onRangeChange={(
												rangeArr,
												isCustom
											) =>
												this.onRangeChangeOfflineTrend(
													rangeArr,
													isCustom
												)
											}
											onOpenChange={
												this.onOpenChangeOfflineTrend
											}
										/>
										<div className="offline-trend-section">
											{offline_trend_display}
										</div>
									</AntTabPane>
									<AntTabPane
										tab={this.props.t? this.props.t('network_details'): "Network Details"}
										key="network_details"
									>
										<SelectWithRangepicker
											t={this.props.t}
											fromTime={
												this.state
													.network_detail_from_date
											}
											uptoTime={
												this.state
													.network_detail_upto_date
											}
											style_type="dotted"
											config={datePickerConfig}
											ranges={
												getRanges(moment().unix())
													.ranges
											}
											onRangeChange={(
												rangeArr,
												isCustom
											) =>
												this.onRangeChangeNetworkDetail(
													rangeArr,
													isCustom
												)
											}
											onOpenChange={
												this.onOpenChangeNetworkDetail
											}
										/>
										<div className="network-section">
											{network_section}
										</div>
									</AntTabPane>
									<AntTabPane
										tab={this.props.t? this.props.t('device_error'): "Device Error"}
										key="device_error"
									>
										{csvDeviceButton}
										<SelectWithRangepicker
											t={this.props.t}
											fromTime={
												this.state
													.device_error_from_date
											}
											uptoTime={
												this.state
													.device_error_upto_date
											}
											style_type="dotted"
											config={datePickerConfig}
											ranges={
												getRanges(moment().unix())
													.ranges
											}
											onRangeChange={(
												rangeArr,
												isCustom
											) =>
												this.onRangeChangeDeviceError(
													rangeArr,
													isCustom
												)
											}
											onOpenChange={
												this.onOpenChangeDeviceError
											}
										/>
										{device_error_section}
									</AntTabPane>
									{(!this.checkUserisEndCustomer() || this.checkIsVendor()) && (
										<AntTabPane tab={this.props.t? this.props.t('raw_log'): "Raw Log"} key="raw_log">
											{csvButton}
											<div className='rangepicker-container'>
												<AntRangePicker
													validation_type="array"
													onChange={
														this
															.handleRawErrorCalenderChange
													}
													config={
														DeviceDebugObjectData
															.rawRangePicker.config
													}
													value={[
														moment.unix(
															raw_log_from_time
														),
														moment.unix(
															raw_log_upto_time
														),
													]}
													onOk={
														this
															.handleRawErrorCalenderOk
													}
													onOpenChange={
														this.onRawCalendarOpenChange
													}
												/>
											</div>
											<div className="device-raw-error">
												<CustomTable
													config_data={
														DeviceDebugObjectData
															.device_error_raw_table
															.config
													}
													columns={
														DeviceDebugObjectData
															.device_error_raw_table
															.head_data
													}
													dataSource={
														device_error_raw_logs
													}
													loading={
														!device_error_raw_logs
															? true
															: false
													}
													onPaginationChange={
														this.rawErrorPagination
													}
													current={
														device_raw_error_page_no
													}
												/>
											</div>
										</AntTabPane>
									)}
									<AntTabPane
										tab={this.props.t? this.props.t('offline_logs'): "Offline Logs"}
										key="offline_logs"
									>
										<SelectWithRangepicker
											t={this.props.t}
											fromTime={
												this.state.offline_log_from_date
											}
											uptoTime={
												this.state.offline_log_upto_date
											}
											style_type="dotted"
											config={datePickerConfig}
											ranges={
												getRanges(moment().unix())
													.ranges
											}
											onRangeChange={(
												rangeArr,
												isCustom
											) =>
												this.onRangeChangeOfflineLog(
													rangeArr,
													isCustom
												)
											}
											onOpenChange={
												this.onOpenChangeOfflineLog
											}
										/>
										<div className="device-offline-logs-section">
											<CustomTable
												config_data={
													DeviceDebugObjectData
														.device_offline_table
														.config
												}
												columns={
													DeviceDebugObjectData
														.device_offline_table
														.head_data
												}
												dataSource={device_offline_logs}
												loading={
													this.state
														.offline_log_loading
												}
												// loading={
												// 	!device_offline_logs
												// 		? true
												// 		: false
												// }
												onPaginationChange={
													this.offlineLogsPagination
												}
												current={device_offline_page_no}
											/>
										</div>
									</AntTabPane>
								</AntTabs>
							</div>
						</div>
					</div>
				);
			}

			bodySection = (
				<div className="body-section">
					<div className="upper-section">{deviceSection}</div>
				</div>
			);
		} else if (unauthorised_access) {
			bodySection = (
				<div className="body-section">
					<div className="no-data-text">
						<AntAlert
							message="Access Denied"
							description={unauthorised_access_msg}
							type="error"
						/>
					</div>
				</div>
			);
		}

		console.log('show_data_drawer_', this.state.show_data_drawer);
		let historicalDrawer;
		if (this.state.show_data_drawer) {
			historicalDrawer = (
				<DataFileDrawer
					clientId={this.props.client_id}
					selected_device={this.state.selected_device[0]}
					from_date={this.state.from_date}
					upto_date={this.state.upto_date}
					closeDrawer={() => this.closeDataFileDrawer()}
				/>
			);
		}

		return (
			<Suspense
				fallback={
					<div>
						<AntLayout className={'contains'}>
							<AntSpin className="align-center-loading" />
						</AntLayout>
					</div>
				}
			>
				<div className="device-debug">
					{bodySection}
					{rawErrorReportSection}
					{deviceErrorReportSection}
					{historicalDrawer}
				</div>
			</Suspense>
		);
	}
}
