@import '../../../../styles/default-color.less';

.debug-page-calendar-dropdown {
	.ant-calendar-footer {
		.ant-calendar-time-picker-btn {
			display: none !important;
		}
	}
}

.historical-data-drawer {
	.ant-drawer-title {
		font: normal normal 600 18px/24px Segoe UI;
	}
	.ant-drawer-close {
		font-size: 17px;
		.anticon-close {
			padding: 2px;
			border-radius: 9px;
			background: #e4e6e9 0% 0% no-repeat padding-box;
			svg {
				padding: 2px;
				color: #232323;
			}
		}
	}

	.head-text {
		text-align: left;
		color: #232323;
		font: normal normal 600 18px/24px Segoe UI;
		letter-spacing: 0px;
		opacity: 1;

		.color-text {
			color: #808080;
		}
		.color-date {
			color: @defaultColor;
		}
	}

	.ant-table-thead {
		background-color: #eeeeee;
	}

	.ant-table-tbody {
		.status-block {
			padding: 3px 8px;
			text-align: center;
			border-radius: 14px;
			display: flex;
			font-size: 12px;
			align-items: center;
			justify-content: left;

			&.processed {
				background: #9fe48a;

				.anticon-check {
					color: black;
				}
			}

			&.pending {
				background: #ffba6f;

				.anticon-clock-circle {
					color: black;
				}
			}

			.anticon {
				margin-right: 4px;
			}
		}

		.anticon-cloud-download {
			color: #c4c2c2;

			svg {
				width: 28px;
				height: 23px;
				margin-top: 7px;
			}
		}
	}
}

.device-debug {
	.auto-width .ant-tabs-nav {
		width: 100% !important;
		overflow-y: hidden;
		overflow-x: auto;
	}

	.ant-tabs-top > .ant-tabs-nav::before {
		border-bottom: 1px solid #e8e8e8 !important;
	}

	.ant-input {
		font-size: 12px !important;
		background-color: transparent !important;
		color: #7686a1;
	}

	.heatmap-container {
		.heatmap {
			justify-content: center;
		}
	}

	&.collapsed {
		width: calc(100% - 80px) !important;
	}

	&.wid-100 {
		width: 100% !important;
		margin-top: 0 !important;
	}

	.dspl-flex-cntr {
		display: flex;
		justify-content: center;
		.error-tag {
			color: black;
		}
	}

	.view-with-data {
		padding: 0 !important;
	}

	.ant-drawer-mask {
		display: none;
	}

	.aln-cntr {
		text-align: center;
	}

	.ant-drawer-mask {
		display: none;
	}

	.aln-cntr {
		text-align: center;
	}

	.body-section {
		width: 80%;
		margin: auto;
		height: calc(100vh - 110px);
		overflow: auto;

		&.no-data {
			text-align: center;
			height: calc(100vh - 130px);
			position: relative;

			.ant-spin {
				position: absolute !important;
				top: 50% !important;
				left: 50% !important;
				transform: translate(-50%, -50%) !important;
			}
		}

		.upper-section {
			background: #f3f3f3;
			padding: 15px 40px;
			padding-bottom: 30px;
			margin: auto;
			margin-top: 20px;
			border-radius: 20px;
			box-shadow: 1px 2px 2px #a2a2a229;

			.ant-tabs-tab {
				border-bottom: 1px solid #e8e8e8 !important;
			}

			.ant-tabs-tab .ant-tabs-tab-btn {
				color: #374375 !important;
				padding: 0 13px;
			}
			.ant-tabs-ink-bar {
				background: #f58740 !important;
			}
		}

		.no-device-text {
			text-align: center;
			font-weight: 600;
		}

		.no-device-text {
			text-align: center;
			font-weight: 600;
		}

		.device-data-section {
			position: relative;
			min-height: calc(100vh - 750px);

			.ant-spin {
				position: absolute !important;
				top: 50% !important;
				left: 50% !important;
				transform: translate(-50%, -50%) !important;
			}
		}

		.select-container {
			width: fit-content;
			min-width: 120px;
			margin: auto;

			.select-button {
				font-size: 13px;
				color: #374375;

				.ant-select-selection {
					border: none;
					border-bottom: 1px dashed;
					background: transparent;

					&:hover,
					&:focus {
						outline: none;
						box-shadow: none;
					}
				}

				.ant-select-arrow {
					color: #374375;
				}
			}
		}

		.device-details {
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			justify-content: center !important;
			color: #232323;
			margin: auto;
			margin-top: 30px;
			padding-bottom: 30px;
			font-size: 13px;

			.divider {
				margin: 0 25px;
				top: 2px;
				width: 1px;
				height: 0.9em;
				border-color: #d3d3d3;
			}

			.device-status {
				display: flex;
				align-items: center;

				.status-text {
					margin-left: 10px;
				}
				// .status-icon {
				// 	height: 8px;
				// 	width: 8px;
				// 	margin-right: 5px;
				// 	border-radius: 50%;
				// 	margin-top: 3px;

				// 	&.online {
				// 		background: #1fbe74;
				// 	}

				// 	&.offline {
				// 		background: #f00;
				// 	}
				// }
			}

			.battery-mode-icon {
				display: flex;
				align-items: center;

				.battery {
					display: flex;
					align-items: center;
					justify-content: center;

					.bat-body {
						width: 40px;
						padding: 0 2px;
						height: 15px;
						border-radius: 4px;
						border: 1px solid #707070;
						display: flex;
						align-items: center;
						justify-content: center;

						.bar {
							width: 9px;
							height: 13px;
							margin-right: 2px;
							background: #e9e9e9;
						}
					}

					.bat-top {
						height: 8px;
						width: 3px;
						border: 1px solid #707070;
						border-radius: 15px;
						border-left: none;
					}
				}

				.mode-text {
					margin-left: 15px;
				}
			}

			.battery-section {
				display: flex;
				align-items: center;

				.battery {
					display: flex;
					align-items: center;
					justify-content: center;

					.minus-icon {
						margin-right: 5px;
					}

					.bat-body {
						width: 50px;
						padding: 0 2px;
						height: 20px;
						border-radius: 4px;
						border: 1px solid #707070;
						display: flex;
						align-items: center;
						justify-content: center;

						.bar {
							width: 9px;
							height: 18px;
							margin-right: 2px;
							background: #e9e9e9;

							&.active {
								background: #1da57a !important;
							}

							&.deactive {
								background: #0000007a !important;
							}
						}
					}

					.bat-top {
						height: 10px;
						width: 5px;
						border: 1px solid #707070;
						border-radius: 15px;
						border-left: none;
					}

					.plus-icon {
						margin-left: 5px;
					}
				}

				.charging-status {
					display: flex;
					align-items: center;
					margin-left: 5px;

					.charge-icon {
						display: flex;
						margin-top: 3px;
					}
				}
			}

			.nw-mode {
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 20px;

				.network-icon {
					margin-right: 15px;
					display: flex;
					align-items: center;
				}

				.no-data {
					font-size: 16px;
					color: #808080;
				}
			}

			.signal-strength {
				display: flex;
				align-items: center;

				.signal-value {
					margin-left: 10px;
				}
			}
		}

		.network-device-error-section {
			background: #fff;
			border-radius: 10px;
			padding: 20px;

			.one-line {
				display: flex;
				justify-content: space-between;

				.head-text {
					color: #374375;
					font-weight: 700;
					text-align: left;
					margin-bottom: 20px;
				}
			}

			.device-error-section {
				width: 98%;
				padding: 0 !important;
				padding-bottom: 25px !important;
				background: #fff;
				margin: 0 auto !important;
				border-radius: 20px;
				position: relative;
				// box-shadow: 10px 12px 50px #8D8D8D29;

				.head-text {
					font-weight: 700;
					color: #232323;
					margin-top: 10px;
					margin-bottom: 20px;
					text-align: left;
				}

				.ant-tabs-tabpane {
					padding-top: 10px;
				}

				.offline-trend-section {
					margin-top: 0;
					background: #fff;
					border-radius: 10px;
					padding: 20px;
					padding-top: 0;
					margin-bottom: 20px;

					.one-line {
						display: flex;
						justify-content: space-between;

						.head-text {
							text-align: left;
							font: normal normal 600 14px/19px Segoe UI;
							letter-spacing: 0px;
							color: #374375;
							opacity: 1;
						}
					}
					.no-data-display {
						font-size: 15px;
						text-align: center;
						position: relative;
						min-height: 50px;
						padding-top: 20px;
					}
					.graph-data-title {
						font-size: 16px;
						color: #232323;
						padding-left: 40px;
					}
				}

				.network-section {
					width: 98%;
					padding: 15px 0;
					padding-bottom: 25px;
					background: #fff;
					margin: auto;
					margin-top: 0;
					border-radius: 20px;
					// box-shadow: 10px 12px 50px #8D8D8D29;

					.no-data-display {
						min-height: 90px;
						position: relative;
					}

					.head-text {
						font-weight: 700;
						color: #232323;
						margin-top: 10px;
						margin-bottom: 20px;
						text-align: left;
					}

					.graph-section {
						display: flex;
						align-items: center;
						justify-content: center;
						width: 100%;
						border: 2px solid #e1e1e1;
						padding: 10px;
						border-radius: 6px;
						.history-pie-chart,
						.history-graph {
							position: relative;
							.no-data-network {
								position: absolute;
								top: 50%;
								left: 50%;
								transform: translate(-50%, -50%);
								color: #969696;
							}
						}

						.history-pie-chart {
							width: 40%;
							position: relative;
							height: 228px;

							.graph-container-no-data {
								font-size: 15px;
								position: absolute;
								top: 50%;
								left: 50%;
								transform: translate(-50%, -50%);
							}

							.highcharts-credits {
								display: none;
							}

							.name {
								font-size: 10px;
								color: #808080;
								text-align: center;
								position: absolute;
								bottom: 0;
								margin: 0 auto;
								left: 0;
								right: 0;
							}
						}

						.history-graph {
							width: 60%;
							position: relative;
							height: 228px;

							.graph-container-no-data {
								font-size: 15px;
								position: absolute;
								top: 50%;
								left: 50%;
								transform: translate(-50%, -50%);
							}

							.highcharts-credits {
								display: none;
							}

							.name {
								font-size: 10px;
								color: #808080;
								text-align: center;
								position: absolute;
								bottom: 0;
								margin: 0 auto;
								left: 0;
								right: 0;
							}
						}
					}

					.no-data {
						display: flex;
						justify-content: center;
						align-items: center;
						color: #808080;
					}
				}

				.ant-tabs-bar {
					width: fit-content;

					.ant-tabs-nav .ant-tabs-tab {
						color: #808080;
					}

					.ant-tabs-tab-active {
						color: #3a3c46 !important;
						font-weight: bold;
					}
				}

				.ant-calendar-picker {
					position: absolute;
					right: 0;
					top: 10px;
					width: 300px !important;
				}

				.report-button {
					position: absolute;
					background: #f58740;
					padding: 5px 10px;
					color: #fff;
					border-radius: 6px;
					cursor: pointer;

					.ant-spin-dot-item {
						background-color: #fff !important;
					}

					.ant-spin-dot {
						margin-top: 7px !important;
					}

					&.disabled {
						cursor: not-allowed !important;

						.text {
							margin-left: 0 !important;
							padding: 0 10px;
						}
					}

					.text {
						margin-left: 10px;
					}
				}

				.report-button-raw {
					//left: 380px;
					right: 0px !important;
					top: 111px !important;
				}

				.report-butn-device {
					right: 18px !important;
					top: 128px !important;
				}

				.rangepicker-container {
					display: flex;
					justify-content: flex-end;
					// .ant-select {
					// 	position: absolute;
					// 	right: 10px;
					// 	top: -18px;
					// }
					// .ant-picker {
					// 	position: absolute;
					// 	right: 10px;
					// 	top: -18px !important;
					// }
				}

				// .ant-picker {
				// 	position: absolute;
				// 	top: 80px;
				// 	right: 0px;
				// }

				.no-data-min-height {
					min-height: 90px !important;
				}

				.device-error {
					width: 100% !important;
					margin: auto;
					margin-top: 88px !important;
					position: relative;

					.data-file-section {
						display: flex;
						box-shadow: 10px 10px 40px #00000017;
						border: 1px solid #e1e1e1;
						border-radius: 10px;
						background: #fff;
						padding: 25px 35px;
						align-items: center;
						justify-content: center;
						margin-bottom: 30px;

						.data-count-section {
							width: 100%;
							display: flex;
							justify-content: space-evenly;
							align-items: center;
							//margin-left: 270px;

							.data-count {
								margin: 0 10px;
								text-align: center;

								.value {
									font-weight: bold;
									font-size: 18px;
								}

								.name {
									font-size: 16px;
								}
							}
						}
					}

					.pie-chart-container {
						display: flex;
						flex-wrap: wrap;
						justify-content: space-between;
						.pie-item {
							width: 23%;
							height: 240px;
							border: 1px solid #e1e1e1;
							background: #ffffff 0% 0% no-repeat padding-box;
							box-shadow: 10px 20px 40px #00000017;
							border-radius: 6px;
							position: relative;

							.no-pie-data {
								text-align: center;
								position: absolute;
								top: 50%;
								left: 50%;
								transform: translate(-50%, -50%);
								color: #969696;
							}

							.error-name {
								text-align: center;
								margin-top: 14px;
								color: #3a3c46;

								.total-value {
									font: normal normal bold 18px/24px Segoe UI;
									color: black;
								}
							}
						}
					}

					.filter-error-container {
						margin-top: 30px;

						.head-text {
							text-align: left;
							margin-bottom: 10px;
							padding-top: 15px;
							font: normal normal 600 16px/21px Segoe UI;
							letter-spacing: 0px;
							color: #3a3c46;
							opacity: 1;
						}
						.filter-tag-container {
							.select-filter-container {
								border-color: #d5d5d5;
								&:hover {
									border-color: @defaultColor !important;
								}
								.anticon-filter {
									display: none !important;
								}
							}
						}
					}

					.card-view {
						background: #fff !important;
						margin-top: 20px;
						border-radius: 10px !important;
						border: 2px solid #fff !important;
						padding: 5px 0px;
						box-shadow: 4px 8px 12px #8d8d8d29;
						border-radius: 15px !important;

						.value-class {
							font-size: 24px !important;
							margin-bottom: 8px;
							font-weight: normal !important;
						}

						.caption-class {
							font-size: 13px !important;
						}

						&.active {
							border: 2px solid #f58740 !important;
						}

						&:hover {
							box-shadow: 4px 8px 12px #8d8d8d14 !important;
						}
					}

					.device-error-table {
						.ant-pagination-mini {
							.ant-pagination-item {
								margin-left: 4px !important;
							}
						}
					}

					.ant-tag {
						border-radius: 6px;
					}

					.ant-table-thead {
						background-color: #d2d2d2;
					}

					.ant-table-thead > tr > th,
					.ant-table-tbody > tr > td {
						padding: 10px 14px !important;
						color: #232323 !important;
					}

					.ant-table-pagination.ant-pagination {
						margin-top: 40px;
						margin-bottom: 16px !important;
					}

					.ant-table-thead > tr > th {
						background: transparent !important;
						border-bottom: 1px solid !important;
					}
				}

				.device-raw-error {
					margin-top: 50px !important;

					.sent-by-icon {
						font-size: 14px;
					}

					.ant-table-thead {
						background-color: #d2d2d2;
					}

					.ant-table-thead > tr > th,
					.ant-table-tbody > tr > td {
						padding: 10px 14px !important;
						color: #232323 !important;
					}

					.ant-table-tbody > tr > td {
						word-break: break-all !important;
					}

					.ant-table-pagination.ant-pagination {
						.ant-pagination-item {
							margin-left: 4px !important;
						}
					}
				}

				.device-offline-logs-section {
					margin-top: 10px !important;
					.ant-table-thead {
						background-color: #d2d2d2;
					}

					.ant-table-thead > tr > th,
					.ant-table-tbody > tr > td {
						padding: 10px 13px !important;
					}

					.ant-table-pagination.ant-pagination {
						.ant-pagination-item {
							margin-left: 4px !important;
						}
					}
				}

				// .device-offline-logs-section {
				// 	margin-top: 20px;

				// 	.ant-collapse {
				// 		border: none !important;

				// 		.ant-collapse-item {
				// 			.ant-collapse-content {
				// 				.ant-collapse-content-box {
				// 					.antD-table-class {
				// 						.ant-table-wrapper {
				// 							.ant-spin-nested-loading {
				// 								.ant-spin-container {
				// 									.ant-table {
				// 										padding-top: 16px;
				// 										.ant-table-content {
				// 											.ant-table-thead {
				// 												background-color: #eeeeee;
				// 											}
				// 											.ant-table-thead
				// 												> tr
				// 												> th {
				// 												.mar-left-12 {
				// 													margin-left: 0px !important;
				// 												}
				// 											}
				// 										}
				// 									}
				// 								}
				// 							}
				// 						}
				// 					}
				// 				}
				// 			}
				// 		}
				// 	}

				// 	.ant-collapse > .ant-collapse-item:last-child,
				// 	.ant-collapse
				// 		> .ant-collapse-item:last-child
				// 		> .ant-collapse-header {
				// 		background: #e1e1e1 !important;
				// 		border: none !important;
				// 	}

				// 	.ant-collapse > .ant-collapse-item:last-child,
				// 	.ant-collapse
				// 		> .ant-collapse-item:last-child
				// 		> .ant-collapse-header {
				// 		font-weight: bold !important;
				// 	}
				// }
			}
		}
	}
}

@media (max-width: 1500px) {
	.device-debug {
		.body-section {
			width: 95%;

			.device-error-section {
				.device-error {
					.card-view {
						padding: 10px 0 !important;
					}
				}
			}
		}
	}
}

@media (max-width: 1440px) {
	.historical-data-drawer {
		.ant-drawer-title,
		.head-text {
			font: normal normal 600 16px/21px Segoe UI;
		}
	}
}

@media (max-width: 1366px) {
	.historical-data-drawer {
		.ant-drawer-title,
		.head-text {
			font: normal normal 600 15px/20px Segoe UI;
		}
	}
	.device-debug {
		.body-section {
			.device-details {
				width: 101% !important;

				// .break {
				// 	display: none;
				// }

				.battery-section,
				.nw-mode,
				.signal-strength,
				.mar-top {
					margin-top: 0px !important;
				}
			}
		}
	}
}

@media (max-width: 1280px) {
	.historical-data-drawer {
		.ant-drawer-title,
		.head-text {
			font: normal normal 600 14px/19px Segoe UI;
		}
	}
	.device-debug {
		.body-section {
			.device-details {
				.divider {
					margin: 0 12px !important;
				}
				// .break {
				// 	display: block;
				// }
			}
		}
	}
}

@media (max-width: 1024px) {
	#debug_drawer {
		width: 100% !important;
	}
	.device-debug {
		.body-section {
			width: 100%;

			.device-details {
				width: 90% !important;
				.divider {
					margin: 0 25px !important;
				}
				.battery-section,
				.nw-mode,
				.signal-strength,
				.mar-top {
					margin-top: 15px !important;
				}
			}

			.device-error-section {
				width: 95%;

				.network-section {
					width: 95%;

					.graph-section {
						.history-pie-chart,
						.history-graph {
							.graph-container-no-data {
								font-size: 13px;
							}
						}
					}
				}
			}
		}
	}
}

@media (max-width: 1023px) {
	.device-debug {
		&.collapsed {
			width: 100% !important;
		}

		.ant-input {
			padding: 5px 0 !important;
		}

		.body-section {
			width: 98%;

			.device-details {
				width: 95% !important;

				.battery-section {
					margin-top: 0;
				}
			}

			.device-error-section {
				width: 100%;

				.network-section {
					width: 100%;
				}
			}
		}
	}
}

@media (max-width: 960px) {
	.device-debug {
		.body-section {
			// .device-details {
			// 	.battery-section {
			// 		margin-top: 10px;
			// 	}
			// }
			.network-device-error-section {
				.device-error-section {
					.device-error {
						// .data-file-section {
						// 	.data-download-button {
						// 		margin-top: 10px;
						// 	}
						// 	.data-count-section {
						// 		margin-left: 20px;
						// 	}
						// }
						.pie-chart-container {
							.pie-item {
								width: 49%;
								height: 350px;
								margin-top: 15px;
							}
						}
					}
				}
			}
		}
	}
}

@media (max-width: 800px) {
	.device-debug {
		.ant-table-pagination-right {
			display: block !important;
			text-align: right !important;

			.ant-pagination-total-text {
				width: 100% !important;
				margin-bottom: 10px !important;
			}

			.ant-pagination-options {
				.ant-pagination-options-size-changer {
					margin-right: 0px !important;
				}
			}
		}

		.body-section {
			.device-details {
				.battery-mode-icon,
				.break {
					margin-top: 15px !important;
				}
			}

			.network-device-error-section {
				.device-error-section {
					.network-section {
						.graph-section {
							.history-pie-chart {
								width: 100%;
							}
						}
					}
					.device-error {
						.filter-error-container {
							.filter-tag-container {
								.select-filter-container {
									.ant-select-multiple {
										width: 400px !important;
									}
								}
							}
						}
						.data-file-section {
							.data-count-section {
								.data-count {
									.name {
										font-size: 12px;
									}
								}
							}
						}
					}
					.report-butn-device {
						right: 8px !important;
					}
					// .ant-tabs {
					// 	.ant-tabs-nav {
					// 		.ant-tabs-nav-wrap {
					// 			.ant-tabs-nav-list {
					// 				.ant-tabs-tab {
					// 					margin-right: 25px;
					// 				}
					// 			}
					// 		}
					// 	}
					// }
				}
			}
		}
	}
}

@media (max-width: 720px) {
	.device-debug {
		.body-section {
			// .device-details {
			// 	.battery-mode-icon {
			// 		margin-top: 10px;
			// 	}
			// }
			.network-device-error-section {
				.device-error-section {
					.network-section {
						.graph-section {
							display: block;
							.history-pie-chart {
								margin: 0 auto;
							}
							.history-graph {
								margin: 0 auto;
							}
						}
					}
					.head-text {
						margin-top: 15px;
					}
					.device-error {
						.filter-error-container {
							.filter-tag-container {
								.select-filter-container {
									.ant-select-multiple {
										width: 325px !important;
									}
								}
							}
						}
						.data-file-section {
							.data-count-section {
								width: fit-content;
								display: block;
								margin-left: 0px;
								.data-count {
									display: flex;
									flex-direction: row-reverse;
									.value {
										margin-left: 10px;
									}
									.name {
										font-size: 16px;
										text-align: left;
										width: 154px;
									}
								}
							}
						}
						.pie-chart-container {
							.pie-item {
								width: 100%;
							}
						}
						.device-error-table {
							.ant-table-pagination-right {
								margin: 16px 0px !important;
							}
						}
					}
					// .ant-tabs {
					// 	.ant-tabs-nav {
					// 		.ant-tabs-nav-wrap {
					// 			.ant-tabs-nav-list {
					// 				.ant-tabs-tab {
					// 					margin-right: 8px;
					// 				}
					// 			}
					// 		}
					// 	}
					// }
				}
			}
		}
	}
}

@media (max-width: 600px) {
	.historical-data-drawer {
		.ant-drawer-title,
		.head-text {
			font: normal normal 600 12px/16px Segoe UI;
		}
	}
	.device-debug {
		.dspl-flex-cntr {
			display: block;

			.error-tag {
				margin-top: 2px;
			}
		}

		.body-section {
			.upper-section {
				padding-left: 15px;
				padding-right: 15px;
				.device-data-section {
					.device-details {
						width: 100% !important;

						.device-firmware,
						.step-2 {
							margin-top: 15px !important;
						}
					}

					.network-device-error-section {
						.device-error-section {
							.network-section {
								.graph-section {
									flex-wrap: wrap;

									.history-pie-chart {
										width: 100%;
										text-align: center;
									}

									.graph-container-no-data {
										width: 100%;
										text-align: center;
										margin-top: 10px;
									}
								}
							}
							// .report-butn-raw {
							// 	top: 130px;
							// 	left: 0px;
							// }

							.device-error {
								.filter-error-container {
									.filter-tag-container {
										.select-filter-container {
											.ant-select-single {
												width: 144px !important;
											}
											.ant-select-multiple {
												width: 268px !important;
											}
										}
									}
								}
								.data-file-section {
									margin-top: 50px;
								}
							}
							// .ant-picker {
							// 	top: 25px !important;
							// 	right: -10px !important;
							// 	width: 350px !important;
							// }
							.ant-calendar-picker {
								position: relative;
							}

							.device-raw-error {
								// margin-top: 100px !important;

								.ant-table-content::-webkit-scrollbar {
									height: 8px !important;
								}

								.ant-table-pagination.ant-pagination {
									margin-top: 40px !important;
								}

								.ant-table-body {
									width: 150% !important;
								}
							}
							// .ant-tabs {
							// 	.ant-tabs-nav {
							// 		.ant-tabs-nav-wrap {
							// 			.ant-tabs-nav-list {
							// 				.ant-tabs-tab {
							// 					margin-right: 0px;
							// 					.ant-tabs-tab-btn {
							// 						padding: 0 10px;
							// 					}
							// 				}
							// 			}
							// 		}
							// 	}
							// }
						}
					}
				}
			}
		}
	}
}

@media (max-width: 540px) {
	.device-debug {
		.body-section {
			.device-details {
				font-size: 14px;
				width: 96% !important;

				.step-2 {
					display: block;
					margin-top: 15px;
				}
			}

			.device-error-section {
				.device-error {
					.ant-table-content::-webkit-scrollbar {
						height: 8px !important;
					}

					.ant-table-body {
						width: 150% !important;
					}
				}
			}
		}
	}
}

@media (max-width: 520px) {
	.historical-data-drawer {
		.ant-drawer-title,
		.head-text {
			font: normal normal 600 14px/19px Segoe UI;
		}
	}
	.device-debug {
		.body-section {
			.network-device-error-section {
				.device-error-section {
					// .ant-picker {
					// 	width: 248px !important;
					// }
					.device-error {
						.filter-error-container {
							.filter-tag-container {
								.select-filter-container {
									.ant-select-single {
										width: 122px !important;
									}
									.ant-select-multiple {
										width: 216px !important;
									}
								}
							}
						}
						.data-file-section {
							display: block;
							// .data-download-button {
							// 	margin-bottom: 10px;
							// 	margin-left: 10px;
							// }
							.data-count-section {
								margin-left: 32px;
							}
						}
					}
					// .ant-tabs {
					// 	.ant-tabs-nav {
					// 		.ant-tabs-nav-wrap {
					// 			.ant-tabs-nav-list {
					// 				.ant-tabs-tab {
					// 					font-size: 13px;
					// 					.ant-tabs-tab-btn {
					// 						padding: 0 4px;
					// 					}
					// 				}
					// 			}
					// 		}
					// 	}
					// }
				}
			}
		}
	}
}

@media (max-width: 420px) {
	.device-debug {
		.body-section {
			.device-details {
				width: 91% !important;

				.step-3 {
					display: block;
				}

				.step-4 {
					display: none;
				}

				.device-firmware {
					margin-top: 15px;
				}
			}

			.device-error-section {
				.device-error {
					.ant-table-body {
						width: 180% !important;
					}
				}
			}
		}
	}
}

@media (max-width: 414px) {
	.device-debug {
		.body-section {
			.upper-section {
				padding-left: 0px !important;
				padding-right: 0px !important;
			}
			.device-details {
				width: 100% !important;
				.divider {
					margin: 0 4px !important;
				}
				.step-3,
				.break {
					margin-top: 15px !important;
				}
			}
			.network-device-error-section {
				.one-line {
					.head-text {
						margin-top: 10px;
					}
					.rangepicker-container {
						.ant-select-single {
							width: 128px !important;
						}
					}
				}
				.device-error-section {
					.offline-trend-section {
						.one-line {
							.head-text {
								margin-top: 10px;
							}
							.rangepicker-container {
								.ant-select-single {
									width: 128px !important;
								}
							}
						}
					}
					// .report-butn-raw {
					// 	top: 150px !important;
					// }

					// .device-raw-error {
					// 	.antD-table-class {
					// 		margin-top: 22px !important;
					// 	}
					// }

					// .ant-tabs {
					// 	.ant-tabs-nav {
					// 		.ant-tabs-nav-wrap {
					// 			.ant-tabs-nav-list {
					// 				margin-top: 10px !important;
					// 			}
					// 		}
					// 	}
					// }

					// .ant-picker {
					// 	top: 56px !important;
					// 	right: 44px !important;
					// }
					.device-error {
						.filter-error-container {
							.filter-tag-container {
								.select-filter-container {
									.ant-select-single {
										width: 120px !important;
									}
									.ant-select-multiple {
										width: 118px !important;
									}
								}
							}
						}
						.data-file-section {
							.data-count-section {
								margin-left: 0px;
							}
						}
					}
				}
			}
		}
	}
}

@media (max-width: 370px) {
	.device-debug {
		.body-section {
			.network-device-error-section {
				.device-error-section {
					.ant-tabs {
						.ant-tabs-nav {
							.ant-tabs-nav-wrap {
								.ant-tabs-nav-list {
									.ant-tabs-tab {
										#rc-tabs-1-tab-offline_logs {
											padding-left: 0px;
										}
									}
								}
							}
						}
					}

					.device-error {
						.filter-error-container {
							.filter-tag-container {
								.select-filter-container {
									.ant-select-single {
										width: 119px !important;
									}
									.ant-select-multiple {
										width: 78px !important;
									}
								}
							}
						}
					}
					.ant-picker {
						right: 5px !important;
					}
				}
			}
		}
	}
}

@media (max-width: 360px) {
	.device-debug {
		.body-section {
			.device-error-section {
				.device-raw-error {
					.ant-table-body {
						width: 250% !important;
					}
				}
			}
		}
	}
}
