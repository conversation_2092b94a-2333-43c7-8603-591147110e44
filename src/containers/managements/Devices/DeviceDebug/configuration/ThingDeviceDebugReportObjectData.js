let reportData = {
	report_format: 'csv',
	report_type: 'save',
	file_name: 'Device_Raw_Log_Data',
	page_orientation: 'p',
	page_layout: 'mm',
	page_size: 'a4',
	heading_width: 88,
	head_section: {
		report_head: 'Device Raw Log Data',
		head_style: {
			font_size: 14,
			font_type: 'italic',
			font_color: { r: 0, g: 0, b: 0 },
		},
	},
	body_section: {
		body_data: [
			{
				type: 'table',
				section_head: '',
				id: 'device-error',
				table_type: 'normal',
				table_head: {
					head_data: [
						{
							text: 'Sl No',
							key: 'sl_no',
						},
						{
							text: 'Date&Time',
							key: 'date',
						},
						{
							text: 'Message By',
							key: 'raw_log_sent_by',
						},
						{
							text: 'Data Packet',
							key: 'message',
						},
					],
					head_style: {
						backgroundColor: '#ddd',
						color: '#232323',
						fontWeight: '600',
					},
				},
				table_data: [],
				section_style: {
					head_color: { r: 0, g: 0, b: 0 },
					head_font_size: 10,
					head_font_type: 'bold',
				},
				table_style: {
					styles: {
						fontSize: 10,
					},
					margin: { top: 15 },
					headStyles: { fillColor: [245, 145, 64], halign: 'center' },
					bodyStyles: { halign: 'center' },
					theme: 'grid',
				},
			},
		],
	},
	footer_data: {
		text: 'Datoms - Phoenix Robotix Pvt. Ltd.',
		footer_text_pos: 30,
		style: {
			font_type: 'italic',
			font_size: 8,
			font_color: { r: 148, g: 148, b: 148 },
		},
	},
	end_credit: {
		text: 'Report generated by DATOMS at ',
		footer_text_pos: 30,
		style: {
			font_type: 'italic',
			font_size: 10,
			font_color: { r: 0, g: 0, b: 0 },
			fill_color: { r: 242, g: 242, b: 240 },
		},
	},
};

export default {
	report_data: reportData,
};
