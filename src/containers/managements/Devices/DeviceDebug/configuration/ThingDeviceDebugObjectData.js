import React from 'react';
import moment from 'moment-timezone';
import AntTag from '@datoms/react-components/src/components/AntTag';
import AntTooltip from '@datoms/react-components/src/components/AntTooltip';
import CloudOutlined from '@ant-design/icons/CloudOutlined';
import TabletOutlined from '@ant-design/icons/TabletOutlined';
import InfoCircleOutlined from '@ant-design/icons/InfoCircleOutlined';
import CloudDownloadOutlined from '@ant-design/icons/CloudDownloadOutlined';
import CheckOutlined from '@ant-design/icons/CheckOutlined';
import ClockCircleOutlined from '@ant-design/icons/ClockCircleOutlined';
import './ThingDeviceDebugObjectData.less';

import Online from '../../assets/debug-icons/Online.svg';
import Offline from '../../assets/debug-icons/Offline.svg';

import GraphObjectData from './GraphObjectData';

let GraphObjectNetworkPieChart = JSON.parse(JSON.stringify(GraphObjectData));

GraphObjectNetworkPieChart.graph_data.config.chart.type = 'pie';
GraphObjectNetworkPieChart.graph_data.config.chart.height = 213;
GraphObjectNetworkPieChart.graph_data.config.legend.enabled = true;
GraphObjectNetworkPieChart.graph_data.config.legend.itemStyle.fontSize = '12px';
GraphObjectNetworkPieChart.graph_data.config.tooltip = {
	pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>',
};
GraphObjectNetworkPieChart.graph_data.config.responsive = {};
//GraphObjectNetworkPieChart.graph_data.config.timezone = '';
GraphObjectNetworkPieChart.graph_data.config.plotOptions = {
	pie: {
		colors: ['#A5A5A5', '#ED7D31', '#4472C4'],
		allowPointSelect: false,
		cursor: '',
		dataLabels: {
			enabled: true,
			format: '{point.name} ({point.percentage:.1f}%)',
			distance: 8,
			color: '#232323',
			style: {
				textShadow: false,
				textOutline: false,
				fontSize: 10,
				fontWeight: 'normal',
			},
		},
		showInLegend: false,
	},
};
GraphObjectNetworkPieChart.graph_data.series_data = [];

let GraphObjectNetworkUsage = JSON.parse(JSON.stringify(GraphObjectData));

GraphObjectNetworkUsage.graph_data.config.chart.type = 'area';
GraphObjectNetworkUsage.graph_data.config.chart.height = 213;
GraphObjectNetworkUsage.graph_data.config.legend.enabled = true;
GraphObjectNetworkUsage.graph_data.config.xAxis = {
	type: 'datetime',
	crosshair: false,
	title: {
		text: 'Date & Time',
		useHTML: true,
		style: {
			color: 'rgb(118, 134, 161)',
			fontSize: '11px',
		},
	},
	labels: {
		style: {
			color: 'rgba(118, 134, 161, 0.8)',
			fontSize: '10px',
		},
	},
};
GraphObjectNetworkUsage.graph_data.config.yAxis = {
	min: 0,
	max: 50,
	tickInterval: 10,
	title: {
		text: 'Strength',
	},
};
GraphObjectNetworkUsage.graph_data.config.legend.itemStyle.fontSize = '12px';
GraphObjectNetworkUsage.graph_data.config.tooltip = {
	formatter: function () {
		let strengthText = '';
		let value = parseInt(this.point.y);
		if (value > 20) {
			strengthText = ' (Excellent)';
		} else if (value >= 13 && value <= 20) {
			strengthText = ' (Good)';
		} else if (value >= 6 && value < 13) {
			strengthText = ' (Poor)';
		} else {
			strengthText = ' (No Signal)';
		}
		let tooltip = moment(this.x).format('dddd, MMM D, YYYY') + '<br/>';
		tooltip =
			tooltip +
			this.series.name +
			' strength <b>' +
			value +
			strengthText +
			'</b>';
		return tooltip;
	},
};
GraphObjectNetworkUsage.graph_data.config.responsive = {};
//GraphObjectNetworkUsage.graph_data.config.timezone = '';
GraphObjectNetworkUsage.graph_data.config.plotOptions = {
	area: {
		pointStart: 0,
		marker: {
			enabled: false,
			symbol: 'circle',
			radius: 2,
			states: {
				hover: {
					enabled: true,
				},
			},
		},
	},
};
GraphObjectNetworkUsage.graph_data.series_data = [];

function debugDisabledDate(current) {
	// Can not select future dates
	return current && current >= moment().endOf('day');
}

let device_error_table_data = {
	config: {
		pagination_data: {
			size: 'small',
			total: 0,
			pageSize: 10,
			showSizeChanger: true,
			showQuickJumper: false,
			hideOnSinglePage: false,
		},
		bordered: false,
		size: 'default',
		showHeader: true,
		scroll: { y: 240 },
		loading: false,
		locale: {
			filterTitle: 'Select Filter',
			filterConfirm: 'Ok',
			filterReset: 'Reset',
			emptyText: 'No Error',
		},
	},
	head_data: [
		{
			title: (
				<span
					className=""
					dangerouslySetInnerHTML={{ __html: 'Timestamp' }}
				/>
			),
			dataIndex: 'timestamp',
			key: 'timestamp',
			width: '25%',
			render: (timestamp) => (
				<span
					className=""
					dangerouslySetInnerHTML={{
						__html: moment
							.unix(timestamp)
							.format('Do MMM YYYY, HH:mm:ss'),
					}}
				/>
			),
		},
		{
			title: (
				<span
					className=""
					dangerouslySetInnerHTML={{ __html: 'Tags' }}
				/>
			),
			dataIndex: 'error_type',
			key: 'error_type',
			align: 'center',
			width: '20%',
			render: (error_type, all_data) => {
				//console.log('ALL-DATA-DE', all_data);
				if (error_type != null && error_type != '') {
					let color = '',
						text = '';
					if (error_type == 2) {
						color = '#A9D9F7';
						text = 'Network Error';
					} else if (error_type == 1) {
						color = '#FBC7AC';
						text = 'Power Error';
					} else if (error_type == 3) {
						color = '#EECD96';
						text = 'Sensor Error';
					} else {
						color = '#A1D7DD';
						text = 'Other Error';
					}
					return (
						<div className="dspl-flex-cntr">
							<AntTag
								className="error-tag"
								color={color}
								key={error_type}
							>
								{text}
							</AntTag>
							<AntTag
								className="error-tag"
								color={color}
								key={error_type}
							>
								{all_data.error_name}
							</AntTag>
						</div>
					);
				} else {
					return '-';
				}
			},
		},
		{
			title: (
				<span
					className=""
					dangerouslySetInnerHTML={{ __html: 'Errors' }}
				/>
			),
			key: 'error_message',
			dataIndex: 'error_message',
			width: '55%',
			render: (error_message, row_value) => {
				if (error_message != null && error_message != '') {
					let text = '';
					if (row_value.error_type == 2) {
						text = 'Network Error';
					} else if (row_value.error_type == 1) {
						text = 'Power Error';
					} else if (row_value.error_type == 3) {
						text = 'Sensor Error';
					} else {
						text = 'Other Error';
					}
					return text + ' : ' + error_message;
				} else {
					return '-';
				}
			},
		},
	],
	row_data: undefined,
};

let sensor_error_table_data = {
	config: {
		pagination_data: {
			size: 'small',
			total: 0,
			pageSize: 10,
			showSizeChanger: true,
			showQuickJumper: true,
			hideOnSinglePage: false,
		},
		bordered: false,
		size: 'default',
		showHeader: true,
		scroll: { y: 240 },
		loading: false,
		locale: {
			filterTitle: 'Select Filter',
			filterConfirm: 'Ok',
			filterReset: 'Reset',
			emptyText: 'No Error',
		},
	},
	head_data: [
		{
			title: (
				<span
					className=""
					dangerouslySetInnerHTML={{ __html: 'Timestamp' }}
				/>
			),
			dataIndex: 'timestamp',
			key: 'timestamp',
			width: '25%',
			render: (timestamp) => (
				<span
					className=""
					dangerouslySetInnerHTML={{
						__html: moment
							.unix(timestamp)
							.format('Do MMM YYYY, HH:mm:ss'),
					}}
				/>
			),
		},
		{
			title: (
				<span
					className=""
					dangerouslySetInnerHTML={{ __html: 'Tags' }}
				/>
			),
			dataIndex: 'error_type',
			key: 'error_type',
			align: 'center',
			width: '20%',
			render: (error_type) => {
				if (error_type != null && error_type != '') {
					let color = '',
						text = '';
					if (error_type === 'zero_error') {
						color = '#FAE9D5';
						text = 'Zero Error';
					} else if (error_type === 'sensor_failure') {
						color = '#EDD5FA';
						text = 'Sensor Failure';
					} else if (error_type === 'standard_deviation') {
						color = '#D5F1FA';
						text = 'Standard Deviation';
					}
					return (
						<AntTag
							className="error-tag"
							color={color}
							key={error_type}
						>
							{text}
						</AntTag>
					);
				} else {
					return '-';
				}
			},
		},
		{
			title: (
				<span
					className=""
					dangerouslySetInnerHTML={{ __html: 'Errors' }}
				/>
			),
			key: 'message',
			dataIndex: 'message',
			width: '55%',
			render: (error_message, row_value) => {
				if (error_message != null && error_message != '') {
					let text = '';
					if (row_value.error_type === 'zero_error') {
						text = 'Zero Error';
					} else if (row_value.error_type === 'sensor_failure') {
						text = 'Sensor Failure';
					} else if (row_value.error_type === 'standard_deviation') {
						text = 'Standard Deviation';
					}
					return text + ' : ' + error_message;
				} else {
					return '-';
				}
			},
		},
	],
	row_data: undefined,
};

let device_error_raw_table_data = {
	config: {
		pagination_data: {
			size: 'small',
			total: 0,
			pageSize: 100,
			showSizeChanger: true,
			showQuickJumper: false,
			hideOnSinglePage: false,
		},
		bordered: false,
		size: 'default',
		showHeader: true,
		scroll: { y: 240 },
		loading: false,
		locale: {
			filterTitle: 'Select Filter',
			filterConfirm: 'Ok',
			filterReset: 'Reset',
			emptyText: 'No Error',
		},
	},
	head_data: [
		{
			title: (
				<span
					className=""
					dangerouslySetInnerHTML={{ __html: 'Sl No' }}
				/>
			),
			dataIndex: 'sl_no',
			key: 'sl_no',
			align: 'center',
			width: '10%',
			render: (text) => (
				<span className="" dangerouslySetInnerHTML={{ __html: text }} />
			),
		},
		{
			title: (
				<span
					className=""
					dangerouslySetInnerHTML={{ __html: 'Date&Time' }}
				/>
			),
			dataIndex: 'date',
			key: 'date',
			width: '20%',
			render: (date) => (
				<span className="" dangerouslySetInnerHTML={{ __html: date }} />
			),
		},
		{
			title: (
				<span
					className=""
					dangerouslySetInnerHTML={{ __html: 'Message By' }}
				/>
			),
			dataIndex: 'raw_log_sent_by',
			key: 'raw_log_sent_by',
			align: 'center',
			width: '15%',
			render: (raw_log_sent_by) => {
				if (raw_log_sent_by === 'server') {
					return (
						<AntTooltip placement="left" title="Server">
							<CloudOutlined
								className="sent-by-icon"
								type="cloud"
							/>
						</AntTooltip>
					);
				} else if (raw_log_sent_by === 'device') {
					return (
						<AntTooltip placement="left" title="Device">
							<TabletOutlined
								className="sent-by-icon"
								type="tablet"
							/>
						</AntTooltip>
					);
				} else if (raw_log_sent_by === 'info') {
					return (
						<AntTooltip placement="left" title="Info">
							<InfoCircleOutlined
								className="sent-by-icon"
								type="info-circle"
							/>
						</AntTooltip>
					);
				} else {
					return '-';
				}
			},
		},
		{
			title: (
				<span
					className=""
					dangerouslySetInnerHTML={{ __html: 'Data Packet' }}
				/>
			),
			key: 'message',
			dataIndex: 'message',
			width: '55%',
			render: (message, row_value) => {
				if (message != null && message != '') {
					return message;
				} else {
					return '-';
				}
			},
		},
	],
	row_data: undefined,
};

let historical_data_table_data = {
	config: {
		pagination_data: {
			size: 'small',
			total: 0,
			pageSize: 10,
			showSizeChanger: true,
			showQuickJumper: true,
			hideOnSinglePage: false,
		},
		bordered: false,
		size: 'default',
		showHeader: true,
		scroll: { y: 240 },
		loading: false,
		locale: {
			filterTitle: 'Select Filter',
			filterConfirm: 'Ok',
			filterReset: 'Reset',
			emptyText: 'No Error',
		},
	},
	head_data: [
		{
			title: (
				<b>
					<span
						className="mar-right-25"
						dangerouslySetInnerHTML={{ __html: 'Date' }}
					/>
				</b>
			),
			dataIndex: 'date',
			key: 'date',
			align: 'center',
			width: '20%',
			render: (date) => (
				<span className="" dangerouslySetInnerHTML={{ __html: date }} />
			),
		},
		{
			title: (
				<b>
					<span
						className=""
						dangerouslySetInnerHTML={{ __html: 'File Name' }}
					/>
				</b>
			),
			dataIndex: 'name',
			key: 'name',
			width: '40%',
			render: (text) => (
				<span className="" dangerouslySetInnerHTML={{ __html: text }} />
			),
		},
		{
			title: (
				<b>
					<span
						className=""
						dangerouslySetInnerHTML={{ __html: 'Size' }}
					/>
				</b>
			),
			dataIndex: 'size',
			key: 'size',
			width: '20%',
			render: (text) => (
				<span className="" dangerouslySetInnerHTML={{ __html: text }} />
			),
		},
		{
			title: (
				<b>
					<span
						className=""
						dangerouslySetInnerHTML={{ __html: 'Status' }}
					/>
				</b>
			),
			dataIndex: 'status',
			key: 'status',
			align: 'center',
			width: '10%',
			render: (status) => {
				if (status === 'Processed') {
					return (
						<div className="status-block processed">
							<CheckOutlined />
							{status}
						</div>
					);
				} else if (status === 'Pending') {
					return (
						<div className="status-block pending">
							<ClockCircleOutlined />
							{status}
						</div>
					);
				}
			},
		},
		{
			title: (
				<b>
					<span
						className=""
						dangerouslySetInnerHTML={{ __html: 'Action' }}
					/>
				</b>
			),
			key: 'action',
			align: 'center',
			width: '10%',
			render: (location) => {
				return (
					<AntTooltip placement="top" title="Download Text File">
						<a href={location} target="_blank" rel="noreferrer">
							<CloudDownloadOutlined
								className="download-icon"
								type="cloud"
							/>
						</a>
					</AntTooltip>
				);
			},
		},
	],
	row_data: undefined,
};

let device_offline_table_data = {
	config: {
		pagination_data: {
			size: 'small',
			total: 0,
			pageSize: 10,
			showSizeChanger: true,
			showQuickJumper: true,
			hideOnSinglePage: false,
		},
		bordered: false,
		size: 'default',
		showHeader: true,
		scroll: { y: 240 },
		loading: false,
		locale: {
			filterTitle: 'Select Filter',
			filterConfirm: 'Ok',
			filterReset: 'Reset',
			emptyText: 'No Error',
		},
	},
	head_data: [
		{
			title: (
				<span
					className="off-color"
					dangerouslySetInnerHTML={{ __html: 'Sl No' }}
				/>
			),
			dataIndex: 'sl_no',
			key: 'sl_no',
			align: 'center',
			width: '10%',
			render: (text) => (
				<span
					className="off-color"
					dangerouslySetInnerHTML={{ __html: text }}
				/>
			),
		},
		{
			title: (
				<div>
					<span>
						<img src={Offline} width={23} height={19} />
					</span>
					<span
						className="off-color mar-left-12"
						dangerouslySetInnerHTML={{
							__html: 'Offline Date Time',
						}}
					/>
				</div>
			),
			dataIndex: 'offline_date',
			key: 'offline_date',
			width: '30%',
			render: (date) => (
				<span
					className="off-color mar-left-12"
					dangerouslySetInnerHTML={{ __html: date }}
				/>
			), //
		},
		{
			title: (
				<div>
					<span>
						<img src={Online} width={23} height={19} />
					</span>
					<span
						className="on-color mar-left-12"
						dangerouslySetInnerHTML={{ __html: 'Online Date Time' }}
					/>
				</div>
			),
			dataIndex: 'online_date',
			key: 'online_date',
			width: '30%',
			render: (date) => (
				<span
					className="on-color mar-left-12"
					dangerouslySetInnerHTML={{ __html: date }}
				/>
			),
		},
		{
			title: (
				<span
					className="off-color"
					dangerouslySetInnerHTML={{ __html: 'Offline Duration' }}
				/>
			),
			key: 'duration',
			dataIndex: 'duration',
			width: '30%',
			render: (text) => (
				<span
					className="off-color mar-left-12"
					dangerouslySetInnerHTML={{ __html: text }}
				/>
			),
		},
	],
	row_data: undefined,
};

let data_colors = [
	{
		value: 1,
		color: '#781D0F',
		text: '<60%',
	},
	{
		value: 60,
		color: '#EE492F',
		text: '90-60%',
	},
	{
		value: 91,
		color: '#FC941D',
		text: '>90%',
	},
	{
		value: 100,
		color: '#50D99E',
		text: '100%',
	},
];

export default {
	rangePicker: {
		config: {
			allowClear: false,
			placeholder: ['From', 'To'],
			size: 'default',
			disabledDate: debugDisabledDate,
			ranges: {
				'Last 15 Days': [
					moment().subtract(15, 'days').startOf('day'),
					moment().endOf('day'),
				],
				'Last 1 Month': [
					moment().subtract(1, 'M').startOf('day'),
					moment().endOf('day'),
				],
				'Last 3 Months': [
					moment().subtract(3, 'M').startOf('day'),
					moment().endOf('day'),
				],
			},
			showTime: true,
			separator: ' To ',
			format: 'DD-MMM-YYYY',
		},
	},
	device_select_option: [
		{
			show_arrow: true,
			default_value: '',
			options: [],
		},
	],
	network_pie_graph: {
		ref: 'network_pie_chart',
		graph_data_object: GraphObjectNetworkPieChart.graph_data,
	},
	network_usage_graph: {
		ref: 'network_usage_chart',
		graph_data_object: GraphObjectNetworkUsage.graph_data,
	},
	device_error_table: device_error_table_data,
	device_error_raw_table: device_error_raw_table_data,
	device_offline_table: device_offline_table_data,
	historical_data_table: historical_data_table_data,
	sensor_error_table: sensor_error_table_data,
	daily_data_available: {
		colors: data_colors,
		monthSpace: 40,
		displayLegend: false,
		displayCustomLegend: false,
		textColor: '#000',
		fadeAnimation: { animate: true, duration: 0.5 },
		defaultColor: '#781D0F',
		rectWidth: 13,
		marginBottom: 5,
		marginRight: 5,
		shouldStartMonday: false,
		textDefaultColor: '0',
		classnames: 'daily-data-available',
		showMonth: 3,
	},
	hourly_data_available: {
		colors: data_colors,
	},
	device_filter: [
		{
			key: 'total_error',
			name: 'All Errors',
			value: 0,
		},
		{
			key: 'network_error',
			name: 'Network',
			value: 0,
		},
		{
			key: 'power_error',
			name: 'Power',
			value: 0,
		},
		{
			key: 'sensor_error',
			name: 'Sensor',
			value: 0,
		},
	],
	sensor_filter: [
		{
			key: 'total_error',
			name: 'All Errors',
			value: 0,
		},
		{
			key: 'zero_error',
			name: 'Zero',
			value: 0,
		},
		{
			key: 'standard_deviation',
			name: 'Standard Deviation',
			value: 0,
		},
		{
			key: 'sensor_failure',
			name: 'Sensor Failure',
			value: 0,
		},
	],
	rawRangePicker: {
		config: {
			allowClear: false,
			placeholder: ['From', 'To'],
			size: 'default',
			disabledDate: debugDisabledDate,
			ranges: {
				Today: [moment().startOf('day'), moment().endOf('day')],
			},
			showTime: true,
			separator: ' To ',
			format: 'DD-MMM-YYYY, HH:mm',
		},
	},
};
