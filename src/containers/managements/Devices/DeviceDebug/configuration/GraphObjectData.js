import moment from 'moment-timezone';

let graph_data = {
	config: {
		chart: {
			type: 'area',
			zoomType: 'x',
			height: 150,
		},
		// Optional parameter
		title: {
			useHTML: true,
			text: '',
		},
		// Optional parameter
		subtitle: {
			text: '',
			useHTML: true,
		},
		// Optional parameter
		xAxis: {
			type: 'datetime',
			crosshair: false,
			title: {
				text: '',
				useHTML: true,
				style: {
					color: 'rgb(118, 134, 161)',
					fontSize: '11px',
				},
			},
			labels: {
				style: {
					color: 'rgba(118, 134, 161, 0.8)',
					fontSize: '10px',
				},
			},
		},
		// Optional parameter
		yAxis: {
			type: '',
			title: {
				text: '',
			},
			offset: -10,
			labels: {
				style: {
					color: 'rgba(118, 134, 161, 0.8)',
					fontSize: '10px',
				},
			},
		},
		// Optional parameter
		legend: {
			enabled: false,
			itemStyle: {
				fontSize: '10px',
			},
		},
		// Optional parameter
		plotOptions: {
			// for area
			area: {
				marker: {
					enabled: false,
					symbol: 'circle',
					radius: 2,
					states: {
						hover: {
							enabled: true,
						},
					},
				},
			},
			arearange: {
				marker: {
					enabled: false,
					symbol: 'circle',
					radius: 1,
					states: {
						hover: {
							enabled: true,
						},
					},
				},
			},
			line: {
				marker: {
					enabled: true,
					symbol: 'circle',
					radius: 1,
					states: {
						hover: {
							enabled: true,
						},
					},
				},
			},
			spline: {
				marker: {
					enabled: true,
					symbol: 'circle',
					radius: 2,
					states: {
						hover: {
							enabled: true,
						},
					},
				},
			},
			// For column Graph
			column: {
				pointPadding: 0.2,
				borderWidth: 0,
			},
			series: {
				fillOpacity: 0.3,
				animation: false,
			},
		},
		// Optional parameter
		tooltip: {
			headerFormat:
				'<span style="font-size:10px">{point.key}</span><table>',
			pointFormat:
				'<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
				'<td style="padding:0"><b>&nbsp;{point.y} ',
			footerFormat: '</table>',
			valueDecimals: 20,
			shared: true,
			useHTML: true,
		},
		// Optional parameter
		exporting: {
			enabled: false,
		},
		// Optional parameter
		credits: {
			enabled: false,
		},
		// Optional parameter
		responsive: {
			rules: [
				{
					condition: {
						maxWidth: 600,
					},
					chartOptions: {
						legend: {
							layout: 'horizontal',
							align: 'center',
							verticalAlign: 'bottom',
						},
					},
				},
			],
		},
		timezone: moment.tz.guess(),
	},
	// Mendatory parameter
	series_data: [
		{
			name: 'abc',
			data: [
				[1576302345000, null],
				[1576302445000, null],
				[1576302545000, null],
				[1576302645000, 10],
				[1576302745000, 10],
				[1576302845000, 10],
				[1576302945000, null],
			],
			color: '#fff000',
		},
		{
			name: 'abc-1',
			data: [
				[1576302345000, 1],
				[1576302445000, 2],
				[1576302545000, 3],
				[1576302645000, 10],
				[1576302745000, 10],
				[1576302845000, 10],
				[1576302945000, 10],
			],
			color: '#ddd',
		},
		{
			name: 'abc-2',
			data: [
				[1576302345000, 1],
				[1576302445000, null],
				[1576302545000, 2],
				[1576302645000, 10],
				[1576302745000, 10],
				[1576302845000, 10],
				[1576302945000, 4],
			],
			color: '#232323',
		},
	],
};

export default {
	graph_data: graph_data,
};
