import React from 'react';
import AntDrawer from '@datoms/react-components/src/components/AntDrawer';
import AntNotification from '@datoms/react-components/src/components/AntNotification';
import CustomTable from '../imports/CustomTable';
import moment from 'moment-timezone';
import DeviceDebugObjectData from '../configuration/ThingDeviceDebugObjectData';
import { deviceDebugDataFiles } from '@datoms/js-sdk';

// async function getDataFiles(
// 	clientId,
// 	deviceId,
// 	from_date,
// 	upto_date,
// 	page,
// 	pageSize
// ) {
// 	try {
// 		let configData = {
// 			url:
// 				'/diagnostics/vendors/' +
// 				clientId +
// 				'/devices/' +
// 				deviceId +
// 				'/datafiles?page_size=' +
// 				pageSize +
// 				'&page_number=' +
// 				page +
// 				'&from_time=' +
// 				from_date +
// 				'&upto_time=' +
// 				upto_date,
// 			method: 'GET',
// 		};
// 		return await callFetch(configData);
// 	} catch (err) {
// 		throw err;
// 	}
// }

export default class DataFileDrawer extends React.Component {
	constructor(props) {
		super(props);
		this.state = {
			historical_data_page_no: 1,
			historical_data_page_size:
				DeviceDebugObjectData.historical_data_table.config
					.pagination_data.pageSize,
			// historical_data_logs: [
			// 	{
			// 		date: '1-11-2020',
			// 		name: '1234.txt',
			// 		size: '45000kb',
			// 		status: 'Processed',
			// 	},
			// 	{
			// 		date: '5-11-2020',
			// 		name: '5678.txt',
			// 		size: '45000kb',
			// 		status: 'Pending',
			// 	},
			// ],
		};

		this.fetchDataFiles = this.fetchDataFiles.bind(this);
		this.historicalDataPagination = this.historicalDataPagination.bind(
			this
		);
		this.closeModal = this.closeModal.bind(this);
	}

	componentDidMount() {
		this.fetchDataFiles();
	}

	/**
	 * Shows ant notification.
	 * @param {string} type - type of notification.
	 * @param {string} msg - message to be shown.
	 */
	openNotification(type, msg) {
		AntNotification({
			type: type,
			message: msg,
			// description: 'This is success notification',
			placement: 'bottomLeft',
			className: 'alert-' + type,
		});
	}

	async fetchDataFiles(
		page = 1,
		pageSize = DeviceDebugObjectData.historical_data_table.config
			.pagination_data.pageSize
	) {
		let response = await deviceDebugDataFiles({
			client_id: this.props.clientId,
			device_id: this.props.selected_device,
			from_date: this.props.from_date,
			upto_date: this.props.upto_date,
			page: page,
			page_size: pageSize,
		});

		if (response.status === 403) {
			this.setState({
				unauthorised_access: true,
				historical_data_logs: [],
				unauthorised_access_msg: response.message,
			});
		}
		if (response.status === 'success') {
			let historical_logs = [];
			let historical_data = response.data_file_details;

			historical_data.map((data) => {
				let time_format =
					data.timestamp && Number(data.timestamp)
						? moment(data.offline_time * 1000).format('DD-MM-YYYY')
						: '';
				historical_logs.push({
					date: time_format !== '' ? time_format : '-',
					name: data.name,
					size: data.size,
					status: data.status,
					location: data.location,
				});
			});

			DeviceDebugObjectData.historical_data_table.config.pagination_data.total =
				response.total_files;

			this.setState({
				historical_data_logs: historical_logs,
			});
		} else {
			this.openNotification('error', response.message);
			this.setState({
				unauthorised_access: false,
				error_API: true,
				error_API_msg: response.message,
				historical_data_logs: [],
			});
		}
	}

	historicalDataPagination(page, pageSize) {
		this.setState(
			{
				historical_data_logs: undefined,
				historical_data_page_no: page,
				historical_data_page_size: pageSize,
			},
			() => {
				this.fetchDataFiles(page, pageSize);
			}
		);
	}

	closeModal() {
		this.props.closeDrawer();
	}

	render() {
		return (
			<AntDrawer
				title={
					'Historical Data File Received - ' +
					(this.state.historical_data_logs
						? this.state.historical_data_logs.length
						: 0)
				}
				className="historical-data-drawer"
				visible={true}
				mask={true}
				onClose={() => this.closeModal()}
				destroyOnClose={false}
				maskClosable={false}
				width={820}
			>
				<div className="head-text">
					<b>Historical Files</b>
					<span className="color-text"> From </span>
					<span className="color-date">
						{' '}
						{moment(this.props.from_date * 1000).format(
							'DD MMM YYYY'
						)}
					</span>
					<span className="color-text"> To </span>
					<span className="color-date">
						{' '}
						{moment(this.props.upto_date * 1000).format(
							'DD MMM YYYY'
						)}
					</span>
				</div>

				<CustomTable
					config_data={
						DeviceDebugObjectData.historical_data_table.config
					}
					columns={
						DeviceDebugObjectData.historical_data_table.head_data
					}
					dataSource={this.state.historical_data_logs}
					loading={!this.state.historical_data_logs ? true : false}
					onPaginationChange={() => this.historicalDataPagination()}
					current={this.state.historical_data_page_no}
				/>
			</AntDrawer>
		);
	}
}
