import React from 'react';
import AntTable from '@datoms/react-components/src/components/AntTable';
import './custom-table.less';

function shouldComponentUpdate(nextProps) {
	console.log('nextProps.dataSource ', nextProps.dataSource);
	if (nextProps.dataSource) {
		return true;
	}
}

function CustomTableView(props) {
	let rowSelection = {
		selectedRowKeys: props.selectedRowKeys,
		onChange: props.onChangeRowSelect,
	};
	console.log('props.dataSource view ', props.dataSource);
	return (
		<div>
			<div
				className={
					'antD-table-class ' +
					(props.dataSource && props.dataSource.length
						? ''
						: 'mar-top-40')
				}
			>
				<AntTable
					loading={props.loading ? true : false}
					pagination={
						props.pagination ? props.pagination_config : false
					}
					rowSelection={props.rowSelect ? rowSelection : undefined}
					columns={props.columns}
					dataSource={props.hasData ? props.dataSource : null}
				/>
			</div>
		</div>
	);
}

export default CustomTableView;
