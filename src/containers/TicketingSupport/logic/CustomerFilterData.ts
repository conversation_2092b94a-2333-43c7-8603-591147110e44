import {
  retriveCustomerList,
  retriveThingsList,
  retriveVendorThingsList,
  retriveUsers,
  getSiteTypes,
  retrieveSitesList,
  retriveCustomerDetails
} from "@datoms/js-sdk";
import {
  CSMListProps,
  CustomerFilterDataProps,
  CustomerListProps,
  ThingDataProps,
  SiteCategoryDataProps,
  SiteDataProps,
  UserProps,
} from "../types";

export const fetchCustomersList = async (props: CustomerFilterDataProps) => {
  const { application_id, client_id } = props;
  let customersList: Array<{
    value: number | string;
    label: string;
    title: string;
    application_id: number;
  }> = [];
  let customerIDName: {[key:string]: string} = {};
  if (application_id !== 16) {
    let response = await retriveCustomerList(client_id);
    response?.customers.map(
      (customer: {
        vendor_id: number;
        name: string;
        is_vendor: boolean;
        id: number;
        applications: number[];
      }) => {
        if (props.client_id !== 1 || customer.vendor_id === 1) {
          customersList.push({
            title: customer.name + (customer.is_vendor ? " *" : ""),
            label: `${customer.name}${customer.is_vendor ? " *" : ""} (${customer.id})`,
            value: customer.id,
            application_id: customer?.applications?.[0],
          });
        }
        customerIDName[customer.id] = customer.name
      },
    );
  }
  return {customerData:customersList, customerIDName};
};

export const fetchEndCustDetails = async (
  clientIDs: (number | string)[],
  customerOptions: CustomerListProps[]
) => {
  // Filter customers based on clientIDs
  const fetchCustomerDetails = customerOptions.filter((customer) =>
    clientIDs.includes(customer.value)
  );

  // Array to hold the required details
  let customerAccessData: { customer_id: number | string; customer_name: string; feature_list: any[] }[] = [];

  // Fetch details for each customer in parallel
  await Promise.all(
    fetchCustomerDetails.map(async (customer: CustomerListProps) => {
      if (customer.value !== "All Customers") {
        const totalData = await retriveCustomerDetails(customer.value, "?customer_type=new");
        if (totalData && Array.isArray(totalData.application_details)) {
          // Filter for application_id === 16
          const filteredApps = totalData.application_details.filter(
            (app: any) => app.application_id === 16
          );
          // Combine all feature_list arrays from filtered applications
          const featureList = filteredApps
            .map((app: any) => app.features_list || [])
            .flat();

          customerAccessData.push({
            customer_id: customer.value,
            customer_name: totalData.customer_name,
            feature_list: featureList,
          });
        }
      }
    })
  );

  return customerAccessData;
};

export const fetchEndCustSiteCategoryList = async (clientID: number) => {
  let totalData = await getSiteTypes(clientID);
  const categoryOptions = totalData?.data.map((category : SiteCategoryDataProps) => {
    return {
      value: category?.id,
      label: category?.name,
      title: category?.name,
    };
  });
  
  return categoryOptions;
}

export const fetchSiteCategoryList = async (
  clientIDs: (number | string)[],
  customerOptions: CustomerListProps[],
  customerAccessData: any[]
) => {
  // Filter customers based on clientIDs
  const fetchSiteCategoryData = customerOptions.filter((customer) =>
    clientIDs.includes(customer.value)
  );

  let siteCategoryData: SiteCategoryDataProps[] = [];

  await Promise.all(
    fetchSiteCategoryData.map(async (customer: CustomerListProps) => {
      // Find feature_list for this customer
      const access = customerAccessData.find(
        (c) => c.customer_id === customer.value
      );
      const featureList = access?.feature_list || [];

      // Only call getSiteTypes if feature_list includes the feature
      if (
        customer.value !== "All Customers" &&
        featureList.includes("SiteManagement:SiteManagement")
      ) {
        const totalData = await getSiteTypes(customer.value);
        siteCategoryData = [...siteCategoryData, ...(totalData?.data || [])];
      }
    })
  );

  // Ensure unique categories by id using a Map
  const uniqueCategoryMap = new Map();
  siteCategoryData.forEach((category) => {
    if (category?.id !== undefined && !uniqueCategoryMap.has(category.id)) {
      uniqueCategoryMap.set(category.id, category);
    }
  });

  // Format the return object as required
  const siteCategoryOptions = Array.from(uniqueCategoryMap.values()).map(
    (category) => ({
      value: category?.id,
      label: category?.name,
      title: category?.name,
    })
  );

  return siteCategoryOptions;
};

export const fetchEndCustSiteList = async (clientID: number, query: string) => {
  let totalData = await retrieveSitesList( clientID, query );
  const siteOptions = totalData?.data.map((site : SiteDataProps) => {
    return {
      value: site?.id,
      label: site?.name,
      title: site?.name,
      operational_status: site?.operational_status, 
    };
  });
  return siteOptions;
}

export const fetchSiteList = async (
  clientIDs: (number | string)[],
  customerOptions: CustomerListProps[],
  query: string,
  customerAccessData: any[]
) => {
  // Filter customers based on clientIDs
  const fetchSiteData = customerOptions.filter((customer) =>
    clientIDs.includes(customer.value)
  );

  let siteData: SiteDataProps[] = [];

  await Promise.all(
    fetchSiteData.map(async (customer: CustomerListProps) => {
      // Find feature_list for this customer
      const access = customerAccessData.find(
        (c) => c.customer_id === customer.value
      );
      const featureList = access?.feature_list || [];

      // Only call retrieveSitesList if feature_list includes the feature
      if (
        customer.value !== "All Customers" &&
        featureList.includes("SiteManagement:SiteManagement")
      ) {
        const totalData = await retrieveSitesList(customer.value, query);
        siteData = [...siteData, ...(totalData?.data || [])];
      }
    })
  );

  // Ensure unique sites by id using a Map
  const uniqueSiteMap = new Map();
  siteData.forEach((site) => {
    if (site?.id !== undefined && !uniqueSiteMap.has(site.id)) {
      uniqueSiteMap.set(site.id, site);
    }
  });

  // Format the return object as required
  const siteOptions = Array.from(uniqueSiteMap.values()).map((site) => ({
    value: site?.id,
    label: site?.name,
    title: site?.name,
    operational_status: site?.operational_status,
  }));

  return siteOptions;
};

export const fetchVendorThingList = async (clientID: number) => {
  let totalData = await retriveVendorThingsList({ vendor_id: clientID, application_id: 17 }, '?lite=true');
  const thingOptions = totalData?.things.map((thing : ThingDataProps) => {
    return {
      value: thing?.id,
      label: thing?.name,
      title: thing?.name,
    };
  });
  return thingOptions;
}

export const fetchEndCustThingList = async (clientID: number) => {
  let totalData = await retriveThingsList({ client_id: clientID, application_id: 16 }, '?lite=true');
  const thingOptions = totalData?.things.map((thing : ThingDataProps) => {
    return {
      value: thing?.id,
      label: thing?.name,
      title: thing?.name,
    };
  });
  return thingOptions;
}

export const fetchThingList = async (
  clientIDs: (number | string)[],
  customerOptions: CustomerListProps[],
) => {
  const fetchThingData = customerOptions.filter((customer) => {
    return clientIDs.includes(customer.value);
  });

  let thingData: ThingDataProps[] = [];
  await Promise.all(
    fetchThingData.map(async (customer: CustomerListProps) => {
      let totalData;
      if (customer.value !== "All Customers") {
        if ([12, 17].includes(customer.application_id)) {
          totalData = await retriveVendorThingsList({
            vendor_id: customer.value,
            application_id: customer.application_id,
          }, '?lite=true');
        } else {
          totalData = await retriveThingsList({
            client_id: customer.value,
            application_id: customer.application_id,
          }, '?lite=true');
        }
        thingData = [...thingData, ...totalData?.things];
      }
    }),
  );

  const thingOptions = thingData?.map((thing) => {
    return {
      value: thing?.id,
      label: thing?.name,
      title: thing?.name,
    };
  });
  return thingOptions;
};

export const fetchCSMList = async () => {
  let UserList = await retriveUsers(1, 12);
  const UserListData = UserList.user_details;
  let CSMList: CSMListProps[] = [];
  UserListData.map((user: UserProps) => {
    if (user.role_details[0].role_id === 397) {
      CSMList.push({
        value: user.contact_id,
        label:
          user.first_name + " " + (user.last_name.length ? user.last_name : ""),
        title:
          user.first_name + " " + (user.last_name.length ? user.last_name : ""),
      });
    }
  });
  return CSMList;
};

export const CustomerNameFromID = (
  customerIDs: string,
  allCustomerIDName: {[key:string]: string},
) => {
  if (!customerIDs) return "";
  if (customerIDs === "All Customers") return "All Customers";
  const customerIDArray = customerIDs?.split(",");
  let customerName: string[] = [];
  customerIDArray?.map((customerID: string) => {
    customerName.push(allCustomerIDName[parseInt(customerID)]);
  });
  return customerName.join(", ");
};

export const CSMNameFromID = (csmID: string, CSMList: CSMListProps[]) => {
  let customerName = "";
  for (const customer of CSMList) {
    if (customer.value === parseInt(csmID)) {
      customerName = customer.label;
      return customerName;
    }
  }

  return customerName;
};
