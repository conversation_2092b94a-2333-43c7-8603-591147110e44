.ticket-creation {
  padding: 24px;
  margin-bottom: 80px;

  .input-label {
    width: 35%;
    display: flex;
    justify-content: flex-start;
  }

  .input-field {
    margin-bottom: 16px;

    .input-container {
      display: flex;
      align-items: baseline;
      width: 100%;

      .input-field {
        margin-left: 16px;
        width: 65%;
      }
    }

    .react-multi-email {
      margin: 0px;
      padding: 2px 8px;
    }

    .react-multi-email:focus-within {
      border: 1px solid #ff850029;
      box-shadow: 0 0 2px #ff850029;
    }

    .react-multi-email>span[data-placeholder] {
      font-size: 14px;
      top: 0em;
    }
  }

  .help-text {
    width: 100%;
    display: flex;
    align-content: flex-end;
    font-size: 12px;
    margin-top: 16px;
  }

  .upload-container {
    .input-label {
      width: 35%;
    }

    .ant-upload {
      width: 100%;
    }

    .input-field {
      margin-left: 16px;
      width: 65%;
    }

    .uploader {
      display: flex;
      flex-direction: column;
      max-width: 100%;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      margin: 0;

      .upload-back {
        width: 100%;
        height: 96px;
        margin: auto;
        background: #f6f6f6;
        border: 1px dotted #c4c2c2;
        border-radius: 13px;
        display: grid;
        place-content: center;
        text-align: center;

        .anticon {
          justify-content: center;
        }

        .text {
          margin-top: 8px;
          width: 125px;
          font-size: 10px;
        }
      }
    }
  }
}

.create-ticket-drawer {
  .button-container {
    display: flex;
    padding: 0;
    justify-content: flex-end;
    position: fixed;
    bottom: 0;
    background: #f6f6f6;
    padding: 12px 24px;
    margin-top: 16px;
  
    .button {
      margin: 0 8px;
      z-index: 1;
      width: 120px;
  
      .loader-white {
        :where(.css-dev-only-do-not-override-ujbh7r).ant-spin-sm .ant-spin-dot {
          color: white;
        }
      }
    }
  }
  
  .mobile-button-container {
    display: flex;
    padding: 0;
    justify-content: center;
    position: fixed;
    bottom: 0;
    background: #f6f6f6;
    padding: 16px 16px;
    margin-top: 16px;
  
    .button {
      z-index: 1;
      width: 100%;
    }
  }
}