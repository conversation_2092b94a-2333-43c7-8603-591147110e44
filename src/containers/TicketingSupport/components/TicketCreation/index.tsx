import React, { useEffect, useRef, useState } from "react";
import { Form, Input, message, Upload, Spin, Select, Space } from "antd";
import AntButton from "@datoms/react-components/src/components/AntButton";
import FileOutlined from "@ant-design/icons/FileOutlined";
import UploadOutlined from "@ant-design/icons/UploadOutlined";
import { ReactMultiEmail } from "react-multi-email";
import { createTicket } from "@datoms/js-sdk";
import AntNotification from "@datoms/react-components/src/components/AntNotification";
import AntDrawer from "@datoms/react-components/src/components/AntDrawer";
import { TicketCreationProps } from "../../types";
import { LoadingOutlined } from "@ant-design/icons";
import {
  fetchEndCustThingList,
  fetchThingList,
  fetchSiteList,
  fetchSiteCategoryList,
  fetchEndCustSiteList,
  fetchEndCustSiteCategoryList,
  fetchEndCustDetails,
  fetchVendorThingList
} from "../../logic/CustomerFilterData";

import "react-multi-email/dist/style.css";
import "./styles.less";

const TicketCreation: React.FC<TicketCreationProps> = (props) => {
  const {
    isDrawerOpen,
    handleCancel,
    handleOk,
    clientID,
    isMobileScreen,
    setShowTicketCreatedPrompt,
    application_id,
    customerOptions,
    enabledFeatures,
  } = props;

  let customerOptionList = customerOptions;
  const lastUpdateTime = useRef<Record<string, number>>({});
  const [form] = Form.useForm();
  const [ccEmails, setCCEmails] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSiteLoading, setIsSiteLoading] = useState<boolean>(false);
  const [isSiteCategoryLoading, setIsSiteCategoryLoading] = useState<boolean>(false);
  const [attachmentSize, setAttachmentSize] = useState<number>(0);
  const [emailError, setEmailError] = useState<string | null>(null);
  const [selectedCustomers, setSelectedCustomers] = useState<any[]>([]);
  const [selectedSiteCategory, setSelectedSiteCategory] = useState<any[]>([]);
  const [thingOptions, setThingOptions] = useState<any[]>([]);
  const [siteAccess, setSiteAccess] = useState<boolean>(false);
  const [siteData, setSiteOptions] = useState<any[]>([]);
  const [siteListData, setSiteListOptions] = useState<any[]>([]);
  const [siteCategoryOptions, setSiteCategoryOptions] = useState<any[]>([]);
  const [customerList, setCustomerList] = useState<any[]>([]);
  const [customerAccessData, setCustomerAccessData] = useState<any[]>([]);

  const handleOKClick = async () => {
    const values = form.getFieldsValue();
    const ticketData = new FormData();

    setIsLoading(true);
    setIsSiteLoading(true);
    setIsSiteCategoryLoading(true);

    if(!siteAccess) {
      values.site = [];
      form.setFieldsValue({ site: values.site });
    }

    const customerArr = selectedCustomers.filter((customer) => {
      return customer.value !== "All Customers";
    });

    if (selectedCustomers.includes("All Customers")) {
      ticketData.append("customers", "All Customers");
    } else if (customerArr.length) {
      ticketData.append("customers", customerArr.join(", "));
    }

    if(application_id === 16) {
      ticketData.append("customers", String(clientID));
    }

    let assets = [],
      sites = [];

    let assetNames = [];
    if (values.assets && Array.isArray(values.assets)) {
      assetNames = values.assets
      .map(value => thingOptions.find(thing => thing.value === value))
      .filter(Boolean) // Removes undefined if id not found
      .map(thing => thing.label);
    }

    let siteNames = [];
    if (values.sites && Array.isArray(values.sites)) {
      siteNames = values.sites
      .map(value => siteData.find(site => site.value === value))
      .filter(Boolean) // Removes undefined if id not found
      .map(site => site.label);
    }

    if(assetNames) {
      assets = assetNames.join(", ")
    } else {
      assets = values.assets.join(", ");
    }

    if(siteNames) {
      sites = siteNames.join(", ")
    } else{
      sites = values.sites.join(", ");
    }

    values?.attachments?.fileList.forEach((file: any) => {
      ticketData.append("file", file.originFileObj);
    });
    ticketData.append("subject", values.subject);
    ticketData.append("description", values.description);
    ticketData.append("cc_emails", values.cc_emails);
    ticketData.append("assets", assets);
    ticketData.append("sites", sites);

    const response = await createTicket(clientID, ticketData);

    setIsLoading(false);
    setIsLoading(false);
    setIsSiteLoading(false);
    setIsSiteCategoryLoading(false);

    handleOk();
    form.resetFields();
    setCCEmails([]);

    setSiteAccess(false);

    AntNotification({
      type: response?.data?.status === "success" ? "success" : "error",
      message:
        response?.data?.status === "success"
          ? "Ticket created successfully!"
          : "Failed to create ticket, please try again later.",
      placement: "bottomLeft",
    });
    response?.data?.status === "success" && setShowTicketCreatedPrompt(true);
  };

  const onCancelClick = () => {
    setSiteAccess(false);
    handleCancel();
    form.resetFields();
    setCCEmails([]);
  };

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleInputChange = (currentInput: string) => {
    if (currentInput && !validateEmail(currentInput)) {
      setEmailError("Please enter a valid email address.");
    } else {
      setEmailError(null);
    }
  };

  const handleChange = (clientIDs: string[]) => {
    setSelectedCustomers(clientIDs);
  };

  const handleCategoryChange = (category: number[]) => {
    
    const values = form.getFieldsValue();
    values.site = [];
    form.setFieldsValue({ site: values.site });
    
    setSelectedSiteCategory(category);

    const fetchSiteData = async () => {
      setIsSiteLoading(true);
 
      let query="?page_no=1&results_per_page=1000";

      if (category) {
        query += `&site_type=${category}`;
      }

      form.setFieldValue("sites", []);
      const siteListData = await fetchSiteList(
        selectedCustomers?.length ? selectedCustomers : [clientID],
        customerOptions,
        query,
        customerAccessData
      );

      const siteData = siteListData.filter(site => site.operational_status === 1); 

      if (selectedCustomers.includes("All Customers") || siteData.length > 1) {
        siteData.unshift({
          value: "All Sites",
          label: "All Sites",
          title: "All Sites",
          operational_status: 1
        });
      }

      setSiteListOptions(siteListData);
      setSiteOptions(siteData);
      setIsSiteLoading(false);
    };

    const fetchSiteListForDirectCust = async () => {
      setIsSiteLoading(true);
      let query="?page_no=1&results_per_page=1000";

      if (category) {
        query += `&site_type=${category}`;
      }

      form.setFieldValue("sites", []);
      const siteListData = await fetchEndCustSiteList(clientID, query);
      const siteData = siteListData.filter(site => site.operational_status === 1);
      if (selectedCustomers.includes("All Customers") || siteData.length > 1) {
        siteData.unshift({
          value: "All Sites",
          label: "All Sites",
          title: "All Sites",
          operational_status: 1
        });
      }
      setSiteOptions(siteData);
      setSiteListOptions(siteListData);
      setIsSiteLoading(false);
    };
    
    if (props.application_id === 16) {
      fetchSiteListForDirectCust();
    } else {
      fetchSiteData();
    }
  };

  useEffect(() => {
    setThingOptions([]);
    setSiteOptions([]);
    setSiteCategoryOptions([]);
    const fetchAssetData = async () => {
      setIsLoading(true);

      form.setFieldValue("assets", []);
      const updateTime = new Date().getTime();
      lastUpdateTime.current = {assets: updateTime};
      const thingData = await fetchThingList(
        selectedCustomers?.length ? selectedCustomers : [clientID],
        customerOptions,
      );

      if (updateTime !== lastUpdateTime.current.assets) {
        return;
      }

      if (selectedCustomers.includes("All Customers") || thingData.length > 1) {
        thingData.unshift({
          value: "All Assets",
          label: "All Assets",
          title: "All Assets",
        });
      }

      if (
        selectedCustomers.length === 1 &&
        selectedCustomers[0] === "All Customers"
      ) {
        form.setFieldValue("assets", ["All Assets"]);
        form.setFieldValue("sites", ["All Sites"]);
      }

      setThingOptions(thingData);
      
      setIsLoading(false);
    };
    const fetchSiteData = async (customerAccessData: any[]) => {
      setIsSiteLoading(true);
      
      let query="?page_no=1&results_per_page=1000";

      if (selectedSiteCategory && selectedSiteCategory.length > 0) {
        query += `&site_type=${selectedSiteCategory}`;
      }

      form.setFieldValue("sites", []);
      const siteListData = await fetchSiteList(
        selectedCustomers?.length ? selectedCustomers : [clientID],
        customerOptions,
        query,
        customerAccessData
      );

      const siteData = siteListData.filter(site => site.operational_status === 1); 

      if (selectedCustomers.includes("All Customers") || siteData.length > 1) {
        siteData.unshift({
          value: "All Sites",
          label: "All Sites",
          title: "All Sites",
          operational_status: 1
        });
      }

      if (siteListData.length) {
        setSiteAccess(true);
      } else {
        setSiteAccess(false);
      }
      
      setSiteOptions(siteData);
      setSiteListOptions(siteListData);
      setIsSiteLoading(false);
    };
    const fetchSiteCategoryData = async (customerAccessData: any[]) => {
      setIsSiteLoading(true);
      setIsSiteCategoryLoading(true);

      form.setFieldValue("siteCategory", []);
      const siteCategoryData = await fetchSiteCategoryList(
        selectedCustomers?.length ? selectedCustomers : [clientID],
        customerOptions,
        customerAccessData
      );

      setSiteCategoryOptions(siteCategoryData);
      setIsSiteLoading(false);
      setIsSiteCategoryLoading(false);
    };

    const checkCustomersSiteAccess = async () => {
      setIsSiteLoading(true);
      setIsSiteCategoryLoading(true);

      const customerAccessData = await fetchEndCustDetails(
        selectedCustomers?.length ? selectedCustomers : [clientID],
        customerOptions
      );

      if (customerAccessData &&
        customerAccessData.some(
          (cust) =>
            Array.isArray(cust.feature_list) &&
            cust.feature_list.includes("SiteManagement:SiteManagement")
        )
      ) {
        fetchSiteData(customerAccessData);
        fetchSiteCategoryData(customerAccessData);
      } else {
        setSiteAccess(false);
      }
    
      setIsSiteLoading(false);
      setIsSiteCategoryLoading(false);
      setCustomerAccessData(customerAccessData);
    };
    
    if (selectedCustomers.length) {
      fetchAssetData();
      checkCustomersSiteAccess();
    } else {
      setSiteAccess(false);

      if (props.application_id !== 16) {
        fetchThingListForVendor();
      }
      
      form.setFieldValue("sites", []);
      form.setFieldValue("assets", []);
      form.setFieldValue("siteCategory", []);
    }
  }, [selectedCustomers]);

  const fetchThingListForVendor = async () => {
    setIsLoading(true);
    
    const updateTime = new Date().getTime();
    lastUpdateTime.current = {assets: updateTime};

    const thingData = await fetchVendorThingList(clientID);

    if (updateTime !== lastUpdateTime.current.assets) {
      return;
    }

    if (thingData.length > 1) {
      thingData.unshift({
        value: "All Assets",
        label: "All Assets",
        title: "All Assets",
      });
    }

    setThingOptions(thingData);
    setIsLoading(false);
  };

  useEffect(() => {
    if (
      customerOptionList.length > 1 &&
      customerOptionList[0].value !== "All Customers"
    ) {
      customerOptionList.unshift({
        value: "All Customers",
        label: "All Customers",
        title: "All Customers",
        application_id: 12,
      });
    }
    setCustomerList(customerOptionList);

    const fetchThingListForDirectCust = async () => {
      setIsLoading(true);

      const thingData = await fetchEndCustThingList(clientID);
      if (selectedCustomers.includes("All Customers") || thingData.length > 1) {
        thingData.unshift({
          value: "All Assets",
          label: "All Assets",
          title: "All Assets",
        });
      }

      setThingOptions(thingData);
      setIsLoading(false);
    };

    const fetchSiteCategoryListForDirectCust = async () => {
      setIsSiteLoading(true);
      setIsSiteCategoryLoading(true);

      const siteCategoryData = await fetchEndCustSiteCategoryList(clientID);
      setSiteCategoryOptions(siteCategoryData);
      setIsSiteLoading(false);
      setIsSiteCategoryLoading(false);
    };

    const fetchSiteListForDirectCust = async () => {
      setIsSiteLoading(false);

      let query="?page_no=1&results_per_page=1000";

      if (selectedSiteCategory && selectedSiteCategory.length > 0) {
        query += `&site_type=${selectedSiteCategory}`;
      }

      const siteListData = await fetchEndCustSiteList(clientID, query);
     
      const siteData = siteListData.filter(site => site.operational_status === 1);
      
      if (selectedCustomers.includes("All Customers") || siteData.length > 1) {
        siteData.unshift({
          value: "All Sites",
          label: "All Sites",
          title: "All Sites",
          operational_status: 1
        });
      }
      if (siteListData.length) {
        setSiteAccess(true);
      } else {
        setSiteAccess(false);
      }

      setSiteOptions(siteData);
      setSiteListOptions(siteListData);
      setIsSiteLoading(false);
    };

    const checkSiteAccess = async () => {
      setIsSiteLoading(true);

      if (Array.isArray(enabledFeatures) &&
        enabledFeatures.includes("SiteManagement:SiteManagement")) 
      {
        fetchSiteListForDirectCust();
        fetchSiteCategoryListForDirectCust();
      }

      setIsSiteLoading(false);
    };

    if (props.application_id === 16) {
      checkSiteAccess();
      fetchSiteListForDirectCust();
      fetchThingListForDirectCust();
    }
  }, []);
  
  return (
    <AntDrawer
      title="Enter the details of the Ticket:"
      placement="right"
      closable={false}
      onClose={onCancelClick}
      open={isDrawerOpen}
      destroyOnClose={true}
      maskClosable={false}
      getContainer={false}
      width={isMobileScreen ? "100%" : "35%"}
      className="create-ticket-drawer"
    >
      <Form
        name="create_ticket"
        labelAlign="left"
        form={form}
        onFinish={handleOKClick}
      >
        <div className="ticket-creation">
          <h4 className="input-label">Subject *</h4>
          <Form.Item
            name="subject"
            className="input-field"
            rules={[{ required: true, message: "Please enter subject!" }]}
          >
            <Input placeholder="Enter the subject" />
          </Form.Item>

          <h4 className="input-label">Description *</h4>
          <Form.Item
            name="description"
            className="input-field"
            rules={[{ required: true, message: "Please enter description!" }]}
          >
            <Input.TextArea
              placeholder="Describe your issue"
              style={{
                height: "100px",
                resize: "vertical",
                overflow: "auto",
              }}
            />
          </Form.Item>

          <h4 className="input-label">CC E-mails</h4>
          <Form.Item
            name="cc_emails"
            className="input-field"
            validateStatus={emailError ? "error" : ""}
            help={emailError}
          >
            <ReactMultiEmail
              placeholder="For multiple Emails, use 'Enter' key"
              emails={ccEmails}
              onChange={(_emails: string[]) => {
                setCCEmails(_emails);
              }}
              onChangeInput={handleInputChange}
              getLabel={(
                email: string,
                index: number,
                removeEmail: (index: number) => void,
              ) => {
                return (
                  <div data-tag key={index}>
                    {email}
                    <span data-tag-handle onClick={() => removeEmail(index)}>
                      ×
                    </span>
                  </div>
                );
              }}
            />
          </Form.Item>

          {application_id !== 16 && (
            <>
              <h4 className="input-label">Customers</h4>
              <Form.Item
                name="customers"
                className="input-field"
                rules={[
                  { required: false, message: "Please select the customer(s)!" },
                ]}
              >
                <Select
                  mode="multiple"
                  placeholder="Select the customer(s)"
                  onChange={handleChange}
                  options={customerList}
                  filterOption={(input, option) =>
                    option?.label
                      .toLowerCase()
                      .includes(input.toLowerCase()) as boolean
                  }
                  optionRender={(option) => (
                    <Space>
                      <span>
                        {option?.data?.title} ({option?.data?.value})
                      </span>
                    </Space>
                  )}
                  
                />
              </Form.Item>{" "}
            </>
          )}

          {siteAccess && (
            <>
              <h4 className="input-label">Site Type</h4>
              <Form.Item
                name="siteCategory"
                className="input-field"
                rules={[
                  { required: false, message: "Please select the site type" },
                ]}
              >
                <Select
                  placeholder="Select the site type"
                  options={siteCategoryOptions}
                  onChange={handleCategoryChange}
                  allowClear
                  filterOption={(input, option) =>
                    option?.label
                      .toLowerCase()
                      .includes(input.toLowerCase()) as boolean
                  }
                  optionRender={(option) => (
                    <Space>
                      <span role="img" aria-label={option.data.id}>
                        {option?.data?.title}
                      </span>
                    </Space>
                  )}
                  disabled={isSiteCategoryLoading}
                  loading={isSiteCategoryLoading}
                />
              </Form.Item>{" "}
            </>
          )}

          {siteAccess && (
            <>
              <h4 className="input-label">Sites</h4>
              <Form.Item
                name="sites"
                className="input-field"
                rules={[
                  { required: false, message: "Please select the site(s)" },
                ]}
              >
                <Select
                  mode="multiple"
                  placeholder="Select the site(s)"
                  options={siteData}
                  filterOption={(input, option) =>
                    option?.label
                      .toLowerCase()
                      .includes(input.toLowerCase()) as boolean
                  }
                  optionRender={(option) => (
                    <Space>
                      <span role="img" aria-label={option.data.id}>
                        {option?.data?.title}
                      </span>
                    </Space>
                  )}
                  disabled={isSiteLoading}
                  loading={isSiteLoading}
                />
              </Form.Item>{" "}
            </>
          )}

          <h4 className="input-label">Assets</h4>
          <Form.Item
            name="assets"
            className="input-field"
            rules={[{ required: false, message: "Please select the asset(s)!" }]}
          >
            <Select
              mode="multiple"
              placeholder="Select the asset(s)"
              options={thingOptions}
              filterOption={(input, option) =>
                option?.label
                  .toLowerCase()
                  .includes(input.toLowerCase()) as boolean
              }
              optionRender={(option) => (
                <Space>
                  <span role="img" aria-label={option.data.id}>
                    {option?.data?.title}
                  </span>
                </Space>
              )}
              disabled={isLoading}
              loading={isLoading}
            />
          </Form.Item>

          <h4 className="input-label">Attachments</h4>
          <div className="upload-container">
            <Form.Item name="attachments" className="uploader">
              <Upload
                customRequest={() => {}}
                iconRender={() => <FileOutlined />}
                accept="image/png, image/jpeg, video/mp4, video/quicktime"
                maxCount={10}
                onChange={(info) => {
                  setAttachmentSize(
                    info.fileList.reduce(
                      (total: number, currentFile: any) =>
                        total + currentFile.size,
                      0,
                    ) /
                      1024 /
                      1024,
                  );
                }}
                beforeUpload={(file, fileList) => {
                  const isAllowedExtension =
                    file.type === "image/jpeg" ||
                    file.type === "image/png" ||
                    file.type === "video/mp4" ||
                    file.type === "video/quicktime";
                  if (!isAllowedExtension) {
                    message.error("You can only upload JPG/PNG/MP4/MOV file!");
                    return Upload.LIST_IGNORE;
                  }

                  const isAllowedSize = file.size / 1024 / 1024 < 20;
                  if (!isAllowedSize) {
                    message.error("File must be smaller than 20MB!");
                    return Upload.LIST_IGNORE;
                  }

                  let isMaxFileSizeAllowed = true;
                  if (attachmentSize > 20) {
                    isMaxFileSizeAllowed = false;
                    message.error("Total file size must be smaller than 20MB!");
                    return Upload.LIST_IGNORE;
                  }

                  let isMaxFileCountAllowed = true;
                  if (
                    fileList.length > 10 &&
                    fileList.indexOf(file) === fileList.length - 1
                  ) {
                    isMaxFileCountAllowed = false;
                    message.error("You can only upload up to 10 files!");
                    fileList.slice(0, 9);
                    return Upload.LIST_IGNORE;
                  }

                  return (
                    isAllowedExtension &&
                    isAllowedSize &&
                    isMaxFileSizeAllowed &&
                    isMaxFileCountAllowed
                  );
                }}
                multiple={true}
              >
                <div className="upload-back">
                  <UploadOutlined style={{ fontSize: 34 }} />
                  <p className="text">
                    Click to upload attachments (upto 10 files and 20 MB)
                  </p>
                </div>
              </Upload>
            </Form.Item>
          </div>
          <div className="help-text">
            **It may take 3 minutes for the ticket to display.
          </div>
        </div>
        <div
          className={
            isMobileScreen ? "mobile-button-container" : "button-container"
          }
          style={{ width: isMobileScreen ? "100%" : "34%" }}
        >
          <AntButton
            onClick={onCancelClick}
            disabled={isLoading}
            style={{ marginRight: "16px" }}
          >
            Cancel{" "}
            {isLoading && (
              <Spin
                indicator={<LoadingOutlined spin />}
                size="small"
                style={{ marginLeft: "8px" }}
              />
            )}
          </AntButton>
          <AntButton
            type="primary"
            htmlType="submit"
            disabled={isLoading}
            className="loader-white"
          >
            Create{" "}
            {isLoading && (
              <Spin
                indicator={<LoadingOutlined spin />}
                size="small"
                style={{ marginLeft: "8px", color: "white" }}
              />
            )}
          </AntButton>
        </div>
      </Form>
    </AntDrawer>
  );
};

export default TicketCreation;
