import React, { useEffect, useState } from "react";
import { getSingleTicket, createTicketReply } from "@datoms/js-sdk";
import { Form, Input, message, Space, Spin, Upload } from "antd";
import { ReactMultiEmail } from "react-multi-email";
import FileOutlined from "@ant-design/icons/FileOutlined";
import UploadOutlined from "@ant-design/icons/UploadOutlined";
import AntButton from "@datoms/react-components/src/components/AntButton";
import SkeletonLoader from "@datoms/react-components/src/components/SkeletonLoader";
import { RouteComponentProps, withRouter } from "react-router-dom";
import AntNotification from "@datoms/react-components/src/components/AntNotification";
import { ArrowLeftOutlined, LoadingOutlined } from "@ant-design/icons";
import { TicketThreadProps } from "../../types";
import moment from "moment-timezone";

import "./styles.less";
import {
  CSMNameFromID,
  CustomerNameFromID,
} from "../../logic/CustomerFilterData";
import AntRow from "@datoms/react-components/src/components/AntRow";
import AntCol from "@datoms/react-components/src/components/AntCol";

const { TextArea } = Input;
const statusCode = ["Open", "Pending", "Resolved", "Closed"];
const statusColor = ["#000000", "#FF0000", "#0C8CD0", "#008000"];

const TicketThread: React.FC<TicketThreadProps & RouteComponentProps> = (
  props,
) => {
  const [ticketDetails, setTicketDetails] = useState<any>({});
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [ccEmails, setCCEmails] = useState<string[]>([]);
  const [isReplying, setIsReplying] = useState<boolean>(false);
  const [attachmentSize, setAttachmentSize] = useState<number>(0);
  const [emailError, setEmailError] = useState<string | null>(null);
  const [showReplyPrompt, setShowReplyPrompt] = useState<boolean>(false);
  const currentTicketId = props.currentTicketId;
  const prevPage = props.prevPage;
  const clientID = props.clientID;
  const isMobileScreen = props.isMobileScreen;
  const userTimeZone = props.userTimeZone;
  const ticketInfo = ticketDetails?.data;
  const allCustomerIDName = props.allCustomerIDName;
  const ticketConversations = ticketInfo?.conversations;
  const ticketConversationsList: any = [];
  const [form] = Form.useForm();

  const handleReplyClick = async () => {
    const values = form.getFieldsValue();
    const status = ticketInfo.status;
    const ticketData = new FormData();

    setIsReplying(true);

    values?.attachments?.fileList.forEach((file: any) => {
      ticketData.append("file", file.originFileObj);
    });
    ticketData.append("body", values.body);
    ticketData.append("status", status);
    ticketData.append("cc_emails", values.cc_emails);

    const response = await createTicketReply(
      clientID,
      currentTicketId,
      ticketData,
    );

    response?.status === "success" && setShowReplyPrompt(true);

    setIsReplying(false);

    form.resetFields();
    setCCEmails([]);

    AntNotification({
      type: response?.status === "success" ? "success" : "error",
      message:
        response?.status === "success"
          ? "Reply created, will be reflected shortly!"
          : "Failed to create reply, please try again later.",
      placement: "bottomLeft",
    });

    const interval = setInterval(async () => {
      const ticketResponse = await getSingleTicket(clientID, currentTicketId);
      setTicketDetails(ticketResponse);
    }, 15000);

    setTimeout(() => {
      clearInterval(interval);
      setShowReplyPrompt(false);
    }, 60000);
  };

  if (ticketConversations?.length) {
    ticketConversations.forEach(
      (conversation: {
        id: number;
        reply_from_name: string;
        created_at: string;
        body: string;
        attachments: any;
        reply_from_email: string;
      }) => {
        ticketConversationsList.push(
          <div className="ticket-conversations" key={conversation.id}>
            <div
              className="comment-heading"
              style={{
                display: isMobileScreen ? "flex" : "",
                flexDirection: isMobileScreen ? "column" : "row",
              }}
            >
              <span className="comment-creator">{`Comment by: ${conversation?.reply_from_name} [${conversation?.reply_from_email}] ${!isMobileScreen ? " at " : ""} `}</span>
              <span className="comment-date">{`(${moment.utc(conversation?.created_at).tz(userTimeZone).format("DD MMM YYYY, HH:mm")})`}</span>
            </div>
            <span
              className=""
              dangerouslySetInnerHTML={{ __html: conversation?.body }}
            />
            {conversation.attachments?.length > 0 && (
              <div className="ticket-attachments">
                <h4>Attachments:</h4>
                <Space>
                  {conversation.attachments.map((attachment: any) => (
                    <a
                      href={attachment.attachment_url}
                      target="_blank"
                      rel="noreferrer"
                      key={attachment.id}
                    >
                      <FileOutlined
                        style={{ fontSize: "20px", marginRight: "8px" }}
                      />
                      {attachment.name}
                    </a>
                  ))}
                </Space>
              </div>
            )}
          </div>,
        );
      },
    );
  }

  const ticketHeaderMobileVersion = () => {
    return (
      <div className="mobile-ticket-title">
        <div
          className="mobile-ticket-id-name"
          style={{ flexDirection: "column" }}
        >
          <div className="back-button-id">
            <AntButton
              onClick={() => {
                props?.history?.push(prevPage);
              }}
              style={{ border: "none", fontSize: "20px", padding: "0px" }}
            >
              <ArrowLeftOutlined />
            </AntButton>
            <span className="mobile-ticket-id">{`#${currentTicketId}`}</span>
          </div>
          <span className="mobile-ticket-subject">{`${ticketInfo?.subject}`}</span>
          <span className="mobile-ticket-date">{`(${moment.utc(ticketInfo?.created_at).tz(userTimeZone).format("DD MMM YYYY, HH:mm")})`}</span>
        </div>
      </div>
    );
  };

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleInputChange = (currentInput: string) => {
    if (currentInput && !validateEmail(currentInput)) {
      setEmailError("Please enter a valid email address.");
    } else {
      setEmailError(null);
    }
  };

  useEffect(() => {
    setIsLoading(false);
    const fetchData = async () => {
      setIsLoading(true);
      const ticketResponse = await getSingleTicket(clientID, currentTicketId);
      setTicketDetails(ticketResponse);
      setIsLoading(false);
    };
    fetchData();
  }, []);

  let mobileSpan = window.innerWidth >= 1336 ? 20 : 24;
  let mobileSpaceSpan = window.innerWidth >= 1336 ? 2 : 0;

  return (
    <div id="ticket-details">
      {isLoading ? (
        <SkeletonLoader count={1} rows={8} />
      ) : (
        <>
          <AntRow>
            <AntCol span={mobileSpaceSpan} />
            <AntCol span={mobileSpan}>
              <AntCol span={mobileSpaceSpan} />

              <div className="ticket-header">
                {isMobileScreen ? (
                  ticketHeaderMobileVersion()
                ) : (
                  <div className="ticket-title">
                    <div className="ticket-id-name">
                      <span className="ticket-subject">{`#${currentTicketId}: ${ticketInfo?.subject}`}</span>
                      <span className="ticket-date">{`(${moment.utc(ticketInfo?.created_at).tz(userTimeZone).format("DD MMM YYYY, HH:mm")})`}</span>
                    </div>
                    <AntButton
                      onClick={() => {
                        props?.history?.push(prevPage);
                      }}
                    >
                      <ArrowLeftOutlined /> Back
                    </AntButton>
                  </div>
                )}

                <div className="ticket-title-details">
                  {clientID === 1 ? (
                    <div className="ticket-title-created-by">
                      <span className="ticket-title-label-datomsX">
                        Created by-{" "}
                      </span>{" "}
                      <span className="ticket-title-text-datomsX">
                        Account:{" "}
                        <div className="ticket-title-value-datomsX">
                          {CustomerNameFromID(
                            String(ticketInfo.custom_fields.cf_datoms_id),
                            allCustomerIDName,
                          ) || ""}
                        </div>
                      </span>
                      <span className="ticket-title-text-datomsX">
                        User:{" "}
                        <div className="ticket-title-value-datomsX">
                          {ticketInfo?.custom_fields?.cf_user_name_in_datoms ||
                            ""}
                        </div>
                      </span>
                    </div>
                  ) : (
                    <div>
                      <span className="ticket-title-label">Created by: </span>
                      <span className="ticket-title-text">
                        {ticketInfo?.custom_fields?.cf_user_name_in_datoms ||
                          ""}
                      </span>
                    </div>
                  )}
                </div>
                {props.application_id !== 16 && (
                  <div className="ticket-title-details">
                    <span className="ticket-title-label">Customers: </span>{" "}
                    <span className="ticket-title-text">
                      {CustomerNameFromID(
                        ticketInfo?.custom_fields?.cf_customer,
                        props.allCustomerIDName,
                      ) || " "}
                    </span>
                  </div>
                )}
                {ticketInfo?.custom_fields?.cf_sites && (
                  <div className="ticket-title-details">
                    <span className="ticket-title-label">Sites: </span>{" "}
                    <span className="ticket-title-text">
                      {ticketInfo?.custom_fields?.cf_sites ||  ticketInfo?.custom_fields?.cf_sites || " "}
                    </span>
                  </div>
                )}
                <div className="ticket-title-details">
                  <span className="ticket-title-label">Assets: </span>{" "}
                  <span className="ticket-title-text">
                    {ticketInfo?.custom_fields?.cf_assets ||  ticketInfo?.custom_fields?.cf_asset || " "}
                  </span>
                </div>
                {clientID === 1 && (
                  <div className="ticket-title-details">
                    <span className="ticket-title-label">
                      Customer Service Manager:{" "}
                    </span>{" "}
                    <span className="ticket-title-text">
                      {CSMNameFromID(
                        ticketInfo?.custom_fields?.cf_customer_service_manager,
                        props.CSMList,
                      ) || ""}
                    </span>
                  </div>
                )}
                <div className="ticket-title-details">
                  <span className="ticket-title-label">Status: </span>{" "}
                  <span
                    className="ticket-title-text"
                    style={{ color: statusColor[ticketInfo?.status - 2] }}
                  >
                    {statusCode[ticketInfo?.status - 2] || ""}
                  </span>
                </div>
                <div className="ticket-title-details">
                  <span className="ticket-title-label">Due Date: </span>{" "}
                  <span className="ticket-title-text">
                    {moment
                      .utc(ticketInfo?.due_by)
                      .tz(userTimeZone)
                      .format("DD MMM YYYY, HH:mm")}
                  </span>
                </div>
                <div className="headings">
                  <span>Description:</span>
                </div>

                <div className="ticket-description">
                  <span
                    className="ticket-description-content"
                    dangerouslySetInnerHTML={{
                      __html: ticketInfo?.description,
                    }}
                  />
                  {ticketInfo?.attachments?.length > 0 && (
                    <div className="ticket-attachments">
                      <h4>Attachments:</h4>
                      <Space>
                        {ticketInfo.attachments.map(
                          (attachment: {
                            id: number;
                            attachment_url: string;
                            name: string;
                          }) => (
                            <a
                              href={attachment.attachment_url}
                              target="_blank"
                              rel="noreferrer"
                              key={attachment.id}
                            >
                              <FileOutlined
                                style={{ fontSize: "20px", marginRight: "8px" }}
                              />
                              {attachment.name}
                            </a>
                          ),
                        )}
                      </Space>
                    </div>
                  )}
                </div>
              </div>
              <div className="comments-title">
                <span>Comments:</span>
              </div>
              {showReplyPrompt && (
                <div
                  className="ticket-reply-prompt"
                  style={{ marginTop: isMobileScreen ? 16 : 8 }}
                >
                  Newly created replies will be reflected shortly.
                </div>
              )}
              <div>{ticketConversationsList}</div>
              <div className="ticket-reply">
                <Form
                  name="create_reply"
                  labelAlign="left"
                  form={form}
                  onFinish={handleReplyClick}
                >
                  <div className="reply-field-title">Description *</div>
                  <Form.Item
                    name="body"
                    rules={[
                      {
                        required: true,
                        message: "Please enter the description!",
                      },
                    ]}
                  >
                    <TextArea placeholder="Type your reply here" rows={4} />
                  </Form.Item>

                  <div className="reply-field-title">CC E-mail</div>
                  <Form.Item
                    name="cc_emails"
                    className="input-field"
                    validateStatus={emailError ? "error" : ""}
                    help={emailError}
                  >
                    <ReactMultiEmail
                      placeholder="For multiple Emails, use 'Enter' key"
                      emails={ccEmails}
                      onChange={(_emails: string[]) => {
                        setCCEmails(_emails);
                      }}
                      onChangeInput={handleInputChange}
                      getLabel={(
                        email: string,
                        index: number,
                        removeEmail: (index: number) => void,
                      ) => {
                        return (
                          <div data-tag key={index}>
                            {email}
                            <span
                              data-tag-handle
                              onClick={() => removeEmail(index)}
                            >
                              ×
                            </span>
                          </div>
                        );
                      }}
                    />
                  </Form.Item>

                  <div className="cc-email-upload">
                    <div className="upload-container">
                      <Form.Item name="attachments" className="uploader">
                        <Upload
                          customRequest={() => {}}
                          iconRender={() => <FileOutlined />}
                          accept="image/png, image/jpeg, video/mp4, video/quicktime"
                          maxCount={10}
                          onChange={(info) => {
                            setAttachmentSize(
                              info.fileList.reduce(
                                (total: number, currentFile: any) =>
                                  total + currentFile.size,
                                0,
                              ) /
                                1024 /
                                1024,
                            );
                          }}
                          beforeUpload={(file, fileList) => {
                            const isAllowedExtension =
                              file.type === "image/jpeg" ||
                              file.type === "image/png" ||
                              file.type === "video/mp4" ||
                              file.type === "video/quicktime";
                            if (!isAllowedExtension) {
                              message.error(
                                "You can only upload JPG/PNG/MP4/MOV file!",
                              );
                              return Upload.LIST_IGNORE;
                            }

                            const isAllowedSize = file.size / 1024 / 1024 < 20;
                            if (!isAllowedSize) {
                              message.error("File must be smaller than 20MB!");
                              return Upload.LIST_IGNORE;
                            }

                            let isMaxFileSizeAllowed = true;
                            if (attachmentSize > 20) {
                              isMaxFileSizeAllowed = false;
                              message.error(
                                "Total file size must be smaller than 20MB!",
                              );
                              return Upload.LIST_IGNORE;
                            }

                            let isMaxFileCountAllowed = true;
                            if (
                              fileList.length > 10 &&
                              fileList.indexOf(file) === fileList.length - 1
                            ) {
                              isMaxFileCountAllowed = false;
                              message.error(
                                "You can only upload up to 10 files!",
                              );
                              fileList.slice(0, 9);
                              return Upload.LIST_IGNORE;
                            }

                            return (
                              isAllowedExtension &&
                              isAllowedSize &&
                              isMaxFileSizeAllowed &&
                              isMaxFileCountAllowed
                            );
                          }}
                          multiple={true}
                        >
                          <div className="upload-back">
                            <UploadOutlined style={{ fontSize: 34 }} />
                            <p className="text">
                              Click to upload attachments (upto 10 files and 20
                              MB)
                            </p>
                          </div>
                        </Upload>
                      </Form.Item>
                    </div>
                    <div className="help-text">
                      **It may take up to 60 seconds for the created replies to
                      be reflected on the page.
                    </div>

                    <div
                      className={
                        isMobileScreen ? "mobile-reply-button" : "reply-button"
                      }
                    >
                      <AntButton
                        type="primary"
                        htmlType="submit"
                        style={{ width: "200px", marginTop: "16px" }}
                        disabled={isReplying}
                      >
                        Reply{" "}
                        {isReplying && (
                          <Spin
                            indicator={<LoadingOutlined spin />}
                            size="small"
                            style={{ marginLeft: "8px", color: "white" }}
                          />
                        )}
                      </AntButton>
                    </div>
                  </div>
                </Form>
              </div>
            </AntCol>
          </AntRow>
        </>
      )}
    </div>
  );
};

export default withRouter(TicketThread);
