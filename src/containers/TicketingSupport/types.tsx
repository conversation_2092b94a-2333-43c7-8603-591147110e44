export interface CustomerListProps {
  title: string;
  label: string;
  value: number | string;
  application_id: number;
}
export interface CSMListProps {
  title: string;
  label: string;
  value: number | string;
}
export interface TicketThreadProps {
  clientID: number;
  currentTicketId: number;
  prevPage: any;
  isMobileScreen: boolean;
  userTimeZone: string;
  application_id: number;
  customersList: CustomerListProps[];
  CSMList: CustomerListProps[];
  allCustomerIDName: { [key: string]: string };
  enabledFeatures: string[];
}

export interface TicketingSupportProps {
  client_id: number;
  user_preferences: {
    timezone: string;
  };
  application_id: number;
  vendor_id: number;
}

export interface CustomerFilterDataProps {
  application_id: number;
  client_id: number;
  vendor_id: number;
}

export interface TicketListProps {
  ticketDetails: any;
  totalTickets: number;
  ticketsPerPage: number;
  isLoading: boolean;
  isMobileScreen: boolean;
  setTicketPage: (page: number) => void;
  client_id: number;
  customersList: CustomerListProps[];
  CSMList: CustomerListProps[];
  ticketPage: number;
  allCustomerIDName: { [key: string]: string };
  searchMode: boolean;
  handleLoadMoreClick: () => void;
  endOfTicketList: boolean;
  searchQuery: string;
}

export interface TicketCreationProps {
  isDrawerOpen: boolean;
  handleCancel: () => void;
  handleOk: () => void;
  clientID: number;
  isMobileScreen: boolean;
  setShowTicketCreatedPrompt: (value: boolean) => void;
  application_id: number;
  customerOptions: CustomerListProps[];
  enabledFeatures: string[];
}

export interface ThingDataProps {
  name: string;
  label: string;
  title: string;
  id: number;
}

export interface CustomeDetailsDataProps {
  name: string;
  access: any;
}

export interface SiteCategoryDataProps {
  name: string;
  label: string;
  title: string;
  id: number;
}

export interface SiteDataProps {
  name: string;
  label: string;
  title: string;
  operational_status: number;
  id: number;
}

export interface UserProps {
  contact_id: number;
  first_name: string;
  last_name: string;
  role_details: { role_id: number }[];
}

export interface TicketProps {
  id: number;
  description: string;
  subject: string;
  description_text: string;
  status: number;
  custom_fields: any;
  created_at: number;
  searchMode: boolean;
  handleLoadMoreClick: () => void;
  endOfTicketList: boolean;
  length: number;
  forEach: (ticket: TicketProps) => void;
}
