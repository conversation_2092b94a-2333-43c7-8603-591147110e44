import React, { useEffect, useState } from "react";
import TicketCreation from "./components/TicketCreation";
import TicketsList from "./components/TicketList";
import AntButton from "@datoms/react-components/src/components/AntButton";
import { withRouter, RouteComponentProps } from "react-router-dom";
import moment from "moment-timezone";
import TicketThread from "./components/TicketThread";
import GenericFilter from "@datoms/react-components/src/components/GenericFilter/pages";
import { getBaseUrl } from "@datoms/js-utils/src/base-url-logic";
import { TicketingSupportProps, TicketProps } from "./types";
import { getTickets } from "@datoms/js-sdk";
import _findIndex from "lodash/findIndex";
import { fetchCSMList, fetchCustomersList } from "./logic/CustomerFilterData";
import { Skeleton } from "antd";
import Support_Ticket_Illustration from "./images/Support_Ticket_Illustration.svg";
import { filterTickets, getUniqueTicketList } from "./logic/TicketFilterData";

import "./styles.less";

let searchInterval: NodeJS.Timeout | null = null;
let ticketSearchPage = 1;

const TicketingSupport: React.FC<
  RouteComponentProps & TicketingSupportProps
> = (props) => {
  const clientID: number = props.client_id;
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [currentTicketId, setCurrentTicketId] = useState<number>(1);
  const [ticketDetails, setTicketDetails] = useState<any>([]);
  const [status, setStatus] = useState<number | undefined>(2);
  const [ticketPage, setTicketPage] = useState<number>(1);
  const [totalTickets, setTotalTickets] = useState<number>(10);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [fromTime, setFromTime] = useState(
    moment().subtract(3, "months").startOf("day").unix(),
  );
  const [uptoTime, setUptoTime] = useState(moment().endOf("day").unix());
  const [showTicketCreatedPrompt, setShowTicketCreatedPrompt] =
    useState<boolean>(false);
  const [customersList, setCustomersList] = useState<any>([]);
  const [pageLoading, setPageLoading] = useState<boolean>(true);
  const [selectedCustomerFilterId, setSelectedCustomerFilterId] = useState<
    number | undefined
  >(undefined);
  const [selectedCSMId, setSelectedCSMId] = useState<number | undefined>(
    undefined,
  );
  const [CSMList, setCSMList] = useState<any>([]);
  const [allCustomerIDName, setAllCustomerIDName] = useState<any>({});
  const [searchMode, setSearchMode] = useState<boolean>(false);
  // const [ticketSearchPage, setTicketSearchPage] = useState<number>(1);
  const [endOfTicketList, setEndOfTicketList] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>("");

  const isMobileScreen = window.innerWidth <= 576;
  const userTimeZone = props?.user_preferences?.timezone || "Asia/Kolkata";

  const disabledDate = (current: any) => {
    return current && current > moment().endOf("day");
  };

  const dateFilterRanges = {
    "This Week": [moment().startOf("week"), moment().endOf("day")],
    "Last Week": [
      moment().subtract(1, "week").startOf("week"),
      moment().subtract(1, "week").endOf("week"),
    ],
    "Last 7 days": [
      moment().subtract(7, "days").startOf("day"),
      moment().endOf("day"),
    ],
    "Last 30 Days": [
      moment().subtract(30, "days").startOf("day"),
      moment().endOf("day"),
    ],
    "This Month": [moment().startOf("month"), moment().endOf("month")],
    "Last Month": [
      moment().subtract(1, "M").startOf("month"),
      moment().subtract(1, "M").endOf("month"),
    ],
    "Last 3 Months": [
      moment().subtract(3, "M").startOf("day"),
      moment().endOf("day"),
    ],
    "Last 120 Days": [
      moment().subtract(120, "days").startOf("day"),
      moment().endOf("day"),
    ],
  };

  const datePickerConfig = {
    placeholder: ["From", "To"],
    size: "default",
    disabledDate: disabledDate,
    showTime: false,
    separator: " - ",
    format: "DD-MMM-YYYY",
  };
  const filterData = [
    {
      type: "date",
      ranges: dateFilterRanges,
      datePickerConfig: datePickerConfig,
      selectValue: fromTime + "-" + uptoTime,
      placeholder: "Select Range",
      label: "Date Range",
      no_outside_label: true,
      url_name: "date_range",
      is_outside_filter_drawer: !isMobileScreen,
      is_inside_filter_drawer: isMobileScreen,
    },
    {
      optionData: [
        { value: 2, title: "Open" },
        { value: 3, title: "Pending" },
        { value: 4, title: "Resolved" },
        { value: 5, title: "Closed" },
      ],
      type: "select",
      selectValue: status,
      showSearch: true,
      allowClear: true,
      sorted: false,
      placeholder: "Ticket Status",
      label: "Status",
      no_outside_label: true,
      url_name: "status",
      is_outside_filter_drawer: !isMobileScreen,
      is_inside_filter_drawer: isMobileScreen,
    },
    {
      optionData: customersList,
      type: "select",
      selectValue: selectedCustomerFilterId,
      showSearch: true,
      allowClear: true,
      sorted: false,
      placeholder: "Account",
      label: "Account",
      hideField: props.client_id !== 1,
      no_outside_label: true,
      url_name: "customer",
      is_outside_filter_drawer: !isMobileScreen,
      is_inside_filter_drawer: isMobileScreen,
    },
    {
      optionData: CSMList,
      type: "select",
      selectValue: selectedCSMId,
      showSearch: true,
      allowClear: true,
      sorted: false,
      placeholder: "CSM",
      label: "CSM",
      hideField: props.client_id !== 1,
      no_outside_label: true,
      url_name: "csm",
      is_outside_filter_drawer: !isMobileScreen,
      is_inside_filter_drawer: isMobileScreen,
    },
  ];

  const getUrlBreak = (value: string) => {
    let getURLData: string = "";
    let getURLDataIndex = _findIndex(
      props.history?.location?.search?.split(","),
      (url: string) => {
        return url.includes(value);
      },
    );
    if (getURLDataIndex > -1) {
      getURLData = props.history?.location?.search
        ?.split(",")
        [getURLDataIndex].split(":")[1];
    }
    return getURLData === "undefined" ? "" : getURLData;
  };

  const onUrlChange = () => {
    const dateRangesFilter: any = getUrlBreak("date_range");
    const dates = dateRangesFilter.split("-");
    const statusFilter: number | undefined =
      getUrlBreak("status") && !isNaN(parseInt(getUrlBreak("status")))
        ? parseInt(getUrlBreak("status"))
        : undefined;
    const customerFilter: string = getUrlBreak("customer");
    const csmFilter: string = getUrlBreak("csm");
    const searchQuery: string = getUrlBreak("search");
    setFromTime(dates[0]);
    setUptoTime(dates[1]);
    setStatus(statusFilter);
    setSelectedCustomerFilterId(parseInt(customerFilter));
    setSelectedCSMId(parseInt(csmFilter));
    setSearchQuery(searchQuery);
  };

  const handleCreateTicket = () => {
    setIsDrawerOpen(true);
  };

  const handleOk = () => {
    setIsDrawerOpen(false);

    const interval = setInterval(async () => {
      const fetchQuery = `?created_from_time=${fromTime}&created_upto_time=${uptoTime}&status=${status}&page=${ticketPage}`;
      const ticketResponse = await getTickets(clientID, fetchQuery);
      setTicketDetails(ticketResponse?.tickets);
      setTotalTickets(ticketResponse?.total);
    }, 30000);

    setTimeout(() => {
      clearInterval(interval);
      setShowTicketCreatedPrompt(false);
    }, 180000);
  };

  const handleCancel = () => {
    setIsDrawerOpen(false);
  };

  const currentURL = props.history.location.search;
  if (currentURL.includes("ticket_id")) {
    const ticketId = Number(currentURL.split("=")[1]);
    if (ticketId !== currentTicketId) {
      setCurrentTicketId(ticketId);
    }
  }
  const applyFilterSelect = (value: string | number, _: string | number) => {
    return {
      selected_values: value,
      total_options: {},
    };
  };

  useEffect(() => {
    if (searchMode) return;
    const currentURL = props.history.location.search;
    if (!currentURL.includes("ticket_id")) {
      let prevURL = `?filter=date_range:${fromTime}-${uptoTime},status:${status},page=${ticketPage}`;
      if (props.client_id === 1)
        prevURL += `,customer=${selectedCustomerFilterId},csm=${selectedCSMId}`;
      props.history.push(prevURL);
    }
    let ticketResponse;

    let fetchQuery = `?created_from_time=${fromTime}&created_upto_time=${uptoTime}&status=${status}&page=${ticketPage}`;
    if (props.client_id === 1)
      fetchQuery += `&customer=${selectedCustomerFilterId}&csm=${selectedCSMId}`;

    const fetchData = async () => {
      setIsLoading(true);
      ticketResponse = await getTickets(clientID, fetchQuery);
      setTicketDetails(ticketResponse?.tickets);
      setTotalTickets(ticketResponse?.total);
      setIsLoading(false);
    };
    fetchData();
  }, [
    fromTime,
    uptoTime,
    status,
    ticketPage,
    selectedCustomerFilterId,
    selectedCSMId,
    searchMode,
  ]);

  useEffect(() => {
    setTicketPage(1);
  }, [fromTime, uptoTime, status, selectedCustomerFilterId, selectedCSMId]);

  useEffect(() => {
    let CSMData;
    const fetchData = async () => {
      const { customerData, customerIDName } = await fetchCustomersList(props);
      setCustomersList(customerData);
      setAllCustomerIDName(customerIDName);
      if (props.client_id === 1) {
        CSMData = await fetchCSMList();
        setCSMList(CSMData);
      }
      setPageLoading(false);
    };
    fetchData();
  }, []);

  const handleHelpSectionURLClick = () => {
    if (props.application_id !== 12) {
      window.open("https://iot-docs.datoms.io/docs/datoms/login", "_blank");
    } else {
      window.open(
        "https://docs.datoms.io/docs/datoms/Customers/overview",
        "_blank",
      );
    }
  };

  const handleLoadMoreClick = () => {
    ticketSearchPage += 1;
    const totalPagesToSearch = Math.ceil(totalTickets / 30);
    if (ticketSearchPage > totalPagesToSearch) {
      setEndOfTicketList(true);
      return;
    }
    let filteredTickets = [];
    const getTickets = async () => {
      filteredTickets = await getSearchedTickets();
      if (filteredTickets?.length > 0) {
        let currTickets = ticketDetails;
        currTickets.pop();
        setTicketDetails(getUniqueTicketList(currTickets, filteredTickets));
      }
    };
    if (!endOfTicketList) {
      setIsLoading(true);
      getTickets();
    }
  };

  const getSearchedTickets = async () => {
    let filteredTickets: TicketProps[] = [];
    if (searchQuery.length > 0) {
      setIsLoading(true);
      for (let i = ticketSearchPage; i <= Math.ceil(totalTickets / 30); i++) {
        let fetchQuery = `?created_from_time=${fromTime}&created_upto_time=${uptoTime}&status=${status}&page=${i}`;
        if (props.client_id === 1)
          fetchQuery += `&customer=${selectedCustomerFilterId}&csm=${selectedCSMId}`;
        const fetchData = async () => {
          const ticketSearchResponse = await getTickets(clientID, fetchQuery);
          filteredTickets = filterTickets(
            ticketSearchResponse?.tickets,
            searchQuery,
          );
        };
        await fetchData();
        if (
          filteredTickets.length > 0 &&
          i !== ticketSearchPage &&
          i <= Math.ceil(totalTickets / 30)
        ) {
          // setTicketSearchPage(i);
          ticketSearchPage = i;
          break;
        }
      }
    }
    setIsLoading(false);
    return filteredTickets;
  };

  useEffect(() => {
    if (!searchQuery.length) {
      setSearchMode(false);
      return;
    }
    setSearchMode(true);
    // setTicketSearchPage(1);
    ticketSearchPage = 1;
    setEndOfTicketList(false);
    setIsLoading(true);
    setTicketDetails([]);
    let filteredTickets = [];
    const getTickets = async () => {
      filteredTickets = await getSearchedTickets();
      if (filteredTickets?.length > 0) setTicketDetails(filteredTickets);
    };
    if (searchInterval) {
      clearInterval(searchInterval);
    }
    searchInterval = setTimeout(async () => {
      await getTickets();
    }, 1500);
  }, [
    searchQuery,
    fromTime,
    uptoTime,
    status,
    selectedCustomerFilterId,
    selectedCSMId,
  ]);

  return (
    <div id="ticket-support" className="basic-page-layout-height">
      {pageLoading ? (
        <Skeleton active paragraph={{ rows: 6 }} />
      ) : currentURL.includes("ticket_id") ? (
        <TicketThread
          currentTicketId={currentTicketId}
          clientID={clientID}
          prevPage={`?filter=date_range:${fromTime}-${uptoTime},status:${status},page=${ticketPage}${props.client_id === 1 ? `,customer=${selectedCustomerFilterId},csm=${selectedCSMId}` : ""}`}
          isMobileScreen={isMobileScreen}
          userTimeZone={userTimeZone}
          application_id={props.application_id}
          customersList={customersList}
          CSMList={CSMList}
          allCustomerIDName={allCustomerIDName}
          enabledFeatures={props.enabled_features}
        />
      ) : (
        <>
          <div className="ticket-info">
            <div
              className={
                isMobileScreen
                  ? "ticket-support-header-mobile"
                  : "ticket-support-header"
              }
            >
              <div className="header-icon">
                <img
                  src={Support_Ticket_Illustration}
                  alt="Support Ticket Illustration"
                />
              </div>
              <div className="header-content">
                <div className="ticket-support-title">Support Tickets</div>
                <div className="ticket-support-subtitle-1">
                  Facing any issues? Just open a support ticket, and we'll take
                  it from there.
                </div>
                {!isMobileScreen && (
                  <div className="ticket-support-subtitle-2">
                    Explore our support knowledge base in the
                    <div
                      className="ticket-support-help-section"
                      onClick={handleHelpSectionURLClick}
                    >
                      Help section!{" "}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className = {isMobileScreen ? "ticket-support-options-mobile" : "ticket-support-options"}>
              <GenericFilter
                isFlexWrap
                hideIconCount
                backgroundWhite
                history={props.history}
                url={getBaseUrl(props, "tickets")}
                width={isMobileScreen ? undefined : 525}
                default={["", ""]}
                filterData={filterData}
                panelFilterSelect={(
                  value: string | number,
                  key: string | number,
                ) => applyFilterSelect(value, key)}
                onUrlChange={onUrlChange}
                searchObject={{
                  placeholder: "Search by id, subject or description",
                  value: searchQuery,
                  size: "default",
                }}
              />
              {clientID !== 1 && (
                <div className="create-ticket-btn">
                <AntButton type="primary" onClick={handleCreateTicket}>
                  Create a Ticket
                </AntButton>
                </div>
              )}
            </div>
            {showTicketCreatedPrompt && (
              <div
                className="ticket-created-prompt"
                style={{ marginTop: isMobileScreen ? 16 : 8 }}
              >
                Newly created tickets will be reflected shortly.
              </div>
            )}
            <div
              className="ticket-list-container"
              style={{
                marginTop: totalTickets ? "0px" : "16px",
                marginBottom: isMobileScreen ? "60px" : "30px",
              }}
            >
              <TicketsList
                ticketDetails={ticketDetails}
                totalTickets={totalTickets}
                ticketsPerPage={30}
                setTicketPage={setTicketPage}
                isLoading={isLoading}
                isMobileScreen={isMobileScreen}
                client_id={clientID}
                customersList={customersList}
                CSMList={CSMList}
                ticketPage={ticketPage}
                allCustomerIDName={allCustomerIDName}
                searchMode={searchMode}
                handleLoadMoreClick={handleLoadMoreClick}
                endOfTicketList={endOfTicketList}
                searchQuery={searchQuery}
              />
              {endOfTicketList && searchMode && (
                <div className="end-of-ticket-list">
                  You have reached the end of the list!
                </div>
              )}
            </div>
          </div>
          {isDrawerOpen && (
            <TicketCreation
              isDrawerOpen={isDrawerOpen}
              handleCancel={handleCancel}
              handleOk={handleOk}
              clientID={clientID}
              isMobileScreen={isMobileScreen}
              setShowTicketCreatedPrompt={setShowTicketCreatedPrompt}
              application_id={props.application_id}
              customerOptions={customersList}
              enabledFeatures={props.enabled_features}
            />  
          )}
          
        </>
      )}
    </div>
  );
};

export default withRouter(TicketingSupport);
