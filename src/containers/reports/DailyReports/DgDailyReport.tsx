import GravityReport from "../../../components/configurable/compositeComponents/GravityReport";
import DgDailyReportOld from "@datoms/dg-monitoring-views/src/js/features/Reports/DGReport/pages/DailyReports";

interface Props {
  application_id: number;
  client_id: number;
  vendor_id: number;
}

const DgDailyReport = (props: Props) => {
  const getPageId = () => {
    if (props.client_id === 16486) {
      return 58;
    }
    if (props.vendor_id === 1140) {
      return 9;
    }
    return 8;
  };

  return (
    <div>
      {props.vendor_id === 1140 ? (
        <DgDailyReportOld {...props} />
      ) : (
        <GravityReport {...props} page_id={getPageId()} />
      )}
    </div>
  );
};

export default DgDailyReport;
