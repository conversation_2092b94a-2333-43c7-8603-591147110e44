import GravityReport from "../../../components/configurable/compositeComponents/GravityReport";

interface Props {
  application_id: number;
  client_id: number;
  vendor_id: number;
}

const DgMultiAssetReport = (props: Props) => {
  const getPageId = () => {
      if (props.client_id === 16486) {
        return 59;
      }
      if (props.vendor_id === 1140) {
        return 18;
      }
      return 10;
    };
  
    return (
      <div>
        <GravityReport {...props} page_id={getPageId()} />
      </div>
    );
};

export default DgMultiAssetReport;
