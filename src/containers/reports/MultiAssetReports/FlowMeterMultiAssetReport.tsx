import GravityReport from "../../../components/configurable/compositeComponents/GravityReport";
import { useGlobalContext } from "../../../store/globalStore";

interface Props {
  application_id: number;
  client_id: number;
  vendor_id: number;
}

const FlowMeterMultiAssetReport = (props: Props) => {
  const context = useGlobalContext();
  return (
    <div>
      <GravityReport {...props} page_id={context.client_id === 11382 ? 51 : 42} />
    </div>
  );
};

export default FlowMeterMultiAssetReport;
