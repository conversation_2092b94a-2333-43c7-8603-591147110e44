import GravityReport from "../../../components/configurable/compositeComponents/GravityReport";
import { useGlobalContext } from "../../../store/globalStore";

interface Props {
  application_id: number;
  client_id: number;
  vendor_id: number;
}

const GasGensetMultiAssetReport = (props: Props) => {
  const context = useGlobalContext();
  return (
    <div>
      <GravityReport {...props} page_id={context.client_id === 1184 ? 52 : context.vendor_id === 1184 ? 50 : 19} />
    </div>
  );
};

export default GasGensetMultiAssetReport;
