// @import '~antd/dist/antd.css';
// @import '../../node_modules/react-phone-input-2/lib/style.css';
// @import "antd/es/style/themes/default.less";
// @import "antd/dist/antd.less";

@import './CommonFiles/Template-1/menu.less';
@import './CommonFiles/Template-1/image-gallery.css';
@import './CommonFiles/Template-1/header.less';
@import './CommonFiles/Template-1/newheader.less';
@import './CommonFiles/Template-1/loading-spinner.css';
// @import './CommonFiles/Template-1/slick-style.css';
// @import './CommonFiles/Template-1/slick-theme.css';
@import './CommonFiles/Template-1/style.css';
@import './CommonFiles/Template-1/menu.less';
@import url('./NewCommonStyles/style.less');
@import './default-color.less';

@primary-color: #1cb855; 

[class*=ant-],[class*=ant-] *,[class*=ant-] :after,[class*=ant-] :before,[class^=ant-],[class^=ant-] *,[class^=ant-] :after,[class^=ant-] :before {
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

body,html {
    width: 100%;
    height: 100%
}

input::-ms-clear,input::-ms-reveal {
    display: none
}

*,:after,:before {
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

html {
    font-family: sans-serif;
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -ms-overflow-style: scrollbar;
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

@-ms-viewport {
    width: device-width
}

body {
    margin: 0;
    color: rgba(0,0,0,.85);
    font-size: 14px;
    font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";
    font-variant: tabular-nums;
    line-height: 1.5715;
    background-color: #fff;
    -webkit-font-feature-settings: "tnum","tnum";
    font-feature-settings: "tnum","tnum";
	overflow: hidden;
}

[tabindex="-1"]:focus {
    outline: none!important
}

h1,h2,h3,h4,h5,h6 {
    margin-top: 0;
    margin-bottom: .5em;
    color: rgba(0,0,0,.85);
    font-weight: 500
}

p {
    margin-top: 0;
    margin-bottom: 1em
}

dl,ol,ul {
    margin-top: 0;
    margin-bottom: 1em
}

ol ol,ol ul,ul ol,ul ul {
    margin-bottom: 0
}

img {
    vertical-align: middle;
    border-style: none
}

.ant-table-column-sorter-up.active, .ant-table-column-sorter-down.active {
	color: @defaultColor !important;
}

// .ant-input-affix-wrapper:hover {
//     border-color: @hoverColor !important;
// }

.ant-spin .ant-spin-dot-item {
	background-color: @defaultColor !important;
}

// .ant-input:hover, .ant-input:focus, .ant-input-focused {
// 	border-color: @hoverColor !important;
// 	box-shadow:  0 0 2px @hoverColor !important
// }

.ant-switch-checked {
    background-color: @defaultColor !important;
}

.ant-tabs .ant-tabs-content {
	position: unset;
}

.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn, .ant-tabs-tab:hover, .ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active, .ant-tabs-tab-btn:active {
	color: @defaultColor !important;
}

.ant-pagination-item-active {
	border-color: @defaultColor !important
}

.ant-tabs-ink-bar {
	background: @defaultColor !important;
}

.ant-picker:hover, .ant-picker-focused {
    border-color: @defaultColor !important;
}

.ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before {
    border: 1px solid @defaultColor !important;
}

.ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner, 
.ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner {
	background: @defaultColor !important;
}

.ant-picker-range .ant-picker-active-bar {
	background: @defaultColor !important;
}

.ant-checkbox:not(.ant-checkbox-disabled) .ant-checkbox-wrapper:hover .ant-checkbox-inner {
	border-color: @defaultColor !important;
}

.ant-menu-dark.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item-selected {
    background-color: @defaultColor !important;
}

.ant-radio-checked:focus {
	color: @defaultColor !important;
}

.ant-radio-wrapper .ant-radio-checked {
	.ant-radio-inner {
		border-color: @defaultColor !important;
		background-color: unset;
	}
	.ant-radio-inner::after {
		background-color: @defaultColor !important;
	}
}

.ant-checkbox:not(.ant-checkbox-disabled) .ant-checkbox-checked .ant-checkbox-inner {
	background-color: @defaultColor !important;
    border-color: @defaultColor !important;
}

.ant-select:not(.ant-select-disabled):hover .ant-select-selector {
	border-color: @defaultColor !important;
}

.offline {
	color: #565656;
}

.ant-progress-circle-path {
	stroke: @defaultColor !important;
}

.ant-btn[disabled],.ant-btn[disabled]:active,.ant-btn[disabled]:focus,.ant-btn[disabled]:hover {
    color: rgba(0,0,0,.25);
    background: #f5f5f5;
    border-color: #d9d9d9;
    text-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
	cursor: not-allowed;
}

.ant-btn-primary:hover {
    border-color: #FF9B06 !important;
    color: @defaultColor !important;
}

.ant-btn-primary {
	border-color: @defaultColor !important;
	background: @defaultColor !important;
}

.ant-btn-primary:hover {
    color: #fff !important;
	background-color: #FF9B06 !important;
}

.ant-collapse>.ant-collapse-item >.ant-collapse-header .ant-collapse-header-text {
	margin-inline-end: unset;
}

.ant-collapse-icon-position-end>.ant-collapse-item>.ant-collapse-header .ant-collapse-expand-icon {
    right: 16px;
    left: auto;
	position: absolute;
}

.ant-modal .ant-modal-header {
    padding: 16px 24px;
	margin-bottom: 0;
}

.ant-modal:not(.ant-modal-confirm) .ant-modal-content {
    padding: 0;
}

.ant-modal .ant-modal-body {
    padding: 24px;
}
.ant-modal .ant-modal-footer {
    padding: 10px 16px;
	margin-top: 0;
}
.ant-modal .ant-modal-footer button+button {
    margin-bottom: 0;
    margin-left: 8px
}
// .ant-select:hover {
// 	border-color: @defaultColor !important;
// }

.online {
	color: #1cb855;
}

// .ant-checkbox-checked .ant-checkbox-inner {
// 	background-color: #f58740 !important;
// 	border-color: #f58740 !important;
// }

.switch-off {
	color: #f65f54;
}

.hp-blue-shade {
	color: #374375;
}

.lp-blue-shade {
	color: #7686a1;
}

.hp-black-shade {
	color: #232323;
}

.pdf-warning {
	font-size: 12px;
	font-style: italic;
	color: #808080;
	margin-top: 30px;
	b {
		color: #232323;
		font-weight: 600;
	}
}

#google_translate_element {
	position: absolute;
	top: -24px;
	left: -9px !important;
	z-index: 99999;
}

#phone-form-control:hover {
	border-color: #ffab6b;
}

#phone-form-control:hover + #flag-dropdown {
	border-color: #ffab6b;
}

#phone-form-control:focus {
	border-color: #ffab6b;
	outline: 0;
	box-shadow: 0 0 0 2px rgba(245, 135, 64, 0.2);
}

.remote-lock-wrapper {
	margin-right: 7px;
}

#phone-form-control:focus + #flag-dropdown {
	border-color: #ffab6b;
	outline: 0;
}

#phone-form-control {
	width: inherit !important;
}

#phone-form-control:disabled {
	color: rgba(0, 0, 0, 0.25);
	background-color: #f5f5f5;
	cursor: not-allowed;
	opacity: 1;
}

#phone-form-control:disabled + #flag-dropdown {
	color: rgba(0, 0, 0, 0.25);
	background-color: #f5f5f5;
	cursor: not-allowed;
}

.ant-menu-submenu-selected {
	.ant-menu-submenu-title {
		color: #f58740 !important;
	}
}

.goog-te-gadget {
	font-family: Roboto, 'Open Sans', sans-serif !important;
	text-transform: uppercase;
}

.goog-logo-link {
	display: none !important;
}

.goog-te-gadget {
	color: transparent !important;
}

.goog-te-banner-frame.skiptranslate {
	display: none !important;
}

.goog-te-menu-value {
	color: #fff !important;
	&:before {
		font-family: 'Material Icons';
		content: '\E927';
		margin-right: 16px;
		font-size: 2rem;
		vertical-align: -10px;
		// width:32px!important;
	}
}

.goog-te-gadget {
	.goog-te-combo {
		color: #232323ab;
		border-radius: 4px;
		padding: 5px;
		height: 32px;
		width: 300px;
		border: 1px solid #ddd;
		cursor: pointer;
		outline: none;

		option {
			padding: 10px !important;
		}
	}
	.goog-te-combo:hover,
	.goog-te-combo:focus,
	.goog-te-combo:active {
		border-color: #ffab6b;
	}
}

.goog-te-gadget {
	div {
		border: 1px solid rgba(255, 255, 255, 0.5) !important;
		padding: 8px !important;
		border-radius: 4px !important;
		font-size: 1rem !important;
		line-height: 2rem !important;
		display: inline-block;
		cursor: pointer;
		zoom: 1;
	}
}

.goog-logo-link {
	display: none;
}

.slow-hide {
	opacity: 0;
	transition: 10s all;
}

.information-meaasge {
	margin-top: 25px;
	font: status-bar;
	color: orange;
	display: flex;
	align-items: center;

	.info-msg {
		color: #232323ab;
		margin-left: 8px;
	}
}

#google_translate_element_new {
	position: relative;
}

.graph-container {
	// padding: 20px;

	.highcharts-xaxis {
		text {
			tspan {
				color: #232323;
				fill: #232323;
			}
		}
	}

	// .highcharts-title {
	// 	tspan {
	// 		color: #232323;
	// 		font-weight: 500;
	// 		font-size: 16px;
	// 		fill: #232323;
	// 	}
	// }

	.highcharts-subtitle {
		tspan {
			color: #232323;
			font-weight: 500;
			font-size: 12px;
			fill: #232323;
		}
	}

	.highcharts-background {
		fill: #fff;
	}
}

.graph-container-no-data {
	transform: translate(-50%, -50%) !important;
	top: 50% !important;
	left: 50% !important;
	position: absolute !important;
	font-size: 20px;
	color: #808080;
	font-weight: 500;
}
.margin-top-20 {
	margin-top: 20px;
}

.margin-bottom-5 {
	margin-bottom: 5px;
}

.mar-lt-24 {
	margin-left: 24px !important;
}

.mar-lt-37 {
	margin-left: 37px !important;
}

.box-shadow-new {
	box-shadow: 9px 9px 16px #d7dbe0, -9px -9px 16px #ffffff !important;
	background-color: unset !important;
	border: 4px solid #ffffff96 !important;
}

.card-box-shadow-new {
	box-shadow: 9px 9px 16px #d7dbe0, -9px -9px 16px #ffffff !important;
	background-color: #e4eaf7 !important;
	border: 4px solid #ffffff96 !important;
}

.disabled-shadow {
	box-shadow: 9px 9px 16px #d7dbe0, -9px -9px 16px #ffffff !important;
	background-color: unset !important;
	border: 4px solid #ffffff96 !important;
}

.ant-legacy-form label {
	font-size: 13px !important;
}

.report-modal {
	.report-form {
		.section {
			margin-bottom: 10px;

			.label {
				font-weight: 500;
			}

			.checkbox-section {
				margin-left: 10px;

				.checkbox-group {
					margin-top: 10px;
					display: flex;
					flex-wrap: wrap;

					div {
						margin: 5px 0;
						width: 50%;
					}
				}

				.format {
					div {
						width: 30%;
					}
				}
			}
		}

		.button-container {
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 10px;
			margin-bottom: 15px;

			.button {
				padding: 10px;
				margin: 10px;
				cursor: pointer;
				box-shadow: 0px 0px 18px #d0d0d054;
				transition: all 0.5s;
				border-bottom-left-radius: 10px;
				border-top-right-radius: 10px;
				width: 100px;
				text-align: center;

				&:hover {
					box-shadow: 0px 0px 18px #d0d0d0;
				}
			}
		}
	}

	.ant-modal-footer {
		display: none !important;
	}
}

.parameter-label {
	color: rgba(0, 0, 0, 0.85);
	display: inline-block;
	overflow: hidden;
	white-space: nowrap;
	text-align: right;
	vertical-align: middle;
	margin-bottom: 10px;
}
.content-split-height-1 {
	height: calc(100vh - 52px);
	max-height: calc(100vh - 52px);
}
.content-split-height-2 {
	height: calc((100vh - 52px) / 2);
	max-height: calc((100vh - 52px) / 2);
}
.content-split-height-3 {
	height: calc((100vh - 52px) / 3);
	max-height: calc((100vh - 52px) / 3);
}
.content-split-height-4 {
	height: calc((100vh - 52px) / 4);
	max-height: calc((100vh - 52px) / 4);
}
.content-split-height-5 {
	height: calc((100vh - 52px) / 5);
	max-height: calc((100vh - 52px) / 5);
}

.no-data-icon {
	width: 100%;
	position: absolute;
	left: 50%;
	top: 50%;
	-webkit-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	text-align: center;
	padding: 0 15px;
}

#login_page {
	font-family: 'Roboto';
	height: 100vh !important;
	margin: 0 auto;
	font-size: 16px;
	overflow: hidden;
	color: #232323;

	.login-container {
		/* background: -webkit-gradient(linear, left bottom, left top, from(#f58740), to(#f06352)); */
		/* background: -webkit-linear-gradient(bottom, #f58740, #f06352); */
		/* background: linear-gradient(0deg, #f58740, #f06352); */
		width: 100%;
		height: 100vh;
		overflow: hidden;
		display: table;
		margin: 0 auto;

		.back-img {
			position: absolute;
			height: 100vh;
			width: 350px;
			z-index: 2;
			background-image: url(https://static.datoms.io/images/dg-login-bg.jpg);
			background-size: cover;
		}

		.form {
			/* width: 30%; */
			max-width: 350px;
			min-width: 60px;
			/* position: relative; */
			/* right: -60%; */
			z-index: 3;
			display: table;
			margin: auto;
			margin-top: 70px;

			.panel {
				margin: 0 auto;
				border-radius: 10px;
				position: relative;

				.front {
					position: relative;
					// top: 0;
					margin-top: 50%;
					z-index: 900;
					transition: all 0.4s ease-in-out;
					width: 100%;

					.form-sec {
						height: 100%;
						margin: 0 auto;
						padding: 20px 30px;
						background-color: #ffffffbf;
						border-radius: 10px;

						.head {
							font-size: 25px;
							text-align: left;
							margin-bottom: 20px;
							font-family: Lora, serif;
						}

						.second-head {
							font-weight: 500;
							margin-bottom: 50px;
							font-size: 12px;
							line-height: 1.5;
							color: grey;
						}

						.form-container {
							margin-bottom: 20px;
							font-size: 14px;

							.header-text {
								text-align: left;
								margin-bottom: 10px;
								font-size: 13px;
							}

							.form-control {
								display: block;
								width: calc(100% - 10px);
								padding-left: 10px;
								height: 35px;
								font-size: 14px;
								line-height: 1.42857143;
								color: grey;
								background-image: none;
								border: 1px solid grey;
								border-radius: 4px;
								box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
								transition: border-color ease-in-out 0.15s,
									box-shadow ease-in-out 0.15s;
								font-family: Quicksand, sans-serif !important;

								&:focus {
									border: 1px solid #f58740;
									box-shadow: none;
									outline: 0;
									background-color: #fff;
								}
							}

							.check {
								margin-right: 5px;
							}
						}

						.form-container.mar-top-30 {
							margin-top: 30px;
						}

						.error-msg {
							font-size: 12px;
							margin-top: -10px;
							position: absolute;
							margin-left: 4px;
							color: red;
						}

						.btn-contain {
							text-align: center;

							.btn {
								line-height: 10px;
								background: linear-gradient(
									315deg,
									#f58740,
									#f06352
								);
								color: #fff;
								border-radius: 20px;
								border: none;
								box-shadow: none;
								width: 100%;
								padding: 10px;
								margin-top: 50px;
								-webkit-font-feature-settings: 'pcap', 'c2pc';
								font-feature-settings: 'pcap', 'c2pc';
								font-variant: all-petite-caps;
								letter-spacing: 2px;
								cursor: pointer;
								font-size: 16px;

								&:focus {
									box-shadow: none;
									outline: 0;
								}

								&:disabled {
									cursor: not-allowed;
									opacity: 0.75;
								}
							}
						}
						#forgot_pswd {
							cursor: pointer;
							margin-top: 20px;
							text-align: center;
							font-size: 14px;

							a {
								text-decoration: none;
								color: #c4c2c2;
								transition: all 0.5s;

								&:hover {
									color: #f06352;
								}
							}
						}
						#back_login {
							cursor: pointer;
							margin-top: 20px;
							text-align: center;
							font-size: 14px;

							a {
								text-decoration: none;
								color: #c4c2c2;
								transition: all 0.5s;

								&:hover {
									color: #f06352;
								}
							}
						}
					}
				}
			}
		}

		.datoms-logo {
			position: absolute;
			bottom: 30px;
			right: 30px;
			width: 15%;
			opacity: 0.5;

			img {
				width: 100%;
			}
		}

		.copyright {
			position: absolute;
			left: 20px;
			bottom: 20px;
			color: #fff;
			opacity: 0.5;
		}
	}

	.mar-top-20 {
		margin-top: 20px !important;
	}
}

#ant-header-parent{
	display: flex;
	flex-direction: column;
	height: 76px !important;

	.compliance-banner-container{
		height: 30px;
		width: 100%;
	}
}

.basic-page-layout-height{
	height: calc(100vh - 52px) !important;
	height: calc(100svh - 52px) !important;
}

.ant-drawer{
	&:focus-visible {
		outline: none;
	}
	.ant-drawer-header-title {
	flex-direction: row-reverse;
	.ant-drawer-close{
		margin-right: 0;
		margin-left: 12px;
	}
}}

.ant-dropdown-button>.anticon.anticon-down,.ant-dropdown-link>.anticon.anticon-down,.ant-dropdown-trigger>.anticon.anticon-down {
    vertical-align: initial;
    display: inline-block;
    font-size: 10px
}
@media screen and (max-width: 550px) {
	.graph-container-no-data {
		font-size: 14px !important;
	}
	#ant-header-parent{
		height: 82px !important;
	}
}
@media screen and (max-width: 576px) {
	.basic-page-layout-height{
		height: calc(100vh - 52px - 55px) !important;
		height: calc(100svh - 52px - 55px) !important;
	}
}

@media (max-width: 576px) {
	.ant-picker-dropdown .ant-picker-date-panel {
		width: 260px;
	}
}