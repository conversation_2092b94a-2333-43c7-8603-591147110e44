body {
	background-color: #fff !important;
	color: #232323 !important;
	height: auto !important;
	font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen,
		Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
	font-size: 13px;
}
::-webkit-scrollbar {
	width: 7px;
}
::-webkit-scrollbar-thumb {
	border-radius: 10px;
	background-color: #dfdfdf;
}
::-webkit-scrollbar-track {
	border-radius: 10px;
	background-color: #f2f8f9;
}
::-webkit-scrollbar-thumb:hover {
	background-color: #c1c1c1;
}
.ant-table-content::-webkit-scrollbar {
	height: 5px;
}
.ant-tooltip-content {
	font-size: 12px;
}
.highcharts-contextbutton {
	display: none !important;
}
.ant-calendar-range.ant-calendar-time .ant-calendar-time-picker-select {
	width: 50% !important;
}
.ant-calendar-range.ant-calendar-time
	.ant-calendar-time-picker-select:last-child {
	display: none;
}
.no-feature-access {
	background: transparent
		url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 44 44'><path d='M43.7.3A1 1 0 0 0 43 0H1.1a1 1 0 0 0-.8.3L.1.6 0 1c0 .*******.7L15 22.3v21.1c.******* 1 .6.3 0 .6-.1.7-.3l12-12c.3-.2.3-.5.3-.8v-8.6L43.7 1.7c.4-.4.4-1 0-1.4zm-16.4 21a1 1 0 0 0-.3.7v8.6l-10 10V22c0-.2 0-.5-.3-.7L3 2h38.2L27.3 21.3z'></path></svg>")
		no-repeat 100%;
}
.hide {
	display: none !important;
}
.display-flex {
	display: flex !important;
	align-items: center !important;
}
.msg-container {
	font-size: 20px;
	text-align: center;
	color: #909090;
}
.msg-container .access-icon {
	color: #ff00007a;
	margin-bottom: 10px;
}
.ant-spin-dot-item {
	background: #f58740;
}
.user-icon {
	border-radius: 50%;
	margin-right: 5px;
	background: #ddd;
	/* height: 40px; */
	/* width: 40px; */
	padding: 8px 13px 9px 14px;
}
.disabled {
	cursor: not-allowed !important;
	opacity: 0.8;
}
a {
	color: inherit !important;
	text-decoration: none !important;
}
.opacity-half {
	opacity: 0.5 !important;
}
.display-none {
	display: none !important;
}
.text-rt {
	text-align: right;
}
.medium-text-col {
	color: #808080 !important;
}
.light-text-col {
	color: #c4c2c2 !important;
}
.shadow {
	box-shadow: -14px 12px 18px rgba(208, 208, 208, 0.329) !important;
}
.rotate-180 {
	transform: rotate(180deg);
	transition: all 0.5s;
}
.border-bot {
	border-bottom: 1px solid #e8e8e8 !important;
}
.hellip {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
	-webkit-appearance: none;
	margin: 0;
}
.mar-top-5 {
	margin-top: 5px !important;
}
.align-center-loading {
	transform: translate(-50%, -50%) !important;
	top: 50% !important;
	left: 50% !important;
	position: absolute !important;
}
.mar-lt-10 {
	margin-left: 10px !important;
}
.mar-lt-20 {
	margin-left: 20px !important;
}
.mar-lt-30 {
	margin-left: 30px !important;
}
.mar-rt-10 {
	margin-right: 10px !important;
}
.mar-rt-20 {
	margin-right: 20px !important;
}
.mar-rt-30 {
	margin-right: 30px !important;
}
.mar-top--5 {
	margin-top: -5px !important;
}
.mar-top-10 {
	margin-top: 10px !important;
}
.mar-top-20 {
	margin-top: 20px !important;
}
.mar-top-30 {
	margin-top: 30px !important;
}
.mar-top-40 {
	margin-top: 40px !important;
}
.mar-top-60 {
	margin-top: 60px !important;
}
.mar-bot-0 {
	margin-bottom: 0px !important;
}
.mar-bot-10 {
	margin-bottom: 10px !important;
}
.mar-bot-20 {
	margin-bottom: 20px !important;
}
.mar-bot-30 {
	margin-bottom: 30px !important;
}
.mar-bot-40 {
	margin-bottom: 40px !important;
}
.mar-bot-60 {
	margin-bottom: 60px !important;
}
.bdr-top {
	border-top: 1px solid #ddd;
}
.pad-top-20 {
	padding-top: 20px !important;
}
.pad-top-10 {
	padding-top: 10px !important;
}
.pad-bot-10 {
	padding-bottom: 10px !important;
}
.font-600 {
	font-weight: 600 !important;
}
.font-20 {
	font-size: 20px !important;
}
.font-16 {
	font-size: 16px !important;
}
.font-weight-500 {
	font-weight: 500 !important;
}
.font-weight-600 {
	font-weight: 600 !important;
}
.font-weight-normal {
	font-weight: normal;
}
.clear {
	clear: both;
}
.pointer {
	cursor: pointer;
}
.border-curve {
	border: 1px solid #ddd;
}
.height-100-percent {
	height: 100% !important;
}
.height-100-60-percent {
	height: calc(100% - 60px) !important;
}
.height-100-92-percent {
	height: calc(100% - 92px) !important;
}
.color-000 {
	color: #000 !important;
}
.font-lighter {
	color: #c4c2c2 !important;
}
.dspl-flex {
	display: flex !important;
	allign-items: center !important;
}
.dspl-inline-blk {
	display: inline-block !important;
}
.dspl-inline-flx {
	display: inline-flex !important;
}
.space-betn {
	justify-content: space-between;
}
.ant-tag {
	cursor: default !important;
}
.green {
	color: #1da57a !important;
}
.green-back {
	background-color: #1da57a !important;
}
.orange {
	color: #ff7d3f !important;
}
.orange-back {
	background-color: #ff7d3f !important;
}
.red {
	color: #f00 !important;
}
.red-back {
	background-color: #f00 !important;
}
.click-tag {
	cursor: pointer !important;
	transition: all 0.3s;
}
.click-tag:hover {
	color: #ff7d3f;
	border-color: #ff7d3f;
}
.close-row {
	cursor: pointer;
	transition: all 0.3s;
}
.close-row:hover {
	color: #f00;
}
.save-row {
	cursor: pointer;
	transition: all 0.3s;
	margin-left: 10px;
}
.save-row:hover {
	color: #1da57a;
}
.add-new {
	cursor: pointer;
	transition: all 0.3s;
	color: #ff7d3f;
	opacity: 0.5;
}
.add-new:hover {
	opacity: 1;
}
.alert-error {
	border: 1px solid #ffa39e;
	background-color: #fff1f0;
}
.alert-warning {
	border: 1px solid #ffe58f;
	background-color: #fffbe6;
}
.alert-info {
	border: 1px solid #91d5ff;
	background-color: #e6f7ff;
}
.alert-success {
	border: 1px solid #b7eb8f;
	background-color: #f6ffed;
}
.center-error {
	margin: 0 auto;
	text-align: center;
	width: calc(100% - 200px);
	margin-left: 200px;
}
.anticon.anticon-cross-circle-o.ant-notification-notice-icon.ant-notification-notice-icon-error {
	margin-top: 4px;
}
.ant-layout {
	background-color: transparent !important;
}
.data-err-page {
	margin: auto;
	margin-top: 50px;
	width: 750px;
	padding: 20px;
	font-size: 18px;
	text-align: center;
	color: #5d5d5d;
	background-color: #fff;
	box-shadow: 2px 2px 4px -2px rgba(0, 0, 0, 0.5);
	z-index: 9999;
}
.data-err-page-img {
	margin: auto;
	height: 85px;
	width: 85px;
}
.redirect-home {
	cursor: pointer;
	font-size: 15px;
	padding: 2px 20px;
	line-height: 28px;
	letter-spacing: 1px;
	color: #fff;
	border: none;
	outline: none;
	background: #ff8500;
	transition: all ease-in-out 0.5s;
}
.err-btn {
	margin-top: 50px;
}
.background {
	top: 0;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	position: absolute;
	background-color: #fff;
}
.background {
	background-color: #f2f2f2;
}
.err-msg {
	color: #f00;
	text-align: center;
	font-size: 20px;
}
.loading {
	top: 0;
	width: calc(100% - 260px);
	height: 100%;
	display: flex;
	align-items: center;
	position: absolute;
	background-color: #fff;
}
.loading .loading-spinner {
	margin: auto;
}
.inline-loading {
	padding: 30px 0;
	text-align: center;
}
.loading.collapsed-side {
	width: calc(100% - 140px);
}
.inline-loading.collapsed-side {
	width: calc(100% - 140px);
}
.no-data-text-container {
	width: 100%;
}
.no-data-text {
	color: #999;
	height: 100%;
	font-size: 24px;
	text-align: center;
	display: flex !important;
	justify-content: center;
	padding: 30px 0;
}
.no-data-text center {
	display: table;
	margin: auto;
}
.center {
	display: table;
	text-align: center;
}
.border-radius-crisp {
	border-radius: 0 !important;
}
.date-time-input {
	width: 160px;
	display: inline-block;
	margin-left: 20px;
}
.title-span {
	font-size: 18px;
}
.bold {
	font-weight: bold;
}
.no-label {
	margin-top: 29px !important;
}
.normal {
	font-weight: normal !important;
}
.font-600 {
	font-weight: 600;
}
.text-center {
	text-align: center !important;
}
.mar-top-84 {
	margin-top: 84px !important;
}
.compressed-table .ant-table-thead > tr.ant-table-row-hover > td,
.compressed-table .ant-table-tbody > tr.ant-table-row-hover > td,
.compressed-table .ant-table-thead > tr:hover > td,
.compressed-table .ant-table-tbody > tr:hover > td {
	background: #ffe2bd !important;
}
.compressed-table .tr-backgound-change {
	background: #fff7f0;
}

/* Check once before deployment*/
.ant-table-thead > tr > th {
	background: #f2eeee !important;
}
.ant-table-header {
	overflow-y: auto !important;
}

/* .ant-table-body > .ant-table-tbody {
  font-size: 12.5px !important;
} */

.please-wait-text {
	position: absolute;
	top: 50%;
	left: 50%;
}

.ant-tag {
	cursor: default !important;
}
.anticon.anticon-cross-circle-o.ant-notification-notice-icon.ant-notification-notice-icon-error {
	margin-top: 4px;
}
.ant-layout {
	background-color: transparent !important;
}
.ant-drawer-content-wrapper {
	transition: all 0.5s !important;
	box-shadow: 3px 3px 12px #b5b5b5 !important;
}
.ant-layout-sider-trigger {
	transition: all 0.5s !important;
}
.ant-table-content {
	overflow-x: auto;
}
.ant-drawer-wrapper-body {
	overflow: hidden !important;
}
.ant-tabs {
	color: #232323 !important;
}
.ant-drawer-close-x {
	font-size: 26px !important;
}
.ant-btn-primary {
	background: linear-gradient(315deg, #f58740, #f06352) !important;
	border-color: transparent;
}
.ant-legacy-form-item-children .ant-input,
.ant-legacy-form-item-children .ant-select {
	font-size: 13px !important;
}
/* .ant-table-body::-webkit-scrollbar {
	height: 3px;
} */
.ant-tabs.ant-tabs-card > .ant-tabs-bar .ant-tabs-tab {
	border-top-left-radius: 10px !important;
	border-top-right-radius: 10px !important;
}
.ant-popover-message-title {
	padding-left: 0 !important;
}
.ant-drawer-close-x {
	margin-top: 4px !important;
}
.ant-legacy-form {
	width: 100% !important;
}
.transparent-loading {
	width: 100%;
	height: 100vh;
	position: absolute;
	background: #0003;
	z-index: 4;
}
.not-visible {
	display: none !important;
}

.block-icon {
	cursor: not-allowed;
}

.block-icon:hover {
	color: inherit;
}

.btn-block {
	width: 50px;
	right: 30px;
	display: flex;
	align-items: center;
	position: absolute;
	overflow: hidden;
	transition: 0.5s all;
}
.btn-block > .width-control-blocked-add {
	cursor: not-allowed;
	padding: 0 5px;
	border-radius: 30px;
	background: #edf1f2;
	width: auto;
	font-size: 12px;
	padding: 4.5px 0px;
	text-align: left;
	margin-left: 0px;
	transition: 0.5s all;
	margin-left: 5px;
	height: 40px;
	width: 38px;
}

.btn-block > .width-control-blocked-add > .blocked-add-btn {
	cursor: not-allowed;
	font-size: 20px;
	padding: 0 5px;
	border-radius: 30px;
	color: #808080;
	background: #d6d6d6;
	transition: 0.5s all;
	padding: 7px 9px;
	margin-left: -1px;
}

.block-butn {
	background: #edf1f2 !important;
	cursor: not-allowed !important;
}

.block-butn:hover {
	background: #edf1f2 !important;
	color: rgba(0, 0, 0, 0.65) !important;
	border-color: #d9d9d9 !important;
}

.block {
	cursor: not-allowed !important;
}

.block:hover {
	color: unset !important;
}

@media screen and (max-width: 425px) {
	.wid-100 {
		width: 100% !important;
	}
	.loading {
		margin-left: 0;
		width: 100%;
	}
	.center-error {
		margin: auto;
	}
	.center-error .data-err-page {
		width: 420px;
	}
	.flex {
		display: flex !important;
		align-items: center;
	}
}