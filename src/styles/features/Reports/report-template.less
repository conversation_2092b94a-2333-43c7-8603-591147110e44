#archive_templates_page {
  background-color: #fff;
  min-height: 100vh;

  .contains {
    // margin-top: 53px;
    margin-left: 0px;
    transition: 0.5s;
    background: #fff;
    margin-right: 0;

    .reports-template-page {
      // margin-top: 20px;

      .header-section {
        font-size: 15px;
        height: 50px;
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        border-bottom: 2px solid #f9f2e2;
        padding: 22px;
        padding-bottom: 20px;
        background-color: #f9f2e2;

        .reports-head-with-search {
          display: flex;
          align-items: center;

          .archive-page-header {
            // background-color: #fff;
            display: inline;
            font-weight: 600;
            padding: 6px;
            padding-left: 12px;
            padding-right: 12px;
            // border-bottom: 1.4px solid #000;
          }

          .reports-template-search {
            width: auto;
            display: inline-block;
            padding-bottom: 0px;
            margin-right: 10px;
            // margin-bottom: 15px;
            margin-bottom: -6px;
            transition: 0.75s all;
            margin-left: 30px;

            .filter-search-display {
              border-radius: 8px;
              border: 1px solid transparent;
              transition: 0.5s all;

              input {
                height: 28px;
                font-size: 13px;
                background-color: #edf1f2;
                border: 1px solid transparent;
                border-radius: 8px;
                transition: 0.5s all;
                width: 15vw;
                font-weight: 700 !important;

                &:hover {
                  background-color: #edf1f2b3;
                  border-color: #1da57ae3;
                }
                &::placeholder {
                  font-size: 12px !important;
                  font-weight: normal !important;
                }

                &:focus,
                &:active {
                  background-color: rgba(237, 241, 242, 0.2);
                  border-color: #1da57a;
                  box-shadow: unset;
                  // width: 35vw;
                }
              }

              i {
                font-size: 12px;
                color: rgba(0, 0, 0, 0.75);
                font-weight: 600;
                margin-top: 1px;
              }
            }

            .active-search {
              input {
                background-color: rgba(237, 241, 242, 0.2);
                border-color: #1da57a;
                font-weight: 500;
              }
            }

            .ant-input-group-addon {
              height: 28px;
              border: 1px solid transparent;
              border-radius: 8px;
              outline: none;
              padding: 0;
              border: none;

              .addon-filter-type-class {
                border: 1px solid transparent;
                height: 28px;
                border-radius: 8px;
                font-size: 12px;
                width: 0 !important;
                overflow: hidden;
                padding: 0;
                margin: 0;
                border: none;
              }
            }
          }
        }

        .back-to-reports-page {
          background: #ffffffb3 /* #c1bdbd14 */;
          display: block;
          float: right;
          border-radius: 20px;
          padding: 3px 14px;
          border: 1px solid transparent /* #8080804f */;
          cursor: pointer;
          cursor: not-allowed;
          color: #2323237d;
        }
      }

      .reports-container {
        //height: calc(100% - 100px);
        height: calc(100vh - 52px);
        padding: 10px 30px;
        overflow: auto;
        display: block;

        .report-type-custom {
          display: inline-flex;
          align-items: center;
          margin-bottom: 15px;
          /* margin-left: 10px; */
          cursor: pointer;
          width: 200px;
          background-color: #ddd;
          border-radius: 10px;
          padding: 2px 10px;

          .report-type-custom-icon {
            margin-bottom: 4px;
            margin-right: 8px;
          }

          .report-type-custom-text {
            line-height: 30px;
            font-size: 14px;
            font-weight: 500;
            color: #fff;
          }
        }
        .report-template-header {
          font-weight: 600;
          margin-top: 20px;
          margin-left: 10px;
        }
        .template-type-section {
          margin-bottom: 20px;

          .template-type-section-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            margin-left: 10px;

            .template-type-section-icon {
              margin-bottom: -5px;
              margin-right: 3px;

              svg {
                fill: #0000ff9c;
              }
            }

            .template-type-section-text {
              font-size: 16px;
              font-weight: 500;
              margin-left: 5px;
              color: #004083;
            }
          }

          .template-type-section-body {
            .template-type-section-body-row {
              display: flex;
              align-items: center;
              margin-top: 15px;
              cursor: pointer;
              color: rgb(55, 67, 117);

              .template-type-section-body-icon {
                margin-bottom: -6px;
              }

              .template-type-section-body-text {
                font-size: 14px;
                font-weight: normal;
                margin-left: 10px;
                margin-top: 9px;
              }
            }
          }
        }

        .add-new-template {
          margin-bottom: 30px;

          .add-new-template-btn-text-container {
            display: flex;
            align-items: center;
            width: 200px;
            cursor: pointer;

            .add-new-template-btn {
              .anticon {
                vertical-align: -2.5px !important;
              }
            }

            .add-new-template-text {
              margin-left: 20px;
              font-size: 14px;
              color: rgb(55, 67, 117);
            }
          }
        }
      }

      .template-menu-devider {
        height: 1.2em !important;
        margin: 0 10px !important;
      }

      .template-application-select,
      .template-category-select {
        width: 200px !important;
        margin-left: 12px !important;

        .ant-select-selection {
          height: 26px !important;
          font-size: 12px !important;

          .ant-select-selection__rendered {
            line-height: 25px !important;
          }
        }
      }

      .template-category-select {
        margin-left: 10px !important;
      }

      .category-devider {
        margin-left: 20px !important;
      }
    }
  }

  .contains.collapsed-side {
    margin-left: 80px !important;
    transition: 0.5s;
  }

  .margin-bottom-4px {
    margin-bottom: 4px !important;
  }
}

@media (max-width: 576px) {
  #archive_templates_page {
    .contains {
      margin-left: 0px !important;
      .reports-template-page {
        .reports-container {
          padding: 0px !important;
          height: calc(100vh - 107px);
          .custom-report-container {
            padding: 20px;
            background: transparent
              linear-gradient(90deg, #fff4ec 0%, #f2f5f9 100%) 0% 0% no-repeat
              padding-box;
            .report-type-custom {
              background: #ffffff;
              margin-bottom: 0px;
              width: 100%;
              padding: 5px 20px;
            }
          }
          .report-template-header {
            margin-left: 40px !important;
          }
          .template-type-section
            .template-type-section-body
            .template-type-section-body-row {
            margin-left: 40px;
            color: #808080;
            font-weight: normal !important;
          }
        }
      }
    }
  }
}
