import "./styles/style.less";
import { ConfigProvider } from "antd";
import StopOutlined from "@ant-design/icons/StopOutlined";
import React, { Suspense } from "react";
import _find from "lodash/find";
import _filter from "lodash/filter";
import _intersection from "lodash/intersection";
import AlertObjectData from "./js/configuration/AlertObjectData";
import Loading from "@datoms/react-components/src/components/Loading";
import { singleAppProjectRoutes } from "./routes/OldDGRoutes/App";
import { iotPlatformDatomsXRoutes } from "./routes/DatomsxPartnerRoutes/App";
import { GenericRoutes } from "./routes/EndCustomerRoutes/App";
import { StoreRoutes } from "./routes/StoreRoutes/App";
import "./styles/single-app-style-fix.less";
import Layout from "./layout";
import { LoadScript } from "@react-google-maps/api";
import { useGlobalContext } from "./store/globalStore";

const Impact = React.lazy(
  () => import("./packages/iot-platform-views/src/js/DatomsX/Impact"),
);

const allApps = [
  "dg-monitoring",
  "pollution-monitoring",
  "delivery-tracking",
  "environment-monitoring",
  "iot-platform",
  "datoms-x",
];

const mapLibraries = ["geometry", "places", "drawing"];

interface AppProps{}

const App: React.FC<AppProps> = (props) => {
  const {
    collapsed,
    client_id,
    client_name,
    enabled_features,
    plan_description,
    app_name,
    availableThingCats,
    currentUserPreferences,
    setCurrentUserPreferences,
    unauthorised_access,
    unauthorised_access_msg,
    error_api_msg,
    user_preferences,
    user_id,
    application_id,
    is_white_label,
    template_id,
    client_logo,
    vendor_logo,
    vendor_id,
    user_name,
    head_side_object_data,
    customer_type,
    isRentalStore,
    logged_in_user_client_id,
    application_name,
    vendor_name,
    logged_in_user_name,
    logged_in_user_role_type,
    is_calibration_supported,
    things_list,
    is_iot_enabled,
    isGenericApp,
    isAurassure,
    menuUrlArray,
    setMenuUrl,
    getUserAccess,
    getViewAccess,
    getRemoteAccess,
    getRemoteLockAccess,
    checkEndCustomer,
    bannerToBeShown,
    onCollapseSider,
    viewDataAccess,
    supportedViewsPage,
    setStoreLogoutFunc,
    isLumenEnergy,
    isTataMotors,
    customizations,
    page_type,
    getExtraHeaderComps,
    onmenuClick,
    store_logout,
    setStoreLogout,
    mobileAppPackageName,
    showPolicyPage,
    parent_vendor_id,
    globalConfig,
    i18n,
    t,
    translationUrls,
    language
  } = useGlobalContext();


  let findThings = things_list?.things?.[0];

  if (
    client_id === 1 &&
    window.location &&
    window.location.href &&
    window.location.href.includes("/iot-platform")
  ) {
    window.location = "https://app.datoms.io";
    return <div className="please-wait-text">Please Wait...</div>;
  } else {
    let isMobilePageName = false;
    let checkCustomerFuelBuddy = checkEndCustomer(
        client_id,
        logged_in_user_client_id,
      ),
      singleAppRoutes = {},
      mainRoute = {};
    let authToken = "";
    let loadingLogo = window.location.href.includes("app.datoms.io");

    if (localStorage && localStorage.getItem("Auth-Token"))
      authToken = localStorage.getItem("Auth-Token");

    if ([12, 17].includes(application_id)) {
      singleAppRoutes = iotPlatformDatomsXRoutes(
        client_id,
        head_side_object_data,
        AlertObjectData,
        getViewAccess,
        enabled_features,
        props,
        application_id,
        application_name,
        client_name,
        client_logo,
        vendor_logo,
        user_id,
        user_preferences,
        collapsed,
        onCollapseSider,
        t,
        globalConfig,
        getRemoteAccess,
        vendor_id,
        logged_in_user_role_type,
        logged_in_user_client_id,
        app_name,
        customer_type,
        is_iot_enabled,
        viewDataAccess,
        setMenuUrl,
        supportedViewsPage,
        allApps,
        currentUserPreferences,
        setCurrentUserPreferences,
        availableThingCats ? availableThingCats : [],
        is_calibration_supported,
        translationUrls
      );
    } else if (isRentalStore) {
      isMobilePageName = true;
      singleAppRoutes = StoreRoutes(
        client_id,
        head_side_object_data,
        AlertObjectData,
        template_id,
        getViewAccess,
        getRemoteAccess,
        getRemoteLockAccess,
        enabled_features,
        plan_description,
        props,
        application_id,
        application_name,
        client_name,
        client_logo,
        vendor_logo,
        user_id,
        user_preferences,
        collapsed,
        t,
        globalConfig,
        app_name,
        vendor_id,
        logged_in_user_client_id,
        logged_in_user_role_type,
        authToken,
        checkCustomerFuelBuddy,
        is_white_label,
        vendor_name,
        loadingLogo,
        findThings,
        setMenuUrl,
        user_name,
        setStoreLogoutFunc,
        currentUserPreferences,
        setCurrentUserPreferences,
      );
    } else if (isGenericApp) {
      isMobilePageName = true;
      const finalVendorId = parent_vendor_id === 1819 ? parent_vendor_id : vendor_id;
      singleAppRoutes = GenericRoutes(
        client_id,
        head_side_object_data,
        menuUrlArray,
        AlertObjectData,
        template_id,
        getViewAccess,
        getRemoteAccess,
        getRemoteLockAccess,
        enabled_features,
        plan_description,
        props,
        application_id,
        application_name,
        client_name,
        client_logo,
        vendor_logo,
        user_id,
        user_preferences,
        collapsed,
        onCollapseSider,
        t,
        globalConfig,
        app_name,
        finalVendorId,
        logged_in_user_client_id,
        logged_in_user_role_type,
        authToken,
        checkCustomerFuelBuddy,
        is_white_label,
        vendor_name,
        loadingLogo,
        findThings,
        setMenuUrl,
        things_list,
        bannerToBeShown(),
        logged_in_user_name,
        allApps,
        currentUserPreferences,
        setCurrentUserPreferences,
        parent_vendor_id
      );
    } else if (template_id === 17) {
      singleAppRoutes = singleAppProjectRoutes(
        client_id,
        head_side_object_data,
        menuUrlArray,
        AlertObjectData,
        template_id,
        getViewAccess,
        getRemoteAccess,
        getRemoteLockAccess,
        enabled_features,
        plan_description,
        props,
        application_id,
        application_name,
        client_name,
        client_logo,
        vendor_logo,
        user_id,
        user_preferences,
        collapsed,
        onCollapseSider,
        t,
        globalConfig,
        app_name,
        vendor_id,
        logged_in_user_client_id,
        logged_in_user_role_type,
        authToken,
        checkCustomerFuelBuddy,
        is_white_label,
        vendor_name,
        loadingLogo,
        findThings,
        allApps,
        currentUserPreferences,
        setCurrentUserPreferences,
        things_list,
      );
    } else {
      if (enabled_features && head_side_object_data) {
        singleAppRoutes = singleAppProjectRoutes(
          client_id,
          head_side_object_data,
          menuUrlArray,
          AlertObjectData,
          template_id,
          getViewAccess,
          getRemoteAccess,
          getRemoteLockAccess,
          enabled_features,
          undefined,
          props,
          application_id,
          application_name,
          client_name,
          client_logo,
          vendor_logo,
          user_id,
          user_preferences,
          collapsed,
          onCollapseSider,
          t,
          globalConfig,
          app_name,
          parent_vendor_id,
          logged_in_user_client_id,
          logged_in_user_role_type,
          authToken,
          checkCustomerFuelBuddy,
          is_white_label,
          vendor_name,
          loadingLogo,
          findThings,
          allApps,
          currentUserPreferences,
          setCurrentUserPreferences,
          things_list,
        );
      }
    }
    mainRoute = singleAppRoutes;

    if (unauthorised_access && unauthorised_access_msg) {
      return (
        <div className="align-center-loading msg-container">
          <StopOutlined className="access-icon" />
          <div className="access-msg">{unauthorised_access_msg}</div>
        </div>
      );
    } else if (error_api_msg) {
      return (
        <div className="align-center-loading msg-container">
          <StopOutlined className="access-icon" />
          <div className="access-msg">{error_api_msg}</div>
        </div>
      );
    } else {
      return (
        <LoadScript
          loadingElement={
            <Loading
              show_logo={window.location.href.includes("app.datoms.io")}
              className="align-center-loading"
            />
          }
          googleMapsApiKey={import.meta.env.VITE_MAPS_API_KEY}
          libraries={mapLibraries}
        >
          <Suspense
            fallback={
              <Loading
                show_logo={window.location.href.includes("app.datoms.io")}
                className="align-center-loading"
              />
            }
          >
            {window.location.href.includes("impact-dashboard") ? (
              <Impact />
            ) : (
              <ConfigProvider
                theme={{
                  token: {
                    colorPrimary: isAurassure ? "#2497aa" : "#f58740",
                  },
                  components: {
                    Segmented: {
                      itemSelectedBg: "#ff8500",
                      itemSelectedColor: "#fff",
                    },
                    Table: {
                      cellFontSize: 13,
                      cellFontSizeMD: 13,
                      cellFontSizeSM: 13,
                    }
                  },
                }}
              >
                <Layout
                  structure_style={
                    isAurassure
                      ? {
                          sider_style: {
                            background_color: "#e9f5f7",
                            icon_color: "#747677",
                          },
                        }
                      : undefined
                  }
                  white_label_icon={{
                    small_icon: isAurassure
                      ? "https://prstatic.phoenixrobotix.com/aurassure/image/aurassure_logo.svg"
                      : isLumenEnergy
                        ? "https://datoms-files-storage.s3.ap-south-1.amazonaws.com/datoms/device-management/firmwares/image/1712121615432.png"
                        : isTataMotors
                          ? "https://static.datoms.io/images/Tata-Motors-T.png"
                          : "",
                    large_icon: isAurassure
                      ? "https://datoms-files-storage.s3.ap-south-1.amazonaws.com/datoms/device-management/firmwares/image/1678194419779.png"
                      : isLumenEnergy
                        ? "https://datoms-files-storage.s3.ap-south-1.amazonaws.com/datoms/device-management/firmwares/image/1712121615432.png"
                        : isTataMotors
                          ? "https://static.datoms.io/images/Tata-Motors-full-logo.png"
                          : "",
                  }}
                  isFixedLogoForWhitelabel={isLumenEnergy}
                  customizations={customizations}
                  loading_logo={window.location.href.includes("app.datoms.io")}
                  application_id={application_id}
                  page_type={page_type}
                  enabled_features={enabled_features}
                  client_name={client_name}
                  getExtraHeaderComps={getExtraHeaderComps}
                  showComplianceBanner={bannerToBeShown()}
                  onmenuClick={onmenuClick}
                  head_side_object_data={head_side_object_data}
                  home_page={
                    app_name === "fleet" ||
                    app_name === "tanker" ||
                    app_name === "energy"
                    ? "map-view"
                      : ""
                  }
                  getUserAccess={getUserAccess}
                  app_name={app_name}
                  customer_type={customer_type}
                  isRentalStore={isRentalStore}
                  store_logout={store_logout}
                  setStoreLogin={() => setStoreLogout(false)}
                  logged_in_user_client_id={logged_in_user_client_id}
                  deskTopLogout={props.signout}
                  mobileAppPackageName={mobileAppPackageName}
                  showPolicyPage={showPolicyPage}
                  t={t}
                  i18n={i18n}
                  globalConfig={globalConfig}
                  is_mobile_page_name={isMobilePageName}
                  user_preferences={user_preferences}
                  user_id={user_id}
                  client_id={client_id}
                  is_white_label={is_white_label}
                  template_id={template_id}
                  client_logo={client_logo}
                  vendor_logo={vendor_logo}
                  vendor_id={vendor_id}
                  user_name={user_name}
                  collapsed={collapsed}
                  onCollapseSider={onCollapseSider}                  
                  logged_in_user_role_type={logged_in_user_role_type}
                  language={language}
                >
                  {mainRoute}
                </Layout>
              </ConfigProvider>
            )}
          </Suspense>
        </LoadScript>
      );
    }
  }
};

export default App;
