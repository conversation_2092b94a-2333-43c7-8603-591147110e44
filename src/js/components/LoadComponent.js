import React from "react";
import { Layout } from "antd";
import "../../styles/component/LoadComponent.less";

export default class LoadComponent extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      message: [
        "Fetching your application...",
        "Initializing engines...",
        "Loading your data...",
        "Few seconds more...",
      ],
      flag: 0,
    };
  }

  componentDidMount() {
    this.v = setInterval(() => this.update(), 5000);
  }

  componentWillUnmount() {
    clearInterval(this.v);
  }

  update() {
    this.setState({ flag: this.state.flag + 1 });

    if (this.state.flag > this.state.message.length - 1) {
      this.setState({ flag: 0 });
    }
  }

  abx = () => {
    if (this.props.ta) {
      return (
        <div>
          <img
            alt=""
            src="https://prstatic.phoenixrobotix.com/imgs/datoms/iot-platform/datoms_logo.svg"
          />{" "}
        </div>
      );
    }
  };

  render() {
    console.log("LOAD COMPONENT3", this.props);

    return (
      <Layout
        className={"conts" + (this.props.c ? " collapsed-side" : "")}
        style={{ marginTop: "60px" }}
      >
        <div className="load-center">
          <img
            alt=""
            className="svg"
            src="https://prstatic.phoenixrobotix.com/imgs/index/index-page-loader.svg"
          />
          <div className="msg">{this.state.message[this.state.flag]}</div>
        </div>
        {/* <div class="b-logo">{this.abx()}</div> */}
        {/* <div class="b-logo">{(()=>{
                                        if(this.props.ta){
                                            
                                            return <div ><img alt='' src="https://prstatic.phoenixrobotix.com/imgs/datoms/iot-platform/datoms_logo.svg" /> </div>;
                                        }
                                        
                                    })()
                                }</div> */}
        <div className="b-logo">{this.abx()}</div>
      </Layout>
    );
  }
}
