import {
	disconnectSocketConnection,
	establishSocketConnection,
	getThingsAndParameterData,
	getThingsData,

	// getAllparameterDataAggr,
	retriveEventsData,
	retriveTasksData,
	retriveThingsList,
	retriveFaultsData,
	subscribeForEntityUpdates,
	subscribeForEventsUpdates,
	subscribeForThingsUpdates,
} from '@datoms/js-sdk';
import SearchOutlined from '@ant-design/icons/SearchOutlined';
// import { getThingsAndParameterData } from '../../data_handling/thingsListManipulation';
import { getAllparameterDataAggr } from '@datoms/js-utils/src/ParameterDataManipulation';
import { getSelectedparameterDataWithTimestamp } from '../../data_handling/ParameterDataManipulation';
import AntDrawer from '@datoms/react-components/src/components/AntDrawer';
import AntLayout from '@datoms/react-components/src/components/AntLayout';
import AntSpin from '@datoms/react-components/src/components/AntSpin';
import Loading from '@datoms/react-components/src/components/Loading';
import AntButton from '@datoms/react-components/src/components/AntButton';
import AntDivider from '@datoms/react-components/src/components/AntDivider';

import _find from 'lodash/find';
import _findIndex from 'lodash/findIndex';
import _uniqBy from 'lodash/uniqBy';
import _orderBy from 'lodash/orderBy';
import _sortBy from 'lodash/sortBy';
import _merge from 'lodash/merge';
import _filter from 'lodash/filter';
import moment from 'moment-timezone';
import queryString from 'query-string';
import React from 'react';
/*Styles*/
import './map-view.less';
import GoogleMapComponent from '../MapComponents/GoogleMapComponent';
import GraphObjectData from '../../configuration/GraphObjectData';
import Kpis from '../Kpis';
import ListView from '../ListView';
/*Components*/
import PanelComponent from '../PanelComponent';
import ActivityObject from '../../configuration/dashboardConfiguration/ActivityObject';
import GraphWithCardConfig from '../../configuration/dashboardConfiguration/GraphWithCardConfig';
/*Configs*/
import CardViewConfig from '../../configuration/dashboardConfiguration/KpiConfig';
import ListViewConfig from '../../configuration/dashboardConfiguration/ListViewConfig';
import MapConfig from '../../configuration/dashboardConfiguration/MapConfig';
import PanelConfig from '../../configuration/panelConfig/PanelConfigObject';
import MapSwitchOn from '../../../images/DGMonitoring/map_switch_on.svg';
import MapSwitchOff from '../../../images/DGMonitoring/map_switch_off.svg';
import MapOffline from '../../../images/DGMonitoring/map_offline.svg';
import MahindraSwitchOn from '../../../images/DGMonitoring/powerol_online.svg';
import MahindraSwitchOff from '../../../images/DGMonitoring/powerol_switch_off.svg';
import MahindraOffline from '../../../images/DGMonitoring/powerol_offline.svg';
import MapMoving from '../../../images/DGMonitoring/map_moving.svg';
import FuelOnIcon from '../../../images/DGMonitoring/fuel tank icon-green.svg';
import FuelOffIcon from '../../../images/DGMonitoring/fuel tank icon-grey.svg';
//import MapMoving from '../../../images/DGMonitoring/moving-dg-gif.gif';
import { getFault } from '../../data_handling/FaultDataWithDuration';
import { maintenanceCalculation } from '../../data_handling/MaintenanceCalculation';
import {
	filteredFuelTankThings,
	findFuelTankThing,
} from '../../data_handling/FuelTankThing';
import { filterDginIot } from '../../data_handling/DGinIot';

export default class Dashboard extends React.Component {
	constructor(props) {
		super(props);
		this.parsed = queryString.parse(props.location.search);
		GraphObjectData.graph_data.config.timezone =
			props.user_preferences.timezone;
		this.state = {
			screenWidth: window.innerWidth,
			PanelConfig: PanelConfig.panelViewObject,
			CardViewConfig: CardViewConfig,
			MapConfig: MapConfig.mapdetails,
			ListViewConfig: ListViewConfig.ListObject,
			GraphObjectData: GraphObjectData,
			GraphWithCardConfig: GraphWithCardConfig.GraphSeriesConfig,
			loading: true,
			selectedTab: 2,
			StartAfter: moment().subtract(1825, 'days').startOf('day').unix(),
			StartBefore: moment().endOf('day').unix(),
			resButton: 'trend',
			dateSelected: 'last_thirty_days',
			ActivityObject: ActivityObject,
			type: this.parsed['type']
				? this.parsed['type']
				: CardViewConfig.data[0].key,
			fromTime: moment().subtract(30, 'days').startOf('day').unix(),
			uptoTime: moment().endOf('day').unix(),
			faultFromTime: moment().subtract(30, 'days').startOf('day').unix(),
			faultUptoTime: moment().endOf('day').unix(),
			fuel_trend_from_time: moment()
				.subtract(7, 'days')
				.startOf('day')
				.unix(),
			drawer_visible: false,
			fuel_trend_upto_time: moment().endOf('day').unix(),
		};
		this.listApiData = {
			client_id: props.client_id,
			application_id: props.application_id,
		};
		if (window.localStorage.getItem('Client-Id')){
			this.listApiData.client_id=window.localStorage.getItem('Client-Id')
		}
		this.flagForTimeout = [];
		this.timer = null;
		this.updateEntityDetails = this.updateEntityDetails.bind(this);
		this.dgBasePath = 'https://app.datoms.io';

		if (!import.meta.env.VITE_MOBILE && typeof window !== undefined) {
			this.dgBasePath =
				window.location.protocol +
				'//' +
				window.location.host +
				(!window.location.href.includes('localhost')
					? '/enterprise/' + this.props.client_id
					: '');
		}
	}
	windowResize() {
		this.setState({ screenWidth: window.innerWidth });
	}
	eventChange(e) {
		this.setState({
			selectedTab: e,
		});
	}

	async getThingsList(data) {
		let totalData = await retriveThingsList(data);
			totalData = filterDginIot.bind(this)(totalData, 'map-view');
		let filteredThingWithFuelTankThingIds = filteredFuelTankThings(
			totalData,
			71
		).filteredThingWithFuelTankThingIds;
		let offlineArray = this.getOnlineOfflineArray(
			filteredFuelTankThings(totalData, 71).totalThings
		);
		let modifiedResponse = getThingsAndParameterData(totalData);
		let latestParameterData = this.offlineTimeOutFunction(
			undefined,
			modifiedResponse.latest_parameter_data,
			modifiedResponse
		);
		let statusArray = [];
		if (totalData.things && totalData.things.length) {
			totalData.things.map((things) => {
				statusArray.push({
					id: things.id,
					status: things.status,
				});
			});
		}
		this.setState({
			latestParameterData: latestParameterData,
			totalData: totalData,
			modifiedResponse: modifiedResponse,
			fuel_tank_things: filteredThingWithFuelTankThingIds,
			thing_status: statusArray,
			offlineArray,
		});
		this.latestDataFunc();
	}

	getOnlineOfflineArray(totalData) {
		let offlineArray = [];
		if (totalData && totalData.length) {
			totalData.map((thing) => {
				offlineArray.push({
					thing_id: thing.id,
					status: thing.status,
				});
			});
		}
		return offlineArray;
	}

	realTimeEventsUpdate(payload) {
		let offlineArray = this.state.offlineArray;
		if (
			payload &&
			//payload.event_data &&
			Object.keys(payload).length &&
			payload.entity_type === 'thing' &&
			payload.tags &&
			Array.isArray(payload.tags)
		) {
			let indexOffline = _findIndex((offlineArray, {
				thing_id: parseInt(payload.entity_id),
			});
			if (indexOffline > -1) {
				if (payload.tags.includes('Offline'))
					offlineArray[indexOffline].status = 'offline';
				if (payload.tags.includes('Online'))
					offlineArray[indexOffline].status = 'online';
			}
			this.setState({
				offlineArray,
			});
		}
	}

	checkOfflineOnlineStatus(thing_id) {
		let offlineStatus = _find(this.state.offlineArray, {
			thing_id,
		})?.status;
		return offlineStatus === 'online' ? '1' : '2';
	}

	latestDataFunc() {
		let latestRcvd = [],
			onlineDgs = [],
			switchOffDgs = [],
			offlineDgs = [];
		this.state.latestParameterData.map((latestParam) => {
			let findThing = _find(this.state.totalData.things, {
				id: latestParam.thing_id,
			});
			if (parseInt(findThing.category) === 71) {
				if (this.state.type === 'all_things') {
					latestRcvd.push(latestParam.thing_id);
				} else if (this.state.type === 'running') {
					if (
						this.checkOfflineOnlineStatus(latestParam.thing_id) ===
						'1'
					) {
						latestRcvd.push(latestParam.thing_id);
					}
				} else if (this.state.type === 'offline') {
					if (
						this.checkOfflineOnlineStatus(latestParam.thing_id) ===
						'2'
					) {
						latestRcvd.push(latestParam.thing_id);
					}
				}
				if (
					this.checkOfflineOnlineStatus(latestParam.thing_id) === '1'
				) {
					onlineDgs.push(latestParam.thing_id);
				}
				if (
					this.checkOfflineOnlineStatus(latestParam.thing_id) === '2'
				) {
					offlineDgs.push(latestParam.thing_id);
				}
			} else {
				if (this.state.type === 'all_things') {
					latestRcvd.push(latestParam.thing_id);
				} else if (this.state.type === 'running') {
					if (latestParam.OnOffStatus === '1') {
						latestRcvd.push(latestParam.thing_id);
					}
				} else if (this.state.type === 'offline') {
					if (latestParam.OnOffStatus === '2') {
						latestRcvd.push(latestParam.thing_id);
					}
				} else if (this.state.type === 'switch_off') {
					if (
						latestParam.OnOffStatus === '0' ||
						latestParam.OnOffStatus === ''
					) {
						latestRcvd.push(latestParam.thing_id);
					}
				}
				if (latestParam.OnOffStatus === '1') {
					onlineDgs.push(latestParam.thing_id);
				} else if (latestParam.OnOffStatus === '2') {
					offlineDgs.push(latestParam.thing_id);
				} else {
					switchOffDgs.push(latestParam.thing_id);
				}
			}
			return 1;
		});
		this.setState(
			{
				latestRcvd: latestRcvd,
				onlineDgs: onlineDgs,
				offlineDgs: offlineDgs,
				switchOffDgs: switchOffDgs,
			},
			() => {
				this.filterCardFuncTion();
				this.mapFunction();
				this.listViewFunction();
			}
		);
	}

	async getThingDailyAggrData() {
		let parameterKeys = [
			'calculated_runhour',
			'enrg',
			'calculated_energy',
			'fuel_consumption',
		];
		let data_packet_aggr = {
			data_type: 'aggregate',
			aggregation_period: 86400,
			parameters: parameterKeys,
			parameter_attributes: ['sum'],
			things: this.state.listId
				? [this.state.listId]
				: this.state.modifiedResponse.all_thing_ids,
			from_time: this.state.fromTime,
			upto_time: this.state.uptoTime,
		};
		let clientId=this.props.client_id
		if (window.localStorage.getItem('Client-Id')){
			clientId=window.localStorage.getItem('Client-Id')
		}
		let response = await getThingsData(
			data_packet_aggr,
			clientId,
			this.props.application_id
		);
		if (response.status === 'success') {
			this.setState({
				responseData: response.data,
				parameterKeys: parameterKeys,
			});
		}
	}
	isGpsPathEnabled() {
		return (
			this.props.plan_description &&
			this.props.plan_description.dashboard_view_section &&
			this.props.plan_description.dashboard_view_section.map_gps_path
		);
	}
	async getThingsRawData() {
		if (this.isGpsPathEnabled()) {
			let dataPacketRaw = {
				data_type: 'raw',
				aggregation_period: 0,
				parameters: ['lat', 'long'],
				parameter_attributes: [],
				things: [this.state.listId],
				from_time: parseInt(this.state.moving_start_time),
				upto_time: moment().unix(),
			};
			let rawData = await getThingsData(
				dataPacketRaw,
				this.props.client_id,
				this.props.application_id
			);
			// {
			// 	"status": "success",
			// 	"data": [
			// 		{
			// 			"parameter_values": {
			// 				"lat": 18.506312,
			// 				"long": 73.844483
			// 			},
			// 			"time": 1648569380
			// 		},
			// 		{
			// 			"parameter_values": {
			// 				"lat": 18.506386,
			// 				"long": 73.845465
			// 			},
			// 			"time": 1648569620
			// 		},
			// 		{
			// 			"parameter_values": {
			// 				"lat": 18.505653,
			// 				"long": 73.845722
			// 			},
			// 			"time": 1648569740
			// 		},
			// 		{
			// 			"parameter_values": {
			// 				"lat": 18.505831,
			// 				"long": 73.846929
			// 			},
			// 			"time": 1648569920
			// 		},
			// 		{
			// 			"parameter_values": {
			// 				"lat": 18.503482,
			// 				"long": 73.847461
			// 			},
			// 			"time": 1648570220
			// 		},
			// 		{
			// 			"parameter_values": {
			// 				"lat": 18.502019,
			// 				"long": 73.847259
			// 			},
			// 			"time": 1648570580
			// 		}
			// 	]
			// }
			if (rawData.status === 'success') {
				let movingDgRawData = this.convertLatLng(rawData.data);
				this.setState(
					{
						latLngData: movingDgRawData.modifiedData,
						latLngDataWithTime:
							movingDgRawData.modifiedDataWithTime,
					},
					async () => {
						if (window.innerWidth > 576) {
							this.zoomOnPolyline();
						}
						await this.getAddress(this.state.latLngDataWithTime);
					}
				);
			}
		}
	}

	//modify lat-lng data for map
	convertLatLng(data) {
		let modifiedData = [],
			modifiedDataWithTime = [];
		data.map((point) => {
			modifiedDataWithTime.push({
				lat: parseFloat(point.parameter_values?.lat),
				lng: parseFloat(point.parameter_values?.long),
				time: point.time,
			});
			modifiedData.push({
				lat: parseFloat(point.parameter_values?.lat),
				lng: parseFloat(point.parameter_values?.long),
			});
			return 1;
		});
		return {
			modifiedData: _uniqBy(
				modifiedData,
				(item) => item.lat || item.lng
			),
			modifiedDataWithTime: _uniqBy(
				modifiedDataWithTime,
				(item) => item.lat || item.lng
			),
		};
	}

	// split latLngDataWithAddress into necessary data for timeline
	splitLocationArray(points) {
		if (points.length <= 50) {
			return this.getPoints(points, 5);
		}
		return this.getPoints(points, 10);
	}

	getPoints(points, maxPoints) {
		const interval = Math.ceil(points.length / (maxPoints - 1));
		const newPoints = [];
		for (let i = 0; i < points.length - 1; i += interval) {
			newPoints.push(points[i]);
		}
		newPoints.push(points[points.length - 1]);
		return newPoints;
	}

	//reverse geocode all lat-lng points
	async getAddress(latLngData, socket = false) {
		let addressArray = [];
		if (socket && this.state.latLngDataWithAddress?.length) {
			addressArray = this.state.latLngDataWithAddress;
		}
		await Promise.all(
			this.splitLocationArray(latLngData).map(async (latlng) => {
				let address = await this.reverseGeocode(latlng);
				addressArray.push({
					lat: latlng.lat,
					lng: latlng.lng,
					address: address,
					time: latlng.time,
				});
				return 1;
			})
		);
		this.setState({
			latLngDataWithAddress: socket
				? addressArray
				: _orderBy(addressArray, ['time'], ['asc']),
		});
	}

	// get address from lat-lng
	async reverseGeocode(latlng) {
		let address = '';
		const geocoder = new window.google.maps.Geocoder();
		try {
			const response = await geocoder.geocode({
				location: latlng,
			});
			address = response?.results?.[0]?.formatted_address;
		} catch (err) {}
		return address;
	}

	// calculate the distance travelled by the moving DG
	calculateDistance(latLngData = this.state.latLngData) {
		let lengthInMeters = '';
		if (latLngData?.length > 1) {
			lengthInMeters = window.google.maps.geometry.spherical.computeLength(
				latLngData
			);
		}
		return lengthInMeters
			? (parseFloat(lengthInMeters) / 1000).toFixed(2) + ' Km'
			: 'NA';
	}
	// calculate the duration travelled by the moving DG
	calculateDuration(
		moving_start_time = parseInt(this.state.moving_start_time),
		moving_stop_time = this.state.latLngDataWithTime?.length > 1
			? this.state.latLngDataWithTime[
					this.state.latLngDataWithTime.length - 1
			  ].time
			: ''
	) {
		let duration = '';
		if (moving_start_time && moving_stop_time) {
			let diffInMiliSecs = moment(moving_stop_time * 1000).diff(
				moving_start_time * 1000
			);
			if (diffInMiliSecs) {
				duration = this.convertMiliSecsToHhMm(diffInMiliSecs) + ' Hrs';
			}
		}
		return duration || 'NA';
	}
	// calculate current speed of the moving DG
	calculateSpeed(latLngDataWithTime = this.state.latLngDataWithTime) {
		let modifiedDataArray =
			latLngDataWithTime?.length > 1
				? latLngDataWithTime.slice(latLngDataWithTime.length - 2)
				: [];
		let speed = '';
		if (modifiedDataArray?.length > 1) {
			let distanceArray = modifiedDataArray.map((i) => {
				return {
					lat: i.lat,
					lng: i.lng,
				};
			});
			let duration = this.calculateTimeDiff(
				modifiedDataArray[0].time,
				modifiedDataArray[1].time
			);
			let distance = parseFloat(this.calculateDistance(distanceArray));
			if (distance > 0 && duration > 0) {
				speed =
					(parseFloat(distance) / parseFloat(duration)).toFixed(2) +
					' Kmph';
			}
		}
		return speed || 'NA';
	}

	// calculate time diff in hour
	calculateTimeDiff(moving_start_time, moving_end_time) {
		let diffInMiliSecs = moment(moving_end_time * 1000).diff(
				moving_start_time * 1000
			),
			duration = '';
		if (diffInMiliSecs) {
			duration = diffInMiliSecs / 1000 / 60 / 60;
		}
		return duration;
	}
	// convert miliseconds to hh:mm format, add leading zero if required
	convertMiliSecsToHhMm(miliSecs) {
		let hh = Math.floor(miliSecs / 1000 / 60 / 60);
		let mm = Math.floor(miliSecs / 1000 / 60) % 60;
		return (hh < 10 ? '0' : '') + hh + ':' + (mm < 10 ? '0' : '') + mm;
	}

	async getEvents(data) {
		let url_string = encodeURI(
			'?get_details=true&generated_after=' +
				this.state.fromTime +
				'&generated_before=' +
				this.state.uptoTime
		);
		let event_response = await retriveEventsData(data, url_string);
		if (event_response.response.status === 'success') {
			this.setState({
				loading: false,
				events: event_response.response.events,
				event_types: event_response.response.event_types,
				trendNotificationLoading: false,
			});
		}
	}

	async getfaultApi(download) {
		let entityId = parseInt(this.state.listId);
		let dataToBeSent = {};
		dataToBeSent['thing_id'] = entityId;
		dataToBeSent['client_id'] = this.listApiData.client_id;
		dataToBeSent['application_id'] = this.listApiData.application_id;
		let url_string = encodeURI(
			'?from_time=' +
				this.state.faultFromTime +
				'&upto_time=' +
				this.state.faultUptoTime +
				'&results_per_page=1'
		);
		let faultResponse = await retriveFaultsData(dataToBeSent, url_string);
		if (faultResponse.response.status === 'success') {
			this.setState({
				faultResponse: faultResponse.response.data,
			});
		}
	}

	offlineTimeOutFunction(payload = undefined, lastdata, response) {
		let latestDataArray = [],
			latestData = {},
			onOffStatus = {};
		response.all_thing_ids.map((things) => {
			let sortedThingsdata = _sortBy(lastdata, ['time']);
			let filteredLatestData = _filter(sortedThingsdata, {
				thing_id: things,
			});
			if (!latestData[things]) {
				latestData[things] =
					filteredLatestData[filteredLatestData.length - 1];
			}
			if (!onOffStatus[things]) {
				onOffStatus[things] = latestData[things].data['mc_st'];
			}
			let findThings = _find(response.things_list, { id: things });
			if (findThings) {
				let timeout = findThings.offline_timeout
					? findThings.offline_timeout * 1000
					: 900 * 1000;
				let timeInterval =
					(moment().unix() - latestData[things].time) * 1000;
				if (payload === undefined && timeInterval < timeout) {
					this.flagForTimeout[things] = true;
					this.timer = setTimeout(() => {
						this.changeToOffline(things, lastdata);
					}, timeout - timeInterval);
				} else if (payload === undefined && timeInterval > timeout) {
					this.flagForTimeout[things] = false;
				} else if (payload) {
					if (timeInterval < timeout) {
						this.flagForTimeout[things] = true;
						clearTimeout(this.timer);
						this.timer = setTimeout(() => {
							this.changeToOffline(things, lastdata);
						}, timeout - timeInterval);
					} else if (timeInterval > timeout) {
						this.flagForTimeout[things] = false;
					}
				}
				if (this.flagForTimeout[things] === true) {
					onOffStatus[things] = parseInt(
						latestData[things].data['mc_st']
					)
						? parseInt(latestData[things].data['mc_st']).toString()
						: '';
				} else {
					onOffStatus[things] = '2';
				}
				latestDataArray.push(latestData[things]);
				let findindex = _findIndex((latestDataArray, {
					thing_id: things,
				});
				if (findindex > -1) {
					latestDataArray[findindex]['OnOffStatus'] =
						onOffStatus[things];
				}
			}
		});
		return latestDataArray;
	}

	changeToOffline(thing_ids, latestdata) {
		let findindex = _findIndex((latestdata, { thing_id: thing_ids });
		if (findindex > -1) {
			latestdata[findindex]['OnOffStatus'] = '2';
		}
		this.setState(
			{
				latestParameterData: latestdata,
			},
			() => {
				this.latestDataFunc();
			}
		);
	}

	filterCardFuncTion() {
		let cardObject = JSON.parse(JSON.stringify(this.state.CardViewConfig));
		cardObject.data[0].name = 'All Assets'
		let noOfAllDgs = 0,
			noOfRunningDgs = 0,
			noOfSwitchOffDgs = 0,
			noOfOfflineDgs = 0;
		noOfAllDgs += this.state.modifiedResponse.all_thing_ids.length;
		noOfRunningDgs = this.state.onlineDgs.length;
		noOfSwitchOffDgs = this.state.switchOffDgs.length;
		noOfOfflineDgs = this.state.offlineDgs.length;
		cardObject.config.rowGutter = {
			xxl: 20,
			xl: 20,
			lg: 20,
			md: 20,
			sm: 15,
			xs: 10,
		}
		cardObject.config.colResponsive = {
			xxl: 6,
			xl: 6,
			lg: 6,
			md: 6,
			sm: 6,
			xs: 6,
		}
		cardObject.data.map((cardObjectParam) => {
			if (cardObjectParam.key === 'all_things') {
				cardObjectParam.value = noOfAllDgs;
			} else if (cardObjectParam.key === 'running') {
				cardObjectParam.value = noOfRunningDgs;
			} else if (cardObjectParam.key === 'switch_off') {
				cardObjectParam.value = noOfSwitchOffDgs;
			} else if (cardObjectParam.key === 'offline') {
				cardObjectParam.value = noOfOfflineDgs;
			}
			return cardObjectParam;
		});
		return cardObject;
	}

	// lat/lng not equals -1
	notEqualsMinusOne(lat, lng) {
		return lat > -90 && lat < 90 && lng > -180 && lng < 180;
	}

	mapFunction() {
		let mapConfigData = JSON.parse(JSON.stringify(this.state.MapConfig));
		let mapPointers = [],
			mapLegend = [];
		if (this.state.latestRcvd && this.state.latestRcvd.length) {
			this.state.latestRcvd.map((latestParam) => {
				let filteredList = _filter(this.state.totalData.things, {
					id: latestParam,
				});
				let findParamList = _find(this.state.latestParameterData, {
					thing_id: latestParam,
				});
				return filteredList.map((filteredListData) => {
					return mapPointers.push({
						lat:
							parseFloat(findParamList?.data?.['lat']) &&
							parseFloat(findParamList?.data?.['long']) &&
							this.notEqualsMinusOne(
								parseFloat(findParamList?.data?.['lat']),
								parseFloat(findParamList?.data?.['long'])
							)
								? parseFloat(findParamList?.data?.['lat'])
								: filteredListData.latitude,
						lng:
							//findParamList?.data?.['is_moving'] &&
							parseFloat(findParamList?.data?.['long']) &&
							parseFloat(findParamList?.data?.['lat']) &&
							this.notEqualsMinusOne(
								parseFloat(findParamList?.data?.['lat']),
								parseFloat(findParamList?.data?.['long'])
							)
								? parseFloat(findParamList?.data?.['long'])
								: filteredListData.longitude,
						hoverText: filteredListData.name,
						id: filteredListData.id,
						kva:
							filteredListData.thing_details &&
							!isNaN(
								parseFloat(filteredListData.thing_details.kva)
							)
								? '(' +
								  (parseFloat(
										filteredListData.thing_details.kva
								  ) +
										' KVA') +
								  ')'
								: '',
						is_moving: parseInt(findParamList?.data?.['is_moving']),
						moving_start_time:
							findParamList?.data?.['moving_start_time'], //1646117119,
						isDgOffline: findParamList.OnOffStatus === '2',
						icon:
							parseInt(filteredListData.category) === 71
								? this.checkOfflineOnlineStatus(
										filteredListData.id
								  ) === '1'
									? FuelOnIcon
									: FuelOffIcon
								: //is_moving && findParamList.OnOffStatus !== '2' ?
								parseInt(findParamList?.data?.['is_moving']) &&
								  findParamList.OnOffStatus !== '2'
								? MapMoving
								: findParamList.OnOffStatus === '1'
								? parseInt(this.props.vendor_id) === 1140
									? MahindraSwitchOn
									: MapSwitchOn
								: findParamList.OnOffStatus === '2'
								? parseInt(this.props.vendor_id) === 1140
									? MahindraOffline
									: MapOffline
								: parseInt(this.props.vendor_id) === 1140
								? MahindraSwitchOff
								: MapSwitchOff,
					});
					//}
				});
			});
		}

		if (this.state.type === 'all_things') {
			mapLegend = [
				{
					name: 'Running',
					color: '#00b300',
				},
				{
					name: 'Moving',
					color: '#8EAFE7',
				},
				{
					name: 'Stopped',
					color: '#ff0000',
				},
				{
					name: 'Not Connected',
					color: '#858787',
				},
			];
		} else if (this.state.type === 'running') {
			mapLegend = [
				{
					name: 'Running',
					color: '#00b300',
				},
			];
		} else if (this.state.type === 'switch_off') {
			mapLegend = [
				{
					name: 'Stopped',
					color: '#ff0000',
				},
				{
					name: 'Moving',
					color: '#8EAFE7',
				},
			];
		} else if (this.state.type === 'offline') {
			mapLegend = [
				{
					name: 'Not Connected',
					color: '#858787',
				},
			];
		}
		mapConfigData.map_legend = mapLegend;
		mapConfigData.map_pointers = mapPointers;
		return mapConfigData;
	}

	//create bounds for polyline
	createBoundsForPolyline() {
		let bounds = new window.google.maps.LatLngBounds();
		this.state.latLngData.map((points) => {
			return bounds.extend(points);
		});
		return bounds;
	}

	//zoom on the polyline bound
	zoomOnPolyline() {
		let mapDiv = document.getElementById('map_id');
		let mapDim = {
			height: mapDiv.clientHeight,
			width: mapDiv.clientWidth,
		};
		let bounds = this.createBoundsForPolyline();
		let zoomLevel = bounds ? this.getBoundsZoomLevel(bounds, mapDim) : 0;
		let centerValue = bounds
			? bounds.getCenter()
			: new window.google.maps.LatLng(0, 0);
		this.setState({
			zoomLevel: zoomLevel,
			centerValue: centerValue,
		});
	}

	getBoundsZoomLevel(bounds, mapDim) {
		let WORLD_DIM = { height: 256, width: 256 };
		let ZOOM_MAX = 21;

		function latRad(lat) {
			let sin = Math.sin((lat * Math.PI) / 180);
			let radX2 = Math.log((1 + sin) / (1 - sin)) / 2;
			return Math.max(Math.min(radX2, Math.PI), -Math.PI) / 2;
		}

		function zoom(mapPx, worldPx, fraction) {
			return Math.floor(Math.log(mapPx / worldPx / fraction) / Math.LN2);
		}

		let ne = bounds.getNorthEast();
		let sw = bounds.getSouthWest();

		let latFraction = (latRad(ne.lat()) - latRad(sw.lat())) / Math.PI;

		let lngDiff = ne.lng() - sw.lng();
		let lngFraction = (lngDiff < 0 ? lngDiff + 360 : lngDiff) / 360;

		let latZoom = zoom(mapDim.height, WORLD_DIM.height, latFraction);
		let lngZoom = zoom(mapDim.width, WORLD_DIM.width, lngFraction);

		return Math.min(latZoom, lngZoom, ZOOM_MAX);
	}

	mapOnClick(
		hoverText,
		lastData,
		modifiedResponse,
		id,
		is_moving,
		moving_start_time,
		isDgOffline
	) {
		this.setState(
			{
				listId: id,
				fromTime: moment().subtract(30, 'days').startOf('day').unix(),
				uptoTime: moment().endOf('day').unix(),
				panelView: true,
				listLoading: true,
				drawer_visible: true,
				trendNotificationLoading: true,
				moving_start_time,
			},
			async () => {
				if (is_moving && !isDgOffline) {
					await this.getThingsRawData();
				}
				await this.getThingDailyAggrData();
				if (
					findFuelTankThing(
						this.state.fuel_tank_things,
						this.state.listId
					)
				) {
					await this.thingsDataFunction();
				}
				await this.getEvents(this.listApiData);
				await this.getfaultApi();
				await this.fetchMissionList(this.state.listId);
				this.getPanelData(this.state.listId);
			}
		);
	}

	listViewFunction() {
		let listViewData = JSON.parse(
			JSON.stringify(this.state.ListViewConfig)
		);
		let listViewArray = [];
		if (this.state.latestRcvd && this.state.latestRcvd.length) {
			this.state.latestRcvd.map((latestParamVal) => {
				if (latestParamVal) {
					let findParamList = _find(this.state.latestParameterData, {
						thing_id: latestParamVal,
					});
					let findThings = _find(this.state.totalData.things, {
						id: latestParamVal,
					});
					let fuelTankThing = false;
					if (
						findFuelTankThing(
							this.state.fuel_tank_things,
							latestParamVal
						)
					) {
						fuelTankThing = true;
					}
					listViewArray.push({
						id: findThings.id,
						key: latestParamVal,
						time: findParamList.time,
						name: findThings.name,
						kva:
							!fuelTankThing &&
							findThings.thing_details &&
							!isNaN(parseFloat(findThings.thing_details.kva))
								? '(' +
								  (parseFloat(findThings.thing_details.kva) +
										' KVA') +
								  ')'
								: '',
						address: findThings.address,
						dgStatus: fuelTankThing
							? this.checkOfflineOnlineStatus(findThings.id)
							: findParamList.OnOffStatus,
						commandStatus: _find(this.state.totalData.things, {
							id: latestParamVal,
						}).commands,
						fuelTankThing: fuelTankThing,
						dg_lock_status:
							findParamList.data.dg_lock_status || '0',
						operation_mode:
							findThings && findThings.thing_details
								? findThings.thing_details.operation_mode
								: undefined,
						isLockControlEnabled:
							findThings &&
							findThings.thing_details &&
							findThings.thing_details
								.is_lock_unlock_control_enabled === 'enable'
								? true
								: false,
						isThingMechanicalDG: this.isThingMechanicalDG(latestParamVal),
						isControlEnabled:
							findThings &&
							findThings.thing_details &&
							findThings.thing_details
								.is_start_stop_control_enabled === 'enable'
								? true
								: false,
						is_moving:
							parseInt(findParamList?.data?.['is_moving']) ||
							false,
						moving_start_time:
							findParamList?.data?.['moving_start_time'],
						//1646117119,
						isDgOffline: findParamList.OnOffStatus === '2',
					});
				}
				return listViewArray;
			});
		}
		let sortListByName = _sortBy(listViewArray, ['value']);
		listViewData.data = sortListByName;
		return listViewData;
	}

	async fetchMissionList(listId) {
		let data = {
			OrderBy: 'desc',
			client_id: this.props.client_id,
			application_id: this.props.application_id,
			StartAfter: moment.unix(this.state.StartAfter).toISOString(),
			StartBefore: moment.unix(this.state.StartBefore).toISOString(),
			ResultsPerPage: 50,
			task_type: 1,
			thing_list: [listId],
			GetDetails: true,
		};
		let missionList = await retriveTasksData(data);
		this.setState({
			missionList: missionList,
			listLoading: false,
		});
		this.getPanelData(listId);
	}

	async thingsDataFunction() {
		let dataPacketRaw = {
			data_type: 'raw',
			aggregation_period: 0,
			parameters: ['fuel'],
			parameter_attributes: [],
			things: this.state.fuel_tank_things,
			from_time: this.state.fuel_trend_from_time,
			upto_time: this.state.fuel_trend_upto_time,
		};
		let dataPacketAggr = {
			data_type: 'aggregate',
			aggregation_period:
				this.state.fuel_trend_from_time ===
				moment().subtract(7, 'days').startOf('day').unix()
					? 86400
					: 3600,
			parameters: ['fuel_consumption', 'fuel_filled'],
			parameter_attributes: ['sum'],
			things: this.state.fuel_tank_things,
			from_time: this.state.fuel_trend_from_time,
			upto_time: this.state.fuel_trend_upto_time,
		};
		let rawData = await getThingsData(
			dataPacketRaw,
			this.props.client_id,
			this.props.application_id
		);
		let aggrData = await getThingsData(
			dataPacketAggr,
			this.props.client_id,
			this.props.application_id
		);
		this.setState(
			{
				rawData: rawData.data,
				aggrData: aggrData.data,
				fuel_trend_loading: false,
			},
			() => {
				this.graphDataFunc();
			}
		);
	}

	graphDataFunc() {
		let thingParameterDataHourlyAggr = {},
			thingParameterDataRaw = {};
		thingParameterDataHourlyAggr = getAllparameterDataAggr(
			this.state.aggrData,
			this.state.fuel_tank_things,
			this.state.modifiedResponse.param_key_data,
			this.state.fuel_trend_from_time,
			this.state.fuel_trend_upto_time,
			this.state.fuel_trend_from_time ===
				moment().subtract(7, 'days').startOf('day').unix()
				? 86400
				: 3600,
			'sum'
		);
		thingParameterDataRaw = getSelectedparameterDataWithTimestamp(
			this.state.rawData,
			this.state.fuel_tank_things,
			this.state.modifiedResponse.param_key_data,
			this.state.fuel_trend_from_time,
			this.state.fuel_trend_upto_time
		);
		this.setState({
			thingParameterDataHourlyAggr: thingParameterDataHourlyAggr,
			thingParameterDataRaw: thingParameterDataRaw,
		});
	}

	payloadDataFunc(payloadRawData, hourlyAggrDataPayload) {
		let thingParameterDataRaw = this.state.thingParameterDataRaw;
		let thingParameterDataHourlyAggr = this.state
			.thingParameterDataHourlyAggr;
		if (Array.isArray(this.state.modifiedResponse.param_key_data)) {
			this.state.modifiedResponse.param_key_data.map((paramData) => {
				if (
					payloadRawData &&
					Object.keys(payloadRawData).length &&
					payloadRawData.thing_id === parseInt(this.state.listId) &&
					payloadRawData.parameter_values[paramData] &&
					thingParameterDataRaw &&
					thingParameterDataRaw[this.state.listId]
				) {
					thingParameterDataRaw[this.state.listId][paramData].push([
						payloadRawData.time * 1000,
						parseFloat(payloadRawData.parameter_values[paramData]),
					]);
					thingParameterDataRaw[this.state.listId][
						paramData
					] = thingParameterDataRaw[this.state.listId][
						paramData
					].sort(function (a, b) {
						return a[0] - b[0];
					});
				}
				if (
					hourlyAggrDataPayload &&
					Object.keys(hourlyAggrDataPayload).length &&
					hourlyAggrDataPayload.thing_id ===
						parseInt(this.state.listId) &&
					hourlyAggrDataPayload.parameter_values[paramData] &&
					thingParameterDataHourlyAggr &&
					thingParameterDataHourlyAggr[this.state.listId]
				) {
					let findIndexAggrDataForTime = _findIndex((
						thingParameterDataHourlyAggr[this.state.listId][
							paramData
						],
						function (o) {
							return (
								moment(o[0]).format('HH') ===
								moment
									.unix(hourlyAggrDataPayload.time)
									.format('HH')
							);
						}
					);
					if (findIndexAggrDataForTime === -1) {
						thingParameterDataHourlyAggr[this.state.listId][
							paramData
						].push([
							hourlyAggrDataPayload.time * 1000,
							parseFloat(
								hourlyAggrDataPayload.parameter_values[
									paramData
								].sum
							),
						]);
						thingParameterDataHourlyAggr[this.state.listId][
							paramData
						] = thingParameterDataHourlyAggr[this.state.listId][
							paramData
						].sort(function (a, b) {
							return a[0] - b[0];
						});
					} else {
						thingParameterDataHourlyAggr[this.state.listId][
							paramData
						][findIndexAggrDataForTime] = [
							hourlyAggrDataPayload.time * 1000,
							parseFloat(
								hourlyAggrDataPayload.parameter_values[
									paramData
								].sum
							),
						];
					}
				}
				return 1;
			});
		}

		let fuelTrend = {
			thingParameterDataRaw: thingParameterDataRaw,
			thingParameterDataHourlyAggr: thingParameterDataHourlyAggr,
		};
		return fuelTrend;
	}

	realTimeDataFunc(payload) {
		let rawDataRealTime = {};
		let hourlyAggrData = {};
		if (payload) {
			if (payload.type === 'raw') {
				let findThing = _find(
					this.state.modifiedResponse.latest_parameter_data,
					{ thing_id: parseInt(payload.thing_id) }
				);
				let latestData = findThing.data;
				if (
					findThing &&
					findThing.data &&
					Object.keys(findThing.data).length
				) {
					Object.keys(findThing.data).map((thingData) => {
						if (payload.data[thingData] !== undefined) {
							latestData[thingData] = payload.data[thingData];
						}
						return latestData;
					});
				}

				rawDataRealTime = {
					time: payload.time,
					thing_id: parseInt(payload.thing_id),
					parameter_values: payload.data,
				};
			}
			if (payload.type === '1_hr_avg') {
				hourlyAggrData = {
					time: payload.time,
					thing_id: parseInt(payload.thing_id),
					parameter_values: payload.data,
				};
			}
			let payloadDataFuel = this.payloadDataFunc(
				rawDataRealTime,
				hourlyAggrData
			);
			this.setState({
				thingParameterDataRaw: payloadDataFuel.thingParameterDataRaw,
				thingParameterDataHourlyAggr:
					payloadDataFuel.thingParameterDataHourlyAggr,
			});
		}
	}

	getPanelGraph() {
		let rawFuelGraph = JSON.parse(
			JSON.stringify(this.state.GraphObjectData)
		);
		let rawFuelGraphArray = [];
		if (
			this.state.thingParameterDataRaw &&
			this.state.thingParameterDataRaw[this.state.listId] &&
			Array.isArray(
				this.state.thingParameterDataRaw[this.state.listId]['fuel']
			)
		) {
			this.state.thingParameterDataRaw[this.state.listId]['fuel'].map(
				(thingParameterDataRaw_data) => {
					if (
						thingParameterDataRaw_data &&
						thingParameterDataRaw_data.length
					) {
						return rawFuelGraphArray.push([
							thingParameterDataRaw_data[0],
							thingParameterDataRaw_data[1],
						]);
					}
				}
			);
		}
		let rawFuelGraphData = [
			{
				name: 'Fuel Level',
				data: rawFuelGraphArray,
				color: '#becadd',
			},
		];
		rawFuelGraph.graph_data.series_data = rawFuelGraphData;
		rawFuelGraph.graph_data.config.chart.type = 'area';
		rawFuelGraph.graph_data.config.chart.backgroundColor = 'rgba(0,0,0,0)';
		rawFuelGraph.graph_data.config.xAxis.title = {
			text: 'Fuel Level',
			style: {
				color: 'red',
			},
		};

		let aggrFuelConsFuelFilled = JSON.parse(
			JSON.stringify(this.state.GraphObjectData)
		);
		let aggrFuelConsArray = [],
			aggrFuelFilledArray = [];
		if (
			this.state.thingParameterDataHourlyAggr &&
			this.state.thingParameterDataHourlyAggr[this.state.listId] &&
			Array.isArray(
				this.state.thingParameterDataHourlyAggr[this.state.listId][
					'fuel_consumption'
				]
			)
		) {
			this.state.thingParameterDataHourlyAggr[this.state.listId][
				'fuel_consumption'
			].map((thingParameterDataHourlyAggr_data) => {
				if (
					thingParameterDataHourlyAggr_data &&
					thingParameterDataHourlyAggr_data.length
				) {
					return aggrFuelConsArray.push([
						thingParameterDataHourlyAggr_data[0],
						thingParameterDataHourlyAggr_data[1],
					]);
				}
			});
		}
		if (
			this.state.thingParameterDataHourlyAggr &&
			this.state.thingParameterDataHourlyAggr[this.state.listId] &&
			Array.isArray(
				this.state.thingParameterDataHourlyAggr[this.state.listId][
					'fuel_filled'
				]
			)
		) {
			this.state.thingParameterDataHourlyAggr[this.state.listId][
				'fuel_filled'
			].map((thingParameterDataHourlyAggr_data) => {
				if (
					thingParameterDataHourlyAggr_data &&
					thingParameterDataHourlyAggr_data.length
				) {
					return aggrFuelFilledArray.push([
						thingParameterDataHourlyAggr_data[0],
						thingParameterDataHourlyAggr_data[1],
					]);
				}
			});
		}
		let aggrFuelConsFuelFilledData = [
			{
				name: 'Fuel Consumption',
				data: aggrFuelConsArray,
				color: '#f58740',
				type: 'column',
			},
			{
				name: 'Fuel Filled',
				data: aggrFuelFilledArray,
				color: '#becadd',
				type: 'column',
			},
		];
		aggrFuelConsFuelFilled.graph_data.config.xAxis.title = {
			text: 'Fuel',
		};
		aggrFuelConsFuelFilled.graph_data.config.legend.enabled = true;
		aggrFuelConsFuelFilled.graph_data.series_data = aggrFuelConsFuelFilledData;
		return {
			rawFuelGraph: rawFuelGraph,
			aggrFuelConsFuelFilled: aggrFuelConsFuelFilled,
		};
	}

	dateChange(e) {
		let fromTime, uptoTime;
		if (e === 'last_7_days') {
			fromTime = moment().subtract(7, 'days').startOf('day').unix();
			uptoTime = moment().endOf('day').unix();
		} else if (e === 'last_24_hr') {
			fromTime = moment().subtract(24, 'hours').unix();
			uptoTime = moment().unix();
		} else if (e === 'today') {
			fromTime = moment().startOf('day').unix();
			uptoTime = moment().endOf('day').unix();
		}
		this.setState(
			{
				fuel_trend_from_time: fromTime,
				fuel_trend_upto_time: uptoTime,
				fuel_trend_loading: true,
			},
			async () => {
				await this.thingsDataFunction();
			}
		);
	}

	getPanelData(listId) {
		if (listId) {
			let PanelConfigObject = JSON.parse(
				JSON.stringify(this.state.PanelConfig)
			);
			let findLastData = _find(this.state.latestParameterData, {
				thing_id: listId,
			});
			let fuelTankThing = false;
			if (findFuelTankThing(this.state.fuel_tank_things, listId)) {
				fuelTankThing = true;
			}
			if (findLastData) {
				let totalPanelData = [],
					parameterValuesObject = {},
					parameterList = {},
					tripDataObj = {},
					tripDataValue = {};
				if (!parameterValuesObject[findLastData.thing_id]) {
					parameterValuesObject[findLastData.thing_id] = [];
				}
				if (!parameterList[findLastData.thing_id]) {
					parameterList[findLastData.thing_id] = [];
				}
				if (!tripDataObj[findLastData.thing_id]) {
					tripDataObj[findLastData.thing_id] = [];
				}
				if (!tripDataValue[findLastData.thing_id]) {
					tripDataValue[findLastData.thing_id] = [];
				}
				let filteredArrayMisson =
					this.state.missionList &&
					this.state.missionList.response &&
					this.state.missionList.response.Missions
						? this.state.missionList.response.Missions.filter(
								(missions) =>
									missions.Devices.includes(
										findLastData.thing_id.toString()
									)
						  )
						: [];
				let sortedFilterMissionArrayLocalTime = _orderBy(
					filteredArrayMisson,
					['StartDate'],
					['desc']
				);
				let lastTripToBeDisplayed = _find(
					sortedFilterMissionArrayLocalTime,
					function (o) {
						return o.EndDate !== 'NA';
					}
				);
				let findThings = _find(this.state.totalData.things, {
					id: findLastData.thing_id,
				});
				let fuelCapacity =
					findThings &&
					findThings.thing_details &&
					findThings.thing_details.capacity
						? findThings.thing_details.capacity
						: '';
				let fuelLevel = findLastData.data.fuel
					? parseFloat(findLastData.data.fuel)
					: _find(findThings.parameters, { key: 'fuel' })
					? _find(findThings.parameters, { key: 'fuel' }).value
					: '';
				fuelLevel = fuelLevel > 100 ? 100 : fuelLevel;
				let fuelLevelInLtr = parseFloat(
					(fuelLevel * fuelCapacity) / 100
				);
				let findCalculatedLifetimeRunhour = 0;
				let findRnHr = _find(findThings.parameters, {
					key: 'rnhr',
				});
				let findCalcRnhr = _find(findThings.parameters, {
					key: 'calculated_runhour',
				});
				if (
					findRnHr &&
					findRnHr.value &&
					findRnHr.value !== '' &&
					parseFloat(findRnHr.value) > 0
				) {
					findCalculatedLifetimeRunhour =
						parseFloat(findRnHr.value) * 3600;
				} else if (
					findCalcRnhr &&
					findCalcRnhr.aggregated_value &&
					findCalcRnhr.aggregated_value.lifetime &&
					findCalcRnhr.aggregated_value.lifetime.sum
				) {
					findCalculatedLifetimeRunhour =
						findCalcRnhr.aggregated_value.lifetime.sum;
				}
				let totalRnhr = 0,
					totalFuelCons = 0;
				if (
					this.state.missionList &&
					this.state.missionList.response &&
					this.state.missionList.response.Missions &&
					this.state.missionList.response.Missions.length
				) {
					for (
						let ind = 0;
						ind < this.state.missionList.response.Missions.length;
						ind++
					) {
						if (
							this.state.missionList.response.Missions[ind]
								.Details &&
							this.state.missionList.response.Missions[ind]
								.Details.aggregate_data &&
							this.state.missionList.response.Missions[ind]
								.Details.aggregate_data.calculated_runhour &&
							this.state.missionList.response.Missions[ind]
								.Details.aggregate_data.calculated_runhour.sum >
								0 &&
							this.state.missionList.response.Missions[ind]
								.Details &&
							this.state.missionList.response.Missions[ind]
								.Details.aggregate_data &&
							this.state.missionList.response.Missions[ind]
								.Details.aggregate_data.fuel_consumption &&
							this.state.missionList.response.Missions[ind]
								.Details.aggregate_data.fuel_consumption.sum > 0
						) {
							totalRnhr += this.state.missionList.response
								.Missions[ind].Details.aggregate_data
								.calculated_runhour.sum;
							totalFuelCons += this.state.missionList.response
								.Missions[ind].Details.aggregate_data
								.fuel_consumption.sum;
						}
						if (totalRnhr > 86400) {
							break;
						}
					}
				}
				let calculateEstimatedRunhour = 'NA';
				if (totalRnhr > 3600) {
					calculateEstimatedRunhour =
						totalFuelCons > 0
							? (totalRnhr * fuelLevelInLtr) / totalFuelCons
							: 'NA';
				}
				let estimatedRunhourValue = 'NA';
				if (calculateEstimatedRunhour !== 'NA') {
					let estimatedHour = Math.floor(
						calculateEstimatedRunhour / 3600
					);
					let estimatedMin = Math.floor(
						(calculateEstimatedRunhour % 3600) / 60
					);
					estimatedRunhourValue =
						(estimatedHour < 10
							? '0' + estimatedHour
							: estimatedHour) +
						' : ' +
						(estimatedMin < 10
							? '0' + estimatedMin
							: estimatedMin) +
						' Hrs';
				}

				let fuelLevelLatestEvent = 'NA',
					fuelLevelLatestEventTime = 'NA';
				if (
					findThings &&
					findThings.latest_events &&
					findThings.latest_events.tags &&
					findThings.latest_events.tags['Fuel Filled']
				) {
					if (
						findThings.latest_events.tags['Fuel Filled'].data &&
						findThings.latest_events.tags['Fuel Filled'].data
							.fuel_filled
					) {
						fuelLevelLatestEvent =
							parseFloat(
								findThings.latest_events.tags['Fuel Filled']
									.data.fuel_filled
							).toFixed(2) + ' L';
					}
					if (
						findThings.latest_events.tags['Fuel Filled'].event_time
					) {
						fuelLevelLatestEventTime = moment
							.unix(
								findThings.latest_events.tags['Fuel Filled']
									.event_time
							)
							.format('DD MMM YYYY, HH:mm');
					}
				}
				let lifetimehour = Math.floor(
					findCalculatedLifetimeRunhour / 3600
				);
				let lifetimeMin = Math.floor(
					(findCalculatedLifetimeRunhour % 3600) / 60
				);
				let lifeTimeRunHourValue =
					(lifetimehour < 10 ? '0' + lifetimehour : lifetimehour) +
					' : ' +
					(lifetimeMin < 10 ? '0' + lifetimeMin : lifetimeMin) +
					' Hrs';
				PanelConfigObject.data.parameterValues.map((params) => {
					let findParamDataWithKey = _find(
						this.state.modifiedResponse.param_data,
						{
							key: params.key,
						}
					);
					if (findParamDataWithKey) {
						parameterList[findLastData.thing_id].push({
							name: findParamDataWithKey.name,
							data: findLastData.data[params.key]
								? parseFloat(
										findLastData.data[params.key]
								  ).toFixed(2) +
								  ' ' +
								  findParamDataWithKey.unit
								: 'NA',
						});
					} else {
						parameterList[findLastData.thing_id].push({
							name: 'NA',
							data: 'NA',
						});
					}
					return parameterList;
				});

				parameterList[findLastData.thing_id].map(
					(parameterValuesData, parameterValuesInd) => {
						return parameterValuesObject[
							findLastData.thing_id
						].push({
							icon:
								PanelConfigObject.data.parameterValues[
									parameterValuesInd
								].icon,
							value: parameterValuesData,
						});
					}
				);
				let triphour = 0,
					tripMin = 0,
					tripFuelCons = 'NA',
					enrgValue = 'NA',
					loadValue = 'NA';
				if (
					lastTripToBeDisplayed &&
					lastTripToBeDisplayed.Details &&
					lastTripToBeDisplayed.Details.aggregate_data
				) {
					let calculatedRnhr = lastTripToBeDisplayed.Details
						.aggregate_data.calculated_runhour
						? lastTripToBeDisplayed.Details.aggregate_data
								.calculated_runhour.sum
						: 0;

					triphour = Math.floor(calculatedRnhr / 3600);
					tripMin = Math.floor((calculatedRnhr % 3600) / 60);
					let findLoad = _find(findThings.parameters, {
						key: 'load_percentage',
					});
					if (findLoad && findLoad !== undefined) {
						if (
							lastTripToBeDisplayed.Details.aggregate_data
								.load_percentage &&
							!isNaN(
								lastTripToBeDisplayed.Details.aggregate_data
									.load_percentage.avg
							)
						) {
							loadValue = parseFloat(
								lastTripToBeDisplayed.Details.aggregate_data
									.load_percentage.avg
							).toFixed(2);
						} else {
							loadValue = 'NA';
						}
					} else {
						loadValue = 'NA';
					}
					let findCalculatedEnrg = _find(findThings.parameters, {
						key: 'calculated_energy',
					});
					if (
						findCalculatedEnrg &&
						findCalculatedEnrg !== undefined
					) {
						if (
							lastTripToBeDisplayed.Details.aggregate_data
								.calculated_energy &&
							!isNaN(
								lastTripToBeDisplayed.Details.aggregate_data
									.calculated_energy.sum
							)
						) {
							enrgValue = parseFloat(
								lastTripToBeDisplayed.Details.aggregate_data
									.calculated_energy.sum
							).toFixed(2);
						} else {
							enrgValue = 'NA';
						}
					} else if (
						lastTripToBeDisplayed.Details.aggregate_data.enrg
					) {
						enrgValue = parseFloat(
							lastTripToBeDisplayed.Details.aggregate_data.enrg
								.sum
						).toFixed(2);
					} else {
						enrgValue = 'NA';
					}
					if (
						lastTripToBeDisplayed.Details.aggregate_data
							.fuel_consumption
					) {
						tripFuelCons = parseFloat(
							lastTripToBeDisplayed.Details.aggregate_data
								.fuel_consumption.sum
						).toFixed(2);
					}
				}
				tripDataObj[findLastData.thing_id].push(
					(triphour < 10 ? '0' + triphour : triphour) +
						' : ' +
						(tripMin < 10 ? '0' + tripMin : tripMin),
					tripFuelCons,
					enrgValue,
					loadValue
				);
				// let tripParamArray = [
				// 	'calculated_runhour',
				// 	'fuel_consumption',
				// 	'enrg',
				// 	'load_percentage',
				// ];
				// tripParamArray.map((tripData, tripInd) => {
				// 	return tripDataValue[findLastData.thing_id].push({
				// 		name: _find(this.state.modifiedResponse.param_data, {
				// 			key: tripData,
				// 		})
				// 			? _find(this.state.modifiedResponse.param_data, {
				// 					key: tripData,
				// 			  }).name
				// 			: '',
				// 		icon:
				// 			PanelConfigObject.data.lastTripDetails.paramDetails[
				// 				tripInd
				// 			].icon,
				// 		value:
				// 			tripDataObj[findLastData.thing_id][tripInd] +
				// 			' ' +
				// 			(tripData === 'calculated_runhour'
				// 				? 'Hrs'
				// 				: tripData === 'enrg'
				// 				? _find(
				// 						this.state.modifiedResponse.param_data,
				// 						{
				// 							key: 'enrg',
				// 						}
				// 				  )
				// 					? _find(
				// 							this.state.modifiedResponse
				// 								.param_data,
				// 							{
				// 								key: 'enrg',
				// 							}
				// 					  ).unit
				// 					: _find(
				// 							this.state.modifiedResponse
				// 								.param_data,
				// 							{
				// 								key: 'calculated_energy',
				// 							}
				// 					  )
				// 					? _find(
				// 							this.state.modifiedResponse
				// 								.param_data,
				// 							{
				// 								key: 'calculated_energy',
				// 							}
				// 					  ).unit
				// 					: 'kWh'
				// 				: _find(
				// 						this.state.modifiedResponse.param_data,
				// 						{
				// 							key: tripData,
				// 						}
				// 				  )
				// 				? _find(
				// 						this.state.modifiedResponse.param_data,
				// 						{
				// 							key: tripData,
				// 						}
				// 				  ).unit
				// 				: ''),
				// 	});
				// });
				let lastFaultData = getFault(
					findThings,
					this.state.faultResponse
				);
				let runhourValue = _find(findThings.parameters, {
					key: 'rnhr',
				})
					? _find(findThings.parameters, { key: 'rnhr' }).value
					: _find(findThings.parameters, {
							key: 'calculated_runhour',
					  }) &&
					  _find(findThings.parameters, {
							key: 'calculated_runhour',
					  }).aggregated_value &&
					  _find(findThings.parameters, {
							key: 'calculated_runhour',
					  }).aggregated_value.lifetime
					? _find(findThings.parameters, {
							key: 'calculated_runhour',
					  }).aggregated_value.lifetime.sum
					: '';
				runhourValue = !isNaN(parseFloat(runhourValue))
					? parseFloat(runhourValue)
					: 0;
				let commissionTime =
					findThings.thing_details &&
					findThings.thing_details.commissioning_date
						? findThings.thing_details.commissioning_date
						: 0;
				let sortedMaintenance = maintenanceCalculation(
					runhourValue,
					commissionTime
				);
				let tripParamArray = [];
				if (
					this.props.plan_description &&
					this.props.plan_description.dashboard_view_section &&
					this.props.plan_description.dashboard_view_section
						.last_dg_run_parameters &&
					this.props.plan_description.dashboard_view_section
						.last_dg_run_parameters.length
				) {
					this.props.plan_description.dashboard_view_section.last_dg_run_parameters.map(
						(keys) => {
							if (keys === 'rnhr') {
								tripDataObj[findLastData.thing_id].push(
									(triphour < 10
										? '0' + triphour
										: triphour) +
										' : ' +
										(tripMin < 10 ? '0' + tripMin : tripMin)
								);
								tripParamArray.push('calculated_runhour');
							}
							if (keys === 'fuel_consumption') {
								tripDataObj[findLastData.thing_id].push(
									tripFuelCons
								);
								tripParamArray.push('fuel_consumption');
							}
							if (keys === 'enrg') {
								tripDataObj[findLastData.thing_id].push(
									enrgValue
								);
								tripParamArray.push('enrg');
							}
							if (keys === 'load_percentage') {
								tripDataObj[findLastData.thing_id].push(
									loadValue
								);
								tripParamArray.push('load_percentage');
							}
						}
					);
				}
				// tripDataObj[findLastData.thing_id].push(
				// 	(triphour < 10 ? '0' + triphour : triphour) +
				// 		' : ' +
				// 		(tripMin < 10 ? '0' + tripMin : tripMin),
				// 	tripFuelCons,
				// 	enrgValue,
				// 	loadValue
				// );
				tripParamArray.map((tripData, tripInd) => {
					return tripDataValue[findLastData.thing_id].push({
						name: _find(this.state.modifiedResponse.param_data, {
							key: tripData,
						})
							? _find(this.state.modifiedResponse.param_data, {
									key: tripData,
							  }).name
							: '',
						icon: _find(
							PanelConfigObject.data.lastTripDetails.paramDetails,
							{ key: tripData }
						).icon,
						value:
							tripDataObj[findLastData.thing_id][tripInd] +
							' ' +
							(_find(this.state.modifiedResponse.param_data, {
								key: tripData,
							})
								? tripData === 'calculated_runhour'
									? 'Hrs'
									: _find(
											this.state.modifiedResponse
												.param_data,
											{
												key: tripData,
											}
									  ).unit
								: ''),
					});
				});
				totalPanelData.push({
					panelId: findLastData.thing_id,
					thingName: this.state.modifiedResponse.thing_name_list[
						findLastData.thing_id
					],
					kva:
						findThings.thing_details &&
						!isNaN(
							parseFloat(findThings.thing_details.kva).toFixed(2)
						) &&
						!fuelTankThing
							? '(' +
							  parseFloat(findThings.thing_details.kva) +
							  ' KVA)'
							: '',
					thingValue: {
						thing_name: this.state.modifiedResponse.thing_name_list[
							findLastData.thing_id
						],
						date:
							findLastData.time === 0
								? 'No Data Received'
								: moment
										.unix(findLastData.time)
										.format('DD MMM YYYY, HH:mm'),
					},
					dgStatus: findLastData.OnOffStatus,
					is_moving:
						this.isGpsPathEnabled() &&
						parseInt(findLastData.data?.is_moving),
					runningStatus:
						findLastData.OnOffStatus === '1'
							? 'Running'
							: findLastData.OnOffStatus === '2'
							? 'Not Connected'
							: 'Stopped',
					commandStatus: _find(this.state.totalData.things, {
						id: findLastData.thing_id,
					}).commands,
					dg_lock_status:
						(findThings &&
							findThings.thing_details &&
							findThings.thing_details.dg_lock_status) ||
						'0',
					isControlEnabled:
						findThings &&
						findThings.thing_details &&
						findThings.thing_details
							.is_start_stop_control_enabled === 'enable'
							? true
							: false,
					tankData: {
						tank_level: fuelLevel,
						tank_capacity: fuelCapacity,
					},
					estimatedRunhourLastFuelFilledValue: fuelTankThing
						? [
								{
									key: 'current_fuel_level',
									name: 'Current Fuel Level',
									value:
										parseFloat(fuelLevelInLtr).toFixed(2) +
										' L',
								},
								{
									key: 'last_fuel_filled',
									name: 'Last Fuel Filled',
									value: fuelLevelLatestEvent,
								},
						  ]
						: this.props.plan_description &&
						  this.props.plan_description.estimated_runhour
						? [
								{
									key: 'estimated',
									name: 'Estimated Runhour',
									value: estimatedRunhourValue,
								},
								{
									key: 'last_fuel_filled',
									name: 'Last Fuel Filled',
									value: fuelLevelLatestEvent,
								},
						  ]
						: [
								{
									key: 'last_fuel_filled',
									name: 'Last Fuel Filled',
									value: fuelLevelLatestEvent,
								},
						  ],
					lastFuelFilledTime: fuelLevelLatestEventTime,
					lifetimeRunhourDataValue: !fuelTankThing
						? [
								{
									key: 'lifetime_runhour',
									name: lifeTimeRunHourValue,
									value: 'Lifetime Runhour',
								},
						  ]
						: [],
					parameterValues: !fuelTankThing
						? parameterValuesObject[findLastData.thing_id]
						: {},
					maintenance_on:
						sortedMaintenance &&
						Object.keys(sortedMaintenance).length &&
						!fuelTankThing
							? ' Next maintenance due: ' +
							  sortedMaintenance.runhour +
							  ' Hr' +
							  (commissionTime.toString().length > 1
									? ' / ' + sortedMaintenance.date
									: '')
							: '',
					last_fault_data: {
						name:
							lastFaultData && lastFaultData.length
								? lastFaultData[0].name
								: '',
						time:
							lastFaultData && lastFaultData.length
								? lastFaultData[0].date_time
								: '',
						duration:
							lastFaultData && lastFaultData.length
								? lastFaultData[0].duration
									? '(' + lastFaultData[0].duration + ')'
									: '(Ongoing)'
								: '',
					},
					lastTripDetails: !fuelTankThing
						? this.props.plan_description.dashboard_view_section
								.last_dg_run
							? {
									heading: 'Last DG Run Details',
									date: lastTripToBeDisplayed
										? moment(
												lastTripToBeDisplayed.StartDate,
												moment.ISO_8601
										  ).format('DD MMM YYYY,  HH:mm') +
										  ' to ' +
										  (moment(
												lastTripToBeDisplayed.StartDate,
												moment.ISO_8601
										  ).format('DD MMM YYYY') ===
										  moment(
												lastTripToBeDisplayed.EndDate,
												moment.ISO_8601
										  ).format('DD MMM YYYY')
												? moment(
														lastTripToBeDisplayed.EndDate,
														moment.ISO_8601
												  ).format('HH:mm')
												: moment(
														lastTripToBeDisplayed.EndDate,
														moment.ISO_8601
												  ).format(
														'DD MMM YYYY,  HH:mm'
												  ))
										: '',
									paramDetails:
										tripDataValue[findLastData.thing_id],
							  }
							: {}
						: {},
					fuelTankThing: fuelTankThing,
					graphs: fuelTankThing ? this.getPanelGraph() : {},
					fuel_tank_status: this.checkOfflineOnlineStatus(listId),
					isThingMechanicalDG: this.isThingMechanicalDG(findLastData.thing_id)
				});
				PanelConfigObject.total_and_running_thing = [];
				PanelConfigObject.filter_data = [];
				PanelConfigObject.data = totalPanelData[0];
				return PanelConfigObject;
			}
		}
	}

	listOnClick(listId, is_moving, moving_start_time, isDgOffline) {
		this.setState(
			{
				trendNotificationLoading: true,
				panelView: true,
				listId: listId,
				fromTime: moment().subtract(30, 'days').startOf('day').unix(),
				uptoTime: moment().endOf('day').unix(),
				listLoading: true,
				moving_start_time,
			},
			async () => {
				if (is_moving && !isDgOffline) {
					await this.getThingsRawData();
				}
				await this.getThingDailyAggrData();
				await this.getEvents(this.listApiData);
				await this.getfaultApi();
				if (
					findFuelTankThing(
						this.state.fuel_tank_things,
						this.state.listId
					)
				) {
					await this.thingsDataFunction();
				}
				await this.fetchMissionList(this.state.listId);
				this.getPanelData(this.state.listId);
			}
		);
	}

	onCloseClick() {
		this.setState(
			{
				panelView: false,
				listId: undefined,
				trendNotificationLoading: true,
				fromTime: moment().subtract(30, 'days').startOf('day').unix(),
				uptoTime: moment().endOf('day').unix(),
				moving_start_time: undefined,
				latLngData: undefined,
				latLngDataWithTime: undefined,
				latLngDataWithAddress: undefined,
				zoomLevel: undefined,
				centerValue: undefined,
			},
			async () => {
				await this.getThingDailyAggrData();
				await this.getEvents(this.listApiData);
			}
		);
	}

	kpiOnClick(key) {
		this.setState(
			{
				type: key,
				listId: undefined,
				trendNotificationLoading: true,
				fromTime: moment().subtract(30, 'days').startOf('day').unix(),
				uptoTime: moment().endOf('day').unix(),
			},
			async () => {
				if(!this.props.dg_in_iot_mode){
					this.props.history.push(
						'/dg-monitoring/map-view/?type=' + this.state.type
					);
				}
				await this.getThingDailyAggrData();
				await this.getEvents(this.listApiData);
				this.latestDataFunc();
			}
		);
	}

	async componentDidMount() {
		window.addEventListener('resize', this.windowResize.bind(this));
		await this.getThingsList(this.listApiData);
		if(!this.props.dg_in_iot_mode){
			this.props.history.push(
				'/dg-monitoring/map-view/?type=' + this.state.type
			);
		}
		this.socket = establishSocketConnection();
		this.socket.on('connect', () => {
			subscribeForThingsUpdates(
				this.socket,
				this.state.modifiedResponse.all_thing_ids,
				0
			);
			subscribeForThingsUpdates(
				this.socket,
				this.state.modifiedResponse.all_thing_ids,
				86400
			);
			subscribeForEventsUpdates(
				this.socket,
				this.listApiData.client_id,
				this.listApiData.application_id
			);
			subscribeForEntityUpdates(
				this.socket,
				this.state.modifiedResponse.all_thing_ids,
				'thing'
			);
		});
		this.socket.on('new_event_generated', (payload) => {
			if (payload) {
				this.realTimeEventsUpdate(payload);
				let realtimeEventsData = this.state.events;
				realtimeEventsData.unshift({
					entity_id: parseInt(payload.entity_id),
					type: payload.type,
					generated_at: payload.generated_at,
					message: payload.message,
					tags: payload.tags,
					details: payload.details,
				});
				this.setState(
					{
						events: realtimeEventsData,
					},
				);
			}
		});
		this.socket.on('new_data_generated_for_station', (payload) => {
			if (payload) {
				this.realTimeDataFunc(payload);
				let realtimeThingsData = this.state.latestParameterData;
				let dailyAggrData = this.state.responseData;
				if (payload.type === 'raw') {
					let findIndex = _findIndex((realtimeThingsData, {
						thing_id: parseInt(payload.thing_id),
					});
					if (findIndex > -1) {
						realtimeThingsData[findIndex]['time'] = payload.time;
						realtimeThingsData[findIndex]['data'] = payload.data;
					}
				} else if (
					payload.type === '1_day_avg' &&
					payload.time >= this.state.fromTime &&
					payload.time <= this.state.uptoTime
				) {
					let findIndexAggrDataForTime = _findIndex((
						dailyAggrData,
						function (o) {
							return (
								moment.unix(o.time).format('DD MM YYYY') ===
									moment
										.unix(payload.time)
										.format('DD MM YYYY') &&
								o.thing_id === parseInt(payload.thing_id)
							);
						}
					);
					if (findIndexAggrDataForTime === -1) {
						dailyAggrData.push({
							thing_id: parseInt(payload.thing_id),
							time: payload.time,
							parameter_values: payload.data,
						});
					} else {
						dailyAggrData[findIndexAggrDataForTime] = {
							thing_id: parseInt(payload.thing_id),
							time: payload.time,
							parameter_values: payload.data,
						};
					}
				}
				let latestParameterData = this.offlineTimeOutFunction(
					payload,
					realtimeThingsData,
					this.state.modifiedResponse
				);
				this.setState(
					{
						latestParameterData: latestParameterData,
						responseData: dailyAggrData,
					},
					() => {
						this.latestDataFunc();
						//	this.updatelatLngData();
						if (this.state.listId) {
							this.getPanelData(this.state.listId);
						}
					}
				);
			}
		});
		this.socket.on('details_updated', (payload) => {
			this.updateEntityDetails(payload);
		});
		await this.getThingDailyAggrData();
		await this.getEvents(this.listApiData);
	}

	updateEntityDetails(payload) {
		if (
			payload.entity_type === 'thing' &&
			!isNaN(parseInt(payload.entity_id)) &&
			payload.details
		) {
			const { totalData } = this.state;
			if (totalData.things && totalData.things.length) {
				let totalDataResponse = JSON.parse(JSON.stringify(totalData));
				let thingDetailsIndex = _findIndex((totalDataResponse.things, {
					id: parseInt(payload.entity_id),
				});
				if (
					thingDetailsIndex > -1 &&
					totalDataResponse &&
					totalDataResponse.things
				) {
					totalDataResponse.things[thingDetailsIndex] = _merge(
						{},
						totalDataResponse.things[thingDetailsIndex],
						payload.details
					);
				}
				this.setState(
					{
						totalData: totalDataResponse,
					},
					() => {
						this.listViewFunction();
						if (this.state.listId) {
							this.getPanelData(this.state.listId);
						}
					}
				);
			}
		}
	}

	componentWillUnmount() {
		window.removeEventListener('resize', this.windowResize.bind(this));
		disconnectSocketConnection(this.socket);
	}

	handleChange(e) {
		this.setState(
			{
				dateSelected: e,
			},
			() => this.callingDatePicker(this.state.dateSelected)
		);
	}

	callingDatePicker(date_value) {
		let fromTimeGenerator, uptoTimeGenerator;
		if (date_value === 'this_month') {
			fromTimeGenerator = moment().startOf('month').unix();
			uptoTimeGenerator = moment().endOf('month').unix();
		} else if (date_value === 'last_month') {
			fromTimeGenerator = moment()
				.subtract(1, 'month')
				.startOf('month')
				.unix();
			uptoTimeGenerator = moment()
				.subtract(1, 'month')
				.endOf('month')
				.unix();
		} else if (date_value === 'last_three_months') {
			fromTimeGenerator = moment()
				.subtract(3, 'month')
				.startOf('month')
				.unix();
			uptoTimeGenerator = moment()
				.subtract(1, 'month')
				.endOf('month')
				.unix();
		} else if (date_value === 'last_thirty_days') {
			fromTimeGenerator = moment()
				.subtract(30, 'days')
				.startOf('day')
				.unix();
			uptoTimeGenerator = moment().endOf('day').unix();
		} else if (date_value === 'last_one_twenty_days') {
			fromTimeGenerator = moment()
				.subtract(120, 'days')
				.startOf('day')
				.unix();
			uptoTimeGenerator = moment().endOf('day').unix();
		}
		this.setState(
			{
				fromTime: fromTimeGenerator,
				uptoTime: uptoTimeGenerator,
			},
			() => {
				this.getThingDailyAggrData();
				this.getEvents(this.listApiData);
			}
		);
	}

	ResToggleSwitch(e) {
		this.setState({
			resButton: e,
		});
	}
	noFuelAtAll() {
		let Fuel = true,
			fuelCount = 0;
		if (this.state.listId) {
			let isFuel = true;
			if (this.state.totalData) {
				let currentAsset = _find(this.state.totalData.things, {
					id: this.state.listId,
				});
				if (
					currentAsset &&
					currentAsset.parameters &&
					currentAsset.parameters.length
				) {
					isFuel = currentAsset.parameters.some((currParam) => {
						return currParam.key === 'fuel';
					});
				}
			}
			return !isFuel;
		} else {
			if (
				this.state.totalData &&
				this.state.latestRcvd &&
				this.state.latestRcvd.length
			) {
				this.state.latestRcvd.map((asset) => {
					let currentAsset = _find(this.state.totalData.things, {
						id: parseInt(asset),
					});
					if (
						currentAsset &&
						currentAsset.parameters &&
						currentAsset.parameters.length
					) {
						Fuel = currentAsset.parameters.some((currParam) => {
							return currParam.key === 'fuel';
						});
						if (!Fuel) {
							fuelCount += 1;
						}
					}
				});
				if (fuelCount === this.state.latestRcvd.length) {
					return true;
				}
			}
			return false;
		}
	}
	isOnlyFuelDG() {
		let Fuel = false,
			fuelCount = 0;
		if (this.state.listId) {
			let isOnlyFuel = false;
			if (this.state.totalData) {
				let currentAsset = _find(this.state.totalData.things, {
					id: this.state.listId,
				});
				if (
					currentAsset &&
					currentAsset.thing_details &&
					currentAsset.thing_details.dg_parameter_type
				) {
					isOnlyFuel =
						currentAsset.thing_details.dg_parameter_type ===
						'fuel_only';
				}
			}
			return (
				isOnlyFuel || (this.isMechanicalDG(1) && !this.noFuelAtAll())
			);
		} else {
			if (
				this.state.totalData &&
				this.state.latestRcvd &&
				this.state.latestRcvd.length
			) {
				this.state.latestRcvd.map((asset) => {
					let currentAsset = _find(this.state.totalData.things, {
						id: parseInt(asset),
					});
					if (
						currentAsset &&
						currentAsset.thing_details &&
						currentAsset.thing_details.dg_parameter_type
					) {
						Fuel =
							currentAsset.thing_details.dg_parameter_type ===
							'fuel_only';
						if (Fuel) {
							fuelCount += 1;
						}
					}
				});
				if (fuelCount === this.state.latestRcvd.length) {
					return true;
				}
			}
			return this.isMechanicalDG(1) && !this.noFuelAtAll();
		}
	}

	isThingMechanicalDG(selectedThing) {
		let isMechanicalDG = false;
		if (this.state.totalData) {
			let currentAsset = _find(this.state.totalData.things, {
				id: parseInt(selectedThing),
			});
			if (
				currentAsset &&
				currentAsset.thing_details &&
				currentAsset.thing_details.dg_parameter_type
			) {
				isMechanicalDG =
					currentAsset.thing_details.dg_parameter_type ===
					'mechanical';
			}
		}
		return isMechanicalDG;
	}

	isMechanicalDG(fuel = false) {
		let Mech = false,
			mechanicalCount = 0;
		if (this.state.listId) {
			let isMechanicalDG = false;
			if (this.state.totalData) {
				let currentAsset = _find(this.state.totalData.things, {
					id: this.state.listId,
				});
				if (
					currentAsset &&
					currentAsset.thing_details &&
					currentAsset.thing_details.dg_parameter_type
				) {
					isMechanicalDG =
						currentAsset.thing_details.dg_parameter_type ===
						'mechanical';
				}
			}
			if (fuel) {
				return isMechanicalDG;
			}
			return isMechanicalDG && this.noFuelAtAll();
		} else {
			if (
				this.state.totalData &&
				this.state.latestRcvd &&
				this.state.latestRcvd.length
			) {
				this.state.latestRcvd.map((asset) => {
					let currentAsset = _find(this.state.totalData.things, {
						id: parseInt(asset),
					});
					if (
						currentAsset &&
						currentAsset.thing_details &&
						currentAsset.thing_details.dg_parameter_type
					) {
						Mech =
							currentAsset.thing_details.dg_parameter_type ===
							'mechanical';
						if (Mech) {
							mechanicalCount += 1;
						}
					}
				});
				if (mechanicalCount === this.state.latestRcvd.length) {
					if (fuel) {
						return true;
					}
					return this.noFuelAtAll();
				}
			}
			return false;
		}
	}
	drawerIconClick() {
		this.setState({
			drawer_visible: true,
		});
	}

	closeDrawer() {
		this.setState({
			drawer_visible: false,
		});
	}
	render() {
		let isFuelPanel = true, //basic_fuel
			isPanelOnlyFuel = false,
			isPanelMechanical = false,
			isFuelMechanical = false;
		let mapRawData = {
			rawData: this.state.latestParameterData,
			modifiedResponseRaw: this.state.modifiedResponse,
		};

		let pageRender = '';
		if (this.state.loading) {
			pageRender = (
				<Loading
					t={this.props.t}
					show_logo={this.props.loading_logo}
					className="align-center-loading"
				/>
			);
		} else {
			let mapData = this.mapFunction();
			let cardData = this.filterCardFuncTion();
			let listRender = '';
			let panelData = '';
			let PageMapRender = (
				<GoogleMapComponent
					data={mapRawData}
					mapOnClick={(
						hoverText,
						lastData,
						modifiedResponse,
						id,
						is_moving,
						moving_start_time,
						isDgOffline
					) =>
						this.mapOnClick(
							hoverText,
							lastData,
							modifiedResponse,
							id,
							is_moving,
							moving_start_time,
							isDgOffline
						)
					}
					reverseGeocode={async (latlng) =>
						await this.reverseGeocode(latlng)
					}
					calculateDistance={(latLngData) =>
						this.calculateDistance(latLngData)
					}
					calculateDuration={(moving_start_time) =>
						this.calculateDuration(moving_start_time)
					}
					calculateSpeed={(latLngDataWithTime) =>
						this.calculateSpeed(latLngDataWithTime)
					}
					latLngData={this.state.latLngData}
					panelView={this.state.panelView}
					listId={this.state.listId}
					{...mapData.config}
					legends={mapData.map_legend}
					mapPointers={mapData.map_pointers}
					zoomLevel={this.state.zoomLevel}
					centerValue={this.state.centerValue}
					isGpsPathEnabled={this.isGpsPathEnabled()}
				/>
			);
			let mobileConfig = [];
			if(cardData && cardData.data && cardData.data.length) {
				cardData.data.map((parameterDataDetails) => {
					mobileConfig.push(
						<div className="each-option-container">
							<AntButton
								className={`each-option ${
									parameterDataDetails.key === this.state.type
										? ' active-each'
										: ''
								}`}
								onClick={() => this.kpiOnClick(
									parameterDataDetails.key
								)}
							>
								<span className={'status ' + (parameterDataDetails.key === 'offline' ? 'gray' : parameterDataDetails.key === 'running' ? 'green' : parameterDataDetails.key === 'switch_off' ? 'red' : '')}></span><b>{parameterDataDetails.value}</b>{parameterDataDetails.key === 'all_things' ? 'All Assets' : parameterDataDetails.key === 'offline' ? 'Not Con.' : parameterDataDetails.name}
							</AntButton>
							{this.state.type === parameterDataDetails.value ? (
								''
							) : (
								<AntDivider
									class="option-divider"
									type="vertical"
								/>
							)}
						</div>
					);
				})	
			}
			let KpiFilterRender = window.innerWidth < 576 ? <div className="button-tray">{mobileConfig}</div> : <Kpis
				kpiOnClick={(key) => this.kpiOnClick(key)}
				{...cardData.config}
				data={cardData.data}
				activeKpi={this.state.type}
			/>
			if (this.state.listId) {
				panelData = this.getPanelData(this.state.listId);
			}
			if (this.state.listLoading) {
				listRender = (
					<div className="list-view-container">
						<AntSpin className="align-center-loading" />
					</div>
				);
			} else if (this.state.panelView && panelData) {
				if (this.state.listId) {
					if (this.state.totalData) {
						let currentAsset = _find(this.state.totalData.things, {
							id: this.state.listId,
						});
						if (
							//basic_fuel
							currentAsset &&
							currentAsset.parameters &&
							currentAsset.parameters.length
						) {
							isFuelPanel = currentAsset.parameters.some(
								(currParam) => {
									return currParam.key === 'fuel';
								}
							);
						}
						if (
							currentAsset &&
							currentAsset.thing_details &&
							currentAsset.thing_details.dg_parameter_type
						) {
							isPanelMechanical =
								currentAsset.thing_details.dg_parameter_type ===
									'mechanical' && !isFuelPanel;
							isFuelMechanical =
								currentAsset.thing_details.dg_parameter_type ===
								'mechanical';
						}
						if (
							currentAsset &&
							currentAsset.thing_details &&
							currentAsset.thing_details.dg_parameter_type
						) {
							isPanelOnlyFuel =
								currentAsset.thing_details.dg_parameter_type ===
									'fuel_only' ||
								(isFuelMechanical && isFuelPanel);
						}
					}
				}
				listRender = (
					<div className="list-view-container">
						<PanelComponent
							socket={this.socket}
							plan_description={this.props.plan_description}
							client_id={this.props.client_id}
							vendor_id={this.props.vendor_id}
							application_id={this.props.application_id}
							getRemoteAccess={this.props.getRemoteAccess()}
							onCloseClick={() => this.onCloseClick()}
							closeEnabled={true}
							isWithoutFuel={!isFuelPanel}
							isPanelOnlyFuel={isPanelOnlyFuel}
							isPanelMechanical={isPanelMechanical}
							isFuelMechanical={isFuelMechanical}
							history={this.props.history}
							data={panelData.data}
							dgBasePath={this.dgBasePath}
							latLngDataWithAddress={
								this.state.latLngDataWithAddress
							}
							calculateDistance={(latLngData) =>
								this.calculateDistance(latLngData)
							}
							calculateDuration={(moving_start_time) =>
								this.calculateDuration(moving_start_time)
							}
							calculateSpeed={(latLngDataWithTime) =>
								this.calculateSpeed(latLngDataWithTime)
							}
							{...panelData.config}
							dateChange={(e) => this.dateChange(e)}
							fuel_trend_loading={this.state.fuel_trend_loading}
						/>
					</div>
				);
			} else {
				let listViewData = this.listViewFunction();
				listRender = (
					<ListView
						socket={this.socket}
						client_id={this.props.client_id}
						application_id={this.props.application_id}
						getRemoteAccess={this.props.getRemoteAccess()}
						listOnClick={(
							listId,
							is_moving,
							moving_start_time,
							isDgOffline
						) =>
							this.listOnClick(
								listId,
								is_moving,
								moving_start_time,
								isDgOffline
							)
						}
						getRemoteLockAccess={this.props.getRemoteLockAccess()}
						data={listViewData.data}
						{...listViewData.config}
						history={this.props.history}
						user_id={this.props.user_id}
						dgBasePath={this.dgBasePath}
					/>
				);
			}
			pageRender = (
				<div id="dg_map_view">
					{PageMapRender}
					<div className='filter-section'>{KpiFilterRender}</div>
					<div
						className="drawer-icon"
						onClick={() => this.drawerIconClick()}
					>
						<SearchOutlined />
					</div>
					{this.state.mobile_visible ? (
						''
					) : 
					<AntDrawer
						placement="right"
						visible={this.state.drawer_visible}
						onClose={() => this.closeDrawer()}
						closable={!this.state.panelView}
						className="dg-map-view-drawer"
						getContainer={false}
						drawerStyle={{
							background: '#FFFFFF 0% 0% no-repeat padding-box',
							boxShadow: '15px 16px 25px #92929229',
						}}
						mask={false}
						width={
							window.innerWidth > 576
								? 444
								: '100%'
						}
						destroyOnClose={true}
						styleType={'rounded'}
					>{listRender}</AntDrawer>}
				</div>
			);
		}

		return <AntLayout className="layout">{pageRender}</AntLayout>;
	}
}
