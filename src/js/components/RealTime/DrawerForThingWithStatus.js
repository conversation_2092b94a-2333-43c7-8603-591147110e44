import React from "react";
import AntDrawer from "@datoms/react-components/src/components/AntDrawer";
import SearchInput from "@datoms/react-components/src/components/SearchInput";
import SearchObjectData from "../../configuration/SearchObjectData";
import NoSearchImage from "../../../images/DGMonitoring/no-data-icon/search.svg";
import "../../../styles/DGMonitoring/Template-20/component/drawer-for-thing-status.less";

export default class DrawerForThingWithStatus extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      SearchObjectData: SearchObjectData,
    };
  }
  onSearch(e) {
    let SearchData = this.state.SearchObjectData;
    SearchData.placeholder = "Search Asset";
    SearchData.search_data.value = e;
    SearchData.value = e;
    this.setState({
      SearchObjectData: SearchData,
    });
  }
  thingsDrawerClick(id) {
    this.props.thingsDrawerClick(id);
  }
  render() {
    let thingsListDropdownItems = [
      <SearchInput
        t={this.props.t}
        onSearch={(e) => this.onSearch(e)}
        {...this.state.SearchObjectData}
      />,
    ];
    let drawerThings = [];
    if (
      this.props.drawer_data &&
      this.props.drawer_data.thingsList &&
      this.props.drawer_data.thingsList.length
    ) {
      this.props.drawer_data.thingsList.map((thingsListData) => {
        if (
          thingsListData.name
            .toLowerCase()
            .includes(
              this.state.SearchObjectData.search_data.value.toLowerCase(),
            )
        ) {
          drawerThings.push(
            <div
              onClick={(e) => this.thingsDrawerClick(thingsListData.id)}
              className={
                "things-details " +
                (parseInt(this.props.thingId) === thingsListData.id
                  ? "selected-thing"
                  : "")
              }
            >
              <div className="things-name">{thingsListData.name}</div>
              <div
                className={
                  "status-shown-div" +
                  (thingsListData.onOffStatus === "1"
                    ? " online"
                    : thingsListData.onOffStatus === "2"
                      ? " offline"
                      : " switch-off")
                }
              >
                {thingsListData.onOffStatus === "1"
                  ? "Running"
                  : thingsListData.onOffStatus === "2"
                    ? "Not Connected"
                    : "Stopped"}
              </div>
            </div>,
          );
        }
        return thingsListDropdownItems;
      });
    }
    if (drawerThings && drawerThings.length) {
      thingsListDropdownItems = thingsListDropdownItems.concat(drawerThings);
    } else {
      thingsListDropdownItems.push(
        <div className="no-search">
          <div className="no-data-icon">
            <div className="image">
              <img src={NoSearchImage} />
            </div>
            <div className="text">No results!</div>
          </div>
        </div>,
      );
    }
    return (
      <AntDrawer
        placement="left"
        visible={this.props.drawerVisible}
        onClose={() => this.props.closeDrawer()}
        closable={false}
        className="custom-drawer-for-thing-status"
        getContainer={false}
        style={{ position: "absolute" }}
        drawerStyle={{
          background: "#FFFFFF 0% 0% no-repeat padding-box",
          boxShadow: "15px 16px 25px #92929229",
        }}
        width={280}
        destroyOnClose={true}
        styleType={"rounded"}
      >
        {thingsListDropdownItems}
      </AntDrawer>
    );
  }
}
