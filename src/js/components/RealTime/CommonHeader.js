import React from "react";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import ControlButton from "@datoms/react-components/src/components/ControlButton";
import { triggerCommand } from "@datoms/js-sdk";
import CalendarOutlined from "@ant-design/icons/CalendarOutlined";
import MenuFoldOutlined from "@ant-design/icons/MenuFoldOutlined";
import OnOffButton from "./OnOffButton";
import "../../../styles/DGMonitoring/Template-20/component/common-head.less";

export default class CommonHeader extends React.Component {
  constructor(props) {
    super(props);
  }
  render() {
    console.log("pppp", this.props);
    let headerData = this.props.head_obj;
    let headerThingDetails = (
      <div className="thing-name-date">
        <div className="thing-name">
          <MenuFoldOutlined t={this.props.t} onClick={() => this.props.showDrawer()} />
          {headerData.name}
        </div>
        <AntTooltip title="Last data received date">
          <div className="thing-date">
            <CalendarOutlined style={{ "margin-right": 10 }} />
            {headerData.date}
          </div>
        </AntTooltip>
      </div>
    );
    let controlPanelIconDiv = "",
      lockUnlockIcon = "";
    if (this.props.getRemoteLockAccess && !headerData.isThingMechanicalDG) {
      //if (true) {
      lockUnlockIcon = (
        <ControlButton
          socket={this.props.socket}
          thing_id={parseInt(this.props.thingId)}
          client_id={this.props.client_id}
          application_id={this.props.application_id}
          thingCommandStatus={headerData.commandStatus}
          operation_mode={headerData.operation_mode}
          isControlEnabled={headerData.isLockControlEnabled}
          dgStatus={headerData.dgStatus}
          onCommand={"dg_lock"}
          offCommand={"dg_unlock"}
          getViewAccess={this.props.getRemoteLockAccess}
          subTitle={
            <p>
              Please enter your password to{" "}
              <span>
                {headerData.dg_lock_status === "1" ? "unlock" : "lock"}
              </span>{" "}
              the DG.
            </p>
          }
          triggerCommand={triggerCommand}
          assetType={"DG"}
          dgLockStatus={headerData.dg_lock_status}
          isRentalDG={true}
          rent_status={"ongoing"}
          featureLockUnlock={true}
          isOnlyLock={true}
        />
      );
    }
    if (
      parseInt(this.props.vendor_id) !== 1140 &&
      !headerData.isThingMechanicalDG
    ) {
      controlPanelIconDiv = (
        <ControlButton
          socket={this.props.socket}
          thing_id={parseInt(this.props.thingId)}
          client_id={this.props.client_id}
          application_id={this.props.application_id}
          thingCommandStatus={headerData.commandStatus}
          isControlEnabled={headerData.isControlEnabled}
          dgStatus={headerData.dgStatus}
          onCommand={"dg_start"}
          offCommand={"dg_stop"}
          getViewAccess={this.props.getRemoteAccess}
          modalTitle={
            headerData.dgStatus === "1" ? "DG Switch Off" : "DG Switch On"
          }
          subTitle={
            <p>
              Please enter your password to{" "}
              <span>
                {headerData.dgStatus === "1" ? "switch off" : "switch on"}{" "}
              </span>
              the DG.
            </p>
          }
          enableTooltip={true}
          enableSideText={true}
          triggerCommand={triggerCommand}
          assetType={"DG"}
        />
      );
    } else if (this.props.getRemoteAccess && !headerData.isThingMechanicalDG) {
      controlPanelIconDiv = (
        <OnOffButton
          socket={this.props.socket}
          client_id={this.props.client_id}
          application_id={this.props.application_id}
          checkedChildren="ON"
          unCheckedChildren="OFF"
          thingCommandStatus={headerData.commandStatus}
          isControlEnabled={headerData.isControlEnabled}
          thing_id={parseInt(this.props.thingId)}
          dgStatus={headerData.dgStatus}
        />
      );
    }
    if (headerData.isThingMechanicalDG) {
      controlPanelIconDiv = (
        <div
          className={
            "no-icon-switch " +
            (headerData.dgStatus === "2"
              ? "offline"
              : headerData.dgStatus === "1"
                ? "online"
                : "switch-off")
          }
        >
          {headerData.dgStatus === "2"
            ? "Not Connected"
            : headerData.dgStatus === "1"
              ? "Running"
              : "Stopped"}
        </div>
      );
    }
    return (
      <div className="common-header">
        <div className="inner-common-header">
          <div className="thing-details-container">
            {headerThingDetails}
            {window.innerWidth > 576 ? (
              <div className="make-model-lifetime-div">
                <div className="make-model-div">
                  <div className="make-div">{"Make - " + headerData.make}</div>
                  <div className="make-div">
                    {"Model - " + headerData.model}
                  </div>
                  <div className="energy-div">{headerData.energyRating}</div>
                </div>
                <div className="lifetime-div">
                  {"Lifetime Runhour - " + headerData.lifetimeRunhour}
                </div>
              </div>
            ) : (
              <div className="make-model-lifetime-div">
                <div className="make-model-div">
                  <div className="make-div">{"Make - " + headerData.make}</div>
                  <div className="make-div">
                    {"Model - " + headerData.model}
                  </div>
                </div>
                <div className="lifetime-div make-model-div">
                  <div className="energy-div">{headerData.energyRating}</div>
                  {"Lifetime Runhour - " + headerData.lifetimeRunhour}
                </div>
              </div>
            )}
            {headerData.operation_mode_header ? (
              <div className="operation-mode">
                {headerData.operation_mode_header === "auto"
                  ? "Auto Mode"
                  : "Manual Mode"}
              </div>
            ) : (
              ""
            )}
          </div>
          <div
            className={
              parseInt(this.props.vendor_id) !== 1140
                ? "status-toggle realtime-dark-back"
                : "status-toggle"
            }
          >
            {window.innerWidth > 540 && (
              <div className="dg-realtime-lock">{lockUnlockIcon}</div>
            )}
            {parseInt(this.props.vendor_id) === 1140 && (
              <div className="status-shown-div">
                {headerData.dgStatus === "1"
                  ? "Running"
                  : headerData.dgStatus === "2"
                    ? "Not Connected"
                    : "Stopped"}
              </div>
            )}
            {controlPanelIconDiv}
            {window.innerWidth <= 540 && (
              <div className="dg-realtime-lock-mobile">{lockUnlockIcon}</div>
            )}
          </div>
        </div>
      </div>
    );
  }
}
