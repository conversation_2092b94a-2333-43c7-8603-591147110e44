import React, { JSX, useState } from "react";
import { <PERSON><PERSON> } from "antd";
import moment from "moment-timezone";
import {
  SettingOutlined,
  DownloadOutlined,
  ArrowLeftOutlined,
} from "@ant-design/icons";
import ConfigurationDrawer from "../ConfigurationDrawer";
import DownloadModal from "../DownloadModal";
import "./styles.less";
import { useGlobalContext } from "../../../../store/globalStore";
import DateRangeSelect from "../DateRangeSelect";
import AntSelect from "@datoms/react-components/src/components/AntSelect";
import AntRangePicker from "../../../../packages/react-components/src/components/AntRangePicker";
import { defaultRanges } from "../DateRangeSelect/configs/DateConfig";
import BackButton from "./back-button.svg";
import PageFilter from "../../../configurable/PageFilter";

const DefaultAggregationPeriodOptions: { value: number; label: string }[] = [
  { value: 900, label: "15 minutes" },
  { value: 1800, label: "30 minutes" },
  { value: 3600, label: "1 hour" },
  { value: 43200, label: "12 hours" },
  { value: 86400, label: "Daily" },
  { value: 604800, label: "Weekly" },
  { value: 2592000, label: "Monthly" },
  { value: 7776000, label: "Quarterly" },
  { value: 31536000, label: "Yearly" },
];

interface HeaderProps {
  pageConfig: any;
  filters: any;
  pageFilterObject: any;
  selectedParam: string;
  setSelectedParam: (value: string) => void;
  dateRangeTimes: { fromTime: number; uptoTime: number };
  setDateRangeTimes: (value: { fromTime: number; uptoTime: number }) => void;
  selectedThings: any[];
  setSelectedThings: (value: any[]) => void;
  paramList: any[];
  listData: any[];
  reportName: string;
  maxTimeRangeInDays: number;
  dateRangeFormat: string;
  dateRangePicker: any;
  aggregationPeriodOptions: any[];
  headerText: string;
  isConfigModalVisible: boolean;
  setIsConfigModalVisible: (value: boolean) => void;
  allThingsData: any[];
  datePickerRanges: any;
  datePickerConfig: any;
  customRangePickerEnabled: boolean;
  defaultDateRange: string;
  selectedAssets: any[];
  setSelectedAssets: (value: any[]) => void;
  machineInfo: any;
  selectedThingData: any;
  summaryData: any;
  tableData: any;
  chartDimensions: any;
  columnConfig: any;
  fetchData: () => void;
  assetDetailsExp: any;
  graphColors: any;
  pastTimeLimitDays: number;
  sortedInfo: any;
  datePickerFormat: string;
  datePickerInterval: string;
  defaultDateSelector: string | boolean;
}

const antRangeConfig: Record<string, any> = {
  3600: { picker: "time", format: "DD MMM YYYY, HH:mm" },
  // 86400: {picker: "date", format: "DD MMM YYYY"},
  604800: { picker: "week", format: false },
  2592000: { picker: "month", format: "MMM YYYY" },
  7776000: { picker: "quarter", format: "YYYY-[Q]Q" },
  31536000: { picker: "year", format: "YYYY" },
};

const ReportHeader: React.FC<HeaderProps> = (props) => {
  const {
    filters,
    pageFilterObject,
    dateRangeTimes,
    setDateRangeTimes,
    selectedThings,
    setSelectedThings,
    paramList,
    listData,
    reportName,
    maxTimeRangeInDays,
    dateRangeFormat,
    dateRangePicker,
    aggregationPeriodOptions,
    headerText,
    isConfigModalVisible,
    setIsConfigModalVisible,
    allThingsData,
    datePickerRanges,
    datePickerConfig,
    customRangePickerEnabled,
    defaultDateRange,
    selectedAssets,
    pageConfig,
    setSelectedAssets,
    machineInfo,
    selectedThingData,
    summaryData,
    tableData,
    chartDimensions,
    columnConfig,
    fetchData,
    assetDetailsExp,
    graphColors,
    pastTimeLimitDays,
    filterValues,
    setFilterValues,
    territoryData,
    sortedInfo,
    datePickerFormat,
    datePickerInterval,
    defaultDateSelector,
    getDownloadFilterArray,
  } = props;

  const [isDownloadModalVisible, setIsDownloadModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedRange, setSelectedRange] = useState<string | null>(
    defaultDateRange || "Last 30 Days",
  );
  const [dateRangeConfig, setDateRangeConfig] = useState<
    Record<string, number | string | boolean>
  >({});

  const {
    client_name,
    user_name,
    is_white_label,
    vendor_name,
    client_logo,
    user_preferences,
    vendor_logo,
    vendor_id,
  } = useGlobalContext();
  const timezone = user_preferences?.timezone || "Asia/Kolkata";

  const isMobileScreen = window.innerWidth <= 768;
  const rightSection = (
    <>
      {filters?.length && !pageFilterObject?.filters?.length ? (
        <div className="configuration-button">
          <Button
            type="default"
            icon={<SettingOutlined />}
            onClick={() => setIsConfigModalVisible(true)}
          >
            {!isMobileScreen ? "Customize" : ""}
          </Button>
        </div>
      ) : null}
      {!isMobileScreen && (
        <div className="download-button">
          <Button
            type="primary"
            icon={<DownloadOutlined />}
            onClick={() => setIsDownloadModalVisible(true)}
            loading={loading}
            disabled={loading || !columnConfig}
          >
            Download
          </Button>
        </div>
      )}
    </>
  );

  const onAggregationChange = (value: number) => {
    console.log("AgrrValue", value, Object.keys(antRangeConfig));
      if (Object.keys(antRangeConfig).includes(value?.toString())) {
        setSelectedRange("Custom");
  
        const reqConfig = antRangeConfig[value];
        setDateRangeConfig((prev) => ({ ...prev, ...reqConfig }));
        console.log("AgrrValue reqConfig", reqConfig);
        const fromTime = moment().startOf(reqConfig?.picker || "date");
        const uptoTime = moment().endOf(reqConfig?.picker || "date");
  
        setDateRangeTimes({
          fromTime: fromTime.unix(),
          uptoTime: uptoTime.unix(),
        });
      } else {
        setSelectedRange(defaultDateRange);
        setDateRangeConfig((prev) => ({ ...prev, picker: "date", format: "DD MMM YYYY" }));
        setDateRangeTimes({
          fromTime: defaultRanges[defaultDateRange][0].unix(),
          uptoTime: defaultRanges[defaultDateRange][1].unix(),
        });
      }
  
      setFilterValues((p:any) => ({ ...p, aggregation_period: value }));
    
  };
  
  
  const aggrOptions = aggregationPeriodOptions?.length ? aggregationPeriodOptions : DefaultAggregationPeriodOptions;

  console.log("aggrOptions", filters);
  const aggregationPeriodFilter: JSX.Element =  filters?.includes("aggregation_period") ? (
      <AntSelect
        className="agrregation-period-select"
        placeholder="Select Aggregation Period"
        value={filterValues?.aggregation_period}
        options={aggrOptions}
        onChange={onAggregationChange}
      />
  ) : <></>;

  const pageFilter = pageFilterObject?.filters?.length ? (
    <div style={{ marginTop: "10px", marginBottom: "-20px" }}>
      <PageFilter
        {...props}
        ref={pageFilterObject?.filterRef}
        isFlexWrap
        // backgroundWhite
        history={pageFilterObject?.history}
        url={pageFilterObject?.baseUrl}
        width={525}
        filterData={pageFilterObject?.filters}
        resetDependentFields={pageFilterObject?.dependentFilters}
        filterCallback={(_:any,formattedFilters:any) =>
          {
          pageFilterObject?.setSelectedFilters(formattedFilters);
          pageFilterObject?.setPagination((prev: any) => ({
            ...prev,
            page_no: pageFilterObject?.initial ? pageFilterObject?.pagination.page_no : 1,
          }))
        }}
      />
    </div>
  ) : <></>;

  return (
    <div id="configuration_header">
      <div className="header-container">
        <div className="left-section">
          <div className="left-section-title">
          <div className="back-button-container">
            <img src={BackButton} alt="back" onClick={() => window.history.back()} style={{cursor: "pointer"}} />
          </div>
          <div className="report-name">{headerText}</div>
          </div>
          <div className="left-section-filters">
            {aggregationPeriodFilter}
            <div className="date-range-selector">
              {defaultDateSelector === false ? (
                ""
              ) : defaultDateSelector === "date_picker" ? (
                <AntRangePicker
                  type="date_picker_default"
                  showTime={{ format: datePickerFormat || "HH" }}
                  showNow={false}
                  value={moment.unix(dateRangeTimes.fromTime)}
                  onChange={(value) => {
                    setDateRangeTimes({
                      fromTime: value.unix(),
                      uptoTime: value.endOf(datePickerInterval || "hour").unix(),
                    });
                  }}
                />
              ) : (
                <DateRangeSelect
                  defaultDateRange={defaultDateRange}
                  customEnabled={customRangePickerEnabled}
                  datePickerConfig={datePickerConfig}
                  datePickerRanges={datePickerRanges}
                  pastTimeLimitDays={pastTimeLimitDays}
                  maxTimeRangeInDays={maxTimeRangeInDays}
                  dateRangeTimeStateVal={dateRangeTimes}
                  setDateRageTimeStateFunc={setDateRangeTimes}
                  rangeKeys={["fromTime", "uptoTime"]}
                  realTimeUpdate={true}
                  selectedRangeStateVal={selectedRange}
                  setSelectedRangeStateFunc={setSelectedRange}
                  filterValues={filterValues}
                  dateRangeConfig={dateRangeConfig}
                  setDateRangeConfig={setDateRangeConfig}
                  antRangeConfig={antRangeConfig}
                />
              )}
            </div>
            {isMobileScreen && rightSection}
          </div>
        </div>
        {!isMobileScreen && <div className="right-section">{rightSection}</div>}
      </div>
      {pageFilter}

      <ConfigurationDrawer
        filters={filters}
        visible={isConfigModalVisible}
        onClose={() => setIsConfigModalVisible(false)}
        onApply={() => setIsConfigModalVisible(false)}
        dateRangeTimes={dateRangeTimes}
        setDateRangeTimes={setDateRangeTimes}
        selectedThings={selectedThings}
        setSelectedThings={setSelectedThings}
        listData={listData}
        paramList={paramList}
        maxTimeRangeInDays={maxTimeRangeInDays}
        dateRangeFormat={dateRangeFormat}
        dateRangePicker={dateRangePicker}
        aggregationPeriodOptions={aggregationPeriodOptions}
        allThingsData={allThingsData}
        selectedAssets={selectedAssets}
        setSelectedAssets={setSelectedAssets}
        filterValues={filterValues}
        setFilterValues={setFilterValues}
        territoryData={territoryData}
        defaultDateRange={defaultDateRange}
        customRangePickerEnabled={customRangePickerEnabled}
        datePickerConfig={datePickerConfig}
        datePickerRanges={datePickerRanges}
        pastTimeLimitDays={pastTimeLimitDays}
        selectedRange={selectedRange}
        setSelectedRange={setSelectedRange}
        datePickerFormat={datePickerFormat}
        datePickerInterval={datePickerInterval}
        dateRangeConfig={dateRangeConfig}
        setDateRangeConfig={setDateRangeConfig}
        antRangeConfig={antRangeConfig}
        requiredFields={{
          things: pageConfig.report_type !== "multi_entity",
        }}
      />

      <DownloadModal
        visible={isDownloadModalVisible}
        setIsDownloadModalVisible={setIsDownloadModalVisible}
        reportTitle={reportName}
        customerName={client_name}
        userName={user_name}
        waterMark={
          is_white_label
            ? vendor_name
            : vendor_id === 1
              ? "Datoms"
              : vendor_name
        }
        fromTime={dateRangeTimes.fromTime}
        uptoTime={dateRangeTimes.uptoTime}
        machineInfo={machineInfo}
        selectedThingData={selectedThingData}
        summaryData={summaryData}
        tableData={tableData}
        chartDimensions={chartDimensions}
        columnConfig={columnConfig}
        fetchData={fetchData}
        selectedAssets={
          pageConfig.report_type === "multi_entity" ? [1] : selectedAssets
        }
        allThingsData={allThingsData}
        assetDetailsExp={assetDetailsExp}
        graphColors={graphColors}
        loading={loading}
        setLoading={setLoading}
        clientLogo={client_logo}
        timezone={timezone}
        vendorLogo={vendor_logo}
        sortedInfo={sortedInfo}
        getDownloadFilterArray={getDownloadFilterArray}
      />
    </div>
  );
};

export default ReportHeader;
