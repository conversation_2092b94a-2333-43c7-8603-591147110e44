import { assetDetails } from "../../../../configurable/compositeComponents/GravityReport/utils/assetDetails";
import moment from "moment-timezone";
import { Backyard } from "./Backyard";
import AntNotification from "@datoms/react-components/src/components/AntNotification";
import { getTotalColumns } from "../../../../configurable/baseComponents/ConfigurableTable/utils/logic";
import { reorderAndFilterData } from "../../../../configurable/baseComponents/ConfigurableSummary";

interface Props {
  reportTitle: any;
  customerName: any;
  userName: any;
  fromTime: any;
  uptoTime: any;
  waterMark: any;
  machineInfo: any;
  selectedThingData: any;
  downloadFormat?: string;
  chartDimensions: any;
  fetchData: any;
  selectedAssets: number[];
  allThingsData: any;
  assetDetailsExp: string[];
  graphColors: string[];
  timezone: string;
  sortedInfo: any;
  filters: any;
}

interface Column {
  pdf_title: string;
  title?: string;
  dataIndex: string;
  children?: Column[];
}

export const downloadXlsx = async (props: Props, resolve?: any) => {
  const {
    reportTitle,
    userName,
    fromTime,
    uptoTime,
    fetchData,
    selectedAssets,
    allThingsData,
    assetDetailsExp,
    customerName,
    timezone,
    sortedInfo,
    filters,
  } = props;

  const results = await Promise.all(
    selectedAssets.map(async (assetId: number) => {
      return await fetchData(assetId, fromTime, uptoTime);
    }),
  );

  const machineInfo: { [key: number]: any } = {};
  results.forEach((result) => {
    const currThingData = allThingsData.find(
      (thing: { id: number }) => thing.id === result.assetId,
    );
    const machineDetails = assetDetails(assetDetailsExp, currThingData);
    machineInfo[result.assetId] = machineDetails;
  });

  console.log("sortedInfo", sortedInfo);

  const runFunction = (
    ctx: any,
    input: {
      results: typeof results;
      allThingsData: any;
      assetDetailsExp: string[];
      reportTitle: string;
      userName: string;
      fromTime: number;
      uptoTime: number;
      machineInfo: any;
      customerName: string;
      timezone: string;
      sortedInfo: any;
      filters: any;
    },
    cb: (result: ArrayBuffer) => void,
  ) => {
    let {
      results,
      allThingsData,
      reportTitle,
      userName,
      fromTime,
      uptoTime,
      machineInfo,
      customerName,
      timezone,
      sortedInfo,
      filters,
    } = input;

    const Workbook = ctx.ExcelJS.Workbook;
    const workbook = new Workbook();
    const moment = ctx.moment;
    moment.tz.setDefault(timezone);
    let sortedColumnTitle = "";

    let durationText: string = "Report ";
    if (fromTime && uptoTime && uptoTime - fromTime >= 86400) {
      durationText += `from: ${moment.unix(fromTime).format("DD MMM YYYY")} to ${moment.unix(uptoTime).format("DD MMM YYYY")}`;
    } else {
      if(fromTime) durationText += `for ${moment.unix(fromTime).format("DD MMM YYYY")}`;
      else durationText = "";
    }
    const generatedOnText = `Generated at: ${moment().format("DD MMM YYYY, HH:mm")} by ${userName}`;

    const worksheet = workbook.addWorksheet("Info");
    worksheet.views = [
      {
        showGridLines: false,
      },
    ];

    const titleRow = worksheet.addRow([reportTitle]);
    worksheet.mergeCells(titleRow.number, 1, titleRow.number, 15);
    titleRow.font = { bold: true, size: 20 };

    const customerNameRow = worksheet.addRow([customerName]);
    worksheet.mergeCells(customerNameRow.number, 1, customerNameRow.number, 15);
    customerNameRow.font = { bold: true, size: 16 };

    worksheet.addRow([]);
    const durationRow = worksheet.addRow([durationText]);
    durationRow.font = { size: 12 };
    worksheet.mergeCells(durationRow.number, 1, durationRow.number, 15);

    const generatedOnRow = worksheet.addRow([generatedOnText]);
    worksheet.mergeCells(generatedOnRow.number, 1, generatedOnRow.number, 15);
    generatedOnRow.font = { size: 12 };
    worksheet.addRow([]);

    const timeFormatRow = worksheet.addRow([
      "* All the times are in 24 Hours time format",
      "",
      "",
      "",
      "",
    ]);
    worksheet.mergeCells(timeFormatRow.number, 1, timeFormatRow.number, 15);
    worksheet.addRow([]);
    worksheet.addRow([]);

    // Add "Applied Filters" section
    if (filters?.length) {
      const filtersTitleRow = worksheet.addRow(["Applied Filters"]);
      worksheet.mergeCells(
        filtersTitleRow.number,
        1,
        filtersTitleRow.number,
        15,
      );
      filtersTitleRow.font = { bold: true, size: 14 };
      filters.forEach((filter: any) => {
        const filterRow = worksheet.addRow([
          `${filter.title}: ${filter.value}`,
        ]);
        worksheet.mergeCells(filterRow.number, 1, filterRow.number, 15);
        filterRow.font = { size: 12 };
      });
      worksheet.addRow([]);
      worksheet.addRow([]);
    }

    const extractHeadersAndKeys = (
      columns: Column[],
    ): {
      headers: string[][];
      keys: string[];
      merges: { start: number; end: number; row: number }[];
    } => {
      const headers: string[][] = [];
      const keys: string[] = [];
      const merges: { start: number; end: number; row: number }[] = [];
    
      // First pass: determine the maximum depth of headers
      const getMaxDepth = (cols: Column[], currentDepth: number = 0): number => {
        let maxDepth = currentDepth;
        
        for (const col of cols) {
          if (col.children && col.children.length > 0) {
            const childDepth = getMaxDepth(col.children, currentDepth + 1);
            maxDepth = Math.max(maxDepth, childDepth);
          }
        }
        
        return maxDepth;
      };
      
      const maxDepth = getMaxDepth(columns) + 1; // +1 for 0-based index
      
      // Initialize headers array with empty strings
      for (let i = 0; i < maxDepth; i++) {
        headers.push([]);
      }
    
      // Second pass: populate headers and keys
      const processColumns = (
        cols: Column[],
        depth: number,
        startColIndex: number,
      ): number => {
        let colIndex = startColIndex;
        
        for (const column of cols) {
          const startCol = colIndex;
          
          if (column.children && column.children.length > 0) {
            // This is a parent column with children
            let headerTitle = column.pdf_title || column.title || "";
            if (
              sortedInfo?.columnKey &&
              sortedInfo?.columnKey === column?.dataIndex &&
              headerTitle
            ) {
              sortedColumnTitle = headerTitle;
              headerTitle = "*" + headerTitle;
            }
            
            headers[depth][colIndex] = headerTitle;
            
            // Process children and get the ending column index
            const endCol = processColumns(column.children, depth + 1, colIndex) - 1;
            
            // Merge parent header cells if there are multiple children
            if (startCol < endCol) {
              merges.push({
                start: startCol + 1,
                end: endCol + 1,
                row: depth + 1,
              });
              
              // Fill the merged cells with empty strings
              for (let i = startCol + 1; i <= endCol; i++) {
                headers[depth][i] = "";
              }
            }
            
            colIndex = endCol + 1;
          } else {
            // This is a leaf column (no children)
            let headerTitle = column.pdf_title || column.title || "";
            if (
              sortedInfo?.columnKey &&
              sortedInfo?.columnKey === column?.dataIndex &&
              headerTitle
            ) {
              sortedColumnTitle = headerTitle;
              headerTitle = "*" + headerTitle;
            }
            
            // Set header title in current depth
            headers[depth][colIndex] = headerTitle;
            
            // For leaf columns, make sure they span all the way to the last row
            // so the header aligns with the data
            for (let i = depth + 1; i < maxDepth; i++) {
              headers[i][colIndex] = "";
            }
            
            // Add dataIndex to keys array for data mapping
            keys.push(column.dataIndex);
            
            colIndex++;
          }
        }
        
        return colIndex;
      };
    
      processColumns(columns, 0, 0);
      
      // Ensure all header rows have the same length
      const maxLength = Math.max(...headers.map(row => row.length));
      headers.forEach(row => {
        while (row.length < maxLength) {
          row.push("");
        }
      });
    
      return { headers, keys, merges };
    };

    const addBorders = (worksheet: any, range: any, borderColor = "000000") => {
      for (let row = range.startRow; row <= range.endRow; row++) {
        for (let col = range.startCol; col <= range.endCol; col++) {
          const cell = worksheet.getCell(row, col);
          cell.border = {
            top: { style: "thin", color: { argb: borderColor } },
            left: { style: "thin", color: { argb: borderColor } },
            bottom: { style: "thin", color: { argb: borderColor } },
            right: { style: "thin", color: { argb: borderColor } },
          };
        }
      }
    };

    const addSummary = (worksheet: any, summaryData: any, assetId?: number) => {
      if(assetId !== 1) {
        const summaryRow = worksheet.addRow(["Summary"]);
        summaryRow.font = { bold: true, size: 14 };
      }

      const startRow = worksheet.lastRow ? worksheet.lastRow.number + 1 : 1;
      const parameterValueRow = worksheet.addRow(["Parameter", "", "Value"]);
      parameterValueRow.font = { bold: true };
      parameterValueRow.getCell(1).fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "E4E4E4" },
      };
      parameterValueRow.getCell(3).fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "E4E4E4" },
      };
      worksheet.mergeCells(
        parameterValueRow.number,
        1,
        parameterValueRow.number,
        2,
      );
      let endRow = parameterValueRow.number;

      summaryData.forEach(
        (item: { text: string; unit: string; value: string }) => {
          const row = worksheet.addRow([
            `${item.text}` + (item.unit ? `(${item.unit})` : ""),
            "",
            item.value === "-"
              ? ""
              : !isNaN(parseFloat(item.value)) && !isNaN(Number(item.value))
                ? parseFloat(item.value)
                : item.value,
          ]);
          worksheet.mergeCells(row.number, 1, row.number, 2);
          row.eachCell((cell: { [key: string]: any }) => {
            cell.alignment = { horizontal: "left", vertical: "middle" };
          });
          endRow = row.number;
        },
      );

      addBorders(
        worksheet,
        { startRow, endRow, startCol: 1, endCol: 3 },
        "AEAAAA",
      );
      worksheet.addRow([]);
      worksheet.addRow([]);
    };

    const embeddThingWiseData = (
      assetId: number,
      summaryData: any,
      tableData: any,
      tableColumns: any,
    ) => {
      const currThingData = allThingsData.find(
        (thing: { id: number }) => thing.id === assetId,
      );
    
      const sanitizeSheetName = (name: string): string => {
        return name.replace(/[:\\\/\?\*\[\]]/g, "_").substring(0, 31);
      };
    
      const assetName =
        currThingData?.name || (assetId === 1 ? "Data" : `Asset_${assetId}`);
      const sanitizedAssetName = sanitizeSheetName(assetName);
    
      // If assetId === 1 and summary exists, create separate summary sheet first
      if (assetId === 1 && summaryData?.length) {
        const summarySheet = workbook.addWorksheet("Summary", { position: 1 });
        summarySheet.views = [{ showGridLines: false }];
    
        const summaryTitleRow = summarySheet.addRow(["Summary"]);
        summaryTitleRow.font = { bold: true, size: 16 };
        summarySheet.mergeCells(summaryTitleRow.number, 1, summaryTitleRow.number, 15);
        summarySheet.addRow([]);
    
        addSummary(summarySheet, summaryData, assetId);
      }
    
      const worksheet = workbook.addWorksheet(sanitizedAssetName);
      worksheet.views = [{ showGridLines: false }];
    
      if(assetId !== 1) {
        const assetNameRow = worksheet.addRow([assetName]);
        assetNameRow.font = { bold: true, size: 20 };
        worksheet.mergeCells(assetNameRow.number, 1, assetNameRow.number, 15);
        worksheet.addRow([]);
      }
    
      if (machineInfo[assetId]?.length) {
        const assetInfoTitleRow = worksheet.addRow(["Asset Info"]);
        assetInfoTitleRow.font = { bold: true, size: 14 };
        machineInfo[assetId].forEach((info: any) => {
          const machineInfoRow = worksheet.addRow([info.label, "", info.value]);
          worksheet.mergeCells(machineInfoRow.number, 1, machineInfoRow.number, 2);
        });
        worksheet.addRow([]);
        worksheet.addRow([]);
      }
    
      // Don't call addSummary here if assetId === 1 and summary was already handled
      if (summaryData?.length && assetId !== 1) {
        addSummary(worksheet, summaryData);
      }
      if(assetId !== 1) {
        const DetailRow = worksheet.addRow(["Detailed Data"]);
        DetailRow.font = { bold: true, size: 14 };
      }
    
      const tableStartRow = worksheet.lastRow ? worksheet.lastRow.number + 1 : 1;

      const { headers, keys, merges } = extractHeadersAndKeys(tableColumns);
      
      // Add headers
      headers.forEach((headerRow) => {
        const row = worksheet.addRow(headerRow);
        row.eachCell((cell: { [key: string]: any }) => {
          cell.font = { bold: true };
          cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: "E4E4E4" },
          };
          cell.alignment = { horizontal: "left", vertical: "middle" };
        });
      });
      
      merges.forEach(({ start, end, row }) => {
        worksheet.mergeCells(row, start, row, end);
      });
      
      // Add table rows
      tableData.forEach((row: any) => {
        const newRow = worksheet.addRow(
          keys.map((key) =>
            row[key] === "-"
              ? ""
              : !isNaN(parseFloat(row[key])) && !isNaN(Number(row[key]))
                ? parseFloat(row[key])
                : row[key],
          ),
        );
        newRow.eachCell((cell: { [key: string]: any }) => {
          cell.alignment = { horizontal: "left", vertical: "middle" };
        });
      });
      
      const tableEndRow = worksheet.lastRow.number;
      const headerRowCount = headers.length;
      
      // Add autofilter - applies to the last header row and all data
      worksheet.autoFilter = {
        from: { row: tableStartRow + headerRowCount - 1, column: 1 },
        to: { row: tableEndRow, column: keys.length }
      };
      
      addBorders(
        worksheet,
        {
          startRow: tableStartRow,
          endRow: tableEndRow,
          startCol: 1,
          endCol: keys.length,
        },
        "AEAAAA",
      );
    
      worksheet.addRow([]);
    
      if (sortedColumnTitle) {
        const sortedColumnRow = worksheet.addRow([
          `*Sorted by ${sortedColumnTitle} in ${sortedInfo.order === "ascend" ? "Ascending" : "Descending"} order`,
        ]);
        worksheet.mergeCells(sortedColumnRow.number, 1, sortedColumnRow.number, 15);
        sortedColumnRow.font = { size: 10 };
      }
    
      worksheet.columns = keys.map(() => ({ width: 15 }));
    };    

    results.forEach((result) =>
      embeddThingWiseData(
        result.assetId,
        result.summaryData,
        result.tableData,
        result.tableColumns,
      ),
    );

    workbook.xlsx.writeBuffer().then((buffer: ArrayBuffer) => {
      cb(buffer);
    });
  };

  const finalResults = results.map((result) => {
    return {
      assetId: result.assetId,
      summaryData: reorderAndFilterData(
        result.summaryResponse?.[0]?.data,
        result.preferences,
        result.summaryResponse?.[0]?.config.preferenceKeys,
      ).finalData,
      tableData: result.tableResponse[0].data,
      tableColumns: getTotalColumns(
        result.tableResponse[0].config.tableProps.columns,
        result.tableResponse[0].config.preferenceKeys,
        result.orgPreferenceKey,
        result.preferences,
        true,
      ),
    };
  });

  new Backyard({
    input: {
      results: finalResults,
      allThingsData,
      assetDetailsExp,
      reportTitle,
      userName,
      fromTime,
      uptoTime,
      machineInfo,
      customerName,
      timezone,
      sortedInfo: { columnKey: sortedInfo.columnKey, order: sortedInfo.order },
      filters,
    },
    run: runFunction,
    scripts: [
      "https://cdn.jsdelivr.net/npm/exceljs/dist/exceljs.min.js",
      "https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js",
      "https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.23/moment-timezone.min.js",
      "https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.33/moment-timezone-with-data-1970-2030.min.js",
    ],
    cb: (result: ArrayBuffer | { error: string }) => {
      if ("error" in result) {
        console.log("error", result);
        AntNotification({
          type: "error",
          message: "Failed to download the report, please try again.",
          placement: "bottomLeft",
        });
        if(typeof resolve === "function") {
          resolve();
        }
        return;
      }
      const blob = new Blob([result], { type: "application/octet-stream" });
      const link = document.createElement("a");
      let reportName = reportTitle;
      if (fromTime && uptoTime && uptoTime - fromTime >= 86400) {
        reportName += ` ${moment.unix(fromTime).format("DD MMM YYYY")}_To_${moment.unix(uptoTime).format("DD MMM YYYY")}.xlsx`;
      } else {
        reportName += fromTime?` ${moment.unix(fromTime).format("DD MMM YYYY")}.xlsx`:`.xlsx`;
      }
      link.href = URL.createObjectURL(blob);
      link.download = reportName;
      link.click();
      setTimeout(() => {
        if (typeof window.reportGenerationCompleted === "function") {
          window.reportGenerationCompleted(reportName);
        }
      }, 2000);
      if(typeof resolve === "function") {
        resolve();
      }
    },
  });
};
