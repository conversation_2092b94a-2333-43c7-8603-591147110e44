import React from "react";
import { <PERSON>Fn, Meta } from "@storybook/react";
import IconFrame from "./index";
import {
  Canvas,
  Source,
  Stories,
  Subtitle,
  Title,
  Controls,
} from "@storybook/blocks";
import { Table } from "@storybook/components";

interface RowProps {
  key: string;
  required: string;
  allowedValues: string;
  description: string;
  defaultValue: string;
}

const CustomPropsTable = ({ rows }: { rows: RowProps[] }) => (
  <Table>
    <thead>
      <tr>
        <th>Key</th>
        <th>Required</th>
        <th>Allowed Values</th>
        <th>Default Value</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      {rows?.map((row: RowProps) => (
        <tr key={row.key}>
          <td>{row.key}</td>
          <td>{row.required}</td>
          <td>{row.allowedValues}</td>
          <td>{row.defaultValue}</td>
          <td>{row.description}</td>
        </tr>
      ))}
    </tbody>
  </Table>
);

export default {
  title: "Components/IconFrame",
  component: IconFrame,
  tags: ["autodocs"],
  argTypes: {
    color: {
      control: "object",
      description: "The colors for the icon frame (outer and inner)",
    },
    entityType: {
      control: { type: "select", options: ["site", "asset"] },
      description: "The type of entity (site or asset)",
    },
    iconType: {
      control: { type: "select", options: ["map", "normal"] },
      description: "The type of icon (map or normal)",
    },
  },
  parameters: {
    componentSubtitle: "A configurable icon frame component for status icons.",
    docs: {
      page: () => (
        <>
          <Title>IconFrame</Title>
          <p>
            The IconFrame component provides different frame styles for status icons based on entity type and icon type.
          </p>
          <CustomPropsTable
            rows={[
              {
                key: "color",
                required: "Y",
                allowedValues: "{ outer: string, inner: string }",
                defaultValue: "-",
                description: "The colors for the icon frame. Outer color is used for the frame border, inner color is used for the inner part of site frames.",
              },
              {
                key: "entityType",
                required: "Y",
                allowedValues: "'site' | 'asset'",
                defaultValue: "-",
                description: "The type of entity the icon represents. Different entity types have different frame shapes.",
              },
              {
                key: "iconType",
                required: "Y",
                allowedValues: "'map' | 'normal'",
                defaultValue: "-",
                description: "The type of icon display. Map icons are used on maps, normal icons are used in lists and other UI elements.",
              },
            ]}
          />
          <Subtitle />
          <Source of={AssetNormalFrame} />
          <Stories />
          <Subtitle>Interactive Examples</Subtitle>
          <Canvas of={AssetNormalFrame} />
          <Controls of={AssetNormalFrame} />
        </>
      ),
    },
  },
} as Meta;

const Template: StoryFn<{
  color: { outer: string; inner: string };
  entityType: "site" | "asset";
  iconType: "map" | "normal";
}> = (args) => (
  <div style={{ width: "100px", height: "100px" }}>
    <IconFrame {...args} />
  </div>
);

const frameColor = {
  outer: "#BAEBC7",
  inner: "#008C3F",
};

export const AssetNormalFrame = Template.bind({});
AssetNormalFrame.args = {
  color: frameColor,
  entityType: "asset",
  iconType: "normal",
};

export const AssetMapFrame = Template.bind({});
AssetMapFrame.args = {
  color: frameColor,
  entityType: "asset",
  iconType: "map",
};

export const SiteNormalFrame = Template.bind({});
SiteNormalFrame.args = {
  color: frameColor,
  entityType: "site",
  iconType: "normal",
};

export const SiteMapFrame = Template.bind({});
SiteMapFrame.args = {
  color: frameColor,
  entityType: "site",
  iconType: "map",
};
