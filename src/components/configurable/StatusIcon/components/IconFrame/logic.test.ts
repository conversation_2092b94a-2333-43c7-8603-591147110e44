import { darkenColor, hexToRgb, rgbToHsl, hslToRgb, rgbToHex } from './logic';

describe('Color conversion utilities', () => {
  describe('hexToRgb', () => {
    it('should convert valid hex to RGB', () => {
      expect(hexToRgb('#FF0000')).toEqual([255, 0, 0]);
      expect(hexToRgb('#00FF00')).toEqual([0, 255, 0]);
      expect(hexToRgb('#0000FF')).toEqual([0, 0, 255]);
      expect(hexToRgb('#FFFFFF')).toEqual([255, 255, 255]);
      expect(hexToRgb('#000000')).toEqual([0, 0, 0]);
    });

    it('should convert shorthand hex to RGB', () => {
      expect(hexToRgb('#F00')).toEqual([255, 0, 0]);
      expect(hexToRgb('#0F0')).toEqual([0, 255, 0]);
      expect(hexToRgb('#00F')).toEqual([0, 0, 255]);
      expect(hexToRgb('#FFF')).toEqual([255, 255, 255]);
      expect(hexToRgb('#000')).toEqual([0, 0, 0]);
    });

    it('should handle hex without # prefix', () => {
      expect(hexToRgb('FF0000')).toEqual([255, 0, 0]);
      expect(hexToRgb('F00')).toEqual([255, 0, 0]);
    });

    it('should return null for invalid hex', () => {
      expect(hexToRgb('not-a-color')).toBeNull();
      expect(hexToRgb('#XYZ')).toBeNull();
      expect(hexToRgb('#12')).toBeNull();
      expect(hexToRgb('')).toBeNull();
    });
  });

  describe('rgbToHsl', () => {
    it('should convert RGB to HSL correctly', () => {
      // Red
      const [h1, s1, l1] = rgbToHsl(255, 0, 0);
      expect(h1).toBeCloseTo(0, 2);
      expect(s1).toBeCloseTo(1, 2);
      expect(l1).toBeCloseTo(0.5, 2);

      // Green
      const [h2, s2, l2] = rgbToHsl(0, 255, 0);
      expect(h2).toBeCloseTo(1/3, 2);
      expect(s2).toBeCloseTo(1, 2);
      expect(l2).toBeCloseTo(0.5, 2);

      // Blue
      const [h3, s3, l3] = rgbToHsl(0, 0, 255);
      expect(h3).toBeCloseTo(2/3, 2);
      expect(s3).toBeCloseTo(1, 2);
      expect(l3).toBeCloseTo(0.5, 2);

      // White (achromatic case)
      const [h4, s4, l4] = rgbToHsl(255, 255, 255);
      expect(h4).toBe(0);
      expect(s4).toBe(0);
      expect(l4).toBe(1);

      // Black (achromatic case)
      const [h5, s5, l5] = rgbToHsl(0, 0, 0);
      expect(h5).toBe(0);
      expect(s5).toBe(0);
      expect(l5).toBe(0);

      // Gray (achromatic case)
      const [h6, s6, l6] = rgbToHsl(128, 128, 128);
      expect(h6).toBe(0);
      expect(s6).toBe(0);
      expect(l6).toBeCloseTo(0.5, 2);
    });

    it('should handle different max cases for hue calculation', () => {
      // Test r is max
      const [h1, s1, l1] = rgbToHsl(255, 100, 50);
      expect(h1).toBeCloseTo(0.04, 2);

      // Test g is max
      const [h2, s2, l2] = rgbToHsl(100, 255, 50);
      expect(h2).toBeCloseTo(0.29, 2);

      // Test b is max
      const [h3, s3, l3] = rgbToHsl(50, 100, 255);
      expect(h3).toBeCloseTo(0.63, 2);

      // Test g < b case for r is max
      const [h4, s4, l4] = rgbToHsl(255, 50, 100);
      expect(h4).toBeCloseTo(0.96, 2);
    });
  });

  describe('hslToRgb', () => {
    it('should convert HSL to RGB correctly', () => {
      // Red
      const [r1, g1, b1] = hslToRgb(0, 1, 0.5);
      expect(r1).toBe(255);
      expect(g1).toBe(0);
      expect(b1).toBe(0);

      // Green
      const [r2, g2, b2] = hslToRgb(1/3, 1, 0.5);
      expect(r2).toBe(0);
      expect(g2).toBe(255);
      expect(b2).toBe(0);

      // Blue
      const [r3, g3, b3] = hslToRgb(2/3, 1, 0.5);
      expect(r3).toBe(0);
      expect(g3).toBe(0);
      expect(b3).toBe(255);

      // White (achromatic case)
      const [r4, g4, b4] = hslToRgb(0, 0, 1);
      expect(r4).toBe(255);
      expect(g4).toBe(255);
      expect(b4).toBe(255);

      // Black (achromatic case)
      const [r5, g5, b5] = hslToRgb(0, 0, 0);
      expect(r5).toBe(0);
      expect(g5).toBe(0);
      expect(b5).toBe(0);
    });

    it('should handle edge cases in hue2rgb helper function', () => {
      // Test the different branches of the hue2rgb function
      // t < 0
      const [r0, g0, b0] = hslToRgb(-0.1, 1, 0.5);
      expect(r0).toBe(255);
      expect(g0).toBe(0);
      expect(b0).toBe(153);

      // t > 1
      const [r01, g01, b01] = hslToRgb(1.1, 1, 0.5);
      expect(r01).toBe(255);
      expect(g01).toBe(153);
      expect(b01).toBe(0);

      // t < 1/6
      const [r1, g1, b1] = hslToRgb(0.05, 1, 0.5);
      expect(r1).toBe(255);
      expect(g1).toBe(77);
      expect(b1).toBe(0);

      // t < 1/2
      const [r2, g2, b2] = hslToRgb(0.2, 1, 0.5);
      expect(r2).toBe(204);
      expect(g2).toBe(255);
      expect(b2).toBe(0);

      // t < 2/3
      const [r3, g3, b3] = hslToRgb(0.55, 1, 0.5);
      expect(r3).toBe(0);
      expect(g3).toBe(178);
      expect(b3).toBe(255);

      // t >= 2/3
      const [r4, g4, b4] = hslToRgb(0.8, 1, 0.5);
      expect(r4).toBe(204);
      expect(g4).toBe(0);
      expect(b4).toBe(255);
    });

    it('should handle different lightness cases', () => {
      // Test l < 0.5
      const [r1, g1, b1] = hslToRgb(0.3, 0.8, 0.3);
      expect(r1).toBe(40);
      expect(g1).toBe(138);
      expect(b1).toBe(15);

      // Test l >= 0.5
      const [r2, g2, b2] = hslToRgb(0.3, 0.8, 0.7);
      expect(r2).toBe(142);
      expect(g2).toBe(240);
      expect(b2).toBe(117);
    });
  });

  describe('rgbToHex', () => {
    it('should convert RGB to hex correctly', () => {
      expect(rgbToHex(255, 0, 0)).toBe('#ff0000');
      expect(rgbToHex(0, 255, 0)).toBe('#00ff00');
      expect(rgbToHex(0, 0, 255)).toBe('#0000ff');
      expect(rgbToHex(255, 255, 255)).toBe('#ffffff');
      expect(rgbToHex(0, 0, 0)).toBe('#000000');
    });

    it('should clamp RGB values to 0-255 range', () => {
      expect(rgbToHex(-10, 300, 128)).toBe('#00ff80');
    });

    it('should pad single-digit hex values with leading zeros', () => {
      expect(rgbToHex(10, 10, 10)).toBe('#0a0a0a');
    });
  });
});

describe('darkenColor utility', () => {
  it('should darken a color with default amount', () => {
    const result = darkenColor('#FFFFFF');
    expect(result).toMatch(/^#[0-9A-F]{6}$/i);
    expect(result).not.toBe('#FFFFFF');
  });

  it('should darken a color with custom amount', () => {
    const result = darkenColor('#FFFFFF', 0.5);
    expect(result).toMatch(/^#[0-9A-F]{6}$/i);
    expect(result).not.toBe('#FFFFFF');
  });

  it('should return the original color if input is invalid color (string)', () => {
    const originalWarn = console.warn;
    console.warn = jest.fn();

    const invalidColor = 'not-a-color';
    const result = darkenColor(invalidColor);
    expect(result).toBe(invalidColor);

    console.warn = originalWarn;
  });

  it('should handle invalid colors (not string)', () => {
    const originalWarn = console.warn;
    console.warn = jest.fn();

    const result = darkenColor(123456, 0.3);
    expect(result).toBe(123456);

    console.warn = originalWarn;
  })

  it('should use default amount if amount is invalid', () => {
    // Mock console.warn to avoid test output pollution
    const originalWarn = console.warn;
    console.warn = jest.fn();

    // Test with negative amount
    const result1 = darkenColor('#FFFFFF', -0.5);
    expect(result1).toMatch(/^#[0-9A-F]{6}$/i);
    expect(result1).not.toBe('#FFFFFF');

    // Test with amount > 1
    const result2 = darkenColor('#FFFFFF', 1.5);
    expect(result2).toMatch(/^#[0-9A-F]{6}$/i);
    expect(result2).not.toBe('#FFFFFF');

    // Test with NaN
    const result3 = darkenColor('#FFFFFF', NaN);
    expect(result3).toMatch(/^#[0-9A-F]{6}$/i);
    expect(result3).not.toBe('#FFFFFF');

    // Restore console.warn
    console.warn = originalWarn;
  });

  // Test with different colors
  it('should correctly darken different colors', () => {
    // Red
    const redResult = darkenColor('#FF0000', 0.3);
    expect(redResult).toMatch(/^#[0-9A-F]{6}$/i);
    expect(redResult).not.toBe('#FF0000');

    // Green
    const greenResult = darkenColor('#00FF00', 0.3);
    expect(greenResult).toMatch(/^#[0-9A-F]{6}$/i);
    expect(greenResult).not.toBe('#00FF00');

    // Blue
    const blueResult = darkenColor('#0000FF', 0.3);
    expect(blueResult).toMatch(/^#[0-9A-F]{6}$/i);
    expect(blueResult).not.toBe('#0000FF');
  });

  it('should handle shorthand hex colors', () => {
    const result = darkenColor('#FFF', 0.3);
    expect(result).toMatch(/^#[0-9A-F]{6}$/i);
    expect(result).not.toBe('#FFFFFF');
  });

  it('should handle hex colors without # prefix', () => {
    const result = darkenColor('FFFFFF', 0.3);
    expect(result).toMatch(/^#[0-9A-F]{6}$/i);
    expect(result).not.toBe('#FFFFFF');
  });

  it('should handle extreme darkening values', () => {
    // Amount = 0 (no darkening)
    const noChange = darkenColor('#FFFFFF', 0);
    expect(noChange).toBe('#ffffff');

    // Amount = 1 (full darkening to black)
    const fullDark = darkenColor('#FFFFFF', 1);
    expect(fullDark).toBe('#000000');
  });
});
