// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`IconFrame Component should render AssetFrame when entityType is asset and iconType is normal 1`] = `
<div
  class="icon-frame-wrapper"
>
  <div
    class="icon-frame-circle"
    style="background-color: rgb(255, 0, 0);"
  />
</div>
`;

exports[`IconFrame Component should render AssetMapFrame when entityType is asset and iconType is map 1`] = `
<div
  class="icon-frame-wrapper"
>
  <svg
    fill="none"
    height="58"
    viewBox="-0.5 -0.5 46 59"
    width="45"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      clip-rule="evenodd"
      d="M33.2287 42.1914C40.2404 38.3888 45 30.9748 45 22.4516C45 10.0519 34.9264 0 22.5 0C10.0736 0 0 10.0519 0 22.4516C0 31.1482 4.95517 38.6899 12.2019 42.4189L17.247 48.8941C17.2781 48.9339 17.3053 48.9765 17.3283 49.0214L21.5951 57.3228C21.8945 57.9053 22.7261 57.9086 23.03 57.3284L27.2107 49.348C27.2381 49.2957 27.2711 49.2466 27.3092 49.2016L33.2287 42.1914Z"
      fill="#FF0000"
      fill-rule="evenodd"
      stroke="darkened-#FF0000-0.4"
      stroke-width="1"
    />
  </svg>
</div>
`;

exports[`IconFrame Component should render SiteFrame when entityType is site and iconType is normal 1`] = `
<div
  class="icon-frame-wrapper"
>
  <svg
    fill="none"
    height="43"
    viewBox="0 0 48 43"
    width="48"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M33.2036 0.541016H14.7964C13.3743 0.541016 12.0592 1.29603 11.3421 2.52409L1.23046 19.8417C0.474907 21.1357 0.505938 22.7435 1.31087 24.0074L11.821 40.5101C12.5553 41.6632 13.8278 42.3614 15.1948 42.3614H32.8052C34.1722 42.3614 35.4447 41.6632 36.179 40.5101L46.6891 24.0074C47.4941 22.7435 47.5251 21.1357 46.7695 19.8417L36.6579 2.52409C35.9408 1.29603 34.6257 0.541016 33.2036 0.541016Z"
      fill="#FF0000"
    />
    <g
      transform="translate(5, 4)"
    >
      <path
        d="M25.8116 0.541016H12.1884C10.7684 0.541016 9.45479 1.2939 8.73702 2.51918L1.23748 15.3214C0.47798 16.6179 0.509154 18.2307 1.3182 19.4969L9.11198 31.6947C9.84692 32.845 11.1177 33.541 12.4827 33.541H25.5173C26.8823 33.541 28.1531 32.845 28.888 31.6947L36.6818 19.4969C37.4908 18.2307 37.522 16.6179 36.7625 15.3214L29.263 2.51918C28.5452 1.2939 27.2316 0.541016 25.8116 0.541016Z"
        fill="#00FF00"
      />
    </g>
  </svg>
</div>
`;

exports[`IconFrame Component should render SiteMapFrame when entityType is site and iconType is map 1`] = `
<div
  class="icon-frame-wrapper"
>
  <svg
    fill="none"
    height="58"
    viewBox="0 0 49 58"
    width="49"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M33.9622 1H15.0378C13.626 1 12.3187 1.74432 11.5981 2.95846L1.53561 19.9127C0.626147 21.445 0.839969 23.3942 2.06 24.6929L15.3924 38.8854C15.4637 38.9613 15.5227 39.0479 15.5673 39.142L23.6206 56.1434C23.9775 56.897 25.0458 56.9084 25.4187 56.1625L33.9383 39.1234C33.9792 39.0415 34.0312 38.9657 34.0928 38.8979L47.0103 24.6886C48.1927 23.388 48.3875 21.468 47.4904 19.9564L37.4019 2.95846C36.6813 1.74432 35.374 1 33.9622 1Z"
      fill="#FF0000"
      stroke="darkened-#FF0000-0.4"
    />
    <g
      transform="translate(5.5, 5)"
    >
      <path
        d="M25.8116 0H12.1884C10.7684 0 9.45479 0.752883 8.73702 1.97817L1.50109 14.3304C0.609766 15.8519 0.819988 17.7777 2.01861 19.0711L12.8128 30.7189C13.5697 31.5357 14.633 32 15.7467 32H23.165C24.33 32 25.437 31.4921 26.1969 30.6091L36.1364 19.0585C37.2512 17.763 37.4197 15.9023 36.5559 14.4276L29.263 1.97817C28.5452 0.752882 27.2316 0 25.8116 0Z"
        fill="#00FF00"
      />
    </g>
  </svg>
</div>
`;
