import "./style.less";
import { darkenColor } from "./logic";

/**
 * Represents the type of entity that the icon frame will display.
 * - 'site': Used for site-related icons, renders with a hexagonal shape
 * - 'asset': Used for asset-related icons, renders with a circular shape
 */
type EntityType = "site" | "asset";

/**
 * Represents the display mode of the icon.
 * - 'map': Used when displaying icons on a map view, has specific styling for map visibility
 * - 'normal': Used in regular UI components like lists, tables, etc.
 */
type IconType = "map" | "normal";

/**
 * AssetMapFrame Component
 *
 * Renders a map-specific SVG frame for asset icons with a pin/marker shape.
 * Used when displaying asset icons on maps.
 *
 * @param props - Component props
 * @param props.outerColor - The color to fill the frame with
 * @param props.darkAmount - Amount to darken the stroke color (0-1 scale)
 * @returns SVG element with the asset map frame
 */
const AssetMapFrame = ({
  outerColor,
  darkAmount,
}: {
  outerColor: string;
  darkAmount: number;
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="45"
      height="58"
      viewBox="-0.5 -0.5 46 59" 
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M33.2287 42.1914C40.2404 38.3888 45 30.9748 45 22.4516C45 10.0519 34.9264 0 22.5 0C10.0736 0 0 10.0519 0 22.4516C0 31.1482 4.95517 38.6899 12.2019 42.4189L17.247 48.8941C17.2781 48.9339 17.3053 48.9765 17.3283 49.0214L21.5951 57.3228C21.8945 57.9053 22.7261 57.9086 23.03 57.3284L27.2107 49.348C27.2381 49.2957 27.2711 49.2466 27.3092 49.2016L33.2287 42.1914Z"
        fill={outerColor}
        stroke={darkenColor(outerColor, darkAmount)}
        stroke-width="1"
      />
    </svg>
  );
};

/**
 * SiteMapFrame Component
 *
 * Renders a map-specific SVG frame for site icons with a hexagonal marker shape.
 * Used when displaying site icons on maps.
 *
 * @param props - Component props
 * @param props.outerColor - The color for the outer frame
 * @param props.innerColor - The color for the inner part of the frame
 * @param props.darkAmount - Amount to darken the stroke color (0-1 scale)
 * @returns SVG element with the site map frame
 */
const SiteMapFrame = ({
  outerColor,
  innerColor,
  darkAmount,
}: {
  outerColor: string;
  innerColor: string;
  darkAmount: number;
}) => {
  const offsetX = 5.5;
  const offsetY = 5;
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="49"
      height="58"
      viewBox="0 0 49 58"
      fill="none"
    >
      <path
        d="M33.9622 1H15.0378C13.626 1 12.3187 1.74432 11.5981 2.95846L1.53561 19.9127C0.626147 21.445 0.839969 23.3942 2.06 24.6929L15.3924 38.8854C15.4637 38.9613 15.5227 39.0479 15.5673 39.142L23.6206 56.1434C23.9775 56.897 25.0458 56.9084 25.4187 56.1625L33.9383 39.1234C33.9792 39.0415 34.0312 38.9657 34.0928 38.8979L47.0103 24.6886C48.1927 23.388 48.3875 21.468 47.4904 19.9564L37.4019 2.95846C36.6813 1.74432 35.374 1 33.9622 1Z"
        fill={outerColor}
        stroke={darkenColor(outerColor, darkAmount)}
      />

      <g transform={`translate(${offsetX}, ${offsetY})`}>
        {/** innerIcon box-size: 38 x 32 */}
        <path
          d="M25.8116 0H12.1884C10.7684 0 9.45479 0.752883 8.73702 1.97817L1.50109 14.3304C0.609766 15.8519 0.819988 17.7777 2.01861 19.0711L12.8128 30.7189C13.5697 31.5357 14.633 32 15.7467 32H23.165C24.33 32 25.437 31.4921 26.1969 30.6091L36.1364 19.0585C37.2512 17.763 37.4197 15.9023 36.5559 14.4276L29.263 1.97817C28.5452 0.752882 27.2316 0 25.8116 0Z"
          fill={innerColor}
        />
      </g>
    </svg>
  );
};

/**
 * SiteFrame Component
 *
 * Renders a standard SVG frame for site icons with a hexagonal shape.
 * Used for displaying site icons in regular UI components.
 *
 * @param props - Component props
 * @param props.outerColor - The color for the outer frame
 * @param props.innerColor - The color for the inner part of the frame
 * @returns SVG element with the site frame
 */
const SiteFrame = ({
  outerColor,
  innerColor,
}: {
  outerColor: string;
  innerColor: string;
}) => {
  const offsetX = 5;
  const offsetY = 4;
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="48"
      height="43"
      viewBox="0 0 48 43"
      fill="none"
    >
      <path
        d="M33.2036 0.541016H14.7964C13.3743 0.541016 12.0592 1.29603 11.3421 2.52409L1.23046 19.8417C0.474907 21.1357 0.505938 22.7435 1.31087 24.0074L11.821 40.5101C12.5553 41.6632 13.8278 42.3614 15.1948 42.3614H32.8052C34.1722 42.3614 35.4447 41.6632 36.179 40.5101L46.6891 24.0074C47.4941 22.7435 47.5251 21.1357 46.7695 19.8417L36.6579 2.52409C35.9408 1.29603 34.6257 0.541016 33.2036 0.541016Z"
        fill={outerColor}
      />

      <g transform={`translate(${offsetX}, ${offsetY})`}>
        <path
          d="M25.8116 0.541016H12.1884C10.7684 0.541016 9.45479 1.2939 8.73702 2.51918L1.23748 15.3214C0.47798 16.6179 0.509154 18.2307 1.3182 19.4969L9.11198 31.6947C9.84692 32.845 11.1177 33.541 12.4827 33.541H25.5173C26.8823 33.541 28.1531 32.845 28.888 31.6947L36.6818 19.4969C37.4908 18.2307 37.522 16.6179 36.7625 15.3214L29.263 2.51918C28.5452 1.2939 27.2316 0.541016 25.8116 0.541016Z"
          fill={innerColor}
        />
      </g>
    </svg>
  );
};

/**
 * AssetFrame Component
 *
 * Renders a standard circular frame for asset icons.
 * Used for displaying asset icons in regular UI components.
 *
 * @param props - Component props
 * @param props.outerColor - The color to fill the circle with
 * @returns A div element with circular styling
 */
const AssetFrame = ({ outerColor, innerColor }: { outerColor: string, innerColor?: string }) => {
  return (
    <div
      className="icon-frame-circle"
      style={{ backgroundColor: outerColor }}
    >
      {innerColor && (
        <div
          className="icon-frame-circle-inner"
          style={{ backgroundColor: innerColor }}
        ></div>
      )}
    </div>
  );
};

/**
 * Type for the props of any frame component
 */
type FrameComponentProps =
  | { outerColor: string; innerColor?: string; darkAmount?: number } // AssetMapFrame or AssetFrame
  | { outerColor: string; innerColor: string; darkAmount?: number }; // SiteMapFrame or SiteFrame

/**
 * Type for a frame component function
 */
type FrameComponent = React.FC<FrameComponentProps>;

/**
 * Map of frame components organized by entity type and icon type.
 * Used to dynamically select the appropriate frame component based on the provided props.
 */
const frameComponentsMap: Record<EntityType, Record<IconType, FrameComponent>> = {
  site: {
    map: SiteMapFrame as FrameComponent,
    normal: SiteFrame as FrameComponent,
  },
  asset: {
    map: AssetMapFrame as FrameComponent,
    normal: AssetFrame as FrameComponent,
  },
};

/**
 * Interface for the IconFrame component props
 */
interface IconFrameProps {
  /**
   * The colors for the icon frame
   * @property {string} outer - The color for the outer part of the frame
   * @property {string} inner - The color for the inner part of the frame (used for site frames)
   */
  color: { outer: string; inner: string };

  /**
   * The type of entity the icon represents
   */
  entityType: EntityType;

  /**
   * The type of icon display mode
   */
  iconType: IconType;

  /**
   * Whether to display the full frame (mainly for asset normal frame)
   */
  fullFrame?: boolean;
}

/**
 * IconFrame Component
 *
 * A configurable component that renders different frame styles for status icons
 * based on entity type (site/asset) and icon type (map/normal).
 *
 * The component dynamically selects the appropriate frame component from the frameComponentsMap
 * based on the provided entityType and iconType props.
 *
 * @component
 * @example
 * // Asset icon for normal display
 * <IconFrame
 *   color={{ outer: '#FF0000', inner: '#00FF00' }}
 *   entityType="asset"
 *   iconType="normal"
 * />
 *
 * // Site icon for map display
 * <IconFrame
 *   color={{ outer: '#BAEBC7', inner: '#008C3F' }}
 *   entityType="site"
 *   iconType="map"
 * />
 *
 * @param props - Component props
 * @returns A div element containing the appropriate frame component
 */
const IconFrame = ({
  color,
  entityType,
  iconType,
  fullFrame = false,
}: IconFrameProps) => {
  const borderDarkness = 0.4;

  let frameProps: FrameComponentProps = {
    outerColor: color.outer,
    // Add darkAmount only for map icons
    ...(iconType === "map" ? { darkAmount: borderDarkness } : {})
  };

  if (entityType === "site" || fullFrame) {
    frameProps = {
      ...frameProps,
      innerColor: color.inner,
    };
  }

  // Get the appropriate frame component based on entity and icon type
  const SelectedFrameComponent = frameComponentsMap[entityType][iconType];

  return (
    <div className="icon-frame-wrapper">
      <SelectedFrameComponent {...frameProps} />
    </div>
  );
};

export default IconFrame;
