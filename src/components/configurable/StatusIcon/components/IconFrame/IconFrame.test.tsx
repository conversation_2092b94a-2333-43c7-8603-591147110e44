import React from 'react';
import { render } from '@testing-library/react';
import Icon<PERSON>rame from './IconFrame';
import { darkenColor } from './logic';

jest.mock('./logic', () => ({
  darkenColor: jest.fn((color, amount) => `darkened-${color}-${amount}`)
}));

describe('IconFrame Component', () => {
  it('should render SiteFrame when entityType is site and iconType is normal', () => {
    const { container } = render(
      <IconFrame
        color={{ outer: '#FF0000', inner: '#00FF00' }}
        entityType="site"
        iconType="normal"
      />
    );

    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('width', '48');
    expect(svg).toHaveAttribute('height', '43');
    expect(container.firstChild).toMatchSnapshot();
  });

  it('should render SiteMapFrame when entityType is site and iconType is map', () => {
    const { container } = render(
      <IconFrame
        color={{ outer: '#FF0000', inner: '#00FF00' }}
        entityType="site"
        iconType="map"
      />
    );

    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('width', '49');
    expect(svg).toHaveAttribute('height', '58');
    expect(container.firstChild).toMatchSnapshot();
  });

  it('should render AssetFrame when entityType is asset and iconType is normal', () => {
    const { container } = render(
      <IconFrame
        color={{ outer: '#FF0000', inner: '#00FF00' }}
        entityType="asset"
        iconType="normal"
      />
    );

    expect(container.querySelector('.icon-frame-circle')).toBeInTheDocument();
    expect(container.firstChild).toMatchSnapshot();
  });

  it('should render AssetMapFrame when entityType is asset and iconType is map', () => {
    const { container } = render(
      <IconFrame
        color={{ outer: '#FF0000', inner: '#00FF00' }}
        entityType="asset"
        iconType="map"
      />
    );

    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('width', '45');
    expect(svg).toHaveAttribute('height', '58');
    expect(container.firstChild).toMatchSnapshot();
  });

  it('should call darkenColor function with correct arguements when entityType is site and iconType is map', () => {
    render(
      <IconFrame
        color={{ outer: '#FF0000', inner: '#00FF00' }}
        entityType="site"
        iconType="map"
      />
    );

    expect(darkenColor).toHaveBeenCalledWith('#FF0000', 0.4);
  });

  it('should call darkenColor function with correct arguements when entityType is asset and iconType is map', () => {
    render(
      <IconFrame
        color={{ outer: '#FF0000', inner: '#00FF00' }}
        entityType="asset"
        iconType="map"
      />
    );

    expect(darkenColor).toHaveBeenCalledWith('#FF0000', 0.4);
  });
});
