/**
 * Converts an RGB color value to HSL.
 * Assumes r, g, and b are contained in the set [0, 255] and returns h, s, and l in the set [0, 1].
 *
 * @param r - The red color value (0-255)
 * @param g - The green color value (0-255)
 * @param b - The blue color value (0-255)
 * @returns The HSL representation as [h, s, l] (each 0-1)
 */
export function rgbToHsl(r: number, g: number, b: number): [number, number, number] {
    r /= 255; g /= 255; b /= 255;
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0; // Achromatic case
    let s = 0;
    let l = (max + min) / 2; // Lightness

    if (max !== min) {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min); // Saturation
      // Hue calculation
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }

    return [h, s, l];
  }

  /**
   * Converts an HSL color value to RGB.
   * Assumes h, s, and l are contained in the set [0, 1] and returns r, g, and b in the set [0, 255].
   *
   * @param h - The hue (0-1)
   * @param s - The saturation (0-1)
   * @param l - The lightness (0-1)
   * @returns The RGB representation as [r, g, b] (each 0-255)
   */
  export function hslToRgb(h: number, s: number, l: number): [number, number, number] {
    let r: number, g: number, b: number;

    if (s === 0) {
      r = g = b = l; // Achromatic
    } else {
      // Helper function for hue to RGB conversion
      const hue2rgb = (p: number, q: number, t: number): number => {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1 / 6) return p + (q - p) * 6 * t;
        if (t < 1 / 2) return q;
        if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
        return p;
      };

      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      r = hue2rgb(p, q, h + 1 / 3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1 / 3);
    }

    // Convert back to 0-255 range and round
    return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
  }

  /**
   * Converts a Hex color string to an RGB array.
   * Handles 3 and 6 digit hex codes, with or without the leading '#'.
   *
   * @param hex - The hex color string (e.g., "#abc", "#ABCDEF")
   * @returns The RGB representation [r, g, b] or null if the hex string is invalid.
   */
  export function hexToRgb(hex: string): [number, number, number] | null {
    // Expand shorthand form (e.g., "03F") to full form (e.g., "0033FF")
    const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
    const fullHex = hex.replace(shorthandRegex, (_, r, g, b) => r + r + g + g + b + b);

    // Parse the full hex string
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(fullHex);
    return result
      ? [
          parseInt(result[1], 16), // Red
          parseInt(result[2], 16), // Green
          parseInt(result[3], 16), // Blue
        ]
      : null; // Return null if parsing fails
  }

  /**
   * Converts an RGB color value array to a Hex color string.
   * Ensures values are clamped within the 0-255 range.
   *
   * @param r - The red color value (0-255)
   * @param g - The green color value (0-255)
   * @param b - The blue color value (0-255)
   * @returns The Hex representation (e.g., "#RRGGBB")
   */
  export function rgbToHex(r: number, g: number, b: number): string {
    // Helper to convert a single color channel to its 2-digit hex representation
    const toHex = (c: number): string => {
      // Clamp value to ensure it's between 0 and 255
      const clamped = Math.max(0, Math.min(255, Math.round(c)));
      const hex = clamped.toString(16);
      return hex.length === 1 ? "0" + hex : hex; // Pad with leading zero if needed
    };
    return "#" + toHex(r) + toHex(g) + toHex(b);
  }


  /**
   * Darkens a HEX color by a given amount (percentage 0-1).
   * Uses the HSL color space for a more perceptually accurate darkening effect.
   *
   * @param hexColor - The hex color string (e.g., "#BAEBC7", "#eed960").
   * @param amount - The factor by which to darken the color (0 = no change, 1 = black).
   *                 Must be between 0 and 1. Defaults to 0.3 (30%).
   * @returns The darkened hex color string, or the original color string if the input is invalid.
   */
  export const darkenColor = (hexColor: string, amount: number = 0.3): string => {
    // Validate input color string
    if (typeof hexColor !== 'string' || !hexColor) {
      console.warn("darkenColor: Invalid hexColor input provided:", hexColor);
      return hexColor;
    }

    // Validate and clamp the darkening amount
    let validAmount = amount;
    if (typeof amount !== 'number' || isNaN(amount) || amount < 0 || amount > 1) {
        const defaultAmount = 0.3;
        console.warn(`darkenColor: Invalid amount ${amount}. Clamping between 0 and 1.`);
        // Clamp the value between 0 and 1, defaulting if completely invalid
        validAmount = Math.max(0, Math.min(1, typeof amount === 'number' && !isNaN(amount) ? amount : defaultAmount));
        if (amount !== validAmount) console.warn(`Using clamped amount: ${validAmount}`);
    }


    // Convert hex to RGB
    const rgb = hexToRgb(hexColor);
    if (!rgb) {
      console.warn("darkenColor: Could not parse hexColor:", hexColor);
      return hexColor; // Return original if parsing failed
    }

    // Convert RGB to HSL
    let [h, s, l] = rgbToHsl(rgb[0], rgb[1], rgb[2]);

    // Decrease the Lightness component
    // Multiply lightness by (1 - amount) to scale it towards black
    l = l * (1 - validAmount);
    l = Math.max(0, l); // Ensure lightness doesn't go below 0

    // Convert the modified HSL back to RGB
    const [rNew, gNew, bNew] = hslToRgb(h, s, l);

    // Convert the new RGB back to Hex
    return rgbToHex(rNew, gNew, bNew);
  }