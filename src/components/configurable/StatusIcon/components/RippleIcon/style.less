.ripple-circle {
    width: var(--circle-size, 15px); /* Default fallback if var isn't set */
    height: var(--circle-size, 15px);
    background-color: var(--ripple-color, #E67E22);
    border: 1px solid white;
  
    border-radius: 50%;
    position: relative;  /* Crucial for positioning the ::after pseudo-element */
    display: inline-block; 
  }
  
  /* The Ripple pseudo-element */
  .ripple-circle::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  
    width: 100%;
    height: 100%;
  
    border-radius: 50%;
  
    /* Ripple style using box-shadow (doesn't affect layout) */
    /* Use the SAME color variable, but control opacity via animation */
    box-shadow: 0 0 0 0px var(--ripple-color, #E67E22);
  
    /* Animation */
    animation: ripple-pulse 1.2s infinite ease-out;
  
    /* Ensure it doesn't interfere with clicks */
    pointer-events: none;
  
    opacity: 0;
  }
  
  /* The Ripple Animation Keyframes */
  @keyframes ripple-pulse {
    0% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0.6; /* Adjust starting opacity */
       /* Box shadow starts thin */
      box-shadow: 0 0 0 0px var(--ripple-color, #E67E22);
    }
    100% {
      transform: translate(-50%, -50%) scale(1.8);
      opacity: 0;
      /* Box shadow expands and fades (via overall opacity) */
      box-shadow: 0 0 0 5px var(--ripple-color, #E67E22);  /* Adjust final thickness if needed, but opacity handles fade */
    }
  }