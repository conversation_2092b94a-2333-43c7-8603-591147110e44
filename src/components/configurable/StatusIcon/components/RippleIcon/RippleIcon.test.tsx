import React from 'react';
import { render } from '@testing-library/react';
import RippleIcon from './RippleIcon';

describe('RippleIcon Component', () => {
  it('should render with default color and size', () => {
    const { container } = render(<RippleIcon color="default" />);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('should render with custom color', () => {
    const { container } = render(<RippleIcon color="#FF0000" />);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('should render with custom size', () => {
    const { container } = render(<RippleIcon color="default" size={20} />);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('should render with both custom color and size', () => {
    const { container } = render(<RippleIcon color="#0000FF" size={25} />);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('should apply correct CSS variables', () => {
    const { container } = render(<RippleIcon color="#FF0000" size={30} />);
    const rippleDiv = container.firstChild as HTMLElement;
    
    expect(rippleDiv).toHaveStyle({
      '--ripple-color': '#FF0000',
      '--circle-size': '30px'
    });
  });

  it('should use default color when "default" is specified', () => {
    const { container } = render(<RippleIcon color="default" />);
    const rippleDiv = container.firstChild as HTMLElement;
    
    expect(rippleDiv).toHaveStyle({
      '--ripple-color': '#E67E22',
      '--circle-size': '15px'
    });
  });
});