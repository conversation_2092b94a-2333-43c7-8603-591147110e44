import React from 'react';
import './style.less';

/**
 * <PERSON>ps interface for the RippleIcon component
 */
interface RippleIconProps {
  /**
   * The color of the icon and ripple effect.
   * Use "default" for the predefined orange color (#E67E22).
   */
  color: string;

  /**
   * The size of the icon in pixels.
   * @default 15
   */
  size?: number;
}

/**
 * RippleIcon Component
 *
 * A component that renders a circular icon with a pulsating ripple effect.
 * The ripple effect is created using CSS animations on a pseudo-element.
 *
 * @component
 * @example
 * // Basic usage with default size
 * <RippleIcon color="#FF0000" />
 *
 * // With custom size
 * <RippleIcon color="#0000FF" size={25} />
 *
 * // Using default color
 * <RippleIcon color="default" />
 *
 * @returns A div element with ripple effect styling
 */
const RippleIcon: React.FC<RippleIconProps> = ({ color, size = 15 }) => {
  // Convert "default" color to the actual hex value, otherwise use the provided color
  let rippleColor = color === "default" ? "#E67E22" : color;
  const iconStyle = {
    '--ripple-color': rippleColor,
    '--circle-size': `${size}px`,
  };

  return (
    <div className="ripple-circle" style={iconStyle as React.CSSProperties} >
      {/* The circle itself is the div, the ripple effect is created by the ::after pseudo-element */}
    </div>
  );
};

export default RippleIcon;