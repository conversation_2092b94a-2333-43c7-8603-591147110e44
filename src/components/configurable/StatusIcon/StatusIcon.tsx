import React from 'react';
import { Tooltip } from 'antd';
import './style.less';
import DefaultConfig from './configs/DefaultConfig.json';
import IconFrame from './components/IconFrame';
import RippleIcon from './components/RippleIcon';

/**
 * StatusIcon Component
 *
 * A configurable component that displays status icons for assets and sites with customizable
 * colors, indicators, and tooltips. The component supports different display modes (map/normal)
 * and entity types (site/asset) with appropriate visual styling for each combination.
 *
 * The component consists of:
 * - A main icon with a frame (using IconFrame component)
 * - A category icon displayed in the center
 * - Optional indicator icons on the left and right sides
 * - An optional action icon (ripple effect) at the top
 * - An optional tooltip with additional information
 *
 * @example
 * // Basic usage for an asset
 * <StatusIcon
 *   config={{
 *     statusColor: "#008C3F",
 *     categoryIcon: "https://example.com/icon.svg"
 *   }}
 * />
 *
 * // Site with custom styling and indicators
 * <StatusIcon
 *   config={{
 *     entityType: "site",
 *     iconType: "map",
 *     statusColor: "#964B00",
 *     deviceStatusColor: "#BAEBC7",
 *     categoryIcon: "https://example.com/site-icon.svg",
 *     leftIcons: ["https://example.com/indicator1.svg"],
 *     rightIcons: ["https://example.com/indicator2.svg"],
 *     tooltipItems: [
 *       { title: "Status", value: "Active" },
 *       { title: "Site ID", value: "S-001" }
 *     ]
 *   }}
 * />
 */

/**
 * Valid entity types for the status icon
 */
type EntityType = 'site' | 'asset';

/**
 * Valid icon display modes
 */
type IconType = 'map' | 'normal';

/**
 * Configuration options for the StatusIcon component
 */
export interface StatusIconConfig {
  /**
   * The display mode of the icon
   * - 'map': Used when displaying icons on a map view
   * - 'normal': Used in regular UI components like lists, tables, etc.
   * @default 'normal'
   */
  iconType?: IconType;
  /**
   * The main color of the status icon
   * Used for the inner part of the icon frame and the background of the category icon
   * Typically represents the status of the entity (e.g., green for running, grey for offline)
   */
  statusColor?: string;
  /**
   * Optional color for the outer part of the icon frame
   * If not provided, statusColor will be used for both inner and outer parts
   * Can be used to represent a secondary status (e.g., device status)
   */
  deviceStatusColor?: string;
  /**
   * The type of entity the icon represents
   * - 'site': Used for site-related icons, renders with a hexagonal shape
   * - 'asset': Used for asset-related icons, renders with a circular shape
   * @default 'asset'
   */
  entityType?: EntityType;
  /**
   * URL of the icon image to display in the center of the status icon
   * Represents the category or type of the entity (e.g., battery, solar panel, etc.)
   */
  categoryIcon?: string;
  /**
   * Array of URLs / images for indicator icons to display on the left side of the status icon
   * Maximum of 2 icons will be displayed, additional icons will be ignored
   * @default []
   */
  leftIcons?: string[];
  /**
   * Array of URLs for indicator icons to display on the right side of the status icon
   * Maximum of 2 icons for site type or 3 icons for asset type will be displayed
   * @default []
   */
  rightIcons?: string[];
  /**
   * If provided, displays a ripple action icon at the top of the status icon
   * The value can be a color string (e.g., '#E67E22') or "default" to use the default color
   * @default ''
   */
  actionIcon?: string;
  /**
   * Array of tooltip items to display when hovering over the status icon
   * Each item has a title and value that will be displayed as "title : value"
   * If provided, the icon will be wrapped in an antd Tooltip component
   * @default []
   */
  tooltipItems?: {title: string, value?: string}[];
}

/**
 * Props for the StatusIcon component
 */
export interface StatusIconProps {
  /**
   * Configuration object for the StatusIcon
   * Any missing properties will be filled with values from DefaultConfig
   */
  config: StatusIconConfig;
}

/**
 * Renders a status icon for assets or sites with customizable styling and indicators
 *
 * @param props - Component props
 * @returns A status icon with the specified configuration, optionally wrapped in a tooltip
 */
const StatusIcon: React.FC<StatusIconProps> = ({config}) => {
  // Merge provided config with default config
  const mergedConfig = { ...DefaultConfig, ...config };

  const {
    statusColor,
    deviceStatusColor,
    categoryIcon,
    leftIcons = [],
    rightIcons = [],
    actionIcon,
    tooltipItems
  } = mergedConfig;

  // Ensure proper typing for these values
  const iconType = mergedConfig.iconType as IconType;
  const entityType = mergedConfig.entityType as EntityType;

  const maxLeft = 2;
  const maxRight = entityType === 'site' ? 2 : 3;

  const indicatorIcons = {
    left: leftIcons.slice(0, maxLeft),
    right: rightIcons.slice(0, maxRight),
  }

  const statusBorderColor = deviceStatusColor || statusColor;
  const isTooltip = Array.isArray(tooltipItems) && tooltipItems.length > 0;

  let containerClass = `status-icon-container entity-${entityType} icon-${iconType}`;
  if(isTooltip) {
    containerClass += ' with-tooltip';
  }

  /**
   * Renders indicator icons for the specified slot (left or right)
   *
   * @param slot - The position where indicators should be rendered ('left' or 'right')
   * @returns A div containing the indicator icons, or null if no icons are available
   */
  const renderIndicators = (slot: 'left' | 'right') => {
    const icons = indicatorIcons[slot];

    if (Array.isArray(icons) && icons.length > 0) {
      return (
        <div className={`${slot}-indicators`}>
          {icons.map((icon, index) => (
            icon ? (
              <img
                key={`${slot}-${index}`}
                className={`indicator-slot indicator-${entityType} ${slot}${index + 1}`}
                src={icon}
              />
            ) : null
          ))}
        </div>
      );
    } return null;
  };

  const content = (
    <div className={containerClass}>
      <div className="icon-with-frame">
          <IconFrame color={{outer: statusBorderColor, inner: statusColor}} entityType={entityType} iconType={iconType} />
          <div className={`category-icon-${entityType} icon-${iconType}`} style={{backgroundColor: statusColor}}>
            <img src={categoryIcon} />
          </div>
      </div>

        {renderIndicators('left')}
        {renderIndicators('right')}

        {actionIcon && (
          <div className="action-icon">
            <RippleIcon color={actionIcon} size={11} />
          </div>
        )}
    </div>
  );

  if (isTooltip) {
    const tooltipContent = (<div>
        {tooltipItems.map((item, index) => (
            <div key={`tooltip-${index}`}>{item.title}{"value" in item ? `: ${item.value}` : ''}</div>
        ))}
    </div>)

    return (
        <Tooltip title={tooltipContent}>
            {content}
        </Tooltip>
    );
  }

  return content;
};

export default StatusIcon;