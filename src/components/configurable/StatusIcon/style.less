.status-icon-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  vertical-align: middle;
  flex-shrink: 0;

  &.with-tooltip {
    cursor: pointer;
  }
}

.icon-with-frame {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
}

.entity-site.icon-normal
.icon-with-frame {
  width: 48px;
  height: 42px;
}

.category-icon-site {
  top: 11px;
  display: flex;
  align-items: center;
  position: absolute;
  z-index: 1;

  &.icon-normal {
    top: 11px;
  }

  img {
    width: 18px;
    height: 18px;
  }
}

.category-icon-asset {
  top: 6px;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 33px;
  height: 33px;
  z-index: 1;

  &.icon-normal {
    top: unset;
    width: 38px;
    height: 38px;
  }

  img {
    width: 18px;
  }
}

.indicator-slot {
  position: absolute;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: all;
  z-index: 2;

  & > * {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  // Indicator Positioning - adjust transforms as needed
  &.left1 {
    top: 25px;
    left: 0px;

    &.indicator-site {
      top: 25px;
    }
  }
  &.left2 {
    top: 4px;
    left: 0px;

    &.indicator-site {
      top: 5px;
    }
  }
  &.right1 {
    top: 48px;
    right: 0px;
    transform: translateY(-100%);

    &.indicator-site {
      top: 41px;
      right: 0px;
    }
  }
  &.right2 {
    top: 24px;
    transform: translateY(-50%);
    right: -6px;

    &.indicator-site {
      top: 12px;
      right: 0px;
    }
  }
  &.right3 {
    top: 0px;
    right: -1px;
  }
}

.action-icon {
  position: absolute;
  top: 0px;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  z-index: 1;
}