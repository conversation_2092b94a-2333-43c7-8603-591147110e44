import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import StatusIcon from './StatusIcon';
import IconFrame from './components/IconFrame';
import RippleIcon from './components/RippleIcon';
import { Tooltip } from 'antd';
import DefaultConfig from './configs/DefaultConfig.json';

jest.mock('./components/IconFrame', () => jest.fn(() => <div data-testid="icon-frame">IconFrame</div>));
jest.mock('./components/RippleIcon', () => jest.fn(() => <div data-testid="ripple-icon">RippleIcon</div>));
jest.mock('antd', () => ({
  Tooltip: jest.fn(({ title, children }) => {
    // Instead of trying to stringify the entire title object, just check if it exists
    return (
      <div data-testid="tooltip" data-has-tooltip={!!title}>
        {children}
      </div>
    );
  }),
}));

jest.mock('./configs/DefaultConfig.json', () => ({
  entityType: 'asset',
  iconType: 'normal',
  statusColor: '#008C3F',
  categoryIcon: 'https://static.datoms.io/images/icons/thing-category/default.svg',
  leftIcons: [],
  rightIcons: [],
  maxLeft: 2,
  maxRightAsset: 3,
  maxRightSite: 2
}), { virtual: true });

describe('StatusIcon Component', () => {
  const createConfig = (customConfig = {}) => {
    return {
      statusColor: '#008C3F',
      categoryIcon: 'https://static.datoms.io/images/icons/thing-category/default.svg',
      leftIcons: [],
      rightIcons: [],
      ...customConfig
    } as any; // Using 'any' to avoid TypeScript errors with optional properties
  };

  it('should render with default config', () => {
    // Empty config will use all default values from DefaultConfig.json
    const { container } = render(<StatusIcon config={createConfig()} />);

    // Check if the component renders with the correct classes
    expect(container.querySelector('.status-icon-container')).toBeInTheDocument();
    expect(container.querySelector('.entity-asset')).toBeInTheDocument();
    expect(container.querySelector('.icon-normal')).toBeInTheDocument();

    // Check if IconFrame is called with correct props
    expect(IconFrame).toHaveBeenCalledWith(
      expect.objectContaining({
        color: { outer: DefaultConfig.statusColor, inner: DefaultConfig.statusColor },
        entityType: DefaultConfig.entityType,
        iconType: DefaultConfig.iconType,
      }),
      expect.anything()
    );

    // Check if category icon is rendered
    const categoryIcon = container.querySelector('.category-icon-asset img');
    expect(categoryIcon).toBeInTheDocument();
    expect(categoryIcon).toHaveAttribute('src', DefaultConfig.categoryIcon);
  });

  it('should render with custom config', () => {
    const customConfig = {
      iconType: 'map' as const,
      statusColor: '#FF0000',
      entityType: 'site' as const,
      categoryIcon: 'custom-icon.svg',
    };

    const { container } = render(<StatusIcon config={customConfig} />);

    // Check if the component renders with the correct classes
    expect(container.querySelector('.entity-site')).toBeInTheDocument();
    expect(container.querySelector('.icon-map')).toBeInTheDocument();

    // Check if IconFrame is called with correct props
    expect(IconFrame).toHaveBeenCalledWith(
      expect.objectContaining({
        color: { outer: customConfig.statusColor, inner: customConfig.statusColor },
        entityType: customConfig.entityType,
        iconType: customConfig.iconType,
      }),
      expect.anything()
    );

    // Check if category icon is rendered with custom icon
    const categoryIcon = container.querySelector('.category-icon-site img');
    expect(categoryIcon).toBeInTheDocument();
    expect(categoryIcon).toHaveAttribute('src', customConfig.categoryIcon);
  });

  it('should use deviceStatusColor for outer color when provided', () => {
    const config = createConfig({
      statusColor: '#FF0000',
      deviceStatusColor: '#00FF00',
    });

    render(<StatusIcon config={config} />);

    // Check if IconFrame is called with correct colors
    expect(IconFrame).toHaveBeenCalledWith(
      expect.objectContaining({
        color: { outer: config.deviceStatusColor, inner: config.statusColor },
      }),
      expect.anything()
    );
  });

  it('should render left indicators correctly', () => {
    const config = createConfig({
      leftIcons: ['left1.svg', 'left2.svg', 'left3.svg'], // Third one should be ignored due to maxLeft=2
    });

    const { container } = render(<StatusIcon config={config} />);

    // Check if left indicators are rendered
    const leftIndicators = container.querySelector('.left-indicators');
    expect(leftIndicators).toBeInTheDocument();

    // Check if only the first two indicators are rendered
    const leftIndicatorImages = container.querySelectorAll('.left-indicators img');
    expect(leftIndicatorImages.length).toBe(2);
    expect(leftIndicatorImages[0]).toHaveAttribute('src', 'left1.svg');
    expect(leftIndicatorImages[0]).toHaveClass('left1');
    expect(leftIndicatorImages[1]).toHaveAttribute('src', 'left2.svg');
    expect(leftIndicatorImages[1]).toHaveClass('left2');
  });

  it('should render right indicators correctly for asset type', () => {
    const config = createConfig({
      entityType: 'asset' as const,
      rightIcons: ['right1.svg', 'right2.svg', 'right3.svg', 'right4.svg'], // Fourth one should be ignored due to maxRight=3 for assets
    });

    const { container } = render(<StatusIcon config={config} />);

    // Check if right indicators are rendered
    const rightIndicators = container.querySelector('.right-indicators');
    expect(rightIndicators).toBeInTheDocument();

    // Check if only the first three indicators are rendered
    const rightIndicatorImages = container.querySelectorAll('.right-indicators img');
    expect(rightIndicatorImages.length).toBe(3);
    expect(rightIndicatorImages[0]).toHaveAttribute('src', 'right1.svg');
    expect(rightIndicatorImages[0]).toHaveClass('right1');
    expect(rightIndicatorImages[1]).toHaveAttribute('src', 'right2.svg');
    expect(rightIndicatorImages[1]).toHaveClass('right2');
    expect(rightIndicatorImages[2]).toHaveAttribute('src', 'right3.svg');
    expect(rightIndicatorImages[2]).toHaveClass('right3');
  });

  it('should render right indicators correctly for site type', () => {
    const config = createConfig({
      entityType: 'site' as const,
      rightIcons: ['right1.svg', 'right2.svg', 'right3.svg'], // Third one should be ignored due to maxRight=2 for sites
    });

    const { container } = render(<StatusIcon config={config} />);

    // Check if right indicators are rendered
    const rightIndicators = container.querySelector('.right-indicators');
    expect(rightIndicators).toBeInTheDocument();

    // Check if only the first two indicators are rendered
    const rightIndicatorImages = container.querySelectorAll('.right-indicators img');
    expect(rightIndicatorImages.length).toBe(2);
    expect(rightIndicatorImages[0]).toHaveAttribute('src', 'right1.svg');
    expect(rightIndicatorImages[1]).toHaveAttribute('src', 'right2.svg');
  });

  it('should handle empty indicator arrays', () => {
    const config = createConfig({
      leftIcons: [],
      rightIcons: [],
    });

    const { container } = render(<StatusIcon config={config} />);

    // Check that no indicators are rendered
    expect(container.querySelector('.left-indicators')).not.toBeInTheDocument();
    expect(container.querySelector('.right-indicators')).not.toBeInTheDocument();
  });

  it('should handle null values in indicator arrays', () => {
    const config = createConfig({
      leftIcons: [null, 'left2.svg'],
      rightIcons: ['right1.svg', null],
    });

    const { container } = render(<StatusIcon config={config} />);

    // Check that only non-null indicators are rendered
    const leftIndicatorImages = container.querySelectorAll('.left-indicators img');
    expect(leftIndicatorImages.length).toBe(1);
    expect(leftIndicatorImages[0]).toHaveAttribute('src', 'left2.svg');

    const rightIndicatorImages = container.querySelectorAll('.right-indicators img');
    expect(rightIndicatorImages.length).toBe(1);
    expect(rightIndicatorImages[0]).toHaveAttribute('src', 'right1.svg');
  });

  it('should render action icon when provided', () => {
    const config = createConfig({
      actionIcon: 'action.svg',
    });

    const { container } = render(<StatusIcon config={config} />);

    // Check if action icon container is rendered
    const actionIcon = container.querySelector('.action-icon');
    expect(actionIcon).toBeInTheDocument();

    // Check if RippleIcon is called
    expect(RippleIcon).toHaveBeenCalledWith(
      expect.objectContaining({
        color: 'default',
        size: 11,
      }),
      expect.anything()
    );
  });

  it('should not render action icon when not provided', () => {
    const config = createConfig({
      actionIcon: '',
    });

    const { container } = render(<StatusIcon config={config} />);

    // Check that action icon is not rendered
    expect(container.querySelector('.action-icon')).not.toBeInTheDocument();
    expect(RippleIcon).not.toHaveBeenCalled();
  });

  it('should render tooltip when tooltipItems are provided', () => {
    const config = createConfig({
      tooltipItems: [
        { title: 'Status', value: 'Online' },
        { title: 'Battery', value: '80%' },
      ],
    });

    render(<StatusIcon config={config} />);

    // Check if Tooltip is called with correct props
    expect(Tooltip).toHaveBeenCalled();

    // Check if tooltip is rendered
    const tooltipElement = screen.getByTestId('tooltip');
    expect(tooltipElement).toHaveAttribute('data-has-tooltip', 'true');

    // Check if the container has the with-tooltip class
    expect(tooltipElement.querySelector('.with-tooltip')).toBeInTheDocument();
  });

  it('should not render tooltip when tooltipItems is empty', () => {
    const config = createConfig({
      tooltipItems: [],
    });

    const { container } = render(<StatusIcon config={config} />);

    // Check that Tooltip is not called
    expect(Tooltip).not.toHaveBeenCalled();

    // Check that the container doesn't have the with-tooltip class
    expect(container.querySelector('.with-tooltip')).not.toBeInTheDocument();
  });

  it('should handle non-array tooltipItems', () => {
    // @ts-ignore - Testing invalid prop type
    const config = createConfig({
      tooltipItems: 'not-an-array',
    });

    const { container } = render(<StatusIcon config={config} />);

    // Check that Tooltip is not called
    expect(Tooltip).not.toHaveBeenCalled();

    // Check that the container doesn't have the with-tooltip class
    expect(container.querySelector('.with-tooltip')).not.toBeInTheDocument();
  });
});
