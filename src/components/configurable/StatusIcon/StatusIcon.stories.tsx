import React from "react";
import { <PERSON>Fn, Meta } from "@storybook/react";
import StatusIcon from "./index";
import {
  Canvas,
  Source,
  Stories,
  Subtitle,
  Title,
  Controls,
} from "@storybook/blocks";
import { Table } from "@storybook/components";
import TripFaultIcon from "./assets/fault-trip.svg";
import MaintenanceDueIcon from "./assets/maintenance-due.svg";
import ViolationIcon from "./assets/violation-icon.svg";
import SystemErrorIcon from "./assets/system-error.svg";
import DeviceErrorIcon from "./assets/device-error.svg";

interface RowProps {
  key: string;
  required: string;
  allowedValues: string;
  description: string;
  defaultValue: string;
}

const CustomPropsTable = ({ rows }: { rows: RowProps[] }) => (
  <Table>
    <thead>
      <tr>
        <th>Key</th>
        <th>Required</th>
        <th>Allowed Values</th>
        <th>Default Value</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      {rows?.map((row: RowProps) => (
        <tr key={row.key}>
          <td>{row.key}</td>
          <td>{row.required}</td>
          <td>{row.allowedValues}</td>
          <td>{row.defaultValue}</td>
          <td>{row.description}</td>
        </tr>
      ))}
    </tbody>
  </Table>
);

export default {
  title: "Components/StatusIcon",
  component: StatusIcon,
  tags: ["autodocs"],
  argTypes: {
    config: {
      control: "object",
      description: "Configuration object for the status icon",
    },
  },
  parameters: {
    componentSubtitle: "A configurable status icon component for assets and sites.",
    docs: {
      page: () => (
        <>
          <Title>StatusIcon</Title>
          <p>
            The StatusIcon component displays a status indicator for assets or sites with customizable colors, icons, and tooltips.
          </p>
          <CustomPropsTable
            rows={[
              {
                key: "config.iconType",
                required: "N",
                allowedValues: "'map' | 'normal'",
                defaultValue: "normal",
                description: "The type of icon display. Map icons are used on maps, normal icons are used in lists and other UI elements.",
              },
              {
                key: "config.statusColor",
                required: "Y",
                allowedValues: "string (hex color)",
                defaultValue: "#008C3F",
                description: "The main color of the status icon, used for the inner part of the icon frame and the background of the category icon.",
              },
              {
                key: "config.deviceStatusColor",
                required: "N",
                allowedValues: "string (hex color)",
                defaultValue: "",
                description: "The color for the outer part of the icon frame. If not provided, statusColor is used.",
              },
              {
                key: "config.entityType",
                required: "N",
                allowedValues: "'site' | 'asset'",
                defaultValue: "asset",
                description: "The type of entity the icon represents. Different entity types have different frame shapes.",
              },
              {
                key: "config.categoryIcon",
                required: "Y",
                allowedValues: "string (URL)",
                defaultValue: "https://static.datoms.io/images/icons/thing-category/default.svg",
                description: "The URL of the icon to display in the center of the status icon.",
              },
              {
                key: "config.leftIcons",
                required: "N",
                allowedValues: "string[] (URLs)",
                defaultValue: "[]",
                description: "Array of icon URLs to display on the left side of the status icon. Maximum 2 icons.",
              },
              {
                key: "config.rightIcons",
                required: "N",
                allowedValues: "string[] (URLs)",
                defaultValue: "[]",
                description: "Array of icon URLs to display on the right side of the status icon. Maximum 2-3 icons depending on entityType.",
              },
              {
                key: "config.actionIcon",
                required: "N",
                allowedValues: "string (hex color)",
                defaultValue: "#E67E22",
                description: "Color for the ripple action icon. If provided, a ripple icon will be displayed.",
              },
              {
                key: "config.tooltipItems",
                required: "N",
                allowedValues: "{ title: string, value: string }[]",
                defaultValue: "[]",
                description: "Array of tooltip items to display when hovering over the status icon.",
              },
            ]}
          />
          <Subtitle />
          <Source of={AssetNormalIcon} />
          <Stories />
          <Subtitle>Interactive Examples</Subtitle>
          <Canvas of={AssetNormalIcon} />
          <Controls of={AssetNormalIcon} />
        </>
      ),
    },
  },
} as Meta;

// Example icon URLs
const categoryIcons = {
  default: "https://static.datoms.io/images/icons/thing-category/default.svg",
  battery: "https://static.datoms.io/images/icons/thing-category/battery-transparent.svg",
  solarPump: "https://static.datoms.io/images/icons/thing-category/solar-pump-transparent.svg",
};

// Example indicator icons (using publicly available icons for demonstration)
const indicatorIcons = {
  trip: TripFaultIcon,
  error: DeviceErrorIcon,
  maintenance: MaintenanceDueIcon,
  violation: ViolationIcon,
  systemError: SystemErrorIcon,
};

// Default configs for different entity types
const defaultAssetConfig = {
  iconType: "normal",
  statusColor: "#008C3F",
  deviceStatusColor: "#BAEBC7",
  entityType: "asset",
  categoryIcon: categoryIcons.solarPump,
  leftIcons: [],
  rightIcons: [],
  actionIcon: "",
  tooltipItems: [
    { title: "Status", value: "Active" },
    { title: "Asset Type", value: "Solar Pump" },
    { title: "Asset ID", value: "A-001" },
  ],
};

const defaultSiteConfig = {
  iconType: "normal",
  statusColor: "#964B00",
  deviceStatusColor: "#BAEBC7",
  entityType: "site",
  categoryIcon: categoryIcons.solarPump,
  leftIcons: [],
  rightIcons: [],
  actionIcon: "",
  tooltipItems: [
    { title: "Status", value: "Active" },
    { title: "Site Type", value: "Pump Station" },
    { title: "Site ID", value: "S-001" },
  ],
};

const Template: StoryFn<{
  config: {
    iconType?: "map" | "normal";
    statusColor: string;
    deviceStatusColor?: string;
    entityType?: "site" | "asset";
    categoryIcon: string;
    leftIcons?: string[];
    rightIcons?: string[];
    actionIcon?: string;
    tooltipItems?: { title: string; value: string }[];
  };
}> = (args) => (
  <div style={{ padding: "20px", display: "flex", justifyContent: "center" }}>
    <StatusIcon {...args} />
  </div>
);

export const AssetNormalIcon = Template.bind({});
AssetNormalIcon.args = {
  config: defaultAssetConfig,
};

export const AssetMapIcon = Template.bind({});
AssetMapIcon.args = {
  config: {
    ...defaultAssetConfig,
    iconType: "map",
  },
};

export const SiteNormalIcon = Template.bind({});
SiteNormalIcon.args = {
  config: defaultSiteConfig,
};

export const SiteMapIcon = Template.bind({});
SiteMapIcon.args = {
  config: {
    ...defaultSiteConfig,
    iconType: "map",
  },
};

export const WithIndicatorIcons = Template.bind({});
WithIndicatorIcons.args = {
  config: {
    ...defaultAssetConfig,
    categoryIcon: categoryIcons.battery,
    leftIcons: [indicatorIcons.systemError, indicatorIcons.error],
    rightIcons: [indicatorIcons.trip, indicatorIcons.maintenance, indicatorIcons.violation],
    tooltipItems: [
      { title: "Status", value: "Active" },
      { title: "Asset Type", value: "Battery" },
      { title: "Asset ID", value: "BAT-001" },
      { title: "Trips", value: "2" },
      { title: "Violations", value: "3" },
    ],
  },
};

export const WithActionIcon = Template.bind({});
WithActionIcon.args = {
  config: {
    ...defaultAssetConfig,
    categoryIcon: categoryIcons.battery,
    actionIcon: "#E67E22",
    tooltipItems: [
      { title: "Status", value: "Active" },
      { title: "Asset Type", value: "DG" },
      { title: "Asset ID", value: "DG-001" },
      { title: "Action", value: "Fuel Filling" },
    ],
  },
};

export const AssetWithSingleIndicator = Template.bind({});
AssetWithSingleIndicator.args = {
  config: {
    ...defaultAssetConfig,
    deviceStatusColor: "#D2D2D2",
    rightIcons: [indicatorIcons.violation],
  },
};

export const withoutDeviceAsset = Template.bind({});
withoutDeviceAsset.args = {
  config: {
    ...defaultAssetConfig,
    deviceStatusColor: "",
    tooltipItems: [
      { title: "Status", value: "Without Device" },
      { title: "Asset Type", value: "Default" },
      { title: "Asset ID", value: "DEF-001" },
    ],
  },
};

export const withoutDeviceSiteMapIcon = Template.bind({});
withoutDeviceSiteMapIcon.args = {
  config: {
    ...defaultSiteConfig,
    iconType: "map",
    deviceStatusColor: "",
    tooltipItems: [
      { title: "Status", value: "Without Device" },
      { title: "Site Type", value: "Factory" },
      { title: "Site ID", value: "FAC-001" },
    ],
  },
};

export const SiteIconWithoutTooltip = Template.bind({});
SiteIconWithoutTooltip.args = {
  config: {
    ...defaultSiteConfig,
    tooltipItems: [],
  },
};

// export const AlertSite = Template.bind({});
// AlertSite.args = {
//   config: {
//     ...defaultSiteConfig,
//     statusColor: "#F5A623",
//     deviceStatusColor: "#F8E71C",
//     leftIcons: [indicatorIcons.trip],
//     rightIcons: [indicatorIcons.alert],
//     actionIcon: "#E67E22",
//     tooltipItems: [
//       { title: "Status", value: "Alert" },
//       { title: "Site Type", value: "Factory" },
//       { title: "Site ID", value: "FAC-001" },
//       { title: "Alert Type", value: "Temperature High" },
//     ],
//   },
// };