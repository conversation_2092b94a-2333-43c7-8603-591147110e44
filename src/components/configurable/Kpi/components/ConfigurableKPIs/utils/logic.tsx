import deepmerge from "deepmerge";
import  DefaultConfig  from "../configs/DefaultConfig.json";


/**
 * Custom merge function that merges two objects or arrays, giving priority
 * to the source object. If both are arrays, the source array replaces the target.
 * If both are objects, it uses deepmerge to merge them.
 *
 * @param {Object} target - The target object to merge.
 * @param {Object} source - The source object to merge into the target.
 * @returns {Object} - The merged object.
 * @example
 * const target = { a: 1, b: 2 };
 * const source = { b: 3, c: 4 };
 * const result = customMerge(target, source);
 * console.log(result); // { a: 1, b: 3, c: 4 }
 */
const customMerge = (target: Object, source: Object): Object => {
  if (Array.isArray(target) && Array.isArray(source)) {
    return source;
  }

  if (typeof target === "object" && typeof source === "object") {
    return deepmerge(target, source, {
      arrayMerge: (destinationArray, sourceArray) => {
        return sourceArray;
      },
    });
  }

  return source;
};

/**
 * Merges a custom configuration with a default configuration and restores functions within the merged configuration.
 *
 * This function uses `deepmerge` to merge the `DefaultConfig` with the provided `customConfig`. It allows for customized
 * merging behavior, such as replacing arrays instead of concatenating them, and uses a custom function to handle
 * specific keys. After merging, the function restores any serialized or removed functions within the configuration.
 *
 * @param customConfig - An optional custom configuration object to merge with the default configuration.
 *   Defaults to an empty object.
 *
 * @returns The merged configuration object with functions restored.
 *
 * @example
 * // Default configuration
 * const DefaultConfig = {
 *   general: { theme: "light" },
 *   options: { maxLimit: 100 },
 *   actions: [() => console.log("default action")]
 * };
 *
 * // Custom configuration
 * const customConfig = {
 *   options: { maxLimit: 200 },
 *   actions: [() => console.log("custom action")]
 * };
 *
 * // Merged configuration
 * const mergedConfig = getMergedUpdatedConfig(customConfig);
 * console.log(mergedConfig);
 * // Output:
 * // {
 * //   general: { theme: "light" },
 * //   options: { maxLimit: 200 },
 * //   actions: [() => console.log("custom action")]
 * // }
 *
 * @param customConfig - The custom configuration to merge with the default configuration.
 * @returns The merged configuration with restored functions.
 */
export const getMergedUpdatedConfig = (customConfig: any = {}) => {
  let updatedConfig = deepmerge(DefaultConfig, customConfig, {
    arrayMerge: (destinationArray, sourceArray) => {
      return sourceArray;
    },
    customMerge: (key) => {
      return customMerge;
    },
  });
  return updatedConfig;
};


