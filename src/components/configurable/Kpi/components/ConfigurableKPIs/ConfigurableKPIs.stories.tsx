import React from "react";
import { <PERSON>Fn, Meta } from "@storybook/react";
import ConfigurableKPIs from "./index";
import {
  Canvas,
  Description,
  Source,
  Stories,
  Subtitle,
  Title,
  Controls,
} from "@storybook/blocks";
import { Table } from "@storybook/components";

interface RowProps {
  key: string;
  required: string;
  allowedValues: string;
  description: string;
  defaultValue: string;
}

const CustomPropsTable = ({ rows }: { rows: RowProps[] }) => (
  <Table>
    <thead>
      <tr>
        <th>Key</th>
        <th>Required</th>
        <th>Allowed Values</th>
        <th>Default Value</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      {rows?.map((row: RowProps) => (
        <tr key={row.key}>
          <td>{row.key}</td>
          <td>{row.required}</td>
          <td>{row.allowedValues}</td>
          <td>{row.defaultValue}</td>
          <td>{row.description}</td>
        </tr>
      ))}
    </tbody>
  </Table>
);

export default {
  title: "Components/ConfigurableKPIs",
  component: ConfigurableKPIs,
  tags: ["autodocs"],
  argTypes: {
    config: {
      control: "object",
      description:
        "The configuration json to render and control the properties of KPI Component.",
    },
    dataSource: {
      control: "object",
      description: "The data to be shown in the KPI component.",
    },
    loading: {
      control: "boolean",
      defaultValue: true,
      description: "Flag to show or hide the loading state.",
    },
  },
  parameters: {
    componentSubtitle: "A configurable KPI component.",
    docs: {
      page: () => (
        <>
          <Title> Configurable KPIs </Title>
          <Description />
          {/* <ArgTypes /> */}
          <CustomPropsTable
            rows={[
              {
                key: "configs",
                required: "Y",
                allowedValues: "Object",
                defaultValue: "-",
                description:
                  "The configuration json to render and control the properties of KPI Component.",
              },
              {
                key: "dataSource",
                required: "Y",
                allowedValues: "Array of Objects",
                defaultValue: "-",
                description: "Data to be displayed in the KPI component.",
              },
              {
                key: "loading",
                required: "Y",
                allowedValues: "Boolean",
                defaultValue: "-",
                description: "Flag to show or hide the loading state.",
              },
            ]}
          />
          <Subtitle />
          <Source of={ConfigurableKPIStory} />
          <Stories />
          <Subtitle>Real-Time Behaviour</Subtitle>
          <Canvas of={ConfigurableKPIStory} />
          <Controls of={ConfigurableKPIStory} />
        </>
      ),
    },
  },
} as Meta;

const Template: StoryFn<{
  config: Object;
  data: Object[];
  loading: boolean;
}> = (args: any) => <ConfigurableKPIs {...args} />;

export const LoadingState = Template.bind({});
LoadingState.args = {
  config: {
    title: "Assets",
    color: "blue",
    width: 6,
    state: "titleFirst",
    showInfo: true,
    size: "large",
  },
  loading: true,
  data: [
    {
      key: "assets",
      title: "Assets",
      value: 8,
      description: "Total number of assets",
    },
    {
      key: "running",
      title: "Running",
      value: 8,
      description: "Total number of running assets",
    },
    {
      key: "stopped",
      title: "Stopped",
      value: 8,
      description: "Total number of stopped assets",
    },
    {
      key: "notConnected",
      title: "Not Connected",
      value: 8,
      description: "Total number of not connected assets",
    },
  ],
};

export const ConfigurableKPIStory = Template.bind({});
ConfigurableKPIStory.args = {
  config: {
    title: "Assets",
    color: "blue",
    width: 6,
    state: "titleFirst",
    showInfo: true,
    size: "large",
  },
  loading: false,
  data: [
        {
          key: "assets",
          title: "Assets",
          value: 8,
          description: "Total number of assets",
        },
        {
          key: "running",
          title: "Running",
          value: 8,
          description: "Total number of running assets",
        },
        {
          key: "stopped",
          title: "Stopped",
          value: 8,
          description: "Total number of stopped assets",
        },
        {
          key: "notConnected",
          title: "Not Connected",
          value: 8,
          description: "Total number of not connected assets",
        },
      ]
};

export const NoDataKPI = Template.bind({});
NoDataKPI.args = {
  config: {
    title: "Assets",
    color: "blue",
    width: 6,
    state: "titleFirst",
    showInfo: true,
    size: "large",
  },
  loading: false,
  data: [
    {
      key: "assets",
      title: "Assets",
      description: "Total number of assets",
    },
    {
      key: "running",
      title: "Running",
      description: "Total number of running assets",
    },
    {
      key: "stopped",
      title: "Stopped",
      description: "Total number of stopped assets",
    },
    {
      key: "notConnected",
      title: "Not Connected",
      description: "Total number of not connected assets",
    },
  ],
};
