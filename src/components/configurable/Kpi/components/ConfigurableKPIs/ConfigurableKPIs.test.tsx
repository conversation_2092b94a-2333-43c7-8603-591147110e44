import React from "react";
import { render, screen } from "@testing-library/react";
import ConfigurableKPIs from "./ConfigurableKPIs";
import { InfoCircleOutlined } from "@ant-design/icons";

describe("ConfigurableKPIs Component", () => {
  const config = {
    size: "medium",
    key: "kpi-demo",
    title: "KPIs",
    color: "blue",
    width: 4,
    state: "titleFirst",
    showInfo: true,
  };

  const data = [
    {
      key: "assets",
      title: "Assets",
      value: 8,
      description: "Total number of assets",
    },
    {
      key: "running",
      title: "Running",
      value: 4,
      description: "Number of running assets",
    },
    {
      key: "stopped",
      title: "Stopped",
      value: 2,
      description: "Number of stopped assets",
    },
    {
      key: "notConnected",
      title: "Not Connected",
      value: 3,
      description: "Number of not connected assets",
    },
  ];

  test("renders the component without crashing", () => {
    render(<ConfigurableKPIs config={config} data={data} loading={false} />);
    expect(screen.getByText("Assets")).toBeInTheDocument();
    expect(screen.getByText("Running")).toBeInTheDocument();
    expect(screen.getByText("Stopped")).toBeInTheDocument();
    expect(screen.getByText("Not Connected")).toBeInTheDocument();
  });

  test("displays the correct KPI values", () => {
    render(<ConfigurableKPIs config={config} data={data} loading={false} />);
    expect(screen.getByText("8")).toBeInTheDocument();
    expect(screen.getByText("4")).toBeInTheDocument();
    expect(screen.getByText("2")).toBeInTheDocument();
    expect(screen.getByText("3")).toBeInTheDocument();
  });

  test("displays a spinner when loading is true", () => {
    render(<ConfigurableKPIs config={config} data={data} loading={true} />);
    expect(screen.getAllByRole("status")).toHaveLength(data.length); // Ensures a spinner for each KPI
  });

  test("renders the tooltip icon when showInfo is true", () => {
    render(<ConfigurableKPIs config={config} data={data} loading={false} />);
    const tooltipIcons = screen.getAllByTestId("info-icon");
    expect(tooltipIcons).toHaveLength(data.length);
  });

  test("does not render the tooltip icon when showInfo is false", () => {
    const modifiedConfig = { ...config, showInfo: false };
    render(<ConfigurableKPIs config={modifiedConfig} data={data} loading={false} />);
    expect(screen.queryByTestId("info-icon")).not.toBeInTheDocument();
  });

  test("renders the correct title-value order for titleFirst state", () => {
    const { container } = render(
      <ConfigurableKPIs config={{ ...config, state: "titleFirst" }} data={data} loading={false} />
    );
    const kpiItems = container.querySelectorAll(".kpi");
    kpiItems.forEach((kpi) => {
      const title = kpi.querySelector(".kpi-title");
      const value = kpi.querySelector(".kpi-value");
      expect(title).toBeTruthy();
      expect(value).toBeTruthy();
      expect(title?.nextSibling).toBe(value); // Title should come before value
    });
  });

  test("renders the correct title-value order for valueFirst state", () => {
    const { container } = render(
      <ConfigurableKPIs config={{ ...config, state: "valueFirst" }} data={data} loading={false} />
    );
    const kpiItems = container.querySelectorAll(".kpi");
    kpiItems.forEach((kpi) => {
      const title = kpi.querySelector(".kpi-title");
      const value = kpi.querySelector(".kpi-value");
      expect(title).toBeTruthy();
      expect(value).toBeTruthy();
      expect(value?.nextSibling).toBe(title); // Value should come before title
    });
  });

  test("renders the correct width for the component", () => {
    const { container } = render(<ConfigurableKPIs config={config} data={data} loading={false} />);
    const kpis = container.querySelector("#configurable_kpis");
    expect(kpis).toHaveStyle(`width: ${(100 / 24) * (config.width || 3) * data.length}%`);
  });

  test("renders the correct grid spans", () => {
    const { container } = render(<ConfigurableKPIs config={config} data={data} loading={false} />);
    const cols = container.querySelectorAll(".ant-col");
    cols.forEach((col) => {
      expect(col).toHaveClass(`ant-col-${24 / data.length}`); // Each column should have the correct span
    });
  });
});
