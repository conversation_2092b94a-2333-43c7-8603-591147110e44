# ConfigurableKPIs Component

The `ConfigurableKPIs` component is designed to display a set of Key Performance Indicators (KPIs) in a dynamic, responsive grid layout. This component uses Ant Design's `Row` and `Col` components for grid-based layout and provides various customization options, including title-value order, information tooltips, and dynamic width calculation.

---

## Overview

The `ConfigurableKPIs` component accepts two main inputs:
1. **`config`**: Controls the appearance and behavior of the KPIs.
2. **`data`**: Provides the actual KPI data to be displayed.

Additionally, a `loading` state can be passed to show a spinner while data is being fetched or processed.

---

## Props

### `config`
- **Type**: `KPIConfig` (optional)
- **Description**: Defines the layout and style of the KPI grid.
- **Fields**:
  - `size`: Determines the size of the KPIs (`small`, `medium`, `large`).
  - `key`: A unique key to identify the KPI configuration.
  - `title`: The title of the KPI section.
  - `color`: The color scheme of the KPIs (`blue` or `white`).
  - `width`: The width of each KPI (calculated as a fraction of the total grid width).
  - `state`: The order of title and value (`titleFirst` or `valueFirst`).
  - `showInfo`: Whether to show an information tooltip for each KPI.

---

### `data`
- **Type**: `Data[]`
- **Description**: Array of KPI data to be displayed.
- **Fields**:
  - `key`: Unique key for the KPI item.
  - `title`: The title of the KPI (e.g., "Assets").
  - `value`: The value of the KPI (e.g., `8`).
  - `description`: A description of the KPI for tooltips.

---

### `loading`
- **Type**: `boolean`
- **Description**: Controls whether a spinner should be shown in place of KPI values.

---

## Key Features

1. **Dynamic Layout**:
   - Uses Ant Design's grid system to dynamically arrange KPI elements.
   - Width of the container is calculated based on `config.width` and the number of KPIs.

2. **Customizable KPI Order**:
   - Supports dynamic ordering of title and value based on `config.state`:
     - `titleFirst`: Title is displayed above the value.
     - `valueFirst`: Value is displayed above the title.

3. **Loading State**:
   - Displays a spinner in place of KPI values when `loading` is `true`.

4. **Tooltips**:
   - Shows additional information for each KPI using Ant Design's `Tooltip` component if `config.showInfo` is `true`.

---

## Code Example

### Usage Example

```tsx
import React from "react";
import ConfigurableKPIs from "./ConfigurableKPIs";

const data = [
  {
    key: "assets",
    title: "Assets",
    value: 8,
    description: "Total number of assets",
  },
  {
    key: "running",
    title: "Running",
    value: 4,
    description: "Number of running assets",
  },
  {
    key: "stopped",
    title: "Stopped",
    value: 2,
    description: "Number of stopped assets",
  },
  {
    key: "notConnected",
    title: "Not Connected",
    value: 3,
    description: "Number of not connected assets",
  },
];

const config = {
  size: "medium",
  key: "kpi-demo",
  title: "KPIs",
  color: "blue",
  width: 4,
  state: "titleFirst",
  showInfo: true,
};

const App = () => {
  return <ConfigurableKPIs config={config} data={data} loading={false} />;
};

export default App;
```

---

## Dynamic Width Calculation

The width of the entire KPI container is dynamically calculated using the following formula:
```tsx
style={{ width: `${(100 / 24) * (config?.width || 3) * data?.length}%` }}
```

- **`config.width`**: Specifies the grid span for each KPI (default is 3 if not provided).
- **`data.length`**: Determines the number of KPIs.

This ensures that the grid layout remains proportional and responsive.

---

## Component Logic

1. **Grid Layout**:
   - Ant Design's `Row` and `Col` components are used to create a responsive grid layout.
   - The width of each column is determined dynamically based on the number of KPIs (`data.length`).

2. **Dynamic Order**:
   - The order of the title and value in each KPI block is controlled by the `config.state` property:
     - If `titleFirst`, the title appears above the value.
     - Otherwise, the value appears above the title.

3. **Loading State**:
   - If `loading` is `true`, a spinner is displayed instead of the KPI values.

4. **Tooltips**:
   - Tooltips are conditionally rendered based on the `config.showInfo` property, providing additional information about each KPI.

---

## Styling

### Styles (styles.less)

```less
#configurable_kpis {
  padding: 16px;
  background-color: #fcfcfc;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

  &.kpi-blue {
    background-color: #f5f5f5;
    color: #374375;
  }

  &.kpi-white {
    background-color: #ffffff;
    color: #000000;
    border: 1px solid #d9d9d9;
  }

  .kpi {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    padding: 16px;
    transition: all 0.3s ease;

    &.no-hover:hover {
      transform: none;
      box-shadow: none;
    }

    .kpi-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 8px;
    }

    .kpi-value {
      font-size: 20px;
      font-weight: bold;

      &.kpi-value-marginLeft {
        margin-left: 8px;
      }

      &.kpi-value-marginRight {
        margin-right: 8px;
      }
    }

    .info-icon {
      margin-left: 8px;
      cursor: pointer;
    }
  }
}
```

---

## Dependencies

- **React**
- **Ant Design**
- **LESS for styles**

---


