import React, { useState } from "react";
import { Spin, Tooltip, Divider } from "antd";
import "./styles.less";
import { InfoCircleOutlined } from "@ant-design/icons";
import { getMergedUpdatedConfig } from "./utils/logic";
import { KPIConfig, Data, ConfigurableKPIsProps } from "../../types";

/**
 * @fileoverview ConfigurableKPIs component for displaying a set of Key Performance Indicators.
 * @module components/configurable/Kpi/components/ConfigurableKPIs
 */

/**
 * ConfigurableKPIs component renders a set of Key Performance Indicators (KPIs) with configurable appearance and behavior.
 *
 * This component displays a horizontal row of KPI items, each showing a title and value. The appearance and behavior
 * of the KPIs can be customized through the config prop. Features include:
 * - Configurable size (small, medium, large)
 * - Configurable display order (title first or value first)
 * - Optional information tooltips
 * - Clickable KPIs with selection state
 * - Loading state with spinners
 * - Dividers between KPI items
 * - Custom colors and styling
 *
 * @component
 * @example
 * // Basic usage
 * const data = [
 *   { key: 'assets', title: 'Assets', value: 100, description: 'Total number of assets', divider: true },
 *   { key: 'running', title: 'Running', value: 83, description: 'Number of running assets' }
 * ];
 *
 * const config = {
 *   size: 'medium',
 *   state: 'titleFirst',
 *   showInfo: true,
 *   color: 'blue',
 *   itemsClickable: true
 * };
 *
 * <ConfigurableKPIs
 *   data={data}
 *   config={config}
 *   loading={false}
 *   onKPIClick={(key) => console.log('KPI clicked:', key)}
 * />
 *
 * @example
 * // With loading state
 * <ConfigurableKPIs
 *   data={data}
 *   config={config}
 *   loading={true}
 * />
 *
 * @example
 * // With value displayed before title
 * const valueFirstConfig = {
 *   ...config,
 *   state: 'valueFirst'
 * };
 *
 * <ConfigurableKPIs
 *   data={data}
 *   config={valueFirstConfig}
 *   loading={false}
 * />
 */
const ConfigurableKPIs: React.FC<ConfigurableKPIsProps> = (
  props: ConfigurableKPIsProps,
) => {
  /**
   * State to track which KPI item is currently selected
   */
  const [selectedKey, setSelectedKey] = useState<string | null>(null);

  let { data, config, loading } = props;

  /**
   * Merge the provided config with default configuration
   */
  config = getMergedUpdatedConfig(config) as KPIConfig;

  /**
   * Handles click events on KPI items
   *
   * If the item is clickable, toggles its selection state and calls the onKPIClick callback.
   * Clicking an already selected item will deselect it.
   *
   * @param {Data} item - The KPI item that was clicked
   * @param {boolean | undefined} isClickable - Whether the item is clickable
   */
  const handleKPIClick = (item: Data, isClickable: boolean | undefined) => {
    if (!isClickable) return;
    const finalSelectedKey = props.selectedKpiKey ?? selectedKey;
    let kpiKey = finalSelectedKey === item.key ? null : item.key; // double click to deselect
    setSelectedKey(kpiKey);
    props.onKPIClick?.(kpiKey, item);
  };

  /**
   * Renders a single KPI item
   *
   * @param {Data} item - The KPI data to render
   * @param {number} index - The index of the item in the data array
   * @returns {React.ReactNode} The rendered KPI item
   */
  console.log("props.selectedKpiKey", props.selectedKpiKey, selectedKey);
  const getKPI = (item: Data, index: number): React.ReactNode => {
    // Determine display and behavior based on configuration
    const isValueFirst = config?.state === "valueFirst";
    const isClickable = config?.itemsClickable && item.clickable !== false;
    const isDivider = item.divider && index < data.length - 1;
    const finalSelectedKey = props.selectedKpiKey ?? selectedKey;
    return (
      <div key={index} className="kpi-item">
        <div
          className={`kpi
            kpi-size-${config?.size}
            ${isClickable ? "clickable-kpi" : ""}
            ${finalSelectedKey === item.key ? "active-kpi" : ""}`}
          onClick={() => handleKPIClick(item, isClickable)}
          style={{ "--kpi-color": config?.color } as React.CSSProperties}
        >
          {/* Render title before value if not in valueFirst mode */}
          {!isValueFirst && (
            <div className={`kpi-title size-${config.size}`}>{item?.title}</div>
          )}

          {/* Render value or loading spinner */}
          <div className={`kpi-value size-${config.size}`}>
            {loading ? <Spin size="small" /> : item?.value || "-"}
          </div>

          {/* Render title after value if in valueFirst mode */}
          {isValueFirst && (
            <div className={`kpi-title size-${config.size}`}>{item?.title}</div>
          )}

          {/* Render info icon with tooltip if showInfo is enabled */}
          {config?.showInfo && item?.description && (
            <Tooltip title={item?.description}>
              <InfoCircleOutlined
                className="info-icon"
                data-testid="info-icon"
              />
            </Tooltip>
          )}
        </div>

        {/* Render divider if specified and not the last item */}
        {isDivider && (
          <Divider
            type="vertical"
            style={{ borderColor: config.color }}
            className={`divider-size-${config.size}`}
          />
        )}
      </div>
    );
  };

  return (
    <div
      id="configurable_kpis"
      className="configurable_kpis"
      style={{
        backgroundColor: config?.backgroundColor,
        boxShadow: config?.isBoxShadow
          ? "0 1px 4px rgba(0, 0, 0, 0.1)"
          : "none",
      }}
    >
      {Array.isArray(data) && data?.map((item, index) => getKPI(item, index))}
    </div>
  );
};

export default ConfigurableKPIs;
