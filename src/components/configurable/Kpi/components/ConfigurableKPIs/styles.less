.size-check(small) {
  @font-size-title: 12px;
  @font-size-value: 12px;
  @font-size-divider: 10px;
  @spacing-title-value: 4px;
  @spacing-kpi-items: 12px;
  @height-kpi: 24px;
}

.size-check(medium) {
  @font-size-title: 14px;
  @font-size-value: 18px;
  @font-size-divider: 14px;
  @spacing-title-value: 8px;
  @spacing-kpi-items: 16px;
  @height-kpi: 36px;
}

.size-check(large) {
  @font-size-title: 16px;
  @font-size-value: 22px;
  @font-size-divider: 14px;
  @spacing-title-value: 10px;
  @spacing-kpi-items: 24px;
  @height-kpi: 40px;
}

.size-variant(@size) {
  .size-check(@size);

  margin: 0 @spacing-kpi-items / 2;
  height: @height-kpi;

  .kpi-title {
    font-size: @font-size-title;
  }

  .kpi-value {
    font-size: @font-size-value;
  }

  div:first-of-type {
    margin-right: @spacing-title-value;
  }

  .info-icon {
    font-size: @font-size-title;
  }

  + .ant-divider {
    font-size: @font-size-divider;
    margin-inline: @spacing-kpi-items / 2;
  }
}

.configurable_kpis {
  padding: 0px 18px;
  margin-right: 16px;
  width: fit-content;
  display: flex;

  // Default styles for the KPI block
  background-color: #fcfcfc;
  border-radius: 30px;

  &.kpi-blue {
    background-color: #cee2ff; // Blue background
    color: #374375; // Blue text
  }

  &.kpi-white {
    background-color: #fcfcfc; // White background
    color: #374375; // Black text
  }

  .kpi-item {
    display: flex;
    align-items: center;
  }

  .kpi {
    display: flex;
    border-radius: 30px;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: var(--kpi-color);
    position: relative;

    &.kpi-size-small {
      .size-variant(small);
    }
    &.kpi-size-medium {
      .size-variant(medium);
    }
    &.kpi-size-large {
      .size-variant(large);
    }

    &.clickable-kpi {
      cursor: pointer;
      &:hover {
        color: color-mix(in srgb, var(--kpi-color) 80%, white);
      }
    }

    &.active-kpi {
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        height: 4px;
        width: 100%;
        background: orange;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
      }
    }

    .kpi-value {
      font-weight: 600;
    }

    .info-icon {
      margin-left: 8px;
      cursor: pointer;
      color: #BBC6D9;
    }

  }

  .ant-divider {
    margin-top: 2px;
  }
}
