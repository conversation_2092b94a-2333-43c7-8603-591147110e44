import React from "react";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import K<PERSON> from "./Kpi";
import { KpiInstanceConfig, KPIConfig } from "./types";

// Mock the ConfigurableKPIs component to verify props passing
jest.mock("./components/ConfigurableKPIs", () => {
  return jest.fn(({ data, loading, config, onKPIClick }) => (
    <div data-testid="configurable-kpis-mock">
      <div data-testid="data-prop">{JSON.stringify(data)}</div>
      <div data-testid="loading-prop">{JSON.stringify(loading)}</div>
      <div data-testid="config-prop">{JSON.stringify(config)}</div>
      <button
        data-testid="click-handler"
        onClick={() => onKPIClick && onKPIClick("test-key")}
      >
        <PERSON><PERSON> Handler
      </button>
    </div>
  ));
});

describe("Kpi Component", () => {
  // Sample test data
  const mockConfig: KPIConfig = {
    size: "medium",
    state: "titleFirst",
    showInfo: true,
    color: "blue",
    itemsClickable: true
  };

  const mockKpiData: KpiInstanceConfig[] = [
    {
      id: "assets-kpi",
      loading: false,
      data: [
        {
          key: "assets",
          title: "Assets",
          value: 100,
          description: "Total number of assets",
          divider: true
        },
        {
          key: "running_assets",
          title: "Running",
          value: 83,
          description: "Total number of running assets"
        }
      ]
    },
    {
      id: "performance-kpi",
      loading: true,
      data: [
        {
          key: "performance",
          title: "Performance",
          value: 87,
          description: "Average performance score",
          divider: true
        },
        {
          key: "uptime",
          title: "Uptime",
          value: "95%",
          description: "Uptime percentage"
        }
      ]
    }
  ];

  const mockOnKPIClick = jest.fn();

  afterEach(() => {
    jest.clearAllMocks();
  });

  test("renders without crashing", () => {
    render(<Kpi kpiData={mockKpiData} config={mockConfig} />);
    expect(screen.getAllByTestId("configurable-kpis-mock")).toHaveLength(2);
  });

  test("renders the correct number of ConfigurableKPIs components based on kpiData", () => {
    render(<Kpi kpiData={mockKpiData} config={mockConfig} />);
    expect(screen.getAllByTestId("configurable-kpis-mock")).toHaveLength(mockKpiData.length);
  });

  test("passes the correct props to each ConfigurableKPIs component", () => {
    render(<Kpi kpiData={mockKpiData} config={mockConfig} />);

    const dataPropElements = screen.getAllByTestId("data-prop");
    const loadingPropElements = screen.getAllByTestId("loading-prop");
    const configPropElements = screen.getAllByTestId("config-prop");

    // Check first KPI instance
    expect(JSON.parse(dataPropElements[0].textContent || "")).toEqual(mockKpiData[0].data);
    expect(JSON.parse(loadingPropElements[0].textContent || "")).toBe(mockKpiData[0].loading);
    expect(JSON.parse(configPropElements[0].textContent || "")).toEqual(mockConfig);

    // Check second KPI instance
    expect(JSON.parse(dataPropElements[1].textContent || "")).toEqual(mockKpiData[1].data);
    expect(JSON.parse(loadingPropElements[1].textContent || "")).toBe(mockKpiData[1].loading);
    expect(JSON.parse(configPropElements[1].textContent || "")).toEqual(mockConfig);
  });

  test("calls onKPIClick when a KPI is clicked", async () => {
    render(<Kpi kpiData={mockKpiData} config={mockConfig} onKPIClick={mockOnKPIClick} />);

    const clickHandlers = screen.getAllByTestId("click-handler");
    await userEvent.click(clickHandlers[0]);

    expect(mockOnKPIClick).toHaveBeenCalledTimes(1);
    expect(mockOnKPIClick).toHaveBeenCalledWith("test-key");
  });

  test("handles empty kpiData array", () => {
    render(<Kpi kpiData={[]} config={mockConfig} />);
    expect(screen.queryByTestId("configurable-kpis-mock")).not.toBeInTheDocument();
  });

  test("renders with minimal props", () => {
    render(<Kpi kpiData={mockKpiData} />);
    expect(screen.getAllByTestId("configurable-kpis-mock")).toHaveLength(2);
  });
});