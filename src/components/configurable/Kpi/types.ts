/**
 * @fileoverview Type definitions for the Kpi component and related interfaces.
 * @module components/configurable/Kpi/types
 */

/**
 * Configuration options for KPI components.
 *
 * @typedef {Object} KPIConfig
 * @property {string} [key] - Optional unique identifier for the KPI configuration.
 * @property {'small' | 'medium' | 'large'} [size='medium'] - Size of the KPI component.
 * @property {'valueFirst' | 'titleFirst'} [state='titleFirst'] - Display order of title and value.
 * @property {boolean} [showInfo=false] - Whether to show information icon with tooltip.
 * @property {string} [color] - Primary color for the KPI component.
 * @property {string} [backgroundColor] - Background color for the KPI component.
 * @property {boolean} [itemsClickable=false] - Whether KPI items can be clicked.
 */
export type KPIConfig = {
  key?: string;
  size?: 'small' | 'medium' | 'large';
  state?: 'valueFirst' | 'titleFirst';
  showInfo?: boolean;
  color?: string;
  backgroundColor?: string;
  itemsClickable?: boolean;
  isBoxShadow?: boolean;
};

/**
 * Data structure for a single KPI item.
 *
 * @typedef {Object} Data
 * @property {string} [description] - Optional description shown in tooltip when hovering over info icon.
 * @property {string} key - Unique identifier for the KPI item.
 * @property {string} title - Display title for the KPI.
 * @property {number | string} value - Value to display for the KPI.
 * @property {boolean} [divider=false] - Whether to show a divider after this KPI item.
 * @property {boolean} [clickable] - Whether this specific KPI item is clickable (overrides config.itemsClickable).
 */
export type Data = {
  description?: string;
  key: string;
  title: string;
  value: number | string;
  divider?: boolean;
  clickable?: boolean;
};

/**
 * Configuration for a single KPI instance.
 *
 * @interface KpiInstanceConfig
 * @property {string} id - Unique identifier for the KPI instance.
 * @property {Data[]} data - Array of KPI data items to display.
 * @property {boolean} loading - Whether the KPI instance is in loading state.
 * @property {KPIConfig} [config] - Optional configuration that will be merged with the global config.
 */
export interface KpiInstanceConfig {
  id?: string;
  data: Data[];
  loading: boolean;
  config?: KPIConfig;
  selectedKpiKey?: string;
}

/**
 * Props for the Kpi component.
 *
 * @interface KpiProps
 * @property {KpiInstanceConfig[]} kpiData - Array of KPI instances to display.
 * @property {KPIConfig} [config] - Global configuration applied to all KPI instances.
 * @property {(key: string | null) => void} [onKPIClick] - Callback function when a KPI item is clicked.
 *                                                         Receives the key of the clicked item, or null if deselected.
 */
export interface KpiProps {
  kpiData: KpiInstanceConfig[];
  config?: KPIConfig;
  onKPIClick?: (key: string | null) => void;
  kpiRef?: any;
}

/**
 * Props for the ConfigurableKPIs component.
 * This component is used internally by the Kpi component.
 *
 * @typedef {Object} ConfigurableKPIsProps
 * @property {KPIConfig} [config] - Configuration for the KPI display.
 * @property {Data[]} data - Array of KPI data items to display.
 * @property {(key: string | null) => void} [onKPIClick] - Callback function when a KPI item is clicked.
 * @property {boolean} loading - Whether the KPI is in loading state.
 */
export type ConfigurableKPIsProps = {
  ref?: any;
  config?: KPIConfig;
  data: Data[];
  onKPIClick?: (key: string | null, payload?: any) => void;
  loading: boolean;
  selectedKpiKey?: string;
};
