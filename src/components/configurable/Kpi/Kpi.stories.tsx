import React from "react";
import { <PERSON>Fn, Meta } from "@storybook/react";
import <PERSON><PERSON> from "./Kpi";
import {
  Canvas,
  Description,
  Source,
  Stories,
  Subtitle,
  Title,
  Controls,
} from "@storybook/blocks";
import { Table } from "@storybook/components";
import { KPIConfig, KpiInstanceConfig, Data } from "./types";

interface RowProps {
  key: string;
  required: string;
  allowedValues: string;
  description: string;
  defaultValue: string;
}

const CustomPropsTable = ({ rows }: { rows: RowProps[] }) => (
  <Table>
    <thead>
      <tr>
        <th>Key</th>
        <th>Required</th>
        <th>Allowed Values</th>
        <th>Default Value</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      {rows?.map((row: RowProps) => (
        <tr key={row.key}>
          <td>{row.key}</td>
          <td>{row.required}</td>
          <td>{row.allowedValues}</td>
          <td>{row.defaultValue}</td>
          <td>{row.description}</td>
        </tr>
      ))}
    </tbody>
  </Table>
);

// Common data sets
const assetsData: Data[] = [
  {
    key: "assets",
    title: "Assets",
    value: 100,
    description: "Total number of assets",
    divider: true
  },
  {
    key: "running_assets",
    title: "Running",
    value: 83,
    description: "Total number of running assets"
  },
  {
    key: "stopped_assets",
    title: "Stopped",
    value: 14,
    description: "Total number of stopped assets"
  },
  {
    key: "disconnected_assets",
    title: "Not Connected",
    value: 2,
    description: "Total number of disconnected assets"
  }
];

const performanceData: Data[] = [
  {
    key: "performance",
    title: "Performance",
    value: 87,
    description: "Average performance score",
    divider: true
  },
  {
    key: "uptime",
    title: "Uptime",
    value: "95%",
    description: "Uptime percentage"
  },
  {
    key: "efficiency",
    title: "Efficiency",
    value: "82%",
    description: "Efficiency rating"
  }
];

// Common KPI instances
const assetsKpi: KpiInstanceConfig = {
  id: "assets-kpi",
  loading: false,
  data: assetsData
};

const performanceKpi: KpiInstanceConfig = {
  id: "performance-kpi",
  loading: false,
  data: performanceData
};

const loadingPerformanceKpi: KpiInstanceConfig = {
  ...performanceKpi,
  loading: true
};

// Common configs
const defaultConfig: KPIConfig = {
  size: "medium",
  state: "titleFirst",
  showInfo: true,
  color: "blue",
  itemsClickable: true
};

const valueFirstConfig: KPIConfig = {
  ...defaultConfig,
  state: "valueFirst"
};

export default {
  title: "Components/Kpi",
  component: Kpi,
  tags: ["autodocs"],
  argTypes: {
    kpiData: {
      control: "object",
      description: "Array of KPI instances to be displayed",
    },
    config: {
      control: "object",
      description:
        "The configuration json to render and control the properties of KPI Component.",
    },
    onKPIClick: {
      action: "clicked",
      description: "Callback function when a KPI is clicked",
    },
  },
  parameters: {
    componentSubtitle: "A wrapper component for multiple KPI instances",
    docs: {
      page: () => (
        <>
          <Title>Kpi Component</Title>
          <Description />
          <div>
            <p>The Kpi component is a wrapper that can display multiple KPI instances.</p>
            <p>Each instance is rendered using the ConfigurableKPIs component.</p>
          </div>
          <CustomPropsTable
            rows={[
              {
                key: "kpiData",
                required: "Y",
                allowedValues: "Array of KpiInstanceConfig objects",
                defaultValue: "-",
                description: "Array of KPI instances to be displayed",
              },
              {
                key: "config",
                required: "N",
                allowedValues: "KPIConfig object",
                defaultValue: "-",
                description:
                  "The configuration json to render and control the properties of KPI Component.",
              },
              {
                key: "onKPIClick",
                required: "N",
                allowedValues: "Function",
                defaultValue: "-",
                description: "Callback function when a KPI is clicked",
              },
            ]}
          />
          <Subtitle />
          <Source of={MultipleKpiInstances} />
          <Stories />
          <Subtitle>Real-Time Behaviour</Subtitle>
          <Canvas of={MultipleKpiInstances} />
          <Controls of={MultipleKpiInstances} />
        </>
      ),
    },
  },
} as Meta;

const Template: StoryFn<{
  kpiData: KpiInstanceConfig[];
  config?: KPIConfig;
  onKPIClick?: (key: string | null) => void;
}> = (args) => 
<div style={{padding: '20px'}}>
  <Kpi {...args} />
</div>;

// Create additional KPI instances for different sizes
const smallKpi: KpiInstanceConfig = {
  id: "small-kpi",
  loading: false,
  data: [
    {
      key: "assets-small",
      title: "Assets",
      value: 100,
      description: "Total number of assets",
      divider: true
    },
    {
      key: "disconnected-small",
      title: "Disconnected",
      value: 83,
      description: "Total number of disconnected assets"
    }
  ],
  config: {
    size: "small"
  }
};

const mediumKpi: KpiInstanceConfig = {
  id: "medium-kpi",
  loading: false,
  data: [
    {
      key: "assets-medium",
      title: "Assets",
      value: 100,
      description: "Total number of assets",
      divider: true
    },
    {
      key: "disconnected-medium",
      title: "Disconnected",
      value: 83,
      description: "Total number of disconnected assets"
    }
  ],
  config: {
    size: "medium"
  }
};

const largeKpi: KpiInstanceConfig = {
  id: "large-kpi",
  loading: false,
  data: [
    {
      key: "assets-large",
      title: "Assets",
      value: 100,
      description: "Total number of assets",
      divider: true
    },
    {
      key: "disconnected-large",
      title: "Disconnected",
      value: 83,
      description: "Total number of disconnected assets"
    }
  ], 
  config: {
    size: "large"
  }
};

// Create a KPI instance with partial data
const partialAssetsKpi: KpiInstanceConfig = {
  id: "assets-kpi",
  loading: false,
  data: assetsData.slice(0, 2) // Using only first two items
};

const partialAssetsKpiForValueFirst: KpiInstanceConfig = {
  id: "assets-kpi",
  loading: false,
  data: assetsData.slice(0, 3) // Using only first three items
};

// Story definitions
export const SingleKpiInstance = Template.bind({});
SingleKpiInstance.args = {
  kpiData: [assetsKpi],
  config: defaultConfig
};

export const MultipleKpiInstances = Template.bind({});
MultipleKpiInstances.args = {
  kpiData: [assetsKpi, performanceKpi],
  config: defaultConfig
};

export const WithLoadingState = Template.bind({});
WithLoadingState.args = {
  kpiData: [partialAssetsKpi, loadingPerformanceKpi],
  config: defaultConfig
};

export const DifferentSizes = Template.bind({});
DifferentSizes.args = {
  kpiData: [smallKpi, mediumKpi, largeKpi],
  config: defaultConfig
};

export const ValueFirstState = Template.bind({});
ValueFirstState.args = {
  kpiData: [partialAssetsKpiForValueFirst],
  config: valueFirstConfig
};
