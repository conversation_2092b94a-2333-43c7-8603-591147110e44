import React from 'react'
import ConfigurableKPIs from './components/ConfigurableKPIs';
import './style.less';
import { KpiInstanceConfig, KpiProps } from './types';

/**
 * @fileoverview Kpi component that renders multiple KPI instances.
 * @module components/configurable/Kpi
 */

/**
 * Kpi component - A wrapper component that renders multiple KPI (Key Performance Indicator) instances.
 *
 * The Kpi component takes an array of KPI instances and renders each one using the ConfigurableKPIs component.
 * It allows for displaying multiple groups of KPIs with different data and loading states, while sharing common
 * configuration options.
 *
 * @component
 * @example
 * // Basic usage with a single KPI instance
 * const kpiData = [{
 *   id: 'assets-kpi',
 *   loading: false,
 *   data: [
 *     { key: 'assets', title: 'Assets', value: 100, description: 'Total number of assets', divider: true },
 *     { key: 'running', title: 'Running', value: 83, description: 'Total number of running assets' }
 *   ]
 * }];
 *
 * const config = {
 *   size: 'medium',
 *   state: 'titleFirst',
 *   showInfo: true,
 *   color: 'blue',
 *   itemsClickable: true
 * };
 *
 * const handleKpiClick = (key) => {
 *   console.log('KPI clicked:', key);
 * };
 *
 * <Kpi kpiData={kpiData} config={config} onKPIClick={handleKpiClick} />
 *
 * @example
 * // Usage with multiple KPI instances, one in loading state
 * const kpiData = [
 *   {
 *     id: 'assets-kpi',
 *     loading: false,
 *     data: [
 *       { key: 'assets', title: 'Assets', value: 100 },
 *       { key: 'running', title: 'Running', value: 83 }
 *     ]
 *   },
 *   {
 *     id: 'performance-kpi',
 *     loading: true,
 *     data: [
 *       { key: 'performance', title: 'Performance', value: 87 },
 *       { key: 'uptime', title: 'Uptime', value: '95%' }
 *     ]
 *   }
 * ];
 *
 * <Kpi kpiData={kpiData} config={config} />
 */
const Kpi: React.FC<KpiProps> = ({
  kpiData,
  config,
  onKPIClick,
  kpiRef
}) => {
  return (
    <div className="kpi-wrapper">
      {kpiData.map((instance: KpiInstanceConfig, index: number) => (
        <ConfigurableKPIs
          ref={kpiRef}
          key={instance.id || index}
          data={instance.data}
          loading={instance.loading}
          config={{...config, ...instance.config}}
          onKPIClick={onKPIClick}
          selectedKpiKey={instance.selectedKpiKey}
        />
      ))}
    </div>
  )
}

export default Kpi