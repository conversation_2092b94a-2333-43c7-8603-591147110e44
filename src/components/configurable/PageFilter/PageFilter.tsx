import { useImperativeHandle, forwardRef, useRef, useEffect } from "react";
import GenericFilter from "@datoms/react-components/src/components/GenericFilter/pages";
import { usePageFilters } from "./usePageFilter";
import { Skeleton } from "antd";

type PageFilterProps = any;

const PageFilter = forwardRef((props: PageFilterProps, ref) => {
  const genericFilterRef = useRef<any>(null);
  const {
    filterData,
    searchObject,
    onUrlChange,
    applyFilterSelect,
    filterLoaded,
    selectSearch,
    onPopupScroll,
    selectOnBlur,
    filters,
  } = usePageFilters(props);


  useEffect(() => {
    if(props.setIsFilterLoaded) {
      props.setIsFilterLoaded(filterLoaded);
    }
  }, [filterLoaded]);

  // Expose filters to the parent via ref
  useImperativeHandle(ref, () => ({
    filterLoaded: filterLoaded,
    getFilters: () => filters,
    setFilters: (filterConfig: any) => {
      console.log("genericFilterRef.current", filterConfig);
      genericFilterRef.current?.optionOnChange?.(filterConfig?.value, filterConfig?.index, true);
    }
  }));

   const loadingComponent = (
      <div style={{ marginTop: 24, marginLeft: 32, marginRight: 24, width: '100%' }}>
        <Skeleton active paragraph={{rows: props.loaderRows || 3}} title={false} />
      </div>
    );

  return filterLoaded ? (
    <GenericFilter
      {...props}
      ref={genericFilterRef}
      filterData={filterData}
      searchObject={searchObject}
      default={filterData.map(_=>{
        return ''
      })}
      onPopupScroll={onPopupScroll}
      selectSearch={selectSearch}
      selectOnBlur={selectOnBlur}
      panelFilterSelect={applyFilterSelect}
      onUrlChange={onUrlChange}
      replaceSeparator
    />
  ) : (
    loadingComponent
  );
});

export default PageFilter;
