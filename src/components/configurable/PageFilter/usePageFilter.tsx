import { message, TreeSelect } from "antd";
import { useState, useCallback, useEffect, useRef } from "react";
import _uniqBy from "lodash/uniqBy";
import {
  retrieveSitesList,
  retriveCustomerList,
  retriveVendorThingsList,
  retriveThingsList,
  getSiteTypes,
  getTableData,
  getTableDataV4,
  retriveVendorFirmwareList,
} from "@datoms/js-sdk";
import { getTerritoryData } from "../../../packages/webapp-component-user-management/src/components/TerritoryPage/TerritorySelect";
import { useGlobalContext } from "../../../store/globalStore";

type Filters = {
  [key: string]: any;
};

type UseFiltersReturn = {
  filters: Filters;
  setFilter: (key: keyof Filters, value: Filters[keyof Filters]) => void;
  resetFilters: () => void;
  filterData: any[];
  pageLoading: boolean;
};

type FilterOptions = {
  territories?: any[];
  partners?: any[];
  customers?: any[];
  sites?: any[];
  device_qr?: any[];
};

interface OptionItem {
  value: number;
  title: string;
}

interface CustomerListState {
  customers?: OptionItem[];
  partners?: OptionItem[];
}

type FilterState = {
  [key: string]: { page: number; search: string; loading: boolean };
};

function isValidNumber(value: any) {
	return !isNaN(value) && !isNaN(parseFloat(value));
}

const simplifiedUrlVal = (urlVal: any) => {
  if (urlVal === "undefined") return "";
  else if (!isValidNumber(urlVal)) return urlVal;

  return Number(urlVal);
};

const getUrlFilterValue = (key, props) => {
  const { filterData } = props;

  const urlSearch = props.history?.location?.search;
  if (!urlSearch) return undefined;

  // Extract the filter part from the URL
  const filterPattern = /filter=([^&]+)/;
  const filterMatch = urlSearch.match(filterPattern);
  if (!filterMatch) return undefined;
  
  const filterString = filterMatch[1];
  
  // Split the filter string by commas that are followed by a key and colon
  // This preserves commas within values
  const filterParts = filterString.split(/,(?=[^:,]+:)/);
  
  // Find the part that starts with our key
  const keyPart = filterParts.find(part => part.startsWith(`${key}:`));
  if (!keyPart) return undefined;
  
  // Extract the value part
  const urlValue = keyPart.substring(key.length + 1);
  const currentFilter = filterData.find((item) => item.url_name === key);

  if (
    currentFilter?.multiSelect ||
    currentFilter?.filter_api === "territories"
  ) {
    const decodedValue = decodeURIComponent(urlValue);
    if (
      decodedValue &&
      decodedValue !== "undefined" &&
      decodedValue !== "null"
    ) {
      return decodedValue
        .split(",")
        .map((item) => (isNaN(parseInt(item)) ? item : parseInt(item)));
    }
    return decodedValue === "undefined" ? "" : decodedValue;
  }

  return simplifiedUrlVal(urlValue);
};

const getUrlValues: Filters = (props, initial = false) => {
  const { filterData } = props;
  const filterKeys = filterData.map((item) => item.url_name);
  if (props.searchObject) {
    filterKeys.push(props.searchObject.url_name || "search");
  }
  const updatedFilters: { [key: string]: any } = {};
  const defaultFilters = initial ? initializeFilters(filterData).filters : {};
  filterKeys.forEach((key: string) => {
    const value = getUrlFilterValue(key, props);
    if (value !== undefined || !initial) {
      updatedFilters[key] = value;
    } else if (defaultFilters[key]) {
      updatedFilters[key] = defaultFilters[key];
    }
  });
  return updatedFilters;
};

const initializeFilters = (filterData: any) => {
  const filters: Filters = {};
  const filterState: FilterState = {};
  filterData.forEach((filter: any) => {
    if (filter.hideField) return;
    if (filter.url_name && filter.defaultValue) {
      filters[filter.url_name] = filter.defaultValue;
    }
    if (filter.filter_api) {
      filterState[filter.filter_api] = { page: 1, search: "", loading: false };
    }
  });
  return { filters, filterState };
};

const updateFilterDataWithOptions: any[] = (props, filterOptions, filters) => {
  let hidden_filters = [];
  props.filterData?.forEach((filter: any) => {
    hidden_filters = hidden_filters.concat(
      filter.remove_filters?.[filters[filter.url_name]] || [],
    );
  });
  const finalFilterData = props.filterData.map((filter) => {
    if (hidden_filters.length && hidden_filters.includes(filter.url_name)) {
      filter.hideField = true;
    } else {
      filter.hideField = false;
    }

    if (
      filter.url_name === "territories" &&
      filterOptions[filter.filter_api] &&
      !filterOptions[filter.filter_api].length
    ) {
      filter.hideField = true;
    }

    if (!filter.hideField) {
      if (filter.url_name === "territories") {
        return {
          ...filter,
          component_props: {
            ...filter.component_props,
            treeData: filterOptions[filter.filter_api],
            value: filters[filter.url_name] || [],
            // showCheckedStrategy: TreeSelect.SHOW_ALL,
            maxTagPlaceholder: (omittedValues) =>
              omittedValues.length +
              ` territor${omittedValues.length === 1 ? "y" : "ies"} selected`,
            filterTreeNode: (search, item) => {
              return item.title.toLowerCase().indexOf(search.toLowerCase()) >= 0;
            },
          },
        };
      }

      return {
        ...filter,
        optionData: filterOptions[filter.filter_api] || filter.optionData,
        selectValue: filters[filter.url_name],
      };
    }
    return filter;
  });
  let finalSearchObject;
  if (props.searchObject) {
    const searchValueKey = props.searchObject.url_name || "search";
    finalSearchObject = {
      ...props.searchObject,
      value: filters[searchValueKey],
    };
  }

  return { finalFilterData, finalSearchObject };
};

export const usePageFilters = (props: any): UseFiltersReturn => {
  const debounceTimer = useRef<NodeJS.Timeout | null>(null);
  const [filterLoaded, setFilterLoaded] = useState<boolean>(false);
  const [filters, setFilters] = useState<Filters>({});
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({});
  const [filterState, setFilterState] = useState(
    () => initializeFilters(props.filterData).filterState,
  );
  const [debouncedSearch, setDebouncedSearch] = useState({});

  const context = useGlobalContext();

  const clientId = props.client_id || context.client_id;
  const clientName = props.client_name || context.client_name;
  const applicationId = props.application_id || context.application_id;

  const setLoadingForFilterOptions = (urlNames) => {
    setFilterOptions((prevOptions) => {
      const updatedOptions = { ...prevOptions };

      urlNames.forEach((urlName) => {
        updatedOptions[urlName] = [{ value: "loading", title: "Loading..." }];
      });

      return updatedOptions;
    });
  };

  const updateFilterState = (urlNames, key, value) => {
    setFilterState((prevState) => {
      const updatedState = { ...prevState };

      urlNames.forEach((urlName) => {
        updatedState[urlName] =
          key === "reset_all"
            ? { page: 1, search: "", loading: false }
            : {
                ...updatedState[urlName],
                [key]: value,
              };
      });

      return updatedState;
    });
  };

  const setFilterFunc = useCallback((updates: Filters) => {
    setFilters((prevFilters) => ({
      ...prevFilters,
      ...updates,
    }));
  }, []);

  // Add a function that can be called whenever user selects a filter

  const onUrlChange = () => {
    const updatedValues = getUrlValues(props);
    const previousFilters = { ...filters };

    setFilterFunc(updatedValues);

    const updatedKeys = Object.keys(updatedValues).filter(
      (key) =>
        updatedValues[key] !== previousFilters[key] &&
        !(!updatedValues[key] && !previousFilters[key]),
    );

    const apiCalls = [];
    const apiCallFields = [];

    updatedKeys.forEach((key) => {
      if (props.resetDependentFields[key]) {
        props.resetDependentFields[key].forEach((dependentField) => {
          const dependentFilter = props.filterData.find(
            (filter) => filter.url_name === dependentField,
          );

          if (dependentFilter?.filter_api) {
            const apiFunction = apiFunctionMapping[dependentFilter.filter_api];
            if (apiFunction) {
              apiCallFields.push(dependentFilter.filter_api);
              apiCalls.push(apiFunction(false, true, updatedValues, 1));
            }
          }
        });
      }
    });

    if (apiCalls.length > 0) {
      setLoadingForFilterOptions(apiCallFields);
      updateFilterState([apiCallFields], "reset_all");
      Promise.all(apiCalls)
        .then(() => {
          console.log("Dependent APIs executed successfully.");
        })
        .catch((error) => {
          console.error("Error executing dependent APIs:", error);
        });
    }
  };

  function applyFilterSelect(value, key) {
    const selectedValues = [...value];
    const totalOptions: any = {};
    const optionsToUpdate: string[] = [];

    let changedValueIndex: number = props.filterData.findIndex(
      (filter: any) => filter.url_name === key,
    );
    // const changedIndex = props.filterData.findIndex((filter) => filter.url_name === key);
    if (props.resetDependentFields[key]) {
      const resetFieldIndexes = props.resetDependentFields[key].map(
        (dependentKey) => {
          const findIndex = props.filterData.findIndex(
            (filter) => filter.url_name === dependentKey,
          );
          if (inplaceOptionsUpdate.includes(dependentKey)) {
            optionsToUpdate.push(dependentKey);
          }
          return findIndex;
        },
      );

      optionsToUpdate.forEach((key) => {
        switch (key) {
          case "firmware_version": {
            if (!value[changedValueIndex])
              totalOptions["firmware_version"] = filterOptions.firmware_version;
            else
              totalOptions["firmware_version"] =
                filterOptions.firmware_version.filter(
                  (device: any) => device.id === value[changedValueIndex],
                );
            break;
          }
          case "site_status": {
            if (!value[changedValueIndex]) {
              totalOptions["site_status"] = [];
            } else {
              const selectedSiteType = filterOptions.site_type.find(
                (site: any) => site.value === value[changedValueIndex],
              );
              totalOptions["site_status"] =
                selectedSiteType?.details2?.statuses?.map((status) => ({
                  value: status.key,
                  title: status.label,
                })) || [];
            }
            break;
          }
        }
      });

      for (let i = 0; i < selectedValues.length; i++) {
        if (resetFieldIndexes.includes(i)) {
          selectedValues[i] = undefined;
        }
      }
    }
    const hidden_filters = {};
    const changedFilter = props.filterData[changedValueIndex];
    if (changedFilter?.remove_filters) {
      if (changedFilter.remove_filters[value[changedValueIndex]]) {
        changedFilter.remove_filters[value[changedValueIndex]].forEach(
          (field: any) => {
            hidden_filters[field] = true;
          },
        );
      }
    }

    return {
      selected_values: selectedValues,
      total_options: totalOptions,
      hide_field: hidden_filters,
    };
  }

  const selectSearch = (searchValue, url_name) => {
    const filter = props.filterData.find((item) => item.url_name === url_name);

    if (filter?.filter_api && filter?.api_pagination) {
      setFilterState((prevState) => ({
        ...prevState,
        [url_name]: {
          ...prevState[url_name],
          search: searchValue,
          loading: true,
        },
      }));
      setLoadingForFilterOptions([url_name]);
      setDebouncedSearch((prevDebouncedSearch) => ({
        ...prevDebouncedSearch,
        [url_name]: searchValue,
      }));
    }
  };

  /*Update Calls*/

  useEffect(() => {
    fetchAllFilters(false, true);
  }, []);

  useEffect(() => {
    const debounceTimeout = 1000; // 500ms debounce interval
    const timer = setTimeout(() => {
      Object.keys(debouncedSearch).forEach((key) => {
        const filter = props.filterData.find((item) => item.url_name === key);

        if (filter?.filter_api && filterState[key]?.loading) {
          const apiFunction = apiFunctionMapping[filter.filter_api];

          if (apiFunction) {
            apiFunction(false, true)
              .then(() => {
                setFilterState((prevState) => ({
                  ...prevState,
                  [key]: {
                    ...prevState[key],
                    loading: false,
                  },
                }));
              })
              .catch((error) => {
                console.error(`Error fetching data for ${key}:`, error);
                setFilterState((prevState) => ({
                  ...prevState,
                  [key]: {
                    ...prevState[key],
                    loading: false,
                  },
                }));
              });
          }
        }
      });
    }, debounceTimeout);

    return () => clearTimeout(timer);
  }, [debouncedSearch, filterState]);

  // Function to create formatted filter array for display
  const createFormattedFilterArray = (filterObj:any) => {
    if (!filterObj || !props.filterData) return [];
    
    const formattedFilters: { title: string; value: any }[] = [];
    
    // Process regular filters
    Object.keys(filterObj).forEach(key => {
      // Skip empty values
      if (filterObj[key] === undefined || filterObj[key] === null || filterObj[key] === '') return;
      
      const filterItem = props.filterData.find(item => item.url_name === key);
      if (!filterItem) return;
      
      // Handle search type separately
      if (filterItem.type === 'search') {
        formattedFilters.push({
          title: filterItem.label || 'Search',
          value: filterObj[key]
        });
        return;
      }
      const optionData = filterOptions[filterItem.filter_api] || filterItem.optionData;
      // Handle multi-select values
      if (Array.isArray(filterObj[key]) && filterObj[key].length > 0) {
        const selectedValues: string[] = [];
        
        // Special handling for territories which has a nested structure
        if (key === 'territories') {
          // Function to find territory by value in a nested structure
          const findTerritoryTitle = (value: any, territories: any) => {
            if (!territories || !Array.isArray(territories)) return null;
            
            for (const territory of territories) {
              if (territory.value === value) {
                return territory.title;
              }
              
              if (territory.children) {
                const foundInChildren: any = findTerritoryTitle(value, territory.children);
                if (foundInChildren) return foundInChildren;
              }
            }
            
            return null;
          };
          
          filterObj[key].forEach(val => {
            if (val === undefined || val === null) return;
            
            // Search in the territory tree structure
            const territoryTitle = findTerritoryTitle(val, filterOptions.territories);
            if (territoryTitle) {
              selectedValues.push(territoryTitle);
            }
          });
        } else {
          // Regular multi-select handling
          filterObj[key].forEach(val => {
            if (val === undefined || val === null) return;
            
            const option = optionData?.find(opt => opt.value === val);
            if (option) {
              selectedValues.push(option.title);
            }
          });
        }
        
        if (selectedValues.length > 0) {
          formattedFilters.push({
            title: filterItem.label || filterItem.placeholder || key,
            value: selectedValues.join(", ")
          });
        }
      } else {
        // Handle single select values
        const option = optionData?.find((opt: any) => opt.value === filterObj[key]);
        if (option) {
          formattedFilters.push({
            title: filterItem.label || filterItem.placeholder || key,
            value: option.title
          });
        }
      }
    });
    
    // Handle search separately if it's not in filterData
    if (filterObj.search && !formattedFilters.some(f => f.title === 'Search')) {
      formattedFilters.push({
        title: props.searchObject?.label || 'Search',
        value: filterObj.search
      });
    }
    
    return formattedFilters;
  };

  useEffect(() => {
    if (
      Object.keys(filters).length &&
      typeof props.filterCallback === "function"
    ) {
      if (filters.search) {
        // If `filters.search` is changing, use debounce logic
        if (debounceTimer.current) {
          clearTimeout(debounceTimer.current);
        }
        debounceTimer.current = setTimeout(() => {
          const formattedFilters = createFormattedFilterArray(filters);
          props.filterCallback(filters, formattedFilters);
        }, 300);
      } else {
        const formattedFilters = createFormattedFilterArray(filters);
        props.filterCallback(filters, formattedFilters);
      }
    }

    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, [filters]);

  /*End */

  /*API Functions */
  const fetchAllFilters = async (append: boolean, setState: boolean) => {
    try {
      const validFilterData = props.filterData.filter(
        (filter: any) =>
          !filter.hideField &&
          Object.keys(apiFunctionMapping).includes(filter.filter_api),
      );

      const apiPromises = validFilterData.map((filter: any) => {
        const apiFunction = apiFunctionMapping[filter.filter_api];
        return apiFunction(append, setState);
      });

      // Wait for all API responses and collect their results
      const apiResults = await Promise.all(apiPromises);

      const urlValues = getUrlValues(props, true);
      const firstOptionValues: { [key: string]: any } = {};

      // Map API results to their corresponding filters
      validFilterData.forEach((filter: any, index: number) => {
        if (
          filter.select_first_option &&
          filter.filter_api !== "territories" &&
          apiResults[index]?.length
        ) {
          firstOptionValues[filter.url_name] = apiResults[index][0].value;
        }
      });

      setFilters({
        ...firstOptionValues,
        ...urlValues,
      });
      setFilterLoaded(true);
    } catch (error) {
      console.error("Error fetching filters:", error);
      message.error("Failed to fetch filter data");
    }
  };

  const apiFunctionMapping: any = {
    partners: getVendorList,
    customers: getCustomerList,
    territories: getTerritoryList,
    thing_category: getAvailableThingCategories,
    site_type: getAvailableSiteTypes,
    device_type: getAvailableDeviceTypesAndFirmwares,
    sites: getSiteList,
    device_qr: getDeviceQrList,
  };

  const inplaceOptionsUpdate = ["firmware_version", /*"site_status"*/];

  const onPopupScroll = async (e, key) => {
    if (
      Math.abs(
        e.target.scrollTop + e.target.offsetHeight - e.target.scrollHeight,
      ) < 1
    ) {
      const filter = props.filterData.find((item) => item.url_name === key);
      const filterApi = filter?.filter_api;

      if (apiFunctionMapping[filterApi] && filter.api_pagination) {
        const currentPage = filterState[key]?.page || 1;
        const nextPage = currentPage + 1;

        setFilterState((prevState) => ({
          ...prevState,
          [key]: {
            ...prevState[key],
            page: nextPage,
          },
        }));
        const apiFunction = apiFunctionMapping[filterApi];
        await apiFunction(true, true, undefined, nextPage);
      }
    }
  };

  const selectOnBlur = (key) => {
    updateFilterState([key], "reset_all");
  };

  async function getTerritoryList() {
    const territoryResponse = await getTerritoryData(
      clientId,
      clientName,
      true,
    );
    if (territoryResponse?.territoryData) {
      setFilterOptions((prev) => ({
        ...prev,
        territories: territoryResponse?.territoryData?.children || [],
      }));
    }
  }

  async function getVendorList(
    append: boolean,
    setState: boolean,
    currentFilter = filters,
    pageNo = filterState.partners.page || 1,
  ): Promise<OptionItem[]> {
    let allVendorList: OptionItem[] = [];

    let queryString = `?results_per_page=20&vendor_only=true`;
    if (currentFilter.account_type) {
      queryString += `&account_type=${currentFilter.account_type}`;
    }
    if (filterState.partners.search) {
      queryString += `&search=${filterState.partners.search}`;
    } else {
      queryString += `&page_no=${pageNo}`;
      if (currentFilter.partners?.length) {
        queryString += `&vendors=[${currentFilter.partners.join(",")}]`;
      }
    }

    try {
      const vendorListData = await retriveCustomerList(
        clientId,
        queryString,
      );

      if (vendorListData.status === "success") {
        vendorListData.customers.forEach((item: any) => {
          if (item.id !== 1) {
            allVendorList.push({
              value: item.id,
              title: item.name,
            });
          }
        });

        if (append) {
          allVendorList = [...(filterOptions.partners || []), ...allVendorList];
          allVendorList = _uniqBy(allVendorList, "value");
        }

        if (setState) {
          setFilterOptions((prev) => ({
            ...prev,
            partners: allVendorList,
          }));
        }
      } else if (vendorListData.message) {
        message.error(vendorListData.message);
      }
    } catch (error) {
      console.error("Error fetching vendor list:", error);
      message.error("Failed to fetch vendor list");
    } finally {
      setFilterState((prevState) => ({
        ...prevState,
        partners: {
          ...prevState.partners,
          loading: false,
        },
      }));
    }

    return allVendorList;
  }

  async function getCustomerList(
    append: boolean,
    setState: boolean,
    currentFilter = filters,
    pageNo = filterState.customers.page || 1,
  ): Promise<OptionItem[]> {
    let allCustomerList: OptionItem[] = [];

    try {
      // Construct the query string dynamically
      let queryString = `?results_per_page=20&end_customer_only=true`;

      if (currentFilter.account_type) {
        queryString += `&account_type=${currentFilter.account_type}`;
      }
      if (currentFilter.partners?.length) {
        queryString += `&vendors=[${currentFilter.partners.join(",")}]`;
      }
      if (filterState.customers.search?.length) {
        queryString += `&search=${filterState.customers.search}`;
      } else {
        queryString += `&page_no=${pageNo}`;
        if (currentFilter.customers?.length) {
          queryString += `&clients=[${currentFilter.customers.join(",")}]`;
        }
      }

      // API call to retrieve customer list
      const customerListData = await retriveCustomerList(
        clientId,
        queryString,
      );

      if (customerListData.status === "success") {
        customerListData.customers.forEach((item: any) => {
          if (item.id !== 1) {
            allCustomerList.push({
              value: item.id,
              title: item.name,
            });
          }
        });

        if (append) {
          allCustomerList = _uniqBy(
            [...(filterOptions.customers || []), ...allCustomerList],
            "value",
          );
        }

        if (append || setState) {
          setFilterOptions((prevOptions) => ({
            ...prevOptions,
            customers: allCustomerList,
          }));
        }
      } else if (customerListData.message) {
        message.error(customerListData.message);
      }
    } catch (error) {
      console.error("Error fetching customer list:", error);
      message.error("Failed to fetch customer list");
    } finally {
      // Set loading state to false
      setFilterState((prevState) => ({
        ...prevState,
        customers: {
          ...prevState.customers,
          loading: false,
        },
      }));
    }

    return allCustomerList;
  }

  async function getSiteList(
    append: boolean,
    setState: boolean,
    currentFilter = filters,
    pageNo = filterState.sites.page || 1,) {

    let allSiteList: OptionItem[] = [];

    try {
      // Construct the query string dynamically
      let queryString = `?results_per_page=20&end_customer_only=true`;

      if (currentFilter.account_type) {
        queryString += `&account_type=${currentFilter.account_type}`;
      }
      if (currentFilter.partners?.length) {
        queryString += `&vendors=[${currentFilter.partners.join(",")}]`;
      }
      if (currentFilter.customers?.length) {
        queryString += `&clients=[${currentFilter.customers.join(",")}]`;
      }
      if (filterState.sites.search?.length) {
        queryString += `&search=${filterState.sites.search}`;
      } else {
        queryString += `&page_no=${pageNo}`;
        if (currentFilter.sites?.length) {
          queryString += `&sites=[${currentFilter.sites.join(",")}]`;
        }
      }

      const siteListData = await retrieveSitesList(clientId, queryString);

      if (siteListData.status === "success") {
        siteListData.data.forEach((item: any) => {
          if (item.id !== 1) {
            allSiteList.push({
              value: item.id,
              title: item.name,
            });
          }
        });

        if (append) {
          allSiteList = _uniqBy(
            [...(filterOptions.sites || []), ...allSiteList],
            "value",
          );
        }

        if (append || setState) {
          setFilterOptions((prevOptions) => ({
            ...prevOptions,
            sites: allSiteList,
          }));
        }
      } else if (siteListData.message) {
        message.error(siteListData.message);
      }
    } catch (error) {
      console.error("Error fetching site list:", error);
      message.error("Failed to fetch site list");
    } finally {
      setFilterState((prevState) => ({
        ...prevState,
        sites: {
          ...prevState.sites,
          loading: false,
        },
      }));
    }

    return allSiteList;
  }

  async function getDeviceQrList(
    append: boolean,
    setState: boolean,
    currentFilter = filters,
    pageNo = filterState.device_qr.page || 1,) {

    let allDeviceQrList: OptionItem[] = [];

    try {
      let filters:any =  {};
      let search = "";
      if (currentFilter.partners?.length) {
        filters["info.partner_org_id"] = currentFilter.partners;
      }
      if (currentFilter.customers?.length) {
        filters["info.customer_org_id"] = currentFilter.customers;
      }
      if (filterState.sites.search?.length) {
        filters["info.site.id"] = currentFilter.sites;
      } else {
        search = filterState.device_qr.search;
        if (currentFilter.device_qr?.length) {
          filters["info.id"] = currentFilter.device_qr;
        }
      }
      const deviceQrListData = await getTableDataV4(clientId, {
          apiType: "devices",
          apiConfig: {
              api_query: {
                  page: pageNo,
                  limit: 20,
                  filter: filters,
                  search: search
              }
          },
          columns: [
              {
                  key: "device_id",
                  dataIndex: "device_id",
                  colPathExp: "info.id",
                  valueIndex: 0
              },
              {
                  key: "device_name",
                  dataIndex: "device_name",
                  colPathExp: "info.name",
                  valueIndex: 0
              }
          ],
          dataPathExp: "data"
      });

      if (deviceQrListData.status === "success") {
        deviceQrListData.tableData.forEach((item: any) => {
          console.log("item", item);
            allDeviceQrList.push({
              value: item.device_id,
              title: item.device_name,
            });
        });

        if (append) {
          allDeviceQrList = _uniqBy(
            [...(filterOptions.device_qr || []), ...allDeviceQrList],
            "value",
          );
        }

        if (append || setState) {
          setFilterOptions((prevOptions) => ({
            ...prevOptions,
            device_qr: allDeviceQrList,
          }));
        }
      } else if (deviceQrListData.message) {
        message.error(deviceQrListData.message);
      }
    } catch (error) {
      console.error("Error fetching device QR list:", error);
      message.error("Failed to fetch device QR list");
    } finally {
      setFilterState((prevState) => ({
        ...prevState,
        device_qr: {
          ...prevState.device_qr,
          loading: false,
        },
      }));
    }

    return allDeviceQrList;
  }

  async function getAvailableThingCategories(): Promise<OptionItem[]> {
    try {
      let categoryList;

      if (applicationId === 16) {
        categoryList = await retriveThingsList(
          {
            client_id: clientId,
            application_id: applicationId,
          },
          "?page_no=1&results_per_page=1&available_categories=true&lite=true",
        );
      } else {
        categoryList = await retriveVendorThingsList(
          {
            vendor_id: clientId,
            application_id: applicationId,
          },
          "?page_no=1&results_per_page=1&available_categories=true&lite=true",
        );
      }

      const categoryListOptions: OptionItem[] = [];
      if (categoryList.available_categories?.length) {
        categoryList.available_categories.forEach((item: any) => {
          const catName = categoryList.things_categories?.find(
            (cat: any) => cat.id === item.id,
          )?.name;
          categoryListOptions.push({
            value: item.id,
            title: catName || item.id,
          });
        });
      }
      setFilterOptions((prevOptions) => ({
        ...prevOptions,
        thing_category: categoryListOptions,
      }));
      return categoryListOptions;
    } catch (error) {
      console.error("Error fetching thing categories:", error);
      message.error("Failed to fetch thing categories");
    }
  }

  async function getAvailableDeviceTypesAndFirmwares(): Promise<OptionItem[]> {
    try {
      let response = await retriveVendorFirmwareList(
        clientId,
        "application=all",
      );
      const deviceTypeListOptions: OptionItem[] = [];
      const firmwareTypeListOptions: any[] = [];

      if (response.status === "success") {
        response.device_types?.forEach((device: any) => {
          deviceTypeListOptions.push({
            value: device.id,
            title: device.name,
          });
          if (device.firmwares) {
            let firmwareListwrtDevice: any = {
              id: device.id,
              label: device.name,
              title: device.name,
              options: [],
            };
            device.firmwares.forEach((firmware: any) => {
              firmwareListwrtDevice.options.push({
                groupLabel: device.name + " " + firmware.version,
                label: firmware.version,
                value: firmware.id,
              });
            });
            firmwareTypeListOptions.push(firmwareListwrtDevice);
          }
        });
      }
      setFilterOptions((prevOptions) => ({
        ...prevOptions,
        device_type: deviceTypeListOptions,
        firmware_version: firmwareTypeListOptions,
      }));
      return deviceTypeListOptions;
    } catch (error) {
      console.error("Error fetching device types:", error);
      message.error("Failed to fetch device types");
    }
  }

  async function getAvailableSiteTypes(): Promise<OptionItem[]> {
    try {
      // const siteTypeList = { status: "success", data: allSiteTypeConfigs };
      const siteTypeList = await getSiteTypes(clientId);
      const siteTypeListOptions: OptionItem[] = [];
      // let statusOptions: OptionItem[] = [];

      if (siteTypeList.status === "success") {
        siteTypeList.data?.forEach((item: any) => {
          siteTypeListOptions.push({
            value: item.id,
            title: item.name,
            details: item.details,
          });
        });
      }

      // Set site status options if select_first_option is true for site_type
      // const siteTypeFilter = props.filterData?.find(
      //   (filter) => filter.url_name === "site_type",
      // );
      // if (
      //   siteTypeFilter?.select_first_option &&
      //   siteTypeListOptions.length > 0
      // ) {
      //   statusOptions =
      //     siteTypeListOptions[0].details.statuses?.map((status) => ({
      //       value: status.key,
      //       title: status.label,
      //     })) || [];
      // }

      setFilterOptions((prevOptions) => ({
        ...prevOptions,
        site_type: siteTypeListOptions,
        // site_status: statusOptions,
      }));

      return siteTypeListOptions;
    } catch (error) {
      console.error("Error fetching site types:", error);
      message.error("Failed to fetch site types");
    }
  }

  /*End */

  const { finalFilterData, finalSearchObject } = updateFilterDataWithOptions(
    props,
    filterOptions,
    filters,
  );

  console.log("filterSelected", filters);

  return {
    filters,
    setFilterFunc,
    filterData: finalFilterData,
    searchObject: finalSearchObject,
    onUrlChange,
    applyFilterSelect,
    filterLoaded,
    selectSearch,
    onPopupScroll,
    selectOnBlur,
  };
};
