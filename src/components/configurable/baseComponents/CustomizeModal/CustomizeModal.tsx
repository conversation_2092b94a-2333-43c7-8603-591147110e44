import React from "react";
import { Modal, Select, Checkbox, message } from "antd";
import { MoreOutlined } from "@ant-design/icons";
import ReorderableListCustom from "@datoms/react-components/src/components/ReorderableList";
import { CustomizeModalProps } from "./types";
import "./style.less";

const padNumber = (value: number = 0, total: number = 0): string => {
  if (total > 10 && value < 10) {
    return `0${value}`;
  }
  return value.toString();
};

const CustomizeModal: React.FC<CustomizeModalProps> = ({
  visible,
  modalLoading,
  totalColumns,
  visibleColumns,
  hiddenColumns,
  fixedColumnsCount,
  onClose,
  onOk,
  onReset,
  setFixedColumnsCount,
  setVisibleColumns,
  setHiddenColumns,
  isValidGrouping,
  customBody,
  ...props
}) => {
  console.log("modalColumns", totalColumns, visibleColumns, hiddenColumns);

  const findColumnByIdentifier = (columns: any[], identifier: any) => {
    return columns.findIndex(
      (col) =>
        (col.key && col.key === identifier.key) ||
        (col.dataIndex && col.dataIndex === identifier.dataIndex),
    );
  };

  const onHideColumn = (index: number) => {
    const newVisibleColumns = [...visibleColumns];
    const newHiddenColumns = [...hiddenColumns];

    const columnToHide = { ...newVisibleColumns[index] };

    newVisibleColumns.splice(index, 1);

    const existingHiddenIndex = findColumnByIdentifier(
      newHiddenColumns,
      columnToHide,
    );

    if (existingHiddenIndex !== -1) {
      const existingHiddenColumn = newHiddenColumns[existingHiddenIndex];

      if (columnToHide.children && existingHiddenColumn.children) {
        existingHiddenColumn.children = [
          ...existingHiddenColumn.children,
          ...columnToHide.children,
        ];
      } else if (columnToHide.children) {
        existingHiddenColumn.children = [...columnToHide.children];
      }

      newHiddenColumns[existingHiddenIndex] = existingHiddenColumn;
    } else {
      newHiddenColumns.push(columnToHide);
    }

    setVisibleColumns(newVisibleColumns);
    setHiddenColumns(newHiddenColumns);
  };

  const onShowColumn = (index: number) => {
    const newVisibleColumns = [...visibleColumns];
    const newHiddenColumns = [...hiddenColumns];

    const columnToShow = { ...newHiddenColumns[index] };

    newHiddenColumns.splice(index, 1);

    const existingVisibleIndex = findColumnByIdentifier(
      newVisibleColumns,
      columnToShow,
    );

    if (existingVisibleIndex !== -1) {
      const existingVisibleColumn = newVisibleColumns[existingVisibleIndex];

      if (columnToShow.children && existingVisibleColumn.children) {
        existingVisibleColumn.children = [
          ...existingVisibleColumn.children,
          ...columnToShow.children,
        ];
      } else if (columnToShow.children) {
        existingVisibleColumn.children = [...columnToShow.children];
      }

      newVisibleColumns[existingVisibleIndex] = existingVisibleColumn;
    } else {
      const groupKey = columnToShow.group_key;
      if (groupKey) {
        const groupIndices = newVisibleColumns
          .map((col, idx) => (col.group_key === groupKey ? idx : -1))
          .filter((idx) => idx !== -1);

        if (groupIndices.length > 0) {
          const insertPosition = groupIndices[groupIndices.length - 1] + 1;
          newVisibleColumns.splice(insertPosition, 0, columnToShow);
        } else {
          newVisibleColumns.push(columnToShow);
        }
      } else {
        newVisibleColumns.push(columnToShow);
      }
    }

    setVisibleColumns(newVisibleColumns);
    setHiddenColumns(newHiddenColumns);
  };

  const onHideChildColumn = (parentIndex: number, childIndex: number) => {
    const newVisibleColumns = [...visibleColumns];
    const newHiddenColumns = [...hiddenColumns];

    const parent = { ...newVisibleColumns[parentIndex] };
    if (!parent.children || !Array.isArray(parent.children)) {
      console.error("Parent column doesn't have children array");
      return;
    }

    const childToHide = { ...parent.children[childIndex] };

    const newChildren = [...parent.children];
    newChildren.splice(childIndex, 1);

    if (newChildren.length === 0) {
      onHideColumn(parentIndex);
      return;
    }

    parent.children = newChildren;
    newVisibleColumns[parentIndex] = parent;

    const hiddenParentIndex = findColumnByIdentifier(newHiddenColumns, parent);

    if (hiddenParentIndex !== -1) {
      const hiddenParent = { ...newHiddenColumns[hiddenParentIndex] };
      if (!hiddenParent.children) {
        hiddenParent.children = [];
      }
      hiddenParent.children = [...hiddenParent.children, childToHide];
      newHiddenColumns[hiddenParentIndex] = hiddenParent;
    } else {
      const { children, ...parentWithoutChildren } = parent;
      const hiddenParentFixed = {
        ...parentWithoutChildren,
        children: [childToHide],
      };
      newHiddenColumns.push(hiddenParentFixed);
    }

    setVisibleColumns(newVisibleColumns);
    setHiddenColumns(newHiddenColumns);
  };

  const onShowChildColumn = (parentIndex: number, childIndex: number) => {
    const newVisibleColumns = [...visibleColumns];
    const newHiddenColumns = [...hiddenColumns];

    const parent = { ...newHiddenColumns[parentIndex] };
    if (!parent.children || !Array.isArray(parent.children)) {
      console.error("Parent column doesn't have children array");
      return;
    }

    const childToShow = { ...parent.children[childIndex] };

    const newChildren = [...parent.children];
    newChildren.splice(childIndex, 1);

    if (newChildren.length === 0) {
      onShowColumn(parentIndex);
      return;
    }

    parent.children = newChildren;
    newHiddenColumns[parentIndex] = parent;

    const visibleParentIndex = findColumnByIdentifier(
      newVisibleColumns,
      parent,
    );

    if (visibleParentIndex !== -1) {
      const visibleParent = { ...newVisibleColumns[visibleParentIndex] };
      if (!visibleParent.children) {
        visibleParent.children = [];
      }
      visibleParent.children = [...visibleParent.children, childToShow];
      newVisibleColumns[visibleParentIndex] = visibleParent;
    } else {
      const visibleParent = {
        ...parent,
        children: [childToShow],
      };

      const groupKey = parent.group_key;
      if (groupKey) {
        const groupIndices = newVisibleColumns
          .map((col, idx) => (col.group_key === groupKey ? idx : -1))
          .filter((idx) => idx !== -1);

        if (groupIndices.length > 0) {
          const insertPosition = groupIndices[groupIndices.length - 1] + 1;
          newVisibleColumns.splice(insertPosition, 0, visibleParent);
        } else {
          newVisibleColumns.push(visibleParent);
        }
      } else {
        newVisibleColumns.push(visibleParent);
      }
    }

    setVisibleColumns(newVisibleColumns);
    setHiddenColumns(newHiddenColumns);
  };

  return (
    <Modal
      title={props.title || "Customize"}
      open={visible}
      onCancel={onClose}
      onOk={onOk}
      okText="Apply"
      cancelText="Cancel"
      confirmLoading={modalLoading}
      closeIcon={null}
      width={1094}
      className="customize-modal-class"
      {...props}
    >
      <section className="cstm-tbl-modal-body">
        {props.showFreezeColumns && (
          <div className="cstm-tbl-modal-body-sec">
            <h4>
              <span className="cstm-label">Freeze Columns: </span>
              <Select
                value={fixedColumnsCount}
                options={[
                  {
                    label: "NA",
                    value: 0,
                  },
                  {
                    label: "1st Column",
                    value: 1,
                  },
                  {
                    label: "2nd Column",
                    value: 2,
                  },
                  {
                    label: "3rd Column",
                    value: 3,
                  },
                  {
                    label: "4th Column",
                    value: 4,
                  },
                  {
                    label: "5th Column",
                    value: 5,
                  },
                ]}
                onChange={(value) =>
                  typeof setFixedColumnsCount === "function" &&
                  setFixedColumnsCount(Number(value))
                }
              />
            </h4>
          </div>
        )}
        <div className="cstm-tbl-modal-body-sec-wrapper">
          <div className="cstm-tbl-modal-body-sec cstm-list">
            <h4>
              Visible columns -{" "}
              {padNumber(visibleColumns?.length, totalColumns)}/{totalColumns}
            </h4>
            <ReorderableListCustom
              items={
                Array.isArray(visibleColumns) &&
                visibleColumns.map((item, index) => {
                  // Create the base item with renderTitle
                  const baseItem: any = {
                    ...item,
                    renderTitle: (
                      <div>
                        <span>
                          <MoreOutlined style={{ color: "#808080" }} />
                          <MoreOutlined
                            style={{
                              color: "#808080",
                              position: "relative",
                              right: "7px",
                            }}
                          />
                        </span>
                        <Checkbox
                          checked
                          onChange={() => onHideColumn(index)}
                          disabled={item.not_customizable}
                        >
                          {item.title || item.text}
                        </Checkbox>
                      </div>
                    ),
                  };

                  // If the item has children, map them with their own renderTitle
                  if (
                    item.children &&
                    Array.isArray(item.children) &&
                    item.children.length > 0
                  ) {
                    baseItem.children = item.children.map(
                      (child, childIndex) => ({
                        ...child,
                        renderTitle: (
                          <div>
                            <span>
                              <MoreOutlined style={{ color: "#808080" }} />
                              <MoreOutlined
                                style={{
                                  color: "#808080",
                                  position: "relative",
                                  right: "7px",
                                }}
                              />
                            </span>
                            <Checkbox
                              checked
                              onChange={() =>
                                onHideChildColumn(index, childIndex)
                              }
                              disabled={child.not_customizable}
                            >
                              {child.title || child.text}
                            </Checkbox>
                          </div>
                        ),
                      }),
                    );
                  }

                  return baseItem;
                })
              }
              setItems={(items: any[]) => {
                if (
                  typeof isValidGrouping !== "function" ||
                  isValidGrouping(items)
                ) {
                  setVisibleColumns(items);
                } else {
                  message.warning("Invalid ordering!");
                }
              }}
            />
          </div>
          <div className="cstm-tbl-modal-body-sec cstm-list">
            <h4>
              Hidden columns - {padNumber(hiddenColumns?.length, totalColumns)}/
              {totalColumns}
            </h4>
            <section className="cstm-hdn-cols">
              {Array.isArray(hiddenColumns) &&
                hiddenColumns.map((item, index) => (
                  <div key={index} className="hidden-cols">
                    <Checkbox
                      checked={false}
                      onChange={() => onShowColumn(index)}
                      disabled={item.not_customizable}
                    >
                      <div className="hdn-cols-title">
                        {item?.title || item?.text}
                      </div>
                    </Checkbox>

                    {/* Render children if they exist */}
                    {item.children &&
                      Array.isArray(item.children) &&
                      item.children.length > 0 && (
                        <div className="hidden-cols-children">
                          {item.children.map((child, childIndex) => (
                            <div
                              key={`${index}-${childIndex}`}
                              className="hidden-cols child"
                            >
                              <Checkbox
                                checked={false}
                                onChange={() =>
                                  onShowChildColumn(index, childIndex)
                                }
                                disabled={child.not_customizable}
                              >
                                <div className="hdn-cols-title">
                                  {child?.title || child?.text}
                                </div>
                              </Checkbox>
                            </div>
                          ))}
                        </div>
                      )}
                  </div>
                ))}
            </section>
          </div>
        </div>
        {customBody}
        <span className="cstm-reset" onClick={onReset}>
          Reset to Default
        </span>
      </section>
    </Modal>
  );
};

export default CustomizeModal;
