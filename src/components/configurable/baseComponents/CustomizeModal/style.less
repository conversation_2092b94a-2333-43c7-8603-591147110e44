.customize-modal-class {
  // width: 742px !important;

  .ant-modal-header {
    padding-top: 25px;
    border-radius: 16px;
    .ant-modal-title {
      color: #4f4f4f;
    }
  }

  .ant-modal-content {
    background: #fbfbfb;
    border-radius: 16px;

    .ant-modal-body {
      // padding: 0px 0 24px 0;
      padding-top: 0;

      .cstm-tbl-modal-body {
        width: 100%;
        // margin-top: 20px;
        // padding: 5px 40px 0px;

        > h3 {
          color: #232323;
          font-size: 18px;
          font-weight: 500;
          margin-bottom: 8px;
        }

        .cstm-reset {
          color: #7689A1;
          cursor: pointer;
          font-size: 14px;
          font-weight: normal;
          margin-left: 3px;
          padding: 4px 12px;
          border-radius: 4px;
          display: inline-flex;
          position: absolute;
          bottom: 26px;

          &:hover {
            background-color: #e6f4ff87;
          }
        }

        .cstm-tbl-modal-body-sec {
          margin-bottom: 22px;

          > h4 {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #232323;
          }

          .cstm-label {
            font-weight: normal;
            color: #4f4f4f;
          }

          .ant-select {
            margin-left: 8px;
            height: 28px;
            min-width: 90px;

            .ant-select-selector {
              border-radius: 5px !important;
            }
          }

          .cstm-hdn-cols {
            height: 386px;
            overflow: auto;
            padding-right: 10px;
            
            .hidden-cols {
              padding: 11px 12px;
              margin-bottom: 10px;
              background-color: white;
              border-radius: 4px;
              box-shadow: 0px 2px 2px #0000000F;
              
              &.child {
                margin-bottom: 8px;
                padding: 8px 12px;
                box-shadow: 0px 1px 1px #0000000F;
                
                &:last-child {
                  margin-bottom: 0;
                }
              }
            }
            
            .hidden-cols-children {
              margin-top: 8px;
              padding-left: 4px;
              border-left: 2px solid #f0f0f0;
              margin-left: 6px;
            }
            
            .hdn-cols-title {
              display: inline-block;
              margin-left: 8px;
            }
          }

          .hidden-cols {
            margin: 0 12px 12px;
          }

          .ant-checkbox + span {
            color: #374375;
            font-weight: 500;
            margin-left: 10px;
          }
        }

        .cstm-tbl-modal-body-sec-wrapper {
          display: flex;
          flex-wrap: wrap;
          gap: 19px;
          .cstm-list {
            flex: 1;
            padding: 16px;
            margin-bottom: 0;
            border-radius: 8px;
            border: 1px solid #EBEBEB;
            background: rgba(242, 241, 241, 0.25);
            box-shadow: 0px 2px 1px 0px rgba(112, 112, 112, 0.1);
            @media (max-width: 768px) {
                flex: none;
                width: 100%;
                .dtms-reorderable-list {
                    max-height: 280px;
                }
                .cstm-hdn-cols {
                    max-height: 172px;
                }
            }
          }
          .dtms-reorderable-list {
            height: 386px;
            max-height: unset;
          }
        }
      }
    }
  }

  .ant-modal-footer {
    padding: 4px 24px 24px 24px;
    > .ant-btn + .ant-btn {
      margin-left: 24px;
    }
  }
}
