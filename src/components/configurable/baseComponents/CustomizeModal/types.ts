import { ModalProps } from 'antd';
import { Dispatch,ReactNode, SetStateAction } from 'react';

export interface Column {
  title: string;
  not_customizable?: boolean;
  [key: string]: any;
}

export interface CustomizeModalProps extends Omit<ModalProps, 'visible'> {
  visible: boolean;
  modalLoading?: boolean;
  totalColumns: number;
  visibleColumns: Column[];
  hiddenColumns: Column[];
  fixedColumnsCount?: number;
  onClose: () => void;
  onOk: () => void;
  onReset: () => void;
  setFixedColumnsCount?: (count: number) => void;
  setVisibleColumns: Dispatch<any>;
  setHiddenColumns: Dispatch<any>;
  isValidGrouping?: (items: any[]) => boolean;
  customBody?: ReactNode;
  showFreezeColumns?: boolean;
} 