import React, { useEffect } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import HighchartsMore from "highcharts/highcharts-more";
import HeatmapModule from "highcharts/modules/heatmap";
import Exporting from "highcharts/modules/exporting";
import ExportData from "highcharts/modules/export-data";
import FunnelModule from "highcharts/modules/funnel";
import SolidGaugeModule from "highcharts/modules/solid-gauge";
import MapModule from "highcharts/modules/map";
import TreeMapModule from "highcharts/modules/treemap";
import NetworkGraphModule from "highcharts/modules/networkgraph";
import StockModule from "highcharts/modules/stock";
import GanttModule from "highcharts/modules/gantt";
import SankeyModule from "highcharts/modules/sankey";
import VennModule from "highcharts/modules/venn";
import BulletModule from "highcharts/modules/bullet";
import NoDataToDisplay from "highcharts/modules/no-data-to-display";
import { mergeDefaultConfig } from "./utils/logic";
import SkeletonLoader from "@datoms/react-components/src/components/SkeletonLoader";

import "./styles.less";

Exporting(Highcharts);
ExportData(Highcharts);
HighchartsMore(Highcharts);
HeatmapModule(Highcharts);
FunnelModule(Highcharts);
SolidGaugeModule(Highcharts);
MapModule(Highcharts);
TreeMapModule(Highcharts);
NetworkGraphModule(Highcharts);
StockModule(Highcharts);
GanttModule(Highcharts);
SankeyModule(Highcharts);
VennModule(Highcharts);
BulletModule(Highcharts);
NoDataToDisplay(Highcharts);

/**
 * `ConfigurableChart` is a React functional component that renders a Highcharts chart
 * with the ability to dynamically merge custom configurations, handle loading states, and
 * support various Highcharts modules (such as Heatmap, Radar, Area, Line, Bubble, and more).
 *
 * This component takes in the following props:
 *
 * @component
 *
 * @param {Object} options - The Highcharts configuration object to configure the chart.
 *    This will be merged with default configuration options before rendering.
 * @param {string} chartType - The type of chart to render. This will determine the default plot options.
 * @param {boolean} loading - A boolean flag that determines if the chart is in a loading state.
 *    When `true`, a loading message will be displayed instead of the chart.
 *
 * @example
 * // Example usage of ConfigurableChart component:
 * const chartOptions = {
 *   chart: {
 *     type: 'heatmap'
 *   },
 *   title: {
 *     text: 'My Heatmap Chart'
 *   },
 *   series: [{
 *     data: [
 *       [0, 0, 1],
 *       [0, 1, 3],
 *       [1, 0, 2],
 *       [1, 1, 4]
 *     ]
 *   }]
 * };
 *
 * <ConfigurableChart options={chartOptions} loading={false} />
 *
 * @returns {JSX.Element} - The rendered chart or loading message.
 */
const ConfigurableChart: React.FC<{
  config: Record<string, any>;
  data: Object[];
  loading: boolean;
}> = ({ config, data, loading }) => {
  const chartType = config.chartType;
  const options = { ...config, series: data };

  useEffect(() => {
    if(config?.graph_colors) {
      Highcharts.setOptions({ colors: config?.graph_colors });
    }
  }, []);

  //when data will come in props
  // let min = 0;
  // let max = 100;
  // seriesData.forEach((s : any) => {
  //   if (s?.data?.length) {
  //     min = Math.min(min, ...s.data[s.data.length - 1]);
  //     max = Math.max(max, ...s.data[s.data.length - 1]);
  //   }
  // })

  const updatedOptions: Object = mergeDefaultConfig(options, chartType);

  // updatedOptions.yAxis.min = min - 10;
  // updatedOptions.yAxis.max = max + 10;

  return (
    <div className="configurable-chart-container" >
      {loading ? (
        <div data-testid="skeleton-loading-container">
          <SkeletonLoader active={true}/>
        </div>
      ) : (
        <HighchartsReact highcharts={Highcharts} options={updatedOptions} />
      )}
    </div>
  );
};

export default ConfigurableChart;
