import React from "react";
import { render, screen } from "@testing-library/react";
import ConfigurableSummary from "./index";

const mockPreferences = {
  page_preferences: {
    mockKey1: {
      mockKey2: {
        columns: [
          { title: "Column 1", dataIndex: "col1", checked: true },
          { title: "Column 2", dataIndex: "col2", checked: true },
          { title: "Column 3", dataIndex: "col3", checked: false },
        ],
        fixed_column_count: 2,
      },
    },
  },
};

jest.mock("../../../../store/globalStore", () => ({
  useGlobalContext: jest.fn(() => ({
    client_id: "mock-client-id",
    user_id: "mock-user-id",
    application_id: "mock-application-id",
    currentUserPreferences: mockPreferences,
    setCurrentUserPreferences: jest.fn(),
  })),
}));

jest.mock("../CustomizeModal", () => {
  return jest.fn(() => <div data-testid="customize-modal"></div>);
});

jest.mock("./utils/logic", () => ({
  getMergedUpdatedConfig: jest.fn(),
}));

jest.mock("antd", () => ({
  ...jest.requireActual("antd"),
  Row: ({ children }: any) => <div data-testid="ant-row">{children}</div>,
  Col: ({ span, children }: { span: number; children: React.ReactNode }) => (
    <div data-testid="ant-col" data-span={span}>
      {children}
    </div>
  ),
  Spin: jest.fn(() => <div data-testid="spin-loader" />),
  Tooltip: ({ title, children }: { title: string; children: React.ReactNode }) => (
    <div data-testid="tooltip">
      {children}
      <div>{title}</div>
    </div>
  ),
}));

jest.mock("@datoms/react-components/src/components/SkeletonLoader", () =>
  jest.fn((props) => <div data-testid="skeleton-loading">{JSON.stringify(props)}</div>)
);

const getMergedUpdatedConfig = require("./utils/logic").getMergedUpdatedConfig;
const modal = require("../CustomizeModal");

describe("ConfigurableSummary Component", () => {
  const defaultConfig = {
    title: "Summary Title",
    pipeline: true,
  };

  const mockData = [
    { id: 1, value: 100, unit: "USD", text: "Revenue", description: "Total revenue" },
    { id: 2, value: 50, unit: "USD", text: "Profit", description: "Net profit" },
  ];

  getMergedUpdatedConfig.mockImplementation((customConfig: any) => {
    return { ...customConfig, additionalKey: "value" };
  });

  it("renders the summary header with title", () => {
    render(<ConfigurableSummary config={defaultConfig} data={mockData} loading={false} />);
    expect(screen.getByText("Summary Title")).toBeInTheDocument();
  });

  it("renders skeleton loader when loading is true", () => {
    render(<ConfigurableSummary config={defaultConfig} data={mockData} loading={true} />);
    const Spin = require("antd").Spin;
    expect(Spin).toHaveBeenCalled();
  });

  it("displays custom text for 'No Data' case", () => {
    const customText = "No items found";
    render(<ConfigurableSummary config={{ ...defaultConfig, noDataText: customText }} data={[]} loading={false} />);
    expect(screen.getByText(customText)).toBeInTheDocument();
  });

  it("displays default 'No data available' message when no custom text is provided", () => {
    render(<ConfigurableSummary config={defaultConfig} data={[]} loading={false} />);
    expect(screen.getByText(/no data available/i)).toBeInTheDocument();
  });

  it("renders the correct text and value for each summary item", () => {
    render(<ConfigurableSummary config={{ ...defaultConfig, info: false }} data={mockData} loading={false} />);
    mockData.forEach((item) => {
      expect(screen.getByText(item.text)).toBeInTheDocument();
      expect(screen.getByText(`${item.value} ${item.unit}`)).toBeInTheDocument();
    });
  });

  it("displays tooltip/description when config.info is true and item has description", () => {
    render(<ConfigurableSummary config={{ ...defaultConfig, info: true }} data={mockData} loading={false} />);
    mockData.forEach((item) => {
      expect(screen.getByText(item.description)).toBeInTheDocument();
    });
  });

  it("does not display tooltip/description when config.info is false", () => {
    render(<ConfigurableSummary config={{ ...defaultConfig, info: false }} data={mockData} loading={false} />);
    mockData.forEach((item) => {
      expect(screen.queryByText(item.description)).not.toBeInTheDocument();
    });
  });

  it("does not render tooltip if description is missing even when config.info is true", () => {
    const dataWithoutDescription = [
      { id: 1, value: 123, text: "Users" },
      { id: 2, value: 456, text: "Sessions" },
    ];
    render(<ConfigurableSummary config={{ ...defaultConfig, info: true }} data={dataWithoutDescription} loading={false} />);
    expect(screen.queryByTestId("tooltip")).not.toBeInTheDocument();
  });

  it("calls getMergedUpdatedConfig with correct arguments", () => {
    render(<ConfigurableSummary config={defaultConfig} data={mockData} loading={false} />);
    expect(getMergedUpdatedConfig).toHaveBeenCalledWith(defaultConfig);
  });

  it("renders the customize modal", () => {
    render(<ConfigurableSummary config={defaultConfig} data={mockData} loading={false} />);
    expect(screen.getByTestId("customize-modal")).toBeInTheDocument();
  });

  it("renders the customize modal with correct props", () => {
    render(<ConfigurableSummary config={defaultConfig} data={mockData} loading={false} />);
    expect(modal).toHaveBeenCalledWith(
      expect.objectContaining({
        title: "Customize Summary",
        visible: false,
        modalLoading: false,
        totalColumns: mockData.length,
        visibleColumns: expect.any(Array),
        hiddenColumns: expect.any(Array),
        onClose: expect.any(Function),
        onOk: expect.any(Function),
        onReset: expect.any(Function),
        setHiddenColumns: expect.any(Function),
        setVisibleColumns: expect.any(Function),
      }),
      {},
    );
  });
});