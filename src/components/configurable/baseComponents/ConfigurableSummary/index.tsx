import React, { useEffect, useState } from "react";
import { Col, Divider, message, Row, Spin, Tooltip } from "antd";
import InfoCircleOutlined from "@ant-design/icons/InfoCircleOutlined";
import { getMergedUpdatedConfig } from "./utils/logic";
import IconsLibrary from "../../../../containers/icons/IconLibrary";
import CustomizeModal from "../CustomizeModal";
import SkeletonLoader from "@datoms/react-components/src/components/SkeletonLoader";
import { setPreferences } from "@datoms/js-sdk";
import { useGlobalContext } from "../../../../store/globalStore";
import "./styles.less";

type Config = {
  icon?: string;
  title?: string;
  info?: boolean;
  pipeline?: boolean;
  noDataText?: string;
  maxItemInRow?: number;
  bordered?: boolean;
  borderColor?: string;
  backgroundColor?: string;
};

type Props = {
  config: Config;
  data: any[];
  loading: boolean;
  orgPreferenceKey?: string;
  preferenceKeys?: string[];
};

export const reorderAndFilterData = (
  data: any[],
  preferences: any,
  preferenceKeys: any[],
) => {
  const existingPreference = preferences?.["page_preferences"] || {};
  const prefferedSummmary =
    existingPreference?.[preferenceKeys?.[0]]?.[preferenceKeys?.[1]]?.summary;
  if (prefferedSummmary?.length) {
    const uncheckedItems: any = [];
    const finalData = data.filter((item: any) => {
      const findResult = prefferedSummmary.find(
        (prefItem: any) => item.key === prefItem.key,
      );
      console.log("findResult", item.key, findResult);
      if (findResult) {
        item.order = findResult.order || 0;
        if (findResult.checked === false) uncheckedItems.push(item);
        return findResult.checked !== false;
      }
      item.order = 0;
      if (item.checked === false) uncheckedItems.push(item);
      return item.checked !== false;
    });
    return {
      finalData: finalData.sort((a, b) => {
        return a.order - b.order;
      }),
      uncheckedItems,
    };
  } else {
    return {
      finalData: data?.filter((item: any) => item.checked !== false),
      uncheckedItems: data?.filter((item: any) => item.checked === false),
    };
  }
};

const ConfigurableSummary = (props: Props) => {
  const {
    client_id,
    user_id,
    application_id,
    currentUserPreferences,
    setCurrentUserPreferences,
  } = useGlobalContext();
  let { config, data, loading, preferenceKeys }: any = props;
  const [visible, setVisible] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);
  const [visibleColumns, setVisibleColumns] = useState<any>(undefined);
  const [hiddenColumns, setHiddenColumns] = useState<any>([]);
  const updatedConfig: Config = getMergedUpdatedConfig(config);
  const isMobileScreen = window.innerWidth <= 768;

  useEffect(() => {
    updatePrefferedSummary();
  }, [currentUserPreferences]);

  const saveSummaryPreference = async () => {
    const summary = [
      ...visibleColumns.map((item: any, index: number) => ({
        ...item,
        checked: true,
        order: index + 1,
        renderTitle: undefined,
      })),
      ...hiddenColumns.map((item: any) => ({
        ...item,
        checked: false,
        renderTitle: undefined,
      })),
    ];
    const existingPreference =
      currentUserPreferences?.["page_preferences"] || {};
    const pagePreferences = {
      ...existingPreference,
      [preferenceKeys[0]]: {
        ...(existingPreference[preferenceKeys[0]] || {}),
        [preferenceKeys[1]]: {
          ...(existingPreference[preferenceKeys[0]]?.[preferenceKeys[1]] || {}),
          summary,
        },
      },
    };
    const preferences = {
      page_preferences: pagePreferences,
    };
    console.log("preferences -->", preferences);
    setModalLoading(true);
    const response = await setPreferences(
      user_id,
      client_id,
      application_id,
      preferences,
    );
    setModalLoading(false);
    if (response.status === "success") {
      message.success("Summary preference saved successfully!");
      setCurrentUserPreferences(preferences);
      setVisible(false);
    }
  };

  const updatePrefferedSummary = () => {
    const { finalData, uncheckedItems } = reorderAndFilterData(
      data,
      currentUserPreferences,
      preferenceKeys,
    );
    setVisibleColumns(finalData);
    setHiddenColumns(uncheckedItems);
  };

  const onReset = () => {
    setVisibleColumns(data.filter((item: any) => item.checked !== false));
    setHiddenColumns(data.filter((item: any) => item.checked === false));
  };

  if (!visibleColumns) {
    return (
      <div data-testid="skeleton-loading-container">
        <SkeletonLoader active={true} />
      </div>
    );
  }

  const maxItemsInRow = Math.min(
    updatedConfig?.maxItemInRow || 6,
    visibleColumns.length,
  );
  const colSpan = isMobileScreen ? 12 : Math.floor(24 / maxItemsInRow);

  const groupedData = visibleColumns.reduce(
    (acc: any, item: any, index: number) => {
      const rowIndex = Math.floor(index / maxItemsInRow);
      if (!acc[rowIndex]) acc[rowIndex] = [];
      acc[rowIndex].push(item);
      return acc;
    },
    [] as any[][],
  );

  return (
    <div
      id="configurable_summary"
      style={{
        border:
          updatedConfig?.bordered === false
            ? "none"
            : `1px solid ${updatedConfig?.borderColor}`,
        padding: updatedConfig?.bordered === false ? "0" : `16px`,
        backgroundColor: updatedConfig?.backgroundColor,
      }}
    >
      <div className="summary-header">
        {updatedConfig?.icon &&
          React.createElement(IconsLibrary[updatedConfig.icon])}
        <h2>{updatedConfig?.title}</h2>
        {preferenceKeys?.length > 0 && (
          <img
            src={IconsLibrary.settings}
            alt="settings"
            style={{ marginLeft: 9, cursor: "pointer" }}
            onClick={() => setVisible(true)}
          />
        )}
      </div>
      {!data?.length && (
        <div className="no-data">
          {updatedConfig?.noDataText ?? "No data available"}
        </div>
      )}
      {groupedData.map((row: Record<string, any>, rowIndex: number) => (
        <Row gutter={isMobileScreen ? [0, 16] : 16} key={rowIndex}>
          {row.map((item: Record<string, any>, index: number) => (
            <Col key={item.id} span={colSpan}>
              <div className="summary-item">
                <div className="summary-item-value">
                  {loading ? (
                    <Spin size="small" />
                  ) : (
                    `${item.value} ${typeof item.unit === "string" && item.value !== "-" ? item.unit : ""}`
                  )}
                </div>
                <div className="summary-item-text">
                  {item.text}
                  {config?.info && item.description && (
                    <Tooltip title={item.description}>
                      <InfoCircleOutlined />
                    </Tooltip>
                  )}
                </div>
              </div>
              {updatedConfig?.pipeline &&
                index !== 0 &&
                index % (24 / colSpan) !== 0 && (
                  <Divider type="vertical" className="summary-divider" />
                )}
            </Col>
          ))}
        </Row>
      ))}

      <CustomizeModal
        title="Customize Summary"
        visible={visible}
        modalLoading={modalLoading}
        totalColumns={data?.length || 0}
        visibleColumns={visibleColumns || []}
        hiddenColumns={hiddenColumns}
        onClose={() => {
          updatePrefferedSummary();
          setVisible(false);
          setModalLoading(false);
        }}
        onOk={saveSummaryPreference}
        onReset={onReset}
        setHiddenColumns={setHiddenColumns}
        setVisibleColumns={setVisibleColumns}
      />
    </div>
  );
};

export default ConfigurableSummary;
