#configurable_table {
	// margin: 20px 24px;
	margin-top: 40px;

	.configurable-table-wrapper {
		.ant-pagination-item{
			margin-right: 8px;
		}
		.ant-table-sticky-scroll {
			opacity: 0;
			height: 0;
			overflow: hidden;
		}
		.ant-table {
			// Enable this to have custom onHover color but the fixed left column will misbehave
			/*.ant-table-tbody>tr:hover>td {
				background: unset !important;
			}
			.ant-table-thead>tr:hover>th {
				background: unset !important;
			}*/

			.ant-table-thead>tr>th {
				background: #f7f7f7 !important;
			}

			.table-row-even {
				background-color: #fcfcfc !important;
			}

			.table-row-odd {
				background-color: #ffffff;
			}

			.last-row-highlight td {
				background-color: rgb(255, 208, 158);
				font-weight: 600;
			}

			.ant-table-summary td{
				background-color: rgb(255, 208, 158);
				font-weight: 600;
			}

			.searchable-column{
				// display: flex;
				// flex-direction: column;
				// align-items: flex-end;
			}
		}
	}

	.table-section-title{
		font-size: 15px;
		font-weight: 700;
		color: #4f4f4f;
		margin-bottom: 16px;
	}

	.table-options {
		margin-left: 10px;
		top: 40px;
		position: relative;
		margin-top: -40px;
		gap: 8px;
		display: flex;
		flex-direction: column;
		z-index: 2;
		color: #4f4f4f !important;

		.bulk-selection-option {
			display: flex;
			align-items: center;
			font-size: 12px !important;
			gap: 20px;

			.device-selected {
				.device-selected-value {
					border: 1px solid;
					padding: 0px 6px;
					border-radius: 50%;
					margin-right: 10px;
					color: #f58740;
				}
			}

			.show-checked-switch-container {
				display: flex;
				align-items: center;
				color: #f58740;

				.show-checked-switch {
					margin-right: 5px;
				}
			}

			.deselect-all-btn {
				cursor: pointer;
				color: #f58740;
				display: flex;
				align-items: center;

				.deselect-icon {
					margin-right: 5px;
					color: #f58740;
					font-size: 16px;
				}
			}
		}
	}
}

.react-resizable-handle {
	position: absolute;
	right: 0;
	bottom: 0;
	cursor: col-resize;
	width: 10px;
	height: 100%;
  }
