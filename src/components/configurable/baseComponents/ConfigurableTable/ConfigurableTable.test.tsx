import React from "react";
import {
  render,
  screen,
  waitFor,
  cleanup,
  act,
  fireEvent,
  within,
} from "@testing-library/react";
import ConfigurableTable from "./index";
import * as XLSX from "xlsx";

const mockPreferences = {
  page_preferences: {
    mockKey1: {
      mockKey2: {
        columns: [
          { title: "Column 1", dataIndex: "col1", checked: true },
          { title: "Column 2", dataIndex: "col2", checked: true },
          { title: "Column 3", dataIndex: "col3", checked: false },
        ],
        fixed_column_count: 2,
      },
    },
  },
};

// Mocking Ant Design components
jest.mock("antd", () => ({
  ...jest.requireActual("antd"),

  Skeleton: jest.fn(({ active }: { active?: boolean }) => (
    <div role="progressbar" data-active={active ?? true}>
      Skeleton
    </div>
  )),

  Table: jest.fn(
    ({
      components,
      columns,
      ...props
    }: {
      components?: any;
      columns?: any[];
    }) => {
      return (
        <div data-testid="table-wrapper" {...props}>
          {columns?.map((col) => (
            <div key={col.dataIndex} data-testid={`header-${col.dataIndex}`}>
              {/* Render ResizableTitle if resizable */}
              {components?.header?.cell ? (
                <components.header.cell
                  data-testid={`resizableHeader-${col.dataIndex}`}
                  {...(col.onHeaderCell ? col.onHeaderCell(col) : {})}
                >
                  {col.title}
                </components.header.cell>
              ) : (
                col.title // Render plain title if not resizable
              )}
            </div>
          ))}
        </div>
      );
    },
  ),

  Button: jest.fn(
    ({
      onClick,
      children,
      ...props
    }: {
      onClick: () => void;
      children: React.ReactNode;
    }) => (
      <button onClick={onClick} {...props} data-testid="button">
        {children}
      </button>
    ),
  ),

  Dropdown: jest.fn(
    ({
      menu,
      children,
    }: {
      menu: { items?: any[] };
      children: React.ReactNode;
    }) => {
      const [visible, setVisible] = React.useState(false);

      const handleClick = () => {
        setVisible((prev) => !prev); // Toggle visibility on click
      };

      return (
        <div data-testid="dropdown">
          <div data-testid="dropdown-trigger" onClick={handleClick}>
            {children}
          </div>
          {visible && (
            <div data-testid="dropdown-menu">
              {menu?.items?.map((item) => (
                <div key={item.key} data-testid={`dropdown-item-${item.key}`}>
                  {item.label}
                </div>
              ))}
            </div>
          )}
        </div>
      );
    },
  ),

  Select: Object.assign(
    jest.fn(
      ({
        value,
        onChange,
        children,
        options,
      }: {
        value: any;
        onChange: (value: string) => void;
        children: React.ReactNode;
        options: any[];
      }) => (
        <select
          value={value}
          data-testid="select"
          onChange={(e) => onChange(e.target.value)}
        >
          {children}
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      ),
    ),
    {
      Option: jest.fn(
        ({ value, children }: { value: string; children: React.ReactNode }) => (
          <option value={value}>{children}</option>
        ),
      ),
    },
  ),

  Checkbox: jest.fn(({ onChange, checked, children }) => (
    <div data-testid="checkbox">
      <input type="checkbox" checked={checked} onChange={onChange} />
      {children}
    </div>
  )),

  Switch: jest.fn(({ onChange, checked }) => (
    <div data-testid="switch">
      <input
        type="checkbox"
        checked={checked}
        onChange={(e) => {
          onChange(e.target.checked);
        }}
      />
    </div>
  )),

  Input: jest.fn(({ onChange, placeholder }) => (
    <input
      type="text"
      placeholder={placeholder}
      onChange={(e) => onChange(e.target.value)}
      data-testid="input"
    />
  )),

  Modal: jest.fn(
    ({
      open,
      onOk,
      onCancel,
      children,
    }: {
      open: boolean;
      onOk: () => void;
      onCancel: () => void;
      children: React.ReactNode;
    }) =>
      open ? (
        <div data-testid="custom-modal">
          {children}
          <button onClick={onOk}>Apply</button>
          <button onClick={onCancel}>Cancel</button>
        </div>
      ) : null,
  ),

  message: {
    error: jest.fn(),
    success: jest.fn(),
  },
}));

jest.mock("xlsx", () => ({
  utils: {
    book_new: jest.fn(() => ({})), // Mocking workbook creation
    aoa_to_sheet: jest.fn(() => ({})), // Mocking worksheet object
    book_append_sheet: jest.fn(), // Mocking appending sheets
  },
  writeFile: jest.fn(), // Mocking writing to file
}));

jest.mock("../../../../store/globalStore", () => ({
  useGlobalContext: jest.fn(() => ({
    client_id: "mock-client-id",
    user_id: "mock-user-id",
    application_id: "mock-application-id",
    currentUserPreferences: mockPreferences,
    setCurrentUserPreferences: jest.fn(),
  })),
}));

jest.mock("@datoms/js-sdk", () => ({
  setPreferences: jest.fn(),
}));

// // Mocking CommonModal
// jest.mock("@datoms/react-components/src/components/CommonModal", () => {
//   return jest.fn(({ isVisible, onOk, onCancel, customBody }: any) => {
//     return isVisible ? (
//       <div data-testid="custom-modal">
//         {customBody}
//         <button onClick={onOk}>Apply</button>
//         <button onClick={onCancel}>Cancel</button>
//       </div>
//     ) : null;
//   });
// });

// Mocking ReorderableList
jest.mock("@datoms/react-components/src/components/ReorderableList", () =>
  jest.fn(({ items, setItems }) => (
    <div data-testid="reorderable-list">
      {items.map((item: any) => (
        <div key={item.dataIndex} data-testid={`column-${item.dataIndex}`}>
          {item.renderTitle}
        </div>
      ))}
    </div>
  )),
);

jest.mock("@datoms/react-components/src/components/HeirarchyPopover", () => ({
  HeirarchyPopover: jest.fn(({ children }) => (
    <div data-testid="heirarchy-popover">{children}</div>
  )),
}));

jest.mock("@datoms/react-components/src/components/AntTooltip", () => ({
  AntTooltip: jest.fn(({ children }) => (
    <div data-testid="ant-tooltip">{children}</div>
  )),
}));

// jest.mock("@datoms/react-components/src/components/AntSwitch", () => ({
//   AntSwitch: jest.fn(({ checked, onChange, children }) => (
//     <div data-testid="switch">
//       <input
//         type="checkbox"
//         checked={checked}
//         onChange={(e) => {
//           onChange(e.target.checked);
//         }}
//       />
//       {children}
//     </div>
//   )),
// }));

jest.mock("@datoms/react-components/src/components/AntMenu", () => ({
  AntMenu: jest.fn(({ children }) => (
    <div data-testid="ant-menu">{children}</div>
  )),
}));

jest.mock("@datoms/react-components/src/components/AntMenuItem", () => ({
  AntMenuItem: jest.fn(({ children }) => (
    <div data-testid="ant-menu-item">{children}</div>
  )),
}));

jest.mock("@datoms/react-components/src/components/AntDropdown", () => ({
  AntDropdown: jest.fn(({ children }) => (
    <div data-testid="ant-dropdown">{children}</div>
  )),
}));

// jest.mock("@ant-design/icons", () => ({
//   DownOutlined: jest.fn(({ children }) => (
//     <div data-testid="down-outlined">{children}</div>
//   )),
//   DownloadOutlined: jest.fn(({ children }) => (
//     <div data-testid="download-outlined">{children}</div>
//   )),
//   MinusSquareOutlined: jest.fn(({ children }) => (
//     <div data-testid="minus-square-outlined">{children}</div>
//   )),
//   MoreOutlined: jest.fn(({ children }) => (
//     <div data-testid="more-outlined">{children}</div>
//   )),
// }));

jest.mock("react-router-dom", () => ({
  Link: jest.fn(({ children }) => <div data-testid="link">{children}</div>),
}));

jest.mock("react-resizable", () => ({
  Resizable: jest.fn(({ children }) => (
    <div data-testid="resizable">
      {children}
      <div data-testid="resize-handle" />
    </div>
  )),
}));

jest.mock("./utils/logic", () => {
  return {
    ...jest.requireActual("./utils/logic"),
    getMergedUpdatedConfig: jest.fn(),
  };
});

const getMergedUpdatedConfig = require("./utils/logic").getMergedUpdatedConfig;
getMergedUpdatedConfig.mockImplementation((config: any) => config);

const mockSetPreferences = require("@datoms/js-sdk").setPreferences;
mockSetPreferences.mockResolvedValue({ status: "success" });

// const { Table } = require("antd");

describe("ConfigurableTable - Skeleton and Loading States", () => {
  it("renders Skeleton when loading is true", () => {
    render(
      <ConfigurableTable config={{}} data={[]} loading={true} props={{}} />,
    );

    expect(screen.getByRole("progressbar")).toBeInTheDocument();
    expect(screen.queryByTestId("action-buttons")).not.toBeInTheDocument();
    expect(screen.queryByTestId("table-wrapper")).not.toBeInTheDocument();
    expect(screen.queryByTestId("custom-modal")).not.toBeInTheDocument();
  });

  it("does not render Skeleton and renders other elements when loading is false and tableConfigWithData is valid", () => {
    const columns = [{ title: "Col1", dataIndex: "col1" }];
    const data = [{ col1: "Value1" }];
    render(
      <ConfigurableTable
        config={{ tableProps: { columns } }}
        data={data}
        loading={false}
        props={{}}
      />,
    );

    expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();
    expect(screen.getByTestId("action-buttons")).toBeInTheDocument();
    expect(screen.getByTestId("table-wrapper")).toBeInTheDocument();
  });
});

describe("ConfigurableTable - Default Action Buttons", () => {
  const mockColumns = [
    { title: "Column 1", pdf_title: "Column 1", dataIndex: "col1" },
    { title: "Column 2", pdf_title: "Column 2", dataIndex: "col2" },
  ];
  const mockData = [{ col1: "Value1", col2: "Value2" }];
  const defaultProps = {
    config: {
      disableCustomization: false,
      downloadable: true,
      resizable: true,
      tableProps: { columns: mockColumns },
    },
    data: mockData,
    loading: false,
    props: {},
  };

  it("renders 'Customize Table' button when disableCustomization is false", () => {
    render(<ConfigurableTable {...defaultProps} />);
    expect(screen.getByText("Customize Table")).toBeInTheDocument();
  });

  it("does not render 'Customize Table' button when disableCustomization is true", () => {
    render(
      <ConfigurableTable
        {...{
          ...defaultProps,
          config: { ...defaultProps.config, disableCustomization: true },
        }}
      />,
    );
    expect(screen.queryByText("Customize Table")).not.toBeInTheDocument();
  });

  it("opens the modal when 'Customize Table' button is clicked", () => {
    render(<ConfigurableTable {...defaultProps} />);
    const button = screen.getByText("Customize Table");
    fireEvent.click(button);
    expect(screen.getByTestId("custom-modal")).toBeInTheDocument();
  });

  it("does not render Table-view dropdown when resizable is false", () => {
    render(
      <ConfigurableTable
        {...{
          ...defaultProps,
          config: { ...defaultProps.config, resizable: false },
        }}
      />,
    );
    expect(screen.queryByTestId("dropdown-trigger")).not.toBeInTheDocument();
  });

  it("does not render 'Download' button when downloadable is false", () => {
    render(
      <ConfigurableTable
        {...{
          ...defaultProps,
          config: { ...defaultProps.config, downloadable: false },
        }}
      />,
    );
    expect(screen.queryByText("Download")).not.toBeInTheDocument();
  });

  it("renders and opens the table view dropdown on interaction", () => {
    render(<ConfigurableTable {...defaultProps} />);
    const dropdownTrigger = screen.getByTestId("dropdown-trigger");
    expect(dropdownTrigger).toBeInTheDocument();

    fireEvent.click(dropdownTrigger);
    const dropdownMenu = screen.getByTestId("dropdown-menu");
    dropdownMenu.style.display = "block";

    expect(dropdownMenu).toHaveTextContent("Default View");
    expect(dropdownMenu).toHaveTextContent("Compact View");
    expect(dropdownMenu).toHaveTextContent("Comfortable View");
  });

  it("changes table size when a dropdown option is clicked", () => {
    const mockedTable = require("antd").Table;

    render(<ConfigurableTable {...defaultProps} />);

    // Open the dropdown menu
    const dropdownTrigger = screen.getByTestId("dropdown-trigger");
    fireEvent.click(dropdownTrigger);

    // Click "Default View" and check the size
    fireEvent.click(screen.getByText("Default View"));
    expect(
      mockedTable.mock.calls[mockedTable.mock.calls.length - 1][0].size,
    ).toBe("middle");

    // Click "Compact View" and check the size
    fireEvent.click(screen.getByText("Compact View"));
    expect(
      mockedTable.mock.calls[mockedTable.mock.calls.length - 1][0].size,
    ).toBe("small");

    // Click "Comfortable View" and check the size
    fireEvent.click(screen.getByText("Comfortable View"));
    expect(
      mockedTable.mock.calls[mockedTable.mock.calls.length - 1][0].size,
    ).toBe("large");
  });

  it("renders the download button and triggers XLSX functionality when clicked", () => {
    const { writeFile, utils } = XLSX;

    render(<ConfigurableTable {...defaultProps} />);
    const button = screen.getByText("Download");
    expect(button).toBeInTheDocument();

    fireEvent.click(button);

    expect(utils.book_new).toHaveBeenCalled();
    expect(utils.aoa_to_sheet).toHaveBeenCalledWith([
      ["Title: My Table"],
      [],
      ["Column 1", "Column 2"],
      ["", ""],
      ["Value1", "Value2"],
    ]);
    expect(utils.book_append_sheet).toHaveBeenCalled();
    expect(writeFile).toHaveBeenCalledWith(
      expect.any(Object),
      expect.stringMatching(/table_data_\d+\.xlsx/),
    );
  });
});

describe("ConfigurableTable - Table Functionality", () => {
  const mockColumns = [
    { title: "Column 1", pdf_title: "Column 1", dataIndex: "col1" },
    { title: "Column 2", pdf_title: "Column 2", dataIndex: "col2" },
  ];
  const mockData = [
    { key: 1, col1: "Value1", col2: "B" },
    { key: 2, col1: "Value2", col2: "A" },
  ];
  const defaultProps = {
    config: {
      tableProps: { columns: mockColumns, rowSelection: {} },
      preferenceKeys: ["mockKey1", "mockKey2"],
    },
    data: mockData,
    loading: false,
    props: {},
  };

  const defaultPropsWithoutCheckbox = {
    ...defaultProps,
    config: {
      ...defaultProps.config,
      tableProps: {
        ...defaultProps.config.tableProps,
        rowSelection: undefined,
      },
    },
  };

  let Table: any;

  beforeEach(() => {
    Table = require("antd").Table;
  });

  it("renders the table with correct rows and columns", () => {
    render(<ConfigurableTable {...defaultProps} />);

    const mockedTable = require("antd").Table;

    // Verify that the Table mock was called with the correct props
    expect(
      mockedTable.mock.calls[mockedTable.mock.calls.length - 1][0],
    ).toMatchObject({
      columns: mockColumns, // Correct columns passed
      dataSource: mockData, // Correct data passed
    });
  });

  it("renders 'No Data' message when data is empty", () => {
    const noDataConfig = {
      noData: {
        image: "mock-no-data.png",
        text: "No data available",
      },
    };

    render(
      <ConfigurableTable
        {...{
          ...defaultProps,
          data: [],
          config: { ...defaultProps.config, ...noDataConfig },
        }}
      />,
    );

    const mockedTable = require("antd").Table;

    // Get the last call to the Table mock
    const lastCall =
      mockedTable.mock.calls[mockedTable.mock.calls.length - 1][0];

    // Simulate the locale.emptyText rendering
    const { emptyText } = lastCall.locale;

    cleanup(); // Clean up the rendered component

    render(emptyText); // Render the JSX element

    expect(screen.getByText("No data available")).toBeInTheDocument();
    expect(screen.getByAltText("No Data")).toBeInTheDocument();
  });

  it("won't render the Table rows with 'checkbox' (Table of antd is called with 'null' rowSelection) if 'rowSelection' is set to 'undefined' in config", () => {
    render(<ConfigurableTable {...defaultPropsWithoutCheckbox} />);
    expect(Table.mock.calls[Table.mock.calls.length - 1][0].rowSelection).toBe(
      null,
    );
  });

  it("renders row selection checkboxes properly checked/unchecked (selectedRowKeys passed to Table)", async () => {
    render(<ConfigurableTable {...defaultProps} />);
    const rowSelectionProp =
      Table.mock.calls[Table.mock.calls.length - 1][0].rowSelection;
    console.log("row sels prop    ", rowSelectionProp);
    await act(async () => {
      await rowSelectionProp.onChange([1], [mockData[0]]); // updates the state (hence re-renders the table)
    });

    console.log("row sels props2   ", rowSelectionProp);

    // Asserting if the last call to the Table mock has selectedRowKeys set to [1]
    expect(
      Table.mock.calls[Table.mock.calls.length - 1][0].rowSelection
        .selectedRowKeys,
    ).toEqual([1]);
  });

  it("handles 'Select All' functionality", async () => {
    render(<ConfigurableTable {...defaultProps} />);
    const rowSelectionProp =
      Table.mock.calls[Table.mock.calls.length - 1][0].rowSelection;
    await act(async () => {
      rowSelectionProp.onChange(
        mockData.map((item) => item.key),
        mockData,
      ); // updates the state (hence re-renders the table)
    });

    expect(
      Table.mock.calls[Table.mock.calls.length - 1][0].rowSelection
        .selectedRowKeys,
    ).toEqual([1, 2]);
  });

  it("displays Selected Row Count when rows are selected", async () => {
    render(<ConfigurableTable {...defaultProps} />);

    const rowSelectionProp =
      Table.mock.calls[Table.mock.calls.length - 1][0].rowSelection;
    await act(async () => {
      rowSelectionProp.onChange([1], [mockData[0]]); // updates the state (hence re-renders the table)
    });

    expect(screen.getByTestId("selected-rows-count").textContent).toBe("1Rows Selected");

    await act(async () => {
      rowSelectionProp.onChange([1, 2], mockData); // updates the state (hence re-renders the table)
    });

    expect(screen.getByTestId("selected-rows-count").textContent).toBe("2Rows Selected");
  });

  it("calls the callback function with selected rows if bulk action button is clicked", async () => {
    const bulkAction = jest.fn();
    const bulkRowActionButtons = [
      {
        text: "Demo Button 1",
        key: "add_item",
        onClickFunction: bulkAction,
        type: "ghost",
        disabled: false,
      },
    ];

    render(
      <ConfigurableTable
        {...{
          ...defaultProps,
          config: { ...defaultProps.config, bulkRowActionButtons },
        }}
      />,
    );

    const rowSelectionProp =
      Table.mock.calls[Table.mock.calls.length - 1][0].rowSelection;

    // 1. When one row is selected
    await act(async () => {
      rowSelectionProp.onChange([1], [mockData[0]]); // selecting 1 row
    });

    const button = screen.getByText("Demo Button 1");
    fireEvent.click(button);

    expect(bulkAction).toHaveBeenCalledWith([mockData[0]]);

    // 2. When two rows are selected
    await act(async () => {
      rowSelectionProp.onChange([1, 2], mockData); // selecting 2 rows
    });

    fireEvent.click(button);

    expect(bulkAction).toHaveBeenCalledWith(mockData);
  });

  it("deselects all rows when 'Deselect All' is clicked", async () => {
    render(<ConfigurableTable {...defaultProps} />);

    const rowSelectionProp =
      Table.mock.calls[Table.mock.calls.length - 1][0].rowSelection;
    await act(async () => {
      rowSelectionProp.onChange([1, 2], mockData); // selects all rows (2 rows in the example)
    });

    // Verify bulk action buttons are rendered
    expect(screen.getByText("Deselect All")).toBeInTheDocument();

    // Simulate clicking "Deselect All"
    const deselectAllButton = screen.getByText("Deselect All");
    await act(async () => {
      fireEvent.click(deselectAllButton);
    });

    // Verify states are cleared
    expect(
      Table.mock.calls[Table.mock.calls.length - 1][0].rowSelection
        .selectedRowKeys,
    ).toEqual([]);

    // Verify bulk action buttons are hidden
    expect(screen.queryByText("Deselect All")).not.toBeInTheDocument();
    expect(screen.queryByText("Rows Selected")).not.toBeInTheDocument();
    expect(screen.queryByText("Show Selected Only")).not.toBeInTheDocument();
  });

  it("toggles 'Show Selected Only'", async () => {
    render(<ConfigurableTable {...defaultProps} />);

    const rowSelectionProp =
      Table.mock.calls[Table.mock.calls.length - 1][0].rowSelection;
    await act(async () => {
      rowSelectionProp.onChange([1], [mockData[0]]); // selects 1st row
    });

    const showSelectedOnlySwitch = within(
      screen.getByTestId("switch"),
    ).getByRole("checkbox");

    await act(async () => {
      fireEvent.click(showSelectedOnlySwitch); // Enable "Show Selected Only"
    });

    // Verify dataSource is filtered to selected rows
    expect(Table.mock.calls[Table.mock.calls.length - 1][0].dataSource).toEqual(
      [mockData[0]],
    );

    Table.mockClear(); // Clear the mock calls

    // Simulate toggling "Show Selected Only" off
    await act(async () => {
      fireEvent.click(showSelectedOnlySwitch);
    });

    // Verify dataSource is reset to original
    expect(Table.mock.calls[Table.mock.calls.length - 1][0].dataSource).toEqual(
      mockData,
    );
  });

  it("clicks 'Deselect All' when 'Show Selected Only' is enabled", async () => {
    render(<ConfigurableTable {...defaultProps} />);

    const rowSelectionProp =
      Table.mock.calls[Table.mock.calls.length - 1][0].rowSelection;
    await act(async () => {
      rowSelectionProp.onChange([1], [mockData[0]]); // selects 1st row
    });

    const showSelectedOnlySwitch = within(
      screen.getByTestId("switch"),
    ).getByRole("checkbox");
    fireEvent.click(showSelectedOnlySwitch); // Enables "Show Selected Only"

    // Clicks "Deselect All"
    const deselectAllButton = screen.getByText("Deselect All");
    fireEvent.click(deselectAllButton);

    // Verify dataSource is reset to original
    expect(Table.mock.calls[Table.mock.calls.length - 1][0].dataSource).toEqual(
      mockData,
    );
  });

  it("calls pageNumChange and pageSizeChange on pagination change", async () => {
    const pageNumChangeMock = jest.fn();
    const pageSizeChangeMock = jest.fn();

    const defaultPaginationProps = {
      ...defaultProps,
      config: {
        tableProps: {
          columns: [{ title: "Name", dataIndex: "name" }],
          pagination: {
            current: 2,
            pageSize: 10,
            total: 50,
          },
        },
        pageChangeActions: {
          pageNumChange: pageNumChangeMock,
          pageSizeChange: pageSizeChangeMock,
        },
      },
      data: Array.from({ length: 50 }, (_, i) => ({
        key: i + 1,
        name: `Row ${i + 1}`,
      })),
      loading: false,
    };

    render(<ConfigurableTable {...defaultPaginationProps} />);

    const paginationProp =
      Table.mock.calls[Table.mock.calls.length - 1][0].pagination;

    // Verify the initial pagination
    expect(paginationProp.current).toBe(1);
    expect(paginationProp.pageSize).toBe(10);

    await act(async () => {
      paginationProp.onChange(3, 15);
    });

    // Verify that the pagination was updated
    const paginationAfterChange =
      Table.mock.calls[Table.mock.calls.length - 1][0].pagination;
    expect(paginationAfterChange.current).toBe(3);
    expect(paginationAfterChange.pageSize).toBe(15);

    // Verify that pageNumChange and pageSizeChange were called with correct arguments
    expect(pageNumChangeMock).toHaveBeenCalledWith(3); // Page number
    expect(pageSizeChangeMock).toHaveBeenCalledWith(15); // Page size
  });

  it("updates column width and triggers setPreferences on resize", async () => {
    const { Table } = require("antd");
    render(
      <ConfigurableTable
        {...{
          ...defaultProps,
          config: { ...defaultProps.config, resizableColumns: true },
        }}
      />,
    );

    // Locate the resizable header
    const resizableHeader = screen.getByTestId("resizableHeader-col1");
    expect(resizableHeader).toBeInTheDocument();

    const headerResize = Table.mock.calls[
      Table.mock.calls.length - 1
    ][0].columns[0].onHeaderCell({});
    // Simulate the resize event
    await act(async () => {
      headerResize.onResize(null, { size: { width: 120 } });
    });

    const newHeaderResize = Table.mock.calls[
      Table.mock.calls.length - 1
    ][0].columns[0].onHeaderCell({});
    await act(async () => {
      newHeaderResize.onResizeStop(null, { size: { width: 120 } });
    });

    // Verify setPreferences was called with the updated column width
    expect(mockSetPreferences).toHaveBeenCalledWith(
      "mock-user-id",
      "mock-client-id",
      "mock-application-id",
      expect.objectContaining({
        page_preferences: expect.objectContaining({
          mockKey1: expect.objectContaining({
            mockKey2: expect.objectContaining({
              columns: expect.arrayContaining([
                expect.objectContaining({ title: "Column 1", width: 120 }),
              ]),
            }),
          }),
        }),
      }),
    );
  });
});

describe("ConfigurableTable - Modal Functionality", () => {
  const mockColumns = [
    {
      title: "Column 1",
      pdf_title: "Column 1",
      dataIndex: "col1",
      checked: true,
    },
    {
      title: "Column 2",
      pdf_title: "Column 2",
      dataIndex: "col2",
      checked: false,
    },
    {
      title: "Column 3",
      pdf_title: "Column 3",
      dataIndex: "col3",
      checked: true,
    },
    {
      title: "Column 4",
      pdf_title: "Column 4",
      dataIndex: "col4",
      checked: false,
    },
  ];

  const mockData = [
    {
      key: 1,
      col1: "Row 1 Col 1",
      col2: "Row 1 Col 2",
      col3: "Row 1 Col 3",
      col4: "Row 1 Col 4",
    },
    {
      key: 2,
      col1: "Row 2 Col 1",
      col2: "Row 2 Col 2",
      col3: "Row 2 Col 3",
      col4: "Row 2 Col 4",
    },
    {
      key: 3,
      col1: "Row 3 Col 1",
      col2: "Row 3 Col 2",
      col3: "Row 3 Col 3",
      col4: "Row 3 Col 4",
    },
    {
      key: 4,
      col1: "Row 4 Col 1",
      col2: "Row 4 Col 2",
      col3: "Row 4 Col 3",
      col4: "Row 4 Col 4",
    },
  ];

  const defaultProps = {
    config: {
      tableProps: { columns: mockColumns },
      preferenceKeys: ["mockKey1", "mockKey2"],
      defaultFixedCount: 1,
    },
    data: mockData,
    loading: false,
    props: {},
  };

  const mockedUseGlobalContext =
    require("../../../../store/globalStore").useGlobalContext;

  beforeEach(() => {
    mockedUseGlobalContext.mockReturnValue({
      client_id: "mock-client-id",
      user_id: "mock-user-id",
      application_id: "mock-application-id",
      currentUserPreferences: mockPreferences,
      setCurrentUserPreferences: jest.fn(),
    });
  });

  it("renders the correct content in the modal if preferences are set", () => {
    render(<ConfigurableTable {...defaultProps} />);

    // Open modal
    fireEvent.click(screen.getByText("Customize Table"));

    // Verify visible and hidden columns count
    const modal = screen.getByTestId("custom-modal");
    expect(
      within(modal).getByText("Visible columns - 2/4"),
    ).toBeInTheDocument();
    expect(within(modal).getByText("Hidden columns - 2/4")).toBeInTheDocument();
    const fixedColumnsDropdown = within(modal).getByTestId("select");
    expect(fixedColumnsDropdown).toHaveValue("2");

    // Verify visible columns are passed to ReorderableListCustom
    const reorderableListMock = require("@datoms/react-components/src/components/ReorderableList");
    expect(reorderableListMock).toHaveBeenCalledWith(
      expect.objectContaining({
        items: expect.arrayContaining([
          expect.objectContaining({ title: "Column 1", checked: true }),
          expect.objectContaining({ title: "Column 2", checked: true }),
        ]),
        setItems: expect.any(Function),
      }),
      {},
    );

    // Check hidden columns in the hidden section
    const hiddenColumnsSection = within(modal)
      .getByText(/Hidden columns/i)
      .closest("div");

    expect(hiddenColumnsSection).toBeInTheDocument();

    expect(
      within(hiddenColumnsSection!).getByText("Column 3"),
    ).toBeInTheDocument();
    expect(
      within(hiddenColumnsSection!).getByText("Column 4"),
    ).toBeInTheDocument();
  });

  it("renders the correct content in the modal if preferences are not set", () => {
    mockedUseGlobalContext.mockReturnValue({
      client_id: "mock-client-id",
      user_id: "mock-user-id",
      application_id: "mock-application-id",
      currentUserPreferences: {}, // No preferences
      setCurrentUserPreferences: jest.fn(),
    });

    render(<ConfigurableTable {...defaultProps} />);

    // Open modal
    fireEvent.click(screen.getByText("Customize Table"));

    // Verify visible and hidden columns count
    const modal = screen.getByTestId("custom-modal");
    expect(
      within(modal).getByText("Visible columns - 2/4"),
    ).toBeInTheDocument();
    expect(within(modal).getByText("Hidden columns - 2/4")).toBeInTheDocument();

    const fixedColumnsDropdown = within(modal).getByTestId("select");
    expect(fixedColumnsDropdown).toHaveValue("1");

    // Verify visible columns are passed to ReorderableListCustom
    const reorderableListMock = require("@datoms/react-components/src/components/ReorderableList");
    expect(reorderableListMock).toHaveBeenCalledWith(
      expect.objectContaining({
        items: expect.arrayContaining([
          expect.objectContaining({ title: "Column 1", checked: true }),
          expect.objectContaining({ title: "Column 3", checked: true }),
        ]),
        setItems: expect.any(Function),
      }),
      {},
    );

    // Check hidden columns in the hidden section
    const hiddenColumnsSection = within(modal)
      .getByText(/Hidden columns/i)
      .closest("div");
    expect(hiddenColumnsSection).toBeInTheDocument();

    expect(
      within(hiddenColumnsSection!).getByText("Column 2"),
    ).toBeInTheDocument();
    expect(
      within(hiddenColumnsSection!).getByText("Column 4"),
    ).toBeInTheDocument();
  });

  it("closes the modal when 'Cancel' button is clicked", () => {
    render(<ConfigurableTable {...defaultProps} />);

    // Open modal
    fireEvent.click(screen.getByText("Customize Table"));

    const cancelButton = screen.getByText("Cancel");
    fireEvent.click(cancelButton);

    // Verify modal is closed
    expect(screen.queryByTestId("custom-modal")).not.toBeInTheDocument();
  });

  it("saves updated fixed columns count when 'Apply' is clicked", async () => {
    const setPreferences = require("@datoms/js-sdk").setPreferences;

    render(<ConfigurableTable {...defaultProps} />);

    // Open modal
    fireEvent.click(screen.getByText("Customize Table"));

    // Simulate changing fixed column count
    const fixedColumnsDropdown = within(
      screen.getByTestId("custom-modal"),
    ).getByTestId("select");
    fireEvent.change(fixedColumnsDropdown, { target: { value: "3" } });

    // Click "Apply"
    fireEvent.click(screen.getByText("Apply"));

    // Verify correct preferences are passed to setPreferences
    expect(setPreferences).toHaveBeenCalledWith(
      "mock-user-id",
      "mock-client-id",
      "mock-application-id",
      expect.objectContaining({
        page_preferences: expect.objectContaining({
          mockKey1: expect.objectContaining({
            mockKey2: expect.objectContaining({
              columns: expect.any(Array),
              fixed_column_count: 3,
            }),
          }),
        }),
      }),
    );
  });

  it("handles column reordering when setItems is called", async () => {
    render(<ConfigurableTable {...defaultProps} />);

    // Open the modal
    fireEvent.click(screen.getByText("Customize Table"));

    const reorderableListMock = require("@datoms/react-components/src/components/ReorderableList");
    const lastCallArgs = reorderableListMock.mock.calls[0][0];

    // Simulate calling setItems with reordered columns
    const reorderedColumns = [
      { title: "Column 2", dataIndex: "col1", checked: true, order: 1 },
      { title: "Column 1", dataIndex: "col1", checked: true, order: 2 },
    ];
    act(() => {
      lastCallArgs.setItems(reorderedColumns);
    });

    // Verify preferences update when "Apply" is clicked
    fireEvent.click(screen.getByText("Apply"));
    expect(mockSetPreferences).toHaveBeenCalledWith(
      "mock-user-id",
      "mock-client-id",
      "mock-application-id",
      expect.objectContaining({
        page_preferences: expect.objectContaining({
          mockKey1: expect.objectContaining({
            mockKey2: expect.objectContaining({
              columns: expect.arrayContaining([
                expect.objectContaining({
                  title: "Column 2",
                  checked: true,
                  order: 1,
                }),
                expect.objectContaining({
                  title: "Column 1",
                  checked: true,
                  order: 2,
                }),
              ]),
            }),
          }),
        }),
      }),
    );
  });

  it("toggles columns between visible and hidden", async () => {
    render(<ConfigurableTable {...defaultProps} />);

    // Open modal
    fireEvent.click(screen.getByText("Customize Table"));

    const modal = screen.getByTestId("custom-modal");

    const column1Container = within(modal).getByText("Column 1").closest("div");
    expect(column1Container).toBeInTheDocument();

    // Find the input within the container div
    const visibleCheckbox = column1Container?.querySelector("input");
    expect(visibleCheckbox).toBeInTheDocument();

    // Simulate a click event on the checkbox
    fireEvent.click(visibleCheckbox!);

    // finding the hidden column container
    const column3Container = within(modal)
      .getByText("Column 3")
      .closest('div[data-testid="checkbox"]');
    const hiddenCheckbox = column3Container?.querySelector("input");

    expect(hiddenCheckbox).toBeInTheDocument();

    // Simulate a click event on the checkbox
    fireEvent.click(hiddenCheckbox!);

    // Click "Apply"
    fireEvent.click(screen.getByText("Apply"));

    // Verify preferences are updated with column visibility changes
    expect(mockSetPreferences).toHaveBeenCalledWith(
      "mock-user-id",
      "mock-client-id",
      "mock-application-id",
      expect.objectContaining({
        page_preferences: expect.objectContaining({
          mockKey1: expect.objectContaining({
            mockKey2: expect.objectContaining({
              columns: expect.arrayContaining([
                expect.objectContaining({ title: "Column 1", checked: false }),
                expect.objectContaining({ title: "Column 2", checked: true }),
                expect.objectContaining({ title: "Column 3", checked: true }),
              ]),
            }),
          }),
        }),
      }),
    );
  });

  it("restores default column settings when 'Reset' button is clicked", () => {
    render(<ConfigurableTable {...defaultProps} />);

    // Open modal
    fireEvent.click(screen.getByText("Customize Table"));

    const resetButton = screen.getByText("Reset to Default");
    fireEvent.click(resetButton);

    // Verify columns are reset to default
    expect(screen.getByText("Visible columns - 2/4")).toBeInTheDocument();
    expect(screen.queryByText("Hidden columns - 2/4")).toBeInTheDocument();

    const fixedColumnsDropdown = within(
      screen.getByTestId("custom-modal"),
    ).getByTestId("select");
    expect(fixedColumnsDropdown).toHaveValue("1");

    const visibleColumnsContainer = screen
      .getByText(/Visible columns/i)
      .closest("div");
    const hiddenColumnsContainer = screen
      .getByText(/Hidden columns/i)
      .closest("div");

    expect(
      within(visibleColumnsContainer!).getByText("Column 1"),
    ).toBeInTheDocument();
    expect(
      within(visibleColumnsContainer!).getByText("Column 3"),
    ).toBeInTheDocument();

    expect(
      within(hiddenColumnsContainer!).getByText("Column 2"),
    ).toBeInTheDocument();
    expect(
      within(hiddenColumnsContainer!).getByText("Column 4"),
    ).toBeInTheDocument();
  });

  it("error message is invoked when saving preferences fails", async () => {
    mockSetPreferences.mockResolvedValueOnce({
      status: "error",
      message: "Failed to save preferences",
    });

    render(<ConfigurableTable {...defaultProps} />);

    // Open modal
    fireEvent.click(screen.getByText("Customize Table"));

    // Click "Apply"
    fireEvent.click(screen.getByText("Apply"));

    const errorMessage = require("antd").message.error;

    // Verify error message is displayed
    await waitFor(() => {
      expect(errorMessage).toHaveBeenCalledTimes(1);
      expect(errorMessage).toHaveBeenCalledWith("Failed to save preferences");
    });
  });
});
