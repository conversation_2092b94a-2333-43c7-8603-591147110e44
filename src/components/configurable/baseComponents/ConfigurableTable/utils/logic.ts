import React from "react";
import deepmerge from "deepmerge";
import * as Babel from "@babel/standalone";
import DefaultConfig from "../configs/DefaultConfig.json";
import _cloneDeep from "lodash/cloneDeep";
import dependencies from "./dependencies";

interface Column {
  fixed: string;
  align: string;
  title?: string;
  pdf_title?: string;
  dataIndex: string;
  checked?: boolean;
  not_customizable?: boolean;
  order?: number;
}

/**
 * Checks if the given string is a valid function string.
 *
 * @param {string} str - The value to check.
 * @returns {boolean} - Returns `true` if the string is a valid function string, otherwise `false`.
 * @example
 * isFunctionString("function() { return 1; }"); // true
 * isFunctionString("Hello"); // false
 */
export const isFunctionString = (str: string): boolean =>
  typeof str === "string" && str.trim().startsWith("function");

/**
 * Parses a stringified function (including JSX) and returns an executable function.
 * @param {string} funcStr - The function string with JSX.
 * @returns {Function} - The restored executable function.
 */
export const parseFunctionFromString = (
  funcStr: string,
  componentDependencies: any = {},
): Function => {
  try {
    const wrappedCode = `(${funcStr})`; // wrap so it's parsed as expression
    const transpiled = Babel.transform(wrappedCode, {
      presets: ["react"],
    }).code;
    
    return new Function(
      "React",
      ...Object.keys(dependencies),
      ...Object.keys(componentDependencies),
      `return ${transpiled}`,
    )(
      React,
      ...Object.values(dependencies),
      ...Object.values(componentDependencies),
    );
  } catch (err) {
    console.error("Failed to parse function from string:", err);
    throw err;
  }
};

/**
 * Recursively traverses through an object to find and restore functions
 * represented as strings by evaluating them.
 *
 * @param {{ [key: string]: string }} obj - The object to traverse.
 * @returns {void} - This function modifies the input object in place.
 * @example
 * const config = { fn: "function() { return 42; }" };
 * traverse(config);
 */
export const traverse = (
  obj: Record<string, any>,
  componentDependencies: any = {},
): void => {
  for (const key in obj) {
    if (!obj.hasOwnProperty(key)) continue;

    const value = obj[key];
    if (typeof value === "object" && value !== null) {
      traverse(value, componentDependencies);
    } else if (isFunctionString(value)) {
      try {
        obj[key] = parseFunctionFromString(value, componentDependencies);
      } catch (error) {
        console.error(`Error restoring function for key "${key}":`, error);
      }
    }
  }
};

export const parseRenderConfig = (obj: [], componentDependencies: any = {}) => {
  obj.forEach((item: any) => {
    if (item.render) {
      traverse(item, componentDependencies);
    }
  });
  return obj;
};

/**
 * Restores all functions in a configuration object by traversing the object
 * and evaluating any function strings found.
 *
 * @param {any} config - The configuration object to restore functions in.
 * @returns {any} - The updated configuration object with restored functions.
 * @example
 * const config = { fn: "function() { return 42; }" };
 * const restoredConfig = restoreFunctions(config);
 */
export const restoreFunctions = (
  config: any,
  componentDependencies: any,
): any => {
  traverse(config, componentDependencies);
  return config;
};

/**
 * Custom merge function that merges two objects or arrays, giving priority
 * to the source object. If both are arrays, the source array replaces the target.
 * If both are objects, it uses deepmerge to merge them.
 *
 * @param {Object} target - The target object to merge.
 * @param {Object} source - The source object to merge into the target.
 * @returns {Object} - The merged object.
 * @example
 * const target = { a: 1, b: 2 };
 * const source = { b: 3, c: 4 };
 * const result = customMerge(target, source);
 */
const customMerge = (target: Object, source: Object): Object => {
  if (Array.isArray(target) && Array.isArray(source)) {
    return source;
  }

  if (typeof target === "object" && typeof source === "object") {
    return deepmerge(target, source, {
      arrayMerge: (destinationArray, sourceArray) => {
        return sourceArray;
      },
    });
  }

  return source;
};

/**
 * Merges the default configuration with a custom configuration and restores any functions
 * in the configuration that may be represented as strings. This function uses a deep merge
 * strategy where the custom configuration takes precedence over the default configuration.
 *
 * @param {any} customConfig - The custom configuration object to merge with the default configuration.
 *
 * @returns {any} - The merged configuration object, with custom configuration values
 *                  overriding the default configuration, and with functions restored.
 *
 * @example
 * import { getMergedUpdatedConfig } from "./path-to-utils";
 *
 * const customConfig = {
 *   tableProps: {
 *     columns: [
 *       { title: "Name", dataIndex: "name" },
 *       { title: "Age", dataIndex: "age" },
 *     ],
 *   },
 * };
 *
 * const updatedConfig = getMergedUpdatedConfig(customConfig);
 *
 * // Example output:
 * // {
 * //   tableProps: {
 * //     columns: [
 * //       { title: "Name", dataIndex: "name" },
 * //       { title: "Age", dataIndex: "age" },
 * //     ],
 * //     ...other default tableProps properties
 * //   }
 * // }
 *
 * @remarks
 * - This function relies on the `deepmerge` library to handle deep merging of objects.
 * - Arrays in the source configuration completely replace arrays in the target configuration.
 * - Any function strings in the configuration are converted to executable functions using `restoreFunctions`.
 */

export const getMergedUpdatedConfig = (
  customConfig: any,
  componentDependencies: any,
) => {
  let updatedConfig = deepmerge(DefaultConfig.tableProps, customConfig, {
    arrayMerge: (destinationArray, sourceArray) => {
      return sourceArray;
    },
    customMerge: (key) => {
      return customMerge;
    },
  });
  updatedConfig = restoreFunctions(updatedConfig, componentDependencies);
  return updatedConfig;
};

function filterOrgPrefColumns(orgPreference: any, tableColumns: any) {
  const reverseMap: any = {
    calculated_runhour: "runhour",
    no_of_runs: "total_dg_runs",
    fuel_consumption: "fuel_consumed",
    calculated_energy: "energy_generated",
    fuel_consumption_p_calculated_runhour: "fuel_consumption_per_hr",
    fuel_initial: "start_fuel",
    fuel_snapshot: "end_fuel",
    fuel_consumption_p_calculated_energy: "fuel_consumed_per_unit_kwh",
    load_percentage: "load_percentage",
    fuel_filled: "fuel_filled",
    fuel_theft: "fuel_drained",
    calculated_energy_p_fuel_consumption:
      "energy_generated_per_unit_litre_fuel_consumed",
  };

  return tableColumns.map((column: any) => {
    const key = reverseMap[column.key];
    if ((key && orgPreference[key]) || column.dataIndex === "time") {
      column.checked = true;
    } else {
      column.checked = false;
    }
    return column;
  });
}

const filterColumnsByCondition = (columns: any[], requireDownloadable = false) => {
  return columns
    .map(col => {
      if (!col.children?.length) return col;
      
      const filteredChildren = col.children.filter(
        (child) => child.checked !== false && 
                  (!requireDownloadable || child.downloadable !== false)
      );
      
      return {
        ...col,
        children: filteredChildren,
        // Set parent checked to true if any child is visible
        ...(filteredChildren.length > 0 && { checked: true })
      };
    })
    .filter(col => {
      if (col.children?.length === 0) return false;
      
      return col.checked !== false && 
            (!requireDownloadable || col.downloadable !== false);
    });
};

const sortByOrder = (a: any, b: any) => {
  const orderA = typeof a.order === 'number' ? a.order : 0;
  const orderB = typeof b.order === 'number' ? b.order : 0;
  return orderA - orderB;
};

export const getTotalColumns = (
  defaultColumns: any[],
  preferenceKeys: any,
  orgPreferenceKey: any,
  preferences: any,
  getDownloadColumn?: boolean,
  toDefault?: boolean,
): any => {

  const existingPreference = preferences?.["page_preferences"] || {};
  const preferredColumns = existingPreference[preferenceKeys?.[0]]?.[preferenceKeys?.[1]]?.columns;

  const hasGroupedColumns = defaultColumns.find(
    (item) => item.group_key && item.group_repeated,
  );

  const modalColumns = hasGroupedColumns ? transformColumns(_cloneDeep(defaultColumns)) : [];

  // Return default columns if requested
  if (toDefault) {
    return {
      tableColumns: defaultColumns.filter((col) => col.checked !== false),
      modalColumns: separateVisibleAndHiddenColumns(
        hasGroupedColumns ? modalColumns : defaultColumns,
      ),
    };
  }

  // Handle case with no preferred columns
  if (!preferredColumns || preferredColumns.length === 0) {
    // Check for organization preference
    if (
      orgPreferenceKey &&
      preferences?.reports?.[orgPreferenceKey]?.table &&
      Object.keys(preferences?.reports?.[orgPreferenceKey].table).length > 0
    ) {
      const finalColumns = filterOrgPrefColumns(
        preferences?.reports?.[orgPreferenceKey].table,
        defaultColumns,
      );
      
      return getDownloadColumn
        ? finalColumns.filter(
            (col: any) => col.checked !== false && col.downloadable !== false,
          )
        : {
            tableColumns: finalColumns.filter(
              (col: any) => col.checked !== false,
            ),
            modalColumns: separateVisibleAndHiddenColumns(finalColumns),
          };
    }

    // Use default columns with filtering
    const filteredColumns = filterColumnsByCondition(
      [...defaultColumns], 
      !!getDownloadColumn
    );

    return getDownloadColumn 
      ? filteredColumns 
      : {
          tableColumns: filteredColumns,
          modalColumns: separateVisibleAndHiddenColumns(
            hasGroupedColumns ? modalColumns : defaultColumns
          ),
        };
  }

  // Handle grouped columns with preferences
  if (hasGroupedColumns) {
    return getDownloadColumn
      ? reorderAndFilterColumns(defaultColumns, preferredColumns)
      : {
          tableColumns: reorderAndFilterColumns(
            defaultColumns,
            preferredColumns,
          ),
          modalColumns: separateVisibleAndHiddenColumns(
            mergeGroupedModalArray(preferredColumns, modalColumns),
          ),
        };
  }

  // Handle regular columns with preferences
  const preferredMap = new Map(
    preferredColumns.map((col) => [col.dataIndex, col]),
  );
  
  const mergedColumns = defaultColumns.map((defaultCol) => {
    const preferredCol = preferredMap.get(defaultCol.dataIndex);

    if (!preferredCol) return { ...defaultCol };

    const mergedColumn = {
      ...defaultCol,
      ...(preferredCol.order !== undefined && { order: preferredCol.order }),
      ...(preferredCol.checked !== undefined && { checked: preferredCol.checked }),
      ...(defaultCol.not_customizable && { checked: true }),
    };

    // Process children if they exist
    if (defaultCol.children?.length && preferredCol.children?.length) {
      const preferredChildrenMap = new Map(
        preferredCol.children.map((child) => [child.dataIndex, child]),
      );
      
      mergedColumn.children = defaultCol.children.map((defaultChild) => {
        const preferredChild = preferredChildrenMap.get(defaultChild.dataIndex);

        if (!preferredChild) return { ...defaultChild };

        return {
          ...defaultChild,
          ...(preferredChild.order !== undefined && { order: preferredChild.order }),
          ...(preferredChild.checked !== undefined && { checked: preferredChild.checked }),
          ...(defaultChild.not_customizable && { checked: true }),
        };
      });

      // Sort children by order without mutation
      mergedColumn.children = [...mergedColumn.children].sort(sortByOrder);

      // Set parent checked status based on children
      if (mergedColumn.children.some((child) => child.checked !== false)) {
        mergedColumn.checked = true;
      }
    }
    
    return mergedColumn;
  });

  // Sort columns by order
  const sortedColumns = [...mergedColumns].sort(sortByOrder);
  
  const filteredColumns = filterColumnsByCondition(sortedColumns, !!getDownloadColumn);
  
  return getDownloadColumn
    ? filteredColumns
    : {
        tableColumns: filteredColumns,
        modalColumns: separateVisibleAndHiddenColumns(sortedColumns),
      };
};

export function transformColumns(columns: Column[]): Column[] {
  const result: Column[] = [];
  const groupedKeys: Set<string> = new Set();

  columns.forEach((column: any, index: number) => {
    if (column.group_repeated && column.group_key) {
      if (!groupedKeys.has(column.group_key) && column.children) {
        groupedKeys.add(column.group_key);
        result.push(
          ...column.children.map((child: any, childIndex: number) => ({
            ...child,
            group_key: column.group_key,
            checked: child.checked !== undefined ? child.checked : true,
            order: index + childIndex + 1,
          })),
        );
      }
    } /* else if (column.children) {
      result.push(
        ...column.children.map((child: any) => ({
          ...child,
          order: index + 1,
        })),
      );
    } */ else {
      column.order = index + 1;
      result.push(column);
    }
  });

  return result;
}

export function reorderAndFilterColumns(columns: Column[], result: any): any {
  const groupOrderMap = new Map<string, number>();
  const finalColumns = columns.filter((col: any) => {
    if (col.children?.length) {
      col.children = reorderAndFilterColumns(col.children, result);
      if (col.children.length === 0) {
        col.checked = false;
      }
      col.order = col.children[0].order;
    } else if (col.commonColKey) {
      const findResult = result.find(
        (item: any) => item.commonColKey === col.commonColKey,
      );
      if (findResult) {
        col.checked = findResult.checked;
        col.order = findResult.order;
      }
    } else if (col.dataIndex) {
      const findResult = result.find(
        (item: any) => item.dataIndex === col.dataIndex,
      );
      if (findResult) {
        col.checked = findResult.checked;
        col.order = findResult.order;
      }
    }

    if (col.checked !== false && col.order !== undefined) {
      if (!groupOrderMap.has(col.group_key)) {
        groupOrderMap.set(col.group_key, col.order);
      } else {
        groupOrderMap.set(
          col.group_key,
          Math.min(groupOrderMap.get(col.group_key)!, col.order),
        );
      }
    }

    return col.checked !== false;
  });
  return finalColumns.sort((a: any, b: any) => {
    const groupOrderA = groupOrderMap.get(a.group_key) ?? Infinity;
    const groupOrderB = groupOrderMap.get(b.group_key) ?? Infinity;

    if (groupOrderA !== groupOrderB) {
      return groupOrderA - groupOrderB;
    }

    if (a.order !== undefined && b.order !== undefined) {
      return a.order - b.order;
    }

    if (a.order !== undefined) {
      return -1;
    }

    if (b.order !== undefined) {
      return 1;
    }

    return 0;
  });
}

const mergeGroupedModalArray = (preferredArray, defaultArray) => {
  const preferredMap = new Map();

  // Create a map for quick lookup from preferredArray
  preferredArray.forEach((item) => {
    preferredMap.set(item.dataIndex, {
      checked: item.checked,
      order: item.order,
    });
  });

  // Merge data from defaultArray, updating checked and order from preferredArray
  const finalArray = defaultArray.map((item) => {
    if (preferredMap.has(item.dataIndex)) {
      return {
        ...item,
        checked: preferredMap.get(item.dataIndex).checked,
        order: preferredMap.get(item.dataIndex).order,
      };
    }
    return item;
  });

  return finalArray;
};

/**
 * Separates modal columns into visible and hidden arrays without mutation
 *
 * @param modalColumns Array of column configurations
 * @returns Object containing visible and hidden column arrays
 */
export const separateVisibleAndHiddenColumns = (
  modalColumns: any[],
): {
  visibleColumns: any[];
  hiddenColumns: any[];
} => {
  const visibleColumns: any[] = [];
  const hiddenColumns: any[] = [];

  // Process each column in the modalColumns array
  modalColumns.forEach((column) => {
    // Handle columns with children
    if (column.children?.length) {
      // Separate children into visible and hidden
      const visibleChildren = column.children.filter(
        (child) => child.checked !== false,
      );
      const hiddenChildren = column.children.filter(
        (child) => child.checked === false,
      );

      // If there are visible children, add to visible columns
      if (visibleChildren.length > 0) {
        visibleColumns.push({
          ...column,
          children: [...visibleChildren],
          checked: true,
        });
      }

      // If there are hidden children, add to hidden columns
      if (hiddenChildren.length > 0) {
        hiddenColumns.push({
          ...column,
          children: [...hiddenChildren],
          checked: false,
        });
      }
    }
    // Handle columns without children
    else {
      if (column.checked !== false) {
        visibleColumns.push({ ...column });
      } else {
        hiddenColumns.push({ ...column });
      }
    }
  });

  return {
    visibleColumns,
    hiddenColumns,
  };
};
