
import { Switch } from "antd";
import { <PERSON> } from "react-router-dom";
import { DownOutlined } from "@ant-design/icons";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import AntPopover from "@datoms/react-components/src/components/AntPopover";
import HeirarchyPopover from "@datoms/react-components/src/components/HeirarchyPopover";
import AntMenu from "@datoms/react-components/src/components/AntMenu";
import AntMenuItem from "@datoms/react-components/src/components/AntMenuItem";
import AntDropdown from "@datoms/react-components/src/components/AntDropdown";
import StatusIcon from "@components/configurable/StatusIcon";
import { Switch } from "antd";
import { DownOutlined, WarningOutlined } from "@ant-design/icons";
import { Link } from "react-router-dom";
import Highlighter from "react-highlight-words";


const dependencies = {
    AntTooltip,
    AntPopover,
    AntMenu,
    Switch,
    AntMenuItem,
    AntDropdown,
    DownOutlined,
    Highlighter,
    Link,
    HeirarchyPopover,
    StatusIcon,
    WarningOutlined
};

export default dependencies;
