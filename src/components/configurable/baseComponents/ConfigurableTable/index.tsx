import React, { useState, useEffect } from "react";
import {
  Button,
  Dropdown,
  Input,
  message,
  Skeleton,
  Switch,
  Table,
  Tooltip,
  ConfigProvider,
  theme,
} from "antd";
import {
  ControlOutlined,
  DownloadOutlined,
  InfoCircleOutlined,
  MinusSquareOutlined,
  SearchOutlined,
  StopOutlined,
} from "@ant-design/icons";
import CustomizeModal from "../CustomizeModal";
import { setPreferences } from "@datoms/js-sdk";
import { getMergedUpdatedConfig, getTotalColumns, parseRenderConfig } from "./utils/logic";
import { useGlobalContext } from "../../../../store/globalStore";
import TableViews from "./icons/TableViews.svg";
import * as XLSX from "xlsx";
import { Resizable } from "react-resizable";

import "./style.less";

interface ConfigurableTableProps {
  config: any;
  tableStyle?: any;
  data: any[];
  loading: boolean;
  innerLoading?: boolean;
  updateColumnConfig?: (config: any) => void;
  orgPreferenceKey?: string;
  pagination?: {
    page_no?: number;
    page_size?: number;
    total?: number;
  };
  sortedInfo?: {
    order?: string;
    columnKey?: string;
  };
  updateSortedInfo?: (info: any) => void;
  downloadTableCallback?: () => void;
  dependencies?: any;
  hideLoading?: boolean;
}

interface Column {
  children: any[];
  width: any;
  title: string;
  dataIndex: string;
  order?: number;
  checked?: boolean;
  not_customizable?: boolean;
}

const ResizableTitle = ({
  onResize,
  onResizeStop,
  width,
  ...restProps
}: {
  onResize: (e: any, size: any) => void;
  onResizeStop: (e: any, size: any) => void;
  width: number;
}) => {
  const handleResize = (e: any, { size }: any) => {
    if (onResize) onResize(e, { size });
  };

  const handleResizeStop = (e: any, { size }: any) => {
    if (onResizeStop) onResizeStop(e, { size });
  };

  if (!width) {
    return <th {...restProps} />;
  }

  return (
    <Resizable
      width={width}
      height={0}
      onResize={handleResize}
      onResizeStop={handleResizeStop}
      handle={<span className="react-resizable-handle" />}
      draggableOpts={{ enableUserSelectHack: false }}
    >
      <th {...restProps} />
    </Resizable>
  );
};

const isValidGrouping = (items: any[]) => {
  let lastGroupKey: any = null;

  for (let i = 0; i < items.length; i++) {
    const currentGroupKey = items[i].group_key;

    if (currentGroupKey !== lastGroupKey) {
      if (
        items.slice(i + 1).some((item) => item.group_key === lastGroupKey)
      ) {
        return false;
      }
      lastGroupKey = currentGroupKey;
    }

    if (
      items[i].not_customizable === true &&
      items[i].fixed &&
      items[i].order &&
      items[i].order !== i + 1
    ) {
      return false;
    }
  }
  return true;
};

// [TODO] Table column preference logic to be shifted to main page level or not

const ConfigurableTable = (props: ConfigurableTableProps) => {
  const { config, data, loading, innerLoading, updateColumnConfig, orgPreferenceKey, downloadTableCallback, hideLoading } = props;
  const [visible, setVisible] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);
  const [tableColumns, setTableColumns] = useState<any[]>([]);
  const [visibleColumns, setVisibleColumns] = useState<Column[]>([]);
  const [hiddenColumns, setHiddenColumns] = useState<Column[]>([]);
  const [fixedColumnsCount, setFixedColumnsCount] = useState<number>(2);
  const [showSelectedOnly, setShowSelectedOnly] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [tableConfigWithData, setTableConfigWithData] =
    useState<any>(undefined);
  const [columnConfig, setColumnConfig] = useState<any>(undefined);
  const [paginationConfig, setPaginationConfig] = useState({
    current: props?.pagination?.page_no || 1,
    pageSize:
      props?.pagination?.page_size ||
      config?.tableProps?.pagination?.pageSize ||
      50,
  });
  const [bulkOptions, setBulkOptions] = useState<any>([]);
  const [downloadLoading, setDownloadLoading] = useState(false);

  const {
    client_id,
    user_id,
    application_id,
    currentUserPreferences,
    setCurrentUserPreferences,
  } = useGlobalContext();

  const isMobile = window.innerWidth <= 576;
  const isTablet = window.innerWidth < 834;

  const getDefaultFixedColCount = (toDefault: boolean): number => {
    const { preferenceKeys, defaultFixedCount } = config;
    if (toDefault) {
      return !isNaN(Number(defaultFixedCount)) ? Number(defaultFixedCount) : 2;
    }
    const existingPreference =
      currentUserPreferences?.["page_preferences"] || {};
    return !isNaN(
      parseInt(
        existingPreference[preferenceKeys?.[0]]?.[preferenceKeys?.[1]]?.[
          "fixed_column_count"
        ],
      ),
    )
      ? Number(
          existingPreference[preferenceKeys?.[0]]?.[preferenceKeys?.[1]]?.[
            "fixed_column_count"
          ],
        )
      : !isNaN(parseInt(defaultFixedCount))
        ? Number(defaultFixedCount)
        : 2;
  };

  const getVisibleAndHiddenColumns = (defaultColumns: any[]) => {
    const modifiedColumns: Column[] =
      (defaultColumns?.length &&
        defaultColumns
          .filter(
            (col) =>
              typeof col.pdf_title === "string" ||
              typeof col.title === "string",
          )
          .map((col, index) => ({
            ...col,
            pdf_title: col.pdf_title || col.title,
            title: col.pdf_title || col.title,
            dataIndex: col.dataIndex,
            order: col.order || index + 1,
            checked: col.checked !== undefined ? col.checked : true,
            not_customizable: col.not_customizable,
            children: col.children,
            width: col.width || 150,
          }))) ||
      [];

    const { tableColumns, modalColumns } = getTotalColumns(
      modifiedColumns,
      config.preferenceKeys,
      orgPreferenceKey,
      currentUserPreferences,
    );

    return {
      tableColumns,
      visibleColumns: modalColumns.visibleColumns,
      hiddenColumns: modalColumns.hiddenColumns,
    };
  };

  const resetColumns = (toDefault = true) => {
    let modifiedColumns = [];
    if (toDefault) {
      const defaultMergeConfig = getMergedUpdatedConfig(config?.tableProps, props.dependencies);
      modifiedColumns = defaultMergeConfig?.columns.map((col: any) => ({
        ...col,
        checked: col.checked !== undefined ? col.checked : true,
      }));
    } else {
      modifiedColumns = tableConfigWithData?.columns.map(
        (
          col: {
            pdf_title: any;
            title: any;
            dataIndex: any;
            not_customizable: any;
            children: any;
            checked: any;
          },
          index: number,
        ) => ({
          ...col,
          title: col.pdf_title || col.title,
          dataIndex: col.dataIndex,
          order: index + 1,
          checked: col.checked !== undefined ? col.checked : true,
          not_customizable: col.not_customizable,
          children: col.children,
        }),
      );
    }
    const { tableColumns, modalColumns } = getTotalColumns(
      modifiedColumns,
      config.preferenceKeys,
      orgPreferenceKey,
      currentUserPreferences,
      undefined,
      toDefault,
    );
    setTableColumns(tableColumns);
    setVisibleColumns(modalColumns.visibleColumns);
    setHiddenColumns(modalColumns.hiddenColumns);
    setFixedColumnsCount(getDefaultFixedColCount(toDefault));
  };

  const updatePreferenceValue = (value: Column[]) => {
    const existingPreference =
      currentUserPreferences?.["page_preferences"] || {};
    return {
      ...existingPreference,
      [config.preferenceKeys[0]]: {
        ...(existingPreference[config.preferenceKeys[0]] || {}),
        [config.preferenceKeys[1]]: {
          ...(existingPreference[config.preferenceKeys[0]]?.[config.preferenceKeys[1]] || {}),
          columns: value,
          fixed_column_count: fixedColumnsCount,
        },
      },
    };
  };

  const saveTablePreferences = async () => {
    if (!visibleColumns.length) {
      message.error("Please select at least one column!");
      return;
    }
    // First, create a map to track columns by dataIndex
    const columnsByDataIndex = new Map();
    
    // Process visible columns first
    visibleColumns.forEach((col, index) => {
      const columnData = {
        ...col,
        render: undefined,
        renderTitle: undefined,
        checked: true,
        order: index + 1,
        children: col.children ? col.children.map((child, childIndex) => ({
          ...child,
          render: undefined,
          renderTitle: undefined,
          checked: true,
          order: childIndex + 1,
        })) : undefined
      };
      
      columnsByDataIndex.set(col.dataIndex, columnData);
    });
    
    // Process hidden columns, merging with visible ones if they have the same dataIndex
    hiddenColumns.forEach((col) => {
      if (columnsByDataIndex.has(col.dataIndex)) {
        // This column exists in visible columns - we need to merge children
        const existingColumn = columnsByDataIndex.get(col.dataIndex);
        
        // If the hidden column has children, add them to the existing column's children
        if (col.children && col.children.length > 0) {
          const hiddenChildren = col.children.map(child => ({
            ...child,
            render: undefined,
            renderTitle: undefined,
            checked: false,
          }));
          
          if (existingColumn.children) {
            // Merge children arrays, avoiding duplicates by dataIndex
            const childrenByDataIndex = new Map();
            existingColumn.children.forEach(child => {
              childrenByDataIndex.set(child.dataIndex, child);
            });
            
            hiddenChildren.forEach(child => {
              if (!childrenByDataIndex.has(child.dataIndex)) {
                childrenByDataIndex.set(child.dataIndex, child);
              }
            });
            
            existingColumn.children = Array.from(childrenByDataIndex.values());
          } else {
            existingColumn.children = hiddenChildren;
          }
        }
      } else {
        // This is a new hidden column
        columnsByDataIndex.set(col.dataIndex, {
          ...col,
          render: undefined,
          renderTitle: undefined,
          checked: false,
          children: col.children ? col.children.map(child => ({
            ...child,
            render: undefined,
            renderTitle: undefined,
            checked: false,
          })) : undefined
        });
      }
    });
    
    const updatedPrefCols = Array.from(columnsByDataIndex.values());
    setModalLoading(true);
    const newPreferences = updatePreferenceValue(updatedPrefCols);
    const preferences = {
      page_preferences: newPreferences,
    };
    const response = await setPreferences(
      user_id,
      client_id,
      application_id,
      preferences,
    );
    setModalLoading(false);
    if (response.status === "success") {
      message.success("Column preferences saved successfully!");
      setCurrentUserPreferences(preferences);
      toggleSettings();
    } else {
      message.error(response.message || "Couldn't set columns preference!");
    }
  };

  const toggleSettings = (reset: boolean = false) => {
    setVisible(!visible);
    if (reset) resetColumns(false);
  };

  const onRowSelectionChange = (keys: any[], rows: any[]) => {
    setSelectedRows(rows);
    setSelectedRowKeys(keys);
  };

  useEffect(() => {
    if (showSelectedOnly) {
      setTableConfigWithData({
        ...tableConfigWithData,
        dataSource: selectedRows,
      });
    }

    const currConfig = tableConfigWithData;
    setTableConfigWithData({
      ...currConfig,
      rowSelection: {
        ...currConfig?.rowSelection,
        selectedRowKeys: selectedRowKeys,
      },
    });
  }, [selectedRows, selectedRowKeys]);

  useEffect(() => {
    if (showSelectedOnly) {
      setTableConfigWithData({
        ...tableConfigWithData,
        dataSource: selectedRows,
        rowSelection: {
          ...tableConfigWithData?.rowSelection,
          selectedRowKeys: selectedRows.map((row) => row.key),
        },
      });
    } else {
      setTableConfigWithData({
        ...tableConfigWithData,
        dataSource: data,
        rowSelection: {
          ...tableConfigWithData?.rowSelection,
          selectedRowKeys: selectedRowKeys,
        },
      });
    }
  }, [showSelectedOnly]);

  useEffect(() => {
    const defaultMergeConfig = getMergedUpdatedConfig(config?.tableProps, props.dependencies || {});
    const updatedBulkOptions = parseRenderConfig(config?.bulkRowActionButtons || [], props.dependencies || {});
    const isRowSelectionEnabled = defaultMergeConfig?.rowSelection;
    console.log("Visible Modal", props?.pagination)
    if(!visible) {
      const { tableColumns, visibleColumns, hiddenColumns } =
      getVisibleAndHiddenColumns(defaultMergeConfig?.columns);
      setTableColumns(tableColumns);
      setVisibleColumns(visibleColumns);
      setHiddenColumns(hiddenColumns);
      setFixedColumnsCount(getDefaultFixedColCount(false));
    }
    setBulkOptions(updatedBulkOptions);
    const tableProps = {
      ...defaultMergeConfig,
      ...(!isRowSelectionEnabled
        ? { rowSelection: null }
        : {
            rowSelection: {
              ...defaultMergeConfig?.rowSelection,
              onChange: onRowSelectionChange,
            },
          }),
      onRow: (record: any, rowIndex: number) => ({
        onClick: () => {
          config?.onRowActions?.click(record, rowIndex);
        },
        onDoubleClick: () => {
          config?.onRowActions?.doubleClick(record, rowIndex);
        },
        onMouseEnter: (event: React.MouseEvent<HTMLElement>) => {
          (event.currentTarget as HTMLElement).style.backgroundColor =
            config?.rowHoverColor;
        },
        onMouseLeave: (event: React.MouseEvent<HTMLElement>) => {
          (event.currentTarget as HTMLElement).style.backgroundColor = "";
        },
      }),
      pagination:
        config?.tableProps?.pagination === false
          ? false
          : {
              ...defaultMergeConfig?.pagination,
              ...config?.tableProps?.pagination,
              current:  props?.pagination?.page_no || paginationConfig.current,
              pageSize:  props?.pagination?.page_size || paginationConfig.pageSize,
              total: props.pagination?.total || data.length,
              onChange: (page: number, pageSize: number) => {
                setPaginationConfig({ current: page, pageSize });
                config?.pageChangeActions?.pageNumChange(page);
                config?.pageChangeActions?.pageSizeChange(pageSize);
              },
              simple: isMobile,
            },
      size: isTablet ? "small" : "middle",
    };
    setTableConfigWithData({ ...tableProps, dataSource: data });
  }, [data, currentUserPreferences, paginationConfig]);

  const handleDeselectAll = () => {
    setSelectedRows([]);
    setSelectedRowKeys([]);
    if (showSelectedOnly) {
      setTableConfigWithData({
        ...tableConfigWithData,
        dataSource: data,
      });
    }
    setShowSelectedOnly(false);
  };

  const defaultActionButtons = [];
  const tableActions:any [] = [];
  const bulkRowActionButtons: any[] = [];
  const { token } = theme.useToken();

  const customizeTableButton = (
    <div className="customize-table-button">
        <Button
          icon={
            <ControlOutlined
              style={{fontSize: 17}}
            />
          }
          onClick={() => toggleSettings()}
        >
          Customize Table
        </Button>
    </div>
  );

  const defaulTableView = () => {
    const currConfig = tableConfigWithData;
    setTableConfigWithData({
      ...currConfig,
      size: "middle",
    });
  };

  const compactTableView = () => {
    const currConfig = tableConfigWithData;
    setTableConfigWithData({
      ...currConfig,
      size: "small",
    });
  };

  const comfortableTableView = () => {
    const currConfig = tableConfigWithData;
    setTableConfigWithData({
      ...currConfig,
      size: "large",
    });
  };

  const tableViewOptions = [
    {
      key: "default",
      label: <p onClick={defaulTableView}>Default View</p>,
    },
    {
      key: "compact",
      label: <p onClick={compactTableView}>Compact View</p>,
    },
    {
      key: "comfortable",
      label: <p onClick={comfortableTableView}>Comfortable View</p>,
    },
  ];

  const tableViewDropdown = (
    <div className="table-view-dropdown">
      <Dropdown
        menu={{
          items: tableViewOptions,
        }}
        className="table-view-dropdown-menu"
      >
        <img src={TableViews} />
      </Dropdown>
    </div>
  );

  const extractHeadersAndKeys = (
    columns: Column[],
  ): { headers: string[][]; keys: string[]; merges: XLSX.Range[] } => {
    const headers: string[][] = [[], []];
    const keys: string[] = [];
    const merges: XLSX.Range[] = [];

    let colIndex = 0;
    columns.forEach((column: Column) => {
      if (column.children && column.children.length > 0) {
        const startCol = colIndex;
        column.children.forEach((child: { title: any; dataIndex: string }) => {
          headers[0].push(column.title || "");
          headers[1].push(child.title || child.dataIndex);
          keys.push(child.dataIndex);
          colIndex++;
        });
        merges.push({
          s: { r: 0, c: startCol },
          e: { r: 0, c: colIndex - 1 },
        });
      } else {
        headers[0].push(column.title || column.dataIndex);
        headers[1].push("");
        keys.push(column.dataIndex);
        colIndex++;
      }
    });

    return { headers, keys, merges };
  };

  const handleTableDownload = async () => {
    if(typeof downloadTableCallback === "function"){
      setDownloadLoading(true);
      await downloadTableCallback();
      setDownloadLoading(false);
      return;
    }
    const { headers, keys, merges } = extractHeadersAndKeys(columnConfig || []);

    const excelData = data.map((row) =>
      keys.map((key) => (key ? row[key] || "" : "")),
    );

    const titleRow = [`Title: ${config?.tableTitle || "My Table"}`];
    const headerRows = headers;
    const worksheetData = [[...titleRow], [], ...headerRows, ...excelData];
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

    worksheet["!merges"] = [
      {
        s: { r: 0, c: 0 },
        e: { r: 0, c: keys.length - 1 },
      },
      ...merges,
    ];

    const columnWidths = keys.map(() => ({ wch: 20 }));
    worksheet["!cols"] = columnWidths;

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Table Data");

    XLSX.writeFile(
      workbook,
      `table_data_${Math.floor(Date.now() / 1000)}.xlsx`,
    );
  };

  const tableDownloadButton = (
    <div className="table-download-button">
      <Button
        icon={
          <DownloadOutlined
            style={{ fontSize: 17 }}
          />
        }
        loading={downloadLoading}
        onClick={handleTableDownload}
      >
        Download
      </Button>
    </div>
  );

  if (!config?.disableCustomization) {
    defaultActionButtons.push(customizeTableButton);
  }

  if (config?.resizable) {
    defaultActionButtons.push(tableViewDropdown);
  }

  if (config?.downloadable) {
    defaultActionButtons.push(tableDownloadButton);
  }

  const totalRowsSelected = (
    <div className="total-rows-selected">
      {selectedRows?.length} Rows Selected
    </div>
  );

  const showSelectedOnlyButton = (
    <div className="show-selected-only">
      <p>Show Selected Only </p>
      <Switch
        size="small"
        value={showSelectedOnly}
        onChange={(value) => {
          setShowSelectedOnly(value);
        }}
      />
    </div>
  );

  const deselectAllButton = (
    <div className="deselect-all">
      <Button
        type="ghost"
        size="small"
        icon={
          <MinusSquareOutlined />
        }
        onClick={handleDeselectAll}
      >
        Deselect All
      </Button>
    </div>
  );

  if (selectedRows?.length) {
    tableActions.push(totalRowsSelected);
    tableActions.push(showSelectedOnlyButton);
    tableActions.push(deselectAllButton);
    // bulkRowActionButtons.push(totalRowsSelected);
    // bulkRowActionButtons.push(showSelectedOnlyButton);
    // bulkRowActionButtons.push(deselectAllButton);
    // bulkRowActionButtons.push(<Divider type="vertical" />);
  }

  const bulkRowActionButtonConfig = bulkOptions || [];

  bulkRowActionButtonConfig.forEach((buttonConfig: any) => {
    if(typeof buttonConfig.render === "function") {
      bulkRowActionButtons.push(
        <div key={buttonConfig.key} className="bulk-row-action-button">
          {buttonConfig.render(selectedRows)}
        </div>
      );
      return;
    }
    bulkRowActionButtons.push(
      <div key={buttonConfig.key} className="bulk-row-action-button">
        <Button
          key={buttonConfig.key}
          onClick={() => buttonConfig.onClickFunction(selectedRows)}
          type={buttonConfig.type}
          disabled={buttonConfig.disabled}
          icon={buttonConfig.icon}
        >
          {buttonConfig.text}
        </Button>
      </div>,
    );
  });

  const handleResize =
    (index: number) =>
    (_e: any, { size }: any) => {
      setColumnConfig((prevColumns: any) => {
        const nextColumns = [...prevColumns];
        nextColumns[index] = {
          ...nextColumns[index],
          width: size.width,
        };
        return nextColumns;
      });
    };

  const handleResizeStop =
    (_index: number) =>
    async (_e: any, { size }: any) => {
      const updatedPrefCols = [
        ...columnConfig?.map((col: any, index: number) => ({
          ...col,
          renderTitle: undefined,
          checked: true,
          order: index + 1,
          width: index === _index ? size.width : col.width,
        })),
        ...hiddenColumns.map((col) => ({
          ...col,
          checked: false,
          renderTitle: undefined,
        })),
      ];

      const newPreferences = updatePreferenceValue(updatedPrefCols);
      const preferences = {
        page_preferences: newPreferences,
      };
      const response = await setPreferences(
        user_id,
        client_id,
        application_id,
        preferences,
      );
      if (response.status === "success") {
        message.success("Column preferences saved successfully!");
        // setCurrentUserPreferences(preferences);
      } else {
        message.error(response.message || "Couldn't set columns preference!");
      }
    };

  useEffect(() => {
    if (!tableConfigWithData) return;
    const columnsWithSearch = tableColumns?.map((col, index) => {
      const colItem = {
        ...col,
        title: col.hoverText ? (
          <Tooltip title={col.hoverText || col.title}>
            {" "}
            {col.title}{" "}
            <InfoCircleOutlined
              style={{ color: "grey", marginLeft: 8, cursor: "pointer" }}
            />
          </Tooltip>
        ) : (
          col.title
        ),
        fixed: !isMobile && index < fixedColumnsCount ? "left" : undefined,
        align: !isMobile && index < fixedColumnsCount && !col.align ? "left" : col.align,
        sortOrder:
          props.sortedInfo?.columnKey === col.dataIndex
            ? props.sortedInfo?.order
            : null,
        ...(col.children?.length && {
          children: col.children.map((childCol: any) => ({
            ...childCol,
            fixed: !isMobile && index < fixedColumnsCount ? "left" : undefined,
            title: childCol.hoverText ? (
              <Tooltip title={childCol.hoverText || childCol.title}>
                {" "}
                {childCol.title}{" "}
                <InfoCircleOutlined
                  style={{ color: "grey", marginLeft: 8, cursor: "pointer" }}
                />
              </Tooltip>
            ) : (
              childCol.title
            ),
            sortOrder:
              props.sortedInfo?.columnKey === childCol.dataIndex
                ? props.sortedInfo?.order
                : null,
          })),
        }),
      };

      if (config?.searchableColumns?.includes(col.dataIndex)) {
        colItem.filters = [];
        colItem.onFilter = (
          value: string,
          record: { [x: string]: { toString: () => string } },
        ) =>
          record[col.dataIndex]
            ?.toString()
            .toLowerCase()
            .includes(value.toLowerCase());
        colItem.filterDropdown = ({
          setSelectedKeys,
          selectedKeys,
          confirm,
          clearFilters,
        }: {
          setSelectedKeys: (keys: React.Key[]) => void;
          selectedKeys: React.Key[];
          confirm: () => void;
          clearFilters: () => void;
        }) => (
          <div style={{ padding: 8 }}>
            <Input
              placeholder={`Search ${col.title}`}
              value={selectedKeys[0]}
              onChange={(e) =>
                setSelectedKeys(e.target.value ? [e.target.value] : [])
              }
              onPressEnter={() => confirm()}
              style={{ marginBottom: 8, display: "block" }}
            />
            <div style={{ display: "flex", justifyContent: "space-between" }}>
              <Button
                type="primary"
                onClick={() => confirm()}
                icon={
                  <SearchOutlined />
                }
                size="small"
                style={{ width: 90 }}
              >
                Search
              </Button>
              <Button
                onClick={() => {
                  clearFilters();
                  confirm();
                }}
                size="small"
                style={{ width: 90 }}
              >
                Reset
              </Button>
            </div>
          </div>
        );
      }
      return colItem;
    });
    console.log("columnsWithSearch", columnsWithSearch);
    setColumnConfig(columnsWithSearch);
    updateColumnConfig && updateColumnConfig(columnsWithSearch);
  }, [tableConfigWithData, props.sortedInfo]);

  if ((!innerLoading && loading) || !tableConfigWithData || !Array.isArray(columnConfig)) {
    return hideLoading ? null : <Skeleton active />;
  }

  const components = {
    header: {
      cell: ResizableTitle,
    },
  };

  const getFinalDataSource = () => {
    return config?.showTotalRowHighlight &&
      tableConfigWithData?.dataSource?.length
      ? tableConfigWithData.dataSource.slice(0, -1)
      : tableConfigWithData?.dataSource;
  };

  const getTableSummary = () => {
    const summaryData = tableConfigWithData?.dataSource?.length
      ? tableConfigWithData.dataSource[
          tableConfigWithData.dataSource.length - 1
        ]
      : {};
    let summary = undefined;
    if (
      config?.showTotalRowHighlight &&
      tableConfigWithData?.dataSource?.length > 2
    ) {
      summary = [];
      let index = 0;
      columnConfig.forEach((col) => {
        if (col.children?.length) {
          col.children.forEach((childCol: any) => {
            summary.push(
              <Table.Summary.Cell key={childCol.dataIndex} index={index}>
                {summaryData?.[childCol.dataIndex] !== undefined ? (
                  <div>{summaryData[childCol.dataIndex]}</div>
                ) : (
                  ""
                )}
              </Table.Summary.Cell>,
            );
          });
          index++;
        } else {
          summary.push(
            <Table.Summary.Cell key={col.dataIndex} index={index}>
              {summaryData?.[col.dataIndex] !== undefined ? (
                <div>{summaryData[col.dataIndex]}</div>
              ) : (
                ""
              )}
            </Table.Summary.Cell>,
          );
          index++;
        }
      });
    }
    return summary;
  };

  const noDataLocale = {
    emptyText: (
      <div style={{ textAlign: "center" }}>
        <img
          src={config?.noData?.image}
          alt="No Data"
          style={{ width: 100, height: 100, marginBottom: 10 }}
        />
        <p style={{ fontSize: "16px", color: "#888" }}>
          {config?.noData?.text}
        </p>
      </div>
    ),
  };

  const singlePagePagination =
    tableConfigWithData?.pagination === false ||
    (tableConfigWithData?.pagination?.hideOnSinglePage === true &&
      data.length <= paginationConfig.pageSize);

  let finalColumns = columnConfig;
  if (config?.resizableColumns) {
    finalColumns = columnConfig.map((col, index) => {
      return {
        ...col,
        onHeaderCell: (column: Column) => ({
          width: column.width,
          onResize: handleResize(index),
          onResizeStop: handleResizeStop(index),
        }),
      };
    });
  }
  return (
    <div id="configurable_table" style={props.tableStyle}>
      {config?.title && <h2 className="table-section-title">{config.title}</h2>}
      <div
        className="table-options"
        data-testid="action-buttons"
        style={{ top: isTablet ? 18 : undefined, maxWidth: "50%" }}
      >
        {selectedRows?.length ? (
          <>
            <div className="bulk-selection-option">
              <div
                className="device-selected"
                data-testid="selected-rows-count"
              >
                <span className="device-selected-value">
                  {selectedRows.length}
                </span>
                Rows Selected
              </div>

              <div className="show-checked-switch-container">
                <Switch
                  className="show-checked-switch"
                  size="small"
                  checked={showSelectedOnly}
                  onChange={(value: any) => {
                    setShowSelectedOnly(value);
                  }}
                />
                {/* {this.props.t? this.props.t('show_selected_only'): "Show Selected Only"} */}
                Show Selected Only
              </div>

              <div className="deselect-all-btn" onClick={handleDeselectAll}>
                <StopOutlined className="deselect-icon" />
                Deselect All
              </div>
            </div>
            <div className="bulk-selection-option">{bulkRowActionButtons}</div>
          </>
        ) : (
          <ConfigProvider
            theme={{
              components: {
                Button: {
                  paddingInline: 0,
                  defaultColor: "#4F4F4F",
                  defaultShadow: "none",
                  defaultHoverColor: token.colorPrimary,
                  defaultBorderColor: "transparent",
                  defaultActiveBorderColor: "transparent",
                  defaultHoverBorderColor: "transparent",
                  lineWidthFocus: 0,
                },
              },
            }}
          >
            <div style={{ display: "flex", gap: 16 }}>
              {defaultActionButtons}
            </div>
          </ConfigProvider>
        )}
        {/* <div data-testid="action-buttons" className="action-buttons">
          {selectedRows?.length ? bulkRowActionButtons : defaultActionButtons}
        </div> */}
      </div>

      <div
        className="configurable-table-wrapper"
        style={{
          top: singlePagePagination ? 56 : 0,
          position: singlePagePagination ? "relative" : undefined,
        }}
      >
        <Table
          {...tableConfigWithData}
          loading={innerLoading}
          columns={finalColumns}
          dataSource={getFinalDataSource()}
          components={config?.resizableColumns ? components : {}}
          bordered
          locale={config?.noData ? noDataLocale : null}
          rowKey="device_id"
          onChange={(_, __, sorter: any) => {
            typeof props.updateSortedInfo === "function" && props.updateSortedInfo(
              sorter.column
                ? {
                    ...sorter,
                    columnKey: sorter.columnKey || sorter.field,
                    api_sort_key: sorter.column.api_sort_key,
                  }
                : {},
            );
          }}
          rowClassName={(_, index) => {
            if (index === data.length - 1 && config?.showTotalRowHighlight) {
              return "last-row-highlight";
            }
            return index % 2 === 0 ? "table-row-even" : "table-row-odd";
          }}
          summary={getTableSummary}
        />
      </div>

      <CustomizeModal
        title="Customize Table"
        showFreezeColumns
        visible={visible}
        modalLoading={modalLoading}
        totalColumns={visibleColumns?.length + hiddenColumns?.length}
        visibleColumns={visibleColumns}
        hiddenColumns={hiddenColumns}
        fixedColumnsCount={fixedColumnsCount}
        onClose={() => toggleSettings(true)}
        onOk={saveTablePreferences}
        onReset={resetColumns}
        setFixedColumnsCount={setFixedColumnsCount}
        setVisibleColumns={setVisibleColumns}
        setHiddenColumns={setHiddenColumns}
        isValidGrouping={isValidGrouping}
      />
    </div>
  );
};

export default ConfigurableTable;
