import React, { useState, useEffect, useRef } from "react";
import { Divider, Tabs } from "antd";
import ConfigurableTable from "../../baseComponents/ConfigurableTable";
import ConfigurableChart from "../../baseComponents/ConfigurableChart";
import moment from "moment";
import _orderby from "lodash/orderby";
import ReportHeader from "../../../nonConfigurable/baseComponents/ReportHeader";
import {
  retriveThingsList,
  getTableData,
  getTableDataV4,
  getGraphData,
  getPageConfig,
  getSummaryData,
} from "@datoms/js-sdk";
import SkeletonLoader from "@datoms/react-components/src/components/SkeletonLoader";
import { useGlobalContext } from "../../../../store/globalStore";
import _find from "lodash/find";
import _cloneDeep from "lodash/cloneDeep";
import AntRow from "@datoms/react-components/src/components/AntRow";
import AntCol from "@datoms/react-components/src/components/AntCol";
import ConfigurableSummary from "../../baseComponents/ConfigurableSummary";
import { replaceParamKeyAndName } from "./utils/logic";
import { assetDetails } from "./utils/assetDetails";
import { withRouter, RouteComponentProps } from "react-router-dom";
import { downloadPDF } from "../../../nonConfigurable/baseComponents/DownloadModal/utils/reportDownloadPDF";
import { downloadXlsx } from "../../../nonConfigurable/baseComponents/DownloadModal/utils/reportDownloadXlsx";
import { downloadCsv } from "../../../nonConfigurable/baseComponents/DownloadModal/utils/reportDownloadCsv";
import { getTerritoryData } from "@datoms/webapp-component-user-management/src/components/TerritoryPage/TerritorySelect";
import datomsLogo from "../../../nonConfigurable/baseComponents/DownloadModal/icons/datoms-logo.png";
import { defaultRanges } from "../../../nonConfigurable/baseComponents/DateRangeSelect/configs/DateConfig";
import { getBaseUrl } from "@datoms/js-utils/src/base-url-logic";
import NoDataImage from "./images/no-data.svg";
import "./styles.less";

const { TabPane } = Tabs;
interface Props {
  history?: any;
  page_id: number;
  application_id?: number;
  client_id?: number;
  client_name?: string;
  plan_description?: any;
}

const GravityReport: React.FC<Props & RouteComponentProps> = (props) => {
  const { page_id, application_id, client_id, history } = props;
  const {
    user_preferences,
    client_name,
    user_name,
    is_white_label,
    vendor_name,
    currentUserPreferences,
    logged_in_user_client_id,
    client_logo,
    vendor_logo,
    vendor_id,
    client_id: contextClientId,
    enabled_features,
  } = useGlobalContext();

  const [dateRangeTimes, setDateRangeTimes] = useState<{
    fromTime: number;
    uptoTime: number;
  }>({
    fromTime: moment().subtract(30, "days").startOf("day").unix(),
    uptoTime: moment().endOf("day").unix(),
  });
  const pageFilterRef = useRef<any>(null);
  const [initial, setInitial] = useState<boolean>(true);
  const [baseUrl, setBaseUrl] = useState<string>("");
  const [pageConfig, setPageConfig] = useState<any | null>(null);
  const [tableData, setTableData] = useState<any[]>([]);
  const [chartData, setChartData] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [allThingsData, setAllThingsData] = useState<any[]>([]);
  const [listData, setListData] = useState<any[]>([]);
  const [selectedThing, setSelectedThing] = useState<number | string>();
  const [paramList, setParamList] = useState<any[]>([]);
  const [currPageNumber, setCurrPageNumber] = React.useState<number>(1);
  const [currPageSize, setCurrPageSize] = React.useState<number>(0);
  const [pagination, setPagination] = useState<{ page_no: number, page_size: number }>({
    page_no: 1,
    page_size: 50
  });
  const [sortedInfo, setSortedInfo] = useState<Record<string, any>>({});
  const [pageLoading, setPageLoading] = useState<boolean>(true);
  const [summaryData, setSummaryData] = useState<any[]>([]);
  const [selectedThingData, setSelectedThingData] = useState<any>({});
  const [isConfigModalVisible, setIsConfigModalVisible] = useState(false);
  const [listedThings, setListedThings] = useState<any[]>([]);
  const [selectedAssets, setSelectedAssets] = React.useState<number[]>([]);
  const [configFilters, setConfigFilters] = useState<string[]>([]);
  const [pageFilters, setPageFilters] = useState<any>([]);
  const [dependentPageFilters, setDependentPageFilters] = useState<any>({});
  const [pageFilterMap, setPageFilterMap] = useState<any>({});
  const [selectedPageFilters, setSelectedPageFilters] = useState<any>([]);
  const [graphConfigs, setGraphConfigs] = useState<any[]>([]);
  const [machineInfo, setMachineInfo] = useState<any[]>([]);
  const [tableColumnConfig, setTableColumnConfig] = useState<any[]>([]);
  const [territoryData, setTerritoryData] = useState<any[]>([]);
  const [filterValues, setFilterValues] = useState<any>({});
  const [defaultPageNumber, setDefaultPageNumber] = useState<number>(1);
  const [componentLoading, setComponentLoading] = useState<{
    [key: string]: boolean;
  }>({
    graph: true,
    table: true,
    summary: true,
    machineInfo: true,
  });
  const isCustomizeFeatureEnabled = enabled_features.includes("Reports:ParameterCustomization") && logged_in_user_client_id === contextClientId;

  const updateColumnConfig = (newColumnConfig: any) => {
    setTableColumnConfig(newColumnConfig);
  };

  const isScheduledDownload =
    props?.history?.location?.search?.includes("download=true") &&
    typeof window?.getInputDataSet === "function";

  const pageNumChange = (page: number) => {
    setDefaultPageNumber(page);
    setCurrPageNumber(page);
  };

  const pageSizeChange = (size: number) => {
    setCurrPageSize(size);
  };

  // Implementation of action buttons if required

  /*const actionButtons = [
    {
      text: "Demo Button 1",
      key: "add_item",
      onClickFunction: (selectedRows: any) => {
        console.log("Demo Button 1 Pressed -> ", selectedRows);
      },
      type: "ghost",
      disabled: false,
      icon: <DownOutlined />,
    },
    {
      text: "Demo Button 2",
      key: "delete_item",
      onClickFunction: (selectedRows: any) => {
        console.log("Demo Button 2 Pressed -> ", selectedRows);
      },
      type: "ghost",
      disabled: false,
    },
  ];

  tableData[0].bulkRowActionButtons = actionButtons;*/

  useEffect(() => {
    if (
      !tableData?.length ||
      tableData[0]?.config?.tableProps?.pagination === false
    )
      return;

    if (tableData[0].config.api_pagination) {
      console.log("API Pagination", sortedInfo);
      fetchData(undefined, undefined, undefined, true);
    }
  }, [currPageNumber, currPageSize, sortedInfo]);

  // useEffect(() => {
  //   console.log("pagination", pagination);
  //   fetchData(undefined, undefined, undefined, true);
  // }, [pagination]);

  const formattedFilterValues = () => {
    const filterValueObj: Record<string, any> = {};
    const filters = pageConfig?.filters || [];
    if (filters.includes("aggregation_period")) {
      filterValueObj["aggregation_period"] = filterValues.aggregation_period;
    }
    if (filters.includes("dg_type")) {
      filterValueObj["without_device"] = filterValues.without_device;
    }
    if (filters.includes("territories")) {
      filterValueObj["territories"] = filterValues.territories;
    }
    if (filters.includes("onboarding_status")) {
      filterValueObj["onboarding_status"] = filterValues.onboarding_status;
    }
    if (filters.includes("violation_type")) {
      filterValueObj["tag"] = filterValues.tag;
    }
    if (filters.includes("asset_added_range")) {
      filterValueObj["added_from"] = filterValues.added_from;
      filterValueObj["added_upto"] = filterValues.added_upto;
    }
    return filterValueObj;
  };

  const processPageFilters = (filterArr: any) => {
    const depFilters: any = {};
    const filters: any = [];
    const filterQuery: any = {};
    filterArr.forEach((filter: any) => {
      const isFeatureEnabled = !filter.feature_key ||enabled_features.find((feature: any) => feature.feature_key === filter.feature_key || true);
      if (!isFeatureEnabled) return;
      
      filters.push(filter);
      if (filter.url_name && filter.dependent_filters) {
        depFilters[filter.url_name] = filter.dependent_filters;
      }
      if (filter.url_name && filter.query_key) {
        filterQuery[filter.url_name] = filter.query_key;
      }  
    });
    setPageFilterMap(filterQuery);
    return {filters, depFilters};
  }

  const getSelectedPageFilters = () => {
    const mainFilter = pageFilterRef?.current?.getFilters() || {};
    const filters: any = {
      ...mainFilter,
    };

    return filters;
  };

  const filterSummary = (orgPreference: any, tableColumns: any) => {
    const reverseMap: any = {
      calculated_runhour: "total_runhour",
      calculated_energy: "total_energy",
      fuel_consumption: "total_fuel_consumed",
      fuel_filled: "total_fuel_filled",
      fuel_theft: "total_fuel_drained",
    };

    return tableColumns.filter((column: any) => {
      const key = reverseMap[column.key];
      return !key || orgPreference[key];
    });
  };

  const orgPreferenceFilter = (summaryData: any) => {
    if (
      pageConfig?.orgPreferenceKey &&
      currentUserPreferences?.reports?.[pageConfig?.orgPreferenceKey]
        ?.summary &&
      Object.keys(
        currentUserPreferences?.reports?.[pageConfig?.orgPreferenceKey].summary,
      ).length > 0
    ) {
      return filterSummary(
        currentUserPreferences?.reports?.[pageConfig?.orgPreferenceKey].summary,
        summaryData,
      );
    }

    return summaryData;
  };

  const sortArrayByColumn = (
    data: any,
    sortedInfo: any,
    sortLastRow = true,
  ) => {
    if (!sortedInfo || !sortedInfo.columnKey || !sortedInfo.order) {
      return data;
    }

    let { columnKey, order } = sortedInfo;
    if (columnKey === "time") {
      columnKey = "sorter_key_time";
    }

    const sortableData = sortLastRow ? data.slice() : data.slice(0, -1);
    const lastRow = sortLastRow ? [] : data.slice(-1);

    const sortedData = sortableData.sort((a, b) => {
      let valueA = a[columnKey];
      let valueB = b[columnKey];

      if (!isNaN(valueA) && !isNaN(valueB)) {
        valueA = parseFloat(valueA);
        valueB = parseFloat(valueB);
      }

      if (valueA === "-") {
        valueA = 0;
      }

      if (valueB === "-") {
        valueB = 0;
      }

      if (valueA < valueB) {
        return order === "ascend" ? -1 : 1;
      } else if (valueA > valueB) {
        return order === "ascend" ? 1 : -1;
      } else {
        return 0;
      }
    });

    return [...sortedData, ...lastRow];
  };

  const getFinalAssetsArray = (finalThingId: any) => {
    if (pageConfig?.report_type === "multi_entity") {
      return selectedAssets;
    }
    return [finalThingId];
  };

  const getCurrentTable = () => {
    if(filterValues?.aggregation_period === "none" && pageConfig?.no_aggregation?.table) {
      return pageConfig?.no_aggregation?.table;
    }
    return pageConfig?.table
  }

  const getCurrentGraph = () => {
    if(filterValues?.aggregation_period === "none" && pageConfig?.no_aggregation?.graphs) {
      return pageConfig?.no_aggregation?.graphs;
    }
    const daysDiff = Math.ceil(moment.duration(dateRangeTimes.uptoTime - dateRangeTimes.fromTime, "seconds").asDays());
    return pageConfig?.graphs?.filter((graph: any) => {
      if(!graph.min_time_range_days || isNaN(daysDiff)) return true;
      return daysDiff >= graph.min_time_range_days;
    });
  }

  const getAggrPeriod = (period:any) => {
    return period === "none" ? 86400 : period || 86400;
  }

  const fetchData = async (
    downloadThingId: number | undefined = undefined,
    fromTime?: number,
    uptoTime?: number,
    onlyTable?: boolean,
    resetPageNumber: boolean = false,
  ) => {
    const reportType = pageConfig?.report_type;

    if(isScheduledDownload && !downloadThingId) return;

    if (
      downloadThingId &&
      reportType === "multi_entity" &&
      !isScheduledDownload
    ) {
      const tableResults = _cloneDeep(tableData);
      console.log("downloadThingId", tableResults);
      let tableDataArr = tableResults[0].data;
      if (tableResults[0].config.api_pagination) {
        tableDataArr = await fetchAllPagesData(
          tableResults[0].totalCount,
          undefined,
          fromTime,
          uptoTime,
        );
      }
      tableDataArr = sortArrayByColumn(
        tableDataArr,
        sortedInfo,
        !tableResults[0].config?.showTotalRowHighlight,
      );
      tableResults[0].data =
        tableResults[0].config?.showTotalRowHighlight &&
        tableDataArr.length === 2
          ? tableDataArr.slice(0, -1)
          : tableDataArr;
      return {
        assetId: downloadThingId,
        graphResponse: chartData,
        tableResponse: tableResults,
        summaryResponse: summaryData,
        preferences: currentUserPreferences,
      };
    }

    if (
      (!pageConfig || !selectedThing) &&
      !isScheduledDownload &&
      !(
        reportType === "multi_entity" && !pageConfig.filters?.includes("things")
      )
    )
      return;
    if (!downloadThingId) {
      setLoading(true);
      setComponentLoading({
        graph: true,
        table: true,
        summary: true,
        machineInfo: true,
      });
    }

    const finalThingId = downloadThingId || selectedThing;

    const chartPromises =
      chartData.length && onlyTable
        ? chartData
        : getCurrentGraph()?.length
          ? getCurrentGraph().map(async (graphConfig: any) => {
              graphConfig.time = {
                timezone: user_preferences.timezone || "Asia/Calcutta",
              };
              if (graphConfig.chartType === "windRose") {
                const seriesConfig = graphConfig.highChartOptions.series[0];

                const response = await getGraphData(client_id, {
                  series: seriesConfig,
                });
                return {
                  config: {
                    ...graphConfig.highChartOptions,
                    chartType: graphConfig.chartType,
                  },
                  seriesData: response?.data?.[0]?.data?.length
                    ? response.data[0].data
                    : [],
                  colSpan: graphConfig.colSpan,
                };
              }

              let yAxis = graphConfig.highChartOptions.yAxis
                ? JSON.parse(JSON.stringify(graphConfig.highChartOptions.yAxis))
                : graphConfig.highChartOptions.yAxis;

              const updatedSeries = await Promise.all(
                graphConfig.highChartOptions.series.map(
                  async (singleSeries: any, index: number) => {
                    const response = await getGraphData(client_id, {
                      series: [
                        {
                          ...singleSeries,
                          param_key: filterValues.selectedParam,
                          apiConfig: {
                            ...formattedFilterValues(),
                            ...singleSeries.apiConfig,
                            aggregation_period:
                              getAggrPeriod(filterValues?.aggregation_period),
                            things: getFinalAssetsArray(finalThingId),
                            from_time: fromTime || dateRangeTimes.fromTime,
                            upto_time: uptoTime || dateRangeTimes.uptoTime,
                            offlineTimeout: selectedThingData?.offline_timeout
                              ? selectedThingData.offline_timeout * 1000
                              : 900000,
                          },
                        },
                      ],
                      yAxis: Array.isArray(yAxis)
                        ? yAxis?.[index]
                          ? [yAxis[index]]
                          : undefined
                        : [yAxis],
                    });
                    if (
                      Array.isArray(yAxis) &&
                      response?.data?.[0]?.yAxis?.[0]
                    ) {
                      yAxis[index] = response?.data?.[0]?.yAxis?.[0];
                    } else if (
                      !Array.isArray(yAxis) &&
                      response?.data?.[0]?.yAxis?.[0]
                    ) {
                      yAxis = response?.data?.[0]?.yAxis?.[0];
                    }

                    const data = response?.data?.[0]?.data?.length
                      ? response?.data?.[0]?.data
                      : [];
                    const isTimestampValueArray =
                      Array.isArray(data) &&
                      data.length > 0 &&
                      Array.isArray(data[0]) &&
                      data[0].length === 2 &&
                      typeof data[0][0] === "number";

                    if (!data.length) {
                      return null;
                    } else if (
                      isTimestampValueArray &&
                      !data.some(([, value]: [number, any]) => value !== null)
                    ) {
                      return null;
                    }

                    const checkData = singleSeries.othersCount
                      ? data.filter((item: any) => item.name !== "---")
                      : data;
                    if (
                      singleSeries.minDataCount &&
                      checkData.length < singleSeries.minDataCount
                    ) {
                      return null;
                    }

                    return {
                      ...singleSeries,
                      tooltip:
                        response?.data?.[0]?.tooltip || singleSeries.tooltip,
                      data,
                    };
                  },
                ),
              );
              return {
                config: {
                  ...graphConfig.highChartOptions,
                  yAxis,
                  chartType: graphConfig.chartType,
                },
                data:
                  graphConfig?.chartType === "calendarHeatmap"
                    ? updatedSeries?.[0]?.data
                    : updatedSeries.filter((item: any) => item),
                colSpan: graphConfig.colSpan,
              };
            })
          : [];

    const selectedPageFilters = getSelectedPageFilters();

    const finalFilters = Object.fromEntries(
      Object.entries(selectedPageFilters || {})
        .filter(([_, value]) => value !== "")
        .map(([key, value]) => {
          // Parse string booleans to actual boolean values
          if (value === "true") return [pageFilterMap[key] || key, true];
          if (value === "false") return [pageFilterMap[key] || key, false];
          return [pageFilterMap[key] || key, value];
        })
    );

    // TODO: We will modify the filters later on
    const modifiedFilters: Record<string, any> = {};
    for (const key in finalFilters) {
      const value = finalFilters[key];
      if (Array.isArray(value)) {
        if (value.length > 1) {
          modifiedFilters[key] = { in: value };
        } else if (value.length === 1) {
          modifiedFilters[key] = value[0];
        }
      } else {
        modifiedFilters[key] = value;
      }
    }
    const tablePromises = getCurrentTable()?.length
    ? getCurrentTable().map(async (tableConfig: any, index: number) => {
          const payload = {
            reportType,
            dataSource: pageConfig.data_source,
            columns: tableConfig.tableProps.columns,
            columnIntervalConfig: tableConfig.tableProps.columnIntervalConfig,
            rowTotalConfig: tableConfig.tableProps.rowTotalConfig,
            apiType: tableConfig.apiType,
            dataPathExp: tableConfig.dataPathExp,
            tableDataPathExp: tableConfig.tableDataPathExp,
            totalCountPathExp: tableConfig.totalCountPathExp,
            timezone: user_preferences.timezone,
            apiConfig: {
              ...formattedFilterValues(),
              ...tableConfig.apiConfig,
              things: getFinalAssetsArray(finalThingId),
              aggregation_period: getAggrPeriod(filterValues?.aggregation_period),
              from_time: fromTime || dateRangeTimes.fromTime,
              upto_time: uptoTime || dateRangeTimes.uptoTime,
              pageNo: tableConfig.api_pagination ? (resetPageNumber ? 1 : currPageNumber) : undefined,
              resultPerPage: tableConfig.api_pagination
                ? currPageSize ||
                  tableConfig.tableProps.pagination?.pageSize ||
                  50
                : undefined,
              get_total_count: onlyTable ? "false" : undefined,
              orderKey: sortedInfo?.api_sort_key,
              orderBy: sortedInfo?.order === "ascend" ? "asc" : undefined,
              api_query: {
                ...(tableConfig.apiConfig.api_query &&
                  tableConfig.apiConfig.api_query),
                filter: {...modifiedFilters},
                page: pagination.page_no,
                limit: pagination.page_size,
                things: getFinalAssetsArray(finalThingId),
              },
            },
          };
          const response = pageConfig.api_version?.table_api === "v4"
            ? await getTableDataV4(client_id, payload)
            : await getTableData(client_id, payload);

          let finalTableConfig = deepCloneWithFunctions(tableConfig);
          if (response.columns?.length) {
            finalTableConfig.tableProps.columns = response.columns;
          } else if (
            response.hiddenColumns?.length &&
            finalTableConfig.tableProps?.columns?.length
          ) {
            finalTableConfig.tableProps.columns =
              finalTableConfig.tableProps.columns.filter(
                (column: any) =>
                  !response.hiddenColumns.includes(column.dataIndex),
              );
          }
          return {
            config: finalTableConfig,
            data: response?.tableData || [],
            pagination: response?.pagination || {},
            totalCount:
              tableConfig.api_pagination && !onlyTable
                ? response?.totalDataCount
                : tableData?.[index]?.totalCount || 0,
          };
        })
      : [];

    const summaryPromises =
      summaryData.length && onlyTable
        ? summaryData
        : pageConfig?.summary?.length
          ? pageConfig.summary.map(async (summaryConfig: any) => {
              const response = await getSummaryData(client_id, {
                apiType: summaryConfig.apiType,
                dataPathExp: summaryConfig.dataPathExp,
                timezone: user_preferences.timezone,
                apiConfig: {
                  ...formattedFilterValues(),
                  ...summaryConfig.apiConfig,
                  things: getFinalAssetsArray(finalThingId),
                  aggregation_period: getAggrPeriod(filterValues?.aggregation_period),
                  from_time: fromTime || dateRangeTimes.fromTime,
                  upto_time: uptoTime || dateRangeTimes.uptoTime,
                },
                data: summaryConfig.data,
              });
              return {
                config: summaryConfig,
                data: orgPreferenceFilter(response?.data || []) || [],
              };
            })
          : [];
    let [tableResults, chartResults, summaryResults] = await Promise.all([
      Promise.all(tablePromises),
      Promise.all(chartPromises),
      Promise.all(summaryPromises),
    ]);

    chartResults = chartResults.filter((item: any) => item.data?.length);

    if (!downloadThingId) {
      setTableData(tableResults || []);
      setChartData(chartResults || []);
      setSummaryData(summaryResults || []);
      setLoading(false);
      setPageLoading(false);
      setComponentLoading({
        graph: false,
        table: false,
        summary: false,
        machineInfo: false,
      });
    } else {
      let tableDataArr = tableResults[0].data;
      tableDataArr = sortArrayByColumn(tableDataArr, sortedInfo);
      tableResults[0].data = tableDataArr;

      return {
        assetId: finalThingId,
        graphResponse: chartResults,
        tableResponse: tableResults,
        summaryResponse: summaryResults,
        orgPreferenceKey: pageConfig.orgPreferenceKey,
        preferences: currentUserPreferences,
      };
    }
  };

  const fetchAllPagesData = async (
    totalDataCount: number,
    finalThingId?: number,
    fromTime?: number,
    uptoTime?: number,
  ) => {
    let allData: any[] = []; // To collect all pages data
    let currentPage = 1;
    let hasMorePages = true;
    const tableConfig = getCurrentTable()[0];
    const selectedPageFilters = getSelectedPageFilters();

    const finalFilters = Object.fromEntries(
      Object.entries(selectedPageFilters || {})
        .filter(([_, value]) => value !== "")
        .map(([key, value]) => {
          // Parse string booleans to actual boolean values
          if (value === "true") return [pageFilterMap[key] || key, true];
          if (value === "false") return [pageFilterMap[key] || key, false];
          return [pageFilterMap[key] || key, value];
        })
    );

    // TODO: We will modify the filters later on
    const modifiedFilters: Record<string, any> = {};
    for (const key in finalFilters) {
      const value = finalFilters[key];
      if (Array.isArray(value)) {
        if (value.length > 1) {
          modifiedFilters[key] = { in: value };
        } else if (value.length === 1) {
          modifiedFilters[key] = value[0];
        }
      } else {
        modifiedFilters[key] = value;
      }
    }
    while (hasMorePages) {
      const payload = {
        reportType: pageConfig.report_type,
        dataSource: pageConfig.data_source,
        columns: tableConfig.tableProps.columns,
        columnIntervalConfig: tableConfig.tableProps.columnIntervalConfig,
        rowTotalConfig: tableConfig.tableProps.rowTotalConfig,
        apiType: tableConfig.apiType,
        dataPathExp: tableConfig.dataPathExp,
        tableDataPathExp: tableConfig.tableDataPathExp,
        totalCountPathExp: tableConfig.totalCountPathExp,
        timezone: user_preferences.timezone,
        apiConfig: {
          ...formattedFilterValues(),
          ...tableConfig.apiConfig,
          things: getFinalAssetsArray(finalThingId),
          aggregation_period: getAggrPeriod(filterValues?.aggregation_period),
          from_time: fromTime || dateRangeTimes.fromTime,
          upto_time: uptoTime || dateRangeTimes.uptoTime,
          pageNo: currentPage,
          resultPerPage: 2000,
          get_total_count: "false",
          api_query: {
            ...(tableConfig.apiConfig.api_query &&
              tableConfig.apiConfig.api_query),
            filter: {...modifiedFilters},
            page: currentPage,
          },
        },
      };
      const response = pageConfig.api_version?.table_api === "v4"
        ? await getTableDataV4(client_id, payload)
        : await getTableData(client_id, payload);

      if (response.status !== "success") {
        throw new Error(response.message);
      }

      let finalTableConfig = deepCloneWithFunctions(tableConfig);
      if (response.columns?.length) {
        finalTableConfig.tableProps.columns = response.columns;
      } else if (
        response.hiddenColumns?.length &&
        finalTableConfig.tableProps?.columns?.length
      ) {
        finalTableConfig.tableProps.columns =
          finalTableConfig.tableProps.columns.filter(
            (column: any) => !response.hiddenColumns.includes(column.dataIndex),
          );
      }

      const pageData = response?.tableData || [];
      allData = allData.concat(pageData);
      hasMorePages = allData.length < totalDataCount;

      currentPage++;
    }

    return allData;
  };

  useEffect(() => {
    if (pageFilterRef?.current?.filterLoaded === true) {
      setDefaultPageNumber(1);
      fetchData(undefined, undefined, undefined, undefined, true);
      setBaseUrl(getBaseUrl(props, pageConfig?.page_url));
      setInitial(false);
    }
  }, [pageConfig, selectedPageFilters])

  useEffect(() => {
    setDefaultPageNumber(1);
    fetchData(undefined, undefined, undefined, undefined, true);
    setBaseUrl(getBaseUrl(props, pageConfig?.page_url));
    setInitial(false);
  }, [dateRangeTimes, selectedThing, filterValues]);

  useEffect(() => {
    if (pageConfig?.report_type === "multi_entity") {
      return;
    }
    const firstSelectedThing = allThingsData.find(
      (thing) => thing.id === selectedAssets[0],
    );
    if (firstSelectedThing && firstSelectedThing.parameters?.length) {
      const paramsArr = firstSelectedThing.parameters.filter(
        (param: { key: string }) =>
          !param.key.includes("debug") && !["lat", "long"].includes(param.key),
      );
      setSelectedThingData(firstSelectedThing);
      setParamList(paramsArr);
      if (filterValues.selectedParam !== firstSelectedThing.parameters[0].key) {
        setFilterValues((prevFilterValues: any) => ({
          ...prevFilterValues,
          selectedParam: firstSelectedThing.parameters[0].key,
        }));
      }
    }
  }, [selectedAssets]);

  useEffect(() => {
    if (
      !pageConfig ||
      !paramList ||
      !filterValues.selectedParam ||
      !graphConfigs.length
    )
      return;
    if (pageConfig.filters?.includes("parameters")) {
      const updatedGraphs = replaceParamKeyAndName(
        graphConfigs,
        filterValues.selectedParam,
        paramList,
      );
      if (JSON.stringify(updatedGraphs) !== JSON.stringify(pageConfig.graphs)) {
        setPageConfig((prevConfig: any) => ({
          ...prevConfig,
          graphs: updatedGraphs,
        }));
      }
    }
  }, [filterValues, paramList, graphConfigs]);

  useEffect(() => {
    if (
      !pageConfig ||
      (!allThingsData?.length && !(pageConfig?.report_type === "multi_entity" && !pageConfig.filters?.includes("things")))
    )
      return;

    const backgroundDownload = async () => {
      if (isScheduledDownload) {
        let inputDataSet = await window.getInputDataSet();

        const allThingsIds = allThingsData.map((thing) => thing.id);
        const assets = pageConfig?.report_type === "multi_entity" ? [1] : allThingsIds;
        const downloadFormat = inputDataSet?.type || "xlsx";
        const downloadProps = {
          reportTitle: inputDataSet?.name || pageConfig.title,
          customerName: client_name,
          userName: user_name,
          fromTime: inputDataSet?.from_time,
          uptoTime: inputDataSet?.upto_time,
          waterMark: is_white_label
            ? vendor_name
            : vendor_id === 1
              ? "Datoms"
              : vendor_name,
          downloadFormat: downloadFormat,
          columnConfig:
            tableColumnConfig || getCurrentTable()?.tableProps?.columns,
          fetchData,
          selectedAssets: inputDataSet?.things?.length ? inputDataSet.things : assets,
          allThingsData,
          assetDetailsExp: pageConfig.assetDetails,
          graphColors: pageConfig.graph_colors,
          timezone: user_preferences.timezone,
          clientLogo: client_logo,
          datomsLogo: datomsLogo,
          vendorLogo: vendor_logo,
          sortedInfo,
        };
        switch (downloadFormat) {
          case "pdf": {
            await downloadPDF(downloadProps);
            break;
          }
          case "xlsx": {
            await downloadXlsx(downloadProps);
            break;
          }
          case "csv": {
            await downloadCsv(downloadProps);
            break;
          }
          default: {
            break;
          }
        }
      }
    };
    backgroundDownload();
  }, [allThingsData, pageConfig]);

  useEffect(() => {
    if (
      !allThingsData.length ||
      (pageConfig?.report_type !== "multi_entity" && !listedThings?.length)
    )
      return;

    if (pageConfig?.report_type === "multi_entity") {
      setSelectedThing(listedThings?.length ? listedThings.join("_") : 1);
      return;
    }

    const thingsToDisplay = allThingsData.filter((thing) =>
      listedThings.includes(parseInt(thing.id)),
    );

    if (thingsToDisplay.length) {
      const firstThing = thingsToDisplay[0];
      setListData(thingsToDisplay);
      setSelectedThing(parseInt(firstThing.id));
    }
  }, [listedThings, allThingsData]);

  useEffect(() => {
    const reportType = pageConfig?.report_type;
    if (
      !pageConfig ||
      (reportType === "multi_entity" && !pageConfig.filters?.includes("things"))
    )
      return;

    const fetchThingsList = async () => {
      const [thingListResponse, territoryResponse] = await Promise.all([
        retriveThingsList({ application_id, client_id }, "?parameters=false"),
        getTerritoryData(
          client_id,
          client_name,
          pageConfig?.filters?.includes("territory"),
        ),
      ]);
      const thingsData = thingListResponse.things.filter(
        (thing: any) =>
          !pageConfig?.thingCategory ||
          thing?.category === pageConfig?.thingCategory,
      );
      setTerritoryData(territoryResponse?.territoryData || []);
      if (!thingsData) {
        setAllThingsData([]);
      } else if (JSON.stringify(thingsData) !== JSON.stringify(allThingsData)) {
        setAllThingsData(thingsData);
      }

      if (reportType !== "multi_entity") {
        if (thingsData?.length > 6) {
          if (!configFilters.includes("things")) {
            setConfigFilters((p) => [...p, "things"]);
          }
          setIsConfigModalVisible(true);
        } else {
          const newListData = thingsData;
          const newListedThings = thingsData.map(
            (thing: { id: any }) => thing.id,
          );
          const newSelectedAssets = thingsData.map(
            (thing: { id: any }) => thing.id,
          );

          if (JSON.stringify(newListData) !== JSON.stringify(listData)) {
            setListData(newListData);
          }

          if (
            JSON.stringify(newListedThings) !== JSON.stringify(listedThings)
          ) {
            setListedThings(newListedThings);
          }

          if (
            JSON.stringify(newSelectedAssets) !== JSON.stringify(selectedAssets)
          ) {
            setSelectedAssets(newSelectedAssets);
          }
        }
      } else {
        setListedThings([1]);
      }

      setPageLoading(false);
    };
    fetchThingsList();
  }, [pageConfig]);

  useEffect(() => {
    const fetchPageConfig = async () => {
      // const response = { data: { config: dummyConfig } };
      const response = await getPageConfig(page_id);
      const pageConfigObj = response?.data?.config || {};
      setPageConfig(pageConfigObj || {});
      setConfigFilters(pageConfigObj.filters || []);
      setGraphConfigs(pageConfigObj?.graphs || []);
      const {filters, depFilters} = processPageFilters(pageConfigObj?.page_filters ||[]);
      setPageFilters(filters);
      setDependentPageFilters(depFilters);

      console.log("Page Filters", pageConfigObj?.page_filters);
      if (pageConfigObj?.default_time_range === "current_hour") {
        setDateRangeTimes({
          fromTime: moment().startOf("hour").unix(),
          uptoTime: moment().endOf("hour").unix(),
        });
      } else if (
        pageConfigObj?.defaultDateRange &&
        defaultRanges[pageConfigObj.defaultDateRange]
      ) {
        setDateRangeTimes({
          fromTime: defaultRanges[pageConfigObj.defaultDateRange][0].unix(),
          uptoTime: defaultRanges[pageConfigObj.defaultDateRange][1].unix(),
        });
      }
      if (
        pageConfigObj?.filters?.length &&
        pageConfigObj?.defaultFilterValues
      ) {
        setFilterValues({
          ...pageConfigObj.defaultFilterValues,
          aggregation_period:
            pageConfigObj.defaultFilterValues.aggregation_period ||
            pageConfigObj?.default_aggregation_period,
        });
      } else {
        setFilterValues({
          ...filterValues,
          aggregation_period: pageConfigObj?.default_aggregation_period,
        });
      }
    };
    fetchPageConfig();
  }, []);

  let graphComponents: any[] = [];
  chartData.forEach((graph: any) => {
    const colSpan = graph.colSpan;
    graphComponents.push(
      <AntCol span={colSpan} key={graph.key}>
        <ConfigurableChart
          config={{ ...graph.config, graph_colors: pageConfig.graph_colors }}
          data={graph.data}
          loading={componentLoading.graph}
        />
      </AntCol>,
    );
  });

  const tableComponents: any[] = [];
  let dataAvailable = false;
  tableData?.forEach((table, index) => {
    const colSpan = table?.colSpan || 24;
    const config = {
      ...table.config,
      disableCustomization:
        table.config.disableCustomization || !isCustomizeFeatureEnabled,
        pageChangeActions: {
          pageNumChange: (page: number) => {
            console.log("pageNumChange", page);
            // setTableInnerLoading(true);
            setPagination((prev) => ({ ...prev, page_no: page }))
          },
          pageSizeChange: (pageSize: number) => {
            console.log("pageSizeChange", pageSize);
            // setTableInnerLoading(true);
            setPagination((prev) => ({ ...prev, page_size: pageSize }))
          }
        },
    };
    console.log("config", config);
    dataAvailable = dataAvailable || table.data?.length > 0;
    tableComponents.push(
      <AntCol span={colSpan} key={index}>
        <ConfigurableTable
          tableStyle={page_id === 56 ? { marginTop: "0px" } : undefined}
          config={_cloneDeep(config)}
          data={table.data}
          loading={componentLoading.table}
          updateColumnConfig={updateColumnConfig}
          orgPreferenceKey={pageConfig?.orgPreferenceKey}
          pagination={{
            page_no: pagination.page_no,
            page_size: pagination.page_size,
            total: table?.pagination?.total || table?.totalDataCount || 0,
          }}
          sortedInfo={sortedInfo}
          updateSortedInfo={setSortedInfo}
        />
      </AntCol>,
    );
  });
  const summaryComponent: any[] = [];
  summaryData?.forEach((summary, index) => {
    summaryComponent.push(
      <AntCol span={24} key={index}>
        <ConfigurableSummary
          orgPreferenceKey={pageConfig?.orgPreferenceKey}
          config={summary.config.config}
          data={summary.data}
          loading={componentLoading.summary}
          preferenceKeys={isCustomizeFeatureEnabled ? summary.config.preferenceKeys : []}
        />
      </AntCol>,
    );
  });

  useEffect(() => {
    const currAssetData = allThingsData.find(
      (thing) => thing.id === selectedThing,
    );
    setSelectedThingData(currAssetData);
    const assetInfo = assetDetails(pageConfig?.assetDetails, currAssetData);
    setMachineInfo(assetInfo);
  }, [selectedThing]);

  const assetDetailsContent = (
    <div className="asset-details-container">
      {componentLoading?.machineInfo ? (
        <SkeletonLoader active />
      ) : (
        machineInfo.map((item, index) => (
          <div key={index} className="asset-detail-item">
            <span>
              {item.label} - {item.value}
            </span>
            {index < machineInfo.length - 1 && <Divider type="vertical" />}
          </div>
        ))
      )}
    </div>
  );

  const getMaxPastDateAllowed = () => {
    let maxPastMonths = pageConfig?.past_time_limit_months || 36;
    const planAllowedMonths = parseInt(
      props.plan_description?.maximum_pre_defined_report_time_aggr_allowed,
    );
    if (!isNaN(planAllowedMonths)) {
      maxPastMonths = Math.min(maxPastMonths, planAllowedMonths);
    }
    const no_of_days = moment().diff(
      moment().startOf("month").subtract(maxPastMonths, "months"),
      "days",
    );

    return no_of_days;
  };

  const getDownloadFilterArray = () => {
    const filterArray = [];
    if (contextClientId !== client_id) {
      filterArray.push({
        title: "Customer Name",
        value: props.client_name,
      });
    }
    let assetsString: string = "";
    console.log("filtersDownload 0", listedThings);
    if (listedThings?.length) {
      listedThings.forEach((thing: number, index: number) => {
        const findAsset = allThingsData.find((asset) => thing === asset.id);
        console.log("filtersDownload 1", findAsset);
        if (!findAsset?.name) return;
        assetsString +=
          findAsset?.name + (index === listedThings.length - 1 ? "" : ", ");
      });
    }
    if (assetsString) {
      filterArray.push({
        title: "Assets",
        value: assetsString,
      });
    }
    return filterArray;
  };

  const loadingComponent = (
    <div style={{ marginTop: 24, marginLeft: 32, marginRight: 24 }}>
      <SkeletonLoader rows={4} />
    </div>
  );

  return (
    <div id="gravity_report_page" className="basic-page-layout-height">
      {pageLoading ? (
        loadingComponent
      ) : (
        <>
          <ReportHeader
            pageConfig={pageConfig || {}}
            filters={configFilters}
            pageFilterObject={{
              initial,
              pagination,
              filterRef: pageFilterRef,
              filters: pageFilters,
              dependentFilters: dependentPageFilters,
              setSelectedFilters: setSelectedPageFilters,
              setPagination: setPagination,
              history: history,
              baseUrl: baseUrl,
            }}
            dateRangeTimes={dateRangeTimes}
            setDateRangeTimes={setDateRangeTimes}
            selectedThings={listedThings}
            setSelectedThings={setListedThings}
            paramList={paramList}
            reportName={pageConfig?.title}
            maxTimeRangeInDays={pageConfig?.max_time_range_days}
            pastTimeLimitDays={getMaxPastDateAllowed()}
            dateRangeFormat={pageConfig?.date_range_format}
            dateRangePicker={pageConfig?.date_range_picker}
            aggregationPeriodOptions={pageConfig?.aggregation_period_options}
            headerText={pageConfig?.title}
            isConfigModalVisible={isConfigModalVisible}
            setIsConfigModalVisible={setIsConfigModalVisible}
            allThingsData={allThingsData}
            datePickerRanges={pageConfig?.datePickerRanges}
            datePickerConfig={pageConfig?.datePickerConfig}
            customRangePickerEnabled={pageConfig?.customRangePickerEnabled}
            defaultDateRange={pageConfig?.defaultDateRange}
            selectedAssets={selectedAssets}
            setSelectedAssets={setSelectedAssets}
            machineInfo={machineInfo}
            selectedThingData={selectedThingData}
            summaryData={summaryData}
            tableData={tableData}
            chartDimensions={pageConfig?.pdf_chart_dimensions}
            columnConfig={tableColumnConfig}
            fetchData={fetchData}
            assetDetailsExp={pageConfig?.assetDetails}
            graphColors={pageConfig?.graph_colors}
            filterValues={filterValues}
            setFilterValues={setFilterValues}
            territoryData={territoryData}
            sortedInfo={sortedInfo}
            datePickerFormat={pageConfig?.datePickerFormat}
            datePickerInterval={pageConfig?.datePickerInterval}
            defaultDateSelector={pageConfig?.defaultDateSelector}
            getDownloadFilterArray={getDownloadFilterArray}
          />

          {pageConfig?.report_type === "multi_entity" ? (
            <>
              {loading ? (
                loadingComponent
              ) : dataAvailable === false ? (
                <div
                  className="report-no-data"
                  style={{ paddingBottom: "10%" }}
                >
                  <img
                    src={NoDataImage}
                    alt="no-data"
                    className="no-data-image"
                  />
                  <div className="no-data-p">
                    {pageConfig?.no_data_text || "No Data Available"}
                  </div>
                </div>
              ) : (
                <div className="content-container multi-entity-container">
                  {pageConfig?.showAssetDetails ? assetDetailsContent : null}
                  {pageConfig?.summary?.length ? (
                    <AntRow gutter={16}>{summaryComponent}</AntRow>
                  ) : null}
                  {getCurrentGraph()?.length ? (
                    <AntRow gutter={16}>{graphComponents}</AntRow>
                  ) : null}
                  {getCurrentTable()?.length ? (
                    <AntRow gutter={16}>{tableComponents}</AntRow>
                  ) : null}
                </div>
              )}
            </>
          ) : (
            <Tabs
              activeKey={
                selectedThing
                  ? String(selectedThing)
                  : listData.length > 0
                    ? String(listData[0].id)
                    : undefined
              }
              onChange={(key) => setSelectedThing(parseInt(key))}
              type="card"
              destroyInactiveTabPane
            >
              {listData.map((thing) => (
                <TabPane tab={thing.name} key={thing.id}>
                  {loading ? (
                    loadingComponent
                  ) : dataAvailable === false ? (
                    <div className="report-no-data">
                      <img
                        src={NoDataImage}
                        alt="no-data"
                        className="no-data-image"
                      />
                      <div className="no-data-p">
                        {pageConfig?.no_data_text || "No Data Available"}
                      </div>
                    </div>
                  ) : (
                    <div className="content-container">
                      {pageConfig?.showAssetDetails
                        ? assetDetailsContent
                        : null}
                      {pageConfig?.summary?.length ? (
                        <AntRow gutter={16}>{summaryComponent}</AntRow>
                      ) : null}
                      {getCurrentGraph()?.length ? (
                        <AntRow gutter={16}>{graphComponents}</AntRow>
                      ) : null}
                      {getCurrentTable()?.length ? (
                        <AntRow gutter={16}>{tableComponents}</AntRow>
                      ) : null}
                    </div>
                  )}
                </TabPane>
              ))}
            </Tabs>
          )}
        </>
      )}
    </div>
  );
};

function deepCloneWithFunctions<T>(obj: T): T {
  if (obj === null || typeof obj !== "object") {
    return obj; // Return primitives and functions as-is
  }

  if (Array.isArray(obj)) {
    return obj.map(deepCloneWithFunctions) as unknown as T; // Recursively clone arrays
  }

  const clonedObj = {} as T;
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      (clonedObj as any)[key] = deepCloneWithFunctions((obj as any)[key]);
    }
  }
  return clonedObj;
}

export default withRouter(GravityReport);
