import React, { ReactNode } from 'react';
import { Drawer as <PERSON><PERSON><PERSON><PERSON><PERSON>, DrawerP<PERSON>, ConfigProvider, theme } from 'antd';
import './style.less';

/**
 * Props for the Drawer component
 * Extends Ant Design's DrawerProps to support all standard Drawer properties
 */
interface CustomDrawerProps extends DrawerProps {
  /**
   * Custom z-index for the drawer
   */
  zIndex?: number;
  
  /**
   * Children to render inside the drawer
   */
  children: ReactNode;
}

/**
 * Drawer Component
 *
 * A customizable drawer component built on top of Ant Design's Drawer component.
 * Allows customizing z-index through the ConfigProvider.
 *
 * @component
 * @example
 * // Basic usage
 * <Drawer open={isOpen} onClose={closeDrawer}>
 *   <YourContent />
 * </Drawer>
 *
 * @example
 * // With custom z-index
 * <Drawer
 *   open={isOpen}
 *   onClose={closeDrawer}
 *   zIndex={1000}
 *   width={320}
 * >
 *   <YourContent />
 * </Drawer>
 *
 * @param props - Component props including all standard Ant Design Drawer props
 * @returns A styled drawer component
 */
const Drawer: React.FC<CustomDrawerProps> = (props) => {
  const { zIndex, children, ...drawerProps } = props;

  // Get theme token from Ant Design's theme context
  const { token } = theme.useToken();

  /**
   * Create a custom theme for the Drawer using Ant Design's theme customization
   * This applies our styling to the Drawer component without affecting other components
   */
  const customTheme = {
    components: {
      Drawer: {
        zIndexPopup: zIndex || 1000,
      },
    },
  };

  // Default drawer props that can be overridden
  const mergedDrawerProps: DrawerProps = {
    placement: 'right',
    closable: false,
    mask: false,
    getContainer: false,
    forceRender: true,
    className: 'custom-drawer',
    ...drawerProps, // Allow overriding of defaults
  };

  return (
    <ConfigProvider theme={customTheme}>
      <AntDrawer {...mergedDrawerProps}>
        {children}
      </AntDrawer>
    </ConfigProvider>
  );
};

export default Drawer;