import React from 'react'
import { ArrowLeftOutlined } from "@ant-design/icons";
import { Button, ConfigProvider, theme } from "antd";
import './style.less';

const BackIcon = ({ onBack }: { onBack: () => void }) => {
  const { token } = theme.useToken();
  return (
    <ConfigProvider
      theme={{
        components: {
          Button: {
            defaultBg: "#FFFFFF",
            defaultColor: "#000000",
            defaultBorderColor: "#7686A1",
            defaultHoverBg: token.colorPrimary,
            defaultHoverColor: "#fff",
            defaultActiveBg: token.colorPrimary,
            defaultActiveColor: "#fff",
          },
        },
      }}
    >
      <Button
        size="small"
        className="back-icon"
        shape="circle"
        onClick={onBack}
      >
        <ArrowLeftOutlined className='bold-icon' style={{ fontSize: 12 }} />
      </Button>
    </ConfigProvider>
  )
}

export default BackIcon