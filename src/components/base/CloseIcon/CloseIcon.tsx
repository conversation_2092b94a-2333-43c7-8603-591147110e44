import React from 'react'
import { CloseOutlined } from "@ant-design/icons";
import { But<PERSON>, ConfigProvider, theme } from "antd";
import './style.less';

const CloseIcon = ({ onClose }: { onClose: () => void }) => {
    const { token } = theme.useToken();
  return (
    <ConfigProvider
          theme={{
            components: {
              Button: {
                defaultBg: "#E4E6EC",
                defaultColor: "#7686A1",
                defaultHoverBg: token.colorPrimary,
                defaultHoverColor: "#fff",
                defaultActiveBg: token.colorPrimary,
                defaultActiveColor: "#fff",
              },
            },
          }}
        >
          <Button
            size="small"
            className="close-icon"
            shape="circle"
            onClick={onClose}
          >
            <CloseOutlined className='bold-icon' style={{ fontSize: 12 }} />
          </Button>
        </ConfigProvider>
  )
}

export default CloseIcon