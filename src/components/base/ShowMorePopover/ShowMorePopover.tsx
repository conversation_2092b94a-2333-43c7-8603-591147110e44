import React from "react";
import { Popover } from "antd";
import "./style.less";

interface ShowMorePopoverProps<T> {
  /**
   * Array of items to display in the popover
   */
  items: T[];
  
  /**
   * Text to display in the "show more" button
   */
  text: string;
  
  /**
   * Optional render function for each item
   * If not provided, items will be rendered as strings
   */
  renderItem?: (item: T, index: number) => React.ReactNode;
  
  /**
   * Optional class name for the popover
   */
  overlayClassName?: string;
  
  /**
   * Optional class name for the container
   */
  className?: string;

  /**
   * Optional title for the popover
   */
  title?: React.ReactNode;
  
  /**
   * Optional placement for the popover
   */
  placement?: 'top' | 'left' | 'right' | 'bottom' | 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight' | 'leftTop' | 'leftBottom' | 'rightTop' | 'rightBottom';
}

const ShowMorePopover = <T extends any>({
  items,
  text,
  renderItem,
  overlayClassName = "items-popover",
  className = "",
  title,
  placement = "bottom"
}: ShowMorePopoverProps<T>) => {
  const PopoverContent = () => (
    <ul className="popover-items">
      {items?.map((item, index) => (
        <li key={`item-${index}`} className="popover-item">
          {renderItem ? renderItem(item, index) : String(item)}
        </li>
      ))}
    </ul>
  );
  
  return (
    <div className={`show-more-popover-container ${className}`}>
      <Popover
        content={<PopoverContent />}
        title={title}
        trigger="click"
        placement={placement}
        overlayClassName={overlayClassName}
      >
        <div className="more-count">{text}</div>
      </Popover>
    </div>
  );
};

export default ShowMorePopover;
