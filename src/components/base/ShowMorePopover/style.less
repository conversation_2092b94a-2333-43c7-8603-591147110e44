.show-more-popover-container {
  display: inline-block;
  white-space: nowrap;

  .more-count {
    padding: 2px 8px;
    background-color: #f0f0f0;
    border-radius: 7px;
    font-size: 11px;
    color: #808080;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    
    &:hover {
      background-color: #e5e5e5;
      border-color: #d9d9d9;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }
    
    &:active {
      background-color: #d9d9d9;
      box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
      transform: translateY(1px);
    }
  }
}

// Generic styling for items popover
.items-popover {
  max-width: 300px;
  
  .ant-popover-inner-content {
    padding: 12px 12px 0 0;
    max-height: 350px;
    overflow-y: auto;
    width: max-content;
    max-width: 100%;
  }
}

.popover-items {
  padding-right: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  
//   .popover-item {
//     // background-color: rgba(0, 0, 0, 0.03);
//     border-radius: 4px;
//     line-height: 1.2;
//     margin: 2px;
//   }
}

