import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { EnvironmentOutlined, SettingOutlined } from '@ant-design/icons';
import SegmentedTabs from './SegmentedTabs';

describe('SegmentedTabs', () => {
  // Basic rendering tests
  describe('Rendering', () => {
    it('renders with basic options', () => {
      render(<SegmentedTabs options={['Daily', 'Weekly', 'Monthly']} />);
      
      expect(screen.getByText('Daily')).toBeInTheDocument();
      expect(screen.getByText('Weekly')).toBeInTheDocument();
      expect(screen.getByText('Monthly')).toBeInTheDocument();
    });

    it('renders with icons and labels', () => {
      const options = [
        { label: 'Site', value: 'site', icon: <EnvironmentOutlined /> },
        { label: 'Asset', value: 'asset', icon: <SettingOutlined /> },
      ];

      render(<SegmentedTabs options={options} />);
      
      expect(screen.getByText('Site')).toBeInTheDocument();
      expect(screen.getByText('Asset')).toBeInTheDocument();
    });
  });

  // Configuration tests
  describe('Configuration', () => {
    it('applies rectangular shape by default', () => {
      render(<SegmentedTabs options={['Option 1', 'Option 2']} />);
      
      const container = screen.getByRole('tablist').closest('#segmented-tabs');
      expect(container).toHaveClass('shape-rectangular');
    });

    it('applies rounded shape when configured', () => {
      render(
        <SegmentedTabs 
          options={['Option 1', 'Option 2']} 
          segmentedConfig={{ type: 'rounded' }}
        />
      );
      
      const container = screen.getByRole('tablist').closest('#segmented-tabs');
      expect(container).toHaveClass('shape-rounded');
    });

    it('applies default size by default', () => {
      const { container } = render(
        <SegmentedTabs options={['Option 1', 'Option 2']} />
      );
      
      // Check if the Ant Design Segmented component has the correct size class
      const segmented = container.querySelector('.ant-segmented');
      expect(segmented).toHaveClass('ant-segmented-sm');
    });

    it('applies large size when configured', () => {
      const { container } = render(
        <SegmentedTabs 
          options={['Option 1', 'Option 2']} 
          segmentedConfig={{ size: 'large' }}
        />
      );
      
      const segmented = container.querySelector('.ant-segmented');
      expect(segmented).toHaveClass('ant-segmented-lg');
    });
  });

  // Interaction tests
  describe('Interactions', () => {
    it('calls onChange when option is selected', () => {
      const handleChange = jest.fn();
      render(
        <SegmentedTabs 
          options={['Option 1', 'Option 2']} 
          onChange={handleChange}
        />
      );
      
      fireEvent.click(screen.getByText('Option 2'));
      expect(handleChange).toHaveBeenCalledWith('Option 2');
    });

    it('maintains selected value', () => {
      const { rerender } = render(
        <SegmentedTabs 
          options={['Option 1', 'Option 2']} 
          value="Option 2"
        />
      );
      
      const option2 = screen.getByText('Option 2').closest('.ant-segmented-item');
      expect(option2).toHaveClass('ant-segmented-item-selected');

      // Test that it updates when value prop changes
      rerender(
        <SegmentedTabs 
          options={['Option 1', 'Option 2']} 
          value="Option 1"
        />
      );
      
      const option1 = screen.getByText('Option 1').closest('.ant-segmented-item');
      expect(option1).toHaveClass('ant-segmented-item-selected');
    });
  });

  // Edge cases
  describe('Edge cases', () => {
    it('handles empty options array', () => {
      render(<SegmentedTabs options={[]} />);
      const container = screen.getByRole('tablist');
      expect(container).toBeInTheDocument();
    });

    it('handles single option', () => {
      render(<SegmentedTabs options={['Single']} />);
      expect(screen.getByText('Single')).toBeInTheDocument();
    });

    it('handles undefined segmentedConfig', () => {
      render(<SegmentedTabs options={['Option 1']} segmentedConfig={undefined} />);
      const container = screen.getByRole('tablist').closest('#segmented-tabs');
      expect(container).toHaveClass('shape-rectangular');
    });
  });
});