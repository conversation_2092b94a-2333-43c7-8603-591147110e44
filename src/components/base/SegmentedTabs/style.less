#segmented-tabs {
  .ant-segmented-item {
    .ant-segmented-item-label {
      padding: 0 16px;
    }
    // &-selected {
    //   font-weight: 600;
    // }
  }
  // Shape variants
  &.shape-rounded {
    .ant-segmented {
      border-radius: 20px;

      .ant-segmented-item {
        border-radius: 20px;
      }
    }
  }

  &.shape-rectangular {
    .ant-segmented {
      border-radius: 6px;

      .ant-segmented-item {
        border-radius: 0;
        &:first-child {
          border-top-left-radius: 6px;
          border-bottom-left-radius: 6px;
        }

        &:last-child {
          border-top-right-radius: 6px;
          border-bottom-right-radius: 6px;
        }
      }
    }
  }
}
