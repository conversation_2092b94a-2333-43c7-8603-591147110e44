import React from "react";
import { Segmented, SegmentedProps, ConfigProvider } from "antd";
import type { SizeType } from "antd/es/config-provider/SizeContext";
import "./style.less";

/**
 * A customizable segmented tabs component built on top of Ant Design's Segmented component.
 * Provides additional styling options while maintaining all standard Segmented functionality.
 * 
 * @component
 * @example
 * ```tsx
 * // Basic usage
 * <SegmentedTabs 
 *   options={['Daily', 'Weekly', 'Monthly']} 
 * />
 * 
 * // With custom configuration
 * <SegmentedTabs 
 *   options={[
 *     { label: 'Site', value: 'site', icon: <EnvironmentOutlined /> },
 *     { label: 'Asset', value: 'asset', icon: <SettingOutlined /> }
 *   ]}
 *   segmentedConfig={{
 *     type: 'rounded',
 *     size: 'large',
 *     trackPadding: 4
 *   }}
 * />
 * ```
 * 
 * @props
 * - All props from Ant Design's Segmented component (except 'size')
 * - segmentedConfig: Additional configuration options
 *   - type: 'rounded' | 'rectangular' - Shape of the tabs (default: 'rectangular')
 *   - size: 'default' | 'large' - Size of the tabs (default: 'default')
 *   - trackPadding: Number - Padding around the track (default: 0)
 */

interface SegmentedTabsProps extends Omit<SegmentedProps, 'size'> {
  segmentedConfig?: {
    type?: "rounded" | "rectangular";
    size?: "default" | "large";
    trackPadding?: number;
  };
}

const SegmentedTabs: React.FC<SegmentedTabsProps> = (props) => {
  const { segmentedConfig, ...segmentedProps } = props;
  
  const segmentedMergedConfig = {
    type: "rectangular",
    size: "default",
    trackPadding: 0,
    ...segmentedConfig,
  };

  // Map our size prop to Antd's size prop
  const antdSize: SizeType = segmentedMergedConfig.size === "default" ? "middle" : "large";

  const mergedSegmentedProps = {
    size: antdSize,
    ...segmentedProps,
  };

  // Create a custom theme for the SegmentedTabs
  const customTheme = {
    components: {
      Segmented: {
        itemHoverColor: '#808080',
        trackBg: '#F3F3F4',
        trackPadding: segmentedMergedConfig.trackPadding
      },
    },
  };

  return (
    <div id="segmented-tabs" className={`shape-${segmentedMergedConfig.type}`}>
      <ConfigProvider theme={customTheme}>
        <Segmented {...mergedSegmentedProps} />
      </ConfigProvider>
    </div>
  );
};

export default SegmentedTabs;

