import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { EnvironmentOutlined, SettingOutlined } from '@ant-design/icons';
import SegmentedTabs from './SegmentedTabs';

const meta: Meta<typeof SegmentedTabs> = {
  title: 'Components/Base/SegmentedTabs',
  component: SegmentedTabs,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof SegmentedTabs>;

// Basic usage with text only options
export const Default: Story = {
  args: {
    options: ['Daily', 'Weekly', 'Monthly'],
  },
};

// Rounded shape with default size
export const RoundedShape: Story = {
  args: {
    options: ['Daily', 'Weekly', 'Monthly'],
    segmentedConfig: {
      type: 'rounded',
      size: 'default',
    },
  },
};

// Rectangular shape with large size
export const LargeSize: Story = {
  args: {
    options: ['Daily', 'Weekly', 'Monthly'],
    segmentedConfig: {
      type: 'rectangular',
      size: 'large',
    },
  },
};

// With icons and text
export const WithIcons: Story = {
  args: {
    options: [
      {
        label: 'Site',
        value: 'site',
        icon: <EnvironmentOutlined />,
      },
      {
        label: 'Asset',
        value: 'asset',
        icon: <SettingOutlined />,
      },
    ],
    segmentedConfig: {
      type: 'rounded',
      size: 'large',
    },
  },
};

// With track padding
export const WithTrackPadding: Story = {
  args: {
    options: ['Option 1', 'Option 2', 'Option 3'],
    segmentedConfig: {
      type: 'rectangular',
      size: 'default',
      trackPadding: 4,
    },
  },
};

// All combinations in one story
export const AllCombinations: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
      <div>
        <h3>Default Size, Rectangular</h3>
        <SegmentedTabs 
          options={['Option 1', 'Option 2', 'Option 3']}
          segmentedConfig={{ type: 'rectangular', size: 'default' }}
        />
      </div>

      <div>
        <h3>Default Size, Rounded</h3>
        <SegmentedTabs 
          options={['Option 1', 'Option 2', 'Option 3']}
          segmentedConfig={{ type: 'rounded', size: 'default' }}
        />
      </div>

      <div>
        <h3>Large Size, Rectangular</h3>
        <SegmentedTabs 
          options={['Option 1', 'Option 2', 'Option 3']}
          segmentedConfig={{ type: 'rectangular', size: 'large' }}
        />
      </div>

      <div>
        <h3>Large Size, Rounded</h3>
        <SegmentedTabs 
          options={['Option 1', 'Option 2', 'Option 3']}
          segmentedConfig={{ type: 'rounded', size: 'large' }}
        />
      </div>

      <div>
        <h3>With Icons, Large Size, Rounded</h3>
        <SegmentedTabs 
          options={[
            { label: 'Site', value: 'site', icon: <EnvironmentOutlined /> },
            { label: 'Asset', value: 'asset', icon: <SettingOutlined /> },
          ]}
          segmentedConfig={{ type: 'rounded', size: 'large' }}
        />
      </div>
    </div>
  ),
};