import React from "react";
import { <PERSON>Fn, Meta } from "@storybook/react";
import SearchBar from "./index";
import {
  Canvas,
  Source,
  Stories,
  Subtitle,
  Title,
  Controls,
} from "@storybook/blocks";
import { Table } from "@storybook/components";

interface RowProps {
  key: string;
  required: string;
  allowedValues: string;
  description: string;
  defaultValue: string;
}

const CustomPropsTable = ({ rows }: { rows: RowProps[] }) => (
  <Table>
    <thead>
      <tr>
        <th>Key</th>
        <th>Required</th>
        <th>Allowed Values</th>
        <th>Default Value</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      {rows?.map((row: RowProps) => (
        <tr key={row.key}>
          <td>{row.key}</td>
          <td>{row.required}</td>
          <td>{row.allowedValues}</td>
          <td>{row.defaultValue}</td>
          <td>{row.description}</td>
        </tr>
      ))}
    </tbody>
  </Table>
);

export default {
  title: "Components/SearchBar",
  component: SearchBar,
  tags: ["autodocs"],
  argTypes: {
    placeholder: {
      control: "text",
      description: "Placeholder text for the search input",
    },
    size: {
      control: { type: "select", options: ["large", "middle", "small"] },
      description: "Size of the search input",
    },
    searchBarConfig: {
      control: "object",
      description: "Configuration object for the search bar appearance",
    },
  },
  parameters: {
    componentSubtitle: "A customizable search bar component",
    docs: {
      page: () => (
        <>
          <Title>SearchBar</Title>
          <p>
            The SearchBar component provides a customizable search input with different styling options.
          </p>
          <CustomPropsTable
            rows={[
              {
                key: "placeholder",
                required: "N",
                allowedValues: "string",
                defaultValue: "Search",
                description: "Placeholder text for the search input",
              },
              {
                key: "size",
                required: "N",
                allowedValues: "'large' | 'middle' ",
                defaultValue: "middle",
                description: "Size of the search input",
              },
              {
                key: "searchBarConfig.type",
                required: "N",
                allowedValues: "'rounded' | 'rectangular'",
                defaultValue: "rounded",
                description: "Shape of the search bar",
              },
              {
                key: "searchBarConfig.color.bg",
                required: "N",
                allowedValues: "string (hex color)",
                defaultValue: "#FBFDFF",
                description: "Background color of the search bar",
              },
              {
                key: "searchBarConfig.color.border",
                required: "N",
                allowedValues: "string (hex color)",
                defaultValue: "#DFE0EC",
                description: "Border color of the search bar",
              },
              {
                key: "allowClear",
                required: "N",
                allowedValues: "boolean",
                defaultValue: "true",
                description: "Whether to show clear button",
              },
            ]}
          />
          <Subtitle />
          <Source of={DefaultSearchBar} />
          <Stories />
          <Subtitle>Interactive Examples</Subtitle>
          <Canvas of={DefaultSearchBar} />
          <Controls of={DefaultSearchBar} />
        </>
      ),
    },
  },
} as Meta;

const Template: StoryFn<{
  placeholder?: string;
  size?: "large" | "middle";
  searchBarConfig?: {
    type?: "rounded" | "rectangular";
    color?: {
      bg?: string;
      border?: string;
    };
  };
}> = (args) => (
  <div style={{ padding: "20px", maxWidth: "400px" }}>
    <SearchBar {...args} />
  </div>
);

export const DefaultSearchBar = Template.bind({});
DefaultSearchBar.args = {};

export const LargeSearchBar = Template.bind({});
LargeSearchBar.args = {
  placeholder: "Search items...",
  size: "large",
};

export const RectangularSearchBar = Template.bind({});
RectangularSearchBar.args = {
  placeholder: "Search",
  searchBarConfig: {
    type: "rectangular",
  },
};

export const CustomColorSearchBar = Template.bind({});
CustomColorSearchBar.args = {
  placeholder: "Search with custom colors",
  searchBarConfig: {
    color: {
      bg: "#F0F8FF",
      border: "#1890FF",
    },
  },
};

export const CustomSearchBar = Template.bind({});
CustomSearchBar.args = {
  placeholder: "Custom search bar",
  size: "large",
  searchBarConfig: {
    type: "rectangular",
    color: {
      bg: "#F6FFED",
      border: "#52C41A",
    },
  },
};
