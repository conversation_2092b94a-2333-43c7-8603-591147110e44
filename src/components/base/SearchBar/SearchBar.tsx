import React from "react";
import { Input, InputProps, ConfigProvider, theme } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import "./style.less";

/**
 * Configuration options for the SearchBar component
 */
interface SearchBarConfig {
  /**
   * The shape of the search bar
   * @default "rounded"
   */
  type?: "rounded" | "rectangular";

  /**
   * Color customization options
   */
  color?: {
    /**
     * Background color of the search bar
     * @default "#FBFDFF"
     */
    bg?: string;

    /**
     * Border color of the search bar
     * @default "#D5E0EC"
     */
    border?: string;
  };
}

/**
 * Props for the SearchBar component
 * Extends Ant Design's InputProps to support all standard Input properties
 */
interface SearchBarProps extends InputProps {
  /**
   * Configuration options for customizing the appearance of the search bar
   */
  searchBarConfig?: SearchBarConfig;
  
  /**
   * Callback function that is called when the search input changes.
   * It receives the current search text as a parameter.
   * This can be used for live search functionality.
   * @param searchText The current text in the search input
   */
  onSearch?: (searchText: string) => void;
}

/**
 * SearchBar Component
 *
 * A customizable search input component built on top of Ant Design's Input component.
 * Supports different shapes (rounded/rectangular) and custom colors for background and border.
 * Provides real-time search capabilities through the onSearch callback.
 *
 * @component
 * @example
 * // Basic usage with default styling
 * <SearchBar placeholder="Search items..." />
 *
 * @example
 * // With custom styling
 * <SearchBar
 *   placeholder="Find something..."
 *   size="large"
 *   searchBarConfig={{
 *     type: "rectangular",
 *     color: {
 *       bg: "#F0F8FF",
 *       border: "#1890FF"
 *     }
 *   }}
 * />
 *
 * @param props - Component props including all standard Ant Design Input props
 * @returns A styled search input component
 */
const SearchBar: React.FC<SearchBarProps> = (props) => {
  const { searchBarConfig, onSearch, ...inputProps } = props;

  // Get theme token from Ant Design's theme context
  const { token } = theme.useToken();

  const searchBarMergedConfig = {
    type: "rounded", 
    color: {
      bg: "#FBFDFF",
      border: "#D5E0EC",
      ...searchBarConfig?.color,
    },
    ...searchBarConfig, 
  };

  const mergedInputProps = {
    placeholder: "Search",
    allowClear: true,
    prefix: <SearchOutlined />,
    ...inputProps,
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
      if (typeof onSearch === 'function') {
        onSearch(e.target.value);
      }
    },
  };

  const borderRadius = searchBarMergedConfig.type === "rounded" ? "99px" : "6px";
  const backgroundColor = searchBarMergedConfig.color.bg;
  const borderColor = searchBarMergedConfig.color.border;

  /**
   * Create a custom theme for the SearchBar using Ant Design's theme customization
   * This applies our styling to the Input component without affecting other components
   */
  const customTheme = {
    components: {
      Input: {
        activeBorderColor: token.colorPrimary, 
        hoverBorderColor: token.colorPrimary, 
        activeShadow: 'none', 
        borderRadius: parseInt(borderRadius),  
        borderRadiusLG: parseInt(borderRadius),
        colorBgContainer: backgroundColor, 
        colorBorder: borderColor,
      },
    },
  };

  return (
    <div id="search-bar">
      <ConfigProvider theme={customTheme}>
        <Input {...mergedInputProps} />
      </ConfigProvider>
    </div>
  );
};

export default SearchBar;
