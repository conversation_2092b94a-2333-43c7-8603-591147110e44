import { render, screen, act, waitFor } from '@testing-library/react';
import { useListingPageLogic } from './logic/useListingPageLogic';
import {
  getPageConfig,
  getTableDataV4,
  establishSocketConnection,
  disconnectSocketConnection,
} from '@datoms/js-sdk';
import pageConfigData from './page-config.json';

// Mock dependencies
jest.mock('@datoms/js-sdk', () => ({
  getPageConfig: jest.fn(),
  getTableDataV4: jest.fn(),
  establishSocketConnection: jest.fn(),
  disconnectSocketConnection: jest.fn(),
}));

jest.mock('lodash/cloneDeep', () => jest.fn(obj => JSON.parse(JSON.stringify(obj))));

jest.mock('../../../store/globalStore', () => ({
  useGlobalContext: jest.fn(() => ({
    enabled_features: [ // Example: Enable all features from config for simplicity
      "UserManagement:Assignment",
      "UserManagement:AccountType",
      "UserManagement:Territory",
      "UserManagement:Partner",
      "UserManagement:Customer",
      "UserManagement:DeviceType",
      "UserManagement:FirmwareVersion",
      "UserManagement:DeviceConnectivity",
      "UserManagement:DeviceStatus",
      "DeviceManagement:Firmware",
      "DeviceManagement:Debug",
      "DeviceManagement:RawLog",
      "DeviceManagement:Configure",
      "DeviceManagement:CustomCommand",
      "DeviceManagement:DeviceStatus",
      "DeviceManagement:Assign",
      "DeviceManagement:DeviceDeactivate",
      "DeviceManagement:OTA",
    ],
    user_preferences: {
      timezone: 'UTC',
    },
  })),
}));

// Mock window.location
const mockPushState = jest.fn();
Object.defineProperty(window, 'location', {
  value: {
    pathname: '/devices/list',
    search: '',
  },
  writable: true,
});

Object.defineProperty(window, 'history', {
  value: {
    pushState: mockPushState,
  },
  writable: true,
});

// Mock data
const mockTableData = {
  tableData: [
    {
      qr_code: 'QR001',
      device_type: 'Gateway',
      device_type_id: 1,
      online_status: 'online',
      firmware: '1.0.1',
      customer_id: 'cust123',
      device_id: 'dev123',
    },
    {
      qr_code: 'QR002',
      device_type: 'TRB',
      device_type_id: 1,
      online_status: 'offline',
      firmware: '1.0.3',
      customer_id: '456',
      device_id: 'dev332',
    },
    {
      qr_code: 'QR003',
      device_type: 'Gateway',
      device_type_id: 1,
      online_status: 'online',
      firmware: '1.0.1',
      customer_id: '-',
      device_id: 'dev342',
    },
    {
      qr_code: 'QR004',
      device_type: 'Gateway',
      device_type_id: 1,
      online_status: 'online',
      firmware: '1.0.1',
      customer_id: '123',
      device_id: 'dev342',
    },
    {
      qr_code: 'QR005',
      device_type: 'Gateway',
      device_type_id: 2,
      online_status: 'online',
      firmware: '1.0.1',
      customer_id: '123',
      device_id: 'dev345',
    }
  ],
  totalDataCount: 1,
  columns: [
    {
      key: "qr_code",
      align: "center",
      fixed: "left",
      title: "QR Code",
      width: 150,
      sorter: true,
      ellipsis: false,
      dataIndex: "qr_code",
      pdf_title: "QR Code",
      colPathExp: "info.name",
      api_sort_key: "info.name",
      not_customizable: true
    }
  ],
};

// Create a test component that uses the hook
function TestComponent({ props }) {
  const hookResult = useListingPageLogic(props);

  // Expose the hook result to the window for testing
  window.hookResult = hookResult;

  return (
    <div>
      <div data-testid="loading">{String(hookResult.loading)}</div>
      <div data-testid="tableLoading">{String(hookResult.tableLoading)}</div>
      <div data-testid="pageNo">{hookResult.pagination.page_no}</div>
      <div data-testid="pageSize">{hookResult.pagination.page_size}</div>
      <div data-testid="tableDataLength">
        {hookResult.tableData.length > 0 ? hookResult.tableData[0]?.data?.length || 0 : 0}
      </div>
      <button
        data-testid="triggerAction"
        onClick={() => hookResult.triggerAction('download', [])}
      >
        Trigger Action
      </button>
      <button
        data-testid="setPagination"
        onClick={() => hookResult.setPagination({ page_no: 2, page_size: 50 })}
      >
        Change Page
      </button>
      <button
        data-testid="setSortedInfo"
        onClick={() => hookResult.setSortedInfo({ api_sort_key: 'info.customer_org_id', order: 'ascend', columnKey: 'customer_id' })}
      >
        Sort
      </button>
      <button
        data-testid="resetAction"
        onClick={() => hookResult.resetActionState()}
      >
        Reset Action
      </button>
    </div>
  );
}

describe('useListingPageLogic', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset window.hookResult
    window.hookResult = null;

    // Mock default implementations
    getPageConfig.mockResolvedValue({ data: { config: pageConfigData } });
    getTableDataV4.mockResolvedValue(mockTableData);
    establishSocketConnection.mockReturnValue({ connection: 'established' });
  });



  test('should handle downloading table data', async () => {
    const props = { page_id: 'device_listing', client_id: '1', user_preferences: { timezone: 'UTC' } };

    act(() => {
      render(<TestComponent props={props} />);
    });

    // Mock the fetchAllPagesData result
    const mockDownloadData = {
      assetId: 1,
      selectedAssets: [],
      tableResponse: [
        {
          config: { showTotalRowHighlight: false },
          data: [{ qr_code: 'QR001' }, { qr_code: 'QR002' }],
        },
      ],
      preferences: { timezone: 'UTC' },
    };

    await waitFor(() => {
      screen.getByTestId('setPagination').click();
    });

    act(() => {
      screen.getByTestId('setSortedInfo').click();
    });

    act(() => {
      screen.getByTestId('triggerAction').click();
    });

    // Call fetchAllPagesData directly to test download functionality
    let downloadResult;
    // // Check download state
    // expect(window.hookResult.downloading).toBe(false);
    await act(async () => {
      downloadResult = await window.hookResult.fetchAllPagesData();
    });
  });

  test('should handle download without sorted info', async () => {
    const props = { page_id: 'device_listing', client_id: '1', user_preferences: { timezone: 'UTC' } };

    act(() => {
      render(<TestComponent props={props} />);
    });

    await waitFor(() => {
      screen.getByTestId('setPagination').click();
    });

    act(() => {
      screen.getByTestId('triggerAction').click();
    });

    // Call fetchAllPagesData directly to test download functionality
    let downloadResult;
    // // Check download state
    // expect(window.hookResult.downloading).toBe(false);
    await act(async () => {
      downloadResult = await window.hookResult.fetchAllPagesData();
    });
  });

  test('should handle failed download', async () => {
    const props = { page_id: 'device_listing', client_id: '1', user_preferences: { timezone: 'UTC' } };

    act(() => {
      render(<TestComponent props={props} />);
    });

    // Call fetchAllPagesData directly to test download functionality
    let downloadResult;
    // // Check download state
    // expect(window.hookResult.downloading).toBe(false);
    await act(async () => {
      downloadResult = await window.hookResult.fetchAllPagesData();
    });
  });

  test('should initialize with default states', async () => {
    const props = { page_id: 'device_listing', client_id: '1', user_preferences: { timezone: 'UTC' } };

    act(() => {
      render(<TestComponent props={props} />);
    });

    // Check initial loading state
    expect(screen.getByTestId('loading').textContent).toBe('false');
    expect(screen.getByTestId('tableLoading').textContent).toBe('true');

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.getByTestId('loading').textContent).toBe('false');
    });

    // Verify the socket connection was established
    expect(establishSocketConnection).toHaveBeenCalled();
  });

  test('should handle pagination changes', async () => {
    const props = { page_id: 'device_listing', client_id: '1', user_preferences: { timezone: 'UTC' } };

    act(() => {
      render(<TestComponent props={props} />);
    });
    expect(screen.getByTestId('loading').textContent).toBe('false');

    // Verify initial page number
    expect(screen.getByTestId('pageNo').textContent).toBe('1');

    // Change pagination
    act(() => {
      screen.getByTestId('setPagination').click();
    });

    // Verify page number changed
    expect(screen.getByTestId('pageNo').textContent).toBe('2');

    // Verify URL update
    expect(window.history.pushState).toHaveBeenCalled();
  });

  test('should handle sorting changes', async () => {
    const props = { page_id: 'device_listing', client_id: '1', user_preferences: { timezone: 'UTC' } };

    act(() => {
      render(<TestComponent props={props} />);
    });

    await waitFor(() => {
      expect(screen.getByTestId('loading').textContent).toBe('false');
    });

    // Initial sort state
    expect(window.hookResult.sortedInfo).toEqual({ api_sort_key: '', order: '', columnKey: '' });

    // Change sort
    act(() => {
      screen.getByTestId('setSortedInfo').click();
    });

    // Verify sort state changed
    expect(window.hookResult.sortedInfo).toEqual({
      api_sort_key: 'info.customer_org_id',
      order: 'ascend',
      columnKey: 'customer_id'
    });

    // Verify URL update with sort parameters
    expect(window.history.pushState).toHaveBeenCalled();
    const pushStateArgs = mockPushState.mock.calls[mockPushState.mock.calls.length - 1];
    expect(pushStateArgs[2]).toContain('api_sort_key=info.customer_org_id');
    expect(pushStateArgs[2]).toContain('order=ascend');
    expect(pushStateArgs[2]).toContain('columnKey=customer_id');
  });

  test('should handle action triggering', async () => {
    const props = { page_id: 'device_listing', client_id: '1', user_preferences: { timezone: 'UTC' } };

    act(() => {
      render(<TestComponent props={props} />);
    });

    await waitFor(() => {
      expect(screen.getByTestId('loading').textContent).toBe('false');
    });

    // Initial action state
    expect(window.hookResult.actionState).toEqual({ action: '', row_value: [] });

    // Trigger action
    act(() => {
      screen.getByTestId('triggerAction').click();
    });

    // Verify action state updated
    expect(window.hookResult.actionState).toEqual({ action: 'download', row_value: [] });
  });

  test('should handle URL parameters on initialization', async () => {
    // Set up URL parameters
    window.location.search = '?page_no=3&page_size=25&api_sort_key=info.name&order=ascend&columnKey=qr_code';

    const props = { page_id: 'device_listing', client_id: '1', user_preferences: { timezone: 'UTC' } };

    act(() => {
      render(<TestComponent props={props} />);
    });

    await waitFor(() => {
      expect(screen.getByTestId('loading').textContent).toBe('false');
    });

    // Verify pagination state initialized from URL
    expect(window.hookResult.pagination).toEqual({ page_no: 3, page_size: 25 });

    // Verify sort state initialized from URL
    expect(window.hookResult.sortedInfo).toEqual({
      api_sort_key: 'info.name',
      order: 'ascend',
      columnKey: 'qr_code'
    });
  });

  test('should clean up socket connection on unmount', async () => {
    const props = { page_id: 'device_listing', client_id: '1', user_preferences: { timezone: 'UTC' } };

    const { unmount } = render(<TestComponent props={props} />);

    await waitFor(() => {
      expect(screen.getByTestId('loading').textContent).toBe('false');
    });

    // Unmount component to trigger cleanup
    unmount();

    // Verify socket disconnection was called
    expect(disconnectSocketConnection).toHaveBeenCalled();
  });

  test('should handle filter processing from pageConfig', async () => {
    const props = { page_id: 'device_listing', client_id: '1', user_preferences: { timezone: 'UTC' } };

    act(() => {
      render(<TestComponent props={props} />);
    });

    await waitFor(() => {
      expect(screen.getByTestId('loading').textContent).toBe('false');
    });

    // Verify filters are processed
    expect(window.hookResult.filters).toHaveLength(9); // Based on mocked enabled_features

    // Verify dependent filters are processed
    expect(window.hookResult.dependentFilters).toBeDefined();
  });


  test('should reset actions', async () => {
    const props = { page_id: 'device_listing', client_id: '1', user_preferences: { timezone: 'UTC' } };

    act(() => {
      render(<TestComponent props={props} />);
    });
    // Check download state
    act(() => {
      screen.getByTestId('resetAction').click();
    });
  });

});