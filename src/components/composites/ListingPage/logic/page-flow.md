# Listing Page Flow (Timeout Based on Last Update Time)

```mermaid
flowchart TD


    LinkedFilter --> |NO| RestAPIUpdate
    LinkedFilter --> |Yes| InternalFilterUpdate[Updates the filter component selected values] --> FilterCalback
    FilterUpdate[User Clicks on any Filter. kpiLoading+tableLoading=true] --> InternalFilterUpdate
    FilterUpdate --> Linked<PERSON><PERSON>{finds the kpi linked to the filter & selects it}

    PageLoad[Page Loads] ----> FilterComp[Filter Component Renders. Fetches filter options. Then sets default filter values.]

    KpiUpdate[User Clicks on any KPI. tableLoading=true] --> LinkedFilter{Check if exists in main filter}
    Pagination[User Changes Pagination or Sorting. tableLoading=true] --> RestAPIUpdate
    FilterComp --> FilterCalback[Filter Callback is called. Sets page_no = 1.] --> dd[If dynamic columns are enabled and related filter changes, it fetches the table config based on dynamic key, and updates the table column config] --> RestAPIUpdate
    RestAPIUpdate --> TableIds
    TableIds["Table Ids Changed"] --> |Yes| Subscribe[Unsubscribe & subscribe to new table ids] --> WaitSocketEvent[Wait for socket events]
    WaitSocketEvent --> NewData
    NewData["New data received from socket"] --> CheckLastUpdateTime{Is last update time set?}

    Interval["Interval running every 60s"] ---> CheckTime{Has 15s passed since last update?}  --> |Yes| RestAPIUpdate[/Rest API call for kpi & table. sets all loadings=false/]
    CheckTime{Has 15s passed since last update from socket?}  --> |No| Interval

    TimeoutFires["Timeout fires after remaining time"] ---> BulkUpdate

    CheckLastUpdateTime -->|No| BulkUpdate

    CheckLastUpdateTime -->|Yes| CheckElapsedTime{Has 15s passed since last update?}
    CheckElapsedTime --->|Yes| BulkUpdate[/Rest API call for kpi & table./]
    BulkUpdate --> UpdateLastUpdate1[Update last update time = now]

    CheckElapsedTime -->|No| CheckIfTimerRunning{Is a timeout already scheduled?}
    
    CheckIfTimerRunning -->|No| StartDelayTimer[Start timeout for 15s - time elapsed since last update]


```