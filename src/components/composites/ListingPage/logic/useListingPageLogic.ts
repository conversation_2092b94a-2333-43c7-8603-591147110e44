import { useState, useEffect, useRef } from 'react';
import {
    getPageConfig,
    // getTableData,
    // getSummaryData,
    getTableDataV4,
    getSummaryDataV4
} from "@datoms/js-sdk";
import _cloneDeep from "lodash/cloneDeep";
import _findIndex from "lodash/findIndex";
import { useGlobalContext } from '../../../../store/globalStore';
import { TableData } from '../types';
// import pageConfigData from '../page-config.json';
import { useDynamicConfig } from './useDynamicConfig';
import { useRealtimeUpdates } from './useRealtimeUpdates';
import { downloadXlsx } from '@components/nonConfigurable/baseComponents/DownloadModal/utils/reportDownloadXlsx';

// import { dummyKPIData } from '../dummyData';
// import { dummyTableData } from '../../../../containers/views/ListView/dummyData';

const setPaginationState = () => {
  const params = new URLSearchParams(window.location.search);
  const page_no = parseInt(params.get("page_no") || "1");
  const page_size = parseInt(params.get("page_size") || "15");
  return {
    page_no: page_no,
    page_size: page_size
  }
}


const setSortedState = () => {
  const params = new URLSearchParams(window.location.search);
  const api_sort_key = params.get("api_sort_key") || "";
  const order = params.get("order") || "";
  const columnKey = params.get("columnKey") || "";

  return {
    api_sort_key: api_sort_key,
    order: order,
    columnKey: columnKey
  }
}

export const useListingPageLogic = (props: any) => {
  const kpiRef = useRef<any>(null);
  const filterRef = useRef<any>(null);
  const kpiChangeRef = useRef<any>(null);
  const [pageConfig, setPageConfig] = useState<any | null>(null);
  const [tableData, setTableData] = useState<TableData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [tableLoading, setTableLoading] = useState<boolean>(true);
  const [tableInnerLoading, setTableInnerLoading] = useState<boolean>(false);
  const [pagination, setPagination] = useState<{ page_no: number, page_size: number }>(setPaginationState());
  const [sortedInfo, setSortedInfo] = useState<{ api_sort_key: string, order: string, columnKey: string }>(setSortedState());
  const [filters, setFilters] = useState<any>([]);
  const [dependentFilters, setDependentFilters] = useState<any>([]);
  const [searchConfig, setSearchConfig] = useState<any>({});
  const [downloading, setDownloading] = useState<boolean>(false);
  const [filterMap, setFilterMap] = useState<any>({});
  const [initial, setInitial] = useState<boolean>(true);
  const [actionState, setActionState] = useState<any>({
    action: "",
    row_value: []
  });
  const [kpiData, setKpiData] = useState<Record<string, any>[]>([{config:{}, data: []}]);
  const [kpiLoading, setKpiLoading] = useState<boolean>(true);
  const [selectedFilters, setSelectedFilters] = useState<any>([]);
  const [kpiFilters, setKpiFilters] = useState<any>(undefined);
  const [selectedKpiKey, setSelectedKpiKey] = useState<string>("");

  const context = useGlobalContext();
  const {
    enabled_features,
    currentUserPreferences: user_preferences,
    user_name,
    client_name,
    client_id: contextOrgId,
  } = context;

  const triggerAction = (action: String, row_value: any) => {
    if(!action) return;
    console.log("action triggered->   ", action);
    setActionState({
      action: action,
      row_value: Array.isArray(row_value)? row_value : [row_value]
    });
  }

  const resetActionState = () => {
    setActionState({
      action: "",
      row_value: []
    });
  }

  // Process filters
  const processFilters = (filterArr: any) => {
    const depFilters: any = {};
    const filters: any = [];
    const filterQuery: any = {};
    filterArr.forEach((filter: any) => {
      if(filter.type === "search") {
        setSearchConfig(filter);
      } else {
        const isFeatureEnabled = !filter.feature_key ||enabled_features.find((feature: any) => feature.feature_key === filter.feature_key || true);
        if (!isFeatureEnabled) return;
        
        filters.push(filter);
        if (filter.url_name && filter.dependent_filters) {
          depFilters[filter.url_name] = filter.dependent_filters;
        }
        if (filter.url_name && filter.query_key) {
          filterQuery[filter.url_name] = filter.query_key;
        }
        
      }
    });

    setFilterMap(filterQuery);
    return {filters, depFilters};
  }

  // Fetch page configuration
  const fetchPageConfig = async () => {
    try {
      setLoading(true);
      const response = await getPageConfig(props.page_id);
      const pageConfigObj = response?.data?.config || {};
      // const pageConfigObj = props.pageConfig // ?? pageConfigData;
      setPageConfig(pageConfigObj);

      if (pageConfigObj?.filters) {
        const {filters, depFilters} = processFilters(pageConfigObj.filters);
        setFilters(filters);
        setDependentFilters(depFilters);
      }
    } catch (error) {
      console.error('Error fetching page config:', error);
    } finally {
      setLoading(false);
    }
  };

  const getKpiData = async (selectedFilters = getSelectedFilters()) => {
    console.log("updatedPageConfig?.kpi", updatedPageConfig?.kpi);
    if (!updatedPageConfig?.kpi || !Array.isArray(updatedPageConfig.kpi) || updatedPageConfig.kpi.length === 0) {
      return;
    }
    
    // Process each KPI configuration in the array
    const kpiPromises = updatedPageConfig.kpi.map(async (kpiItem) => {
      console.log("Processing KPI item:", kpiItem.apiType);
      
      const response = await getSummaryDataV4(contextOrgId, {
        apiType: kpiItem.apiType,
        dataPathExp: kpiItem.dataPathExp,
        timezone: props.user_preferences.timezone,
        apiConfig: {
          ...kpiItem.apiConfig,
          kpi_queries: getKpiPayload(kpiItem.apiConfig.kpi_queries, {...selectedFilters, "info.customer_org_id": props.client_id}, kpiItem.includeFilters)
        },
        data: kpiItem.data,
      });
      
      return {
        config: kpiItem.config,
        data: response.data
      };
    });
    
    // Wait for all KPI requests to complete
    const kpiResults = await Promise.all(kpiPromises);
    
    // Store the results
    setKpiData(kpiResults);
    setKpiLoading(false);
  };

  // Fetch all data (table and KPIs)
  const fetchAllData = async (selectedFilters = getSelectedFilters(), paginationState = pagination, setLoading=true) => {
    try {
      console.log("Stack", new Error().stack)
      setTableLoading(setLoading ? true: false);
      if (updatedPageConfig?.kpi) {
        setKpiLoading(setLoading ? true: false);
      }

      const promises = [];

    
      promises.push(getKpiData());

      // Add table data promises
      const tablePromises = updatedPageConfig.table.map((tableConfig: any) => 
        fetchTableData(tableConfig, selectedFilters, paginationState)
      );
      promises.push(...tablePromises);

      const [_, ...tableDataResponse] = await Promise.all(promises);
      setTableData(tableDataResponse);
      console.log("tableDataResponse", tableDataResponse)

    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setTableLoading(false);
      setTableInnerLoading(false);
      setKpiLoading(false);
      setInitial(false);
    }
  };

  const getColumns = (columns: any[], additionalData: any[]) => {
    const dataColumns: any[] = [];
    const additionalColumnsArr: any[] = [];
    columns.forEach((column: any) => {
      column.additionalData?.forEach((additional_column: any) => {
        // TODO: going forward there willbe provision that only the columns that are visible to the user will be requested in the api call,
        // for this we are maintaining separate additional data for each column, also it will be feature driven, it will be decided later on.
        additionalColumnsArr.push(additional_column);
        delete column.additional_data;
      });
      dataColumns.push(column);
    });

    additionalData?.forEach((data: any) => {
      if(data.feature_key) {
        const hasCommon = data.feature_key.some((key: string) => enabled_features.includes(key));
        if(hasCommon) {
          additionalColumnsArr.push(data);
        }
      } else {
        additionalColumnsArr.push(data);
      }
    });

    // Remove duplicates based on dataIndex
    const additionalColumns = Array.from(
      new Map(additionalColumnsArr.map((col) => [col.dataIndex, col])).values()
    );

    return {dataColumns, additionalColumns};
  }

  // Fetch table data
  const fetchTableData = async (tableConfig: any, selectedFilters: any, paginationState: any) => {
    try {
      const finalFilters = Object.fromEntries(
        Object.entries(selectedFilters || {})
          .filter(([_, value]) => value !== "")
          .map(([key, value]) => {
            // Parse string booleans to actual boolean values
            if (value === "true") return [filterMap[key] || key, true];
            if (value === "false") return [filterMap[key] || key, false];
            return [filterMap[key] || key, value];
          })
      );
      const {dataColumns, additionalColumns} = getColumns(tableConfig.tableProps.columns, tableConfig.additionalData);
      // console.log("final filters   ", finalFilters);

      // TODO: We will modify the filters later on
      const modifiedFilters: Record<string, any> = {};
      for (const key in finalFilters) {
        const value = finalFilters[key];
        if (Array.isArray(value)) {
          if (value.length > 1) {
            modifiedFilters[key] = { in: value };
          } else if (value.length === 1) {
            modifiedFilters[key] = value[0];
          }
        } else {
          modifiedFilters[key] = value;
        }
      }
      // console.log("modified filters   ", modifiedFilters);
      // const response = {
      //   tableData: dummyTableData,
      //   totalDataCount: dummyTableData.length
      // }
      const response = await getTableDataV4(contextOrgId, {
        columns: dataColumns,
        additional_data: additionalColumns,
        apiType: tableConfig.apiType,
        dataPathExp: tableConfig.dataPathExp,
        totalCountPathExp: tableConfig.totalCountPathExp,
        timezone: props.user_preferences.timezone,
        apiConfig: {
            ...tableConfig.apiConfig,
            api_query: {
              page: paginationState.page_no,
              limit: paginationState.page_size,
              sort_by: sortedInfo?.api_sort_key,
              sort_order: sortedInfo?.order? (sortedInfo?.order === "ascend" ? "asc" : "desc") : undefined,
              filter: {...modifiedFilters, ...(tableConfig?.apiConfig?.api_query?.filters || {}), "info.customer_org_id": props.client_id},
              search: selectedFilters?.search?.toString().trim(),
            },
          },
      });

      let finalTableConfig = deepCloneWithFunctions(tableConfig);
      if (response.columns?.length) {
        finalTableConfig.tableProps.columns = response.columns;
      }
      console.log("config    ", finalTableConfig);
      return {
        config: finalTableConfig,
        data: response?.tableData || [],
        totalCount: response?.totalDataCount,
      };
    } catch (error) {
      console.error('Error fetching table data:', error);
    }
  };

  // Download table data
  const fetchAllPagesData = async () => {
    try {
      setDownloading(true);
      if (updatedPageConfig?.table?.length) {
        const tableResults = _cloneDeep(tableData);
        let tableDataArr = tableResults[0].data;

        if (tableResults[0].config.api_pagination) {
          let allData: any[] = []; // To collect all pages data
          const tableConfig = updatedPageConfig.table[0];
          const selectedFilters = getSelectedFilters();
          let currentPage = 1;
          let hasMorePages = true;
          while (hasMorePages && tableData?.length) {
            const response = await fetchTableData(tableConfig, selectedFilters, {page_no: currentPage, page_size: 1000});
            try {
              if (response?.data?.length) {
                response?.data.map((item: any) => {
                  if (item && Object.keys(item).length) {
                    Object.keys(item).forEach((param_key) => {
                      if (param_key.includes("dr_st"))
                        item[param_key] =
                          item[param_key] === 1 ? "Open": item[param_key] === 0 ? "Closed" : item[param_key] === 'NA' ? 'NA' : "-";
                    });
                  }
                });
              }
            } catch (e) {}
            allData = allData.concat(response?.data || []);
            hasMorePages = allData.length < (tableData[0].totalCount || 0);
            currentPage++;
          }
          tableDataArr = allData;
        } else {
          tableDataArr = sortArrayByColumn(
            tableDataArr,
            sortedInfo,
            !tableResults[0].config?.showTotalRowHighlight,
          );
        }
        tableResults[0].data =
          tableResults[0].config?.showTotalRowHighlight &&
          tableDataArr.length === 2
            ? tableDataArr.slice(0, -1)
            : tableDataArr;
        
        if(tableResults[0].config?.preferenceKeys) {
          tableResults[0].config.preferenceKeys = getPrefenceKeys(tableResults[0].config.preferenceKeys);
        }

        return {
          assetId: 1,
          selectedAssets: [],
          tableResponse: tableResults,
          summaryResponse: [],
          preferences: user_preferences,
        };
      }
    } catch (error) {
      console.error('Error downloading table data:', error);
    }
  };

  const downloadTableData = async () => {
    const downloadPayload = {
      reportTitle: updatedPageConfig?.title,
      userName: user_name,
      fetchData: fetchAllPagesData,
      selectedAssets: [1],
      allThingsData: [1],
      customerName: props.client_name || client_name,
      timezone: user_preferences.timezone || "Asia/Calcutta",
      sortedInfo,
      filters: selectedFilters,
    };
    await new Promise((resolve) => {
      downloadXlsx(downloadPayload, resolve);
    });
  };

  const getPrefenceKeys = (preferenceKeys: string[] = []) => {
    if (
      pageConfig?.dynamicComponentConfig?.enabled &&
      pageConfig.dynamicComponentConfig.dependent_filter_keys?.length &&
      preferenceKeys.length === 2
    ) {
      const filterValues = getSelectedFilters()
      
      const dependentValuesSuffix = pageConfig.dynamicComponentConfig.dependent_filter_keys
        .map(key => filterValues[key] ? `${key}:${filterValues[key]}` : '')
        .filter(Boolean)
        .join("_");
      
      return [
        preferenceKeys[0],
        dependentValuesSuffix ? 
          preferenceKeys[1] + "_" + dependentValuesSuffix : 
          preferenceKeys[1]
      ];
    }
    return preferenceKeys;
  };

  const getSelectedFilters = () => {
    const mainFilter = filterRef?.current?.getFilters() || {};
    let kpiFilter = {};
    if (selectedKpiKey && kpiFilters?.[selectedKpiKey]) {
      kpiFilter = {
        [kpiFilters[selectedKpiKey].key]: kpiFilters[selectedKpiKey].value,
      };
    }
    const filters: any = {
      ...mainFilter,
      ...kpiFilter,
    };

    return filters;
  };

  const sortArrayByColumn = (
    data: any,
    sortedInfo: any,
    sortLastRow = true,
  ) => {
    if (!sortedInfo || !sortedInfo.columnKey || !sortedInfo.order) {
      return data;
    }

    let { columnKey, order } = sortedInfo;
    const sortableData = sortLastRow ? data.slice() : data.slice(0, -1);
    const lastRow = sortLastRow ? [] : data.slice(-1);

    const sortedData = sortableData.sort((a, b) => {
      let valueA = a[columnKey];
      let valueB = b[columnKey];

      if (!isNaN(valueA) && !isNaN(valueB)) {
        valueA = parseFloat(valueA);
        valueB = parseFloat(valueB);
      }

      if (valueA === "-") {
        valueA = 0;
      }

      if (valueB === "-") {
        valueB = 0;
      }

      if (valueA < valueB) {
        return order === "ascend" ? -1 : 1;
      } else if (valueA > valueB) {
        return order === "ascend" ? 1 : -1;
      } else {
        return 0;
      }
    });
    console.log("I am sorted")
    return [...sortedData, ...lastRow];
  };

  const handleKPIClick = (key: string | null, itemData?: any) => {
    if (!itemData?.filter_query) {
      return;
    }
    
    let prevKeyExistsInMainFilter: any = null;

    if (pageConfig?.kpi?.[0]?.data?.length) {
      const findKpi = pageConfig?.kpi?.[0]?.data.find((kpi: any) => {
       return selectedKpiKey === kpi.key;
      });
      if(findKpi) {
        prevKeyExistsInMainFilter = existInMainFilter(null, findKpi.filter_query);
      }
    }


    setSelectedKpiKey(!key ? "" : itemData.key);
    setTableInnerLoading(true);

    const mainFilterConfig: any = existInMainFilter(key, itemData.filter_query);
    console.log("mainFilterConfig", mainFilterConfig);
    if (!key) {
      if (mainFilterConfig) {
        filterRef.current?.setFilters(mainFilterConfig);
      } else {
        setKpiFilters({});
        setPagination((prev) => ({
          ...prev,
          page_no: 1,
        }));
      }
      return;
    }

    const filterQuery = itemData.filter_query;
    const kpifilterMap: any = {
      [filterQuery.key]: filterQuery.query_key,
    };
    const filtersToApply = {
      [key]: filterQuery,
    };

    setFilterMap((prev: any) => ({ ...prev, ...kpifilterMap }));
    if (mainFilterConfig) {
      kpiChangeRef.current = true;
      filterRef.current?.setFilters(mainFilterConfig);
    } else {
      if(prevKeyExistsInMainFilter) {
        kpiChangeRef.current = true;
        filterRef.current?.setFilters(prevKeyExistsInMainFilter);
      } else {
        setPagination((prev) => ({
          ...prev,
          page_no: 1,
        }));
      }
      setKpiFilters(filtersToApply);
    }
  };

  const existInMainFilter = (key: string | null, filterQuery: any) => {
    const findIndex = _findIndex(filters, (filter: any) => filter.url_name === filterQuery.key);
    console.log("findIndex", findIndex, filterQuery);
    if(findIndex > -1) {
      return {
        index: findIndex,
        value: key ? filterQuery?.value : undefined
      };
    }
  };

  const updateKpiSelection = () => {
    if(kpiChangeRef?.current && kpiChangeRef.current === true) {
      kpiChangeRef.current = false;
      return;
    }
    console.log("updateKpiSelection");
    const selectedFilters = getSelectedFilters();
    if (pageConfig?.kpi?.[0]?.data?.length) {
      const findKpis = pageConfig?.kpi?.[0]?.data.filter((kpi: any) => {
        const filterQuery = kpi.filter_query;
        if (!filterQuery) return false;
        const mainFilterConfig = existInMainFilter(kpi.key, filterQuery);
        return mainFilterConfig;
      });
      if (findKpis.length) {
        const findMatchingValue = findKpis.find((kpi: any) => {
          const filterQuery = kpi.filter_query;
          return filterQuery.value === selectedFilters[filterQuery.key];
        });
        // kpiRef?.current?.setSelectedKey(
        //   findMatchingValue ? findMatchingValue.key : null,
        // );
        console.log("updateKey_1", findMatchingValue ? findMatchingValue.key : null);
        setSelectedKpiKey(findMatchingValue ? findMatchingValue.key : "");
      }
    }
  };

   /**
   * Adds filter values to KPI payload
   * @param body Original KPI body configuration
   * @param finalFilters Processed filters from user selection
   * @returns Updated KPI body with filters applied
   */
   const getKpiPayload = (body: any[], selectedFilters: Record<string, any>, includeFilters?: string[]) => {
     
     const finalFilters = Object.fromEntries(
       Object.entries(selectedFilters || {})
       .filter(([key, value]) => value !== "" && (!(includeFilters && !includeFilters.includes(key)) || key === "info.customer_org_id"))
       .map(([key, value]) => {
         // Parse string booleans to actual boolean values
         if (value === "true") return [filterMap[key] || key, true];
         if (value === "false") return [filterMap[key] || key, false];
         return [filterMap[key] || key, value];
        })
      );
      
      if (!body || !finalFilters || Object.keys(finalFilters).length === 0) {
        return body;
      }
    // Create a deep clone to avoid mutating the original body
    const updatedBody = _cloneDeep(body);
    
    // Process each KPI item in the body
    updatedBody.forEach((kpiItem: any) => {
      // Initialize filters array if it doesn't exist
      if (!kpiItem.filters) {
        kpiItem.filters = [];
      }
      
      // Add each filter from finalFilters to the KPI item
      Object.entries(finalFilters).forEach(([key, value]) => {
        // Skip empty values
        if (value === undefined || value === null || value === '') {
          return;
        }
        
        // Handle array values (for multi-select filters)
        if (Array.isArray(value)) {
          if (value.length > 0) {
            kpiItem.filters.push({
              field: key,
              operator: "in",
              value: value
            });
          }
        } 
        // Handle regular values
        else {
          kpiItem.filters.push({
            field: key,
            operator: "eq",
            value: value
          });
        }
      });
      
      // Add search if it exists in finalFilters
      if (finalFilters.search) {
        kpiItem.search = finalFilters.search.toString().trim();
      }
    });
    
    return updatedBody;
  };

  useEffect(() => {
    fetchPageConfig();
  }, [props.page_id]);

  const {
    componentConfigLoading,
    updatedPageConfig,
    urlPayload
  } = useDynamicConfig({
    pageConfig,
    filterRef,
    pagination,
    sortedInfo,
    fetchAllData,
    client_id: props.client_id,
    props
  });

  useEffect(() => {
    if (
      updatedPageConfig?.dynamicComponentConfig?.enabled &&
      updatedPageConfig?.table?.length &&
      filterRef.current
    ) {
      fetchAllData();
    }
  }, [updatedPageConfig]);

  // useEffect(() => {
  //   if(kpiFilters) {
  //     fetchAllData();
  //   }
  // }, [kpiFilters])

  useEffect(() => {
    setTableInnerLoading(true);
  }, [sortedInfo]);

  // Add the real-time updates hook
  const socket = useRealtimeUpdates(
    tableData,
    pageConfig?.realtimeConfig,
    setTableData,
    updatedPageConfig,
    fetchAllData,
    pagination,
    sortedInfo
  );

  return {
    pageConfig: updatedPageConfig,
    kpiData,
    kpiLoading: !tableInnerLoading && kpiLoading,
    tableData,
    loading,
    pagination,
    sortedInfo,
    filters,
    dependentFilters,
    filterRef,
    kpiRef,
    searchConfig,
    downloading,
    tableLoading: !tableInnerLoading && (tableLoading || componentConfigLoading),
    tableInnerLoading,
    setTableInnerLoading,
    urlPayload,
    initial,
    actionState,
    socket,
    fetchAllData,
    setPagination,
    setSortedInfo,
    setDownloading,
    fetchAllPagesData,
    triggerAction,
    resetActionState,
    downloadTableData,
    setSelectedFilters,
    getPrefenceKeys,
    handleKPIClick,
    updateKpiSelection,
    selectedKpiKey
  };
};

function deepCloneWithFunctions<T>(obj: T): T {
  if (obj === null || typeof obj !== "object") {
    return obj; // Return primitives and functions as-is
  }

  if (Array.isArray(obj)) {
    return obj.map(deepCloneWithFunctions) as unknown as T; // Recursively clone arrays
  }

  const clonedObj = {} as T;
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      (clonedObj as any)[key] = deepCloneWithFunctions((obj as any)[key]);
    }
  }
  return clonedObj;
}
