import _cloneDeep from "lodash/cloneDeep";
import { useState, useRef, useEffect } from "react";
import { configHandlerRegistry } from "./configHandlerRegistry";
import { getBaseUrl } from "@datoms/js-utils/src/base-url-logic";

interface DynamicConfigProps {
  pageConfig: any;
  filterRef: any;
  pagination: any;
  sortedInfo: any;
  fetchAllData: any;
  client_id: number;
  props: any;
}

interface DynamicConfigReturn {
  componentConfigLoading: boolean;
  updatedPageConfig: any;
  urlPayload: String | undefined;
}

export const useDynamicConfig = ({
  pageConfig,
  filterRef,
  pagination,
  sortedInfo,
  fetchAllData,
  client_id,
  props
}: DynamicConfigProps): DynamicConfigReturn => {
  const [componentConfigLoading, setComponentConfigLoading] = useState(false);
  const [updatedPageConfig, setUpdatedPageConfig] = useState(pageConfig);
  const [urlPayload, setUrlPayload] = useState<String>();
  const configCache = useRef<Record<string, any>>({});
  const prevFilters = useRef<Record<string, any>>({});

  const handleDynamicConfig = async (selectedFilters: any) => {
    try {
      setComponentConfigLoading(true);
      const dependentValues =
        pageConfig.dynamicComponentConfig.dependent_filter_keys
          .map((key: string) => selectedFilters[key])
          .join("_");

      if (configCache.current[dependentValues]) {
        return configCache.current[dependentValues];
      }

      const configHandler =
        configHandlerRegistry[pageConfig.dynamicComponentConfig.config_api];
      if (!configHandler) {
        console.error(
          `No handler found for config_api: ${pageConfig.dynamicComponentConfig.config_api}`,
        );
        return null;
      }

      const response = await configHandler(selectedFilters, client_id);
      configCache.current[dependentValues] = response;
      return response;
    } finally {
      setComponentConfigLoading(false);
    }
  };

  const handleUrlUpdate = (selectedFilters: any) => {
    const baseUrlPayload = `page_no=${pagination.page_no}&page_size=${pagination.page_size}&api_sort_key=${sortedInfo.api_sort_key}&order=${sortedInfo.order}&columnKey=${sortedInfo.columnKey}`;

    const params = new URLSearchParams(window.location.search);
    const filter = params.get("filter");

    if (sortedInfo.api_sort_key || sortedInfo.order || sortedInfo.columnKey) {
      params.set("api_sort_key", sortedInfo.api_sort_key);
      params.set("order", sortedInfo.order);
      params.set("columnKey", sortedInfo.columnKey);
    } else {
      params.delete("api_sort_key");
      params.delete("order");
      params.delete("columnKey");
    }

    params.set("page_no", String(pagination.page_no));
    params.set("page_size", String(pagination.page_size));
    params.delete("filter");

    let finalUrl = getBaseUrl(props, pageConfig?.page_url);
    finalUrl += (finalUrl.includes("?") ? "&" : "?") + `${baseUrlPayload}${filter ? `&filter=${filter}` : ""}`
    props.history.push(finalUrl);
    setUrlPayload(baseUrlPayload);
  };

  useEffect(() => {
    const updateDynamicConfig = async () => {
      if (!pageConfig?.table) return;

      const selectedFilters = filterRef?.current?.getFilters();

      try {
        let hasFilterChanges = false;
        if (pageConfig.dynamicComponentConfig?.enabled) {
          hasFilterChanges =
            pageConfig.dynamicComponentConfig.dependent_filter_keys.some(
              (key: string) =>
                selectedFilters[key] !== prevFilters.current[key],
            );

          if (hasFilterChanges) {
            const dynamicConfig = await handleDynamicConfig(selectedFilters);
              const existingColumns = pageConfig.table[0].tableProps.columns;
              const newColumns = dynamicConfig?.table?.columns || [];
              const updatedConfig = _cloneDeep(pageConfig);
              updatedConfig.table[0].tableProps.columns = [
                ...existingColumns,
                ...newColumns,
              ];

              setUpdatedPageConfig(updatedConfig);
          }
        }
        if (!hasFilterChanges) {
          fetchAllData();
        }
        handleUrlUpdate(selectedFilters);
        prevFilters.current = selectedFilters;
      } catch (error) {
        console.error("Error in dynamic config update:", error);
      }
    };

    updateDynamicConfig();
  }, [pagination, sortedInfo]);

  return {
    componentConfigLoading,
    updatedPageConfig: pageConfig?.dynamicComponentConfig?.enabled
      ? updatedPageConfig
      : pageConfig,
    urlPayload,
  };
};
