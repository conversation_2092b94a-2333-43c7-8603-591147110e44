import { getSiteTypes } from "@datoms/js-sdk";
// import siteTypeConfig from "../../../../containers/views/ListView/siteTypeConfig.json";

export const handleSiteTypeConfig = async (
  selectedFilters: any,
  client_id: number,
) => {
  try {
    if (!selectedFilters?.site_type) return null;
    // const { parameter_group } = siteTypeConfig;
    const response = await getSiteTypes(client_id);

    const siteType = response?.data?.find(
      (type: any) => type.id === selectedFilters.site_type,
    );
    if (!siteType || !siteType.details_v2?.parameter_group?.length) {
      return {
        table: {
          columns: [],
        },
      };
    }
    const parameter_group = siteType.details_v2?.parameter_group;
    const dynamicColumns = parameter_group.map((group: any) => ({
      title: group.title,
      align: "center",
      dataIndex: group.key,
      children: group.parameters.map((param:any) => {
        let renderFunction = `function(value, row_value) {
          const isOutOfRange = row_value['parameters_compliance']?.${param.key} === false;
          let displayValue = value;

          if(displayValue === '-') {
             return (
              <span style={{ fontSize: 18 }}>
                {displayValue}
              </span>
            );
          }

          if(displayValue === 'NA') {
            return (
              <span style={{fontSize: 18,backgroundColor:'#F2F2F2',color:'#808080',width:'100%',height:'100%',position:'absolute',top:0,left:0,display:'grid',placeContent:'center'}}>
                {displayValue}
              </span>
            );
          }
          
          if (${param.key?.includes("dr_st")}) {
            displayValue = value === 1 ? 'Open' : value === 0 ? 'Closed' : value === 'NA' ? 'NA' : '-';
          }
          
          return (
            <span style={{ fontSize: 18, color: isOutOfRange ? '#ff4d4f' : 'inherit' }}>
              {displayValue}
            </span>
          );
        }`;
        
        return {
          key: param.key,
          title: param.name,
          dataIndex: param.key,
          width: 120,
          align: "center",
          ellipsis: false,
          pdf_title: param.name,
          colPathExp: `last_data_packet.${param.key}`,
          render: renderFunction,
        };
      }),
    }));

    return {
      table: {
        columns: dynamicColumns,
      },
    };
  } catch (e) {}
};
