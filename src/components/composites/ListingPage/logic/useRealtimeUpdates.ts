import { useEffect, useRef } from "react";
import {
  establishSocketConnection,
  disconnectSocketConnection,
  subscribeForSitesUpdates,
  subscribeForEntityUpdates,
} from "@datoms/js-sdk";

interface TableData {
  config: any;
  data: any[];
  totalCount: number;
}

interface RealtimeConfig {
  enabled: boolean;
  key: string;
}

interface PayloadBuffer {
  site_id: number;
  data: any;
}

export const useRealtimeUpdates = (
  tableData: TableData[],
  realtimeConfig: RealtimeConfig,
  setTableData: React.Dispatch<React.SetStateAction<TableData[]>>,
  updatedPageConfig:  any,
  fetchAllData: (
    selectedFilters?: any,
    paginationState?: any,
    setLoading?: boolean,
  ) => void,
  pagination: any,
  sortedInfo: any
) => {
  const restApiIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const socketRef = useRef<any>(null);
  const payloadBufferRef = useRef<PayloadBuffer[]>([]);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Create separate refs for event updates
  const eventBufferRef = useRef<PayloadBuffer[]>([]);
  const eventTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateTimeRef = useRef<number | null>(null);

  const processBatchUpdate = () => {
    const payloads = payloadBufferRef.current;
    if (payloads.length === 0) return;
    console.log("finalEvent_Raw", payloads);
    setTableData((prevTableData: TableData[]) =>
      prevTableData.map(
        (table): TableData => ({
          ...table,
          data: table.data.map((row) => {
            const matchingPayload = payloads.find(
              (p) => p.site_id === row.site_id,
            );
            return matchingPayload ? { ...row, ...matchingPayload.data } : row;
          }),
        }),
      ),
    );

    // Clear the buffer after processing
    payloadBufferRef.current = [];
  };

  // Process event updates in batch
  const processEventUpdates = () => {
    const payloads = eventBufferRef.current;
    if (payloads.length === 0) return;
    /*
    setTableData((prevTableData: TableData[]) =>
      prevTableData.map(
        (table): TableData => ({
          ...table,
          data: table.data.map((row) => {
            const matchingPayload = payloads.find(
              (p) => p.site_id === row.site_id,
            );
            return matchingPayload ? { ...row, ...matchingPayload.data } : row;
          }),
        }),
      ),
    );  */
    console.log("Rest_API Events")
    fetchAllData(undefined, undefined, false);
    // Clear the buffer after processing
    eventBufferRef.current = [];
  };

  useEffect(() => {
    restApiIntervalRef.current = setInterval(() => {
      if (
        lastUpdateTimeRef.current &&
        Date.now() - lastUpdateTimeRef.current < 15000
      ) {
        console.log("Rest_API Skip")
        return;
      }
      console.log("Rest_API Polling")
      fetchAllData(undefined, undefined, false);
    }, 60000);
    return () => {
      console.log("Rest_API_Cleanup")
      if (restApiIntervalRef.current) {
        clearInterval(restApiIntervalRef.current);
      }
    };
  }, [updatedPageConfig, pagination, sortedInfo])

  useEffect(() => {
    console.log("Realtime_useEffectCalls", realtimeConfig);
    if (!realtimeConfig?.enabled || !tableData?.length) {
      return;
    }

    // Step-1: Establish Socket Connection
    socketRef.current = establishSocketConnection();

    let siteIds: number[] = [];

    // Step-2: Handle Socket Connection
    socketRef.current.on("connect", () => {
      console.log("realTimeUpdate 1");
      switch (realtimeConfig.key) {
        case "site_id":
          siteIds =
            tableData[0]?.data?.map((item) => item[realtimeConfig.key]) || [];

          console.log(
            "realTimeUpdate 1.1",
            siteIds,
            socketRef.current.connected,
          );

          // Step-3: Subscribe for required Data
          // raw data
          subscribeForSitesUpdates(socketRef.current, siteIds);

          // events
          subscribeForEntityUpdates(socketRef.current, siteIds, "site");

          break;
        // Add more cases here for different key types
        default:
          console.warn(
            `Unsupported real-time update key: ${realtimeConfig.key}`,
          );
      }
    });

    socketRef.current.on("reconnect", function (attemptNumber) {
      console.log("Reconnected after", attemptNumber, "attempt(s)");
      fetchAllData(undefined, undefined, false);
    });

    // Step-4: Handle Socket Events (data handling)
    socketRef.current.on("new_data_generated_for_site", (payload: any) => {
      console.log("realTimeUpdate 2", payload);
      if (!payload?.site_id) return;
      const siteExists = siteIds.includes(payload.site_id);

      if (!siteExists) return;

      payloadBufferRef.current.push({
        site_id: payload.site_id,
        data: payload.data,
      });

      if (!timeoutRef.current) {
        timeoutRef.current = setTimeout(() => {
          processBatchUpdate();
          timeoutRef.current = null;
        }, 2000);
      }
    });

    socketRef.current.on("update", (payload: any) => {
      console.log("Events Update", payload);
      if (!payload?.entity_type || !payload?.entity_id || !payload?.details)
        return;

      // Only process site updates
      if (payload.entity_type !== "site") return;

      const siteExists = siteIds.includes(payload.entity_id);
      if (!siteExists) return;

      /* const payloadData = payload.details?.status || {};
      const updateData:any = {
        site_id: payload.entity_id,
        data: {},
      };

     if("active_alerts_count" in payloadData) {
        updateData.data.active_alerts_count = payloadData.active_alerts_count;
      }

      if("active_alerts_names" in payloadData) {
        updateData.data.active_alerts_names = payloadData.active_alerts_names;
      }

      if("compliance" in payloadData) {
        updateData.data.compliance = payloadData.compliance;
      }

      if("alerts" in payloadData) {
        updateData.data.alerts = payloadData.alerts;
      }

      if("active_device_issues_count" in payloadData) {
        updateData.data.active_device_issues_count = payloadData.active_device_issues_count;
      }

      if("parameters_compliance" in payloadData) {
        updateData.data.parameters_compliance = payloadData.parameters_compliance;
      }

      if("device_issues" in payloadData) {
        updateData.data.device_issues = payloadData.device_issues;
      }*/

      // Add to event buffer
      eventBufferRef.current.push(payload.entity_id);

      const currentTime = Date.now();
      console.log("simulateEvent_0", currentTime, lastUpdateTimeRef.current);
      if (!lastUpdateTimeRef.current) {
        // No previous updates, process immediately
        processEventUpdates();
        lastUpdateTimeRef.current = currentTime;
      } else {
        const timeElapsed = currentTime - lastUpdateTimeRef.current;
        console.log("simulateEvent_1", timeElapsed);

        if (timeElapsed >= 15000) {
          // 15 seconds passed since last update, process immediately
          processEventUpdates();
          lastUpdateTimeRef.current = currentTime;
        } else if (!eventTimeoutRef.current) {
          console.log("simulateEvent_2", timeElapsed);
          // Start a timer for remaining time
          eventTimeoutRef.current = setTimeout(() => {
            processEventUpdates();
            lastUpdateTimeRef.current = Date.now();
            eventTimeoutRef.current = null;
          }, 15000 - timeElapsed);
        }
      }
    });

    // Cleanup
    return () => {
      if (socketRef.current) {
        disconnectSocketConnection(socketRef.current);
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (eventTimeoutRef.current) {
        clearTimeout(eventTimeoutRef.current);
      }
      if (payloadBufferRef.current.length > 0) {
        payloadBufferRef.current = [];
      }
      if (eventBufferRef.current.length > 0) {
        eventBufferRef.current = [];
      }
      if (lastUpdateTimeRef.current) {
        lastUpdateTimeRef.current = null;
      }
    };
  }, [
    JSON.stringify(
      tableData?.[0]?.data?.map((item) => item[realtimeConfig?.key]),
    ),
  ]);

  return socketRef.current;
};
