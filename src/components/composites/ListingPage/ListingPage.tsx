import AntCol from "@datoms/react-components/src/components/AntCol";
import AntRow from "@datoms/react-components/src/components/AntRow";
import SkeletonLoader from "@datoms/react-components/src/components/SkeletonLoader";

import _cloneDeep from "lodash/cloneDeep";
import PageFilter from "../../configurable/PageFilter";
import DownloadModal from "../../nonConfigurable/baseComponents/DownloadModal";
import ConfigurableTable from "../../configurable/baseComponents/ConfigurableTable";
import ListRenderer from "./components/utils/ListRenderer/ListRenderer";
import ActionButtons from "./components/ActionButtons";
import Kpi from "../../configurable/Kpi";

import { useGlobalContext } from "../../../store/globalStore";
import { getBaseUrl } from "@datoms/js-utils/src/base-url-logic";
import { useListingPageLogic } from "./logic/useListingPageLogic";

import "./styles.less";

// Define placeholder types used within documentation for clarity if not imported
/**
 * @typedef {object} ActionButtonConfig
 * @property {string} key - Identifier for the action.
 * @property {string} title - Display text for the button/menu item.
 * @property {string} [link] - Optional URL for navigation actions.
 */

/**
 * @typedef {object} PageConfig
 * @property {string} [title] - The title displayed for the page or used in reports.
 * @property {string} [page_url] - Base URL segment for this page, used in filtering/API calls.
 * @property {ActionButtonConfig[]} [actions] - Array of configurations for page-level actions (buttons/menu items).
 * @property {string} [orgPreferenceKey] - Key used for storing user preferences related to this page (e.g., table columns).
 */

/**
 * @typedef {object} TableConfig
 * @property {boolean} [disableCustomization] - Flag to disable table column customization.
 * // ... other table config properties
 */

/**
 * @typedef {object} TableDataEntry
 * @property {TableConfig} config - Configuration specific to this table instance.
 * @property {any[]} data - Array of data rows for the table.
 * @property {number} totalCount - Total number of records available for this table (for pagination).
 */

/**
 * @typedef {object} PaginationState
 * @property {number} page_no - Current page number.
 * @property {number} page_size - Number of items per page.
 */

/**
 * @typedef {object} SortedInfoState
 * @property {string} [columnKey] - Key of the column being sorted.
 * @property {'ascend' | 'descend'} [order] - Sort direction.
 */

/**
 * @typedef {object} ActionState
 * @property {string | null} action - Identifier of the currently active action (e.g., "add_device", "download"), or null if none.
 * @property {any} [row_value] - Data associated with the action (e.g., selected row(s) for edit/update).
 */


/**
 * @component ListingPage
 * @description A generic page component designed to display configurable lists of data,
 * typically fetched from an API. It integrates filtering, pagination, sorting,
 * data download, and various actions (like add, edit, update status) which are
 * handled by conditionally rendering components via `ListRenderer`.
 *
 * The core logic, state management (data, loading, pagination, filters, sorting, actions),
 * and data fetching are encapsulated within the `useListingPageLogic` custom hook.
 * Global context provides environment-specific details like user info, client info, logos, etc.
 *
 * @param {any} props - The component props. These props are primarily passed down to the
 *   `useListingPageLogic` hook to initialize state or provide configuration.
 *   Specific props like `props.history` might be used directly by child components like `PageFilter`.
 *   While typed as `any`, the expected props are determined by the requirements of `useListingPageLogic`.
 *
 * @returns {React.ReactNode} The rendered listing page UI, including filters, action buttons,
 *          data tables, loading states, and conditionally rendered action modals/drawers.
 */
const ListingPage = (props: any) => {
  // --- Hooks ---
  /**
   * @description Access global application context (client info, user info, preferences, etc.).
   * @type {GlobalContextType} context
   */
  const context = useGlobalContext();
  const {
    client_name,
    user_name,
    is_white_label,
    vendor_name,
    vendor_id,
    client_logo,
    vendor_logo,
    user_preferences,
  } = context;


   /**
   * @description Custom hook managing the state and logic for the listing page.
   * It handles data fetching, filtering, sorting, pagination, action triggering,
   * loading states, and communication with the backend/sockets.
   * @type {UseListingPageLogicReturn} Logic hook results
   */
  const {
    pageConfig,
    tableData,
    loading,
    pagination,
    sortedInfo,
    filters,
    dependentFilters,
    filterRef,
    kpiRef,
    searchConfig,
    downloading,
    tableLoading,
    urlPayload,
    initial,
    actionState,
    socket,
    kpiData,
    kpiLoading,
    fetchAllData,
    setPagination,
    setSortedInfo,
    setDownloading,
    fetchAllPagesData,
    triggerAction,
    resetActionState,
    downloadTableData,
    setSelectedFilters,
    getPrefenceKeys,
    handleKPIClick,
    updateKpiSelection,
    selectedKpiKey,
    tableInnerLoading,
    setTableInnerLoading
  } = useListingPageLogic(props);

  const baseUrl = getBaseUrl(props, pageConfig?.page_url + "?" + urlPayload);
  // const isMobileScreen = window.innerWidth <= 768;
  const isCustomizeFeatureEnabled = context.enabled_features.includes("Reports:ParameterCustomization") && context.logged_in_user_client_id === context.client_id;
  const loadingComponent = (
    <div
      style={{ marginTop: 24, marginLeft: 32, marginRight: 24, width: "100%" }}
    >
      <SkeletonLoader rows={4} />
    </div>
  );

  /**
   * @description Renders the action buttons section (typically top-right).
   * On desktop, it shows a dropdown menu with actions defined in `pageConfig`.
   * On mobile, it's currently hidden.
   * @type {React.ReactNode}
   */

  const tableComponents: any[] = [];
  let dataAvailable = false;
  !tableLoading &&
    tableData?.forEach((table, index) => {
      const colSpan = 24;
      const config = {
        ...table.config,
        disableCustomization: table.config.disableCustomization || !isCustomizeFeatureEnabled,
        preferenceKeys: getPrefenceKeys(table.config.preferenceKeys),
        pageChangeActions: {
          pageNumChange: (page: number) => {
            setTableInnerLoading(true);
            setPagination((prev) => ({ ...prev, page_no: page }))
          },
          pageSizeChange: (pageSize: number) => {
            setTableInnerLoading(true);
            setPagination((prev) => ({ ...prev, page_size: pageSize }))
          }
        },
      };
      dataAvailable = dataAvailable || table.data?.length > 0;
      tableComponents.push(
        <AntCol span={colSpan} key={index}>
          <ConfigurableTable
            hideLoading
            config={_cloneDeep(config)}
            data={table.data}
            innerLoading={tableInnerLoading}
            updateColumnConfig={() => {}}
            orgPreferenceKey={pageConfig?.orgPreferenceKey}
            pagination={{
              page_no: pagination.page_no,
              page_size: pagination.page_size,
              total: table.totalCount,
            }}
            sortedInfo={sortedInfo}
            updateSortedInfo={setSortedInfo}
            dependencies={{ props: props, baseUrl: getBaseUrl(context, ""), triggerAction, table_search: filterRef?.current?.getFilters()?.search? filterRef?.current?.getFilters()?.search.toString() : "" }}
            downloadTableCallback={downloadTableData}
          />
        </AntCol>,
      );
    });
  
const hasActions = pageConfig?.actions?.length > 0 && pageConfig.actions.some((action: any) => action.position === "right");
console.log("selectedKpiKey", selectedKpiKey)
  return (
    <div id="listing_page_controller" className="basic-page-layout-height">
      {loading ? (
        loadingComponent
      ) : (
        <>
          <ListRenderer
            pageConfig={pageConfig}
            socket={socket}
            context={context}
            actionState={actionState}
            resetActionState={resetActionState}
            fetchDeviceList={fetchAllData}
          />

          {actionState.action === "download" && (
            <DownloadModal
              visible
              setIsDownloadModalVisible={resetActionState}
              customerName={client_name}
              userName={user_name}
              waterMark={
                is_white_label
                  ? vendor_name
                  : vendor_id === 1
                    ? "Datoms"
                    : vendor_name
              }
              tableData={tableData}
              fetchData={fetchAllPagesData}
              loading={downloading}
              setLoading={setDownloading}
              clientLogo={client_logo}
              timezone={user_preferences.timezone || "Asia/Calcutta"}
              vendorLogo={vendor_logo}
              sortedInfo={sortedInfo}
              selectedAssets={[1]}
              allThingsData={[1]}
              reportTitle={pageConfig?.title}
            />
          )}

          <AntRow gutter={10} style={{ marginTop: 10 }}>
            <AntCol span={hasActions ? 20 : 24} style={{ display: "flex", gap: 14, alignItems: "flex-start" }}>
              <ActionButtons
                actions={pageConfig?.actions}
                context={props}
                onActionClick={triggerAction}
                position="left"
              />
              <PageFilter
                {...props}
                ref={filterRef}
                isFlexWrap
                // backgroundWhite
                history={props.history}
                url={baseUrl}
                width={525}
                filterData={filters}
                resetDependentFields={dependentFilters}
                searchObject={searchConfig}
                filterCallback={(_:any,formattedFilters:any) =>
                 {
                  setSelectedFilters(formattedFilters);
                  setPagination((prev) => ({
                    ...prev,
                    page_no: initial ? pagination.page_no : 1,
                  }))
                  updateKpiSelection()
                }}
              />
            </AntCol>
            <AntCol
              span={hasActions ? 4 : 0}
              style={{ display: "flex", justifyContent: "flex-end" }}
            >
              <ActionButtons
                actions={pageConfig?.actions}
                context={context}
                onActionClick={triggerAction}
                position="right"
                downloadLoading={downloading}
              />
            </AntCol>
          </AntRow>

          {/* KPIs section */}
          {pageConfig?.kpi && (
            <AntRow style={{ margin: "32px 0 0 0" }}>
              <AntCol span={24} style={{ display: "flex", justifyContent: "center" }}>
                {kpiData && kpiData.length > 0 && (
                  <Kpi 
                    kpiRef={kpiRef}
                    kpiData={kpiData.map((kpiItem, index) => ({
                      id: `kpi-${index}`,
                      loading: kpiLoading,
                      data: Array.isArray(kpiItem.data) ? kpiItem.data : [],
                      config: kpiItem.config,
                      selectedKpiKey,
                    }))}
                    onKPIClick={handleKPIClick}
                  />
                )}
              </AntCol>
            </AntRow>
          )}

          <AntRow gutter={16}>
            {tableComponents.length ? tableComponents : loadingComponent}
          </AntRow>
        </>
      )}
    </div>
  );
};

export default ListingPage;
