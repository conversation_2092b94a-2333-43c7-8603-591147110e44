import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import AntRow from '@datoms/react-components/src/components/AntRow';
import AntCol from '@datoms/react-components/src/components/AntCol';
import AntDrawer from '@datoms/react-components/src/components/AntDrawer';
import AntButton from '@datoms/react-components/src/components/AntButton';
import AntInput from '@datoms/react-components/src/components/AntInput';
import AntTextArea from '@datoms/react-components/src/components/AntTextArea';
import AntSelect from '@datoms/react-components/src/components/AntSelect';
import AntOption from '@datoms/react-components/src/components/AntOption';
import AntNotification from '@datoms/react-components/src/components/AntNotification';
import _find from 'lodash/find';
import { addDevice, editDevice, retriveVendorFirmwareList } from '@datoms/js-sdk';
import { message } from 'antd';

/**
 * @typedef {object} DeviceType
 * @property {string|number} id - The unique identifier for the device type.
 * @property {string} name - The display name of the device type.
 * @property {string} [sl_prefix] - The serial number prefix associated with this device type.
 */

/**
 * @typedef {object} SelectedRowData
 * @property {string|number} id - The unique identifier of the device being edited.
 * @property {string|number} type_id - The ID of the device's type.
 * @property {string} qr - The full serial number (potentially including a prefix).
 * @property {string} description - The existing description of the device.
 */

/**
 * @typedef {Object} AddDeviceProps
 * @property {Function} fetchDeviceList - Function to refresh the device list
 * @property {boolean} edit_device - Whether in edit mode
 * @property {Function} onClose - Function to close the drawer
 * @property {Object} selected_row_data - Data of the selected device for editing
 * @property {string|number} client_id - Client ID
 */

/**
 * @component AddDevice
 * @description A form component rendered within a drawer for adding a new device or editing an existing one.
 * It fetches available device types, handles form input and validation, and interacts with a JS SDK
 * to perform add or edit operations via API calls.
 *
 * This component uses the older `@ant-design/compatible` Form HOC (`Form.create()`).
 *
 * @param {AddDeviceProps & { form: Object }} props - The component props.
 * @param {Function} props.fetchDeviceList - Callback function invoked after a device is successfully added or edited, typically to refresh a list in the parent component.
 * @param {boolean} props.edit_device - Flag indicating if the component is in 'edit' mode. Controls drawer title and submit logic.
 * @param {Function} props.onClose - Callback function invoked when the drawer is closed (Cancel button or successful submit).
 * @param {SelectedRowData} [props.selected_row_data] - Data for the device to be edited. If provided, the form will be pre-filled, and the component operates in 'edit' mode.
 * @param {string|number} props.client_id - The ID of the client, required for API calls.
 * @param {object} props.form - Ant Design form object injected by `Form.create()`. Used for form handling.
 *
 * @extends {React.Component}
 */

/**
 * deviceAdded: boolean - Flag indicating if a device has been added successfully (call fetch table data only if device is added successfully).
 * loading: boolean - Flag indicating if the component is in loading state.
 * devicePrefix: string - The prefix of the device serial number.
 * deviceTypes: array - The list of device types.
 * deviceSlNo: string - The serial number of the device.
 * deviceType: string - The type of the device.
 * deviceDescription: string - The description of the device.
 * error: object - The error object.
 */

/**
 * Workflow:
 * 1. User opens the drawer to add a new device.
 * 2. User selects a device type and enters a serial number.
 * 3. User enters a description and clicks submit.
 * 4. If the device is added successfully, the fetchDeviceList function is called to refresh the table data.
 * 5. If the device is not added successfully, the error message is displayed.
 * 6. The form is reset on successful submit.
 * 7. If user hits cancel button, if device is added successfully, fetch table data is called and drawer is closed.
 * 8. If user hits cancel button, if no device is added, drawer is closed.
 * 9. the state deviceAdded is set true only if device is added successfully.
 */

const AddDeviceForm = ({ 
	fetchDeviceList,
	edit_device = false,
	onClose,
	selected_row_data = null,
	client_id,
	form
}) => {
	const { getFieldDecorator, resetFields, validateFieldsAndScroll } = form;

	const [deviceAdded, setDeviceAdded] = useState(false);
	const [loading, setLoading] = useState(false);
	const [devicePrefix, setDevicePrefix] = useState('');
	const [deviceTypes, setDeviceTypes] = useState([]);
	const [deviceSlNo, setDeviceSlNo] = useState('');
	const [deviceType, setDeviceType] = useState('');
	const [deviceDescription, setDeviceDescription] = useState('');
	const [error, setError] = useState({
		unauthorised: false,
		unauthorisedMsg: '',
		api: false,
		apiMsg: ''
	});

	useEffect(() => {
		getDeviceTypes();
	}, []);

	useEffect(() => {
		if (selected_row_data && deviceTypes.length) {
			setFormValue(selected_row_data);
		}
	}, [selected_row_data, deviceTypes]);

	const openNotification = (type, msg) => {
		AntNotification({
			type,
			message: msg,
			placement: 'bottomLeft',
			className: 'alert-' + type,
		});
	};

	const getDeviceTypes = async () => {
		try {
			const response = await retriveVendorFirmwareList(client_id, "application=all");
			if (response.status === "success") {
				const types = response.device_types?.map(device => ({
					id: device.id,
					name: device.name,
					sl_prefix: device.sl_prefix
				})) || [];
				setDeviceTypes(types);
			}
		} catch (err) {
			console.error("Error fetching device types:", err);
			message.error("Failed to fetch device types");
		}
	};

	const setFormValue = (data) => {
		const foundType = _find(deviceTypes, { id: data.type_id });
		const prefix = foundType?.sl_prefix || '';
		const updatedValue = prefix ? data.qr.replace(prefix, '') : data.qr;

		setDeviceType(foundType?.name || '');
		setDevicePrefix(prefix);
		setDeviceSlNo(updatedValue);
		setDeviceDescription(data.description);
	};

	const resetFormFields = () => {
		resetFields();
		setDeviceSlNo('');
		setDeviceType('');
		setDeviceDescription('');
		setDevicePrefix('');
		setError({
			unauthorised: false,
			unauthorisedMsg: '',
			api: false,
			apiMsg: ''
		});
	};

	const closeDrawer = () => {
		onClose();
		// resetFormFields();
		if (deviceAdded) {
			fetchDeviceList();
		}
		setDeviceAdded(false);
	};

	const handleDeviceFormChange = (value, key) => {
		const val = value.target.value;
		if (key === 'sl_no') {
			const updatedValue = devicePrefix && val.includes(devicePrefix) 
				? val.replace(devicePrefix, '') 
				: val;
			setDeviceSlNo(updatedValue);
		} else if (key === 'description') {
			setDeviceDescription(val);
		}
	};

	const typeSelect = (value) => {
		const deviceDet = value ? _find(deviceTypes, { name: value }) : null;
		setDeviceType(value);
		setDevicePrefix(deviceDet?.sl_prefix || '');
	};

	const handleSubmit = () => {
		validateFieldsAndScroll(async (err, values) => {
			if (!err) {
				if (edit_device) {
					await deviceEditFunction();
				} else {
					await deviceAddFunction();
				}
			}
		});
	};

	const deviceAddFunction = async () => {
		try {
			setLoading(true);
			const serialNo = devicePrefix ? devicePrefix + deviceSlNo : deviceSlNo;

			const response = await addDevice({
				devices: [{
					serial_no: serialNo,
					device_type: deviceType,
					description: deviceDescription,
				}],
				client_id
			});

			if (response.status === 403) {
				openNotification('error', response.message);
				setError({
					unauthorised: true,
					unauthorisedMsg: response.message,
					api: false,
					apiMsg: ''
				});
			} 
			else if (response.status === 'success') {
				openNotification('success', 'Device added successfully');
				setDeviceAdded(true);
				resetFormFields();
			}
			else {
				openNotification('error', response.message || 'Failed to add device');
				setError({
					unauthorised: false,
					unauthorisedMsg: '',
					api: true,
					apiMsg: response.message || 'Failed to add device'
				});
			}

		} catch (error) {
			console.error('Add device error:', error);
			openNotification('error', 'Failed to add device');
			setError({
				unauthorised: false,
				unauthorisedMsg: '',
				api: true,
				apiMsg: 'Failed to add device'
			});
		} finally {
			setLoading(false);
		}
	};

	const deviceEditFunction = async () => {
		if (!selected_row_data) return;
		
		try {
			setLoading(true);
			const serialNo = devicePrefix ? devicePrefix + deviceSlNo : deviceSlNo;

			const response = await editDevice({
				serial_no: serialNo,
				device_type: deviceType,
				description: deviceDescription,
			}, selected_row_data.id, client_id);

			if (response.status === 403) {
				openNotification('error', response.message);
				setError({
					unauthorised: true,
					unauthorisedMsg: response.message,
					api: false,
					apiMsg: ''
				});
			}
			else if (response.status === 'success') {
				openNotification('success', 'Device edited successfully');
				try {
					await fetchDeviceList();
					resetFormFields();
				} catch (fetchError) {
					console.error('Error fetching device list:', fetchError);
				}
			}
			else {
				openNotification('error', response.message || 'Failed to edit device');
				setError({
					unauthorised: false,
					unauthorisedMsg: '',
					api: true,
					apiMsg: response.message || 'Failed to edit device'
				});
			}

		} catch (error) {
			console.error('Edit device error:', error);
			openNotification('error', 'Failed to edit device');
			setError({
				unauthorised: false,
				unauthorisedMsg: '',
				api: true,
				apiMsg: 'Failed to edit device'
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<AntDrawer
			title={
				<div className="drawer-title">
					{edit_device ? 'Edit Device' : 'Add New Device'}
				</div>
			}
			width={720}
			className="add-edit-device-drawer"
			placement="right"
			visible={true}
			closable={false}
			onCancel={closeDrawer}
			destroyOnClose={true}
			maskClosable={false}
			style={{ paddingBottom: 53 }}
			getContainer={false}
		>
			<Form autoComplete="off" layout="vertical" hideRequiredMark>
				<AntRow gutter={16}>
					<AntCol span={17} className="wid-100">
						<Form.Item label="Device type *">
							{getFieldDecorator('type', {
								rules: [{ required: true, message: 'Please select device type' }],
								initialValue: deviceType,
								onChange: typeSelect,
							})(
								<AntSelect
									showSearch
									optionFilterProp="title"
									placeholder="Select device type"
								>
									{deviceTypes.map(device => (
										<AntOption
											key={device.id}
											title={device.name}
											value={device.name}
										>
											{device.name}
										</AntOption>
									))}
								</AntSelect>
							)}
						</Form.Item>
					</AntCol>
				</AntRow>
				<AntRow gutter={16}>
					<AntCol span={17} className="wid-100">
						<Form.Item label="Device serial no *">
							{getFieldDecorator('sl_no', {
								rules: [{ required: true, message: 'Please enter serial no' }],
								initialValue: deviceSlNo,
								onChange: (e) => handleDeviceFormChange(e, 'sl_no'),
							})(
								<AntInput
									className="device-serial"
									addonBefore={devicePrefix}
									placeholder="Enter serial no"
								/>
							)}
						</Form.Item>
					</AntCol>
				</AntRow>
				<AntRow gutter={16}>
					<AntCol span={17} className="wid-100">
						<Form.Item label="Description">
							{getFieldDecorator('description', {
								initialValue: deviceDescription,
								onChange: (e) => handleDeviceFormChange(e, 'description'),
							})(
								<AntTextArea
									rows={3}
									placeholder="Please enter device description"
								/>
							)}
						</Form.Item>
					</AntCol>
				</AntRow>
			</Form>
			<div
				style={{
					position: 'absolute',
					bottom: 0,
					width: '100%',
					borderTop: '1px solid #e8e8e8',
					padding: '10px 16px',
					textAlign: 'right',
					left: 0,
					background: '#fff',
					borderRadius: '0 0 4px 4px',
				}}
			>
				<AntButton
					style={{ marginRight: 8 }}
					onClick={closeDrawer}
				>
					Cancel
				</AntButton>
				<AntButton
					onClick={handleSubmit}
					loading={loading}
					type="primary"
				>
					Submit
				</AntButton>
			</div>
		</AntDrawer>
	);
};

AddDeviceForm.propTypes = {
	fetchDeviceList: PropTypes.func.isRequired,
	onClose: PropTypes.func.isRequired,
	edit_device: PropTypes.bool,
	selected_row_data: PropTypes.shape({
		id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
		type_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
		qr: PropTypes.string,
		description: PropTypes.string
	}),
	client_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
	form: PropTypes.shape({
		getFieldDecorator: PropTypes.func.isRequired,
		resetFields: PropTypes.func.isRequired,
		validateFieldsAndScroll: PropTypes.func.isRequired
	}).isRequired
};

AddDeviceForm.defaultProps = {
	edit_device: false,
	selected_row_data: null
};

const AddDevice = Form.create()(AddDeviceForm);

export default AddDevice;
