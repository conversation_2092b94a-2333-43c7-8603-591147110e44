import React, { Component } from 'react'
import AntModal from '@datoms/react-components/src/components/AntModal';
import AntOption from '@datoms/react-components/src/components/AntOption';
import AntSelect from '@datoms/react-components/src/components/AntSelect';
import AntMessage from '@datoms/react-components/src/components/AntMessage';
import AntNotification from '@datoms/react-components/src/components/AntNotification';
import AntPasswordModal from '@datoms/react-components/src/components/AntPasswordModal';

import { getUniqueArray } from '@datoms/js-utils/src/basic-utility';
import { isCustomerValid } from '@datoms/js-utils/src/access-functions';
import {assignDevice, unassignDevice, retriveCustomerList} from '@datoms/js-sdk';
import _find from 'lodash/find';
import "./style.less";

/**
 * @typedef {object} SelectedDeviceRow
 * @property {string|number} device_id - The unique identifier of the device.
 * @property {string|number} [customer_id] - The ID of the customer the device is currently assigned to (if assigned).
 * @property {string|number} [vendor_id] - The ID of the vendor the device is currently assigned to (used as fallback for customer_id in unassign).
 * @property {string} qr_code - The serial number or QR code identifier of the device.
 * @property {'assigned' | string} assign - Indicates the current assignment status (e.g., "assigned"). Used to determine assign/unassign flow.
 */

/**
 * @typedef {object} CustomerData
 * @property {string|number} id - Customer ID.
 * @property {string} name - Customer name.
 * @property {string} status - Customer status (e.g., 'active', 'inactive').
 * @property {Array<number>} customer_type - Array of numerical type identifiers for the customer.
 * @property {Array<number>} [applications] - Array of application IDs associated directly.
 * @property {Array<number>} [access_applications] - Array of application IDs accessible (potentially inherited).
 * @property {boolean} is_vendor - Flag indicating if the customer is also a vendor.
 * @property {string|number} [vendor_id] - ID of the associated vendor (if applicable).
 * @property {string|number} [parent_vendor_id] - ID of the parent vendor (if applicable).
 */

/**
 * @typedef {object} ApplicationData
 * @property {string|number} id - Application ID.
 * @property {string} name - Application name.
 * @property {Array<number>} [categories] - Array of category IDs associated with the application (potentially related to 'things').
 */

/**
 * @typedef {object} VendorData
 * @property {string|number} id - Vendor ID.
 * @property {string} name - Vendor name.
 */

/**
 * @component AssignDevice
 * @description A component that renders a modal to either assign selected devices to a customer/application
 * or confirm the unassignment of already assigned devices. It fetches customer and application lists,
 * handles user selections within the modal, and interacts with the backend SDK for assign/unassign operations.
 * The specific modal shown (standard assign modal or confirmation modal for unassign) depends on the
 * `assign` status of the first selected device.
 *
 * @param {object} props - The component props.
 * @param {string|number} props.client_id - The ID of the current client context, required for API calls and logic.
 * @param {Array<SelectedDeviceRow>} props.selected_rows - An array of objects representing the devices selected for the action.
 * @param {Function} props.resetAction - Callback function invoked when the modal is cancelled or the operation completes, used to reset the parent component's state.
 * @param {Function} props.fetchDeviceList - Callback function invoked after a successful assign/unassign operation to refresh the device list in the parent component.
 * @param {Array<string>} [props.enabled_features] - An array of feature flags, used to conditionally show UI elements (e.g., the Partner dropdown).
 * @param {string|number} [props.application_id] - The ID of the current application context, used for conditional logic (e.g., showing Partner dropdown).
 * @param {string} [props.client_name] - The name of the current client, used when constructing the vendor list if specific features are enabled.
 *
 * @extends {Component}
 */

export class AssignDevice extends Component {
    c/**
     * @constructor
     * @param {object} props - Component props.
     */
    constructor(props) {
      super(props);
      /**
       * @property {object} state - Component state.
       * @property {boolean} state.optionLoading - Indicates if customer/application options are being loaded.
       * @property {Array<React.ReactNode>} state.customerSelectOptions - Array of `<AntOption>` components for the customer dropdown.
       * @property {Array<React.ReactNode>} state.vendorSelectOptions - Array of `<AntOption>` components for the vendor/partner dropdown.
       * @property {Array<React.ReactNode>} state.applicationSelectOptions - Array of `<AntOption>` components for the application dropdown.
       * @property {boolean} [state.btn_loading] - Indicates if the assign/unassign API call is in progress (used for modal confirm button).
       * @property {Array<ApplicationData>} [state.app_lists] - Raw list of available applications fetched from API.
       * @property {Array<object>} [state.cust_app_access] - Processed list mapping customer IDs to their accessible application IDs.
       * @property {Array<VendorData>} [state.vendor_list] - List of available vendors/partners.
       * @property {Array<CustomerData>} [state.customer_list] - Raw list of available customers fetched from API.
       * @property {string|number|null} [state.device_assignment_customer_id] - The ID of the customer selected in the modal.
       * @property {string|number|null} [state.device_assignment_application_id] - The ID of the application selected in the modal.
       * @property {Array<number>} [state.device_assignment_customer_type] - The customer type array of the selected customer.
       * @property {string|number|undefined} [state.selected_vendor] - The ID of the vendor/partner selected in the modal.
       * @property {boolean} [state.convert_to_things] - Flag potentially used in assign payload (logic seems specific to client_id 392 or potentially future use).
       * @property {Array<object>} [state.selected_app_thing_types] - *Note: This state seems set in `selectApplication` but isn't clearly used elsewhere in the provided code.*
       * @property {any} [state.selected_thing_type] - *Note: This state seems set in `selectApplication` but isn't clearly used elsewhere in the provided code.*
       * @property {any} [state.thing_name] - *Note: This state seems set in `selectApplication` but isn't clearly used elsewhere in the provided code.*
       * @property {Array<object>} [state.things_categories] - *Note: This state is used in `selectApplication` but not initialized or fetched in the provided code. Assumed to be potentially populated elsewhere or part of incomplete logic.*
       */
        this.state = {
          optionLoading: true,
          customerSelectOptions: [],
          vendorSelectOptions: [],
          applicationSelectOptions: [],
        };
    }

     /**
     * @memberof AssignDevice
     * @description Lifecycle method called after the component mounts. Initiates fetching of the customer list.
     * @returns {void}
     */
    componentDidMount() {
      if (this.props.action === "assign_device") {
        this.getCustomerListFunction();
      }
    }

    /**
   * This function calls the notification alert.
   * @param  {String}	type
   * @param  {String}	msg
   * @return {void}
   */
    openNotification(type, msg) {
        if (window.innerWidth < 576) {
        AntMessage(type, msg);
        } else {
        AntNotification({
            type: type,
            message: msg,
            placement: "bottomLeft",
            className: "alert-" + type,
        });
        }
    }

    /**
     * @memberof AssignDevice
     * @description Displays a notification message. Uses `AntMessage` for smaller screens
     * and `AntNotification` for larger screens.
     * @param {'success' | 'error' | 'info' | 'warning'} type - The type of the notification ('success', 'error', etc.).
     * @param {string} msg - The message content to display.
     * @returns {void}
     */
    async unassignDeviceFunction(data) {
      let clientId = this.props.client_id;
      let that = this;
      let response = await unassignDevice(data, clientId);
      if (response.status === "success") {
        that.openNotification("success", "Device(s) unassigned successfully");
        that.props.resetAction();
        that.props.fetchDeviceList();
      } else {
        that.openNotification("error", response.message);
        that.props.resetAction();
      }
    }

     /**
     * @memberof AssignDevice
     * @description Callback executed when the user confirms unassignment (clicks "Yes, Sure" in the `AntPasswordModal`).
     * Constructs the payload from `props.selected_rows` and calls `unassignDeviceFunction`.
     * @returns {void}
     */
    unassignedOnOk() {
      if (this.props.selected_rows && this.props.selected_rows.length) {
        let deviceLists = [];
        this.props.selected_rows.map((row) => {
          deviceLists.push({
            id: row.device_id,
            customer_id: row.customer_id ? row.customer_id : row.vendor_id,
            serial_no: row.qr_code,
          });
        });
        let data = {
          devices: deviceLists,
          client_id: this.props.client_id,
        };
        this.unassignDeviceFunction(data);
      }
    }

   /**
     * @memberof AssignDevice
     * @async
     * @description Handles the confirmation ("OK") action in the assignment modal.
     * Constructs the payload using selected devices from props and selected customer/application/vendor from state.
     * Calls the `assignDevice` SDK function, handles the response, shows notifications, and triggers parent updates.
     * @returns {Promise<void>} A promise that resolves when the function completes.
     */
    async handleAssignDevicesToCustomer() {
      let that = this;
      if (this.props.selected_rows && this.props.selected_rows.length) {
        let deviceLists = [];
        // console.log('selectedRows_', this.state.selected_rows);
        this.props.selected_rows.map((row) => {
          deviceLists.push({
            id: row.device_id,
            serial_no: row.qr_code,
          });
        });

        this.setState({
          optionLoading: true,
          btn_loading: true
        });

        let data = {
          device_ids: deviceLists,
          customer_id: this.state.device_assignment_customer_id,
          application_id: this.state.device_assignment_application_id,
          convert_device_to_things:
            this.props.client_id == 392
              ? true
              : this.state.convert_to_things,
        };

        if (this.state.selected_vendor && !this.state.device_assignment_customer_type.includes(4)) {
          data['device_vendor_id'] = this.state.selected_vendor;
        } else {
          data['device_vendor_id'] = this.state.device_assignment_customer_id;
        }
        let response = await assignDevice(data, this.props.client_id);
        if (response.status === 403) {
          that.setState({
            optionLoading: false
          });
        } else if (response.status === 'success') {
          that.openNotification(
            'success',
            'Device(s) assigned successfully.'
          );
          console.log("action reseting action");
          that.props.resetAction();
          console.log("action fetching device list");
          that.props.fetchDeviceList();
        } else {
          that.openNotification('error', response.message);
          that.setState({
            optionLoading: false,
            btn_loading: false
          });
        }
      }
    }

    /**
     * @memberof AssignDevice
     * @description Determines if a customer should be included in the dropdown list based on specific rules,
     * particularly for client_id 1, related to vendor status and parent vendors.
     * @param {CustomerData} customer - The customer object to validate.
     * @returns {boolean} `true` if the customer is allowed, `false` otherwise.
     */
    isCustomerAllowed(customer){
      if(this.props.client_id !== 1){
        return true;
      }
      if(customer.is_vendor && customer.vendor_id !== 1){
        return false;
      }
      if(!customer.is_vendor && customer.parent_vendor_id){
        return false
      }
      return true;
    }

    /**
     * @memberof AssignDevice
     * @async
     * @description Fetches the list of customers and related application/vendor data using the SDK.
     * Processes the response to filter valid/allowed customers, extract application access information,
     * build vendor lists, and create dropdown options (`<AntOption>`). Updates the component state with the fetched data and options.
     * @returns {Promise<void>} A promise that resolves when the function completes.
     */
    async getCustomerListFunction() {
      let that = this;
      this.setState({
        optionLoading: true,
      });
      let response = await retriveCustomerList(this.props.client_id);
      if (response.status === 403) {
        that.setState({
          optionLoading: false
        });
      } else if (response.status === 'success') {
        const vendorList = this.props.enabled_features?.includes(
          'IndustryManagement:Dealers'
        ) ? [{id: this.props.client_id, name: this.props.client_name}] : [];

        const customerOptions = [];
        let custAppAccess = [];
        if (response.customers && response.customers.length) {
          response.customers
            .map((client) => {
              if(isCustomerValid(client.status) && this.isCustomerAllowed(client)){
                customerOptions.push(
                  <AntOption value={client.id}>
                    {client.name}
                  </AntOption>
                );
              }

              custAppAccess.push({
                id: client.id,
                applications: client.customer_type.includes(5) ? getUniqueArray(client.applications, client.access_applications) : client.customer_type.includes(4) ? getUniqueArray(client.access_applications) : getUniqueArray(client.applications)
              });
              if (client.is_vendor) {
                vendorList.push({
                  id: client.id,
                  name: client.name,
                });
              }
            })
        }

        this.setState(
          {
            app_lists: response.applications,
            cust_app_access: custAppAccess,
            vendor_list: vendorList,
            customer_list: response.customers,
            customerSelectOptions: customerOptions,
            optionLoading: false
          },
        );
      } else {
        that.openNotification('error', response.message);
        that.setState({
          optionLoading: true,
        });
      }
    }

    /**
     * @memberof AssignDevice
     * @description Handles the selection of a customer from the dropdown.
     * Finds the selected customer's data, determines their accessible applications and potential vendor/partner options.
     * Updates the state with the selected customer ID, type, and populates the application and vendor dropdown options.
     * Automatically selects the application if only one is available.
     * @param {string|number} value - The ID of the selected customer.
     * @returns {void}
     */
    selectClient(value) {
      let appLists = [],
        foundCust,
        custType = [];

      if (this.state.cust_app_access && this.state.cust_app_access.length) {
        foundCust = _find(this.state.cust_app_access, {
          id: parseInt(value),
        });
        if (foundCust) {
          if (foundCust.applications && foundCust.applications.length) {
            if (this.state.app_lists && this.state.app_lists.length) {
              foundCust.applications.map((app) => {
                let foundApp = _find(this.state.app_lists, {
                  id: parseInt(app),
                });
                if (foundApp) {
                  appLists.push({
                    id: foundApp.id,
                    name: foundApp.name,
                  });
                }
              });
            }
          }
        }
      }
      let selectedCust = _find(this.state.customer_list, {
        id: parseInt(value),
      });
      if (selectedCust && selectedCust.customer_type) {
        custType = selectedCust.customer_type;
      }

      let vendorSelectionOptions = [],
        vendor_id;
      if (selectedCust && selectedCust.vendor_id) {
        vendor_id = selectedCust.vendor_id;
        this.state.vendor_list.forEach((item) => {
          if (item.id == 1 || item.id == this.props.client_id || item.id == selectedCust.vendor_id) {
            vendorSelectionOptions.push({
              id: item.id,
              name: item.name,
            });
          }
        });
      }
      vendorSelectionOptions = vendorSelectionOptions.map((vendor) => {
        return <AntOption value={vendor.id}>{vendor.name}</AntOption>;
      });
      let applicationSelectionOptions = [];
      if (appLists.length) {
        applicationSelectionOptions = appLists
          .map((application) => {
            return (
              <AntOption value={application.id}>
                {application.name}
              </AntOption>
            );
          })
          .filter(Boolean);
      }
      let stateUpdate = {
        device_assignment_customer_id: value,
        device_assignment_application_id: null,
        device_assignment_customer_type: custType,
        vendorSelectOptions: vendorSelectionOptions,
        applicationSelectOptions: applicationSelectionOptions,
      };
      stateUpdate['selected_vendor'] = vendor_id;
      this.setState(stateUpdate, () => {
        if (this.state.applicationSelectOptions.length == 1) {
          this.selectApplication(appLists[0].id);
        }
      });
    }

     /**
     * @memberof AssignDevice
     * @description Handles the selection of an application from the dropdown.
     * Updates the state with the selected application ID.
     * *Note: The logic related to finding 'thing types' based on application categories seems incomplete or unused in the rest of the provided code.*
     * @param {string|number} value - The ID of the selected application.
     * @returns {void}
     */
    selectApplication(value) {
      let selectedApplicationThingTypes = [];
      if (
        this.state.application_lists &&
        this.state.application_lists.length
      ) {
        let found_application = _find(this.state.application_lists, {
          id: value,
        });
        if (found_application) {
          if (
            found_application.categories &&
            found_application.categories.length
          ) {
            if (
              this.state.things_categories &&
              this.state.things_categories.length
            ) {
              this.state.things_categories.map((thing) => {
                if (
                  found_application.categories.includes(thing.id)
                ) {
                  selectedApplicationThingTypes.push({
                    id: thing.id,
                    name: thing.name,
                  });
                }
              });
            }
          }
        }
      }
      this.setState({
        device_assignment_application_id: value,
        selected_app_thing_types: selectedApplicationThingTypes,
        selected_thing_type: undefined,
        thing_name: undefined,
        convert_to_things:
          this.props.client_id == 392
            ? true
            : this.state.convert_to_things,
      });
    }

    /**
     * @memberof AssignDevice
     * @description Handles the selection of a vendor/partner from the dropdown.
     * Updates the state with the selected vendor ID.
     * @param {string|number} value - The ID of the selected vendor.
     * @returns {void}
     */
    selectVendor(value) {
      this.setState({ selected_vendor: value });
    }

    /**
     * @memberof AssignDevice
     * @description Search handler for the application dropdown (currently only logs the search value).
     * @param {string} val - The search input value.
     * @returns {void}
     */
    onSearchApplication(val) {
      // console.log('search:', val);
    }

    /**
     * @memberof AssignDevice
     * @description Renders the appropriate modal based on the assignment status of the selected devices.
     * - If devices are already 'assigned', renders `AntPasswordModal` for unassignment confirmation.
     * - Otherwise, renders `AntModal` with dropdowns for selecting customer, partner (optional), and application for assignment.
     * @returns {React.ReactNode} The modal component.
     */
    render() {
      let assignModalOptions = [
        <div className="assign-to-customer-label-select">
          <span className="customer-select-label">Customer</span>
          <AntSelect
            showSearch
            style={{
              width: 250,
            }}
            className="customer-select"
            placeholder="Select Customer"
            optionFilterProp="children"
            loading={this.state.optionLoading}
            onChange={(e) => this.selectClient(e)}
            filterOption={(input, option) =>
              option.props.children
                .toLowerCase()
                .indexOf(input.toLowerCase()) >= 0
            }
          >
            {this.state.customerSelectOptions}
          </AntSelect>
        </div>,
        <>
          {(parseInt(this.props.application_id) == 12 || this.props.enabled_features?.includes(
          'IndustryManagement:Dealers'
        )) && this.state.vendorSelectOptions.length > 1 && (
              <div className="assign-to-customer-label-select">
                <span className="customer-select-label">
                  Partner
                </span>
                <AntSelect
                  showSearch
                  style={{
                    width: 250,
                  }}
                  className="customer-select"
                  placeholder="Select Partner"
                  optionFilterProp="children"
                  disabled={
                    this.state.vendorSelectOptions.length == 1 &&
                    parseInt(this.state.selected_vendor) == 1
                  }
                  value={this.state.selected_vendor}
                  onChange={(e) => this.selectVendor(e)}
                  filterOption={(input, option) =>
                    option.props.children
                      .toLowerCase()
                      .indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {this.state.vendorSelectOptions}
                </AntSelect>
              </div>
            )}
        </>,
        <>
          {this.state.applicationSelectOptions.length > 1 && (
            <div className="assign-to-application-label-select">
              <span className="application-select-label">
                Application
              </span>
              <AntSelect
                showSearch
                className="application-select"
                style={{
                  width: 250,
                }}
                placeholder="Select Application"
                optionFilterProp="children"
                onChange={(e) => this.selectApplication(e)}
                onSearch={(e) => this.onSearchApplication(e)}
                filterOption={(input, option) =>
                  option.props.children
                    .toLowerCase()
                    .indexOf(input.toLowerCase()) >= 0
                }
              >
                {this.state.applicationSelectOptions}
              </AntSelect>
            </div>
          )}
        </>,
      ];

      console.log("action received-> ", this.props.action);

      return (
        this.props.action === "unassign_device" ?
        (
          <AntPasswordModal
            customBody={
              <div style={{width: '100%', textAlign: 'center', fontSize: 15, marginTop: 10}}>
                {"Do you want to un-assign all the selected device(s)?"}
              </div>
            }
            isVisible
            title=""
            okTitle="Yes,Sure"
            custom_body_className={`device-unassign-modal mobile un-assign`}
            onOk={() => this.unassignedOnOk()}
            onCancel={this.props.resetAction}
            icon={
            <img
              src={"https://static.datoms.io/images/icons/device/unassign-device.svg"}
                alt="logo"
                style={{
                width: 50,
                height: 50,
              }}
            />
            }
          />
        ) : (
          <AntModal
            className="customer-assign-modal"
            title="Assign To Customer"
            visible
            onOk={() => this.handleAssignDevicesToCustomer()}
            onCancel={this.props.resetAction}
            destroyOnClose={true}
            confirmLoading={this.state.btn_loading}
          >
            {assignModalOptions}
          </AntModal>
        )
      )
    }
}

export default AssignDevice