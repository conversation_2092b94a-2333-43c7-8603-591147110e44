import React from 'react'

import AddDevice from './DeviceAdd/AddDevice'
import SimAddEdit from './SimAddEdit/SimAddEdit'
import AssignDevice from './AssignDevice/AssignDevice'
import DeviceStatus from './DeviceStatus/DeviceStatus'
import AddBulkDevice from './AddBulkDevice/AddBulkDevice'
import FirmwareUpdate from './FirmwareUpdate/FirmwareUpdate'
import ThirdPartyConnection from './ThirdPartyConnection/ThirdPartyConnection'
import {DevicePageProps} from "./types"


/**
 * @component DevicePage
 * @description
 * A functional component that acts as a router or conditional renderer for various device-related
 * action components (Modals, Drawers, etc.). Based on the `action` property within the `actionState` prop,
 * it mounts and displays the appropriate child component (e.g., `AddDevice`, `DeviceStatus`, `FirmwareUpdate`).
 *
 * It serves as a central point to manage which action UI is currently active, passing necessary data,
 * context, and callback functions down to the rendered child component. This component itself doesn't
 * render any persistent UI, only the conditionally selected child.
 *
 * @param {DevicePageProps} props - The component props, conforming to the DevicePageProps interface.
 * @param {object} props.actionState - Contains the current action state required to determine which child component to render and what data to pass.
 * @param {string} props.actionState.action - A string identifier (e.g., "add_device", "firmware_update", "assign_device") that determines which specific action component to render.
 * @param {any} props.actionState.row_value - Data relevant to the current action, typically an array of selected device objects or related information, passed down to the active child component.
 * @param {Function} props.fetchDeviceList - A callback function passed down to child components (like `AddDevice`, `AssignDevice`, `DeviceStatus`, `SimAddEdit`, `AddBulkDevice`) that need to trigger a refresh of the device list in the parent component after a successful operation.
 * @param {Function} props.resetActionState - A callback function passed down to all child components. It should be called by the child component upon completion (e.g., successful save, cancellation, closing the modal/drawer) to signal the parent component to reset the `actionState`, effectively hiding the action component.
 * @param {object} props.context - An object containing contextual information relevant to the current user session or client, passed down to specific child components that require it.
 * @param {string|number} props.context.client_id - The ID of the current client.
 * @param {string} [props.context.client_name] - The name of the current client (used in `AssignDevice`).
 * @param {string|number} [props.context.application_id] - The ID of the current application context (used in `AssignDevice`).
 * @param {Array<string>} [props.context.enabled_features] - An array of strings representing enabled feature flags (used in `AssignDevice`).
 * @param {object} props.socket - An active Socket.IO client instance, passed specifically to the `FirmwareUpdate` component for real-time communication related to firmware updates.
 *
 * @returns {React.ReactNode} The conditionally rendered child action component corresponding to the current `props.actionState.action`, or null/empty fragment if no action matches or `actionState.action` is null/undefined.
 */
const DevicePage: React.FC<DevicePageProps> = (props) => {
    return (
        <>
            {
                props.actionState.action === "add_device" &&
                <AddDevice
                    fetchDeviceList={props.fetchDeviceList}
                    edit_device={false}
                    onClose={props.resetActionState}
                    selected_row_data={props.actionState.row_value}
                    client_id={props.context.client_id}
                />
            }

            {
                (props.actionState.action === "update_device_status" || props.actionState.action === "unblock_devices" || props.actionState.action === "block_devices") &&
                <DeviceStatus
                    action={props.actionState.action}
                    selected_rows={props.actionState.row_value}
                    resetAction={props.resetActionState}
                    client_id={props.context.client_id}
                    fetchDeviceList={props.fetchDeviceList}
                />
            }

            {
                props.actionState.action === "add_bulk_device" &&
                <AddBulkDevice
                    resetAction={props.resetActionState}
                    client_id={props.context.client_id}
                    fetchDeviceList={props.fetchDeviceList}
                />
            }

            {
                props.actionState.action === "firmware_update" &&
                <FirmwareUpdate
                    socket={props.socket}
                    client_id={props.context.client_id}
                    row_value={props.actionState.row_value}
                    resetAction={props.resetActionState}
                />
            }

            {
                props.actionState.action === "sim_add_edit" &&
                <SimAddEdit
                    client_id={props.context.client_id}
                    row_value={props.actionState.row_value}
                    resetAction={props.resetActionState}
                    fetchDeviceList={props.fetchDeviceList}
                />
            }

            {
                props.actionState.action === "3rd_party_connection" &&
                <ThirdPartyConnection
                    row_value={props.actionState.row_value}
                    resetAction={props.resetActionState}
                />
            }

            {
                (props.actionState.action === "assign_device" || props.actionState.action === "unassign_device") &&
                <AssignDevice
                    client_id={props.context.client_id}
                    client_name={props.context.client_name}
                    application_id={props.context.application_id}
                    enabled_features={props.context.enabled_features}
                    selected_rows={props.actionState.row_value}
                    action={props.actionState.action}
                    resetAction={props.resetActionState}
                    fetchDeviceList={props.fetchDeviceList}
                />
            }
        </>
    )
}

export default DevicePage