import React from "react";
import { Button as AntButton } from "antd";
import AntMenu from "@datoms/react-components/src/components/AntMenu";
import AntMenuItem from "@datoms/react-components/src/components/AntMenuItem";
import * as allIcons from "@ant-design/icons";
import CustomUiButton from "@datoms/react-components/src/components/CustomUiButton";
import SegmentedTabs from "@components/base/SegmentedTabs";
import { Link, useHistory } from "react-router-dom";
import { getBaseUrl } from "@datoms/js-utils/src/base-url-logic";
import "./style.less";

interface ActionOption {
  value: string;
  label: string;
  link?: string;
}

interface MenuAction {
  key: string;
  title: string;
  link?: string;
}

interface ComponentProps {
  options: ActionOption[];
  value: string;
  menuItems?: MenuAction[];
  loading?: boolean;
  disabled?: boolean;
}

interface Action {
  key: string;
  title?: string;
  component?: string;
  component_props?: ComponentProps;
  position: "left" | "right";
}

interface ActionButtonsProps {
  actions: Action[];
  context: any;
  onActionClick: (key: string) => void;
  position: "left" | "right";
  downloadLoading?: boolean;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  actions,
  context,
  onActionClick,
  position,
  downloadLoading,
}) => {
  const history = useHistory();

  const filteredActions = actions?.filter(
    (action) =>
      action.position === position || (!action.position && position !== "left"),
  ) ?? [];

  const renderComponent = (action: Action) => {
    switch (action.component) {
      case "SegmentedTabs":
        return (
          <SegmentedTabs
            {...action.component_props}
            onChange={(value) => {
              const selectedOption = action.component_props?.options.find(
                (opt) => opt.value === value,
              );
              if (selectedOption?.link) {
                history.push(getBaseUrl(context, selectedOption.link));
                return;
              }
              onActionClick(action.key, []);
            }}
          />
        );

      case "CustomUiButton":
        const Icon = action.icon ? allIcons[action.icon] : allIcons.PlusOutlined;
        return (
            <CustomUiButton
              isMultiple={true}
              loading={action.component_props?.loading}
              is_disabled={action.component_props?.disabled}
              overlay={
                <AntMenu>
                  {action.component_props?.menuItems?.map((item) => (
                    <AntMenuItem key={item.key}>
                      {item.link ? (
                        <Link to={getBaseUrl(context, item.link)}>
                          {item.title}
                        </Link>
                      ) : (
                        <div onClick={() => onActionClick(item.key, [])}>
                          {item.title}
                        </div>
                      )}
                    </AntMenuItem>
                  ))}
                </AntMenu>
              }
              custom_icon={<Icon />}
            />
        );

      default:
        return (
          <AntButton
            type="primary"
            onClick={() => onActionClick(action.key, [])}
            // make this generic for other actions later
            loading={action.key === "download" && downloadLoading} 
          >
            {action.title}
          </AntButton>
        );
    }
  };

  if (!filteredActions.length) return null;

  return (
    <div className={`action-buttons-${position}`}>
        <div style={{display: "flex", alignItems: "center", gap: 16}}>
      {filteredActions.map((action, index) => (
        <div key={`${action.key}-${index}`} className="action-button-wrapper">
          {renderComponent(action)}
        </div>
      ))}
        </div>
    </div>
  );
};

export default ActionButtons;
