export interface TableColumn {
    key: string;
    title: string;
    dataIndex: string;
    align?: 'left' | 'right' | 'center';
    fixed?: boolean | 'left' | 'right';
    width?: number;
    sorter?: boolean;
    ellipsis?: boolean;
    render?: string | ((text: any, record: any) => React.ReactNode);
    [key: string]: any;
  }

  export interface TableProps {
    size: 'small' | 'middle' | 'large';
    columns: TableColumn[];
    expandable: boolean;
    pagination: {
      pageSize: number;
      hideOnSinglePage: boolean;
    };
    rowSelection: boolean | object;
  }

  export interface TableData {
    config: any;
    data: any[];
    totalCount: number;
  }

export type KPIData = {
  description?: string;
  key: string;
  title: string;
  value: number | string;
  divider?: boolean;
  clickable?: boolean;
};

export type KPIConfig = {
  key?: string;
  size?: 'small' | 'medium' | 'large';
  state?: 'valueFirst' | 'titleFirst';
  showInfo?: boolean;
  color?: string;
  backgroundColor?: string;
  itemsClickable?: boolean;
};

export interface PageConfig {
  title?: string;
  page_url?: string;
  orgPreferenceKey?: string;
  kpis?: {
    config?: KPIConfig;
    data: KPIData[];
  };
}
