{"filters": [{"optionData": [{"value": "assigned", "title": "Assigned"}, {"value": "unassigned", "title": "Unassigned"}], "feature_key": "UserManagement:Assignment", "showSearch": false, "sorted": false, "allowClear": true, "label": "Assignment Status", "placeholder": "Select Status", "no_outside_label": true, "url_name": "assignment", "key": "assignment", "query_key": "info.assigned", "is_outside_filter_drawer": true, "is_inside_filter_drawer": false, "defaultValue": "assigned", "remove_filters": {"unassigned": ["customers", "partners", "territories", "account_type"]}}, {"optionData": [{"value": "production", "title": "Production"}, {"value": "sandbox", "title": "Sandbox"}, {"value": "development", "title": "Development"}], "feature_key": "UserManagement:AccountType", "showSearch": false, "sorted": false, "allowClear": true, "label": "Account Type", "placeholder": "Select Account Type", "no_outside_label": true, "url_name": "account_type", "key": "account_type", "query_key": "info.account_type", "is_outside_filter_drawer": true, "is_inside_filter_drawer": false, "defaultValue": "production", "dependent_filters": ["partners", "customers"]}, {"type": "tree_select", "feature_key": "UserManagement:Territory", "label": "Territory", "url_name": "territories", "key": "territories", "query_key": "info.territory_id", "filter_api": "territories", "is_options_dynamic": true, "component_props": {"treeData": [], "value": [], "treeDefaultExpandAll": true, "treeCheckable": true, "maxTagCount": 0, "placeholder": "Select territories"}, "no_outside_label": true, "is_outside_filter_drawer": true, "is_inside_filter_drawer": false, "dependent_filters": ["partners", "customers"]}, {"feature_key": "UserManagement:Partner", "showSearch": true, "sorted": false, "allowClear": true, "label": "Partner", "placeholder": "Partners", "no_outside_label": true, "multiSelect": true, "url_name": "partners", "is_options_dynamic": true, "filterOption": false, "autoClearSearchValue": false, "key": "partners", "query_key": "info.partner_org_id", "showSingleOption": true, "showEmptyOption": true, "is_outside_filter_drawer": true, "is_inside_filter_drawer": false, "filter_api": "partners", "api_pagination": true, "dependent_filters": ["customers"]}, {"feature_key": "UserManagement:Customer", "showSearch": true, "sorted": false, "allowClear": true, "multiSelect": true, "label": "Customer", "placeholder": "Customers", "is_options_dynamic": true, "filterOption": false, "no_outside_label": true, "autoClearSearchValue": false, "url_name": "customers", "query_key": "info.customer_org_id", "showSingleOption": true, "showEmptyOption": true, "key": "customers", "is_outside_filter_drawer": true, "is_inside_filter_drawer": false, "filter_api": "customers", "api_pagination": true}, {"feature_key": "UserManagement:DeviceType", "showSearch": true, "sorted": false, "allowClear": true, "multiSelect": false, "label": "Device Type", "placeholder": "Device Type", "no_outside_label": true, "key": "device_type", "url_name": "device_type", "query_key": "info.type_id", "is_outside_filter_drawer": true, "is_inside_filter_drawer": false, "filter_api": "device_type", "dependent_filters": ["firmware_version"]}, {"feature_key": "UserManagement:FirmwareVersion", "showSearch": true, "sorted": false, "allowClear": true, "multiSelect": true, "group_options": true, "label": "Firmware Version", "placeholder": "Firmware Version", "no_outside_label": true, "key": "firmware_version", "url_name": "firmware_version", "query_key": "info.firmware_version_id", "is_outside_filter_drawer": true, "is_inside_filter_drawer": false, "optionLabelProp": "label", "showSingleOption": true, "filter_api": "firmware_version"}, {"optionData": [{"title": "Online", "value": "online"}, {"title": "Offline", "value": "offline"}], "feature_key": "UserManagement:DeviceConnectivity", "showSearch": false, "allowClear": false, "sorted": false, "label": "Connectivity Status", "placeholder": "Connectivity Status", "no_outside_label": true, "multiSelect": false, "key": "connectivity", "url_name": "connectivity", "query_key": "status.connectivity", "is_outside_filter_drawer": true, "is_inside_filter_drawer": false}, {"feature_key": "UserManagement:DeviceStatus", "optionData": [{"title": "Active", "value": "active"}, {"title": "Inactive", "value": "inactive"}], "showSearch": false, "allowClear": true, "sorted": false, "label": "Device Status", "placeholder": "Device Status", "no_outside_label": true, "key": "device_status", "url_name": "device_status", "query_key": "status.active", "is_outside_filter_drawer": true, "is_inside_filter_drawer": false}, {"type": "search", "feature_key": "DeviceManagement:Search", "placeholder": "Search Assets", "size": "default"}], "realtimeConfig": {"enabled": true, "key": "device_id"}, "table": [{"title": "Device List", "filters": [], "apiType": "devices", "colSpan": 24, "apiConfig": {}, "resizable": false, "downloadable": true, "additionalData": [{"dataIndex": "partner_id", "colPathExp": "info.partner_org_id"}, {"dataIndex": "assign", "colPathExp": "info.assignment"}], "tableProps": {"size": "middle", "columns": [{"key": "qr_code", "align": "center", "fixed": "left", "title": "QR Code", "width": 150, "sorter": true, "ellipsis": false, "dataIndex": "qr_code", "pdf_title": "QR Code", "colPathExp": "info.name", "api_sort_key": "info.name", "not_customizable": true, "render": "function(qr_code,row_value){return(<Highlighter searchWords={[table_search]} autoEscape={true} textToHighlight={qr_code} />)}"}, {"key": "device_type", "align": "center", "fixed": "left", "title": "Device Type", "width": 120, "sorter": true, "ellipsis": false, "dataIndex": "device_type", "pdf_title": "Device Type", "colPathExp": "info.type_name", "api_sort_key": "info.type_name", "not_customizable": true, "render": "function(device_type,row_value){return(<div style={{display:'flex',flexDirection:'column',alignItems:'center',justifyContent:'center',gap:'3px'}}><span>{device_type}</span>{(row_value.device_type_id===17||row_value.device_type_id===52)&&<AntTooltip title='Click for more info...'><div style={{background:'#7687a0',borderRadius:'5px',padding:'3px 8px',display:'inline-flex',alignItems:'center',cursor:'pointer',marginLeft:'6px'}} onClick={()=>triggerAction('3rd_party_connection',[row_value])}><img src=\"https://static.datoms.io/images/icons/device/device_pop_key.svg\" height={14} width={14} style={{marginRight:'5px'}}/><div style={{width:'50px',fontSize:'10px',overflow:'hidden',whiteSpace:'nowrap',textOverflow:'ellipsis',color:'#fff'}}>Connect</div></div></AntTooltip>}</div>);}"}, {"key": "online_status", "align": "center", "fixed": "left", "title": "Online Status", "width": 100, "ellipsis": false, "dataIndex": "online_status", "pdf_title": "Online Status", "colPathExp": "status.connectivity", "render": "function(online_status) {if(online_status ===\"online\") return <i style={{color: 'green'}}>Online</i>; return <i style={{color: 'red'}}>Offline</i>;}"}, {"key": "last_online_time", "align": "center", "fixed": "left", "title": "Last Online Time", "width": 120, "format": "DD-MM-YYYY, HH:mm", "sorter": true, "ellipsis": false, "dataIndex": "last_online_time", "pdf_title": "Last Online Time", "colPathExp": "time.last_online", "api_sort_key": "time.last_online", "render": "function(time){ if(time?.includes('1970')) return '-'; return time;}"}, {"key": "last_data_received", "align": "center", "fixed": "left", "title": "Last Data Received", "width": 120, "format": "DD-MM-YYYY, HH:mm", "sorter": true, "ellipsis": false, "dataIndex": "last_data_received", "pdf_title": "Last Data Received", "colPathExp": "time.last_data_receive", "api_sort_key": "time.last_data_receive", "render": "function(time){ if(time?.includes('1970')) return '-'; return time;}"}, {"key": "created_date", "align": "center", "fixed": "left", "title": "Created Date", "width": 120, "format": "DD-MM-YYYY, HH:mm", "sorter": true, "ellipsis": false, "dataIndex": "created_date", "pdf_title": "Created Date", "colPathExp": "time.addition", "api_sort_key": "time.addition", "render": "function(time){ if(time?.includes('1970')) return '-'; return time;}"}, {"key": "firmware", "align": "center", "title": "Firmware", "width": 100, "sorter": true, "ellipsis": false, "dataIndex": "firmware", "feature_key": "DeviceManagement:Firmware", "pdf_title": "Firmware", "colPathExp": "info.firmware_version", "api_sort_key": "info.firmware_version", "render": "function(firmware,row_value){return(<div style={{display:'flex',alignItems:'center',justifyContent:'center', gap: 3}}><span>{(firmware && firmware != '-')?firmware:''}</span><img style={{cursor:'pointer'}} src=\"https://static.datoms.io/images/icons/device/update.svg\" height={15} width={17} onClick={()=>triggerAction('firmware_update', [row_value])} /></div>);}"}, {"key": "assets", "align": "center", "fixed": "left", "title": "Assets", "width": 150, "sorter": true, "ellipsis": false, "dataIndex": "assets", "pdf_title": "Assets", "colPathExp": "assets", "api_sort_key": "assets", "render": "function(assets) { if (!assets || !Array.isArray(assets) || assets.length === 0) return '-'; if (assets.length === 1) return assets[0]; return <AntTooltip title={<div style={{ whiteSpace: 'pre-line' }}>{assets.slice(1).map((item, index) => <div key={index}>{item}</div>)}</div>}>{assets[0] + ' +' + (assets.length - 1) + ' more'}</AntTooltip>; }"}, {"key": "customer", "align": "center", "fixed": "left", "title": "Customer", "width": 150, "sorter": true, "ellipsis": false, "dataIndex": "customer", "pdf_title": "Customer", "colPathExp": "info.customer_org_name", "api_sort_key": "info.customer_org_name", "render": "function(customer,row_value){return(<Highlighter searchWords={[table_search]} autoEscape={true} textToHighlight={customer} />)}"}, {"key": "partner", "align": "center", "fixed": "center", "title": "Partner", "width": 150, "sorter": true, "ellipsis": false, "dataIndex": "partner", "pdf_title": "Partner", "colPathExp": "info.partner_org_name", "api_sort_key": "info.partner_org_name", "render": "function(partner,row_value){return(<Highlighter searchWords={[table_search]} autoEscape={true} textToHighlight={partner} />)}"}, {"key": "ownership_chain", "align": "center", "fixed": "left", "title": "Ownership", "width": 100, "ellipsis": false, "dataIndex": "ownership_chain", "pdf_title": "Ownership", "colPathExp": "info.ownership_chain", "downloadable": false, "render": "function(ownership_chain) { if(!ownership_chain?.length || ownership_chain === \"-\") return \"-\"; return <HeirarchyPopover icon={<i style={{cursor: 'pointer', color: 'blue'}}>View</i>} items={ownership_chain}/>;}"}, {"key": "network_status", "align": "center", "fixed": "left", "title": "Network Status", "width": 100, "sorter": true, "ellipsis": false, "dataIndex": "network_status", "pdf_title": "Network Status", "colPathExp": "diagnostics.debug.network_used", "api_sort_key": "diagnostics.debug.network_used", "render": "function(network_status,row_value){if(!network_status)return \"-\";const sim=1;let icon,tooltipText=\"\";if(network_status===\"gprs\"){if(sim)tooltipText+=`Sim ${sim}\\n`;if(row_value.online_status===\"offline\"){if(row_value.network_strength>=80){icon=<img src=\"https://static.datoms.io/images/icons/device/gprs-signal/gprs_signal_rounded_5.svg\" height={20} width={20}/>;tooltipText+=\"Strength: Excellent\"}else if(row_value.network_strength>=60){icon=<img src=\"https://static.datoms.io/images/icons/device/gprs-signal/gprs_signal_rounded_4.svg\" height={20} width={20}/>;tooltipText+=\"Strength: Good\"}else if(row_value.network_strength>=40){icon=<img src=\"https://static.datoms.io/images/icons/device/gprs-signal/gprs_signal_rounded_3.svg\" height={20} width={20}/>;tooltipText+=\"Strength: Poor\"}else if(row_value.network_strength>=20){icon=<img src=\"https://static.datoms.io/images/icons/device/gprs-signal/gprs_signal_rounded_2.svg\" height={20} width={20}/>;tooltipText+=\"Strength: Poor\"}else{icon=<img src=\"https://static.datoms.io/images/icons/device/gprs-signal/gprs_signal_rounded_1.svg\" height={20} width={20}/>;tooltipText+=\"No Signal\"}}else{tooltipText=\"Last Reported Strength: \"+(row_value.network_strength>=80?\"Excellent\":row_value.network_strength>=60?\"Good\":row_value.network_strength>=40?\"Poor\":row_value.network_strength>=20?\"Very Poor\":\"No Signal\")}}else if(network_status===\"wifi\"){if(row_value.online_status===\"online\"){if(row_value.network_strength>=70){icon=<img src=\"https://static.datoms.io/images/icons/device/wifi-signal/wifi_signal_rounded3.svg\" height={20} width={20}/>;tooltipText=\"Strength: Excellent\"}else if(row_value.network_strength>=45){icon=<img src=\"https://static.datoms.io/images/icons/device/wifi-signal/wifi_signal_rounded2.svg\" height={20} width={20}/>;tooltipText=\"Strength: Good\"}else if(row_value.network_strength>=20){icon=<img src=\"https://static.datoms.io/images/icons/device/wifi-signal/wifi_signal_rounded1.svg\" height={20} width={20}/>;tooltipText=\"Strength: Poor\"}else{icon=<img src=\"https://static.datoms.io/images/icons/device/wifi-signal/wifi_signal_rounded1.svg\" height={20} width={20}/>;tooltipText=\"No Signal\"}}else{tooltipText=\"Last Reported Strength: \"+(row_value.network_strength>=70?\"Excellent\":row_value.network_strength>=45?\"Good\":row_value.network_strength>=20?\"Poor\":\"No Signal\")}}else if(network_status===\"ethernet\"){icon=row_value.online_status===\"online\"?<img src=\"https://static.datoms.io/images/icons/device/ethernet_icon_active.svg\" height={20} width={20}/>:<img src=\"https://static.datoms.io/images/icons/device/ethernet_inactive.svg\" height={20} width={20}/>;tooltipText=row_value.online_status===\"online\"?\"Ethernet Active\":\"Ethernet Inactive\"}else{return \"-\";}return <div><AntTooltip title={tooltipText}>{icon}</AntTooltip></div>; }"}, {"key": "network_strength", "align": "center", "fixed": "left", "title": "Network Strength", "width": 100, "sorter": true, "ellipsis": false, "dataIndex": "network_strength", "pdf_title": "Network Strength", "colPathExp": "diagnostics.debug.signal_strength", "api_sort_key": "diagnostics.debug.signal_strength", "render": "function(network_strength, row_value) { if(!network_strength || network_strength === \"-\") return \"-\"; return <span style={{ fontSize: \"15px\" }}>{network_strength}%</span>;}"}, {"key": "power_status", "align": "center", "fixed": "left", "title": "Power Status", "width": 100, "sorter": true, "ellipsis": false, "dataIndex": "power_status", "pdf_title": "Power Status", "colPathExp": "diagnostics.debug.is_ac_present", "api_sort_key": "diagnostics.debug.is_ac_present", "render": "function (power_status) { if (power_status === true) { return (<div><img src=\"https://static.datoms.io/images/icons/device/power_on.svg\" height={22} width={22} /></div>); } return (<img src=\"https://static.datoms.io/images/icons/device/power_inactive.svg\" height={25} width={25} />); }"}, {"key": "battery_status", "align": "center", "fixed": "left", "title": "Battery Status", "width": 100, "sorter": true, "ellipsis": false, "dataIndex": "battery_status", "pdf_title": "Battery Status", "colPathExp": "diagnostics.debug.battery_status", "api_sort_key": "diagnostics.debug.battery_status", "render": "function(battery_status) { if (battery_status == null || battery_status == '-') return \"-\"; let icon, tooltipText = \"\"; if (battery_status > 80) { icon = <img src=\"https://static.datoms.io/images/icons/device/battery/battery_full.svg\" height={20} width={20} />; tooltipText = \"Battery: Full\"; } else if (battery_status > 60) { icon = <img src=\"https://static.datoms.io/images/icons/device/battery/battery_high.svg\" height={20} width={20} />; tooltipText = \"Battery: High\"; } else if (battery_status > 40) { icon = <img src=\"https://static.datoms.io/images/icons/device/battery/battery_medium.svg\" height={20} width={20} />; tooltipText = \"Battery: Medium\"; } else if (battery_status > 20) { icon = <img src=\"https://static.datoms.io/images/icons/device/battery/battery_low.svg\" height={20} width={20} />; tooltipText = \"Battery: Low\"; } else { icon = <img src=\"https://static.datoms.io/images/icons/device/battery/battery_critical.svg\" height={25} width={25} />; tooltipText = \"Battery: Critical\"; } return (<div style={{ display: \"flex\", alignItems: \"center\", flexDirection: \"row\", justifyContent: \"center\", gap: 5 }}><AntTooltip title={tooltipText}>{icon}</AntTooltip></div>); }"}, {"key": "sim_details", "align": "center", "fixed": "left", "title": "SIM Details", "width": 90, "sorter": true, "ellipsis": false, "dataIndex": "sim_details", "pdf_title": "SIM Details", "colPathExp": "sim_details", "api_sort_key": "sim_details", "downloadable": false, "render": "function(sim_details, row_value){return(<img style={{cursor:'pointer'}} src=\"https://static.datoms.io/images/icons/device/sim.svg\" height={25} width={25} onClick={()=>triggerAction('sim_add_edit', row_value)} />);}"}, {"key": "debug", "align": "center", "fixed": "left", "title": "Debug", "width": 90, "ellipsis": false, "dataIndex": "debug", "colPathExp": "info.type_id", "feature_key": "DeviceManagement:Debug", "downloadable": false, "render": "function(_, row_value) { return (<Link to={baseUrl + 'devices/assigned/' + row_value.device_id + '/device-debug?customer_id=' + row_value.customer_id + '&application_id=16&device_id=' + row_value.device_id + '&device_qr_code=' + row_value.qr_code + '&type_name=' + row_value.device_type + '&tab=offline_trend'} target='_blank'><img src='https://static.datoms.io/images/icons/device/debug.svg' height={28} width={32} alt='debug-icon'/></Link>); }", "additionalData": [{"dataIndex": "device_id", "colPathExp": "info.id"}, {"dataIndex": "customer_id", "colPathExp": "info.customer_org_id"}]}, {"key": "raw_log", "align": "center", "fixed": "left", "title": "Raw Log", "width": 90, "ellipsis": false, "dataIndex": "raw_log", "colPathExp": "info.type_id", "feature_key": "DeviceManagement:RawLog", "downloadable": false, "render": "function(_, row_value) { return ( <Link to={baseUrl + 'devices/assigned/' + row_value.device_id + '/device-debug?customer_id=' + row_value.customer_id + '&application_id=16&device_id=' + row_value.device_id + '&device_qr_code=' + row_value.qr_code + '&type_name=' + row_value.device_type + '&tab=raw_log'} target='_blank'><img src=\"https://static.datoms.io/images/icons/device/raw_log_icon.svg\" height={28} width={32}/></Link> ); }", "additionalData": [{"dataIndex": "device_id", "colPathExp": "info.id"}, {"dataIndex": "customer_id", "colPathExp": "info.customer_org_id"}]}, {"key": "configure", "align": "center", "fixed": "left", "title": "Configure", "width": 90, "ellipsis": false, "dataIndex": "configure", "colPathExp": "info.type_id", "feature_key": "DeviceManagement:Configure", "downloadable": false, "render": "function(configure, row_value) { if (configure !== 11 && configure !== 12 && configure !== 51) return \"-\"; return ( <Link to={baseUrl + 'devices/assigned/' + row_value.device_id + '/communication?type_id=' + row_value.device_type_id} target='_blank'><img src=\"https://static.datoms.io/images/icons/device/configuration.svg\" height={28} width={32}/></Link> ); }", "additionalData": [{"dataIndex": "device_id", "colPathExp": "info.id"}, {"dataIndex": "device_type_id", "colPathExp": "info.type_id"}]}, {"key": "custom_command", "align": "center", "fixed": "left", "title": "Custom Command", "width": 90, "ellipsis": false, "dataIndex": "custom_command", "colPathExp": "info.type_id", "feature_key": "DeviceManagement:CustomCommand", "downloadable": false, "render": "function(_, row_value) { return ( <Link to={baseUrl + 'devices/assigned/' + row_value.device_id + '/custom-command?type_id=' + row_value.device_type_id} target='_blank'><img src=\"https://static.datoms.io/images/icons/device/custom_command.svg\" height={28} width={32}/></Link> ); }", "additionalData": [{"dataIndex": "device_id", "colPathExp": "info.id"}, {"dataIndex": "device_type_id", "colPathExp": "info.type_id"}]}, {"key": "device_status", "align": "center", "fixed": "left", "title": "Device Status", "width": 120, "ellipsis": false, "dataIndex": "device_status", "colPathExp": "status.active", "feature_key": "DeviceManagement:DeviceStatus", "render": "function(status_code, row_value) { return <Switch size='medium' checkedChildren='Active' unCheckedChildren='Inactive' checked={status_code == 'active' ? true : false} onChange={() => triggerAction('update_device_status',[row_value])} ></Switch>;}"}], "expandable": false, "pagination": {"pageSize": 50, "hideOnSinglePage": false}, "rowSelection": {"type": "checkbox"}}, "preferenceKeys": ["listing_page", "device_list"], "bulkRowActionButtons": [{"key": "unassign", "feature_key": "DeviceManagement:Assign", "render": "function(selected_rows){const stateText=selected_rows?.[0]?.assign==='assigned'?'Un-assign':'Assign to Customer';return(<div style={{display:'flex',alignItems:'center',fontSize:'13px',cursor:'pointer',color:'#7686a1'}} onClick={()=>triggerAction('assign_device',selected_rows)}><img src=\"https://static.datoms.io/images/icons/device/unassign.svg\" style={{marginRight:5}} height={18} width={22}/><span>{stateText}</span></div>);}", "disabled": false}, {"key": "update_status", "feature_key": "DeviceManagement:DeviceDeactivate", "render": "function(selected_rows){let statusMenu=(<AntMenu><AntMenuItem onClick={()=>triggerAction('unblock_devices',selected_rows)} key='unblock'>Unblock</AntMenuItem><AntMenuItem onClick={()=>triggerAction('block_devices',selected_rows)} key='block'>Block</AntMenuItem></AntMenu>);return(<div style={{display:'flex',alignItems:'center',fontSize:'13px',cursor:'pointer',color:'#1818c9'}}><AntDropdown overlay={statusMenu} trigger={['click']} placement='bottomCenter'><div style={{color:'#7686a1'}}><img src=\"https://static.datoms.io/images/icons/device/update_status.svg\" height={18} width={22} style={{marginRight:'5px'}}/><span>Update Status</span><DownOutlined style={{marginLeft:'5px'}}/></div></AntDropdown></div>)}", "disabled": false}, {"key": "firmware_update", "feature_key": "DeviceManagement:OTA", "render": "function(selected_rows){const t=selected_rows.every(e=>e.device_type_id===selected_rows[0]?.device_type_id);return t?(<div style={{display:'flex',alignItems:'center',fontSize:'13px',cursor:'pointer',color:'#7686a1'}} onClick={()=>triggerAction('firmware_update',selected_rows)}><img src=\"https://static.datoms.io/images/icons/device/update_firmware.svg\" style={{marginRight:5}}/><span>Update Firmware</span></div>):null;}", "disabled": false}], "defaultFixedCount": 2, "searchableColumns": [], "dataPathExp": "data", "totalCountPathExp": "pagination.total", "disableCustomization": false, "api_pagination": true}], "actions": [{"key": "menu", "component": "CustomUiButton", "position": "right", "component_props": {"menuItems": [{"key": "add_device", "title": "Add <PERSON>"}, {"key": "add_bulk_device", "title": "Bulk Add"}]}}], "page_url": "devices/list", "title": "Device List"}