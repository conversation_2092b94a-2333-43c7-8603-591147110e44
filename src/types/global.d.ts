declare module "@datoms/js-sdk";
declare module "lodash/find";
declare module "lodash/intersection";
declare module '*.svg';
declare module '*.png';
declare module '*.jpg';
declare module '*.jpeg';  
declare module "react-router-dom/HashRouter" {
  import { HashRouter } from "react-router-dom";
  export default HashRouter;
}

interface ImportMetaEnv {
  VITE_MOBILE: boolean;
  VITE_DESKTOP: boolean;
  VITE_BUILD_MODE: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

declare global {
  interface Window {
    mixpanel: any;
  }
}
