export interface ThingListResponseProps {
  status: string;
  message?: string;
  things?: ThingProps[];
}

export interface GlobalContextProps {
  client_id: number;
  client_name: string;
  enabled_features: any;
  plan_description: {};
  app_name: string;
  t: any;
  availableThingCats: any;
  currentUserPreferences: any;
  setCurrentUserPreferences: any;
  unauthorised_access: boolean;
  unauthorised_access_msg: string;
  error_api_msg: string;
  user_preferences: any;
  user_id: number | string;
  application_id: number;
  is_white_label: boolean;
  template_id: number | string;
  client_logo: string;
  vendor_logo: string;
  vendor_id: number | string;
  user_name: string;
  head_side_object_data: any;
  customer_type: number[];
  isRentalStore: boolean;
  logged_in_user_client_id: number | string;
  application_name: string;
  vendor_name: string;
  logged_in_user_name: string;
  logged_in_user_role_type: string;
  is_calibration_supported: boolean;
  things_list: any;
  is_iot_enabled: boolean;
  isGenericApp: boolean;
  isAurassure: boolean;
  menuUrlArray: any;
  setMenuUrl: any;
  getUserAccess: any;
  getViewAccess: any;
  getRemoteAccess: any;
  getRemoteLockAccess: any;
  checkEndCustomer: any;
  bannerToBeShown: any;
  onCollapseSider: any;
  viewDataAccess: any;
  supportedViewsPage: any;
  setStoreLogoutFunc: any;
  isLumenEnergy: boolean;
  isTataMotors: boolean;
  customizations: any;
  page_type: string;
  getExtraHeaderComps: any;
  onmenuClick: any;
  store_logout: any;
  setStoreLogout: any;
  mobileAppPackageName: string;
  showPolicyPage: any;
  collapsed: boolean;
  setCollapsed: (collapsed: boolean) => void;
  collapse: boolean;
  setCollapse: (collapse: boolean) => void;
  setClientId: (client_id: number) => void;
  setClientName: (client_name: string) => void;
  alertObjectData: any;
  setAlertObjectData: (alertObjectData: any) => void;
  setPageType: (page_type: string) => void;
  setEnabledFeatures: (enabled_features: any) => void;
  setPlanDescription: (plan_description: any) => void;
  setAppName: (app_name: string) => void;
  setSelectThingModal: (select_thing_modal: boolean) => void;
  setAvailableThingCats: (availableThingCats: any) => void;
  setMobileAppPackageName: (mobileAppPackageName: string) => void;
  setUnauthorisedAccess: (unauthorised_access: boolean) => void;
  setUnauthorisedAccessMsg: (unauthorised_access_msg: string) => void;
  setErrorAPI: (error_api: boolean) => void;
  setErrorAPIMsg: (error_api_msg: string) => void;
  setLoadedData: (loadedData: boolean) => void;
  setStructureStyle: (structure_style: any) => void;
  setIsMobilePageName: (is_mobile_page_name: string) => void;
  setUserPreferences: (user_preferences: any) => void;
  setUserId: (user_id: number | string) => void;
  setApplicationId: (application_id: number) => void;
  setIsWhiteLabel: (is_white_label: boolean) => void;
  setIsFixedLogoForWhitelabel: (isFixedLogoForWhitelabel: boolean) => void;
  setCustomizations: (customizations: any) => void;
  setLoadingLogo: (loading_logo: boolean) => void;
  setTemplateId: (template_id: number | string) => void;
  setClientLogo: (client_logo: string) => void;
  setVendorLogo: (vendor_logo: string) => void;
  setVendorId: (vendor_id: number | string) => void;
  setUserName: (user_name: string) => void;
  setGetExtraHeaderComps: (getExtraHeaderComps: any) => void;
  setHeadSideObjectData: (head_side_object_data: any) => void;
  setHomePage: (home_page: string) => void;
  setCustomerType: (customer_type: number[]) => void;
  setIsRentalStore: (isRentalStore: boolean) => void;
  setLoggedInUserClientId: (logged_in_user_client_id: number | string) => void;
  setDeskTopLogout: (deskTopLogout: any) => void;
  setApplicationName: (application_name: string) => void;
  setCustomerAccess: (customer_access: string) => void;
  setVendorName: (vendor_name: string) => void;
  setLoggedInUserName: (logged_in_user_name: string) => void;
  setLoggedInUserRoleType: (logged_in_user_role_type: string) => void;
  setIsLumenEnergy: (isLumenEnergy: boolean) => void;
  setIsTataMotors: (isTataMotors: boolean) => void;
  setIs_calibration_supported: (is_calibration_supported: boolean) => void;
  setThingsList: (things_list: any) => void;
  setIsIotEnabled: (is_iot_enabled: boolean) => void;
  setIsGenericApp: (isGenericApp: boolean) => void;
  setIsAurassure: (isAurassure: boolean) => void;
  setMenuUrlArray: (menuUrlArray: any) => void;
  menuUrl: string;
  mapThingsList: any;
  getPackageName: any;
}
export interface ResponseProps {
  is_white_label: boolean;
  customer_type: number[];
  status: number | string;
  message: string;
  policy_acceptance_required: boolean;
  application_id: number;
  client_name: string;
  application_name: string;
  logged_in_user: number;
  access_list: string;
  features: string[];
  client_logo: string;
  vendor_logo: string;
  vendor_id: number;
  logged_in_user_name: string;
  logged_in_user_role_type: string;
  logged_in_user_client_id: string;
  logged_in_user_email: string;
  logged_in_user_vendor_id: string;
  vendor_name: string;
  template_id: number;
  customizations: object;
  user_preferences: UserPreferencesProps;
  client_id: number;
  parent_vendor_id: number;
  account_type: string;
}

export interface UserPreferencesProps {
  timezone: string;
  language: string;
}

export interface Customizations {
  favicon?: string;
  portal_name?: string;
}

export interface PlanDescription {
  remote_lock: boolean;
  remote_control: boolean;
}

export interface ThingProps {
  category: number;
  is_iot_enabled: boolean;
}

export interface GlobalStoreFunctionsProps {
  things_list: ThingListResponseProps;
  setMobileAppPackageName: (pkg: string) => void;
  setIsRentalStore: (val: boolean) => void;
  customer_access: string;
  plan_description: PlanDescription;
  enabled_features: string[];
  client_id: number;
  logged_in_user_client_id: string | number;
  isAurassure: boolean;
  application_id: number;
  isGenericApp: boolean;
  collapsed: boolean;
  setCollapsed: (collapsed: boolean) => void;
  customer_type: number[];
  is_iot_enabled: boolean;
  availableThingCats: string[];
  logged_in_user_role_type: string;
  setStoreLogout: (logout: boolean) => void;
}

export interface FinalPlanResponseProps {
  plan_details: {
    reports: boolean;
    dashboard: boolean;
    detailed_view: boolean;
    realtime: boolean;
    panel_view: boolean;
    alert_management: boolean;
    user_management: boolean;
    thing_management: boolean;
    remote_control: boolean;
    remote_lock: boolean;
    remote_calibration: boolean;
    pre_defined_role_type_ids: number[] | undefined;
  };
  features: string;
  plan_customizable: number;
}
