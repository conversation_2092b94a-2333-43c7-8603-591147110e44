import _intersection from "lodash/intersection";
import _find from "lodash/find";
import {
  FinalPlanResponseProps,
  GlobalStoreFunctionsProps,
  ThingListResponseProps,
} from "./types";
import { getMenuUrlArray } from "../routes/routeHandler";

export const useGlobalStoreFunctions = (props: GlobalStoreFunctionsProps) => {
  const {
    things_list,
    setMobileAppPackageName,
    setIsRentalStore,
    customer_access,
    plan_description,
    enabled_features,
    client_id,
    logged_in_user_client_id,
    isAurassure,
    application_id,
    isGenericApp,
    collapsed,
    setCollapsed,
    customer_type,
    is_iot_enabled,
    availableThingCats,
    logged_in_user_role_type,
    client_logo,
    vendor_logo,
    client_name,
    setStoreLogout,
    setHeadSideObjectData,
    setAppName,
    setMenuUrlArray,
    parent_vendor_id,
  } = props;

  const mapThingsList = (things_list: ThingListResponseProps) => {
    let is_iot_enabled = false,
      availableThingCats: number[] = [];
    if (things_list && things_list.things && things_list.things.length) {
      things_list.things.map((thing) => {
        if (thing.is_iot_enabled) {
          is_iot_enabled = thing.is_iot_enabled;
        }
        if (!availableThingCats.includes(thing.category)) {
          availableThingCats.push(thing.category);
        }
      });
    }
    return { is_iot_enabled, availableThingCats };
  };

  const getPackageName = (e: { data: { type: string; name: string } }) => {
    if (e.data.type === "package") {
      window.localStorage.setItem("package_name", e.data.name);
      setMobileAppPackageName(e.data.name);
      setIsRentalStore(e.data.name == "com.yoyorydes");
    }
  };

  const getViewAccess = (
    access_key_arr: string | any[],
    checkFeatures: boolean,
  ) => {
    if (access_key_arr && access_key_arr.length) {
      const isFeaturePresent =
        checkFeatures && application_id === 17
          ? _intersection(enabled_features, access_key_arr).length
            ? true
            : false
          : true;
      if (customer_access) {
        if (customer_access === "*") {
          return isFeaturePresent;
        } else if (customer_access.length) {
          let found_key = _intersection(customer_access, access_key_arr);
          if (found_key.length) {
            return isFeaturePresent;
          }
          return false;
        }
      }
    }
  };

  const getRemoteAccess = () => {
    if (plan_description.remote_control) {
      return true;
    } else if (enabled_features.includes("RemoteControl:RemoteControl")) {
      return true;
    } else {
      return false;
    }
  };

  const getRemoteLockAccess = () => {
    if (plan_description.remote_lock) {
      return true;
    } else if (enabled_features.includes("RemoteLock:RemoteLock")) {
      return true;
    } else {
      return false;
    }
  };

  const checkEndCustomer = () => {
    if (parseInt(client_id) === parseInt(logged_in_user_client_id)) {
      return true;
    } else {
      return false;
    }
  };

  const bannerToBeShown = () => {
    return (
      !isAurassure &&
      parseInt(application_id) !== 17 &&
      isGenericApp &&
      _find(things_list?.things, (o: { category: number }) => {
        return o.category === 21 || o.category === 22 || o.category === 102 || o.category === 23;
      })
    );
  };

  const getFaviconEl = () => {
    return document.getElementById("favicon");
  };

  const getPageTitleEl = () => {
    return document.getElementById("page_title");
  };

  const handleGenericIcon = (iconLink: string) => {
    try {
      const favicon = getFaviconEl();
      if (favicon) {
        (favicon as HTMLLinkElement).href = iconLink;
      }
    } catch (e) {}
  };

  const handleGenericPageTitle = (title: string) => {
    try {
      const pageTitle = getPageTitleEl();
      if (pageTitle) {
        pageTitle.innerHTML = title;
      }
    } catch (e) {}
  };

  const onCollapseSider = () => {
    setCollapsed(!collapsed);
  };

  const viewDataAccess = () => {
    if (enabled_features?.length) {
      if (customer_type?.length) {
        if (customer_type.length === 1 && customer_type.includes(4)) {
          return (
            is_iot_enabled &&
            enabled_features.includes("AccessThingData:AccessThingData")
          );
        } else {
          return enabled_features.includes("AccessThingData:AccessThingData");
        }
      } else {
        return enabled_features.includes("AccessThingData:AccessThingData");
      }
    } else {
      return false;
    }
  };

  const supportedViewsPage = (page_name: string, arr1 = availableThingCats) => {
    if (parseInt(logged_in_user_role_type) === 10) return false;
    return true;
  };

  const setStoreLogoutFunc = () => {
    setStoreLogout(true);
    window.location.hash = "/";
  };

  const getUrl = (page_name: string) => {
    return logged_in_user_client_id === client_id
      ? "/" + page_name
      : "/dg-monitoring/" + page_name;
  };

  const getLayoutSideMenu = (sideMenuOptions) => {
    let sideMenu = {},
      menuOptions = [],
      header = {};
    let app_name = "dg_monitoring";
    let app_name_1 = "dg-monitoring";
    if (client_id === 1) {
      app_name = "datoms_x";
      app_name_1 = "datoms-x";
      sideMenuOptions?.forEach((menu) => {
        if (menu?.submenu_items?.length) {
          menu.submenu_items.forEach((submenu) => {
            submenu.key = submenu.url;
            submenu.url = "/datoms-x/" + submenu.url;
          });
        }
        menuOptions.push({
          alternativeKeys: menu?.alternativeKeys || [],
          translate_key: menu.translate_key,
          page_name: menu.name,
          icon_type: menu.icon_type,
          key: menu.url === "" ? menu.name.toLowerCase() : menu.url,
          url:
            "/datoms-x/" +
            (menu.url === "" ? menu.name.toLowerCase() : menu.url),
          submenu_items: menu.submenu_items || [],
        });
      });

      header = {
        logo: client_logo,
        vendor_logo: vendor_logo || "",
        company_name: client_name || "Phoenix Robotix",
        page_name: "",
        change_pass_link: "/accounts/change-password",
        logout: "/accounts/logout",
        bg_color: "#fff",
        user_name: "",
        selected_menu_color: "#fff",
        menu_items: [],
      };

      sideMenu = {
        collapsible: true,
        non_collapse_logo:
          "https://prstatic.phoenixrobotix.com/imgs/cold_storage_monitoring/datoms_logo.svg",
        collapse_logo: "https://static.datoms.io/images/d_logo.svg",
        menu_items: [],
        bg_color: "#001529",
        selected_menu_color: "#fff",
      };
    } else if (customer_type.includes(5)) {
      if (parent_vendor_id === 1819) {
        const isMap = sideMenuOptions.some((menu) => menu.url === "map-view");
        {
          if (!isMap) {
            sideMenuOptions.unshift({
              url: "generic/map-view",
              name: "Map View",
              key: "map-view",
              icon_type: "environment",
              translate_key: "map_view",
              submenu_items: [],
            });
          }
        }
      }
      sideMenuOptions?.forEach((menu) => {
        if (menu?.submenu_items?.length) {
          menu.submenu_items.forEach((submenu) => {
            submenu.key = submenu.url;
            submenu.url = getUrl(menu.url);
          });
        }
        menuOptions.push({
          translate_key: menu.translate_key,
          page_name: menu.name,
          icon_type: menu.icon_type,
          key: menu.url === "" ? menu.name.toLowerCase() : menu.url,
          url: getUrl(menu.url === "" ? menu.name.toLowerCase() : menu.url),
          submenu_items: menu.submenu_items || [],
        });
      });

      header = {
        logo: client_logo,
        vendor_logo: vendor_logo || "",
        notification_enabled: true,
        notification_drop: [],
        company_name: client_name || "Phoenix Robotix",
        page_name: "",
        change_pass_link: "/accounts/change-password",
        logout: "/accounts/logout",
        bg_color: "#fff",
        selected_menu_color: "#fff",
      };

      sideMenu = {
        collapsible: true,
        non_collapse_logo:
          "https://prstatic.phoenixrobotix.com/imgs/cold_storage_monitoring/datoms_logo.svg",
        collapse_logo: "https://static.datoms.io/images/d_logo.svg",
        menu_items: [],
        bg_color: "#001529",
        selected_menu_color: "#fff",
      };
    } else {
      app_name = "iot_platform";
      app_name_1 = "iot-platform";
      sideMenuOptions?.forEach((menu) => {
        if (menu?.submenu_items?.length) {
          menu.submenu_items.forEach((submenu) => {
            submenu.key = submenu.url;
            submenu.url = "/iot-platform/" + submenu.url;
          });
        }
        menuOptions.push({
          alternativeKeys: menu?.alternativeKeys || [],
          translate_key: menu.translate_key,
          page_name: menu.name,
          icon_type: menu.icon_type,
          key: menu.url === "" ? menu.name.toLowerCase() + "_1" : menu.url,
          url:
            "/iot-platform/" +
            (menu.url === "" ? menu.name.toLowerCase() : menu.url),
          submenu_items: menu.submenu_items || [],
        });
      });

      header = {
        logo: client_logo,
        vendor_logo: vendor_logo || "",
        company_name: client_name || "Phoenix Robotix",
        page_name: "",
        change_pass_link: "/accounts/change-password",
        logout: "/accounts/logout",
        bg_color: "#fff",
        user_name: "",
        selected_menu_color: "#fff",
      };

      sideMenu = {
        collapsible: true,
        non_collapse_logo:
          "https://prstatic.phoenixrobotix.com/imgs/cold_storage_monitoring/datoms_logo.svg",
        collapse_logo: "https://static.datoms.io/images/d_logo.svg",
        menu_items: [],
        bg_color: "#001529",
        selected_menu_color: "#fff",
      };
    }

    sideMenu.menu_items = menuOptions;

    setHeadSideObjectData({
      header_component: {
        sider: sideMenu,
        mobile_menu: menuOptions,
        header: header,
      },
    });
    setMenuUrlArray(
      getMenuUrlArray({
        header_component: {
          sider: sideMenu,
          mobile_menu: menuOptions,
          header: header,
        },
      }),
    );
    setAppName(app_name_1);
  };

  return {
    mapThingsList,
    getPackageName,
    getViewAccess,
    getRemoteAccess,
    getRemoteLockAccess,
    checkEndCustomer,
    bannerToBeShown,
    handleGenericIcon,
    handleGenericPageTitle,
    onCollapseSider,
    viewDataAccess,
    supportedViewsPage,
    setStoreLogoutFunc,
    getLayoutSideMenu,
  };
};

export const getFinalPlanDetails = (response: FinalPlanResponseProps) => {
  const finalPlan = response.plan_details;
  const enabled_features = response.features;
  if (finalPlan && enabled_features?.length) {
    if (response.plan_customizable === 1) {
      if (!enabled_features.includes("Reports:Customized")) {
        finalPlan.reports = false;
      }
      if (!enabled_features.includes("AccessData:Dashboard")) {
        finalPlan.dashboard = false;
      }
      if (!enabled_features.includes("AccessData:DetailedView")) {
        finalPlan.detailed_view = false;
      }
      if (!enabled_features.includes("AccessData:RealtimeView")) {
        finalPlan.realtime = false;
      }
      if (!enabled_features.includes("AccessData:PanelView")) {
        finalPlan.panel_view = false;
      }
      if (!enabled_features.includes("AlertManagement:AlertManagement")) {
        finalPlan.alert_management = false;
      }
      if (!enabled_features.includes("UserManagement:UserManagement")) {
        finalPlan.user_management = false;
      }
      if (!enabled_features.includes("ThingManagement:ThingManagement")) {
        finalPlan.thing_management = false;
      }
      if (!enabled_features.includes("RemoteControl:RemoteControl")) {
        finalPlan.remote_control = false;
      }
      if (!enabled_features.includes("RemoteLock:RemoteLock")) {
        finalPlan.remote_lock = false;
      }
      if (!enabled_features.includes("RemoteLock:RemoteLock")) {
        finalPlan.remote_lock = false;
      }
      if (!enabled_features.includes("Workflow:RemoteCalibration")) {
        finalPlan.remote_calibration = false;
      }
    }
    // if (finalPlan.pre_defined_role_type_ids?.length) {
    //   if (!enabled_features.includes("FuelManagement:FuelManagement")) {
    //     finalPlan.pre_defined_role_type_ids =
    //       finalPlan.pre_defined_role_type_ids.filter((id) => id !== 6);
    //   }
    //   if (!enabled_features.includes("UserManagement:Tracking")) {
    //     finalPlan.pre_defined_role_type_ids =
    //       finalPlan.pre_defined_role_type_ids.filter((id) => id !== 5);
    //   }
    // }
  }
  return finalPlan;
};

export const getDefaultColor = (color: string) => {
  document.documentElement.style.setProperty("--defaultColor", color);
  document.documentElement.style.setProperty("--hoverColor", color + 29);
};
