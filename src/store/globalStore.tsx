import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";
import AlertObjectData from "../js/configuration/AddAlertObjectData";
import _find from "lodash/find";
import _intersection from "lodash/intersection";
import DatomsIcon from "../images/CommonFiles/favicon.ico";
import GenericIcon from "../images/CommonFiles/Icon Android32.png";
import moment from "moment-timezone";
import { storeMenuUpdate } from "../routes/StoreRoutes/menuUpdate";
import { mixPanelRegisterUser, mixPanelStartSession, mixPanelEndSession } from "@datoms/js-utils/src/mix-panel";
import {
  getUserOptions,
  getOptionsData,
  getUserOptionsSingleApp,
} from "@datoms/js-sdk";
import { GlobalConfig } from "../GlobalConfig";
import {
  useGlobalStoreFunctions,
  getFinalPlanDetails,
  getDefaultColor,
} from "./logic";
import {
  Customizations,
  GlobalContextProps,
  OptionsApiResponse,
  PlanDescription,
  ResponseProps,
  ThingListResponseProps,
  UserPreferencesProps,
} from "./types";
import useLanguage from "./useLanguage";

export const GlobalContext = createContext<GlobalContextProps>({
  collapsed: true,
  client_id: 0,
  client_name: "",
  enabled_features: [],
  plan_description: {},
  app_name: "dg-monitoring",
  t: {},
  availableThingCats: [],
  currentUserPreferences: {},
  setCurrentUserPreferences: () => {},
  unauthorised_access: false,
  unauthorised_access_msg: "",
  error_api_msg: "",
  user_preferences: {},
  user_id: "",
  application_id: 0,
  is_white_label: false,
  template_id: 0,
  client_logo: "",
  vendor_logo: "",
  vendor_id: 0,
  user_name: "",
  head_side_object_data: {},
  customer_type: [],
  isRentalStore: false,
  logged_in_user_client_id: "",
  application_name: "",
  vendor_name: "",
  logged_in_user_name: "",
  logged_in_user_role_type: "",
  is_calibration_supported: false,
  things_list: {},
  is_iot_enabled: false,
  isGenericApp: false,
  isAurassure: false,
  menuUrlArray: {},
  setMenuUrl: () => {},
  getUserAccess: () => {},
  getViewAccess: () => {},
  getRemoteAccess: () => {},
  getRemoteLockAccess: () => {},
  checkEndCustomer: () => {},
  bannerToBeShown: () => {},
  onCollapseSider: () => {},
  viewDataAccess: () => {},
  supportedViewsPage: () => {},
  setStoreLogoutFunc: () => {},
  isLumenEnergy: false,
  isTataMotors: false,
  customizations: {},
  page_type: "device",
  getExtraHeaderComps: {},
  onmenuClick: () => {},
  store_logout: false,
  setStoreLogout: () => {},
  mobileAppPackageName: "",
  showPolicyPage: false,
  setCollapsed: () => {},
  collapse: false,
  setCollapse: () => {},
  setClientId: () => {},
  setClientName: () => {},
  alertObjectData: undefined,
  setAlertObjectData: () => {},
  setPageType: () => {},
  setEnabledFeatures: () => {},
  setPlanDescription: () => {},
  setAppName: () => {},
  setSelectThingModal: () => {},
  setAvailableThingCats: () => {},
  setMobileAppPackageName: () => {},
  setUnauthorisedAccess: () => {},
  setUnauthorisedAccessMsg: () => {},
  setErrorAPI: () => {},
  setErrorAPIMsg: () => {},
  setLoadedData: () => {},
  setStructureStyle: () => {},
  setIsMobilePageName: () => {},
  setUserPreferences: () => {},
  setUserId: () => {},
  setApplicationId: () => {},
  setIsWhiteLabel: () => {},
  setIsFixedLogoForWhitelabel: () => {},
  setCustomizations: () => {},
  setLoadingLogo: () => {},
  setTemplateId: () => {},
  setClientLogo: () => {},
  setVendorLogo: () => {},
  setVendorId: () => {},
  setUserName: () => {},
  setGetExtraHeaderComps: () => {},
  setHeadSideObjectData: () => {},
  setHomePage: () => {},
  setCustomerType: () => {},
  setIsRentalStore: () => {},
  setLoggedInUserClientId: () => {},
  setDeskTopLogout: () => {},
  setApplicationName: () => {},
  setCustomerAccess: () => {},
  setVendorName: () => {},
  setLoggedInUserName: () => {},
  setLoggedInUserRoleType: () => {},
  setIsLumenEnergy: () => {},
  setIsTataMotors: () => {},
  setIs_calibration_supported: () => {},
  setThingsList: () => {},
  setIsIotEnabled: () => {},
  setIsGenericApp: () => {},
  setIsAurassure: () => {},
  setMenuUrlArray: () => {},
  menuUrl: "",
  mapThingsList: undefined,
  getPackageName: undefined,
});

const allApps = [
  "dg-monitoring",
  "pollution-monitoring",
  "delivery-tracking",
  "environment-monitoring",
  "iot-platform",
  "datoms-x",
];

let client_slug: string | null = "1";
let application_slug = "";
let appName = "";
let response: ResponseProps = {
  status: "",
  message: "",
  policy_acceptance_required: false,
  application_id: 0,
  client_name: "",
  application_name: "",
  logged_in_user: 0,
  access_list: "",
  features: [],
  client_logo: "",
  vendor_logo: "",
  vendor_id: 0,
  logged_in_user_name: "",
  logged_in_user_role_type: "",
  logged_in_user_client_id: "",
  logged_in_user_email: "",
  logged_in_user_vendor_id: "",
  vendor_name: "",
  template_id: 0,
  customizations: {},
  user_preferences: {
    timezone: "",
    language: "",
  },
  client_id: 0,
  is_white_label: false,
  customer_type: [],
  parent_vendor_id: 0,
};
let newOptionsResponse: OptionsApiResponse = {
  status: "",
  person: {},
  organization: {},
  vendor: {},
  application: {},
  features: [],
};

export const GlobalContextProvider: React.FC<{
  children: ReactNode
}> = ({ children }) => {
  const [collapsed, setCollapsed] = useState<boolean>(true);
  const [collapse, setCollapse] = useState<boolean>(true);
  const [client_id, setClientId] = useState<number>(0);
  const [client_name, setClientName] = useState<string>("");
  const [alertObjectData, setAlertObjectData] = useState(AlertObjectData);
  const [page_type, setPageType] = useState<string>("device");
  const [enabled_features, setEnabledFeatures] = useState<string[]>([]);
  const [plan_description, setPlanDescription] = useState<PlanDescription>({
    remote_lock: false,
    remote_control: false,
  });
  const [app_name, setAppName] = useState<string>("dg-monitoring");
  const [select_thing_modal, setSelectThingModal] = useState<boolean>(false);
  const [availableThingCats, setAvailableThingCats] = useState<number[]>([]);
  const [currentUserPreferences, setCurrentUserPreferences] = useState<object>(
    {},
  );
  const [mobileAppPackageName, setMobileAppPackageName] = useState<string>("");
  const [unauthorised_access, setUnauthorisedAccess] = useState<boolean>(false);
  const [unauthorised_access_msg, setUnauthorisedAccessMsg] =
    useState<string>("");
  const [error_api, setErrorAPI] = useState<boolean>(false);
  const [error_api_msg, setErrorAPIMsg] = useState<string>("");
  const [loadedData, setLoadedData] = useState<boolean>(false);
  const [showPolicyPage, setShowPolicyPage] = useState<boolean>(false);
  const [structure_style, setStructureStyle] = useState<object>({});
  const [is_mobile_page_name, setIsMobilePageName] = useState<string>("");
  const [user_preferences, setUserPreferences] = useState<UserPreferencesProps>(
    {
      timezone: "",
      language: "",
    },
  );
  const [user_id, setUserId] = useState<number>(0);
  const [is_white_label, setIsWhiteLabel] = useState<boolean>(false);
  const [isFixedLogoForWhitelabel, setIsFixedLogoForWhitelabel] =
    useState<boolean>(false);
  const [customizations, setCustomizations] = useState<Customizations>({});
  const [loading_logo, setLoadingLogo] = useState<boolean>(false);
  const [application_id, setApplicationId] = useState<number>(0);
  const [template_id, setTemplateId] = useState<number>(0);
  const [client_logo, setClientLogo] = useState<string>("");
  const [vendor_logo, setVendorLogo] = useState<string>("");
  const [vendor_id, setVendorId] = useState<string | number>("");
  const [user_name, setUserName] = useState<string>("");
  const [getExtraHeaderComps, setGetExtraHeaderComps] = useState<object>({});
  const [head_side_object_data, setHeadSideObjectData] = useState<
    object | undefined
  >(undefined);
  const [home_page, setHomePage] = useState<string>("");
  const [customer_type, setCustomerType] = useState<number[]>([]);
  const [isRentalStore, setIsRentalStore] = useState<boolean>(false);
  const [store_logout, setStoreLogout] = useState<boolean>(false);
  const [logged_in_user_client_id, setLoggedInUserClientId] =
    useState<string>("");
  const [deskTopLogout, setDeskTopLogout] = useState<object>({});
  const [application_name, setApplicationName] = useState<string>("");
  const [customer_access, setCustomerAccess] = useState<string>("");
  const [vendor_name, setVendorName] = useState<string>("");
  const [logged_in_user_name, setLoggedInUserName] = useState<string>("");
  const [logged_in_user_role_type, setLoggedInUserRoleType] =
    useState<string>("");
  const [isLumenEnergy, setIsLumenEnergy] = useState<boolean>(false);
  const [isTataMotors, setIsTataMotors] = useState<boolean>(false);
  const [is_calibration_supported, setIs_calibration_supported] =
    useState<boolean>(false);
  const [things_list, setThingsList] = useState<ThingListResponseProps>({
    status: "",
    message: "",
    things: [],
  });
  const [is_iot_enabled, setIsIotEnabled] = useState<boolean>(false);
  const [isGenericApp, setIsGenericApp] = useState<boolean>(false);
  const [isAurassure, setIsAurassure] = useState<boolean>(false);
  const [menuUrlArray, setMenuUrlArray] = useState<object>({});
  const [menuUrl, setMenuUrl] = useState<string>("");
  const [parent_vendor_id, setParentVendorId] = useState<number>(0);


  const globalConfig = GlobalConfig;

  const {
    mapThingsList,
    getPackageName,
    getViewAccess,
    getRemoteAccess,
    getRemoteLockAccess,
    checkEndCustomer,
    bannerToBeShown,
    handleGenericIcon,
    handleGenericPageTitle,
    onCollapseSider,
    viewDataAccess,
    supportedViewsPage,
    setStoreLogoutFunc,
    getLayoutSideMenu,
  } = useGlobalStoreFunctions({
    things_list,
    setMobileAppPackageName,
    setIsRentalStore,
    customer_access,
    plan_description,
    enabled_features,
    client_id,
    logged_in_user_client_id,
    isAurassure,
    parent_vendor_id,
    application_id,
    isGenericApp,
    collapsed,
    setCollapsed,
    customer_type,
    is_iot_enabled,
    availableThingCats,
    logged_in_user_role_type,
    client_logo,
    vendor_logo,
    client_name,
    setStoreLogout,
    setHeadSideObjectData,
    setAppName,
    setMenuUrlArray,
  });

  const {
    language,
    setLanguage,
    translationUrls,
    setTranslationUrls,
    i18n
  } = useLanguage();

  
  const t = i18n?.t ? i18n.t : (key: string) => key;
  const tataMotorsLogo = "https://datoms-files-storage.s3.ap-south-1.amazonaws.com/datoms/device-management/firmwares/image/1714634184654.png"

  const getUserAccess = async (): Promise<void> => {
    if (localStorage.getItem("Client-Id")) {
      client_slug = localStorage.getItem("Client-Id");
      appName = "dg-monitoring";
    }

    if (
      import.meta.env.VITE_MOBILE ||
      import.meta.env.VITE_DESKTOP ||
      !allApps.includes(appName)
    ) {
      [response, newOptionsResponse] = await Promise.all([
        getUserOptionsSingleApp(
          import.meta.env.VITE_DESKTOP ? client_id : client_slug,
        ),
        getOptionsData(
          client_slug,
          `?source_type=${window.innerWidth > 576 ? "web" : "mobile"}`,
        ),
      ]);
    } else {
      [response, newOptionsResponse] = await Promise.all([
        getUserOptions(client_slug, appName),
        getOptionsData(
          client_slug,
          `?application_slug=${appName}&source_type=${window.innerWidth > 576 ? "web" : "mobile"}`,
        ),
      ]);
      if (response.status == "failure" && appName === "dg-monitoring") {
        appName = "pollution-monitoring";
        [response, newOptionsResponse] = await Promise.all([
          getUserOptions(client_slug, "pollution-monitoring"),
          getOptionsData(
            client_slug,
            `?application_slug=${appName}&source_type=${window.innerWidth > 576 ? "web" : "mobile"}`,
          ),
        ]);
      }
    }

    if (
      response.policy_acceptance_required &&
      (import.meta.env.VITE_BUILD_MODE === "development" ||
        (!import.meta.env.VITE_MOBILE &&
          window.location.host.includes(".datoms.io")) ||
        (import.meta.env.VITE_MOBILE &&
          mobileAppPackageName === "io.datoms.dgmonitoring"))
    ) {
      setShowPolicyPage(response.policy_acceptance_required);
      return;
    } else {
      setShowPolicyPage(false);
    }

    if (
      appName === "fleet-monitoring" ||
      appName === "energy-meter" ||
      appName === "electrical-machines" ||
      appName === "delivery-tracking"
    ) {
      appName = "dg-monitoring";
    }

    if (response.status === 403 || newOptionsResponse.status === 403) {
      setUnauthorisedAccess(true);
      setUnauthorisedAccessMsg(response.message);
    }
    else if (
      response.status === "success" &&
      newOptionsResponse.status === "success"
    ) {
      let user_preferences: UserPreferencesProps = response.user_preferences
        ? response.user_preferences
        : newOptionsResponse.preferences || {};
      user_preferences["timezone"] =
        newOptionsResponse.preferences &&
        newOptionsResponse.preferences.timezone
          ? newOptionsResponse.preferences.timezone
          : moment.tz.guess();
      let thingListResponse: ThingListResponseProps =
          {} as ThingListResponseProps,
        is_calibration_supported = false,
        is_iot_enabled = false,
        availableThingCats: number[] = [],
        isGenericApp = false;

      setApplicationId(response.application_id);
      setApplicationName(response.application_name);
      setUserId(newOptionsResponse.person.id);
      setUserPreferences(user_preferences);
      setCurrentUserPreferences(user_preferences || {});
      setLanguage(user_preferences?.language || "en");
      // setTranslationUrls(response.translations || {}); //uncomment this line
      setCustomerAccess(response.access_list);
      setEnabledFeatures(response.features);
      setIsWhiteLabel(newOptionsResponse.white_labelling.applicable);
      setCustomizations(response.customizations);
      setPlanDescription(getFinalPlanDetails(response));
      setClientLogo(newOptionsResponse.organization.logo);
      setVendorLogo(newOptionsResponse.vendor?.id === 2120 ? tataMotorsLogo : newOptionsResponse.vendor?.logo);
      setTemplateId(newOptionsResponse.organization.template_id);
      setUserName(
        newOptionsResponse.person.name
          ? newOptionsResponse.person.name
          : "User",
      );
      setClientId(newOptionsResponse.organization.id);
      setClientName(newOptionsResponse.organization.name);
      setVendorName(newOptionsResponse.vendor.name);
      setVendorId(response.vendor_id);
      setLoggedInUserClientId(newOptionsResponse.person.org_id);
      setLoggedInUserName(newOptionsResponse.person.name);
      setLoggedInUserRoleType(newOptionsResponse.person.role_type);
      setThingsList(thingListResponse);
      setCustomerType(newOptionsResponse.organization.type);
      setIsIotEnabled(is_iot_enabled);
      setAvailableThingCats(availableThingCats);
      setIsGenericApp(newOptionsResponse.isGenericApp);
      setIsAurassure(parseInt(newOptionsResponse.vendor.id) === 1819);
      setIsLumenEnergy(parseInt(newOptionsResponse.vendor.id) === 10786);
      setIsTataMotors(parseInt(newOptionsResponse.vendor?.id) === 2120 || parseInt(newOptionsResponse.organization?.id) === 2120);
      setIs_calibration_supported(is_calibration_supported);
      setParentVendorId(newOptionsResponse.vendor.id);
    } else {
      setUnauthorisedAccess(false);
      setLoadedData(true);
      setErrorAPI(true);
      setErrorAPIMsg(response.message);
    }
  };

  const initialCall = () => {
    getDefaultColor(isAurassure ? "#2497aa" : "#ff8500");

    is_white_label
      ? customizations &&
        customizations.favicon &&
        customizations.favicon.length > 0
        ? handleGenericIcon(customizations.favicon)
        : handleGenericIcon(
            Number(newOptionsResponse.person.org_id) !==
              newOptionsResponse.organization.id
              ? DatomsIcon
              : GenericIcon,
          )
      : handleGenericIcon(DatomsIcon);

    is_white_label
      ? customizations &&
        customizations.portal_name &&
        customizations.portal_name.length > 0
        ? handleGenericPageTitle(customizations.portal_name)
        : handleGenericPageTitle(
            Number(newOptionsResponse.person.org_id) !==
              newOptionsResponse.organization.id
              ? "DATOMS"
              : "&#65279;",
          )
      : handleGenericPageTitle("DATOMS");

    moment.tz.setDefault(
      newOptionsResponse?.preferences?.timezone
        ? newOptionsResponse.preferences.timezone
        : moment.tz.guess(),
    );

    if (isRentalStore) {
      storeMenuUpdate({
        client_logo,
        vendor_logo,
        client_name,
        setHeadSideObjectData,
        setAppName,
      });
    } else {
      getLayoutSideMenu(newOptionsResponse.menu);
    }

    let body = document.body;
    let classNameToAddInBody =
      user_preferences &&
      user_preferences.language &&
      user_preferences.language !== "en"
        ? "translate"
        : "notranslate";
    if (body) {
      body.classList.add(classNameToAddInBody);
    }

    let userProperties = {
      Email: newOptionsResponse.person.email,
      Name: newOptionsResponse.person.name,
      Customer: newOptionsResponse.organization.name ? newOptionsResponse.organization.name : response.client_name,
      Vendor: newOptionsResponse.vendor.name,
      Application: response.application_name,
      Platform: "Web",
    };

    if (window.cordova) {
      if (window.cordova.platformId == "ios") {
        userProperties.Platform = "iOS";
      } else {
        userProperties.Platform = "Android";
      }
    }

    try {
      if (!import.meta.env.VITE_MOBILE) {
        if (window?.mixpanel?.init) {
          window.mixpanel.init("448c1893fd8657d024dea174b99759a2", {
            debug: true,
            track_pageview: true,
            persistence: "localStorage",
            record_sessions_percent: 10,  //records 10% of all sessions
            record_mask_text_selector: [] // 👈 Show all text including inputs
          });
        }
      }

      mixPanelRegisterUser(userProperties);
    } catch (e) {
      console.log("mixpanel_error", e);
    }
  };

  useEffect(() => {
    if (!import.meta.env.VITE_MOBILE) {
      if (import.meta.env.VITE_BUILD_MODE != "development") {
        client_slug = window.location.pathname.split("/")[2];
        application_slug = window.location.pathname.split("/")[3];
      } else {
        application_slug = window.location.pathname.split("/")[1];
      }

      if (application_slug && typeof application_slug == "string") {
        appName = application_slug;
      }
    } else {
      client_slug = window.location.pathname.split("/")[2];
    }

    if (!import.meta.env.VITE_MOBILE) {
      getUserAccess();
    }
    if (import.meta.env.VITE_MOBILE) {
      window.addEventListener("message", (e) => getPackageName(e), false);
    }
  }, []);

  useEffect(() => {
    if (application_id) {
      initialCall();
    }
  }, [application_id]);

  useEffect(() => {
    const sessionStartTime = Date.now(); // Store session start
  
    const handleUnload = () => {
      const duration = Math.floor((Date.now() - sessionStartTime) / 1000);
  
      if (window.mixpanel?.track) {
        window.mixpanel.track("Session End", {
          session_duration: duration,
          time: Math.floor(Date.now() / 1000) // Optional, improves consistency
        });
      }
    };
  
    window.addEventListener("beforeunload", handleUnload);
    window.addEventListener("pagehide", handleUnload); // For mobile/iOS Safari
  
    return () => {
      handleUnload(); // Fire even on unmount
      window.removeEventListener("beforeunload", handleUnload);
      window.removeEventListener("pagehide", handleUnload);
    };
  }, []);

  return (
    <GlobalContext.Provider
      value={{
        collapsed,
        setCollapsed,
        collapse,
        setCollapse,
        client_id,
        setClientId,
        client_name,
        setClientName,
        alertObjectData,
        setAlertObjectData,
        page_type,
        setPageType,
        enabled_features,
        setEnabledFeatures,
        plan_description,
        setPlanDescription,
        app_name,
        setAppName,
        select_thing_modal,
        setSelectThingModal,
        availableThingCats,
        setAvailableThingCats,
        currentUserPreferences,
        setCurrentUserPreferences,
        client_slug,
        mobileAppPackageName,
        setMobileAppPackageName,
        unauthorised_access,
        setUnauthorisedAccess,
        unauthorised_access_msg,
        setUnauthorisedAccessMsg,
        error_api,
        setErrorAPI,
        error_api_msg,
        setErrorAPIMsg,
        loadedData,
        setLoadedData,
        showPolicyPage,
        setShowPolicyPage,
        structure_style,
        setStructureStyle,
        t,
        is_mobile_page_name,
        setIsMobilePageName,
        user_preferences,
        setUserPreferences,
        user_id,
        setUserId,
        application_id,
        setApplicationId,
        is_white_label,
        setIsWhiteLabel,
        isFixedLogoForWhitelabel,
        setIsFixedLogoForWhitelabel,
        customizations,
        setCustomizations,
        loading_logo,
        setLoadingLogo,
        template_id,
        setTemplateId,
        client_logo,
        setClientLogo,
        vendor_logo,
        setVendorLogo,
        vendor_id,
        setVendorId,
        user_name,
        setUserName,
        getExtraHeaderComps,
        setGetExtraHeaderComps,
        head_side_object_data,
        setHeadSideObjectData,
        home_page,
        setHomePage,
        customer_type,
        setCustomerType,
        isRentalStore,
        setIsRentalStore,
        store_logout,
        setStoreLogout,
        logged_in_user_client_id,
        setLoggedInUserClientId,
        deskTopLogout,
        setDeskTopLogout,
        application_name,
        setApplicationName,
        customer_access,
        setCustomerAccess,
        vendor_name,
        setVendorName,
        logged_in_user_name,
        setLoggedInUserName,
        logged_in_user_role_type,
        setLoggedInUserRoleType,
        isLumenEnergy,
        setIsLumenEnergy,
        isTataMotors,
        setIsTataMotors,
        is_calibration_supported,
        setIs_calibration_supported,
        things_list,
        setThingsList,
        is_iot_enabled,
        setIsIotEnabled,
        isGenericApp,
        setIsGenericApp,
        isAurassure,
        setIsAurassure,
        menuUrlArray,
        setMenuUrlArray,
        menuUrl,
        setMenuUrl,
        mapThingsList,
        getPackageName,
        getViewAccess,
        getRemoteAccess,
        getRemoteLockAccess,
        checkEndCustomer,
        bannerToBeShown,
        handleGenericIcon,
        handleGenericPageTitle,
        onCollapseSider,
        viewDataAccess,
        supportedViewsPage,
        getUserAccess,
        setStoreLogoutFunc,
        parent_vendor_id,
        globalConfig,
        language,
        setLanguage,
        translationUrls,
        i18n,
        isSistemaBioCustomer: vendor_id === 1184
      }}
    >
      {children}
    </GlobalContext.Provider>
  );
};
export const useGlobalContext = (): GlobalContextProps =>
  useContext(GlobalContext);
