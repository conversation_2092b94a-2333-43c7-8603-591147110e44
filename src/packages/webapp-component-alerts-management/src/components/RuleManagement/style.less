@import '../../../../../styles/default-color.less';

body {
	padding: 0 !important;
}

.cust-modal {
	.ant-modal-content {
		border-radius: 20px;
		padding: 20px;
	}

	.ant-modal-header {
		border-radius: 20px 20px 0 0;
		border: none;
	}

	.ant-modal-title {
		color: rgba(178, 117, 51, 1) !important;
		font-size: 13px !important;
	}

	.ant-modal-body {
		padding-top: 10px;
		font-size: 13px !important;

		.ant-radio-wrapper {
			font-size: 13px !important;
		}

		.reset {
			border: none;
			box-shadow: none;
			float: right;
		}

		.message {
			font-weight: bold;
			margin-bottom: 20px;
		}

		.operations {
			color: #808080;
			font-size: 13px;
			margin-bottom: 10px;

			.ant-checkbox {
				margin-right: 12px;
			}
		}

		.ant-checkbox-wrapper+.ant-checkbox-wrapper {
			margin: 12px 0;
		}

		.ant-row {
			margin: 6px 0;
			.ant-col {
				font-size: 13px;
			}
		}
	}

	.ant-modal-footer {
		border-radius: 0 0 20px 20px;
		border: none;
		padding: 10px 16px;
		margin-top: 0;

		.ant-btn {
			background-color: #f6f6f6;
			border-color: #f6f6f6;
		}

		.ant-btn-primary {
			background-color: @defaultColor;
			border-color: @defaultColor;
		}
	}
}

@media only screen and (min-width: 1600px) {
	#rule_management {
		width: 80%;
		margin: 0 auto;
	}
}

.rule_management_end_application {
	width: 100% !important;
	margin: 0 auto !important;
	overflow-y: auto !important;
	height: calc(100vh - 60px) !important;
	&.rule_management_rental_things{
		width: 80% !important;
		overflow-y: hidden !important;
		#thing_settings .add-new-ma-rules {
			font-size: 10px;
		}
	}

	.page {
		width: 100% !important;
		margin: 0 auto !important;
	}
}

#rule_management {
	background-color: #fff;
	padding: 10px;

	.ant-tabs-nav-operations {
		display: none;
	}

	#thing_settings .entity .entity-switch .quick-button {
		display: none;
	}

	.ant-table-thead {
		background-color: transparent !important;
	}

	.search-field {
		border-radius: 20px !important;
		width: 350px;
		padding: 3px 12px;

		.anticon-search {
			color: #c4c2c2;
		}

		.ant-input {
			font-size: 12px;
			margin-left: 8px;
		}
	}

	.expression {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin: 4px 0;
		min-height: 32px;
		width: 100%;
		.rule-exp-status{
			color: #d5a672;
			padding: 9px 8px 4px;
		}
		.threshold-input{
			margin-bottom: 4px;
			.threshold-text{
				color: #808080;
			}
		}
		.alert-duration-div {
			display: flex;
			gap: 12px;
			.alert-duration-input
			.ant-input-group
			.ant-input-group-addon 
			.ant-select
			.ant-select-selector {
				border: none;
			}
		}
		.alerts-time-picker{
			border: none;
			padding: 2px;
			margin-left: 2px;
			&.ant-picker-focused{
				box-shadow: none;
			}
			input{
				// border-bottom: none;
				font-size: 16px;
				width: 70px;
			}
		}
		.text {
			font-size: 16px;
			position: relative;
			top: 2px;
		}

		.ant-tag {
			color: white !important;
			font-size: 10px;
			margin: 0 4px;
		}

		input {
			font-size: 12px;
			width: 32px;
			border: none;
			color: #d5a672;
			border-bottom: 2px dotted #c4c2c2;
			border-radius: 0 !important;
			background-color: transparent;
			text-align: center;
			margin: 0 10px;
			padding: unset;
		}

		.options {
			display: flex;
			align-items: center;
			justify-content: flex-end;

			.ant-btn {
				border: none;
				box-shadow: none;
				color: #374375;
				font-size: 11px;
				font-weight: 500;
				padding: 0px 4px;
			}
		}

		.meta {
			border-radius: 10px !important;
			color: black !important;
		}
	}

	.header {
		display: flex;
		justify-content: center !important;
		width: 100%;
		position: relative;

		&.end-alert {
			justify-content: center !important;
		}

		#application_dropdown {
			position: absolute;
			left: 0;
		}

		.setting-select-container {
			display: none !important;
		}

		.datoms-twin-tabs {
			margin-bottom: 0 !important;
			display: none !important;
		}
	}

	.things-dropdown {
		margin-right: 20px;
		min-width: 196px;
		width: fit-content;

		.ant-select-selection-item {
			color: #374375;
			font-size: 13px;
			font-weight: 600;
		}

		.ant-select-arrow {
			right: 4px;
		}

		.cust-button {
			display: none;
		}
	}

	#user_settings {
		display: flex;
		align-items: center;
		margin: 5px 0;
		margin-top: 30px;

		.third-level {
			.things-select {
				display: none !important;
			}
		}

		.user-dropdown {
			//	margin-right: 60px;
			min-width: 192px;
			width: fit-content;
			display: flex;

			&.iot-rental-things {
				margin-right: 32px;

				>.ant-row {
					width: unset;
					flex: 1;

					.cust-select .select-container {
						width: 100%;
					}
				}
			}

			.ant-select-selector {
				border: none;
				border-bottom: 1px dotted #808080;
				border-radius: 0px !important;
			}

			.select-label {
				//	display: none;
				margin-right: 12px;
				color: #232323;
			}

			.ant-select-selection-item {
				color: #374375;
				font-size: 13px;
				font-weight: 600;
			}

			.ant-select-arrow {
				right: 4px;
			}

			.cust-button {
				display: none;
			}
		}

		.type-switch {
			border-radius: 6px;
			box-shadow: 2px 3px 5px #74747430;
			padding: 5px 15px;
		}

		.third-level {
			.ant-dropdown-trigger.filter-icon {
				font-size: 15px;
				height: 32px;
				margin-left: 20px;
			}

			.cust-select {
				display: none;
			}
		}
	}

	.actions {
		margin: 10px 0;
		display: flex;
		justify-content: flex-end;

		.action-dropdown {
			margin: 0 20px;
		}
	}

	.data {
		display: flex;

		.thing-device-list {
			margin-right: 20px;
		}

		.thing-list-container {
			.alert-things-cat-filter-sec {
				display: flex;
				justify-content: space-between;

				.search-header {
					flex: 1;
					margin-right: 8px;
				}

				.filter-icon {
					height: unset;
				}
			}
		}

		.alert-rules-list,
		.user-rule-list {
			width: 100%;
			flex: 3;
		}
	}

	@media only screen and (max-width: 767px) {
		.header {
			flex-wrap: wrap;

			#application_dropdown {
				width: 100%;
				margin-bottom: 16px;

				.cust-select {
					.select-container {
						margin: 0 auto;
					}
				}
			}

			.cust-tabs {
				align-items: flex-start !important;
			}

			.datoms-twin-tabs {
				display: flex !important;
				margin: 0 auto !important;
				margin-bottom: 10px !important;
			}
		}

		#user_settings {
			flex-wrap: wrap;
			align-items: flex-start;

			.user-dropdown {
				width: 100%;
				margin: 20px 0 30px;
			}

			.cust-radio-group {
				margin-right: 36px;
			}

			.third-level {
				width: 100%;
				margin: 10px 0;

				.ant-dropdown-trigger {
					display: none;
				}

				.cust-select {
					display: block;
				}

				.select-container {
					width: 100%;

					.ant-select-selector {
						border: 0.25px solid #808080;
						border-radius: 5px;
						padding: 0 8px;
						// margin: 10px 0;
					}
				}
			}
		}
	}

	@media only screen and (max-width: 576px) and (min-width: 0px) {
		.header {
			display: block;

			#application_dropdown {
				position: relative !important;
				width: 140px;
				margin: inherit !important;
				margin-bottom: 20px !important;
			}

			.cust-tabs {
				display: none !important;
				align-items: flex-start !important;
			}

			.setting-select-container {
				display: flex !important;
				align-items: center !important;

				.setting-select-icon {
					font-size: 20px;
					margin-right: 10px;
					color: #7686a1;
				}

				.cust-select {
					.select-container {
						min-width: 140px !important;

						.ant-select-arrow {
							color: #7686a1 !important;
						}
					}

					.ant-select-selector {
						border: none !important;
						font-weight: bold !important;
					}
				}
			}

			.cust-button {
				display: none !important;
			}
		}

		.actions {
			margin: 0 !important;
		}

		#user_settings {
			flex-direction: column;
			flex-wrap: wrap;
			align-items: flex-start;
			margin-top: 0 !important;

			.things-dropdown .select-container .ant-select-selection-search {
				padding-left: 10px;
			}

			.type-switch {
				display: none !important;
			}

			.user-dropdown {
				display: flex;
				justify-content: space-between;
				width: 100%;
				align-items: center;
				margin-top: 15px !important;
				margin-bottom: 5px !important;

				.select-label {
					display: block !important;
					min-width: 60px;
					margin-bottom: -10px;
				}

				.cust-button {
					display: none !important;
				}
			}

			.cust-radio-group {
				margin: 20px 0 30px;
			}

			.third-level {
				width: 100%;

				.things-select {
					display: flex !important;
					align-items: center !important;

					.select-label {
						min-width: 60px;
						margin-bottom: 5px;
					}
				}

				.select-container {
					width: 100%;
					margin-left: 0 !important;

					.ant-select-selector {
						border: 0.25px solid #808080;
						border-radius: 5px;
						padding: 0 8px;
						// margin: 10px 0;
					}
				}
			}
		}

		#thing_settings {
			.entity {
				width: 100%;
				flex-wrap: nowrap !important;
				flex-direction: unset !important;
				margin-top: 10px !important;

				.entity-switch {
					width: auto;
					min-width: 80px;
					margin: 10px 0;
					display: flex;
					justify-content: space-between;
					align-items: center;
					//	display: none;

					/*.quick-button {
						display: block !important;
					}*/
				}

				.things-dropdown {
					display: flex;
					justify-content: center;
					width: 100%;

					.select-container {
						width: 80%;
						margin: 0 auto;
						margin-left: 0 !important;
					}

					.cust-button {
						display: inline-block;
					}
				}
			}

			.actions {
				justify-content: space-evenly !important;
			}

			.filter-section {
				margin: 20px 0 30px !important;
			}

			.rule-templates-list {
				.rule {
					.expression {
						display: none;
					}
				}
			}
		}
	}

	.user-settings-options {
		display: flex;
		align-items: center;
		padding: 8px 12px 20px;

		.user-dropdown {
			width: 214px;
			margin-right: 24px;
		}

		.entity-toggle {
			margin-right: 24px;
		}
	}

	.entity-filter {
		.ant-dropdown-trigger {
			color: #a5afc1;
			height: 28px;
		}
	}

	.things-device-filter {
		.ant-dropdown-trigger {
			color: #a5afc1;
			height: 24px;
		}
	}

	.ant-dropdown-trigger {
		color: #a5afc1;
		display: inline-block;
		font-size: 12px;
		height: 20px;

		&[disabled] {
			color: #dcdbdb;
		}
	}

	.unselect {
		background-color: @defaultColor;
		color: #e1fcf8;
		border: none;
		box-shadow: none;
		font-size: 10px;
		padding: 2px 12px;
		height: 20px;
		border-radius: 20px;
	}

	.search-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-weight: 400;
		min-height: 22px;

		.anticon-search {
			color: #c4c2c2;
			position: relative;
			top: 2px;
		}
	}

	.search {
		background-color: transparent;
		border: none;
		font-size: 12px;
		outline: none;
		padding: 0;
		width: 100%;
		min-height: 24px;

		.ant-input {
			border: none;
			padding: 0 8px;
		}

		.ant-input:focus {
			box-shadow: none;
		}

		.ant-input-group-addon {
			border: none;
			background: none;
			padding: 0 4px;

			.anticon {
				color: #c4c2c2;
			}
		}
	}

	.search:focus {
		box-shadow: none !important;
	}

	.ant-switch-handle {
		.ant-switch-loading-icon {
			top: unset;
			color: red;
		}
	}

	.filter-icon {
		background: #f2f2f2 0% 0% no-repeat padding-box;
		border-radius: 4px;
		padding: 4px 6px;
	}

	@media only screen and (max-width: 768px) {
		.things-device-filter {
			display: none;
		}

		.thing-device-list {
			display: none;
		}

		.search-container {
			.ant-input {
				width: 100% !important;
			}
		}
	}

	.ant-layout {
		background-color: #fff;

		&.mnt-rt-row {
			background-color: #F9F9F9 !important;
		}
	}

	.header {
		#more_options {
			display: flex;
			justify-content: flex-end;
		}
	}

	.settings-data {
		.show-tab {
			display: block;
		}

		.hide-tab {
			display: none;
		}
	}

	.thing-device-list {
		width: 320px;

		.search-header {
			min-height: 24px;
		}

		.ant-layout {
			background-color: transparent;
		}

		.ant-table {
			background-color: unset;

			.ant-table-container {
				background: #ffffff 0% 0% no-repeat padding-box;
				box-shadow: 2px 4px 6px #00000005;
				border: 1px solid #f2f2f2;
				border-radius: 30px;
				padding: 20px 20px 35px;
			}
		}

		.ant-table-filter-column-title {
			color: #7686a1;
			font-size: 13px;
		}

		.ant-table-thead,
		.ant-table-tbody {
			.ant-table-cell {
				background-color: transparent !important;
				border-bottom: 0.25px solid whitesmoke;
				color: #7686a1;
				font-size: 13px;
				font-weight: normal;
				padding: 0;
			}
		}

		.ant-table-thead {
			.ant-table-cell {
				border-bottom: 0.25px solid #eef0f3;
				padding-bottom: 6px;
			}
		}

		.ant-table-tbody {
			.ant-table-cell {
				color: #808080;
				font-size: 13px;
				padding: 9px 0;
				cursor: pointer;

				.active {
					color: @defaultColor;
				}
			}
		}
	}

	.cust-select {
		.select-container {
			color: #7686a1;
			font-size: 13px;
			min-width: 192px;
			width: fit-content;
		}

		.ant-select-selector {
			color: #7686a1;
			border: none;
			border-bottom: 0.5px dashed #7686a1;
			font-size: 13px;
			padding: 0;
			padding-left: 2px;
		}
	}

	.cust-tabs {
		&.disabled {
			.ant-tabs-tab {
				cursor: not-allowed !important;
			}
		}

		.ant-tabs-nav::before {
			border: none;
		}

		.ant-tabs-tab {
			padding: 0;
			height: 30px;
		}

		.ant-tabs-tab-active {
			.ant-tabs-tab-btn {
				color: #374375;
				font-weight: 600;
			}
		}

		.ant-tabs-tab-btn {
			color: #7686a1;
			font-size: 13px;
			padding: 0 15px;
		}

		.ant-tabs-ink-bar {
			background: @defaultColor;
			height: 1px;
		}
	}

	.cust-button {
		font-size: 13px !important;
		color: #7686a1 !important;
		position: absolute;
		right: 0;

		&.disabled {
			cursor: not-allowed !important;
		}
	}

	.cust-radio-group {
		.ant-radio-button-wrapper {
			border: 0.5px solid #f2f2f2;
			border-radius: 6px;
			font-size: 12px;
			margin: 0 4px;
		}

		.ant-radio-button-wrapper-checked {
			background: #ffffff 0% 0% no-repeat padding-box;
			box-shadow: 2px 3px 5px #74747430;
			border: 0.5px solid #f2f2f2;

			color: #232323;
			font-weight: 600;
		}

		.ant-radio-button-wrapper:not(:first-child)::before {
			display: none;
		}
	}

	#thing_settings {
		.rule {
			padding: 10px;

			.rules-row {
				display: flex;
				flex-wrap: nowrap;
				align-items: flex-start;
			}
		}

		.entity {
			align-items: center;
			display: flex;
			// margin-top: 30px;

			.label-with-select {
				margin: 0 20px;
			}

			.entity-switch {
				border-radius: 6px;
				// box-shadow: 2px 3px 5px #74747430;
				padding: 5px 15px;

				/*.quick-button {
					display: none;
				}*/
			}
		}

		.actions {
			display: flex;
			align-items: center;
			justify-content: flex-end;

			.ant-btn-text {
				color: #565656;
				font-size: 12px;
				padding: 4px;
			}
		}

		.filter-section {
			display: flex;
			align-items: center;
			margin: 38px 0 12px;

			.ant-dropdown-trigger {
				margin-left: 16px;
				font-size: 15px;
				height: 30px;
			}
		}

		.ant-radio-button-wrapper {
			margin: 0;
		}

		.ant-radio-button-wrapper {
			background-color: transparent;
			border: none;
			border-radius: 6px;
			color: #7686a1;
		}

		.ant-radio-button-wrapper-checked {
			background-color: #fff;
		}

		.ant-radio-group-outline {
			padding: 2px;
			background-color: #f9f9f9;
		}
	}

	.things-list {
		.ant-list-items {
			.active {
				color: @defaultColor;
			}
		}
	}

	.rule-templates-list {
		/*table {
			border-spacing: 0 20px;
		}*/

		/*.ant-checkbox {
			overflow: hidden !important;
		}*/

		/*.ant-checkbox-indeterminate .ant-checkbox-inner::after {
			width: 15px !important;
		}*/

		.ant-table-thead {
			.ant-table-cell {
				color: #7686a1;
				font-size: 12px;
				padding: 2px !important;
			}
		}

		.ant-table-thead,
		.ant-table-tbody {
			.ant-table-cell {
				background-color: transparent !important;
				padding-left: 0;
				padding-right: 8px;
				border: none;
			}
		}

		.ant-table-tbody {
			.ant-table-row {
				vertical-align: text-top;
				// background: #FEFEFE 0% 0% no-repeat padding-box;
				//	box-shadow: 1px 2px 4px #80808014;

				.ant-table-cell {
					// border: 0.5px solid #F2F2F2;
					// border-left: none;
					//		box-shadow: 1px 2px 4px #80808014;
				}

				.ant-table-cell.ant-table-selection-column {
					// border-left: 0.5px solid #F2F2F2;
					// border-right: none;
				}
			}

			.rule {
				.name {
					color: #232323;
					font-size: 13px;
					display: inline-block;
					margin-right: 12px;
				}

				.status {
					border-radius: 6px !important;
					box-sizing: border-box;
					font-size: 11px;
					text-align: center;
				}

				.status.active {
					background: #e1fcf8 0% 0% no-repeat padding-box !important;
					border: 1px solid #46dfab;
					color: #57bf95;
				}

				.status.inactive {
					background: #f14a4a52 0% 0% no-repeat padding-box !important;
					border: 1px solid #f14a4a;
					color: #232323;
				}
			}
		}
	}

	.thing-device-list.active {
		.ant-table-thead {
			.ant-table-cell {
				border-bottom-color: @defaultColor;
			}
		}
	}

	.alert-rules-list.active {
		.ant-table-thead {
			.ant-table-cell {
				padding-bottom: 11.5px;
				border-bottom-color: @defaultColor;
			}
		}
	}

	.alert-rules-list {
		border: 2px solid #ffffff;

		.ant-layout {
			background-color: transparent;
		}

		.ant-table-container {
			padding: 0 18px 16px 12px;
		}

		.ant-spin-nested-loading {
			height: 100%;

			.ant-spin-container {
				height: 100%;

				.ant-table {
					background: #F9F9F9;
					border-radius: 16px;
					height: 100%;
				}
			}
		}

		.ant-table-filter-column-title {
			color: #7686a1;
			font-size: 13px;
		}

		.ant-table-thead,
		.ant-table-tbody {
			.ant-table-row {
				vertical-align: text-top;
			}

			.ant-table-cell {
				background-color: transparent !important;
				padding-left: 0;
				border-bottom: 0.25px solid whitesmoke;
			}
		}

		.ant-table-thead {
			.ant-table-cell {
				border-bottom: 0.25px solid #eef0f3;
				padding: 14px 8px 14px 0;
			}
		}

		.ant-table-tbody {
			.ant-table-cell {
				.rule-name {
					color: #808080;
					font-size: 13px;
				}

				.active-alert,
				.inactive-alert {
					.label {
						font-size: 12px;
						margin-right: 16px;
						//color: #c4c2c2 !important;
						color: #808080 !important;
					}
				}

				.active-alert {
					color: #232323;
				}

				.inactive-alert {
					color: #c4c2c2;
				}

				.ant-switch,
				.ant-switch-checked {
					background: #ffffff 0% 0% no-repeat padding-box !important;
					box-shadow: 2px 4px 6px #00000026;
					border-radius: 20px;
					height: 18px;

					.ant-switch-handle::before {
						background: #c4c2c2 0% 0% no-repeat padding-box;
						border-radius: 12px;
						left: 2px;
						bottom: 2px;
						height: 14px;
						width: 15px;
					}
				}

				.ant-switch-checked {
					.ant-switch-handle::before {
						background: #47ccc3 0% 0% no-repeat padding-box;
					}
				}
			}
		}
	}

	.search-container {
		margin: 0;
		width: 348px;

		input::placeholder {
			color: #7686a199;
			font-size: 12px;
		}
	}

	.rounded {
		position: relative;
		color: #232323;
	}

	.data {
		color: #232323;
	}

	.ant-switch,
	.ant-switch-checked {
		background: #ffffff 0% 0% no-repeat padding-box !important;
		box-shadow: 2px 4px 6px #00000026;
		border-radius: 20px;
		height: 18px;

		.ant-switch-handle::before {
			background: #c4c2c2 0% 0% no-repeat padding-box;
			border-radius: 12px;
			left: 2px;
			bottom: 2px;
			height: 14px;
			width: 15px;
		}
	}

	.ant-switch-checked {
		.ant-switch-handle::before {
			background: #47ccc3 0% 0% no-repeat padding-box;
		}
	}
}

//override styles for end application
.rule_management_end_application {
	.page {
		#thing_settings {
			.entity {
				margin-top: 18px;
			}

			.filter-section {
				margin-top: 34px;
			}

			.ant-table-body {
				max-height: calc(100vh - 276px) !important;
			}
		}

		.thing-list-container {
			display: inline-block;

			.ant-table-body {
				max-height: calc(100vh - 331px) !important;
			}
		}

		.alert-rules-list {
			.ant-table-body {
				max-height: calc(100vh - 268px) !important;
			}
		}
	}

	.user-dropdown,
	.things-dropdown {
		.ant-select-arrow {
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}

@media only screen and (max-width: 800px) {
	.rule_management_end_application {
		height: calc(100vh - 62px);
		overflow-y: auto;
		overflow-x: hidden;
	}
}

@media only screen and (max-width: 576px) {
	.rule_management_end_application.rule_management_rental_things {
		width: 95% !important;
	}
	.rule_management_end_application {
		height: calc(100vh - 107px) !important;

		.things-dropdown {
			&.iot-rental-things {
				width: unset !important;
			}
		}

		&#rule_management .header #application_dropdown {
			margin-bottom: 0 !important;
		}

		.ant-table-wrapper {
			.ant-pagination-mini {
				display: flex;
				justify-content: end;
				align-items: center;

				.ant-pagination-next {
					height: auto;
				}
			}

			.ant-pagination-item {
				display: none;
			}

			.ant-pagination-item-active {
				display: block;
				width: fit-content;
			}

			.ant-pagination-jump-next {
				display: none;
			}

			.ant-pagination-prev {
				height: auto;
			}

			.ant-pagination-next {
				height: auto;
			}

			.ant-table {
				box-shadow: none;

				.ant-table-thead tr {
					background-color: transparent !important;

					.ant-table-cell {
						border: none;
					}
				}
			}
		}
	}

	#rule_management {
		#thing_settings {
			.entity {
				flex-wrap: nowrap !important;

				.label-with-select {
					margin-left: 0 !important;
				}

				.entity-switch {
					width: auto !important;
					min-width: 80px !important;

					.quick-button {
						display: none !important;
					}
				}
			}
		}

		.page {
			.data {
				.thing-list-container {
					display: none !important;
				}

				>div {
					width: 100%;
				}
			}
		}
	}
}