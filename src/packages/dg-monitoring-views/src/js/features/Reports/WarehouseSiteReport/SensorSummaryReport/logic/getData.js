import React from "react";
import {
  retrieveSiteDetails,
  getThingsData,
  getTasksData,
  retriveTasksData,
} from "@datoms/js-sdk";
import Backyard from "@datoms/js-utils/src/Backyard/Backyard_back";
import _filter from "lodash/filter";
import _find from "lodash/find";
import _remove from "lodash/remove";
import _uniqBy from "lodash/uniqBy";
import moment from "moment-timezone";

function initialSpaces(spaceCount) {
  let space = "";
  for (let i = 0; i < spaceCount; i++) {
    space += " ";
  }
  return space;
}

export async function getSiteDetails(isDownload) {
  const { client_id } = this.props;
  const { selectedSite, selectedSiteArray, thingsDetails, columns } =
    this.state;
  const siteArray = isDownload ? selectedSiteArray : selectedSite;
  const siteDetailPromises = siteArray.map(async (site) => {
    return await retrieveSiteDetails(client_id, site);
  });
  const siteDetails = await Promise.all(siteDetailPromises);
  let downloadColumn = {},
    downloadColumnChildren = {},
    totalAssetIds = {},
    totalUniqParams = {},
    downloadFirstDataSource = {};
  const columnWithChildren = [];
  if (siteDetails?.length) {
    siteDetails.map((siteDetail) => {
      if (siteDetail?.data?.id) {
        if (!totalAssetIds[siteDetail.data.id]) {
          totalAssetIds[siteDetail.data.id] = [];
        }
        if (!totalUniqParams[siteDetail.data.id]) {
          totalUniqParams[siteDetail.data.id] = [];
        }
        if (!downloadColumn[siteDetail.data.id]) {
          downloadColumn[siteDetail.data.id] = [];
        }
        if (!downloadColumnChildren[siteDetail.data.id]) {
          downloadColumnChildren[siteDetail.data.id] = [];
        }
        if (!downloadFirstDataSource[siteDetail.data.id]) {
          downloadFirstDataSource[siteDetail.data.id] = [
            {
              date: "Date",
            },
          ];
        }
      }
      const assets = siteDetail?.data?.assets;
      const filterAssets = _filter(assets, (asset) =>
        [
          "Cold Storage Chambers",
          "Ambient Temperature & Humidity - Environment",
        ].includes(asset.purpose),
      );
      let spaceCount = 1;
      if (filterAssets?.length) {
        filterAssets.map((asset, index) => {
          spaceCount += index + 1;
          totalAssetIds[siteDetail.data.id].push(asset.id);
          const findAsset = _find(thingsDetails?.things, { id: asset.id });
          const filterParams = _filter(findAsset?.parameters, function (o) {
            return ["temperature", "humidity", "duration"].includes(o.type);
          });
          const columnChildren = [];
          if (filterParams?.length) {
            downloadColumnChildren[siteDetail.data.id].push({
              title: findAsset?.name,
              dataIndex: `${findAsset?.id}_${filterParams[0]?.key}`,
            });
            filterParams.map((param, ind) => {
              totalUniqParams[siteDetail.data.id].push(param?.key);
              if(param.key === "calculated_runhour") return;
              columnChildren.push({
                title: `${param?.name} (${param?.unit})`,
                dataIndex: `${findAsset?.id}_${param?.key}`,
                width: 120,
              });
              if (ind > 0) {
                spaceCount += 1;
                downloadColumnChildren[siteDetail.data.id].push({
                  title: initialSpaces(spaceCount),
                  dataIndex: `${findAsset?.id}_${param?.key}`,
                });
              }
              downloadFirstDataSource[siteDetail.data.id][0][
                `${findAsset?.id}_${param?.key}`
              ] = `${param?.name} (${param?.unit})`;
            });
          }
          if (_find(findAsset?.parameters, { key: "dr_st" })) {
            totalUniqParams[siteDetail.data.id].push("dr_st");
            columnChildren.push(
              {
                title: `Door Sensor (No. of times opened)`,
                dataIndex: `${findAsset?.id}_dr_st`,
                width: 120,
              },
              {
                title: `Total Duration Opened`,
                dataIndex: `${findAsset?.id}_rnhr`,
                width: 120,
              },
            );
            downloadColumnChildren[siteDetail.data.id].push(
              {
                title: initialSpaces(spaceCount + 1),
                dataIndex: `${findAsset?.id}_dr_st`,
              },
              {
                title: initialSpaces(spaceCount + 2),
                dataIndex: `${findAsset?.id}_rnhr`,
              },
            );
            downloadFirstDataSource[siteDetail.data.id][0][
              `${findAsset?.id}_dr_st`
            ] = "Door Sensor (No. of times opened)";
            downloadFirstDataSource[siteDetail.data.id][0][
              `${findAsset?.id}_rnhr`
            ] = "Total Duration Opened";
          }
          columnWithChildren.push({
            title: findAsset?.name,
            children: columnChildren,
          });
        });
      }
      totalUniqParams[siteDetail.data.id] = _uniqBy(
        totalUniqParams[siteDetail.data.id],
      );
      downloadColumn[siteDetail.data.id] = [
        {
          title: " ",
          dataIndex: `date`,
        },
        ...downloadColumnChildren[siteDetail.data.id],
      ];
    });
  }
  let columnsCopy =
    this.state.columns && JSON.parse(JSON.stringify(this.state.columns));

  if (!isDownload) {
    columnsCopy = [
      {
        title: "Date",
        dataIndex: `date`,
        width: 120,
      },
    ];
    columnsCopy = [
      {
        title: "Date",
        dataIndex: `date`,
        width: 120,
      },
      ...columnWithChildren,
    ];
  }
  this.setState(
    {
      siteDetails,
      totalAssetIds,
      totalUniqParams,
      columns: columnsCopy,
      downloadColumn,
      downloadFirstDataSource,
    },
    async () => {
      await this.getAllData(isDownload);
    },
  );
}

export async function getAllData(isDownload) {
  const { totalAssetIds, totalUniqParams, fromTime, uptoTime, siteDetails } =
    this.state;
  const { client_id, application_id } = this.props;
  let allDataRes = {};
  const dataPromises = siteDetails.map(async (site) => {
    if (totalAssetIds[site?.data?.id]?.length) {
      if (site?.data?.id) {
        if (!allDataRes[site.data.id]) {
          allDataRes[site.data.id] = [];
        }
      }
      let dataPacketSum = {
        data_type: "aggregate",
        aggregation_period: 86400,
        things: totalAssetIds[site?.data?.id],
        parameters: totalUniqParams[site?.data?.id].filter(
          (item) => item !== "dr_st",
        ),
        parameter_attributes: ["avg", "sum"],
        from_time: fromTime,
        upto_time: uptoTime,
      };
      const taskApiStr = `&category_id=45&results_per_page=1000&start_after=${moment
        .unix(fromTime)
        .toISOString()}&start_before=${moment.unix(uptoTime).toISOString()}&thing_list=${totalAssetIds[site?.data?.id].join(", ")}&GetDetails=true&parameters=${encodeURIComponent(
        JSON.stringify(["calculated_runhour"]),
      )}`;
      const [apiAvgData, apiTaskData] = await Promise.all([
        getThingsData(dataPacketSum, client_id, application_id),
        totalUniqParams[site?.data?.id]?.length &&
        totalUniqParams[site?.data?.id].includes("dr_st")
          ? getTasksData(
              {
                client_id,
                application_id,
              },
              `?page_no=${1}${taskApiStr}`,
            )
          : null,
      ]);
      const remainingTaskApis = [];
      if (apiTaskData?.response) {
        const totalTaskApiPages =
          parseInt(apiTaskData?.response?.total_mission_count / 1000) + 1;
        if (totalTaskApiPages > 1) {
          for (let i = 2; i <= totalTaskApiPages; i++) {
            remainingTaskApis.push(
              getTasksData(
                {
                  client_id,
                  application_id,
                },
                `?page_no=${i}${taskApiStr}`,
              ),
            );
          }
        }
      }
      const remainingTastData = await Promise.all(remainingTaskApis);
      allDataRes[site.data.id].push(apiAvgData, [
        apiTaskData,
        ...remainingTastData,
      ]);
      return { apiAvgData, apiTaskData, remainingTastData };
    }
  });
  await Promise.all(dataPromises);
  this.setState(
    {
      fetchedDataRes: allDataRes,
    },
    () => {
      this.getTableData(isDownload);
    },
  );
}

export function getTableData(isDownload) {
  const {
    fetchedDataRes,
    purposeThings,
    fromTime,
    uptoTime,
    siteTabArray,
    downloadColumn,
    downloadFirstDataSource,
    totalAssetIds,
    totalUniqParams,
  } = this.state;
  const timezone = this.props.user_preferences.timezone;
  new Backyard({
    scripts: [
      "https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js",
      "https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.23/moment-timezone.min.js",
      "https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.33/moment-timezone-with-data-1970-2030.min.js",
      "https://cdn.jsdelivr.net/npm/lodash@4.17.15/lodash.min.js",
    ],
    input: {
      fetchedDataRes,
      purposeThings,
      fromTime,
      uptoTime,
      isDownload,
      downloadColumn,
      timezone,
      siteTabArray,
      downloadFirstDataSource,
      totalAssetIds,
      totalUniqParams,
      client_id: this.props.client_id,
    },
    run: function (ctx, input, cb) {
      const {
        fetchedDataRes,
        purposeThings,
        fromTime,
        uptoTime,
        isDownload,
        downloadColumn,
        timezone,
        siteTabArray,
        downloadFirstDataSource,
        totalAssetIds,
        totalUniqParams,
        client_id,
      } = input;
      function tableData() {
        let siteSummaryData = {},
          siteDetailedData = {},
          downloadData = { conf: [], data: [] };
        function textFunction(
          text,
          size,
          fontWeight,
          forceNewPage,
          excelNewSheet,
        ) {
          let { textProps, textValue } = {
            textProps: {
              props: {
                gutter: 10,
              },
              child: [
                {
                  excelNewSheet,
                  pdf_force_new_page: forceNewPage,
                  textColor: [0, 0, 0],
                  type: fontWeight ? "bold" : "normal",
                  compo: "Text",
                  props: {
                    type: fontWeight ? "bold" : "normal",
                  },
                  pdf_size: size,
                  col_props: {
                    span: 5,
                  },
                  secStyle: {
                    body: {
                      font: {
                        size: size ? size : 12,
                        bold: fontWeight,
                      },
                    },
                  },
                },
              ],
            },
            textValue: [
              {
                textData: text,
              },
            ],
          };
          return {
            textProps,
            textValue,
          };
        }
        const ReportHeader = {
          text_conf: {
            props: {
              gutter: 5,
            },
            child: [
              {
                pdf_text_align: "center",
                textColor: [255, 255, 255],
                pdf_size: 16,
                type: "bold",
                fill: {
                  fill_color: [255, 133, 0],
                  y_value: 20,
                  top: 8,
                },
                compo: "Text",
                col_props: {
                  span: 24,
                },
                secStyle: {
                  body: {
                    font: {
                      size: 20,
                      bold: true,
                    },
                  },
                },
              },
              {
                type: "bold",
                compo: "Text",
                pdf_text_align: "center",
                textColor: [255, 255, 255],
                pdf_size: 10,
                fill: {
                  fill_color: [255, 133, 0],
                  y_value: 10,
                  top: 1,
                },
              },
              {
                type: "bold",
                compo: "Text",
                props: {
                  type: "bold",
                },
                pdf_text_align: "center",
                textColor: [255, 255, 255],
                pdf_size: 10,
                fill: {
                  fill_color: [255, 133, 0],
                  y_value: 8,
                  top: 3,
                },
              },
            ],
          },
          text_data: [
            {
              textData: ["Sensor Summary Report"],
            },
            {
              textData: [
                `From ${ctx.moment
                  .unix(fromTime)
                  .tz(timezone)
                  .format("DD MMM YYYY, HH:mm")} to ${ctx.moment
                  .unix(uptoTime)
                  .format("DD MMM YYYY, HH:mm")}`,
              ],
            },
            {
              textData: [
                `Generated on ${ctx.moment
                  .unix(ctx.moment().unix())
                  .tz(timezone)
                  .format("DD MMM YYYY, HH:mm")}`,
              ],
            },
          ],
        };
        downloadData.conf.push(ReportHeader.text_conf);
        downloadData.data.push(ReportHeader.text_data);
        function paramTotal(things, siteData, time, type, key, param) {
          const thingArr = things[type][key].map((thing) => {
            return thing.id;
          });
          let thingData = [];
          if (thingArr?.length) {
            thingArr.map((thingId) => {
              thingData = ctx["_"].filter(siteData, function (o) {
                return o.thing_id === thingId && o.time === time;
              });
            });
          }
          const sumValueOfParam = ctx["_"].sumBy(thingData, (item) =>
            ctx["_"].get(item, `parameter_values.${param}.sum`, 0),
          );
          return !isNaN(parseFloat(sumValueOfParam))
            ? parseFloat(sumValueOfParam).toFixed(2)
            : "-";
        }
        function totalValue(...values) {
          const total = values.reduce((acc, val) => {
            return acc + parseFloat(val);
          }, 0);
          return !isNaN(total) ? total.toFixed(2) : "-";
        }
        function hourMinSecConversion(seconds) {
          const hours = Math.floor(seconds / 3600);
          const minutes = Math.floor((seconds % 3600) / 60);
          const remainingSeconds = seconds % 60;
          return { hours, minutes, remainingSeconds };
        }
        const pad = (value) => (value < 10 ? `0${value}` : `${value}`);
        if (fetchedDataRes && Object.keys(fetchedDataRes)?.length) {
          Object.keys(fetchedDataRes).map((siteId, sinteInd) => {
            const findSite = ctx["_"].find(siteTabArray, {
              id: parseInt(siteId),
            });
            if (!siteDetailedData[siteId]) {
              siteDetailedData[siteId] = [];
            }
            if (!siteSummaryData[siteId]) {
              siteSummaryData[siteId] = {};
            }
            const totalMissionsAvl = [];
            if (fetchedDataRes[siteId]?.[1]?.length) {
              fetchedDataRes[siteId][1].map((task) => {
                if (task?.response?.Missions?.length) {
                  totalMissionsAvl.push(...task.response.Missions);
                }
              });
            }
            const getAggregation = {};
            if (totalAssetIds[siteId]?.length) {
              totalAssetIds[siteId].map((assetId) => {
                getAggregation[`${assetId}_dr_st`] = 0;
                getAggregation[`${assetId}_rnhr`] = 0;
                if (totalUniqParams[siteId]?.length) {
                  totalUniqParams[siteId].map((param) => {
                    getAggregation[`${assetId}_${param}`] = 0;
                  });
                }
              });
            }
            for (let time = uptoTime - 86399; time >= fromTime; time -= 86400) {
              const filterData = ctx["_"].filter(
                fetchedDataRes[siteId]?.[0]?.data,
                { time },
              );
              if (filterData?.length) {
                let filterTrips = [];
                siteDetailedData[siteId].push({
                  time: time,
                  date: ctx.moment
                    .unix(time)
                    .tz(timezone)
                    .format("DD MMM YYYY"),
                });
                filterData.map((data) => {
                  if (totalMissionsAvl?.length) {
                    filterTrips = ctx["_"].filter(totalMissionsAvl, (trip) => {
                      return (
                        trip.Devices.includes(data.thing_id) &&
                        ctx.moment
                          .unix(ctx.moment(trip.StartDate).unix())
                          .format("DD MMM YYYY") ===
                          ctx.moment.unix(data.time).format("DD MMM YYYY")
                      );
                    });
                  }
                  const findIndexOfData = ctx["_"].findIndex(
                    siteDetailedData[siteId],
                    { time: data.time },
                  );

                  let runhourSeconds = ctx["_"].sumBy(filterTrips, (trip) => {
                    return trip?.Details?.aggregate_data?.calculated_runhour
                      ?.sum;
                  });

                  if (data?.parameter_values) {
                    Object.keys(data.parameter_values).map((param) => {
                      getAggregation[`${data.thing_id}_${param}`] += parseFloat(
                        data?.parameter_values?.[param]?.avg,
                      );
                      if (client_id === 13853) {
                        runhourSeconds =
                          data?.parameter_values?.calculated_runhour?.sum;
                      }
                      siteDetailedData[siteId][findIndexOfData][
                        `${data.thing_id}_${param}`
                      ] = !isNaN(
                        parseFloat(data?.parameter_values?.[param]?.avg),
                      )
                        ? parseFloat(data.parameter_values[param].avg).toFixed(
                            2,
                          )
                        : "-";
                    });
                  }

                  getAggregation[`${data.thing_id}_rnhr`] += runhourSeconds;
                  const hours = hourMinSecConversion(runhourSeconds).hours;
                  const minutes = hourMinSecConversion(runhourSeconds).minutes;
                  const remainingSeconds =
                    hourMinSecConversion(runhourSeconds).remainingSeconds;
                  siteDetailedData[siteId][findIndexOfData][
                    `${data.thing_id}_dr_st`
                  ] = filterTrips.length > 0 || client_id === 13853 ? filterTrips.length : "-";
                  getAggregation[`${data.thing_id}_dr_st`] +=
                    filterTrips.length;
                  siteDetailedData[siteId][findIndexOfData][
                    `${data.thing_id}_rnhr`
                  ] = filterTrips?.length || client_id === 13853 
                    ? `${pad(hours)}:${pad(minutes)}:${pad(remainingSeconds)}`
                    : "-";
                });
              }
            }
            const tableDataWithoutAggr = JSON.parse(
              JSON.stringify(siteDetailedData[siteId]),
            );
            const aggrFinal = {};
            Object.keys(getAggregation).map((key) => {
              if (key.includes("rnhr")) {
                const hours = hourMinSecConversion(getAggregation[key]).hours;
                const minutes = hourMinSecConversion(
                  getAggregation[key],
                ).minutes;
                const remainingSeconds = hourMinSecConversion(
                  getAggregation[key],
                ).remainingSeconds;
                aggrFinal[`${key}_seconds_value`] = getAggregation[key];
                aggrFinal[key] =
                  `${pad(hours)}:${pad(minutes)}:${pad(remainingSeconds)}`;
              } else {
                if (!key.includes("dr_st")) {
                  aggrFinal[key] =
                    !isNaN(getAggregation[key]) &&
                    tableDataWithoutAggr.length > 0
                      ? parseFloat(
                          getAggregation[key] / tableDataWithoutAggr.length,
                        ).toFixed(2)
                      : "-";
                } else {
                  aggrFinal[key] = getAggregation[key];
                }
              }
            });
            aggrFinal["date"] = "Aggregation";
            siteDetailedData[siteId].push(aggrFinal);
            let sumDrSt = 0,
              sumSeconds = 0;
            for (const key in aggrFinal) {
              if (key.includes("_dr_st")) {
                sumDrSt += aggrFinal[key];
              }
              if (key.includes("rnhr_seconds_value")) {
                sumSeconds += aggrFinal[key];
              }
            }
            const hours = hourMinSecConversion(sumSeconds).hours;
            const minutes = hourMinSecConversion(sumSeconds).minutes;
            const remainingSeconds =
              hourMinSecConversion(sumSeconds).remainingSeconds;
            siteSummaryData[siteId] = [
              {
                parameter: "No of times the Doors Opened",
                value: sumDrSt,
              },
              {
                parameter: "Total Duration",
                value: `${pad(hours)}:${pad(minutes)}:${pad(remainingSeconds)}`,
              },
            ];
            if (isDownload) {
              let downloadSiteDataSource = [...downloadFirstDataSource[siteId]];
              downloadData.conf.push(
                textFunction(null, 13, true, sinteInd === 0 ? null : true, true)
                  .textProps,
              );
              downloadData.data.push(
                textFunction(["", `Site: ${findSite?.name}`]).textValue,
              );
              if (siteDetailedData[siteId]?.length) {
                downloadSiteDataSource = [
                  ...downloadSiteDataSource,
                  ...siteDetailedData[siteId],
                ];
                downloadData.conf.push(textFunction(null, 13, true).textProps);
                downloadData.data.push(
                  textFunction(["", `Summary Data`]).textValue,
                );
                if (
                  siteSummaryData[siteId] &&
                  Object.keys(siteSummaryData[siteId])?.length
                ) {
                  downloadData.conf.push({
                    pdf_force_new_page: false,
                    props: {
                      gutter: 10,
                      style: {},
                      className: "tableRow",
                    },
                    child: [
                      {
                        compo: "Table",
                        widget: "",
                        classname: "tab-1",
                        table_new_page: true,
                        hellipRow: true,
                        props: {
                          columns: [
                            { title: "Parameters", dataIndex: "parameter" },
                            { title: "Value", dataIndex: "value" },
                          ],
                          headerFont: 13,
                          size: "small",
                          tabRadius: 0,
                          horizontalScroll: true,
                          shadow: false,
                          breakPoint: 1000,
                          breakPoint2: 500,
                          largeTable: true,
                          mediumTable: false,
                          smallTable: false,
                        },
                        col_props: {
                          span: 24,
                        },
                        pdf_width: 50,
                        pdf_table_break: {
                          col_no: 12,
                          row_no: 20,
                        },
                      },
                    ],
                  });
                  downloadData.data.push([siteSummaryData[siteId]]);
                  downloadData.conf.push(
                    textFunction(null, 13, true).textProps,
                  );
                  downloadData.data.push(
                    textFunction(["", "", "", ""]).textValue,
                  );
                } else {
                  downloadData.conf.push(
                    textFunction(null, 12, true).textProps,
                  );
                  downloadData.data.push(
                    textFunction(["", "No summary available"]),
                  );
                }
              }
              downloadData.conf.push(
                textFunction(null, 13, true, true).textProps,
              );
              downloadData.data.push(
                textFunction(["", "Detailed Data"]).textValue,
              );
              downloadData.conf.push({
                pdf_force_new_page: false,
                props: {
                  gutter: 10,
                  style: {},
                  className: "tableRow",
                },
                child: [
                  {
                    compo: "Table",
                    widget: "",
                    classname: "tab-1",
                    table_new_page: true,
                    shouldColBreak: true,
                    props: {
                      columns: downloadColumn[siteId],
                      headerFont: 13,
                      size: "small",
                      tabRadius: 0,
                      horizontalScroll: true,
                      shadow: false,
                      breakPoint: 1000,
                      breakPoint2: 500,
                      largeTable: true,
                      mediumTable: false,
                      smallTable: false,
                    },
                    col_props: {
                      span: 24,
                    },
                    pdf_width: 50,
                    pdf_table_break: {
                      col_no: 5,
                      row_no: 20,
                    },
                  },
                ],
              });

              downloadData.data.push([downloadSiteDataSource]);
            }
          });
        }
        downloadData.file_name = "Sensor Summary Report";
        return {
          viewData: { siteSummaryData, siteDetailedData },
          downloadData,
        };
      }
      let siteFinalData, siteDownloadData;
      siteFinalData = tableData()?.viewData;
      if (isDownload) {
        siteDownloadData = tableData()?.downloadData;
      }
      cb({ siteFinalData, siteDownloadData });
    },
    cb: (value) => {
      this.setState(
        {
          siteDownloadData: value.siteDownloadData,
          ...(value.siteFinalData &&
            !isDownload && { siteFinalData: value.siteFinalData }),
          pageLoading: false,
          loading: false,
        },
        () => {
          if (isDownload) {
            this.getDownloadRender();
          }
        },
      );
    },
  });
}
