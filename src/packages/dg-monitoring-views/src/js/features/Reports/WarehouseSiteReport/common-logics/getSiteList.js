import { retrieveSitesList, retriveThingsList, getSiteTypes } from '@datoms/js-sdk';
import _find from 'lodash/find';
import AntNotification from '@datoms/react-components/src/components/AntNotification';
import AntMessage from '@datoms/react-components/src/components/AntMessage';
import { flushSync } from 'react-dom';

export function openNotification(type, msg) {
	if (window.innerWidth > 576) {
		AntNotification({
			type: type,
			message: msg,
			placement: 'bottomLeft',
			className: 'alert-' + type,
		});
	} else {
		AntMessage(type, msg);
	}
}

export async function callThingsList() {
	const { client_id, application_id } = this.props;
	const thingsDetails = await retriveThingsList(
		{
			client_id: client_id,
			application_id: application_id,
		},
	);
	flushSync(()=>{
		this.setState({
			thingsDetails,
		});
	});
}

export async function callSitesList(siteTypes) {
	const { client_id } = this.props;
	const [siteListResponse, siteTypeResponse] = await Promise.all([
		retrieveSitesList(client_id,`?page_no=${1}&results_per_page=${1000}`),
		getSiteTypes(client_id),
	]);
	if (siteListResponse?.status !== 'success') {
		this.openNotification('error', siteListResponse?.message);
		return;
	}
	const siteList = siteListResponse?.data;
	const siteTabArray = [], selectedSiteArray = [];
	if (siteList?.length) {
		siteList.map((list) => {
			if(siteTypes.includes(list.site_type)) {
				siteTabArray.push({ 
					id: list.id, 
					name: list.name,
					type: _find(siteTypeResponse?.data, { id: list.site_type })?.name,
				});
				selectedSiteArray.push(list.id)
			}
		});
	}
	const selectedSite = selectedSiteArray.length > 0 ? [selectedSiteArray[0]] : null;
	flushSync(()=>{
		this.setState({
			siteTabArray,
			selectedSite,
			selectedSiteArray,
		});
	});
}
