import React from "react";
import Backyard from "@datoms/js-utils/src/Backyard/Backyard_back";
import { retriveThingsList } from "@datoms/js-sdk";
import moment from "moment-timezone";
import { filteredFuelTankThings } from "../../../../../data_handling/FuelTankThing";
import { filterDginIot } from "../../../../../data_handling/DGinIot";
import _find from "lodash/find";
import _remove from "lodash/remove";
import _filter from "lodash/filter";
import Loading from "@datoms/react-components/src/components/Loading";
import TableList from "@datoms/react-components/src/components/TableList";
import ReportController from "@datoms/react-components/src/components/ReportController";
import AntTabs from "@datoms/react-components/src/components/AntTabs";
import AntTabPane from "@datoms/react-components/src/components/AntTabPane";
import GraphObjectData from "../../../../../configuration/GraphObjectData";
import ReportsHeader from "../../components/reports-header";
import RunhourIcon from "./assets/Group 148.svg";
import FuelIcon from "./assets/Group 945.svg";
import EnrgIcon from "./assets/Group 946.svg";
import DownloadModal from "../../components/download-modal";
import CustomizeDrawer from "../../components/customize-drawer";
import "./lifetime-report.less";
import { getBaseUrl } from "@datoms/js-utils/src/base-url-logic";
import ScheduleDrawer from "../../../components/ScheduleDrawer";
import { findDownload } from "../../../data_handling/FindDownloadInUrl";
import { getDownloadFileName } from "../../../data_handling/downloadFilename";
import { getThingsAndParameterData } from "../../../../../data_handling/thingsListManipulation";

export default class LifetimeReport extends React.Component {
  downloadModalRef = React.createRef();
  invisibleReportRef = React.createRef();
  customizeDrawerRef = React.createRef();
  scheduleDrawerRef = React.createRef();
  constructor(props) {
    GraphObjectData.graph_data.config.timezone =
      props.user_preferences.timezone;
    super(props);
    this.state = {
      scheduleDrawerVisible: false,
      loading: true,
      downloadData: undefined,
      from_time: moment().subtract(30, "days").startOf("day").unix(),
      upto_time: moment().endOf("day").unix(),
      isDownloadInUrl: findDownload(props?.history),
    };
    this.onExportReady = this.onExportReady(this);
    this.headerNameKey = [];
  }

  async getInputForDownload() {
    const { isDownloadInUrl } = this.state;
    if (typeof window.getInputDataSet === "function") {
      let inputDataSet = await window.getInputDataSet();
      if (isDownloadInUrl) {
        this.setState({
          inputDataSet: inputDataSet,
        });
      }
    }
  }

  scheduleBtnClicked() {
    this.setState(
      {
        scheduleDrawerVisible: true,
      },
      () => {
        this.scheduleDrawerRef.current.showScheduleDrawer();
      },
    );
  }

  scheduleDrawerClosed() {
    this.setState({
      scheduleDrawerVisible: false,
    });
  }

  async componentDidMount() {
    await this.getInputForDownload();
    await this.getThingsDataFunc();
  }
  isGmmco() {
    if (parseInt(this.props.vendor_id) === 1062) {
      return true;
    } else {
      return false;
    }
  }
  async getThingsDataFunc(customOptions) {
    this.headerNameKey = [
      {
        title: "Assets",
        dataIndex: "dgs",
        sorter: (a, b) => a.dgs.localeCompare(b.dgs),
      },
      {
        title: "Runhour " + (this.isGmmco() ? "(SMU)" : "(HH:MM:SS)"),
        dataIndex: "rnhr",
        sorter: (a, b) =>
          a.rnhr.split(":").join("") - b.rnhr.split(":").join(""),
      },
    ];
    const { inputDataSet } = this.state;
    let totalData = await retriveThingsList({
      client_id: this.props.client_id,
      application_id: this.props.application_id,
    });
    totalData = filterDginIot.bind(this)(totalData, "reports");

    let filteredThingWithFuelTankThingIds = filteredFuelTankThings(
      totalData,
      71,
    ).filteredThingWithFuelTankThingIds;
    let filteredThing = _filter(totalData.things, function (o) {
      return [18, 96].includes(o.category);
    });
    totalData["things"] = filteredThing;
    let modifiedResponse = getThingsAndParameterData(totalData);
    const isFuel =
      modifiedResponse?.param_key_data?.includes("fuel") ||
      modifiedResponse?.param_key_data?.includes("fuel_lt") ||
      modifiedResponse?.param_key_data?.includes("fuel_litre");
    if (isFuel) {
      this.headerNameKey.push(
        {
          title: "Fuel Consumed (L)",
          dataIndex: "fuel_cons",
          sorter: (a, b) => a.fuel_cons - b.fuel_cons,
        },
        {
          title: "Energy Generated (kWh)",
          dataIndex: "enrg",
          sorter: (a, b) => a.enrg - b.enrg,
        },
      );
    } else {
      this.headerNameKey.push({
        title: "Energy Generated (kWh)",
        dataIndex: "enrg",
        sorter: (a, b) => a.enrg - b.enrg,
      });
    }
    if (
      filteredThingWithFuelTankThingIds &&
      filteredThingWithFuelTankThingIds.length
    ) {
      filteredThingWithFuelTankThingIds.map((thing_id) => {
        _remove(totalData.things, { id: thing_id });
      });
    }
    let fromTime =
      customOptions && customOptions.from_time
        ? customOptions.from_time
        : this.state.from_time;
    let uptoTime =
      customOptions && customOptions.upto_time
        ? customOptions.upto_time
        : this.state.upto_time;
    let totalThings =
      customOptions && customOptions.things_ids
        ? customOptions.things_ids
        : totalData.things;
    let selectedMake =
      customOptions && customOptions.select_make
        ? customOptions.select_make
        : "All";
    let selectedKva =
      customOptions && customOptions.select_kva
        ? customOptions.select_kva
        : "All";
    let timeZone = this.props.user_preferences.timezone;
    let timeFormat = this.props.user_preferences?.time_format;
    let isGmmco = this.isGmmco();
    let client_name = this.props.client_name;
    new Backyard({
      /* eslint-disable no-unused-expressions */
      scripts: [
        "https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js",
        "https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.23/moment-timezone.min.js",
        "https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.33/moment-timezone-with-data-1970-2030.min.js",
        "https://cdn.jsdelivr.net/npm/lodash@4.17.15/lodash.min.js",
      ],
      input: {
        totalThings: totalThings,
        GraphObjectData: GraphObjectData,
        totalData: totalData,
        selectedMake: selectedMake,
        selectedKva: selectedKva,
        timeZone: timeZone,
        timeFormat: timeFormat,
        fromTime: fromTime,
        uptoTime: uptoTime,
        isGmmco: isGmmco,
        client_name: client_name,
        inputDataSet: inputDataSet,
        isFuel: isFuel,
      },
      run: function (ctx, input, cb) {
        let totalData = input.totalData;
        let totalThings = input.totalThings;
        let timeZone = input.timeZone;
        let timeFormat = input.timeFormat;
        let selectedMake = input.selectedMake;
        let inputDataSet = input.inputDataSet;
        let selectedKva = input.selectedKva;
        let fromTime = input.fromTime;
        let uptoTime = input.uptoTime;
        let client_name = input.client_name;
        let isGmmco = input.isGmmco;
        const isFuel = input.isFuel;
        let dataObj = [];
        let lifetimeRnhr = {},
          lifetimeFuelConsumption = {},
          lifetimeEnrg = {},
          lifetimeDistance = {},
          totalLifetimeRnhr = 0,
          totalLifetimeHr = 0,
          totalLifetimeFuelCons = 0,
          totalLifetimeEnrg = 0,
          totalLifetimeDistance = 0,
          downloadData = { conf: [], data: [] };
        let hourMinVariable = timeFormat === "12_hr" ? "hh:mm A" : "HH:mm";
        if (totalThings && totalThings && totalThings.length) {
          totalThings.map(function (things, ind) {
            if (!lifetimeRnhr[things.id]) {
              lifetimeRnhr[things.id] = 0;
            }
            if (!lifetimeFuelConsumption[things.id]) {
              lifetimeFuelConsumption[things.id] = 0;
            }
            if (!lifetimeEnrg[things.id]) {
              lifetimeEnrg[things.id] = 0;
            }
            if (!lifetimeDistance[things.id]) {
              lifetimeDistance[things.id] = 0;
            }
            if (
              ctx["_"].find(things.parameters, { key: "rnhr" }) &&
              ctx["_"].find(things.parameters, { key: "rnhr" }).value !== "" &&
              parseFloat(
                ctx["_"].find(things.parameters, {
                  key: "rnhr",
                }).value,
              ) > 0
            ) {
              lifetimeRnhr[things.id] =
                parseFloat(
                  ctx["_"].find(things.parameters, {
                    key: "rnhr",
                  }).value,
                ) * 3600;
            } else if (
              ctx["_"].find(things.parameters, {
                key: "calculated_runhour",
              }) &&
              ctx["_"].find(things.parameters, {
                key: "calculated_runhour",
              }).aggregated_value &&
              ctx["_"].find(things.parameters, {
                key: "calculated_runhour",
              }).aggregated_value.lifetime &&
              ctx["_"].find(things.parameters, {
                key: "calculated_runhour",
              }).aggregated_value.lifetime
            ) {
              lifetimeRnhr[things.id] = ctx["_"].find(things.parameters, {
                key: "calculated_runhour",
              }).aggregated_value.lifetime.sum;
            }
            let hours =
              Math.floor(lifetimeRnhr[things.id] / 3600) < 10
                ? "0" + Math.floor(lifetimeRnhr[things.id] / 3600)
                : Math.floor(lifetimeRnhr[things.id] / 3600);
            let reminderRnhr = {};
            if (!reminderRnhr[things.id]) {
              reminderRnhr[things.id] = 0;
            }
            reminderRnhr[things.id] = lifetimeRnhr[things.id] % 3600;
            let minutes =
              Math.floor(reminderRnhr[things.id] / 60) < 10
                ? "0" + Math.floor(reminderRnhr[things.id] / 60)
                : Math.floor(reminderRnhr[things.id] / 60);
            let seconds =
              Math.floor(reminderRnhr[things.id] % 60) < 10
                ? "0" + Math.floor(reminderRnhr[things.id] % 60)
                : Math.floor(reminderRnhr[things.id] % 60);
            if (
              ctx["_"].find(things.parameters, {
                key: "fuel_consumption",
              }) &&
              ctx["_"].find(things.parameters, {
                key: "fuel_consumption",
              }).aggregated_value &&
              ctx["_"].find(things.parameters, {
                key: "fuel_consumption",
              }).aggregated_value.lifetime &&
              ctx["_"].find(things.parameters, {
                key: "fuel_consumption",
              }).aggregated_value.lifetime
            ) {
              lifetimeFuelConsumption[things.id] = ctx["_"].find(
                things.parameters,
                { key: "fuel_consumption" },
              ).aggregated_value.lifetime.sum;
            }
            if (
              ctx["_"].find(things.parameters, { key: "enrg" }) &&
              ctx["_"].find(things.parameters, { key: "enrg" }).value !== "" &&
              parseFloat(
                ctx["_"].find(things.parameters, {
                  key: "enrg",
                }).value,
              ) > 0
            ) {
              lifetimeEnrg[things.id] = ctx["_"].find(things.parameters, {
                key: "enrg",
              }).value;
            } else if (
              ctx["_"].find(things.parameters, {
                key: "calculated_energy",
              }) &&
              ctx["_"].find(things.parameters, {
                key: "calculated_energy",
              }).aggregated_value &&
              ctx["_"].find(things.parameters, {
                key: "calculated_energy",
              }).aggregated_value.lifetime &&
              ctx["_"].find(things.parameters, {
                key: "calculated_energy",
              }).aggregated_value.lifetime
            ) {
              lifetimeEnrg[things.id] = ctx["_"].find(things.parameters, {
                key: "calculated_energy",
              }).aggregated_value.lifetime.sum;
            }
            if (
              ctx["_"].find(things.parameters, {
                key: "lifetime_distance",
              }) &&
              ctx["_"].find(things.parameters, {
                key: "lifetime_distance",
              }).value !== "" &&
              parseFloat(
                ctx["_"].find(things.parameters, {
                  key: "lifetime_distance",
                }).value,
              ) > 0
            ) {
              lifetimeDistance[things.id] = ctx["_"].find(things.parameters, {
                key: "lifetime_distance",
              }).value;
            }
            dataObj.push({
              dgs: things.name,
              total_rnhr: lifetimeRnhr[things.id],
              rnhr: hours + ":" + minutes + ":" + seconds,
              fuel_cons: !isNaN(parseFloat(lifetimeFuelConsumption[things.id]))
                ? parseFloat(lifetimeFuelConsumption[things.id]).toFixed(2)
                : "-",
              enrg: !isNaN(parseFloat(lifetimeEnrg[things.id]))
                ? parseFloat(lifetimeEnrg[things.id]).toFixed(2)
                : "-",
              distance: !isNaN(parseInt(lifetimeDistance[things.id]))
                ? parseInt(lifetimeDistance[things.id])
                : "-",
            });
          });
          totalLifetimeRnhr = ctx["_"].sumBy(dataObj, function (o) {
            if (o.total_rnhr) {
              return parseFloat(o.total_rnhr);
            } else {
              return 0;
            }
          });
          let hours =
            Math.floor(totalLifetimeRnhr / 3600) < 10
              ? "0" + Math.floor(totalLifetimeRnhr / 3600)
              : Math.floor(totalLifetimeRnhr / 3600);
          let reminderRnhr = 0;
          reminderRnhr = totalLifetimeRnhr % 3600;
          let minutes =
            Math.floor(reminderRnhr / 60) < 10
              ? "0" + Math.floor(reminderRnhr / 60)
              : Math.floor(reminderRnhr / 60);
          let seconds =
            Math.floor(reminderRnhr % 60) < 10
              ? "0" + Math.floor(reminderRnhr % 60)
              : Math.floor(reminderRnhr % 60);
          totalLifetimeHr = hours + ":" + minutes + ":" + seconds;
          totalLifetimeFuelCons = ctx["_"].sumBy(dataObj, function (o) {
            return parseFloat(o.fuel_cons);
          });

          totalLifetimeEnrg = ctx["_"].sumBy(dataObj, function (o) {
            return parseFloat(o.enrg);
          });

          totalLifetimeDistance = ctx["_"].sumBy(dataObj, function (o) {
            return parseFloat(o.distance);
          });
          //pdf data creation

          let pdfMainHeaderOption = {
            // pdf_top_line: true,
            // pdf_bottom_line: true,
            pdf_text_align: "center",
            textColor: [255, 255, 255],
            pdf_size: 10,
            fill: {
              fill_color: [255, 133, 0],
              y_value: 13,
              top: 1,
            },
          };
          let pushObj = pdfMainHeaderOption;
          pushObj["compo"] = "Text";
          pushObj["props"] = {
            type: "bold",
          };
          pushObj["col_props"] = {
            span: 24,
          };
          let REPORT_TYPE = {
            text_conf: {
              props: {
                gutter: 5,
              },
              child: [
                {
                  pdf_text_align: "center",
                  textColor: [255, 255, 255],
                  pdf_size: 16,
                  type: "bold",
                  fill: {
                    fill_color: [255, 133, 0],
                    y_value: 24,
                    top: 8,
                  },
                  compo: "Text",
                  props: {
                    type: "bold",
                  },
                  col_props: {
                    span: 24,
                  },
                  secStyle: {
                    body: {
                      font: {
                        size: 20,
                        bold: true,
                      },
                    },
                  },
                },
                pushObj,
              ],
            },
            text_data: [
              {
                textData: ["Lifetime Report - " + client_name],
              },
              {
                textData: [
                  "Generated on: " +
                    ctx.moment
                      .unix(ctx.moment().tz(timeZone).unix())
                      .format("DD MMM YYYY, " + hourMinVariable),
                  "* All the times are in " +
                    (timeFormat === "12_hr" ? "12" : "24") +
                    " Hours time format",
                  "",
                ],
              },
            ],
          };
          downloadData.conf.push(REPORT_TYPE.text_conf);
          downloadData.data.push(REPORT_TYPE.text_data);
          let pageConfigInsight = {
            pdf_force_new_page: false,
          };
          let textPushInsight = pageConfigInsight;
          textPushInsight["compo"] = "Text";
          textPushInsight["type"] = "bold";

          textPushInsight["col_props"] = {
            span: 24,
          };
          textPushInsight["pdf_size"] = 12;
          let { text_conf_insight, text_data_insight } = {
            text_conf_insight: {
              props: {
                gutter: 10,
              },
              child: [textPushInsight],
            },
            text_data_insight: [
              {
                textData: ["", "Insights"],
              },
            ],
          };
          downloadData.conf.push(text_conf_insight);
          downloadData.data.push(text_data_insight);
          let SummaryData = [
            {
              parameter: "Total Runhour",
              value: totalLifetimeHr + (isGmmco ? " SMU" : " hrs"),
            },
          ];
          if (isFuel) {
            SummaryData.push(
              {
                parameter: "Total Fuel Consumption",
                value: parseFloat(totalLifetimeFuelCons).toFixed(2) + " L",
              },
              {
                parameter: "Total Energy Generated",
                value: parseFloat(totalLifetimeEnrg).toFixed(2) + " kWh",
              },
            );
          } else {
            SummaryData.push({
              parameter: "Total Energy Generated",
              value: parseFloat(totalLifetimeEnrg).toFixed(2) + " kWh",
            });
          }

          downloadData.conf.push({
            pdf_force_new_page: false,
            props: {
              gutter: 10,
              style: {},
              className: "tableRow",
            },
            child: [
              {
                compo: "Table",
                widget: "",
                classname: "tab-1",
                table_new_page: true,
                props: {
                  columns: [
                    {
                      title: "Parameter",
                      dataIndex: "parameter",
                    },
                    {
                      title: "Value",
                      dataIndex: "value",
                    },
                  ],
                  headerFont: 13,
                  size: "small",
                  tabRadius: 0,
                  horizontalScroll: true,
                  shadow: false,
                  breakPoint: 1000,
                  breakPoint2: 500,
                  largeTable: true,
                  mediumTable: false,
                  smallTable: false,
                },
                col_props: {
                  span: 24,
                },
                pdf_width: 50,
                pdf_table_break: {
                  col_no: 9,
                  row_no: 20,
                },
              },
            ],
          });
          downloadData.data.push([SummaryData]);
          let pageConfigDetailed = {
            pdf_force_new_page: true,
          };
          let textPushDetailed = pageConfigDetailed;
          textPushDetailed["compo"] = "Text";
          textPushDetailed["type"] = "bold";

          textPushDetailed["col_props"] = {
            span: 24,
          };
          textPushDetailed["pdf_size"] = 12;
          let { text_conf_detailed, text_data_detailed } = {
            text_conf_detailed: {
              props: {
                gutter: 10,
              },
              child: [textPushDetailed],
            },
            text_data_detailed: [
              {
                textData: ["Detailed data"],
              },
            ],
          };
          downloadData.conf.push(text_conf_detailed);
          downloadData.data.push(text_data_detailed);
          let headerNameKey = [
            {
              title: "Assets",
              dataIndex: "dgs",
            },
            {
              title: "Runhour " + (isGmmco ? "(SMU)" : "(HH:MM:SS)"),
              dataIndex: "rnhr",
            },
          ];
          if (isFuel) {
            headerNameKey.push(
              {
                title: "Fuel Consumed (L)",
                dataIndex: "fuel_cons",
              },
              {
                title: "Energy Generated (kWh)",
                dataIndex: "enrg",
              },
            );
          } else {
            headerNameKey.push({
              title: "Energy Generated (kWh)",
              dataIndex: "enrg",
            });
          }

          downloadData.conf.push({
            props: {
              gutter: 10,
              style: {},
              className: "tableRow",
            },
            child: [
              {
                compo: "Table",
                widget: "",
                classname: "tab-1",
                table_new_page: true,
                hellipRow: true,
                props: {
                  columns: headerNameKey,
                  headerFont: 13,
                  size: "small",
                  tabRadius: 0,
                  horizontalScroll: true,
                  shadow: false,
                  breakPoint: 1000,
                  breakPoint2: 500,
                  largeTable: true,
                  mediumTable: false,
                  smallTable: false,
                },
                col_props: {
                  span: 24,
                },
                pdf_width: 50,
                pdf_table_break: {
                  col_no: 12,
                  row_no: 20,
                },
              },
            ],
          });
          downloadData.data.push([dataObj]);
          downloadData.file_name = inputDataSet?.name || "Lifetime report";

          //---------------------------------------//
        }
        cb({
          total_data: totalData,
          data_source: dataObj,
          downloadData: downloadData,
          selected_make: selectedMake,
          selected_kva: selectedKva,
          total_lifetime_hr: totalLifetimeHr,
          total_lifetime_enrg: totalLifetimeEnrg,
          total_lifetime_fuel_cons: totalLifetimeFuelCons,
          total_lifetime_distance: totalLifetimeDistance,
          isFuel: isFuel,
        });
      },
      cb: (value) => {
        this.setState(
          {
            header: value.header,
            data_source: value.data_source,
            downloadData: value.downloadData,
            total_data: value.total_data,
            selected_make: value.selected_make,
            selected_kva: value.selected_kva,
            total_lifetime_hr: value.total_lifetime_hr,
            total_lifetime_enrg: value.total_lifetime_enrg,
            total_lifetime_fuel_cons: value.total_lifetime_fuel_cons,
            total_lifetime_distance: value.total_lifetime_distance,
            is_fleet: value.is_fleet,
            is_ev: value.is_ev,
            isFuel: value.isFuel,
          },
          () => {
            this.getDownloadRender();
          },
        );
      },
    });
  }

  getDownloadRender() {
    const { inputDataSet } = this.state;
    let downloadRender = this.state.downloadData ? (
      <div
        style={{
          opacity: 0,
          visibility: "hidden",
          overflow: "hidden",
          "max-height": 0,
        }}
      >
        <ReportController
          is_white_label={this.props.is_white_label}
          vendor_name={this.props.vendor_name}
          ref={this.invisibleReportRef}
          onExportReady={this.onExportReady}
          key={moment().unix()}
          {...this.state.downloadData}
          parameters={[]}
        />
      </div>
    ) : (
      ""
    );
    this.setState(
      {
        download_render: downloadRender,
        loading: false,
      },
      () => {
        if (inputDataSet) {
          setTimeout(() => {
            this.downloadModalCallback(inputDataSet?.type);
            if (typeof window.reportGenerationCompleted === "function") {
              window.reportGenerationCompleted(
                getDownloadFileName(
                  inputDataSet?.name ?? "Lifetime report",
                  inputDataSet?.type,
                ),
              );
            }
          }, 2000);
        }
      },
    );
  }

  downloadBtnClicked() {
    this.downloadModalRef.current.showModal();
  }

  downloadModalCallback(fileFormat) {
    if (this.state.downloadData) {
      if (fileFormat.includes("csv")) {
        this.invisibleReportRef.current.exportCSV(null, {});
      }

      if (fileFormat.includes("xls")) {
        this.invisibleReportRef.current.exportXLSX(null, {
          maxCellMergeCount: 20,
        });
      }

      if (fileFormat.includes("pdf")) {
        this.invisibleReportRef.current.exportPDF({
          header: {
            left: {
              text: "",
              fontType: "italics",
            },
            right: {
              text: "",
            },
          },
        });
      }
    }
  }

  onExportReady() {
    let { autoDownloadFormat } = this.props;
    if (autoDownloadFormat) {
      this.downloadModalCallback(autoDownloadFormat);
    }
  }

  goBackPage() {
    this.props.history.push(getBaseUrl(this.props, "reports"));
  }

  onRangeChange(e) {
    this.setState(
      {
        from_time: moment.unix(e[0]).startOf("day").unix(),
        upto_time: moment.unix(e[1]).endOf("day").unix(),
        loading: true,
      },
      async () => {
        await this.getThingsDataFunc();
      },
    );
  }

  onOpenChange(e) {
    console.log("oopppp", e);
  }

  customDrawerCallback(e) {
    this.setState(
      {
        loading: true,
        from_time: e.from_time,
        upto_time: e.upto_time,
      },
      () => {
        this.getThingsDataFunc(e);
      },
    );
  }

  customizeBtnClicked() {
    this.customizeDrawerRef.current.showDrawer();
  }

  render() {
    const { inputDataSet } = this.state;
    if (
      this.state.total_data &&
      (this.state.total_data.status !== "success" ||
        (this.state.total_data.things &&
          this.state.total_data.things.length === 0))
    ) {
      return <div className="no-data-text">No Asset Found</div>;
    } else if (this.state.loading) {
      return <Loading />;
    } else {
      let reportsHeader = (
        <ReportsHeader
          goBackPage={() => this.goBackPage()}
          from_time={this.state.from_time}
          upto_time={this.state.upto_time}
          things_list={this.state.totalData}
          onRangeChange={(e) => this.onRangeChange(e)}
          onOpenChange={(e) => this.onOpenChange(e)}
          downloadBtnClicked={() => this.downloadBtnClicked()}
          customizeBtnClicked={() => this.customizeBtnClicked()}
          report_type_title="Lifetime Report"
          date_selection={false}
          scheduleBtnClicked={() => this.scheduleBtnClicked()}
          isScheduledReport={this.props.isScheduledReport}
          {...this.props}
          disabledDownload={
            this.props.application_id === 16 &&
            this.props.logged_in_user_client_id === this.props.client_id &&
            !this.props.getViewAccess(["Reports:Download"])
          }
        />
      );
      let summaryData = (
        <div className="summary">
          <div className="header">Insights</div>
          <div className="total-max-data">
            <div className="values">
              <div className="image">
                <img src={RunhourIcon} />
              </div>
              <div className="data">
                Total Runhour
                <div>
                  {this.state.total_lifetime_hr +
                    (this.isGmmco() ? " SMU" : " hrs")}
                </div>
              </div>
            </div>
            {this.state.is_ev || !this.state.isFuel ? (
              ""
            ) : (
              <div className="values">
                <div className="image">
                  <img src={FuelIcon} />
                </div>
                <div className="data">
                  Total Fuel consumed
                  <div>
                    {parseFloat(this.state.total_lifetime_fuel_cons).toFixed(
                      2,
                    ) + " L"}
                  </div>
                </div>
              </div>
            )}
            {this.state.is_fleet || this.state.is_ev ? (
              <div className="values">
                <div className="image">
                  <img src={EnrgIcon} />
                </div>
                <div className="data">
                  Total Distance
                  <div>
                    {parseInt(this.state.total_lifetime_distance) + " Km"}
                  </div>
                </div>
              </div>
            ) : (
              <div className="values">
                <div className="image">
                  <img src={EnrgIcon} />
                </div>
                <div className="data">
                  Total Energy Generated
                  <div>
                    {parseFloat(this.state.total_lifetime_enrg).toFixed(2) +
                      " kWh"}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      );
      let detailedData = (
        <div className="detailed-data">
          <div className="header">Detailed data</div>
          <TableList
            columns={this.headerNameKey}
            dataSource={this.state.data_source}
          />
        </div>
      );
      let mobileView = (
        <>
          <AntTabs defaultActiveKey="1" centered>
            <AntTabPane tab="Insights" key="1">
              {summaryData}
            </AntTabPane>
            <AntTabPane tab="Detailed data" key="2">
              {detailedData}
            </AntTabPane>
          </AntTabs>
        </>
      );
      return inputDataSet ? (
        <>{this.state.download_render}</>
      ) : (
        <div id="lifetime_summary_report">
          <div className="report-container">
            {reportsHeader}
            {window.innerWidth <= 1024 ? (
              mobileView
            ) : (
              <>
                {summaryData}
                {detailedData}
              </>
            )}
            <DownloadModal
              ref={this.downloadModalRef}
              callback={(e) => this.downloadModalCallback(e)}
              label="Lifetime"
            />
            {this.state.download_render}
            <CustomizeDrawer
              ref={this.customizeDrawerRef}
              callback={(e) => this.customDrawerCallback(e)}
              from_time={this.state.from_time}
              upto_time={this.state.upto_time}
              things_list={this.state.total_data}
              selected_make={this.state.selected_make}
              selected_kva={this.state.selected_kva}
              date_selection={false}
              {...this.props}
            />
            {this.state.scheduleDrawerVisible && (
              <ScheduleDrawer
                from_time={this.state.from_time}
                upto_time={this.state.upto_time}
                ref={this.scheduleDrawerRef}
                scheduleDrawerClosed={() => this.scheduleDrawerClosed()}
                selected_things={this.state.total_data.things.map((things) => {
                  return things.id;
                })}
                all_things_list={this.state.total_data}
                report_type="Summary Lifetime report"
                client_id={this.props.client_id}
                parent_client_id={this.props.parent_client_id}
                parent_application_id={this.props.parent_application_id}
                app_id={this.props.application_id}
              />
            )}
          </div>
        </div>
      );
    }
  }
}
