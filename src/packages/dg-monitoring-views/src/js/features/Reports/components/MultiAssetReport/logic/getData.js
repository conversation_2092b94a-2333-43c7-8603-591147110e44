import { retrieveSitesList, getThingsData } from "@datoms/js-sdk";
import Backyard from "@datoms/js-utils/src/Backyard/Backyard_back";
import _filter from "lodash/filter";
import _uniqBy from "lodash/uniqBy";
import GraphObjectData from "../../../../../configuration/GraphObjectData";
import { flushSync } from "react-dom";

export async function totalDataApisCalled(isDownload, checkboxChecked) {
  await Promise.all([
    checkboxChecked ? null : this.getSummaryData(),
    this.getAllData(),
    // this.getSiteDetails(),
  ]).then(() => {
    this.getRenderDownloadData(isDownload);
  });
}

export async function getSiteDetails(client_id, enabled_features) {
  let thingSiteIdName = {};
  if (!enabled_features?.includes("SiteManagement:SiteManagement")) {
    return thingSiteIdName;
  }
  const siteListResponse = await retrieveSitesList(
    client_id,
    `?page_no=${1}&results_per_page=${1000}`,
  );
  if (siteListResponse?.status !== "success") {
    this.openNotification("error", siteDetail?.message);
    return {};
  } else {
    siteListResponse?.data?.forEach((site) => {
      thingSiteIdName[site.id] = site.name;
    });
  }
  return thingSiteIdName;
}

export async function getSummaryData() {
  const { client_id, application_id, multiAssetReportConfig, categories } =
    this.props;
  const { fromTime, uptoTime, aggrPeriod } = this.state;
  const summaryParams = multiAssetReportConfig?.summaryParams;
  const summaryDataPacketTimeWise = {
    data_type: "aggregate",
    aggregation_period: aggrPeriod,
    things: [],
    from_time: fromTime,
    upto_time: uptoTime,
    categories,
    summarize: [
      {
        query: [
          ...summaryParams.map(
            (param) => `${param.data_aggr}(${param.value}.${param.data_aggr})`,
          ),
        ],
        group_by: "time",
      },
    ],
  };
  const summaryData =
    summaryParams?.length &&
    (await getThingsData(summaryDataPacketTimeWise, client_id, application_id));
  flushSync(() =>
    this.setState({
      summaryData: summaryData?.summary?.length
        ? summaryData.summary[0].data
        : [],
    }),
  );
}

export async function getAllData() {
  const {
    client_id,
    application_id,
    multiAssetReportConfig,
    thingIdsArray,
    categories,
    enabled_features,
    thingsDetails:thing_list,
  } = this.props;
  const { fromTime, uptoTime, checkedParams, aggrPeriod } = this.state;
  const selectedParams = multiAssetReportConfig?.detailedParams;
  const tableParamsToShow = _filter(selectedParams, function (o) {
    return checkedParams?.includes(o.value);
  });

  if (this.props?.enabled_features.includes("SiteManagement:SiteManagement")) {
    let thingWithSite = 0;
    if (thing_list?.things?.length) {
      thing_list.things.forEach((thing) => {
        if (thing.site_id !== 0) {
          thingWithSite += 1;
        }
      });
    }
    if (thingWithSite > 0) {
      this.setState({
        siteEnabled: true,
      });
    }
  }

  const dataPacket = {
    data_type: "aggregate",
    aggregation_period: aggrPeriod,
    things: thingIdsArray,
    parameters: checkedParams
      .filter((param) => !param.includes("exceeded"))
      .map((param) => (param === "pm2#5" ? "pm2.5" : param)),
    parameter_attributes: _uniqBy([
      ...tableParamsToShow.map((param) => `${param.data_aggr}`),
    ]),
    from_time: fromTime,
    upto_time: uptoTime,
  };
  const dataPacketThingWiseSum = {
    data_type: "aggregate",
    aggregation_period: aggrPeriod,
    things: [],
    from_time: fromTime,
    upto_time: uptoTime,
    categories,
    summarize: [
      {
        query: [
          ...tableParamsToShow
            .filter((param) => !param?.operation)
            .map((param) =>
              param.value === "aqi"
                ? "avg(aqi.value)"
                : `${param.data_aggr}(${param.value}.${param.data_aggr})`,
            ),
        ],
        group_summary: [
          ...tableParamsToShow
            .filter((param) => !param?.operation)
            .map((param) =>
              param.value === "aqi"
                ? "avg(avg(aqi.value))"
                : `${param.data_aggr}(${param.data_aggr}(${param.value}.${param.data_aggr}))`,
            ),
        ],
        group_by: "thing",
      },
    ],
  };
  const dataPacketTimeWiseSum = {
    data_type: "aggregate",
    aggregation_period: aggrPeriod,
    things: [],
    from_time: fromTime,
    upto_time: uptoTime,
    categories,
    summarize: [
      {
        query: [
          ...tableParamsToShow
            .filter((param) => !param?.operation)
            .map((param) =>
              param.value === "aqi"
                ? "avg(aqi.value)"
                : `${param.data_aggr}(${param.value}.${param.data_aggr})`,
            ),
        ],
        group_by: "time",
      },
    ],
  };
  const dataPacketExceeded = {
    data_type: "aggregate",
    aggregation_period: this.props?.customAggregationPeriod || 900,
    things: [],
    from_time: fromTime,
    upto_time: uptoTime,
    categories,
    summarize: [
      {
        threshold_query: tableParamsToShow
          .filter((param) => param?.operation === ">")
          .map((param) => ({
            parameter: param.parameter,
            attribute: param.attribute,
            operation: param.operation,
            threshold: param.threshold,
            result: param.result,
          })),
        group_by: ["thing"],
      },
    ],
  };
  const [
    apiData,
    apiThingWiseSumData,
    apiTimeWiseSumData,
    apiExceededData,
    siteIdNameArr,
  ] = await Promise.all([
    getThingsData(dataPacket, client_id, application_id),
    getThingsData(dataPacketThingWiseSum, client_id, application_id),
    getThingsData(dataPacketTimeWiseSum, client_id, application_id),
    getThingsData(
      dataPacketExceeded,
      client_id,
      application_id,
      false,
      "?data_source=new",
    ),
    getSiteDetails(client_id, enabled_features),
  ]);
  flushSync(() =>
    this.setState({
      getDetailedData: apiData?.data,
      getThingWiseSumData: apiThingWiseSumData?.summary?.length
        ? apiThingWiseSumData.summary[0].data
        : [],
      getThingWiseTotalSumData: apiThingWiseSumData?.summary?.length
        ? apiThingWiseSumData.summary[0]?.summary
        : {},
      getTimeWiseSumData: apiTimeWiseSumData?.summary?.length
        ? apiTimeWiseSumData.summary[0].data
        : [],
      getTimeWiseExceededData: apiExceededData?.summary?.length
        ? apiExceededData.summary[0].data
        : [],
      filteredTableParams: tableParamsToShow,
      thingSiteArray: siteIdNameArr,
    }),
  );
}

export function getRenderDownloadData(isDownload) {
  this.getColumn();
  const {
    multiAssetReportConfig,
    thingsDetails,
    dontShowLatestDate,
    showTotal,
    customAggregationPeriod,
    hideHeaderTitle,
  } = this.props;
  const {
    summaryData,
    getDetailedData,
    getThingWiseSumData,
    getThingWiseTotalSumData,
    getTimeWiseSumData,
    getTimeWiseExceededData,
    fromTime,
    uptoTime,
    filteredTableParams,
    downloadColumns,
    aggrPeriod,
    allTimes,
    thingSiteArray,
    siteEnabled,
  } = this.state;
  const totalDataPoints = Math.ceil(
    (uptoTime - fromTime) / (customAggregationPeriod || aggrPeriod),
  );
  console.log("siteEnabled", siteEnabled, thingSiteArray);
  const { user_preferences, reportName } = this.props;
  new Backyard({
    scripts: [
      "https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js",
      "https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.23/moment-timezone.min.js",
      "https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.33/moment-timezone-with-data-1970-2030.min.js",
      "https://cdn.jsdelivr.net/npm/lodash@4.17.15/lodash.min.js",
    ],
    input: {
      summaryData,
      getDetailedData,
      getThingWiseSumData,
      getThingWiseTotalSumData,
      getTimeWiseSumData,
      getTimeWiseExceededData,
      fromTime,
      uptoTime,
      thingsDetails,
      isDownload,
      filteredTableParams,
      downloadColumns,
      user_preferences,
      GraphObjectData,
      multiAssetReportConfig,
      aggrPeriod,
      reportName,
      dontShowLatestDate,
      allTimes,
      thingSiteArray,
      showTotal,
      totalDataPoints,
      siteEnabled,
      hideHeaderTitle,
    },
    run: function (ctx, input, cb) {
      const {
        summaryData,
        getDetailedData,
        getThingWiseSumData,
        getThingWiseTotalSumData,
        getTimeWiseSumData,
        getTimeWiseExceededData,
        fromTime,
        uptoTime,
        thingsDetails,
        isDownload,
        filteredTableParams,
        downloadColumns,
        user_preferences,
        GraphObjectData,
        multiAssetReportConfig,
        aggrPeriod,
        reportName,
        dontShowLatestDate,
        allTimes,
        thingSiteArray,
        showTotal,
        totalDataPoints,
        siteEnabled,
        hideHeaderTitle,
      } = input;
      const picker =
        aggrPeriod === 7 * 86400
          ? "week"
          : aggrPeriod === 30 * 86400
            ? "month"
            : aggrPeriod === 3 * 30 * 86400
              ? "quarter"
              : aggrPeriod === 365 * 86400
                ? "year"
                : "day";
      const things = thingsDetails?.things;
      const lifetimeAggr = aggrPeriod === "lifetime";
      function secondToTime(input) {
        const hours = Math.floor(input / 3600);
        const minutes = Math.floor((input % 3600) / 60);
        const seconds = Math.floor(input % 60);

        const pad = (value) => (value < 10 ? `0${value}` : `${value}`);
        return `${pad(hours)}:${pad(minutes)}`;
      }
      function flexTime(i, picker) {
        return moment.unix(i).tz(timezone).startOf(picker).unix();
      }
      function findParamValue(
        value,
        paramType,
        graph,
        param_key,
        key,
        calledFrom,
      ) {
        let finalValue = "",
          result;
        if (key === "offline_percentage" && param_key === "onp") {
          if (!isNaN(parseFloat(value))) {
            finalValue = (100 - parseFloat(value)).toFixed(2);
          } else {
            finalValue = param_key === "data_availability" ? "0.00" : "-";
          }
        } else {
          finalValue = value;
        }
        if (!isNaN(parseFloat(finalValue))) {
          if (graph) {
            if (paramType === "time") {
              result = parseFloat(finalValue) / 3600;
            } else {
              result = parseFloat(finalValue);
            }
          } else {
            if (paramType === "time") {
              result = secondToTime(parseFloat(finalValue));
            } else {
              result = parseFloat(finalValue).toFixed(2);
            }
          }
        } else {
          result = graph
            ? null
            : param_key === "data_availability"
              ? "0.00"
              : "-";
        }
        return result;
      }
      const timezone = user_preferences?.timezone;
      function graphDataFunc() {
        const summaryConfig = multiAssetReportConfig?.summaryParams;
        const graphObj = JSON.parse(JSON.stringify(GraphObjectData));
        graphObj.graph_data.config.backgroundColor = "transparent";
        graphObj.graph_data.config.plotOptions = {
          series: {
            boostThreshold: 0,
            marker: {
              radius: 1.5,
            },
          },
        };
        const commonAxisStyle = {
          color: "#7686A1",
        };
        const allYAxis = [],
          series = [],
          xCats = [];
        if (summaryConfig?.length) {
          summaryConfig.map((config, yInd) => {
            const seriesData = [];
            allYAxis.push({
              title: {
                text: config.unit,
                style: commonAxisStyle,
              },
              labels: {
                format: "{value}",
              },
              opposite: yInd % 2 === 1 ? true : false,
            });
            if (lifetimeAggr) {
              things.map((thing) => {
                xCats.push(thing.name);
                seriesData.push([
                  findParamValue(
                    ctx["_"].find(thing?.parameters, { key: config.value })
                      ?.aggregated_value?.lifetime?.[config.data_aggr],
                    config.type,
                    true,
                    config.value,
                    config.key,
                  ),
                ]);
              });
            } else {
              summaryData.map((data) => {
                if (
                  dontShowLatestDate &&
                  picker === "day" &&
                  ctx.moment.unix(data.time).format("DD MMM YYYY") ===
                    ctx.moment.unix(ctx.moment().unix()).format("DD MMM YYYY")
                ) {
                  return;
                } else {
                  seriesData.push([
                    data.time * 1000,
                    findParamValue(
                      data?.parameter_values?.[config.data_aggr]?.[
                        config.value === "pm2#5" ? "pm2.5" : config.value
                      ]?.[config.data_aggr],
                      config.type,
                      true,
                      config.value,
                      config.key,
                    ),
                  ]);
                }
              });
            }
            series.push({
              name: config.label,
              unit: config.unit,
              type: "column",
              data: seriesData,
              color: config.color,
              yAxis: yInd > 0 ? yInd : undefined,
              tooltip: {
                valueSuffix: ` ${config.unit}`,
              },
            });
          });
        }
        if (xCats?.length) {
          graphObj.graph_data.config.xAxis.categories = xCats;
        }
        graphObj.graph_data.config.xAxis.title = {
          text: lifetimeAggr ? "Assets" : "Date & time",
          style: commonAxisStyle,
        };
        graphObj.graph_data.config.yAxis = allYAxis;
        graphObj.graph_data.config.chart.height = 200;
        graphObj.graph_data.series_data = series;
        return graphObj;
      }
      function tableDataFunc() {
        let tableData = [];
        if (isDownload && !hideHeaderTitle) {
          tableData.push({
            site: "Site",
            asset: "Asset",
          });
        }
        let lifetimeSum = {};
        if (things?.length) {
          things.map((thing, thingInd) => {
            tableData.push({
              id: thing.id,
              asset: thing.name,
              site: thingSiteArray?.[thing.site_id] || "-",
            });
            if (lifetimeAggr) {
              const findthingIndex = ctx["_"].findIndex(tableData, {
                id: thing.id,
              });
              if (findthingIndex > -1) {
                if (filteredTableParams?.length) {
                  filteredTableParams.map((param) => {
                    if (!lifetimeSum[param.value]) {
                      lifetimeSum[param.value] = 0;
                    }
                    tableData[findthingIndex][`total_${param.value}`] =
                      findParamValue(
                        ctx["_"].find(thing?.parameters, { key: param.value })
                          ?.aggregated_value?.lifetime?.[param.data_aggr],
                        param.type,
                        false,
                        param.value,
                        param.key,
                      );
                    lifetimeSum[param.value] += parseFloat(
                      ctx["_"].find(thing?.parameters, { key: param.value })
                        ?.aggregated_value?.lifetime?.[param.data_aggr],
                    );
                  });
                }
              }
            } else {
              if (allTimes?.length) {
                allTimes.map((time) => {
                  if (filteredTableParams?.length) {
                    filteredTableParams.map((param) => {
                      const paramKey =
                        param.value === "pm2#5" ? "pm2.5" : param.value;
                      tableData[thingInd][`${paramKey}_${time}`] =
                        param.key === "data_availability" ? "0.00" : "-";
                    });
                  }
                });
              }
              if (getDetailedData?.length) {
                getDetailedData.map((data) => {
                  const findthingIndex = ctx["_"].findIndex(tableData, {
                    id: data.thing_id,
                  });
                  if (findthingIndex > -1) {
                    if (filteredTableParams?.length) {
                      filteredTableParams.map((param) => {
                        let paramVal =
                          param.value === "pm2#5" ? "pm2.5" : param.value;
                        tableData[findthingIndex][
                          `${param.value}_${data.time}`
                        ] = findParamValue(
                          data.parameter_values?.[paramVal]?.[param.data_aggr],
                          param.type,
                          false,
                          param.value,
                          param.key,
                        );
                      });
                    }
                  }
                });
              }
              if (getThingWiseSumData?.length) {
                getThingWiseSumData.map((data) => {
                  const findthingIndex = ctx["_"].findIndex(tableData, {
                    id: data.thing_id,
                  });
                  if (findthingIndex > -1) {
                    if (filteredTableParams?.length) {
                      filteredTableParams.map((param) => {
                        let paramVal =
                          param.value === "pm2#5" ? "pm2.5" : param.value;
                        tableData[findthingIndex][`total_${paramVal}`] =
                          findParamValue(
                            data.parameter_values?.[param.data_aggr]?.[
                              param.value
                            ]?.[param.data_aggr],
                            param.type,
                            false,
                            param.value,
                            param.key,
                          );
                      });
                    }
                  }
                });
              }
              if (getTimeWiseExceededData?.length) {
                getTimeWiseExceededData.map((data) => {
                  const findthingIndex = ctx["_"].findIndex(tableData, {
                    id: data.thing_id,
                  });
                  if (findthingIndex > -1) {
                    tableData[findthingIndex][
                      `${data.parameter}_exceeded_${fromTime}`
                    ] = Math.min(
                      100,
                      (data.count * 100) / totalDataPoints,
                    ).toFixed(2);
                  }
                });
              }
            }
          });
        }
        const totalOfALlParamsRow = [{ asset: "Total" }];
        if (lifetimeAggr) {
          if (filteredTableParams?.length) {
            filteredTableParams.map((param) => {
              totalOfALlParamsRow[0][`total_${param.value}`] = findParamValue(
                lifetimeSum[param.value],
                param.type,
                false,
                param.value,
                param.key,
              );
            });
          }
        } else {
          if (filteredTableParams?.length) {
            filteredTableParams.map((param) => {
              if (
                getThingWiseTotalSumData &&
                Object.keys(getThingWiseTotalSumData).length
              ) {
                const thingWiseTotalData =
                  getThingWiseTotalSumData?.[param.data_aggr]?.[
                    param.data_aggr
                  ];
                totalOfALlParamsRow[0][`total_${param.value}`] = findParamValue(
                  thingWiseTotalData?.[param.value]?.[param.data_aggr],
                  param.type,
                  false,
                  param.value,
                  param.key,
                );
              }
            });
          }
          if (getTimeWiseSumData?.length) {
            getTimeWiseSumData.map((data) => {
              if (filteredTableParams?.length) {
                filteredTableParams.map((param) => {
                  let paramVal =
                    param.value === "pm2#5" ? "pm2.5" : param.value;
                  totalOfALlParamsRow[0][`${paramVal}_${data.time}`] =
                    findParamValue(
                      data.parameter_values?.[param.data_aggr]?.[param.value]?.[
                        param.data_aggr
                      ],
                      param.type,
                      false,
                      param.value,
                      param.key,
                    );
                });
              }
            });
          }
        }
        if (isDownload && !hideHeaderTitle) {
          if (filteredTableParams?.length) {
            filteredTableParams.map((param) => {
              tableData[0][`total_${param.value}`] =
                `${param.label} (${param.unit})`;
            });
          }
          const startDayUptoTime = ctx.moment
            .unix(uptoTime)
            .tz(timezone)
            .startOf(picker)
            .unix();
          for (let i = startDayUptoTime; i >= fromTime; i -= aggrPeriod) {
            if (filteredTableParams?.length) {
              filteredTableParams.map((param) => {
                tableData[0][`${param.value}_${flexTime(i, picker)}`] =
                  `${param.label} (${param.unit})`;
              });
            }
          }
        }
        if (showTotal === undefined || showTotal) {
          tableData = tableData.concat(totalOfALlParamsRow);
        }
        if (siteEnabled) {
          tableData.sort((a, b) => {
            if (a.site === "Site") return -1;
            if (b.site === "Site") return 1;
            if (a.site === "-" && b.site !== "-") return 1;
            if (a.site !== "-" && b.site === "-") return -1;
            return a.site?.localeCompare(b.site);
          });
        }
        return tableData;
      }
      function finalObj() {
        let downloadData = { conf: [], data: [] };
        if (isDownload) {
          function textFunction(text, size, fontWeight, forceNewPage) {
            let { textProps, textValue } = {
              textProps: {
                props: {
                  gutter: 10,
                },
                child: [
                  {
                    pdf_force_new_page: forceNewPage,
                    textColor: [0, 0, 0],
                    type: fontWeight ? "bold" : "normal",
                    compo: "Text",
                    props: {
                      type: fontWeight ? "bold" : "normal",
                    },
                    pdf_size: size,
                    col_props: {
                      span: 5,
                    },
                    secStyle: {
                      body: {
                        font: {
                          size: size ? size : 12,
                          bold: fontWeight,
                        },
                      },
                    },
                  },
                ],
              },
              textValue: [
                {
                  textData: text,
                },
              ],
            };
            return {
              textProps,
              textValue,
            };
          }
          const ReportHeader = {
            text_conf: {
              props: {
                gutter: 5,
              },
              child: [
                {
                  pdf_text_align: "center",
                  textColor: [255, 255, 255],
                  pdf_size: 16,
                  type: "bold",
                  fill: {
                    fill_color: [255, 133, 0],
                    y_value: 20,
                    top: 8,
                  },
                  compo: "Text",
                  col_props: {
                    span: 24,
                  },
                  secStyle: {
                    body: {
                      font: {
                        size: 20,
                        bold: true,
                      },
                    },
                  },
                },
                {
                  type: "bold",
                  compo: "Text",
                  pdf_text_align: "center",
                  textColor: [255, 255, 255],
                  pdf_size: 10,
                  fill: {
                    fill_color: [255, 133, 0],
                    y_value: 10,
                    top: 1,
                  },
                },
                {
                  type: "bold",
                  compo: "Text",
                  props: {
                    type: "bold",
                  },
                  pdf_text_align: "center",
                  textColor: [255, 255, 255],
                  pdf_size: 10,
                  fill: {
                    fill_color: [255, 133, 0],
                    y_value: 8,
                    top: 3,
                  },
                },
              ],
            },
            text_data: [
              {
                textData: [
                  reportName ? `${reportName} Report` : "Multi Assets Report",
                ],
              },
              {
                textData: [
                  `From ${ctx.moment
                    .unix(fromTime)
                    .tz(timezone)
                    .format("DD MMM YYYY, HH:mm")} to ${ctx.moment
                    .unix(uptoTime)
                    .tz(timezone)
                    .format("DD MMM YYYY, HH:mm")}`,
                ],
              },
              {
                textData: [
                  `Generated on ${ctx.moment
                    .unix(ctx.moment().unix())
                    .tz(timezone)
                    .format("DD MMM YYYY, HH:mm")}`,
                ],
              },
            ],
          };
          downloadData.conf.push(ReportHeader.text_conf);
          downloadData.data.push(ReportHeader.text_data);
          downloadData.file_name = reportName
            ? `${reportName} Report`
            : "Multi Assets Report";
          const hideGraph = multiAssetReportConfig?.summaryParams?.length ? false : true;
          if(!hideGraph) {
            let graphConfigForDownload = { ...graphDataFunc()?.graph_data };
            graphConfigForDownload = {
              ...graphConfigForDownload.config,
              ...{ chart: { width: 1280, height: 300 } },
              ...{ series: graphConfigForDownload.series_data },
            };
            downloadData.conf.push({
              props: {
                gutter: 10,
                style: {},
                className: "rowGraph",
              },
              child: [
                {
                  compo: "Graph",
                  widget: "",
                  classname: "graph-1",
                  props: {
                    id: "graph-id",
                  },
                  col_props: {
                    span: 24,
                  },
                pdf_force_new_page: true,
                  datatype: {
                    "xAxis.categories": lifetimeAggr
                      ? "string"
                      : "datetime::HH:MM",
                    "series.data": "number::2",
                  },
                },
              ],
            });
            downloadData.data.push([graphConfigForDownload]);
          }
          downloadData.conf.push(textFunction(null, 13, true, hideGraph ? false : true).textProps);
          downloadData.data.push(textFunction(["", "Detailed Data"]).textValue);
          downloadData.conf.push({
            pdf_force_new_page: false,
            props: {
              gutter: 10,
              style: {},
              className: "tableRow",
            },
            child: [
              {
                compo: "Table",
                widget: "",
                classname: "tab-1",
                table_new_page: true,
                props: {
                  columns: downloadColumns,
                  headerFont: 13,
                  size: "small",
                  tabRadius: 0,
                  horizontalScroll: true,
                  shadow: false,
                  breakPoint: 1000,
                  breakPoint2: 500,
                  largeTable: true,
                  mediumTable: false,
                  smallTable: false,
                },
                theme: "grid",
                col_props: {
                  span: 24,
                },
                pdf_width: 50,
                pdf_table_break: {
                  col_no: 12,
                  row_no: hideGraph ? 12 : 16,
                },
              },
            ],
          });
          downloadData.data.push([tableDataFunc()]);
        }
        return {
          viewData: {
            dataSource: tableDataFunc(),
            graphConfig: graphDataFunc()?.graph_data,
          },
          downloadData,
        };
      }
      let viewData, downloadData;
      viewData = finalObj()?.viewData;
      if (isDownload) {
        downloadData = finalObj()?.downloadData;
      }
      cb({ viewData, downloadData });
    },
    cb: (value) => {
      this.setState(
        {
          downloadData: value.downloadData,
          ...(value.viewData && !isDownload && { viewData: value.viewData }),
          pageLoading: false,
          loading: false,
        },
        () => {
          if (isDownload) {
            this.getDownloadRender();
          }
        },
      );
    },
  });
}
