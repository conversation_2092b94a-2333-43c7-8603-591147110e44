import React from "react";
import _filter from "lodash/filter";
import _find from "lodash/find";
import moment from "moment-timezone";
import { flushSync } from "react-dom";

function initialSpaces(spaceCount) {
  let space = "";
  for (let i = 0; i < spaceCount; i++) {
    space += " ";
  }
  return space;
}

export function getColumn() {
  const { multiAssetReportConfig, dontShowLatestDate, thing_list, hideHeaderTitle } = this.props;
  const { fromTime, uptoTime, checkedParams, aggrPeriod } = this.state;
  let spaceCount = 0;
  const columns = [
      {
        title: "Asset",
        dataIndex: "asset",
        fixed: "left",
        width: 180,
      },
    ],
    downloadColumns = [
      {
        title: hideHeaderTitle ? "Asset" : " ", //for csv as csv requires unit title for its text output nature
        dataIndex: "asset",
      },
    ];

  this.state.siteEnabled &&
    columns.unshift({
      title: "Site",
      dataIndex: "site",
      fixed: "left",
      width: 180,
    }),
    downloadColumns.unshift({
      title: hideHeaderTitle ? "Site" : "  ",
      dataIndex: "site",
    });

  spaceCount += 1;
  const paramsToShow = _filter(
    multiAssetReportConfig?.detailedParams,
    function (o) {
      return checkedParams?.includes(o.value);
    },
  );
  const totalParamChildren = [];
  const totalTitle = aggrPeriod === "lifetime" ? "Lifetime" : "Total";
  if (paramsToShow?.length) {
    paramsToShow.map((params, index) => {
      spaceCount += index + 1;
      if (
        this.props?.showTotalOfTotal === undefined ||
        this.props.showTotalOfTotal
      ) {
        downloadColumns.push({
          title: index === 0 ? totalTitle : initialSpaces(spaceCount),
          dataIndex: `total_${params.value}`,
        });
      }
      totalParamChildren.push({
        title: `${params.label} (${params.unit})`,
        dataIndex: `total_${params.value}`,
        width: 150,
        render: (text) => {
          return <span style={{ "font-weight": "bold" }}>{text}</span>;
        },
      });
    });
  }
  if (
    this.props?.showTotalOfTotal === undefined ||
    this.props.showTotalOfTotal
  ) {
    columns.push({
      title: totalTitle,
      children: totalParamChildren,
    });
  }
  const picker =
    aggrPeriod === 7 * 86400
      ? "week"
      : aggrPeriod === 30 * 86400
        ? "month"
        : aggrPeriod === 3 * 30 * 86400
          ? "quarter"
          : aggrPeriod === 365 * 86400
            ? "year"
            : "day";
  const dateFormat =
    picker === "day" ? "DD MMM YYYY" : picker === "year" ? "YYYY" : "MMM YYYY";
  const allTimes = [];
  if (aggrPeriod !== "lifetime") {
    const startDayUptoTime = moment.unix(uptoTime).startOf(picker).unix();
    let currentTime =
      dontShowLatestDate &&
      picker === "day" &&
      startDayUptoTime === moment().startOf("day").unix()
        ? startDayUptoTime - 86400
        : startDayUptoTime;
    while (currentTime >= fromTime) {
      const flexTime = moment.unix(currentTime).startOf(picker).unix();
      const paraChildren = [];
      const timeAsPerPicker =
        picker === "quarter"
          ? `${moment.unix(flexTime).format("DD MMM")} to ${moment.unix(flexTime + 3 * 30 * 86400).format("DD MMM YYYY")}`
          : picker === "week"
            ? `${moment.unix(flexTime).format("DD MMM")} to ${moment.unix(flexTime + 6 * 86400).format("DD MMM YYYY")}`
            : moment.unix(flexTime).format(dateFormat);
      if (paramsToShow?.length) {
        paramsToShow.map((params, index) => {
          spaceCount += index + 1;
          if(!hideHeaderTitle) {
            downloadColumns.push({
              title: index === 0 ? timeAsPerPicker : initialSpaces(spaceCount),
              dataIndex: `${params.value}_${flexTime}`,
            });
          }
          allTimes.push(flexTime);
          paraChildren.push({
            title: `${params.label} (${params.unit})`,
            dataIndex: `${params.value}_${flexTime}`,
            width: 150,
            render: (text) => {
              return (
                <span>
                  {!isNaN(parseFloat(text))
                    ? text
                    : params.value === "data_availability"
                      ? "0.00"
                      : "-"}
                </span>
              );
            },
          });
        });
      }

      if (hideHeaderTitle) {
        paraChildren.forEach((para) => {
          columns.push({
            title: para.title,
            dataIndex: para.dataIndex,
            width: 150,
            render: para.render,
          });
          downloadColumns.push({
            title: para.title,
            dataIndex: para.dataIndex,
          });
        });
      } else {
        columns.push({
          title: timeAsPerPicker,
          children: paraChildren,
        });
      }

      if (picker === "month") {
        currentTime = moment.unix(currentTime).subtract(1, "months").unix();
      } else if (picker === "year") {
        currentTime = moment.unix(currentTime).subtract(1, "year").unix();
      } else {
        currentTime -= aggrPeriod;
      }
    }
  }
  if (columns.length === 3) {
    columns.splice(1, 1);
  }
  flushSync(() => {
    this.setState({
      allTimes,
      columns,
      downloadColumns,
    });
  })
}
