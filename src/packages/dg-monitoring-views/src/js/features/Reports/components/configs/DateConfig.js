import moment from 'moment-timezone';

// today, Yesterday, this week, last week, this month, last month, last 7 days, last 30 days
export default {
	ranges: {
		'Today': [moment().startOf('day'), moment().endOf('day')],
		'Yesterday': [
			moment().subtract(1, 'day').startOf('day'),
			moment().subtract(1, 'day').endOf('day'),
		],
		'This Week': [moment().startOf('week'), moment().endOf('day')],
		'Last Week': [
			moment().subtract(1, 'week').startOf('week'),
			moment().subtract(1, 'week').endOf('week'),
		],
		'Last 7 Days': [
			moment().subtract(6, 'days').startOf('day'),
			moment().endOf('day'),
		],
		'Last 30 Days': [
			moment().subtract(30, 'days').startOf('day'),
			moment().endOf('day'),
		],
		'This Month': [moment().startOf('month'), moment().endOf('day')],
		'Last Month': [
			moment().subtract(1, 'M').startOf('month'),
			moment().subtract(1, 'M').endOf('month'),
		],
		'Last 3 Months': [
			moment().subtract(3, 'M').startOf('day'),
			moment().endOf('day'),
		],
		'Last 120 Days': [
			moment().subtract(120, 'days').startOf('day'),
			moment().endOf('day'),
		],
	},
	datePickerConfig: {
		placeholder: ['From', 'To'],
		size: 'default',
		showTime: false,
		separator: ' - ',
		format: 'DD-MMM-YYYY',
	},
};
