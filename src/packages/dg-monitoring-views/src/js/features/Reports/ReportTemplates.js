import React from "react";
import "@ant-design/compatible/assets/index.css";
import AntLayout from "@datoms/react-components/src/components/AntLayout";
import AntRow from "@datoms/react-components/src/components/AntRow";
import AntCol from "@datoms/react-components/src/components/AntCol";
import AntNotification from "@datoms/react-components/src/components/AntNotification";
import AntSpin from "@datoms/react-components/src/components/AntSpin";
import AntTabs from "@datoms/react-components/src/components/AntTabs";
import AntTabPane from "@datoms/react-components/src/components/AntTabPane";
import AntContent from "@datoms/react-components/src/components/AntContent";
import NoDataComponent from "@datoms/react-components/src/components/NoDataComponent";
import {
  retriveThingsList,
  retriveCustomerDetails,
  retrieveSitesList,
  getThingsAndParameterData,
} from "@datoms/js-sdk";
import _find from "lodash/find";
import _filter from "lodash/filter";
import "../../../styles/features/Reports/report-template.less";
import CustomReports from "../../../images/CommonFiles/custom_reports.svg";
// import { ReactComponent as FileIcon } from '../../../images/DatomsX/file_icon.svg';
import { formData } from "../../configuration/reportFormData";
// import CustomReportsDetails from '../Reports/CustomReports';
import { getApplicationDataWithManipulatedData } from "@datoms/js-utils/src/ApplicationsDataManipulation";
import ReportsTemplatesForm from "./imports/ReportsTemplatesForm";
import ReportIcon from "../../../images/DGMonitoring/report-icon.svg";
import { filterDginIot } from "../../data_handling/DGinIot";
import ConfigureParams from "./ReportsParamConfigure";
import ParamRangeConfiguration from "./PollutionMonitoringReports/components/ParamRangeConfiguration";
import { getBaseUrl } from "@datoms/js-utils/src/base-url-logic";
import CustomReportParticle from "./assets/custom-report-particle.png";
import CustomReportGoToIcon from "./assets/go-to-custom-report.png";
import ScheduledList from "./components/ScheduledList";
import { isAurassure } from "../../GenericTemplate/logic/isAurassure";
// import { thingStatus } from '../../GenericTemplate/pages/AssetDashboard/Dashboards/FlowmeterDashboard/logic/insightsLogic';
import LeftOutlined from "@ant-design/icons/LeftOutlined";
import plan_description from "../../DGMonitoring/Template-17/logic/getPlanDesc";

const time_ranges = {
  today: "Today",
  yesterday: "Yesterday",
  this_week: "This Week",
  last_week: "Last Week",
  last_7_days: "Last 7 Days",
  last_month: "Last Month",
  this_month: "This Month",
  last_30_days: "Last 30 Days",
  last_120_days: "Last 120 Days",
};

const time_intervals = {
  1: "Hourly",
  2: "Daily",
  3: "Monthly",
  4: "Yearly",
  5: "Monthly",
  6: "Monthly",
};

export default class ReportTemplates extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      client_id: props.client_id,
      application_id: props.application_id,
      application_name: props.application_name ? props.application_name : "-",
      client_name: props.client_name,
      collapse: true,
      loading: true,
      formData: formData,
      show_template: true,
      selected_application_id: null,
      getPageName: props.dg_in_iot_mode
        ? `${this.props.history.location.pathname.split("/")[1]}/dg-monitoring`
        : this.props.history.location.pathname.split("/")[1],
    };
    this.data = {
      client_id: props.client_id,
      application_id: props.application_id,
    };
    this.retriveApplicationThingsFunction(props.application_id);
  }

  async retriveApplicationThingsFunction(application_id) {
    let response_data =
      await getApplicationDataWithManipulatedData(application_id);
    if (response_data?.response?.status === "success") {
      this.setState({
        //loading: false,
        data: response_data.response,
        application_data: response_data.modified_data.applications,
        category_data: response_data.modified_data.categories,
      });
    } else {
      // this.openNotification(
      // 	'error',
      // 	'Something went wrong, Refresh the page'
      // );
    }
  }

  async componentDidMount() {
    // document.title = 'Report Templates - DATOMS';
    await this.getCustomerDetails();
    await this.getListData(this.data);
  }

  async flowMeterSiteData() {
    const { flowMeterMachinesType } = this.state;
    if (flowMeterMachinesType) {
      const queryForFlowSites = `?site_type=3&page_no=${1}&results_per_page=${1000}`;
      const siteListResponse = await retrieveSitesList(
        this.props.client_id,
        queryForFlowSites,
      );
      let flowMeterSiteList = [];
      if (siteListResponse?.status !== "success") {
        flowMeterSiteList = [];
        return;
      }
      flowMeterSiteList = siteListResponse?.data;
      this.setState({
        flowMeterSiteList,
      });
    }
  }

  async dgSiteData() {
    const { dgSetType } = this.state;
    if (dgSetType) {
      const queryForFlowSites = `?site_type=4&page_no=${1}&results_per_page=${1000}`;
      const siteListResponse = await retrieveSitesList(
        this.props.client_id,
        queryForFlowSites,
      );
      let dgSiteList = [];
      if (siteListResponse?.status !== "success") {
        dgSiteList = [];
        return;
      }
      dgSiteList = siteListResponse?.data;
      this.setState({
        dgSiteList,
      });
    }
  }

  async warehouseSiteData() {
    const siteListResponse = [1, 2].map(async (ids) => {
      return await retrieveSitesList(
        this.props.client_id,
        `?site_type=${ids}&page_no=${1}&results_per_page=${1000}&show_things=true`,
      );
    });
    const siteDetails = await Promise.all(siteListResponse);
    let warehouseSiteList = [];
    let riceMilletSiteList = [];
    if (siteDetails?.length) {
      if (
        siteDetails?.[0]?.status !== "success" ||
        (siteDetails?.length > 1 && siteDetails?.[1]?.status !== "success")
      ) {
        warehouseSiteList = [];
        return;
      }
      siteDetails.forEach((data, index) => {
        warehouseSiteList.push(...data?.data);
        index === 1 && riceMilletSiteList.push(...data?.data);
      });
    }

    const allThings = warehouseSiteList.map((item) => item.things || []).flat();

    let sensorInSite = false;
    if (
      allThings?.length &&
      this.state.modifiedTotalData?.things_list?.length
    ) {
      sensorInSite = this.state.modifiedTotalData.things_list.some(
        (item) =>
          [45, 90].includes(item.category) && allThings.includes(item.id),
      );
    }

    let energyMeterInSite = false;
    if (
      allThings?.length &&
      this.state.modifiedTotalData?.things_list?.length
    ) {
      energyMeterInSite = this.state.modifiedTotalData.things_list.some(
        (item) =>
          [79, 91, 92].includes(item.category) && allThings.includes(item.id),
      );
    }

    this.setState({
      warehouseSiteList,
      riceMilletSiteList,
      sensorInSite,
      energyMeterInSite,
    });
  }

  /**
   * This function opens the notification in render.
   */
  openNotification(type, msg) {
    AntNotification({
      message: msg,
      type: type,
      placement: "bottomLeft",
      className: "alert-" + type,
    });
  }

  getFuelFillDrainOnClick() {
    this.props.history.push(getBaseUrl(this.props, "reports/fuel-fill-drain"));
  }
  getFuelTankFillOnClick() {
    this.props.history.push(getBaseUrl(this.props, "reports/fuel-tank-fill"));
  }
  getTemplateTypeOnClick() {
    this.props.history.push(getBaseUrl(this.props, "reports/template-reports"));
  }

  getCriticalTrendOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/critical-trends-reports"),
    );
  }

  getParmameterSummaryTrendOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/parameter-reports"),
    );
  }

  getLifetimeSummaryTrendOnClick() {
    this.props.history.push(getBaseUrl(this.props, "reports/lifetime-reports"));
  }

  collapseState(collapse) {
    this.setState({
      collapse: collapse,
    });
  }

  goToCustomReportsPage() {
    console.log(
      "this.props.location.match",
      this.props.history.location.pathname.split("/"),
    );
    this.props.history.push(getBaseUrl(this.props, "reports/custom-reports"));
  }

  onSelectApplication(e) {
    console.log("onSelectApplication", e);
  }

  onSelectThingCategory(e) {
    console.log("onSelectThingCategory", e);
  }

  onChange(value) {
    console.log("selectedvalue", value);
  }

  openEditPage() {
    this.setState({
      open_form: true,
      show_template: false,
    });
  }

  getTripOnClick() {
    this.props.history.push(getBaseUrl(this.props, "reports/dg-run-reports"));
  }

  getDgMultiAssetReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/dg-multi-assets-report"),
    );
  }

  getDgDataAvailabilityReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/data-availability-report"),
    );
  }

  getDgSnapshotReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/dg-snapshot-report"),
    );
  }

  getDgStatusReoortOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/asset-status-report"),
    );
  }

  getSummaryOnClick() {
    // alert("Summary reports")
    // const win=window.open(getBaseUrl(this.props, 'reports/summary-reports','_blank'))
    this.props.history.push(getBaseUrl(this.props, "reports/summary-reports"));
    // win.focus();
  }

  getYesterdayReportOnClick() {
    // alert("Summary reports")
    // const win=window.open(getBaseUrl(this.props, 'reports/summary-reports','_blank'))
    this.props.history.push(
      getBaseUrl(this.props, "reports/yesterday-data-reports"),
    );
    // win.focus();
  }

  getTestReportOnClick() {
    this.props.history.push(getBaseUrl(this.props, "reports/dg-test-reports"));
  }

  getFaultOnClick() {
    this.props.history.push(getBaseUrl(this.props, "reports/fault-reports"));
  }
  getAlertDeliveryReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/alert-delivery-summary-reports"),
    );
  }
  getFuelTankOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/fuel-tank/daily-reports"),
    );
  }
  getSnapshotOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/fuel-tank/snapshot-reports"),
    );
  }
  getCompressorDailyOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/compressor/daily-reports"),
    );
  }
  getCompressorTripOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/compressor/run-reports"),
    );
  }

  getCompressorFaultOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/compressor/fault-reports"),
    );
  }

  getAcElectricalMachinesDailyOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/ac-electrical-machines/daily-reports"),
    );
  }
  getAcElectricalMachinesTripOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/ac-electrical-machines/trip-reports"),
    );
  }
  getAcElectricalMachinesMultiAssetOnClick() {
    this.props.history.push(
      getBaseUrl(
        this.props,
        "reports/ac-electrical-machines/multi-asset-reports",
      ),
    );
  }

  getExhaustFanDailyOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/exhaust-fan/daily-reports"),
    );
  }
  getExhaustFanTripOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/exhaust-fan/trip-reports"),
    );
  }
  getExhaustFanMultiAssetOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/exhaust-fan/multi-asset-reports"),
    );
  }
  getFlowMetersDailyOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/flow-meter/daily-reports"),
    );
  }

  getFlowMetersSiteConsReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/flow-meter/site-consumption-reports"),
    );
  }

  getGensetFuelTankSiteReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/genset-fuel-tank-site-report"),
    );
  }

  getEnergySummaryReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/energy-summary-report"),
    );
  }

  getRiceMilletEnergyReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/site-reports/rice-millet-energy-report"),
    );
  }

  getSensorSummaryReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/sensor-summary-report"),
    );
  }

  getcomplianceReportsViolanceOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/violation-summary-report"),
    );
  }

  getPomoParamRangeReportReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/pollution-monitoring/param-range-report"),
    );
  }

  getHotspotReportOnclick() {
    this.props.history.push(getBaseUrl(this.props, "reports/hotspot-report"));
  }

  getJindalMultiAssetReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/aaqms/multi-asset-report"),
    );
  }

  // getMultiAssetDataReportOnClick(){
  //   this.props.history.push(getBaseUrl(this.props, "reports/poc/multi-asset-data"));
  // }

  getAurassureHourlyReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/aurassure/hourly-report"),
    );
  }

  getAurassureDailyReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/aurassure/daily-report"),
    );
  }

  getAurassureMonthlyReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/aurassure/monthly-report"),
    );
  }

  async getCustomerDetails() {
    let customerDetails = await retriveCustomerDetails(this.props.client_id);
    let showAlertDeliveryReport = false;
    if (
      customerDetails &&
      customerDetails.application_details &&
      customerDetails.application_details.length &&
      _find(customerDetails.application_details, {
        application_id: this.props.application_id,
      }) &&
      _find(customerDetails.application_details, {
        application_id: this.props.application_id,
      }).alert_delivery_media &&
      _find(customerDetails.application_details, {
        application_id: this.props.application_id,
      }).alert_delivery_media.length
    ) {
      if (
        _find(customerDetails.application_details, {
          application_id: this.props.application_id,
        }).alert_delivery_media.includes("sms") ||
        _find(customerDetails.application_details, {
          application_id: this.props.application_id,
        }).alert_delivery_media.includes("whatsapp")
      ) {
        showAlertDeliveryReport = true;
      }
    }

    this.setState({
      showAlertDeliveryReport: showAlertDeliveryReport,
    });
  }

  async getListData(data) {
    let totalData = await retriveThingsList(data);
    totalData = filterDginIot.bind(this)(totalData, "reports");
    const modifiedTotalData = getThingsAndParameterData(totalData);
    let dgSetType = _find(totalData.things, function (o) {
      return [18].includes(o.category);
    });
    let gasGensetype = _find(totalData.things, function (o) {
      return [96].includes(o.category);
    });
    let fuelTankType = _find(totalData.things, { category: 71 });
    let compressorType = _find(totalData.things, { category: 73 });
    let fleetType =
      _find(totalData.things, { category: 67 }) ||
      _find(totalData.things, { category: 76 });
    let tankerType = _find(totalData.things, { category: 74 });
    let acEnergyMeterType = _find(totalData.things, { category: 79 });
    let gridEnergyMeterType = _find(totalData.things, { category: 101 });
    let solarPowerType = _find(totalData.things, { category: 91 });
    let ElevatorType = _find(totalData.things, { category: 100 });
    let SolarPumpType = _find(totalData.things, { category: 103 });
    let processAnalyzerType = _find(totalData.things, { category: 94 });
    let tempHumidType = _find(totalData.things, { category: 90 });
    let inverterType = _find(totalData.things, { category: 93 });
    let dcEnergyMeterType = _find(totalData.things, { category: 77 });
    let electricalMachinesType = _find(totalData.things, { category: 78 });
    let exhaustFanType = _find(totalData.things, { category: 99 });
    let flowMeterMachinesType = _find(totalData.things, { category: 86 });
    let complianceMachinesType = _find(totalData.things, function (o) {
      return [21, 22, 102, 23, 44, 85].includes(o.category);
    });
    let chillerType = _find(totalData.things, { category: 83 });
    let cemsType = _find(totalData.things, { category: 21 });
    let aaqmsType = _find(totalData.things, (thing) => [22, 102].includes(thing.category));
    let eqmsType = _find(totalData.things, { category: 23 });
    let ipCamType = _find(totalData.things, { category: 44 });
    let digitalDisplayType = _find(totalData.things, { category: 85 });
    let coldStorageType = _find(totalData.things, { category: 45 });
    const batteryType = _find(totalData.things, { category: 92 });

    let noSupportedReport = false;
    if (
      totalData.things?.length === 1 &&
      _find(totalData.things, function (o) {
        return [44, 85].includes(o.category);
      })
    ) {
      noSupportedReport = true;
    }
    if (dgSetType) {
      this.setState({
        dgSetType: true,
      });
    }
    if (gasGensetype) {
      this.setState({
        gasGensetype: true,
      });
    }
    if (fuelTankType) {
      this.setState({
        fuelTankType: true,
        //	loading: false,
      });
    }
    if (chillerType) {
      this.setState({
        chillerType: true,
        //	loading: false,
      });
    }
    if (compressorType) {
      this.setState({
        compressorType: true,
        //	loading: false,
      });
    }
    if (electricalMachinesType) {
      this.setState({
        electricalMachinesType: true,
      });
    }
    if (flowMeterMachinesType) {
      this.setState({
        flowMeterMachinesType: true,
      });
    }

    if (complianceMachinesType) {
      this.setState({
        complianceMachinesType: true,
      });
    }

    if (coldStorageType) {
      this.setState({
        coldStorageType: true,
      });
    }

    if (fleetType) {
      this.setState({
        fleetType: true,
        //	loading: false,
      });
    }
    if (tankerType) {
      this.setState({
        tankerType: true,
      });
    }
    if (acEnergyMeterType) {
      this.setState({
        acEnergyMeterType: true,
        //	loading: false,
      });
    }
    if (gridEnergyMeterType) {
      this.setState({
        gridEnergyMeterType: true,
      });
    }
    if (dcEnergyMeterType) {
      this.setState({
        dcEnergyMeterType: true,
        //	loading: false,
      });
    }
    if (cemsType) {
      this.setState({
        cemsType: true,
        //	loading: false,
      });
    }
    if (aaqmsType) {
      this.setState({
        aaqmsType: true,
        //	loading: false,
      });
    }
    if (eqmsType) {
      this.setState({
        eqmsType: true,
        //	loading: false,
      });
    }
    if (ipCamType) {
      this.setState({
        ipCamType: true,
        //	loading: false,
      });
    }
    if (digitalDisplayType) {
      this.setState({
        digitalDisplayType: true,
        //	loading: false,
      });
    }
    if (solarPowerType) {
      this.setState({
        solarPowerType: true,
      });
    }
    if (ElevatorType) {
      this.setState({
        ElevatorType: true,
      });
    }
    if (SolarPumpType) {
      this.setState({
        SolarPumpType: true,
      });
    }
    if (processAnalyzerType) {
      this.setState({
        processAnalyzerType: true,
      });
    }
    if (tempHumidType) {
      this.setState({
        tempHumidType: true,
      });
    }
    if (inverterType) {
      this.setState({
        inverterType: true,
      });
    }
    if (exhaustFanType) {
      this.setState({
        exhaustFanType: true,
      });
    }
    if (batteryType) {
      this.setState({
        batteryType: true,
      });
    }
    this.setState(
      {
        noSupportedReport: noSupportedReport,
        totalData: totalData,
        modifiedTotalData,
      },
      async () => {
        await Promise.all([
          this.flowMeterSiteData(),
          this.dgSiteData(),
          this.warehouseSiteData(),
        ]).then(() => {
          this.setState({
            loading: false,
          });
        });
      },
    );
  }

  getFleetDailyOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/fleet/daily-reports"),
    );
  }

  getTankerTruckTripReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/tanker-truck/trip-reports"),
    );
  }

  getEnergyMeterOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/energy-meter/daily-reports"),
    );
  }

  getSolarPowerDailyOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/solar-power/daily-reports"),
    );
  }

  getSolarPumpDailyOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/solar-pump/daily-reports"),
    );
  }

  getProcessAnalyzerDailyOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/process-analyzer/daily-reports"),
    );
  }

  getProcessAnalyzerFaultOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/process-analyzer/fault-reports"),
    );
  }

  getSolarPowerTripOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/solar-power/trip-reports"),
    );
  }

  getSolarPowerMultiAssetOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/solar-power/multi-asset-reports"),
    );
  }

  getSolarPumpTripOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/solar-pump/trip-reports"),
    );
  }

  getSolarPumpMultiAssetOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/solar-pump/multi-asset-reports"),
    );
  }

  getAcEnergyDailyOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/ac-energy-meter/daily-reports"),
    );
  }

  getAcEnergyTripOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/ac-energy-meter/trip-reports"),
    );
  }

  getAcEnergyMultiAssetOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/ac-energy-meter/multi-asset-reports"),
    );
  }

  getTempHumidOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/temp-humid/daily-reports"),
    );
  }

  getInverterOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/inverter/daily-reports"),
    );
  }

  getDcEnergyMeterAvailabilityReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/energy-meter/availability-reports"),
    );
  }

  getFleeTripOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/fleet/trip-reports"),
    );
  }

  getTankerTruckFuelTankerDailyReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/tanker-truck/daily-reports"),
    );
  }

  getTankerTruckVehicleDailyReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/vehicle/daily-reports"),
    );
  }

  getTankerTruckFuelFillDispenseDailyReportOnClick() {
    this.props.history.push(
      getBaseUrl(this.props, "reports/fuel-fill-dispense"),
    );
  }

  render() {
    const { dg_in_iot_mode, iotViewsVendorId, client_id } = this.props;
    const { dgSetType, gasGensetype, noSupportedReport } = this.state;
    const isFuel =
      this.state.modifiedTotalData?.param_key_data?.includes("fuel") ||
      this.state.modifiedTotalData?.param_key_data?.includes("fuel_lt") ||
      this.state.modifiedTotalData?.param_key_data?.includes("fuel_litre");
    const showConfigureIcon = dg_in_iot_mode
      ? !import.meta.env.VITE_MOBILE && iotViewsVendorId === client_id
      : !import.meta.env.VITE_MOBILE;
    return (
      <div id="archive_templates_page">
        {/* <NewHead new_head={this.props.head_side_object_data} active_page_type={'device'} location_path={this.props.location.pathname} collapseState={(collapse) => this.collapseState(collapse)} is_collapsed={this.state.collapse} collapse={this.state.collapse}/> */}
        {(() => {
          if (this.state.loading) {
            return (
              <AntLayout className={"contains"}>
                <AntSpin className="align-center-loading" />
              </AntLayout>
            );
          } else {
            if (noSupportedReport) {
              return (
                <AntLayout className="contains">
                  {this.props.application_id === 16 ? (
                    <NoDataComponent height="85vh" text="No reports found" />
                  ) : (
                    <AntContent>
                      <AntRow className="reports-template-page">
                        <AntRow className="reports-container">
                          <div
                            className="report-type-custom"
                            onClick={() => this.goToCustomReportsPage()}
                          >
                            <div className="report-type-custom-icon">
                              <img
                                src={CustomReportParticle}
                                alt="custom_report_icon"
                              />
                            </div>
                            <div className="report-type-custom-text">
                              Custom Report
                              <div className="sub-text">
                                Get your customized reports on
                                <br />
                                single click with table & graphs.
                              </div>
                            </div>
                            <div className="go-to-btn">
                              <img
                                src={CustomReportGoToIcon}
                                alt="custom_report_icon"
                              />
                            </div>
                          </div>
                        </AntRow>
                      </AntRow>
                    </AntContent>
                  )}
                </AntLayout>
              );
            } else if(this.props.logged_in_user_role_type === 11) {
              return <div className="contains">
                <div className="reports-template-page">
                  <div className="reports-container">
                    <div>
                      <div className="report-template-header">
                        Flow Meter Reports
                      </div>
                      <div className="template-type-section">
                        <div className="template-type-section-body">
                          <div className="template-type-section-body-row">
                            <div className="template-type-section-body-icon">
                              <img src={ReportIcon} />
                            </div>
                            <div
                              className="template-type-section-body-text"
                              onClick={() => this.getFlowMetersDailyOnClick()}
                            >
                              Asset Specific Report
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            }else {
              let fuelTankReport = "";
              fuelTankReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getFuelTankOnClick()}
                      >
                        Daily Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let snapshotReport = "";
              snapshotReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getSnapshotOnClick()}
                      >
                        Snapshot Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let compressorDailyReport = "";
              compressorDailyReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getCompressorDailyOnClick()}
                      >
                        Daily Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let compressorTripReport = "";
              compressorTripReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getCompressorTripOnClick()}
                      >
                        Run Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let compressorFaultReport = "";
              compressorFaultReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getCompressorFaultOnClick()}
                      >
                        Fault Analysis Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let compressorMultiAssetReport =  (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.props.history.push(
                          getBaseUrl(this.props, "reports/compressor/multi-asset-reports"),
                        )}
                      >
                        Multi Asset Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let energyMeterDailyReport = "";
              energyMeterDailyReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => {
                          this.getEnergyMeterOnClick();
                        }}
                      >
                        Daily Report
                      </div>
                    </div>
                  </div>
                  {this.state.dcEnergyMeterType ? (
                    <div className="template-type-section-body">
                      <div className="template-type-section-body-row">
                        <div className="template-type-section-body-icon">
                          <img src={ReportIcon} />
                        </div>
                        <div
                          className="template-type-section-body-text"
                          onClick={() =>
                            this.getDcEnergyMeterAvailabilityReportOnClick()
                          }
                        >
                          Availability Report
                        </div>
                      </div>
                    </div>
                  ) : (
                    ""
                  )}
                </div>
              );
              let inverterDailyReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getInverterOnClick()}
                      >
                        Daily Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let solarPowerDailyReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getSolarPowerDailyOnClick()}
                      >
                        Asset Specific Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let solarPumpDailyReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getSolarPumpDailyOnClick()}
                      >
                        Asset Specific Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let processAnalyzerDailyReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getProcessAnalyzerDailyOnClick()}
                      >
                        Daily Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let processAnalyzerFaultReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getProcessAnalyzerFaultOnClick()}
                      >
                        Fault Analysis Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let solarPowerTripReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getSolarPowerTripOnClick()}
                      >
                        Trip Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let solarPowerMultiAssetReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getSolarPowerMultiAssetOnClick()}
                      >
                        Multi Asset Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let solarPumpTripReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getSolarPumpTripOnClick()}
                      >
                        Run Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let solarPumpMultiAssetReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getSolarPumpMultiAssetOnClick()}
                      >
                        Multi Asset Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let acEnergyMeterDailyReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getAcEnergyDailyOnClick()}
                      >
                        Asset Specific Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let acEnergyMeterMultiAssetReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getAcEnergyMultiAssetOnClick()}
                      >
                        Multi Asset Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let gridEnergyMeterDailyReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.props.history.push(
                          getBaseUrl(this.props, "reports/grid-energy-meter/daily-reports"),
                        )}
                      >
                        Asset Specific Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let gridEnergyMeterMultiAssetReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.props.history.push(
                          getBaseUrl(this.props, "reports/grid-energy-meter/multi-asset-reports"),
                        )}
                      >
                        Multi Asset Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let dcEnergyMeterDailyReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() =>  this.props.history.push(
                          getBaseUrl(this.props, "reports/dc-energy-meter/daily-reports"),
                        )}
                      >
                        Asset Specific Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let dcEnergyMeterMultiAssetReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.props.history.push(
                          getBaseUrl(this.props, "reports/dc-energy-meter/multi-asset-reports"),
                        )}
                      >
                        Multi Asset Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let tempHumidDailyReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getTempHumidOnClick()}
                      >
                        Daily Report
                      </div>
                    </div>
                  </div>
                </div>
              );

              let electricalMachinesReports = "";
              electricalMachinesReports = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() =>
                          this.getAcElectricalMachinesDailyOnClick()
                        }
                      >
                        Asset Specific Report
                      </div>
                    </div>
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() =>
                          this.getAcElectricalMachinesTripOnClick()
                        }
                      >
                        Trip Report
                      </div>
                    </div>
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() =>
                          this.getAcElectricalMachinesMultiAssetOnClick()
                        }
                      >
                        Multi Asset Report
                      </div>
                    </div>
                  </div>
                </div>
              );

              let exhaustFanReports = "";
              exhaustFanReports = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getExhaustFanDailyOnClick()}
                      >
                        Asset Specific Report
                      </div>
                    </div>
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getExhaustFanTripOnClick()}
                      >
                        Trip Report
                      </div>
                    </div>
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getExhaustFanMultiAssetOnClick()}
                      >
                        Multi Asset Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let batteryReports = "";
              batteryReports = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() =>
                          this.props.history.push(
                            getBaseUrl(
                              this.props,
                              "reports/battery/daily-reports",
                            ),
                          )
                        }
                      >
                        Asset Specific Report
                      </div>
                    </div>
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() =>
                          this.props.history.push(
                            getBaseUrl(
                              this.props,
                              "reports/battery/multi-asset-reports",
                            ),
                          )
                        }
                      >
                        Multi Asset Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let flowMeterReports = "";
              flowMeterReports = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getFlowMetersDailyOnClick()}
                      >
                        Asset Specific Report
                      </div>
                    </div>
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.props.history.push(
                          getBaseUrl(this.props, "reports/flow-meter/hourly-reports")
                        )}
                      >
                        Hourly Report
                      </div>
                    </div>
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.props.history.push(
                          getBaseUrl(this.props, "reports/flow-meter/multi-asset-reports")
                        )}
                      >
                        Multi Asset Report
                      </div>
                    </div>
                    {this.state.flowMeterSiteList?.length ? (
                      <div className="template-type-section-body-row">
                        <div className="template-type-section-body-icon">
                          <img src={ReportIcon} />
                        </div>
                        <div
                          className="template-type-section-body-text"
                          onClick={() =>
                            this.getFlowMetersSiteConsReportOnClick()
                          }
                        >
                          Site Consumption Report
                        </div>
                      </div>
                    ) : (
                      ""
                    )}
                  </div>
                </div>
              );
              let complianceReports = "";
              complianceReports = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() =>
                          this.getcomplianceReportsViolanceOnClick()
                        }
                      >
                        {this.props.t? this.props.t('violation_report'): "Violation Report"}
                        {/* Violation Report */}
                      </div>
                    </div>
                  </div>
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() =>
                          this.getPomoParamRangeReportReportOnClick()
                        }
                      >
                        {this.props.t? this.props.t('parameter_range_report'): "Parameter Range Report"}
                        {/* Parameter Range Report */}
                      </div>
                      {showConfigureIcon ? (
                        <ParamRangeConfiguration
                          things={_filter(
                            this.state.totalData?.things,
                            function (o) {
                              return [21, 22, 102, 23, 86].includes(o.category);
                            },
                          )}
                          report_name="param_range_report"
                          {...this.props}
                          paramData={this.state.modifiedTotalData?.param_data}
                        />
                      ) : (
                        ""
                      )}
                    </div>
                  </div>
                 {this.state.aaqmsType || this.state.eqmsType || this.state.cemsType ? 
                 <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() =>
                          this.props.history.push(
                            getBaseUrl(
                              this.props,
                              "reports/data-availability",
                            ),
                          )
                        }
                      >
                        Data Availability Report
                      </div>
                    </div>
                  </div> : ''}
                </div>
              );
              let fleetDailyReport = "",
                fleetTripReport = "",
                tankerTruckDailyReport = "";
              fleetDailyReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getFleetDailyOnClick()}
                      >
                        Daily Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              fleetTripReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getFleeTripOnClick()}
                      >
                        Trip Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              tankerTruckDailyReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() =>
                          this.getTankerTruckFuelTankerDailyReportOnClick()
                        }
                      >
                        Fuel Tanker Daily Report
                      </div>
                    </div>
                  </div>
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() =>
                          this.getTankerTruckVehicleDailyReportOnClick()
                        }
                      >
                        Vehicle Daily Report
                      </div>
                    </div>
                  </div>
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() =>
                          this.getTankerTruckFuelFillDispenseDailyReportOnClick()
                        }
                      >
                        Fuel Fill & Dispense Report
                      </div>
                    </div>
                  </div>
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getTankerTruckTripReportOnClick()}
                      >
                        Trip Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let faultReport = "";
              faultReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getFaultOnClick()}
                      >
                        Fault Analysis Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let dgTestReport = "";
              if (parseInt(this.state.client_id) === 1058) {
                dgTestReport = (
                  <div className="template-type-section">
                    <div className="template-type-section-body">
                      <div className="template-type-section-body-row">
                        <div className="template-type-section-body-icon">
                          <img src={ReportIcon} />
                        </div>
                        <div
                          className="template-type-section-body-text"
                          onClick={() => this.getTestReportOnClick()}
                        >
                          DG Test Reports
                        </div>
                      </div>
                    </div>
                  </div>
                );
              }
              const dgMultiAssetsReport =
                  <div className="template-type-section">
                    <div className="template-type-section-body">
                      <div className="template-type-section-body-row">
                        <div className="template-type-section-body-icon">
                          <img src={ReportIcon} />
                        </div>
                        <div
                          className="template-type-section-body-text"
                          onClick={() => this.getDgMultiAssetReportOnClick()}
                        >
                          Multi Assets Report
                        </div>
                      </div>
                    </div>
                  </div>
              const dgDataAvailabilityReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() =>
                          this.getDgDataAvailabilityReportOnClick()
                        }
                      >
                        Data Availability Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              const dgSnapShotReport = !isFuel ? (
                ""
              ) : (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getDgSnapshotReportOnClick()}
                      >
                        Fuel Snapshot Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              const dgStatusReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getDgStatusReoortOnClick()}
                      >
                        Status Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              let machineReportsView = [];
              if (this.props.plan_description) {
                if (
                  this.props.plan_description.reports &&
                  this.props.plan_description.reports.daily_report
                ) {
                  machineReportsView.push(
                    <div className="template-type-section">
                      <div className="template-type-section-body">
                        <div className="template-type-section-body-row">
                          <div className="template-type-section-body-icon">
                            <img src={ReportIcon} />
                          </div>
                          <div
                            className="template-type-section-body-text"
                            onClick={() => this.getTemplateTypeOnClick()}
                          >
                            Asset Specific Report
                          </div>
                          {/* {showConfigureIcon ? (
                            <ConfigureParams
                              report_name="daily_report"
                              {...this.props}
                            />
                          ) : (
                            ""
                          )} */}
                        </div>
                      </div>
                    </div>,
                  );
                }
                if (
                  this.props.plan_description.reports &&
                  this.props.plan_description.reports.critical_trends_report
                ) {
                  machineReportsView.push(
                    <div className="template-type-section">
                      <div className="template-type-section-body">
                        <div className="template-type-section-body-row">
                          <div className="template-type-section-body-icon">
                            <img src={ReportIcon} />
                          </div>
                          <div
                            className="template-type-section-body-text"
                            onClick={() => this.getCriticalTrendOnClick()}
                          >
                            Critical Trends Report
                          </div>
                        </div>
                      </div>
                    </div>,
                  );
                }

                if (
                  this.props.plan_description.reports &&
                  this.props.plan_description.reports.dg_run_report
                ) {
                  machineReportsView.push(
                    <div className="template-type-section">
                      <div className="template-type-section-body">
                        <div className="template-type-section-body-row">
                          <div className="template-type-section-body-icon">
                            <img src={ReportIcon} />
                          </div>
                          <div
                            className="template-type-section-body-text"
                            onClick={() => this.getTripOnClick()}
                          >
                            Run Reports
                          </div>
                          {/* {showConfigureIcon ? (
                            // 	&&
                            // parseInt(
                            // 	this.props.vendor_id
                            // ) === 1608
                            <ConfigureParams
                              report_name="dg_run_report"
                              {...this.props}
                            />
                          ) : (
                            ""
                          )} */}
                        </div>
                      </div>
                    </div>,
                  );
                }
                if (
                  this.props.plan_description.reports &&
                  this.props.plan_description.reports.fault_report
                ) {
                  machineReportsView.push(faultReport);
                }
                machineReportsView.push(
                  dgMultiAssetsReport,
                  dgSnapShotReport,
                  dgStatusReport,
                );
                if (
                  this.props.client_id === 1418 ||
                  this.props.host_app_name === "datoms-x"
                ) {
                  machineReportsView.push(dgDataAvailabilityReport);
                }
              } else {
                machineReportsView.push(
                  <div className="template-type-section">
                    <div className="template-type-section-body">
                      <div className="template-type-section-body-row">
                        <div className="template-type-section-body-icon">
                          <img src={ReportIcon} />
                        </div>
                        <div
                          className="template-type-section-body-text"
                          onClick={() => this.getTemplateTypeOnClick()}
                        >
                          Daily Report
                        </div>
                      </div>
                    </div>
                  </div>,
                  <div className="template-type-section">
                    {
                      <div className="template-type-section-body">
                        <div className="template-type-section-body-row">
                          <div className="template-type-section-body-icon">
                            <img src={ReportIcon} />
                          </div>
                          <div
                            className="template-type-section-body-text"
                            onClick={() => this.getCriticalTrendOnClick()}
                          >
                            Critical Trends Report
                          </div>
                        </div>
                      </div>
                    }
                  </div>,
                  <div className="template-type-section">
                    <div className="template-type-section-body">
                      <div className="template-type-section-body-row">
                        <div className="template-type-section-body-icon">
                          <img src={ReportIcon} />
                        </div>
                        <div
                          className="template-type-section-body-text"
                          onClick={() => this.getTripOnClick()}
                        >
                          Run Reports
                        </div>
                      </div>
                    </div>
                  </div>,
                  faultReport,
                  dgMultiAssetsReport,
                  dgStatusReport,
                  this.props.client_id === 1418 ||
                    this.props.host_app_name === "datoms-x"
                    ? dgDataAvailabilityReport
                    : undefined,
                );
              }
              if (this.state.showAlertDeliveryReport) {
                machineReportsView.push(
                  <div className="template-type-section">
                    <div className="template-type-section-body">
                      <div className="template-type-section-body-row">
                        <div className="template-type-section-body-icon">
                          <img src={ReportIcon} />
                        </div>
                        <div
                          className="template-type-section-body-text"
                          onClick={() => this.getAlertDeliveryReportOnClick()}
                        >
                          {this.props.t? this.props.t('alert_usage_report'): "Alert Usage Report"}
                          {/* Alert Usage Report */}
                        </div>
                      </div>
                    </div>
                  </div>,
                );
              }
              let fuelFillDrainReport = !isFuel ? (
                ""
              ) : (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getFuelFillDrainOnClick()}
                      >
                        Fuel Fill Drain Report
                      </div>
                      {showConfigureIcon ? (
                        <ConfigureParams
                          report_name="fuel_fill_drain_report"
                          {...this.props}
                        />
                      ) : (
                        ""
                      )}
                    </div>
                  </div>
                </div>
              );
              let fuelTankFillReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getFuelTankFillOnClick()}
                      >
                        Fuel Fill Report
                      </div>
                      {showConfigureIcon ? (
                        <ConfigureParams
                          report_name="fuel_tank_fill_report"
                          {...this.props}
                        />
                      ) : (
                        ""
                      )}
                    </div>
                  </div>
                </div>
              );
              const hotspotReports = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getHotspotReportOnclick()}
                      >
                        {this.props.t? this.props.t('hotspot_report'): "Hotspot Report"}
                        {/* Hotspot Report */}
                      </div>
                    </div>
                  </div>
                </div>
              );
              const jindalMultiAssetReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getJindalMultiAssetReportOnClick()}
                      >
                        {this.props.t? this.props.t('multi_asset_report'): "Multi Asset Report"}
                        {/* Multi Asset Report */}
                      </div>
                    </div>
                  </div>
                </div>
              );
              const AurassureHourlyReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getAurassureHourlyReportOnClick()}
                      >
                        {this.props.t? this.props.t('hourly_report'): "Hourly Report"}
                        {/* Hourly Report */}
                      </div>
                    </div>
                  </div>
                </div>
              );
              const AurassureDailyReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getAurassureDailyReportOnClick()}
                      >
                        {this.props.t? this.props.t('daily_report'): "Daily Report"}
                      </div>
                    </div>
                  </div>
                </div>
              );
              const AurassureMonthlyReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getAurassureMonthlyReportOnClick()}
                      >
                        {this.props.t? this.props.t('monthly_report'): "Monthly Report"}
                      </div>
                    </div>
                  </div>
                </div>
              );
              // const MultiAssetDataReport = (
              //   <div className="template-type-section">
              //     <div className="template-type-section-body">
              //       <div className="template-type-section-body-row">
              //         <div className="template-type-section-body-icon">
              //           <img src={ReportIcon} />
              //         </div>
              //         <div
              //           className="template-type-section-body-text"
              //           onClick={() => this.getMultiAssetDataReportOnClick()}
              //         >
              //           Multi Asset Data Report
              //         </div>
              //       </div>
              //     </div>
              //   </div>
              // );
              if (parseInt(this.props.vendor_id) !== 1140) {
                machineReportsView.push(fuelFillDrainReport);
              }
              const siteReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() =>
                          this.getGensetFuelTankSiteReportOnClick()
                        }
                      >
                        Genset & Fuel Tank Site Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              const EnergySummaryReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getEnergySummaryReportOnClick()}
                      >
                        Energy Summary Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              const RiceMilletEnergyReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getRiceMilletEnergyReportOnClick()}
                      >
                        Site Wise Energy Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              const sensorSummaryReport = (
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.getSensorSummaryReportOnClick()}
                      >
                        Sensor Summary Report
                      </div>
                    </div>
                  </div>
                </div>
              );
              const gasGensetReports = [
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() =>
                          this.props.history.push(
                            getBaseUrl(
                              this.props,
                              "reports/gas-genset/daily-report",
                            ),
                          )
                        }
                      >
                        Daily Report
                      </div>
                    </div>
                  </div>
                </div>,
                <div className="template-type-section">
                  {
                    <div className="template-type-section-body">
                      <div className="template-type-section-body-row">
                        <div className="template-type-section-body-icon">
                          <img src={ReportIcon} />
                        </div>
                        <div
                          className="template-type-section-body-text"
                          onClick={() => this.getCriticalTrendOnClick()}
                        >
                          Critical Trends Report
                        </div>
                      </div>
                    </div>
                  }
                </div>,
                <div className="template-type-section">
                  <div className="template-type-section-body">
                    <div className="template-type-section-body-row">
                      <div className="template-type-section-body-icon">
                        <img src={ReportIcon} />
                      </div>
                      <div
                        className="template-type-section-body-text"
                        onClick={() => this.props.history.push(
                          getBaseUrl(
                            this.props,
                            "reports/gas-genset/run-reports",
                          ),
                        )}
                      >
                        Run Reports
                      </div>
                    </div>
                  </div>
                </div>,
                <div className="template-type-section">
                <div className="template-type-section-body">
                  <div className="template-type-section-body-row">
                    <div className="template-type-section-body-icon">
                      <img src={ReportIcon} />
                    </div>
                    <div
                      className="template-type-section-body-text"
                      onClick={() => this.props.history.push(
                        getBaseUrl(
                          this.props,
                          "reports/gas-genset/fault-reports",
                        ),
                      )}
                    >
                      Fault Analysis Report
                    </div>
                  </div>
                </div>
              </div>,
                <div className="template-type-section">
                <div className="template-type-section-body">
                  <div className="template-type-section-body-row">
                    <div className="template-type-section-body-icon">
                      <img src={ReportIcon} />
                    </div>
                    <div
                    className="template-type-section-body-text"
                    onClick={() =>
                      this.props.history.push(
                        getBaseUrl(
                          this.props,
                          "reports/gas-genset/multi-asset-reports",
                        ),
                      )
                    }
                  >
                    Multi Asset Report
                  </div>
                  </div>
                </div>
              </div>,
                dgStatusReport,
              ];
              let mainReports = (
                <AntRow className="reports-container">
                  {parseInt(this.props.vendor_id) === 1140 &&
                  parseInt(this.props.logged_in_user_client_id) !== 1140 &&
                  parseInt(this.props.logged_in_user_client_id) !== 1 ? (
                    ""
                  ) : parseInt(this.props.template_id) === 17 ? (
                    this.props.plan_description &&
                    this.props.plan_description.reports &&
                    this.props.plan_description.reports.custom_report ? (
                      <div
                        className="report-type-custom"
                        onClick={() => this.goToCustomReportsPage()}
                      >
                        <div className="report-type-custom-icon">
                          <img
                            src={CustomReportParticle}
                            alt="custom_report_icon"
                          />
                        </div>
                        <div className="report-type-custom-text">
                          {this.props.t('custom_reports')}
                          {/* Custom Reports */}
                          <div className="sub-text">
                            {this.props.t("get_your_customized_reports_on")}
                            {/* Get your customized reports on */}
                            <br />
                            {this.props.t('single_click_with_table_and_graphs')}
                            {/* single click with table & graphs. */}
                          </div>
                        </div>
                        <div className="go-to-btn">
                          <img
                            src={CustomReportGoToIcon}
                            alt="custom_report_icon"
                          />
                        </div>
                      </div>
                    ) : (
                      ""
                    )
                  ) : (
                    <div
                      className="report-type-custom"
                      onClick={() => this.goToCustomReportsPage()}
                    >
                      <div className="report-type-custom-icon">
                        <img
                          src={CustomReportParticle}
                          alt="custom_report_icon"
                        />
                      </div>
                      <div className="report-type-custom-text">
                        {this.props.t? this.props.t('custom_reports'): "Custom Reports"}
                        {/* Custom Reports */}
                        <div className="sub-text">
                          {this.props.t? this.props.t('get_your_customized_reports_on'): "Get your customized reports on"}
                          {/* Get your customized reports on */}
                          <br />
                          {this.props.t? this.props.t('single_click_with_table_and_graphs'): "single click with table & graphs."}
                          {/* single click with table & graphs. */}
                        </div>
                      </div>
                      <div className="go-to-btn">
                        <img
                          src={CustomReportGoToIcon}
                          alt="custom_report_icon"
                        />
                      </div>
                    </div>
                  )}
                  {!this.state.dgSetType ? (
                    ""
                  ) : (
                    <div>
                      <div className="report-template-header">DG Set Reports</div>
                      {machineReportsView}
                      {this.props.vendor_id !== 1140 && <><div className="template-type-section">
                          <div className="template-type-section-body">
                            <div className="template-type-section-body-row">
                              <div className="template-type-section-body-icon">
                                <img src={ReportIcon} />
                              </div>
                              <div
                                className="template-type-section-body-text"
                                onClick={() =>
                                  this.getParmameterSummaryTrendOnClick()
                                }
                              >
                                Parameter report
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="template-type-section">
                        <div className="template-type-section-body">
                          <div className="template-type-section-body-row">
                            <div className="template-type-section-body-icon">
                              <img src={ReportIcon} />
                            </div>
                            <div
                              className="template-type-section-body-text"
                              onClick={() =>
                                this.getLifetimeSummaryTrendOnClick()
                              }
                            >
                              Lifetime report
                            </div>
                          </div>
                        </div>
                      </div>
                        </>
                      }
                    </div>
                  )}
                  {!gasGensetype ? (
                    ""
                  ) : (
                    <div>
                      <div className="report-template-header">
                        Gas Genset Reports
                      </div>
                      {gasGensetReports}
                      {this.props.vendor_id !== 1140 && <>
                        <div className="template-type-section">
                        <div className="template-type-section-body">
                          <div className="template-type-section-body-row">
                            <div className="template-type-section-body-icon">
                              <img src={ReportIcon} />
                            </div>
                            <div
                              className="template-type-section-body-text"
                              onClick={() =>
                                this.getLifetimeSummaryTrendOnClick()
                              }
                            >
                              Lifetime report
                            </div>
                          </div>
                        </div>
                      </div>
                        </>
                      }
                    </div>
                  )}
                  {this.state.compressorType ? (
                    <div>
                      <div className="report-template-header">
                        Compressor Reports
                      </div>
                      {compressorDailyReport}
                      {compressorMultiAssetReport}
                      {compressorTripReport}
                      {compressorFaultReport}
                    </div>
                  ) : (
                    ""
                  )}
                  {this.state.fleetType ? (
                    <div>
                      <div className="report-template-header">Car Reports</div>
                      {fleetDailyReport}
                      {fleetTripReport}
                    </div>
                  ) : (
                    ""
                  )}
                  {this.state.tankerType ? (
                    <div>
                      <div className="report-template-header">
                        Tanker Truck Reports
                      </div>
                      {tankerTruckDailyReport}
                    </div>
                  ) : (
                    ""
                  )}
                  {this.state.inverterType ? (
                    <div>
                      <div className="report-template-header">
                        Inverter Reports
                      </div>
                      {inverterDailyReport}
                    </div>
                  ) : (
                    ""
                  )}
                  {this.state.solarPowerType ? (
                    <div>
                      <div className="report-template-header">
                        Solar Power Report
                      </div>
                      {solarPowerDailyReport}
                      {solarPowerTripReport}
                      {solarPowerMultiAssetReport}
                    </div>
                  ) : (
                    ""
                  )}
                  {this.state.ElevatorType ? (
                    <div>
                      <div className="report-template-header">
                        Elevator Report
                      </div>
                      <div className="template-type-section">
                      <div className="template-type-section-body">
                        <div className="template-type-section-body-row">
                          <div className="template-type-section-body-icon">
                            <img src={ReportIcon} />
                          </div>
                          <div
                            className="template-type-section-body-text"
                            onClick={() => this.props.history.push(
                              getBaseUrl(
                                this.props,
                                "reports/elevator/fault-reports",
                              ),
                            )}
                          >
                            Fault Analysis Report
                          </div>
                        </div>
                      </div>
                    </div>
                    </div>
                  ) : (
                    ""
                  )}
                  {this.state.SolarPumpType ? (
                    <div>
                      <div className="report-template-header">
                        Solar Pump Report
                      </div>
                      {solarPumpDailyReport}
                      {solarPumpMultiAssetReport}
                      {solarPumpTripReport}
                      <div className="template-type-section">
                      <div className="template-type-section-body">
                        <div className="template-type-section-body-row">
                          <div className="template-type-section-body-icon">
                            <img src={ReportIcon} />
                          </div>
                          <div
                            className="template-type-section-body-text"
                            onClick={() => this.props.history.push(
                              getBaseUrl(
                                this.props,
                                "reports/solar-pump/fault-reports",
                              ),
                            )}
                          >
                            Fault Analysis Report
                          </div>
                        </div>
                      </div>
                    </div>
                    </div>
                  ) : (
                    ""
                  )}
                  {this.state.processAnalyzerType ? (
                    <div>
                      <div className="report-template-header">
                        Process Analyzer Report
                      </div>
                      {processAnalyzerDailyReport}
                      {processAnalyzerFaultReport}
                      {/* {processAnalyzerTripReport}
											{processAnalyzerMultiAssetReport} */}
                    </div>
                  ) : (
                    ""
                  )}
                  {this.state.acEnergyMeterType ? (
                    <div>
                      <div className="report-template-header">
                        AC Energy Meter Report
                      </div>
                      {acEnergyMeterDailyReport}
                      {acEnergyMeterMultiAssetReport}
                    </div>
                  ) : (
                    ""
                  )}
                    {this.state.gridEnergyMeterType ? (
                    <div>
                      <div className="report-template-header">
                        Grid Energy Meter Report
                      </div>
                      {gridEnergyMeterDailyReport}
                      {gridEnergyMeterMultiAssetReport}
                    </div>
                  ) : (
                    ""
                  )}
                  {this.state.dcEnergyMeterType ? (
                    <div>
                      <div className="report-template-header">
                        DC Energy Meter Report
                      </div>
                      {dcEnergyMeterDailyReport}
                      {dcEnergyMeterMultiAssetReport}
                    </div>
                  ) : (
                    ""
                  )}
                  {this.state.tempHumidType ? (
                    <div>
                      <div className="report-template-header">
                        Temperature & Humidity Report
                      </div>
                      {tempHumidDailyReport}
                    </div>
                  ) : (
                    ""
                  )}
                  {this.state.electricalMachinesType ? (
                    <div>
                      <div className="report-template-header">
                        AC Electrical Machines' Reports
                      </div>
                      {electricalMachinesReports}
                    </div>
                  ) : (
                    ""
                  )}
                  {this.state.exhaustFanType ? (
                    <div>
                      <div className="report-template-header">
                        Exhaust Fans' Reports
                      </div>
                      {exhaustFanReports}
                    </div>
                  ) : (
                    ""
                  )}
                  {this.state.batteryType ? (
                    <div>
                      <div className="report-template-header">
                        Battery Reports
                      </div>
                      {batteryReports}
                    </div>
                  ) : (
                    ""
                  )}
                  {this.state.flowMeterMachinesType ? (
                    <div>
                      <div className="report-template-header">
                        Flow Meter Reports
                      </div>
                      {flowMeterReports}
                    </div>
                  ) : (
                    ""
                  )}
                  {this.state.complianceMachinesType &&
                  !isAurassure(this.props.vendor_id) ? (
                    <div>
                      <div className="report-template-header">
                        {this.props.t? this.props.t('compliance_reports'): "Compliance Reports"}
                        {/* Compliance reports */}
                      </div>
                      {complianceReports}
                    </div>
                  ) : (
                    ""
                  )}
                  {this.state.fuelTankType ? (
                    <div>
                      <div className="report-template-header">
                        Fuel Tank Reports
                      </div>
                      {fuelTankReport}
                      {snapshotReport}
                      {fuelTankFillReport}
                    </div>
                  ) : (
                    ""
                  )}
                  {this.props.vendor_id === 1819 &&
                  this.props.client_id === 1938 ? (
                    <div>
                      {hotspotReports}
                      {AurassureHourlyReport}
                      {AurassureDailyReport}
                      {AurassureMonthlyReport}
                    </div>
                  ) : (
                    ""
                  )}
                  {this.props.vendor_id === 1819 && this.state.aaqmsType ? (
                    <div>{jindalMultiAssetReport}</div>
                  ) : (
                    ""
                  )}
                  {this.state.dgSiteList?.length ? (
                    <div>
                      <div className="report-template-header">
                        Genset Site Reports
                      </div>
                      {siteReport}
                    </div>
                  ) : (
                    ""
                  )}
                  {this.state.warehouseSiteList?.length ? (
                    <div>
                      <div className="report-template-header">Site Reports</div>
                      {this.state.energyMeterInSite && EnergySummaryReport}
                      {this.state.sensorInSite && sensorSummaryReport}
                    </div>
                  ) : (
                    ""
                  )}
                  {this.state.riceMilletSiteList?.length ? (
                    <div>
                      <div className="report-template-header">Rice/Millet Mills Reports</div>
                      {RiceMilletEnergyReport}
                    </div>
                  ) : (
                    ""
                  )}
                  {
                    // <div>
                    //   <div className="report-template-header">
                    //     POC Reports
                    //   </div>
                    //   {
                    //     <div className="template-type-section">
                    //       <div className="template-type-section-body">
                    //         <div className="template-type-section-body-row">
                    //           <div className="template-type-section-body-icon">
                    //             <img src={ReportIcon} />
                    //           </div>
                    //           <div
                    //             className="template-type-section-body-text"
                    //             onClick={() =>
                    //               this.getMultiAssetDataReportOnClick()
                    //             }
                    //           >
                    //             Multi Asset Data Report
                    //           </div>
                    //         </div>
                    //       </div>
                    //     </div>
                    //   }
                    //   {/* <div className="template-type-section">
                    //     <div className="template-type-section-body">
                    //       <div className="template-type-section-body-row">
                    //         <div className="template-type-section-body-icon">
                    //           <img src={ReportIcon} />
                    //         </div>
                    //         <div
                    //           className="template-type-section-body-text"
                    //           onClick={() =>
                    //             this.getLifetimeSummaryTrendOnClick()
                    //           }
                    //         >
                    //           Lifetime report
                    //         </div>
                    //       </div>
                    //     </div>
                    //   </div> */}
                    // </div>
                  }
                </AntRow>
              );
              let scheduleRpeortsRender = this.props.isScheduledReport ? (
                <AntCol xs={24} sm={6} md={6} lg={6} xl={6} xxl={6}>
                  <ScheduledList
                    client_id={this.props.client_id}
                    application_id={this.props.application_id}
                    totalData={this.state.totalData}
                  />
                </AntCol>
              ) : (
                ""
              );
              return (
                <AntLayout className={"contains"}>
                  <AntContent>
                    {(() => {
                      if (this.state.show_template) {
                        return this.props.dg_in_iot_mode ? (
                          <div className="reports-template-page">
                            {mainReports}
                          </div>
                        ) : window.innerWidth < 576 ? (
                          //   <AntTabs
                          //     defaultActiveKey="1"
                          //     tabPosition="top"
                          //     type="card"
                          //   >
                          //     <AntTabPane tab="Asset Reports" key="1">
                          //       <div className="reports-template-page">
                          //         {mainReports}
                          //       </div>
                          //     </AntTabPane>
                          //     <AntTabPane tab="Scheduled Reports" key="2">
                          //       <div className="reports-template-page">
                          //         {scheduleRpeortsRender}
                          //       </div>
                          //     </AntTabPane>
                          //   </AntTabs>
                          // )
                          <div className="reports-template-page">
                            {mainReports}
                          </div>
                        ) : (
                          <AntRow className="reports-template-page">
                            <AntCol span={18}>{mainReports}</AntCol>
                            {scheduleRpeortsRender}
                          </AntRow>
                        );
                      } else if (this.state.open_form) {
                        return (
                          <ReportsTemplatesForm
                            formData={this.state.formData}
                          />
                        );
                      } else if (this.state.customReportOnclick) {
                        // return (
                        // 	<CustomReportsDetails
                        // 		{...this.props}
                        // 	/>
                        // );
                      }
                    })()}
                  </AntContent>
                </AntLayout>
              );
            }
          }
        })()}
      </div>
    );
  }
}
