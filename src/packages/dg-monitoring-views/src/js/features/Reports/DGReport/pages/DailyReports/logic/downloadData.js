import React from "react";
import Backyard from "@datoms/js-utils/src/Backyard/Backyard_back";
import { TimeFormatter } from "../../../../../../data_handling/TimeFormatting";

export async function filesDownload() {
  let dataResponse = await this.getApiData(this.state.selectedThingArray, true);
  let apiData = dataResponse.response;
  let apiDataSnapShot = dataResponse.responseSnapshot;
  let missionList = dataResponse.missionList;
  let totalThings = this.state.totalData.things.filter((thing) =>
    this.state.selectedThingArray.includes(thing.id),
  );
  const { template_id, plan_description, vendor_id } = this.props;
  const { config_object, totalData, fromTime, uptoTime, inputDataSet } =
    this.state;
  const table_index_map = this.table_index_map;
  let getMachineInfoThingWise = {};
  if (totalData?.things?.length) {
    totalData.things.map((things) => {
      if (!getMachineInfoThingWise[things.id]) {
        getMachineInfoThingWise[things.id] = {};
      }
      getMachineInfoThingWise[things.id]["pdf_string1"] = this.getMachineInfo(
        things.id,
      ).pdf_string1;
      getMachineInfoThingWise[things.id]["pdf_string2"] = this.getMachineInfo(
        things.id,
      ).pdf_string2;
    });
  }
  let timeFormat = this.props.user_preferences?.time_format;
  let timezone = this.props.user_preferences?.timezone;
  let client_name = this.props.client_name;
  console.log("dataResponse", dataResponse);
  new Backyard({
    /* eslint-disable no-unused-expressions */
    scripts: [
      "https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js",
      "https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.23/moment-timezone.min.js",
      "https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.33/moment-timezone-with-data-1970-2030.min.js",
      "https://cdn.jsdelivr.net/npm/lodash@4.17.15/lodash.min.js",
    ],
    input: {
      apiData: apiData,
      apiDataSnapShot: apiDataSnapShot,
      totalThings: totalThings,
      totalData: totalData,
      fromTime: fromTime,
      uptoTime: uptoTime,
      template_id: template_id,
      plan_description: plan_description,
      missionList: missionList,
      config_object: config_object,
      table_index_map: table_index_map,
      vendor_id: vendor_id,
      getMachineInfoThingWise: getMachineInfoThingWise,
      timeFormat: timeFormat,
      timezone: timezone,
      client_name: client_name,
      inputDataSet: inputDataSet,
    },
    run: function (ctx, input, cb) {
      const {
        totalData,
        apiData,
        missionList,
        apiDataSnapShot,
        totalThings,
        fromTime,
        uptoTime,
        template_id,
        plan_description,
        config_object,
        table_index_map,
        vendor_id,
        getMachineInfoThingWise,
        timeFormat,
        timezone,
        client_name,
        inputDataSet,
      } = input;
      console.log("dataResponse2", apiData, missionList, apiDataSnapShot);
      let downloadData = { conf: [], data: [] };
      let hourMinVariable = timeFormat === "12_hr" ? "hh:mm A" : "HH:mm";
      let pdfMainHeaderOptionDate = {
        // pdf_top_line: true,
        // pdf_bottom_line: true,
        pdf_text_align: "center",
        textColor: [255, 255, 255],
        pdf_size: 13,
        fill: {
          fill_color: [255, 133, 0],
          y_value: 15,
          top: 1,
        },
      };
      let pushObjDate = pdfMainHeaderOptionDate;
      pushObjDate["compo"] = "Text";
      pushObjDate["props"] = {
        type: "bold",
      };
      pushObjDate["col_props"] = {
        span: 24,
      };
      let pdfMainHeaderOption = {
        // pdf_top_line: true,
        // pdf_bottom_line: true,
        pdf_text_align: "center",
        textColor: [255, 255, 255],
        pdf_size: 10,
        fill: {
          fill_color: [255, 133, 0],
          y_value: 13,
          top: 1,
        },
      };
      let pushObj = pdfMainHeaderOption;
      pushObj["compo"] = "Text";
      pushObj["props"] = {
        type: "bold",
      };
      pushObj["col_props"] = {
        span: 24,
      };
      let REPORT_TYPE = {
        text_conf: {
          props: {
            gutter: 5,
          },
          child: [
            {
              pdf_text_align: "center",
              textColor: [255, 255, 255],
              pdf_size: 16,
              type: "bold",
              fill: {
                fill_color: [255, 133, 0],
                y_value: 24,
                top: 8,
              },
              compo: "Text",
              props: {
                type: "bold",
              },
              col_props: {
                span: 24,
              },
              secStyle: {
                body: {
                  font: {
                    size: 20,
                    bold: true,
                  },
                },
              },
            },
            pushObjDate,
            pushObj,
          ],
        },
        text_data: [
          {
            textData: ["Daily Report - " + client_name],
          },
          {
            textData: [
              "From: " +
                ctx.moment
                  .unix(fromTime)
                  .tz(timezone)
                  .format("DD MMM YYYY, " + hourMinVariable) +
                " to " +
                ctx.moment
                  .unix(uptoTime)
                  .tz(timezone)
                  .format("DD MMM YYYY, " + hourMinVariable),
            ],
          },
          {
            textData: [
              "Generated on: " +
                ctx.moment
                  .unix(ctx.moment().unix())
                  .tz(timezone)
                  .format("DD MMM YYYY, " + hourMinVariable),
              "* All the times are in " +
                (timeFormat === "12_hr" ? "12" : "24") +
                " Hours time format",
              "",
            ],
          },
        ],
      };
      downloadData.conf.push(REPORT_TYPE.text_conf);
      downloadData.data.push(REPORT_TYPE.text_data);
      let headerNameKey = [];
      if (totalThings && totalThings && totalThings.length) {
        let dateArray = [];
        for (
          let i = ctx.moment.unix(uptoTime).tz(timezone).startOf("day").unix();
          i >= fromTime;
          i -= 86400
        ) {
          dateArray.push(i);
        }
        totalThings.map(function (things, ind) {
          let isThingMechanical =
            things?.thing_details?.dg_parameter_type === "mechanical";
          const isFuel = ctx["_"].find(things?.parameters, function (o) {
            return ["fuel", "fuel_lt", "fuel_litre"].includes(o.key);
          });
          let isGmmco = parseInt(vendor_id) === 1062;
          let findLoad = ctx["_"].find(things?.parameters, {
            key: "load_percentage",
          });
          if (template_id === 21) {
            headerNameKey = [
              {
                title: "Date",
                dataIndex: "date",
              },
              {
                title: "Runhour " + (isGmmco ? "(SMU)" : "(HH:MM)"),
                dataIndex: "runhour",
              },
              {
                title: "Runs",
                dataIndex: "trips",
              },
              {
                title: "Fuel Consumed (L)",
                dataIndex: "fuel_cons",
              },
              {
                title:
                  "Fuel consumption per hr (Litre/" +
                  (isGmmco ? "SMU" : "Hr") +
                  ")",
                dataIndex: "mileage",
              },
              {
                title: "Start Fuel (L)",
                dataIndex: "start_fuel",
              },
              {
                title: "End Fuel (L)",
                dataIndex: "end_fuel",
              },
            ];
          } else if (template_id === 24) {
            headerNameKey = [
              {
                title: "Date",
                dataIndex: "date",
              },
              {
                title: "Runhour " + (isGmmco ? "(SMU)" : "(HH:MM)"),
                dataIndex: "runhour",
              },
              {
                title: "Runs",
                dataIndex: "trips",
              },
              {
                title: "Fuel Consumed (%)",
                dataIndex: "fuel_consumed_perc",
              },
              {
                title: "Energy Generated (kWh)",
                dataIndex: "enrg_gen",
              },
              {
                title: "Start Energy (kWh)",
                dataIndex: "start_energy",
              },
              {
                title: "End Energy (kWh)",
                dataIndex: "end_energy",
              },
              {
                title: "Start Fuel (%)",
                dataIndex: "start_fuel",
              },
              {
                title: "End Fuel (%)",
                dataIndex: "end_fuel",
              },
              {
                title: "Load Percentage (%)",
                dataIndex: "load_percentage",
              },
              {
                title: "Fuel Filled (%)",
                dataIndex: "fuel_filled_perc",
              }
            ];
          } else {
            if (isThingMechanical) {
              if (isFuel) {
                headerNameKey = [
                  {
                    title: "Date",
                    dataIndex: "date",
                  },
                  {
                    title: "Runhour " + (isGmmco ? "(SMU)" : "(HH:MM)"),
                    dataIndex: "runhour",
                  },
                  {
                    title: "Runs",
                    dataIndex: "trips",
                  },
                  {
                    title: "Fuel Consumed (L)",
                    dataIndex: "fuel_cons",
                  },
                  {
                    title:
                      "Fuel consumption per hr (Litre/" +
                      (isGmmco ? "SMU" : "Hr") +
                      ")",
                    dataIndex: "mileage",
                  },
                  {
                    title: "Start Fuel (L)",
                    dataIndex: "start_fuel",
                  },
                  {
                    title: "End Fuel (L)",
                    dataIndex: "end_fuel",
                  },
                  {
                    title: "Fuel Filled (L)",
                    dataIndex: "fuel_filled",
                  },
                  {
                    title: "Fuel Drained (L)",
                    dataIndex: "fuel_drained",
                  },
                ];
              } else {
                headerNameKey = [
                  {
                    title: "Date",
                    dataIndex: "date",
                  },
                  {
                    title: "Runhour " + (isGmmco ? "(SMU)" : "(HH:MM)"),
                    dataIndex: "runhour",
                  },
                ];
              }
            } else {
              if (
                plan_description &&
                plan_description.reports &&
                plan_description.reports.daily_report_details_params &&
                plan_description.reports.daily_report_details_params.length
              ) {
                headerNameKey = [
                  {
                    title: "Date",
                    dataIndex: "date",
                  },
                ];

                plan_description.reports.daily_report_details_params.forEach(
                  (keys) => {
                    if (keys === "rnhr") {
                      headerNameKey[headerNameKey.length] = {
                        title: "Runhour " + (isGmmco ? "(SMU)" : "(HH:MM)"),
                        dataIndex: "runhour",
                      };
                    }
                    if (keys === "dg_runs") {
                      headerNameKey[headerNameKey.length] = {
                        title: "Runs",
                        dataIndex: "trips",
                      };
                    }
                    if (keys === "fuel_consumption" && isFuel) {
                      headerNameKey[headerNameKey.length] = {
                        title: "Fuel Consumed (L)",
                        dataIndex: "fuel_cons",
                      };
                    }
                    if (keys === "enrg") {
                      headerNameKey[headerNameKey.length] = {
                        title: "Energy Generated (kWh)",
                        dataIndex: "enrg_gen",
                      };
                      headerNameKey[headerNameKey.length] = {
                        title: "Start Energy (kWh)",
                        dataIndex: "start_energy",
                      };
                      headerNameKey[headerNameKey.length] = {
                        title: "End Energy (kWh)",
                        dataIndex: "end_energy",
                      };
                    }
                    if (keys === "fuel_consumption_per_hr" && isFuel) {
                      headerNameKey[headerNameKey.length] = {
                        title:
                          "Fuel consumption per hr (Litre/" +
                          (isGmmco ? "SMU" : "Hr") +
                          ")",
                        dataIndex: "mileage",
                      };
                    }
                    if (keys === "start_fuel" && isFuel) {
                      headerNameKey[headerNameKey.length] = {
                        title: "Start Fuel (L)",
                        dataIndex: "start_fuel",
                      };
                    }
                    if (keys === "end_fuel" && isFuel) {
                      headerNameKey[headerNameKey.length] = {
                        title: "End Fuel (L)",
                        dataIndex: "end_fuel",
                      };
                    }
                    if (keys === "fuel_consumption_per_enrg" && isFuel) {
                      headerNameKey[headerNameKey.length] = {
                        title: "Fuel Consumed per unit kWh (L/kWh)",
                        dataIndex: "fuel_per_enrg",
                      };
                    }
                    if (keys === "enrg_per_fuel_consumption" && isFuel) {
                      headerNameKey[headerNameKey.length] = {
                        title:
                          "Energy Generated per unit Litre Fuel Consumed (kWh/L)",
                        dataIndex: "enrg_per_fuel",
                      };
                    }
                    if (findLoad && keys === "load_percentage") {
                      headerNameKey[headerNameKey.length] = {
                        title: "Load Percentage (%)",
                        dataIndex: "load_percentage",
                      };
                    }
                  },
                );
                if (isFuel) {
                  headerNameKey[headerNameKey.length] = {
                    title: "Fuel Filled (L)",
                    dataIndex: "fuel_filled",
                  };
                  headerNameKey[headerNameKey.length] = {
                    title: "Fuel Drained (L)",
                    dataIndex: "fuel_drained",
                  };
                }
              }
            }
          }
          if (ctx["_"].find(things?.parameters, { key: "mn_hlth_st" }) && template_id !== 24) {
            headerNameKey.push({
              title: "Mains Runhour (HH:MM)",
              dataIndex: "mains_rnhr",
            });
          }
          let final_filtered_column = [];
          headerNameKey.map((row) => {
            if (
              config_object["table"][table_index_map[row.dataIndex]] ||
              !table_index_map[row.dataIndex]
            ) {
              final_filtered_column.push(row);
            }
          });
          headerNameKey = final_filtered_column;
          let dataObj = [],
            starAdded = false;
          let filteredDataResponse = [];
          if (apiData) {
            filteredDataResponse = ctx["_"].filter(apiData.data, {
              thing_id: parseInt(things.id),
            });
          }
          let sortedResponse = ctx["_"].sortBy(filteredDataResponse, ["time"]);

          let filteredDataresponseSnapshot = [];
          if (apiDataSnapShot) {
            filteredDataresponseSnapshot = ctx["_"].filter(
              apiDataSnapShot.data,
              {
                thing_id: parseInt(things.id),
              },
            );
          }

          let sortedResponseSnapshot = ctx["_"].sortBy(
            filteredDataresponseSnapshot,
            ["time"],
          );
          let findCalculatedEnergy = ctx["_"].find(things?.parameters, {
            key: "calculated_energy",
          });
          let filteredMissionArray = [];
          if (missionList?.response?.Missions?.length) {
            filteredMissionArray = missionList.response.Missions.filter(
              (missions) => missions.Devices.includes(things.id.toString()),
            );
          }
          dateArray.map((date, index) => {
            let filteredDataResponseData = ctx["_"].find(sortedResponse, {
              time: date,
            })
              ? ctx["_"].find(sortedResponse, { time: date })
              : undefined;
            let runHour = "NA",
              calculatedRnhr = 0,
              runMin = "NA",
              runSec = "NA",
              mainsRnhr = "NA",
              mainsCalculatedRnhr = 0,
              mainsRunMin = "NA",
              mainsRunSec = "NA",
              fuelCons = "NA",
              rpm = "NA",
              enrgGen = "NA",
              startFuel = "NA",
              endFuel = "NA",
              startEnergy = "NA",
              endEnergy = "NA",
              filteredMissionWithStartDate = [],
              missionCount = 0,
              loadCalculation = "NA",
              fuelFilled = "NA",
              fuelDrained = "NA",
              fuel_consumed_perc = "NA",
              fuel_filed_perc = "NA";
            if (missionList) {
              filteredMissionWithStartDate = ctx["_"].filter(
                filteredMissionArray,
                function (o) {
                  if (
                    ctx
                      .moment(o.EndDate, ctx.moment.ISO_8601)
                      .format("DD MMM YYYY") !== "Invalid date"
                  ) {
                    return (
                      ctx
                        .moment(o.StartDate, ctx.moment.ISO_8601)
                        .format("DD MMM YYYY") ===
                      ctx.moment.unix(date).format("DD MMM YYYY")
                    );
                  }
                },
              );
              let star = "";
              filteredMissionWithStartDate.map((filteredMission) => {
                if (
                  ctx
                    .moment(filteredMission.EndDate, ctx.moment.ISO_8601)
                    .format("DD MMM YYYY") !==
                  ctx.moment.unix(date).tz(timezone).format("DD MMM YYYY")
                ) {
                  star = "*";
                  starAdded = true;
                }
                missionCount = filteredMissionWithStartDate.length + " " + star;
                return missionCount;
              });
            }
            if (filteredDataResponseData !== undefined) {
              if (
                filteredDataResponseData.parameter_values &&
                filteredDataResponseData.parameter_values.calculated_runhour &&
                filteredDataResponseData.parameter_values.calculated_runhour
                  .sum >= 0
              ) {
                calculatedRnhr =
                  filteredDataResponseData.parameter_values.calculated_runhour
                    .sum;
                runHour =
                  Math.floor(calculatedRnhr / 3600) < 10
                    ? "0" + Math.floor(calculatedRnhr / 3600)
                    : Math.floor(calculatedRnhr / 3600);

                let modCalculateRnhr = calculatedRnhr % 3600;

                runMin =
                  Math.floor(modCalculateRnhr / 60) < 10
                    ? "0" + Math.floor(modCalculateRnhr / 60)
                    : Math.floor(modCalculateRnhr / 60);

                runSec =
                  Math.floor(modCalculateRnhr % 60) < 10
                    ? "0" + Math.floor(modCalculateRnhr % 60)
                    : Math.floor(modCalculateRnhr % 60);
              }
              if (
                filteredDataResponseData?.parameter_values
                  ?.calculated_mains_runhour?.sum >= 0
              ) {
                mainsCalculatedRnhr =
                  filteredDataResponseData.parameter_values
                    .calculated_mains_runhour.sum;
                mainsRnhr =
                  Math.floor(mainsCalculatedRnhr / 3600) < 10
                    ? "0" + Math.floor(mainsCalculatedRnhr / 3600)
                    : Math.floor(mainsCalculatedRnhr / 3600);

                let modCalculateRnhr = mainsCalculatedRnhr % 3600;

                mainsRunMin =
                  Math.floor(modCalculateRnhr / 60) < 10
                    ? "0" + Math.floor(modCalculateRnhr / 60)
                    : Math.floor(modCalculateRnhr / 60);

                mainsRunSec =
                  Math.floor(modCalculateRnhr % 60) < 10
                    ? "0" + Math.floor(modCalculateRnhr % 60)
                    : Math.floor(modCalculateRnhr % 60);
              }

              if (
                filteredDataResponseData.parameter_values &&
                filteredDataResponseData.parameter_values.fuel_consumption &&
                filteredDataResponseData.parameter_values.fuel_consumption
                  .sum >= 0
              ) {
                fuelCons =
                  filteredDataResponseData.parameter_values.fuel_consumption
                    .sum;
              }
              if (
                filteredDataResponseData.parameter_values &&
                filteredDataResponseData.parameter_values.rpm &&
                filteredDataResponseData.parameter_values.rpm.avg >= 0
              ) {
                rpm = filteredDataResponseData.parameter_values.rpm.avg;
              }
              if (findCalculatedEnergy && findCalculatedEnergy !== undefined) {
                if (
                  filteredDataResponseData.parameter_values &&
                  filteredDataResponseData.parameter_values.calculated_energy &&
                  filteredDataResponseData.parameter_values.calculated_energy
                    .sum >= 0
                ) {
                  enrgGen =
                    filteredDataResponseData.parameter_values.calculated_energy
                      .sum;
                }
              }
              let findSortedResponseSnapshot = ctx["_"].find(
                sortedResponseSnapshot,
                { time: date },
              )
                ? ctx["_"].find(sortedResponseSnapshot, {
                    time: date,
                  })
                : undefined;
                if (
                  things?.thing_details?.capacity &&
                  (findSortedResponseSnapshot?.parameter_values?.fuel || findSortedResponseSnapshot?.parameter_values?.trip_valid_fuel)
                ) {
                  startFuel =
                    template_id === 24
                      ? parseFloat(
                          findSortedResponseSnapshot.parameter_values?.trip_valid_fuel?.initial,
                        )
                      : (things.thing_details.capacity *
                          parseFloat(
                            findSortedResponseSnapshot.parameter_values?.fuel?.initial,
                          )) /
                        100;
                  endFuel =
                    template_id === 24
                      ? parseFloat(
                          findSortedResponseSnapshot.parameter_values?.trip_valid_fuel?.snapshot,
                        )
                      : (things.thing_details.capacity *
                          parseFloat(
                            findSortedResponseSnapshot.parameter_values?.fuel?.snapshot,
                          )) /
                        100;
                }
              if (
                things.thing_details &&
                things.thing_details.capacity &&
                findSortedResponseSnapshot &&
                findSortedResponseSnapshot.parameter_values &&
                findSortedResponseSnapshot.parameter_values.enrg
              ) {
                startEnergy = parseFloat(
                  findSortedResponseSnapshot.parameter_values.enrg.initial,
                );
                endEnergy = parseFloat(
                  findSortedResponseSnapshot.parameter_values.enrg.snapshot,
                );
              }
              if (findLoad && findLoad !== undefined) {
                if (
                  filteredDataResponseData.parameter_values.load_percentage &&
                  !isNaN(
                    filteredDataResponseData.parameter_values.load_percentage
                      .avg,
                  )
                ) {
                  loadCalculation = parseFloat(
                    filteredDataResponseData.parameter_values.load_percentage
                      .avg,
                  ).toFixed(2);
                } else {
                  loadCalculation = "-";
                }
              } else if (
                things.thing_details &&
                things.thing_details.kva > 0 &&
                filteredDataResponseData.parameter_values.calculated_runhour &&
                filteredDataResponseData.parameter_values.calculated_runhour
                  .sum > 0
              ) {
                loadCalculation = parseFloat(
                  (enrgGen /
                    (things.thing_details.kva *
                      0.8 *
                      (filteredDataResponseData.parameter_values
                        .calculated_runhour.sum /
                        3600))) *
                    100,
                ).toFixed(2);
              } else {
                loadCalculation = "-";
              }
              if (
                filteredDataResponseData.parameter_values &&
                filteredDataResponseData.parameter_values.fuel_filled &&
                filteredDataResponseData.parameter_values.fuel_filled.sum >= 0
              ) {
                fuelFilled =
                  filteredDataResponseData.parameter_values.fuel_filled.sum;
              }
              if (
                filteredDataResponseData.parameter_values &&
                filteredDataResponseData.parameter_values.fuel_theft &&
                filteredDataResponseData.parameter_values.fuel_theft.sum >= 0
              ) {
                fuelDrained =
                  filteredDataResponseData.parameter_values.fuel_theft.sum;
              }
              if (template_id === 21) {
                dataObj.push({
                  date: ctx.moment
                    .unix(date)
                    .tz(timezone)
                    .format("DD MMM YYYY"),
                  runhour:
                    runHour === "NA"
                      ? "NA"
                      : runHour + ":" + runMin,
                  mains_rnhr:
                    mainsRnhr === "NA"
                      ? "NA"
                      : mainsRnhr + ":" + mainsRunMin,
                  trips: runHour === "NA" ? "NA" : missionCount,
                  fuel_cons:
                    fuelCons === "NA"
                      ? "NA"
                      : !isNaN(parseFloat(fuelCons))
                        ? parseFloat(fuelCons).toFixed(2)
                        : "-",
                  mileage:
                    calculatedRnhr === "NA" && fuelCons === "NA"
                      ? "NA"
                      : calculatedRnhr > 0
                        ? !isNaN(parseFloat(fuelCons / (calculatedRnhr / 3600)))
                          ? parseFloat(
                              fuelCons / (calculatedRnhr / 3600),
                            ).toFixed(2)
                          : "-"
                        : "-",
                  start_fuel:
                    startFuel === "NA"
                      ? "NA"
                      : !isNaN(parseFloat(startFuel))
                        ? parseFloat(startFuel).toFixed(2)
                        : "-",
                  end_fuel:
                    endFuel === "NA"
                      ? "NA"
                      : !isNaN(parseFloat(endFuel))
                        ? parseFloat(endFuel).toFixed(2)
                        : "-",
                  start_energy:
                    startEnergy === "NA"
                      ? "NA"
                      : !isNaN(parseFloat(startEnergy))
                        ? parseFloat(startEnergy).toFixed(2)
                        : "-",
                  end_energy:
                    endEnergy === "NA"
                      ? "NA"
                      : !isNaN(parseFloat(endEnergy))
                        ? parseFloat(endEnergy).toFixed(2)
                        : "-",
                });
              } else if (template_id === 24) {
                dataObj.push({
                  date: ctx.moment
                    .unix(date)
                    .tz(timezone)
                    .format("DD MMM YYYY"),
                  runhour:
                    runHour === "NA"
                      ? "NA"
                      : runHour + ":" + runMin,
                  mains_rnhr:
                    mainsRnhr === "NA"
                      ? "NA"
                      : mainsRnhr + ":" + mainsRunMin,
                  trips: runHour === "NA" ? "NA" : missionCount,
                  enrg_gen:
                    enrgGen === "NA"
                      ? "NA"
                      : !isNaN(parseFloat(enrgGen))
                        ? parseFloat(enrgGen).toFixed(2)
                        : "-",
                  start_fuel:
                    startFuel === "NA"
                      ? "NA"
                      : !isNaN(parseFloat(startFuel))
                        ? parseFloat(startFuel).toFixed(2)
                        : "-",
                  end_fuel:
                    endFuel === "NA"
                      ? "NA"
                      : !isNaN(parseFloat(endFuel))
                        ? parseFloat(endFuel).toFixed(2)
                        : "-",
                  start_energy:
                    startEnergy === "NA"
                      ? "NA"
                      : !isNaN(parseFloat(startEnergy))
                        ? parseFloat(startEnergy).toFixed(2)
                        : "-",
                  end_energy:
                    endEnergy === "NA"
                      ? "NA"
                      : !isNaN(parseFloat(endEnergy))
                        ? parseFloat(endEnergy).toFixed(2)
                        : "-",
                  load_percentage: loadCalculation,fuel_consumed_perc:
                  fuelCons === "NA"
                    ? "NA"
                    : !isNaN(parseFloat(fuelCons)) && things?.thing_details?.capacity
                      ? parseFloat(
                          (fuelCons / things.thing_details.capacity) * 100,
                        ).toFixed(0)
                      : "-",
                fuel_filled_perc:
                  fuelFilled === "NA"
                    ? "NA"
                    : !isNaN(parseFloat(fuelFilled)) && things?.thing_details?.capacity
                      ? parseFloat(
                          (fuelFilled / things.thing_details.capacity) *
                            100,
                        ).toFixed(0)
                      : "-",
              });
              } else {
                dataObj.push({
                  date: ctx.moment
                    .unix(date)
                    .tz(timezone)
                    .format("DD MMM YYYY"),
                  runhour:
                    runHour === "NA"
                      ? "NA"
                      : runHour + ":" + runMin,
                  mains_rnhr:
                    mainsRnhr === "NA"
                      ? "NA"
                      : mainsRnhr + ":" + mainsRunMin,
                  trips: runHour === "NA" ? "NA" : missionCount,
                  fuel_cons:
                    fuelCons === "NA"
                      ? "NA"
                      : !isNaN(parseFloat(fuelCons))
                        ? parseFloat(fuelCons).toFixed(2)
                        : "-",
                  enrg_gen:
                    enrgGen === "NA"
                      ? "NA"
                      : !isNaN(parseFloat(enrgGen))
                        ? parseFloat(enrgGen).toFixed(2)
                        : "-",
                  mileage:
                    calculatedRnhr === "NA" && fuelCons === "NA"
                      ? "NA"
                      : calculatedRnhr > 0
                        ? !isNaN(parseFloat(fuelCons / (calculatedRnhr / 3600)))
                          ? parseFloat(
                              fuelCons / (calculatedRnhr / 3600),
                            ).toFixed(2)
                          : "-"
                        : "-",
                  start_fuel:
                    startFuel === "NA"
                      ? "NA"
                      : !isNaN(parseFloat(startFuel))
                        ? parseFloat(startFuel).toFixed(2)
                        : "-",
                  end_fuel:
                    endFuel === "NA"
                      ? "NA"
                      : !isNaN(parseFloat(endFuel))
                        ? parseFloat(endFuel).toFixed(2)
                        : "-",
                  start_energy:
                    startEnergy === "NA"
                      ? "NA"
                      : !isNaN(parseFloat(startEnergy))
                        ? parseFloat(startEnergy).toFixed(2)
                        : "-",
                  end_energy:
                    endEnergy === "NA"
                      ? "NA"
                      : !isNaN(parseFloat(endEnergy))
                        ? parseFloat(endEnergy).toFixed(2)
                        : "-",
                  fuel_per_enrg:
                    enrgGen === "NA" && fuelCons === "NA"
                      ? "NA"
                      : enrgGen > 0
                        ? !isNaN(parseFloat(fuelCons / enrgGen))
                          ? parseFloat(fuelCons / enrgGen).toFixed(2)
                          : "-"
                        : "-",
                  enrg_per_fuel:
                    enrgGen === "NA" && fuelCons === "NA"
                      ? "NA"
                      : fuelCons > 0
                        ? !isNaN(parseFloat(enrgGen / fuelCons))
                          ? parseFloat(enrgGen / fuelCons).toFixed(2)
                          : "-"
                        : "-",

                  load_percentage: loadCalculation,
                  fuel_filled:
                    fuelFilled === "NA"
                      ? "NA"
                      : !isNaN(parseFloat(fuelFilled))
                        ? parseFloat(fuelFilled).toFixed(2)
                        : "-",
                  fuel_drained:
                    fuelDrained === "NA"
                      ? "NA"
                      : !isNaN(parseFloat(fuelDrained))
                        ? parseFloat(fuelDrained).toFixed(2)
                        : "-",
                });
                if (
                  plan_description &&
                  plan_description.reports &&
                  plan_description.reports.daily_report_details_params &&
                  plan_description.reports.daily_report_details_params.length
                ) {
                  if (dataObj && dataObj.length) {
                    dataObj.map((tableData) => {
                      if (
                        !plan_description.reports.daily_report_details_params.includes(
                          "rnhr",
                        )
                      ) {
                        delete tableData.runhour;
                      }
                      if (
                        !plan_description.reports.daily_report_details_params.includes(
                          "dg_runs",
                        )
                      ) {
                        delete tableData.trips;
                      }
                      if (
                        !plan_description.reports.daily_report_details_params.includes(
                          "fuel_consumption",
                        )
                      ) {
                        delete tableData.fuel_cons;
                      }
                      if (
                        !plan_description.reports.daily_report_details_params.includes(
                          "enrg",
                        )
                      ) {
                        delete tableData.enrg_gen;
                      }
                      if (
                        !plan_description.reports.daily_report_details_params.includes(
                          "fuel_consumption_per_hr",
                        )
                      ) {
                        delete tableData.mileage;
                      }
                      if (
                        !plan_description.reports.daily_report_details_params.includes(
                          "start_fuel",
                        )
                      ) {
                        delete tableData.start_fuel;
                      }
                      if (
                        !plan_description.reports.daily_report_details_params.includes(
                          "end_fuel",
                        )
                      ) {
                        delete tableData.end_fuel;
                      }
                      if (
                        !plan_description.reports.daily_report_details_params.includes(
                          "fuel_consumption_per_enrg",
                        )
                      ) {
                        delete tableData.fuel_per_enrg;
                      }
                      if (
                        !plan_description.reports.daily_report_details_params.includes(
                          "enrg_per_fuel_consumption",
                        )
                      ) {
                        delete tableData.enrg_per_fuel;
                      }
                      if (
                        !plan_description.reports.daily_report_details_params.includes(
                          "load_percentage",
                        )
                      ) {
                        delete tableData.load_percentage;
                      }
                    });
                  }
                }
              }
            }
          });

          let pageConfig = {
            pdf_force_new_page: ind === 0 ? false : true,
            secStyle: {
              body: {
                font: {
                  size: 16,
                  bold: true,
                },
              },
            },
          };
          let textPush = pageConfig;
          textPush["compo"] = "Text";
          textPush["props"] = {
            type: "bold",
          };

          textPush["col_props"] = {
            span: 24,
          };
          textPush["pdf_size"] = 14;
          textPush["type"] = "bold";
          textPush["excelNewSheet"] = true;

          let machine_txt = { pdf_force_new_page: false };
          machine_txt["compo"] = "Text";
          machine_txt["col_props"] = {
            span: 24,
          };
          machine_txt["pdf_size"] = 10;
          let { text_conf, text_data } = {
            text_conf: {
              props: {
                gutter: 10,
              },
              child: [textPush],
            },
            text_data: [
              {
                textData: ["", "Asset: " + things.name],
              },
            ],
          };
          downloadData.conf.push(text_conf);
          downloadData.data.push(text_data);
          let pdf_string1 = getMachineInfoThingWise[things.id].pdf_string1,
            pdf_string2 = getMachineInfoThingWise[things.id].pdf_string2;
          let pdfStringInsight = {
            pdf_force_new_page: false,
          };
          let pdfStringHeader = pdfStringInsight;
          pdfStringHeader["compo"] = "Text";
          pdfStringHeader["props"] = {
            type: "bold",
          };

          pdfStringHeader["col_props"] = {
            span: 24,
          };
          pdfStringHeader["pdf_size"] = 10;
          let { pdf_string1Headerprops, pdf_string1HeaderText } = {
            pdf_string1Headerprops: {
              props: {
                gutter: 10,
              },
              child: [pdfStringHeader, pdfStringHeader],
            },

            pdf_string1HeaderText: [
              {
                textData: ["", pdf_string1],
              },
              { textData: [pdf_string2] },
            ],
          };
          downloadData.conf.push(pdf_string1Headerprops);
          downloadData.data.push(pdf_string1HeaderText);
          let summaryInsight = {
            pdf_force_new_page: false,
          };
          let sumarryHeader = summaryInsight;
          sumarryHeader["compo"] = "Text";
          sumarryHeader["type"] = "bold";

          sumarryHeader["col_props"] = {
            span: 24,
          };
          sumarryHeader["pdf_size"] = 12;
          let { summaryHeaderprops, summaryHeaderText } = {
            summaryHeaderprops: {
              props: {
                gutter: 10,
              },
              child: [sumarryHeader],
            },
            summaryHeaderText: [
              {
                textData: ["", "Summary"],
              },
            ],
          };
          downloadData.conf.push(summaryHeaderprops);
          downloadData.data.push(summaryHeaderText);
          let SummaryData = [],
            totalRunhour = 0,
            fuelConsumed = 0,
            compositeFuelConsumed = 0,
            compositeEnrgGen = 0,
            mileage = 0,
            totalenrgGen = 0,
            fuelPerLoad = 0,
            loadPerFuel = 0,
            totalfuelFilled = 0,
            loadSum = 0,
            tripRnhr = 0,
            totalfuelDrained = 0;
          let filteredResponse =
            apiData && apiData.data && apiData.data.length
              ? ctx["_"].filter(apiData.data, {
                  thing_id: parseInt(things.id),
                })
              : [];

          if (filteredResponse && filteredResponse.length) {
            filteredResponse.forEach((filteredResponseData) => {
              const paramValues = filteredResponseData.parameter_values;

              if (paramValues) {
                // Validate and sum totalRunhour
                const runhourSum =
                  parseFloat(paramValues.calculated_runhour?.sum) || 0;
                if (!isNaN(runhourSum)) {
                  totalRunhour += runhourSum;
                }

                // Validate and sum fuelConsumed
                const fuelSum =
                  parseFloat(paramValues.fuel_consumption?.sum) || 0;
                if (!isNaN(fuelSum) && fuelConsumed !== "-") {
                  fuelConsumed =
                    (parseFloat(fuelConsumed) || 0) + parseFloat(fuelSum);
                }
                console.log("dlfhkjhdkjhfjhf", fuelSum, fuelConsumed);
                // Validate and sum compositeFuelConsumed
                if (paramValues.fuel_consumption?.sum) {
                  const fuelSum =
                    parseFloat(paramValues.fuel_consumption.sum) || 0;
                  if (findCalculatedEnergy) {
                    if (paramValues.calculated_energy?.sum > 0) {
                      const calcEnergySum =
                        parseFloat(paramValues.calculated_energy.sum) || 0;
                      if (!isNaN(calcEnergySum)) {
                        compositeFuelConsumed += fuelSum;
                      }
                    }
                  }
                }

                // Validate and sum compositeEnrgGen
                if (findCalculatedEnergy) {
                  if (paramValues.calculated_energy?.sum) {
                    const calcEnergyGen =
                      parseFloat(paramValues.calculated_energy.sum) || 0;
                    const fuelSum =
                      parseFloat(paramValues.fuel_consumption?.sum) || 0;
                    if (!isNaN(calcEnergyGen) && fuelSum > 0) {
                      compositeEnrgGen += calcEnergyGen;
                    }
                  }
                }

                // Validate and sum totalenrgGen
                const energyGenSum =
                  parseFloat(paramValues.calculated_energy?.sum) || 0;
                if (!isNaN(energyGenSum) && totalenrgGen !== "-") {
                  totalenrgGen = (totalenrgGen || 0) + energyGenSum;
                }
                console.log("totalenrgGen", totalenrgGen, energyGenSum);
                // Validate and sum totalfuelFilled
                const fuelFilledSum =
                  parseFloat(paramValues.fuel_filled?.sum) || 0;
                if (!isNaN(fuelFilledSum)) {
                  totalfuelFilled += fuelFilledSum;
                }

                // Validate and sum totalfuelDrained
                const fuelTheftSum =
                  parseFloat(paramValues.fuel_theft?.sum) || 0;
                if (!isNaN(fuelTheftSum)) {
                  totalfuelDrained += fuelTheftSum;
                }
              }

              return filteredResponseData;
            });
          }

          let filteredMissions = [];
          if (
            missionList &&
            missionList.response &&
            Array.isArray(missionList.response.Missions)
          ) {
            filteredMissions = missionList.response.Missions.filter(
              (missions) => missions.Devices.includes(things.id.toString()),
            );
          }
          if (filteredMissions && filteredMissions.length) {
            filteredMissions.map((filteredResponseData) => {
              if (findLoad && findLoad !== undefined) {
                if (
                  filteredResponseData &&
                  filteredResponseData.Details &&
                  filteredResponseData.Details.aggregate_data &&
                  filteredResponseData.Details.aggregate_data
                    .calculated_runhour &&
                  filteredResponseData.Details.aggregate_data.calculated_runhour
                    .sum
                ) {
                  tripRnhr +=
                    filteredResponseData.Details.aggregate_data
                      .calculated_runhour.sum;
                }
                if (
                  filteredResponseData &&
                  filteredResponseData.Details &&
                  filteredResponseData.Details.aggregate_data &&
                  filteredResponseData.Details.aggregate_data.load_percentage &&
                  filteredResponseData.Details.aggregate_data.load_percentage
                    .avg &&
                  filteredResponseData.Details.aggregate_data
                    .calculated_runhour &&
                  filteredResponseData.Details.aggregate_data.calculated_runhour
                    .sum
                ) {
                  loadSum +=
                    filteredResponseData.Details.aggregate_data
                      .calculated_runhour.sum *
                    filteredResponseData.Details.aggregate_data.load_percentage
                      .avg;
                }
              } else {
                if (
                  filteredResponseData.Details &&
                  filteredResponseData.Details.aggregate_data &&
                  filteredResponseData.Details.aggregate_data &&
                  filteredResponseData.Details.aggregate_data
                    .calculated_runhour &&
                  filteredResponseData.Details.aggregate_data.calculated_runhour
                    .sum
                ) {
                  if (
                    findCalculatedEnergy &&
                    findCalculatedEnergy !== undefined
                  ) {
                    if (
                      filteredResponseData.Details &&
                      filteredResponseData.Details.aggregate_data &&
                      filteredResponseData.Details.aggregate_data &&
                      filteredResponseData.Details.aggregate_data
                        .calculated_energy &&
                      filteredResponseData.Details.aggregate_data
                        .calculated_energy.sum
                    ) {
                      loadSum +=
                        filteredResponseData.Details.aggregate_data
                          .calculated_runhour.sum *
                        (filteredResponseData.Details.aggregate_data
                          .calculated_energy.sum /
                          (things.thing_details.kva *
                            0.8 *
                            (filteredResponseData.Details.aggregate_data
                              .calculated_runhour.sum /
                              3600))) *
                        100;
                    }
                  }
                }
              }
            });
          }

          let hours =
            Math.floor(totalRunhour / 3600) < 10
              ? "0" + Math.floor(totalRunhour / 3600)
              : Math.floor(totalRunhour / 3600);
          let reminderRnhr = "NA";
          if (!reminderRnhr) {
            reminderRnhr = 0;
          }
          reminderRnhr = totalRunhour % 3600;
          let minutes =
            Math.floor(reminderRnhr / 60) < 10
              ? "0" + Math.floor(reminderRnhr / 60)
              : Math.floor(reminderRnhr / 60);
          let seconds =
            Math.floor(reminderRnhr % 60) < 10
              ? "0" + Math.floor(reminderRnhr % 60)
              : Math.floor(reminderRnhr % 60);
          mileage =
            totalRunhour > 0 && fuelConsumed !== "-"
              ? (fuelConsumed / (totalRunhour / 3600)).toFixed(2)
              : "-";
          fuelPerLoad =
            compositeEnrgGen > 0 && compositeFuelConsumed !== "-"
              ? (compositeFuelConsumed / compositeEnrgGen).toFixed(2)
              : "-";
          loadPerFuel =
            compositeFuelConsumed > 0 && compositeEnrgGen !== "-"
              ? (compositeEnrgGen / compositeFuelConsumed).toFixed(2)
              : "-";
          loadSum =
            tripRnhr > 0 ? parseFloat(loadSum / tripRnhr).toFixed(2) : 0.0;
          console.log("dlfhlkhhf", fuelConsumed);
          if (template_id === 21) {
            SummaryData.push(
              {
                parameter:
                  "Total Runhour " + (isGmmco ? "(SMU)" : "(HH:MM)"),
                value: hours + " : " + minutes,
                key: "total_runhour",
              },
              {
                parameter: "Total Fuel Consumed(L)",
                value:
                  fuelConsumed !== "-" && !isNaN(parseFloat(fuelConsumed))
                    ? parseFloat(fuelConsumed)
                    : "-",
                key: "total_fuel_consumed",
              },
              {
                parameter:
                  "Fuel consumption per hr(L/" + (isGmmco ? "SMU" : "Hr") + ")",
                value: mileage,
                key: "fuel_consumption_per_hr",
              },
              {
                parameter: "Total Fuel Filled (L)",
                value: !isNaN(parseFloat(totalfuelFilled))
                  ? parseFloat(totalfuelFilled).toFixed(2)
                  : "-",
                key: "total_fuel_filled",
              },
              {
                parameter: "Total Fuel Drained (L)",
                value: !isNaN(parseFloat(totalfuelDrained))
                  ? parseFloat(totalfuelDrained).toFixed(2)
                  : "-",
                key: "total_fuel_drained",
              },
            );
          } else if (template_id === 24) {
            SummaryData.push(
              {
                parameter:
                  "Total Runhour " + (isGmmco ? "(SMU)" : "(HH:MM)"),
                value: hours + " : " + minutes,
                key: "total_runhour",
              },
              {
                parameter: "Total Energy (kWh)",
                value:
                  totalenrgGen !== "-" && !isNaN(parseFloat(totalenrgGen))
                    ? parseFloat(totalenrgGen).toFixed(2)
                    : "-",
                key: "total_energy",
              },
              {
                parameter: "Load %",
                value: loadSum,
                key: "load",
              },
            );
          } else {
            if (isThingMechanical) {
              if (isFuel) {
                SummaryData.push(
                  {
                    parameter:
                      "Total Runhour " + (isGmmco ? "(SMU)" : "(HH:MM)"),
                    value: hours + " : " + minutes,
                    key: "total_runhour",
                  },
                  {
                    parameter: "Total Fuel Consumed(L)",
                    value:
                      fuelConsumed !== "-" && !isNaN(parseFloat(fuelConsumed))
                        ? parseFloat(fuelConsumed).toFixed(2)
                        : "-",
                    key: "total_fuel_consumed",
                  },
                  {
                    parameter:
                      "Fuel consumption per hr(L/" +
                      (isGmmco ? "SMU" : "Hr") +
                      ")",
                    value: mileage,
                    key: "fuel_consumption_per_hr",
                  },
                  {
                    parameter: "Total Fuel Filled (L)",
                    value: !isNaN(parseFloat(totalfuelFilled))
                      ? parseFloat(totalfuelFilled).toFixed(2)
                      : "-",
                    key: "total_fuel_filled",
                  },
                  {
                    parameter: "Total Fuel Drained (L)",
                    value: !isNaN(parseFloat(totalfuelDrained))
                      ? parseFloat(totalfuelDrained).toFixed(2)
                      : "-",
                    key: "total_fuel_drained",
                  },
                );
              } else {
                SummaryData.push({
                  parameter:
                    "Total Runhour " + (isGmmco ? "(SMU)" : "(HH:MM)"),
                  value: hours + " : " + minutes,
                  key: "total_runhour",
                });
              }
            } else {
              if (
                plan_description &&
                plan_description.reports &&
                plan_description.reports.daily_report_summary_params &&
                plan_description.reports.daily_report_summary_params.length
              ) {
                plan_description.reports.daily_report_summary_params.map(
                  (keys) => {
                    if (keys === "rnhr") {
                      SummaryData.push({
                        parameter:
                          "Total Runhour " + (isGmmco ? "(SMU)" : "(HH:MM)"),
                        value: hours + " : " + minutes,
                        key: "total_runhour",
                      });
                    }
                    if (keys === "enrg") {
                      SummaryData.push({
                        parameter: "Total Energy (kWh)",
                        value:
                          totalenrgGen !== "-" &&
                          !isNaN(parseFloat(totalenrgGen))
                            ? parseFloat(totalenrgGen).toFixed(2)
                            : "-",
                        key: "total_energy",
                      });
                    }
                    if (keys === "fuel_consumption" && isFuel) {
                      SummaryData.push({
                        parameter: "Total Fuel Consumed(L)",
                        value:
                          fuelConsumed !== "-" &&
                          !isNaN(parseFloat(fuelConsumed))
                            ? parseFloat(fuelConsumed).toFixed(2)
                            : "-",
                        key: "total_fuel_consumed",
                      });
                    }
                    if (keys === "fuel_consumption_per_hr" && isFuel) {
                      SummaryData.push({
                        parameter:
                          "Fuel consumption per hr(L/" +
                          (isGmmco ? "SMU" : "Hr") +
                          ")",
                        value: mileage,
                        key: "fuel_consumption_per_hr",
                      });
                    }
                    if (keys === "fuel_consumed_per_unit_kWh" && isFuel) {
                      SummaryData.push({
                        parameter: "Fuel Consumed Per unit kWh (L/kWh)",
                        value: fuelPerLoad,
                        key: "fuel_consumed_per_unit_kwh",
                      });
                    }
                    if (
                      keys === "energy_generated_per_unit_fuel_consumption" &&
                      isFuel
                    ) {
                      SummaryData.push({
                        parameter:
                          "Energy Generated per Unit Litre Fuel Consumed (kWh/L)",
                        value: loadPerFuel,
                        key: "energy_generated_per_unit_litre_fuel_consumption",
                      });
                    }
                    if (findLoad && keys === "load_percentage") {
                      SummaryData.push({
                        parameter: "Load %",
                        value: loadSum,
                        key: "load",
                      });
                    }
                    if (keys === "fuel_filled" && isFuel) {
                      SummaryData.push({
                        parameter: "Total Fuel Filled (L)",
                        value: !isNaN(parseFloat(totalfuelFilled))
                          ? parseFloat(totalfuelFilled).toFixed(2)
                          : "-",
                        key: "total_fuel_filled",
                      });
                    }
                    if (keys === "fuel_theft" && isFuel) {
                      SummaryData.push({
                        parameter: "Total Fuel Drained (L)",
                        value: !isNaN(parseFloat(totalfuelDrained))
                          ? parseFloat(totalfuelDrained).toFixed(2)
                          : "-",
                        key: "total_fuel_drained",
                      });
                    }
                  },
                );
              }
            }
          }
          let final_filtered_summary = [];
          SummaryData.map((row) => {
            if (config_object["summary"][row.key] || !row.key) {
              final_filtered_summary.push(row);
            }
          });
          SummaryData = final_filtered_summary;
          downloadData.conf.push({
            pdf_force_new_page: false,
            props: {
              gutter: 10,
              style: {},
              className: "tableRow",
            },
            child: [
              {
                compo: "Table",
                widget: "",
                classname: "tab-1",
                table_new_page: true,
                props: {
                  columns: [
                    {
                      title: "Parameter",
                      dataIndex: "parameter",
                    },
                    {
                      title: "Value",
                      dataIndex: "value",
                    },
                  ],
                  headerFont: 13,
                  size: "small",
                  tabRadius: 0,
                  horizontalScroll: true,
                  shadow: false,
                  breakPoint: 1000,
                  breakPoint2: 500,
                  largeTable: true,
                  mediumTable: false,
                  smallTable: false,
                },
                col_props: {
                  span: 24,
                },
                pdf_width: 50,
                pdf_table_break: {
                  col_no: 9,
                  row_no: 20,
                },
              },
            ],
          });
          downloadData.data.push([SummaryData]);
          if (dataObj?.length) {
            let detailedInsight = {
              pdf_force_new_page: true,
            };
            let detailedHeader = detailedInsight;
            detailedHeader["compo"] = "Text";
            detailedHeader["type"] = "bold";

            detailedHeader["col_props"] = {
              span: 24,
            };
            detailedHeader["pdf_size"] = 12;
            let { detailedHeaderProps, detailedHeaderText } = {
              detailedHeaderProps: {
                props: {
                  gutter: 10,
                },
                child: [detailedHeader],
              },
              detailedHeaderText: [
                {
                  textData: ["Detailed Data"],
                },
              ],
            };
            downloadData.conf.push(detailedHeaderProps);
            downloadData.data.push(detailedHeaderText);
            downloadData.conf.push({
              pdf_force_new_page: false,
              props: {
                gutter: 10,
                style: {},
                className: "tableRow",
              },
              child: [
                {
                  compo: "Table",
                  widget: "",
                  classname: "tab-1",
                  table_new_page: true,
                  hellipRow: true,
                  props: {
                    columns: headerNameKey,
                    headerFont: 13,
                    size: "small",
                    tabRadius: 0,
                    horizontalScroll: true,
                    shadow: false,
                    breakPoint: 1000,
                    breakPoint2: 500,
                    largeTable: true,
                    mediumTable: false,
                    smallTable: false,
                  },
                  col_props: {
                    span: 24,
                  },
                  pdf_width: 50,
                  pdf_table_break: {
                    col_no: 12,
                    row_no: 20,
                  },
                },
              ],
            });
            downloadData.data.push([dataObj]);
          }
        });

        downloadData.file_name = inputDataSet?.name || "Daily Report";
        //---------------------------------------//
      }
      cb({
        downloadData: downloadData,
      });
    },
    cb: (value) => {
      console.log("dataResponse3", value);
      this.setState(
        {
          downloadData: value.downloadData,
          loading: true,
        },
        () => {
          this.getDownloadRender();
        },
      );
    },
  });
}
