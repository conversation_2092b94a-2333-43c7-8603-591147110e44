import React from "react";
import _find from "lodash/find";
import _filter from "lodash/filter";
import _sortBy from "lodash/sortBy";
import moment from "moment-timezone";

export function viewTableDataFunction(
  response,
  responseSnapshot,
  missionList,
  thing,
) {
  let detailedData = JSON.parse(JSON.stringify(this.state.TableObjectData));
  let tableDateArray = {},
    starAdded = {},
    summaryData = {},
    tableColumn = {};
  let dateArray = [];
  for (
    let i = moment.unix(this.state.uptoTime).startOf("day").unix();
    i >= this.state.fromTime;
    i -= 86400
  ) {
    dateArray.push(i);
  }
  if (thing) {
    thing.map((selectedThing) => {
      let findThingList = _find(this.state.totalData.things, {
        id: parseInt(selectedThing),
      });
      let findCalculatedEnergy = _find(findThingList?.parameters, {
        key: "calculated_energy",
      });
      let findLoad = _find(findThingList?.parameters, {
        key: "load_percentage",
      });
      if (!tableDateArray[selectedThing]) {
        tableDateArray[selectedThing] = [];
      }

      if (!starAdded[selectedThing]) {
        starAdded[selectedThing] = false;
      }

      if (!summaryData[selectedThing]) {
        summaryData[selectedThing] = [];
      }

      if (!tableColumn[selectedThing]) {
        tableColumn[selectedThing] = [];
      }
      const isFuel = _find(findThingList?.parameters, function (o) {
        return ["fuel", "fuel_lt", "fuel_litre"].includes(o.key);
      });
      let totalRunhour = {},
        fuelConsumed = {},
        compositeFuelConsumed = {},
        compositeEnrgGen = {},
        mileage = {},
        enrgGen = {},
        fuelPerLoad = {},
        loadPerFuel = {},
        fuelFilled = {},
        loadSum = {},
        tripRnhr = {},
        fuelDrained = {};
      if (!totalRunhour[parseInt(selectedThing)]) {
        totalRunhour[parseInt(selectedThing)] = 0;
      }
      if (isFuel || _find(findThingList?.parameters, { key: "fuel_consume" })) {
        if (!fuelConsumed[parseInt(selectedThing)]) {
          fuelConsumed[parseInt(selectedThing)] = 0;
        }
      } else {
        if (!fuelConsumed[parseInt(selectedThing)]) {
          fuelConsumed[parseInt(selectedThing)] = "-";
        }
      }
      if (!compositeFuelConsumed[parseInt(selectedThing)]) {
        compositeFuelConsumed[parseInt(selectedThing)] = 0;
      }
      if (!compositeEnrgGen[parseInt(selectedThing)]) {
        compositeEnrgGen[parseInt(selectedThing)] = 0;
      }
      if (!mileage[parseInt(selectedThing)]) {
        mileage[parseInt(selectedThing)] = 0;
      }
      if (
        _find(findThingList?.parameters, { key: "enrg" }) ||
        _find(findThingList?.parameters, {
          key: "calculated_energy",
        })
      ) {
        if (!enrgGen[parseInt(selectedThing)]) {
          enrgGen[parseInt(selectedThing)] = 0;
        }
      } else {
        if (!enrgGen[parseInt(selectedThing)]) {
          enrgGen[parseInt(selectedThing)] = "-";
        }
      }
      if (!fuelPerLoad[parseInt(selectedThing)]) {
        fuelPerLoad[parseInt(selectedThing)] = 0;
      }
      if (!loadPerFuel[parseInt(selectedThing)]) {
        loadPerFuel[parseInt(selectedThing)] = 0;
      }
      if (!loadSum[parseInt(selectedThing)]) {
        loadSum[parseInt(selectedThing)] = 0;
      }
      if (!tripRnhr[parseInt(selectedThing)]) {
        tripRnhr[parseInt(selectedThing)] = 0;
      }
      if (!fuelFilled[parseInt(selectedThing)]) {
        fuelFilled[parseInt(selectedThing)] = 0;
      }
      if (!fuelDrained[parseInt(selectedThing)]) {
        fuelDrained[parseInt(selectedThing)] = 0;
      }
      let filteredDataResponse =
        response && response.data && response.data.length
          ? _filter(response.data, {
              thing_id: parseInt(selectedThing),
            })
          : [];

      if (filteredDataResponse && filteredDataResponse.length) {
        filteredDataResponse.forEach((filteredResponseData) => {
          const thingId = parseInt(filteredResponseData.thing_id);

          if (filteredResponseData.parameter_values) {
            const paramValues = filteredResponseData.parameter_values;

            // Validate and sum totalRunhour
            const runhourSum =
              parseFloat(paramValues.calculated_runhour?.sum) || 0;
            if (!isNaN(runhourSum)) {
              totalRunhour[thingId] = (totalRunhour[thingId] || 0) + runhourSum;
            }
            // Validate and sum fuelConsumed
            const fuelSum = parseFloat(paramValues.fuel_consumption?.sum) || 0;
            if (!isNaN(fuelSum) && fuelConsumed[thingId] !== "-") {
              fuelConsumed[thingId] =
                (parseFloat(fuelConsumed[thingId]) || 0) + fuelSum;
            }

            // Validate and sum compositeFuelConsumed
            if (
              findCalculatedEnergy &&
              paramValues.calculated_energy?.sum > 0
            ) {
              const calcEnergySum =
                parseFloat(paramValues.calculated_energy?.sum) || 0;
              if (!isNaN(calcEnergySum)) {
                compositeFuelConsumed[thingId] =
                  (compositeFuelConsumed[thingId] || 0) + fuelSum;
              }
            }
            if (findCalculatedEnergy && paramValues.calculated_energy?.sum) {
              const calcEnergyGen =
                parseFloat(paramValues.calculated_energy?.sum) || 0;
              if (!isNaN(calcEnergyGen) && fuelSum > 0) {
                compositeEnrgGen[thingId] =
                  (compositeEnrgGen[thingId] || 0) + calcEnergyGen;
              }
            }
            // Validate and sum enrgGen
            const energyGenSum =
              parseFloat(paramValues.calculated_energy?.sum) || 0;
            if (!isNaN(energyGenSum) && enrgGen[thingId] !== "-") {
              enrgGen[thingId] =
                (parseFloat(enrgGen[thingId]) || 0) + energyGenSum;
            }

            // Validate and sum fuelFilled
            const fuelFilledSum = parseFloat(paramValues.fuel_filled?.sum) || 0;
            if (!isNaN(fuelFilledSum)) {
              fuelFilled[thingId] = (fuelFilled[thingId] || 0) + fuelFilledSum;
            }

            // Validate and sum fuelDrained
            const fuelTheftSum = parseFloat(paramValues.fuel_theft?.sum) || 0;
            if (!isNaN(fuelTheftSum)) {
              fuelDrained[thingId] = (fuelDrained[thingId] || 0) + fuelTheftSum;
            }
          }

          return filteredResponseData;
        });
      }

      let filteredMissionArray = [];
      if (missionList?.response?.Missions?.length) {
        filteredMissionArray = missionList.response.Missions.filter(
          (missions) => missions.Devices.includes(selectedThing.toString()),
        );
      }
      if (filteredMissionArray && filteredMissionArray.length) {
        filteredMissionArray.map((filteredResponseData) => {
          if (findLoad && findLoad !== undefined) {
            if (
              filteredResponseData &&
              filteredResponseData.Details &&
              filteredResponseData.Details.aggregate_data &&
              filteredResponseData.Details.aggregate_data.calculated_runhour &&
              filteredResponseData.Details.aggregate_data.calculated_runhour.sum
            ) {
              tripRnhr[parseInt(selectedThing)] +=
                filteredResponseData.Details.aggregate_data.calculated_runhour.sum;
            }
            if (
              filteredResponseData &&
              filteredResponseData.Details &&
              filteredResponseData.Details.aggregate_data &&
              filteredResponseData.Details.aggregate_data.load_percentage &&
              filteredResponseData.Details.aggregate_data.load_percentage.avg &&
              filteredResponseData.Details.aggregate_data.calculated_runhour &&
              filteredResponseData.Details.aggregate_data.calculated_runhour.sum
            ) {
              loadSum[parseInt(selectedThing)] +=
                filteredResponseData.Details.aggregate_data.calculated_runhour
                  .sum *
                filteredResponseData.Details.aggregate_data.load_percentage.avg;
            }
          } else {
            if (
              filteredResponseData.Details &&
              filteredResponseData.Details.aggregate_data &&
              filteredResponseData.Details.aggregate_data &&
              filteredResponseData.Details.aggregate_data.calculated_runhour &&
              filteredResponseData.Details.aggregate_data.calculated_runhour.sum
            ) {
              if (findCalculatedEnergy && findCalculatedEnergy !== undefined) {
                if (
                  filteredResponseData.Details &&
                  filteredResponseData.Details.aggregate_data &&
                  filteredResponseData.Details.aggregate_data &&
                  filteredResponseData.Details.aggregate_data
                    .calculated_energy &&
                  filteredResponseData.Details.aggregate_data.calculated_energy
                    .sum
                ) {
                  loadSum[parseInt(selectedThing)] +=
                    filteredResponseData.Details.aggregate_data
                      .calculated_runhour.sum *
                    (filteredResponseData.Details.aggregate_data
                      .calculated_energy.sum /
                      (findThingList.thing_details.kva *
                        0.8 *
                        (filteredResponseData.Details.aggregate_data
                          .calculated_runhour.sum /
                          3600))) *
                    100;
                }
              }
            }
          }
        });
      }

      let hours =
        Math.floor(totalRunhour[parseInt(selectedThing)] / 3600) < 10
          ? "0" + Math.floor(totalRunhour[parseInt(selectedThing)] / 3600)
          : Math.floor(totalRunhour[parseInt(selectedThing)] / 3600);
      let reminderRnhr = {};
      if (!reminderRnhr[parseInt(selectedThing)]) {
        reminderRnhr[parseInt(selectedThing)] = 0;
      }
      reminderRnhr[parseInt(selectedThing)] =
        totalRunhour[parseInt(selectedThing)] % 3600;
      let minutes =
        Math.floor(reminderRnhr[parseInt(selectedThing)] / 60) < 10
          ? "0" + Math.floor(reminderRnhr[parseInt(selectedThing)] / 60)
          : Math.floor(reminderRnhr[parseInt(selectedThing)] / 60);
      
      let seconds =
        Math.floor(reminderRnhr[parseInt(selectedThing)] % 60) < 10
          ? "0" + Math.floor(reminderRnhr[parseInt(selectedThing)] % 60)
          : Math.floor(reminderRnhr[parseInt(selectedThing)] % 60);
      mileage[parseInt(selectedThing)] =
        totalRunhour[parseInt(selectedThing)] > 0 &&
        fuelConsumed[parseInt(selectedThing)] !== "-"
          ? (
              fuelConsumed[parseInt(selectedThing)] /
              (totalRunhour[parseInt(selectedThing)] / 3600)
            ).toFixed(2)
          : "-";
      fuelPerLoad[parseInt(selectedThing)] =
        compositeEnrgGen[parseInt(selectedThing)] > 0 &&
        compositeFuelConsumed[parseInt(selectedThing)] !== "-"
          ? (
              compositeFuelConsumed[parseInt(selectedThing)] /
              compositeEnrgGen[parseInt(selectedThing)]
            ).toFixed(2)
          : "-";
      loadPerFuel[parseInt(selectedThing)] =
        compositeFuelConsumed[parseInt(selectedThing)] > 0 &&
        compositeEnrgGen[parseInt(selectedThing)] !== "-"
          ? (
              compositeEnrgGen[parseInt(selectedThing)] /
              compositeFuelConsumed[parseInt(selectedThing)]
            ).toFixed(2)
          : "-";
      loadSum[parseInt(selectedThing)] =
        tripRnhr[parseInt(selectedThing)] > 0
          ? parseFloat(
              loadSum[parseInt(selectedThing)] /
                tripRnhr[parseInt(selectedThing)],
            ).toFixed(2)
          : 0.0;

      if (this.props.template_id === 21) {
        summaryData[selectedThing].push(
          {
            name: "Total Runhour " + (this.isGmmco() ? "(SMU)" : "(HH:MM)"),
            value: hours + " : " + minutes,
            key: "total_runhour",
          },
          {
            name: "Total Fuel Consumed(L)",
            value:
              fuelConsumed[parseInt(selectedThing)] !== "-" &&
              !isNaN(fuelConsumed[parseInt(selectedThing)])
                ? fuelConsumed[parseInt(selectedThing)].toFixed(2)
                : "-",
            key: "total_fuel_consumed",
          },
          {
            name:
              "Fuel consumption per hr(L/" +
              (this.isGmmco() ? "SMU" : "Hr") +
              ")",
            value: mileage[parseInt(selectedThing)],
            key: "fuel_consumption_per_hr",
          },
          {
            name: "Total Fuel Filled (L)",
            value: !isNaN(fuelFilled[parseInt(selectedThing)])
              ? fuelFilled[parseInt(selectedThing)].toFixed(2)
              : "-",
            key: "total_fuel_filled",
          },
          {
            name: "Total Fuel Drained (L)",
            value: !isNaN(fuelDrained[parseInt(selectedThing)])
              ? fuelDrained[parseInt(selectedThing)].toFixed(2)
              : "-",
            key: "total_fuel_drained",
          },
        );
      } else if (this.props.template_id === 24) {
        summaryData[selectedThing].push(
          {
            name: "Total Runhour " + (this.isGmmco() ? "(SMU)" : "(HH:MM)"),
            value: hours + " : " + minutes,
            key: "total_runhour",
          },
          {
            name: "Total Energy (kWh)",
            value:
              enrgGen[parseInt(selectedThing)] !== "-" &&
              !isNaN(enrgGen[parseInt(selectedThing)])
                ? enrgGen[parseInt(selectedThing)].toFixed(2)
                : "-",
            key: "total_energy",
          },
          {
            name: "Load %",
            value: loadSum[parseInt(selectedThing)],
            key: "load",
          },
        );
      } else {
        if (this.isMechanicalDG(selectedThing)) {
          if (isFuel) {
            summaryData[selectedThing].push(
              {
                name: "Total Runhour " + (this.isGmmco() ? "(SMU)" : "(HH:MM)"),
                value: hours + " : " + minutes,
                key: "total_runhour",
              },
              {
                name: "Total Fuel Consumed(L)",
                value:
                  fuelConsumed[parseInt(selectedThing)] !== "-" &&
                  !isNaN(fuelConsumed[parseInt(selectedThing)])
                    ? fuelConsumed[parseInt(selectedThing)].toFixed(2)
                    : "-",
                key: "total_fuel_consumed",
              },
              {
                name:
                  "Fuel consumption per hr(L/" +
                  (this.isGmmco() ? "SMU" : "Hr") +
                  ")",
                value: mileage[parseInt(selectedThing)],
                key: "fuel_consumption_per_hr",
              },
              {
                name: "Total Fuel Filled (L)",
                value: !isNaN(fuelFilled[parseInt(selectedThing)])
                  ? fuelFilled[parseInt(selectedThing)].toFixed(2)
                  : "-",
                key: "total_fuel_filled",
              },
              {
                name: "Total Fuel Drained (L)",
                value: !isNaN(fuelDrained[parseInt(selectedThing)])
                  ? fuelDrained[parseInt(selectedThing)].toFixed(2)
                  : "-",
                key: "total_fuel_drained",
              },
            );
          } else {
            summaryData[selectedThing].push({
              name: "Total Runhour " + (this.isGmmco() ? "(SMU)" : "(HH:MM)"),
              value: hours + " : " + minutes,
              key: "total_runhour",
            });
          }
        } else {
          if (
            this.props.plan_description &&
            this.props.plan_description.reports &&
            this.props.plan_description.reports.daily_report_summary_params &&
            this.props.plan_description.reports.daily_report_summary_params
              .length
          ) {
            this.props.plan_description.reports.daily_report_summary_params.map(
              (keys) => {
                if (keys === "rnhr") {
                  summaryData[selectedThing].push({
                    name:
                      "Total Runhour " + (this.isGmmco() ? "(SMU)" : "(HH:MM)"),
                    value: hours + " : " + minutes,
                    key: "total_runhour",
                  });
                }
                if (keys === "enrg") {
                  summaryData[selectedThing].push({
                    name: "Total Energy (kWh)",
                    value:
                      enrgGen[parseInt(selectedThing)] !== "-" &&
                      !isNaN(enrgGen[parseInt(selectedThing)])
                        ? enrgGen[parseInt(selectedThing)].toFixed(2)
                        : "-",
                    key: "total_energy",
                  });
                }
                if (keys === "fuel_consumption" && isFuel) {
                  summaryData[selectedThing].push({
                    name: "Total Fuel Consumed(L)",
                    value:
                      fuelConsumed[parseInt(selectedThing)] !== "-" &&
                      !isNaN(fuelConsumed[parseInt(selectedThing)])
                        ? fuelConsumed[parseInt(selectedThing)].toFixed(2)
                        : "-",
                    key: "total_fuel_consumed",
                  });
                }
                if (keys === "fuel_consumption_per_hr" && isFuel) {
                  summaryData[selectedThing].push({
                    name:
                      "Fuel consumption per hr(L/" +
                      (this.isGmmco() ? "SMU" : "Hr") +
                      ")",
                    value: mileage[parseInt(selectedThing)],
                    key: "fuel_consumption_per_hr",
                  });
                }
                if (keys === "fuel_consumed_per_unit_kWh" && isFuel) {
                  summaryData[selectedThing].push({
                    name: "Fuel Consumed Per unit kWh (L/kWh)",
                    value: fuelPerLoad[parseInt(selectedThing)],
                    key: "fuel_consumed_per_unit_kwh",
                  });
                }
                if (keys === "energy_generated_per_unit_fuel_consumption") {
                  summaryData[selectedThing].push({
                    name: "Energy Generated per Unit Litre Fuel Consumed (kWh/L)",
                    value: loadPerFuel[parseInt(selectedThing)],
                    key: "energy_generated_per_unit_litre_fuel_consumption",
                  });
                }
                if (findLoad && keys === "load_percentage") {
                  summaryData[selectedThing].push({
                    name: "Load %",
                    value: loadSum[parseInt(selectedThing)],
                    key: "load",
                  });
                }
                if (keys === "fuel_filled" && isFuel) {
                  summaryData[selectedThing].push({
                    name: "Total Fuel Filled (L)",
                    value: !isNaN(fuelFilled[parseInt(selectedThing)])
                      ? fuelFilled[parseInt(selectedThing)].toFixed(2)
                      : "-",
                    key: "total_fuel_filled",
                  });
                }
                if (keys === "fuel_theft" && isFuel) {
                  summaryData[selectedThing].push({
                    name: "Total Fuel Drained (L)",
                    value: !isNaN(fuelDrained[parseInt(selectedThing)])
                      ? fuelDrained[parseInt(selectedThing)].toFixed(2)
                      : "-",
                    key: "total_fuel_drained",
                  });
                }
              },
            );
          }
        }
      }
      let final_filtered_summary = [];
      summaryData[selectedThing].map((row) => {
        if (this.state.config_object["summary"][row.key] || !row.key) {
          final_filtered_summary.push(row);
        }
      });
      summaryData[selectedThing] = final_filtered_summary;

      if (this.props.template_id === 21) {
        tableColumn[selectedThing].push(
          {
            title: "Date",
            width: window.innerWidth < 576 ? 150 : "auto",
            dataIndex: "date",
          },
          {
            title: "Runhour " + (this.isGmmco() ? "(SMU)" : "(HH:MM)"),
            dataIndex: "runhour",
          },
          {
            title: "Total Runs",
            dataIndex: "trips",
          },
          {
            title: "Fuel Consumed (L)",
            dataIndex: "fuel_cons",
          },
          {
            title:
              "Fuel consumption per hr (Litre/" +
              (this.isGmmco() ? "SMU" : "Hr") +
              ")",
            dataIndex: "mileage",
          },
          {
            title: "Start Fuel (L)",
            dataIndex: "start_fuel",
          },
          {
            title: "End Fuel (L)",
            dataIndex: "end_fuel",
          },
        );
      } else if (this.props.template_id === 24) {
        tableColumn[selectedThing].push(
          {
            title: "Date",
            width: window.innerWidth < 576 ? 150 : "auto",
            dataIndex: "date",
          },
          {
            title: "Runhour " + (this.isGmmco() ? "(SMU)" : "(HH:MM)"),
            dataIndex: "runhour",
          },
          {
            title: "Total Runs",
            dataIndex: "trips",
          },
          {
            title: "Fuel Consumed (%)",
            dataIndex: "fuel_consumed_perc",
          },
          {
            title: "Energy Generated (kWh)",
            dataIndex: "enrg_gen",
          },
          {
            title: "Start Energy (kWh)",
            dataIndex: "start_energy",
          },
          {
            title: "End Energy (kWh)",
            dataIndex: "end_energy",
          },
          {
            title: "Start Fuel (%)",
            dataIndex: "start_fuel",
          },
          {
            title: "End Fuel (%)",
            dataIndex: "end_fuel",
          },
        );
        if (findLoad) {
          tableColumn[selectedThing].push({
            title: "Load Percentage (%)",
            dataIndex: "load_percentage",
          });
        }
        tableColumn[selectedThing].push({
          title: "Fuel Filled (%)",
          dataIndex: "fuel_filled_perc",
        });
      } else {
        if (this.isMechanicalDG(selectedThing)) {
          if (isFuel) {
            tableColumn[selectedThing].push(
              {
                title: "Date",
                width: window.innerWidth < 576 ? 150 : "auto",
                dataIndex: "date",
              },
              {
                title: "Runhour " + (this.isGmmco() ? "(SMU)" : "(HH:MM)"),
                dataIndex: "runhour",
              },
              {
                title: "Total Runs",
                dataIndex: "trips",
              },
              {
                title: "Fuel Consumed (L)",
                dataIndex: "fuel_cons",
              },
              {
                title:
                  "Fuel consumption per hr (Litre/" +
                  (this.isGmmco() ? "SMU" : "Hr") +
                  ")",
                dataIndex: "mileage",
              },
              {
                title: "Start Fuel (L)",
                dataIndex: "start_fuel",
              },
              {
                title: "End Fuel (L)",
                dataIndex: "end_fuel",
              },
              {
                title: "Fuel Filled (L)",
                dataIndex: "fuel_filled",
              },
              {
                title: "Fuel Drained (L)",
                dataIndex: "fuel_drained",
              },
            );
          } else {
            tableColumn[selectedThing].push(
              {
                title: "Date",
                width: window.innerWidth < 576 ? 150 : "auto",
                dataIndex: "date",
              },
              {
                title: "Runhour " + (this.isGmmco() ? "(SMU)" : "(HH:MM)"),
                dataIndex: "runhour",
              },
              {
                title: "Total Runs",
                dataIndex: "trips",
              },
            );
          }
        } else {
          if (
            this.props.plan_description &&
            this.props.plan_description.reports &&
            this.props.plan_description.reports.daily_report_details_params &&
            this.props.plan_description.reports.daily_report_details_params
              .length
          ) {
            tableColumn[selectedThing].push({
              title: "Date",
              width: window.innerWidth < 576 ? 150 : "auto",
              dataIndex: "date",
            });

            this.props.plan_description.reports.daily_report_details_params.map(
              (keys) => {
                if (keys === "rnhr") {
                  tableColumn[selectedThing].push({
                    title: "Runhour " + (this.isGmmco() ? "(SMU)" : "(HH:MM)"),
                    dataIndex: "runhour",
                  });
                }
                if (keys === "dg_runs") {
                  tableColumn[selectedThing].push({
                    title: "Total Runs",
                    dataIndex: "trips",
                  });
                }
                if (keys === "fuel_consumption" && isFuel) {
                  tableColumn[selectedThing].push({
                    title: "Fuel Consumed (L)",
                    dataIndex: "fuel_cons",
                  });
                }
                if (keys === "enrg") {
                  tableColumn[selectedThing].push(
                    {
                      title: "Energy Generated (kWh)",
                      dataIndex: "enrg_gen",
                    },
                    {
                      title: "Start Energy (kWh)",
                      dataIndex: "start_energy",
                    },
                    {
                      title: "End Energy (kWh)",
                      dataIndex: "end_energy",
                    },
                  );
                }
                if (keys === "fuel_consumption_per_hr" && isFuel) {
                  tableColumn[selectedThing].push({
                    title:
                      "Fuel consumption per hr (Litre/" +
                      (this.isGmmco() ? "SMU" : "Hr") +
                      ")",
                    dataIndex: "mileage",
                  });
                }
                if (keys === "start_fuel" && isFuel) {
                  tableColumn[selectedThing].push({
                    title: "Start Fuel (L)",
                    dataIndex: "start_fuel",
                  });
                }
                if (keys === "end_fuel" && isFuel) {
                  tableColumn[selectedThing].push({
                    title: "End Fuel (L)",
                    dataIndex: "end_fuel",
                  });
                }
                if (keys === "fuel_consumption_per_enrg" && isFuel) {
                  tableColumn[selectedThing].push({
                    title: "Fuel Consumed per unit kWh (L/kWh)",
                    dataIndex: "fuel_per_enrg",
                  });
                }
                if (keys === "enrg_per_fuel_consumption" && isFuel) {
                  tableColumn[selectedThing].push({
                    title:
                      "Energy Generated per unit Litre Fuel Consumed (kWh/L)",
                    dataIndex: "enrg_per_fuel",
                  });
                }
                if (findLoad && keys === "load_percentage") {
                  tableColumn[selectedThing].push({
                    title: "Load Percentage (%)",
                    dataIndex: "load_percentage",
                  });
                }
              },
            );
            if (isFuel) {
              tableColumn[selectedThing].push({
                title: "Fuel Filled (L)",
                dataIndex: "fuel_filled",
              });
              tableColumn[selectedThing].push({
                title: "Fuel Drained (L)",
                dataIndex: "fuel_drained",
              });
            }
          }
        }
      }
      if (_find(findThingList?.parameters, { key: "mn_hlth_st" }) && this.props.template_id !== 24) {
        tableColumn[selectedThing].push({
          title: "Mains Runhour (HH:MM)",
          dataIndex: "mains_rnhr",
        });
      }
      let final_filtered_column = [];
      tableColumn[selectedThing].map((row) => {
        if (
          this.state.config_object["table"][
            this.table_index_map[row.dataIndex]
          ] ||
          !this.table_index_map[row.dataIndex]
        ) {
          final_filtered_column.push(row);
        }
      });
      tableColumn[selectedThing] = final_filtered_column;
      let sortedResponse = _sortBy(filteredDataResponse, ["time"]);

      let filteredDataresponseSnapshot = [];
      if (responseSnapshot) {
        filteredDataresponseSnapshot = _filter(responseSnapshot.data, {
          thing_id: parseInt(selectedThing),
        });
      }

      let sortedResponseSnapshot = _sortBy(filteredDataresponseSnapshot, [
        "time",
      ]);
      dateArray.map((date, index) => {
        let filteredDataResponseData = _find(sortedResponse, {
          time: date,
        })
          ? _find(sortedResponse, { time: date })
          : undefined;
        let runHour = "NA",
          calculatedRnhr = 0,
          runMin = "NA",
          runSec = "NA",
          mainsRnhr = "NA",
          mainsCalculatedRnhr = 0,
          mainsRunMin = "NA",
          mainsRunSec = "NA",
          fuelCons = "NA",
          rpm = "NA",
          enrgGen = "NA",
          startFuel = "NA",
          endFuel = "NA",
          startEnergy = "NA",
          endEnergy = "NA",
          filteredMissionWithStartDate = [],
          missionCount = 0,
          loadCalculation = "NA",
          fuelFilled = "NA",
          fuelDrained = "NA",
          fuel_consumed_perc = "NA",
          fuel_filed_perc = "NA";
        if (missionList) {
          filteredMissionWithStartDate = _filter(
            filteredMissionArray,
            function (o) {
              if (
                moment(o.EndDate, moment.ISO_8601).format("DD MMM YYYY") !==
                "Invalid date"
              ) {
                return (
                  moment(o.StartDate, moment.ISO_8601).format("DD MMM YYYY") ===
                  moment.unix(date).format("DD MMM YYYY")
                );
              }
            },
          );
          let star = "";
          filteredMissionWithStartDate.map((filteredMission) => {
            if (
              moment(filteredMission.EndDate, moment.ISO_8601).format(
                "DD MMM YYYY",
              ) !== moment.unix(date).format("DD MMM YYYY")
            ) {
              star = "*";
              starAdded[selectedThing] = true;
            }
            missionCount = filteredMissionWithStartDate.length + " " + star;
            return missionCount;
          });
        }
        if (filteredDataResponseData !== undefined) {
          if (
            filteredDataResponseData.parameter_values &&
            filteredDataResponseData.parameter_values.calculated_runhour &&
            filteredDataResponseData.parameter_values.calculated_runhour.sum >=
              0
          ) {
            calculatedRnhr =
              filteredDataResponseData.parameter_values.calculated_runhour.sum;
            runHour =
              Math.floor(calculatedRnhr / 3600) < 10
                ? "0" + Math.floor(calculatedRnhr / 3600)
                : Math.floor(calculatedRnhr / 3600);

            let modCalculateRnhr = calculatedRnhr % 3600;

            runMin =
              Math.floor(modCalculateRnhr / 60) < 10
                ? "0" + Math.floor(modCalculateRnhr / 60)
                : Math.floor(modCalculateRnhr / 60);

            runSec =
              Math.floor(modCalculateRnhr % 60) < 10
                ? "0" + Math.floor(modCalculateRnhr % 60)
                : Math.floor(modCalculateRnhr % 60);
          }

          if (
            filteredDataResponseData?.parameter_values?.calculated_mains_runhour
              ?.sum > 0
          ) {
            mainsCalculatedRnhr =
              filteredDataResponseData.parameter_values.calculated_mains_runhour
                .sum;
            mainsRnhr =
              Math.floor(mainsCalculatedRnhr / 3600) < 10
                ? "0" + Math.floor(mainsCalculatedRnhr / 3600)
                : Math.floor(mainsCalculatedRnhr / 3600);

            let modCalculateRnhr = mainsCalculatedRnhr % 3600;

            mainsRunMin =
              Math.floor(modCalculateRnhr / 60) < 10
                ? "0" + Math.floor(modCalculateRnhr / 60)
                : Math.floor(modCalculateRnhr / 60);

            mainsRunSec =
              Math.floor(modCalculateRnhr % 60) < 10
                ? "0" + Math.floor(modCalculateRnhr % 60)
                : Math.floor(modCalculateRnhr % 60);
          }

          if (
            filteredDataResponseData.parameter_values &&
            filteredDataResponseData.parameter_values.fuel_consumption &&
            filteredDataResponseData.parameter_values.fuel_consumption.sum >= 0
          ) {
            fuelCons =
              filteredDataResponseData.parameter_values.fuel_consumption.sum;
          }
          if (
            filteredDataResponseData.parameter_values &&
            filteredDataResponseData.parameter_values.rpm &&
            filteredDataResponseData.parameter_values.rpm.avg >= 0
          ) {
            rpm = filteredDataResponseData.parameter_values.rpm.avg;
          }
          if (findCalculatedEnergy && findCalculatedEnergy !== undefined) {
            if (
              filteredDataResponseData.parameter_values &&
              filteredDataResponseData.parameter_values.calculated_energy &&
              filteredDataResponseData.parameter_values.calculated_energy.sum >=
                0
            ) {
              enrgGen =
                filteredDataResponseData.parameter_values.calculated_energy.sum;
            }
          }
          let findSortedResponseSnapshot = _find(sortedResponseSnapshot, {
            time: date,
          })
            ? _find(sortedResponseSnapshot, { time: date })
            : undefined;
          if (
            findThingList?.thing_details?.capacity &&
            (findSortedResponseSnapshot?.parameter_values?.fuel || findSortedResponseSnapshot?.parameter_values?.trip_valid_fuel)
          ) {
            startFuel =
              this.props.template_id === 24
                ? parseFloat(
                    findSortedResponseSnapshot.parameter_values?.trip_valid_fuel?.initial,
                  )
                : (findThingList.thing_details.capacity *
                    parseFloat(
                      findSortedResponseSnapshot.parameter_values?.fuel?.initial,
                    )) /
                  100;
            endFuel =
              this.props.template_id === 24
                ? parseFloat(
                    findSortedResponseSnapshot.parameter_values?.trip_valid_fuel?.snapshot,
                  )
                : (findThingList.thing_details.capacity *
                    parseFloat(
                      findSortedResponseSnapshot.parameter_values?.fuel?.snapshot,
                    )) /
                  100;
          }
          if (
            findThingList &&
            findThingList.thing_details &&
            findThingList.thing_details.capacity &&
            findSortedResponseSnapshot &&
            findSortedResponseSnapshot.parameter_values &&
            findSortedResponseSnapshot.parameter_values.enrg
          ) {
            startEnergy = parseFloat(
              findSortedResponseSnapshot.parameter_values.enrg.initial,
            );
            endEnergy = parseFloat(
              findSortedResponseSnapshot.parameter_values.enrg.snapshot,
            );
          }
          if (findLoad && findLoad !== undefined) {
            if (
              filteredDataResponseData.parameter_values.load_percentage &&
              !isNaN(
                filteredDataResponseData.parameter_values.load_percentage.avg,
              )
            ) {
              loadCalculation = parseFloat(
                filteredDataResponseData.parameter_values.load_percentage.avg,
              ).toFixed(2);
            } else {
              loadCalculation = "-";
            }
          } else if (
            findThingList &&
            findThingList.thing_details &&
            findThingList.thing_details.kva > 0 &&
            filteredDataResponseData.parameter_values.calculated_runhour &&
            filteredDataResponseData.parameter_values.calculated_runhour.sum > 0
          ) {
            loadCalculation = parseFloat(
              (enrgGen /
                (findThingList.thing_details.kva *
                  0.8 *
                  (filteredDataResponseData.parameter_values.calculated_runhour
                    .sum /
                    3600))) *
                100,
            ).toFixed(2);
          } else {
            loadCalculation = "-";
          }
          if (
            filteredDataResponseData.parameter_values &&
            filteredDataResponseData.parameter_values.fuel_filled &&
            filteredDataResponseData.parameter_values.fuel_filled.sum >= 0
          ) {
            fuelFilled =
              filteredDataResponseData.parameter_values.fuel_filled.sum;
          }
          if (
            filteredDataResponseData.parameter_values &&
            filteredDataResponseData.parameter_values.fuel_theft &&
            filteredDataResponseData.parameter_values.fuel_theft.sum >= 0
          ) {
            fuelDrained =
              filteredDataResponseData.parameter_values.fuel_theft.sum;
          }
          if (this.props.template_id === 21) {
            tableDateArray[selectedThing].push({
              date: moment.unix(date).format("DD MMM YYYY"),
              runhour: runHour === "NA" ? "NA" : runHour + ":" + runMin,
              mains_rnhr:
                mainsRnhr === "NA" ? "NA" : mainsRnhr + ":" + mainsRunMin,
              trips: runHour === "NA" ? "NA" : missionCount,
              fuel_cons:
                fuelCons === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(fuelCons))
                    ? parseFloat(fuelCons).toFixed(2)
                    : "-",
              mileage:
                calculatedRnhr === "NA" && fuelCons === "NA"
                  ? "NA"
                  : calculatedRnhr > 0
                    ? !isNaN(parseFloat(fuelCons / (calculatedRnhr / 3600)))
                      ? parseFloat(fuelCons / (calculatedRnhr / 3600)).toFixed(
                          2,
                        )
                      : "-"
                    : "-",
              start_fuel:
                startFuel === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(startFuel))
                    ? parseFloat(startFuel).toFixed(2)
                    : "-",
              end_fuel:
                endFuel === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(endFuel))
                    ? parseFloat(endFuel).toFixed(2)
                    : "-",
              start_energy:
                startEnergy === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(startEnergy))
                    ? parseFloat(startEnergy).toFixed(2)
                    : "-",
              end_energy:
                endEnergy === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(endEnergy))
                    ? parseFloat(endEnergy).toFixed(2)
                    : "-",
            });
          } else if (this.props.template_id === 24) {
            tableDateArray[selectedThing].push({
              date: moment.unix(date).format("DD MMM YYYY"),
              runhour: runHour === "NA" ? "NA" : runHour + ":" + runMin,
              mains_rnhr:
                mainsRnhr === "NA" ? "NA" : mainsRnhr + ":" + mainsRunMin,
              trips: runHour === "NA" ? "NA" : missionCount,
              enrg_gen:
                enrgGen === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(enrgGen))
                    ? parseFloat(enrgGen).toFixed(2)
                    : "-",
              start_fuel:
                startFuel === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(startFuel))
                    ? parseFloat(startFuel).toFixed(2)
                    : "-",
              end_fuel:
                endFuel === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(endFuel))
                    ? parseFloat(endFuel).toFixed(2)
                    : "-",
              start_energy:
                startEnergy === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(startEnergy))
                    ? parseFloat(startEnergy).toFixed(2)
                    : "-",
              end_energy:
                endEnergy === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(endEnergy))
                    ? parseFloat(endEnergy).toFixed(2)
                    : "-",
              load_percentage: loadCalculation,
              fuel_consumed_perc:
                fuelCons === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(fuelCons)) && findThingList?.thing_details?.capacity
                    ? parseFloat(
                        (fuelCons / findThingList.thing_details.capacity) * 100,
                      ).toFixed(0)
                    : "-",
              fuel_filled_perc:
                fuelFilled === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(fuelFilled)) && findThingList?.thing_details?.capacity
                    ? parseFloat(
                        (fuelFilled / findThingList.thing_details.capacity) *
                          100,
                      ).toFixed(0)
                    : "-",
            });
          } else {
            tableDateArray[selectedThing].push({
              date: moment.unix(date).format("DD MMM YYYY"),
              runhour: runHour === "NA" ? "NA" : runHour + ":" + runMin,
              mains_rnhr:
                mainsRnhr === "NA" ? "NA" : mainsRnhr + ":" + mainsRunMin,
              trips: runHour === "NA" ? "NA" : missionCount,
              fuel_cons:
                fuelCons === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(fuelCons))
                    ? parseFloat(fuelCons).toFixed(2)
                    : "-",
              enrg_gen:
                enrgGen === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(enrgGen))
                    ? parseFloat(enrgGen).toFixed(2)
                    : "-",
              mileage:
                calculatedRnhr === "NA" && fuelCons === "NA"
                  ? "NA"
                  : calculatedRnhr > 0
                    ? !isNaN(parseFloat(fuelCons / (calculatedRnhr / 3600)))
                      ? parseFloat(fuelCons / (calculatedRnhr / 3600)).toFixed(
                          2,
                        )
                      : "-"
                    : "-",
              start_fuel:
                startFuel === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(startFuel))
                    ? parseFloat(startFuel).toFixed(2)
                    : "-",
              end_fuel:
                endFuel === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(endFuel))
                    ? parseFloat(endFuel).toFixed(2)
                    : "-",
              start_energy:
                startEnergy === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(startEnergy))
                    ? parseFloat(startEnergy).toFixed(2)
                    : "-",
              end_energy:
                endEnergy === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(endEnergy))
                    ? parseFloat(endEnergy).toFixed(2)
                    : "-",
              fuel_per_enrg:
                enrgGen === "NA" && fuelCons === "NA"
                  ? "NA"
                  : enrgGen > 0
                    ? !isNaN(parseFloat(fuelCons / enrgGen))
                      ? parseFloat(fuelCons / enrgGen).toFixed(2)
                      : "-"
                    : "-",
              enrg_per_fuel:
                enrgGen === "NA" && fuelCons === "NA"
                  ? "NA"
                  : fuelCons > 0
                    ? !isNaN(parseFloat(enrgGen / fuelCons))
                      ? parseFloat(enrgGen / fuelCons).toFixed(2)
                      : "-"
                    : "-",

              load_percentage: loadCalculation,
              fuel_filled:
                fuelFilled === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(fuelFilled))
                    ? parseFloat(fuelFilled).toFixed(2)
                    : "-",
              fuel_drained:
                fuelDrained === "NA"
                  ? "NA"
                  : !isNaN(parseFloat(fuelDrained))
                    ? parseFloat(fuelDrained).toFixed(2)
                    : "-",
            });
            if (
              this.props.plan_description &&
              this.props.plan_description.reports &&
              this.props.plan_description.reports.daily_report_details_params &&
              this.props.plan_description.reports.daily_report_details_params
                .length
            ) {
              if (
                tableDateArray[selectedThing] &&
                tableDateArray[selectedThing].length
              ) {
                tableDateArray[selectedThing].map((tableData) => {
                  if (
                    !this.props.plan_description.reports.daily_report_details_params.includes(
                      "rnhr",
                    )
                  ) {
                    delete tableData.runhour;
                  }
                  if (
                    !this.props.plan_description.reports.daily_report_details_params.includes(
                      "dg_runs",
                    )
                  ) {
                    delete tableData.trips;
                  }
                  if (
                    !this.props.plan_description.reports.daily_report_details_params.includes(
                      "fuel_consumption",
                    )
                  ) {
                    delete tableData.fuel_cons;
                  }
                  if (
                    !this.props.plan_description.reports.daily_report_details_params.includes(
                      "enrg",
                    )
                  ) {
                    delete tableData.enrg_gen;
                  }
                  if (
                    !this.props.plan_description.reports.daily_report_details_params.includes(
                      "fuel_consumption_per_hr",
                    )
                  ) {
                    delete tableData.mileage;
                  }
                  if (
                    !this.props.plan_description.reports.daily_report_details_params.includes(
                      "start_fuel",
                    )
                  ) {
                    delete tableData.start_fuel;
                  }
                  if (
                    !this.props.plan_description.reports.daily_report_details_params.includes(
                      "end_fuel",
                    )
                  ) {
                    delete tableData.end_fuel;
                  }
                  if (
                    !this.props.plan_description.reports.daily_report_details_params.includes(
                      "fuel_consumption_per_enrg",
                    )
                  ) {
                    delete tableData.fuel_per_enrg;
                  }
                  if (
                    !this.props.plan_description.reports.daily_report_details_params.includes(
                      "enrg_per_fuel_consumption",
                    )
                  ) {
                    delete tableData.enrg_per_fuel;
                  }
                  if (
                    !this.props.plan_description.reports.daily_report_details_params.includes(
                      "load_percentage",
                    )
                  ) {
                    delete tableData.load_percentage;
                  }
                });
              }
            }
          }
        }
      });
      detailedData.table_data.config.pagination_data.total =
        tableDateArray[selectedThing].length;
    });
  }
  detailedData.head_data = tableColumn;
  detailedData.row_data = tableDateArray;
  let detailedTableData = {
    starAdded: starAdded,
    detailedData: detailedData,
    summaryData: summaryData,
  };
  return detailedTableData;
}
