import {
  getThingsData,
  retriveThingsList,
  retriveTasksData,
} from "@datoms/js-sdk";
import AntSelect from "@datoms/react-components/src/components/AntSelect";
import AntOption from "@datoms/react-components/src/components/AntOption";
import AntTabPane from "@datoms/react-components/src/components/AntTabPane";
import AntTabs from "@datoms/react-components/src/components/AntTabs";
import TableList from "@datoms/react-components/src/components/TableList";
import AntDivider from "@datoms/react-components/src/components/AntDivider";
import ReportController from "@datoms/react-components/src/components/ReportController";
import AntSwitch from "@datoms/react-components/src/components/AntSwitch";
import _filter from "lodash/filter";
import _find from "lodash/find";
import _sortBy from "lodash/sortBy";
import _reverse from "lodash/reverse";
import _remove from "lodash/remove";
import moment from "moment-timezone";
import queryString from "query-string";
import React from "react";
import "./style.less";
import Loading from "@datoms/react-components/src/components/Loading";
import { formData } from "../../../../../configuration/reportFormData";
import TableObjectData from "../../../../../configuration/TableObjectData";
import { getThingsAndParameterData } from "../../../../../data_handling/thingsListManipulation";
import { filterDginIot } from "../../../../../data_handling/DGinIot";
import { filteredFuelTankThings } from "../../../../../data_handling/FuelTankThing";
// from summary
import { reportConfigs } from "../../../ReportsParamConfigure/report-configs";
import { getFinalPreference } from "../../../ReportsParamConfigure/logic";
import { TimeFormatter } from "../../../../../data_handling/TimeFormatting";
import { viewTableDataFunction } from "./logic/viewData";
import { filesDownload } from "./logic/downloadData";
import ReportsHeader from "../../../components/reports-header";
import DownloadModal from "../../../components/download-modal";
import CustomizeModal from "../../../components/CustomizeModal";
import ScheduleDrawer from "../../../components/ScheduleDrawer";
import { noOfThingsGreaterThanSix } from "../../../libs/NoOfThingsGreaterSix";
import { findDownload } from "../../../data_handling/FindDownloadInUrl";
import { getDownloadFileName } from "../../../data_handling/downloadFilename";
import { getBaseUrl } from "@datoms/js-utils/src/base-url-logic";
import { flushSync } from "react-dom";

export default class DailyReports extends React.Component {
  downloadModalRef = React.createRef();
  invisibleReportRef = React.createRef();
  customizeDrawerRef = React.createRef();
  scheduleDrawerRef = React.createRef();
  constructor(props) {
    super(props);
    this.parsed = queryString.parse(props.location.search);
    this.state = {
      scheduleDrawerVisible: false,
      inputDataSetLoading: true,
      loading: true,
      application_name: props.application_name ? props.application_name : "-",
      client_name: props.client_name,
      fromTime: moment().subtract(30, "days").startOf("day").unix(),
      uptoTime: moment().endOf("day").unix(),
      formData: formData,
      edit_form_data: false,
      view_type_value: ["grid"],
      TableObjectData: TableObjectData,
      client_id: props.client_id,
      application_id: props.application_id,
      trip_switch: false,
      config_object: this.mergeParamConfig(props),
      isDownloadInUrl: findDownload(props?.history),
    };
    this.data = {
      client_id: props.client_id ? props.client_id : 365,
      application_id: props.application_id ? props.application_id : 16,
    };
    this.onExportReady = this.onExportReady(this);
    this.table_index_map = {
      runhour: "runhour",
      trips: "total_dg_runs",
      fuel_cons: "fuel_consumed",
      enrg_gen: "energy_generated",
      start_energy: "start_energy",
      end_energy: "end_energy",
      mileage: "fuel_consumption_per_hr",
      start_fuel: "start_fuel",
      end_fuel: "end_fuel",
      fuel_per_enrg: "fuel_consumed_per_unit_kwh",
      enrg_per_fuel: "energy_generated_per_unit_litre_fuel_consumed",
      load_percentage: "load_percentage",
      fuel_filled: "fuel_filled",
      fuel_drained: "fuel_drained",
    };
    this.viewTableDataFunction = viewTableDataFunction.bind(this);
    this.filesDownload = filesDownload.bind(this);
  }

  async getListData(data) {
    let totalData = await retriveThingsList(data);
    totalData = filterDginIot.bind(this)(totalData, "reports");
    let filteredThingWithFuelTankThingIds = filteredFuelTankThings(
      totalData,
      71,
    ).filteredThingWithFuelTankThingIds;
    if (
      filteredThingWithFuelTankThingIds &&
      filteredThingWithFuelTankThingIds.length
    ) {
      filteredThingWithFuelTankThingIds.map((thing_id) => {
        _remove(totalData.things, { id: thing_id });
      });
    }
    const categoryIds = [];
    if(this.props.location.pathname.includes("reports/template-reports")) {
      categoryIds.push(18);
    } else {
      categoryIds.push(96);
    }
    let filteredThing = _filter(totalData.things, function (o) {
      return categoryIds.includes(o.category);
    });
    totalData["things"] = filteredThing;
    let modifiedResponse = getThingsAndParameterData(totalData);
    flushSync(() => {
      this.setState({
        loading: noOfThingsGreaterThanSix(totalData?.things) ? false : true,
        totalData: totalData,
        modifiedResponse: modifiedResponse,
        selectedThing:
          this.parsed && this.parsed["thing_id"]
            ? parseInt(this.parsed["thing_id"])
            : modifiedResponse.all_thing_ids[0],
        selectedThingArray: noOfThingsGreaterThanSix(totalData?.things)
          ? []
          : this.parsed && this.parsed["thing_id"]
            ? [parseInt(this.parsed["thing_id"])]
            : modifiedResponse.all_thing_ids,
      });
    });
  }

  async getApiData(thing, file_download = false) {
    let thingNameList = this.state.modifiedResponse.thing_name_list;
    let data_packet_aggr = {
      data_type: "aggregate",
      aggregation_period: 86400,
      parameters: this.state.modifiedResponse.param_key_data,
      parameter_attributes: ["sum", "avg"],
      things: thing,
      from_time: this.state.fromTime,
      upto_time: this.state.uptoTime,
    };
    console.log("skjhjkshf", this.props);
    let response = await getThingsData(
      data_packet_aggr,
      this.props.client_id,
      this.props.application_id,
    );
    let data_packet_snapshot = {
      data_type: "aggregate",
      aggregation_period: 86400,
      parameters: ["fuel", "enrg", "trip_valid_fuel"],
      parameter_attributes: ["snapshot", "initial"],
      things: thing,
      from_time: this.state.fromTime,
      upto_time: this.state.uptoTime,
    };
    let responseSnapshot = await getThingsData(
      data_packet_snapshot,
      this.props.client_id,
      this.props.application_id,
    );

    let missionData = {
      client_id: this.props.client_id,
      application_id: this.props.application_id,
      StartAfter: moment.unix(this.state.fromTime).toISOString(),
      StartBefore: moment.unix(this.state.uptoTime).toISOString(),
      ResultsPerPage: 2000,
      task_type: 1,
      thing_list: thing,
      GetDetails: true,
    };
    let missionList = await retriveTasksData(missionData);

    if (file_download) {
      return { response, responseSnapshot, missionList };
    }

    let detailedViewTable = this.viewTableDataFunction(
      response,
      responseSnapshot,
      missionList,
      thing,
    );
    this.setState(
      {
        loading: false,
        pageLoading: false,
        thingsFormEdit: thingNameList,
        response: response,
        responseSnapshot: responseSnapshot,
        missionList: missionList,
        thingNameList: thingNameList,
        detailedViewTable: detailedViewTable,
      },
      () => {
        this.formDataEditFunction();
      },
    );
  }

  formDataEditFunction() {
    let thingListArray = [];
    Object.keys(this.state.thingNameList).map((thingNameListKey) => {
      return thingListArray.push({
        name: this.state.thingNameList[thingNameListKey],
        value: parseInt(thingNameListKey),
      });
    });
    let FormDataObject = JSON.parse(JSON.stringify(this.state.formData));
    FormDataObject.thingSelectData.data.options = thingListArray;

    FormDataObject.thingSelectData.data.defaultValue =
      this.state.modifiedResponse.all_thing_ids;

    FormDataObject.rangePicker.data.defaultValue = [
      TimeFormatter(
        this.props.user_preferences?.time_format,
        this.state.fromTime,
        "DD-MM-YYYY, HH:mm",
      ),
      TimeFormatter(
        this.props.user_preferences?.time_format,
        this.state.uptoTime,
        "DD-MM-YYYY, HH:mm",
      ),
    ];

    this.setState({
      formData: FormDataObject,
    });
  }

  isGmmco() {
    if (parseInt(this.props.vendor_id) === 1062) {
      return true;
    } else {
      return false;
    }
  }

  isMechanicalDG(selectedThing) {
    let isMechanicalDG = false;
    if (this.state.totalData) {
      let currentAsset = _find(this.state.totalData.things, {
        id: parseInt(selectedThing),
      });
      if (
        currentAsset &&
        currentAsset.thing_details &&
        currentAsset.thing_details.dg_parameter_type
      ) {
        isMechanicalDG =
          currentAsset.thing_details.dg_parameter_type === "mechanical";
      }
    }
    return isMechanicalDG;
  }

  mergeParamConfig(props) {
    let defaultConfig = JSON.parse(
      JSON.stringify(
        reportConfigs(props.vendor_id, props.client_id)["daily_report"],
      ),
    );
    let finalConfig;
    let final_preference = getFinalPreference(
      props.user_preferences,
      "daily_report",
    );
    if (final_preference) {
      let existingConfig = JSON.parse(JSON.stringify(final_preference));
      finalConfig = {
        summary: {
          ...defaultConfig.summary,
          ...existingConfig.summary,
        },
        table: { ...defaultConfig.table, ...existingConfig.table },
      };
    } else {
      finalConfig = defaultConfig;
    }
    return finalConfig;
  }

  async getInputForDownload() {
    const { isDownloadInUrl, modifiedResponse } = this.state;
    if (typeof window.getInputDataSet === "function") {
      let inputDataSet = await window.getInputDataSet();
      if (isDownloadInUrl) {
        flushSync(() =>
          this.setState({
            inputDataSet: inputDataSet,
            selectedThingArray: inputDataSet?.things || modifiedResponse?.all_thing_ids,
            fromTime: inputDataSet?.from_time,
            uptoTime: inputDataSet?.upto_time,
          }),
        );
      }
    }
  }

  async componentDidMount() {
    await this.getListData(this.data);
    await this.getInputForDownload();
    if (this.state.inputDataSet) {
      await this.filesDownload();
    } else {
      if (!noOfThingsGreaterThanSix(this.state.totalData?.things)) {
        await this.getApiData([this.state.selectedThing]);
      }
    }
  }

  showEditModal() {
    this.setState({
      edit_form_data: true,
    });
  }

  cancelEditModal() {
    this.setState({
      edit_form_data: false,
    });
  }

  goBackPage() {
    this.props.history.push(getBaseUrl(this.props, "reports"));
  }

  tabledataDownload(totalTableData, tableId) {
    let totalTable = [],
      tableBody = {},
      tableHead = {};
    if (totalTableData) {
      if (Array.isArray(this.state.selectedThingArray)) {
        this.state.selectedThingArray.map((tableDateArrayKey) => {
          if (!tableHead[tableDateArrayKey]) {
            tableHead[tableDateArrayKey] = [];
          }
          if (!tableBody[tableDateArrayKey]) {
            tableBody[tableDateArrayKey] = [];
          }
          if (
            totalTableData &&
            Array.isArray(totalTableData.row_data[tableDateArrayKey])
          ) {
            totalTableData.row_data[tableDateArrayKey].map((tbodyData) => {
              let tdArray = [];
              Object.keys(tbodyData).map((tbodyDatakey) => {
                return tdArray.push(<td>{tbodyData[tbodyDatakey]}</td>);
              });
              return tableBody[tableDateArrayKey].push(<tr>{tdArray}</tr>);
            });
          }
          if (
            totalTableData &&
            Array.isArray(totalTableData.row_data[tableDateArrayKey])
          ) {
            totalTableData.head_data[tableDateArrayKey].map((theadData) => {
              return tableHead[tableDateArrayKey].push(
                <th>{theadData.title}</th>,
              );
            });
          }
          return totalTable.push(
            <table id={tableId + tableDateArrayKey}>
              <thead>
                <tr>{tableHead[tableDateArrayKey]}</tr>
              </thead>
              <tbody>{tableBody[tableDateArrayKey]}</tbody>
            </table>,
          );
        });
      }
    }
    return totalTable;
  }

  selectedThingFunc(e) {
    this.setState(
      {
        selectedThing: parseInt(e),
        pageLoading: true,
      },
      async () => {
        await this.getApiData([this.state.selectedThing]);
      },
    );
  }

  onRangeChange(e, isCustom) {
    this.setState(
      {
        isCustom,
        fromTime:
          isCustom === "custom"
            ? moment().subtract(30, "days").startOf("day").unix()
            : moment.unix(e[0]).startOf("day").unix(),
        uptoTime:
          isCustom === "custom"
            ? moment().endOf("day").unix()
            : moment.unix(e[1]).endOf("day").unix(),
        pageLoading: true,
        selectedThing:
          this.state.selectedThing ??
          this.state.modifiedResponse.all_thing_ids[0],
      },
      async () => {
        await this.getApiData([this.state.selectedThing]);
      },
    );
  }
  customDrawerCallback(e) {
    this.setState(
      {
        loading: true,
        fromTime:
          e.isCustom === "custom"
            ? moment().subtract(30, "days").startOf("day").unix()
            : e.from_time,
        uptoTime:
          e.isCustom === "custom" ? moment().endOf("day").unix() : e.upto_time,
        selectedThingArray: e.selected_thing_array,
        isCustom: e.isCustom,
        selectedThing: e.selected_thing_array[0],
        vendors: e.selectedVendors,
      },
      async () => {
        await this.getApiData([this.state.selectedThing]);
      },
    );
  }

  onOpenChange(e) {}

  scheduleBtnClicked() {
    this.setState(
      {
        scheduleDrawerVisible: true,
      },
      () => {
        this.scheduleDrawerRef.current.showScheduleDrawer();
      },
    );
  }

  scheduleDrawerClosed() {
    this.setState({
      scheduleDrawerVisible: false,
    });
  }

  customizeBtnClicked() {
    this.customizeDrawerRef.current.showDrawer();
  }

  getMachineInfo(id) {
    //	let thingId = this.state.selectedThing ? this.state.selectedThing : id;
    const { totalData } = this.state;
    let currentThing = "";
    if (totalData && totalData.things && totalData.things.length) {
      currentThing = totalData.things.find((item) => item.id == id);
    }
    let view_string = "",
      pdf_string1 = "",
      pdf_string2 = "",
      machine_info = this.props.machine_info;
    if (machine_info && machine_info.length) {
      machine_info.map((info_item, index) => {
        let machine_value = "NA";

        if (info_item.key === "lifetime_runhour") {
          let findCalculatedLifetimeRunhour = "",
            findRnHr = {},
            findCalcRnhr = {};
          if (
            currentThing &&
            currentThing.parameters &&
            currentThing.parameters.length
          ) {
            findRnHr = currentThing.parameters.find(
              (item) => item.key === "rnhr",
            );

            findCalcRnhr = currentThing.parameters.find(
              (item) => item.key === "calculated_runhour",
            );
            if (
              findRnHr &&
              findRnHr.value &&
              findRnHr.value !== "" &&
              parseFloat(findRnHr.value) > 0
            ) {
              findCalculatedLifetimeRunhour = parseFloat(findRnHr.value) * 3600;
            } else if (
              findCalcRnhr &&
              findCalcRnhr.aggregated_value &&
              findCalcRnhr.aggregated_value.lifetime &&
              findCalcRnhr.aggregated_value.lifetime.sum
            ) {
              findCalculatedLifetimeRunhour =
                findCalcRnhr.aggregated_value.lifetime.sum;
            }
          }
          if (findCalculatedLifetimeRunhour) {
            let lifetimehour = Math.floor(findCalculatedLifetimeRunhour / 3600);
            let lifetimeMin = Math.floor(
              (findCalculatedLifetimeRunhour % 3600) / 60,
            );
            machine_value =
              (lifetimehour < 10 ? "0" + lifetimehour : lifetimehour) +
              " : " +
              (lifetimeMin < 10 ? "0" + lifetimeMin : lifetimeMin) +
              (this.isGmmco() ? " SMU" : " Hrs");
          }
        } else if (info_item.key === "address") {
          machine_value =
            currentThing && currentThing.address ? currentThing.address : "NA";
        } else {
          if (currentThing && currentThing.thing_details) {
            machine_value = currentThing.thing_details[info_item.key]
              ? (info_item.key === "make" &&
                this.state.makeDetails?.[
                  currentThing.thing_details[info_item.key]
                ]
                  ? this.state.makeDetails[
                      currentThing.thing_details[info_item.key]
                    ]
                  : currentThing.thing_details[info_item.key]) +
                (info_item.key === "kva" ? " KVA" : "")
              : "NA";
          }
        }

        if (index + 1 !== machine_info.length) {
          pdf_string1 += `${info_item.label} - ${machine_value}  \xa0\xa0|\xa0\xa0  `;
          view_string += `${info_item.label} - ${machine_value}  \xa0\xa0|\xa0\xa0  `;
        } else {
          pdf_string2 += `${info_item.label} - ${machine_value}`;
          view_string += `${info_item.label} - ${machine_value}`;
        }
      });
    }
    return { pdf_string1, pdf_string2, view_string };
  }

  // download files backyard function

  getDownloadRender() {
    const { inputDataSet } = this.state;
    console.log("Download Modal 1 ==>", this.state.downloadData);
    let downloadRender = this.state.downloadData ? (
      <div
        style={{
          opacity: 0,
          visibility: "hidden",
          overflow: "hidden",
          "max-height": 0,
        }}
      >
        <ReportController
          is_white_label={this.props.is_white_label}
          vendor_name={this.props.vendor_name}
          ref={this.invisibleReportRef}
          onExportReady={this.onExportReady}
          key={moment().unix()}
          {...this.state.downloadData}
          parameters={[
            "calculated_runhour",
            "distance_travelled",
            "fuel_filled",
            "fuel_theft",
            "fuel_consumption",
            "speed",
          ]}
        />
      </div>
    ) : (
      ""
    );
    this.setState(
      {
        download_render: downloadRender,
        loading: false,
        download_loading: false,
        inputDataSetLoading: false,
      },
      () => {
        if (inputDataSet) {
          setTimeout(() => {
            this.downloadModalCallback(inputDataSet?.type);
            if (typeof window.reportGenerationCompleted === "function") {
              window.reportGenerationCompleted(
                getDownloadFileName(
                  inputDataSet?.name ?? "Daily Report",
                  inputDataSet?.type,
                ),
              );
            }
          }, 2000);
        } else {
          setTimeout(() => {
            this.downloadModalCallback(this.state.fileFormat);
          }, 1000);
        }
      },
    );
  }

  downloadBtnClicked() {
    this.downloadModalRef.current.showModal();
  }
  modalDownloadBtnClicked(e) {
    this.setState({ download_loading: true, fileFormat: e }, async () => {
      await this.filesDownload();
    });
  }

  downloadModalCallback(fileFormat) {
    if (this.state.downloadData) {
      if (fileFormat.includes("csv")) {
        this.invisibleReportRef.current.exportCSV(null, {});
      }

      if (fileFormat.includes("xls")) {
        this.invisibleReportRef.current.exportXLSX(null, {
          maxCellMergeCount: 20,
        });
      }

      if (fileFormat.includes("pdf")) {
        this.invisibleReportRef.current.exportPDF({
          header: {
            left: {
              text: "",
              fontType: "italics",
            },
            right: {
              text: "",
            },
          },
        });
      }
    }
  }

  onExportReady() {
    let { autoDownloadFormat } = this.props;
    if (autoDownloadFormat) {
      this.downloadModalCallback(autoDownloadFormat);
    }
  }

  render() {
    const { inputDataSet } = this.state;
    const { dg_in_iot_mode } = this.props;
    let loadPage = "";
    if (this.state.loading) {
      loadPage = (
        <Loading
          show_logo={this.props.loading_logo}
          className="align-center-loading"
        />
      );
    } else {
      let editThing = "";
      let detailedTableToRender = "";
      let summaryView = [];
      if (this.state.thingsFormEdit) {
        let totaTableSection = [];
        if (this.state.selectedThing) {
          if (
            this.state.detailedViewTable &&
            this.state.detailedViewTable.detailedData
          ) {
            detailedTableToRender = (
              <TableList
                config_data={
                  this.state.detailedViewTable.detailedData.table_data.config
                }
                columns={
                  this.state.detailedViewTable.detailedData.head_data[
                    this.state.selectedThing
                  ]
                }
                dataSource={
                  this.state.detailedViewTable.detailedData.row_data[
                    this.state.selectedThing
                  ]
                }
                horizontalScroll={false}
                smallTable={true}
                breakPoint={200}
                breakpoint2={100}
              />
            );
          }
          if (
            this.state.detailedViewTable &&
            this.state.detailedViewTable.summaryData &&
            this.state.detailedViewTable.summaryData[
              this.state.selectedThing
            ] &&
            this.state.detailedViewTable.summaryData[this.state.selectedThing]
              .length
          ) {
            this.state.detailedViewTable.summaryData[
              this.state.selectedThing
            ].map((data) => {
              summaryView.push(
                <div className="summary-container">
                  <div>
                    <div className="name">{data.name}</div>
                    <div className="value">{data.value}</div>
                  </div>
                  <AntDivider type="vertical" />
                </div>,
              );
            });
          }
        }
        let totalPageRender = {},
          optionsArray = [];
        if (Array.isArray(this.state.selectedThingArray)) {
          this.state.selectedThingArray.map((tableDateArrayKey) => {
            if (!totalPageRender[tableDateArrayKey]) {
              totalPageRender[tableDateArrayKey] = [];
            }
            if (this.state.pageLoading) {
              totalPageRender[tableDateArrayKey] = (
                <Loading show_logo={this.props.loading_logo} />
              );
            } else {
              totalPageRender[tableDateArrayKey].push(
                <div className="total-report-div">
                  <div
                    className="machine-info-container"
                    style={{ color: "#7686A1" }}
                  >
                    {
                      this.getMachineInfo(
                        window.innerWidth <= 576
                          ? this.state.selectedThing
                          : tableDateArrayKey,
                      ).view_string
                    }
                  </div>
                  <div className="table-container">
                    <div className="table-heading">Summary</div>
                    <div className="summary-view">{summaryView}</div>
                  </div>
                  <div className="table-container">
                    <div className="table-heading">Detailed Data</div>
                    {detailedTableToRender}
                    <div
                      ref={"start_enable_" + tableDateArrayKey}
                      className="hide"
                    >
                      {this.state.detailedViewTable &&
                      this.state.detailedViewTable.starAdded &&
                      this.state.detailedViewTable.starAdded[tableDateArrayKey]
                        ? "Note:\n* One or more trips are spanning over more than one date"
                        : ""}
                    </div>
                    <div>
                      {this.state.detailedViewTable &&
                      this.state.detailedViewTable.starAdded &&
                      this.state.detailedViewTable.starAdded[
                        tableDateArrayKey
                      ] ? (
                        <div>
                          <div>Note: </div>
                          <div>
                            * One or more trips are spanning over more than one
                            date
                          </div>
                        </div>
                      ) : (
                        ""
                      )}
                    </div>
                  </div>
                </div>,
              );
            }
            if (window.innerWidth <= 576) {
              optionsArray.push(
                <AntOption
                  className="options-to-be-selected"
                  value={tableDateArrayKey}
                >
                  {this.state.thingsFormEdit[tableDateArrayKey]}
                </AntOption>,
              );
              totaTableSection = totalPageRender[tableDateArrayKey];
            } else {
              totaTableSection.push(
                <AntTabPane
                  disabled={this.state.pageLoading}
                  tab={this.state.thingsFormEdit[tableDateArrayKey]}
                  key={tableDateArrayKey}
                >
                  {totalPageRender[tableDateArrayKey]}
                </AntTabPane>,
              );
            }

            return totaTableSection;
          });
        }
        if (window.innerWidth <= 576) {
          editThing = (
            <div className="mobile-report-body">
              <AntSelect
                defaultValue={this.state.selectedThing}
                value={this.state.selectedThing}
                className="template-report-tab"
                onChange={(e) => this.selectedThingFunc(e)}
              >
                {optionsArray}
              </AntSelect>
              {totaTableSection}
            </div>
          );
        } else {
          editThing = (
            <AntTabs
              className="template-report-tab"
              defaultValue={this.state.selectedThing}
              // value={this.state.selectedThing}
              type="card"
              onChange={(e) => this.selectedThingFunc(e)}
            >
              {totaTableSection}
            </AntTabs>
          );
        }
      }
      loadPage = inputDataSet ? (
        this.state.download_render
      ) : (
        <div className="template-reports-content">
          <ReportsHeader
            goBackPage={() => this.goBackPage()}
            from_time={this.state.fromTime}
            upto_time={this.state.uptoTime}
            things_list={this.state.totalData}
            report_type_title="Daily Report"
            date_selection={true}
            onRangeChange={(e, isCustom) => this.onRangeChange(e, isCustom)}
            isCustom={this.state.isCustom}
            onOpenChange={(e) => this.onOpenChange(e)}
            customizeBtnClicked={() => this.customizeBtnClicked()}
            scheduleBtnClicked={() => this.scheduleBtnClicked()}
            isScheduledReport={this.props.isScheduledReport}
            downloadBtnClicked={() => this.downloadBtnClicked()}
            download_loading={this.state.download_loading}
            {...this.props}
            disabledDownload={
              this.props.application_id === 16 &&
              this.props.logged_in_user_client_id === this.props.client_id &&
              !this.props.getViewAccess(["Reports:Download"])
            }
          />
          <DownloadModal
            ref={this.downloadModalRef}
            callback={(e) => this.downloadModalCallback(e)}
            report_name="Daily"
            pdfMaxCol={12}
			modalDownloadBtnClicked={(e)=>this.modalDownloadBtnClicked(e)}
          />
          {this.state.download_render}
          {editThing}
          <CustomizeModal
            ref={this.customizeDrawerRef}
            callback={(e) => this.customDrawerCallback(e)}
            from_time={this.state.fromTime}
            upto_time={this.state.uptoTime}
            selected_things={this.state.selectedThingArray}
            all_things_list={this.state.totalData.things}
            vendors={this.state.vendors}
            goBackPage={() => this.goBackPage()}
            isCustom={this.state.isCustom}
            {...this.props}
          />
          {this.state.scheduleDrawerVisible && (
            <ScheduleDrawer
              from_time={this.state.fromTime}
              upto_time={this.state.uptoTime}
              ref={this.scheduleDrawerRef}
              scheduleDrawerClosed={() => this.scheduleDrawerClosed()}
              selected_things={this.state.selectedThingArray}
              all_things_list={this.state.totalData.things}
              report_type="Daily Report"
              client_id={this.props.client_id}
              parent_client_id={this.props.parent_client_id}
              parent_application_id={this.props.parent_application_id}
              app_id={this.props.application_id}
            />
          )}
        </div>
      );
    }
    return <div id="dg_daily_daily_reports">{loadPage}</div>;
  }
}
