import { retrieveSitesList, retriveThingsList } from '@datoms/js-sdk';
import _find from 'lodash/find';
import AntNotification from '@datoms/react-components/src/components/AntNotification';
import AntMessage from '@datoms/react-components/src/components/AntMessage';

export function openNotification(type, msg) {
	if (window.innerWidth > 576) {
		AntNotification({
			type: type,
			message: msg,
			placement: 'bottomLeft',
			className: 'alert-' + type,
		});
	} else {
		AntMessage(type, msg);
	}
}

export async function callThingsList() {
	const { client_id, application_id } = this.props;
	const thingsDetails = await retriveThingsList(
		{
			client_id: client_id,
			application_id: application_id,
		},
	);
	this.setState({
		thingsDetails,
	});
}

export async function callSitesList() {
	const { client_id } = this.props;
	const query = `?site_type=3&page_no=${1}&results_per_page=${1000}`;
	const siteListResponse = await retrieveSitesList(client_id, query);
	if (siteListResponse?.status !== 'success') {
		this.openNotification('error', siteListResponse?.message);
		return;
	}
	const siteList = siteListResponse?.data;
	const siteTabArray = [], selectedSiteArray = [];
	if (siteList?.length) {
		siteList.map((list) => {
			siteTabArray.push({ id: list.id, name: list.name });
			selectedSiteArray.push(list.id)
		});
	}
	const selectedSite = siteList.length > 0 ? [siteList[0].id] : null;

	this.setState({
		siteTabArray,
		selectedSite,
		selectedSiteArray,
	});
}
