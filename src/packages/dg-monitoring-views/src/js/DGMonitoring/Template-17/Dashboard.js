import { flushSync } from "react-dom";
import {
  disconnectSocketConnection,
  establishSocketConnection,
  getThingsAndParameterData,
  getThingsData,
  retriveEventsData,
  retriveTasksData,
  retriveThingsList,
  retriveFaultsData,
  subscribeForEntityUpdates,
  subscribeForEventsUpdates,
  subscribeForThingsUpdates,
} from "@datoms/js-sdk";
import { getAllparameterDataAggr } from "@datoms/js-utils/src/ParameterDataManipulation";
import { getSelectedparameterDataWithTimestamp } from "../../data_handling/ParameterDataManipulation";
import AntButton from "@datoms/react-components/src/components/AntButton";
import AntCol from "@datoms/react-components/src/components/AntCol";
import AntLayout from "@datoms/react-components/src/components/AntLayout";
import AntOption from "@datoms/react-components/src/components/AntOption";
import AntRow from "@datoms/react-components/src/components/AntRow";
import AntSpin from "@datoms/react-components/src/components/AntSpin";
import AntSelect from "@datoms/react-components/src/components/AntSelect";
import Loading from "@datoms/react-components/src/components/Loading";
import GraphHighcharts from "@datoms/react-components/src/components/GraphHighcharts";
import AntCard from "@datoms/react-components/src/components/AntCard";
import { totalActiveFaultsWithWarnTripStatus } from "../../data_handling/getFaultTypeRealtime";
import _findIndex from "lodash/findIndex";
import _find from "lodash/find";
import _uniqBy from "lodash/uniqBy";
import _orderBy from "lodash/orderBy";
import _filter from "lodash/filter";
import _sortBy from "lodash/sortBy";
import _sumBy from "lodash/sumBy";
import _merge from "lodash/merge";
import moment from "moment-timezone";
import queryString from "query-string";
import React from "react";
/*Styles*/
import "../../../styles/DGMonitoring/Template-20/dashboard.less";
import GoogleMapComponent from "../../components/MapComponents/GoogleMapComponent";
import GraphObjectData from "../../configuration/GraphObjectData";
import ActivityPanel from "../../components/ActivityPanel";
import Kpis from "../../components/Kpis";
import ListView from "../../components/ListView";
/*Components*/
import PanelComponent from "../../components/PanelComponent";
import ActivityObject from "../../configuration/dashboardConfiguration/ActivityObject";
import GraphWithCardConfig from "../../configuration/dashboardConfiguration/GraphWithCardConfig";
/*Configs*/
import CardViewConfig from "../../configuration/dashboardConfiguration/KpiConfig";
import ListViewConfig from "../../configuration/dashboardConfiguration/ListViewConfig";
import MapConfig from "../../configuration/dashboardConfiguration/MapConfig";
import PanelConfig from "../../configuration/panelConfig/PanelConfigObject";
import DashboardMobile from "./DashboardMobile";
import DGMapOnline from "../../GenericTemplate/images/DG-Icon-Online.svg";
import DGMapOffline from "../../GenericTemplate/images/DGSet-MapIcons-Offline.svg";
import DGMapSwitchOff from "../../GenericTemplate/images/DGSet-MapIcons-Stopped.svg";
import DGMapNotConnected from "../../GenericTemplate/images/DGSet-MapIcons-NotConnected.svg";
import DGMapOnlineBoth from "../../GenericTemplate/images/DG-Online-Running-W&T-Icon.svg";
import DGMapOfflineBoth from "../../GenericTemplate/images/DG-Offline-W&T-Icon.svg";
import DGMapSwitchOffBoth from "../../GenericTemplate/images/DG-Online-Stopped-W&T-Icon.svg";
import DGMapNotConnectedBoth from "../../GenericTemplate/images/DG-Online-Disconnected-W&T-Icon.svg";
import DGMapOnlineTrip from "../../GenericTemplate/images/DG-Online-Running-Trip-Icon.svg";
import DGMapOfflineTrip from "../../GenericTemplate/images/DG-Offline-Trip-Icon.svg";
import DGMapSwitchOffTrip from "../../GenericTemplate/images/DG-Online-Stopped-Trip-Icon.svg";
import DGMapNotConnectedTrip from "../../GenericTemplate/images/DG-Online-Disconnected-Trip-Icon.svg";
import DGMapOnlineWarning from "../../GenericTemplate/images/DG-Online-Running-Warning-Icon.svg";
import DGMapOfflineWarning from "../../GenericTemplate/images/DG-Offline-Warning-Icon.svg";
import DGMapSwitchOffWarning from "../../GenericTemplate/images/DG-Online-Stopped-Warning-Icon.svg";
import DGMapNotConnectedWarning from "../../GenericTemplate/images/DG-Online-Disconnected-Warning-Icon.svg";
import DGMapOnlineNotDefined from "../../GenericTemplate/images/DG-Online-Running-Not-Defined-Icon.svg";
import DGMapOfflineNotDefined from "../../GenericTemplate/images/DG-Offline-NotDefined-Icon.svg";
import DGMapSwitchOffNotDefined from "../../GenericTemplate/images/DG-Online-Stopped-NotDefined-Icon.svg";
import DGMapNotConnectedNotDefined from "../../GenericTemplate/images/DG-Online-Disconnected-NotDefined-Icon.svg";
import GmmcoMapSwitchOn from "../../../images/DGMonitoring/GmmcoOnline.svg";
import GmmcoMapSwitchOff from "../../../images/DGMonitoring/GmmcoSwitchOff.svg";
import GmmcoMapOffline from "../../../images/DGMonitoring/GmmcoOffline.svg";
import MahindraSwitchOn from "../../../images/DGMonitoring/powerol_online.svg";
import MahindraSwitchOff from "../../../images/DGMonitoring/powerol_switch_off.svg";
import MahindraOffline from "../../../images/DGMonitoring/powerol_offline.svg";
import MapMoving from "../../../images/DGMonitoring/map_moving.svg";
import FuelOnIcon from "../../../images/DGMonitoring/fuel-tank-icon-green.svg";
import FuelOffIcon from "../../../images/DGMonitoring/fuel-tank-icon-grey.svg";
//import MapMoving from '../../../images/DGMonitoring/moving-dg-gif.gif';
import { getFault } from "../../data_handling/FaultDataWithDuration";
import {
  serviceAlertFetch,
  maintenanceTextGen,
} from "../../data_handling/applicationMaintenanceDue";
import {
  filteredFuelTankThings,
  findFuelTankThing,
} from "../../data_handling/FuelTankThing";
import { filterDginIot } from "../../data_handling/DGinIot";
import { TimeFormatter } from "../../data_handling/TimeFormatting";
import { getBaseUrl } from "@datoms/js-utils/src/base-url-logic";
import { getFuelLevelInLitres } from "../../data_handling/ClientWiseConditions";
import plan_description from "./logic/getPlanDesc";
import FilterSelectWithSearch from "@datoms/react-components/src/components/FilterSelectWithSearch";

export default class Dashboard extends React.Component {
  constructor(props) {
    super(props);
    this.parsed = queryString.parse(props.location.search);
    GraphObjectData.graph_data.config.timezone =
      props.user_preferences.timezone;
    this.state = {
      screenWidth: window.innerWidth,
      PanelConfig: PanelConfig.panelViewObject,
      CardViewConfig: CardViewConfig,
      loading: true,
      MapConfig: MapConfig.mapdetails,
      ListViewConfig: ListViewConfig.ListObject,
      GraphObjectData: GraphObjectData,
      GraphWithCardConfig: GraphWithCardConfig.GraphSeriesConfig,
      StartAfter: moment().subtract(1825, "days").startOf("day").unix(),
      StartBefore: moment().endOf("day").unix(),
      resButton: "trend",
      dateSelected: "last_thirty_days",
      ActivityObject: ActivityObject,
      type: this.parsed["type"]
        ? this.parsed["type"]
        : CardViewConfig.data[0].key,
      fromTime: moment().subtract(30, "days").startOf("day").unix(),
      uptoTime: moment().endOf("day").unix(),
      faultFromTime: moment().subtract(30, "days").startOf("day").unix(),
      faultUptoTime: moment().endOf("day").unix(),
      fuel_trend_from_time: moment().subtract(7, "days").startOf("day").unix(),
      fuel_trend_upto_time: moment().endOf("day").unix(),
    };
    this.listApiData = {
      client_id: props.client_id,
      application_id: props.application_id,
    };
    if (window.localStorage.getItem("Client-Id") && !props.dg_in_iot_mode) {
      this.listApiData.client_id = window.localStorage.getItem("Client-Id");
    }
    this.bucketTime = null;
    this.bucket = {
      raw_data: {},
      "1_hr_avg": {},
      "1_day_avg": {},
    };
    this.flagForTimeout = [];
    this.timer = null;
    this.updateEntityDetails = this.updateEntityDetails.bind(this);
    this.dgBasePath = "https://app.datoms.io";

    if (!import.meta.env.VITE_MOBILE && typeof window !== undefined) {
      this.dgBasePath =
        window.location.protocol +
        "//" +
        window.location.host +
        (!window.location.href.includes("localhost")
          ? "/enterprise/" + this.props.client_id
          : "");
    }
  }
  windowResize() {
    this.setState({ screenWidth: window.innerWidth });
  }
  eventChange(e) {
    this.setState({
      selectedTab: e,
    });
  }

  async getServiceAlert() {
    let data = await serviceAlertFetch(this.props.client_id);
    this.setState({
      service_alert_fetch: data,
    });
  }

  getMaintenanceText() {
    let text = maintenanceTextGen(
      this.state.service_alert_fetch,
      this.state.listId,
    );
    return text;
  }

  async getThingsList(data) {
    let totalData = await retriveThingsList(data);
    totalData = filterDginIot.bind(this)(totalData);
    const filterDgThings = totalData.things.filter((o) =>
      [18, 96].includes(o.category),
    );
    const filterGasGensetThings = totalData.things.filter(
      (o) => o.category === 96,
    );
    let filteredThingWithFuelTankThingIds = filteredFuelTankThings(
      totalData,
      71,
    ).filteredThingWithFuelTankThingIds;
    const assetTypeFilter = [];
    if (filterDgThings?.length) {
      [18, 71, 96].map((category) => {
        let findThingForcat = _find(totalData.things, { category: category });
        if (findThingForcat) {
          assetTypeFilter.push({
            title: (
              <div className="thing-cat-filter-div">
                {findThingForcat?.thing_category_icon?.length ? (
                  <img
                    src={findThingForcat?.thing_category_icon}
                    alt="category icon"
                  />
                ) : null}
                {_find(totalData.things_categories, { id: category })?.name}
              </div>
            ),
            value: category,
          });
        }
      });
    }
    const categoyId =
      this.getUrlBreak("category") &&
      !isNaN(parseInt(this.getUrlBreak("category")))
        ? parseInt(this.getUrlBreak("category"))
        : assetTypeFilter[0]?.value;
    const filterThings = totalData.things.filter(
      (o) => o.category === parseInt(categoyId),
    );
    totalData.things = filterThings;
    let offlineArray = this.getOnlineOfflineArray(
      filteredFuelTankThings(totalData, 71).totalThings,
    );
    let modifiedResponse = getThingsAndParameterData(totalData);
    let latestParameterData = this.offlineTimeOutFunction(
      undefined,
      modifiedResponse.latest_parameter_data,
      modifiedResponse,
      totalData,
    );
    let statusArray = [];
    if (totalData.things && totalData.things.length) {
      totalData.things.map((things) => {
        statusArray.push({
          id: things.id,
          status: things.status,
        });
      });
    }

    const filterAssetTypes = [
      {
        optionData: assetTypeFilter,
        selectValue: categoyId,
        sorted: true,
        showSearch: true,
        className: "select-custom",
      },
    ];
    flushSync(() => {
      this.setState(
        {
          latestParameterData: latestParameterData,
          totalData: totalData,
          modifiedResponse: modifiedResponse,
          fuel_tank_things: filteredThingWithFuelTankThingIds,
          thing_status: statusArray,
          offlineArray,
          categoryId: categoyId,
          filterAssetTypes,
        },
        async () => {
          this.latestDataFunc();
          await Promise.all([
            this.getThingDailyAggrData(),
            this.getEvents(this.listApiData),
          ]).then(() => {
            this.setState({
              loading: false,
              trendNotificationLoading: false,
            });
          });
        },
      );
    });
  }

  getOnlineOfflineArray(totalData) {
    let offlineArray = [];
    if (totalData && totalData.length) {
      totalData.map((thing) => {
        offlineArray.push({
          thing_id: thing.id,
          status: thing.status,
        });
      });
    }
    return offlineArray;
  }

  realTimeEventsUpdate(payload) {
    let offlineArray = this.state.offlineArray;
    if (
      payload &&
      //payload.event_data &&
      Object.keys(payload).length &&
      payload.entity_type === "thing" &&
      payload.tags &&
      Array.isArray(payload.tags)
    ) {
      let indexOffline = _findIndex(offlineArray, {
        thing_id: parseInt(payload.entity_id),
      });
      if (indexOffline > -1) {
        if (payload.tags.includes("Offline"))
          offlineArray[indexOffline].status = "offline";
        if (payload.tags.includes("Online"))
          offlineArray[indexOffline].status = "online";
      }
      this.setState({
        offlineArray,
      });
    }
  }

  checkOfflineOnlineStatus(thing_id) {
    let offlineStatus = _find(this.state.offlineArray, {
      thing_id,
    })?.status;
    return offlineStatus === "online" ? "1" : "2";
  }

  latestDataFunc() {
    let latestRcvd = [],
      onlineDgs = [],
      switchOffDgs = [],
      offlineDgs = [];
    this.state.latestParameterData.map((latestParam) => {
      let findThing = _find(this.state.totalData.things, {
        id: latestParam.thing_id,
      });
      if (parseInt(findThing.category) === 71) {
        if (this.state.type === "all_things") {
          latestRcvd.push(latestParam.thing_id);
        } else if (this.state.type === "running") {
          if (this.checkOfflineOnlineStatus(latestParam.thing_id) === "1") {
            latestRcvd.push(latestParam.thing_id);
          }
        } else if (this.state.type === "offline") {
          if (this.checkOfflineOnlineStatus(latestParam.thing_id) === "2") {
            latestRcvd.push(latestParam.thing_id);
          }
        }
        if (latestParam.deviceStatus === "online") {
          if (this.checkOfflineOnlineStatus(latestParam.thing_id) === "1") {
            onlineDgs.push(latestParam.thing_id);
          }
          if (this.checkOfflineOnlineStatus(latestParam.thing_id) === "2") {
            offlineDgs.push(latestParam.thing_id);
          }
        }
      } else {
        console.log("latestParam", latestParam.OnOffStatus);
        if (this.state.type === "all_things") {
          latestRcvd.push(latestParam.thing_id);
        } else if (this.state.type === "running") {
          if (latestParam.OnOffStatus === "1") {
            latestRcvd.push(latestParam.thing_id);
          }
        } else if (this.state.type === "offline" && latestParam.deviceStatus === "online") {
          if (latestParam.OnOffStatus === "2") {
            latestRcvd.push(latestParam.thing_id);
          }
        } else if (this.state.type === "switch_off") {
          if (
            latestParam.OnOffStatus === "0" ||
            latestParam.OnOffStatus === ""
          ) {
            latestRcvd.push(latestParam.thing_id);
          }
        }
        if (latestParam.deviceStatus === "online") {
          if (latestParam.OnOffStatus === "1") {
            onlineDgs.push(latestParam.thing_id);
          } else if (latestParam.OnOffStatus === "2") {
            offlineDgs.push(latestParam.thing_id);
          } else {
            switchOffDgs.push(latestParam.thing_id);
          }
        }
      }
    });
    this.setState(
      {
        latestRcvd: latestRcvd,
        onlineDgs: onlineDgs,
        offlineDgs: offlineDgs,
        switchOffDgs: switchOffDgs,
      },
      () => {
        this.filterCardFuncTion();
        this.mapFunction();
        this.listViewFunction();
        this.graphData();
        this.getActivityData();
      },
    );
  }

  async getThingDailyAggrData() {
    let parameterKeys = [
      "calculated_runhour",
      "enrg",
      "calculated_energy",
      "fuel_consumption",
    ];
    let data_packet_aggr = {
      data_type: "aggregate",
      aggregation_period: 86400,
      parameters: parameterKeys,
      parameter_attributes: ["sum"],
      things: this.state.listId
        ? [this.state.listId]
        : this.state.modifiedResponse.all_thing_ids,
      from_time: this.state.fromTime,
      upto_time: this.state.uptoTime,
    };
    let clientId = this.props.client_id;
    if (
      window.localStorage.getItem("Client-Id") &&
      !this.props.dg_in_iot_mode
    ) {
      clientId = window.localStorage.getItem("Client-Id");
    }
    let response = await getThingsData(
      data_packet_aggr,
      clientId,
      this.props.application_id,
    );
    if (response.status === "success") {
      this.setState({
        responseData: response.data,
        parameterKeys: parameterKeys,
      });
      this.graphData();
    }
  }

  isGpsPathEnabled() {
    return (
      plan_description &&
      plan_description.dashboard_view_section &&
      plan_description.dashboard_view_section.map_gps_path
    );
  }
  async getThingsRawData() {
    if (this.isGpsPathEnabled()) {
      let dataPacketRaw = {
        data_type: "raw",
        aggregation_period: 0,
        parameters: ["lat", "long"],
        parameter_attributes: [],
        things: [this.state.listId],
        from_time: parseInt(this.state.moving_start_time),
        upto_time: moment().unix(),
      };
      let rawData = await getThingsData(
        dataPacketRaw,
        this.props.client_id,
        this.props.application_id,
      );
      if (rawData.status === "success") {
        let movingDgRawData = this.convertLatLng(rawData.data);
        this.setState(
          {
            latLngData: movingDgRawData.modifiedData,
            latLngDataWithTime: movingDgRawData.modifiedDataWithTime,
          },
          async () => {
            if (window.innerWidth > 576) {
              this.zoomOnPolyline();
            }
            await this.getAddress(this.state.latLngDataWithTime);
          },
        );
      }
    }
  }

  isGmmco() {
    if (parseInt(this.props.vendor_id) === 1062) {
      return true;
    } else {
      return false;
    }
  }

  //modify lat-lng data for map
  convertLatLng(data) {
    let modifiedData = [],
      modifiedDataWithTime = [];
    data.map((point) => {
      modifiedDataWithTime.push({
        lat: parseFloat(point.parameter_values?.lat),
        lng: parseFloat(point.parameter_values?.long),
        time: point.time,
      });
      modifiedData.push({
        lat: parseFloat(point.parameter_values?.lat),
        lng: parseFloat(point.parameter_values?.long),
      });
      return 1;
    });
    return {
      modifiedData: _uniqBy(modifiedData, (item) => item.lat || item.lng),
      modifiedDataWithTime: _uniqBy(
        modifiedDataWithTime,
        (item) => item.lat || item.lng,
      ),
    };
  }

  // split latLngDataWithAddress into necessary data for timeline
  splitLocationArray(points) {
    if (points.length <= 50) {
      return this.getPoints(points, 5);
    }
    return this.getPoints(points, 10);
  }

  getPoints(points, maxPoints) {
    const interval = Math.ceil(points.length / (maxPoints - 1));
    const newPoints = [];
    for (let i = 0; i < points.length - 1; i += interval) {
      newPoints.push(points[i]);
    }
    newPoints.push(points[points.length - 1]);
    return newPoints;
  }

  //reverse geocode all lat-lng points
  async getAddress(latLngData, socket = false) {
    let addressArray = [];
    if (socket && this.state.latLngDataWithAddress?.length) {
      addressArray = this.state.latLngDataWithAddress;
    }
    await Promise.all(
      this.splitLocationArray(latLngData).map(async (latlng) => {
        let address = await this.reverseGeocode(latlng);
        addressArray.push({
          lat: latlng.lat,
          lng: latlng.lng,
          address: address,
          time: latlng.time,
        });
        return 1;
      }),
    );
    this.setState({
      latLngDataWithAddress: socket
        ? addressArray
        : _orderBy(addressArray, ["time"], ["asc"]),
    });
  }

  // get address from lat-lng
  async reverseGeocode(latlng) {
    let address = "";
    const geocoder = new window.google.maps.Geocoder();
    try {
      const response = await geocoder.geocode({
        location: latlng,
      });
      address = response?.results?.[0]?.formatted_address;
    } catch (err) {}
    return address;
  }

  // calculate the distance travelled by the moving DG
  calculateDistance(latLngData = this.state.latLngData) {
    let lengthInMeters = "";
    if (latLngData?.length > 1) {
      lengthInMeters =
        window.google.maps.geometry.spherical.computeLength(latLngData);
    }
    return lengthInMeters
      ? (parseFloat(lengthInMeters) / 1000).toFixed(2) + " Km"
      : "NA";
  }
  // calculate the duration travelled by the moving DG
  calculateDuration(
    moving_start_time = parseInt(this.state.moving_start_time),
    moving_stop_time = this.state.latLngDataWithTime?.length > 1
      ? this.state.latLngDataWithTime[this.state.latLngDataWithTime.length - 1]
          .time
      : "",
  ) {
    let duration = "";
    if (moving_start_time && moving_stop_time) {
      let diffInMiliSecs = moment(moving_stop_time * 1000).diff(
        moving_start_time * 1000,
      );
      if (diffInMiliSecs) {
        duration =
          this.convertMiliSecsToHhMm(diffInMiliSecs) +
          (this.isGmmco() ? " SMU" : " Hrs");
      }
    }
    return duration || "NA";
  }
  // calculate current speed of the moving DG
  calculateSpeed(latLngDataWithTime = this.state.latLngDataWithTime) {
    let modifiedDataArray =
      latLngDataWithTime?.length > 1
        ? latLngDataWithTime.slice(latLngDataWithTime.length - 2)
        : [];
    let speed = "";
    if (modifiedDataArray?.length > 1) {
      let distanceArray = modifiedDataArray.map((i) => {
        return {
          lat: i.lat,
          lng: i.lng,
        };
      });
      let duration = this.calculateTimeDiff(
        modifiedDataArray[0].time,
        modifiedDataArray[1].time,
      );
      let distance = parseFloat(this.calculateDistance(distanceArray));
      if (distance > 0 && duration > 0) {
        speed =
          (parseFloat(distance) / parseFloat(duration)).toFixed(2) + " Kmph";
      }
    }
    return speed || "NA";
  }

  // calculate time diff in hour
  calculateTimeDiff(moving_start_time, moving_end_time) {
    let diffInMiliSecs = moment(moving_end_time * 1000).diff(
        moving_start_time * 1000,
      ),
      duration = "";
    if (diffInMiliSecs) {
      duration = diffInMiliSecs / 1000 / 60 / 60;
    }
    return duration;
  }
  // convert miliseconds to hh:mm format, add leading zero if required
  convertMiliSecsToHhMm(miliSecs) {
    let hh = Math.floor(miliSecs / 1000 / 60 / 60);
    let mm = Math.floor(miliSecs / 1000 / 60) % 60;
    return (hh < 10 ? "0" : "") + hh + ":" + (mm < 10 ? "0" : "") + mm;
  }

  async getEvents() {
    let url_string = encodeURI(
      "?get_details=true&generated_after=" +
        this.state.fromTime +
        "&generated_before=" +
        this.state.uptoTime,
    );
    if (this.state.listId) {
      url_string =
        url_string + "&entity_type=thing&entity_id=" + this.state.listId;
    }
    let data = {
      client_id: this.props.client_id,
      application_id: this.props.application_id,
    };
    const eventResponse = await retriveEventsData(data, url_string);
    this.setState({
      selectedTab: _find(eventResponse.response.event_types, { id: 1 }) ? 1 : 2,
      events: eventResponse.response.events,
      event_types: eventResponse.response.event_types,
    });
    this.getActivityData();
  }

  async getfaultApi(download) {
    let entityId = parseInt(this.state.listId);
    let dataToBeSent = {};
    const { totalData } = this.state;
    const { client_id, application_id } = this.props;
    let findThing = _find(totalData.things, { id: parseInt(entityId) });
    let data = {
      client_id: client_id,
      application_id: application_id,
    };
    dataToBeSent["thing_id"] = entityId;
    dataToBeSent["client_id"] = data.client_id;
    dataToBeSent["application_id"] = data.application_id;
    let url_string = encodeURI(
      "?from_time=" +
        this.state.faultFromTime +
        "&upto_time=" +
        this.state.faultUptoTime +
        "&results_per_page=1",
    );
    let faultResponse = await retriveFaultsData(dataToBeSent, url_string);
    if (faultResponse.response.status === "success") {
      this.setState({
        faultResponse: faultResponse.response.data,
      });
    }
  }

  getFaultName = (fault) => {
    const lastAtIndex = fault.lastIndexOf("at");
    if (lastAtIndex !== -1) {
      return fault.substring(0, lastAtIndex).trim();
    }
    return fault;
  };

  getActivityData() {
    let eventsArray = [],
      selectedId = [];
    if (this.state.listId) {
      selectedId = [];
      selectedId.push(this.state.listId);
    } else {
      selectedId = this.state.modifiedResponse.all_thing_ids;
    }
    let filteredEvent;
    if (selectedId && selectedId.length) {
      selectedId.map((selectedIdData) => {
        if (this.state.events && this.state.events.length) {
          let fuelTankThing = false;
          if (findFuelTankThing(this.state.fuel_tank_things, selectedIdData)) {
            fuelTankThing = true;
          }
          if (fuelTankThing) {
            filteredEvent = _filter(this.state.events, function (o) {
              return o.entity_id === selectedIdData && o.tags.includes("Fuel");
            });
          } else {
            filteredEvent = _filter(this.state.events, {
              entity_id: selectedIdData,
            });
          }
          if (filteredEvent && filteredEvent.length) {
            for (var i = 0; i < 500; i++) {
              if (filteredEvent[i]) {
                eventsArray.push({
                  name: _find(this.state.totalData.things, {
                    id: parseInt(filteredEvent[i].entity_id),
                  })
                    ? _find(this.state.totalData.things, {
                        id: parseInt(filteredEvent[i].entity_id),
                      }).name
                    : "",
                  event:
                    filteredEvent[i].tags?.includes("Fault") &&
                    filteredEvent[i]?.message
                      ? this.getFaultName(filteredEvent[i].message)
                      : filteredEvent[i].tags?.includes("Fault")
                        ? filteredEvent[i]?.details?.view_name
                        : filteredEvent[i].details &&
                            filteredEvent[i].details.rule_template_name
                          ? filteredEvent[i].details.rule_template_name
                          : "NA",
                  type: filteredEvent[i].type,
                  tags: filteredEvent[i].tags,
                  unixDate: filteredEvent[i].generated_at,
                  date: TimeFormatter(
                    this.props.user_preferences?.time_format,
                    filteredEvent[i].generated_at,
                    "DD MMM YYYY, HH:mm",
                  ),
                });
              }
            }
          }
        }
        return eventsArray;
      });
    }
    let actiVityConfig = JSON.parse(JSON.stringify(this.state.ActivityObject));
    let sortedEvent = _orderBy(eventsArray, ["unixDate"], ["desc"]);
    actiVityConfig.activity_violance_data = sortedEvent;
    actiVityConfig.event_types_array = this.state.event_types;
    return actiVityConfig;
  }

  offlineTimeOutFunction(payload = undefined, lastdata, response, totalData) {
    let latestDataArray = [],
      latestData = {},
      onOffStatus = {};
    response.all_thing_ids.map((things) => {
      let sortedThingsdata = _sortBy(lastdata, ["time"]);
      let filteredLatestData = _filter(sortedThingsdata, {
        thing_id: things,
      });
      if (!latestData[things]) {
        latestData[things] = filteredLatestData[filteredLatestData.length - 1];
      }
      if (!onOffStatus[things]) {
        onOffStatus[things] = latestData[things].data["mc_st"];
      }
      let findThings = _find(response.things_list, { id: things });
      const findThingList = _find(totalData.things, { id: things });
      if (findThings) {
        let timeout = findThings.offline_timeout
          ? findThings.offline_timeout * 1000
          : 900 * 1000;
        let timeInterval = (moment().unix() - latestData[things].time) * 1000;
        if (payload === undefined && timeInterval < timeout) {
          this.flagForTimeout[things] = true;
          this.timer = setTimeout(() => {
            this.changeToOffline(things, lastdata);
          }, timeout - timeInterval);
        } else if (payload === undefined && timeInterval > timeout) {
          this.flagForTimeout[things] = false;
        } else if (payload) {
          if (timeInterval < timeout) {
            this.flagForTimeout[things] = true;
            clearTimeout(this.timer);
            this.timer = setTimeout(() => {
              this.changeToOffline(things, lastdata);
            }, timeout - timeInterval);
          } else if (timeInterval > timeout) {
            this.flagForTimeout[things] = false;
          }
        }
        if (this.flagForTimeout[things] === true) {
          onOffStatus[things] = parseInt(latestData[things].data["mc_st"])
            ? parseInt(latestData[things].data["mc_st"]).toString()
            : "";
        } else {
          onOffStatus[things] = "2";
        }
        if (findThings.category === 71 && !("mc_st" in latestData[things].data)) {
          onOffStatus[things] = "1";
        }
        latestDataArray.push(latestData[things]);
        let findindex = _findIndex(latestDataArray, {
          thing_id: things,
        });
        if (findindex > -1) {
          latestDataArray[findindex]["OnOffStatus"] = onOffStatus[things];
          latestDataArray[findindex]["deviceStatus"] =
            findThingList?.devices?.[0]?.online_status === 1
              ? "online"
              : "offline";
        }
      }
    });
    return latestDataArray;
  }

  changeToOffline(thing_ids, latestdata) {
    let findindex = _findIndex(latestdata, { thing_id: thing_ids });
    if (findindex > -1) {
      latestdata[findindex]["OnOffStatus"] = "2";
    }
    this.setState(
      {
        latestParameterData: latestdata,
      },
      () => {
        this.latestDataFunc();
      },
    );
  }

  filterCardFuncTion() {
    let cardObject = JSON.parse(JSON.stringify(this.state.CardViewConfig));
    let noOfAllDgs = 0,
      noOfRunningDgs = 0,
      noOfSwitchOffDgs = 0,
      noOfOfflineDgs = 0;
    noOfAllDgs += this.state.modifiedResponse.all_thing_ids.length;
    noOfRunningDgs = this.state.onlineDgs.length;
    noOfSwitchOffDgs = this.state.switchOffDgs.length;
    noOfOfflineDgs = this.state.offlineDgs.length;
    cardObject.data.map((cardObjectParam) => {
      if (cardObjectParam.key === "all_things") {
        cardObjectParam.value = noOfAllDgs;
      } else if (cardObjectParam.key === "running") {
        cardObjectParam.value = noOfRunningDgs;
      } else if (cardObjectParam.key === "switch_off") {
        cardObjectParam.value = noOfSwitchOffDgs;
      } else if (cardObjectParam.key === "offline") {
        cardObjectParam.value = noOfOfflineDgs;
      }
      return cardObjectParam;
    });
    if (this.props.location.search.includes("category:71")) {
      cardObject.data = cardObject.data.filter(
        (o) => o.key !== "running" && o.key !== "switch_off",
      );
    }
    return cardObject;
  }

  // lat/lng not equals -1
  notEqualsMinusOne(lat, lng) {
    return lat > -90 && lat < 90 && lng > -180 && lng < 180;
  }

  findMapIcons(filteredListData, findParamList) {
    let mapIcon = "",
      componentToShow;
    if (parseInt(filteredListData.category) === 71) {
      mapIcon =
        this.checkOfflineOnlineStatus(filteredListData.id) === "1"
          ? FuelOnIcon
          : FuelOffIcon;
    } else if (this.isGmmco()) {
      if (findParamList.OnOffStatus === "1") {
        mapIcon = GmmcoMapSwitchOn;
      } else if (findParamList.OnOffStatus === "2") {
        mapIcon = GmmcoMapOffline;
      } else {
        mapIcon = GmmcoMapSwitchOff;
      }
    } else if (parseInt(this.props.vendor_id) === 1140) {
      if (findParamList.OnOffStatus === "1") {
        mapIcon = MahindraSwitchOn;
      } else if (findParamList.OnOffStatus === "2") {
        mapIcon = MahindraOffline;
      } else {
        mapIcon = MahindraSwitchOff;
      }
    } else {
      const statusMappings = {
        1: {
          both: DGMapOnlineBoth,
          trip: DGMapOnlineTrip,
          warning: DGMapOnlineWarning,
          not_defined: DGMapOnlineNotDefined,
          default: DGMapOnline,
        },
        2: {
          both: DGMapNotConnectedBoth,
          trip: DGMapNotConnectedTrip,
          warning: DGMapNotConnectedWarning,
          not_defined: DGMapNotConnectedNotDefined,
          default: DGMapNotConnected,
        },
        default: {
          both: DGMapSwitchOffBoth,
          trip: DGMapSwitchOffTrip,
          warning: DGMapSwitchOffWarning,
          not_defined: DGMapSwitchOffNotDefined,
          default: DGMapSwitchOff,
        },
      };
      const filteredListDataCopy = JSON.parse(JSON.stringify(filteredListData));
      const faultStatus = totalActiveFaultsWithWarnTripStatus(
        findParamList,
        this.props.vendor_id,
        this.state.totalData,
      )?.fault_status;

      filteredListDataCopy["fault_status"] = faultStatus;
      mapIcon =
        findParamList.deviceStatus === "offline"
          ? DGMapOffline
          : statusMappings?.[findParamList.OnOffStatus]?.[
              filteredListDataCopy?.fault_status
            ] ||
            statusMappings?.[findParamList.OnOffStatus]?.default ||
            statusMappings?.default?.[filteredListDataCopy?.fault_status] ||
            statusMappings?.default?.default;
    }
    return mapIcon;
  }

  mapFunction() {
    let mapConfigData = JSON.parse(JSON.stringify(this.state.MapConfig));
    let mapPointers = [],
      mapLegend = [];
    if (this.state.latestRcvd && this.state.latestRcvd.length) {
      this.state.latestRcvd.map((latestParam) => {
        let filteredList = _filter(this.state.totalData.things, {
          id: latestParam,
        });
        let findParamList = _find(this.state.latestParameterData, {
          thing_id: latestParam,
        });
        const faultStatus = totalActiveFaultsWithWarnTripStatus(
          findParamList,
          this.props.vendor_id,
          this.state.totalData,
        )?.fault_status;
        return filteredList.map((filteredListData) => {
          return mapPointers.push({
            lat:
              parseFloat(findParamList?.data?.["lat"]) &&
              parseFloat(findParamList?.data?.["long"]) &&
              this.notEqualsMinusOne(
                parseFloat(findParamList?.data?.["lat"]),
                parseFloat(findParamList?.data?.["long"]),
              )
                ? parseFloat(findParamList?.data?.["lat"])
                : filteredListData.latitude,
            lng:
              //findParamList?.data?.['is_moving'] &&
              parseFloat(findParamList?.data?.["long"]) &&
              parseFloat(findParamList?.data?.["lat"]) &&
              this.notEqualsMinusOne(
                parseFloat(findParamList?.data?.["lat"]),
                parseFloat(findParamList?.data?.["long"]),
              )
                ? parseFloat(findParamList?.data?.["long"])
                : filteredListData.longitude,
            hoverText: `Asset name: ${filteredListData.name}\nDevice status: ${findParamList.deviceStatus}\n${
              findParamList.deviceStatus === "online"
                ? [18, 96].includes(filteredListData.category)
                  ? findParamList.OnOffStatus === "1"
                    ? "Asset status: Running"
                    : findParamList.OnOffStatus === "2"
                      ? "Asset status: Disconnected"
                      : "Asset status: Stopped"
                  : findParamList.OnOffStatus === "1"
                    ? ""
                    : "Asset status: Disconnected"
                : ""
            }${
              findParamList.deviceStatus === "online" && faultStatus?.length
                ? `\nFault type: ${faultStatus === "not_defined" ? "Not defined" : faultStatus.charAt(0).toUpperCase() + faultStatus.slice(1) + (faultStatus === "both" ? " (Trip & Warning)" : "")}`
                : ""
            }
            `,
            id: filteredListData.id,
            kva:
              filteredListData.thing_details &&
              !isNaN(parseFloat(filteredListData.thing_details.kva))
                ? "(" +
                  (parseFloat(filteredListData.thing_details.kva) + " KVA") +
                  ")"
                : "",
            is_moving: parseInt(findParamList?.data?.["is_moving"]),
            moving_start_time: findParamList?.data?.["moving_start_time"], //1646117119,
            isDgOffline: findParamList.OnOffStatus === "2",
            icon: this.findMapIcons(filteredListData, findParamList),
          });
          //}
        });
      });
    }
    if (this.props.location.search.includes("category:71")) {
      mapLegend = [
        {
          name: "Not Connected",
          color: "#858787",
        },
        {
          name: "Offline",
          color: "#8EAFE7",
        },
      ];
    } else {
      if (this.state.type === "all_things") {
        mapLegend = [
          {
            name: "Running",
            color: "#00b300",
          },
          {
            name: "Stopped",
            color: "#ff0000",
          },
          {
            name: "Not Connected",
            color: "#858787",
          },
          {
            name: "Offline",
            color: "#8EAFE7",
          },
        ];
      } else if (this.state.type === "running") {
        mapLegend = [
          {
            name: "Running",
            color: "#00b300",
          },
        ];
      } else if (this.state.type === "switch_off") {
        mapLegend = [
          {
            name: "Stopped",
            color: "#ff0000",
          },
          {
            name: "Moving",
            color: "#8EAFE7",
          },
        ];
      } else if (this.state.type === "offline") {
        mapLegend = [
          {
            name: "Not Connected",
            color: "#858787",
          },
        ];
      }
    }
    mapConfigData.map_legend = mapLegend;
    mapConfigData.map_pointers = mapPointers;
    return mapConfigData;
  }

  //create bounds for polyline
  createBoundsForPolyline() {
    let bounds = new window.google.maps.LatLngBounds();
    this.state.latLngData.map((points) => {
      return bounds.extend(points);
    });
    return bounds;
  }

  //zoom on the polyline bound
  zoomOnPolyline() {
    let mapDiv = document.getElementById("map_id");
    let mapDim = {
      height: mapDiv.clientHeight,
      width: mapDiv.clientWidth,
    };
    let bounds = this.createBoundsForPolyline();
    let zoomLevel = bounds ? this.getBoundsZoomLevel(bounds, mapDim) : 0;
    let centerValue = bounds
      ? bounds.getCenter()
      : new window.google.maps.LatLng(0, 0);
    this.setState({
      zoomLevel: zoomLevel,
      centerValue: centerValue,
    });
  }

  getBoundsZoomLevel(bounds, mapDim) {
    let WORLD_DIM = { height: 256, width: 256 };
    let ZOOM_MAX = 21;

    function latRad(lat) {
      let sin = Math.sin((lat * Math.PI) / 180);
      let radX2 = Math.log((1 + sin) / (1 - sin)) / 2;
      return Math.max(Math.min(radX2, Math.PI), -Math.PI) / 2;
    }

    function zoom(mapPx, worldPx, fraction) {
      return Math.floor(Math.log(mapPx / worldPx / fraction) / Math.LN2);
    }

    let ne = bounds.getNorthEast();
    let sw = bounds.getSouthWest();

    let latFraction = (latRad(ne.lat()) - latRad(sw.lat())) / Math.PI;

    let lngDiff = ne.lng() - sw.lng();
    let lngFraction = (lngDiff < 0 ? lngDiff + 360 : lngDiff) / 360;

    let latZoom = zoom(mapDim.height, WORLD_DIM.height, latFraction);
    let lngZoom = zoom(mapDim.width, WORLD_DIM.width, lngFraction);

    return Math.min(latZoom, lngZoom, ZOOM_MAX);
  }

  mapOnClick(
    hoverText,
    lastData,
    modifiedResponse,
    id,
    is_moving,
    moving_start_time,
    isDgOffline,
  ) {
    this.setState(
      {
        listId: id,
        fromTime: moment().subtract(30, "days").startOf("day").unix(),
        uptoTime: moment().endOf("day").unix(),
        panelView: true,
        listLoading: true,
        trendNotificationLoading: true,
        moving_start_time,
      },
      async () => {
        let rawData = undefined,
          fuelTnkThinkData = undefined;
        if (is_moving && !isDgOffline) {
          rawData = this.getThingsRawData();
        }
        if (findFuelTankThing(this.state.fuel_tank_things, this.state.listId)) {
          fuelTnkThinkData = this.thingsDataFunction(true);
        }
        await Promise.all([
          rawData,
          this.getThingDailyAggrData(),
          this.getServiceAlert(),
          fuelTnkThinkData,
          this.getEvents(this.listApiData),
          this.getfaultApi(),
          this.fetchMissionList(this.state.listId),
        ]).then(() => {
          this.setState({
            listLoading: false,
            trendNotificationLoading: false,
          });
        });
        this.graphData();
        this.getActivityData();
        this.getPanelData(this.state.listId);
      },
    );
  }

  listViewFunction() {
    let listViewData = JSON.parse(JSON.stringify(this.state.ListViewConfig));
    let listViewArray = [];
    if (this.state.latestRcvd && this.state.latestRcvd.length) {
      this.state.latestRcvd.map((latestParamVal) => {
        if (latestParamVal) {
          let findParamList = _find(this.state.latestParameterData, {
            thing_id: latestParamVal,
          });
          let findThings = _find(this.state.totalData.things, {
            id: latestParamVal,
          });
          let fuelTankThing = false;
          if (findFuelTankThing(this.state.fuel_tank_things, latestParamVal)) {
            fuelTankThing = true;
          }
          listViewArray.push({
            id: findThings.id,
            key: latestParamVal,
            time: findParamList.time,
            icon: findThings.transparent_category_icon,
            category: findThings.category,
            categoryName: _find(this.state.totalData.things_categories, {
              id: findThings.category,
            })?.name,
            name: findThings.name,
            kva:
              !fuelTankThing &&
              findThings.thing_details &&
              !isNaN(parseFloat(findThings.thing_details.kva))
                ? "(" +
                  (parseFloat(findThings.thing_details.kva) + " KVA") +
                  ")"
                : "",
            address: findThings.address,
            deviceStatus:
              findThings?.devices?.[0]?.online_status === 1
                ? "online"
                : "offline",
            dgStatus: fuelTankThing
              ? this.checkOfflineOnlineStatus(findThings.id)
              : findParamList.OnOffStatus,
            commandStatus: _find(this.state.totalData.things, {
              id: latestParamVal,
            }).commands,
            fuelTankThing: fuelTankThing,
            dg_lock_status: findParamList.data.dg_lock_status || "0",
            operation_mode:
              findThings && findThings.thing_details
                ? findThings.thing_details.operation_mode
                : undefined,
            isLockControlEnabled:
              findThings &&
              findThings.thing_details &&
              findThings.thing_details.is_lock_unlock_control_enabled ===
                "enable"
                ? true
                : false,
            isThingMechanicalDG: this.isThingMechanicalDG(latestParamVal),
            isControlEnabled:
              findThings &&
              findThings.thing_details &&
              findThings.thing_details.is_start_stop_control_enabled ===
                "enable"
                ? true
                : false,
            is_moving: parseInt(findParamList?.data?.["is_moving"]) || false,
            moving_start_time: findParamList?.data?.["moving_start_time"],
            //1646117119,
            isDgOffline: findParamList.OnOffStatus === "2",
            faultStatus: totalActiveFaultsWithWarnTripStatus(
              findParamList,
              this.props.vendor_id,
              this.state.totalData,
            )?.fault_status,
          });
        }
        return listViewArray;
      });
    }
    let sortListByName = _sortBy(listViewArray, ["value"]);
    listViewData.data = sortListByName;
    return listViewData;
  }

  async fetchMissionList(listId) {
    let findThing = _find(this.state.totalData.things, {
      id: parseInt(listId),
    });
    let data = {
      OrderBy: "desc",
      client_id: this.props.client_id,
      application_id: this.props.application_id,
      StartAfter: moment.unix(this.state.StartAfter).toISOString(),
      StartBefore: moment.unix(this.state.StartBefore).toISOString(),
      ResultsPerPage: 50,
      task_type: 1,
      thing_list: [listId],
      GetDetails: true,
      get_counts: false,
      get_summary: false,
      get_trends: false,
    };
    let missionList = await retriveTasksData(data);
    this.setState({
      missionList: missionList,
    });
    this.getPanelData(listId);
  }

  async thingsDataFunction(selectedThing) {
    const things = selectedThing
      ? [this.state.listId]
      : this.state.fuel_tank_things;
    let dataPacketRaw = {
      data_type: "raw",
      aggregation_period: 0,
      parameters: ["fuel", "fuel_litre"],
      parameter_attributes: [],
      things: things,
      from_time: this.state.fuel_trend_from_time,
      upto_time: this.state.fuel_trend_upto_time,
    };
    let dataPacketAggr = {
      data_type: "aggregate",
      aggregation_period:
        this.state.fuel_trend_from_time ===
        moment().subtract(7, "days").startOf("day").unix()
          ? 86400
          : 3600,
      parameters: ["fuel_consumption", "fuel_filled"],
      parameter_attributes: ["sum"],
      things: things,
      from_time: this.state.fuel_trend_from_time,
      upto_time: this.state.fuel_trend_upto_time,
    };
    let rawData = await getThingsData(
      dataPacketRaw,
      this.props.client_id,
      this.props.application_id,
    );
    let aggrData = await getThingsData(
      dataPacketAggr,
      this.props.client_id,
      this.props.application_id,
    );
    this.setState(
      {
        rawData: rawData.data,
        aggrData: aggrData.data,
        fuel_trend_loading: false,
      },
      () => {
        this.graphDataFunc();
      },
    );
  }

  graphDataFunc() {
    let thingParameterDataHourlyAggr = {},
      thingParameterDataRaw = {};
    thingParameterDataHourlyAggr = getAllparameterDataAggr(
      this.state.aggrData,
      this.state.fuel_tank_things,
      this.state.modifiedResponse.param_key_data,
      this.state.fuel_trend_from_time,
      this.state.fuel_trend_upto_time,
      this.state.fuel_trend_from_time ===
        moment().subtract(7, "days").startOf("day").unix()
        ? 86400
        : 3600,
      "sum",
    );
    thingParameterDataRaw = getSelectedparameterDataWithTimestamp(
      this.state.rawData,
      this.state.fuel_tank_things,
      this.state.modifiedResponse.param_key_data,
      this.state.fuel_trend_from_time,
      this.state.fuel_trend_upto_time,
    );
    this.setState({
      thingParameterDataHourlyAggr: thingParameterDataHourlyAggr,
      thingParameterDataRaw: thingParameterDataRaw,
    });
  }

  payloadDataFunc(payloadRawData, hourlyAggrDataPayload) {
    let thingParameterDataRaw = this.state.thingParameterDataRaw;
    let thingParameterDataHourlyAggr = this.state.thingParameterDataHourlyAggr;
    if (Array.isArray(this.state.modifiedResponse.param_key_data)) {
      this.state.modifiedResponse.param_key_data.map((paramData) => {
        if (
          payloadRawData &&
          Object.keys(payloadRawData).length &&
          payloadRawData.thing_id === parseInt(this.state.listId) &&
          payloadRawData.parameter_values[paramData] &&
          thingParameterDataRaw &&
          thingParameterDataRaw[this.state.listId]
        ) {
          thingParameterDataRaw[this.state.listId][paramData].push([
            payloadRawData.time * 1000,
            parseFloat(payloadRawData.parameter_values[paramData]),
          ]);
          thingParameterDataRaw[this.state.listId][paramData] =
            thingParameterDataRaw[this.state.listId][paramData].sort(
              function (a, b) {
                return a[0] - b[0];
              },
            );
        }
        if (
          hourlyAggrDataPayload &&
          Object.keys(hourlyAggrDataPayload).length &&
          hourlyAggrDataPayload.thing_id === parseInt(this.state.listId) &&
          hourlyAggrDataPayload.parameter_values[paramData] &&
          thingParameterDataHourlyAggr &&
          thingParameterDataHourlyAggr[this.state.listId]
        ) {
          let findIndexAggrDataForTime = _findIndex(
            thingParameterDataHourlyAggr[this.state.listId][paramData],
            function (o) {
              return (
                moment(o[0]).format("HH") ===
                moment.unix(hourlyAggrDataPayload.time).format("HH")
              );
            },
          );
          if (findIndexAggrDataForTime === -1) {
            thingParameterDataHourlyAggr[this.state.listId][paramData].push([
              hourlyAggrDataPayload.time * 1000,
              parseFloat(hourlyAggrDataPayload.parameter_values[paramData].sum),
            ]);
            thingParameterDataHourlyAggr[this.state.listId][paramData] =
              thingParameterDataHourlyAggr[this.state.listId][paramData].sort(
                function (a, b) {
                  return a[0] - b[0];
                },
              );
          } else {
            thingParameterDataHourlyAggr[this.state.listId][paramData][
              findIndexAggrDataForTime
            ] = [
              hourlyAggrDataPayload.time * 1000,
              parseFloat(hourlyAggrDataPayload.parameter_values[paramData].sum),
            ];
          }
        }
        return 1;
      });
    }

    let fuelTrend = {
      thingParameterDataRaw: thingParameterDataRaw,
      thingParameterDataHourlyAggr: thingParameterDataHourlyAggr,
    };
    return fuelTrend;
  }

  realTimeDataFunc(payload) {
    let rawDataRealTime = {},
      hourlyAggrData = {};
    let payloadDataFuel;
    if (payload && Object.keys(payload).length) {
      Object.keys(payload).map((payload_key) => {
        if (payload[payload_key].type === "raw") {
          let findThing = _find(
            this.state.modifiedResponse.latest_parameter_data,
            { thing_id: parseInt(payload_key) },
          );
          let latestData = findThing?.data;
          if (
            findThing &&
            findThing.data &&
            Object.keys(findThing.data).length
          ) {
            Object.keys(findThing.data).map((thingData) => {
              if (payload[payload_key].data[thingData] !== undefined) {
                latestData[thingData] = payload[payload_key].data[thingData];
              }
              return latestData;
            });
          }

          rawDataRealTime = {
            time: payload[payload_key].time,
            thing_id: parseInt(payload_key),
            parameter_values: payload[payload_key].data,
          };
        }
        if (payload[payload_key].type === "1_hr_avg") {
          hourlyAggrData = {
            time: payload[payload_key].time,
            thing_id: parseInt(payload_key),
            parameter_values: payload[payload_key].data,
          };
        }
        payloadDataFuel = this.payloadDataFunc(rawDataRealTime, hourlyAggrData);
      });
      this.setState({
        thingParameterDataRaw: payloadDataFuel.thingParameterDataRaw,
        thingParameterDataHourlyAggr:
          payloadDataFuel.thingParameterDataHourlyAggr,
      });
    }
  }

  realTimeDrawerFunc(payload) {
    let realtimeThingsData = this.state.latestParameterData;
    let latestParameterData;
    let dailyAggrData = this.state.responseData;
    if (payload && Object.keys(payload).length) {
      Object.keys(payload).map((payload_key) => {
        if (payload[payload_key].type === "raw") {
          let findIndex = _findIndex(realtimeThingsData, {
            thing_id: parseInt(payload_key),
          });
          if (findIndex > -1) {
            realtimeThingsData[findIndex]["time"] = payload[payload_key].time;
            realtimeThingsData[findIndex]["data"] = payload[payload_key].data;
          }
        } else if (
          payload[payload_key].type === "1_day_avg" &&
          payload[payload_key].time >= this.state.fromTime &&
          payload[payload_key].time <= this.state.uptoTime
        ) {
          let findIndexAggrDataForTime = _findIndex(
            dailyAggrData,
            function (o) {
              return (
                moment.unix(o.time).format("DD MM YYYY") ===
                  moment.unix(payload[payload_key].time).format("DD MM YYYY") &&
                o.thing_id === parseInt(payload_key)
              );
            },
          );
          if (findIndexAggrDataForTime === -1) {
            dailyAggrData.push({
              thing_id: parseInt(payload_key),
              time: payload[payload_key].time,
              parameter_values: payload[payload_key].data,
            });
          } else {
            dailyAggrData[findIndexAggrDataForTime] = {
              thing_id: parseInt(payload_key),
              time: payload[payload_key].time,
              parameter_values: payload[payload_key].data,
            };
          }
        }
        latestParameterData = this.offlineTimeOutFunction(
          payload[payload_key],
          realtimeThingsData,
          this.state.modifiedResponse,
          this.state.totalData,
        );
      });
    }
    this.setState(
      {
        latestParameterData: latestParameterData,
        responseData: dailyAggrData,
      },
      () => {
        this.latestDataFunc();
        this.graphData();
        if (this.state.listId) {
          this.getPanelData(this.state.listId);
        }
      },
    );
  }

  getPanelGraph() {
    const { totalData, listId } = this.state;
    const findThing = _find(totalData.things, { id: parseInt(listId) });
    const findFuelLitre = _find(findThing.parameters, { key: "fuel_litre" });
    let rawFuelGraph = JSON.parse(JSON.stringify(this.state.GraphObjectData));
    let rawFuelGraphArray = [];
    const fuelKey = findFuelLitre ? "fuel_litre" : "fuel";
    // const fuelKey = his.state.thingParameterDataRaw[this.state.listId]['fuel_litre'];
    if (
      this.state.thingParameterDataRaw &&
      this.state.thingParameterDataRaw[this.state.listId] &&
      Array.isArray(
        this.state.thingParameterDataRaw[this.state.listId][fuelKey],
      )
    ) {
      this.state.thingParameterDataRaw[this.state.listId][fuelKey].map(
        (thingParameterDataRaw_data) => {
          if (thingParameterDataRaw_data && thingParameterDataRaw_data.length) {
            return rawFuelGraphArray.push([
              thingParameterDataRaw_data[0],
              thingParameterDataRaw_data[1],
            ]);
          }
        },
      );
    }
    let rawFuelGraphData = [
      {
        name: "Fuel Level",
        data: rawFuelGraphArray,
        color: "#becadd",
      },
    ];
    rawFuelGraph.graph_data.series_data = rawFuelGraphData;
    rawFuelGraph.graph_data.config.chart.type = "area";
    rawFuelGraph.graph_data.config.chart.backgroundColor = "rgba(0,0,0,0)";
    rawFuelGraph.graph_data.config.xAxis.title = {
      text: "Fuel Level",
      style: {
        color: "black",
      },
    };

    let aggrFuelConsFuelFilled = JSON.parse(
      JSON.stringify(this.state.GraphObjectData),
    );
    let aggrFuelConsArray = [],
      aggrFuelFilledArray = [];
    if (
      this.state.thingParameterDataHourlyAggr &&
      this.state.thingParameterDataHourlyAggr[this.state.listId] &&
      Array.isArray(
        this.state.thingParameterDataHourlyAggr[this.state.listId][
          "fuel_consumption"
        ],
      )
    ) {
      this.state.thingParameterDataHourlyAggr[this.state.listId][
        "fuel_consumption"
      ].map((thingParameterDataHourlyAggr_data) => {
        if (
          thingParameterDataHourlyAggr_data &&
          thingParameterDataHourlyAggr_data.length
        ) {
          return aggrFuelConsArray.push([
            thingParameterDataHourlyAggr_data[0],
            thingParameterDataHourlyAggr_data[1],
          ]);
        }
      });
    }
    if (
      this.state.thingParameterDataHourlyAggr &&
      this.state.thingParameterDataHourlyAggr[this.state.listId] &&
      Array.isArray(
        this.state.thingParameterDataHourlyAggr[this.state.listId][
          "fuel_filled"
        ],
      )
    ) {
      this.state.thingParameterDataHourlyAggr[this.state.listId][
        "fuel_filled"
      ].map((thingParameterDataHourlyAggr_data) => {
        if (
          thingParameterDataHourlyAggr_data &&
          thingParameterDataHourlyAggr_data.length
        ) {
          return aggrFuelFilledArray.push([
            thingParameterDataHourlyAggr_data[0],
            thingParameterDataHourlyAggr_data[1],
          ]);
        }
      });
    }
    let aggrFuelConsFuelFilledData = [
      {
        name: "Fuel Dispensed",
        data: aggrFuelConsArray,
        color: "#f58740",
        type: "column",
      },
      {
        name: "Fuel Filled",
        data: aggrFuelFilledArray,
        color: "#becadd",
        type: "column",
      },
    ];
    aggrFuelConsFuelFilled.graph_data.config.xAxis.title = {
      text: "Fuel",
    };
    aggrFuelConsFuelFilled.graph_data.config.legend.enabled = true;
    aggrFuelConsFuelFilled.graph_data.series_data = aggrFuelConsFuelFilledData;
    return {
      rawFuelGraph: rawFuelGraph,
      aggrFuelConsFuelFilled: aggrFuelConsFuelFilled,
    };
  }

  dateChange(e) {
    let fromTime, uptoTime;
    if (e === "last_7_days") {
      fromTime = moment().subtract(7, "days").startOf("day").unix();
      uptoTime = moment().endOf("day").unix();
    } else if (e === "last_24_hr") {
      fromTime = moment().subtract(24, "hours").unix();
      uptoTime = moment().unix();
    } else if (e === "today") {
      fromTime = moment().startOf("day").unix();
      uptoTime = moment().endOf("day").unix();
    }
    this.setState(
      {
        fuel_trend_from_time: fromTime,
        fuel_trend_upto_time: uptoTime,
        fuel_trend_loading: true,
      },
      async () => {
        await this.thingsDataFunction();
      },
    );
  }

  getPanelData(listId) {
    if (listId) {
      let PanelConfigObject = JSON.parse(
        JSON.stringify(this.state.PanelConfig),
      );
      let findLastData = _find(this.state.latestParameterData, {
        thing_id: listId,
      });
      let fuelTankThing = false;
      if (findFuelTankThing(this.state.fuel_tank_things, listId)) {
        fuelTankThing = true;
      }
      if (findLastData) {
        let totalPanelData = [],
          parameterValuesObject = {},
          parameterList = {},
          tripDataObj = {},
          tripDataValue = {};
        if (!parameterValuesObject[findLastData.thing_id]) {
          parameterValuesObject[findLastData.thing_id] = [];
        }
        if (!parameterList[findLastData.thing_id]) {
          parameterList[findLastData.thing_id] = [];
        }
        if (!tripDataObj[findLastData.thing_id]) {
          tripDataObj[findLastData.thing_id] = [];
        }
        if (!tripDataValue[findLastData.thing_id]) {
          tripDataValue[findLastData.thing_id] = [];
        }
        let filteredArrayMisson =
          this.state.missionList &&
          this.state.missionList.response &&
          this.state.missionList.response.Missions
            ? this.state.missionList.response.Missions.filter((missions) =>
                missions.Devices.includes(findLastData.thing_id.toString()),
              )
            : [];
        let sortedFilterMissionArrayLocalTime = _orderBy(
          filteredArrayMisson,
          ["StartDate"],
          ["desc"],
        );
        let lastTripToBeDisplayed = _find(
          sortedFilterMissionArrayLocalTime,
          function (o) {
            return o.EndDate !== "NA";
          },
        );
        let findThings = _find(this.state.totalData.things, {
          id: findLastData.thing_id,
        });
        let fuelCapacity =
          findThings &&
          findThings.thing_details &&
          findThings.thing_details.capacity
            ? findThings.thing_details.capacity
            : "";
        let fuelLevel =
          findThings.category === 71 &&
          _find(findThings.parameters, { key: "fuel_litre" })
            ? (_find(findThings.parameters, { key: "fuel_litre" }).value *
                100) /
              fuelCapacity
            : findLastData.data.fuel_raw
              ? parseFloat(findLastData.data.fuel_raw)
              : findLastData.data.fuel
                ? parseFloat(findLastData.data.fuel)
                : _find(findThings.parameters, { key: "fuel_raw" })
                  ? _find(findThings.parameters, { key: "fuel_raw" }).value
                  : _find(findThings.parameters, { key: "fuel" })
                    ? _find(findThings.parameters, { key: "fuel" }).value
                    : "";
        fuelLevel = fuelLevel > 100 ? 100 : fuelLevel;
        let fuelLevelInLtr = parseFloat((fuelLevel * fuelCapacity) / 100);
        fuelLevelInLtr = getFuelLevelInLitres(listId, fuelLevelInLtr);
        let findCalculatedLifetimeRunhour = 0;
        let findRnHr = _find(findThings.parameters, {
          key: "rnhr",
        });
        let findCalcRnhr = _find(findThings.parameters, {
          key: "calculated_runhour",
        });
        if (
          findRnHr &&
          findRnHr.value &&
          findRnHr.value !== "" &&
          parseFloat(findRnHr.value) > 0
        ) {
          findCalculatedLifetimeRunhour = parseFloat(findRnHr.value) * 3600;
        } else if (
          findCalcRnhr &&
          findCalcRnhr.aggregated_value &&
          findCalcRnhr.aggregated_value.lifetime &&
          findCalcRnhr.aggregated_value.lifetime.sum
        ) {
          findCalculatedLifetimeRunhour =
            findCalcRnhr.aggregated_value.lifetime.sum;
        }
        let totalRnhr = 0,
          totalFuelCons = 0;
        if (
          this.state.missionList &&
          this.state.missionList.response &&
          this.state.missionList.response.Missions &&
          this.state.missionList.response.Missions.length
        ) {
          for (
            let ind = 0;
            ind < this.state.missionList.response.Missions.length;
            ind++
          ) {
            if (
              this.state.missionList.response.Missions[ind].Details &&
              this.state.missionList.response.Missions[ind].Details
                .aggregate_data &&
              this.state.missionList.response.Missions[ind].Details
                .aggregate_data.calculated_runhour &&
              this.state.missionList.response.Missions[ind].Details
                .aggregate_data.calculated_runhour.sum > 0 &&
              this.state.missionList.response.Missions[ind].Details &&
              this.state.missionList.response.Missions[ind].Details
                .aggregate_data &&
              this.state.missionList.response.Missions[ind].Details
                .aggregate_data.fuel_consumption &&
              this.state.missionList.response.Missions[ind].Details
                .aggregate_data.fuel_consumption.sum > 0
            ) {
              totalRnhr +=
                this.state.missionList.response.Missions[ind].Details
                  .aggregate_data.calculated_runhour.sum;
              totalFuelCons +=
                this.state.missionList.response.Missions[ind].Details
                  .aggregate_data.fuel_consumption.sum;
            }
            if (totalRnhr > 86400) {
              break;
            }
          }
        }
        let calculateEstimatedRunhour =
          findThings?.thing_details?.fuel_economy &&
          findLastData?.data?.fuel_litre
            ? findThings.thing_details.fuel_economy *
              findLastData.data.fuel_litre *
              3600
            : "NA";
        let estimatedRunhourValue = "NA";
        if (calculateEstimatedRunhour !== "NA") {
          let estimatedHour = Math.floor(calculateEstimatedRunhour / 3600);
          let estimatedMin = Math.floor(
            (calculateEstimatedRunhour % 3600) / 60,
          );
          estimatedRunhourValue =
            (estimatedHour < 10 ? "0" + estimatedHour : estimatedHour) +
            " : " +
            (estimatedMin < 10 ? "0" + estimatedMin : estimatedMin) +
            (this.isGmmco() ? " SMU" : " Hrs");
        }

        let fuelLevelLatestEvent = "NA",
          fuelLevelLatestEventTime = "NA";
        if (
          findThings &&
          findThings.latest_events &&
          findThings.latest_events.tags &&
          findThings.latest_events.tags["Fuel Filled"]
        ) {
          if (
            findThings.latest_events.tags["Fuel Filled"].data &&
            findThings.latest_events.tags["Fuel Filled"].data.fuel_filled
          ) {
            fuelLevelLatestEvent =
              parseFloat(
                findThings.latest_events.tags["Fuel Filled"].data.fuel_filled,
              ).toFixed(2) + " L";
          }
          if (findThings.latest_events.tags["Fuel Filled"].event_time) {
            fuelLevelLatestEventTime = TimeFormatter(
              this.props.user_preferences?.time_format,
              findThings.latest_events.tags["Fuel Filled"].event_time,
              "DD MMM YYYY, HH:mm",
            );
          }
        }
        let lifetimehour = Math.floor(findCalculatedLifetimeRunhour / 3600);
        let lifetimeMin = Math.floor(
          (findCalculatedLifetimeRunhour % 3600) / 60,
        );
        let lifeTimeRunHourValue =
          (lifetimehour < 10 ? "0" + lifetimehour : lifetimehour) +
          " : " +
          (lifetimeMin < 10 ? "0" + lifetimeMin : lifetimeMin) +
          (this.isGmmco() ? " SMU" : " Hrs");
        PanelConfigObject.data.parameterValues.map((params) => {
          let findParamDataWithKey = _find(
            this.state.modifiedResponse.param_data,
            {
              key: params.key,
            },
          );
          if (findParamDataWithKey) {
            parameterList[findLastData.thing_id].push({
              name: findParamDataWithKey.name,
              data: findLastData.data[params.key]
                ? parseFloat(findLastData.data[params.key]).toFixed(2) +
                  " " +
                  findParamDataWithKey.unit
                : "NA",
            });
          } else {
            parameterList[findLastData.thing_id].push({
              name: "NA",
              data: "NA",
            });
          }
          return parameterList;
        });

        parameterList[findLastData.thing_id].map(
          (parameterValuesData, parameterValuesInd) => {
            return parameterValuesObject[findLastData.thing_id].push({
              icon: PanelConfigObject.data.parameterValues[parameterValuesInd]
                .icon,
              value: parameterValuesData,
            });
          },
        );
        let triphour = 0,
          tripMin = 0,
          tripFuelCons = "NA",
          enrgValue = "NA",
          loadValue = "NA";
        if (
          lastTripToBeDisplayed &&
          lastTripToBeDisplayed.Details &&
          lastTripToBeDisplayed.Details.aggregate_data
        ) {
          let calculatedRnhr = lastTripToBeDisplayed.Details.aggregate_data
            .calculated_runhour
            ? lastTripToBeDisplayed.Details.aggregate_data.calculated_runhour
                .sum
            : 0;

          triphour = Math.floor(calculatedRnhr / 3600);
          tripMin = Math.floor((calculatedRnhr % 3600) / 60);
          let findLoad = _find(findThings.parameters, {
            key: "load_percentage",
          });
          if (findLoad && findLoad !== undefined) {
            if (
              lastTripToBeDisplayed.Details.aggregate_data.load_percentage &&
              !isNaN(
                lastTripToBeDisplayed.Details.aggregate_data.load_percentage
                  .avg,
              )
            ) {
              loadValue = parseFloat(
                lastTripToBeDisplayed.Details.aggregate_data.load_percentage
                  .avg,
              ).toFixed(2);
            } else {
              loadValue = "NA";
            }
          } else {
            loadValue = "NA";
          }
          let findCalculatedEnrg = _find(findThings.parameters, {
            key: "calculated_energy",
          });
          if (findCalculatedEnrg && findCalculatedEnrg !== undefined) {
            if (
              lastTripToBeDisplayed.Details.aggregate_data.calculated_energy &&
              !isNaN(
                lastTripToBeDisplayed.Details.aggregate_data.calculated_energy
                  .sum,
              )
            ) {
              enrgValue = parseFloat(
                lastTripToBeDisplayed.Details.aggregate_data.calculated_energy
                  .sum,
              ).toFixed(2);
            } else {
              enrgValue = "NA";
            }
          } else if (lastTripToBeDisplayed.Details.aggregate_data.enrg) {
            enrgValue = parseFloat(
              lastTripToBeDisplayed.Details.aggregate_data.enrg.sum,
            ).toFixed(2);
          } else {
            enrgValue = "NA";
          }
          if (lastTripToBeDisplayed.Details.aggregate_data.fuel_consumption) {
            tripFuelCons = parseFloat(
              lastTripToBeDisplayed.Details.aggregate_data.fuel_consumption.sum,
            ).toFixed(2);
          }
        }
        tripDataObj[findLastData.thing_id].push(
          (triphour < 10 ? "0" + triphour : triphour) +
            " : " +
            (tripMin < 10 ? "0" + tripMin : tripMin),
          tripFuelCons,
          enrgValue,
          loadValue,
        );
        let lastFaultData = getFault(findThings, this.state.faultResponse);

        let tripParamArray = [];
        if (
          plan_description &&
          plan_description.dashboard_view_section &&
          plan_description.dashboard_view_section.last_dg_run_parameters &&
          plan_description.dashboard_view_section.last_dg_run_parameters.length
        ) {
          plan_description.dashboard_view_section.last_dg_run_parameters.map(
            (keys) => {
              if (keys === "rnhr") {
                tripDataObj[findLastData.thing_id].push(
                  (triphour < 10 ? "0" + triphour : triphour) +
                    " : " +
                    (tripMin < 10 ? "0" + tripMin : tripMin),
                );
                tripParamArray.push("calculated_runhour");
              }
              if (keys === "fuel_consumption") {
                tripDataObj[findLastData.thing_id].push(tripFuelCons);
                tripParamArray.push("fuel_consumption");
              }
              if (keys === "enrg") {
                tripDataObj[findLastData.thing_id].push(enrgValue);
                tripParamArray.push("enrg");
              }
              if (keys === "load_percentage") {
                tripDataObj[findLastData.thing_id].push(loadValue);
                tripParamArray.push("load_percentage");
              }
            },
          );
        }
        // tripDataObj[findLastData.thing_id].push(
        // 	(triphour < 10 ? '0' + triphour : triphour) +
        // 		' : ' +
        // 		(tripMin < 10 ? '0' + tripMin : tripMin),
        // 	tripFuelCons,
        // 	enrgValue,
        // 	loadValue
        // );
        tripParamArray.map((tripData, tripInd) => {
          return tripDataValue[findLastData.thing_id].push({
            name: _find(this.state.modifiedResponse.param_data, {
              key: tripData,
            })
              ? _find(this.state.modifiedResponse.param_data, {
                  key: tripData,
                }).name
              : "",
            icon: _find(PanelConfigObject.data.lastTripDetails.paramDetails, {
              key: tripData,
            }).icon,
            value:
              tripDataObj[findLastData.thing_id][tripInd] +
              " " +
              (_find(this.state.modifiedResponse.param_data, {
                key: tripData,
              })
                ? tripData === "calculated_runhour"
                  ? this.isGmmco()
                    ? "SMU"
                    : "Hrs"
                  : _find(this.state.modifiedResponse.param_data, {
                      key: tripData,
                    }).unit
                : ""),
          });
        });
        totalPanelData.push({
          panelId: findLastData.thing_id,
          thingName:
            this.state.modifiedResponse.thing_name_list[findLastData.thing_id],
          kva:
            findThings.thing_details &&
            !isNaN(parseFloat(findThings.thing_details.kva).toFixed(2)) &&
            !fuelTankThing
              ? "(" + parseFloat(findThings.thing_details.kva) + " KVA)"
              : "",
          thingValue: {
            thing_name:
              this.state.modifiedResponse.thing_name_list[
                findLastData.thing_id
              ],
            date:
              findLastData.time === 0
                ? "No Data Received"
                : TimeFormatter(
                    this.props.user_preferences?.time_format,
                    findLastData.time,
                    "DD MMM YYYY, HH:mm",
                  ),
          },
          dgStatus: findLastData.OnOffStatus,
          is_moving:
            this.isGpsPathEnabled() && parseInt(findLastData.data?.is_moving),
          runningStatus:
            findLastData.OnOffStatus === "1"
              ? "Running"
              : findLastData.OnOffStatus === "2"
                ? "Not Connected"
                : "Stopped",
          commandStatus: _find(this.state.totalData.things, {
            id: findLastData.thing_id,
          }).commands,
          dg_lock_status:
            (findThings &&
              findThings.thing_details &&
              findThings.thing_details.dg_lock_status) ||
            "0",
          isControlEnabled:
            findThings &&
            findThings.thing_details &&
            findThings.thing_details.is_start_stop_control_enabled === "enable"
              ? true
              : false,
          tankData: {
            tank_level: fuelLevel,
            tank_capacity: fuelCapacity,
          },
          estimatedRunhourLastFuelFilledValue: fuelTankThing
            ? [
                {
                  key: "current_fuel_level",
                  name: "Current Fuel Level",
                  value: parseFloat(fuelLevelInLtr).toFixed(2) + " L",
                },
                {
                  key: "last_fuel_filled",
                  name: "Last Fuel Filled",
                  value: fuelLevelLatestEvent,
                },
              ]
            : plan_description && plan_description.estimated_runhour
              ? [
                  {
                    key: "estimated",
                    name: "Estimated Runhour",
                    value: estimatedRunhourValue,
                  },
                  {
                    key: "last_fuel_filled",
                    name: "Last Fuel Filled",
                    value: fuelLevelLatestEvent,
                  },
                ]
              : [
                  {
                    key: "last_fuel_filled",
                    name: "Last Fuel Filled",
                    value: fuelLevelLatestEvent,
                  },
                ],
          lastFuelFilledTime: fuelLevelLatestEventTime,
          lifetimeRunhourDataValue: !fuelTankThing
            ? [
                {
                  key: "lifetime_runhour",
                  name: lifeTimeRunHourValue,
                  value: "Lifetime Runhour",
                },
              ]
            : [],
          parameterValues: !fuelTankThing
            ? parameterValuesObject[findLastData.thing_id]
            : {},
          maintenance_on: this.getMaintenanceText(),
          last_fault_data: {
            name:
              lastFaultData && lastFaultData.length
                ? lastFaultData[0].name
                : "",
            time:
              lastFaultData && lastFaultData.length
                ? lastFaultData[0].date_time
                : "",
            duration:
              lastFaultData && lastFaultData.length
                ? lastFaultData[0].duration
                  ? "(" + lastFaultData[0].duration + ")"
                  : "(Ongoing)"
                : "",
          },
          lastTripDetails: !fuelTankThing
            ? plan_description.dashboard_view_section.last_dg_run
              ? {
                  heading: "Last DG Run Details",
                  date: lastTripToBeDisplayed
                    ? TimeFormatter(
                        this.props.user_preferences?.time_format,
                        moment(lastTripToBeDisplayed.StartDate).unix(),
                        "DD MMM YYYY, HH:mm",
                      ) +
                      " to " +
                      (TimeFormatter(
                        this.props.user_preferences?.time_format,
                        moment(lastTripToBeDisplayed.StartDate).unix(),
                        "DD MMM YYYY",
                      ) ===
                      TimeFormatter(
                        this.props.user_preferences?.time_format,
                        moment(lastTripToBeDisplayed.EndDate).unix(),
                        "DD MMM YYYY",
                      )
                        ? TimeFormatter(
                            this.props.user_preferences?.time_format,
                            moment(lastTripToBeDisplayed.EndDate).unix(),
                            "HH:mm",
                          )
                        : TimeFormatter(
                            this.props.user_preferences?.time_format,
                            moment(lastTripToBeDisplayed.EndDate).unix(),
                            "DD MMM YYYY, HH:mm",
                          ))
                    : "",
                  paramDetails: tripDataValue[findLastData.thing_id],
                }
              : {}
            : {},
          fuelTankThing: fuelTankThing,
          graphs: fuelTankThing ? this.getPanelGraph() : {},
          fuel_tank_status: this.checkOfflineOnlineStatus(listId),
          isThingMechanicalDG: this.isThingMechanicalDG(findLastData.thing_id),
        });
        PanelConfigObject.total_and_running_thing = [];
        PanelConfigObject.filter_data = [];
        PanelConfigObject.data = totalPanelData[0];
        return PanelConfigObject;
      }
    }
  }

  listOnClick(listId, is_moving, moving_start_time, isDgOffline) {
    this.setState(
      {
        trendNotificationLoading: true,
        panelView: true,
        listId: listId,
        fromTime: moment().subtract(30, "days").startOf("day").unix(),
        uptoTime: moment().endOf("day").unix(),
        listLoading: true,
        moving_start_time,
      },
      async () => {
        let rawData = undefined,
          fuelTnkThinkData = undefined;
        if (is_moving && !isDgOffline) {
          rawData = this.getThingsRawData();
        }
        if (findFuelTankThing(this.state.fuel_tank_things, this.state.listId)) {
          fuelTnkThinkData = this.thingsDataFunction(true);
        }
        await Promise.all([
          rawData,
          this.getThingDailyAggrData(),
          this.getServiceAlert(),
          fuelTnkThinkData,
          this.getEvents(this.listApiData),
          this.getfaultApi(),
          this.fetchMissionList(this.state.listId),
        ]).then(() => {
          this.setState({
            listLoading: false,
            trendNotificationLoading: false,
          });
        });
        this.graphData();
        this.getActivityData();
        this.getPanelData(this.state.listId);
      },
    );
  }

  onCloseClick() {
    this.setState(
      {
        panelView: false,
        listId: undefined,
        trendNotificationLoading: true,
        fromTime: moment().subtract(30, "days").startOf("day").unix(),
        uptoTime: moment().endOf("day").unix(),
        moving_start_time: undefined,
        latLngData: undefined,
        latLngDataWithTime: undefined,
        latLngDataWithAddress: undefined,
        zoomLevel: undefined,
        centerValue: undefined,
      },
      async () => {
        await Promise.all([
          this.getThingDailyAggrData(),
          this.getEvents(this.listApiData),
        ]).then(() => {
          this.setState({
            trendNotificationLoading: false,
          });
        });
        this.graphData();
        this.getActivityData();
      },
    );
  }

  graphData() {
    let fuelTankThing =
      findFuelTankThing(this.state.fuel_tank_things, this.state.listId) ||
      this.state.categoryId === 71;
    let selectedId = [];
    if (this.state.listId) {
      selectedId = [];
      selectedId.push(this.state.listId);
    } else {
      selectedId = this.state.modifiedResponse.all_thing_ids;
    }
    let headerText = "";
    headerText = "All Assets";
    if (this.state.listId) {
      headerText = _find(this.state.totalData.things, {
        id: this.state.listId,
      })
        ? _find(this.state.totalData.things, {
            id: this.state.listId,
          }).name
        : "";
    }
    let upperGraphs = [],
      lowerGraph = [];
    if (selectedId && selectedId.length) {
      let calculatedRunhourArray = [],
        calculatedEnrgArray = [],
        calculatedFuelConsArray = [],
        calculatedFuelConsperHrArray = [],
        calculatedLoadperFuelConsArray = [];
      let sortedResponseData = _sortBy(this.state.responseData, ["time"]);
      let parameterDataSum = getAllparameterDataAggr(
        sortedResponseData,
        selectedId,
        this.state.parameterKeys,
        this.state.fromTime,
        this.state.uptoTime,
        86400,
        "sum",
      );
      if (parameterDataSum && Object.keys(parameterDataSum).length) {
        Object.keys(parameterDataSum).map((parameterDataSumKey) => {
          if (parameterDataSum[parameterDataSumKey]) {
            if (
              parameterDataSum[parameterDataSumKey] &&
              parameterDataSum[parameterDataSumKey].calculated_runhour &&
              parameterDataSum[parameterDataSumKey].calculated_runhour.length
            ) {
              parameterDataSum[parameterDataSumKey].calculated_runhour.map(
                (runhourData) => {
                  return calculatedRunhourArray.push({
                    time: runhourData[0],
                    data: runhourData[1] / 3600,
                  });
                },
              );
            }
            if (
              parameterDataSum[parameterDataSumKey] &&
              parameterDataSum[parameterDataSumKey].fuel_consumption &&
              parameterDataSum[parameterDataSumKey].fuel_consumption.length
            ) {
              parameterDataSum[parameterDataSumKey] &&
                parameterDataSum[parameterDataSumKey].fuel_consumption.map(
                  (FuelConsData) => {
                    return calculatedFuelConsArray.push({
                      time: FuelConsData[0],
                      data: FuelConsData[1],
                    });
                  },
                );
            }
            let findThings = _find(this.state.totalData.things, {
              id: parseInt(parameterDataSumKey),
            });
            let findCalculatedEnrg = _find(findThings.parameters, {
              key: "calculated_energy",
            });

            let energyArrayForGraph = [];

            if (
              findCalculatedEnrg &&
              findCalculatedEnrg !== undefined &&
              parameterDataSum &&
              parameterDataSum[parameterDataSumKey] &&
              parameterDataSum[parameterDataSumKey].calculated_energy &&
              parameterDataSum[parameterDataSumKey].calculated_energy.length
            ) {
              energyArrayForGraph =
                parameterDataSum[parameterDataSumKey].calculated_energy;
            } else if (
              parameterDataSum &&
              parameterDataSum[parameterDataSumKey] &&
              parameterDataSum[parameterDataSumKey].enrg &&
              parameterDataSum[parameterDataSumKey].enrg.length
            ) {
              energyArrayForGraph = parameterDataSum[parameterDataSumKey].enrg;
            }

            if (energyArrayForGraph && energyArrayForGraph.length) {
              energyArrayForGraph.map((enrgData) => {
                return calculatedEnrgArray.push({
                  time: enrgData[0],
                  data: enrgData[1],
                });
              });
            }

            if (
              parameterDataSum[parameterDataSumKey] &&
              parameterDataSum[parameterDataSumKey].fuel_consumption &&
              parameterDataSum[parameterDataSumKey].fuel_consumption.length
            ) {
              parameterDataSum[parameterDataSumKey].fuel_consumption.map(
                (FuelConsData, FuelConsInd) => {
                  let dataToBePushed = null;
                  if (parameterDataSum[parameterDataSumKey]) {
                    let rnhrVvlue =
                      parameterDataSum[parameterDataSumKey]
                        .calculated_runhour &&
                      parameterDataSum[parameterDataSumKey].calculated_runhour
                        .length &&
                      parameterDataSum[parameterDataSumKey].calculated_runhour[
                        FuelConsInd
                      ]
                        ? parameterDataSum[parameterDataSumKey]
                            .calculated_runhour[FuelConsInd][1]
                        : 0;
                    if (
                      rnhrVvlue > 0 &&
                      !isNaN(FuelConsData[1] / (rnhrVvlue / 3600))
                    ) {
                      dataToBePushed = FuelConsData[1] / (rnhrVvlue / 3600);
                    } else {
                      dataToBePushed = null;
                    }
                  }
                  return calculatedFuelConsperHrArray.push({
                    time: FuelConsData[0],
                    data: dataToBePushed,
                  });
                },
              );
            }
            if (energyArrayForGraph && energyArrayForGraph.length) {
              energyArrayForGraph.map((engGen, engGenInd) => {
                return calculatedLoadperFuelConsArray.push({
                  time: engGen[0],
                  data:
                    parameterDataSum &&
                    parameterDataSum[parameterDataSumKey] &&
                    parameterDataSum[parameterDataSumKey].fuel_consumption &&
                    parameterDataSum[parameterDataSumKey].fuel_consumption[
                      engGenInd
                    ] &&
                    parameterDataSum[parameterDataSumKey].fuel_consumption[
                      engGenInd
                    ][1] > 0 &&
                    !isNaN(
                      engGen[1] /
                        parameterDataSum[parameterDataSumKey].fuel_consumption[
                          engGenInd
                        ][1],
                    )
                      ? engGen[1] /
                        parameterDataSum[parameterDataSumKey].fuel_consumption[
                          engGenInd
                        ][1]
                      : null,
                });
              });
            }
          }
          return 1;
        });
      }
      let runhourArrayofObj = Object.values(
        calculatedRunhourArray.reduce((a, { time, data }) => {
          a[time] = a[time] || { time, data: 0 };
          a[time].data = Number(a[time].data) + Number(data);
          return a;
        }, {}),
      );
      let fuelConsArrayofObj = Object.values(
        calculatedFuelConsArray.reduce((a, { time, data }) => {
          a[time] = a[time] || { time, data: 0 };
          a[time].data = Number(a[time].data) + Number(data);
          return a;
        }, {}),
      );
      let enrgArrayofObj = Object.values(
        calculatedEnrgArray.reduce((a, { time, data }) => {
          a[time] = a[time] || { time, data: 0 };
          a[time].data = Number(a[time].data) + Number(data);
          return a;
        }, {}),
      );
      let fuelPerHrObj = Object.values(
        calculatedFuelConsperHrArray.reduce((a, { time, data }) => {
          a[time] = a[time] || { time, data: 0 };
          a[time].data = Number(a[time].data) + Number(data);
          return a;
        }, {}),
      );
      let enrgPerFuelArrayofObj = Object.values(
        calculatedLoadperFuelConsArray.reduce((a, { time, data }) => {
          a[time] = a[time] || { time, data: 0 };
          a[time].data = Number(a[time].data) + Number(data);
          return a;
        }, {}),
      );
      let runhourArray = runhourArrayofObj.map((obj) => Object.values(obj));
      let fuelConsArray = fuelConsArrayofObj.map((obj) => Object.values(obj));
      let enrgArray = enrgArrayofObj.map((obj) => Object.values(obj));
      let fuelPerHrArray = fuelPerHrObj.map((obj) => Object.values(obj));
      let enrgPerFuelArray = enrgPerFuelArrayofObj.map((obj) =>
        Object.values(obj),
      );
      let graphData = JSON.parse(
        JSON.stringify(this.state.GraphWithCardConfig),
      );
      graphData.upper_graph_array.calculated_runhour = [
        {
          name: "Runhour",
          type: "column",
          data: runhourArray && runhourArray.length ? runhourArray : [],
          color: "#f19317",
        },
      ];
      graphData.upper_graph_array.fuel_consumption = [
        {
          name: fuelTankThing ? "Fuel Dispensed" : "Fuel Consumption",
          type: fuelTankThing ? "column" : "area",
          data: fuelConsArray && fuelConsArray.length ? fuelConsArray : [],
          color: "#f19317",
        },
      ];
      graphData.lower_graph_array.enrg = [
        {
          name: "Energy",
          type: "area",
          data: enrgArray && enrgArray.length ? enrgArray : [],
          color: "#f19317",
        },
      ];
      graphData.lower_graph_array.energy_generatiion_per_lit = [
        {
          name: "Energy generation per fuel consumed",
          type: "line",
          data:
            enrgPerFuelArray && enrgPerFuelArray.length ? enrgPerFuelArray : [],
          color: "#000",
        },
      ];
      graphData.lower_graph_array.fue_cons_per_lit = [
        {
          name: "Fuel consumed per hr",
          type: "line",
          data: fuelPerHrArray && fuelPerHrArray.length ? fuelPerHrArray : [],
          color: "#000",
        },
      ];
      let runHourGraphConfig = JSON.parse(
        JSON.stringify(this.state.GraphObjectData),
      );
      let fuelConsConfig = JSON.parse(
        JSON.stringify(this.state.GraphObjectData),
      );
      let enrgConfig = JSON.parse(JSON.stringify(this.state.GraphObjectData));
      let enrgPerFuelConfig = JSON.parse(
        JSON.stringify(this.state.GraphObjectData),
      );
      let fuelConsperLoad = JSON.parse(
        JSON.stringify(this.state.GraphObjectData),
      );
      runHourGraphConfig.graph_data.config.chart.marginBottom = 70;
      runHourGraphConfig.graph_data.config.title = {
        text: "Date",
        align: "center",
        verticalAlign: "bottom",
        floating: true,
        style: {
          color: "#7686A1",
          fontSize: "12px",
          fontWeight: "normal",
          textTransform: "none",
        },
      };
      runHourGraphConfig.graph_data.config.chart.height =
        window.innerWidth <= 1536 ? 104 : 170;
      fuelConsConfig.graph_data.config.chart.height = fuelTankThing
        ? window.innerWidth < 576
          ? 104
          : 170
        : window.innerWidth <= 1536
          ? 104
          : 170;
      fuelConsConfig.graph_data.config.chart.marginBottom = 70;
      fuelConsConfig.graph_data.config.title = {
        text: "Date",
        align: "center",
        verticalAlign: "bottom",
        floating: true,
        style: {
          color: "#7686A1",
          fontSize: "12px",
          fontWeight: "normal",
          textTransform: "none",
        },
      };
      enrgConfig.graph_data.config.chart.height =
        window.innerWidth <= 1536 ? 104 : 170;
      enrgConfig.graph_data.config.chart.marginBottom = 70;
      enrgConfig.graph_data.config.title = {
        text: "Date",
        align: "center",
        verticalAlign: "bottom",
        floating: true,
        style: {
          color: "#7686A1",
          fontSize: "12px",
          fontWeight: "normal",
          textTransform: "none",
        },
      };
      fuelConsperLoad.graph_data.config.chart.height =
        window.innerWidth <= 1536 ? 104 : 170;
      fuelConsperLoad.graph_data.config.chart.marginBottom = 70;
      fuelConsperLoad.graph_data.config.title = {
        text: "Date",
        align: "center",
        verticalAlign: "bottom",
        floating: true,
        style: {
          color: "#7686A1",
          fontSize: "12px",
          fontWeight: "normal",
          textTransform: "none",
        },
      };
      enrgPerFuelConfig.graph_data.config.chart.height =
        window.innerWidth <= 1536 ? 104 : 170;
      enrgPerFuelConfig.graph_data.config.chart.marginBottom = 70;
      enrgPerFuelConfig.graph_data.config.title = {
        text: "Date",
        align: "center",
        verticalAlign: "bottom",
        floating: true,
        style: {
          color: "#7686A1",
          fontSize: "12px",
          fontWeight: "normal",
          textTransform: "none",
        },
      };
      let enrgPerFuelArrayMax = 0,
        fuelPerHrArrayMax = 0;
      if (enrgPerFuelArray && enrgPerFuelArray.length) {
        enrgPerFuelArray.map((enrgPerFuelArrayData) => {
          enrgPerFuelArrayMax = parseFloat(
            Math.max(enrgPerFuelArrayData[1]),
          ).toFixed(1);
          return enrgPerFuelArrayMax;
        });
      }
      if (fuelPerHrArray && fuelPerHrArray.length) {
        fuelPerHrArray.map((fuelPerHrArrayData) => {
          fuelPerHrArrayMax = parseFloat(
            Math.max(fuelPerHrArrayData[1]),
          ).toFixed(1);
          return fuelPerHrArrayMax;
        });
      }
      runHourGraphConfig.graph_data.series_data =
        graphData.upper_graph_array.calculated_runhour;
      runHourGraphConfig.graph_data.config.yAxis.title.text = this.isGmmco()
        ? "SMU"
        : "Hr";
      let isGmmco = this.isGmmco();
      runHourGraphConfig.graph_data.config.tooltip = {
        formatter: function () {
          return (
            "<small>" +
            moment(this.x).format("dddd, MMM DD, YYYY") +
            "</small><br>" +
            '<div style="color: orange;">Runhour:</div><b>' +
            ((Math.floor((this.y * 3600) / 3600) < 10
              ? "0" + Math.floor((this.y * 3600) / 3600)
              : Math.floor((this.y * 3600) / 3600)) +
              ":" +
              (Math.floor(((this.y * 3600) % 3600) / 60) < 10
                ? "0" + Math.floor(((this.y * 3600) % 3600) / 60)
                : Math.floor(((this.y * 3600) % 3600) / 60))) +
            (isGmmco ? " SMU" : " Hrs") +
            "</b></br>"
          );
        },
      };
      fuelConsConfig.graph_data.series_data =
        graphData.upper_graph_array.fuel_consumption;
      enrgConfig.graph_data.series_data = graphData.lower_graph_array.enrg;
      enrgPerFuelConfig.graph_data.series_data =
        graphData.lower_graph_array.energy_generatiion_per_lit;
      fuelConsperLoad.graph_data.series_data =
        graphData.lower_graph_array.fue_cons_per_lit;
      runHourGraphConfig.graph_data.config.timezone =
        this.props.user_preferences.timezone;
      fuelConsConfig.graph_data.config.timezone =
        this.props.user_preferences.timezone;
      fuelConsConfig.graph_data.config.yAxis = {
        title: {
          text: "L",
        },
        labels: {
          formatter: function () {
            return [this.value.toFixed(2)];
          },
        },
      };
      enrgConfig.graph_data.config.timezone =
        this.props.user_preferences.timezone;
      enrgConfig.graph_data.config.yAxis = {
        title: {
          text: "kWh",
        },
        labels: {
          formatter: function () {
            return [this.value.toFixed(2)];
          },
        },
      };
      enrgPerFuelConfig.graph_data.config.timezone =
        this.props.user_preferences.timezone;
      enrgPerFuelConfig.graph_data.config.yAxis = {
        title: {
          text: "kWh/L",
        },
        labels: {
          formatter: function () {
            return [this.value.toFixed(2)];
          },
        },
      };
      fuelConsperLoad.graph_data.config.timezone =
        this.props.user_preferences.timezone;
      fuelConsperLoad.graph_data.config.yAxis = {
        title: {
          text: this.isGmmco() ? "L/SMU" : "L/Hr",
        },
        labels: {
          formatter: function () {
            return [this.value.toFixed(2)];
          },
        },
      };

      let d = Number(_sumBy(calculatedRunhourArray, "data") * 3600);
      let h = Math.floor(d / 3600);
      let m = Math.floor((d % 3600) / 60);
      graphData.graph_sum.calculated_runhour.value =
        (h < 10 ? "0" + h : h) +
        ":" +
        (m < 10 ? "0" + m : m) +
        (this.isGmmco() ? " SMU" : " Hrs");
      const sumFuelCons =
        calculatedFuelConsArray && calculatedFuelConsArray.length
          ? _sumBy(calculatedFuelConsArray, "data")
          : undefined;
      const sumEnrgGen =
        calculatedEnrgArray && calculatedEnrgArray.length
          ? _sumBy(calculatedEnrgArray, "data")
          : undefined;
      graphData.graph_sum.fuel_consumed.value = !isNaN(parseFloat(sumFuelCons))
        ? `${parseFloat(sumFuelCons).toFixed(2)} L`
        : "NA";
      graphData.graph_sum.enrg.value = !isNaN(parseFloat(sumEnrgGen))
        ? `${parseFloat(sumEnrgGen).toFixed(2)} kWh`
        : "NA";
      const numberSumFuelCons = !isNaN(parseFloat(sumFuelCons))
        ? parseFloat(sumFuelCons)
        : 0;
      const numberSumEnrgGen = !isNaN(parseFloat(sumEnrgGen))
        ? parseFloat(sumEnrgGen)
        : 0;
      graphData.graph_sum.energy_generatiion_per_lit.value =
        numberSumFuelCons !== 0
          ? (numberSumEnrgGen / numberSumFuelCons).toFixed(2) + " kWh/L"
          : "NA";
      graphData.graph_sum.fue_cons_per_lit.value =
        d !== null && d !== 0
          ? (
              _sumBy(calculatedFuelConsArray, "data").toFixed(2) /
              (d / 3600)
            ).toFixed(2) + (this.isGmmco() ? " L/SMU" : " L/Hr")
          : "NA";

      let runhourValue = graphData.graph_sum.calculated_runhour;
      let fuelConsValue = graphData.graph_sum.fuel_consumed;
      let enrgValue = graphData.graph_sum.enrg;
      let enrgPerFuelValue = graphData.graph_sum.energy_generatiion_per_lit;
      let fuelConsperLoadValue = graphData.graph_sum.fue_cons_per_lit;
      if (
        plan_description &&
        plan_description.dashboard_view_section &&
        plan_description.dashboard_view_section.dashboard_parameters
      ) {
        plan_description.dashboard_view_section.dashboard_parameters.map(
          (keys) => {
            if (keys === "rnhr" && !fuelTankThing) {
              if (this.isMechanicalDG() && !this.isOnlyFuelDG()) {
                upperGraphs.push({
                  graph_config: runHourGraphConfig,
                  param_value: runhourValue,
                });
              } else if (this.noFuelAtAll() && !this.isOnlyFuelDG()) {
                upperGraphs.push({
                  graph_config: runHourGraphConfig,
                  param_value: runhourValue,
                });
              } else if (this.isOnlyFuelDG()) {
                upperGraphs.push({
                  graph_config: runHourGraphConfig,
                  param_value: runhourValue,
                });
              } else {
                upperGraphs.push({
                  graph_config: runHourGraphConfig,
                  param_value: runhourValue,
                });
              }
            }
            if (keys === "fuel_consumption" && !this.noFuelAtAll()) {
              if (fuelTankThing) {
                upperGraphs.push({
                  graph_config: fuelConsConfig,
                  param_value: fuelConsValue,
                });
              } else if (this.isOnlyFuelDG()) {
                upperGraphs.push({
                  graph_config: fuelConsConfig,
                  param_value: fuelConsValue,
                });
              } else {
                if (window.innerWidth <= 1536) {
                  lowerGraph.push({
                    graph_config: fuelConsConfig,
                    param_value: fuelConsValue,
                  });
                } else {
                  upperGraphs.push({
                    graph_config: fuelConsConfig,
                    param_value: fuelConsValue,
                  });
                }
              }
            }
            if (
              keys === "enrg" &&
              !this.isOnlyFuelDG() &&
              !this.isMechanicalDG() &&
              !fuelTankThing
            ) {
              if (this.noFuelAtAll()) {
                upperGraphs.push({
                  graph_config: enrgConfig,
                  param_value: enrgValue,
                });
              } else {
                lowerGraph.push({
                  graph_config: enrgConfig,
                  param_value: enrgValue,
                });
              }
            }
            if (
              keys === "enrg_per_fuel_consumption" &&
              !this.isOnlyFuelDG() &&
              !this.isMechanicalDG() &&
              !fuelTankThing
            ) {
              lowerGraph.push({
                graph_config: enrgPerFuelConfig,
                param_value: enrgPerFuelValue,
              });
            }
            if (
              keys === "fuel_consumption_per_rnhr" &&
              !this.isOnlyFuelDG() &&
              !this.isMechanicalDG() &&
              !fuelTankThing
            ) {
              lowerGraph.push({
                graph_config: fuelConsperLoad,
                param_value: fuelConsperLoadValue,
              });
            }
          },
        );
      }
      // if (window.innerWidth <= 1536) {
      // 	upperGraphs = [];
      // 	lowerGraph = [];
      // 	upperGraphs.push({
      // 		graph_config: runHourGraphConfig,
      // 		param_value: runhourValue,
      // 	});
      // 	lowerGraph.push(
      // 		{
      // 			graph_config: fuelConsConfig,
      // 			param_value: fuelConsValue,
      // 		},
      // 		{
      // 			graph_config: enrgConfig,
      // 			param_value: enrgValue,
      // 		},
      // 		{
      // 			graph_config: enrgPerFuelConfig,
      // 			param_value: enrgPerFuelValue,
      // 		},
      // 		{
      // 			graph_config: fuelConsperLoad,
      // 			param_value: fuelConsperLoadValue,
      // 		}
      // 	);
      // }
    } else {
      upperGraphs = [];
      lowerGraph = [];
    }
    let totalGraphData = {
      upperGraphs: upperGraphs,
      lowerGraph: lowerGraph,
      headerText: headerText,
    };
    return totalGraphData;
  }

  kpiOnClick(key) {
    this.setState(
      {
        type: key,
        listId: this.state.listId ? this.state.listId : undefined,
      },
      async () => {
        // if (!this.props.dg_in_iot_mode) {
        //   this.props.history.push(
        //     getBaseUrl(this.props, "dashboard"),
        //   );
        // }
        this.latestDataFunc();
      },
    );
  }

  async componentDidMount() {
    window.addEventListener("resize", this.windowResize.bind(this));
    await this.getThingsList(this.listApiData);
    if (!this.props.dg_in_iot_mode) {
      if (this.state.modifiedResponse.all_thing_ids.length === 1) {
        if (this.props.location.pathname.includes("dashboard")) {
          // this.props.history.push(
          //   getBaseUrl(this.props, "dashboard"),
          // );
        } else {
          if (
            _find(this.state.totalData?.things, function (o) {
              return o.category !== 71;
            })
          ) {
            this.props.history.push(
              getBaseUrl(
                this.props,
                "detailed-view/?thing_id=" +
                  this.state.modifiedResponse.all_thing_ids[0],
              ),
            );
          } else {
            // this.props.history.push(
            //   getBaseUrl(this.props, "dashboard"),
            // );
          }
        }
      } else {
        // this.props.history.push(
        //   getBaseUrl(this.props, "dashboard"),
        // );
      }
    }
    this.socket = establishSocketConnection();
    this.socket.on("connect", () => {
      subscribeForThingsUpdates(
        this.socket,
        this.state.modifiedResponse.all_thing_ids,
        0,
      );
      subscribeForThingsUpdates(
        this.socket,
        this.state.modifiedResponse.all_thing_ids,
        86400,
      );
      subscribeForEventsUpdates(
        this.socket,
        this.listApiData.client_id,
        this.listApiData.application_id,
      );
      subscribeForEntityUpdates(
        this.socket,
        this.state.modifiedResponse.all_thing_ids,
        "thing",
      );
    });
    this.socket.on("new_event_generated", (payload) => {
      if (payload) {
        this.realTimeEventsUpdate(payload);
        let realtimeEventsData = this.state.events;
        realtimeEventsData.unshift({
          entity_id: parseInt(payload.entity_id),
          type: payload.type,
          generated_at: payload.generated_at,
          message: payload.message,
          tags: payload.tags,
          details: payload.details,
        });
        // let thingsStatus = this.state.things_status;
        // let findEventIndex = _findIndex(thingsStatus, {id: parseInt(payload.entity_id)})
        // if(findEventIndex > 1) {
        // 	if (payload.tags.includes('Offline')) {
        // 		findEventIndex[findEventIndex].status = 'offline';
        // 		if (payload.tags.includes('Online'))
        // 		findEventIndex[findEventIndex].status = 'online';
        // 	}
        // }
        this.setState(
          {
            events: realtimeEventsData,
          },
          () => this.getActivityData(this.state.events),
        );
      }
    });
    this.bucketTime = setInterval(() => {
      if (Object.keys(this.bucket.raw_data).length) {
        this.realTimeDataFunc(this.bucket.raw_data);
        this.realTimeDrawerFunc(this.bucket.raw_data);
      }
      this.bucket = {
        raw_data: {},
        "1_hr_avg": {},
        "1_day_avg": {},
      };
    }, 60000);
    this.socket.on("new_data_generated_for_station", async (payload) => {
      if (payload.type === "raw") {
        this.bucket["raw_data"][payload.thing_id] = payload;
      }
      if (payload.type === "1_hr_avg") {
        this.bucket["1_hr_avg"][payload.thing_id] = payload;
      }
      if (payload.type === "1_day_avg") {
        this.bucket["1_day_avg"][payload.thing_id] = payload;
      }
    });
    this.socket.on("details_updated", (payload) => {
      this.updateEntityDetails(payload);
    });
  }

  updateEntityDetails(payload) {
    if (
      payload.entity_type === "thing" &&
      !isNaN(parseInt(payload.entity_id)) &&
      payload.details
    ) {
      const { totalData } = this.state;
      if (totalData.things && totalData.things.length) {
        let totalDataResponse = JSON.parse(JSON.stringify(totalData));
        let thingDetailsIndex = _findIndex(totalDataResponse.things, {
          id: parseInt(payload.entity_id),
        });
        if (
          thingDetailsIndex > -1 &&
          totalDataResponse &&
          totalDataResponse.things
        ) {
          totalDataResponse.things[thingDetailsIndex] = _merge(
            {},
            totalDataResponse.things[thingDetailsIndex],
            payload.details,
          );
        }
        this.setState(
          {
            totalData: totalDataResponse,
          },
          () => {
            this.listViewFunction();
            if (this.state.listId) {
              this.getPanelData(this.state.listId);
            }
          },
        );
      }
    }
  }

  componentWillUnmount() {
    window.removeEventListener("resize", this.windowResize.bind(this));
    disconnectSocketConnection(this.socket);
  }

  handleChange(e) {
    this.setState(
      {
        dateSelected: e,
      },
      () => this.callingDatePicker(this.state.dateSelected),
    );
  }

  callingDatePicker(date_value) {
    let fromTimeGenerator, uptoTimeGenerator;
    if (date_value === "this_month") {
      fromTimeGenerator = moment().startOf("month").unix();
      uptoTimeGenerator = moment().endOf("month").unix();
    } else if (date_value === "last_month") {
      fromTimeGenerator = moment().subtract(1, "month").startOf("month").unix();
      uptoTimeGenerator = moment().subtract(1, "month").endOf("month").unix();
    } else if (date_value === "last_three_months") {
      fromTimeGenerator = moment().subtract(3, "month").startOf("month").unix();
      uptoTimeGenerator = moment().subtract(1, "month").endOf("month").unix();
    } else if (date_value === "last_thirty_days") {
      fromTimeGenerator = moment().subtract(30, "days").startOf("day").unix();
      uptoTimeGenerator = moment().endOf("day").unix();
    } else if (date_value === "last_one_twenty_days") {
      fromTimeGenerator = moment().subtract(120, "days").startOf("day").unix();
      uptoTimeGenerator = moment().endOf("day").unix();
    }
    this.setState(
      {
        fromTime: fromTimeGenerator,
        uptoTime: uptoTimeGenerator,
      },
      async () => {
        await Promise.all([
          this.getThingDailyAggrData(),
          this.getEvents(this.listApiData),
        ]).then(() => {
          this.setState({
            trendNotificationLoading: false,
          });
        });
      },
    );
  }

  ResToggleSwitch(e) {
    this.setState({
      resButton: e,
    });
  }
  noFuelAtAll() {
    let Fuel = true,
      fuelCount = 0;
    if (this.state.listId) {
      let isFuel = true;
      if (this.state.totalData) {
        let currentAsset = _find(this.state.totalData.things, {
          id: this.state.listId,
        });
        if (
          currentAsset &&
          currentAsset.parameters &&
          currentAsset.parameters.length
        ) {
          isFuel = currentAsset.parameters.some((currParam) => {
            return ["fuel", "fuel_lt", "fuel_litre"].includes(currParam.key);
          });
        }
      }
      return !isFuel;
    } else {
      if (
        this.state.totalData &&
        this.state.latestRcvd &&
        this.state.latestRcvd.length
      ) {
        this.state.latestRcvd.map((asset) => {
          let currentAsset = _find(this.state.totalData.things, {
            id: parseInt(asset),
          });
          if (
            currentAsset &&
            currentAsset.parameters &&
            currentAsset.parameters.length
          ) {
            Fuel = currentAsset.parameters.some((currParam) => {
              return ["fuel", "fuel_lt", "fuel_litre"].includes(currParam.key);
            });
            if (!Fuel) {
              fuelCount += 1;
            }
          }
        });
        if (fuelCount === this.state.latestRcvd.length) {
          return true;
        }
      }
      return false;
    }
  }
  isOnlyFuelDG() {
    let Fuel = false,
      fuelCount = 0;
    if (this.state.listId) {
      let isOnlyFuel = false;
      if (this.state.totalData) {
        let currentAsset = _find(this.state.totalData.things, {
          id: this.state.listId,
        });
        if (
          currentAsset &&
          currentAsset.thing_details &&
          currentAsset.thing_details.dg_parameter_type
        ) {
          isOnlyFuel =
            currentAsset.thing_details.dg_parameter_type === "fuel_only";
        }
      }
      return isOnlyFuel || (this.isMechanicalDG(1) && !this.noFuelAtAll());
    } else {
      if (
        this.state.totalData &&
        this.state.latestRcvd &&
        this.state.latestRcvd.length
      ) {
        this.state.latestRcvd.map((asset) => {
          let currentAsset = _find(this.state.totalData.things, {
            id: parseInt(asset),
          });
          if (
            currentAsset &&
            currentAsset.thing_details &&
            currentAsset.thing_details.dg_parameter_type
          ) {
            Fuel = currentAsset.thing_details.dg_parameter_type === "fuel_only";
            if (Fuel) {
              fuelCount += 1;
            }
          }
        });
        if (fuelCount === this.state.latestRcvd.length) {
          return true;
        }
      }
      return this.isMechanicalDG(1) && !this.noFuelAtAll();
    }
  }

  isThingMechanicalDG(selectedThing) {
    let isMechanicalDG = false;
    if (this.state.totalData) {
      let currentAsset = _find(this.state.totalData.things, {
        id: parseInt(selectedThing),
      });
      if (
        currentAsset &&
        currentAsset.thing_details &&
        currentAsset.thing_details.dg_parameter_type
      ) {
        isMechanicalDG =
          currentAsset.thing_details.dg_parameter_type === "mechanical";
      }
    }
    return isMechanicalDG;
  }

  isMechanicalDG(fuel = false) {
    let Mech = false,
      mechanicalCount = 0;
    if (this.state.listId) {
      let isMechanicalDG = false;
      if (this.state.totalData) {
        let currentAsset = _find(this.state.totalData.things, {
          id: this.state.listId,
        });
        if (
          currentAsset &&
          currentAsset.thing_details &&
          currentAsset.thing_details.dg_parameter_type
        ) {
          isMechanicalDG =
            currentAsset.thing_details.dg_parameter_type === "mechanical";
        }
      }
      if (fuel) {
        return isMechanicalDG;
      }
      return isMechanicalDG && this.noFuelAtAll();
    } else {
      if (
        this.state.totalData &&
        this.state.latestRcvd &&
        this.state.latestRcvd.length
      ) {
        this.state.latestRcvd.map((asset) => {
          let currentAsset = _find(this.state.totalData.things, {
            id: parseInt(asset),
          });
          if (
            currentAsset &&
            currentAsset.thing_details &&
            currentAsset.thing_details.dg_parameter_type
          ) {
            Mech =
              currentAsset.thing_details.dg_parameter_type === "mechanical";
            if (Mech) {
              mechanicalCount += 1;
            }
          }
        });
        if (mechanicalCount === this.state.latestRcvd.length) {
          if (fuel) {
            return true;
          }
          return this.noFuelAtAll();
        }
      }
      return false;
    }
  }
  applyFilterSelect(value, key) {
    this.props.history.push(`dashboard?category:${value}`);
    this.setState(
      { categoryId: value, loading: true },
      async () => await this.getThingsList(this.listApiData),
    );
  }

  getUrlBreak(value) {
    let geturlData;
    let geturlDataIndex = _findIndex(
      this.props.history?.location?.search?.split(","),
      function (o) {
        return o.includes(value);
      },
    );
    if (geturlDataIndex > -1) {
      geturlData = this.props.history?.location?.search
        ?.split(",")
        [geturlDataIndex].split(":")[1];
    }
    return geturlData === "undefined" ? "" : geturlData;
  }

  render() {
    let isFuelPanel = true, //basic_fuel
      isPanelOnlyFuel = false,
      isPanelMechanical = false,
      isFuelMechanical = false;
    let upperGraphRow = [],
      lowerGraphRow = [],
      select_config = {
        label: ["Filter Status"],
        select_option: [
          {
            show_arrow: true,
            options: [
              {
                name: "This Month",
                value: "this_month",
              },
              {
                name: "Last Month",
                value: "last_month",
              },
              {
                name: "Last 3 Months",
                value: "last_three_months",
              },
              {
                name: "Last 30 Days",
                value: "last_thirty_days",
              },
              {
                name: "Last 120 Days",
                value: "last_one_twenty_days",
              },
            ],
          },
        ],
      };
    let timeSelect = (
      <AntSelect
        className="date-select"
        defaultValue="last_thirty_days"
        getPopupContainer={(trigger) => trigger.parentNode}
        onChange={(e) => this.handleChange(e)}
        select_button={select_config}
        style={{ width: 150 }}
      >
        <AntOption value="this_month">This Month</AntOption>
        <AntOption value="last_month">Last Month</AntOption>
        <AntOption value="last_three_months">Last 3 Months</AntOption>
        <AntOption value="last_thirty_days">Last 30 Days</AntOption>
        <AntOption value="last_one_twenty_days">Last 120 Days</AntOption>
      </AntSelect>
    );
    let mapRawData = {
      rawData: this.state.latestParameterData,
      modifiedResponseRaw: this.state.modifiedResponse,
    };

    let pageRender = "";
    if (this.state.loading) {
      pageRender = (
        <Loading
          show_logo={this.props.loading_logo}
          className="align-center-loading"
        />
      );
    } else {
      let mapData = this.mapFunction();
      let cardData = this.filterCardFuncTion();
      let listRender = "";
      let panelData = "";
      if (this.state.listId) {
        panelData = this.getPanelData(this.state.listId);
      }
      if (this.state.listLoading) {
        listRender = (
          <div className="list-view-container">
            <AntSpin className="align-center-loading" />
          </div>
        );
      } else if (this.state.panelView && panelData) {
        if (this.state.listId) {
          if (this.state.totalData) {
            let currentAsset = _find(this.state.totalData.things, {
              id: this.state.listId,
            });
            if (
              //basic_fuel
              currentAsset &&
              currentAsset.parameters &&
              currentAsset.parameters.length
            ) {
              isFuelPanel = currentAsset.parameters.some((currParam) => {
                return ["fuel", "fuel_lt", "fuel_litre"].includes(
                  currParam.key,
                );
              });
            }
            if (
              currentAsset &&
              currentAsset.thing_details &&
              currentAsset.thing_details.dg_parameter_type
            ) {
              isPanelMechanical =
                currentAsset.thing_details.dg_parameter_type === "mechanical" &&
                !isFuelPanel;
              isFuelMechanical =
                currentAsset.thing_details.dg_parameter_type === "mechanical";
            }
            if (
              currentAsset &&
              currentAsset.thing_details &&
              currentAsset.thing_details.dg_parameter_type
            ) {
              isPanelOnlyFuel =
                currentAsset.thing_details.dg_parameter_type === "fuel_only" ||
                (isFuelMechanical && isFuelPanel);
            }
          }
        }
        listRender = (
          <div className="list-view-container">
            <PanelComponent
              socket={this.socket}
              {...this.props}
              plan_description={plan_description}
              client_id={this.props.client_id}
              vendor_id={this.props.vendor_id}
              application_id={this.props.application_id}
              getRemoteAccess={this.props.getRemoteAccess()}
              onCloseClick={() => this.onCloseClick()}
              closeEnabled={true}
              isWithoutFuel={!isFuelPanel}
              isPanelOnlyFuel={isPanelOnlyFuel}
              isPanelMechanical={isPanelMechanical}
              isFuelMechanical={isFuelMechanical}
              data={panelData.data}
              dgBasePath={this.dgBasePath}
              latLngDataWithAddress={this.state.latLngDataWithAddress}
              calculateDistance={(latLngData) =>
                this.calculateDistance(latLngData)
              }
              calculateDuration={(moving_start_time) =>
                this.calculateDuration(moving_start_time)
              }
              calculateSpeed={(latLngDataWithTime) =>
                this.calculateSpeed(latLngDataWithTime)
              }
              {...panelData.config}
              dateChange={(e) => this.dateChange(e)}
              fuel_trend_loading={this.state.fuel_trend_loading}
              template_id={this.props.template_id}
            />
          </div>
        );
      } else {
        let listViewData = this.listViewFunction();
        listRender = (
          <ListView
            user_preferences={this.props.user_preferences}
            socket={this.socket}
            client_id={this.props.client_id}
            application_id={this.props.application_id}
            getRemoteAccess={this.props.getRemoteAccess()}
            listOnClick={(listId, is_moving, moving_start_time, isDgOffline) =>
              this.listOnClick(
                listId,
                is_moving,
                moving_start_time,
                isDgOffline,
              )
            }
            getRemoteLockAccess={this.props.getRemoteLockAccess()}
            data={listViewData.data}
            {...listViewData.config}
            history={this.props.history}
            user_id={this.props.user_id}
            dgBasePath={this.dgBasePath}
          />
        );
      }
      let downrowElement = "",
        toggleSwitchButton = "",
        lowerSectionHeader = "",
        activityData = this.getActivityData(),
        totalGraphs = this.graphData();
      if (this.state.trendNotificationLoading) {
        downrowElement = (
          <div style={{ "text-align": "center" }}>
            <AntSpin />
          </div>
        );
      } else {
        let fuelTankThing = false;
        if (this.props.location.search.includes("category:71")) {
          fuelTankThing = true;
        }
        if (totalGraphs.upperGraphs && totalGraphs.upperGraphs.length) {
          totalGraphs.upperGraphs.map((upperGraphsData) => {
            return upperGraphRow.push(
              <AntCol
                className="graph-col"
                xs={24}
                xl={24}
                xxl={
                  this.noFuelAtAll() ||
                  this.isOnlyFuelDG() ||
                  this.isMechanicalDG() ||
                  fuelTankThing
                    ? 24
                    : 24 / totalGraphs.upperGraphs.length
                }
              >
                <AntCard
                  className={
                    "graph-data-container" + " " + this.props.className
                  }
                >
                  <div className="graph-inner-container">
                    <div className="graph-heading">
                      {fuelTankThing
                        ? "Fuel Dispensed"
                        : upperGraphsData.param_value.name}
                    </div>
                    <div className="value-container">
                      <img src={upperGraphsData.param_value.icon} />
                      <div className="graph-data">
                        {upperGraphsData.param_value.value}
                      </div>
                    </div>
                    <div className="graph-div">
                      <GraphHighcharts
                        graphData={upperGraphsData.graph_config.graph_data}
                      />
                    </div>
                  </div>
                </AntCard>
              </AntCol>,
            );
          });
        }
        if (totalGraphs.lowerGraph && totalGraphs.lowerGraph.length) {
          totalGraphs.lowerGraph.map((lowerGraphData, i) => {
            return lowerGraphRow.push(
              <AntCol
                className="graph-col"
                xs={24}
                lg={totalGraphs.lowerGraph.length > 1 ? 12 : 24}
                xxl={24 / totalGraphs.lowerGraph.length}
              >
                <AntCard
                  className={
                    "graph-data-container" + " " + this.props.className
                  }
                >
                  <div className="graph-inner-container">
                    <div className="graph-heading">
                      {lowerGraphData.param_value.name}
                    </div>
                    <div className="value-container">
                      <img src={lowerGraphData.param_value.icon} />
                      <div className="graph-data">
                        {lowerGraphData.param_value.value}
                      </div>
                    </div>
                    <div className="graph-div">
                      <GraphHighcharts
                        graphData={lowerGraphData.graph_config.graph_data}
                      />
                    </div>
                  </div>
                </AntCard>
              </AntCol>,
            );
          });
        }
        if (window.innerWidth < 1024) {
          lowerSectionHeader = (
            <div className="graph-header">
              <div className="header-text">{totalGraphs.headerText}</div>
            </div>
          );
          toggleSwitchButton = (
            <div className="toggle-btn">
              <div className="btn-div">
                <AntButton
                  type={this.state.resButton === "trend" ? "primary" : ""}
                  onClick={() => this.ResToggleSwitch("trend")}
                >
                  Trends
                </AntButton>
                <AntButton
                  type={
                    this.state.resButton === "notifications" ? "primary" : ""
                  }
                  onClick={() => this.ResToggleSwitch("notifications")}
                >
                  Notifications
                </AntButton>
              </div>
              {timeSelect}
            </div>
          );
          if (this.state.resButton === "trend") {
            downrowElement = (
              <div className="mobile-view-div">
                <div className="down-row">
                  <div className="graph-col-container">
                    <div
                      className={
                        this.noFuelAtAll() ||
                        this.isOnlyFuelDG() ||
                        this.isMechanicalDG()
                          ? "graph-row no-fuel-graph-row"
                          : "graph-row"
                      }
                    >
                      {upperGraphRow}
                    </div>
                    {!this.noFuelAtAll() &&
                      !this.isOnlyFuelDG() &&
                      !this.isMechanicalDG() && (
                        <div className="graph-row lower-graph-section">
                          {lowerGraphRow}
                        </div>
                      )}
                  </div>
                </div>
              </div>
            );
          } else {
            downrowElement = (
              <div
                className={`down-row ${
                  !isFuelPanel || isPanelOnlyFuel || fuelTankThing //basic_fuel
                    ? " fuel-remove"
                    : ""
                }`}
              >
                <div className="mobile-view-div">
                  <div className="activity-class">
                    <ActivityPanel
                      data={activityData.activity_violance_data}
                      type={activityData.event_types_array}
                      {...activityData.config}
                      eventChange={(e) => this.eventChange(e)}
                      selectedTab={this.state.selectedTab}
                      noFuelAtAll={
                        this.noFuelAtAll() ||
                        this.isOnlyFuelDG() ||
                        this.isMechanicalDG()
                      }
                    />
                  </div>
                </div>
              </div>
            );
          }
        } else {
          lowerSectionHeader = (
            <div className="graph-header">
              <div className="header-text">{totalGraphs.headerText}</div>
              {timeSelect}
            </div>
          );
          downrowElement = (
            <AntRow
              className={`down-row ${
                (!isPanelOnlyFuel && isPanelMechanical) ||
                fuelTankThing ||
                (totalGraphs.upperGraphs.length === 1 &&
                  totalGraphs.lowerGraph.length === 0) ||
                (totalGraphs.upperGraphs.length === 0 &&
                  totalGraphs.lowerGraph.length === 1)
                  ? " fuel-tank-thing"
                  : !isFuelPanel ||
                      isPanelOnlyFuel ||
                      isPanelMechanical ||
                      plan_description.dashboard_view_section
                        .dashboard_parameters.length <= 2
                    ? " fuel-remove"
                    : ""
                // fuelTankThing
                // ? ' fuel-tank-thing'
                // : ''
              }`}
              gutter={40}
            >
              <AntCol className="graph-col-container" xs={24} lg={16} xxl={19}>
                <AntRow
                  gutter={[
                    window.innerWidth <= 1366 ? 20 : 40,
                    this.noFuelAtAll() ||
                    this.isOnlyFuelDG() ||
                    this.isMechanicalDG()
                      ? 20
                      : 0,
                  ]}
                  className="graph-row"
                >
                  {upperGraphRow}
                </AntRow>
                {!this.noFuelAtAll() &&
                  !this.isOnlyFuelDG() &&
                  !this.isMechanicalDG() && (
                    <AntRow
                      className="graph-row lower-graph-section"
                      gutter={20}
                    >
                      {lowerGraphRow}
                    </AntRow>
                  )}
              </AntCol>
              <AntCol xs={24} lg={8} xxl={5} className="activity-class">
                <ActivityPanel
                  data={activityData.activity_violance_data}
                  type={activityData.event_types_array}
                  {...activityData.config}
                  eventChange={(e) => this.eventChange(e)}
                  selectedTab={this.state.selectedTab}
                  noFuelAtAll={
                    this.noFuelAtAll() ||
                    this.isOnlyFuelDG() ||
                    this.isMechanicalDG()
                  }
                />
              </AntCol>
            </AntRow>
          );
        }
      }
      pageRender = (
        <div id="dashboard_new">
          <div style={{ "margin-bottom": 20 }}>
            <FilterSelectWithSearch
              noSpaceForFilterTag={true}
              filterData={this.state.filterAssetTypes}
              applyFilterSelect={(e) => this.applyFilterSelect(e)}
            />
          </div>
          <div className="filter-section">
            <Kpis
              kpiOnClick={(key) => this.kpiOnClick(key)}
              {...cardData.config}
              data={cardData.data}
              activeKpi={this.state.type}
            />
          </div>
          <AntRow
            className="middle-map-list"
            gutter={{ lg: 20, xl: 20, xxl: 40 }}
          >
            <AntCol xs={24} lg={14} xl={16} xxl={17} className="map-container">
              <GoogleMapComponent
                data={mapRawData}
                mapOnClick={(
                  hoverText,
                  lastData,
                  modifiedResponse,
                  id,
                  is_moving,
                  moving_start_time,
                  isDgOffline,
                ) =>
                  this.mapOnClick(
                    hoverText,
                    lastData,
                    modifiedResponse,
                    id,
                    is_moving,
                    moving_start_time,
                    isDgOffline,
                  )
                }
                reverseGeocode={async (latlng) =>
                  await this.reverseGeocode(latlng)
                }
                calculateDistance={(latLngData) =>
                  this.calculateDistance(latLngData)
                }
                calculateDuration={(moving_start_time) =>
                  this.calculateDuration(moving_start_time)
                }
                calculateSpeed={(latLngDataWithTime) =>
                  this.calculateSpeed(latLngDataWithTime)
                }
                latLngData={this.state.latLngData}
                panelView={this.state.panelView}
                listId={this.state.listId}
                {...mapData.config}
                legends={mapData.map_legend}
                mapPointers={mapData.map_pointers}
                zoomLevel={this.state.zoomLevel}
                centerValue={this.state.centerValue}
                isGpsPathEnabled={this.isGpsPathEnabled()}
              />
            </AntCol>
            <AntCol className="list-col" xs={24} lg={10} xl={8} xxl={7}>
              {listRender}
            </AntCol>
          </AntRow>
          <div className="down-section-container">
            {lowerSectionHeader}
            {toggleSwitchButton}
            {downrowElement}
          </div>
        </div>
      );
      if (window.innerWidth < 576) {
        let listViewData = this.listViewFunction();
        pageRender = (
          <DashboardMobile
            isGpsPathEnabled={this.isGpsPathEnabled()}
            isWithoutFuel={!isFuelPanel}
            isPanelOnlyFuel={isPanelOnlyFuel}
            isPanelMechanical={isPanelMechanical}
            isFuelMechanical={isFuelMechanical}
            vendor_id={this.props.vendor_id}
            cardData={cardData}
            kpiOnClick={(e) => this.kpiOnClick(e)}
            activeKpi={this.state.type}
            socket={this.socket}
            panelView={this.state.panelView}
            client_id={this.props.client_id}
            application_id={this.props.application_id}
            getRemoteAccess={this.props.getRemoteAccess()}
            onPanelCloseClick={() => this.onCloseClick()}
            closeEnabled={true}
            panelData={panelData}
            listViewData={listViewData}
            mapOnClick={(
              hoverText,
              lastData,
              modifiedResponse,
              id,
              is_moving,
              moving_start_time,
              isDgOffline,
            ) =>
              this.mapOnClick(
                hoverText,
                lastData,
                modifiedResponse,
                id,
                is_moving,
                moving_start_time,
                isDgOffline,
              )
            }
            reverseGeocode={async (latlng) => await this.reverseGeocode(latlng)}
            calculateDistance={(latLngData) =>
              this.calculateDistance(latLngData)
            }
            calculateDuration={(moving_start_time) =>
              this.calculateDuration(moving_start_time)
            }
            calculateSpeed={(latLngDataWithTime) =>
              this.calculateSpeed(latLngDataWithTime)
            }
            noFuel={
              !this.noFuelAtAll() &&
              !this.isOnlyFuelDG() &&
              !this.isMechanicalDG()
            }
            listId={this.state.listId}
            latLngData={this.state.latLngData}
            latLngDataWithAddress={this.state.latLngDataWithAddress}
            zoomLevel={this.state.zoomLevel}
            centerValue={this.state.centerValue}
            history={this.props.history}
            totalGraphs={totalGraphs}
            select_config={select_config}
            listOnClick={(listId, is_moving, moving_start_time, isDgOffline) =>
              this.listOnClick(
                listId,
                is_moving,
                moving_start_time,
                isDgOffline,
              )
            }
            handleChange={(e) => this.handleChange(e)}
            activityData={activityData}
            mapRawData={mapRawData}
            plan_description={plan_description}
            mapData={mapData}
            eventChange={(e) => this.eventChange(e)}
            selectedTab={this.state.selectedTab}
            all_things={this.state.modifiedResponse.thing_name_list}
            getRemoteLockAccess={this.props.getRemoteLockAccess()}
            dgBasePath={this.dgBasePath}
            checkCustomerFuelBuddy={this.props.checkCustomerFuelBuddy}
            user_preferences={this.props.user_preferences}
          />
        );
      }
    }

    return <AntLayout className="layout">{pageRender}</AntLayout>;
  }
}
