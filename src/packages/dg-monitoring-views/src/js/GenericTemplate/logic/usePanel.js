import React, { useState, useEffect } from "react";
import _findIndex from "lodash/findIndex";
import _find from "lodash/find";
import _filter from "lodash/filter";
import _uniqBy from "lodash/uniqBy";
import moment from "moment-timezone";
import MaintenanceImg from "../images/maintenance.svg";
import MaintenanceOffLineImg from "../images/Group 8163.svg";
import FaultIcon from "../images/Group 8159.svg";
import FaultIconOffline from "../images/Group 8162.svg";
import NoFaultIcon from "../images/Group 8164.svg";
import { activefaultViolationArray } from "./activeFaultAndViolation";
import { ParamUnit, ParamName } from "../../data_handling/ParamNameUnitFind";
import { maintenanceTextGen } from "../../data_handling/applicationMaintenanceDue";
import { totalActiveFaultsWithWarnTripStatus } from "../../data_handling/getFaultTypeRealtime";
import {
  isRealTime,
  isDetailedView,
} from "./RealTimeAndDetailedViewAvailability";
import { aaqmsImage } from "./getLocalImageOfAAQMS";
import {
  getAqiAndPcConditions,
  getAqiColorStatusForValue,
} from "../logic/aqiParametersToShow";
import _orderBy from "lodash/orderBy";
import { getThingsAndParameterData } from "@datoms/js-sdk";
import { isAurassure, aurassureThingCat } from "./isAurassure";
import { paramAsPerOrder } from "../../data_handling/ParamOrder";
import { getStatusIcon } from "./getStatusIcon";
import { secondsToTimeFormat } from "../../data_handling/TimeFormatting";
import { customerSpecificConfig } from "../utility/customerPecificConfigs";

export const usePanel = (
  responseThings,
  applicationList,
  offlineArray,
  latestParamData,
  faultData,
  maintenanceData,
  filter_value,
  address_data,
  daily_data,
  alert_data,
  camera_details,
  aqi_data,
  props,
  pageNumber,
  panelDetailedData,
  userRoleType,
) => {
  let panel_data = panelDetails(
    responseThings,
    applicationList,
    offlineArray,
    latestParamData,
    faultData,
    maintenanceData,
    filter_value,
    address_data,
    daily_data,
    alert_data,
    camera_details,
    aqi_data,
    props,
    pageNumber,
    panelDetailedData,
    userRoleType,
  );
  return [panel_data];
};

export const getFaultLatestData = (latestData, findThing) => {
  const faultsData = [];
  if (latestData?.data && Object.keys(latestData.data).length) {
    const filteredFaultParams = _filter(findThing?.parameters, {
      type: "fault",
    });
    filteredFaultParams.forEach((param) => {
      const value = latestData?.data[param.key];
      if (parseInt(value) === 1) {
        faultsData.push(param.name);
      }
    });
  }
  return {
    icon: faultsData && faultsData.length ? FaultIcon : NoFaultIcon,
    faults: faultsData,
  };
};

const aqiMajorData = (findThing, latestThingData, aqi_data, isAqi) => {
  let findAqiValue = "NA";
  let aqiStatus = "";
  if (aqi_data?.[latestThingData?.thing_id]?.length) {
    let orderData = _orderBy(
      aqi_data[latestThingData?.thing_id],
      ["time"],
      ["asc"],
    );
    if (orderData?.[orderData.length - 1].parameter_values.aqi) {
      if (orderData[orderData.length - 1].parameter_values.aqi.value >= 0) {
        findAqiValue =
          orderData[orderData.length - 1].parameter_values.aqi.value;
      }
    }
    aqiStatus = getAqiColorStatusForValue(findAqiValue).status;
  }
  let humidity =
    latestThingData?.data &&
    latestThingData?.data.humidity &&
    !isNaN(parseFloat(latestThingData?.data.humidity))
      ? parseFloat(latestThingData?.data.humidity).toFixed(2) +
        " " +
        _find(findThing.parameters, { key: "humidity" })?.unit
      : "NA";
  let temp =
    latestThingData?.data &&
    latestThingData?.data.temperature &&
    !isNaN(parseFloat(latestThingData?.data.temperature))
      ? parseFloat(latestThingData?.data.temperature).toFixed(2) +
        " " +
        _find(findThing.parameters, { key: "temperature" })?.unit
      : "NA";
  let finalData = {
    is_aqi: isAqi,
    aqi_value: findAqiValue,
    aqi_status: aqiStatus,
    humidity: humidity,
    temp: temp,
  };
  return finalData;
};

const getTotalMaintenance = (maintenance_data, list_api) => {
  let maintenanceArray = [];
  if (list_api.things && list_api.things.length) {
    let getAllMaintenance = list_api.things.map((things) => {
      const maintenanceText = maintenanceTextGen(maintenance_data, things.id);
      maintenanceArray.push({ id: things.id, text: maintenanceText });
    });
  }
  return maintenanceArray;
};

const panelDetails = (
  list,
  applicationList,
  latestParameterData,
  faultData,
  maintenanceData,
  filter_value,
  address_data,
  daily_data,
  alert_data,
  camera_details,
  aqi_data,
  props,
  pageNumber,
  panelDetailedData,
  userRoleType,
) => {
  let modifiedResponse = getThingsAndParameterData(list);
  let totalThings = [];
  if (list?.things?.length) {
    list.things.map((things) => {
      let findCat = _find(list.things_categories, {
        id: things.category,
      });
      let latestThingData = _find(latestParameterData, {
        thing_id: things.id,
      });
      const streamLink = things?.thing_details?.stream_url;
      const streamThumbNail = things?.thing_details?.thumbnail;
      let thingStatus = latestThingData?.on_off_moving_status;
      let findAddress = _find(address_data, {
        id: latestThingData?.thing_id,
      });
      let thingLocation = "";
      if (findAddress) {
        thingLocation = findAddress.address;
      }
      let statusObj = {
        Connected: ["1", "0", ""],
        Disconnected: ["2"],
      };
      statusObj["Running"] = ["1"];
      const findCatCopy = findCat && JSON.parse(JSON.stringify(findCat));
      if (findCatCopy && findCatCopy.status_options) {
        if (findCatCopy.status_options.includes("Moving")) {
          statusObj["Moving"] = ["1"];
        }
        if (findCatCopy.status_options.includes("Stopped")) {
          statusObj["Stopped"] = ["0", ""];
        }
      }

      const latestFaultData = getFaultLatestData(latestThingData, things);

      let eqmsThingCameraId =
        things && things.category === 23 && things.thing_details?.camera_id
          ? things.thing_details.camera_id
          : undefined;
      if (
        things &&
        (!filter_value.on_off_status ||
          statusObj[filter_value.on_off_status]?.includes(thingStatus))
      ) {
        let getAqiAndPcCondition = getAqiAndPcConditions(
          list,
          modifiedResponse,
          latestThingData?.thing_id,
        );
        let aqiData = aqiMajorData(
          things,
          latestThingData,
          aqi_data,
          getAqiAndPcCondition.isAqi,
        );
        let thingDetailsArray = [];
        if (findCatCopy) {
          thingDetailsArray.push({
            key: "category",
            name: "",
            value: findCatCopy.name,
          });
        }
        if (findCatCopy?.machine_info?.length) {
          if (things.thing_details) {
            let thingDetailsKeys = Object.keys(things.thing_details);
            function findCatMachineInfo(key) {
              return _find(findCatCopy.machine_info, {
                key: key,
              });
            }

            const keys = [
              "make",
              "model",
              "serial_no",
              "kva",
              "system_capacity",
              "system_total_voltage",
              "rated_capacity",
              "serial",
              "rated_voltage",
              "rated_power",
              "rated_current",
              "rated_freq",
              "rated_speed",
              "rated_power_factor",
            ];

            keys.forEach((key) => {
              if (thingDetailsKeys.includes(key) && findCatMachineInfo(key)) {
                let value = "NA";
                if (things.thing_details && thingDetailsKeys.includes(key)) {
                  value = things.thing_details[key];
                  if (key === "kva") {
                    value += " kva";
                  }
                }

                thingDetailsArray.push({
                  key: key === "serial_no" ? "sl_no" : key,
                  name:
                    key === "system_capacity" &&
                    things.thing_details["capacity_unit"]
                      ? `${findCatMachineInfo(key).label} (${things.thing_details["capacity_unit"]}): `
                      : `${findCatMachineInfo(key).label}: `,
                  value: value,
                  unit: findCatMachineInfo(key)?.unit || "",
                });
              }
            });
          }
        }
        let lastFaultData = activefaultViolationArray(
          things,
          latestThingData,
          faultData[latestThingData?.thing_id]
            ? faultData[latestThingData?.thing_id]
            : [],
        );
        //storing all parameters in real time
        if (
          isAurassure(props.vendor_id) ||
          [21, 22, 102, 23, 45, 19, 85, 90, 91, 92, 93, 44, 76, 73, 18, 71].includes(
            findCatCopy?.id,
          )
        ) {
          const uniqueSet = _uniqBy(
            [].concat(
              ...list.things.map((obj) =>
                obj.parameters.filter(
                  (param) =>
                    !["data_availability", "aqi", "usaqi", "dr_st", "dr_op_cnt"].includes(
                      param.key,
                    ) &&
                    !param.allowed_visualizations.includes("cumulative") &&
                    ![
                      "latitude",
                      "longitude",
                      "status",
                      "time",
                      "error",
                      "fault",
                    ].includes(param?.type) &&
                    !param.key.includes("calculated"),
                  // && param.allowed_visualizations.includes('cumulative'),
                ),
              ),
            ),
            "key",
          );

          const findAppList = applicationList?.things_categories?.find(
            (category) => category.id === findCatCopy?.id,
          );

          const paramArray = findAppList
            ? paramAsPerOrder(findAppList, uniqueSet)
            : [];

          let finalparamArray = [];
          if (paramArray?.length) {
            paramArray.map((paramData) => {
              finalparamArray.push({ key: paramData.key });
            });
          }
          findCatCopy.pages.panel["real_time_parameters"] = finalparamArray;
        }

        const findCustomerSpecificCofigTypeIsPartner = _find(
          customerSpecificConfig(),
          { id: props.parent_vendor_id },
        );
        if (
          findCustomerSpecificCofigTypeIsPartner?.id ===
            props.parent_vendor_id &&
          findCustomerSpecificCofigTypeIsPartner?.["views"]?.[findCat?.id]
            ?.panel_params?.length
        ) {
          findCat.pages.panel["real_time_parameters"] =
            findCustomerSpecificCofigTypeIsPartner?.["views"]?.[
              findCat?.id
            ]?.panel_params;
        }

        let realTimeParams = [];
        const panelTableFormatData = [];
        let findAlertForThing = _find(alert_data, {
          id: latestThingData?.thing_id,
        });
        let totalRealTimeParams = (
          findCatCopy?.pages?.panel?.real_time_parameters || []
        ).concat(findCatCopy?.pages?.list?.real_time_parameters || []);
        totalRealTimeParams = _uniqBy(totalRealTimeParams, "key");
        if(findCatCopy){
          findCatCopy.pages.panel["real_time_parameters"] = totalRealTimeParams;
        }
        const customerSpecificOfflineValue =
          findCustomerSpecificCofigTypeIsPartner?.id ===
            props.parent_vendor_id &&
          findCustomerSpecificCofigTypeIsPartner.deviceThingOfflineValue
            ? findCustomerSpecificCofigTypeIsPartner.deviceThingOfflineValue
            : undefined;
        if (findCatCopy?.pages?.panel?.real_time_parameters?.length) {
          totalRealTimeParams.map((params) => {
            if (
              !(props.logged_in_user_role_type === 11 && params.key === "flow") &&
              _find(things.parameters, {
                key: params.key,
              })
            ) {
              let upperThreshold = undefined;
              let lowerThreshold = undefined;
              if (findAlertForThing) {
                upperThreshold = _find(
                  findAlertForThing.data.alert_rules,
                  function (o) {
                    return (
                      o?.tags?.includes(params.key) &&
                      o?.tags?.includes("Danger") &&
                      o?.is_active &&
                      o?.rule_data?.ul_danger &&
                      !isNaN(parseFloat(o.rule_data.ul_danger))
                    );
                  },
                )?.rule_data.ul_danger;
                lowerThreshold = _find(
                  findAlertForThing.data.alert_rules,
                  function (o) {
                    return (
                      o?.tags?.includes(params.key) &&
                      o?.tags?.includes("Danger") &&
                      o?.is_active &&
                      o?.rule_data?.ll_danger &&
                      !isNaN(parseFloat(o.rule_data.ll_danger))
                    );
                  },
                )?.rule_data.ll_danger;
              }
              const findParam = _find(things.parameters, {
                key: params.key,
              });
              const findParamName = aurassureThingCat(
                findCatCopy?.id,
                props.vendor_id,
              )
                ? findParam.name
                : ParamName(params.key, things.parameters);
              const isDataAvl =
                latestThingData?.data?.[params.key] &&
                !isNaN(parseFloat(latestThingData?.data[params.key]));
              const unit =
                thingStatus === "2" && customerSpecificOfflineValue
                  ? ""
                  : aurassureThingCat(findCatCopy?.id, props.vendor_id)
                    ? findParam?.unit
                    : ParamUnit(findParam?.unit);
              let value = "NA";
              
              // If device is offline and has a specific offline value, use it
              if (thingStatus === "2" && customerSpecificOfflineValue) {
                value = customerSpecificOfflineValue;
              } 
              // If data is available, process it based on parameter type
              else if (isDataAvl) {
                // Special handling for door status parameter
                if (params.key === "dr_st") {
                  const doorStatus = parseFloat(latestThingData?.data[params.key]);
                  value = doorStatus === 1 ? "Open" : doorStatus === 0 ? "Closed" : "-";
                } 
                // Integer parameters
                else if (params.dataType === "integer") {
                  value = parseInt(latestThingData?.data[params.key]);
                } 
                // Float parameters (default)
                else {
                  value = parseFloat(latestThingData?.data[params.key]).toFixed(2);
                }
              }
              realTimeParams.push({
                tooltipDetails: findParam?.param_details?.description?.length
                  ? `Description: ${findParam.param_details.description}`
                  : undefined,
                icon: findParam?.icon !== "" ? findParam?.icon : "",
                name: findParamName,
                value,
                thresholdString:
                  upperThreshold || lowerThreshold
                    ? lowerThreshold
                      ? lowerThreshold + "-" + upperThreshold
                      : upperThreshold
                    : undefined,
                upperThreshold: upperThreshold,
                lowerThreshold: lowerThreshold,
                unit,
                key: params.key,
              });
              if(panelTableFormatData.length < 8) {
                panelTableFormatData.push([
                  findParamName + (unit ? " " + "(" + unit + ")" : ""),
                  value,
                  panelDetailedData?.[things?.id]?.[params.key]?.[
                    "last_15_mins_avg"
                  ],
                  panelDetailedData?.[things?.id]?.[params.key]?.[
                    "last_24_hrs_min"
                  ],
                  panelDetailedData?.[things?.id]?.[params.key]?.[
                    "last_24_hrs_max"
                  ],
                ]);
              }
            }
          });
        }
        let currentTime = "",
          dgLockStatus = "0";
        if (latestThingData) {
          if (latestThingData?.time === 0) {
            currentTime = props.t? props.t("no_data_received"): "No Data Received";
          } else {
            currentTime = moment
              .unix(latestThingData?.time)
              .format("DD MMM YYYY, HH:mm");
          }
          if (
            _find(latestThingData?.data, {
              key: "dg_lock_status",
            })
          ) {
            dgLockStatus = _find(latestThingData?.data, {
              key: "dg_lock_status",
            })?.value;
          }
        }
        function getSummarydata(summaryValues, findCatCopy) {
          const summaryParams = [];
          if (summaryValues?.length) {
            summaryValues.map((sv) => {
              let findParam = _find(things.parameters, {
                key: sv.parameter,
              });
              let baseParamExist = true;
              if(sv.baseParameter) {
                let findBaseParameter = _find(things.parameters, {
                  key: sv.baseParameter,
                });
                baseParamExist = findBaseParameter ? true : false;
              }
              if (findParam && baseParamExist) {
                let findParamValue =
                  daily_data?.[latestThingData?.thing_id]?.[sv.duration]?.[
                    sv.parameter
                  ]?.value;
                summaryParams.push({
                  param_duration: sv.duration,
                  key: sv.parameter,
                  param_name: sv?.name || findParam?.name,
                  param_value:
                    thingStatus === "2" && customerSpecificOfflineValue
                      ? customerSpecificOfflineValue
                      : !isNaN(parseFloat(findParamValue))
                        ? sv.type === "time" || sv.type === "time_format"
                          ? sv.type === "time"
                            ? secondsToTimeFormat(
                                parseFloat(findParamValue),
                                "HH:mm:ss",
                              )
                            : secondsToTimeFormat(
                                parseFloat(findParamValue),
                                "HH:mm",
                              )
                          : `${parseFloat(findParamValue).toFixed(2)}`
                        : "NA",
                  unit:
                    thingStatus === "2" && customerSpecificOfflineValue
                      ? ""
                      : sv.type === "time"
                        ? ""
                        : aurassureThingCat(findCatCopy?.id, props.vendor_id)
                          ? _find(things.parameters, {
                              key: findParam?.key,
                            }).unit
                          : ParamUnit(
                              _find(things.parameters, {
                                key: findParam?.key,
                              }).unit,
                            ),
                });
              }
            });
          }
          return summaryParams;
        }
        const statusIcon = getStatusIcon(
          things.category,
          parseInt(latestThingData?.data?.mc_st) === 1 ? "on" : "off",
        );
        const tdls = [];
        if (findCat?.req_thing_details) {
          findCat.req_thing_details.map((tds) => {
            tdls.push({ key: tds, value: things.thing_details[tds] });
          });
        }
        totalThings.push({
          icon: aaqmsImage(things, props.vendor_id),
          deviceStatus: [44, 85].includes(things.category)
            ? thingStatus === "2"
              ? "offline"
              : "online"
            : things.devices?.[0]?.online_status === 1
              ? "online"
              : "offline",
          id: things?.id,
          aurassureVar: aurassureThingCat(findCatCopy?.id, props.vendor_id),
          statusIcon,
          thingsData: things,
          is_panel:
            findCatCopy?.pages?.["panel"] &&
            Object.keys(findCatCopy.pages.panel).length,
          category: things.category,
          categoryName: findCatCopy?.name,
          command_status: things?.commands,
          on_off_moving_status: thingStatus,
          customerSpecificOfflineValue: customerSpecificOfflineValue,
          faultStatus: totalActiveFaultsWithWarnTripStatus(
            latestThingData,
            props.vendor_id,
            list,
          )?.fault_status,
          status_options: findCatCopy?.status_options,
          no_status: findCatCopy?.no_status,
          status_option_includes_stopped:
            findCatCopy?.status_options?.includes("Stopped"),
          name: things.name,
          date: currentTime,
          aqi_data: aqiData,
          latestFaultData: latestFaultData,
          lat:
            parseFloat(latestThingData?.data?.["lat"]) &&
            parseFloat(latestThingData?.data?.["long"])
              ? parseFloat(latestThingData?.data?.["lat"])
              : things.latitude,
          lng:
            parseFloat(latestThingData?.data?.["lat"]) &&
            parseFloat(latestThingData?.data?.["long"])
              ? parseFloat(latestThingData?.data?.["long"])
              : things.longitude,
          location: findCatCopy?.show_location
            ? thingLocation
              ? thingLocation
              : things?.address
            : "",
          stream_link: streamLink,
          streamThumbNail: streamThumbNail?.length
            ? streamThumbNail
            : undefined,
          maintenance: {
            icon: thingStatus === 2 ? MaintenanceOffLineImg : MaintenanceImg,
            text: _find(getTotalMaintenance(maintenanceData, list), {
              id: things.id,
            })?.text,
          },
          eqms_ip_camera: eqmsThingCameraId
            ? {
                icon: _find(list.things, {
                  id: eqmsThingCameraId,
                })?.thing_category_icon,
                status: _find(latestParameterData, {
                  thing_id: eqmsThingCameraId,
                })?.on_off_moving_status,
              }
            : undefined,
          is_detailed_view: userRoleType !== 11 && isDetailedView(things.id, list),
          is_real_time: userRoleType !== 11 && isRealTime(things.id, list),
          show_fault: findCatCopy?.show_fault,
          show_switch: findCatCopy?.show_switch,
          show_lock: findCatCopy?.show_lock,
          panel_summary_params: getSummarydata(
            findCatCopy?.pages?.panel?.summary_value,
          ),
          list_summary_params: getSummarydata(
            findCatCopy?.pages?.list?.summary_value ||
              findCatCopy?.pages?.panel?.summary_value,
          ),
          thing_details: thingDetailsArray,
          real_time_params: realTimeParams,
          table_format_data: panelTableFormatData,
          allParams: things.parameters,
          dg_lock_status: dgLockStatus,
          is_data_availability: findCatCopy?.is_data_availability,
          is_lock_control_enabled:
            things &&
            things.thing_details &&
            things.thing_details.is_lock_unlock_control_enabled === "enable"
              ? true
              : false,
          operation_mode:
            things && things.thing_details
              ? things.thing_details.operation_mode
              : undefined,
          is_control_enabled:
            things &&
            things.thing_details &&
            things.thing_details.is_start_stop_control_enabled === "enable"
              ? true
              : false,
          threshold_tooltip: [45, 19].includes(findCatCopy?.id),
          dr_st: latestThingData?.data?.["dr_st"],
          realtimePanelParamsFromCategory:
            findCat?.pages?.panel?.real_time_parameters || realTimeParams,
          realtimeListParamsFromCategory:
            findCat?.pages?.list?.real_time_parameters || realTimeParams,
          req_thing_details: tdls,
          page_number: things.page_number,
        });
        // }
      }
    });
  }
  // let finalFilter = [];
  // if (
  //   filter_value?.on_off_status !== undefined &&
  //   filter_value?.on_off_status?.length > 0
  // ) {
  //   finalFilter = _filter(totalThings, function (o) {
  //     return o.status_options.includes(filter_value?.on_off_status);
  //   });
  // } else {
  //   finalFilter = totalThings;
  // }
  // let filterArrayFault = [],
  //   filterArrayOffline = [],
  //   restPanels = [];
  // if (finalFilter && finalFilter.length) {
  //   finalFilter.map((elements) => {
  //     if (
  //       elements.last_fault_data.faults &&
  //       elements.last_fault_data.faults.length
  //     ) {
  //       filterArrayFault.push(elements);
  //     } else if (parseInt(elements.on_off_moving_status) === 2) {
  //       filterArrayOffline.push(elements);
  //     } else {
  //       restPanels.push(elements);
  //     }
  //   });
  // }
  // filterArrayFault = filterArrayFault
  //   .concat(filterArrayOffline)
  //   .concat(restPanels);
  // return filterArrayFault;
  return totalThings;
};
