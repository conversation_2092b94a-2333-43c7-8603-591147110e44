import React, { useEffect, useState } from "react";
import _find from "lodash/find";
import _filter from "lodash/filter";
import _uniqBy from "lodash/uniqBy";
import _findIndex from "lodash/findIndex";
import { useGlobalContext } from '../../../../../../store/globalStore';

export const useOptions = (
  isAllCategory,
  site_list,
  list,
  history,
  setPageNumber,
  setInternalPanelLoading,
  bucketTime,
  isAurassure,
  defaultApiLoaded
) => {
  function getDataFromUrl(urlName, defaultValue) {
    let urlData = getUrlBreak(urlName);
    if (["site", "category"].includes(urlName) && urlData !== "all") {
      urlData = parseInt(urlData);
    }
    if (urlName === "search" && urlData) {
      urlData = decodeURIComponent(urlData);
    }
    if (
      urlName === "territories"
    ) {
      const decodedValue = decodeURIComponent(urlData);
      if (
        decodedValue &&
        decodedValue !== "undefined" &&
        decodedValue !== "null"
      ) {
        urlData = decodedValue
          .split(",")
          .map((item) => (isNaN(parseInt(item)) ? item : parseInt(item)));
      } else {
        urlData = "";
      }
    }
    return urlData ? urlData : defaultValue;
  }
  const {t} = useGlobalContext();  
  const [filter_value, setFilter] = useState({
    territories: getDataFromUrl("territories", []),
    site: getDataFromUrl("site", "all"),
    sites: getSiteOptions(site_list),
    category: getDataFromUrl("category", "all"),
    categories: getCategoryOptions(isAllCategory, list, history, t),
    on_off_status: getDataFromUrl("status", ""),
    onOffStatus: getOnlineOfflineOption("all", list, isAurassure),
    on_off_device_status: getDataFromUrl("device_st", ""),
  });
  const [search_data, setSearch] = useState(getDataFromUrl("search", ""));
  useEffect(() => {
    if ([site_list?.data?.length, list?.things?.length].includes(undefined)) {
      return;
    }
    onUrlChange(history);
  }, [site_list?.data?.length, list?.things?.length, isAllCategory, defaultApiLoaded]);

  function applyFilterSelect(value, key, history) {
    const deviceStatus = [
      {
        value: "online",
        title: "Online",
      },
      {
        value: "offline",
        title: "Offline",
      },
    ];

    console.log("Category Value", key, value)
    if(key === "territories") {
      const siteOptions = getSiteOptions(site_list, value[0]);
      return {
        selected_values: [value[0]?.length ? value[0] : undefined, 'all', 'all', '', ''],
        total_options: {
          site: siteOptions,
          category: undefined,
          status: undefined,
          device_st: undefined,
        },
      }
    }
    if (key === "site") {
      let filterThings =
        value[1] === "all"
          ? list.things
          : _filter(list.things, { site_id: value[1] });
      let totalCategory = getCategoryOptions(
        isAllCategory,
        {
          things_categories: list.things_categories,
          things: filterThings,
        },
        history,
        t
      );
      let categoryIds = [];
      totalCategory.map((cat) => {
        if (cat.value !== "all") categoryIds.push(cat.value);
      });
      let totalOnOffStatus = getOnlineOfflineOption(
        // totalCategory[0].value,
        categoryIds,
        {
          things_categories: list.things_categories,
          things: filterThings,
        },
        isAurassure
      );
      return {
        selected_values: [value[0]?.length ? value[0] : undefined, value[1], totalCategory[0]?.value, "", ""],
        total_options: {
          site: undefined,
          category: totalCategory,
          status: totalOnOffStatus,
          device_st: deviceStatus,
        },
      };
    }
    if (key === "category") {
      console.log("Category Value", value)
      let totalOnOffStatus = getOnlineOfflineOption(
        value[2],
        {
          things_categories: list.things_categories,
          things: list.things,
        },
        isAurassure
      );
      return {
        selected_values: [value[0]?.length ? value[0] : undefined, value[1], value[2], "", ""],
        total_options: {
          site: undefined,
          category: undefined,
          status: totalOnOffStatus,
          device_st: deviceStatus,
        },
      };
    }
    if (key === "status") {
      return {
        selected_values: [value[0]?.length ? value[0] : undefined, value[1], value[2], value[3], value[4]],
        total_options: {
          site: undefined,
          category: undefined,
          status: undefined,
          device_st: deviceStatus,
        },
      };
    }
    if (key === "device_st") {
      return {
        selected_values: [value[0]?.length ? value[0] : undefined, value[1], value[2], value[3], value[4]],
        total_options: {
          site: undefined,
          category: undefined,
          status: undefined,
          device_st: undefined,
        },
      };
    }
  }

  function onUrlChange(history) {
    setInternalPanelLoading(true);
    // let getSiteValue = "all",
    //   getCategoryValue = "all",
    //   getStatusValue = "";
    const getSiteValue = getDataFromUrl("site", "all");
    const getCategoryValue = getDataFromUrl("category", "all");
    const getStatusValue = getDataFromUrl("status", "");
    const getDeviceStatusValue = getDataFromUrl("device_st", "");
    let filterThings =
      getSiteValue === "all"
        ? list.things
        : _filter(list.things, { site_id: getSiteValue });
    let totalCategory = getCategoryOptions(
      isAllCategory,
      {
        things_categories: list.things_categories,
        things: filterThings,
      },
      history,
      t
    );
    let totalSites = getSiteOptions(site_list);
    let totalOnOffStatus = getOnlineOfflineOption(getCategoryValue, {
      things_categories: list.things_categories,
      things: filterThings,
    }, isAurassure);
    setFilter((p) => ({
      ...p,
      territories: getDataFromUrl("territories", []),
      site: getSiteValue,
      sites: totalSites,
      category: _find(totalCategory, { value: getCategoryValue })
        ? getCategoryValue
        : totalCategory[0]?.value,
      categories: totalCategory,
      on_off_status: getStatusValue,
      onOffStatus: totalOnOffStatus,
      on_off_device_status: getDeviceStatusValue,
    }));
    setPageNumber(1);
    const getSearchValue = getDataFromUrl("search", "");
    setSearch(getSearchValue);
  }

  function getUrlBreak(value) {
    let geturlData;
    let geturlDataIndex = _findIndex(
      history?.location?.search?.split(","),
      function (o) {
        return o.includes(value);
      },
    );
    if (geturlDataIndex > -1) {
      geturlData = history?.location?.search
        ?.split(",")
        [geturlDataIndex].split(":")[1];
    }
    return geturlData;
  }
  return [search_data, applyFilterSelect, filter_value, setFilter, onUrlChange];
};

function getSiteOptions(siteList, territory_ids = []) {
  let allSites = [];
  if (siteList?.data?.length) {
    allSites = [
      {
        title: "All Sites",
        value: "all",
      },
    ];
    siteList.data.forEach((data) => {
      if (
        Array.isArray(territory_ids) &&
        territory_ids.length &&
        !territory_ids.some((id) => parseInt(id) === data.territory_id)
      )
        return;

      allSites.push({
        value: data.id,
        title: data.name,
      });
    });
  }
  return allSites;
}

function getCategoryOptions(isAllCategory, thingData, history, t) {
  let categories = [];
  
  if (isAllCategory) {
    categories = [
      {
        title: t? t("All Assets"): "All Assets",
        value: "all",
      },
    ];
  }
  if (thingData && thingData.things && thingData.things.length) {
    let uniqCat = _uniqBy(thingData.things, "category");
    let finalUniqCat;
    if (history?.location?.search?.includes("list")) {
      let catArray = [...uniqCat];
      finalUniqCat = _filter(catArray, function (o) {
        return ![44, 85].includes(o.category);
      });
    } else {
      finalUniqCat = uniqCat;
    }
    if (finalUniqCat?.length) {
      finalUniqCat.map((cats) => {
        const categoryName = _find(thingData.things_categories, {
          id: cats.category,
        })?.name;
        if (categoryName === "") return;
        categories.push({
          value: cats.category,
          title: categoryName,
        });
      });
    }
  }
  return categories;
}

function getOnlineOfflineOption(category, thingsData, isAurassure) {
  let statusOptions = [];
  if (category === "all" || Array.isArray(category)) {
    let availableThings = [];
    if (thingsData.things && thingsData.things.length) {
      thingsData.things.map((list) => {
        availableThings.push(list.category);
      });
    }
    if (
      thingsData &&
      thingsData.things_categories &&
      thingsData.things_categories.length
    ) {
      let filterThings = [];
      if (availableThings && availableThings.length) {
        availableThings.map((cats) => {
          if (_find(thingsData.things_categories, { id: cats })) {
            filterThings.push(
              _find(thingsData.things_categories, { id: cats }),
            );
          }
        });
      }
      filterThings.map((thingCats) => {
        if (
          thingCats?.status_options &&
          (!Array.isArray(category) ||
            (Array.isArray(category) && category.includes(thingCats.id)))
        ) {
          statusOptions.push(...thingCats.status_options);
        }
      });
    }
  } else {
    if (
      thingsData &&
      thingsData.things_categories &&
      thingsData.things_categories.length
    ) {
      let findSelectedCat = _find(thingsData.things_categories, {
        id: category,
      });
      if (findSelectedCat?.status_options) {
        statusOptions.push(...findSelectedCat.status_options);
      }
    }
  }
  if (isAurassure) {
    statusOptions = ["Running", "Disconnected"];
  } else {
    statusOptions = [...new Set(statusOptions)];
  }
  const desiredOrder = ["Connected", "Running", "Stopped", "Disconnected"];
  const sortedUniqueValues = desiredOrder.filter((status) =>
    statusOptions.includes(status),
  );
  let options = [];
  if (sortedUniqueValues && sortedUniqueValues.length) {
    sortedUniqueValues.map((onOffOptions) => {
      options.push({
        value: onOffOptions,
        title: isAurassure
          ? onOffOptions === "Running"
            ? "Online"
            : "Offline"
          : onOffOptions.replace(/_/g, " "),
      });
    });
  }
  return options;
}
