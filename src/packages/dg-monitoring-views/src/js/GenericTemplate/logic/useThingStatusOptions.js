import React from "react";
import _filter from "lodash/filter";
import _find from "lodash/find";

export const useThingStatusOptions = (t,panel_data,total_on_off_status,isAurassure,deviceStatus,isSistemaBioCustomer) => {
  
  let total = [],
    online = [],
    running = [],
    notConnected = [],
    stopped = [];
  const onlineDevices = _find(deviceStatus, { title: "Online" })?.value;
  let thing_activity_status = [
    {
      title: t? t("total_assets"): "Total Assets",
      // title: "Total Assets",
      total: true,
      value: panel_data?.length,
    },
  ];
  if (panel_data && panel_data.length) {
    panel_data.map((latestParam) => {
      if(isAurassure) {
        if (latestParam.on_off_moving_status === "1") {
          running.push(latestParam);
        } else if (latestParam.on_off_moving_status === "2") {
          notConnected.push(latestParam);
        }
      } else {
        if(latestParam.deviceStatus === "online") {
          if (latestParam.status_options?.includes("Stopped")) {
            if (latestParam.on_off_moving_status === "1") {
              running.push(latestParam);
            } else if (latestParam.on_off_moving_status === "2") {
              notConnected.push(latestParam);
            } else if (
              latestParam.on_off_moving_status === "0" ||
              latestParam.on_off_moving_status === ""
            ) {
              stopped.push(latestParam);
            }
          } else {
            if (latestParam.on_off_moving_status === "1") {
              running.push(latestParam);
            } else if (latestParam.on_off_moving_status === "2") {
              notConnected.push(latestParam);
            }
          }
        }
      }
    });
  }
  if (total_on_off_status?.length) {
    let allThingsStatus = {};
    if (_find(total_on_off_status, { value: "Stopped" })) {
      allThingsStatus = {
        Running: running,
        Stopped: stopped,
        Disconnected: notConnected,
      };
    } else {
      if(isAurassure) {
        allThingsStatus = {
          Running: running,
          Disconnected: notConnected,
        };
      } else {
        allThingsStatus = {
          Disconnected: notConnected,
        };
      }
    }
    total_on_off_status.map((status) => {
      if (allThingsStatus[status.value]) {
        thing_activity_status.push({
          title: status.title.replace(/_/g, " "),
          value:
            onlineDevices > 0 ? allThingsStatus[status.value]?.length : "-",
        });
      }
    });
  }

  if(isSistemaBioCustomer) {
    let modifiedArray = [];
    let total, running = 0;
    thing_activity_status.forEach((status) => {
      if(status.title === "Total Assets") {
        modifiedArray.push({
          title: "Total Gensets",
          total: true,
          value: status.value
        })
        total = isNaN(status.value) ? 0 : status.value;
      } else if(status.title === "Running") {
        modifiedArray.push({
          title: "Gensets On",
          value: status.value
        })
        running = isNaN(status.value) ? 0 : status.value;
      }
    })
    thing_activity_status = modifiedArray;

    thing_activity_status.push({
      title: "Gensets Off",
      value: total - running
    })
  }
  
  return thing_activity_status;
};
