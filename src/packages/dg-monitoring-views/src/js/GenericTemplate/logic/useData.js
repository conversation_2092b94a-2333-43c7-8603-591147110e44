import React, { useState, useEffect } from "react";
import {
  retriveThingsList,
  retriveApplicationThings,
  getThingsAndParameterData,
  retriveEventsData,
  retriveAlerts,
  getThingsData,
  establishSocketConnection,
  subscribeForThingsUpdates,
  subscribeForEntityUpdates,
  disconnectSocketConnection,
  retrieveSitesList,
} from "@datoms/js-sdk";
import { sortedThingsOrderArray } from "./ThingsOrder";
import _findIndex from "lodash/findIndex";
import _find from "lodash/find";
import _filter from "lodash/filter";
import _sortBy from "lodash/sortBy";
import _merge from "lodash/merge";
import _uniqWith from "lodash/uniqWith";
import _isEqual from "lodash/isEqual";
import { fetchAddress, getAddressFromLat } from "../utility";
import { getFinalThingCat } from "./getThingsCats";
import moment from "moment-timezone";
import { filterDginIotNew } from "../../data_handling/DGinIot";
import { ipLatestData } from "./IpLatestData";
import { serviceAlertFetch } from "../../data_handling/applicationMaintenanceDue";
import { isAurassure } from "./isAurassure";
import { flushSync } from "react-dom";
import { useOptions } from "./useOptions";
import { getSocketInterval } from "../logic/socketUtility";
import { getTerritoryData } from "../../../../../webapp-component-user-management/src/components/TerritoryPage/TerritorySelect";

const asset15MinLastUpdateTime = {};
export const useData = (
  props,
  pageNumber,
  setPanelBodyLoading,
  parsedFilterFromUrl,
  setPageNumber,
  page_key,
  isThermofisher,
) => {
  const isPanel = props.location && !props.location.pathname.includes("list-view");
  let bucket = {
    raw_data: {},
  };
  let bucketTime = null;
  const [loadMoreBtnLoading, setLoadMoreBtnLoading] = useState(false);
  const [territoryList, setTerritoryList] = useState(null);
  const [site_list, siteList] = useState({});
  const [allThings, setAllThings] = useState({});
  const [list_data, setList] = useState({});
  const [application_list, setApplicationList] = useState({});
  const [modified_list_data, setModifiedList] = useState({});
  const [latest_data, setLatestData] = useState([]);
  const [fault_data, setFaultData] = useState({});
  const [fault_loading, setFaultLoading] = useState(true);
  const [maintenance_data, setMaintenanceData] = useState({});
  const [maintenance_loading, setMaintenanceLoading] = useState(true);
  const [address_data, setAddress] = useState([]);
  const [daily_data, setDailyData] = useState({});
  const [camera_details, setCameraDetails] = useState({});
  const [alert_data, setAlertData] = useState([]);
  const [daily_data_loading, setDailyDataLoading] = useState({});
  const [offline_array, offlineArrayData] = useState({});
  const [socket_value, setSocket] = useState(null);
  const [loading, getLoading] = useState(true);
  const [aqi_data, setAqiData] = useState({});
  const [aqi_loading, setAqiLoading] = useState(true);
  const [internal_panel_loading, setInternalPanelLoading] = useState(true);
  const [defaultApiLoaded, setDefaultApiLoaded] = useState(false);
  const [panelDetailedData, setPanelDetailedData] = useState({});
  const [last24HoursDataStore, setLast24HoursDataStore] = useState({});
  const [detailedDataLoading, setDetailedDataLoading] = useState(false);
  let updateThings = list_data?.things?.length ? list_data?.things : [];
  let onlyUpdatedThings = list_data?.things?.length ? list_data?.things : [];
  useEffect(() => {
    if (onlyUpdatedThings?.length) {
      setInternalPanelLoading(false);
    }
  }, [onlyUpdatedThings]);
  let updateData = daily_data;
  let updateAqiData = aqi_data;
  async function fetchDefaultThingsApi() {
    let allThingsResponse = await retriveThingsList({
      client_id: props.client_id,
      application_id: props.application_id,
    });
    allThingsResponse.things_categories = getFinalThingCat(
      isAurassure(props.vendor_id),
    );
    flushSync(() => {
      setAllThings(allThingsResponse);
    });
  }
  async function fetchDefaultSiteApis() {
    let siteListResponse = await retrieveSitesList(
      props.client_id,
      `?page_no=${1}&results_per_page=${1000}`,
    );
    flushSync(() => {
      siteList(siteListResponse);
    });
  }
  async function fetchTerritoryList() {
    const territoryResponse = await getTerritoryData(
      props.client_id,
      props.client_name,
      props.enabled_features?.includes("UserManagement:Territory"),
    );
    setTerritoryList(territoryResponse?.territoryData);
  }
  useEffect(() => {
    const defaultlists = async () => {
      await Promise.all([
        fetchDefaultSiteApis(),
        fetchDefaultThingsApi(),
        fetchTerritoryList(),
      ]);
      setDefaultApiLoaded(true);

      // This is done to update device online/offline status, as it is not received from socket
      const interval = setInterval(async () => {
        await fetchDefaultThingsApi();
      }, 60000);
      return () => clearInterval(interval);
    };
    const cleanupDefaultlists = defaultlists();
    return () => {
      cleanupDefaultlists.then((cleanup) => cleanup());
    };
  }, []);
  async function fetchDetails(filter_value, search_data, polling) {
    const siteVal = !isNaN(filter_value.site) ? filter_value.site : "";
    const categoryVal = !isNaN(filter_value.category)
      ? filter_value.category
      : "";
    setLoadMoreBtnLoading(polling ? false : true);
    const onlineOfflineStatus = filter_value.on_off_status?.length
      ? ["running", "stopped", "connected"].includes(
          filter_value.on_off_status.toLowerCase(),
        )
        ? "online"
        : "offline"
      : "";
    let stoppedCategory =
      filter_value.on_off_status.toLowerCase() === "stopped" ? 18 : categoryVal;
    if (props.dg_in_iot_mode) {
      stoppedCategory = props.thing_type;
    }
    let urlString = polling
      ? `?page_no=${1}&results_per_page=100
			&site=${siteVal}&thing_category=${stoppedCategory}&search=${search_data}
			&online_offline_status=${onlineOfflineStatus}`
      : `?page_no=${pageNumber}&results_per_page=12
			&site=${siteVal}&thing_category=${stoppedCategory}&search=${search_data}
			&online_offline_status=${onlineOfflineStatus}`;
    if (filter_value.territories?.length) {
      urlString += `&territories=${encodeURIComponent(filter_value.territories)}`;
    }
    let [responseThings, applicationThings] = await Promise.all([
      retriveThingsList(
        {
          client_id: props.client_id,
          application_id: props.application_id,
        },
        urlString,
      ),
      retriveApplicationThings(props.application_id),
    ]);
    onlyUpdatedThings = responseThings?.things?.length
      ? [...responseThings.things]
      : [];
    pageNumber === 1
      ? (updateThings = [...onlyUpdatedThings])
      : onlyUpdatedThings.forEach((thing) => {
          thing.page_number = pageNumber;
          updateThings.push(thing);
        });
    responseThings.things = _uniqWith(updateThings, _isEqual);
    let thingsOrder = sortedThingsOrderArray(responseThings);
    responseThings = filterDginIotNew(responseThings, props, "panel-view");
    responseThings.things_categories = getFinalThingCat(
      isAurassure(props.vendor_id),
    );
    let modifiedResponse = getThingsAndParameterData(responseThings);
    let offlineArray = getOnlineOfflineArray(responseThings);
    let latestParamData = offlineTimeOutFunction(
      undefined,
      offlineArray,
      responseThings,
      modifiedResponse.latest_parameter_data,
      modifiedResponse,
      props.client_id,
    );
    setLatestData(latestParamData);
    flushSync(() => {
      setList(responseThings);
      setApplicationList(applicationThings);
      setModifiedList(modifiedResponse);
      if(isThermofisher) {
        setDetailedDataLoading(true);
      }
    });
    if (
      responseThings?.status === "success" &&
      applicationThings?.status === "success"
    ) {
      getLoading(false);
      setPanelBodyLoading(false);
      setLoadMoreBtnLoading(false);
      setInternalPanelLoading(false);
    }
    if (modifiedResponse?.param_key_data?.includes("aqi")) {
      setAqiLoading(true);
      const totalAqiResponse = await aqiDataCall(
        latestParamData,
        props.client_id,
        props.application_id,
        responseThings,
        onlyUpdatedThings,
      );
      if (Object.keys(updateAqiData).length) {
        updateAqiData = {
          ...updateAqiData,
          ...totalAqiResponse,
        };
      } else {
        updateAqiData = totalAqiResponse;
      }
      flushSync(() => {
        setAqiData(updateAqiData);
        setAqiLoading(false);
      });
    } else {
      setAqiLoading(false);
    }
    if (!polling) {
      const [
        alertDataArray,
        totalFaultResponse,
        maintenanceData,
        dailyDataArray,
        addressArray,
        { newData: updatedPanelDetailedData, last24HoursDataStoreUpdated },
      ] = await Promise.all([
        getAlerts(
          props.client_id,
          props.application_id,
          responseThings,
          onlyUpdatedThings,
        ),
        getTotalFault(
          props.client_id,
          props.application_id,
          responseThings,
          onlyUpdatedThings,
        ),
        fetchMaintenance(props.client_id, onlyUpdatedThings),
        getDailyData(
          props.client_id,
          props.application_id,
          responseThings,
          daily_data,
          onlyUpdatedThings,
          setDailyDataLoading,
          polling,
        ),
        getTotalAddress(latestParamData, responseThings),
        getPanelDetailedData(
          onlyUpdatedThings,
          panelDetailedData,
          props.client_id,
          props.application_id,
          isThermofisher,
          last24HoursDataStore,
        ),
      ]);
      if (Object.keys(updateData).length) {
        updateData = {
          ...updateData,
          ...getDailyDataReqFormat(dailyDataArray),
        };
      } else {
        updateData = getDailyDataReqFormat(dailyDataArray);
      }
      flushSync(() => {
        setAlertData(alertDataArray);
        setFaultData(totalFaultResponse);
        setMaintenanceData(maintenanceData);
        setDailyData(updateData);
        setAddress(addressArray);
        setPanelDetailedData(updatedPanelDetailedData);
        setLast24HoursDataStore(last24HoursDataStoreUpdated);
        setFaultLoading(false);
        setMaintenanceLoading(false);
        setDetailedDataLoading(false);
      });
    }
    offlineArrayData(offlineArray);
  }

  const [search_data, applyFilterSelect, filter_value, setFilter, onUrlChange] =
    useOptions(
      isPanel,
      site_list,
      allThings,
      props.history,
      setPageNumber,
      setInternalPanelLoading,
      bucketTime,
      isAurassure(props.vendor_id),
      defaultApiLoaded,
    );

  let site, category, on_off_status, on_off_device_status;
  if (filter_value) {
    site = filter_value.site;
    category = filter_value.category;
    on_off_status = filter_value.on_off_status;
    on_off_device_status = filter_value.on_off_device_status;
  }

  useEffect(() => {
    if (
      [
        pageNumber,
        site,
        category,
        on_off_status,
        on_off_device_status,
        search_data,
      ].includes(undefined) ||
      [defaultApiLoaded].includes(false)
    ) {
      return;
    }
    if (import.meta.env.VITE_DESKTOP) {
      const interval = setInterval(async () => {
        await fetchDetails(filter_value, search_data, true);
      }, 10000);
      return () => clearInterval(interval);
    } else {
      const initializeAssetList = async () => {
        await fetchDetails(filter_value, search_data);
      };
      initializeAssetList();
    }
  }, [
    pageNumber,
    filter_value.territories?.length,
    site,
    category,
    on_off_status,
    on_off_device_status,
    search_data,
    defaultApiLoaded,
  ]);

  function updateEntityDetails(payload) {
    if (
      payload.entity_type === "thing" &&
      !isNaN(parseInt(payload.entity_id)) &&
      payload.details
    ) {
      if (list_data.things && list_data.things.length) {
        let totalDataResponse = JSON.parse(JSON.stringify(list_data));
        let thingDetailsIndex = _findIndex(totalDataResponse.things, {
          id: parseInt(payload.entity_id),
        });
        if (
          thingDetailsIndex > -1 &&
          totalDataResponse &&
          totalDataResponse.things
        ) {
          totalDataResponse.things[thingDetailsIndex] = _merge(
            {},
            totalDataResponse.things[thingDetailsIndex],
            payload.details,
          );
        }
        setList(totalDataResponse);
      }
    }
  }

  function getSocketData() {
    if (socket_value) {
      disconnectSocketConnection(socket_value);
    }
    clearInterval(bucketTime);
    let socket = establishSocketConnection();
    setSocket(socket);
    socket.on("connect", () => {
      subscribeForThingsUpdates(socket, modified_list_data?.all_thing_ids, 0);
      if(isThermofisher) {
        subscribeForThingsUpdates(socket, modified_list_data?.all_thing_ids, 900);
      }
      subscribeForEntityUpdates(
        socket,
        modified_list_data?.all_thing_ids,
        "thing",
      );
    });

    const bucketTimeInterval = getSocketInterval(
      modified_list_data?.all_thing_ids?.length || 0,
    );

    bucketTime = setInterval(() => {
      if (Object.keys(bucket.raw_data).length) {
        realTimeDataFunc(
          bucket.raw_data,
          getOnlineOfflineArray(list_data),
          latest_data,
          list_data,
          modified_list_data,
          setLatestData,
          props.client_id,
          detailedDataLoading ? () => {} : updateLast24HrMinMax,
        );
      }
      bucket = {
        raw_data: {},
      };
    }, bucketTimeInterval);

    socket.on("new_data_generated_for_station", async (payload) => {
      if (payload && payload.type === "raw") {
        bucket["raw_data"][payload.thing_id] = payload;
      }
      if (isThermofisher && payload && payload.type === "15_min_avg") {
        if (
          asset15MinLastUpdateTime[payload.thing_id] &&
          asset15MinLastUpdateTime[payload.thing_id].time
        ) {
          const previousTime = moment.unix(
            asset15MinLastUpdateTime[payload.thing_id].time,
          );
          const currentTime = moment.unix(payload.time);
          const now = moment();
          const previous15MinStart = now
            .startOf("minute")
            .subtract(now.minutes() % 15, "minutes")
            .subtract(15, "minutes");
          if (
            previousTime
              .startOf("minute")
              .subtract(previousTime.minutes() % 15, "minutes")
              .unix() <
            currentTime
              .startOf("minute")
              .subtract(currentTime.minutes() % 15, "minutes")
              .unix()
          ) {
            updatePanelDetailedData(asset15MinLastUpdateTime[payload.thing_id]);
          } else if (previous15MinStart.unix() === payload.time) {
            updatePanelDetailedData(payload);
          }
        }
        asset15MinLastUpdateTime[payload.thing_id] = payload;
      }
    });
    let totalThing = JSON.parse(JSON.stringify(list_data));
    socket.on("details_updated", (payload) => {
      updateEntityDetails(payload);
    });
  }

  function updatePanelDetailedData(newData) {
    if (!newData || !newData?.thing_id || !newData?.data || detailedDataLoading) return;

    const { thing_id, data } = newData;
    const updatedData = { ...panelDetailedData };

    if (updatedData[thing_id]) {
      for (const [key, value] of Object.entries(data)) {
        if (updatedData[thing_id][key]) {
          updatedData[thing_id][key].last_15_mins_avg = value.avg
            ? value.avg.toFixed(2)
            : "";
        }
      }
    }

    setPanelDetailedData(updatedData);
  }

  function updateLast24HrMinMax(newData) {
    if (
      !isThermofisher ||
      !newData ||
      Object.keys(panelDetailedData).length === 0
    )
      return;

    const updatedDataPanel = { ...panelDetailedData };
    const updatedDataStore = JSON.parse(JSON.stringify(last24HoursDataStore));
    console.log("24_hr_min_max", newData);
    Object.keys(newData).forEach((thing_id) => {
      if (!updatedDataPanel[thing_id]) return;

      const { result, updatedDataStore: assetUpdatedStore } =
        getMinMaxParameters(last24HoursDataStore[thing_id], newData[thing_id]);
      updatedDataStore[thing_id] = assetUpdatedStore;
      for (const [key, value] of Object.entries(result)) {
        if (updatedDataPanel[thing_id][key]) {
          updatedDataPanel[thing_id][key].last_24_hrs_min = getFinalValue(
            value.min,
          );
          updatedDataPanel[thing_id][key].last_24_hrs_max = getFinalValue(
            value.max,
          );
        }
      }
    });

    setPanelDetailedData(updatedDataPanel);
    setLast24HoursDataStore(updatedDataStore);
  }

  useEffect(() => {
    getSocketData();
  }, [modified_list_data, list_data, latest_data]);
  return [
    site_list,
    allThings,
    list_data,
    application_list,
    modified_list_data,
    latest_data,
    fault_data,
    maintenance_data,
    address_data,
    daily_data,
    alert_data,
    loading,
    socket_value,
    fault_loading,
    maintenance_loading,
    daily_data_loading,
    camera_details,
    aqi_data,
    aqi_loading,
    search_data,
    applyFilterSelect,
    filter_value,
    setFilter,
    onUrlChange,
    loadMoreBtnLoading,
    isPanel,
    internal_panel_loading,
    bucketTime,
    territoryList,
    panelDetailedData,
  ];
};
async function getAlerts(
  client_id,
  application_id,
  list_api,
  onlyUpdatedThings,
) {
  let alertDataArray = [];
  if (onlyUpdatedThings?.length) {
    let getAllData = [];
    onlyUpdatedThings.forEach((thing) => {
      let findThingCat = list_api.things_categories?.find(
        (cat) => cat.id === thing.category,
      );
      if (findThingCat?.threshold) {
        getAllData.push({
          id: thing.id,
          data: () => retriveAlerts(client_id, application_id, thing.id),
        });
      }
    });
    alertDataArray = await Promise.all(
      getAllData.map(async (a) => ({ id: a.id, data: await a.data() })),
    );
  }
  return alertDataArray;
}

async function getDailyData(
  client_id,
  application_id,
  list_api,
  daily_data,
  onlyUpdatedThings,
  setDailyDataLoading,
  polling,
) {
  let dailyDataArray = [];
  if (onlyUpdatedThings?.length) {
    let getAllData = {};
    await Promise.all(
      onlyUpdatedThings.map(async (things) => {
        if (!getAllData[things.id]) {
          getAllData[things.id] = [];
        }
        setDailyDataLoading((prevState) => ({
          ...prevState,
          [things.id]: true,
        }));
        let findThingCat = list_api.things_categories?.find(
          (cat) => cat.id === things.category,
        );
        let totalSummaryParams;
        if (findThingCat?.pages?.panel?.summary_value?.length) {
          totalSummaryParams = JSON.parse(
            JSON.stringify(findThingCat?.pages?.panel?.summary_value),
          );
          if (findThingCat?.pages?.list?.summary_value?.length) {
            findThingCat.pages.list.summary_value.map((panelSummary) => {
              const findExactJson = _find(totalSummaryParams, function (o) {
                return (
                  o.duration === panelSummary.duration &&
                  o.parameter === panelSummary.parameter &&
                  o.attribute === panelSummary.attribute
                );
              });
              if (findExactJson === undefined) {
                totalSummaryParams.push(panelSummary);
              }
            });
          }
        }
        if (
          findThingCat?.id === 18 &&
          !_find(things?.parameters, { key: "mn_hlth_st" })
        ) {
          totalSummaryParams.filter(
            (param) => param.parameter !== "calculated_mains_runhour",
          );
        }
        if (totalSummaryParams?.length) {
          await Promise.all(
            totalSummaryParams.map(async (summary_values) => {
              const fromTime =
                summary_values.duration === "today"
                  ? moment().startOf("day").unix()
                  : summary_values.duration === "yesterday"
                    ? moment().subtract(1, "day").startOf("day").unix()
                    : summary_values.duration === "last_month"
                      ? moment().subtract(1, "month").startOf("month").unix()
                      : moment().startOf("month").unix();
              const uptoTime =
                summary_values.duration === "today"
                  ? moment().endOf("day").unix()
                  : summary_values.duration === "yesterday"
                    ? moment().subtract(1, "day").endOf("day").unix()
                    : summary_values.duration === "last_month"
                      ? moment().subtract(1, "month").endOf("month").unix()
                      : moment().endOf("month").unix();
              const aggrPeriod = ["today", "yesterday"].includes(
                summary_values.duration,
              )
                ? 86400
                : 2592000;
              let dataPacketAvg = {
                data_type: "aggregate",
                aggregation_period: aggrPeriod,
                parameters: [summary_values.parameter],
                parameter_attributes: [summary_values.attribute],
                things: [things.id],
                from_time: fromTime,
                upto_time: uptoTime,
              };
              const apiData = await getThingsData(
                dataPacketAvg,
                client_id,
                application_id,
              );
              getAllData[things.id].push({
                type: summary_values.type,
                attribute: summary_values.attribute,
                duration: summary_values.duration,
                parameter: summary_values.parameter,
                thingId: things.id,
                apiData,
              });
            }),
          );
        }
      }),
    );
    if (Object.keys(getAllData).length) {
      Object.values(getAllData).map((data) => {
        dailyDataArray.push(...data);
        if (data?.length) {
          setDailyDataLoading((prevState) => ({
            ...prevState,
            [data[0].thingId]: false,
          }));
        }
      });
    }
  }
  return dailyDataArray;
}

function realTimeDataFunc(
  payload,
  offlineArray,
  latestParameterData,
  totalData,
  modifiedResponse,
  setLatestData,
  client_id,
  updateLast24HrMinMax,
) {
  let latestParamData;
  let realtimeThingsData = JSON.parse(JSON.stringify(latestParameterData));
  if (payload && Object.keys(payload).length) {
    Object.keys(payload).map((payload_key) => {
      let findIndex = _findIndex(realtimeThingsData, {
        thing_id: parseInt(payload_key),
      });
      let findTotalData = _find(totalData.things, {
        id: parseInt(payload_key),
      });
      let findParamInd = -1;
      if (findIndex > -1) {
        if (
          Object.keys(payload[payload_key].data) &&
          Object.keys(payload[payload_key].data).length
        ) {
          Object.keys(payload[payload_key].data).map((key) => {
            if (findTotalData && Array.isArray(findTotalData.parameters)) {
              findParamInd = _findIndex(findTotalData.parameters, {
                key: key,
              });
            }
            if (findTotalData?.category === 18 && key === "fuel") {
              if (
                payload[payload_key].data["fuel_raw"] &&
                !isNaN(payload[payload_key].data["fuel_raw"])
              ) {
                realtimeThingsData[findIndex]["data"][key] =
                  payload[payload_key].data["fuel_raw"];
              } else {
                realtimeThingsData[findIndex]["data"][key] =
                  payload[payload_key].data[key];
              }
            } else if (findTotalData?.category === 18 && key === "fuel") {
              if (payload[payload_key].data["fuel_litre"]) {
                const capacity = findTotalData?.thing_details?.capacity || 0;
                realtimeThingsData[findIndex]["data"][key] =
                  parseFloat(capacity) > 0
                    ? (parseFloat(payload[payload_key].data["fuel_litre"]) *
                        100) /
                      parseFloat(capacity)
                    : "";
              } else {
                realtimeThingsData[findIndex]["data"][key] =
                  payload[payload_key].data[key];
              }
            } else {
              if (findParamInd > -1) {
                realtimeThingsData[findIndex]["data"][key] =
                  payload[payload_key].data[key];
              }
            }
          });
        }
        realtimeThingsData[findIndex]["time"] = payload[payload_key].time;
      }
      latestParamData = offlineTimeOutFunction(
        payload[payload_key],
        offlineArray,
        totalData,
        realtimeThingsData,
        modifiedResponse,
        client_id,
      );
    });
    setLatestData(latestParamData);
    updateLast24HrMinMax(payload);
  }
}

const getDailyDataReqFormat = (dailyDataArray) => {
  let finalObj = {};
  if (dailyDataArray?.length) {
    dailyDataArray.map((dailyData) => {
      if (!finalObj[dailyData.thingId]) {
        finalObj[dailyData.thingId] = {};
      }
      if (!finalObj[dailyData.thingId][dailyData.duration]) {
        finalObj[dailyData.thingId][dailyData.duration] = {};
      }
      if (
        !finalObj[dailyData.thingId][dailyData.duration][dailyData.parameter]
      ) {
        finalObj[dailyData.thingId][dailyData.duration][dailyData.parameter] = {
          value: 0,
          total_data_length: dailyDataArray?.length,
        };
      }
      if (dailyData?.apiData?.data?.length) {
        dailyData.apiData.data.map((apiVal) => {
          if (
            apiVal?.parameter_values?.[dailyData.parameter]?.[
              dailyData.attribute
            ]
          ) {
            finalObj[dailyData.thingId][dailyData.duration][
              dailyData.parameter
            ]["value"] +=
              apiVal.parameter_values[dailyData.parameter][dailyData.attribute];
          }
        });
      } else {
        finalObj[dailyData.thingId][dailyData.duration][dailyData.parameter][
          "value"
        ] = "NA";
      }
    });
  }
  return finalObj;
};

async function getTotalAddress(latestParamData, list_api, onlyUpdatedThings) {
  let addressArray = [];
  if (onlyUpdatedThings?.length) {
    let getAllAddress = [];
    onlyUpdatedThings.map((thing) => {
      let selectedThinglatestParam = _find(latestParamData, {
        thing_id: thing.id,
      });
      if (selectedThinglatestParam) {
        getAllAddress.push(() =>
          fetchAddress(
            getAddressFromLat(selectedThinglatestParam, thing),
            thing.id,
          ),
        );
      }
    });
    addressArray = await Promise.all(getAllAddress.map((a) => a()));
  }
  return addressArray;
}
async function getTotalFault(
  client_id,
  application_id,
  list_api,
  onlyUpdatedThings,
) {
  let totalFaultResponse = {};
  if (!totalFaultResponse) {
    totalFaultResponse = {};
  }
  let faultArray = [];
  if (onlyUpdatedThings?.length) {
    let getAllfaults = onlyUpdatedThings.map((things) => {
      return () =>
        retriveEventsData(
          {
            client_id: client_id,
            application_id: application_id,
          },
          "?get_details=true&generated_after=" +
            moment().subtract(30, "days").startOf("day").unix() +
            "&generated_before=" +
            moment().endOf("day").unix() +
            "&entity_type=thing&entity_id=" +
            things.id,
        );
    });
    faultArray = await Promise.all(getAllfaults.map((a) => a()));
  }
  if (faultArray && faultArray.length) {
    faultArray.map((faultApi) => {
      if (faultApi.response.status === "success") {
        if (faultApi.response.events && faultApi.response.events.length) {
          faultApi.response.events.map((events) => {
            if (!totalFaultResponse[events.entity_id]) {
              totalFaultResponse[events.entity_id] = [];
            }
            if (events.type === 1) {
              totalFaultResponse[events.entity_id].push({
                name: events.message,
              });
            }
          });
        }
      }
    });
  }
  return totalFaultResponse;
}

async function fetchMaintenance(client_id, onlyUpdatedThings) {
  let data = await serviceAlertFetch(client_id);
  return data;
}

const getOnlineOfflineArray = (totalData) => {
  let offlineArray = [];
  const totalDataArray = [...totalData?.things];
  if (totalDataArray?.length) {
    totalDataArray.map((thing) => {
      offlineArray.push({
        thing_id: thing.id,
        status: thing.status,
      });
    });
  }
  return offlineArray;
};

async function aqiDataCall(
  latestParamData,
  client_id,
  application_id,
  list_api,
  onlyUpdatedThings,
) {
  let totalAqiResponse = {};
  let aqiDataArray = [];
  if (onlyUpdatedThings?.length) {
    let getAqiData = onlyUpdatedThings.map((things) => {
      let findLatestData = _find(latestParamData, {
        thing_id: things.id,
      });
      return () =>
        getThingsData(
          {
            data_type: "aggregate",
            aggregation_period: 3600,
            parameters: ["aqi"],
            parameter_attributes: ["value", "responsible_parameter"],
            things: [parseInt(things.id)],
            from_time:
              findLatestData?.on_off_moving_status === "2"
                ? moment.unix(findLatestData?.time).startOf("hour").unix()
                : moment().startOf("hour").unix(),
            upto_time:
              findLatestData?.on_off_moving_status === "2"
                ? moment.unix(findLatestData?.time).endOf("hour").unix()
                : moment().endOf("hour").unix(),
          },
          client_id,
          application_id,
        );
    });
    aqiDataArray = await Promise.all(getAqiData.map((a) => a()));
  }
  if (aqiDataArray && aqiDataArray.length) {
    aqiDataArray.map((aqiData) => {
      if (aqiData.status === "success") {
        if (aqiData.data && aqiData.data.length) {
          aqiData.data.map((data) => {
            if (!totalAqiResponse[data.thing_id]) {
              totalAqiResponse[data.thing_id] = [];
            }
            totalAqiResponse[data.thing_id].push(data);
          });
        }
      }
    });
  }
  return totalAqiResponse;
}

function getLatestParameterData(thingsListData = []) {
  return thingsListData.map((thing) => {
    const parameterDataKeyValueObject = {};

    thing.parameters?.forEach((param) => {
      parameterDataKeyValueObject[param.key] = param.value ?? "";
    });

    return {
      thing_id: thing.id,
      time: thing.last_data_received_time,
      type: "raw",
      data: parameterDataKeyValueObject,
    };
  });
}


const offlineTimeOutFunction = (
  payload = undefined,
  offlineArray,
  totalData,
  lastdata,
  response,
  client_id,
) => {
  let latestDataArray = [],
    latestData = {},
    onOffStatus = {};
  let sortedThingsdata = _sortBy(lastdata, ["time"]);
  totalData.things.map((things) => {
    let filteredLatestData = _filter(sortedThingsdata, {
      thing_id: things.id,
    });
    if(!filteredLatestData.length) {
      filteredLatestData = getLatestParameterData([things])
    }
    if (!latestData[things.id]) {
      latestData[things.id] =
        filteredLatestData[filteredLatestData.length - 1] || {};
    }
    latestData[things.id]["category"] = things?.category ? things.category : "";
    latestData[things.id]["name"] = things?.name;
    let isMcSt =
      things?.parameters && _find(things.parameters, { key: "mc_st" })
        ? latestData[things.id]?.data?.["mc_st"]
        : things?.status === "offline"
          ? "2"
          : "1";
    if (!onOffStatus[things.id]) {
      onOffStatus[things.id] = isMcSt;
    }
    onOffStatus[things.id] = parseInt(isMcSt)
      ? parseInt(isMcSt).toString()
      : "0";
    latestDataArray.push(latestData[things.id]);
    let findindex = _findIndex(latestDataArray, {
      thing_id: things.id,
    });
    if (findindex > -1) {
      latestDataArray[findindex]["on_off_moving_status"] =
        checkOfflineOnlineStatus(
          onOffStatus[things.id],
          offlineArray,
          things.id,
        );
    }
  });
  ipLatestData(totalData, latestDataArray, payload, client_id);
  return latestDataArray;
};

const checkOfflineOnlineStatus = (OnOffStatus, offlineArray, thing_id) => {
  let offlineStatus = _find(offlineArray, {
    thing_id,
  })?.status;
  return offlineStatus === "offline" ? "2" : OnOffStatus;
};

const getPanelDetailedData = async (
  updatedThings = [],
  assetWiseDetialedData,
  client_id,
  application_id,
  isThermofisher,
  last24HoursDataStore,
) => {
  const newData = assetWiseDetialedData ? { ...assetWiseDetialedData } : {};
  if (!updatedThings?.length || !isThermofisher) return newData;
  
  const existingAssetIds = Object.keys(newData).map(Number);
  const last24HoursDataStoreUpdated = JSON.parse(
    JSON.stringify(last24HoursDataStore),
  );
  await Promise.all(
    updatedThings.map(async (thing) => {
      const { id, parameters, category } = thing;

      if (
        !parameters?.length ||
        existingAssetIds.includes(id) ||
        ![21, 22, 102, 23].includes(category)
      )
        return;

      const finalParameters = [];
      parameters.forEach((param) => {
        if (param.allowed_visualizations?.includes("average")) {
          finalParameters.push(param.key);
        }
      });

      if (!finalParameters.length) return;

      const last24Hours = {
        data_type: "raw",
        aggregation_period: 0,
        parameters: finalParameters,
        parameter_attributes: [],
        things: [id],
        from_time: moment().subtract(24, "hours").unix(),
        upto_time: moment().unix(),
      };
      const last15Mins = {
        data_type: "aggregate",
        aggregation_period: 900,
        parameters: finalParameters,
        parameter_attributes: ["avg"],
        things: [id],
        from_time: moment().subtract(30, "minutes").unix(),
        upto_time: moment().unix(),
      };
      console.log("thermofish apicalled", id);
      const [last24HoursData, last15MinsData] = await Promise.all([
        getThingsData(last24Hours, client_id, application_id),
        getThingsData(last15Mins, client_id, application_id),
      ]);

      newData[id] = {};

      last24HoursDataStoreUpdated[id] = last24HoursData?.data;
      const { result: minMaxValues } = getMinMaxParameters(
        last24HoursData?.data,
      );
      const last15MinsDataValues = last15MinsData?.data?.[0]?.parameter_values;
      finalParameters.forEach((param) => {
        newData[id][param] = {
          last_24_hrs_min: getFinalValue(minMaxValues[param]?.min),
          last_24_hrs_max: getFinalValue(minMaxValues[param]?.max),
          last_15_mins_avg: getFinalValue(last15MinsDataValues?.[param]?.avg),
        };
      });
    }),
  );

  return { newData, last24HoursDataStoreUpdated };
};

function getFinalValue(val) {
  if (isNaN(parseFloat(val))) {
    return "NA";
  }

  const numericValue = parseFloat(val);

  if (numericValue === 0 || Number.isInteger(numericValue)) {
    return numericValue.toString();
  }
  return numericValue.toFixed(2);
}


function getMinMaxParameters(dataStore = [], newData) {
  const result = {};
  const updatedDataStore = [];
  if (newData) {
    dataStore.push({
      time: newData.time,
      parameter_values: newData.data,
    });
  }
  dataStore.forEach((item) => {
    if (item.time <= moment().unix() - 86400) {
      return;
    }
    updatedDataStore.push(item);

    const parameters = item.parameter_values;

    for (const [key, value] of Object.entries(parameters)) {
      const numericValue = parseFloat(value);

      if (isNaN(numericValue)) continue;

      if (!result[key]) {
        result[key] = { min: numericValue, max: numericValue };
      } else {
        result[key].min = Math.min(result[key].min, numericValue);
        result[key].max = Math.max(result[key].max, numericValue);
      }
    }
  });

  return { result, updatedDataStore };
}
