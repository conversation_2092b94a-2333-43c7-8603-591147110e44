import React from "react";
import showActiveFault from "../component/ShowActiveFaults";
import MaintenanceText from "../component/MaintenanceText";
import _find from "lodash/find";
import _filter from "lodash/filter";
import _uniqBy from "lodash/uniqBy";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import AntSpin from "@datoms/react-components/src/components/AntSpin";
import OnOffCompositeSwitch from "../component/OnOffComopsiteSwitch";
import getOnlineOfflineOnlyStatus from "./status";
import OpenMaps from "@datoms/react-components/src/components/OpenMaps";
import { ParamUnit, ParamName } from "../../data_handling/ParamNameUnitFind";
import ImageComponent from "@datoms/react-components/src/components/ImageComponent";
import {
  aqiParameters,
  getAqiColorStatusForValue,
} from "../logic/aqiParametersToShow";
import { isAurassure, aurassureThingCat } from "./isAurassure";

export const usePanelTable = (
  props,
  modifiedThingsResponse,
  panel_data,
  list,
  application_list,
  fault_loading,
  maintenance_loading,
  daily_data_loading,
  aqi_loading,
  socket,
  goToPage,
  pageNumber
) => {
  let findCat = {},
    avlRealTimeParams = [];
  if (panel_data && panel_data.length) {
    findCat = _find(list.things_categories, {
      id: panel_data[0]?.category,
    });
    const arrays = panel_data.map((obj) => obj.real_time_params);
    const flattenedArray = [].concat(...arrays);
    const uniqueSet = _uniqBy(flattenedArray, "key");
    avlRealTimeParams = Array.from(uniqueSet);
  }
  
  let column = [
    {
      title: props.t('asset_name'),
      // title: "Asset Name",
      dataIndex: "thing_name",
      align: "left",
      fixed: "left",
      width: 500,
      render: (text, record) => {
        return (
          <div
            className="thing-header-details"
            onClick={() =>
              record.is_detailed_view &&
              typeof goToPage === "function" &&
              goToPage("detailed-view", record.id)
            }
          >
            <div>
              {isAurassure(props?.vendor_id) ? (
                <ImageComponent
                  title={`Category: ${text?.categoryName}`}
                  iconSize="large"
                  background={"white"}
                  show_status={false}
                  status={text?.status === "1" ? "online" : "offline"}
                  src={
                    getAqiColorStatusForValue(                      
                      undefined, // aqi_value,
                      text.catetgory,
                      text.status,
                    )?.aqiMapIcon
                  }
                  deviceStatus={
                    text?.status === "1"
                      ? "online"
                      : "offline"
                  } 
                />
              ) : (
                <ImageComponent
                  title={`Category: ${text?.categoryName}`}
                  background={"white"}
                  show_status={true}
                  tooltip={true}
                  src={text?.icon}
                  category={text.category}
                  assetHasStatus={text?.status_option_includes_stopped}
                  status={
                    !text?.status_option_includes_stopped
                      ? getOnlineOfflineOnlyStatus(text?.status) === "online"
                        ? "running"
                        : "offline"
                      : text?.status === "1"
                        ? "running"
                        : text?.status === "2"
                          ? "offline"
                          : "stopped"
                  }
                  deviceStatus={text?.deviceStatus}
                  faultStatus={text.faultStatus}
                />
              )}
            </div>
            <div className="name-date">
              <AntTooltip title={text?.name}>
                <div className="thing-name hellip">{text?.name}</div>
              </AntTooltip>
              <div className="time">{text?.date}</div>
              <div className="make-model-div">
                {text.make ? (
                  <AntTooltip title={"Make: " + text.make}>
                    <div className="text-div hellip">
                      {"Make: " + text.make}
                    </div>
                  </AntTooltip>
                ) : (
                  ""
                )}
                {text.model ? (
                  <AntTooltip title={"Model: " + text.model}>
                    <div className="text-div hellip">
                      {"Model: " + text.model}
                    </div>
                  </AntTooltip>
                ) : (
                  ""
                )}
              </div>
            </div>
          </div>
        );
      },
    },
  ];
  if (findCat?.status_options?.includes?.("Stopped")) {
    column.push({
      title: "Status",
      dataIndex: "status",
      align: "left",
      render: (text) => {
        return text?.status_option_includes_stopped ? (
          <OnOffCompositeSwitch
            deviceStatus={text?.deviceStatus}
            dgStatus={text?.on_off_moving_status}
            socket={socket}
            client_id={props?.client_id}
            application_id={props?.application_id}
            thingId={text?.id}
            commandStatus={text?.command_status}
            isLockControlEnabled={text?.is_lock_control_enabled}
            isControlEnabled={text?.is_control_enabled}
            operation_mode={text?.operation_mode}
            dg_lock_status={text?.dg_lock_status}
            getRemoteLockAccess={
              text.show_lock ? props?.getRemoteLockAccess() : false
            }
            getRemoteAccess={
              text.show_switch ? props?.getRemoteAccess() : false
            }
            category_id={findCat.id}
          />
        ) : (
          ""
        );
      },
    });
  }
  const summaryValue =
    findCat?.pages?.list?.summary_value || findCat?.pages?.panel?.summary_value;
  if (summaryValue?.length) {
    summaryValue.map((summary) => {
      const findParam = _find(modifiedThingsResponse?.param_data, {
        key: summary.parameter,
      });
      if (findParam) {
        column.push({
          width: 200,
          title: (
            <>
              <span style={{ "font-weight": 600, "font-size": 14 }}>
                {(summary.duration === "this_month"
                  ? "This Month's "
                  : summary.duration.charAt(0).toUpperCase() +
                    summary.duration.slice(1) +
                    "'s ") +
                  (aurassureThingCat(findCat?.id, props.vendor_id)
                    ? findParam.name
                    : summary.name ||
                      ParamName(
                        summary.parameter,
                        modifiedThingsResponse?.param_data,
                      ))}
              </span>
              <br />
              <span
                style={{
                  "font-weight": "normal",
                  "font-size": 13,
                }}
              >
                {findParam.unit?.length
                  ? "(" +
                    (aurassureThingCat(findCat?.id, props.vendor_id)
                      ? findParam.unit
                      : ParamUnit(findParam.unit)) +
                    ")"
                  : ""}
              </span>
            </>
          ),
          dataIndex: summary.duration + "_summary_" + summary.parameter,
          render: (text) => {
            return daily_data_loading[text.thing_id] ? (
              <AntSpin />
            ) : (
              <>{text.value}</>
            );
          },
          align: "center",
        });
      }
    });
  }
  if (avlRealTimeParams?.length) {
    avlRealTimeParams.map((params) => {
      const findParam = _find(modifiedThingsResponse?.param_data, {
        key: params.key,
      });
      if (
        findParam &&
        ![
          "mc_st",
          "rnhr",
          "rnmin",
          "mrun_hr",
          "ser_due_hr",
          "au_m_stat",
          "fuel_consumption",
          "fuel_filled",
          "fuel_consume",
          "fuel_theft",
          "calculated_runhour",
          "data_availability",
          "calculated_energy",
          "dg_lock_status",
          "fuel_raw",
          "calculated_mains_energy",
          "fuel_consumption_rate",
          "raw_mc_st",
          "calculated_mr_energy",
          "calculated_ma_energy",
          "calculated_ga_energy",
          "calculated_gr_energy",
          "fuel_filling_start_value",
          "fuel_filling_end_value",
          "fuel_filling_start_time",
          "fuel_filling_end_time",
          "fuel_drain_start_value",
          "fuel_drain_end_value",
          "fuel_drain_start_time",
          "fuel_drain_end_time",
          "fluctuation",
          "data_is_artificial",
          "fuel_level",
          "fuel_volt",
        ].includes(params.key)
      ) {
        column.push({
          width: 180,
          title: () => (
            <div className="param-col">
              <span style={{ "font-weight": 600, "font-size": 14 }}>
                {aurassureThingCat(findCat?.id, props.vendor_id)
                  ? findParam.name
                  : params.name ||
                    ParamName(params.key, modifiedThingsResponse?.param_data)}
              </span>
              <br />
              <span
                style={{
                  "font-weight": "normal",
                  "font-size": 13,
                }}
              >
                {findParam.unit?.length
                  ? "(" +
                    (aurassureThingCat(findCat?.id, props.vendor_id)
                      ? findParam.unit
                      : ParamUnit(findParam.unit)) +
                    ")"
                  : ""}
              </span>
            </div>
          ),
          dataIndex: params.key,
          align: "center",
          render: (text) => {
            let upperThreshold, lowerThreshold;
            upperThreshold = text.upperThreshold;
            lowerThreshold = text.lowerThreshold;
            return (
              // <div className="param-table-data danger">
              <AntTooltip title={text.tooltipForParam}>
                <div
                  className={
                    "param-table-data " +
                    (parseFloat(text.value) > parseFloat(upperThreshold) ||
                    parseFloat(text.value) < parseFloat(lowerThreshold)
                      ? "danger"
                      : "")
                  }
                >
                  {isAurassure(props?.vendor_id) ? (
                    <div
                      className="aurassure-aqi"
                      style={{
                        background:
                          aqiParameters()?.aqiParam?.includes(params.key) &&
                          parseFloat(text.value) > 0
                            ? getAqiColorStatusForValue(parseFloat(text.value))
                                ?.color
                            : "",
                      }}
                    ></div>
                  ) : (
                    ""
                  )}
                  <div
                    className={text.threshold.length ? "threshold-true" : ""}
                  >
                    {text.value}
                    {text.threshold.length ? (
                      <div className="threshold">
                        {"Threshold: " + text.threshold}
                      </div>
                    ) : (
                      ""
                    )}
                  </div>
                </div>
              </AntTooltip>
            );
          },
        });
      }
    });
  }
  let dataSource = [];
  let ind = 0;
  if (panel_data && panel_data.length) {
    panel_data.forEach((data) => {
      if(data.page_number !== pageNumber && pageNumber !== 1) return;
      dataSource.push({
        is_detailed_view: data?.is_detailed_view,
        is_real_time: data?.is_real_time,
        id: data.id,
        thing_name: {
          deviceStatus: data?.deviceStatus,
          icon: data.icon,
          faultStatus: data?.faultStatus,
          name: data.name,
          catetgory: data.category,
          categoryName: data.categoryName,
          status: data.on_off_moving_status,
          status_option_includes_stopped: data.status_option_includes_stopped,
          date: data.date,
          make: _find(data.thing_details, { key: "make" })?.value
            ? _find(data.thing_details, { key: "make" }).value
            : undefined,
          model: _find(data.thing_details, { key: "model" })?.value
            ? _find(data.thing_details, { key: "model" }).value
            : undefined,
        },
        status: {
          deviceStatus: data?.deviceStatus,
          status_option_includes_stopped: data.status_option_includes_stopped,
          on_off_moving_status: data.on_off_moving_status,
          id: data.id,
          command_status: data.command_status,
          is_control_enabled: data.is_control_enabled,
          is_lock_control_enabled: data.is_lock_control_enabled,
          operation_mode: data.operation_mode,
          dg_lock_status: data.dg_lock_status,
          show_switch: data.show_switch,
          show_lock: data.show_lock,
        },
        health: {
          location: data.location,
          lat: data.lat,
          lng: data.lng,
          violation: data.last_fault_data,
          maintenance: data.maintenance,
          category: data.category,
        },
      });
      if (summaryValue?.length) {
        summaryValue.map((summary) => {
          dataSource[ind][summary?.duration + "_summary_" + summary?.parameter] =
            {
              value:
                _find(data?.list_summary_params, function (o) {
                  return (
                    o.key === summary?.parameter &&
                    o.param_duration === summary?.duration
                  );
                })?.param_value || "-",
              thing_id: data.id,
            };
        });
      }
      if (avlRealTimeParams?.length) {
        avlRealTimeParams.map((params) => {
          const findParams = _find(data.real_time_params, {
            key: params.key,
          });
          const findParamForThings = _find(data.allParams, {
            key: params.key,
          });
          const tooltipForParam = findParamForThings?.param_details?.description
            ?.length
            ? `Description: ${findParamForThings.param_details.description}`
            : undefined;
          dataSource[ind][params.key] = {
            value: findParams?.value || "-",
            threshold: findCat.threshold
              ? findParams?.thresholdString?.toString() || ""
              : "",
            upperThreshold: findCat.threshold
              ? findParams?.upperThreshold?.toString() || ""
              : "",
            lowerThreshold: findCat.threshold
              ? findParams?.lowerThreshold?.toString() || ""
              : "",
            tooltipForParam,
          };
        });
      }
      ind++;
    });
  }
  if (findCat?.no_list) {
    column = [];
  }
  return [column, dataSource];
};
