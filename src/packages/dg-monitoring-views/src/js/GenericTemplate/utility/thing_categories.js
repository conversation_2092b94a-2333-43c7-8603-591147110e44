import EnergyMapSwitchOn from "../images/EnergyMeter-Online-Icon.svg";
import EnergyMapNotConnected from "../images/EnergyMeter-NotConnected-Icon.svg";
import EnergyMapOffline from "../images/EnergyMeter-Offline-Icon.svg";
import ElectricalMachinesMapSwitchOn from "../images/AC-Electrical-Appliace-Online-Icon.svg";
import ElectricalMachinesMapNotConnected from "../images/AC-Electrical-Appliace-Not-Connected-Icon.svg";
import ElectricalMachinesMapOffline from "../images/AC-Electrical-Appliance-Offline-Icon.svg";
import ExhaustFanOfflineIcon from "../images/ExhaustFan-OfflineIcon.svg";
import ExhaustFanOnlineMapIcon from "../images/ExhaustFan-OnlineMapIcon.svg";
import ExhaustFanOnlineStoppedIcon from "../images/ExhaustFan-OnlineStoppedIcon.svg";
import ExhaustFanOnlineNotConnectedIcon from "../images/ExhaustFan-OnlineNotConnectedIcon.svg";
import ExhaustFanRunning from "../images/ExhaustFan-Running.svg";
import ExhaustFanTransparent from "../images/ExhaustFan-Transparent.svg";
import ExhaustFanUnlockIcon from "../images/ExhaustFan-UnlockIcon.svg";
import DGMapOffline from "../images/DGSet-MapIcons-Offline.svg";
import DGMapSwitchOff from "../images/DGSet-MapIcons-Stopped.svg";
import DGMapNotConnected from "../images/DGSet-MapIcons-NotConnected.svg";
import DGMapOnline from "../images/DG-Icon-Online.svg";
import DGMapOnlineTrip from "../../GenericTemplate/images/DG-Online-Running-Trip-Icon.svg";
import DGMapOfflineTrip from "../../GenericTemplate/images/DG-Offline-Trip-Icon.svg";
import DGMapSwitchOffTrip from "../../GenericTemplate/images/DG-Online-Stopped-Trip-Icon.svg";
import DGMapNotConnectedTrip from "../../GenericTemplate/images/DG-Online-Disconnected-Trip-Icon.svg";
import DGMapOnlineWarning from "../../GenericTemplate/images/DG-Online-Running-Warning-Icon.svg";
import DGMapOfflineWarning from "../../GenericTemplate/images/DG-Offline-Warning-Icon.svg";
import DGMapSwitchOffWarning from "../../GenericTemplate/images/DG-Online-Stopped-Warning-Icon.svg";
import DGMapNotConnectedWarning from "../../GenericTemplate/images/DG-Online-Disconnected-Warning-Icon.svg";
import DGMapOnlineNotDefined from "../../GenericTemplate/images/DG-Online-Running-Not-Defined-Icon.svg";
import DGMapOfflineNotDefined from "../../GenericTemplate/images/DG-Offline-NotDefined-Icon.svg";
import DGMapSwitchOffNotDefined from "../../GenericTemplate/images/DG-Online-Stopped-NotDefined-Icon.svg";
import DGMapNotConnectedNotDefined from "../../GenericTemplate/images/DG-Online-Disconnected-NotDefined-Icon.svg";
import FuelTankOnline from "../images/FuelTank-Map-OnlineIcon.svg";
import FuelTankOffline from "../images/FuelTank-Map-OfflineIcon.svg";
import FuelTankNotConnected from "../images/FuelTank-Map-NotConnectedIcon.svg";
import CarMapOffline from "../images/Car-Offline-MapIcon.svg";
import CarMapSwitchOff from "../images/Car-Parked-MapIcon.svg";
import CarMapSwitchOn from "../images/Car-Moving-MapIcon.svg";
import EvSwitchOn from "../images/Bike-Online-MovingIcon.svg";
import EvSwitchOff from "../images/Bike-Online-ParkedIcon.svg";
import EvOffline from "../images/Bike-OfflineIcon.svg";
import TankerMapOffline from "../images/TankerTruck-Offline-Icon.svg";
import TankerMapSwitchOff from "../images/TankerTruck-Online-Parked-Icon.svg";
import TankerMapSwitchOn from "../images/TankerTruck-Online-Moving-Icon.svg";
import SolarPanelOngoing from "../images/SolarPanel-Online.svg";
import SolarPanelOffline from "../images/SolarPanel-Offline.svg";
import SolarPanelNotConnected from "../images/SolarPanel-NotConnected.svg";
import FlowMeterMapOffline from "../images/FlowMeter-Offline-Icon.svg";
import FlowMeterMapSwitchOff from "../images/FlowMeter-Not-Connected-Icon.svg";
import FlowMeterMapSwitchOn from "../images/FlowMeter-Online-Icon.svg";
import BorewellMapOffline from "../images/Borewell-OfflineIcon.svg";
import BorewellMapSwitchOff from "../images/Borewell-NotConnected.svg";
import BorewellMapSwitchOn from "../images/Borewell-OnlineIcon.svg";
import TempAndHumidOnline from "../images/Temperature&Humidity-Online-Icon.svg";
import TempAndHumidOffline from "../images/Temperature&Humidity-Offline-Icon.svg";
import TempAndHumidNotConnected from "../images/Temperature&Humidity-NotConnected-Icon.svg";
import ColdStorageOnline from "../images/ColdStorage-Online-Icon.svg";
import ColdStorageOffline from "../images/ColdStorage-Offline-Icon.svg";
import ColdStorageNotConnected from "../images/ColdStorage-NotConnected-Icon.svg";
import BatteryOnline from "../images/Battery-Online-Icon.svg";
import BatteryNotConnected from "../images/Battery-NotConnected-Icon.svg";
import BatteryOffline from "../images/Battery-Offline-Icon.svg";
import FreezerOnline from "../images/Freezer-OnlineIcon.svg";
import FreezerOffline from "../images/Frezzer-OfflineIcon.svg";
import FreezerNotConnected from "../images/Freezer-NotConnected.svg";
import PortableCompressorOnline from "../images/PortableCompressor-Online.svg";
import PortableCompressorOffline from "../images/PortableCompressor-Offline.svg";
import PortableCompressorNotConnected from "../images/PortableCompressor-NotConnected.svg";
import GridOnline from "../images/grid-online.svg";
import GridOffline from "../images/grid-offline.svg";
import GridNotConnected from "../images/grid-not-connected.svg";
import SolarInverterOnline from "../images/solar-inverter-online.svg";
import SolarInverterOffline from "../images/solar-inverter-offline.svg";
import SolarInverterNotConnected from "../images/solar-inverter-not-connected.svg";
import ElevatorOnline from "../images/elevator-online.svg";
import ElevatorOffline from "../images/elevator-offline.svg";
import ElevatorNotConnected from "../images/elevator-not-connected.svg";
import ElevatorFaultOnline from "../images/Ele-Map-Fault-Online.svg";
import ElevatorFaultOffline from "../images/Ele-Map-Fault-Offline.svg";
import ElevatorFaultNotConnected from "../images/Ele-Map-Fault-Disconnected.svg";
import SolarPumpRunning from "../images/SolarPump-Map-Running.svg";
import SolarPumpDisconnected from "../images/SolarPump-Map-Disconnected.svg";
import SolarPumpOffline from "../images/SolarPump-Map-Offline.svg";
import SolarPumpStopped from "../images/SolarPump-Map-Stopped.svg";
import SolarPumpTripRunning from "../images/SolarPump-Trip-Running-Map.svg";
import SolarPumpTripDisconnected from "../images/SolarPump-Trip-Disconnected-Map.svg";
import SolarPumpTripStopped from "../images/SolarPump-Trip-Stopped-Map.svg";
import SolarPumpWarningRunning from "../images/SolarPump-Warning-Running-Map.svg";
import SolarPumpWarningDisconnected from "../images/SolarPump-Warning-Disconnected-Map.svg";
import SolarPumpWarningStopped from "../images/SolarPump-Warning-Stopped-Map.svg";
import SolarPumpNotDefinedRunning from "../images/SolarPump-NotDefined-Running-Map.svg";
import SolarPumpNotDefinedDisconnected from "../images/SolarPump-NotDefined-Disconnected-Map.svg";
import SolarPumpNotDefinedStopped from "../images/SolarPump-NotDefined-Stopped-Map.svg";
import SolarPumpWarningTripRunning from "../images/SolarPump-Warning&Trip-Running-Map.svg";
import SolarPumpWarningTripDisconnected from "../images/SolarPump-Warning&Trip-Disconnected-Map.svg";
import SolarPumpWarningTripStopped from "../images/SolarPump-Warning&Trip-Stopped-Map.svg";
import SistemaBioGensetRunning from "../images/sistema-bio-genset-running.svg";
import SistemaBioGensetOffline from "../images/sistema-bio-genset-offline.svg";
import mriMachineConnected from "../images/mriMachine-Connected.svg";
import mriMachineDisconnected from "../images/mriMachine-Disconnected.svg";
import mriMachineOffline from "../images/mriMachine-Offline.svg";

export const things_categories = [
  {
    id: 18,
    name: "DG Set",
    parent_id: 0,
    show_fault: true,
    show_location: false,
    show_switch: true,
    show_lock: true,
    status_options: ["Running", "Stopped", "Disconnected"],
    req_thing_details: ["capacity", "dg_parameter_type"],
    machine_info: [
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
      {
        label: "KVA",
        key: "kva",
      },
    ],
    pages: {
      assetDashboard: {
        pgBtn: [
          {
            name: "Utilization Insights",
            key: "insights",
          },
        ],
      },
      map: {
        map_icons: {
          online: DGMapOnline,
          offline: DGMapOffline,
          switch_off: DGMapSwitchOff,
          disconnected: DGMapNotConnected,
          online_fault_trip: DGMapOnlineTrip,
          offline_fault_trip: DGMapOfflineTrip,
          switch_off_fault_trip: DGMapSwitchOffTrip,
          disconnected_fault_trip: DGMapNotConnectedTrip,
          online_fault_warning: DGMapOnlineWarning,
          offline_fault_warning: DGMapOfflineWarning,
          switch_off_fault_warning: DGMapSwitchOffWarning,
          disconnected_fault_warning: DGMapNotConnectedWarning,
          online_fault_not_defined: DGMapOnlineNotDefined,
          offline_fault_not_defined: DGMapOfflineNotDefined,
          switch_off_fault_not_defined: DGMapSwitchOffNotDefined,
          disconnected_fault_not_defined: DGMapNotConnectedNotDefined,
        },
      },
      panel: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_runhour",
            attribute: "sum",
            type: "time",
          },
          {
            duration: "today",
            parameter: "fuel_consumption",
            attribute: "sum",
          },
        ],
        real_time_parameters: [
          {
            key: "fuel",
          },
          {
            key: "vbat",
          },
          {
            key: "temp",
          },
          {
            key: "press",
          },
        ],
      },
      list: {
        summary_value: [{
          duration: "today",
          parameter: "calculated_runhour",
          attribute: "sum",
          type: "time",
        },
        {
          duration: "today",
          parameter: "fuel_consumption",
          attribute: "sum",
        }, {
          duration: "today",
          parameter: "calculated_mains_runhour",
          attribute: "sum",
          type: "time",
        },]
      },
      detailed_view: {
        parameter_trend: {
          parameters: [],
        },
      },
      real_time: {
        tabs: [
          { label: "Engine", key: "engine" },
          { label: "Generator", key: "generator" },
          { label: "Mains", key: "mains" },
          { label: "ATS", key: "ats" },
        ],
        parameterDetails: [
          {
            key: "vbat",
            visualization: "meter",
            custom_name: "Battery Voltage",
            type: "small",
            ranges: [],
            min_value: 0,
            max_value: 40,
            tab: "engine",
          },
          {
            key: "rpm",
            visualization: "meter",
            custom_name: "Engine Speed",
            type: "large",
            ranges: [{ color: "#ff0000", ranges: [1600, 2000] }],
            min_value: 0,
            max_value: 2000,
            tab: "engine",
          },
          {
            key: "press",
            visualization: "meter",
            custom_name: "Lube Oil Pressure",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [8, 10] }],
            min_value: 0,
            max_value: 10,
            tab: "engine",
          },
          {
            key: "fuel",
            visualization: "fuelGraph",
            type: "fuelGraph",
            tab: "engine",
          },
          {
            key: "fuel_press",
            visualization: "meter",
            custom_name: "Fuel Pressure",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [0, 20] }],
            min_value: 0,
            max_value: 100,
            tab: "engine",
          },
          {
            key: "temp",
            visualization: "meter",
            custom_name: "Coolant Temperature",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [100, 120] }],
            min_value: 0,
            max_value: 120,
            tab: "engine",
          },
          {
            key: "calculated_runhour",
            custom_name: "Run Hour",
            visualization: "flip",
            format: "time",
            tab: "engine",
          },
          {
            key: "fuel_consume",
            custom_name: "Fuel Consumed",
            visualization: "flip",
            tab: "engine",
          },
          {
            key: "load_percentage",
            visualization: "Load Percentage",
            type: "small",
            ranges: [{ color: "#1cd456", ranges: [90, 120] }],
            min_value: 0,
            max_value: 120,
            tab: "generator",
          },
          {
            key: "enrg",
            custom_name: "Active Energy",
            visualization: "flip",
            tab: "generator",
          },
          {
            key: "ga_energy",
            custom_name: "Apparent Energy",
            visualization: "flip",
            tab: "generator",
          },
          {
            key: "gr_energy",
            custom_name: "Reactive Energy",
            visualization: "flip",
            tab: "generator",
          },
          {
            key: "pf",
            custom_name: "Power Factor",
            visualization: "meter",
            type: "large",
            ranges: [],
            min_value: 0,
            max_value: 1,
            tab: "generator",
          },
          {
            key: "hz",
            custom_name: "Frequency",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [47, 51] },
              { color: "#ff0000", ranges: [51, 60] },
            ],
            min_value: 40,
            max_value: 60,
            phase_values: ["hz_r", "hz_y", "hz_b"],
            tab: "generator",
          },
          {
            key: "curr",
            visualization: "meter",
            custom_name: "Current",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [130, 135] }],
            min_value: 0,
            max_value: 135,
            phase_values: ["cur_ir", "cur_iy", "cur_ib"],
            tab: "generator",
          },
          {
            key: "watt",
            visualization: "meter",
            type: "large",
            custom_name: "Active Power",
            ranges: [{ color: "#ff0000", ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: "generator",
            phase_values: ["gpow_r", "gpow_y", "gpow_b"],
          },
          {
            key: "apow",
            visualization: "meter",
            type: "large",
            custom_name: "Apparent Power",
            ranges: [{ color: "#ff0000", ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: "generator",
            phase_values: ["gapow_r", "gapow_y", "gapow_b"],
          },
          {
            key: "rpow",
            visualization: "meter",
            type: "large",
            custom_name: "Reactive Power",
            ranges: [{ color: "#ff0000", ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: "generator",
            phase_values: ["grpow_r", "grpow_y", "grpow_b"],
          },
          {
            key: "volt",
            visualization: "meter",
            type: "small",
            custom_name: "L-N Voltage",
            ranges: [
              { color: "#1cd456", ranges: [207, 253] },
              { color: "#ff0000", ranges: [253, 300] },
            ],
            min_value: 0,
            max_value: 300,
            phase_values: ["gvol_r", "gvol_y", "gvol_b"],
            tab: "generator",
          },
          {
            key: "volt_p",
            visualization: "meter",
            type: "small",
            custom_name: "L-L Voltage",
            ranges: [
              { color: "#1cd456", ranges: [350, 440] },
              { color: "#ff0000", ranges: [440, 500] },
            ],
            min_value: 0,
            max_value: 500,
            phase_values: ["gvol_ry", "gvol_yb", "gvol_br"],
            tab: "generator",
          },
          {
            key: "hz_r",
            visualization: "meter",
            custom_name: "L1 Frequency",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [47, 51] },
              { color: "#ff0000", ranges: [51, 60] },
            ],
            min_value: 40,
            max_value: 60,
            tab: "generator",
            phase: 'single',
          },
          {
            key: 'cur_ir',
            visualization: 'meter',
            type: 'small',
            custom_name: 'L1 Current',
            ranges: [{ color: '#ff0000', ranges: [130, 135] }],
            min_value: 0,
            max_value: 135,
            tab: 'generator',
            phase: 'single',
          },
          {
            key: 'gpow_r',
            visualization: 'meter',
            type: 'large',
            custom_name: 'L1 Active Power',
            ranges: [{ color: '#ff0000', ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: 'generator',
            phase: 'single',
          },
          {
            key: 'gapow_r',
            visualization: 'meter',
            type: 'large',
            custom_name: 'L1 Apparent Power',
            ranges: [{ color: '#ff0000', ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: 'generator',
            phase: 'single',
          },
          {
            key: 'grpow_r',
            visualization: 'meter',
            type: 'large',
            custom_name: 'L1 Reactive Power',
            ranges: [{ color: '#ff0000', ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: 'generator',
            phase: 'single',
          },
          {
            key: 'gvol_r',
            visualization: 'meter',
            type: 'small',
            custom_name: 'L1-N Voltage',
            ranges: [
              { color: "#1cd456", ranges: [207, 253] },
              { color: "#ff0000", ranges: [253, 300] },
            ],
            min_value: 0,
            max_value: 300,
            tab: 'generator',
            phase: 'single',
          },
          {
            key: "mvol",
            visualization: "meter",
            type: "small",
            custom_name: "L-N Voltage",
            ranges: [
              { color: "#1cd456", ranges: [207, 253] },
              { color: "#ff0000", ranges: [253, 300] },
            ],
            min_value: 0,
            max_value: 300,
            phase_values: ["mvol_r", "mvol_y", "mvol_b"],
            tab: "mains",
          },
          {
            key: "mvolt_p",
            visualization: "meter",
            type: "small",
            custom_name: "L-L Voltage",
            ranges: [
              { color: "#1cd456", ranges: [350, 440] },
              { color: "#ff0000", ranges: [440, 500] },
            ],
            min_value: 0,
            max_value: 500,
            phase_values: ["mvol_ry", "mvol_yb", "mvol_br"],
            tab: "mains",
          },
          {
            key: "mt_power",
            visualization: "meter",
            type: "large",
            custom_name: "Active Power",
            ranges: [{ color: "#ff0000", ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: "mains",
            phase_values: ["mpow_r", "mpow_y", "mpow_b"],
          },
          {
            key: "ma_power",
            visualization: "meter",
            type: "large",
            custom_name: "Apparent Power",
            ranges: [{ color: "#ff0000", ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: "mains",
            phase_values: ["mapow_r", "mapow_y", "mapow_b"],
          },
          {
            key: "mr_power",
            visualization: "meter",
            type: "large",
            custom_name: "Reactive Power",
            ranges: [{ color: "#ff0000", ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: "mains",
            phase_values: ["mrpow_r", "mrpow_y", "mrpow_b"],
          },
          {
            key: "mfreq",
            custom_name: "Frequency",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [47, 51] },
              { color: "#ff0000", ranges: [51, 60] },
            ],
            min_value: 40,
            max_value: 60,
            phase_values: ["mfreq_r", "mfreq_y", "mfreq_b"],
            tab: "mains",
          },
          {
            key: 'mvol_r',
            visualization: 'meter',
            type: 'small',
            custom_name: 'L1-N Voltage',
            ranges: [
              { color: '#1cd456', ranges: [207, 253] },
              { color: '#ff0000', ranges: [253, 300] },
            ],
            min_value: 0,
            max_value: 300,
            tab: 'mains',
            phase: 'single',
          },
          {
            key: 'mpow_r',
            visualization: 'meter',
            type: 'large',
            custom_name: 'L1 Active Power',
            ranges: [{ color: '#ff0000', ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: 'mains',
            phase: 'single',
          },
          {
            key: 'mapow_r',
            visualization: 'meter',
            type: 'large',
            custom_name: 'L1 Apparent Power',
            ranges: [{ color: '#ff0000', ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: 'mains',
            phase: 'single',
          },
          {
            key: 'mrpow_r',
            visualization: 'meter',
            type: 'large',
            custom_name: 'L1 Reactive Power',
            ranges: [{ color: '#ff0000', ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: 'mains',
            phase: 'single',
          },
          {
            key: "mfreq_r",
            visualization: "meter",
            custom_name: "L1 Frequency",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [47, 51] },
              { color: "#ff0000", ranges: [51, 60] },
            ],
            min_value: 40,
            max_value: 60,
            tab: "mains",
            phase: 'single',
          },
          {
            key: "mt_pf",
            custom_name: "Power Factor",
            visualization: "meter",
            type: "large",
            ranges: [],
            min_value: 0,
            max_value: 1,
            tab: "mains",
          },
          {
            key: "mt_energy",
            custom_name: "Active Energy",
            visualization: "flip",
            tab: "mains",
          },
          {
            key: "ma_energy",
            custom_name: "Apparent Energy",
            visualization: "flip",
            tab: "mains",
          },
          {
            key: "mr_energy",
            custom_name: "Reactive Energy",
            visualization: "flip",
            tab: "mains",
          },
          {
            key: "def_con",
            visualization: "meter",
            type: "small",
            custom_name: "DEF Conc.",
            min_value: 0,
            max_value: 100,
            tab: "ats",
          },
          {
            key: "def_aft_t1",
            visualization: "meter",
            type: "small",
            custom_name: "DEF Tank Level",
            min_value: 0,
            max_value: 100,
            tab: "ats",
          },
          {
            key: "egr_counter_ver1",
            // custom_name: "",
            visualization: "flip",
            time_in: "minute",
            tab: "ats",
          },
          {
            key: "egr_f_min",
            // custom_name: "",
            visualization: "flip",
            format: "time",
            time_in: "minute",
            tab: "ats",
          },
          {
            key: "egr_hl_min",
            // custom_name: "",
            visualization: "flip",
            format: "time",
            time_in: "minute",
            tab: "ats",
          },
          {
            key: "time_to_fia",
            custom_name: "Inducement Timer",
            visualization: "flip",
            format: "time",
            time_in: "minute",
            tab: "ats",
          },
          // {
          //   key: "time_remain_ind_ovrd",
          //   custom_name: "SCR OID Time Left",
          //   visualization: "flip",
          //   format: "time",
          //   tab: "ats",
          // },
          // {
          //   key: "no_of_ovrd_remain",
          //   custom_name: "SCR OID Left",
          //   visualization: "flip",
          //   tab: "ats",
          // },
          {
            key: "scr_out_temp",
            visualization: "meter",
            type: "small",
            custom_name: "SCR Outlet Temp.",
            min_value: -273,
            max_value: 1735,
            tab: "ats",
          },
          {
            key: "doc_out_temp",
            visualization: "meter",
            type: "small",
            custom_name: "DOC Outlet Temp.",
            min_value: -273,
            max_value: 1735,
            tab: "ats",
          },
          {
            key: "amb_temp",
            visualization: "meter",
            type: "small",
            custom_name: "Ambient Temp.",
            min_value: 0,
            max_value: 60,
            tab: "ats",
          },
          {
            key: "baro_press",
            visualization: "meter",
            type: "small",
            custom_name: "Barometric Press.",
            min_value: 0,
            max_value: 2,
            tab: "ats",
          },
          {
            key: "indu_timer_1",
            visualization: "flip",
            custom_name: "Low Level inducement",
            format: "time",
            time_in: "minute",
            tab: "ats",
          },
          {
            key: "indu_timer_2",
            visualization: "flip",
            custom_name: "Severe level inducement",
            format: "time",
            time_in: "minute",
            tab: "ats",
          },
        ],
      },
    },
  },
  {
    id: 71,
    name: "Fuel Tank",
    parent_id: 0,
    show_location: false,
    status_options: ["Connected", "Disconnected"],
    req_thing_details: ["capacity"],
    machine_info: [
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
    ],
    pages: {
      map: {
        map_icons: {
          online: FuelTankOnline,
          offline: FuelTankOffline,
          disconnected: FuelTankNotConnected,
        },
      },
      real_time: {
        parameterDetails: [
          {
            key: "fuel",
            visualization: "fuelGraph",
            type: "fuelGraph",
          },
        ],
      },
      detailed_view: {
        parameter_trend: {
          parameters: [
            {
              key: "fuel",
            },
          ],
        },
      },
      panel: {
        real_time_parameters: [
          {
            key: "fuel",
          },
        ],
      },
    },
  },
  {
    id: 73,
    name: "Portable Compressor",
    parent_id: 0,
    status_options: ["Running", "Stopped", "Disconnected"],
    machine_info: [
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
    ],
    pages: {
      map: {
        map_icons: {
          online: PortableCompressorOnline,
          offline: PortableCompressorOffline,
          disconnected: PortableCompressorNotConnected,
        },
      },
      panel: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_runhour",
            attribute: "sum",
            type: "time",
          },
          {
            duration: "today",
            parameter: "fuel_consumption",
            attribute: "sum",
          },
        ],
        real_time_parameters: [
          {
            key: "vbat",
          },
          {
            key: "temp",
          },
          {
            key: "rpm",
          },
          {
            key: "oil_temp",
          },
          {
            key: "press",
          },
          {
            key: "ad_press",
          },
        ],
      },
      real_time: {
        tabs: [
          { label: "Important Parameters", key: "important_parameters" },
          { label: "Others", key: "others" },
        ],
        parameterDetails: [
          {
            key: "rpm",
            custom_name: "Engine RPM",
            visualization: "meter",
            type: "large",
            ranges: [
              { color: "#1cd456", ranges: [1200, 2000] },
              { color: "#ff0000", ranges: [2000, 3000] },
            ],
            min_value: 0,
            max_value: 3000,
            tab: "important_parameters",
          },
          {
            key: "ad_press",
            custom_name: "Air Discharge Pressure",
            visualization: "meter",
            type: "large",
            ranges: [
              { color: "#1cd456", ranges: [120, 400] },
              { color: "#ff0000", ranges: [400, 500] },
            ],
            min_value: 0,
            max_value: 500,
            tab: "important_parameters",
          },
          {
            key: "ad_temp",
            custom_name: "Air Discharge Temperature",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [95, 120] },
              { color: "#ff0000", ranges: [120, 150] },
            ],
            min_value: 0,
            max_value: 150,
            tab: "important_parameters",
          },
          {
            key: "press",
            custom_name: "Engine Oil Pressure",
            visualization: "meter",
            type: "small",
            // ranges: [
            //   { color: "#1cd456", ranges: [95, 120] },
            //   { color: "#ff0000", ranges: [120, 150] },
            // ],
            min_value: 0,
            max_value: 200,
            tab: "important_parameters",
          },
          {
            key: "oil_temp",
            custom_name: "Engine Oil Temperature",
            visualization: "meter",
            type: "small",
            // ranges: [
            //   { color: "#1cd456", ranges: [95, 120] },
            //   { color: "#ff0000", ranges: [120, 150] },
            // ],
            min_value: 0,
            max_value: 200,
            tab: "important_parameters",
          },
          {
            key: "temp",
            custom_name: "Coolant Temperature",
            visualization: "meter",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [100, 120] }],
            min_value: 0,
            max_value: 120,
            tab: "important_parameters",
          },
          {
            key: "fuel_consume",
            custom_name: "Total Fuel Used",
            visualization: "flip",
            tab: "important_parameters",
          },
          {
            key: "reg_press",
            custom_name: "Regulation Pressure",
            visualization: "meter",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [50, 60] }],
            min_value: 0,
            max_value: 60,
            tab: "important_parameters",
          },
          {
            key: "is_press",
            custom_name: "Inter Stage Pressure",
            visualization: "meter",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [130, 150] }],
            min_value: 0,
            max_value: 150,
            tab: "important_parameters",
          },
          {
            key: "cmp_oil_press",
            custom_name: "Compressor Oil Pressure",
            visualization: "meter",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [400, 500] }],
            min_value: 0,
            max_value: 500,
            tab: "important_parameters",
          },
          {
            key: "calculated_runhour",
            custom_name: "Run Hour",
            visualization: "flip",
            tab: "others",
            format: "time",
          },
          {
            key: "vbat",
            custom_name: "Battery Voltage",
            visualization: "meter",
            type: "large",
            min_value: 0,
            max_value: 40,
            tab: "others",
          },
          {
            key: "ima_temp",
            custom_name: "Intake manifold temperature",
            visualization: "meter",
            type: "large",
            ranges: [{ color: "#ff0000", ranges: [85, 120] }],
            min_value: 0,
            max_value: 120,
            tab: "others",
          },
          {
            key: "ain_press",
            custom_name: "Air Inlet pressure",
            visualization: "meter",
            type: "large",
            min_value: 0,
            max_value: 200,
            tab: "others",
          },
          {
            key: "fuel_press",
            custom_name: "Engine Fuel Delivery pressure",
            visualization: "meter",
            type: "large",
            min_value: 0,
            max_value: 200,
            tab: "others",
          },
          {
            key: "throt_pos",
            custom_name: "Throttle Position",
            visualization: "meter",
            type: "large",
            min_value: 0,
            max_value: 100,
            tab: "others",
          },
        ],
      },
    },
  },
  {
    id: 67,
    name: "Car",
    parent_id: 0,
    machine_info: [
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
    ],
    status_options: ["Running", "Stopped", "Disconnected"],
    pages: {
      map: {
        map_icons: {
          online: CarMapSwitchOn,
          offline: CarMapOffline,
          disconnected: CarMapSwitchOff,
        },
      },
      real_time: false,
      detailed_view: false,
      panel: {
        real_time_parameters: [
          {
            key: "engine_load",
          },
          {
            key: "temp",
          },
          {
            key: "vbat",
          },
          {
            key: "rpm",
          },
          {
            key: "speed",
          },
          {
            key: "fuel",
          },
        ],
      },
    },
  },
  {
    id: 74,
    name: "Tanker Truck",
    status_options: ["Running", "Stopped", "Disconnected"],
    parent_id: 0,
    machine_info: [
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
    ],
    pages: {
      map: {
        map_icons: {
          online: TankerMapSwitchOn,
          offline: TankerMapOffline,
          disconnected: TankerMapSwitchOff,
        },
      },
      real_time: false,
      detailed_view: false,
      panel: {
        real_time_parameters: [
          {
            key: "engine_load",
          },
          {
            key: "temp",
          },
          {
            key: "vbat",
          },
          {
            key: "rpm",
          },
          {
            key: "speed",
          },
          {
            key: "fuel",
          },
        ],
      },
    },
  },
  {
    id: 21,
    name: "CEMS",
    threshold: true,
    is_data_availability: true,
    parent_id: 0,
    status_options: ["Connected", "Disconnected"],
    pages: {
      map: false,
      real_time: false,
      panel: {},
      detailed_view: {},
    },
  },
  {
    id: 23,
    name: "EQMS",
    is_data_availability: true,
    threshold: true,
    parent_id: 0,
    status_options: ["Connected", "Disconnected"],
    pages: {
      map: false,
      real_time: false,
      panel: {},
      detailed_view: {},
    },
  },
  {
    id: 44,
    name: "IP Camera",
    parent_id: 0,
    is_data_availability: true,
    no_list: true,
    threshold: true,
    status_options: ["Connected", "Disconnected"],
    // no_status: true,
    pages: {
      map: false,
      real_time: false,
      panel: {},
    },
  },
  {
    id: 85,
    name: "Digital Display",
    status_options: ["Connected", "Disconnected"],
    parent_id: 0,
    pages: {
      map: false,
      real_time: false,
      panel: {},
    },
  },
  {
    id: 76,
    name: "E-Bike",
    status_options: ["Running", "Stopped", "Disconnected"],
    parent_id: 0,
    machine_info: [
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
    ],
    pages: {
      map: {
        map_icons: {
          online: EvSwitchOn,
          offline: EvOffline,
          disconnected: EvSwitchOff,
        },
      },
      real_time: false,
      detailed_view: false,
      panel: {},
    },
  },
  {
    id: 94,
    show_fault: true,
    name: "Process Analyzer",
    status_options: ["Connected", "Disconnected"],
    parent_id: 0,
    machine_info: [
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
      {
        label: "Serial No",
        key: "sl_no",
      },
    ],
    pages: {
      map: false,
      real_time: false,
      detailed_view: {
        parameter_trend: {
          parameters: [
            {
              key: "on_time_hrs_asp_1",
              name: "Aspirator 01 Runhour",
              summaryParams: ["calculated_rnhr_asp_1"],
              noRealTimeValue: true,
              type: "rnhr",
            },
            {
              key: "on_time_hrs_asp_2",
              name: "Aspirator 02 Runhour",
              summaryParams: ["calculated_rnhr_asp_2"],
              noRealTimeValue: true,
              type: "rnhr",
            },
            // {
            //   key: "on_time_hrs_asp_3",
            //   name: 'Aspirator 03 Runhour',
            //   summaryParams: ["calculated_rnhr_asp_3"],
            //   noRealTimeValue: true,
            //   type: 'rnhr'
            // },
            // {
            //   key: "on_time_hrs_asp_4",
            //   name: 'Aspirator 04 Runhour',
            //   summaryParams: ["calculated_rnhr_asp_4"],
            //   noRealTimeValue: true,
            //   type: 'rnhr'
            // },
            {
              key: "gas_101_conc",
            },
            {
              key: "gas_102_conc",
            },
            {
              key: "gas_103_conc_spr",
            },
            {
              key: "gas_104_conc_spr",
            },
            {
              key: "diff_press_prb_1",
            },
            {
              key: "diff_press_prb_2",
            },
            {
              key: "abs_press_apt_1",
            },
            {
              key: "abs_press_apt_2",
            },
            {
              key: "abs_press_apt_3",
            },
            {
              key: "abs_press_apt_4",
            },
            {
              key: "temp_prb_1",
            },
            {
              key: "temp_prb_2",
            },
            {
              key: "temp_prb_3_spr",
            },
            {
              key: "temp_prb_4_spr",
            },
            {
              key: "proc_temp",
            },
            {
              key: "proc_press",
            },
            {
              key: "lsr_11_trans",
            },
            {
              key: "lsr_12_trans",
            },
            {
              key: "lsr_13_trans_spr",
            },
            {
              key: "lsr_14_trans_spr",
            },
          ],
        },
      },
      panel: {
        real_time_parameters: [
          {
            key: "gas_101_conc",
          },
          {
            key: "gas_102_conc",
          },
          {
            key: "gas_103_conc_spr",
          },
          {
            key: "gas_104_conc_spr",
          },
        ],
      },
      list: {
        real_time_parameters: [
          {
            key: "gas_101_conc",
          },
          {
            key: "gas_102_conc",
          },
          {
            key: "gas_103_conc_spr",
          },
          {
            key: "gas_104_conc_spr",
          },
          {
            key: "no_sov_1",
          },
          {
            key: "no_sov_2",
          },
          {
            key: "no_sov_3",
          },
          {
            key: "no_sov_4",
          },
          {
            key: "no_sov_5",
          },
          {
            key: "no_sov_6",
          },
          {
            key: "no_sov_7",
          },
          {
            key: "no_sov_8",
          },
          {
            key: "no_sov_9",
          },
          {
            key: "no_sov_10",
          },
          {
            key: "no_sov_11",
          },
          {
            key: "no_sov_12",
          },
          {
            key: "no_sov_13_spr",
          },
          {
            key: "no_sov_14_spr",
          },
          {
            key: "no_sov_15_spr",
          },
          {
            key: "no_sov_16_spr",
          },
          {
            key: "no_sov_17_spr",
          },
          {
            key: "no_sov_18_spr",
          },
          {
            key: "no_sov_19_spr",
          },
          {
            key: "no_sov_20_spr",
          },
        ],
      },
    },
  },
  {
    id: 78,
    name: "AC Electrical Machine",
    parent_id: 0,
    show_switch: false,
    show_lock: false,
    status_options: ["Connected", "Disconnected"],
    pages: {
      map: {
        map_icons: {
          online: ElectricalMachinesMapSwitchOn,
          offline: ElectricalMachinesMapOffline,
          disconnected: ElectricalMachinesMapNotConnected,
        },
      },
      detailed_view: {
        parameter_trend: {
          parameters: [
            {
              key: "mt_energy",
              summaryParams: ["calculated_mains_energy"],
              noRealTimeValue: true,
            },
            {
              key: "mt_power",
              clubbedParams: ["mpow_r", "mpow_y", "mpow_b"],
            },
            {
              key: "mvol",
              clubbedParams: ["mvol_r", "mvol_y", "mvol_b"],
            },
            {
              key: "mcurr",
              clubbedParams: ["mcur_ir", "mcur_iy", "mcur_ib"],
            },
            {
              key: "mvolt_p",
              clubbedParams: ["mvol_ry", "mvol_yb", "mvol_br"],
            },
            {
              key: "mr_power",
              clubbedParams: ["mrpow_r", "mrpow_y", "mrpow_b"],
            },
            {
              key: "ma_power",
              clubbedParams: ["mapow_r", "mapow_y", "mapow_b"],
            },
            {
              key: "mr_energy",
              summaryParams: ["calculated_mr_energy"],
              noRealTimeValue: true,
            },
            {
              key: "ma_energy",
              summaryParams: ["calculated_ma_energy"],
              noRealTimeValue: true,
            },
            {
              key: "mt_pf",
              clubbedParams: ["mpf_r", "mpf_y", "mpf_b"],
            },
            { key: "mfreq" },
          ],
        },
      },
      panel: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_mains_energy",
            attribute: "sum",
          },
          {
            duration: "yesterday",
            parameter: "calculated_mains_energy",
            attribute: "sum",
          },
          {
            duration: "this_month",
            parameter: "calculated_mains_energy",
            attribute: "sum",
          },
          {
            duration: "last_month",
            parameter: "calculated_mains_energy",
            attribute: "sum",
          },
        ],
        real_time_parameters: [
          {
            key: "mt_power",
          },
          {
            key: "mcurr",
          },
          {
            key: "mvol",
          },
        ],
      },
      list: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_mains_energy",
            name: "Active Energy",
          },
          {
            duration: "this_month",
            parameter: "calculated_mains_energy",
            name: "Active Energy",
          },
        ],
        real_time_parameters: [
          {
            key: "mt_power",
          },
          {
            key: "mvol",
          },
          {
            key: "mcurr",
          },
          {
            key: "mvolt_p",
          },
          {
            key: "mr_power",
          },
          {
            key: "ma_power",
          },
          {
            key: "mt_pf",
          },
          {
            key: "mfreq",
          },
        ],
      },
      real_time: {
        parameterDetails: [
          {
            key: "mt_power",
            custom_name: "Active Power",
            visualization: "meter",
            type: "large",
            ranges: [{ color: "#ff0000", ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            phase_values: ["mpow_r", "mpow_y", "mpow_b"],
          },
          {
            key: "mvol",
            custom_name: "L-N Voltage",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [230, 250] },
              { color: "#ff0000", ranges: [251, 300] },
            ],
            min_value: 0,
            max_value: 300,
            phase_values: ["mvol_r", "mvol_y", "mvol_b"],
          },
          {
            key: "mcurr",
            custom_name: "Current",
            visualization: "meter",
            type: "small",
            ranges: [],
            min_value: 0,
            max_value: 0,
            phase_values: ["mcur_ir", "mcur_iy", "mcur_ib"],
          },
          {
            key: "mt_energy",
            custom_name: "Active Energy",
            visualization: "flip",
          },
          {
            key: "mvolt_p",
            custom_name: "L-L Voltage",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [360, 440] },
              { color: "#ff0000", ranges: [441, 500] },
            ],
            min_value: 0,
            max_value: 500,
            phase_values: ["mvol_ry", "mvol_yb", "mvol_br"],
          },
          {
            key: "mr_energy",
            custom_name: "Reactive Energy",
            visualization: "flip",
          },
          {
            key: "ma_energy",
            custom_name: "Apparent Energy",
            visualization: "flip",
          },
          {
            key: "ma_power",
            custom_name: "Apparent Power",
            phase_values: ["mapow_r", "mapow_y", "mapow_b"],
          },
          {
            key: "mr_power",
            custom_name: "Reactive Power",
            phase_values: ["mrpow_r", "mrpow_y", "mrpow_b"],
          },
          {
            key: "mt_pf",
            custom_name: "Power Factor",
            visualization: "meter",
            type: "large",
            ranges: [{ color: "#ff0000", ranges: [] }],
            min_value: 0,
            max_value: 1,
            phase_values: ["mpf_r", "mpf_y", "mpf_b"],
          },
          {
            key: "mfreq",
            custom_name: "Frequency",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [49, 51] },
              { color: "#ff0000", ranges: [52, 60] },
            ],
            min_value: 0,
            max_value: 60,
          },
        ],
      },
    },
  },
  {
    id: 99,
    name: "Exhaust Fan",
    parent_id: 0,
    show_switch: true,
    show_lock: false,
    operation_mode: "manual",
    lock_command: { onCommand: "lock", offCommand: "unlock" },
    switch_command: { onCommand: "start", offCommand: "stop" },
    status_options: ["Running", "Stopped", "Disconnected"],
    machine_info: [
      // {
      //   label: "Serial No.",
      //   key: "serial",
      // },
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
      {
        label: "Rated Power",
        key: "rated_power",
      },
    ],
    pages: {
      map: {
        map_icons: {
          online: ExhaustFanOnlineMapIcon,
          offline: ExhaustFanOfflineIcon,
          switch_off: ExhaustFanOnlineStoppedIcon,
          disconnected: ExhaustFanOnlineNotConnectedIcon,
        },
      },
      detailed_view: {
        parameter_trend: {
          parameters: [
            {
              key: "mt_energy",
              summaryParams: ["calculated_mains_energy"],
              noRealTimeValue: true,
            },
            {
              key: "calculated_runhour",
              summaryParams: ["calculated_runhour"],
              noRealTimeValue: true,
              name: "Runhour",
              type: "rnhr",
              unit: "hr",
            },
            {
              key: "mt_power",
              clubbedParams: ["mpow_r", "mpow_y", "mpow_b"],
            },
            {
              key: "mvol",
              clubbedParams: ["mvol_r", "mvol_y", "mvol_b"],
            },
            {
              key: "mcurr",
              clubbedParams: ["mcur_ir", "mcur_iy", "mcur_ib"],
            },
            {
              key: "mvolt_p",
              clubbedParams: ["mvol_ry", "mvol_yb", "mvol_br"],
            },
            {
              key: "mr_power",
              clubbedParams: ["mrpow_r", "mrpow_y", "mrpow_b"],
            },
            {
              key: "ma_power",
              clubbedParams: ["mapow_r", "mapow_y", "mapow_b"],
            },
            {
              key: "mr_energy",
              summaryParams: ["calculated_mr_energy"],
              noRealTimeValue: true,
            },
            {
              key: "ma_energy",
              summaryParams: ["calculated_ma_energy"],
              noRealTimeValue: true,
            },
            {
              key: "mt_pf",
              clubbedParams: ["mpf_r", "mpf_y", "mpf_b"],
            },
            { key: "mfreq" },
          ],
        },
      },
      panel: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_mains_energy",
            attribute: "sum",
          },
          {
            duration: "yesterday",
            parameter: "calculated_mains_energy",
            attribute: "sum",
          },
          {
            duration: "this_month",
            parameter: "calculated_mains_energy",
            attribute: "sum",
          },
          {
            duration: "last_month",
            parameter: "calculated_mains_energy",
            attribute: "sum",
          },
          {
            duration: "today",
            parameter: "calculated_runhour",
            attribute: "sum",
            type: "time_format",
          },
          {
            duration: "yesterday",
            parameter: "calculated_runhour",
            attribute: "sum",
            type: "time_format",
          },
          {
            duration: "this_month",
            parameter: "calculated_runhour",
            attribute: "sum",
            type: "time_format",
          },
          {
            duration: "last_month",
            parameter: "calculated_runhour",
            attribute: "sum",
            type: "time_format",
          },
        ],
        real_time_parameters: [
          {
            key: "mt_power",
          },
          {
            key: "mcurr",
          },
          {
            key: "mvol",
          },
        ],
      },
      list: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_mains_energy",
            name: "Active Energy",
          },
          {
            duration: "this_month",
            parameter: "calculated_runhour",
            name: "Runhour",
          },
          {
            duration: "this_month",
            parameter: "calculated_mains_energy",
            name: "Active Energy",
          },
          {
            duration: "today",
            parameter: "calculated_runhour",
            name: "Runhour",
          },
        ],
        real_time_parameters: [
          {
            key: "mt_power",
          },
          {
            key: "mvol",
          },
          {
            key: "mcurr",
          },
          {
            key: "mvolt_p",
          },
          {
            key: "mr_power",
          },
          {
            key: "ma_power",
          },
          {
            key: "mt_pf",
          },
          {
            key: "mfreq",
          },
        ],
      },
      real_time: {
        showFaults: false,
        parameterDetails: [
          {
            key: "mt_power",
            custom_name: "Active Power",
            visualization: "meter",
            type: "large",
            ranges: [{ color: "#ff0000", ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            phase_values: ["mpow_r", "mpow_y", "mpow_b"],
          },
          {
            key: "mvol",
            custom_name: "L-N Voltage",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [230, 250] },
              { color: "#ff0000", ranges: [251, 300] },
            ],
            min_value: 0,
            max_value: 300,
            phase_values: ["mvol_r", "mvol_y", "mvol_b"],
          },
          {
            key: "mcurr",
            custom_name: "Current",
            visualization: "meter",
            type: "small",
            ranges: [],
            min_value: 0,
            max_value: 0,
            phase_values: ["mcur_ir", "mcur_iy", "mcur_ib"],
          },
          {
            key: "mt_energy",
            custom_name: "Active Energy",
            visualization: "flip",
          },
          {
            key: "mvolt_p",
            custom_name: "L-L Voltage",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [360, 440] },
              { color: "#ff0000", ranges: [441, 500] },
            ],
            min_value: 0,
            max_value: 500,
            phase_values: ["mvol_ry", "mvol_yb", "mvol_br"],
          },
          {
            key: "mr_energy",
            custom_name: "Reactive Energy",
            visualization: "flip",
          },
          {
            key: "ma_energy",
            custom_name: "Apparent Energy",
            visualization: "flip",
          },
          {
            key: "ma_power",
            custom_name: "Apparent Power",
            phase_values: ["mapow_r", "mapow_y", "mapow_b"],
          },
          {
            key: "mr_power",
            custom_name: "Reactive Power",
            phase_values: ["mrpow_r", "mrpow_y", "mrpow_b"],
          },
          {
            key: "mt_pf",
            custom_name: "Power Factor",
            visualization: "meter",
            type: "large",
            ranges: [{ color: "#ff0000", ranges: [] }],
            min_value: 0,
            max_value: 1,
            phase_values: ["mpf_r", "mpf_y", "mpf_b"],
          },
          {
            key: "mfreq",
            custom_name: "Frequency",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [49, 51] },
              { color: "#ff0000", ranges: [52, 60] },
            ],
            min_value: 0,
            max_value: 60,
          },
        ],
      },
    },
  },
  {
    id: 80,
    name: "Freezer",
    parent_id: 0,
    show_switch: false,
    show_lock: false,
    machine_info: [
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
    ],
    status_options: ["Connected", "Disconnected"],
    pages: {
      map: {
        map_icons: {
          online: FreezerOnline,
          offline: FreezerOffline,
          disconnected: FreezerNotConnected,
        },
      },
      panel: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_energy",
            attribute: "sum",
          },
          {
            duration: "today",
            parameter: "calculated_runhour",
            attribute: "sum",
            type: "time",
          },
        ],
        real_time_parameters: [
          {
            key: "volt",
          },
          {
            key: "curr",
          },
          {
            key: "watt",
          },
          {
            key: "temp",
          },
          {
            key: "humid",
          },
        ],
      },
      detailed_view: {
        parameter_trend: {
          parameters: [
            { key: "volt" },
            { key: "curr" },
            { key: "watt" },
            { key: "temp" },
            { key: "humid" },
          ],
        },
      },
      real_time: {
        parameterDetails: [
          {
            key: "watt",
            visualization: "meter",
            type: "large",
            ranges: [{ color: "#ff0000", ranges: [2500, 3000] }],
            min_value: 0,
            max_value: 3000,
          },
          {
            key: "volt",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [207, 253] },
              { color: "#ff0000", ranges: [253, 300] },
            ],
            min_value: 0,
            max_value: 300,
          },
          {
            key: "curr",
            visualization: "meter",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [140, 150] }],
            min_value: 0,
            max_value: 150,
          },
          {
            key: "temp",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#ff0000", ranges: [5, 35] },
              { color: "#ff0000", ranges: [-30, -25] },
            ],
            min_value: -30,
            max_value: 35,
          },
          {
            key: "humid",
            visualization: "meter",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [140, 150] }],
            min_value: 0,
            max_value: 150,
          },
        ],
      },
    },
  },
  {
    id: 77,
    name: "DC Energy Meter",
    parent_id: 0,
    show_switch: false,
    show_lock: false,
    machine_info: [
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
    ],
    status_options: ["Connected", "Disconnected"],
    pages: {
      real_time: false,
      detailed_view: {
        parameter_trend: {
          parameters: [{ key: "volt" }, { key: "curr" }, { key: "watt" }],
        },
      },
      map: {
        map_icons: {
          online: EnergyMapSwitchOn,
          offline: EnergyMapOffline,
          disconnected: EnergyMapNotConnected,
        },
      },
      panel: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_runhour",
            attribute: "sum",
            type: "time",
          },
        ],
        real_time_parameters: [
          {
            key: "enrg",
          },
          {
            key: "volt",
          },
          {
            key: "curr",
          },
          {
            key: "watt",
          },
        ],
      },
    },
  },
  {
    id: 79,
    name: "AC Energy Meter",
    parent_id: 0,
    show_switch: false,
    show_lock: false,
    status_options: ["Connected", "Disconnected"],
    pages: {
      map: {
        map_icons: {
          online: EnergyMapSwitchOn,
          offline: EnergyMapOffline,
          disconnected: EnergyMapNotConnected,
        },
      },
      detailed_view: {
        parameter_trend: {
          parameters: [
            {
              key: "mt_energy",
              summaryParams: ["calculated_mains_energy"],
              noRealTimeValue: true,
            },
            {
              key: "mt_power",
              clubbedParams: ["mpow_r", "mpow_y", "mpow_b"],
            },
            {
              key: "mvol",
              clubbedParams: ["mvol_r", "mvol_y", "mvol_b"],
            },
            {
              key: "mcurr",
              clubbedParams: ["mcur_ir", "mcur_iy", "mcur_ib"],
            },
            {
              key: "tot_curr",
            },
            {
              key: "mvolt_p",
              clubbedParams: ["mvol_ry", "mvol_yb", "mvol_br"],
            },
            {
              key: "mr_power",
              clubbedParams: ["mrpow_r", "mrpow_y", "mrpow_b"],
            },
            {
              key: "ma_power",
              clubbedParams: ["mapow_r", "mapow_y", "mapow_b"],
            },
            {
              key: "mr_energy",
              summaryParams: ["calculated_mr_energy"],
              noRealTimeValue: true,
            },
            {
              key: "ma_energy",
              summaryParams: ["calculated_ma_energy"],
              noRealTimeValue: true,
            },
            {
              key: "mt_pf",
              clubbedParams: ["mpf_r", "mpf_y", "mpf_b"],
            },
            { key: "mfreq" },
          ],
        },
      },
      panel: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_mains_energy",
            attribute: "sum",
          },
          {
            duration: "yesterday",
            parameter: "calculated_mains_energy",
            attribute: "sum",
          },
          {
            duration: "this_month",
            parameter: "calculated_mains_energy",
            attribute: "sum",
          },
          {
            duration: "last_month",
            parameter: "calculated_mains_energy",
            attribute: "sum",
          },
        ],
        real_time_parameters: [
          {
            key: "mt_power",
          },
          {
            key: "mcurr",
          },
          {
            key: "mvol",
          },
        ],
      },
      list: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_mains_energy",
            name: "Active Energy",
            attribute: "sum",
          },
          {
            duration: "this_month",
            parameter: "calculated_mains_energy",
            name: "Active Energy",
            attribute: "sum",
          },
          {
            duration: "today",
            parameter: "calculated_mt_energy_ex",
            name: "Export Energy",
            attribute: "sum",
          },
          {
            duration: "this_month",
            parameter: "calculated_mt_energy_ex",
            name: "Export Energy",
            attribute: "sum",
          },
          {
            duration: "today",
            parameter: "calculated_mt_energy_net",
            name: "Net Energy",
            attribute: "sum",
          },
          {
            duration: "this_month",
            parameter: "calculated_mt_energy_net",
            name: "Net Energy",
            attribute: "sum",
          },
        ],
        real_time_parameters: [
          {
            key: "mt_power",
          },
          {
            key: "mvol",
          },
          {
            key: "mcurr",
          },
          {
            key: "mvolt_p",
          },
          {
            key: "mr_power",
          },
          {
            key: "ma_power",
          },
          {
            key: "mt_pf",
          },
          {
            key: "mfreq",
          },
        ],
      },
      real_time: {
        parameterDetails: [
          {
            key: "mt_power",
            custom_name: "Active Power",
            visualization: "meter",
            type: "large",
            ranges: [{ color: "#ff0000", ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            phase_values: ["mpow_r", "mpow_y", "mpow_b"],
          },
          {
            key: "mvol",
            custom_name: "L-N Voltage",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [230, 250] },
              { color: "#ff0000", ranges: [251, 300] },
            ],
            min_value: 0,
            max_value: 300,
            phase_values: ["mvol_r", "mvol_y", "mvol_b"],
          },
          {
            key: "mcurr",
            custom_name: "Current",
            visualization: "meter",
            type: "small",
            ranges: [],
            min_value: 0,
            max_value: 0,
            phase_values: ["mcur_ir", "mcur_iy", "mcur_ib"],
          },
          {
            key: "mt_energy",
            custom_name: "Active Energy",
            visualization: "flip",
          },
          {
            key: "mvolt_p",
            custom_name: "L-L Voltage",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [360, 440] },
              { color: "#ff0000", ranges: [441, 500] },
            ],
            min_value: 0,
            max_value: 500,
            phase_values: ["mvol_ry", "mvol_yb", "mvol_br"],
          },
          {
            key: "mr_energy",
            custom_name: "Reactive Energy",
            visualization: "flip",
          },
          {
            key: "ma_energy",
            custom_name: "Apparent Energy",
            visualization: "flip",
          },
          {
            key: "ma_power",
            custom_name: "Apparent Power",
            phase_values: ["mapow_r", "mapow_y", "mapow_b"],
          },
          {
            key: "mr_power",
            custom_name: "Reactive Power",
            phase_values: ["mrpow_r", "mrpow_y", "mrpow_b"],
          },
          {
            key: "mt_pf",
            custom_name: "Power Factor",
            visualization: "meter",
            type: "large",
            ranges: [{ color: "#ff0000", ranges: [] }],
            min_value: 0,
            max_value: 1,
            phase_values: ["mpf_r", "mpf_y", "mpf_b"],
          },
          {
            key: "mfreq",
            custom_name: "Frequency",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [49, 51] },
              { color: "#ff0000", ranges: [52, 60] },
            ],
            min_value: 0,
            max_value: 60,
          },
        ],
      },
    },
  },
  {
    id: 101,
    name: "Grid Energy Meter",
    parent_id: 0,
    show_fault: true,
    show_switch: false,
    show_lock: false,
    status_options: ["Connected", "Disconnected"],
    pages: {
      map: {
        map_icons: {
          online: GridOnline,
          offline: GridOffline,
          disconnected: GridNotConnected,
        },
      },
      detailed_view: {
        parameter_trend: {
          parameters: [
            {
              key: "mt_energy",
              summaryParams: ["calculated_mains_energy"],
              noRealTimeValue: true,
            },
            {
              key: "mt_power",
              clubbedParams: ["mpow_r", "mpow_y", "mpow_b"],
            },
            {
              key: "mvol",
              clubbedParams: ["mvol_r", "mvol_y", "mvol_b"],
            },
            {
              key: "mcurr",
              clubbedParams: ["mcur_ir", "mcur_iy", "mcur_ib"],
            },
            {
              key: "tot_curr",
            },
            {
              key: "mvolt_p",
              clubbedParams: ["mvol_ry", "mvol_yb", "mvol_br"],
            },
            {
              key: "mr_power",
              clubbedParams: ["mrpow_r", "mrpow_y", "mrpow_b"],
            },
            {
              key: "ma_power",
              clubbedParams: ["mapow_r", "mapow_y", "mapow_b"],
            },
            {
              key: "mr_energy",
              summaryParams: ["calculated_mr_energy"],
              noRealTimeValue: true,
            },
            {
              key: "ma_energy",
              summaryParams: ["calculated_ma_energy"],
              noRealTimeValue: true,
            },
            {
              key: "mt_pf",
              clubbedParams: ["mpf_r", "mpf_y", "mpf_b"],
            },
            { key: "mfreq" },
            { key: "envol" },
            {
              key: "mt_energy_ex",
              summaryParams: ["calculated_mt_energy_ex"],
              noRealTimeValue: true,
            },
          ],
        },
      },
      panel: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_mains_energy",
            attribute: "sum",
          },
          {
            duration: "yesterday",
            parameter: "calculated_mains_energy",
            attribute: "sum",
          },
          {
            duration: "this_month",
            parameter: "calculated_mains_energy",
            attribute: "sum",
          },
          {
            duration: "last_month",
            parameter: "calculated_mains_energy",
            attribute: "sum",
          },
        ],
        real_time_parameters: [
          {
            key: "mt_power",
          },
          {
            key: "mcurr",
          },
          {
            key: "mvol",
          },
        ],
      },
      list: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_mains_energy",
            name: "Active Energy",
            attribute: "sum",
          },
          {
            duration: "this_month",
            parameter: "calculated_mains_energy",
            name: "Active Energy",
            attribute: "sum",
          },
          {
            duration: "today",
            parameter: "calculated_mt_energy_ex",
            name: "Export Energy",
            attribute: "sum",
          },
          {
            duration: "this_month",
            parameter: "calculated_mt_energy_ex",
            name: "Export Energy",
            attribute: "sum",
          },
          {
            duration: "today",
            parameter: "calculated_mt_energy_net",
            name: "Net Energy",
            attribute: "sum",
          },
          {
            duration: "this_month",
            parameter: "calculated_mt_energy_net",
            name: "Net Energy",
            attribute: "sum",
          },
        ],
        real_time_parameters: [
          {
            key: "mt_power",
          },
          {
            key: "mvol",
          },
          {
            key: "mcurr",
          },
          {
            key: "mvolt_p",
          },
          {
            key: "mr_power",
          },
          {
            key: "ma_power",
          },
          {
            key: "mt_pf",
          },
          {
            key: "mfreq",
          },
        ],
      },
      real_time: {
        parameterDetails: [
          {
            key: "mt_power",
            custom_name: "Active Power",
            visualization: "meter",
            type: "large",
            ranges: [{ color: "#ff0000", ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            phase_values: ["mpow_r", "mpow_y", "mpow_b"],
          },
          {
            key: "mvol",
            custom_name: "L-N Voltage",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [230, 250] },
              { color: "#ff0000", ranges: [251, 300] },
            ],
            min_value: 0,
            max_value: 300,
            phase_values: ["mvol_r", "mvol_y", "mvol_b"],
          },
          {
            key: "mcurr",
            custom_name: "Current",
            visualization: "meter",
            type: "small",
            ranges: [],
            min_value: 0,
            max_value: 0,
            phase_values: ["mcur_ir", "mcur_iy", "mcur_ib"],
          },
          {
            key: "mt_energy",
            custom_name: "Active Energy",
            visualization: "flip",
          },
          {
            key: "mvolt_p",
            custom_name: "L-L Voltage",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [360, 440] },
              { color: "#ff0000", ranges: [441, 500] },
            ],
            min_value: 0,
            max_value: 500,
            phase_values: ["mvol_ry", "mvol_yb", "mvol_br"],
          },
          {
            key: "mr_energy",
            custom_name: "Reactive Energy",
            visualization: "flip",
          },
          {
            key: "ma_energy",
            custom_name: "Apparent Energy",
            visualization: "flip",
          },
          {
            key: "ma_power",
            custom_name: "Apparent Power",
            phase_values: ["mapow_r", "mapow_y", "mapow_b"],
          },
          {
            key: "mr_power",
            custom_name: "Reactive Power",
            phase_values: ["mrpow_r", "mrpow_y", "mrpow_b"],
          },
          {
            key: "mt_pf",
            custom_name: "Power Factor",
            visualization: "meter",
            type: "large",
            ranges: [{ color: "#ff0000", ranges: [] }],
            min_value: 0,
            max_value: 1,
            phase_values: ["mpf_r", "mpf_y", "mpf_b"],
          },
          {
            key: "mfreq",
            custom_name: "Frequency",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [49, 51] },
              { color: "#ff0000", ranges: [52, 60] },
            ],
            min_value: 0,
            max_value: 60,
          },
          {
            key: "envol",
            custom_name: "Earth-Neutral Voltage",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#ff0000", ranges: [0.5, 1] },
            ],
            min_value: 0,
            max_value: 1,
          },
        ],
      },
    },
  },
  {
    id: 81,
    name: "Storage",
    parent_id: 0,
    machine_info: [
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
    ],
  },
  {
    id: 82,
    name: "Fuel Dispenser",
    parent_id: 0,
    machine_info: [
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
    ],
  },
  {
    id: 83,
    name: "Chiller",
    parent_id: 0,
    show_switch: false,
    show_lock: false,
    machine_info: [
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
    ],
    status_options: ["Connected", "Disconnected"],
    pages: {
      map: false,
      panel: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_energy",
            attribute: "sum",
          },
          {
            duration: "today",
            parameter: "calculated_runhour",
            attribute: "sum",
            type: "time",
          },
        ],
        real_time_parameters: [
          {
            key: "volt",
          },
          {
            key: "curr",
          },
          {
            key: "watt",
          },
          {
            key: "temp",
          },
          {
            key: "humid",
          },
        ],
      },
      detailed_view: {
        parameter_trend: {
          parameters: [
            { key: "volt" },
            { key: "curr" },
            { key: "watt" },
            { key: "temp" },
            { key: "humid" },
          ],
        },
      },
      real_time: {
        parameterDetails: [
          {
            key: "watt",
            visualization: "meter",
            type: "large",
            ranges: [{ color: "#ff0000", ranges: [2500, 3000] }],
            min_value: 0,
            max_value: 3000,
          },
          {
            key: "volt",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [207, 253] },
              { color: "#ff0000", ranges: [253, 300] },
            ],
            min_value: 0,
            max_value: 300,
          },
          {
            key: "curr",
            visualization: "meter",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [140, 150] }],
            min_value: 0,
            max_value: 150,
          },
          {
            key: "temp",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#ff0000", ranges: [5, 35] },
              { color: "#ff0000", ranges: [-20, -10] },
            ],
            min_value: -20,
            max_value: 35,
          },
          {
            key: "humid",
            visualization: "meter",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [80, 100] }],
            min_value: 0,
            max_value: 100,
          },
        ],
      },
    },
  },
  {
    id: 69,
    name: "Chest Freezer",
    parent_id: 0,
    show_switch: false,
    show_lock: false,
    machine_info: [
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
    ],
    status_options: ["Connected", "Disconnected"],
    pages: {
      map: false,
      panel: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_energy",
            attribute: "sum",
          },
          {
            duration: "today",
            parameter: "calculated_runhour",
            attribute: "sum",
            type: "time",
          },
        ],
        real_time_parameters: [
          {
            key: "volt",
          },
          {
            key: "curr",
          },
          {
            key: "watt",
          },
          {
            key: "temp",
          },
          {
            key: "humid",
          },
        ],
      },
      detailed_view: {
        parameter_trend: {
          parameters: [
            { key: "volt" },
            { key: "curr" },
            { key: "watt" },
            { key: "temp" },
            { key: "humid" },
          ],
        },
      },
      real_time: {
        parameterDetails: [
          {
            key: "watt",
            visualization: "meter",
            type: "large",
            ranges: [{ color: "#ff0000", ranges: [2500, 3000] }],
            min_value: 0,
            max_value: 3000,
          },
          {
            key: "volt",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [207, 253] },
              { color: "#ff0000", ranges: [253, 300] },
            ],
            min_value: 0,
            max_value: 300,
          },
          {
            key: "curr",
            visualization: "meter",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [140, 150] }],
            min_value: 0,
            max_value: 150,
          },
          {
            key: "temp",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#ff0000", ranges: [5, 35] },
              { color: "#ff0000", ranges: [-30, -25] },
            ],
            min_value: -30,
            max_value: 35,
          },
          {
            key: "humid",
            visualization: "meter",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [140, 150] }],
            min_value: 0,
            max_value: 150,
          },
        ],
      },
    },
  },
  {
    id: 45,
    name: "Cold Storage",
    parent_id: 0,
    show_switch: false,
    show_lock: false,
    threshold: true,
    status_options: ["Connected", "Disconnected"],
    pages: {
      map: {
        map_icons: {
          online: ColdStorageOnline,
          offline: ColdStorageOffline,
          disconnected: ColdStorageNotConnected,
        },
      },
      panel: {
        real_time_parameters: [],
      },
      list: {
        real_time_parameters: [
          {
            key: "dr_st"
          }
        ]
      },
      detailed_view: {
        parameter_trend: {
          parameters: [],
        },
      },
      real_time: false,
    },
  },
  {
    id: 86,
    name: "Flow Meter",
    parent_id: 0,
    is_data_availability: true,
    threshold: true,
    show_switch: false,
    show_lock: false,
    machine_info: [
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
      {
        label: "Serial No",
        key: "sl_no",
      },
    ],
    status_options: ["Connected", "Disconnected"],
    pages: {
      assetDashboard: {
        pgBtn: [
          {
            name: "Utilization Insights",
            key: "insights",
          },
          {
            name: "Asset Analytics",
            key: "asset_info",
          },
        ],
      },
      map: {
        map_icons: {
          online: FlowMeterMapSwitchOn,
          offline: FlowMeterMapOffline,
          disconnected: FlowMeterMapSwitchOff,
        },
      },
      real_time: {
        parameterDetails: [
          {
            key: "t_flow",
            visualization: "flip",
          },
          {
            key: "flow",
            visualization: "meter",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [850, 935] }],
            min_value: 0,
            max_value: 935,
          },
        ],
      },
      panel: {
        summary_value: [
          {
            duration: "today",
            parameter: "flow",
            attribute: "avg",
          },
          {
            duration: "today",
            parameter: "t_flow",
            attribute: "avg",
          },
        ],
        real_time_parameters: [
          {
            key: "flow",
          },
          {
            key: "t_flow",
          },
          {
            key: "tr_flow",
          },
        ],
      },
      detailed_view: {
        parameter_trend: {
          parameters: [{ key: "flow" }, { key: "t_flow" }],
        },
      },
    },
  },
  {
    id: 89,
    name: "IAQMS",
    parent_id: 0,
    status_options: ["Connected", "Disconnected"],
    param_configs: {},
    pages: {
      map: true,
      real_time: false,
      panel: {
        real_time_parameters: [],
      },
    },
  },
  {
    id: 63,
    name: "Borewell",
    parent_id: 0,
    status_options: ["Connected", "Disconnected"],
    param_configs: {},
    pages: {
      map: {
        map_icons: {
          online: BorewellMapSwitchOn,
          offline: BorewellMapOffline,
          disconnected: BorewellMapSwitchOff,
        },
      },
      real_time: false,
      detailed_view: {
        parameter_trend: {
          parameters: [{ key: "gw_level" }],
        },
      },
      panel: {
        real_time_parameters: [
          {
            key: "gw_level",
          },
        ],
      },
    },
  },
  {
    id: 43,
    name: "Flow & Motor",
    parent_id: 0,
    status_options: ["Connected", "Disconnected"],
    param_configs: {},
    pages: {
      map: false,
      real_time: false,
      detailed_view: {},
      panel: {
        real_time_parameters: [
          { key: "water_level" },
          { key: "flow_rate_1" },
          { key: "flow_rate_2" },
          { key: "flow_rate_3" },
          { key: "flow_rate_4" },
          { key: "motor_current_1" },
          { key: "motor_current_2" },
          { key: "motor_current_3" },
          { key: "motor_current_4" },
          { key: "motor_current_5" },
          { key: "motor_status_1" },
          { key: "motor_status_2" },
          { key: "motor_status_3" },
          { key: "motor_status_4" },
          { key: "motor_status_5" },
          { key: "totalized_flow_1" },
          { key: "totalized_flow_2" },
          { key: "totalized_flow_3" },
          { key: "totalized_flow_4" },
        ],
      },
    },
  },
  {
    id: 7,
    name: "Water Level Monitor",
    parent_id: 0,
    status_options: ["Connected", "Disconnected"],
    param_configs: {},
    pages: {
      map: {
        map_icons: {},
      },
      real_time: false,
      panel: {
        real_time_parameters: [],
      },
    },
  },
  {
    id: 19,
    name: "Temperature",
    parent_id: 0,
    show_switch: false,
    show_lock: false,
    threshold: true,
    status_options: ["Connected", "Disconnected"],
    pages: {
      map: false,
      panel: {
        real_time_parameters: [],
      },
      detailed_view: {
        parameter_trend: {
          parameters: [],
        },
      },
      real_time: false,
    },
  },
  {
    id: 90,
    name: "Temperature & Humidity",
    parent_id: 0,
    show_switch: false,
    show_lock: false,
    threshold: true,
    status_options: ["Connected", "Disconnected"],
    pages: {
      map: {
        map_icons: {
          online: TempAndHumidOnline,
          offline: TempAndHumidOffline,
          disconnected: TempAndHumidNotConnected,
        },
      },
      real_time: false,
      detailed_view: {
        parameter_trend: {
          parameters: [],
        },
      },
      panel: {
        real_time_parameters: [],
      },
    },
  },
  {
    id: 91,
    name: "Solar System",
    parent_id: 0,
    show_switch: false,
    show_lock: false,
    machine_info: [
      {
        label: "System Capacity",
        key: "system_capacity",
      },
      {
        label: "System Total Voltage (V)",
        key: "system_total_voltage",
      },
    ],
    status_options: ["Connected", "Disconnected"],
    pages: {
      real_time: {
        parameterDetails: [
          {
            key: "watt",
            custom_name: "Power",
            visualization: "meter",
            type: "small",
            ranges: [],
            min_value: 0,
            max_value: 0,
          },
          {
            key: "curr",
            custom_name: "Current",
            visualization: "meter",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [40, 50] }],
            min_value: 0,
            max_value: 50,
          },
          {
            key: "volt",
            custom_name: "Voltage",
            visualization: "meter",
            type: "small",
            ranges: [],
            min_value: 0,
            max_value: 0,
          },
          {
            key: "enrg",
            custom_name: "Energy meter",
            visualization: "flip",
          },
          {
            key: "calculated_runhour",
            format: "time",
            custom_name: "Generation Hour",
            visualization: "flip",
          },
        ],
      },
      detailed_view: {
        parameter_trend: {
          include_other_parameters: true,
          parameters: [
            {
              key: "enrg",
              name: "Energy",
              summaryParams: ["calculated_energy", "calculated_runhour"],
              noRealTimeValue: true,
            },
            { key: "watt" },
            { key: "volt" },
            { key: "curr" },
          ],
        },
      },
      map: {
        map_icons: {
          online: SolarPanelOngoing,
          offline: SolarPanelOffline,
          disconnected: SolarPanelNotConnected,
        },
      },
      list: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_energy",
            attribute: "sum",
          },
          {
            duration: "this_month",
            parameter: "calculated_energy",
            attribute: "sum",
          },
        ],
        real_time_parameters: [
          {
            key: "watt",
          },
          {
            key: "volt",
          },
          {
            key: "curr",
          },
        ],
      },
      panel: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_energy",
            attribute: "sum",
          },
          {
            duration: "yesterday",
            parameter: "calculated_energy",
            attribute: "sum",
          },
          {
            duration: "this_month",
            parameter: "calculated_energy",
            attribute: "sum",
          },
          {
            duration: "last_month",
            parameter: "calculated_energy",
            attribute: "sum",
          },
        ],
        real_time_parameters: [
          {
            key: "watt",
          },
        ],
      },
    },
  },
  {
    id: 92,
    name: "Battery",
    parent_id: 0,
    show_fault: true,
    show_switch: false,
    show_lock: false,
    threshold: true,
    machine_info: [
      {
        label: "Rated Voltage(V)",
        key: "rated_capacity",
      },
      {
        label: "Rated Capacity(Ah)",
        key: "rated_voltage",
      },
    ],
    status_options: ["Connected", "Disconnected"],
    pages: {
      map: {
        map_icons: {
          online: BatteryOnline,
          offline: BatteryOffline,
          disconnected: BatteryNotConnected,
        },
      },
      real_time: false,
      detailed_view: {
        parameter_trend: {
          include_other_parameters: true,
          parameters: [
            {
              key: "c_enrg",
              summaryParams: ["calculated_c_enrg"],
              noRealTimeValue: true,
              type: "enrg",
            },
            {
              key: "d_enrg",
              summaryParams: ["calculated_d_enrg"],
              noRealTimeValue: true,
              type: "enrg",
            }
          ],
        },
      },
      panel: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_c_enrg",
            baseParameter: "c_enrg",
            attribute: "sum",
          },
          {
            duration: "this_month",
            parameter: "calculated_c_enrg",
            baseParameter: "c_enrg",
            attribute: "sum",
          },
          {
            duration: "today",
            parameter: "calculated_d_enrg",
            baseParameter: "d_enrg",
            attribute: "sum",
          },
          {
            duration: "this_month",
            parameter: "calculated_d_enrg",
            baseParameter: "d_enrg",
            attribute: "sum",
          },
        ],
        real_time_parameters: [],
      },
    },
  },
  {
    id: 93,
    name: "Solar Inverter",
    parent_id: 0,
    show_fault: true,
    show_switch: false,
    show_lock: false,
    status_options: ["Connected", "Disconnected"],
    pages: {
      map: {
        map_icons: {
          online: SolarInverterOnline,
          offline: SolarInverterOffline,
          disconnected: SolarInverterNotConnected,
        },
      },
      detailed_view: {
        parameter_trend: {
          include_other_parameters: true,
          parameters: [
            {
              key: "enrg",
              summaryParams: ["calculated_energy"],
              noRealTimeValue: true,
            },
            {
              key: "dcl_enrg",
              summaryParams: ["calculated_dcl_energy"],
              noRealTimeValue: true,
            },
          ],
        },
      },
      panel: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_mains_energy",
            attribute: "sum",
          },
          {
            duration: "this_month",
            parameter: "calculated_mains_energy",
            attribute: "sum",
          },
        ],
        real_time_parameters: [
          {
            key: "hs_temp",
          },
          {
            key: "in_temp",
          },
          {
            key: "ld_st",
            dataType: "integer",
          },

        ],
      },
      real_time: false,
    },
  },
  {
    id: 95,
    name: "",
    parent_id: 0,
    status_options: ["Connected", "Disconnected"],
    param_configs: {},
    pages: {
      map: false,
      real_time: false,
      panel: {
        real_time_parameters: [
          { key: "occp" },
          { key: "heart" },
          { key: "resp" },
          { key: "ncbp" },
          { key: "sleep" },
        ],
      },
      detailed_view: {
        parameter_trend: {
          parameters: [
            { key: "occp" },
            { key: "heart" },
            { key: "resp" },
            { key: "ncbp" },
            { key: "sleep" },
          ],
        },
      },
    },
  },
  {
    id: 96,
    name: "Gas Genset",
    parent_id: 0,
    show_switch: true,
    show_lock: true,
    status_options: ["Running", "Stopped", "Disconnected"],
    machine_info: [
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
      {
        label: "KVA",
        key: "kva",
      },
    ],
    pages: {
      map: {
        map_icons: {
          online: DGMapOnline,
          offline: DGMapOffline,
          switch_off: DGMapSwitchOff,
          disconnected: DGMapNotConnected,
        },
        customer_specific_map_icons: {
          1184: {
            online: SistemaBioGensetRunning,
            offline: SistemaBioGensetOffline,
            switch_off: SistemaBioGensetOffline,
            disconnected: SistemaBioGensetOffline,
          }
        }
      },
      panel: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_energy",
            attribute: "sum",
            name: "Energy",
          },
          {
            duration: "today",
            parameter: "calculated_runhour",
            attribute: "sum",
            type: "time",
            name: "Runhour",
          },
        ],
        real_time_parameters: [
          {
            key: "vbat",
          },
          {
            key: "temp",
          },
          {
            key: "press",
          },
        ],
      },
      detailed_view: {
        parameter_trend: {
          parameters: [],
        },
      },
      real_time: {
        tabs: [
          { label: "Engine", key: "engine" },
          { label: "Generator", key: "generator" },
          { label: "Mains", key: "mains" },
          { label: "ATS", key: "ats" }
        ],
        parameterDetails: [
          {
            key: "vbat",
            visualization: "meter",
            type: "small",
            ranges: [],
            min_value: 0,
            max_value: 40,
            tab: "engine",
          },
          {
            key: "rpm",
            visualization: "meter",
            type: "large",
            ranges: [{ color: "#ff0000", ranges: [1600, 2000] }],
            min_value: 0,
            max_value: 2000,
            tab: "engine",
          },
          {
            key: "press",
            visualization: "meter",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [8, 10] }],
            min_value: 0,
            max_value: 10,
            tab: "engine",
          },
          {
            key: "temp",
            visualization: "meter",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [100, 120] }],
            min_value: 0,
            max_value: 120,
            tab: "engine",
          },
          {
            key: "calculated_runhour",
            custom_name: "Run Hour",
            visualization: "flip",
            tab: "others",
            format: "time",
            tab: "engine",
          },
          {
            key: "gas_consume",
            visualization: "flip",
            tab: "engine",
          },
          {
            key: "load_percentage",
            visualization: "meter",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [90, 120] }],
            min_value: 0,
            max_value: 120,
            tab: "generator",
          },
          {
            key: "enrg",
            visualization: "flip",
            tab: "generator",
            custom_name: "Active Energy",
          },
          {
            key: "hz",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [47, 53] },
              { color: "#ff0000", ranges: [52, 60] },
            ],
            min_value: 40,
            max_value: 60,
            phase_values: ["hz_r", "hz_y", "hz_b"],
            tab: "generator",
            custom_name: "Frequency",
          },
          {
            key: "curr",
            visualization: "meter",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [130, 135] }],
            min_value: 0,
            max_value: 135,
            phase_values: ["cur_ir", "cur_iy", "cur_ib"],
            tab: "generator",
            custom_name: "Current",
          },
          {
            key: "watt",
            visualization: "meter",
            type: "large",
            ranges: [{ color: "#ff0000", ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            phase_values: ["gpow_r", "gpow_y", "gpow_b"],
            tab: "generator",
            custom_name: "Active power",
          },
          {
            key: "volt",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [200, 250] },
              { color: "#ff0000", ranges: [250, 300] },
            ],
            min_value: 0,
            max_value: 300,
            phase_values: ["gvol_r", "gvol_y", "gvol_b"],
            tab: "generator",
            custom_name: "L-N Voltage",
          },
          {
            key: "volt_p",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [350, 440] },
              { color: "#ff0000", ranges: [440, 500] },
            ],
            min_value: 0,
            max_value: 500,
            phase_values: ["gvol_ry", "gvol_yb", "gvol_br"],
            tab: "generator",
            custom_name: "L-L Voltage",
          },
          {
            key: "apow",
            visualization: "meter",
            type: "large",
            ranges: [{ color: "#ff0000", ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            phase_values: ["gapow_r", "gapow_y", "gapow_b"],
            tab: "generator",
            custom_name: "Apparent power",
          },
          {
            key: "rpow",
            visualization: "meter",
            type: "large",
            ranges: [{ color: "#ff0000", ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            phase_values: ["grpow_r", "grpow_y", "grpow_b"],
            tab: "generator",
            custom_name: "Reactive power",
          },
          {
            key: "hz_r",
            visualization: "meter",
            custom_name: "L1 Frequency",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [47, 53] },
              { color: "#ff0000", ranges: [52, 60] },
            ],
            min_value: 40,
            max_value: 60,
            tab: "generator",
            phase: 'single',
          },
          {
            key: 'cur_ir',
            visualization: 'meter',
            type: 'small',
            custom_name: 'L1 Current',
            ranges: [{ color: '#ff0000', ranges: [130, 135] }],
            min_value: 0,
            max_value: 135,
            tab: 'generator',
            phase: 'single',
          },
          {
            key: 'gpow_r',
            visualization: 'meter',
            type: 'large',
            custom_name: 'L1 Active Power',
            ranges: [{ color: '#ff0000', ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: 'generator',
            phase: 'single',
          },
          {
            key: 'gvol_r',
            visualization: 'meter',
            type: 'small',
            custom_name: 'L1-N Voltage',
            ranges: [
              { color: '#1cd456', ranges: [200, 250] },
              { color: '#ff0000', ranges: [250, 300] },
            ],
            min_value: 0,
            max_value: 300,
            tab: 'generator',
            phase: 'single',
          },
          {
            key: 'gapow_r',
            visualization: 'meter',
            type: 'large',
            custom_name: 'L1 Apparent Power',
            ranges: [{ color: '#ff0000', ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: 'generator',
            phase: 'single',
          },
          {
            key: 'grpow_r',
            visualization: 'meter',
            type: 'large',
            custom_name: 'L1 Reactive Power',
            ranges: [{ color: '#ff0000', ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: 'generator',
            phase: 'single',
          },
          {
            key: "pf",
            custom_name: "Power Factor",
            visualization: "meter",
            type: "large",
            ranges: [],
            min_value: 0,
            max_value: 1,
            tab: "generator",
          },
          {
            key: "ga_energy",
            custom_name: "Apparent Energy",
            visualization: "flip",
            tab: "generator",
          },
          {
            key: "gr_energy",
            custom_name: "Reactive Energy",
            visualization: "flip",
            tab: "generator",
          },
          {
            key: "mvol",
            visualization: "meter",
            type: "small",
            custom_name: "L-N Voltage",
            ranges: [
              { color: "#1cd456", ranges: [207, 253] },
              { color: "#ff0000", ranges: [253, 300] },
            ],
            min_value: 0,
            max_value: 300,
            phase_values: ["mvol_r", "mvol_y", "mvol_b"],
            tab: "mains",
          },
          {
            key: "mvolt_p",
            visualization: "meter",
            type: "small",
            custom_name: "L-L Voltage",
            ranges: [
              { color: "#1cd456", ranges: [350, 440] },
              { color: "#ff0000", ranges: [440, 500] },
            ],
            min_value: 0,
            max_value: 500,
            phase_values: ["mvol_ry", "mvol_yb", "mvol_br"],
            tab: "mains",
          },
          {
            key: "mt_power",
            visualization: "meter",
            type: "large",
            custom_name: "Active Power",
            ranges: [{ color: "#ff0000", ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: "mains",
            phase_values: ["mpow_r", "mpow_y", "mpow_b"],
          },
          {
            key: "ma_power",
            visualization: "meter",
            type: "large",
            custom_name: "Apparent Power",
            ranges: [{ color: "#ff0000", ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: "mains",
            phase_values: ["mapow_r", "mapow_y", "mapow_b"],
          },
          {
            key: "mr_power",
            visualization: "meter",
            type: "large",
            custom_name: "Reactive Power",
            ranges: [{ color: "#ff0000", ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: "mains",
            phase_values: ["mrpow_r", "mrpow_y", "mrpow_b"],
          },
          {
            key: "mfreq",
            visualization: "meter",
            custom_name: "Frequency",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [47, 53] },
              { color: "#ff0000", ranges: [52, 60] },
            ],
            min_value: 40,
            max_value: 60,
            phase_values: ["mfreq_r", "mfreq_y", "mfreq_b"],
            tab: "mains",
          },
          {
            key: 'mvol_r',
            visualization: 'meter',
            type: 'small',
            custom_name: 'L1-N Voltage',
            ranges: [
              { color: '#1cd456', ranges: [207, 253] },
              { color: '#ff0000', ranges: [253, 300] },
            ],
            min_value: 0,
            max_value: 300,
            tab: 'mains',
            phase: 'single',
          },
          {
            key: 'mpow_r',
            visualization: 'meter',
            type: 'large',
            custom_name: 'L1 Active Power',
            ranges: [{ color: '#ff0000', ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: 'mains',
            phase: 'single',
          },
          {
            key: 'mapow_r',
            visualization: 'meter',
            type: 'large',
            custom_name: 'L1 Apparent Power',
            ranges: [{ color: '#ff0000', ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: 'mains',
            phase: 'single',
          },
          {
            key: 'mrpow_r',
            visualization: 'meter',
            type: 'large',
            custom_name: 'L1 Reactive Power',
            ranges: [{ color: '#ff0000', ranges: [190, 200] }],
            min_value: 0,
            max_value: 200,
            tab: 'mains',
            phase: 'single',
          },
          {
            key: "mfreq_r",
            visualization: "meter",
            custom_name: "L1 Frequency",
            type: "small",
            ranges: [
              { color: "#1cd456", ranges: [47, 53] },
              { color: "#ff0000", ranges: [52, 60] },
            ],
            min_value: 40,
            max_value: 60,
            tab: "mains",
            phase: 'single',
          },
          {
            key: "mt_pf",
            custom_name: "Power Factor",
            visualization: "meter",
            type: "large",
            ranges: [],
            min_value: 0,
            max_value: 1,
            tab: "mains",
          },
          {
            key: "mt_energy",
            custom_name: "Active Energy",
            visualization: "flip",
            tab: "mains",
          },
          {
            key: "ma_energy",
            custom_name: "Apparent Energy",
            visualization: "flip",
            tab: "mains",
          },
          {
            key: "mr_energy",
            custom_name: "Reactive Energy",
            visualization: "flip",
            tab: "mains",
          },
          {
            key: "def_con",
            visualization: "meter",
            type: "small",
            custom_name: "DEF Conc.",
            min_value: 0,
            max_value: 100,
            tab: "ats",
          },
          {
            key: "def_aft_t1",
            visualization: "meter",
            type: "small",
            custom_name: "DEF Tank Level",
            min_value: 0,
            max_value: 100,
            tab: "ats",
          },
          {
            key: "egr_counter_ver1",
            // custom_name: "",
            visualization: "flip",
            time_in: "minute",
            tab: "ats",
          },
          {
            key: "egr_f_min",
            // custom_name: "",
            visualization: "flip",
            format: "time",
            time_in: "minute",
            tab: "ats",
          },
          {
            key: "egr_hl_min",
            // custom_name: "",
            visualization: "flip",
            format: "time",
            time_in: "minute",
            tab: "ats",
          },
          {
            key: "time_to_fia",
            custom_name: "Inducement Timer",
            visualization: "flip",
            format: "time",
            time_in: "minute",
            tab: "ats",
          },
          // {
          //   key: "time_remain_ind_ovrd",
          //   custom_name: "SCR OID Time Left",
          //   visualization: "flip",
          //   format: "time",
          //   tab: "ats",
          // },
          // {
          //   key: "no_of_ovrd_remain",
          //   custom_name: "SCR OID Left",
          //   visualization: "flip",
          //   tab: "ats",
          // },
          {
            key: "scr_out_temp",
            visualization: "meter",
            type: "small",
            custom_name: "SCR Outlet Temp.",
            min_value: -273,
            max_value: 1735,
            tab: "ats",
          },
          {
            key: "doc_out_temp",
            visualization: "meter",
            type: "small",
            custom_name: "DOC Outlet Temp.",
            min_value: -273,
            max_value: 1735,
            tab: "ats",
          },
          {
            key: "amb_temp",
            visualization: "meter",
            type: "small",
            custom_name: "Ambient Temp.",
            min_value: 0,
            max_value: 60,
            tab: "ats",
          },
          {
            key: "baro_press",
            visualization: "meter",
            type: "small",
            custom_name: "Barometric Press.",
            min_value: 0,
            max_value: 2,
            tab: "ats",
          },
          {
            key: "indu_timer_1",
            visualization: "flip",
            custom_name: "Low Level inducement",
            format: "time",
            time_in: "minute",
            tab: "ats",
          },
          {
            key: "indu_timer_2",
            visualization: "flip",
            custom_name: "Severe level inducement",
            format: "time",
            time_in: "minute",
            tab: "ats",
          },
        ],
      },
    },
  },
  {
    id: 100,
    name: "Elevator",
    parent_id: 0,
    show_fault: true,
    show_switch: false,
    show_lock: false,
    status_options: ["Running", "Stopped", "Disconnected"],
    machine_info: [
      // {
      //   label: "Serial No.",
      //   key: "serial",
      // },
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
      {
        label: "Capacity",
        key: "capacity",
      },
    ],
    pages: {
      real_time: false,
      detailed_view: false,
      panel: {
        real_time_parameters: [
          {
            key: "floor",
            dataType: 'integer'
          },
          {
            key: "drxn",
            dataType: 'integer'
          },
          {
            key: "door",
            dataType: 'integer'
          },
          {
            key: "maintenance_mode",
            dataType: 'integer'
          },
          {
            key: "fire_mode",
            dataType: 'integer'
          },
          {
            key: "rescue_mode",
            dataType: 'integer'
          }
        ],
      },
      map: {
        map_icons: {
          online: ElevatorOnline,
          offline: ElevatorOffline,
          disconnected: ElevatorNotConnected,
          online_fault_not_defined: ElevatorFaultOnline,
          offline_fault_not_defined: ElevatorFaultOffline,
          disconnected_fault_not_defined: ElevatorFaultNotConnected,
        },
      },
    },
  },
  {
    id: 103,
    name: "Solar Pump",
    parent_id: 0,
    show_fault: true,
    show_switch: false,
    show_lock: false,
    status_options: ["Running", "Stopped", "Disconnected"],
    machine_info: [
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
      {
        label: "Serial Number",
        key: "serial",
      },
      {
        label: "Motor Nominal Voltage",
        key: "rated_voltage",
        unit: "V",
      },
      {
        label: "Motor Nominal Current",
        key: "rated_current",
        unit: "A",
      },
      {
        label: "Motor Nominal Frequency",
        key: "rated_freq",
        unit: "Hz",
      },
      {
        label: "Motor Nominal Speed",
        key: "rated_speed",
        unit: "RPM",
      },
      {
        label: "Motor Nominal Power",
        key: "rated_power",
        unit: "kW",
      },
      {
        label: "Motor Nominal Power Factor",
        key: "rated_power_factor",
        unit: "",
      },
    ],
    pages: {
      panel: {
        summary_value: [
          {
            duration: "today",
            parameter: "calculated_energy",
            attribute: "sum",
          },
          {
            duration: "this_month",
            parameter: "calculated_energy",
            attribute: "sum",
          },
          {
            duration: "today",
            parameter: "calculated_runhour",
            attribute: "sum",
            type: "time_format",
          },
          {
            duration: "this_month",
            parameter: "calculated_runhour",
            attribute: "sum",
            type: "time_format",
          },
        ],
        real_time_parameters: [
          {
            key: "rpm",
          },
          {
            key: "rpm_per",
          },
          {
            key: "cmd_speed",
          },
          {
            key: "curr"
          },
          {
            key: "mtr_pow"
          },
        ],
      },
      map: {
        map_icons: {
          online: SolarPumpRunning,
          offline: SolarPumpOffline,
          switch_off: SolarPumpStopped,
          disconnected: SolarPumpDisconnected,
          online_fault_trip: SolarPumpTripRunning,
          switch_off_fault_trip: SolarPumpTripStopped,
          disconnected_fault_trip: SolarPumpTripDisconnected,
          online_fault_warning: SolarPumpWarningRunning,
          switch_off_fault_warning: SolarPumpWarningStopped,
          disconnected_fault_warning: SolarPumpWarningDisconnected,
          online_fault_not_defined: SolarPumpNotDefinedRunning,
          switch_off_fault_not_defined: SolarPumpNotDefinedStopped,
          disconnected_fault_not_defined: SolarPumpNotDefinedDisconnected,
          online_fault_both: SolarPumpWarningTripRunning,
          switch_off_fault_both: SolarPumpWarningTripStopped,
          disconnected_fault_both: SolarPumpWarningTripDisconnected,
        },
      },
      detailed_view: {
        parameter_trend: {
          include_other_parameters: true,
          parameters: [
            {
              key: "enrg",
              summaryParams: ["calculated_energy"],
              noRealTimeValue: true,
              type: "enrg",
            },
            {
              key: "calculated_runhour",
              summaryParams: ["calculated_runhour"],
              noRealTimeValue: true,
              type: "rnhr"
            }
          ],
        },
      },
      real_time: {
        tabs: [
          { label: "Motor", key: "motor" },
        ],
        parameterDetails: [
          {
            key: "curr",
            custom_name: "Motor Current",
            visualization: "meter",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [135, 150] }],
            min_value: 0,
            max_value: 150,
            tab: "motor",
          },
          {
            key: "mtr_pow",
            custom_name: "Motor Power",
            visualization: "meter",
            type: "large",
            ranges: [
              { color: "#ff0000", ranges: [-50, -45] },
              { color: "#ff0000", ranges: [45, 50] },
            ],
            min_value: -50,
            max_value: 50,
            tab: "motor",
          },
          {
            key: "rpm",
            custom_name: "Motor RPM",
            visualization: "meter",
            type: "large",
            ranges: [
              { color: "#ff0000", ranges: [-1575, -1500] },
              { color: "#ff0000", ranges: [1500, 1575] },
            ],
            min_value: -1575,
            max_value: 1575,
            tab: "motor",
          },
          {
            key: "mtr_volt",
            custom_name: "Motor Voltage",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#ff0000", ranges: [223, 250] },
            ],
            min_value: 0,
            max_value: 250,
            tab: "motor",
          },
          {
            key: "enrg",
            custom_name: "Energy Counter",
            visualization: "flip",
            tab: "motor",
          },
          {
            key: "rnhr",
            custom_name: "Runhour Counter",
            visualization: "flip",
            tab: "motor",
          },
          {
            key: "hz",
            custom_name: "Drive Output Frequency",
            visualization: "meter",
            type: "large",
            ranges: [
              { color: "#ff0000", ranges: [-55, -50] },
              { color: "#ff0000", ranges: [50, 55] },
            ],
            min_value: -55,
            max_value: 55,
            tab: "motor",
          },
          // {
          //   key: "mtr_trq",
          //   custom_name: "Motor Torque",
          //   visualization: "meter",
          //   type: "large",
          //   ranges: [
          //     { color: "#ff0000", ranges: [-15, -12] },
          //     { color: "#ff0000", ranges: [12, 15] },
          //   ],
          //   min_value: -15,
          //   max_value: 15,
          //   tab: "motor",
          // },
          // {
          //   key: "sft_pow",
          //   custom_name: "Motor Shaft Power",
          //   visualization: "meter",
          //   type: "large",
          //   ranges: [
          //     { color: "#ff0000", ranges: [-1, -0.7] },
          //     { color: "#ff0000", ranges: [0.7, 1] },
          //   ],
          //   min_value: -1,
          //   max_value: 1,
          //   tab: "motor",
          // },
          
        ],

      }
    },
  },
  {
    id: 104,
    name: "MRI Machine",
    parent_id: 0,
    show_fault: true,
    show_switch: false,
    show_lock: false,
    status_options: ["Connected", "Disconnected"],
    machine_info: [
      // Firmware Version
      // Magmom IP Address
      // Mac Address
      // Network type
      // Proxy

      // Chiller Type
      {
        label: "Make",
        key: "make",
      },
      {
        label: "Model",
        key: "model",
      },
      {
        label: "Serial Number",
        key: "serial",
      },
      {
        label: "Compressor 1 Type",
        key: "comp_stat_1",
      },
      {
        label: "Compressor 2 Type",
        key: "comp_stat_2",
      },
      // {
      //   label: "Chiller Type",
      //   key: "",
      // },

    ],
    pages: {
      list: {
        real_time_parameters: [
          {
            key: "helium_lvl",
          },
          {
            key: "helium_pressure",
          },
          {
            key: "case_temp",
          },
          {
            key: "heater_off_psi",
          },
          {
            key: "heater_on_psi",
          },
          {
            key: "water_temp",
          },
          {
            key: "shield_si410",
          },
          {
            key: "recon_ruo"
          },
          {
            key: "recon_si410",
          },
          {
            key: "cold_ruo"
          },
          {
            key: "comp_duty_cyc"
          },
          {
            key: "heater_duty_cyc"
          },
        ]
      },
      panel: {
        real_time_parameters: [
          {
            key: "helium_pressure",
          },
          {
            key: "helium_lvl",
          },
          {
            key: "case_temp",
          },
          {
            key: "heater_off_psi",
          },
          {
            key: "heater_on_psi",
          },
          {
            key: "water_temp",
          },
          {
            key: "shield_si410",
          },
          {
            key: "recon_ruo"
          },
          {
            key: "recon_si410",
          },
          {
            key: "cold_ruo"
          },
          {
            key: "comp_duty_cyc"
          },
          {
            key: "heater_duty_cyc"
          },
        ],
      },
      map: {
        map_icons: {
          online: mriMachineConnected,
          offline: mriMachineOffline,
          disconnected: mriMachineDisconnected,
        },
      },
      detailed_view: {
        parameter_trend: {
          parameters: [
            {
              key: "helium_lvl",
            },
            {
              key: "helium_pressure",
            },
            {
              key: "case_temp",
            },
            {
              key: "heater_off_psi",
            },
            {
              key: "heater_on_psi",
            },
            {
              key: "water_temp",
            },
            {
              key: "shield_si410",
            },
            {
              key: "recon_ruo"
            },
            {
              key: "recon_si410",
            },
            {
              key: "cold_ruo"
            },
            {
              key: "comp_duty_cyc"
            },
            {
              key: "heater_duty_cyc"
            },
          ],
        },
      },
      real_time: {
        parameterDetails: [
          {
            key: "helium_pressure",
            custom_name: "Helium Pressure",
            visualization: "meter",
            type: "small",
            ranges: [{ color: "#ff0000", ranges: [135, 150] }],
            min_value: 0,
            max_value: 150,
          },
          {
            key: "helium_lvl",
            custom_name: "Helium Level",
            visualization: "meter",
            type: "large",
            ranges: [
            ],
            min_value: 0,
            max_value: 100,
          }, 
          {
            key: "case_temp",
            custom_name: "Magmon Case Temperature",
            visualization: "meter",
            type: "small",
            ranges: [
              { color: "#ff0000", ranges: [35, 50] },
            ],
            min_value: 0,
            max_value: 50,
          },
          {
            key: "heater_off_psi",
            custom_name: "Heater Off PSI",
            visualization: "meter",
            type: "large",
            ranges: [
              { color: "#ff0000", ranges: [20, 30] },
            ],
            min_value: 0,
            max_value: 30,
          },
          {
            key: "heater_on_psi",
            custom_name: "Heater On PSI",
            visualization: "meter",
            type: "large",
            ranges: [
              { color: "#ff0000", ranges: [35, 50] },
            ],
            min_value: 0,
            max_value: 50,
          },
          {
            key: "water_temp",
            custom_name: "Water Temperature",
            visualization: "meter",
            type: "large",
            ranges: [
              { color: "#ff0000", ranges: [22, 30] },
            ],
            min_value: 0,
            max_value: 30,
          },
          {
            key: "shield_si410",
            custom_name: "Shield Si410",
            visualization: "meter",
            type: "large",
            ranges: [
              { color: "#ff0000", ranges: [90, 120] },
            ],
            min_value: 0,
            max_value: 120,
          },
          {
            key: "recon_ruo",
            custom_name: "Recon RuO",
            visualization: "meter",
            type: "large",
            ranges: [
              { color: "#ff0000", ranges: [5, 10] },
            ],
            min_value: 0,
            max_value: 10,
          },
          {
            key: "recon_si410",
            custom_name: "Recon Si410",
            visualization: "meter",
            type: "large",
            ranges: [
              { color: "#ff0000", ranges: [5, 10] },
            ],
            min_value: 0,
            max_value: 10,
          },
          {
            key: "cold_ruo",
            custom_name: "Coldhead RuO",
            visualization: "meter",
            type: "large",
            ranges: [
              { color: "#ff0000", ranges: [4, 10] },
            ],
            min_value: 0,
            max_value: 10,
          },
          {
            key: "comp_duty_cyc",
            custom_name: "Compressor Duty Cycle",
            visualization: "meter",
            type: "large",
            ranges: [],
            min_value: 0,
            max_value: 100,
          },
          {
            key: "heater_duty_cyc",
            custom_name: "Heater Duty Cycle",
            visualization: "meter",
            type: "large",
            ranges: [
            ],
            min_value: 0,
            max_value: 100,
          }, 
          {
            key: "water_flow",
            custom_name: "Water Flow",
            visualization: "meter",
            type: "large",
            ranges: [
            ],
            min_value: 0,
            max_value: 100,
          }, 
        ],

      }
    },
  }
];
