import React from "react";
import "./style.less";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import AntSpin from "@datoms/react-components/src/components/AntSpin";
import _find from "lodash/find";
import ChargeIcon from "../../images/charge.svg";
import DisChargeIcon from "../../images/charge.svg";

export default class BatteryPanelBody extends React.Component {
  constructor(props) {
    super(props);
  }
  paramNameWithThreshold(params) {
    return (
      <div className="title-sub-title">
        <div className="params-title">
          <div className="name hellip">{params?.name}</div>
          <div className="unit">
            {params?.unit.length ? ` (${params?.unit})` : ""}
          </div>
        </div>
      </div>
    );
  }

  getSummaryParams(data, paramKey) {
    return data?.panel_summary_params.filter(
      (config) => config?.key === paramKey,
    );
  }

  render() {
    const { daily_data_loading, data } = this.props;
    return (
      <div id="bv_body_id">
        <div className="total-today-section-container">
          {daily_data_loading?.[data.id] ? (
            <AntSpin size="small" />
          ) : (
            <>
              <div className="today-section-container">
                {this.getSummaryParams(data,"calculated_c_enrg").length ? (
                  <span className="summary-header small-text">
                    Charging Energy (kWh):
                  </span>
                ) : (
                  ""
                )}
				{this.getSummaryParams(data,"calculated_c_enrg").map((summary) => {
                //   if ([0, 1].includes(ind)) {
                    return (
                      <span
                        className="span-section small-text"
                        key={summary.param_duration}
                      >
                        <span className="summary-title ">
                          {summary.param_duration === "today"
                            ? "Today"
                            : "This Month"}
                        </span>
                        <span className="panel-value">
                          {`${summary.param_value}`}
                        </span>
                      </span>
                    );
                //   }
                })}
              </div>
              <div className="today-section-container">
                {this.getSummaryParams(data,"calculated_d_enrg").length ? (
                  <span className="summary-header small-text">
                    Discharging Energy (kWh):
                  </span>
                ) : (
                  ""
                )}
				{this.getSummaryParams(data,"calculated_d_enrg").map((summary) => {
                    return (
                      <span
                        className="span-section small-text"
                        key={summary.param_duration}
                      >
                        <span className="summary-title ">
                          {summary.param_duration === "today"
                            ? "Today"
                            : "This Month"}
                        </span>
                        <span className="panel-value">
                          {`${summary.param_value}`}
                        </span>
                      </span>
                    );
                })}
              </div>
            </>
          )}
        </div>
        <div className="real-time-parameters">
          {data?.real_time_params?.map((params) => {
            if (["t_volt", "soc"].includes(params.key)) {
              let chargeSt = 0;
              if (params.key === "soc") {
                chargeSt = _find(data?.real_time_params, {
                  key: "c_st",
                });
              }
              return (
                <AntTooltip placement="topLeft" title={params.tooltipDetails}>
                  <div className="values">
                    <div className="data">
                      {params?.value}
                      {params.key === "soc" ? (
                        <img
                          src={
                            chargeSt === 0
                              ? ChargeIcon
                              : chargeSt === 1
                                ? DisChargeIcon
                                : ""
                          }
                        />
                      ) : (
                        ""
                      )}
                    </div>
                    {this.paramNameWithThreshold(params)}
                  </div>
                </AntTooltip>
              );
            }
          })}
        </div>
      </div>
    );
  }
}
