import React from 'react';
import './style.less';
import AntTooltip from '@datoms/react-components/src/components/AntTooltip';
import _filter from 'lodash/filter';

export default class CompilanceMonitoringPanelBody extends React.Component {
	constructor(props) {
		super(props);
	}

	// Helper method to get filtered parameters
	getFilteredParams() {
		const { data } = this.props;
		return data?.real_time_params?.filter(param => param.key !== 'dr_st') || [];
	}
	
	singleLineParam(filteredParams) {
		return (
			filteredParams.length === 1 ||
			filteredParams.length === 3 ||
			filteredParams.length === 4
		);
	}
	
	strLengthForParam(content, length) {
		let modifiedContent = content;
		if (content.length > length) {
			modifiedContent = content.substring(0, length) + '..';
		}

		return modifiedContent;
	}
	
	paramNameWithThreshold(params, filteredParams) {
		const { data, isMapView } = this.props;
		let csParamName = '',
			hasOneTemp = '',
			hasOneHumid = '';
		if (data.category === 45) {
			const csParams = {
				temperature1: 'T1',
				humidity1: 'H1',
				temperature2: this.props.client_id === 13853 ? 'Temp-2' : 'T2',
				humidity2: this.props.client_id === 13853 ? 'Humid-2' : 'H2',
				temperature3: 'T3',
				humidity3: 'H3',
			};
			hasOneTemp = _filter(filteredParams, function (o) {
				return o.key.includes('temperature');
			})?.length;
			hasOneHumid = _filter(filteredParams, function (o) {
				return o.key.includes('humidity');
			})?.length;
			csParamName =
				hasOneTemp === 1 && params?.key.includes('temperature')
					? this.props.client_id === 13853 ? 'Temp' : 'Temperature'
					: hasOneHumid === 1 && params?.key.includes('humidity')
						? this.props.client_id === 13853 ? 'Humid' :'Humidity'
						: csParams[params?.key]
							? csParams[params?.key]
							: params?.name;
		}
		return (
			<div className="title-sub-title">
				<div className="params-title">
					<div className="name">
						{data.category === 45
							? csParamName
							: filteredParams.length === 1 ||
									filteredParams.length === 2 || isMapView
								? params?.name
								: this.strLengthForParam(
										params?.name,
										params.key === 'temp' ? 4 : 5,
									)}
					</div>
					<div className="unit">
						{params?.unit.length
							? '(' +
								this.strLengthForParam(params?.unit, 7) +
								')'
							: ''}
					</div>
				</div>
				{data?.threshold_tooltip ? (
					''
				) : (
					<div className="params-sub-title">
						{params?.thresholdString
							? 'Threshold: ' + params?.thresholdString
							: ''}
					</div>
				)}
			</div>
		);
	}
	
	render() {
		const { data } = this.props;
		const filteredParams = this.getFilteredParams();
		
		return (
			<div id="pomo_panel_body_id">
				<div
					className={
						// 'real-time-parameters danger ' +
						'real-time-parameters ' +
						(window.innerWidth < 576 ? 'one-param' : (this.singleLineParam(filteredParams) ? 'single-line ' : '') +
						(filteredParams.length === 1
							? 'one-param'
							: filteredParams.length === 2
								? 'two-param'
								: ''))
					}
				>
					{filteredParams.map((params, ind) => {
						if (ind < 8) {
							let upperThreshold, lowerThreshold;
							upperThreshold = params.upperThreshold;
							lowerThreshold = params.lowerThreshold;
							if (params.eqms_ip_camera) {
								return (
									<div className="ip-camera-param">
										<img src={params.eqms_ip_camera.icon} />
										<div
											className={
												'status ' +
												(params.eqms_ip_camera
													.status === '2'
													? 'offline'
													: 'online')
											}
										></div>
									</div>
								);
							} else {
								return (
									<AntTooltip
										placement="topLeft" title={params.tooltipDetails?.length ? params.tooltipDetails : params.name}
									>
										<div
											className={
												'values ' +
												(parseFloat(params?.value) >
													parseFloat(
														upperThreshold,
													) ||
												parseFloat(params?.value) <
													parseFloat(lowerThreshold)
													? 'danger'
													: '')
											}
										>
											{params?.value}
											{this.paramNameWithThreshold(
												params,
												filteredParams
											)}
										</div>
									</AntTooltip>
								);
							}
						}
					})}
				</div>
			</div>
		);
	}
}
