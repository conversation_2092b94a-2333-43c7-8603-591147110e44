import React, { useState } from "react";
import "./style.less";
import ClockCircleOutlined from "@ant-design/icons/ClockCircleOutlined";
import LayoutOutlined from "@ant-design/icons/LayoutOutlined";
import PanelBody from "../PanelBody";
import FlowMeterPanelBody from "../FlowMeterPanelBody";
import BorewellPanelBody from "../BorewellPanelBody";
import CompilanceMonitoringPanelBody from "../CompilanceMonitoringPanelBody";
import TempHumidPanelBody from "../TempHumidPanelBody";
import BatteryPanelBody from "../BatteryPanelBody";
import EnergyMeterPanelBody from "../EnergyMeterPanelBody";
import PanelUpperSection from "../PanelUpperSection";
import IpcameraPanelBody from "../IpcameraPanelBody";
import _find from "lodash/find";
import SolarPanelBody from "../SolarPanelBody";
import ACEnergyMeterPanelBody from "../ACEnergyMeterPanelBody";
import ACElectricalMachinePanelBody from "../ACElectricalMachinePanelBody";
import ProcessAnalyzerPanelBody from "../ProcessAnalyzerPanelBody";
import DGPanelBody from "../DGPanelBody";
import FuelTankPanelBody from "../FuelTankPanelBody";
import ExhaustFanPanelBody from "../ExhaustFanPanelBody";
import DetailedPanelBody from "../DetailedPanelBody";
import SolarInverterPanelBody from "../SolarInverterPanelBody";
import ElevatorPanelBody from "../ElevatorPanelBody";
import SolarPumpPanelBody from "../SolarPumpPanelBody";
import MRIPanelBody from '../MRIPanelBody/index.tsx';

export default function PanelComponent(props) {
  let panels = [];
  panels.push(
    <div id="panel_component" style={{ height: props?.isThermofisher ? 406 : undefined}}>
      <PanelUpperSection data={props} t={props.t}/>
      <div
        className={
          "lower-section " +
          (props?.data?.location === undefined ||
          props?.data?.location.length === 0
            ? "no-address"
            : "")
        }
        style={{ background: props?.isThermofisher ? "none" : undefined }}
      >
        {props?.isThermofisher ? (
          <DetailedPanelBody
            dataColumns={props?.data?.table_format_data || []}
          />
        ) : props?.data?.category === 91 ? (
          <SolarPanelBody {...props} />
        ) : props?.data?.category === 78 ? (
          <ACElectricalMachinePanelBody {...props} />
        ) : props?.data?.category === 79 || props?.data?.category === 101 ? (
          <ACEnergyMeterPanelBody {...props} />
        ) : props?.data?.category === 44 ? (
          <IpcameraPanelBody {...props} />
        ) : [77].includes(props?.data?.category) ? (
          <EnergyMeterPanelBody {...props} />
        ) : [21, 22, 102, 23, 45, 19, 76, 95].includes(props?.data?.category) ? (
          <CompilanceMonitoringPanelBody {...props} />
        ) : [90].includes(props?.data?.category) ? (
          <TempHumidPanelBody {...props} />
        ) : [92].includes(props?.data?.category) ? (
          <BatteryPanelBody {...props} />
        ) : props?.data?.category === 86 ? (
          <FlowMeterPanelBody {...props} />
        ) : props?.data?.category === 63 ? (
          <BorewellPanelBody {...props} />
        ) : props?.data?.category === 94 ? (
          <ProcessAnalyzerPanelBody {...props} />
        ) : props?.data?.category === 99 ? (
          <ExhaustFanPanelBody {...props} />
        ) : props?.data?.category === 18 || props?.data?.category === 96 ? (
          <DGPanelBody {...props} />
        ) : props?.data?.category === 71 ? (
          <FuelTankPanelBody {...props} />
        ) : props?.data?.category === 93 ? (
          <SolarInverterPanelBody {...props} />
        ) : props?.data?.category === 100 ? (
          <ElevatorPanelBody {...props} />
        ) : props?.data?.category === 103 ? (
          <SolarPumpPanelBody {...props} />
        ) : props?.data?.category === 104 ?(
          <MRIPanelBody {...props} />
        ) : (
          <PanelBody {...props} />
        )} 
        {props?.linksPosition !== "top" && (
          <div className="page-links">
            {props?.data?.is_detailed_view ? (
              <div
                className="link"
                onClick={() => props.goToPage("detailed-view", props?.data?.id)}
              >
                <LayoutOutlined />
                {props.t? props.t('detailed_view'): "Detailed View"}
                {/* Detailed View */}
              </div>
            ) : (
              ""
            )}
            {props?.data?.is_real_time ? (
              <div

              
                className="link"
                onClick={() => props.goToPage("real-time", props?.data?.id)}
              >
                <ClockCircleOutlined />
                {props.t? props.t('analog_view'): "Analog View"}
                {/* Analog View */}
              </div>
            ) : (
              ""
            )}
          </div>
        )}
      </div>
    </div>,
  );
  return panels;
}
