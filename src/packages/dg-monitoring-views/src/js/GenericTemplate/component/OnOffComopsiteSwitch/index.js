import React from "react";
import { triggerCommand } from "@datoms/js-sdk";
import ControlButton from "@datoms/react-components/src/components/ControlButton";
import { things_categories } from "../../utility/thing_categories";
import _find from "lodash/find";
import "./style.less";

export default function OnOffCompositeSwitch(props) {
  let array = [];
  const thingInfo = _find(things_categories, { id: props.category_id });
  const operation_mode = thingInfo?.operation_mode || props.operation_mode;
  const thingCatName = thingInfo ? thingInfo.name : "DG Set";
  const lockCommand = thingInfo?.lock_command || {onCommand: "dg_lock", offCommand: "dg_unlock"};
  const switchCommand = thingInfo?.switch_command || {onCommand: "dg_start", offCommand: "dg_stop"};
  const isLockControlEnabled = thingInfo?.show_lock || props.isLockControlEnabled;

  if (props.getRemoteLockAccess && isLockControlEnabled) {
    array.push(
      <ControlButton
        dgStatus={props.dgStatus}
        socket={props.socket}
        client_id={props.client_id}
        application_id={props.application_id}
        thing_id={props.thingId}
        thingCommandStatus={props.commandStatus}
        isControlEnabled={props.isLockControlEnabled}
        operation_mode={operation_mode}
        getViewAccess={props.getRemoteLockAccess}
        {...lockCommand}
        subTitle={
          <p>
            Please enter your password to{" "}
            <span>{props.dg_lock_status === "1" ? "unlock" : "lock"}</span> the{" "}
            {thingCatName}.
          </p>
        }
        triggerCommand={triggerCommand}
        assetType={thingCatName}
        dgLockStatus={props.dg_lock_status}
        isRentalDG={true}
        rent_status={"ongoing"}
        featureLockUnlock={true}
        isOnlyLock={true}
      />,
    );
  }

  if (props.getRemoteAccess) {
    array.push(
      <ControlButton
        dgStatus={props.dgStatus}
        socket={props.socket}
        client_id={props.client_id}
        application_id={props.application_id}
        thing_id={props.thingId}
        thingCommandStatus={props.commandStatus}
        isControlEnabled={props.isControlEnabled}
        getViewAccess={props.getRemoteAccess}
        {...switchCommand}
        modalTitle={
          props.dgStatus === "1"
            ? `${thingCatName} Switch Off`
            : `${thingCatName} Switch On`
        }
        subTitle={
          <p>
            Please enter your password to{" "}
            <span>{props.dgStatus === "1" ? "switch off" : "switch on"} </span>
            the {thingCatName}.
          </p>
        }
        enableTooltip={true}
        enableSideText={false}
        triggerCommand={triggerCommand}
        assetType={thingCatName}
      />,
    );
  } else {
    array.push(
      <div
        className={
          "no-icon-switch " +
          (props.dgStatus === "2"
            ? "offline"
            : props.dgStatus === "1"
              ? "online"
              : "switch-off")
        }
      >
        {props.dgStatus === "2"
          ? props.deviceStatus === "online"
            ? "Disconnected"
            : ""
          : props.dgStatus === "1"
            ? "Running"
            : "Stopped"}
      </div>,
    );
  }
  return <div id="on_off_composite">{array}</div>;
}
