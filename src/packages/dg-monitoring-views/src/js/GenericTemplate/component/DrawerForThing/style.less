.drawer-for-thing-status {
	z-index: 110;
	.ant-drawer-content-wrapper {
		box-shadow: none !important;
		.ant-drawer-content {
				.ant-drawer-body {
					padding: 0px !important;
					background: #3d3d3d;
					.search-container {
						margin: 10px 0;
						.ant-input-affix-wrapper {
							.ant-input {
								background-color: #c4c2c240 !important;
								box-shadow: none;
							}
						}
					}
					.things-details {
						padding: 10px 30px !important;
						cursor: pointer;
						padding: 5px;
						border-bottom: 1px solid rgba(227, 227, 227, 0.459);
						.things-name {
							text-align: left;
							font-size: 14px;
							letter-spacing: 0px;
							color: #808080;
							opacity: 1;
						}
						.status-shown-div {
							text-align: left;
							font-size: 12px;
							letter-spacing: 0px;
							opacity: 1;
						}
						.off-thing {
							color: #ff4904;
						}
						.running-thing {
							color: #25c479;
						}
					}
					.selected-thing {
						background: #232323;
						.things-name {
							font-weight: 600;
							color: #fff;
						}
					}
					.no-search {
						.no-data-icon {
							.text {
								color: #fff;
								margin-top: 10px;
								font-style: italic;
							}
						}
					}
				}
			
		}
	}
	&.search-drawer-light {
		.ant-drawer-content-wrapper .ant-drawer-content .ant-drawer-body {
			background: #fff;
			.selected-thing {
				background: #c4c2c23f;
				.things-name {
					color: #232323;
				}
			}
		}
	}
}
