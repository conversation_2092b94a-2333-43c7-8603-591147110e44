import React from 'react';
import AntDrawer from '@datoms/react-components/src/components/AntDrawer';
import SearchInput from '@datoms/react-components/src/components/SearchInput';
import SearchObjectData from '../../../configuration/SearchObjectData';
import NoSearchImage from '../../../../images/DGMonitoring/no-data-icon/search.svg';
import './style.less';
import getOnlineOfflineOnlyStatus from '../../logic/status';
import { GlobalContext } from "../../../../../../../store/globalStore";

export default class DrawerForThingWithStatus extends React.Component {
	static contextType = GlobalContext;
	constructor(props) {
		super(props);
		this.state = {
			SearchObjectData: SearchObjectData,
		};
	}
	onSearch(e) {
		let SearchData = this.state.SearchObjectData;
		SearchData.placeholder = 'Search Asset';
		SearchData.search_data.value = e;
		SearchData.value = e;
		this.setState({
			SearchObjectData: SearchData,
		});
	}
	thingsDrawerClick(id) {
		this.props.thingsDrawerClick(id);
	}
	getStatus(status) {
		let statusClass, statusText;
		if(this.context?.isSistemaBioCustomer) {
			if(status === '1') {
				statusClass = ' online';
				statusText = 'Genset On';
			} else {
				statusClass = ' offline';
				statusText = 'Genset Off';
			}
		} else {
			if (status === '1') {
				statusClass = ' online';
				statusText = 'Running';
			} else if (status === '2') {
				statusClass = ' offline';
				statusText = 'Offline';
			} else {
				statusClass = ' switch-off';
				statusText = 'Stopped';
			}
		}
		return { statusClass, statusText };
	}

	render() {
		let thingsListDropdownItems = [
			<SearchInput
				t={this.props.t}
				onSearch={(e) => this.onSearch(e)}
				{...this.state.SearchObjectData}
			/>,
		];
		let drawerThings = [];
		if (
			this.props.drawer_data &&
			this.props.drawer_data.thingsList &&
			this.props.drawer_data.thingsList.length
		) {
			this.props.drawer_data.thingsList.map((thingsListData) => {
				if (
					thingsListData.name
						.toLowerCase()
						.includes(
							this.state.SearchObjectData.search_data.value.toLowerCase(),
						)
				) {
					drawerThings.push(
						<div
							onClick={(e) =>
								this.thingsDrawerClick(thingsListData.id)
							}
							className={
								'things-details ' +
								(parseInt(this.props.thingId) ===
								thingsListData.id
									? 'selected-thing'
									: '')
							}
						>
							<div className="things-name">
								{thingsListData.name}
							</div>
							{thingsListData?.no_status ? (
								''
							) : !thingsListData?.status_option_includes_stopped ? (
								getOnlineOfflineOnlyStatus(
									thingsListData?.onOffStatus,
								) === 'online' ? (
									<span className="status-shown-div online">
										Online
									</span>
								) : (
									<div className="offline">Offline</div>
								)
							) : (
								<div
									className={
										'status-shown-div' +
										(this.getStatus(thingsListData.onOffStatus).statusClass)
									}
								>
									{this.getStatus(thingsListData.onOffStatus).statusText}
								</div>
							)}
						</div>,
					);
				}
				return thingsListDropdownItems;
			});
		}
		if (drawerThings && drawerThings.length) {
			thingsListDropdownItems =
				thingsListDropdownItems.concat(drawerThings);
		} else {
			thingsListDropdownItems.push(
				<div className="no-search">
					<div className="no-data-icon">
						<div className="image">
							<img src={NoSearchImage} />
						</div>
						<div className="text">No results!</div>
					</div>
				</div>,
			);
		}
		return (
			<AntDrawer
				placement="left"
				visible={this.props.drawerVisible}
				onClose={() => this.props.closeDrawer()}
				closable={false}
				className={"drawer-for-thing-status" + (this.props.theme === "light" ? " search-drawer-light" : "")}
				getContainer={false}
				style={{ position: 'absolute' }}
				drawerStyle={{
					background: '#FFFFFF 0% 0% no-repeat padding-box',
					boxShadow: '15px 16px 25px #92929229',
				}}
				width={280}
				destroyOnClose={true}
				styleType={'rounded'}
			>
				{thingsListDropdownItems}
			</AntDrawer>
		);
	}
}
