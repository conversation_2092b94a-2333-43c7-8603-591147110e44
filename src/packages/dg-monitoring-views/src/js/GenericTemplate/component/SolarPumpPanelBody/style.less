#solar-pump-panel {
  background: #ffffff;
  border-radius: 14px;
  padding: 10px 18px;

  .value {
    font-weight: 500;
    font-size: 16px;
    color: #232323;
  }

  .main-stats {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 9px;

    .data-part {
      display: flex;
      flex-direction: column;
      gap: 12px;
      flex: 1;
    }

    .solar-icon {
      padding: 0 11px;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .solar-icon-big {
      width: 155px;
    //   height: 137px;
      align-self: flex-start;
      margin-right: 15px;
      @media (max-width: 768px) {
        margin-right: 0;
        width: 140px
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .speed-info {
      background-color: #f6f6f6;
      border-radius: 12px;
      padding: 7px 9px 6px;
      flex: 1;

      .motor-speed {
        display: flex;
        flex-wrap: wrap;
        .label {
            padding-right: 16px;
        }

        .percentage {
          font-size: 14px;
          color: #232323;
          margin-left: 4px;
        }
      }

      .command-speed {
        color: #808080;
        font-size: 12px;
        margin-bottom: 5px;
        font-style: italic;
      }

      .power-metrics {
        display: flex;
        gap: 30px;

        .metric {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 13px;

          .round-label {
            width: 20px;
            height: 20px;
            border-radius: 100%;
            background-color: #ffffff;
            color: #7689a1;
            font-size: 14px;
            text-align: center;
            line-height: normal;
          }

        }
      }
    }
  }

  .stats-container {
    display: flex;
    gap: 12px;

    .stats-box {
      background: #f5f5f5;
      border-radius: 10px;
      padding: 6px 10px;
      display: flex;
      flex-direction: column;
      gap: 4px;
      flex: 1;

      .title {
        line-height: 100%;
        color: #4f4f4f;
        font-size: 12px;
        font-weight: 500;
      }

      .stat-item {
        display: flex;
        align-items: center;
        font-size: 12px;
        line-height: 1.6;

        .label {
          color: #4f4f4f;
          width: 110px;
        }

        .value {
            line-height: 100%;
        }
      }
    }
  }
}
