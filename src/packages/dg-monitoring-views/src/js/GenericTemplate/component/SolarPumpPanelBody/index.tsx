import React from "react";
import SolarPumpIcon from "../../images/solar-pump-icon.svg";
import "./style.less";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import SkeletonLoader from "@datoms/react-components/src/components/SkeletonLoader";

type PanelSummaryParam = {
  key: "calculated_runhour" | "calculated_energy";
  param_name: string;
  unit: string;
  param_duration: string;
  param_value: string;
};

const SolarPumpPanel = (props: any) => {
  const isMapView = props?.view === "map";

  const getParameterValues = () => {
    let parameterValues: { [key: string]: any } = {};
    if (isMapView) {
      parameterValues = {...props.data}
    } else {
        if (Array.isArray(props?.data?.real_time_params)) {
            props.data.real_time_params.forEach((param: any) => {
              let paramValue = param?.value;
              if (paramValue !== "NA") {
                if (param.key === "rpm_per" && parseInt(paramValue) === 100) {
                  paramValue = "100";
                }
                paramValue = paramValue + " " + param?.unit;
              }
              parameterValues[param.key] = paramValue;
            });
        }

        if(Array.isArray(props?.data?.panel_summary_params)) {
            parameterValues.calculated_runhour = {};
            parameterValues.calculated_energy = {};
            
            props.data.panel_summary_params.forEach((param: PanelSummaryParam) => {
                let paramUnit = param.unit;
                let paramValue: string | number = param.param_value;
                if(param.key === "calculated_runhour") {
                    paramUnit = "HH:MM";
                }

                parameterValues[param.key]["title"] = param.param_name + " (" + paramUnit + ")";
                parameterValues[param.key][param.param_duration] = paramValue;
            });
        }
    }
    return parameterValues;
  };
  const paramValues = getParameterValues();

  const getSummaryCard = (
    summaryData: {title: string, today: string, this_month: string},
  ) => {
    return (
      <div className="stats-box">
        <div className="title">{summaryData.title}</div>
        <div className="stat-item">
          <span className="label">Today</span>
          <span className="value">{summaryData.today}</span>
        </div>
        <div className="stat-item">
          <span className="label">This Month</span>
          <span className="value">{summaryData.this_month}</span>
        </div>
      </div>
    );
  };

  const getFloorValue = (value: string) => {
    if(value && value !== "NA") {
      return Math.floor(Number(value.split(" ")[0])) + " " + value.split(" ")[1];
    }
    return "NA";
  }

  const MotorSpeedCard = () => {
    return (
        <div className="speed-info">
          <div className="motor-speed">
            <span className="label">Motor Speed</span>
            <AntTooltip title={paramValues.rpm + " (" + paramValues.rpm_per + ")"}>
                <span className="value">
                    {getFloorValue(paramValues.rpm)}
                </span>
                <span className="percentage">
                    {"(" + getFloorValue(paramValues.rpm_per) + ")"}
                </span>
            </AntTooltip>
          </div>
          <div className="command-speed">
            (Commanding Speed: {paramValues.cmd_speed})
          </div>
          <div className="power-metrics">
            <div className="metric">
              <AntTooltip title="Motor Current">
                <span className="round-label">A</span>
              </AntTooltip>
              <span className="value">{paramValues.curr}</span>
            </div>
            <div className="metric">
              <AntTooltip title="Motor Power">
                <span className="round-label">P</span>
              </AntTooltip>
              <span className="value">{paramValues.mtr_pow}</span>
            </div>
          </div>
        </div>
    )
  }

  const SummaryCards = () => {
    return (
        <div className="stats-container" style={{flexDirection: isMapView ? "column" : "row"}}>
            {getSummaryCard(paramValues.calculated_runhour)}
            {getSummaryCard(paramValues.calculated_energy)}
        </div>
    )
  }

  return (
    props.daily_data_loading?.[props.data?.id]
     ? <SkeletonLoader />
     : (
      <div id="solar-pump-panel">
        <div className="main-stats">
          <div className={isMapView ? "solar-icon-big" : "solar-icon"}>
            <img src={SolarPumpIcon} alt="Solar Pump" />
          </div>
          <div className="data-part">
              <MotorSpeedCard />
              {isMapView && <SummaryCards />}
          </div>
        </div>

        {!isMapView && <SummaryCards />}
      </div>
    )
  );
};

export default SolarPumpPanel;
