#common_header {
    padding: 16px 24px 16px 20px;
    background-color: #F8F8F8;
    display: flex;
    flex-direction: column;
    .upper-part {
        justify-content: space-between;
        align-items: flex-start;
        display: flex;
    }
    .total-header-div {
        display: flex;
        align-items: flex-start;
        .st-icon {
            margin-left: 40px;
        }
        .gch-left{
            display: flex;
            .icon {
                border-radius: 50%;
                width: 44px;
                height: 44px;
                text-align: center;
                margin: 0 12px 0 26px;
                .status-circle {
                    border-radius: 50%;
                    height: 12px;
                    width: 12px;
                    &.online {
                        background-color: #147437;
                    }
                    &.offline {
                        background-color: #808080;
                    }
                    &.switch-off {
                        background-color: #ff0000;
                    }
                }
            }
        }
        .gch-right {
            display: flex;
            flex-direction: column;
            .gch-upper {
                .name-date {
                    .thing-name {
                        color: #232323;
                        font-size: 14px;
                        font-weight: 600;
                    }
                    .time {
                        color: #808080;
                        font-size: 13px;
                        span {
                            margin-left: 10px;
                            border: 1px solid #707070;
                            color: #707070;
                            font-size: 12px;
                            padding: 2px 10px;
                            border-radius: 5px;
                        }
                    }
                }
            }
        }
        .on-off-lock-button{
            margin-left: 36px;
        }
    }
    .lower-part {
        display: flex;
        flex-wrap: wrap;
        padding-top: 3px;
        padding-left: 104px;
        
        .lower-part-val{
            font-size: 12px;
            color: #7686a1;
            margin-right: 12px;
            &.gch-m-wide{
                margin-right: 16px;
            }
        }
        .lower-part-val.location {
            width: 250px;
        }
        .maintenance-fault {
            display: flex;
            #active_faults{
                margin-right: 20px;
            }
        }
    }
}

@media(max-width: 1024px) {
    #common_header {
        #page_switch_internal {
            display: none;
        }
    }
}
