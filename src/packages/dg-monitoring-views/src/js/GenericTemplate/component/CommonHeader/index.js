import React from "react";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import ImageComponent from "@datoms/react-components/src/components/ImageComponent";
import _find from "lodash/find";
import MenuFoldOutlined from "@ant-design/icons/MenuFoldOutlined";
import "./style.less";
import showActiveFault from "../ShowActiveFaults";
import MaintenanceText from "../MaintenanceText";
import PageSwitchInternal from "../PageSwitchInternal";
import getOnlineOfflineOnlyStatus from "../../logic/status";
import { getAqiColorStatusForValue } from "../../logic/aqiParametersToShow";
import OnOffCompositeSwitch from "../OnOffComopsiteSwitch";
import { GlobalContext } from "../../../../../../../store/globalStore";

export default class CommonHeader extends React.Component {
  static contextType = GlobalContext;
  constructor(props) {
    super(props);
  }
  
  render() {
    const {
      analog_view,
      detailed_view,
      statusIcon,
      head_obj,
      isAurassure,
      devices,
      assetStatus,
      lifetime_runhour,
    } = this.props;
    let switchOptions = [];
    if (detailed_view) {
      switchOptions.push({ key: "detailed-view", name: "Detailed View" });
    }
    if (analog_view) {
      switchOptions.push({ key: "real-time", name: "Analog View" });
    }
    return (
      <div id="common_header">
        <div className="upper-part">
          <div className="total-header-div">
            <section className="gch-left">
              <MenuFoldOutlined
                t={this.props.t}
                onClick={() => this.props.showDrawer()}
                style={{
                  fontSize: 22,
                  marginTop: 4,
                  cursor: "pointer",
                }}
              />
              <div className="icon">
                {isAurassure || [44, 85].includes(head_obj?.cat) ? (
                  <ImageComponent
                    background={"white"}
                    iconSize="large"
                    src={
                      getAqiColorStatusForValue(
                        undefined, // aqi_value,
                        head_obj?.cat,
                        head_obj?.dgStatus,
                      )?.aqiMapIcon
                    }
                    show_status={false}
                    status={head_obj?.dgStatus === "1" ? "online" : "offline"}
                    deviceStatus={head_obj?.deviceStatus}
                  />
                ) : (
                  <ImageComponent
                    t={this.props.t}
                    background={"white"}
                    src={head_obj?.thing_category_icon}
                    show_status={true}
                    title={
                      _find(head_obj?.thing_details, {
                        key: "category",
                      })?.value
                    }
                    category={head_obj?.cat}
                    tooltip={true}
                    deviceStatus={
                      devices?.length ? head_obj?.deviceStatus : assetStatus
                    }
                    assetHasStatus={head_obj?.status_option_includes_stopped}
                    status={
                      !head_obj?.status_option_includes_stopped
                        ? getOnlineOfflineOnlyStatus(head_obj?.dgStatus) ===
                          "online"
                          ? "running"
                          : "offline"
                        : head_obj?.dgStatus === "1"
                          ? "running"
                          : head_obj?.dgStatus === "2"
                            ? "offline"
                            : "stopped"
                    }
                  />
                )}
              </div>
            </section>
            <section className="gch-right">
              <section className="gch-upper">
                <div className="name-date">
                  <div className="thing-name">{head_obj?.name}</div>
                  {head_obj?.no_status ? (
                    ""
                  ) : !head_obj?.status_option_includes_stopped ? (
                    getOnlineOfflineOnlyStatus(head_obj?.dgStatus) ===
                    "online" ? (
                      <div className="time online">{head_obj?.date}</div>
                    ) : (
                      <div className="time offline">
                        {this.props.t? this.props.t(head_obj?.date): head_obj?.date}
                        {this.props.isAurassure ||
                        head_obj?.deviceStatus !== "offline" ? (
                          ""
                        ) : (
                          <span>Offline</span>
                        )}
                      </div>
                    )
                  ) : (
                    <div className={"time "}>
                      {head_obj?.date}
                      {!this.context?.isSistemaBioCustomer && head_obj?.deviceStatus === "offline" ? (
                        <span>Offline</span>
                      ) : (
                        ""
                      )}
                    </div>
                  )}
                </div>
              </section>
            </section>
            {statusIcon ? (
              <AntTooltip title={statusIcon.text}>
                <div className="st-icon">
                  <img src={statusIcon.icon} />
                </div>
              </AntTooltip>
            ) : (
              ""
            )}

            {!this.props?.is_control_enabled ? (
              ""
            ) : (
              <div className="on-off-lock-button">
                <OnOffCompositeSwitch
                  dgStatus={this.props?.dgStatus}
                  deviceStatus={head_obj?.deviceStatus}
                  socket={this.props?.socket}
                  client_id={this.props?.client_id}
                  application_id={this.props?.application_id}
                  thingId={this.props?.thingId}
                  commandStatus={this.props?.command_status}
                  isLockControlEnabled={this.props?.is_lock_control_enabled}
                  isControlEnabled={this.props?.is_control_enabled}
                  operation_mode={this.props?.operation_mode}
                  dg_lock_status={this.props?.dg_lock_status}
                  getRemoteLockAccess={this.props?.getRemoteLockAccess}
                  getRemoteAccess={this.props?.getRemoteAccess}
                  status_option_includes_stopped={
                    this.props?.status_option_includes_stopped
                  }
                  category_id={head_obj?.cat}
                />
              </div>
            )}
          </div>
          <PageSwitchInternal
            t={this.props.t}
            value={this.props?.view_switch_value}
            changeView={this.props?.changeView}
            options={switchOptions}
            is_close={this.props.is_close}
            switchClose={this.props.switchClose}
            isAurassure={this.props.isAurassure}
          />
        </div>
        
        <section className="lower-part">
              {head_obj?.thing_details?.map((details) => {
                if (details.key !== "category") {
                  return details.value !== "NA" ? (
                    <AntTooltip title={details.value}>
                      <div className="hellip lower-part-val">
                        {details.name + (details.value ? details.value : "-")}
                      </div>
                    </AntTooltip>
                  ) : (
                    <div className="hellip lower-part-val">
                      {details.name + (details.value ? details.value : "-")}
                    </div>
                  );
                }
              })}
              {this.props.location ? (
                <AntTooltip title={"Location: " + this.props.location}>
                  <div className="lower-part-val gch-m-wide location hellip">
                    {"Location: " + this.props.location}
                  </div>
                </AntTooltip>
              ) : (
                ""
              )}
              {head_obj?.cat === 18 &&
              MaintenanceText(head_obj?.maintenance)?.length ? (
                <div className="maintenance-fault small-text">
                  {/* {showActiveFault(
									head_obj?.last_fault_data
								)} */}

                  {MaintenanceText(head_obj?.maintenance)}
                </div>
              ) : (
                ""
              )}

              {head_obj?.cat === 18 && lifetime_runhour ? (
                <div className="lower-part-val">
                  Lifetime Runhour: {lifetime_runhour}
                </div>
              ) : (
                ""
              )}
        </section>
      </div>
    );
  }
}
