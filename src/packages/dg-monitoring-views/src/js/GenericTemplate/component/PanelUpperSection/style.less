.upper-section {
    flex-grow: 0;
    padding: 8px 12px;
    .panel-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .thing-header-details {
            display: flex;
            align-items: center;
            .status-circle {
                border-radius: 50%;
                height: 12px;
                width: 12px;
                &.online {
                    background-color: #147437;
                }
                &.offline {
                    background-color: #808080;
                }
                &.switch-off {
                    background-color: #ff0000;
                }
            }
            .name-date {
                margin: 0 10px;
                .thing-name {
                    color: #232323;
                    font-size: 14px;
                    font-weight: 600;
                    width: 200px;
                }
                .time {
                    color: #808080;
                    font-size: 13px;
                    span {
                        margin-left: 10px;
                        border: 1px solid #707070;
                        color: #707070;
                        font-size: 12px;
                        padding: 2px 10px;
                        border-radius: 5px;
                    }
                }
            }
        }
        .data-availability-container-panel {
            text-align: center;
            .value {
                font-size: 13px;
                border: 1px solid #93ADD9;
                background-color: #EEF4FF;
                border-radius: 50%;
                width: 37px;
                height: 37px;
                margin: 0 auto;
                padding-top: 7px;
            }
            .title {
                font-size: 10px;
            }
        }
    }
    .thing-details {
        font-size: 11px;
        margin: 4px 0;
        display: flex;
        width: 100%;

        .details-container {
            display: flex;
            align-items: center;
            padding-left: 3px;
            width: 100%;
            overflow: hidden;
            position: relative;
        }

        .details-divs {
            margin-right: 15px;
            color: #8d9bb1;
            white-space: nowrap;
        }

        .details-more {
            cursor: pointer;
            user-select: none;
            display: inline-block;
            position: relative;
            background-color: #ffffff;
            padding: 0 2px;
            border-radius: 5px;
        }
    }

    .hidden-details-tooltip {
        .tooltip-detail-item {
            color: white;
            padding: 4px 0;
            white-space: nowrap;
        }
    }

    .lifetime-runhour{
        display: flex;
        margin-bottom: 5px;
        .lifetime-runhour-value {
            color: #232323;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            margin-left: 5px;
        }
    }
    .maintenance-fault {
        height: 20px;
        margin-bottom: 5px;
        display: flex;
        justify-content: space-between;
        #maintenance_text {
            width: 50%;
        }
        .lamp {
            display: flex;
            align-items: center;
            color: #232323;
            img {
                margin-left: 10px;
            }
        }
    }
    .location {
        color: #232323;
        margin-top: 5px;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .location-div {
            display: flex;
            align-items: center;
            width: calc(100% - 30px);
            .heading {
                color: #808080;
                display: flex;
                img {
                    margin-right: 5px;
                }
            }
            .location-value {
                margin-left: 5px;
            }
        }
        .open-in-maps {
            padding: 2px 4px;
            margin-left: 10px;
            background: #fff;
            border-radius: 20px;
            #datoms_maps_opener {
                margin: 0;
                .maps-opener-text {
                    color: #35507e;
                }
            }
        }
        .open-in-maps:hover {
            box-shadow: 6px 3px 10px #c4c2c250;
        }
    }
    .cs-door-st {
        display: flex;
        align-items: flex-start;
        margin-left: 60px;
        color: #232323 !important;
        img {
            margin: 0 5px;
        }
    }
}
