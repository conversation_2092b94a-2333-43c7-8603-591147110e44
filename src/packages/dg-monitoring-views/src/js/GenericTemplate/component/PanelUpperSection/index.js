import React, { Component } from "react";
import ClockCircleOutlined from "@ant-design/icons/ClockCircleOutlined";
import LayoutOutlined from "@ant-design/icons/LayoutOutlined";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import AntSpin from "@datoms/react-components/src/components/AntSpin";
import showActiveFault from "../ShowActiveFaults";
import MaintenanceText from "../MaintenanceText";
import OnOffCompositeSwitch from "../OnOffComopsiteSwitch";
import getOnlineOfflineOnlyStatus from "../../logic/status";
import ImageComponent from "@datoms/react-components/src/components/ImageComponent";
import OpenMaps from "@datoms/react-components/src/components/OpenMaps";
import _find from "lodash/find";
import "./style.less";
import DoorImage from "../../images/Door_Open.svg";
import LocationIcon from "../../images/location-icon.svg";
import RedLampIcon from "../../images/RedLamp.svg";
import WarningLampIcon from "../../images/WarningLamp.svg";
import GreyLampIcon from "../../images/GreyLamp.svg";

class PanelUpperSection extends Component {
  constructor(props) {
    super(props);
    this.containerRef = React.createRef();
    this.state = {
      visibleDetails: [],
      hiddenDetails: []
    };
  }

  componentDidMount() {
    this.calculateVisibleItems();
    window.addEventListener('resize', this.calculateVisibleItems);
  }

  componentDidUpdate(prevProps) {
    if (prevProps.data.data.thing_details !== this.props.data.data.thing_details) {
      this.calculateVisibleItems();
    }
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.calculateVisibleItems);
  }

  calculateVisibleItems = () => {
    const container = this.containerRef.current;
    if (!container) return;

    const thingDetails = (this.props.data.data.thing_details || []).filter(detail => detail.key !== "category");
    if (!thingDetails.length) return;

    // First show all items to measure them
    this.setState({ visibleDetails: thingDetails, hiddenDetails: [] }, () => {
      // After render, measure and update
      const containerWidth = container.offsetWidth;
      const items = [...container.children];
      let totalWidth = 0;
      let visibleCount = 0;

      // Reserve space for + x more text (approximately 65px: 15px for margin right, 50-57px for text)
      const moreWidth = 72;
      const detailDivMarginRight = 15;  // 15px is the margin right of each detail div
      
      for (let i = 0; i < items.length; i++) {
        const itemWidth = items[i].offsetWidth;
        if (totalWidth + itemWidth + (i < items.length - 1 ? moreWidth : 0) <= containerWidth) {
          totalWidth += itemWidth + detailDivMarginRight;
          visibleCount++;
        } else {
          break;
        }
      }

      this.setState({
        visibleDetails: thingDetails.slice(0, visibleCount),
        hiddenDetails: thingDetails.slice(visibleCount)
      });
    });
  }

  formatDetailText = (detail) => {
    const unit = detail.unit ?? '';
    const val = detail.value ? detail.value + unit : '-';
    return `${detail.name}${val}`;
  }

  formatRunhourValue(value) {
    const hours = Math.floor(value);
    const minutes = Math.round((value - hours) * 60);
    const formattedTime = `${hours.toString().padStart(2, "0")} : ${minutes.toString().padStart(2, "0")} Hrs`;
    return formattedTime;
  }
  render() {
    const { data, getRemoteAccess, getRemoteLockAccess, sockets, thing_data } =
      this.props.data;
    const lifetime_runhour_val = data?.allParams?.filter((param) => param.key === "rnhr")?.[0]?.value;
    const lifetime_runhour = !isNaN(lifetime_runhour_val)
          ? this.formatRunhourValue(lifetime_runhour_val)
          : "NA";

    const tooltipContent = this.state.hiddenDetails.length > 0 ? (
      <div className="hidden-details-tooltip">
        {this.state.hiddenDetails.map(detail => (
          <div key={detail.key} className="tooltip-detail-item">
            {this.formatDetailText(detail)}
          </div>
        ))}
      </div>
    ) : null;

    return (
      <div className="upper-section">
        <div className="panel-header">
          <div className="thing-header-details">
            <div>
              {[44, 85].includes(data?.category) ? (
                <ImageComponent
                  t={this.props.t}
                  background={"white"}
                  show_status={true}
                  src={data?.icon}
                  status={
                    data?.on_off_moving_status === "1" ? "online" : "offline"
                  }
                  deviceStatus={
                    data?.on_off_moving_status === "1" ? "online" : "offline"
                  }
                />
              ) : (
                <ImageComponent
                  t={this.props.t}
                  background={"white"}
                  show_status={true}
                  src={data?.icon}
                  category={data?.category}
                  tooltip={true}
                  title={`Category: ${data?.categoryName}`}
                  assetHasStatus={data?.status_option_includes_stopped}
                  status={
                    !data?.status_option_includes_stopped
                      ? getOnlineOfflineOnlyStatus(
                          data?.on_off_moving_status,
                        ) === "online"
                        ? "running"
                        : "offline"
                      : data?.on_off_moving_status === "1"
                        ? "running"
                        : data?.on_off_moving_status === "2"
                          ? "offline"
                          : "stopped"
                  }
                  deviceStatus={
                    thing_data?.devices?.length
                      ? data?.deviceStatus
                      : thing_data?.status
                  }
                  faultStatus={data?.faultStatus}
                />
              )}
            </div>
            {/*<img src={data?.icon} />*/}
            {/*</div>*/}
            <div className="name-date">
              <AntTooltip title={data?.name}>
                <div className="thing-name hellip">{data?.name}</div>
              </AntTooltip>
              {data?.no_status ? (
                ""
              ) : (
                <div className="time">
                  {data?.date}
                  {/* {(thing_data?.devices?.length &&
                    data?.deviceStatus === "offline") ||
                  thing_data?.status === "offline" ? (
                    <span>Offline</span>
                  ) : (
                    ""
                  )} */}
                </div>
              )}
            </div>
          </div>
          {data?.is_data_availability ? (
            <div className="data-availability-container-panel"></div>
          ) : !data?.status_option_includes_stopped ? (
            // getOnlineOfflineOnlyStatus(
            // 	data?.on_off_moving_status
            // ) === 'online' ? (
            // 	<span className="online">Online</span>
            // ) : (
            // 	''
            // )
            ""
          ) : (
            <div className="on-off-lock-button">
              <OnOffCompositeSwitch
                deviceStatus={
                  thing_data?.devices?.length
                    ? data?.deviceStatus
                    : thing_data?.status
                }
                dgStatus={data?.on_off_moving_status}
                socket={this.props?.data?.socket}
                client_id={this.props?.data?.client_id}
                application_id={this.props?.data?.application_id}
                thingId={data?.id}
                commandStatus={thing_data?.commands}
                isLockControlEnabled={data?.is_lock_control_enabled}
                isControlEnabled={data?.is_control_enabled}
                operation_mode={thing_data?.thing_details?.operation_mode}
                dg_lock_status={data?.dg_lock_status}
                getRemoteLockAccess={
                  data.show_lock ? getRemoteLockAccess : false
                }
                getRemoteAccess={data.show_switch ? getRemoteAccess : false}
                category_id={data?.category}
              />
            </div>
          )}
          {data?.statusIcon ? (
            <AntTooltip title={data.statusIcon.text}>
              <div>
                <img src={data.statusIcon.icon} />
              </div>
            </AntTooltip>
          ) : (
            ""
          )}
         {this.props?.data?.linksPosition === "top" && <div className="page-links" style={{position: 'static', }}>
          {this.props?.data?.data?.is_detailed_view ? (
            <div
              className="link"
              onClick={() => this.props?.data?.goToPage("detailed-view", this.props?.data?.data?.id)}
            >
              <LayoutOutlined />
              Detailed View
            </div>
          ) : (
            ""
          )}
          {this.props?.data?.data?.is_real_time ? (
            <div
              className="link"
              onClick={() => this.props?.data?.goToPage("real-time", this.props?.data?.id)}
            >
              <ClockCircleOutlined />
              Analog View
            </div>
          ) : (
            ""
          )}
        </div>}
        </div>
        {data.thing_details && data.thing_details.length ? (
          <div className="thing-details small-text">
            <div ref={this.containerRef} className="details-container">
              {this.state.visibleDetails.map(details => (
                <div key={details.key} className="details-divs">
                  {this.formatDetailText(details)}
                </div>
              ))}
              {this.state.hiddenDetails.length > 0 && (
                <AntTooltip title={tooltipContent}>
                  <span className="details-more">+{this.state.hiddenDetails.length} more</span>
                </AntTooltip>
              )}
            </div>
          </div>
        ) : (
          ""
        )}
        {data?.location !== undefined &&
        data?.location.length !== 0 &&
        data?.category !== 18 ? (
          <div className="location">
            <div className="location-div">
              <div className="heading">
                <img src={LocationIcon} /> Location:{" "}
              </div>
              <AntTooltip title={"Location: " + data?.location}>
                <div className="location-value hellip">{data?.location}</div>
              </AntTooltip>
            </div>

            <div className="open-in-maps">
              <OpenMaps lat={data?.lat} lng={data?.lng} onlyIcon={true} />
            </div>
          </div>
        ) : (
          ""
        )}
        {data?.category == 18 &&
          data?.thingsData?.thing_details?.lifetime_runhour && (
            <div className="lifetime-runhour">
              Lifetime Runhour:
              <div className="lifetime-runhour-value">
                {lifetime_runhour}
              </div>
            </div>
          )}
        {data?.show_fault ? (
          <div className="maintenance-fault small-text">
            {showActiveFault(data?.latestFaultData)}
            <div className="lamp">
              {_find(data?.real_time_params, { key: "red_stop_lamp" }) ? (
                <div>
                  <img
                    src={
                      parseInt(
                        _find(data?.real_time_params, { key: "red_stop_lamp" })
                          .value,
                      ) === 1
                        ? RedLampIcon
                        : GreyLampIcon
                    }
                  />{" "}
                  Red Lamp
                </div>
              ) : (
                ""
              )}
              {_find(data?.real_time_params, { key: "amb_war_lamp" }) ? (
                <div>
                  <img
                    src={
                      parseInt(
                        _find(data?.real_time_params, { key: "amb_war_lamp" })
                          .value,
                      ) === 1
                        ? WarningLampIcon
                        : GreyLampIcon
                    }
                  />{" "}
                  Warning Lamp
                </div>
              ) : (
                ""
              )}
            </div>
          </div>
        ) : (
          ""
        )}
        {data?.dr_st !== undefined ? (
          <div className="cs-door-st small-text">
            Door status:
            <img src={DoorImage} />
            <div
              style={{
                color:
                parseInt(data?.dr_st) === 1 ? 
                "red" :
                  this.props?.data?.maintenance === "online"
                    ? "#ff0000"
                    : "#232323",
                "font-weight": "bold",
              }}
            >
              {parseInt(data?.dr_st) === 1 ? "Open" : parseInt(data?.dr_st) === 0 ? "Closed" : "-"}
            </div>
          </div>
        ) : (
          ""
        )}
      </div>
    );
  }
}

export default PanelUpperSection;
