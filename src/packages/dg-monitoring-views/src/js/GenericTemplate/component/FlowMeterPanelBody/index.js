import React from 'react';
import './style.less';
import AntTooltip from '@datoms/react-components/src/components/AntTooltip';
import FlowMeterImg from '../../images/Group 8245.svg';

export default class FlowMeterPanelBody extends React.Component {
	constructor(props) {
		super(props);
	}
	render() {
		const { data } = this.props;
		return (
			<div id="flow_meter_panel_body_id">
				<img src={FlowMeterImg} />
				<div className="real-time-parameters">
					{data?.real_time_params?.map((params) => {
						return (
							<AntTooltip placement="topLeft" title={params.tooltipDetails}>
								<div className="values">
									{params?.value}
									<div className="params-title">
										{params?.name}
									</div>
									<div className="params-title">
										{'(' + params?.unit + ')'}
									</div>
								</div>
							</AntTooltip>
						);
					})}
				</div>
			</div>
		);
	}
}
