import React from "react";
import "./style.less";
import InfoCircleOutlined from "@ant-design/icons/InfoCircleOutlined";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";

function tooltipText(title) {
  switch (title) {
    case "Online":
      return "The device is communicating with the server";
    case "Offline":
      return "The device is not communicating with the server";
    case "Running":
      return "Asset is Running.";
    case "Stopped":
      return "Asset is Stopped or Switched Off";
    case "Disconnected":
      return "Our platform is not receiving the asset data, kindly verify the device status";
    case "Faulty Assets":
      return "No. of assets with active faults";
    case "Trip":
      return "No. of assets with active trip faults";
    case "Warning":
      return "No. of assets with active warning faults";
    default:
      return "";
  }
}

export default function ThingStatus(props) {
  let functionTobeCalled, cursorPointer;
  if (typeof props.onThingStatusClicked === "function") {
    functionTobeCalled = props.onThingStatusClicked;
    cursorPointer = "pointer";
  } else {
    functionTobeCalled = () => {};
    cursorPointer = "default";
  }
  const filterValues = [];
  if (props?.data?.length) {
    props.data.forEach((item) => {
      filterValues.push(
        <div
          className={
            "status-text " +
            (props.selectedValue?.toLowerCase() === item.title.toLowerCase()
              ? "active"
              : "")
          }
          onClick={() => {
            const finalItem = props.selectedValue?.toLowerCase() === item.title.toLowerCase() ? props.data[0] : item;
            functionTobeCalled(finalItem);
          }}
          style={{ cursor: cursorPointer }}
        >
          <span className="value">{(props.client_id === 1057 || props.vendor_id === 1057) && item.title === 'Faults'  ? '' : item.value}</span>
          {props.t? props.t(item.title): item.title}
          {item.total || !tooltipText(item.title).length ? (
            ""
          ) : props?.showInfo ? (
            <AntTooltip title={tooltipText(item.title)}>
              <div
                style={{ "margin-left": 5, color: "#c4c2c2", "font-size": 14 }}
              >
                <InfoCircleOutlined />
              </div>
            </AntTooltip>
          ) : (
            ""
          )}
        </div>,
      );
      if (item.total) {
        filterValues.push(<div className="divider-circle"></div>);
      }
    });
  }
  return <div id="thing_status">{filterValues}</div>;
}
