import React, { useState } from "react";
import GenericFilter from "@datoms/react-components/src/components/GenericFilter/pages";
import SegmentedTab from "@components/base/SegmentedTabs";
import { getBaseUrl } from "@datoms/js-utils/src/base-url-logic";
// import PageSwitchInternal from "../PageSwitchInternal";
import "./style.less";

export default function PanelHeader(props) {
  let filterData = [
    {
      type: "tree_select",
      hideField: !props.territories?.options?.children?.length,
      label: "Select Territory",
      url_name: "territories",
      key: "territories",
      filter_api: "territories",
      is_options_dynamic: true,
      no_outside_label: true,
      component_props: {
        treeData: props.territories?.options ? [props.territories.options] : [],
        value: Array.isArray(props.territories?.value) ? props.territories?.value : [],
        treeDefaultExpandAll: true,
        treeCheckable: true,
        showCheckedStrategy: "SHOW_PARENT",
        treeCheckStrictly: true,
        maxTagCount: 0,
        maxTagPlaceholder: (omittedValues) =>
          omittedValues.length +
          ` territor${omittedValues.length === 1 ? "y" : "ies"} selected`,
        placeholder: "Select territories",
        filterTreeNode: (search, item) => {
          return item.title.toLowerCase().indexOf(search.toLowerCase()) >= 0;
        },
      },
      inner_component_props: {
        maxTagCount: 3,
      }
    },
    {
      optionData:
        props.client_id === 1381
          ? [
              {
                title: "All Sites",
                value: "all",
              },
              {
                value: 1,
                title: "Koramangla 3",
              },
              {
                value: 2,
                title: "Ranka Colony",
              },
            ]
          : props?.site?.options,
      selectValue: props?.site?.value,
      //	allowClear: true,
      no_outside_label: true,
      showSearch: true,
      url_name: "site",
      placeholder: "All Sites",
      label: "Select Site",
    },
  ];

  // if (!props.dg_in_iot_mode) {
    filterData.push({
      optionData: props?.category?.options,
      selectValue: props?.category?.value,
      showSearch: true,
      placeholder: props?.category?.placeholder,
      label: props.t? props.t('asset_type') : 'Asset Type',
      // label: "Asset Type",
      no_outside_label: true,
      url_name: "category",
      hideField: props.dg_in_iot_mode,
    });
  // }

  filterData.push(
    {
      is_inside_filter_drawer: true,
      optionData: props?.onOffStatus?.options,
      label: props.t? props.t('asset_status') : 'Asset Status',
      // label: "Asset Status",
      url_name: "status",
      selectValue: props?.onOffStatus?.value,
      allowClear: true,
      showSearch: true,
      no_outside_label: true,
      placeholder: props?.onOffStatus?.placeholder,
      no_outside_label: true,
    },

    {
      is_inside_filter_drawer: true,
      optionData: [
        {
          value: "online",
          title: "Online",
        },
        {
          value: "offline",
          title: "Offline",
        },
      ],
      label: "Device Status",
      url_name: "device_st",
      selectValue: props?.on_off_device_status,
      allowClear: true,
      showSearch: true,
      sorted: false,
      no_outside_label: true,
      placeholder: "Select device status",
      hideField: true || props?.isAurassure
    },
  );
  // if (props?.isAurassure) {
  //   filterData = filterData.filter((item) => item.url_name !== "device_st");
  // }

  const showTabSwitch =  props.client_id === 13853 && props?.view_switch_value === "list" && props.enabled_features?.includes("SiteManagement:SiteManagement");

  const goToSiteList = () => {
    props.history.push(getBaseUrl(props, "list-view/sites"));
  }
  return (
    <div id="panel_header">
     {showTabSwitch && <SegmentedTab options={["Sites", "Assets"]} value="Assets" onChange={goToSiteList}/>}
      <GenericFilter
        t={props.t}
        history={props.history}
        url={props.url}
        default={[undefined, "all", "all", "", ""]}
        width={525}
        filterData={filterData}
        panelFilterSelect={(value, key) => props.applyFilterSelect(value, key)}
        onUrlChange={() => props.onUrlChange()}
        searchObject={{
          // placeholder: "Search Assets",
          placeholder: props.t? props.t('search_assets'): "Search Assets",
          size: "default",
          value: props?.search_data,
        }}
      />
      {/*<PageSwitchInternal
        value={props?.view_switch_value}
        changeView={props?.changeView}
        options={props?.view_switch_options}
        primary_color={props?.primary_color}
      />*/}
    </div>
  );
}
