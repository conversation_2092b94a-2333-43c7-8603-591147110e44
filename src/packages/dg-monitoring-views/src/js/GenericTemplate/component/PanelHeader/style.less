#panel_header {
	display: flex;
	align-items: center;
	gap: 30px;
	// justify-content: space-between;
	.search-filter-component
		.filter-with-search-container
		.select-filter-wrapper {
		.filter-drop {
			margin-left: 0px;
		}
		.select-container {
			margin-right: 10px;
			.select-label {
				color: #707070;
				font-size: 12px;
				font-weight: normal;
			}
			.select-input-container .filter-icon {
				left: 15px;
			}
		}
	}
}

@media (max-width: 1024px) {
	#panel_header {
		display: block;
		#page_switch_internal {
			margin: 0 auto;
			margin-top: 10px;
			float: none;
			width: fit-content;
		}
		#generic_filter .generic-filter-options {
			justify-content: center;
		}
	}
}
