@image-width: 120px;
@image-height: 170px;

@item1-height: 78px;
@item2-height: 78px;
@item3-height: 78.82px;

.mri-panel-container {
  display: flex;
  padding-left: 9px;
  padding-bottom: 10px;
  padding-top: 12px;
  padding-right: 15px;
  gap: 8px;

  .datagrid {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    align-items: start;
    gap: 8px;
  }

  .image-container,
  .item-1,
  .item-2,
  .item-3 {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
  }

  .image-container {
    //   grid-row: 1 / span 2;
    width: @image-width;
    height: @image-height;
    background-color: #f6f6f6;
    border-radius: 12px;
    margin-right: 7px;
    @media (max-width: 576px) {
        display: none;
      }
  }

  .item-1,
  .item-2 {
    flex-direction: column;
    height: @item1-height; // same for item-2
    border-radius: 10px;
    background-color: #f6f6f6;

    .value {
      font: roboto;
      font-weight: bold;
      font-size: 16px;
      color: #232323;
    }

    .label {
      font: roboto;
      color: #4f4f4f;
      font-size: 12px;
    }
  }

  .item-2 {
    background-color: #f6f6f6;
  }

  .item-3 {
    grid-column: 1 / span 2;
    height: @item3-height;
    background-color: #f6f6f6;
    border-radius: 12px;
    // min-width: 270px;
    // width: 100%;
    .grid-2x3 {
      padding: 12px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: repeat(3, 1fr);
      width: 100%;
      height: 100%;
      column-gap: 30px;
    }

    .cell {
      display: flex;
      flex-direction: row; // ← changed from column to row
      align-items: center;
      justify-content: space-between; // or center if you prefer
      text-align: left;
      background-color: #f6f6f6;
      gap: 10px;
      overflow: hidden;
      //   padding:0px;

      .value {
        font: roboto;
        font-weight: bold;
        font-size: 12px;
        color: #232323;
        min-width: fit-content;
        @media (max-width: 576px) {
            font-size: 14px;
          }
      }

      .label {
        font: roboto;
        color: #4f4f4f;
        font-size: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        @media (max-width: 576px) {
            font-size: 12px;
          }
      }
    }
  }
}
