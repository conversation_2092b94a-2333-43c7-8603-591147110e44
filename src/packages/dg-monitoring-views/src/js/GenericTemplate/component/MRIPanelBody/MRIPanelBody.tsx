/**
 * MRIPanelBody Component
 *
 * This React component is responsible for displaying real-time MRI machine parameters
 * along with an illustrative image. It includes styled sections for the key metrics 
 * and presents the remaining parameters in a responsive grid layout with tooltips.
 *
 * Props:
 * - `props.data`: Contains `real_time_params`, an array of parameter objects
 *   with keys: `key`, `value`, and optionally `unit`.
 * - `props.daily_data_loading`: (Optional) A map where each key is a data ID and the value is a loading state.
 *
 * External Dependencies:
 * - MRI machine image from `../../images/mriMachine-Panel-Img.svg`
 * - Styles from `./style.less`
 * - `SkeletonLoader` from `@datoms/react-components`
 * - MRI config from `../../configs/MRIMachineConfig`
 * - Ant Design's `Tooltip` component
 *
 * Example parameter shape from props.data.real_time_params:
 * ```ts
 * {
 *   key: string;
 *   value: string;
 *   unit?: string;
 * }
 * ```
 */
import React from "react";
import mriMachineImgae from "../../images/mriMachine-Panel-Img.svg";
import "./style.less";
import { Tooltip } from "antd";
import SkeletonLoader from "@datoms/react-components/src/components/SkeletonLoader";
import { mriRealTimeParams } from "../../configs/MRIMachineConfig";

/**
 * MRIPanelBody React functional component
 *
 * @param props - Component props containing real-time MRI parameter data and loading state.
 * @returns JSX.Element representing the MRI panel with image and parameter values.
 */
export default function MRIPanelBody(props: any): JSX.Element {

  /**
   * Processes the real_time_params data and maps it to a standardized
   * array format with name and formatted value.
   *
   * @returns Array of parameter objects containing name and formatted value.
   */
  const getParameterValues = (): { name: string; value: string }[] => {
    const result: { name: string; value: string }[] = [];

    // Create a key-value map for quick access to parameter values
    const paramMap: { [key: string]: string } = {};
    if (Array.isArray(props?.data?.real_time_params)) {
      props.data.real_time_params.forEach((param: any) => {
        let paramValue = param?.value;
        if (paramValue && paramValue !== "NA") {
          paramValue += param?.unit === "degC" ? " °C" : ` ${param?.unit ?? ""}`;
        }
        paramMap[param.key] = paramValue;
      });
    }

    // Maintain the order defined in the config file
    mriRealTimeParams.forEach((item) => {
      result.push({
        name: item.name,
        value: paramMap[item.key] || "NA",
      });
    });

    return result;
  };

  const parameterValues = getParameterValues();

  return (
    // Optionally show loader (commented out)
    // props.daily_data_loading?.[props.data?.id] ? (
    //   <SkeletonLoader />
    // ) : (
    <div className="mri-panel-container">
      <div className="image-container">
        <img src={mriMachineImgae} alt="MRI Machine Image" />
      </div>

      <div className="datagrid">
        {/* Top 2 important parameters */}
        <div className="item-1">
          <div className="value">{parameterValues?.[0].value}</div>
          <div className="label">{parameterValues?.[0].name}</div>
        </div>

        <div className="item-2">
          <div className="value">{parameterValues?.[1].value}</div>
          <div className="label">{parameterValues?.[1].name}</div>
        </div>

        {/* Remaining parameters displayed in a grid */}
        <div className="item-3">
          <div className="grid-2x3">
            {parameterValues.slice(2).map((item) => (
              <div key={item.name} className="cell">
                <Tooltip title={item.name}>
                  <div className="label">{item.name}</div>
                </Tooltip>
                <div className="value">{item.value}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
    // )
  );
}
