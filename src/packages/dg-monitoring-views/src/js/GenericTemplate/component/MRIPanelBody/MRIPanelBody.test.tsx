import React from "react";
import { render, screen } from "@testing-library/react";
import MRIPanelBody from "../MRIPanelBody";

// Mock image import
jest.mock("../../images/mriMachine-Panel-Img.svg", () => "mocked-image-path.svg");

// Mock MRI config
jest.mock("../../configs/MRIMachineConfig", () => ({
  mriRealTimeParams: [
    { key: "helium_pressure", name: "Helium Pressure" },
    { key: "case_temp", name: "Case Temperature" },
    { key: "shield_Si410", name: "Shield Si410" },
    { key: "recon_ruo", name: "Recon RuO" },
    { key: "recon_si410", name: "Recon Si410" },
    { key: "cold_ruo", name: "Coldhead RuO" },
  ],
}));

// Mock Tooltip to inspect structure
jest.mock("antd", () => {
  const OriginalAntd = jest.requireActual("antd");
  return {
    ...OriginalAntd,
    Tooltip: jest.fn(({ title, children }) => (
      <div data-testid="tooltip-wrapper" title={title}>{children}</div>
    )),
  };
});

const mockRealTimeParams = [
  { key: "helium_pressure", value: "17.68", unit: "PSI" },
  { key: "case_temp", value: "21.3", unit: "degC" },
  { key: "shield_Si410", value: "20.2", unit: "K" },
  { key: "recon_ruo", value: "12.1" }, // No unit provided
  { key: "recon_si410", value: "22.3", unit: "K" },
  { key: "cold_ruo", value: "19.9", unit: "K" },
];

const props = {
  data: {
    id: "1",
    real_time_params: mockRealTimeParams,
  },
  daily_data_loading: {},
};

describe("MRIPanelBody", () => {
  test("renders the MRI image", () => {
    render(<MRIPanelBody {...props} />);
    expect(screen.getByAltText(/MRI Machine Image/i)).toBeInTheDocument();
  });

  test("displays top 2 parameter values correctly", () => {
    render(<MRIPanelBody {...props} />);
    expect(screen.getByText("17.68 PSI")).toBeInTheDocument();
    expect(screen.getByText("21.3 °C")).toBeInTheDocument();
  });

  test("displays all parameter labels from config", () => {
    render(<MRIPanelBody {...props} />);
    [
      "Helium Pressure",
      "Case Temperature",
      "Shield Si410",
      "Recon RuO",
      "Recon Si410",
      "Coldhead RuO",
    ].forEach((label) => {
      expect(screen.getByText(label)).toBeInTheDocument();
    });
  });

  test("displays NA when value is not available", () => {
    const incompleteProps = {
      data: {
        id: "2",
        real_time_params: [
          { key: "helium_pressure", value: "" },
          { key: "case_temp", value: "NA" },
          { key: "shield_Si410", value: "20.2" },
          { key: "recon_ruo", value: "NA" }, 
          { key: "recon_si410", value: "" },
          { key: "cold_ruo", value: "19.9" },
        ],
      },
      daily_data_loading: {},
    };
    render(<MRIPanelBody {...incompleteProps} />);
    const naElements = screen.getAllByText("NA");
    expect(naElements.length).toBe(4);
  });

  test("wraps parameter labels with Tooltip", () => {
    render(<MRIPanelBody {...props} />);
    const tooltipWrappers = screen.getAllByTestId("tooltip-wrapper");
    expect(tooltipWrappers.length).toBe(4); // 6 total params - 2 top = 4 wrapped
    expect(tooltipWrappers[0]).toHaveAttribute("title", "Shield Si410");
  });

  test("renders value correctly when unit is missing", () => {
    render(<MRIPanelBody {...props} />);
    expect(screen.getByText(/^12.1$/)).toBeInTheDocument(); 
  });
});
