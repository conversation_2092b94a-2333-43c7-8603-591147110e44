interface StatusValue {
[key: string]: string;
}

interface StatusParam {
key: string;
name: string;
values: StatusValue;
}

export const MRIMachineStatusParams: StatusParam[] = [
{
    key: 'he_press_alm',
    name: "He Pressure Alarm",
    values: {
    "0": "No",
    "1": "Yes",
    }
},
{
    key: 'he_lvl_alm',
    name: "Helium Level Alarm",
    values: {
    "0": "No",
    "1": "Yes"
    }
},
{
    key: 'wtr_flow_alm',
    name: "Water Flow Alarm",
    values: {
    "0": "No",
    "1": "Yes",
    }
},
{
    key: 'shield_si410_alm',
    name: "Shield Si410 Alarm",
    values: {
    "0": "No",
    "1": "Yes",
    }
},
{
    key: 'recon_ruo_alm',
    name: "Recon RuO Alarm",
    values: {
    "0": "No",
    "1": "Yes",
    }
},
{
    key: 'heater_stat',
    name: "Heater Status",
    values: {
    "0": "Off",
    "1": "On",
    "2": "Not Available",
    }
},
{
    key: 'rf_active',
    name: "RF Active",
    values: {
    "0": "Inactive",
    "1": "Active",
    }
},
{
    key: 'fill_mode_he',
    name: "Fill Mode (Helium)",
    values: {
    "0": "Normal",
    "1": "Pre-Fill Mode",
    "2": "Fill Mode",
    "3": "Post-Fill Mode",
    }
},
{
    key: 'service_mode',
    name: "Service Mode",
    values: {
    "0": "Normal",
    "1": "Service Mode Active"
    }
},
// {
//     key: 'comp_stat_1',
//     name: "Compressor Status 1",
//     values: {
//     "0": "Normal operation - compressor running",
//     "10": "Compressor stopped, reason unknown",
//     "11": "Compressor stopped due to overheat",
//     "12": "Compressor stopped due to low He pressure",
//     "13": "Compressor stopped due to overheat and low pressure",
//     "20": "Compressor powered off or cable disconnected (no 24v signal)",
//     "30": "Compressor powered off or cable disconnected (no 24v signal)",
//     "31": "Compressor powered off or cable disconnected (no 24v signal)",
//     "32": "Compressor powered off or cable disconnected (no 24v signal)",
//     "33": "Compressor powered off or cable disconnected (no 24v signal)",
//     "8": "Compressor fuse tripped",
//     "18": "Compressor fuse tripped",
//     "4": "Klixon error",
//     "14": "Klixon error",
//     }
// },
// {
//     key: 'comp_stat_2',
//     name: "Compressor Status 2",
//     values: {
//     "0": "Normal operation - compressor running",
//     "10": "Compressor stopped, reason unknown",
//     "11": "Compressor stopped due to overheat",
//     "12": "Compressor stopped due to low He pressure",
//     "13": "Compressor stopped due to overheat and low pressure",
//     "20": "Compressor powered off or cable disconnected (no 24v signal)",
//     "30": "Compressor powered off or cable disconnected (no 24v signal)",
//     "31": "Compressor powered off or cable disconnected (no 24v signal)",
//     "32": "Compressor powered off or cable disconnected (no 24v signal)",
//     "33": "Compressor powered off or cable disconnected (no 24v signal)",
//     "8": "Compressor fuse tripped",
//     "18": "Compressor fuse tripped",
//     "4": "Klixon error",
//     "14": "Klixon error",
//     }
// }
];

export const MRIMachineStatusParamsKeys = MRIMachineStatusParams.map((param) => param.key);

export const mriRealTimeParams = [
    { key: "helium_pressure", name: "He Pressure" },
    { key: "helium_lvl", name: "He Level" },
    { key: "shield_si410", name: "Shield_Si410" },
    { key: "recon_ruo", name: "Recon_RuO" },
    { key: "recon_si410", name: "Recon_Si410" },
    { key: "cold_ruo", name: "Coldhead_RuO" },
    { key: "comp_duty_cyc", name: "CDC" },
    { key: "heater_duty_cyc", name: "HDC" },
];

export const mriRealTimeParamKeys = mriRealTimeParams.map(param => param.key);