const loadStatus: Record<any, string> = {
    16: "Load on Grid",
    8: "Load on Solar + Grid",
    64: "Load on Solar + Battery",
    32: "Load on Solar",
    256: "Load on Inverter/Battery",
    128: "Load on Grid + Battery"
};

const dspState: Record<any, string> = {
    "1": "System Off",
    "2": "Inverting",
    "3": "Inverting & Synchronising",
    "4": "AC Connected",
    "5": "Test Mode (Reserved)",
    "6": "Synchronising"
};

const supervisoryState: Record<any, string> = {
    "0": "System Idle",
    "1": "Bulk Charging",
    "2": "Absorb Charging",
    "3": "Float Charging",
    "4": "Boost Charging",
    "5": "Equalisation Charging",
    "6": "Battery Capacity Test",
    "7": "Globle MPP Scan",
    "10": "MPP Low Power",
    "11": "Battery Disconnection Test",
    "12": "Charging Inhibit (External)",
    "13": "Islanding",
    "14": "Standby"
}

const paramNameMap: Record<string, string> = {
    "enrg": "DG Energy",
    "dcl_enrg": "DC Load Energy",
}

export const SolarInverterStatusKeys: string[] = ["ld_st", "dsp_st", "sp_st",];

export default {
    loadStatus: loadStatus,
    dspState: dspState,
    supervisoryState: supervisoryState,
    paramNameMap: paramNameMap,
}