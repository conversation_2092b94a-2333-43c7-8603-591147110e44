interface StatusValue {
  [key: number]: string;
}

interface StatusParam {
  key: string;
  values: StatusValue;
}

export const solarPumpStatusParams: StatusParam[] = [
  {
    key: 'drv_switch',
    values: {
      1: 'Drive Ready To Switch On',
      0: 'Drive Not Ready To Switch On'
    }
  },
  {
    key: 'drv_ready_run',
    values: {
      1: 'Drive Ready To Operate',
      0: 'Drive Not Ready To Operate'
    }
  },
  {
    key: 'drv_fault',
    values: {
      1: 'Drive At Fault',
      0: 'Drive Not At Fault'
    }
  },
  {
    key: 'drv_warn',
    values: {
      1: 'Drive Warning Active',
      0: 'Drive No Warnings'
    }
  },
  {
    key: 'drv_cnt_loc',
    values: {
      1: 'Drive control location: REMOTE',
      0: 'Drive control location: LOCAL'
    }
  },
  {
    key: 'drv_run_start',
    values: {
      1: 'Both Run & Start Enabled',
      0: 'Both Run & Start Disabled'
    }
  },
  {
    key: 'drv_inhb',
    values: {
      1: 'Drive Inhibited',
      0: 'Drive Not Inhibited'
    }
  },
  {
    key: 'dc_chgd',
    values: {
      1: 'DC Circuit has been charged',
      0: 'DC Circuit has not been charged'
    }
  },
  {
    key: 'drv_ready_to_start',
    values: {
      1: 'Drive is Ready to Receive Start Commands',
      0: 'Drive is Not Ready To Receive Start Commands'
    }
  },
  {
    key: 'drv_ref',
    values: {
      1: 'Drive is Following Reference Parameter',
      0: 'Drive is not following Reference Parameter'
    }
  },
  {
    key: 'drv_start',
    values: {
      1: 'Drive Started',
      0: 'Drive not Started'
    }
  },
  {
    key: 'drv_mod',
    values: {
      1: 'Drive is Modulating',
      0: 'Drive is Not Modulating'
    }
  },
  {
    key: 'drv_opr_lim',
    values: {
      1: 'Operating Limit Active',
      0: 'Operating Limit Inactive'
    }
  },
  {
    key: 'drv_start_req',
    values: {
      1: 'Drive Start Is Requested',
      0: 'Rotating of the motor is disabled'
    }
  },
  {
    key: 'drv_run',
    values: {
      1: 'Drive Running',
      0: 'Drive not Running'
    }
  },
  {
    key: 'drv_n_ready_run',
    values: {
      1: 'DC voltage is missing or drive has not been parametrized correctly',
      0: ''
    }
  },
  {
    key: 'ctrl_loc_cngd',
    values: {
      1: 'Control location has changed',
      0: ''
    }
  },
  {
    key: 'ssw_inhb',
    values: {
      1: 'Control program is keeping itself in inhibited state',
      0: ''
    }
  },
  {
    key: 'fault_reset',
    values: {
      1: 'A fault has been reset',
      0: ''
    }
  },
  {
    key: 'start_en_lost',
    values: {
      1: 'Start enable signal missing',
      0: ''
    }
  },
  {
    key: 'run_en_lost',
    values: {
      1: 'Run enable signal missing',
      0: ''
    }
  },
  {
    key: 'sto_act',
    values: {
      1: 'Safe torque off function active',
      0: ''
    }
  },
  {
    key: 'curr_cal_end',
    values: {
      1: 'Current calibration routine has finished',
      0: ''
    }
  },
  {
    key: 'auto_reset_inhb',
    values: {
      1: 'The autoreset function is inhibiting operation',
      0: ''
    }
  },
  {
    key: 'jog_act',
    values: {
      1: 'The jogging enable signal is inhibiting operation',
      0: ''
    }
  },
  {
    key: 'z_speed',
    values: {
      1: 'Drive has been running below zero speed limit',
      0: ''
    }
  },
  {
    key: 'fwd',
    values: {
      1: 'Drive is running in forward direction above zero speed limit',
      0: ''
    }
  },
  {
    key: 'rev',
    values: {
      1: 'Drive is running in reverse direction above zero speed limit',
      0: ''
    }
  },
  {
    key: 'const_speed',
    values: {
      1: 'A constant speed or frequency has been selected',
      0: ''
    }
  }
];

export const solarPumpStatusParamsKeys = solarPumpStatusParams.map((param) => param.key);

export const solarPumpRealTimeParamsConfig = {
  curr: {
    relatedProperty: "rated_current",
    maxValuePercentage: 30,
    limitPercentage: 5,
    isNegative: false
  },
  mtr_pow: {
    relatedProperty: "rated_power",
    maxValuePercentage: 10,
    limitPercentage: 5,
    isNegative: true
  },
  rpm: {
    relatedProperty: "rated_speed",
    maxValuePercentage: 5,
    limitPercentage: 0,
    isNegative: true
  }
}

export const solarPumpMachineInfo = [
  { key: 'rated_voltage', name: 'Motor Nominal Voltage (V): ' },
  { key: 'rated_current', name: 'Motor Nominal Current (A): ' },
  { key: 'rated_freq', name: 'Motor Nominal Frequency (Hz): ' },
  { key: 'rated_speed', name: 'Motor Nominal Speed (RPM): ' },
  { key: 'rated_power', name: 'Motor Nominal Power (kW): ' },
  { key: 'rated_power_factor', name: 'Motor Nominal Power Factor: ' },
];
