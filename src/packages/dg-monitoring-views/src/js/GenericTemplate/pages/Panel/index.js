import React, { useEffect, useState, useRef, useMemo } from "react";
import PanelComponent from "../../component/PanelComponent";
import AntRow from "@datoms/react-components/src/components/AntRow";
import AntCol from "@datoms/react-components/src/components/AntCol";
import AntButton from "@datoms/react-components/src/components/AntButton";
import Loading from "@datoms/react-components/src/components/Loading";
import PanelHeader from "../../component/PanelHeader";
import ThingStatus from "../../component/ThingStatus";
import _find from "lodash/find";
import _filter from "lodash/filter";
import _uniqBy from "lodash/uniqBy";
import "./style.less";
import { useData } from "../../logic/useData";
import { usePanel } from "../../logic/usePanel";
import PanelTableComponent from "../../component/PanelTableComponent";
import { usePanelTable } from "../../logic/usePanelTable";
import { getBaseUrl } from "@datoms/js-utils/src/base-url-logic";
import { Skeleton } from "antd";
import AurassurePanelComponent from "../../component/AurassureComponents/component/PanelComponent";
import { isAurassure } from "../../logic/isAurassure";
import { useThingStatusOptions } from "../../logic/useThingStatusOptions";

export default function PanelView(props) {
  const isThermofisher = [3018, 2696].includes(props.vendor_id);
  const [panel_body_loading, setPanelBodyLoading] = useState(false);
  const scrollObserver = useRef();
  const pageKey = props.location && !props.location.pathname.includes("list-view") ? "panel-view" : "list-view";
  const baseUrl = getBaseUrl(props, pageKey, true);
  let timer;
  let urlIndex = props.dg_in_iot_mode ? 3 : 2;
  console.log("filterInUrl", props?.filterInUrl)
  let parsedFilterFromUrl = props?.filterInUrl
    ? props?.filterInUrl
    : "?" + props.history.location.search.split("?")[urlIndex];
  const userRoleType = props.logged_in_user_role_type;
  const [pageNumber, setPageNumber] = useState(1);
  const [page_key, setPageKey] = useState(pageKey === "panel-view" ? "panel" : "list");
  const [
    site_list,
    allThings,
    list_data,
    application_list,
    modified_list_data,
    latest_data,
    fault_data,
    maintenance_data,
    address_data,
    daily_data,
    alert_data,
    loading,
    socket,
    fault_loading,
    maintenance_loading,
    daily_data_loading,
    camera_details,
    aqi_data,
    aqi_loading,
    search_data,
    applyFilterSelect,
    filter_value,
    setFilter,
    onUrlChange,
    loadMoreBtnLoading,
    isPanel,
    internal_panel_loading,
    bucketTime,
    territoryList,
    panelDetailedData,
  ] = useData(
    props,
    pageNumber,
    setPanelBodyLoading,
    parsedFilterFromUrl,
    setPageNumber,
    page_key,
    isThermofisher
  );
  const [panel_data] = usePanel(
    list_data,
    application_list,
    latest_data,
    fault_data,
    maintenance_data,
    filter_value,
    address_data,
    daily_data,
    alert_data,
    camera_details,
    aqi_data,
    props,
    pageNumber,
    panelDetailedData,
    userRoleType,
  );
  console.log("panel_data: ", panel_data)
  const [column, dataSource] = usePanelTable(
    props,
    modified_list_data,
    panel_data,
    list_data,
    application_list,
    fault_loading,
    maintenance_loading,
    daily_data_loading,
    aqi_loading,
    socket,
    goToPage,
    pageNumber
  );
  function deviceStatus() {
    let onlineDevices = [],
      offlineDevices = [];
    if (allThings?.things?.length) {
      allThings.things.forEach((things) => {
        things.devices.map((device) => {
          if (device.online_status === 1) {
            onlineDevices.push(device);
          } else {
            offlineDevices.push(device);
          }
        });
      });
    }
    onlineDevices = [..._uniqBy(onlineDevices, "qr_code")];
    offlineDevices = [..._uniqBy(offlineDevices, "qr_code")];
    const finalDeviceStatus = [
      {
        title: "Devices",
        total: true,
        value: [...onlineDevices, ...offlineDevices].length,
      },
      {
        title: "Online",
        value: onlineDevices.length,
      },
      {
        title: "Offline",
        value: offlineDevices.length,
      },
    ];
    return finalDeviceStatus;
  }
  function thingStatus() {
    let getStatusdata = [];
    if (allThings?.things?.length) {
      allThings.things.forEach((thing) => {
        const findThingCat = _find(allThings?.things_categories, {
          id: thing.category,
        });
        let findStatusOptions = findThingCat?.status_options;
        const isMcSt =
          thing?.parameters && _find(thing.parameters, { key: "mc_st" });

        let onOffStatus =
          thing?.status === "offline"
            ? "2"
            : isMcSt
              ? isMcSt.value
              : thing?.category === 18 ||
                  thing?.category === 96
                ? "0"
                : "1";
        // if (this.isFiltered(latestParam, "status")) {
        getStatusdata.push({
          deviceStatus:
            thing?.devices?.[0]?.online_status === 1 ? "online" : "offline",
          on_off_moving_status: onOffStatus,
          status_options: findStatusOptions,
        });
        // }
      });
    }
    return getStatusdata;
  }
  const thingsStatusData = useThingStatusOptions(
    props.t,
    thingStatus(),
    filter_value?.onOffStatus,
    isAurassure(props.vendor_id),
    deviceStatus(),
  );
  function changeView(key) {
    const newCategory =
      key === "list"
        ? !isNaN(parseInt(filter_value?.category))
          ? filter_value?.category
          : filter_value?.categories[1]?.value
        : filter_value?.category === "all"
          ? "all"
          : parseInt(filter_value?.category);
    let findCatFromUrl = parsedFilterFromUrl.match(/category:(\d+)/);
    setPageKey(key);
    setPageNumber(1);
    if (key === "list") {
      if (
        findCatFromUrl?.[1] &&
        ![44, 85].includes(parseInt(findCatFromUrl[1]))
      ) {
        if (parsedFilterFromUrl.includes("category")) {
          props.history.push(
            baseUrl +
              "?view=" +
              key +
              (parsedFilterFromUrl !== "?undefined" ? parsedFilterFromUrl : ""),
          );
        } else if (parsedFilterFromUrl !== "?undefined") {
          props.history.push(
            baseUrl +
              "?view=" +
              key +
              parsedFilterFromUrl +
              ",category:" +
              newCategory,
          );
        } else {
          props.history.push(
            baseUrl + "?view=" + key + "?filter=category:" + newCategory,
          );
        }
      } else {
        props.history.push(
          baseUrl + "?view=" + key + "?filter=category:" + newCategory,
        );
      }
    } else {
      props.history.push(
        baseUrl +
          "?view=" +
          key +
          (parsedFilterFromUrl !== "?undefined" ? parsedFilterFromUrl : ""),
      );
    }
    setFilter((prevState) => ({
      ...prevState,
      category: newCategory,
      on_off_status: "",
      on_off_device_status: "",
    }));
    setPanelBodyLoading(true);
    timer = setTimeout(() => {
      setPanelBodyLoading(false);
    }, 10);
  }
  const handleFetchData = (page) => {
    if (typeof page === "number" && !isNaN(page)) {
      setPageNumber(page);
    } else {
      setPageNumber((prevPageNumber) => prevPageNumber + 1);
    }
  };
  let panels = [];
  if (isPanel) {
    if (panel_data && panel_data.length) {
      panel_data.map((data) => {
        if (data.is_panel) {
          const thing_data = _find(list_data?.things, {id: data.id});
          if(isThermofisher && [21,22,102,23].includes(thing_data?.category)) {
            panels.push(
              <AntCol
                className="each-panel-col"
                span={24}
              >
                  <PanelComponent
                    isThermofisher
                    goToPage={goToPage}
                    t={props.t}
                    client_id={props.client_id}
                    application_id={props.application_id}
                    getRemoteAccess={data.show_switch ? props?.getRemoteAccess() : false}
                    getRemoteLockAccess={data.show_lock ? props?.getRemoteLockAccess() : false}
                    data={data}
                    socket={socket}
                    fault_loading={fault_loading}
                    daily_data_loading={daily_data_loading}
                    dgStatus={data.on_off_moving_status}
                    thing_id={data.id}
                    isControlEnabled={data.isLockControlEnabled}
                    operation_mode={data.operation_mode}
                    dgLockStatus={data.dg_lock_status}
                    thingCommandStatus={data.commandStatus}
                    thing_data={thing_data}
                    linksPosition="top"
  
                  />
              </AntCol>,
            );
          } else {
            panels.push(
              <AntCol
                className="each-panel-col"
                xxl={8}
                xl={window.innerWidth <= 1280 ? 10 : 8}
                lg={12}
                md={18}
                sm={24}
              >
                {data?.aurassureVar ? (
                  <AurassurePanelComponent
                    t={props.t}
                    goToPage={goToPage}
                    client_id={props.client_id}
                    application_id={props.application_id}
                    getRemoteAccess={props.getRemoteAccess()}
                    getRemoteLockAccess={props.getRemoteLockAccess()}
                    data={data}
                    socket={socket}
                    fault_loading={fault_loading}
                    daily_data_loading={daily_data_loading}
                    aqi_loading={aqi_loading}
                  />
                ) : (
                  <PanelComponent
                    t={props.t}
                    goToPage={goToPage}
                    client_id={props.client_id}
                    application_id={props.application_id}
                    getRemoteAccess={data.show_switch ? props?.getRemoteAccess() : false}
                    getRemoteLockAccess={data.show_lock ? props?.getRemoteLockAccess() : false}
                    data={data}
                    socket={socket}
                    fault_loading={fault_loading}
                    daily_data_loading={daily_data_loading}
                    dgStatus={data.on_off_moving_status}
                    thing_id={data.id}
                    isControlEnabled={data.isLockControlEnabled}
                    operation_mode={data.operation_mode}
                    dgLockStatus={data.dg_lock_status}
                    thingCommandStatus={data.commandStatus}
                    thing_data={thing_data}
  
  
                  />
                )}
              </AntCol>,
            );
          }
        }
      });
    }
  } else {
    panels = (
      <PanelTableComponent
        t={props.t}
        client_id={props.client_id}
        application_id={props.application_id}
        getRemoteAccess={props.getRemoteAccess()}
        getRemoteLockAccess={props.getRemoteLockAccess()}
        column={column}
        data_source={dataSource}
        socket={socket}
        list_data={list_data}
        handleFetchData={handleFetchData}
        loadMoreBtnLoading={loadMoreBtnLoading}
        goToPage={(pagename, key) => goToPage(pagename, key)}
        pageNumber={pageNumber}
      />
    );
  }
  useEffect(() => {
    const { history } = props;
    if(parsedFilterFromUrl !== "?undefined") {
      history.push(
        baseUrl + parsedFilterFromUrl,
      );
    }
    console.log("parsedFilterFromUrl", parsedFilterFromUrl);
    // onUrlChange();
    return () => clearTimeout(timer);
  }, []);
  function goToPage(pagename, key) {
    console.log("parsedFilterFromUrl change", parsedFilterFromUrl)
    props.history.push(
      getBaseUrl(props, pagename, true) +
        "?from=" + (isPanel ? "panel" : "list") +
        (parsedFilterFromUrl !== "?undefined" ? parsedFilterFromUrl : "") +
        "&thing_id=" +
        key,
    );
  }
  if (loading) {
    return (
      <div id="panel_view">
        <Loading />
      </div>
    );
  } else if (panel_body_loading) {
    return <Skeleton />;
  } else {
    return (
      <div
        id="panel_view"
        className={props.bannerToBeShown ? "panel-banner" : ""}
      >
        <PanelHeader
          {...props}
          url={baseUrl + "?view=" + page_key}
          history={props.history}
          onUrlChange={onUrlChange}
          applyFilterSelect={applyFilterSelect}
          search_data={search_data}
          changeView={changeView}
          view_switch_value={isPanel ? "panel" : "list"}
          view_switch_options={[
            { 
              key: "panel", 
              name: props.t('panel_view')
              // name: "Panel View" 
            },
            { 
              key: "list", 
              name: props.t('list_view')
              // name: "List View" 
            },
          ]}
          t={props.t}
          onOffStatus={{
            options: filter_value?.onOffStatus,
            value: filter_value?.on_off_status,
            placeholder: props.t? props.t('select_status'): 'Select Status'
          }}
          on_off_device_status={filter_value?.on_off_device_status}
          category={{
            options: filter_value?.categories,
            value: filter_value?.category,
          }}
          site={{
            options: filter_value?.sites,
            value: filter_value?.site,
          }}
          client_id={props.client_id}
          dg_in_iot_mode={props.dg_in_iot_mode}
          isAurassure={isAurassure(props.vendor_id)}
          primary_color={isAurassure(props.vendor_id) ? "#2497aa" : undefined}
          territories={{
            options: territoryList,
            value: filter_value?.territories,
          }}
        />
        {internal_panel_loading ? (
          <div style={{ "margin-top": 20 }}>
            <Skeleton />
          </div>
        ) : (
          <>
            {isAurassure(props.vendor_id) ? (
              ""
            ) : (
              <div className="status-data-availability">
                <ThingStatus
                  t={props.t}
                  showInfo={true}
                  data={thingsStatusData}
                  // onThingStatusClicked={(e) => onThingStatusClicked(e)}
                  // selectedValue={this.state.selectedDeviceStatusBarValue}
                />
                <div style={{'margin-left': 10}}>
                  <ThingStatus
                    t={props.t}
                    data={deviceStatus()}
                    showInfo={true}
                    // onThingStatusClicked={(e) => this.onThingStatusClicked(e)}
                    // selectedValue={this.state.selectedDeviceStatusBarValue}
                  />
                </div>
              </div>
            )}
            <AntRow
              className={
                "generic-panel-row " + (isPanel ? "generic-panel-div" : "")
              }
              justify={window.innerWidth <= 1280 ? "center" : "start"}
              gutter={20}
            >
              {panels}
              {isPanel &&
              list_data?.things?.length < list_data?.total_counts ? (
                <AntCol span={24} style={{ "text-align": "center" }}>
                  <AntButton
                    type="primary"
                    loading={loadMoreBtnLoading}
                    onClick={handleFetchData}
                  >
                    {loadMoreBtnLoading ?
                      props.t('loading') + "..."
                      // "Loading..." 
                      :
                      props.t('load_more') + "..."
                      // "Load More"
                    }
                  </AntButton>
                </AntCol>
              ) : (
                ""
              )}
            </AntRow>
          </>
        )}
      </div>
    );
  }
}
