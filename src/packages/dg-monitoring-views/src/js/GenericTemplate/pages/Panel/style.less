#panel_view {
	padding: 10px 20px;
	height: calc(100vh - 60px);
	overflow-y: auto;
	overflow-x: hidden;
	position: relative;
	.ant-row {
		width: auto !important;
	}
	.status-data-availability {
		margin-top: 20px;
		display: flex;
		justify-content: center;
		#thing_status {
			background-color: #e9f0ff;
			padding: 0 40px;
			border-radius: 25px;
			.status-text:nth-last-child(1) {
				margin-right: 0px;
			}
		}
		.data-availability-container {
			background-color: #e9f0ff;
			padding: 10px 25px;
			border-radius: 25px;
			margin-left: 10px;
			display: flex;
			align-items: center;
			span {
				font-size: 16px;
				font-weight: 600;
				margin-left: 5px;
			}
		}
	}
	.generic-panel-row {
		padding: 20px;
		.each-panel-col {
			margin-bottom: 30px;
		}
	}
	.generic-panel-row.generic-panel-div {
		padding: 20px 190px;
	}
}

.panel-banner {
	height: calc(100vh - 85px) !important;
}

@media (max-width: 1600px) {
	#panel_view {
		.generic-panel-row.generic-panel-div {
			padding: 20px 41px;
		}
	}
}

@media (max-width: 1536px) {
	#panel_view {
		.generic-panel-row .each-panel-col {
			margin-bottom: 20px;
		}
		.generic-panel-row.generic-panel-div {
			padding: 0px;
			margin-top: 20px;
		}
	}
}

@media (max-width: 1440px) {
	#panel_view {
		.generic-panel-row {
			padding: 20px 0;
		}
	}
}

@media (max-width: 1024px) {
	#panel_view {
		.generic-panel-row.generic-panel-div {
			padding: 10px 50px;
		}
	}
}
