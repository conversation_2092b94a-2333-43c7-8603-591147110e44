#workflow_list {
	display: flex;
	flex-direction: column;
	padding: 0 30px;
}
#workflow_list .workflow-apply-btn {
	background: #ff8500;
	border-radius: 4px;
	padding: 4px 14px;
	color: #fff;
	cursor: pointer;
	transition: all 0.3s ease-in-out;
	display: inline-block;
}
#workflow_list .workflow-apply-btn:hover {
	filter: brightness(1.1);
}
#workflow_list .workflow-list {
	flex: 1;
	background: #fff;
	overflow: auto;
}
.industry-workflow {
	padding: 6px 12px 12px;
	margin: 0 !important;
	/* flex: 1; */
	overflow: hidden auto;
}
.industry-workflow .workflow-header {
	display: flex;
	justify-content: space-between;
	padding-bottom: 2px;
}
.industry-workflow .workflow-header h4 {
	margin: 0;
	display: flex;
	align-items: center;
	font-weight: 600;
}
.industry-workflow .workflow-header .workflow-add-btn {
	color: #ff8500;
	font-weight: bold;
	font-size: 16px;
	padding: 6px 8px;
	cursor: pointer;
	transition: all 0.3s ease-in-out;
}
.industry-workflow .workflow-header .workflow-add-btn:hover {
	background-color: #fff3e5;
}
.industry-workflow .filters-header {
	display: inline-block;
	color: #7686a1;
	font-size: 16px;
	margin-bottom: 5px;
}
.industry-workflow .workflow-add-filter {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.industry-workflow .workflow-filters {
	display: flex;
	flex-wrap: wrap;
}
.industry-workflow .workflow-filters .workflow-child.ant-picker-range {
	width: 300px;
}
.industry-workflow .workflow-filters .ant-select {
	margin-right: 10px;
	width: 170px;
	font-size: 11px;
	background: #f5f6f8 !important;
	border-radius: 10px;
}

.industry-workflow .workflow-filters .ant-select .ant-select-selector {
	color: #7686a1;
	background: #f5f6f8 !important;
	border: 1px solid #f5f6f8;
	padding-left: 30px !important;
	transition: all 0.3s;
	cursor: text;
}

.industry-workflow .workflow-filters .ant-select .ant-select-selection-item {
	font-size: 13px !important;
}

.industry-workflow
	.workflow-filters
	.ant-select
	.ant-select-selector
	.ant-select-selection-placeholder {
	color: #7686a1;
}

.industry-workflow .workflow-filters .select-with-icon {
	display: flex;
	align-items: center;
	position: relative;
}
.industry-workflow .workflow-filters .select-with-icon .icon {
	left: 10px;
	position: absolute;
	top: 18px;
	font-size: 12px;
	z-index: 3;
	color: #7686a1;
}
.industry-workflow .workflow-filters .workflow-child {
	margin: 8px 12px 8px 0;
	border-radius: 5px;
	background: #f5f6f8 !important;
}
.industry-workflow
	.workflow-filters
	.workflow-child.ant-select-multiple.wide-select {
	min-width: 214px;
}
.industry-workflow .workflow-filters .workflow-apply-btn {
	margin: 8px 12px 8px 0;
}
.industry-workflow .workflow-table-col {
	margin-top: 12px;
	.ant-table-row {
		height: 50px;
		cursor: pointer;
	}
	.ant-table-pagination.ant-pagination .ant-pagination-item {
		margin: 0 5px;
	}
}
/* table header sticky*/
.industry-workflow
	.ant-table-wrapper
	.ant-table
	.ant-table-container
	.ant-table-thead {
	position: sticky;
	top: 0;
	z-index: 1;
}
