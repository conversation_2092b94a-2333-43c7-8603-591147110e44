import _find from "lodash/find";
import {
  ParamUnit,
  ParamName,
} from "../../../../data_handling/ParamNameUnitFind";
import { timeDurationMapping } from "../logic/getTimePeriod.js";
import { aqiParameters } from "../../../logic/aqiParametersToShow";
import {
  getAllparameterDataAggr,
  getSelectedparameterDataWithTimestamp,
} from "../../../../data_handling/ParameterDataManipulation";
import { aurassureThingCat } from "../../../logic/isAurassure";
import moment from "moment-timezone";
import _minBy from "lodash/minBy";
import _maxBy from "lodash/maxBy";
import GraphImg from "../../../images/graph.svg";
import minGraphImg from "../../../images/Group 1847.svg";
import maxGraphImg from "../../../images/Group 1848.svg";

export function getParameterGraphRender(findThing, param) {
  let selectedParamDateRange = "",
    fromDate = "",
    uptoDate = "",
    dataType = "",
    isRawVisualization = "",
    dataPeriod = 0;
  let allowed_visualizations = true;
  let string = this.props?.location?.search;
  let time_duration = "last_24_hrs";
  let start_time = null;
  let upto_time = null;
  let selected_param = param;
  let timeDuration = this.getUrlValue(string, "time_duration", "=");
  if (timeDuration !== null && timeDuration !== "") {
    time_duration = timeDuration;
  }
  let startTime = this.getUrlValue(string, "from_time", "=");
  if (startTime !== null) {
    start_time = startTime;
  }
  let uptoTime = this.getUrlValue(string, "upto_time", "=");
  if (uptoTime !== null) {
    upto_time = uptoTime;
  }
  let customDataType = "raw";
  let customDataPeriod = 0;
  const timeDifference = parseInt(upto_time) - parseInt(start_time);
  if (timeDifference > 8 * 86400) {
    customDataType = "avg";
    customDataPeriod = 86400;
  } else if (timeDifference > 3 * 86400) {
    customDataType = "avg";
    customDataPeriod = 3600;
  }
  if (findThing) {
    let findSpecificParam = _find(findThing.parameters, { key: param });
    if (
      findSpecificParam &&
      findSpecificParam.allowed_visualizations &&
      findSpecificParam.allowed_visualizations.length
    ) {
      allowed_visualizations =
        findSpecificParam.allowed_visualizations.includes("raw");
      const timeObj = timeDurationMapping()[time_duration];
      if (time_duration === "custom") {
        selectedParamDateRange = "custom";
        fromDate = parseInt(start_time);
        uptoDate = parseInt(upto_time);
        dataType = this.state.data_type || customDataType;
        dataPeriod = this.state.data_period || customDataPeriod;
        isRawVisualization = this.state.is_raw_visualization || true;
      } else {
        if (allowed_visualizations) {
          if (timeObj) {
            selectedParamDateRange = time_duration;
            fromDate = timeObj.fromTime;
            uptoDate = timeObj.uptoTime;
            dataType = timeObj.dataType;
            dataPeriod = timeObj.dataPeriod;
            isRawVisualization = true;
          }
        } else {
          if (timeObj) {
            selectedParamDateRange = time_duration;
            fromDate = timeObj.fromTime;
            uptoDate = timeObj.uptoTime;
            dataType = "sum";
            dataPeriod = timeObj.cumulativePeriod || timeObj.dataPeriod;
            isRawVisualization = true;
          }
        }
      }
    }
  }
  return [
    selectedParamDateRange,
    fromDate,
    uptoDate,
    dataType,
    isRawVisualization,
    dataPeriod,
    allowed_visualizations,
  ];
}

export function detailedGraphDataFunc() {
  let findThing = _find(this.state.totalData.things, {
    id: parseInt(this.state.thingId),
  });
  const { clubbedParamArr } = this.state;
  let thingRawParamData = "",
    thingRawParamDataYesterday = "",
    thingAvgParamData = "",
    pollutionAvgParamData = "",
    enrgRawParamData = "",
    enrgAggrParamData = "",
    aqiAvgParamData = "",
    thingSumParamData = "";
  if (
    aqiParameters(
      this.state.totalData,
      this.state.thingId,
    )?.uniqDetailsParam.includes(this.state.selectedParam?.[0]) ||
    this.state.selectedParam[0] === "aqi"
  ) {
    aqiAvgParamData = getAllparameterDataAggr(
      this.state.aqiData,
      [parseInt(this.state.thingId)],
      this.state.selectedParam,
      moment.unix(this.state.param_graph_from_time).startOf("hour").unix(),
      moment.unix(this.state.param_graph_upto_time).endOf("hour").unix(),
      3600,
      [this.state.selectedParam[0] === "aqi" ? "value" : "avg"],
    );
  }
  if (this.isFifteenMinAggrView(findThing)) {
    pollutionAvgParamData = getAllparameterDataAggr(
      this.state.pollutionData,
      [parseInt(this.state.thingId)],
      this.state.selectedParam,
      this.calculateNearestFifteenMinuteMultiplier(
        this.state.param_graph_from_time,
      ),
      this.state.param_graph_upto_time,
      900,
      ["avg"],
    );
  }
  if (this.isParamCumulative(this.state.getSelectedParamKey)?.isCumulative) {
    const findEnergyKey = this.isParamCumulative(
      this.state.getSelectedParamKey,
    )?.aggrKey;
    if (this.isSolarSystem()) {
      findEnergyKey.push("calculated_runhour");
    }
    enrgRawParamData = getSelectedparameterDataWithTimestamp(
      this.state.energyRawData,
      [parseInt(this.state.thingId)],
      findEnergyKey,
      this.state.param_graph_from_time,
      this.state.param_graph_upto_time,
    );
    const aggrInterval = this.state.data_period === 86400 ? 'day' : 'hour';
    enrgAggrParamData = getAllparameterDataAggr(
      this.state.energyAggrData,
      [parseInt(this.state.thingId)],
      findEnergyKey,
      moment.unix(this.state.param_graph_from_time).startOf(aggrInterval).unix(),
      moment.unix(this.state.param_graph_upto_time).endOf(aggrInterval).unix(),
      this.state.data_period === 0 ? 3600 : this.state.data_period,
      ["sum"],
    );
  } else {
    if (this.state.data_type === "raw" || clubbedParamArr?.some(key => this.onlyRawParams.includes(key))) {
      thingRawParamDataYesterday = getSelectedparameterDataWithTimestamp(
        this.state.thing_raw_data,
        [parseInt(this.state.thingId)],
        clubbedParamArr,
        this.state.param_graph_from_time,
        this.state.param_graph_upto_time,
      );
      thingRawParamData = getSelectedparameterDataWithTimestamp(
        this.state.thing_raw_data,
        [parseInt(this.state.thingId)],
        clubbedParamArr,
        this.state.param_graph_from_time,
        this.state.param_graph_upto_time,
      );
    }else if (this.state.data_type === "avg") {
      thingAvgParamData = getAllparameterDataAggr(
        this.state.thing_avg_data,
        [parseInt(this.state.thingId)],
        clubbedParamArr,
        moment.unix(this.state.param_graph_from_time).startOf("hour").unix(),
        moment.unix(this.state.param_graph_upto_time).endOf("hour").unix(),
        this.state.data_period,
        ["avg"],
      );
    } else if (this.state.data_type === "sum") {
      thingSumParamData = getAllparameterDataAggr(
        this.state.thing_sum_data,
        [parseInt(this.state.thingId)],
        clubbedParamArr,
        moment.unix(this.state.param_graph_from_time).startOf("hour").unix(),
        moment.unix(this.state.param_graph_upto_time).endOf("hour").unix(),
        this.state.data_period,
        ["sum"],
      );
    }
  }
  this.setState({
    raw_graph_data: thingRawParamData,
    raw_graph_data_yesterday: thingRawParamDataYesterday,
    avg_graph_data: thingAvgParamData,
    sum_graph_data: thingSumParamData,
    pollution_avg_data: pollutionAvgParamData,
    aqiAvgParamData: aqiAvgParamData,
    enrgRawParamData,
    enrgAggrParamData,
    graphSectionLoading: false,
    rawGraphLoading: false,
  });
}

export function paramGraphDataConfigFunc() {
  const { clubbedParamArr, selectedAllowedViz } = this.state;
  let actualGraphData =
    this.state.getSelectedParamKey === "aqi"
      ? this.state.aqiAvgParamData
      : (this.state.data_type === "raw" || clubbedParamArr?.some(key => this.onlyRawParams.includes(key))) && !this.state.is_yesterday
        ? this.state.raw_graph_data
        : this.state.data_type === "raw" && this.state.is_yesterday
          ? this.state.raw_graph_data_yesterday
          : this.state.data_type === "avg"
            ? this.state.avg_graph_data
            : this.state.data_type === "sum"
              ? this.state.sum_graph_data
              : "";
  console.log("actualGraphData", actualGraphData);
  let findThing = _find(this.state.totalData.things, {
    id: parseInt(this.state.thingId),
  });
  const findAurassureThingCat = aurassureThingCat(
    findThing?.category,
    this.props.vendor_id,
  );
  let selectedParamUnit = findAurassureThingCat
    ? _find(findThing.parameters, {
        key: this.state.getSelectedParamKey,
      })?.unit
    : ParamUnit(
        _find(findThing.parameters, {
          key: this.state.getSelectedParamKey,
        })?.unit,
      );

  let paramGraph = JSON.parse(JSON.stringify(this.state.GraphObjectData));
  paramGraph.graph_data.config.backgroundColor = "transparent";
  paramGraph.graph_data.config.plotOptions = {
    series: {
      boostThreshold: 0,
      marker: {
        radius: 1.5,
      },
    },
  };
  if (
    clubbedParamArr?.length > 1 ||
    (selectedAllowedViz?.length > 1 && this.state.data_type === "raw")
  ) {
    paramGraph.graph_data.config.legend.enabled = true;
  }
  paramGraph.graph_data.config.yAxis.title = {
    text: selectedParamUnit,
    style: {
      color: "#7686A1",
    },
  };
  const selectedParamKey = this.state.getSelectedParamKey;
  paramGraph.graph_data.config.yAxis.labels = {
    formatter: function () {
      if(selectedParamKey === 'dr_st' && this.value !== 0 && this.value !== 1) {
        return "";
      }
      if(selectedParamKey === 'dr_st') {
        return this.value === 1 ? "Open" : "Closed"
      }
      return [this.value.toFixed(2)];
    },
  };
  paramGraph.graph_data.config.xAxis.title.text = this.isFifteenMinAggrView(
    findThing,
  )
    ? "Raw Trend"
    : "Date & Time";
  let thingParameterDataRawFinal = [];
  const colorArr = ["#4268A757", "#7a010957", "#f2890757", "#3b031057"];
  const seriesData = [],
    rawSeriesData = {},
    rawSeriesDataUnprocessed = {},
    avgMinMaxData = {};
  const selectedAllowedVizCustom = selectedAllowedViz?.length
    ? selectedAllowedViz
    : ["processed"];
  const unProcessedViz = selectedAllowedVizCustom?.includes("unprocessed");
  if (clubbedParamArr?.length) {
    clubbedParamArr.forEach((param, ind) => {
      if (!rawSeriesData[param]) {
        rawSeriesData[param] = [];
      }
      if (!rawSeriesDataUnprocessed[param]) {
        rawSeriesDataUnprocessed[param] = [];
      }
      if (!avgMinMaxData[param]) {
        avgMinMaxData[param] = {};
      }
      let selectedParamName = findAurassureThingCat
        ? _find(findThing.parameters, {
            key: param,
          })?.name
        : ParamName(param, findThing.parameters);
      if (actualGraphData?.[this.state.thingId]?.[param]?.length) {
        const graphData = actualGraphData[this.state.thingId][param];
        if (this.state.data_period === 0) {
          graphData.forEach((data, ind) => {
            const offlineTimeoutCondition = findAurassureThingCat
              ? 120000
              : findThing?.offline_timeout && findThing?.offline_timeout > 0
                ? parseInt(findThing?.offline_timeout) * 1000
                : 900000;
            const isOfflineTimeoutExceeded =
              graphData[ind + 1] &&
              graphData[ind + 1][0] - graphData[ind][0] >
                offlineTimeoutCondition;
            isOfflineTimeoutExceeded
              ? rawSeriesData[param].push(
                  [data[0], parseFloat(data[1])],
                  [data[0] + offlineTimeoutCondition, null],
                )
              : rawSeriesData[param].push([data[0], parseFloat(data[1])]);
            if (unProcessedViz) {
              if (isOfflineTimeoutExceeded) {
                rawSeriesDataUnprocessed[param].push(
                  [
                    data[0],
                    parseFloat(
                      _find(this.state.thing_raw_data, {
                        time: data[0] / 1000,
                      })?.unprocessed_parameter_values?.[param],
                    ),
                  ],
                  [data[0] + offlineTimeoutCondition, null],
                );
              } else {
                rawSeriesDataUnprocessed[param].push([
                  data[0],
                  parseFloat(
                    _find(this.state.thing_raw_data, {
                      time: data[0] / 1000,
                    })?.unprocessed_parameter_values?.[param],
                  ),
                ]);
              }
            }
          });
          thingParameterDataRawFinal = rawSeriesData[param];
        } else {
          thingParameterDataRawFinal =
            actualGraphData?.[this.state.thingId]?.[param];
        }
      }
      if(_find(findThing.parameters, {key: param})) {
        selectedAllowedVizCustom.forEach((viz) => {
          seriesData.push({
            name: `${selectedParamName} ${selectedAllowedViz?.length > 1 ? `(${viz === "unprocessed" ? this.props.t("unprocessed") : this.props.t("processed")})` : ""}`,
            type:
              clubbedParamArr?.length > 1 ||
              selectedAllowedViz?.length > 1 ||
              findAurassureThingCat
                ? "spline"
                : "area",
            data:
              viz === "unprocessed"
                ? rawSeriesDataUnprocessed[param]
                : thingParameterDataRawFinal,
            color: findAurassureThingCat
              ? viz === "unprocessed"
                ? "#7a010957"
                : "#00b050"
              : colorArr[ind],
            key: param,
          });
        });
      }
      let dataLength = 0,
        avgValue = "",
        minValue = "",
        maxValue = "";
      let data = actualGraphData?.[this.state.thingId]?.[param];
      const notNullData = data?.filter((d) => !isNaN(d[1]) && d[1] !== null);
      dataLength = notNullData?.length;
      avgValue = notNullData?.reduce(
        (r, a) => a.map((b, i) => (r[i] || 0) + b),
        [],
      );
      minValue = _minBy(notNullData, function (o) {
        return o[1];
      });
      maxValue = _maxBy(notNullData, function (o) {
        return o[1];
      });
      if (
        _find(findThing.parameters, {
          key: param,
        })
      ) {
        avgMinMaxData[param] = {
          avg: {
            title: "Average",
            value:
              dataLength > 0 && avgValue?.length
                ? parseFloat(avgValue[1] / dataLength).toFixed(2)
                : "NA",
            image: GraphImg,
            unit: selectedParamUnit,
          },
          min: {
            title: "Minimum",
            time: minValue?.length ? minValue[0] : 0,
            value: minValue?.length ? parseFloat(minValue[1]).toFixed(2) : "NA",
            image: minGraphImg,
            unit: selectedParamUnit,
          },
          max: {
            title: "Maximum",
            time: maxValue?.length ? maxValue[0] : 0,
            value: maxValue?.length ? parseFloat(maxValue[1]).toFixed(2) : "NA",
            image: maxGraphImg,
            unit: selectedParamUnit,
          },
          name: selectedParamName,
        };
      }
    });
  }
  if (
    selectedParamKey === "soc" &&
    findThing?.category === 92 &&
    !isNaN(parseInt(avgMinMaxData?.["soc"]?.min?.value))
  ) {
    paramGraph.graph_data.config.yAxis.min = Math.floor(parseInt(
      avgMinMaxData?.["soc"]?.min?.value,
    ));
  }
  if (
    selectedParamKey === "soc" &&
    findThing?.category === 92 &&
    !isNaN(parseInt(avgMinMaxData?.["soc"]?.max?.value))
  ) {
    paramGraph.graph_data.config.yAxis.max = Math.ceil(parseInt(
      avgMinMaxData?.["soc"]?.max?.value,
    ));
  }
  paramGraph.graph_data.series_data = seriesData;
  paramGraph.graph_data.config.tooltip.shared = true;
  paramGraph.graph_data.config.tooltip.valueSuffix = " " + selectedParamUnit;
  paramGraph.graph_data.config.tooltip.xDateFormat =
  this.state.data_type === "raw"
  ? "%d-%m-%Y, %H:%M:%S"
  : this.state.data_period > 3600
  ? "%d-%m-%Y"
  : "%d-%m-%Y, %H:00-%H:59";
  paramGraph.graph_data.config.chart.height = 200;
  return {
    avgMinMaxData,
    graph_data_config: paramGraph,
  };
}

export function paramTrendFunc() {
  const { totalParamToShow, alert_data, totalData } = this.state;
  let paramArray = [],
    thresholdPlotLines = {};
  let selectedThinglatestParam = this.latestDataFunc();
  let findThings = _find(totalData.things, {
    id: selectedThinglatestParam?.thing_id,
  });
  const { things_categories } = totalData;
  if (findThings) {
    const findThingCat = _find(things_categories, { id: findThings.category });
    if (totalParamToShow && totalParamToShow.length) {
      totalParamToShow.map((params) => {
        const findParamFromCategory = _find(
          findThingCat.pages?.detailed_view?.parameter_trend?.parameters,
          { key: params },
        );
        let upperThresholdForParameters = undefined;
        let lowerThresholdForParameters = undefined;
        if (!thresholdPlotLines[params]) {
          thresholdPlotLines[params] = [];
        }
        if (alert_data && alert_data.alert_rules) {
          upperThresholdForParameters = _find(
            alert_data.alert_rules,
            function (o) {
              return (
                o?.tags?.includes(params) &&
                o?.tags?.includes("Danger") &&
                o?.is_active &&
                o?.rule_data?.ul_danger &&
                !isNaN(parseFloat(o.rule_data.ul_danger))
              );
            },
          )?.rule_data.ul_danger;
          if (params === "ph") {
            lowerThresholdForParameters = _find(
              alert_data.alert_rules,
              function (o) {
                return (
                  o?.tags?.includes(params) &&
                  o?.tags?.includes("Danger") &&
                  o?.is_active &&
                  o?.rule_data?.ll_danger &&
                  !isNaN(parseFloat(o.rule_data.ll_danger))
                );
              },
            )?.rule_data.ll_danger;
          }
        }
        if (upperThresholdForParameters) {
          thresholdPlotLines[params].push({
            value: upperThresholdForParameters,
            color: "red",
            width: 1,
            zIndex: 5,
            label: {
              text:
                upperThresholdForParameters +
                " " +
                (aurassureThingCat(findThings?.category, this.props.vendor_id)
                  ? _find(findThings.parameters, {
                      key: params,
                    })?.unit
                  : ParamUnit(
                      _find(findThings.parameters, {
                        key: params,
                      })?.unit,
                    )),
              align: "right",
              style: {
                color: "red",
              },
            },
          });
        }
        if (lowerThresholdForParameters) {
          thresholdPlotLines[params].push({
            value: lowerThresholdForParameters,
            color: "red",
            width: 1,
            zIndex: 5,
            label: {
              text:
                lowerThresholdForParameters +
                " " +
                (aurassureThingCat(findThings?.category, this.props.vendor_id)
                  ? _find(findThings.parameters, {
                      key: params,
                    })?.unit
                  : ParamUnit(
                      _find(findThings.parameters, {
                        key: params,
                      })?.unit,
                    )),
              align: "right",
              style: {
                color: "red",
              },
            },
          });
        }
        const findParams = _find(findThings.parameters, {
          key: params,
        });

        paramArray.push({
          tooltipDetails: findParams?.param_details?.description?.length
            ? `Description: ${findParams.param_details.description}`
            : undefined,
          key: params,
          name: findParamFromCategory?.name
            ? findParamFromCategory.name
            : aurassureThingCat(findThings?.category, this.props.vendor_id)
              ? findParams?.name
              : ParamName(params, findThings.parameters),
          graphThreshold: upperThresholdForParameters,
          threshold:
            upperThresholdForParameters || lowerThresholdForParameters
              ? lowerThresholdForParameters
                ? lowerThresholdForParameters +
                  "-" +
                  upperThresholdForParameters
                : upperThresholdForParameters
              : undefined,
          unit: findParamFromCategory?.unit ? findParamFromCategory.unit :  aurassureThingCat(findThings?.category, this.props.vendor_id)
            ? findParams?.unit
            : ParamUnit(findParams?.unit),
        });
      });
    }
  }
  let paramDetails = {
    paramArray: paramArray,
    thresholdPlotLines: thresholdPlotLines,
    avgMinMaxData: this.paramGraphDataConfigFunc()?.avgMinMaxData,
    graph_config: this.paramGraphDataConfigFunc()?.graph_data_config,
  };
  return paramDetails;
}
