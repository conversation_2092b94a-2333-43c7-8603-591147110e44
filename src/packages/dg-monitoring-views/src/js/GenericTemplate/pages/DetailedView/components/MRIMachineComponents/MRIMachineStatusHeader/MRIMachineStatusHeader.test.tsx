import React from 'react';
import { render, screen } from '@testing-library/react';
import MRIMachineStatusHeader from '.';
// import { MRIMachineStatusParams } from '../../../../../configs/MRIMachineConfig';

describe('MRIMachineStatusHeader', () => {
  const latestParamsMock = {
    data: {
      he_press_alm: 1,
      he_lvl_alm: 0,
      wtr_flow_alm: 1,
      rf_active: 1,
      heater_stat: 2,
    },
  };

  const keyNamePairMock = [
    { key: 'he_press_alm', name: 'He Pressure Alarm' },
    { key: 'he_lvl_alm', name: 'Helium Level Alarm' },
    { key: 'wtr_flow_alm', name: 'Water Flow Alarm' },
    { key: 'rf_active', name: 'RF Active' },
    { key: 'heater_stat', name: 'Heater Status' },
  ];

  it('should render the component with the correct status values', () => {
    render(
      <MRIMachineStatusHeader
        latestParams={latestParamsMock}
        keyNamePair={keyNamePairMock}
      />
    );

    // Check if the component header is rendered
    expect(screen.getByText('Status')).toBeInTheDocument();

    // Verify that all key-name pairs are displayed correctly
    keyNamePairMock.forEach(({ key, name }) => {
      const nameElement = screen.getByText(new RegExp(`^${name}:$`, 'i')); // Handle the colon at the end
      expect(nameElement).toBeInTheDocument();
    });

    // Verify the display values for each status
    const displayValues = {
      'He Pressure Alarm': 'Yes', // mapped from the "1" value in `latestParamsMock`
      'Helium Level Alarm': 'No', // mapped from the "0" value in `latestParamsMock`
      'Water Flow Alarm': 'Yes', // mapped from the "1" value in `latestParamsMock`
      'RF Active': 'Active', // mapped from the "1" value in `latestParamsMock`
      'Heater Status': 'Not Available', // mapped from the "2" value in `latestParamsMock`
    };

    Object.entries(displayValues).forEach(([name, expectedDisplayValue]) => {
      const statusItem = screen.getByText(new RegExp(`${name}:`, 'i')); // Match the name followed by the colon
      expect(statusItem.nextSibling).toHaveTextContent(expectedDisplayValue); // Check the sibling element for the display value
    });
  });

  it('should render "NA" for values that are not available in the config', () => {
    const latestParamsWithInvalidDataMock = {
      data: {
        he_press_alm: 999, // Invalid key, not in MRIMachineStatusParams
      },
    };

    render(
      <MRIMachineStatusHeader
        latestParams={latestParamsWithInvalidDataMock}
        keyNamePair={keyNamePairMock}
      />
    );

    // Expect "NA" for the invalid status key
    const invalidStatus = screen.getByText('He Pressure Alarm:');
    expect(invalidStatus.nextSibling).toHaveTextContent('NA');
  });

  it('should not render a key-value pair if the key does not exist in the keyNamePair', () => {
    const keyNamePairWithoutHeaterMock = keyNamePairMock.filter(
      (pair) => pair.key !== 'heater_stat'
    );

    render(
      <MRIMachineStatusHeader
        latestParams={latestParamsMock}
        keyNamePair={keyNamePairWithoutHeaterMock}
      />
    );

    // Check if Heater Status is not rendered
    expect(screen.queryByText('Heater Status')).not.toBeInTheDocument();
  });

  it('should render the correct divider between status items', () => {
    render(
      <MRIMachineStatusHeader
        latestParams={latestParamsMock}
        keyNamePair={keyNamePairMock}
      />
    );

    // Check if divider is rendered between status items (including after the last item)
    const dividerElements = screen.getAllByText('|');
    expect(dividerElements.length).toBe(keyNamePairMock.length-1); // Now expect the same number of dividers as items
  });
});
