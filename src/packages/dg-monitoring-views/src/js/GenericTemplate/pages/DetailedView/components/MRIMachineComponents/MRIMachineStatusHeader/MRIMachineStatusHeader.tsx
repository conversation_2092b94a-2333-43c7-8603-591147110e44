/**
 * MRIMachineStatusHeader Component
 * 
 * A React functional component that displays the status of an MRI machine.
 * It takes the latest parameters and a key-name pair to render the status information.
 * 
 * @component
 * @param {MRIMachineStatusHeaderProps} props - The props for the component.
 * @param {LatestParams} props.latestParams - The latest parameters for the MRI machine status.
 * @param {Array<KeyNamePair>} props.keyNamePair - An array of key-name pairs for display.
 * 
 * @returns {JSX.Element} The rendered component.
 * 
 * @example
 * const latestParams = {
 *   data: {
 *     status1: 0,
 *     status2: 1,
 *   },
 * };
 * const keyNamePair = [
 *   { key: 'status1', name: 'Status 1' },
 *   { key: 'status2', name: 'Status 2' },
 * ];
 * 
 * <MRIMachineStatusHeader latestParams={latestParams} keyNamePair={keyNamePair} />
 */

import React, { useMemo } from "react";
import { MRIMachineStatusParams } from "../../../../../configs/MRIMachineConfig";
import "./style.less";

// Interface for key-name pairs
interface KeyNamePair {
  key: string;
  name: string;
}

// Interface for the latest parameters
interface LatestParams {
  data?: {
    [key: string]: any;
  };
}

// Props interface for the MRIMachineStatusHeader component
interface MRIMachineStatusHeaderProps {
  latestParams: LatestParams;
  keyNamePair: KeyNamePair[];
}

const MRIMachineStatusHeader: React.FC<MRIMachineStatusHeaderProps> = ({
  latestParams,
  keyNamePair = [],
}) => {
  const displayData = useMemo(() => {
    return MRIMachineStatusParams.map((status) => {
      const incomingStatus = latestParams?.data?.[status.key] ?? null;
      const indexKey = String(incomingStatus);
      const displayValue = status.values?.[indexKey] ??  "NA";

      const keyName =
        keyNamePair.find((pair) => pair.key === status.key)?.name || null;

      if (!keyName) return null;
      return {
        key: status.key,
        name: keyName,
        displayValue,
      };
    });
  }, [latestParams, keyNamePair]);

  return (
    <div className="mri-machine-status-header">
      <h5 className="status-header-title">Status</h5>
      <div className="status-bar">
        {displayData.map(
          (status, index) =>
            status && (<>
                { index!=0 && (
                  <span className="divider">|</span>
                )}
              <div className="status-item-container" key={status.key}>
                <div className="status-item">
                  <span className="status-name">{status.name}:</span>
                  <strong>{status.displayValue}</strong>
                </div>
              </div>
           </>
          ),
        )}
      </div>
    </div>
  );
};

export default MRIMachineStatusHeader;
