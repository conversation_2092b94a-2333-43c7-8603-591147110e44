import React from 'react';
import { solarPumpStatusParams } from '../../../../../configs/SolarPumpConfig';
import './style.less';

interface SolarPumpStatusHeaderProps {
  statusParams: Array<string>;
  latestParamData?: {
    data?: {
      [key: string]: number;
    };
  };
}

const SolarPumpStatusHeader: React.FC<SolarPumpStatusHeaderProps> = ({ statusParams, latestParamData }) => {
  const statusValues = statusParams.map((statusParam) => {
    return { key: statusParam, value: latestParamData?.data?.[statusParam] || -1};
  }).filter(status => parseInt(String(status.value)) === 1);

  const statusValuesDisplayed: string[] = [];
  statusValues.forEach((status) => {
    const configParam = solarPumpStatusParams.find(param => param.key === status.key);
    if(status.value !== -1 && configParam?.values?.[status.value]) {
      statusValuesDisplayed.push(configParam.values[status.value]);
    }
  });

  return (
    <div className="solar-pump-status-header">
      <div className="title">Status</div>
      <div className="status-items-container">
        {statusValuesDisplayed.map((status, index) => (
          <React.Fragment key={index}>
            <div className="status-item">
              {status}
            </div>
            {index < statusValuesDisplayed.length - 1 && <div className="divider" />}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

export default SolarPumpStatusHeader;
