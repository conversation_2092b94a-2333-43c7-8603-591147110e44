import React from "react";
import SolarInverterConfig from "../../../../../configs/SolarInverterConfig";
import "./style.less";

const loadStatus = SolarInverterConfig.loadStatus;
const dspState = SolarInverterConfig.dspState;
const supervisoryState = SolarInverterConfig.supervisoryState;

const SolarInverterStatusHeader: React.FC = (props: any) => {
  let statusData = props.statusParams;

  statusData.forEach((status: any) => {
    const statusVal = props.latestParamData?.data?.[status.key];

    if (status.key === "ld_st") {
      status.value = loadStatus[statusVal] ?? "NA";
    } else if (status.key === "dsp_st") {
      status.value = dspState[statusVal] ?? "NA";
    } else {
      status.value = supervisoryState[statusVal] ?? "NA";
    }
  });

  return (
    <div className={`inverter-status-header ${props.view === "site" ? "align-left" : ""}`}>
      <div className="status-bar">
        {(props.view === "site") && <div>
          <span className="status-name mild">Solar Inverter Status</span>
          <span className="divider">|</span>
        </div>
        }
        {statusData.map((status: any, index: number) => (
          <div className="status-item-container" key={index}>
            <div className="status-item">
              <span className="status-name">{status.name}</span>
              <strong>{status.value}</strong>
            </div>
            {index < statusData.length - 1 && (
                <span className="divider">|</span>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default SolarInverterStatusHeader;