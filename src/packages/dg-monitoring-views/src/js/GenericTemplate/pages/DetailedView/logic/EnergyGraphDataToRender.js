import _find from "lodash/find";
import _minBy from "lodash/minBy";
import _maxBy from "lodash/maxBy";
import {
  ParamName,
  ParamUnit,
} from "../../../../data_handling/ParamNameUnitFind";
import { secondsToTimeFormat } from "../../../../data_handling/TimeFormatting";
import Highcharts from "highcharts";

function getGraphFinalObj(
  findThing,
  selectedCategory,
  GraphObjectData,
  getSelectedParamKey,
  selectedParamUnit,
  selectedParamName,
  key,
  thingId,
  dataPr,
  dataType,
  config = {},
) {
  let finalGraph = JSON.parse(JSON.stringify(GraphObjectData));
  finalGraph.graph_data.config.backgroundColor = "transparent";
  finalGraph.graph_data.config.plotOptions = {
    series: {
      boostThreshold: 0,
      marker: {
        radius: 1.5,
      },
    },
  };
  finalGraph.graph_data.config.yAxis.title = {
    text: selectedParamUnit,
    style: {
      color: "#7686A1",
    },
  };
  finalGraph.graph_data.config.yAxis.labels = {
    formatter: function () {
      return [this.value.toFixed(2)];
    },
  };
  finalGraph.graph_data.config.xAxis.title.text = config.xTitle;
  let seriesData = [];
  const graphData = config.data;
  const findSelectedParamFromCat = _find(
    selectedCategory.pages?.detailed_view?.parameter_trend?.parameters,
    { key: getSelectedParamKey },
  );
  console.log("findSelectedParamFromCat", findSelectedParamFromCat);
  const isParamRnhr = findSelectedParamFromCat?.type === "rnhr";
  if (
    graphData &&
    graphData[thingId] &&
    graphData[thingId][key] &&
    graphData[thingId][key].length
  ) {
    if (dataPr === 0 && dataType === "raw") {
      graphData[thingId][key].map((data, ind) => {
        if (
          graphData[thingId][key][ind + 1] &&
          graphData[thingId][key][ind + 1][0] -
            graphData[thingId][key][ind][0] >
            (findThing?.offline_timeout && findThing?.offline_timeout > 0
              ? parseInt(findThing?.offline_timeout) * 1000
              : 900000)
        ) {
          seriesData.push([
            data[0] +
              (findThing?.offline_timeout && findThing?.offline_timeout > 0
                ? parseInt(findThing?.offline_timeout) * 1000
                : 900000),
            null,
          ]);
        } else {
          seriesData.push([data[0], parseFloat(data[1])]);
        }
      });
    } else {
      graphData[thingId][key].map((data, ind) => {
        seriesData.push([
          data[0],
          isParamRnhr ? parseFloat(data[1]) / 3600 : parseFloat(data[1]),
        ]);
      });
    }
  }
  finalGraph.graph_data.series_data = [
    {
      name: selectedParamName,
      type: config.type,
      data: seriesData,
      color: config.color,
      key: getSelectedParamKey,
    },
  ];

  const xDateFormat =
    dataType === "raw"
      ? "%d-%m-%Y, %H:%M:%S"
      : dataPr > 3600
        ? "%d-%m-%Y"
        : "%d-%m-%Y, %H:00 - %H:59";
  finalGraph.graph_data.config.tooltip.xDateFormat = xDateFormat;
  finalGraph.graph_data.config.tooltip.valueSuffix = " " + selectedParamUnit;
  if (isParamRnhr) {
    finalGraph.graph_data.config.tooltip.formatter = function () {
      const xFormatted = Highcharts.dateFormat(xDateFormat, this.x);
      const yFormatted = secondsToTimeFormat(this.y * 3600, "HH:mm");
      return `${xFormatted}<br/><span style="color: ${this.series.color}">${this.series.name} (HH:mm)</span>: <b>${yFormatted}</b>`;
    };
  }
  finalGraph.graph_data.config.chart.height = 200;

  let avgValue = "",
    minValue = "",
    maxValue = "";
  let actualData = [];
  if (graphData?.[thingId]?.[key]?.length) {
    graphData[thingId][key].map((datas) => {
      if (datas?.[1] && datas[1] !== null) actualData.push(datas);
    });
  }
  let data = graphData?.[thingId]?.[key];
  avgValue = data?.reduce((r, a) => a.map((b, i) => (r[i] || 0) + b), []);
  minValue = _minBy(data, function (o) {
    return o[1];
  });
  maxValue = _maxBy(data, function (o) {
    return o[1];
  });
  let dataLength = 0,
    avg = {},
    min = {},
    max = {};
  if (actualData?.length) {
    dataLength = actualData.length;
  }
  if (dataLength > 0 && avgValue && avgValue.length) {
    const avgData = parseFloat(avgValue[1] / dataLength);
    avg = {
      value: isParamRnhr
        ? secondsToTimeFormat(Math.floor(avgData))
        : avgData.toFixed(2),
    };
  } else {
    avg = "NA";
  }
  if (minValue && minValue.length) {
    min = {
      time: minValue[0],
      value: isParamRnhr
        ? secondsToTimeFormat(Math.floor(minValue[1]))
        : parseFloat(minValue[1]).toFixed(2),
    };
  } else {
    min = {
      time: 0,
      value: "NA",
    };
  }
  if (maxValue && maxValue.length) {
    max = {
      time: maxValue[0],
      value: isParamRnhr
        ? secondsToTimeFormat(Math.floor(maxValue[1]))
        : parseFloat(maxValue[1]).toFixed(2),
    };
  } else {
    max = {
      time: 0,
      value: "NA",
    };
  }
  return {
    total: avgValue?.length
      ? {
          value: isParamRnhr
            ? secondsToTimeFormat(Math.floor(avgValue[1]))
            : parseFloat(avgValue[1]).toFixed(2),
        }
      : undefined,
    avg: avg,
    min: min,
    max: max,
    paramKey: getSelectedParamKey,
    graph_data_config: finalGraph,
  };
}

export function getEnergyGraphData() {
  const { GraphObjectData, getSelectedParamKey } = this.state;
  let rawGraphData = this.state.enrgRawParamData;
  let aggrGraphData = this.state.enrgAggrParamData;
  const findParamkey = this.isParamCumulative(getSelectedParamKey)?.aggrKey;
  let findThing = _find(this.state.totalData.things, {
    id: parseInt(this.state.thingId),
  });
  const selectedCategory = _find(this.state.totalData?.things_categories, {
    id: findThing.category,
  });
  let selectedParamName = ParamName(getSelectedParamKey, findThing.parameters);
  let selectedParamUnit =
    selectedParamName === "Active Hour"
      ? "Hrs"
      : ParamUnit(
          _find(findThing.parameters, {
            key: getSelectedParamKey,
          })?.unit,
        );

  let finalAggrGraphObj = getGraphFinalObj(
    findThing,
    selectedCategory,
    GraphObjectData,
    getSelectedParamKey,
    selectedParamUnit,
    selectedParamName,
    findParamkey,
    this.state.thingId,
    this.state.data_period,
    "aggr",
    {
      xTitle: `Aggregated Data (${
        this.state.data_period < 86400 ? "Hourly" : "Daily"
      })`,
      data: aggrGraphData,
      type: "column",
      color: "#9094fb",
    },
  );
  const aggrObj = {
    total: finalAggrGraphObj?.total,
    avg: finalAggrGraphObj?.avg,
    max: finalAggrGraphObj?.max,
    graphData: finalAggrGraphObj?.graph_data_config,
  };

  if (findThing.category === 91) {
    let getGenerationHoursTotalForSelco = getGraphFinalObj(
      findThing,
      selectedCategory,
      GraphObjectData,
      getSelectedParamKey,
      selectedParamUnit,
      selectedParamName,
      "calculated_runhour",
      this.state.thingId,
      this.state.data_period,
      "aggr",
      {
        data: aggrGraphData,
      },
    );
    aggrObj.generation_hours = getGenerationHoursTotalForSelco?.total?.value;
  }
  return aggrObj;
}
