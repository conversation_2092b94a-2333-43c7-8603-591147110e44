.mri-machine-status-header {
    display: flex;
    flex-direction: column; 
    align-items: flex-start; 
    background-color: #F4F4F4;
    padding: 16px;
    // padding-bottom: 0px;
    width: 100%;
    border-radius: 16px;
    margin-bottom: 30px;
    margin-top: -10px;
    // gap: 8px;
    
    
    .status-header-title {
        font-size: 16px;
        font-weight: 500;
        color: #4F4F4F;
        margin: 0px;
        margin-bottom: 8px;
    }
    
    .status-bar {
        display: flex;
        align-items: center;
        color: #4F4F4F;
        gap: 6px;
        // padding-bottom: 0px;
        // margin-bottom: -32px;
        justify-content: flex-start;

        flex-wrap: wrap;
        margin: auto 0px;
        
        .status-item-container {
            display: flex;
            align-items: center;
            margin: 0px;
        }
    }
    
    .status-item {
        display: flex;
        align-items: center;
        color: #4F4F4F;
        gap: 8px;
        // margin: 8px 8px;
        // margin-left: 0px;
        font-size: 14px;
    }
    
    .status-item strong {
        color: #232323;
        font-weight: 700;
    }

    .divider {
        margin: 0px 8px; 
        // gap: 8px;
        color: #4f4f4f;
    }
}
