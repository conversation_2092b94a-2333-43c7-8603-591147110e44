.inverter-status-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  width: 100%;
  
  &.align-left {
    justify-content: flex-start;
  }
  
  .status-bar {
    display: flex;
    align-items: center;
    background-color: #f8f9fc;
    padding: 7px 24px;
    border-radius: 20px;
    min-width: 320px;
    justify-content: center;
    white-space: nowrap;
    
    .status-item-container {
      display: flex;
    }
  }
  
  .status-item {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .status-name {
      font-size: 13px;
    }
    
  }
  
  .status-item strong {
    font-weight: 600;
    font-size: 16px;
  }
  
  .divider {
    margin: 0 15px;
    color: #afafaf;
  }
  
  .mild {
    font-weight: 600;
  }
}
