import { flushSync } from "react-dom";
/*Data AHndling*/
import { disconnectSocketConnection, retriveAlerts } from "@datoms/js-sdk";
import SelectTimePeriodWithCustom from "../../component/SelectTimePeriodWithCustom";
import { getEnergyGraphData } from "./logic/EnergyGraphDataToRender.js";
import SearchInput from "@datoms/react-components/src/components/SearchInput";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import AntButton from "@datoms/react-components/src/components/AntButton";
import AntCol from "@datoms/react-components/src/components/AntCol";
import AntSelect from "@datoms/react-components/src/components/AntSelect";
import AntOption from "@datoms/react-components/src/components/AntOption";
import AntRow from "@datoms/react-components/src/components/AntRow";
import AntSpin from "@datoms/react-components/src/components/AntSpin";
import GraphHighcharts from "@datoms/react-components/src/components/GraphHighcharts";
import Loading from "@datoms/react-components/src/components/Loading";
import GraphImg from "../../images/graph.svg";
import minGraphImg from "../../images/Group 1847.svg";
import maxGraphImg from "../../images/Group 1848.svg";
import _remove from "lodash/remove";
import _maxBy from "lodash/maxBy";
import _uniqBy from "lodash/uniqBy";
import _minBy from "lodash/minBy";
import _find from "lodash/find";
import _uniq from "lodash/uniq";
import _findIndex from "lodash/findIndex";
import _sortBy from "lodash/sortBy";
import _orderBy from "lodash/orderBy";
import _filter from "lodash/filter";
import _map from "lodash/map";
import _merge from "lodash/merge";
import moment from "moment-timezone";
import queryString from "query-string";
import React from "react";
/*Styles*/
import "./style.less";
import GraphObjectData from "../../../configuration/GraphObjectData";
import SearchObjectData from "../../../configuration/SearchObjectData";
/*Components*/
import ActivityObject from "../../../configuration/detailedViewConfig/ActivityObject";
import AllParametersConfig from "../../../configuration/detailedViewConfig/AllParametersConfig";
/*Configs*/
import HeaderObject from "../../../configuration/detailedViewConfig/HeaderObject";
import PanelObject from "../../../configuration/detailedViewConfig/PanelObject";
import ParamGraphConfig from "../../../configuration/detailedViewConfig/ParamGraphConfig";
import TripDataConfig from "../../../configuration/detailedViewConfig/TripDataConfig";
import {
  serviceAlertFetch,
  maintenanceTextGen,
} from "../../../data_handling/applicationMaintenanceDue";
import CommonHeader from "../../component/CommonHeader";
import { fetchAddress, getAddressFromLat } from "../../utility";
import DrawerForThingWithStatus from "../../component/DrawerForThing";
import { ParamUnit, ParamName } from "../../../data_handling/ParamNameUnitFind";
import {
  isRealTime,
  isDetailedView,
} from "../../logic/RealTimeAndDetailedViewAvailability";
import FifteenMinAggrDetailedView from "./components/FifteenMinAggrDetailedView";
import {
  openNotification,
  doPTZControl,
  ipControl,
} from "../../logic/CameraControlLogic";
import StreamedianPlayer from "../../component/StreamedianPlayer";
import CameraPTZControl from "../../component/CameraPTZControl";
import NoIpCameraImg from "../../images/no_ip_camera_view.png";
import { getBaseUrl } from "@datoms/js-utils/src/base-url-logic";
import { ipLatestData } from "../../logic/IpLatestData";
import { aurassureThingCat } from "../../logic/isAurassure";
import AurassureDetailedView from "../../component/AurassureComponents/pages/DetailedView";
import DGDetailedView from "./components/DGDetailedView";
import FolwAndMotorDetailedView from "../../component/FlowAndMotorComponents/FolwAndMotorDetailedView";
import { paramAsPerOrder } from "../../../data_handling/ParamOrder";
import { mixPanelTrackUser } from "@datoms/js-utils/src/mix-panel";
import SolarSystemLifetimeEnvComp from "./components/SolarComponents/SolarSystemLifetimeEnvComp";
import SolarInverterStatusHeader from "./components/SolarInverterComponents/SolarInverterStatusHeader";
import MRIMachineStatusHeader from "./components/MRIMachineComponents/MRIMachineStatusHeader";
import SolarPumpStatusHeader from "./components/SolarPumpComponents/SolarPumpStatusHeader";
import {SolarInverterStatusKeys} from "../../configs/SolarInverterConfig"
import { solarPumpStatusParamsKeys } from "../../configs/SolarPumpConfig";
import {
  getLifetimeAsPerParam,
  getLifetimeEnergy,
  getCo2EmissionValue,
  getTreeSavedValue,
} from "./logic/solarSystemLifetimeEnvData.js";
import OnlyEnergyGraph from "./components/OnlyEnergyGraph";
import { timeDurationMapping } from "./logic/getTimePeriod.js";
import { getStatusIcon } from "../../logic/getStatusIcon.js";
import { MetricItem } from "./components/MetricComponent";
import PADetailedViewFooter from "./components/ProcessAnalyzerDetailedView/PADetailedViewFooter";
import PADetailedViewHeader from "./components/ProcessAnalyzerDetailedView/PADetailedViewHeader";
import {
  thingGraphDataFunction,
  getEnergySummaryData,
  getLastHourAqi,
  calculateKpiValues,
} from "./logic/dataApiCall.js";
import {
  getThingsList,
  realTimeDataFunc,
  realTimeDrawerFunc,
  payloadDataFunc,
  payloadDataFuncForEnrg,
  updateEntityDetails,
} from "./logic/getThingListAndSocketCallings.js";
import {
  getDiagnosticParamValue,
  getActuationsParamValue,
  getFaultsData,
} from "./logic/processAnalyzerDataManipulation.js";
import { headerdataFunction } from "./logic/headerFunction.js";
import {
  calculateNearestFifteenMinuteMultiplier,
  isFifteenMinAggrView,
  getFifteenMinutesParameterGraphsConfig,
} from "./logic/fifteenMinutesGraphData.js";
import {
  getParameterGraphRender,
  detailedGraphDataFunc,
  paramGraphDataConfigFunc,
  paramTrendFunc,
} from "./logic/getGenericGraphConfigData.js";
import {
  shouldCameraVisible,
  getIpCameraShowIdFunction,
  getStreamLink,
} from "./logic/ipCameraLogic.js";
import { getEvents } from "./logic/eventsApiCallingFunction.js";
import CompressorDetailedView from "../../../DGMonitoring/Template-25/pages/DetailedView";
import { excelDownload } from "./logic/excelDownload";
import ViewMoreDrawer from "./components/ViewMoreDrawer";
import DownloadOutlined from "@ant-design/icons/DownloadOutlined.js";
import DownloadButton from "../../../../../../react-components/src/components/DownloadButton";

function disabledDate(current) {
  return current && current >= moment().endOf("day");
}

const datePickerConfig = {
  placeholder: ["From", "To"],
  size: "small",
  disabledDate: disabledDate,
  showTime: true,
  separator: " - ",
  format: "DD MMM YY, HH:mm",
};

const onlyEnrgTimePeriodKeys = [
  "last_24_hrs",
  "today",
  "yesterday",
  "last_7_days",
  "this_week",
  "last_week",
  "last_30_days",
  "this_month",
  "last_month",
  "last_3_months",
  "last_120_days",
  "custom",
];
const defaultTimePeriodKeys = [
  "last_24_hrs",
  "today",
  "yesterday",
  "last_7_days",
  "custom",
];

export default class GenericDetailedView extends React.Component {
  constructor(props) {
    super(props);
    this.paramGraphRef = React.createRef();
    this.rawGraphRef = React.createRef();
    this.reportControllerRef = React.createRef();
    this.parsed = queryString.parse(props.location.search);
    GraphObjectData.graph_data.config.timezone =
      props.user_preferences.timezone;
    this.state = {
      screenWidth: window.innerWidth,
      HeaderObject: HeaderObject.heaaderConfig,
      PanelObject: PanelObject.panelData,
      TripDataConfig: TripDataConfig.tripDataObject,
      ActivityObject: ActivityObject,
      SearchObjectData: SearchObjectData,
      graphSectionLoading: true,
      loading: true,
      GraphObjectData: GraphObjectData,
      AllParametersConfig: AllParametersConfig.allParamConfig,
      ParamGraphConfig: ParamGraphConfig,
      custom_range_open: false,
      enrgDetailDataLoading: true,
      param_search: "",
      selectedAllowedViz: JSON.parse(
        window.localStorage.getItem("selectedAllowedVisualization"),
      ),
    };

    this.listApiData = {
      client_id: props.client_id,
      application_id: props.application_id,
    };
    this.flagForTimeout = [];
    this.timer = null;
    this.bucketTime = null;
    this.bucket = {
      raw_data: {},
    };
    this.getDiagnosticParamValue = getDiagnosticParamValue.bind(this);
    this.getActuationsParamValue = getActuationsParamValue.bind(this);
    this.getFaultsData = getFaultsData.bind(this);
    this.getEnergyGraphData = getEnergyGraphData.bind(this);
    this.changeView = this.changeView.bind(this);
    this.switchClose = this.switchClose.bind(this);
    this.isPanelLocation = props.location.search.includes("panel");
    this.isListLocation = props.location.search.includes("list");
    this.isFilter = props.location.search.split("filter=")?.[1]?.split("&")[0];
    this.openNotification = openNotification.bind(this);
    this.doPTZControl = doPTZControl.bind(this);
    this.ipControl = ipControl.bind(this);
    this.isMobile = window.innerWidth <= 1024;
    this.getLifetimeAsPerParam = getLifetimeAsPerParam.bind(this);
    this.getLifetimeEnergy = getLifetimeEnergy.bind(this);
    this.getCo2EmissionValue = getCo2EmissionValue.bind(this);
    this.getTreeSavedValue = getTreeSavedValue.bind(this);
    this.thingGraphDataFunction = thingGraphDataFunction.bind(this);
    this.getEnergySummaryData = getEnergySummaryData.bind(this);
    this.getLastHourAqi = getLastHourAqi.bind(this);
    this.getThingsList = getThingsList.bind(this);
    this.headerdataFunction = headerdataFunction.bind(this);
    this.realTimeDataFunc = realTimeDataFunc.bind(this);
    this.realTimeDrawerFunc = realTimeDrawerFunc.bind(this);
    this.payloadDataFunc = payloadDataFunc.bind(this);
    this.payloadDataFuncForEnrg = payloadDataFuncForEnrg.bind(this);
    this.updateEntityDetails = updateEntityDetails.bind(this);
    this.calculateNearestFifteenMinuteMultiplier =
      calculateNearestFifteenMinuteMultiplier.bind(this);
    this.isFifteenMinAggrView = isFifteenMinAggrView.bind(this);
    this.getFifteenMinutesParameterGraphsConfig =
      getFifteenMinutesParameterGraphsConfig.bind(this);
    this.getParameterGraphRender = getParameterGraphRender.bind(this);
    this.detailedGraphDataFunc = detailedGraphDataFunc.bind(this);
    this.paramGraphDataConfigFunc = paramGraphDataConfigFunc.bind(this);
    this.paramTrendFunc = paramTrendFunc.bind(this);
    this.shouldCameraVisible = shouldCameraVisible.bind(this);
    this.getIpCameraShowIdFunction = getIpCameraShowIdFunction.bind(this);
    this.getStreamLink = getStreamLink.bind(this);
    this.getEvents = getEvents.bind(this);
    this.excelDownload = excelDownload.bind(this);
    this.calculateKpiValues = calculateKpiValues.bind(this);

    this.onlyRawParams = ["dr_st", "soc"]
  }

  windowResize() {
    this.setState({ screenWidth: window.innerWidth });
  }

  async getServiceAlert() {
    let data = await serviceAlertFetch(this.props.client_id);
    this.setState({
      service_alert_fetch: data,
    });
  }

  getMaintenanceText() {
    let text = maintenanceTextGen(
      this.state.service_alert_fetch,
      this.state.thingId,
    );
    return text;
  }
  offlineTimeOutFunction(
    payload = undefined,
    offlineArray,
    totalData,
    lastdata,
    response,
  ) {
    let latestDataArray = [],
      latestData = {},
      onOffStatus = {};
    response.all_thing_ids.map((things) => {
      let sortedThingsdata = _sortBy(lastdata, ["time"]);
      let filteredLatestData = _filter(sortedThingsdata, {
        thing_id: things,
      });
      let findFromTotalThing = _find(totalData?.things, { id: things });
      if (!latestData[things]) {
        latestData[things] = filteredLatestData[filteredLatestData.length - 1];
      }
      latestData[things]["category"] = findFromTotalThing.category;
      latestData[things]["site_id"] = findFromTotalThing.site_id;
      let isMcSt =
        findFromTotalThing &&
        findFromTotalThing.parameters &&
        _find(findFromTotalThing.parameters, { key: "mc_st" })
          ? latestData[things].data["mc_st"]
          : findFromTotalThing?.status === "offline"
            ? "2"
            : "1";
      if (!onOffStatus[things]) {
        onOffStatus[things] = isMcSt;
      }
      let findThings = _find(response.things_list, { id: things });
      if (findThings) {
        onOffStatus[things] = parseInt(isMcSt)
          ? parseInt(isMcSt).toString()
          : "0";
        latestDataArray.push(latestData[things]);
        let findindex = _findIndex(latestDataArray, {
          thing_id: things,
        });
        if (findindex > -1) {
          latestDataArray[findindex]["on_off_moving_status"] =
            this.checkOfflineOnlineStatus(
              offlineArray,
              onOffStatus[things],
              things,
            );
        }
      }
    });
    ipLatestData(totalData, latestDataArray, payload, this.props.client_id);
    return latestDataArray;
  }

  checkOfflineOnlineStatus(offlineArray, onOffStatus, thing_id) {
    let offlineStatus = _find(offlineArray, {
      thing_id, //: parseInt(this.state.selected_id),
    })?.status;
    return offlineStatus === "offline" ? "2" : onOffStatus;
  }

  switchClose() {
    let prevPage = this.isPanelLocation
      ? "panel-view?view=panel"
      : this.isListLocation
        ? "list-view"
        : "map-view";
    this.props.history.push(
      getBaseUrl(this.props, prevPage, true) +
        (this.isFilter && this.isFilter.length
          ? "?filter=" + this.isFilter
          : ""),
    );
  }

  allowedVizChanged(e) {
    this.setState(
      {
        selectedAllowedViz: e,
        rawGraphLoading: true,
      },
      () => {
        this.thingGraphDataFunction();
      },
    );
  }

  getUrlValue(string, value, operator) {
    const startIndex =
      string.indexOf(value + operator) + (value + operator).length;
    let output = null;
    if (string.indexOf(value + operator) !== -1) {
      const endIndex = string.indexOf("&", startIndex);
      output =
        endIndex === -1
          ? string.slice(startIndex)
          : string.slice(startIndex, endIndex);
    }
    return output;
  }

  async getAlerts() {
    const { thingId } = this.state;
    const { client_id, application_id } = this.props;
    let alertData = await retriveAlerts(client_id, application_id, thingId);
    this.setState({
      alert_data: alertData,
    });
  }

  async updateAddress() {
    let selectedThinglatestParam = this.latestDataFunc();
    let findThingList = _find(this.state.totalData.things, {
      id: parseInt(this.state.thingId),
    });
    let address = await fetchAddress(
      getAddressFromLat(selectedThinglatestParam, findThingList),
    );
    this.setState({ currentAddress: address });
  }

  getOnlineOfflineArray(totalData) {
    let offlineArray = [];
    if (totalData && totalData.things && totalData.things.length) {
      totalData.things.map((thing) => {
        offlineArray.push({
          thing_id: thing.id,
          status: thing.status,
        });
      });
    }
    return offlineArray;
  }

  solarInverterStatusParams = [];
  solarPumpStatusParams = [];
  findParam(findThingCat, findThing, findApplicationThings) {
    this.solarInverterStatusParams = [];
    this.solarPumpStatusParams = [];

    let sortedParam = paramAsPerOrder(
      findApplicationThings,
      findThing?.parameters,
    );
    let thingAllParams = [];
    if (sortedParam.length) {
      sortedParam.forEach((params) => {
        // Solar Inverter (id: 93) specific params
        if (findThingCat?.id === 93) {
          if (SolarInverterStatusKeys.includes(params.key)) {
            this.solarInverterStatusParams.push({
              key: params.key,
              name: params.name,
            });
            return;
          }
          if (
            ["calculated_dcl_energy", "enrg_d", "calculated_energy"].includes(
              params.key,
            )
          ) {
            return;
          }
        }

        // Solar Plant (id: 91) specific params
        if (
          findThingCat?.id === 91 &&
          [
            "today_enrg",
            "trees_planted",
            "co2_emissions_saved",
            "solar_util",
          ].includes(params.key)
        ) {
          return;
        }

        // Solar Pump (id: 103) specific params
        if (findThingCat?.id === 103) {
          if (params.key === "calculated_runhour") {
            thingAllParams.push(params.key);
            return;
          }
          if (solarPumpStatusParamsKeys.includes(params.key)) {
            this.solarPumpStatusParams.push(params.key);
            return;
          }
          if (
            params.allowed_visualizations?.length === 0 ||
            (params.type === "energy" && params.key !== "enrg")
          ) {
            return;
          }
        }

        // Common params to exclude
        const excludedCalculatedParams = [
          "calculated_mains_energy",
          "calculated_energy",
          "calculated_ma_energy",
          "calculated_mr_energy",
          "calculated_mt_energy_ex",
          "calculated_ga_energy",
          "calculated_gr_energy",
          "data_availability",
          "calculated_runhour",
          "dr_op_cnt",
          "ch_cyc_cnt",
          "calculated_c_enrg",
          "calculated_d_enrg",
          "batt_util",
          "eff_curr"
        ];

        const excludedParamTypes = [
          "latitude",
          "longitude",
          "status",
          "time",
          "error",
          "fault",
        ];

        if (
          !excludedCalculatedParams.includes(params.key) &&
          (!excludedParamTypes.includes(params?.type) || params.key === "dr_st")
        ) {
          thingAllParams.push(params.key);
        }
      });
    }
    let resultArray = [];
    let paramArray = thingAllParams;
    if (
      findThingCat?.pages?.detailed_view?.parameter_trend?.parameters?.length &&
      !findThingCat?.pages?.detailed_view?.parameter_trend
        ?.include_other_parameters
    ) {
      paramArray = [
        ..._map(
          findThingCat.pages.detailed_view.parameter_trend.parameters,
          "key",
        ),
      ];
    }

    findThing?.parameters?.forEach((element) => {
      if (paramArray.includes(element.key)) {
        resultArray.push(element.key);
      }
    });
    console.log("resultArray: ", resultArray);
    return resultArray;
  }

  viewMoreClicked(e, title) {
    this.setState({
      viewMoreData: e,
      viewMoreDrawerVisible: true,
      viewMoreDrawerTitle: title,
    });
  }

  viewMoreCloseClicked() {
    this.setState({ viewMoreDrawerVisible: false, viewMoreData: [] });
  }

  timeAndParamHistoryPush() {
    const { dg_in_iot_mode, history } = this.props;
    const {
      thingId,
      getSelectedParamKey,
      selected_param_date_range,
      param_graph_from_time,
      param_graph_upto_time,
    } = this.state;
    if (!dg_in_iot_mode || 1) {
      history.push(
        getBaseUrl(this.props, "detailed-view", true) +
          "?from=" +
          (this.isPanelLocation
            ? "panel"
            : this.isListLocation
              ? "list"
              : "map") +
          (this.isFilter && this.isFilter.length
            ? "?filter=" + this.isFilter
            : "") +
          "&thing_id=" +
          thingId +
          "&selected_param=" +
          getSelectedParamKey +
          "&time_duration=" +
          selected_param_date_range +
          (selected_param_date_range === "custom"
            ? "&from_time=" +
              param_graph_from_time +
              "&upto_time=" +
              param_graph_upto_time
            : ""),
      );
    }
  }

  cumulativeParamsMap = {
    enrg: ["calculated_energy"],
    mt_energy: ["calculated_mains_energy"],
    ma_energy: ["calculated_ma_energy"],
    mr_energy: ["calculated_mr_energy"],
    ga_energy: ["calculated_ga_energy"],
    gr_energy: ["calculated_gr_energy"],
    mt_energy_ex: ["calculated_mt_energy_ex"],
    c_enrg: ["calculated_c_enrg"],
    d_enrg: ["calculated_d_enrg"],
    dcl_enrg: ["calculated_dcl_energy"],
    calculated_runhour: ["calculated_runhour"],
    on_time_hrs_asp_1: ["calculated_rnhr_asp_1"],
    on_time_hrs_asp_2: ["calculated_rnhr_asp_2"],
  };

  isParamCumulative(paramKey) {
    return {
      isCumulative: Object.keys(this.cumulativeParamsMap).includes(paramKey),
      aggrKey: this.cumulativeParamsMap[paramKey] ? [...this.cumulativeParamsMap[paramKey]] : [
        "calculated_mains_energy",
      ],
    };
  }

  isSolarSystem() {
    return (
      _find(this.state.totalData.things, {
        id: parseInt(this.state.thingId),
      })?.category === 91
    );
  }

  latestDataFunc() {
    let selectedThinglatestParam = _find(this.state.latestParameterData, {
      thing_id: parseInt(this.state.thingId),
    });
    if (
      selectedThinglatestParam &&
      Object.keys(selectedThinglatestParam?.data)
    ) {
      Object.keys(selectedThinglatestParam.data).map((key) => {
        if (selectedThinglatestParam.data[key]) {
          selectedThinglatestParam.data[key] =
            selectedThinglatestParam.data[key].toString();
        }
      });
    }
    return selectedThinglatestParam;
  }

  componentDidUpdate(prevProps) {
    if (
      prevProps.reloadDetailedView !== this.props.reloadDetailedView &&
      this.props.reloadDetailedView
    ) {
      this.setState(
        {
          loading: true,
          enrgDetailDataLoading: true,
        },
        async () => {
          await this.getThingsList(this.listApiData);
          await Promise.all([
            this.getEvents(),
            this.getServiceAlert(),
            this.getAlerts(),
            this.updateAddress(),
            this.getLastHourAqi(),
            this.thingGraphDataFunction(),
            this.getEnergySummaryData(),
          ]);
        },
      );
    }
  }

  async componentDidMount() {
    window.addEventListener("resize", this.windowResize.bind(this));
    await this.getThingsList(this.listApiData);
    await Promise.all([
      this.getEvents(),
      this.getServiceAlert(),
      this.getAlerts(),
      this.updateAddress(),
      this.getLastHourAqi(),
      this.thingGraphDataFunction(),
      this.getEnergySummaryData(),
    ]);
    if (import.meta.env.VITE_DESKTOP) {
      this.updateInterval = setInterval(async () => {
        await this.getThingsList(this.listApiData);
        await Promise.all([
          this.thingGraphDataFunction(),
          this.getEnergySummaryData(),
        ]);
      }, 3000);
    }
  }
  componentWillUnmount() {
    window.removeEventListener("resize", this.windowResize.bind(this));
    disconnectSocketConnection(this.socket);
    clearInterval(this.bucketTime);
    if (import.meta.env.VITE_DESKTOP) {
      clearInterval(this.updateInterval);
    }
  }

  thingsDrawerClick(e) {
    let findThing = _find(this.state.totalData.things, {
      id: parseInt(e),
    });
    let findThingCat = this.state.totalData.things_categories?.find(
      (cat) => cat.id === findThing.category,
    );
    let findApplicationThings = _find(
      this.state.applicationThings?.things_categories,
      { id: findThing.category },
    );
    let paramArray =
      findThingCat.id === 71
        ? _find(findThing.parameters, { key: "fuel_litre" })
          ? ["fuel_litre"]
          : ["fuel"]
        : this.findParam(findThingCat, findThing, findApplicationThings);
    let selectedParamDateRange = "",
      fromDate = "",
      uptoDate = "",
      dataType = "",
      isRawVisualization = "",
      dataPeriod = 0,
      allowed_visualizations = true,
      selected_param = "";
    let string = this.props?.location?.search;
    let paramSelect = this.getUrlValue(string, "selected_param", "=");
    if (findThingCat.id === 71) {
      selected_param = _find(findThing.parameters, { key: "fuel_litre" })
        ? "fuel_litre"
        : "fuel";
    } else {
      if (paramSelect !== null) {
        selected_param = paramSelect;
      }
    }
    let selectedParam = paramArray.includes(selected_param)
      ? selected_param
      : paramArray?.[0]
        ? paramArray[0]
        : undefined;
    if (paramArray && paramArray.length) {
      [
        selectedParamDateRange,
        fromDate,
        uptoDate,
        dataType,
        isRawVisualization,
        dataPeriod,
        allowed_visualizations,
      ] = this.getParameterGraphRender(findThing, selectedParam);
    }
    this.setState(
      {
        thingId: e,
        loading: true,
        enrgDetailDataLoading: true,
        is_raw_visualization: isRawVisualization,
        selected_param_date_range: selectedParamDateRange,
        is_yesterday: selectedParamDateRange === "yesterday",
        param_graph_from_time: fromDate,
        param_graph_upto_time: uptoDate,
        data_type: dataType,
        data_period: dataPeriod,
        allowed_visualizations: allowed_visualizations,
        drawerVisible: false,
        graphSectionLoading: true,
        totalParamToShow: paramArray,
        getSelectedParamKey: selectedParam,
        selectedParam: paramArray.includes(selected_param)
          ? [selected_param]
          : [paramArray?.[0]]
            ? [paramArray[0]]
            : undefined,
      },
      async () => {
        this.timeAndParamHistoryPush();
        await Promise.all([
          this.getAlerts(),
          this.getEvents(),
          this.getServiceAlert(),
          this.updateAddress(),
          this.getLastHourAqi(),
          this.thingGraphDataFunction(),
          this.getEnergySummaryData(),
        ]);
      },
    );
  }
  showDrawer() {
    this.setState(
      {
        drawerVisible: true,
      },
      () => {
        mixPanelTrackUser("Asset List Drawer");
      },
    );
  }
  closeDrawer() {
    this.setState({
      drawerVisible: false,
    });
  }
  selectParam(e) {
    let findThing = _find(this.state.totalData.things, {
      id: parseInt(this.state.thingId),
    });
    const { mobile_view_get } = this.props;
    const [
      selectedParamDateRange,
      fromDate,
      uptoDate,
      dataType,
      isRawVisualization,
      dataPeriod,
      allowed_visualizations,
    ] = this.getParameterGraphRender(findThing, e);
    const getParam =
      findThing.category === 71
        ? _find(findThing.parameters, { key: "fuel_litre" })
          ? "fuel_litre"
          : "fuel"
        : e;
    this.setState(
      {
        is_raw_visualization: isRawVisualization,
        selected_param_date_range: selectedParamDateRange,
        is_yesterday: selectedParamDateRange === "yesterday",
        param_graph_from_time: fromDate,
        param_graph_upto_time: uptoDate,
        data_type: dataType,
        data_period: dataPeriod,
        allowed_visualizations: allowed_visualizations,
        getSelectedParamKey: getParam,
        selectedParam: [getParam],
        graphSectionLoading: true,
        enrgDetailDataLoading: true,
      },
      async () => {
        if (!mobile_view_get) {
          this.timeAndParamHistoryPush();
        }
        Promise.all([
          this.thingGraphDataFunction(),
          this.getEnergySummaryData(),
        ]);
      },
    );
  }

  customBackBtnClicked() {
    this.setState(
      {
        selected_param_date_range: "last_24_hrs",
        param_graph_from_time: moment().subtract(1, "day").unix(),
        param_graph_upto_time: moment().unix(),
        data_period: 0,
        data_type: "raw",
        graphSectionLoading: true,
      },
      () => {
        if (!this.props.mobile_view_get) {
          this.timeAndParamHistoryPush();
        }
        this.thingGraphDataFunction();
      },
    );
  }

  paramRangeChange(value) {
    const { mobile_view_get } = this.props;
    const {
      is_raw_visualization,
      param_graph_from_time,
      param_graph_upto_time,
      data_type,
      data_period,
    } = this.state;
    let fromTime = "",
      uptoTime = "",
      dataType = "",
      dataPeriod = 0,
      isYesterDay = false;
    const timeObj = timeDurationMapping()[value];
    if (value === "custom") {
      fromTime = param_graph_from_time;
      uptoTime = param_graph_upto_time;
      dataPeriod = data_period;
      dataType = data_type;
      isYesterDay = true;
    } else {
      if (value === "yesterday") {
        isYesterDay = true;
      }
      if (timeObj) {
        if (is_raw_visualization) {
          fromTime = timeObj.fromTime;
          uptoTime = timeObj.uptoTime;
          dataType = timeObj.dataType;
          dataPeriod = timeObj.dataPeriod;
        } else {
          fromTime = timeObj.fromTime;
          uptoTime = timeObj.uptoTime;
          dataType = "sum";
          dataPeriod = timeObj.cumulativePeriod || timeObj.dataPeriod;
        }
      }
    }
    this.setState(
      {
        selected_param_date_range: value,
        param_graph_from_time: fromTime,
        param_graph_upto_time: uptoTime,
        data_period: dataPeriod,
        data_type: dataType,
        graphSectionLoading: value === "custom" ? false : true,
        is_yesterday: isYesterDay,
        is_non_upgradable: false,
      },
      () => {
        if (!mobile_view_get) {
          this.timeAndParamHistoryPush();
        }
        this.thingGraphDataFunction();
      },
    );
  }

  togglePicker() {
    this.setState({ custom_range_open: true });
  }

  onSearch(e) {
    this.setState({
      param_search: e,
    });
  }

  onOk(value, mobile_range) {
    const { allowed_visualizations } = this.state;
    const { mobile_view_get } = this.props;
    let startValue = mobile_range ? value[0] : moment(value[0]).unix();
    let endValue = mobile_range ? value[1] : moment(value[1]).unix();
    let dataType = "raw";
    let dataPeriod = 0;
    if (allowed_visualizations) {
      if (
        endValue - startValue > 3 * 86400 &&
        endValue - startValue < 8 * 86400
      ) {
        dataType = "avg";
        dataPeriod = 3600;
      } else if (endValue - startValue > 8 * 86400) {
        dataType = "avg";
        dataPeriod = 86400;
      } else {
        dataType = "raw";
        dataPeriod = 0;
      }
    } else {
      dataType = "sum";
      if (endValue - startValue > 8 * 86400) {
        dataPeriod = 86400;
      } else {
        dataPeriod = 3600;
      }
    }
    this.setState(
      {
        param_graph_from_time: startValue,
        param_graph_upto_time: endValue,
        data_type: dataType,
        data_period: dataPeriod,
        graphSectionLoading: true,
        is_non_upgradable: true,
      },
      () => {
        if (!mobile_view_get) {
          this.timeAndParamHistoryPush();
        }
        this.thingGraphDataFunction();
      },
    );
  }

  changeView() {
    this.props.history.push(
      getBaseUrl(this.props, "real-time", true) +
        "?from=" +
        (this.isPanelLocation
          ? "panel"
          : this.isListLocation
            ? "list"
            : "map") +
        (this.isFilter && this.isFilter.length
          ? "?filter=" + this.isFilter
          : "") +
        "&thing_id=" +
        this.state.thingId,
    );
  }

  onOpenChange() {}

  getYAxisMinMax(plotLines, values) {
    let maxYValue = undefined;
    let minYValue = undefined;
    if (values?.length) {
      if (plotLines?.[0]) {
        maxYValue = Math.max(
          ...values.filter((val) => val !== null),
          parseInt(plotLines[0].value) + 1,
        );
      } else {
        maxYValue = Math.max(...values);
      }
    }

    if (values?.length) {
      if (plotLines?.[1]) {
        minYValue = Math.min(...values, plotLines[1].value - 1);
      } else {
        minYValue = Math.min(...values);
      }
    }
    return {
      minYValue: minYValue,
      maxYValue: maxYValue,
    };
  }

  getStatus(getCat) {
    let findLastData = _find(this.state.latestParameterData, {
      thing_id: parseInt(this.state.thingId),
    });
    return getStatusIcon(
      getCat.id,
      parseInt(findLastData?.data?.mc_st) === 1 ? "on" : "off",
    );
  }

  findParamRanges() {
    const paramRanges = [];
    const findThing = _find(this.state.totalData.things, {
      id: parseInt(this.state.thingId),
    });
    const rangeKeys = [78, 79, 101, 91, 94, 99, 93].includes(
      findThing?.category,
    )
      ? onlyEnrgTimePeriodKeys
      : defaultTimePeriodKeys;
    rangeKeys.map((range) => {
      paramRanges.push({
        value: range,
        title: timeDurationMapping()[range]?.title,
      });
    });
    return paramRanges;
  }

  findListAndCat(totalData, thingId) {
    const findThing = _find(totalData.things, {
      id: parseInt(thingId),
    });
    const findThingCat = totalData.things_categories?.find(
      (cat) => cat.id === findThing?.category,
    );
    return { findThing, findThingCat };
  }

  findSelectedParamInCategory() {
    const { totalData, thingId, getSelectedParamKey } = this.state;
    const findThingCat = this.findListAndCat(totalData, thingId).findThingCat;
    return _find(
      findThingCat?.pages?.detailed_view?.parameter_trend?.parameters,
      { key: getSelectedParamKey },
    );
  }

  formatRunhourValue(value) {
    const hours = Math.floor(value);
    const minutes = Math.round((value - hours) * 60);
    const formattedTime = `${hours.toString().padStart(2, "0")} : ${minutes.toString().padStart(2, "0")} Hrs`;
    return formattedTime;
  }

  getRealTimeValuesInGraphHeading(
    selectedThinglatestParam,
    selectedParamUnit,
    selectedThingParameters,
  ) {
    const { getSelectedParamKey } = this.state;
    const paramValue = this.findSelectedParamInCategory()?.noRealTimeValue
      ? ""
      : selectedThinglatestParam?.data &&
          selectedThinglatestParam.data?.[getSelectedParamKey] &&
          !isNaN(parseFloat(selectedThinglatestParam.data[getSelectedParamKey]))
        ? getSelectedParamKey === "dr_st"
          ? parseFloat(selectedThinglatestParam.data[getSelectedParamKey]) === 1
            ? "Open"
            : parseFloat(selectedThinglatestParam.data[getSelectedParamKey]) ===
                0
              ? "Closed"
              : "-"
          : parseFloat(
              selectedThinglatestParam.data[getSelectedParamKey],
            ).toFixed(2) +
            " " +
            (this.isMobile ? selectedParamUnit : "")
        : "NA";
    const valueColor =
      getSelectedParamKey === "dr_st" &&
      parseFloat(selectedThinglatestParam.data[getSelectedParamKey]) === 1
        ? "red"
        : undefined;
    const clubbedParams = this.findSelectedParamInCategory()?.clubbedParams,
      clubbedParamValuesForDownload = [],
      clubbedParamRender = [],
      clubbedParamName = ["L1", "L2", "L3"];
    if (clubbedParams?.length) {
      clubbedParams.map((param, ind) => {
        if (!_find(selectedThingParameters, { key: param })) {
          return;
        }
        const clubbedParamValue = !isNaN(
          parseFloat(selectedThinglatestParam.data[param]),
        )
          ? parseFloat(selectedThinglatestParam.data[param]).toFixed(2)
          : "NA";
        clubbedParamValuesForDownload.push({
          name: clubbedParamName[ind],
          key: param,
          value: clubbedParamValue,
        });
        clubbedParamRender.push(
          <span>
            <span className="name">{clubbedParamName[ind]}</span>
            {`${clubbedParamValue} ${this.isMobile ? selectedParamUnit : ""}`}
          </span>,
        );
      });
    }

    if (getSelectedParamKey === "soc" && this.state.ch_cyc_cnt) {
      clubbedParamRender.push(
        <span>
          <span className="name">{this.state.ch_cyc_cnt.title}</span>
          {` ${this.state.ch_cyc_cnt.value}`}
        </span>,
      );
    }
    return {
      paramValue,
      clubbedParams: clubbedParamValuesForDownload,
      element: (
        <div className="real-time-header-param">
          <span style={{ color: valueColor }}>{paramValue}</span>
          {clubbedParamRender?.length ? (
            <div className="clubbed-values">{clubbedParamRender}</div>
          ) : (
            ""
          )}
        </div>
      ),
    };
  }

  render() {
    const {
      totalData,
      selected_param_date_range,
      is_raw_visualization,
      enrgSummaryObj,
      clubbedParamArr,
    } = this.state;
    const { mobile_view_get, mobile_selected_param } = this.props;
    let selectedThinglatestParam = this.latestDataFunc();
    let pageRender = "",
      getDrawer = "";
    const diffTimeGreaterThan7Days =
      parseInt(this.state.param_graph_upto_time) -
        parseInt(this.state.param_graph_from_time) >
      8 * 86400;
    if (this.state.loading) {
      // pageRender = (
      //   <Loading
      //     show_logo={this.props.loading_logo}
      //     className="align-center-loading"
      //   />
      // );
    } else if (
      !totalData.things ||
      (totalData.things && totalData.things.length === 0)
    ) {
      pageRender = <div className="no-data-text">No Asset Found</div>;
    } else {
      let findThingAvailibility = _find(totalData.things, {
        id: parseInt(this.state.thingId),
      });

      let parametersNameArray = [],
        graphRender = "";
      if (findThingAvailibility) {
        let findThingCat = totalData.things_categories?.find(
          (cat) => cat.id === findThingAvailibility.category,
        );
        let paramDetails = this.paramTrendFunc();
        let paramRanges = this.findParamRanges();
        let mobileParamSelectOptions = [];
        if (paramDetails) {
          if (Array.isArray(paramDetails.paramArray)) {
            paramDetails.paramArray.map((paramArrayData) => {
              if (paramArrayData?.name) {
                mobileParamSelectOptions.push(
                  <AntOption value={paramArrayData.key}>
                    {findThingAvailibility.category === 71
                      ? "Fuel Level (L)"
                      : (aurassureThingCat(
                          findThingAvailibility?.category,
                          this.props.vendor_id,
                        )
                          ? paramArrayData.name
                          : ParamName(
                              paramArrayData.key,
                              paramDetails.paramArray,
                              findThingAvailibility.category,
                            )) +
                        (paramArrayData.unit
                          ? " (" +
                            (aurassureThingCat(
                              findThingAvailibility?.category,
                              this.props.vendor_id,
                            )
                              ? paramArrayData.unit
                              : ParamUnit(paramArrayData.unit)) +
                            ")"
                          : "")}
                  </AntOption>,
                );
                if (
                  ParamName(
                    paramArrayData.key,
                    paramDetails.paramArray,
                    findThingAvailibility.category,
                  )
                    .toLowerCase()
                    .includes(this.state.param_search.toLowerCase())
                ) {
                  let upperThreshold, lowerThreshold;
                  if (paramArrayData.key === "ph") {
                    upperThreshold = paramArrayData.threshold?.length
                      ? paramArrayData.threshold.split("-")?.[1]
                      : undefined;
                    lowerThreshold = paramArrayData.threshold?.length
                      ? paramArrayData.threshold.split("-")?.[0]
                      : undefined;
                  } else {
                    upperThreshold = paramArrayData.threshold;
                  }
                  parametersNameArray.push(
                    <AntTooltip
                      placement="topLeft"
                      title={paramArrayData.tooltipDetails}
                    >
                      <div
                        className={
                          "param-name " +
                          (this.state.getSelectedParamKey === paramArrayData.key
                            ? "selected"
                            : "")
                        }
                        onClick={() => {
                          this.selectParam(paramArrayData.key);
                        }}
                      >
                        <span>
                          {findThingAvailibility.category === 71
                            ? "Fuel Level (L)"
                            : (aurassureThingCat(
                                findThingAvailibility?.category,
                                this.props.vendor_id,
                              )
                                ? paramArrayData.name
                                : ParamName(
                                    paramArrayData.key,
                                    paramDetails.paramArray,
                                    findThingAvailibility.category,
                                  )) +
                              (paramArrayData.unit?.length
                                ? " (" +
                                  (aurassureThingCat(
                                    findThingAvailibility?.category,
                                    this.props.vendor_id,
                                  )
                                    ? paramArrayData.unit
                                    : ParamUnit(paramArrayData.unit)) +
                                  ")"
                                : "")}
                          {paramArrayData.threshold ? (
                            <div className="threshold">
                              {"Threshold: " + paramArrayData.threshold}
                            </div>
                          ) : (
                            ""
                          )}
                        </span>
                        <span
                          className={
                            "gdv-param-value" +
                            (parseFloat(
                              selectedThinglatestParam?.data?.[
                                paramArrayData.key
                              ],
                            ) > parseFloat(upperThreshold) ||
                            parseFloat(
                              selectedThinglatestParam?.data?.[
                                paramArrayData.key
                              ],
                            ) < parseFloat(lowerThreshold)
                              ? " danger"
                              : "")
                          }
                          style={{
                            color:
                              paramArrayData.key === "dr_st" &&
                              parseInt(
                                selectedThinglatestParam.data[
                                  paramArrayData.key
                                ],
                              ) === 1
                                ? "red"
                                : undefined,
                          }}
                        >
                          {_find(
                            findThingCat?.pages?.detailed_view?.parameter_trend
                              ?.parameters,
                            { key: paramArrayData.key },
                          )?.noRealTimeValue
                            ? ""
                            : selectedThinglatestParam?.data &&
                                selectedThinglatestParam.data?.[
                                  paramArrayData.key
                                ] &&
                                !isNaN(
                                  parseFloat(
                                    selectedThinglatestParam.data[
                                      paramArrayData.key
                                    ],
                                  ),
                                )
                              ? paramArrayData.key === "dr_st"
                                ? parseInt(
                                    selectedThinglatestParam.data[
                                      paramArrayData.key
                                    ],
                                  ) === 1
                                  ? "Open"
                                  : "Closed"
                                : parseFloat(
                                    selectedThinglatestParam.data[
                                      paramArrayData.key
                                    ],
                                  ).toFixed(2)
                              : "NA"}
                        </span>
                      </div>
                    </AntTooltip>,
                  );
                }
              }
            });
          }
          if (window.innerWidth <= 1024) {
            parametersNameArray = mobile_selected_param ? (
              ""
            ) : (
              <AntSelect
                value={this.state.getSelectedParamKey}
                onChange={(e) => this.selectParam(e)}
              >
                {mobileParamSelectOptions}
              </AntSelect>
            );
          }
          graphRender = (
            <GraphHighcharts
              graphData={paramDetails?.graph_config.graph_data}
            />
          );
        }
        let paramSelectionSection = "";
        paramSelectionSection = mobile_selected_param ? (
          ""
        ) : (
          <div className="param-select-container">
            <div className="heading">
              {this.isMobile
                ? "Select Parameter"
                : this.props.t
                  ? this.props.t("parameters")
                  : "Parameters"}
              <div className="sub-text">
                {this.props.t
                  ? this.props.t("parameters_and_real_values_shown")
                  : "Parameters and their real-time values are shown below. Select any parameter to view trends"}
                {/* Parameters and their real-time values are shown below. Select
                any parameter to view trends */}
              </div>
            </div>
            {window.innerWidth > 576 ? (
              <SearchInput
                t={this.props.t}
                onSearch={(e) => this.onSearch(e)}
              />
            ) : (
              ""
            )}
            <div
              className="param-name-container"
              style={{ marginTop: this.isMobile ? 24 : 0 }}
            >
              {parametersNameArray}
            </div>
          </div>
        );

        let paramDateSelection = (
          <SelectTimePeriodWithCustom
            from_time={this.state.param_graph_from_time}
            upto_time={this.state.param_graph_upto_time}
            togglePicker={() => this.togglePicker()}
            onOk={(e, a) => this.onOk(e, a)}
            customBackBtnClicked={() => {
              this.customBackBtnClicked();
            }}
            onOpenChange={(e) => this.onOpenChange(e)}
            is_raw_visualization={this.state.is_raw_visualization}
            selected_param_date_range={selected_param_date_range}
            paramRangeChange={(e) => this.paramRangeChange(e)}
            paramRanges={paramRanges}
            numberOfOptionstoShow={5}
          />
        );
        const selectedParamUnit = ParamUnit(
          _find(paramDetails.paramArray, {
            key: this.state.getSelectedParamKey,
          })?.unit,
        );
        let graphHeader = "";
        if (is_raw_visualization && !this.isMobile) {
          const findParamNameFromThingCat = _find(
            findThingCat?.pages?.detailed_view?.parameter_trend?.parameters,
            { key: this.state.getSelectedParamKey },
          );
          graphHeader = (
            <div className="total-heading-download">
              <div className="graph-heading">
                {this.isMobile
                  ? ""
                  : (findThingAvailibility.category === 71
                      ? "Fuel Level"
                      : findParamNameFromThingCat?.name
                        ? findParamNameFromThingCat.name
                        : ParamName(
                            this.state.getSelectedParamKey,
                            findThingAvailibility.parameters,
                            findThingAvailibility.category,
                          )) +
                    (_find(paramDetails.paramArray, {
                      key: this.state.getSelectedParamKey,
                    })
                      ? selectedParamUnit.length
                        ? ` (${selectedParamUnit})`
                        : ""
                      : "")}
                {
                  this.getRealTimeValuesInGraphHeading(
                    selectedThinglatestParam,
                    selectedParamUnit,
                    findThingAvailibility.parameters,
                  ).element
                }
              </div>
              <DownloadButton
                content={<DownloadOutlined style={{ fontSize: "15px" }} />}
                onClickFunc={() => {
                  this.excelDownload({
                    thingName: findThingAvailibility.name,
                    parameter: {
                      key: this.state.getSelectedParamKey,
                      name: ParamName(
                        this.state.getSelectedParamKey,
                        findThingAvailibility.parameters,
                        findThingAvailibility.category,
                      ),
                      unit: selectedParamUnit,
                      realTimeValue: ["enrg", "c_enrg", "d_enrg"].includes(
                        this.state.getSelectedParamKey,
                      )
                        ? undefined
                        : this.getRealTimeValuesInGraphHeading(
                            selectedThinglatestParam,
                            selectedParamUnit,
                            findThingAvailibility.parameters,
                          ),
                      data: isCumulative
                        ? this.getEnergyGraphData().graphData?.graph_data
                        : paramDetails?.graph_config.graph_data,
                    },
                    dataPeriod: this.state.data_period,
                    fromTime: this.state.param_graph_from_time,
                    uptoTime: this.state.param_graph_upto_time,
                  });
                }}
                type={""}
              />
            </div>
          );
        }
        const isCumulative = this.isParamCumulative(
          this.state.getSelectedParamKey,
        )?.isCumulative;
        console.log(
          "isCumulative",
          isCumulative,
          this.state.getSelectedParamKey,
        );
        let graphSection = "";
        if (this.state.selectedParam) {
          graphSection = this.isFifteenMinAggrView(findThingAvailibility) ? (
            <div className="graph-section-container">
              <FifteenMinAggrDetailedView
                t={this.props.t}
                mobile_view_get={mobile_view_get}
                param_graph_from_time={this.state.param_graph_from_time}
                param_graph_upto_time={this.state.param_graph_upto_time}
                datePickerConfig={datePickerConfig}
                selected_param_date_range={selected_param_date_range}
                disabledDate={disabledDate}
                onOk={(e, status) => this.onOk(e, status)}
                onOpenChange={(e) => this.onOpenChange(e)}
                togglePicker={() => this.togglePicker()}
                paramRangeChange={(e) => this.paramRangeChange(e)}
                GraphImg={GraphImg}
                minGraphImg={minGraphImg}
                maxGraphImg={maxGraphImg}
                paramDetails={paramDetails}
                graphSectionLoading={this.state.graphSectionLoading}
                data={this.getFifteenMinutesParameterGraphsConfig(paramDetails)}
                is_raw_visualization={is_raw_visualization}
                findThingAvailibility={findThingAvailibility}
                selectedThinglatestParam={selectedThinglatestParam}
                getSelectedParamKey={this.state.getSelectedParamKey}
                paramGraphRef={this.paramGraphRef}
                rawGraphRef={this.rawGraphRef}
                customBackBtnClicked={() => this.customBackBtnClicked()}
              />
            </div>
          ) : (
            <div className="graph-section-container">
              {graphHeader}
              {isCumulative ? "" : paramDateSelection}
              {isCumulative ? (
                <OnlyEnergyGraph
                  client_id={this.props.client_id}
                  application_id={this.props.application_id}
                  thingId={this.state.thingId}
                  selectedparamUnit={selectedParamUnit}
                  enrgDetailDataLoading={this.state.enrgDetailDataLoading}
                  {...enrgSummaryObj}
                  summaryParams={
                    this.findSelectedParamInCategory()?.summaryParams
                  }
                  graphSectionLoading={this.state.graphSectionLoading}
                  GraphImg={GraphImg}
                  minGraphImg={minGraphImg}
                  maxGraphImg={maxGraphImg}
                  data_period={this.state.data_period}
                  data={this.getEnergyGraphData()}
                  paramDateSelection={paramDateSelection}
                />
              ) : (
                <>
                  {this.state.graphSectionLoading ? (
                    <AntSpin className="align-center-loading" />
                  ) : this.onlyRawParams.includes(
                      this.state.getSelectedParamKey,
                    ) && diffTimeGreaterThan7Days ? (
                    <div
                      style={{
                        height: "200px",
                        display: "flex",
                        "justify-content": "center",
                        "align-items": "center",
                        color: "#808080",
                      }}
                    >
                      <div
                        style={{
                          padding: 20,
                          background: "#f7f8fa",
                          fontSize: 15,
                          borderRadius: 4,
                        }}
                      >
                        Please select time range within 7 days
                      </div>
                    </div>
                  ) : (
                    <>
                      {MetricItem(
                        this.onlyRawParams.includes(
                          this.state.getSelectedParamKey,
                        )
                          ? this.state.getSelectedParamKey === "soc"
                            ? {}
                            : this.state.otherKpiValues
                          : paramDetails?.avgMinMaxData,
                      )}
                      <div className="graph-render-div">{graphRender}</div>
                    </>
                  )}
                </>
              )}
            </div>
          );
        }
        let cameraView =
          findThingAvailibility?.thing_details?.thumbnail?.length ||
          this.getStreamLink(findThingAvailibility)?.length ? (
            <div className="camera-view">
              {this.getStreamLink(findThingAvailibility)?.length ? (
                <div className="stream-player">
                  <StreamedianPlayer
                    id="streaming_player"
                    streamLink={this.getStreamLink(findThingAvailibility)}
                  />
                </div>
              ) : (
                <div className="stream-player">
                  <img
                    id="streaming_player"
                    src={findThingAvailibility?.thing_details?.thumbnail}
                  />
                </div>
              )}
              <div className="camera-control">
                <CameraPTZControl
                  doPTZControl={this.doPTZControl}
                  ipControl={this.ipControl}
                />
              </div>
            </div>
          ) : (
            <div className="camera-view">
              <img id="streaming_player" src={NoIpCameraImg} />
            </div>
          );
        let middleSection = (
          <div className="down-section">
            <AntRow className="param-select-graph-row">
              <AntCol
                className="param-select-col"
                xs={24}
                sm={24}
                md={9}
                lg={8}
                xl={8}
                xxl={7}
              >
                {paramSelectionSection}
              </AntCol>
              <AntCol
                className="graph-section-col"
                xs={24}
                sm={24}
                md={15}
                lg={16}
                xl={16}
                xxl={17}
              >
                {graphSection}
              </AntCol>
            </AntRow>
          </div>
        );

        let headerData = this.headerdataFunction();
        getDrawer = (
          <DrawerForThingWithStatus
            t={this.props.t}
            thingId={this.state.thingId}
            drawer_data={headerData}
            drawerVisible={this.state.drawerVisible}
            thingsDrawerClick={(id) => this.thingsDrawerClick(id)}
            closeDrawer={() => this.closeDrawer()}
          />
        );
        let paramTrendSection = (
          <AntCol
            className="graph-section"
            xs={24}
            sm={24}
            md={24}
            xl={24}
            xxl={24}
          >
            {findThingAvailibility.category === 44 ? "" : middleSection}
          </AntCol>
        );
        let findThingCatIsDg = [18, 96].includes(findThingCat?.id);
        let findThingCatIsFlowAndMotor = findThingCat?.id === 43;
        const thingData = _find(totalData.things, {
          id: parseInt(this.state.thingId),
        });
        const thingStatus = _find(this.state.latestParameterData, {
          thing_id: parseInt(this.state.thingId),
        });
        const thingDataCat = _find(totalData.things_categories, {
          id: thingData.category,
        });
        const switchProps = {
          getRemoteLockAccess:
            typeof this.props?.getRemoteLockAccess === "function" &&
            this.props.getRemoteLockAccess(),
          getRemoteAccess:
            typeof this.props?.getRemoteAccess === "function" &&
            this.props.getRemoteAccess(),
          socket: this.socket,
          is_lock_control_enabled: thingDataCat?.show_lock,
          is_control_enabled: thingDataCat?.show_switch,
          operation_mode: thingData?.thing_details?.operation_mode,
          command_status: thingData?.commands,
          dgStatus: thingStatus?.on_off_moving_status,
          dg_lock_status: thingStatus?.data?.dg_lock_status || "0",
          on_off_moving_status: thingStatus?.on_off_moving_status,
          status_option_includes_stopped:
            thingDataCat.status_options?.includes("Stopped"),
        };
        const lifetime_runhour_val = thingData?.parameters?.filter(
          (param) => param.key === "rnhr",
        )?.[0]?.value;
        const lifetime_runhour = !isNaN(lifetime_runhour_val)
          ? this.formatRunhourValue(lifetime_runhour_val)
          : "NA";
        pageRender = (
          <div className="inner-div">
            {!mobile_view_get ? (
              <CommonHeader
                t={this.props.t}
                showDrawer={() => this.showDrawer()}
                thingId={parseInt(this.state.thingId)}
                client_id={this.props.client_id}
                application_id={this.props.application_id}
                head_obj={headerData}
                vendor_id={this.props.vendor_id}
                location={
                  !findThingCat?.no_location
                    ? this.state.currentAddress
                    : undefined
                }
                isAurassure={aurassureThingCat(
                  findThingCat?.id,
                  this.props.vendor_id,
                )}
                statusIcon={this.getStatus(findThingCat)}
                view_switch_value={"detailed-view"}
                changeView={this.changeView}
                value={"detailed-view"}
                is_close={
                  totalData?.things?.length === 1 &&
                  _find(totalData?.things, function (o) {
                    return ![67, 74, 76, 77].includes(o.category);
                  })
                    ? false
                    : true
                }
                switchClose={this.switchClose}
                detailed_view={isDetailedView(this.state.thingId, totalData)}
                analog_view={isRealTime(this.state.thingId, totalData)}
                devices={thingData?.devices}
                assetStatus={thingData?.status}
                lifetime_runhour={lifetime_runhour}
                {...switchProps}
              />
            ) : (
              ""
            )}

            {aurassureThingCat(findThingCat?.id, this.props.vendor_id) ? (
              <AurassureDetailedView
                t={this.props.t}
                thingId={this.state.thingId}
                user_preferences={this.props.user_preferences}
                totalData={totalData}
                modifiedResponse={this.state.modifiedResponse}
                is_raw_visualization={this.state.is_raw_visualization || true}
                bucket={this.bucket}
                latestParameterData={this.state.latestParameterData}
                customBackBtnClicked={() => this.customBackBtnClicked()}
                paramRangeChange={(e) => this.paramRangeChange(e)}
                aqiAvgParamData={this.state.aqiAvgParamData}
                graphSectionLoading={this.state.graphSectionLoading}
                selectedParam={this.state.selectedParam}
                findThing={findThingAvailibility}
                latestDataFunc={() => this.latestDataFunc()}
                socket={this.socket}
                selected_param_date_range={this.state.selected_param_date_range}
                param_search={this.state.param_search}
                selectParam={(e) => {
                  this.selectParam(e);
                }}
                onSearch={(e) => {
                  this.onSearch(e);
                }}
                client_id={this.props.client_id}
                vendor_id={this.props.vendor_id}
                logged_in_user_client_id={this.props.logged_in_user_client_id}
                param_graph_from_time={this.state.param_graph_from_time}
                param_graph_upto_time={this.state.param_graph_upto_time}
                paramDetails={paramDetails}
                getSelectedParamKey={this.state.getSelectedParamKey}
                onOk={(e, mobile_view) => this.onOk(e, mobile_view)}
                togglePicker={() => this.togglePicker()}
                onOpenChange={() => this.onOpenChange()}
                data_period={this.state.data_period}
                data_type={this.state.data_type}
                is_yesterday={this.state.is_yesterday}
                // raw_graph_data={this.state.raw_graph_data}
                // raw_graph_data_yesterday={this.state.raw_graph_data_yesterday}
                avg_graph_data={this.state.avg_graph_data}
                sum_graph_data={this.state.sum_graph_data}
                lastHourAqi={this.state.lastHourAqi}
                allowedVizChanged={(e) => this.allowedVizChanged(e)}
                rawGraphLoading={this.state.rawGraphLoading}
              />
            ) : findThingCatIsDg ? (
              <DGDetailedView
                client_id={this.props.client_id}
                application_id={this.props.application_id}
                thingId={this.state.thingId}
                user_preferences={this.props.user_preferences}
                totalData={totalData}
                modifiedResponse={this.state.modifiedResponse}
                bucket={this.bucket}
                latestParameterData={this.state.latestParameterData}
                latestDataFunc={() => this.latestDataFunc()}
                socket={this.socket}
                vendor_id={this.props.vendor_id}
                on_off_moving_status={thingStatus?.on_off_moving_status}
                parent_vendor_id={this.props.parent_vendor_id}
              />
            ) : findThingCatIsFlowAndMotor ? (
              <FolwAndMotorDetailedView
                client_id={this.props.client_id}
                application_id={this.props.application_id}
                totalData={totalData}
                socket={this.socket}
              />
            ) : (
              <AntRow
                className={
                  "middle-section-row " +
                  (this.shouldCameraVisible() ? "camera-section" : "")
                }
              >
                {findThingCat?.id === 91 ? (
                  <div className="solar-lifetime">
                    <SolarSystemLifetimeEnvComp
                      data={{
                        lifetimeEnergy: this.getLifetimeEnergy(),
                        co2EmmitionSaved: this.getCo2EmissionValue(),
                        treesPlanted: this.getTreeSavedValue(),
                      }}
                    />
                  </div>
                ) : (
                  ""
                )}

                {console.log(this.props.panelData, "panel_data123")}

                {findThingCat?.id === 94 ? (
                  <PADetailedViewHeader
                    viewMoreClicked={(e, title) =>
                      this.viewMoreClicked(e, title)
                    }
                    actuationsData={this.getActuationsParamValue()}
                    faultsData={this.getFaultsData()}
                  />
                ) : (
                  ""
                )}

                {findThingCat?.id === 93 ? (
                  <SolarInverterStatusHeader
                    latestParamData={selectedThinglatestParam}
                    statusParams={this.solarInverterStatusParams}
                  />
                ) : (
                  ""
                )}

                {findThingCat?.id === 103 ? (
                  <SolarPumpStatusHeader
                    latestParamData={selectedThinglatestParam}
                    statusParams={this.solarPumpStatusParams}
                  />
                ) : (
                  ""
                )}

                {findThingCat?.id === 104 ? (
                  <MRIMachineStatusHeader
                    latestParams={selectedThinglatestParam}
                    keyNamePair={findThingAvailibility.parameters.map(
                      (param) => {
                        return { key: param.key, name: param.name };
                      }
                    )}                    
                  />
                ) : (
                  ""
                )}

                {this.shouldCameraVisible() ? cameraView : paramTrendSection}
                {findThingCat?.id === 94 ? (
                  <PADetailedViewFooter data={this.getDiagnosticParamValue()} />
                ) : (
                  ""
                )}
              </AntRow>
            )}
            <ViewMoreDrawer
              data={this.state.viewMoreData}
              drawerVisiblity={this.state.viewMoreDrawerVisible}
              viewMoreCloseClicked={() => this.viewMoreCloseClicked()}
              viewMoreDrawerTitle={this.state.viewMoreDrawerTitle}
            />
          </div>
        );
      } else {
        return <div className="no-data-text">No Asset Found</div>;
      }
    }
    return (
      <div>
        {this.state.loading && (
          <Loading
            t={this.props.t}
            show_logo={this.props.loading_logo}
            className="align-center-loading"
          />
        )}
        <div
          id="generic_dg_detailed_view"
          className={this.props.bannerToBeShown ? "map-banner" : ""}
        >
          {getDrawer}
          {pageRender}
        </div>
      </div>
    );
  }
}
