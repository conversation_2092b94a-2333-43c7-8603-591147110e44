import {
  retriveThingsList,
  retriveApplicationThings,
  establishSocketConnection,
  subscribeForThingsUpdates,
  disconnectSocketConnection,
  subscribeForEntityUpdates,
} from "@datoms/js-sdk";
import { isAurassure } from "../../../logic/isAurassure";
import _find from "lodash/find";
import _filter from "lodash/filter";
import { sortedThingsOrderArray } from "../../../logic/ThingsOrder";
import { getFinalThingCat } from "../../../logic/getThingsCats";
import { getThingsAndParameterData } from "../../../../data_handling/thingsListManipulation";
import { filterDginIot } from "../../../../data_handling/DGinIot";
import ParseMarkerName from "../../../../data_handling/ParseMakeName";
import _findIndex from "lodash/findIndex";
import { flushSync } from "react-dom";
import _merge from "lodash/merge";

export async function getThingsList(data) {
  const {
    mobile_view_get,
    total_data,
    socket,
    selected_id,
    mobileViewEnabled,
    mobile_selected_param,
  } = this.props;
  let totalData = total_data ? total_data : await retriveThingsList(data);
  let applicationThings = await retriveApplicationThings(
    this.props.application_id,
    "?all_parameters=true",
  );
  let thingsOrder = sortedThingsOrderArray(totalData);
  totalData.things = thingsOrder;
  totalData.things_categories = getFinalThingCat(
    isAurassure(this.props.vendor_id),
  );
  if (!mobileViewEnabled) {
    let getFilterThings = _filter(totalData.things, function (o) {
      return (
        _find(totalData.things_categories, { id: o.category })?.pages
          ?.detailed_view !== false
      );
    });
    totalData.things = getFilterThings;
  }
  totalData = filterDginIot.bind(this)(totalData);
  let modifiedResponse = getThingsAndParameterData(totalData);
  let makeDetails = await ParseMarkerName(
    this.props.client_id,
    this.props.application_id,
    18,
  );
  let offlineArray = this.getOnlineOfflineArray(totalData);
  let latestParameterData = this.offlineTimeOutFunction(
    undefined,
    offlineArray,
    totalData,
    modifiedResponse.latest_parameter_data,
    modifiedResponse,
  );
  let thingId = "";
  let storedThing = "";
  if (
    this.state.thingId &&
    totalData &&
    totalData.things &&
    totalData.things.length
  ) {
    storedThing = _find(totalData.things, {
      id: parseInt(this.state.thingId),
    });
  }
  if (mobile_view_get) {
    thingId = selected_id;
  } else {
    if (this.state.thingId && storedThing) {
      thingId = this.state.thingId;
    } else if (this.parsed["thing_id"]) {
      thingId = this.parsed["thing_id"];
    } else {
      thingId = modifiedResponse.all_thing_ids[0];
    }
  }
  let findThing = {};
  if (totalData && totalData.things && thingId) {
    findThing = _find(totalData.things, { id: parseInt(thingId) });
  }

  let findThingCat = totalData.things_categories?.find(
    (cat) => cat.id === findThing.category,
  );
  let findApplicationThings = _find(applicationThings?.things_categories, {
    id: findThing.category,
  });
  let paramArray =
    findThingCat?.id === 71
      ? _find(findThing.parameters, { key: "fuel_litre" })
        ? ["fuel_litre"]
        : ["fuel"]
      : this.findParam(findThingCat, findThing, findApplicationThings);
  let selectedParamDateRange = "",
    fromDate = "",
    uptoDate = "",
    dataType = "",
    isRawVisualization = "",
    dataPeriod = 0,
    allowed_visualizations = true,
    selected_param = "";
  let string = this.props?.location?.search;
  let paramSelect = mobile_view_get
    ? paramArray[0]
    : this.getUrlValue(string, "selected_param", "=");
  if (paramSelect !== null) {
    selected_param = paramSelect;
  }
  let selectedParam = paramArray.includes(selected_param)
    ? selected_param
    : paramArray?.[0]
      ? paramArray[0]
      : undefined;
  if (paramArray && paramArray.length) {
    [
      selectedParamDateRange,
      fromDate,
      uptoDate,
      dataType,
      isRawVisualization,
      dataPeriod,
      allowed_visualizations,
    ] = this.getParameterGraphRender(findThing, selectedParam);
  }
  console.log(paramArray, "paramArray");
  flushSync(() =>
    this.setState(
      {
        is_raw_visualization: isRawVisualization,
        selected_param_date_range: selectedParamDateRange,
        param_graph_from_time: fromDate,
        is_yesterday: selectedParamDateRange === "yesterday",
        param_graph_upto_time: uptoDate,
        data_type: dataType,
        data_period: dataPeriod,
        makeDetails,
        latestParameterData: latestParameterData,
        totalData: totalData,
        allowed_visualizations: allowed_visualizations,
        totalParamToShow: paramArray,
        getSelectedParamKey: mobile_selected_param
          ? mobile_selected_param
          : selectedParam,
        selectedParam: mobile_selected_param
          ? [mobile_selected_param]
          : paramArray.includes(selected_param)
            ? [selected_param]
            : [paramArray?.[0]]
              ? [paramArray[0]]
              : undefined,
        modifiedResponse: modifiedResponse,
        thingId: thingId,
        offlineArray,
        applicationThings,
      },
      () => {
        disconnectSocketConnection(this.socket);
        this.socket = mobile_view_get ? socket : establishSocketConnection();
        if (
          this.state.totalData.things &&
          this.state.totalData.things.length &&
          !this.props.location.pathname.includes("thing_id") &&
          !mobile_view_get
        ) {
          this.timeAndParamHistoryPush();
        }
        this.socket.on("connect", () => {
          subscribeForThingsUpdates(
            this.socket,
            this.state.modifiedResponse.all_thing_ids,
            0,
          );
          subscribeForEntityUpdates(
            this.socket,
            this.state.modifiedResponse.all_thing_ids,
            "thing",
          );
        });

        this.socket.on("details_updated", (payload) => {
          this.updateEntityDetails(payload);
        });

        const bucketTimeInterval = 60000;

        this.bucketTime = setInterval(() => {
          if (Object.keys(this.bucket.raw_data).length) {
            this.realTimeDataFunc(this.bucket.raw_data);
            this.realTimeDrawerFunc(this.bucket.raw_data);
          }
          this.bucket = {
            raw_data: {},
          };
        }, bucketTimeInterval);

        this.socket.on("new_data_generated_for_station", async (payload) => {
          if (payload) {
            if(parseInt(payload.thing_id) === parseInt(this.state.thingId)){
              this.realTimeDataFunc({
                [payload.thing_id]: payload
              });
              this.realTimeDrawerFunc({
                [payload.thing_id]: payload
              });
            } else {
              this.bucket["raw_data"][payload.thing_id] = payload;
            }
          }
        });
      },
    ),
  );
}

export function updateEntityDetails(payload) {
  console.log("paload", payload);
  if (
    payload.entity_type === "thing" &&
    !isNaN(parseInt(payload.entity_id)) &&
    payload.details
  ) {
    const { totalData } = this.state;
    if (totalData.things && totalData.things.length) {
      let totalDataResponse = JSON.parse(JSON.stringify(totalData));
      let thingDetailsIndex = _findIndex(totalDataResponse.things, {
        id: parseInt(payload.entity_id),
      });
      if (
        thingDetailsIndex > -1 &&
        totalDataResponse &&
        totalDataResponse.things
      ) {
        totalDataResponse.things[thingDetailsIndex] = _merge(
          {},
          totalDataResponse.things[thingDetailsIndex],
          payload.details,
        );
      }
      this.setState({
        totalData: totalDataResponse,
      });
    }
  }
}

export function realTimeDataFunc(payload) {
  let rawDataRealTime = {};
  let payloadData, payloadDataEnrg;
  if (
    payload &&
    Object.keys(payload).length
  ) {
    Object.keys(payload).forEach((payload_key) => {
      if (
        this.state.selected_param_date_range === "custom" &&
        payload[payload_key].time > this.state.param_graph_upto_time
      )
        return;

      rawDataRealTime = {
        time: payload[payload_key].time,
        thing_id: parseInt(payload_key),
        parameter_values: payload[payload_key].data,
      };
      payloadData = this.payloadDataFunc(rawDataRealTime);
      payloadDataEnrg = this.payloadDataFuncForEnrg(rawDataRealTime);
    });

    if (payloadData || payloadDataEnrg) {
      this.setState({
        raw_graph_data: payloadData,
        enrgRawParamData: payloadDataEnrg,
      });
    }
  }
}

export function realTimeDrawerFunc(payload) {
  let latestParameterData;
  let realtimeThingsData = this.state.latestParameterData;
  if (payload && Object.keys(payload).length) {
    Object.keys(payload).map((payload_key) => {
      let findIndex = _findIndex(realtimeThingsData, {
        thing_id: parseInt(payload_key),
      });
      let findTotalData = _find(this.state.totalData.things, {
        id: parseInt(payload_key),
      });
      let findParamInd = -1;
      if (findIndex > -1) {
        if (
          Object.keys(payload[payload_key].data) &&
          Object.keys(payload[payload_key].data).length
        ) {
          Object.keys(payload[payload_key].data).map((key) => {
            if (findTotalData && Array.isArray(findTotalData.parameters)) {
              findParamInd = _findIndex(findTotalData.parameters, {
                key: key,
              });
            }
            if (findTotalData?.category === 18 && key === "fuel") {
              if (payload[payload_key].data["fuel_raw"]) {
                realtimeThingsData[findIndex]["data"][key] =
                  payload[payload_key].data["fuel_raw"];
              } else {
                realtimeThingsData[findIndex]["data"][key] =
                  payload[payload_key].data[key];
              }
            } else {
              if (findParamInd > -1) {
                realtimeThingsData[findIndex]["data"][key] =
                  payload[payload_key].data[key];
              }
            }
          });
          try{
            if(Object.keys(realtimeThingsData[findIndex]["data"]).length){
              Object.keys(realtimeThingsData[findIndex]["data"]).forEach((key) => {
                if(typeof key === "string" && key.startsWith("debug_f_")) {
                  realtimeThingsData[findIndex]["data"][key] = payload[payload_key].data[key] ?? "";
                }
              })
            }
          } catch(e) {}
        }
        realtimeThingsData[findIndex]["time"] = payload[payload_key].time;
      }
      latestParameterData = this.offlineTimeOutFunction(
        payload[payload_key],
        this.state.offlineArray,
        this.state.totalData,
        realtimeThingsData,
        this.state.modifiedResponse,
      );
    });
    this.setState({
      latestParameterData: latestParameterData,
    });
  }
}

export function payloadDataFunc(payloadRawData) {
  const { is_yesterday, clubbedParamArr } = this.state;
  let thingParameterDataRaw = this.state.raw_graph_data;
  console.log("ldhfhkdjkf", is_yesterday);
  if (clubbedParamArr?.length) {
    clubbedParamArr.map((param) => {
      if (
        payloadRawData &&
        Object.keys(payloadRawData).length &&
        payloadRawData.thing_id === parseInt(this.state.thingId) &&
        payloadRawData.parameter_values[param] &&
        thingParameterDataRaw &&
        thingParameterDataRaw[this.state.thingId] &&
        !is_yesterday
      ) {
        thingParameterDataRaw[this.state.thingId][param].push([
          payloadRawData.time * 1000,
          parseFloat(payloadRawData.parameter_values[param]),
        ]);
        thingParameterDataRaw[this.state.thingId][param] =
          thingParameterDataRaw[this.state.thingId][param].sort(
            function (a, b) {
              return a[0] - b[0];
            },
          );
      }
    });
  }
  return thingParameterDataRaw;
}
export function payloadDataFuncForEnrg(payloadRawData) {
  let thingParameterDataRaw = this.state.enrgRawParamData;
  const enrgKey = this.isParamCumulative(this.state.getSelectedParamKey)
    ?.aggrKey[0];
  if (
    payloadRawData &&
    Object.keys(payloadRawData).length &&
    payloadRawData.thing_id === parseInt(this.state.thingId) &&
    payloadRawData.parameter_values[enrgKey] &&
    thingParameterDataRaw &&
    thingParameterDataRaw[this.state.thingId]
  ) {
    thingParameterDataRaw[this.state.thingId][enrgKey].push([
      payloadRawData.time * 1000,
      parseFloat(payloadRawData.parameter_values[enrgKey]),
    ]);
    thingParameterDataRaw[this.state.thingId][enrgKey] = thingParameterDataRaw[
      this.state.thingId
    ][enrgKey].sort(function (a, b) {
      return a[0] - b[0];
    });
  }
  return thingParameterDataRaw;
}
