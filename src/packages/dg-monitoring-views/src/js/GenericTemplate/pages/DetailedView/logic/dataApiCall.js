import { getThingsData } from '@datoms/js-sdk';
import { isAurassure } from '../../../logic/isAurassure';
import _find from 'lodash/find';
import moment from 'moment-timezone';

export async function thingGraphDataFunction() {
    const {totalData, thingId, getSelectedParamKey, selectedAllowedViz} = this.state;
    let findThing = _find(totalData.things, {
        id: parseInt(thingId),
    });
    const findCat = this.findListAndCat(totalData, thingId).findThingCat;
    const findClubParam = _find(findCat?.pages?.detailed_view?.parameter_trend?.parameters, 
        {key: getSelectedParamKey})?.clubbedParams;
    const clubbedParamArr = findClubParam?.length ? [getSelectedParamKey, ...findClubParam] : 
        this.state.selectedParam;
    let rawData = {},
        unProcessedRawData = {},
        avgData = {},
        aqiData = {},
        pollutionData = {},
        energyRawData = {},
        energyAggrData = {},
        sumData = {};
    if (isAurassure(this.props.vendor_id)) {
        let dataPacketAvgAqi = {
            data_type: 'aggregate',
            aggregation_period: 3600,
            parameters: this.state.selectedParam,
            parameter_attributes: [
                this.state.selectedParam[0] === 'aqi' ? 'value' : 'avg',
            ],
            things: [parseInt(thingId)],
            from_time: parseInt(this.state.param_graph_from_time),
            upto_time: parseInt(this.state.param_graph_upto_time),
        };
        aqiData = await getThingsData(
            dataPacketAvgAqi,
            this.props.client_id,
            this.props.application_id
        );
    }
    if (this.isFifteenMinAggrView(findThing)) {
        let fromTimeForFifteenMinAggr =
            this.calculateNearestFifteenMinuteMultiplier(
                this.state.param_graph_from_time
            );
        let pollutionDataPacketAvg = {
            data_type: 'aggregate',
            aggregation_period: 900,
            parameters: this.state.selectedParam,
            parameter_attributes: ['avg'],
            things: [parseInt(thingId)],
            from_time: parseInt(fromTimeForFifteenMinAggr),
            upto_time: parseInt(this.state.param_graph_upto_time),
        };
        pollutionData = await getThingsData(
            pollutionDataPacketAvg,
            this.props.client_id,
            this.props.application_id
        );
    }
    if (this.isParamCumulative(this.state.getSelectedParamKey)?.isCumulative) {
        const findEnergyKey = this.isParamCumulative(
            this.state.getSelectedParamKey
        )?.aggrKey;
        if (this.state.data_period === 0 && ![78, 79, 101, 91, 94, 99, 93, 103].includes(findThing?.category)) {
            let dataPacketRaw = {
                data_type: 'raw',
                aggregation_period: this.state.data_period,
                parameters: findEnergyKey,
                parameter_attributes: [],
                things: [parseInt(thingId)],
                from_time: parseInt(this.state.param_graph_from_time),
                upto_time: parseInt(this.state.param_graph_upto_time),
            };
            energyRawData = await getThingsData(
                dataPacketRaw,
                this.props.client_id,
                this.props.application_id
            );
        }
        if (this.isSolarSystem()) {
            findEnergyKey.push('calculated_runhour');
        }
        const finalFromTime =
          this.state.data_period === 86400
            ? moment
                .unix(this.state.param_graph_from_time)
                .startOf("day")
                .unix()
            : parseInt(this.state.param_graph_from_time);
        const finalUptoTime =
          this.state.data_period === 86400
            ? moment.unix(this.state.param_graph_upto_time).endOf("day").unix()
            : parseInt(this.state.param_graph_upto_time);
        let dataPacketAggr = {
          data_type: "aggregate",
          aggregation_period:
            this.state.data_period === 0 ? 3600 : this.state.data_period,
          parameters: findEnergyKey,
          parameter_attributes: ["sum"],
          things: [parseInt(thingId)],
          from_time: finalFromTime,
          upto_time: finalUptoTime,
        };
        energyAggrData = await getThingsData(
            dataPacketAggr,
            this.props.client_id,
            this.props.application_id
        );
    } else {
        if (findThing?.category !== 43) {
            const diffTimeGreaterThan7Days = parseInt(this.state.param_graph_upto_time) - parseInt(this.state.param_graph_from_time) > 8 * 86400;
            if (this.state.data_type === 'raw' || this.onlyRawParams.includes(this.state.getSelectedParamKey)) {
                let dataPacketRaw;
                if(selectedAllowedViz?.length) {
                    dataPacketRaw = {
                        data_type: 'raw',
                        aggregation_period: this.state.data_period,
                        parameters: clubbedParamArr,
                        data_source: ['processed', 'unprocessed'],
                        parameter_attributes: [],
                        things: [parseInt(thingId)],
                        from_time: parseInt(this.state.param_graph_from_time),
                        upto_time: parseInt(this.state.param_graph_upto_time),
                    };
                } else {
                    dataPacketRaw = {
                        data_type: 'raw',
                        aggregation_period: this.state.data_period,
                        parameters: clubbedParamArr,
                        parameter_attributes: [],
                        things: [parseInt(thingId)],
                        from_time: parseInt(this.state.param_graph_from_time),
                        upto_time: parseInt(this.state.param_graph_upto_time),
                    };
                }
                const stopApiCall = this.onlyRawParams.includes(this.state.getSelectedParamKey) && diffTimeGreaterThan7Days;
                if(!stopApiCall) {
                    const [rawDataResponse] = await Promise.all([
                        getThingsData(
                            dataPacketRaw,
                            this.props.client_id,
                            this.props.application_id
                        ),
                        this.calculateKpiValues(this.state.getSelectedParamKey),
                    ])
                    console.log("Testing rawDataResponse", rawDataResponse)
                    rawData = rawDataResponse;
                } else {
                    rawData = {
                        data: []
                    };
                }
            } else if (this.state.data_type === 'avg') {
                let dataPacketAvg = {
                    data_type: 'aggregate',
                    aggregation_period: this.state.data_period,
                    parameters: clubbedParamArr,
                    parameter_attributes: ['avg'],
                    things: [parseInt(thingId)],
                    from_time: parseInt(this.state.param_graph_from_time),
                    upto_time: parseInt(this.state.param_graph_upto_time),
                };
                avgData = await getThingsData(
                    dataPacketAvg,
                    this.props.client_id,
                    this.props.application_id
                );
            } else if (this.state.data_type === 'sum') {
                console.log("Test here", this.state.data_period)
                let dataPacketSum = {
                    data_type: 'aggregate',
                    aggregation_period: this.state.data_period,
                    parameters: clubbedParamArr,
                    parameter_attributes: ['sum'],
                    things: [parseInt(thingId)],
                    from_time: parseInt(this.state.param_graph_from_time),
                    upto_time: parseInt(this.state.param_graph_upto_time),
                };
                sumData = await getThingsData(
                    dataPacketSum,
                    this.props.client_id,
                    this.props.application_id
                );
            }
        }
    }
    this.setState(
        {
            thing_raw_data: rawData.data,
            thing_unprocessed_raw_data: unProcessedRawData.data,
            thing_avg_data: avgData.data,
            thing_sum_data: sumData.data,
            pollutionData: pollutionData.data,
            aqiData: aqiData.data,
            energyRawData: energyRawData.data,
            energyAggrData: energyAggrData.data,
            clubbedParamArr,
        },
        () => {
            this.detailedGraphDataFunc();
        }
    );
}

export async function getEnergySummaryData() {
    const {thingId, totalData, getSelectedParamKey} = this.state;
    let findThing = _find(totalData.things, {
        id: parseInt(thingId),
    });
    const apiCondition = this.isParamCumulative(getSelectedParamKey)?.isCumulative && [78, 79, 101, 91, 94, 99, 92, 93, 103].includes(findThing?.category);
    if(apiCondition) {
        const summaryParams = this.findSelectedParamInCategory()?.summaryParams
        let todayEnrgDataPacketAvg = {
            data_type: 'aggregate',
            aggregation_period: 86400,
            parameters: summaryParams,
            parameter_attributes: ['sum'],
            things: [parseInt(thingId)],
            from_time: moment().startOf('day').unix(),
            upto_time: moment().endOf('day').unix(),
        };
        const todayEnergyData = getThingsData(
            todayEnrgDataPacketAvg,
            this.props.client_id,
            this.props.application_id
        );
        let yesterdayEnrgDataPacketAvg = {
            data_type: 'aggregate',
            aggregation_period: 86400,
            parameters: summaryParams,
            parameter_attributes: ['sum'],
            things: [parseInt(thingId)],
            from_time: moment().subtract(1, 'day').startOf('day').unix(),
            upto_time: moment().subtract(1, 'day').endOf('day').unix(),
        };
        const yesterdayEnergyData = getThingsData(
            yesterdayEnrgDataPacketAvg,
            this.props.client_id,
            this.props.application_id
        );
        let thisMonthEnrgDataPacketAvg = {
            data_type: 'aggregate',
            aggregation_period: 2592000,
            parameters: summaryParams,
            parameter_attributes: ['sum'],
            things: [parseInt(thingId)],
            from_time: moment().startOf('month').unix(),
            upto_time: moment().endOf('month').unix(),
        };
        const thisMonthEnergyData = getThingsData(
            thisMonthEnrgDataPacketAvg,
            this.props.client_id,
            this.props.application_id
        );
        let lastMonthEnrgDataPacketAvg = {
            data_type: 'aggregate',
            aggregation_period: 2592000,
            parameters: summaryParams,
            parameter_attributes: ['sum'],
            things: [parseInt(thingId)],
            from_time: moment().subtract(1, 'month').startOf('month').unix(),
            upto_time: moment().subtract(1, 'month').endOf('month').unix(),
        };
        const lastMonthEnergyData = getThingsData(
            lastMonthEnrgDataPacketAvg,
            this.props.client_id,
            this.props.application_id
        );
        let thisYearEnrgDataPacketAvg = {
            data_type: 'aggregate',
            aggregation_period: 31536000,
            parameters: summaryParams,
            parameter_attributes: ['sum'],
            things: [parseInt(thingId)],
            from_time: moment().startOf('year').unix(),
            upto_time: moment().endOf('year').unix(),
        };
        const thisYearEnergyData = getThingsData(
            thisYearEnrgDataPacketAvg,
            this.props.client_id,
            this.props.application_id
        );
        let lastYearEnrgDataPacketAvg = {
            data_type: 'aggregate',
            aggregation_period: 31536000,
            parameters: summaryParams,
            parameter_attributes: ['sum'],
            things: [parseInt(thingId)],
            from_time: moment().subtract(1, 'year').startOf('year').unix(),
            upto_time: moment().subtract(1, 'year').startOf('year').unix(),
        };
        const lastYearEnergyData = getThingsData(
            lastYearEnrgDataPacketAvg,
            this.props.client_id,
            this.props.application_id
        );
        const [
            todayEnergyAggrData,
            yesterdayEnergyAggrData,
            thisMonthEnergyAggrData,
            lastMonthEnergyAggrData,
            thisYearEnergyAggrData,
            lastYearEnergyAggrData,
        ] = await Promise.all([
            todayEnergyData,
            yesterdayEnergyData,
            thisMonthEnergyData,
            lastMonthEnergyData,
            thisYearEnergyData,
            lastYearEnergyData,
        ]);
        const enrgSummaryObj = {
            todayEnergyAggrData,
            yesterdayEnergyAggrData,
            thisMonthEnergyAggrData,
            lastMonthEnergyAggrData,
            thisYearEnergyAggrData,
            lastYearEnergyAggrData,
        }
        this.setState({
            enrgSummaryObj,
            summaryParams,
            enrgDetailDataLoading: false
        });
    }
}

export async function getLastHourAqi() {
    const { client_id, application_id } = this.props;
    const { latestParameterData, thingId } = this.state;
    let findLatestData = _find(latestParameterData, {
        thing_id: parseInt(thingId),
    });
    if (isAurassure(this.props.vendor_id)) {
        let lastHourAqi = await getThingsData(
            {
                data_type: 'aggregate',
                aggregation_period: 3600,
                parameters: ['aqi'],
                parameter_attributes: ['value', 'responsible_parameter'],
                things: [parseInt(thingId)],
                from_time:
                    findLatestData?.on_off_moving_status === '2'
                        ? moment
                                .unix(findLatestData?.time)
                                .startOf('hour')
                                .unix()
                        : moment().startOf('hour').unix(),
                upto_time:
                    findLatestData?.on_off_moving_status === '2'
                        ? moment
                                .unix(findLatestData?.time)
                                .endOf('hour')
                                .unix()
                        : moment().endOf('hour').unix(),
            },
            client_id,
            application_id
        );
        this.setState({
            lastHourAqi: lastHourAqi?.data,
        });
    }
}

export async function calculateKpiValues(param_key) {
  if (param_key === "dr_st") {
    const {
      thingId,
      data_period,
      param_graph_from_time,
      param_graph_upto_time,
      selected_param_date_range,
    } = this.state;
    let dataPacket;

    if (selected_param_date_range === "last_24_hrs") {
      dataPacket = {
        data_type: "raw",
        aggregation_period: 0,
        parameters: ["dr_op_cnt", "calculated_runhour"],
        parameter_attributes: [],
        things: [parseInt(thingId)],
        from_time: parseInt(param_graph_from_time),
        upto_time: parseInt(param_graph_upto_time),
        summarize: ["sum(dr_op_cnt)", "sum(calculated_runhour)"],
      };
    } else {
      dataPacket = {
        data_type: "aggregate",
        aggregation_period: 86400,
        things: [parseInt(thingId)],
        from_time: parseInt(param_graph_from_time),
        upto_time: parseInt(param_graph_upto_time),
        summarize: [
          {
            query: ["sum(dr_op_cnt.sum)", "sum(calculated_runhour.sum)"],
          },
        ],
      };
    }

    const response = await getThingsData(
      dataPacket,
      this.props.client_id,
      this.props.application_id,
      false,
      selected_param_date_range === "last_24_hrs" ? "?data_source=new" : "",
    );

    let doorOpenCount = "-";
    let doorOpenDuration = "-";

    if (selected_param_date_range === "last_24_hrs" && response.summary) {
      doorOpenCount = response.summary.dr_op_cnt?.sum ?? "-";
      doorOpenDuration = response.summary.calculated_runhour?.sum ?? "-";
    }

    if (
      selected_param_date_range !== "last_24_hrs" &&
      response.summary?.[0]?.data?.length > 0
    ) {
      const parameterValues = response.summary[0].data[0].parameter_values;
      if (parameterValues && parameterValues.sum) {
        doorOpenCount = parameterValues.sum.dr_op_cnt?.sum ?? "-";
        doorOpenDuration = parameterValues.sum.calculated_runhour?.sum ?? "-";
      }
    }

    if (!isNaN(parseInt(doorOpenDuration))) {
      // convert to HH:mm from Seconds
      const hours = Math.floor(parseInt(doorOpenDuration) / 3600);
      const minutes = Math.floor((parseInt(doorOpenDuration) % 3600) / 60);
      doorOpenDuration = `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;
    }

    console.log("doorOpenCount", doorOpenCount);
    console.log("doorOpenDuration", doorOpenDuration);

    this.setState({
      otherKpiValues: {
        dr_st: {
          dr_op_cnt: {
            title: "Door Open Count",
            value: `${doorOpenCount}`,
            //   image:
            //     "/src/packages/dg-monitoring-views/src/js/GenericTemplate/images/graph.svg",
            unit: "",
          },
          calculated_runhour: {
            title: "Door Open Duration",
            value: doorOpenDuration,
            //   image:
            //     "/src/packages/dg-monitoring-views/src/js/GenericTemplate/images/Group 1847.svg",
            unit: "HH:mm",
          },
        },
      },
    });
  }

  if (param_key === "soc") {
    const { thingId, totalData } = this.state;

    const findThing = _find(totalData.things, {id: parseInt(thingId)}) || [];

    const findParams = _find(findThing?.parameters ?? [], { key: 'ch_cyc_cnt' })

    let value = "NA";
    if (!isNaN(parseInt(findParams?.aggregated_value?.lifetime?.sum))) {
      value = findParams.aggregated_value.lifetime.sum.toFixed(2);
    }
    this.setState({
      ch_cyc_cnt: {
        title: "Lifetime Charging Cycle Count",
        value: `${value}`,
        unit: "",
      },
    });
  }
}