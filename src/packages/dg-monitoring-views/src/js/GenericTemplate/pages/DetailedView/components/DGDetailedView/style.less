#dg_detailed_view_generic {
	line-height: 1.5;
	position: relative;
	height: calc(100% - 160px);
	overflow: auto;
	.ant-select-selector {
		background: transparent;
		border: none;
		border-bottom: 1px dashed #7686a1;
		border-radius: 0px !important;
		font-style: italic;
		color: #7686a1;
		font-weight: 600;
		font-size: 12px;
		box-shadow: none !important;
		outline: none !important;
		.ant-select-selection-item {
			opacity: 1 !important;
		}
	}
	.ant-row {
		width: auto !important;
	}
	.graph-container {
		.highcharts-background {
			fill: transparent;
		}
	}
	.inner-div {
		background: transparent
			linear-gradient(157deg, #ffffff 0%, #f3f9ff 100%) 0% 0% no-repeat
			padding-box;
		height: auto;
		padding: 20px 70px;
		overflow-y: scroll;
		overflow-x: hidden;
		.detailed-view-header {
			display: flex;
			margin-top: 20px;
			justify-content: space-between;
			align-items: center;
			padding: 0 30px;
			.thing-details-container {
				display: flex;
				align-items: flex-start;
				.thing-name-date {
					.anticon-menu-fold {
						margin-top: 5px;
						font-size: 20px;
						margin-right: 20px;
						color: #232323;
					}
					.thimg-name {
						color: #232323;
						font-size: 14px;
						font-weight: bold;
						display: flex;
						align-items: flex-start;
						.anticon {
							margin-top: 0px;
						}
					}
					.thing-date {
						color: #808080;
						font-size: 12px;
						margin-left: 40px;
						margin-top: 5px;
					}
				}
				.make-model-lifetime-div {
					margin-left: 40px;
					color: #7686a1;
					font-size: 13px;
					.make-model-div {
						display: flex;
						.make-div {
							padding-right: 20px;
						}
					}
					.lifetime-div {
						margin-top: 3px;
					}
				}
				.operation-mode {
					width: fit-content;
					margin-left: 40px;
					color: #808080;
					border: 1px solid #808080;
					padding: 5px 15px;
					border-radius: 10px;
				}
			}
			.status-toggle {
				display: flex;
				align-items: center;
				.lock-header {
					margin-right: 30px;
				}
				.lock-header-mobile {
					margin-left: 25px;
				}
				span {
					margin-right: 10px;
				}
				.status-shown-div {
					margin-right: 20px;
					font-weight: 600;
					font-size: 14px;
				}
				.running-thing {
					color: #47ccc3;
				}
				.off-thing {
					color: #ff0000;
				}
				.on-off-switch {
					width: 72px;
					margin-left: 10px;
					background-color: #ffffff;
					height: 26px;
					border: 2px solid #dfe8f382;
					.ant-switch-inner {
						color: #7686a1;
						font-size: 12px;
					}
				}
				.on-off-switch::after {
					height: 28px;
					width: 28px;
				}
			}
			.switch-toggle {
				padding-left: 10px;
			}
		}
		.genset-status-container {
			padding: 0 30px;
		}
		.total-row {
			padding: 0 30px;
			width: auto;
			margin-top: 40px;
			.panel-trip-col {
				.panel-total-container {
					padding: 20px;
					border-radius: 18px;
					border: 3px solid #fff;
					box-shadow: 10px 8px 12px #6b6b6b0d;
					background: transparent
						linear-gradient(
							180deg,
							#ffffff 0%,
							#fff1df 54%,
							#ffe7db00 100%
						)
						0% 0% no-repeat padding-box;
					.fuel-tank-estimated-total-row {
						.fuel-tank-col {
							.icon-tank-whole-div {
								width: 71px;
								margin: 0 auto;
								box-shadow: 6px 10px 12px #00000024;
							}
						}
						.fuel-tank-col.no-fuel {
							padding: 0px;
							.icon-tank-whole-div {
								margin: 0 auto;
							}
						}
						.estimated-fuel-filled-col {
							.total-estimated-rnhr {
								margin-left: 30px;
								.total-div {
									.value {
										font-size: 16px !important;
										color: #232323 !important;
										font-weight: 600 !important;
									}
									.caption {
										font-size: 12px !important;
										color: #808080 !important;
									}
								}
								.fuel-value-date {
									margin-top: 20px;
									.fuel-filled-div {
										font-size: 10px;
										color: #808080;
									}
								}
							}
						}
						&.fuel-tank-estimated-only-fuel {
							height: 100%;
							.fuel-tank-col {
								padding-left: 0;
								display: flex;
								align-items: center;
								justify-content: center;
							}
							.estimated-fuel-filled-col {
								.total-estimated-rnhr {
									margin-left: 0;
									div {
										text-align: center;
									}
								}
							}
						}
					}
					.panel-param-row {
						width: auto;
						margin-top: 20px;
						.panel-param-col {
							margin-top: 17px;
							.param-div {
								border-radius: 14px;
								border: 1px solid #fff;
								background: transparent
									linear-gradient(
										157deg,
										#ffffff 0%,
										#dedede00 100%
									)
									0% 0% no-repeat padding-box;
								padding: 20px 10px;
								.panel-param-value {
									font-size: 18px;
									color: #232323;
									display: flex;
									justify-content: center;
									img {
										width: 28px;
										height: 26px;
									}
								}
								.panel-param-name {
									text-align: center;
									color: #808080;
									margin-top: 10px;
									font-size: 13px;
								}
							}
						}
					}
					&.no-fuel-panel {
						height: 100%;
						.panel-param-row {
							height: 100%;
							&.no-fuel-panel-param-row {
								height: 74%;
							}
						}
						.fuel-tank-estimated-total-row {
							.fuel-tank-col {
								padding-left: 0px;
								.icon-tank-whole-div {
									margin: 0 auto;
								}
							}
						}
					}
				}
			}
			.rnhr-fuel-enrg-col {
				.graph-data-row {
					height: 100%;
					width: auto;
					padding: 10px;
					border: 2px solid #ffffff;
					background: transparent
						linear-gradient(130deg, #ffffff 0%, #f8fbfe 100%) 0% 0%
						no-repeat padding-box;
					box-shadow: 8px 12px 25px #b6c1cf33;
					border-radius: 18px;
					.heading {
						font-weight: 600;
						font-size: 16px;
						margin-left: 15px;
						margin-top: 5px;
						display: flex;
						align-items: flex-end;
						justify-content: space-between;
					}
					.rnhr-fuel-enrg-data-col {
						margin-top: 20px;
						display: flex;
						justify-content: center;
						.today-container {
							.today-data {
								justify-content: center;
								align-items: center;
								margin-left: 10px;
								.today-data-val {
									text-align: center;
									font-size: 16px;
									letter-spacing: 0px;
									color: #374375;
									font-weight: 600;
								}
								.today-param-name {
									text-align: center;
									img {
										margin-right: 10px;
									}
									margin-top: 5px;
									font-size: 14px;
									letter-spacing: 0px;
									color: #7686a1;
									opacity: 1;
									border-right: 1px solid #70707061;
								}

								.yesterday-data-val {
									margin-top: 5px;
									color: #374375;
									font-size: 12px;
									display: flex;
									justify-content: center;
									.path-icon {
										width: 10px;
										margin-right: 5px;
									}
								}
							}
						}
						.today-container:nth-last-child(1) {
							.today-data {
								.today-param-name {
									border: none;
								}
							}
						}
					}
					.rnhr-fuel-enrg-graph-col {
						margin-top: 36px;
						.graph-container {
							margin-bottom: 5px;
						}
					}
					&.no-fuel-graph-row {
						height: 100%;
					}
				}
			}
			.activity-col {
				height: 100%;
				.tab-total-container {
					border: 2px solid #ffffff;
					box-shadow: 10px 8px 12px #92929212;
					border-radius: 18px;
				}
				.notification-tab {
					height: 463px;
					.ant-tabs-content {
						height: 100% !important;
						.ant-tabs-tabpane {
							height: 305px;
							overflow: auto;
						}
					}
					.ant-tabs-nav {
						.ant-tabs-tab {
							padding: 5px 15px;
						}
					}
				}
				&.dg_only_fuel_activity {
					.tab-total-container {
						.notification-tab {
							height: 336px;
						}
					}
				}
				&.dg_without_fuel_activity {
					.tab-total-container {
						.notification-tab {
							height: 346px;
						}
					}
				}
			}
		}
		.param-trend-section-row {
			padding: 0 30px;
			margin-top: 40px;
			&.only-fuel-middle-section-row {
				margin-top: 0;
			}
			.graph-section {
				.down-section {
					background: #ffffff 0% 0% no-repeat padding-box;
					border-radius: 20px;
					box-shadow: 8px 12px 25px #6a6a6a17;
					.param-select-graph-row {
						.param-select-col {
							border-radius: 20px 0 0 20px;
							background: transparent
								linear-gradient(32deg, #ffffff 0%, #f1f9ff 100%)
								0% 0% no-repeat padding-box;
							.param-select-container {
								height: 605px;
								.heading {
									font-size: 16px;
									font-weight: 600;
									padding: 0 20px;
									margin-top: 20px;
									color: #232323;
								}
								.param-type-select {
									margin-top: 10px;
									padding: 0 20px;
									.ant-select-arrow {
										right: 31px;
									}
									.ant-select-selector {
										border: none;
										.ant-select-selection-item {
											font-weight: 600;
											color: #232323;
										}
									}
								}
								.param-name-container {
									height: 505px;
									margin-top: 10px;
									overflow: auto;
									.param-name {
										display: flex;
										font-size: 14px;
										margin-top: 10px;
										cursor: pointer;
										padding: 10px 20px;
										color: #232323;
									}
									.param-name::before {
										display: inline-block;
										content: 'o ';
										color: #8ca5cf;
										margin-right: 10px;
										font-size: 12px;
									}
									.param-name.selected {
										color: #ff8500;
										background: #fff;
									}
								}
							}
						}
						.graph-section-col {
							.graph-section-container {
								padding: 20px;
								.graph-heading {
									margin-top: 10px;
									color: #ff8500;
									font-size: 14px;
									span {
										font-size: 16px;
										font-weight: 600;
										color: #232323;
										margin-left: 10px;
									}
								}
								.graph-render-div {
									padding: 20px;
									background: transparent
										linear-gradient(
											106deg,
											#ffffff 0%,
											#ffffff 100%
										)
										0% 0% no-repeat padding-box;
									box-shadow: 4px 8px 16px #6767671a;
									border-radius: 15px;
									margin-top: 30px;
									font-size: 14px;
									.heading {
										font-weight: 600;
									}
									.total-aggr-header {
										display: flex;
										align-items: center;
										justify-content: space-between;
									}
									.graph-container {
										margin-top: 20px;
									}
								}
							}
						}
					}
				}
			}
			.fault-section {
				.fault-param-container {
					background: transparent
						linear-gradient(36deg, #ffffff 0%, #f1f9ff 100%) 0% 0%
						no-repeat padding-box;
					box-shadow: 8px 12px 25px #0000000f;
					border-radius: 15px;
					padding: 10px 0;
					border: 2px solid #ffffff;
					.heading {
						padding: 0 20px;
						font-size: 16px;
						color: #232323;
						font-weight: 600;
					}
					.fault-toggle-button {
						display: flex;
						align-items: center;
						justify-content: space-between;
						padding: 0 20px;
						margin-top: 10px;
						.fault-toggle-label {
							color: #333863;
							font-style: italic;
						}
						.ant-switch {
							background: #fff;
							box-shadow: 0px 0px 10px #3b3b3b17;
							.ant-switch-handle::before {
								background: #808080;
							}
						}
						&.fault-toggle-active {
							.ant-switch {
								.ant-switch-handle::before {
									background: #6e71a1;
								}
							}
						}
					}
					.param-container {
						padding: 0 20px;
						margin-top: 10px;
						//height: 567px;
						height: 536px;
						overflow: auto;
						overflow-x: hidden;
						position: relative;
						.no-fault-img {
							position: absolute;
							top: 50%;
							left: 50%;
							transform: translate(-50%, -50%);
						}
						.caption {
							color: #808080;
							font-size: 14px;
						}
						.icon-caption {
							margin-top: 20px;
							display: flex;
							justify-content: space-between;
							.caption {
								width: 150px;
							}
							.status-false {
								width: 19px;
								height: 19px;
								border-radius: 50%;
								background: #8d8d8d24;
							}
						}
					}
				}
			}
		}
		.trip-daily-row {
			padding: 0 30px;
			width: auto;
			margin-top: 40px;
			.trip-col {
				.trip-details-container {
					padding: 0 5px;
					border: 2px solid #ffffff;
					background: transparent
						linear-gradient(140deg, #ffffff 0%, #f6fbff 100%) 0% 0%
						no-repeat padding-box;
					box-shadow: 8px 12px 25px #b6c1cf33;
					border-radius: 18px;
					&.no-fuel-trip-details {
						height: 100%;
					}
					.trip-header {
						padding: 20px;
						padding-bottom: 10px;
						.trip-title {
							font-weight: 600;
							color: #232323;
							font-size: 16px;
						}
						.trip-date {
							margin-top: 10px;
							color: #808080;
							font-size: 12px;
						}
					}
					.trip-param-container {
						padding: 0 20px 20px 20px;
						height: 337px;
						overflow: auto;
						&.no-fuel-trip {
							height: unset;
						}
						.trip-param-row {
							width: auto;
							margin-top: 15px;
							.trip-param-name {
								font-size: 13px;
								color: #808080;
								display: flex;
							}
							.trip-param-name::before {
								/* add the new bullet point */
								display: inline-block;
								content: 'o ';
								color: #8ca5cf;
								margin-right: 10px;
								font-size: 12px;
							}
							.trip-param-value {
								padding-left: 30px;
								color: #232323;
								text-align: right;
								font-size: 14px;
								font-weight: 600;
							}
						}
					}
				}
			}
			.daily-col {
				.critical-kpi {
					border: 2px solid #ffffff;
					background: transparent
						linear-gradient(112deg, #ffffff 0%, #ffffff 100%) 0% 0%
						no-repeat padding-box;
					box-shadow: 8px 12px 25px #b6c1cf33;
					padding: 10px 20px;
					border-radius: 18px;
					.critical-kpi-head {
						display: flex;
						margin-left: 5px;
						align-items: center;
						justify-content: space-between;
						.critical-kpi-title {
							font-weight: 600;
							font-size: 16px;
						}
					}
					.critical-kpi-data-row {
						width: auto;
						height: 345px;
						margin-top: 20px;
						&.no-fuel-critical {
							height: 174px;
							position: relative;
							.critical-param-col .critical-param-div {
								height: auto;
							}
						}
						.ant-spin {
							transform: translate(-50%, -50%) !important;
							top: 50% !important;
							left: 50% !important;
							position: absolute !important;
						}
						.critical-param-col {
							.critical-param-div {
								background: transparent
									linear-gradient(
										181deg,
										#ffffff 0%,
										#d7dbe850 71%,
										#ffffff 97%
									)
									0% 0% no-repeat padding-box;
								border-radius: 18px;
								height: 322px;
								padding: 20px;
								.critical-param-row {
									width: auto;
									margin-bottom: 20px;
									.critical-param-value {
										color: #232323;
										text-align: left;
										font-size: 14px;
										font-weight: 600;
									}
								}
							}
						}
						.kpi-graph-title {
							font-size: 12px;
							margin-bottom: 30px;
							margin-left: 10px;
							color: #374375;
						}
					}
				}
			}
		}
		.raw-data-row {
			width: auto;
			padding: 0 30px;
			margin-top: 40px;
			.real-time-panel-col {
				.real-time-panel-container {
					padding: 20px 0px;
					background: transparent
						linear-gradient(152deg, #ffffff 0%, #f6fbff 100%) 0% 0%
						no-repeat padding-box;
					box-shadow: 8px 12px 25px #b6c1cf33;
					border-radius: 18px;
					border: 2px solid #ffffff;
					.panel-header {
						padding-left: 25px;
						padding-bottom: 10px;
						.panel-title {
							font-weight: 600;
							color: #232323;
							font-size: 14px;
						}
						.panel-date {
							color: #808080;
							margin-top: 10px;
							font-size: 12px;
						}
					}
					.view-with-data {
						height: 500px;
						overflow: auto;
						margin-top: 20px;
						padding: 0px 25px;
						.total-value-caption {
							margin-bottom: 10px;
							.caption-class::before {
								display: inline-block;
								content: 'o ';
								color: #8ca5cf;
								font-size: 12px;
								margin-right: 10px;
							}
							.caption-class {
								display: flex;
								color: #808080 !important;
								font-size: 14px !important;
							}
							.value-class {
								color: #232323 !important;
								font-weight: 600 !important;
							}
						}
					}
				}
			}
			.raw-aggreagted-trend-col {
				.raw-aggreagted-trend-container {
					background: transparent
						linear-gradient(123deg, #ffffff 0%, #f8fbfe 100%) 0% 0%
						no-repeat padding-box;
					box-shadow: 8px 12px 25px #b6c1cf33;
					border-radius: 18px;
					padding: 20px 30px;
					border: 2px solid #fff;
					.section-header {
						display: flex;
						justify-content: space-between;
						.title-with-btn {
							display: flex;
							align-items: center;
							.header-title {
								font-weight: 600;
								font-size: 14px;
							}
						}
					}
					.button-tab {
						.ant-tabs-nav {
							position: absolute;
							margin-top: -25px;
							left: 20%;
							.ant-tabs-tab {
								border-radius: 5px;
								font-size: 13px;
								height: 30px;
								background-color: transparent;
								color: #232323;
								margin-right: 20px;
								border: 1px solid #f1f3f8;
								.ant-tabs-tab-btn {
									color: #232323;
								}
							}
							.ant-tabs-tab-active {
								background: #ff8500 !important;
								color: #fff;
								font-weight: 600;
								box-shadow: 8px 12px 25px #b6c1cf33;
								.ant-tabs-tab-btn {
									color: #ffffff;
								}
							}
							.ant-tabs-ink-bar {
								display: none;
							}
						}
					}
					.graph-total-row {
						margin-right: 0px !important;
						width: auto;
						height: 539px;
						overflow: auto;
						margin-top: 20px;
						.graph-col-container {
							margin-top: 20px;
							.graph-inner-container {
								padding: 10px;
								background: transparent
									linear-gradient(
										180deg,
										#ecf4fc 0%,
										#ffffff00 80%
									)
									0% 0% no-repeat padding-box;
								border-radius: 18px;
								.graph-heading {
									font-size: 12px;
									font-weight: 600;
									color: #232323;
									margin-left: 5px;
									margin-bottom: 20px;
								}
							}
						}
					}
					.graph-total-row::-webkit-scrollbar-thumb {
						background-color: #dfdfdf50;
					}
				}
			}
		}
	}
}
.dg-detailed-view-banner {
	height: calc(100% - 185px) !important;
}
.detailed-view-layout {
	.things-drawer.ant-drawer-open {
		.ant-drawer-content-wrapper {
			.ant-drawer-content {
				.ant-drawer-body {
					padding: 0px !important;
					.search-container {
						margin: 10px 0;
						.search-input {
							width: 100% !important;
							font-size: 13px;
							background-color: transparent;
							.ant-input-prefix {
								left: 230px !important;
								width: 14px;
								height: 14px;
							}
						}
						.ant-input-affix-wrapper {
							.ant-input:hover {
								border: none;
								border: 1px solid #81818177;
								border-radius: 16px;
								padding-left: 20px;
							}
							.ant-input {
								width: 100% !important;
								padding-left: 20px;
								font-size: 13px;
								font-style: italic;
								color: #232323;
								border-radius: 0px;
								padding-bottom: 5px;
								box-shadow: none;
								border-radius: 16px;
								border: 1px solid #81818177;
								width: 100%;
							}
							.ant-input:focus {
								border: none;
								border: 1px solid #ff8500;
							}
						}
						// .ant-input-affix-wrapper-focused {
						// 	.ant-input-prefix {
						// 		display: none;
						// 	}
						// }
					}
					.things-details {
						padding: 10px 30px !important;
						cursor: pointer;
						padding: 5px;
						border-bottom: 1px solid rgba(227, 227, 227, 0.459);
						.things-name {
							text-align: left;
							font-size: 14px;
							letter-spacing: 0px;
							color: #808080;
							opacity: 1;
						}
						.status-shown-div {
							text-align: left;
							font-size: 12px;
							letter-spacing: 0px;
							opacity: 1;
						}
						.off-thing {
							color: #ff4904;
						}
						.running-thing {
							color: #25c479;
						}
					}
					.selected-thing {
						background: #c4c2c23f;
						.things-name {
							font-weight: 600;
							color: #232323;
						}
					}
					.no-search {
						.no-data-icon {
							.text {
								color: #c7b8a8;
								margin-top: 20px;
								font-style: italic;
							}
						}
					}
				}
			}
		}
	}
}

@media (max-width: 1536px) {
	#dg_detailed_view_generic {
		.inner-div {
			padding: 10px;
			.detailed-view-header {
				padding: 0 10px;
				margin-top: 0px;
			}
			.genset-status-container {
				padding: 0 10px;
			}
			.total-row {
				margin-top: 15px;
				padding: 0 10px;
				.panel-trip-col {
					.panel-total-container {
						padding: 15px 10px;

						.fuel-tank-estimated-total-row {
							.fuel-tank-col {
								.icon-tank-whole-div {
									width: 70px;
								}
							}
						}
						.panel-param-row {
							margin-top: 26px;
							.panel-param-col {
								margin-top: 25px;
								.param-div {
									padding: 10px;
									.panel-param-value {
										font-size: 14px;
										img {
											width: 23px;
											height: 19px;
										}
									}
								}
							}
						}
					}
				}
				.rnhr-fuel-enrg-col {
					.graph-data-row {
						.heading {
							font-size: 16px;
						}
						.rnhr-fuel-enrg-data-col {
							.today-container {
								.today-data {
									.today-param-name {
										font-size: 13px;
									}
								}
							}
						}
						.rnhr-fuel-enrg-graph-col {
							margin-top: 30px;
						}
					}
				}
				.activity-col {
					.tab-total-container {
						.notification-tab {
							height: 409px;
						}
					}
					&.dg_only_fuel_activity {
						.tab-total-container {
							.notification-tab {
								height: 280px;
							}
						}
					}
					&.dg_without_fuel_activity {
						.tab-total-container {
							.notification-tab {
								height: 290px;
							}
						}
					}
				}
			}
			.param-trend-section-row {
				padding: 0 10px;
				margin-top: 20px;
				&.only-fuel-middle-section-row {
					margin-top: 0;
				}
			}
			.raw-data-row {
				margin-top: 15px;
				padding: 0 10px;
				.real-time-panel-col {
					.real-time-panel-container {
						.view-with-data {
							.total-value-caption {
								.caption-class {
									font-size: 13px !important;
								}
							}
						}
					}
				}
				.raw-aggreagted-trend-col {
					.raw-aggreagted-trend-container {
						.section-header {
							.title-with-btn {
								.header-title {
									font-size: 12px;
								}
							}
						}
					}
				}
			}
			.trip-daily-row {
				margin-top: 15px;
				padding: 0 10px;
			}
		}
	}
}

@media (max-width: 1366px) {
	#dg_detailed_view_generic {
		.inner-div {
			.total-row {
				.panel-trip-col {
					.panel-total-container {
						.panel-param-row {
							margin-top: 5px;
							.panel-param-col {
								margin-top: 23px;
							}
						}
					}
				}
				.activity-col {
					.tab-total-container {
						.notification-tab {
							height: 383px;
						}
					}
					&.dg_only_fuel_activity {
						.tab-total-container {
							.notification-tab {
								height: 280px;
							}
						}
					}
					&.dg_without_fuel_activity {
						.tab-total-container {
							.notification-tab {
								height: 290px;
							}
						}
					}
				}
				.rnhr-fuel-enrg-col {
					.graph-data-row {
						.rnhr-fuel-enrg-data-col {
							.today-container {
								.today-data {
									.today-param-name {
										font-size: 11px;
									}
								}
							}
							.graph-container {
								margin-bottom: 10px;
							}
						}
					}
				}
			}
		}
	}
}

@media (max-width: 1280px) {
	#dg_detailed_view_generic {
		.inner-div {
			.raw-data-row {
				.real-time-panel-col {
					.real-time-panel-container {
						.view-with-data {
							.total-value-caption {
								.caption-class {
									font-size: 12px !important;
								}
								.value-class {
									font-size: 13px;
								}
							}
						}
					}
				}
			}
			.trip-daily-row {
				.daily-col {
					.critical-kpi {
						.critical-kpi-data-row {
							.critical-param-col {
								.critical-param-div {
									padding: 19px;
								}
							}
						}
					}
				}
				.tab-total-container {
					.notification-tab {
						height: 317px;
					}
				}
			}
		}
	}
}

@media (max-width: 1024px) {
	#dg_detailed_view_generic {
		.inner-div {
			.detailed-view-header {
				.thing-details-container {
					.make-model-lifetime-div {
						margin-left: 20px;
						.make-model-div {
							.model-div {
								padding: 0 10px;
							}
							.energy-div {
								padding: 0 10px;
							}
						}
					}
				}
			}
			.total-row {
				.panel-trip-col {
					.panel-total-container {
						.panel-param-row {
							.panel-param-col {
								margin-top: 20px;
								.param-div {
									padding: 14px 10px;
								}
							}
						}
					}
				}
			}
			.param-trend-section-row
				.fault-section
				.fault-param-container
				.param-container {
				//height: 335px;
				height: 304px;
			}
			.raw-data-row {
				.raw-aggreagted-trend-col {
					margin-top: 20px;
				}
				.real-time-panel-col {
					.real-time-panel-container {
						.view-with-data {
							height: 277px !important;
							.total-value-caption {
								.caption-class {
									font-size: 13px !important;
								}
							}
						}
					}
				}
			}
			.trip-daily-row {
				.trip-col {
					.trip-details-container {
						.trip-param-container {
							height: 307px;
						}
					}
				}
				.daily-col {
					margin-top: 20px;
				}
				.tab-total-container {
					.notification-tab {
						height: 320px;
					}
				}
			}
		}
	}
}

@media (max-width: 960px) {
	#dg_detailed_view_generic {
		.inner-div {
			.detailed-view-header {
				padding: 0 20px;
				margin-top: 10px;
			}
			.genset-status-container {
				padding: 0 70px;
			}
			.total-row {
				padding: 0 70px;
				.panel-trip-col {
					.panel-total-container {
						padding: 20px 120px;
						.panel-param-row {
							.panel-param-col {
								margin-top: 20px;
								.param-div {
									padding: 10px;
									.panel-param-value {
										font-size: 14px;
									}
									.panel-param-name {
										font-size: 11px;
									}
								}
							}
						}
						.fuel-tank-estimated-total-row
							.estimated-fuel-filled-col
							.total-estimated-rnhr {
							display: flex;
							margin-left: 0px;
							.total-div {
								margin: 0 30px;
							}
							.fuel-value-date {
								.total-div {
									margin: 0px;
								}
								margin-top: 0px;
							}
						}
					}
				}
				.rnhr-fuel-enrg-col {
					margin-top: 20px;
					.graph-data-row {
						.rnhr-fuel-enrg-data-col {
							.today-container {
								.today-data {
									.today-param-name {
										font-size: 13px;
									}
								}
							}
						}
					}
				}
			}
			.raw-data-row {
				padding: 0px;
				margin-top: 40px;
				.parameter-toggle {
					text-align: center;
					.param-btn {
						border: none;
						border-bottom: 1px solid #c4c2c288;
						background: transparent;
						border-radius: 0px;
						font-size: 13px;
						padding: 0 50px;
					}
					.selected-btn {
						color: #232323;
						font-weight: bold;
						border-bottom: 2px solid #ff8a14;
					}
				}
				.real-time-panel-col {
					margin-top: 40px;
					padding: 0 291px;
					.real-time-panel-container {
						.view-with-data {
							margin-top: 10px;
							height: 387px !important;
							.total-value-caption {
								margin-bottom: 10px;
							}
						}
						.panel-header {
							border: none;
							.panel-date {
								font-size: 11px;
							}
						}
					}
				}
				.raw-aggreagted-trend-col {
					margin-top: 40px;
					padding: 0 60px;
					.raw-aggreagted-trend-container {
						.graph-total-row {
							height: 456px;
						}
						.button-tab .ant-tabs-nav {
							left: 30%;
							.ant-tabs-tab {
								margin-right: 10px;
							}
						}
					}
				}
			}
			.trip-daily-row {
				padding: 0 10px;
				margin-top: 20px;
				.daily-col {
					.critical-kpi {
						padding: 20px 30px;
						.critical-kpi-data-row {
							height: 445px;
							&.no-fuel-critical {
								height: 210px;
							}
							.critical-param-col {
								.critical-param-div {
									height: 127px;
									padding: 10px 20px;
									.critical-param-row {
										margin-bottom: 0px;
										.mobile-critical-param {
											margin-top: 15px;
										}
										.critical-param-value {
											font-size: 14px;
											padding-left: 20px;
										}
									}
								}
							}
						}
					}
				}
				.trip-col {
					.trip-details-container {
						.trip-param-container {
							.trip-param-row {
								.trip-param-value {
									text-align: right;
								}
							}
						}
					}
				}
				.activity-col {
					.tab-total-container {
						padding: 5px !important;
						border-radius: 11px;
						.notification-tab {
							height: 330px;
						}
					}
				}
			}
		}
	}
}

@media (max-width: 800px) {
	#dg_detailed_view_generic {
		.inner-div {
			.detailed-view-header {
				display: block;
				padding: 0 10px;
				margin-top: 10px;
				.status-toggle {
					margin-top: 15px;
				}
			}
			.genset-status-container {
				padding: 0 10px;
			}
			.total-row {
				padding: 0 10px;
			}
			.trip-daily-row {
				padding: 0 10px;
			}
			.raw-data-row {
				.real-time-panel-col {
					padding: 0 211px;
				}
				.raw-aggreagted-trend-col {
					padding: 0 20px;
				}
			}
		}
	}
}

@media (max-width: 767px) {
	#dg_detailed_view_generic {
		.inner-div {
			.detailed-view-header {
				padding: 0 10px;
				align-items: baseline;
				.status-toggle .status-shown-div {
					margin-right: 10px;
				}
				.thing-details-container {
					display: block;
					.make-model-lifetime-div {
						margin-left: 40px;
						margin-top: 10px;
					}
				}
			}
			.genset-status-container {
				padding: 0 10px;
			}
			.total-row {
				padding: 0 10px;
				.panel-trip-col .panel-total-container {
					padding: 20px 85px;
				}
				.rnhr-fuel-enrg-col {
					padding-left: 0px !important;
					padding-right: 0px !important;
					.graph-data-row {
						.rnhr-fuel-enrg-data-col {
							.today-container {
								.today-data {
									.today-param-name {
										font-size: 12px;
									}
								}
							}
						}
					}
				}
			}
			.param-trend-section-row {
				.graph-section {
					.down-section {
						.param-select-graph-row {
							padding: 30px;
							.param-select-col {
								.ant-select {
									width: 100%;
									.ant-select-selector {
										color: #ff8500;
									}
								}
							}
							.graph-section-col {
								height: 530px;
								.graph-section-container {
									padding: 0px;
									.graph-heading {
										display: none;
									}
									.graph-render-div {
										margin-top: 20px;
									}
								}
							}
						}
					}
				}
			}
			.trip-daily-row {
				padding: 0 10px;
				.trip-col {
					padding: 0 170px;
					.trip-details-container {
						.trip-param-container {
							height: 315px;
							&.no-fuel-trip {
								height: unset;
							}
						}
					}
				}
				.tab-total-container {
					.notification-tab {
						height: 328px;
					}
				}
				.daily-col {
					padding: 0px;
					.critical-kpi {
						.critical-kpi-data-row {
							height: 599px;
							.critical-param-col {
								.critical-param-div {
									.critical-param-row {
										.critical-param-value {
											padding-left: 15px;
										}
									}
								}
							}
						}
					}
				}
			}
			.raw-data-row {
				.real-time-panel-col {
					padding: 0 171px;
				}
			}
		}
	}
}

@media (max-width: 600px) {
	#dg_detailed_view_generic {
		.inner-div {
			.detailed-view-header {
				padding: 0 25px;
				.thing-details-container {
					.make-model-lifetime-div {
						width: 100%;
					}
					.operation-mode {
						margin-top: 10px;
					}
				}
			}
			.genset-status-container {
				padding: 0 20px;
			}
			.total-row {
				padding: 0 20px;
				.panel-trip-col {
					.panel-total-container {
						padding: 20px 35px;
						.fuel-tank-estimated-total-row
							.estimated-fuel-filled-col
							.total-estimated-rnhr {
							margin-left: 0px;
							.total-div {
								margin: 0 28px;
							}
						}
					}
				}
			}
			.trip-daily-row .trip-col {
				padding: 0 130px;
				.trip-details-container .trip-param-container {
					height: 330px;
				}
			}
		}
	}
}

@media (max-width: 576px) {
	#dg_detailed_view_generic {
		height: calc(100% - 115px);
		.inner-div {
			.detailed-view-header {
				padding: 20px;
				background: transparent
					linear-gradient(110deg, #ffffffbd 0%, #f5e7d930 100%) 0% 0%
					no-repeat padding-box;
				.status-toggle {
					margin-left: 40px;
					margin-top: 10px;
				}
				.thing-details-container {
					.make-model-lifetime-div {
						font-size: 12px;
						.mobile-kva-lifetime {
							display: flex;
							align-items: center;
							.lifetime-div {
								margin-left: 20px;
							}
						}
					}
				}
			}
			.genset-status-container {
				padding: 10px;
			}
			.total-row {
				padding: 10px;
				margin-bottom: 30px;
				.panel-trip-col .panel-total-container {
					padding: 15px;
					.fuel-tank-estimated-total-row
						.estimated-fuel-filled-col
						.total-estimated-rnhr {
						display: block;
						text-align: right;
						.total-div {
							margin: 0px;
						}
						.fuel-value-date {
							margin-top: 20px;
						}
					}
				}
				.kpi-section {
					margin-top: 40px;
					border: none;
					border-radius: 20px;
					box-shadow: 1px 2px 3px 1px #00000010;
					.ant-collapse-item-active {
						.ant-collapse-header {
							border-radius: 20px;
							box-shadow: 1px 2px 3px 1px #00000010;
						}
					}
					.ant-collapse-item {
						border-radius: 20px;
						border: none;
						background: #fff;
						.ant-collapse-header {
							padding-right: 0px;
							text-align: center;
							font-weight: 600;
							.total-kpi-header {
								text-align: center;
							}
							.ant-collapse-extra {
								float: left;
								position: absolute;
								bottom: 20px;
								img {
									width: 30px;
									height: 30px;
								}
							}
						}
						.ant-collapse-content {
							border: none;
							margin-top: 10px;
							.ant-collapse-content-box {
								padding: 0px;
								.rnhr-fuel-enrg-col {
									padding-left: 0px !important;
									padding-right: 0px !important;
									margin-top: 0px;
									.graph-data-row {
										height: 590px;
										.heading {
											display: none;
										}
										.rnhr-fuel-enrg-data-col {
											margin-top: 0px;
											.today-container {
												.today-data {
													margin-top: 20px;
													margin-left: 0px;
													.today-param-name {
														text-align: left;
														margin-top: 0px;
														border: none;
														display: flex;
													}
													.today-yesterday-col {
														padding: 0px;
														.today-data-val {
															font-size: 14px;
															display: flex;
															//justify-content: space-between;
															justify-content: flex-end;
															color: #374375;
														}
														.yesterday-data-val {
															font-size: 12px;
															font-weight: normal;
															margin-left: 0px;
															margin-top: 0px;
															align-items: center;
															//justify-content: flex-start;
															justify-content: flex-end;
														}
													}
												}
											}
										}
									}
								}
								.raw-data-row {
									margin-top: 0px;
									padding: 0px;
									.parameter-toggle {
										.param-btn {
											padding: 0px 10px;
										}
									}
									.real-time-panel-col {
										padding: 0px;
										margin-top: 0px;
										.real-time-panel-container {
											.panel-header {
												padding-left: 20px;
												.panel-date {
													margin-top: 10px;
												}
											}
											.view-with-data {
												padding: 0 20px;
												.total-value-caption {
													.value-class {
														font-size: 14px !important;
													}
												}
											}
										}
									}
									.raw-aggreagted-trend-col {
										.raw-aggreagted-trend-container {
											.section-header {
												.title-with-btn {
													.ant-btn:nth-of-type(2) {
														float: none;
														margin-top: 10px;
													}
												}
												.header-date {
													margin-top: 10px;
												}
											}
										}
									}
								}
								.param-trend-section-row {
									margin-top: 0px;
									padding: 0px;
									.fault-section {
										.fault-param-container {
											background: transparent
												linear-gradient(
													134deg,
													#ffffff 0%,
													#f6fbff 100%
												)
												0% 0% no-repeat padding-box;
											.heading {
												display: none;
											}
										}
									}
								}
								.trip-daily-row {
									padding: 0px;
									margin-top: 0px;
									.daily-col {
										margin-top: 0px;
										.critical-kpi {
											padding: 0px;
											margin-top: 20px;
											.critical-kpi-head {
												display: block;
												text-align: center;
												.critical-kpi-title {
													font-size: 13px;
													color: #232323;
													font-weight: 600;
												}
												.critical-kpi-date {
													margin-top: 5px;
													.ant-select-selector {
														.ant-select-selection-item {
															font-size: 12px;
														}
													}
												}
											}
											.critical-kpi-data-row {
												height: 660px;
												&.no-fuel-critical {
													height: 282px;
												}
												.graph-container {
													margin-top: 10px;
												}
											}
										}
									}
									.activity-col {
										padding: 0px !important;
										margin-top: 0px;
										padding: 0px;
										.tab-total-container {
											border-top: 0px;
											background: transparent
												linear-gradient(
													328deg,
													#ff7171 0%,
													#ffc48f 100%
												)
												0% 0% no-repeat padding-box;
											.notification-tab {
												height: 490px;
												.tab-data-container {
													.event-container {
														display: flex;
														align-items: center;
														width: 100%;
														justify-content: space-between;
														.thing-name {
															color: transparent;
															span {
																color: #232323;
															}
														}
														.event-date-container {
															.event-name {
																display: flex;
																font-size: 12px;
																font-style: italic;
															}
															.event-name::before {
																content: '~';
															}
														}
													}
												}
											}
										}
									}
									.trip-col {
										padding: 0px !important;
										.trip-details-container {
											.trip-header {
												padding-top: 10px;
												.trip-title {
													display: none;
												}
											}
										}
									}
								}

								.search-container .ant-input-affix-wrapper .ant-input-suffix .ant-input-clear-icon {
									margin-top: 35% !important;
								}
							}
						}
					}
				}
			}
		}
	}
}