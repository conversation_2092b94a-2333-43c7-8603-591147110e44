import _find from 'lodash/find';
import moment from 'moment-timezone';
import { activefaultViolationArray } from '../../../logic/activeFaultAndViolation';
import FaultIcon from '../../../images/Group 8159.svg';
import FaultIconOffline from '../../../images/Group 8162.svg';
import NoFaultIcon from '../../../images/Group 8164.svg';
import MaintenanceImg from '../../../images/maintenance.svg';
import MaintenanceOffLineImg from '../../../images/Group 8163.svg';
import { aaqmsImage } from '../../../logic/getLocalImageOfAAQMS';
import { solarPumpMachineInfo } from '../../../configs/SolarPumpConfig';

export function headerdataFunction() {
    let selectedThinglatestParam = this.latestDataFunc();
    let headObj = JSON.parse(JSON.stringify(this.state.HeaderObject));
    let findThingList = _find(this.state.totalData.things, {
        id: parseInt(this.state.thingId),
    });
    let thingDetailsArray = [];
    let findCat = _find(this.state.totalData.things_categories, {
        id: findThingList.category,
    });
    if (findCat) {
        thingDetailsArray.push({
            key: 'category',
            name: '',
            value: findCat.name,
        });
    }
    if (findThingList?.thing_details) {
        let thingDetailsKeys = Object.keys(findThingList.thing_details);
        const keys = [
            { key: 'make', name: 'Make: ' },
            { key: 'model', name: 'Model: ' },
            { key: 'serial_no', name: 'Serial No: ' },
            { key: "serial", name: "Serial Number: " },
            { key: 'kva', name: 'KVA: ' },
            { key: 'system_capacity', name: `System Capacity (${findThingList.thing_details.power_unit}): ` },
            { key: 'system_total_voltage', name: 'System Total Voltage (V): ' },
        ];
        if (findThingList.category === 103) {
            keys.push(...solarPumpMachineInfo);
        }
        
        keys.forEach((keyObj) => {
            if (thingDetailsKeys.includes(keyObj.key)) {
                thingDetailsArray.push({
                    key: keyObj.key,
                    name: keyObj.key === 'system_capacity' && findThingList.thing_details['capacity_unit'] ?
                    `${keyObj.name} (${findThingList.thing_details['capacity_unit']}): `
                    : keyObj.name,
                    value: findThingList.thing_details[keyObj.key],
                });
            }
        });
    }
    headObj.thing_details = thingDetailsArray;
    let lastFaultData = activefaultViolationArray(
        findThingList,
        selectedThinglatestParam,
        this.state.totalVaiolations
    );
    headObj.last_fault_data = {
        icon:
            lastFaultData && lastFaultData.length
                ? selectedThinglatestParam?.on_off_moving_status === 2
                    ? FaultIconOffline
                    : FaultIcon
                : NoFaultIcon,
        faults: lastFaultData,
    };
    headObj.maintenance = {
        icon:
            selectedThinglatestParam?.on_off_moving_status === 2
                ? MaintenanceOffLineImg
                : MaintenanceImg,
        text: this.getMaintenanceText(),
    };
    if (findThingList && findThingList.thing_details) {
        headObj.thing_category_icon = aaqmsImage(
            findThingList,
            this.props.vendor_id
        );
        if (findThingList.thing_details.make) {
            headObj.make = this.state.makeDetails?.[
                findThingList.thing_details.make
            ]
                ? this.state.makeDetails?.[findThingList.thing_details.make]
                : findThingList.thing_details.make;
        }
        if (findThingList.thing_details.model) {
            headObj.model = findThingList.thing_details.model;
        }
        if (findThingList.thing_details.kva) {
            headObj.energyRating =
                findThingList.thing_details.kva + ' ' + 'KVA';
        }
    }
    let totalDrawerArray = [];
    if (Array.isArray(this.state.latestParameterData)) {
        this.state.latestParameterData.map((sortedThingsListByNameData) => {
            return totalDrawerArray.push({
                name: _find(this.state.modifiedResponse.things_list, {
                    id: sortedThingsListByNameData.thing_id,
                })
                    ? _find(this.state.modifiedResponse.things_list, {
                            id: sortedThingsListByNameData.thing_id,
                      }).name
                    : '',
                no_status: _find(this.state.totalData.things_categories, {
                    id: sortedThingsListByNameData.category,
                })?.no_status,
                status_option_includes_stopped: _find(
                    this.state.totalData.things_categories,
                    {
                        id: sortedThingsListByNameData.category,
                    }
                )?.status_options?.includes('Stopped'),
                onOffStatus:
                    sortedThingsListByNameData.on_off_moving_status,
                id: sortedThingsListByNameData.thing_id,
            });
        });
    }
    headObj.thingsList = totalDrawerArray;
    headObj.name = _find(this.state.modifiedResponse.things_list, {
        id: selectedThinglatestParam?.thing_id,
    })
        ? _find(this.state.modifiedResponse.things_list, {
                id: selectedThinglatestParam.thing_id,
          }).name
        : '';
    headObj.date =
        selectedThinglatestParam?.time === 0
            ? this.props.t ? this.props.t("no_data_received"): "No Data Received"
            : moment
                    .unix(selectedThinglatestParam?.time)
                    .format('DD MMM YYYY, HH:mm');
    headObj.no_status = findCat?.no_status;
    headObj.deviceStatus = findThingList?.devices?.[0]?.online_status === 1 ? 'online' : 'offline';
    headObj.status_option_includes_stopped =
        findCat?.status_options?.includes('Stopped');
    headObj.dgStatus = selectedThinglatestParam?.on_off_moving_status;
    headObj.cat = selectedThinglatestParam?.category;
    return headObj;
}