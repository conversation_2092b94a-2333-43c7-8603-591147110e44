import React from 'react';
import moment from 'moment-timezone';
import GenericFilter from '@datoms/react-components/src/components/GenericFilter/pages';
import CustomizeTable from '@datoms/react-components/src/components/CustomizeTable';
import DeleteOutlined from '@ant-design/icons/DeleteOutlined'
import AntButton from '@datoms/react-components/src/components/AntButton';
import AntModal from '@datoms/react-components/src/components/AntModal';
import AntPagination from '@datoms/react-components/src/components/AntPagination';
import AntPopover from '@datoms/react-components/src/components/AntPopover';
import PlusOutlined from '@ant-design/icons/PlusOutlined';
import SelectWithRangepicker from '@datoms/react-components/src/components/SelectWithRangepicker';
import SkeletonLoader from '@datoms/react-components/src/components/SkeletonLoader';
import ClockCircleOutlined from "@ant-design/icons/ClockCircleOutlined";
import {
	applyFilterSelect,
	fetchAPIData,
	getSelectedThings,
	getUrlBreak,
	onUrlChange,
	onRangeChange,
	filterData,
	setFilterConfig,
	onPopupScroll,
	getCustomerData,
	getSystemTemplates,
	getCategoryData,
	getCustomerList,
	getVendorList,
	getAssetList,
	clientVendorSearch, selectOnBlur, selectOnDropdownVisibleChange
} from './logic/filter';
import AntList from '@datoms/react-components/src/components/AntList';
import AntListItem from '@datoms/react-components/src/components/AntListItem';
import NoDataComponent from '@datoms/react-components/src/components/NoDataComponent';

import _find from 'lodash/find';
import { getBaseUrl } from '@datoms/js-utils/src/base-url-logic';
import AntTag from '@datoms/react-components/src/components/AntTag';
import AntTooltip from '@datoms/react-components/src/components/AntTooltip';
import ActionsOverlay from '@datoms/react-components/src/components/ActionsOverlay';
import {
	tableData,
	serviceData,
	onTableChange,
	deActivateTicket,
	openTicketDetails,
	getListQuery, checkUserAccess, updateCalibration
} from './logic/tableData';
import CalibrationDetails from './component/CalibrationDetails';
import './style.less';
import PlusCircleFilled from '@ant-design/icons/PlusCircleFilled';
import CalibrationWorkflowDrawer from './component/CalibrationWorkflowDrawer';
import AutoCalibrationAdd from './component/AutoCalibrationAdd';
import AutoCalibrationList from './component/AutoCalibrationList';
import { createService } from '@datoms/js-sdk';
import {openNotification} from "../../logic/CameraControlLogic";

function disabledDate(current) {
	return (
		(current &&
			current <
				moment()
					.startOf('Month')
					.subtract(6, 'month')
					.startOf('day')) ||
		current >= moment().endOf('day')
	);
}

export default class RemoteCalibration extends React.Component {
	calibrationWorkflowDrawerRef = React.createRef();
	constructor(props) {
		super(props);
		this.state = {
			thingCategoryOptions: [],
			loading: true,
			bodyLoading: true,
			filter_loaded:false,
			catWiseThingsOptions: {},
			customerFilterOptions:[],
			tagsFilterOptions:[],
			table_loading:false,
			vendorLoading:false,
			custLoading:false,
			cancel_calib_data:null,
			filterConfig:[...filterData],
			filter_values:{},
			from_time: moment().subtract(30, 'days').startOf('day').unix(),
			upto_time: moment().endOf('day').unix(),
			pageSize:20,
			sort_key:'',
			sort_order:'ASC',
			totalPage:0,
			isMobile:window.innerWidth<576,
			page:1,
			calibrationId: parseInt(
				new URLSearchParams(
					props.location?.search.split('?')?.[1],
				)?.get('id'),
			),
			current:
				new URLSearchParams(
					props.location?.search.split('?')?.[1],
				)?.get('page') || 1,
		};
		this.customerPage=1;
		this.vendorPage=1;
		this.thingsPage=1;
		this.applyFilterSelect = applyFilterSelect.bind(this);
		this.getUrlBreak = getUrlBreak.bind(this);
		this.setFilterConfig = setFilterConfig.bind(this);
		this.getCustomerData = getCustomerData.bind(this);
		this.getCustomerList = getCustomerList.bind(this);
		this.getVendorList = getVendorList.bind(this);
		this.onUrlChange = onUrlChange.bind(this);
		this.fetchAPIData = fetchAPIData.bind(this);
		this.deActivateTicket = deActivateTicket.bind(this);
		this.selectOnBlur = selectOnBlur.bind(this);
		this.getAssetList = getAssetList.bind(this);
		this.checkUserAccess = checkUserAccess.bind(this);
		this.onPopupScroll = onPopupScroll.bind(this);
		this.clientVendorSearch = clientVendorSearch.bind(this);
		this.selectOnDropdownVisibleChange = selectOnDropdownVisibleChange.bind(this);
		this.openTicketDetails = openTicketDetails.bind(this);
		this.getSystemTemplates = getSystemTemplates.bind(this);
		this.getSelectedThings = getSelectedThings.bind(this);
		this.getCategoryData = getCategoryData.bind(this);
		this.tableData = tableData.bind(this);
		this.updateCalibration = updateCalibration.bind(this);
		this.onTableChange = onTableChange.bind(this);
		this.serviceData = serviceData.bind(this);
		this.addBtnClicked = this.addBtnClicked.bind(this);
		this.getListQuery = getListQuery.bind(this);
		this.onRangeChange = onRangeChange.bind(this);
		this.columns=[
			{
				title: '#Ref ID',
				dataIndex: 'id',
				pdf_title:'#Ref ID',
				key: 'id',
				width: 100,
				render: (id, row_value) => (
					<div className={'fw-500 f-13 fc-80 pointer'} onClick={()=>this.openTicketDetails(id)}>{id}</div>
				),
			},{
				title: 'Customer',
				dataIndex: 'customer_id',
				pdf_title:'Customer',
				sorter_key:'customer_name',
				sorter:true,
				key: 'customer_id',
				width: 200,
				render: (customer_id, row_value) => (
					<div className="flex_column"  onClick={()=>this.openTicketDetails(row_value.id)}>
						<span className={'fw-500 f-13 fc_00 pointer'}>{row_value.customer_name}</span>
						{
							this.props.application_id==12?(
								<span className={'fw-400 f-13 fc_80 pointer'}>{row_value?.vendor_name}</span>
							):""
						}
					</div>
				),
			},{
				title: 'Asset',
				dataIndex: 'entity_name',
				pdf_title:'Asset',
				sorter_key:'entity_name',
				sorter:true,
				key: 'entity_name',
				width: 200,
				render: (entity_name, row_value) => (
					<div className="flex_column" onClick={()=>this.openTicketDetails(row_value.id)}>
						<span className={'fw-500 f-13 fc_00 pointer'}>{entity_name}</span>
						<span className={'fw-400 f-13 fc_80 pointer'}>{row_value?.thing_category}</span>
					</div>
				),
			},{
				title: 'Calibration Type',
				dataIndex: 'sub_type',
				pdf_title:'Calibration Type',
				key: 'sub_type',
				sorter_key:'sub_type',
				sorter:true,
				width: 200,
				render: (sub_type, row_value) => (
					<div className={'fw-400 f-13 fc_23'} onClick={()=>this.openTicketDetails(row_value.id)}>{sub_type} {row_value.auto_scheduled ? <AntTag style={{marginLeft: 6}} color="purple">Auto</AntTag> : null}</div>
				),
			},{
				title: 'Parameter',
				dataIndex: 'parameter',
				pdf_title:'Parameter',
				key: 'parameter',
				width: 200,
				render: (parameter, row_value) => (
					parameter.map((tagValue) => {
						return (
							<AntTooltip title={tagValue} key={tagValue}>
								<AntTag
									style={{
										color: '#232323',
										backgroundColor: '#D9D9D987',
										border:'none',
										borderRadius:4,
										fontWeight:400,
										fontSize:13,
										height:24,
										margin:5
									}}
									onClick={()=>this.openTicketDetails(row_value.id)}
								>
									{tagValue}
								</AntTag>
							</AntTooltip>
						);
					})
				),
			},{
				title: 'Created On',
				dataIndex: 'generated_at',
				pdf_title:'Created On',
				key: 'generated_at',
				sorter_key:'generated_at',
				sorter:true,
				width: 200,
				render: (generated_at, row_value) => (
					<div className={'fw-400 f-13 fc_80'} onClick={()=>this.openTicketDetails(row_value.id)}>{generated_at}</div>
				),
			},{
				title: 'Status',
				dataIndex: 'status',
				pdf_title:'Status',
				key: 'status',
				sorter_key:'status',
				sorter:true,
				width: 200,
				render: (status, row_value) => (
					<div className={`status_tag ${status}`} onClick={()=>this.openTicketDetails(row_value.id)}>{status}</div>
				),
			},{
				title: 'Calibrated by',
				dataIndex: 'calibrated_by',
				pdf_title:'Calibrated by',
				key: 'calibrated_by',
				width: 200,
				render: (calibrated_by, row_value) => (
					<div className={'flex_column'} onClick={()=>this.openTicketDetails(row_value.id)}>
						{
							calibrated_by ? (
								<>
									<span className={'fw-400 f-13 fc_00'}>{calibrated_by}</span>
									{row_value?.start_time ? (
										<span className={'fw-400 f-11 fc_80'}>at {row_value.start_time}</span>
									) : ""}
								</>

							) : ""
						}

					</div>
				),
			}, {
				title: 'Duration (HH:MM:SS)',
				dataIndex: 'duration',
				pdf_title:'Duration (HH:MM:SS)',
				key: 'duration',
				sorter_key:'duration',
				sorter:true,
				align: 'center',
				width: 150,
				render: (duration, row_value) => (
					<div className={'fw-400 f-13 fc_00'} onClick={()=>this.openTicketDetails(row_value.id)}>{duration}</div>
				),
			},{
				title: 'Tags',
				dataIndex: 'tags',
				pdf_title:'Tags',
				key: 'tags',
				align: 'center',
				width: 200,
				render: (tags, row_value) => (
					tags.map((tagValue) => {
						return (
							<AntTag
								color={tagValue.color}
								onClick={()=>this.openTicketDetails(row_value.id)}
							>
								{tagValue.name}
							</AntTag>
						);
					})
				),
			}, {
				title: '',
				key: 'action',
				pdf_title: 'Action',
				width: 200,
				align: 'center',
				render: (action,row_data) => {
					let that=this
					return (
						<ActionsOverlay
						onClick={()=>{
							console.log("clicked")
						}}
						menuItems={[{
							label: 'Cancel',
							disabled: row_data.status_code!==0 || !this.checkUserAccess()?.manage,
							action: ()=>that.setState({cancel_calib_data:row_data})
						}]}
					/>)
				}
			}
		]
		if (![12,17].includes(parseInt(this.props.application_id))) {
			this.columns = this.columns.filter(
				(item) =>
					item.key !== 'customer_id'
			);
		}
	}
	isFilter() {
		return this.props.location?.search?.split('filter=')?.[1];
	}
	handlePageChange(newPagination) {
		const { history, location } = this.props;
		this.setState({ current: newPagination }, () =>
			history.push(
				`${location.pathname}?page=${this.state.current}${
					this.isFilter()?.length ? '?filter=' + this.isFilter() : ''
				}`,
			),
		);
	}
	detailsClosed(e) {
		const { current, calibrationId } = this.state;
		console.log('kjdfgkjdhfj', e);
		this.setState(
			{
				detailsStatus: {
					id: calibrationId,
					status: e?.rc_status_code,
					duration: e?.durationInSec,
				},
				// bodyLoading: true
			},
			async () => {
				this.props.history.push(
					getBaseUrl(
						this.props,
						`workflow/calibration?page=${current}${
							this.isFilter()?.length
								? '?filter=' + this.isFilter()
								: ''
						}`,
					),
				);
				// await this.serviceData()
			},
		);
	}
	addBtnClicked() {
		this.calibrationWorkflowDrawerRef.current.showDrawer();
	}
	async componentDidMount() {
		const { history, location } = this.props;
		const { current, calibrationId,filterConfig } = this.state;
		history.push(
			`${location.pathname}?page=${current}${
				calibrationId ? '&id=' + calibrationId : ''
			}${this.isFilter()?.length ? '?filter=' + this.isFilter() : ''}`,
		);



		// set filter values from URL
		let filter_values={}
		let filter_keys=filterConfig.map(item=>item.url_name)
		filter_keys.forEach(key=>{
			filter_values[key]=this.getUrlBreak(key) ? this.getUrlBreak(key) : undefined;
			if (key==='generated_at' && !filter_values[key]){
				filter_values[key]=`${moment().subtract(30, 'days').startOf('day').unix()}-${moment().endOf('day').unix()}`
			}
		})
		this.setState({filter_values},()=>{
			this.fetchAPIData()
		})
	}
	timeRanges() {

	}
	filterOption() {
		const {
			categoryId,
			thingCategoryOptions,
			thingId,
			catWiseThingsOptions,
			totalThings,
			calibrationStatus,
			calibStatus,
			customerFilterOptions,
			tagsFilterOptions,
			filter_values,
			filter_loaded,
			filterConfig
		} = this.state;
		if (!filter_loaded) return;
		let updated_filter_config=this.setFilterConfig(true)
		// let updated_filter_config=filterConfig
		updated_filter_config=updated_filter_config.map(item=>{
			let val=filter_values[item.url_name]
			if (!isNaN(val)){
				val=parseInt(val)
			}
			if (typeof val!=="undefined" && val!=='undefined'){
				item['selectValue']=val
			}
			return item;
		})
		return updated_filter_config;
	}
	async drawerCallback(e) {
		const { client_id } = this.props;
		const { totalData } = this.state;
		const isEndCustomer=parseInt(this.props.application_id)!==12 && parseInt(this.props.application_id)!==17
		const selectedAsset =totalData?.things?.find(thing=>parseInt(thing.id)===parseInt(e?.drawer_thing))
		const customer_id = selectedAsset.customer_id
		const selectedAssetCategory=selectedAsset?.category || 0
		const isDG=[18,71].includes(parseInt(selectedAsset?.category))
		let dataToSave={
			entity_id: e?.drawer_thing,
			data:{
				type: e?.drawer_type,
				parameters: e?.drawer_parameter,
			},
			priority: 'high',
			sub_type:e?.drawer_type,
			type: 'calibration',
		}
		if (isDG){
			if (!e?.allFields?.parameter){
				openNotification('error','Please select parameter')
				return;
			}
			dataToSave['data']['parameters']=[e.allFields.parameter]
		}
		if ([21,22,102].includes(selectedAssetCategory)){
			dataToSave['data']['analyzer_sl_no']= e?.drawer_analyzer
			dataToSave['data']['gas']= e?.drawer_gas
		}
		const saveData=await createService(
			{ customer_id: customer_id },
			dataToSave
		);
		if (saveData.status==='failure'){
			openNotification('error',saveData.message)
			return;
		}

		openNotification('success',"Calibration Added")
		this.setState(
			{
				bodyLoading: true,
			},
			async () => {
				await this.serviceData(true);
			}
		);
	}
	openDetailsView(e) {
		const { current } = this.state;
		this.setState(
			{
				calibrationId: e,
			},
			() => {
				this.props.history.push(
					getBaseUrl(
						this.props,
						`workflow/calibration/details?page=${current}&id=${e}${
							this.isFilter()?.length
								? '?filter=' + this.isFilter()
								: ''
						}`,
					),
				);
			},
		);
	}
	render() {
		const userAcess=this.checkUserAccess();
		let filterData = this.filterOption();
		let that=this;
		const {
			loading,
			totalData,
			bodyLoading,
			from_time,
			upto_time,
			totalCalibList,
			calibrationId,
			current,
			filter_loaded,
			isMobile,
			totalPage,
			system_template_details
		} = this.state;
		const { client_id } = this.props;
		const paginationOptions={
			position: "top",
			pageSize: this.state.pageSize,
			total: this.state.totalPage,
			current:this.state.page,
			showTotal: (
				total,
				range
			) =>
				`${range[0]}-${range[1]} of ${totalPage} items`,
			size: 'small',
			showTotal: (total, range) =>
				`Showing ${range[0]}-${range[1]} of ${totalPage} items`
		}
		let findCalibData = _find(totalCalibList?.data, { id: calibrationId });
		const findThing = _find(totalData?.things, {
			id: parseInt(findCalibData?.entity_id),
		});
		let baseUrl = getBaseUrl(
			this.props,
			'workflow/calibration?page=' + current,
		);
		return loading ? (
			<SkeletonLoader count={3} rows={4} />
		) : this.props?.history?.location?.pathname?.includes(
				'workflow/calibration/details',
		  ) ? (
			  <>
				  {
					  (findCalibData && totalCalibList)?(
						  <CalibrationDetails
							  {...this.props}
							  calibThing={findCalibData?.entity_id}
							  calibData={findCalibData}
							  totalData={totalData}
							  totalCalibList={totalCalibList}
							  openTicketDetails={this.openTicketDetails}
							  updateCalibration={this.updateCalibration}
							  thing_calib_data={this.state.thing_calib_data}
							  system_template_details={this.state.system_template_details}
							  things_categories={this.state.things_categories}
							  client_id={client_id}
							  calibrationId={calibrationId}
							  userAcess={userAcess}
							  detailsClosed={(a, b) => this.detailsClosed(a, b)}
						  />
					  ):(
						  <SkeletonLoader count={3} rows={4} />
					  )
				  }
			  </>

		) : this.props?.history?.location?.pathname?.includes(
			'workflow/calibration/schedules',
	  	) ? (
			<AutoCalibrationList
				client_id={client_id}
				userAcess={userAcess}
				totalData={this.state.totalData}
				goBack={() =>
				this.props.history.push(
					getBaseUrl(this.props, "workflow/calibration"),
				)
				}
			/>
		) : (
			<div id="remote_calibration">
				<div className="rc-filters">
					{
						filter_loaded?(
							<GenericFilter
								backgroundWhite
								isFlexWrap
								key={"calibration"}
								history={this.props.history}
								url={baseUrl}
								width={525}
								default={filterData.map(item=>{
									return ''
								})}
								onPopupScroll={(e, key) =>
									this.onPopupScroll(e, key)
								}
								filterData={filterData}
								selectSearch={(e, key) =>
									this.clientVendorSearch(e, key)
								}
								selectOnBlur={(key) => this.selectOnBlur(key)}
								selectOnDropdownVisibleChange={(open, key) => this.selectOnDropdownVisibleChange(open, key)}
								panelFilterSelect={(value, key) =>
									this.applyFilterSelect(value, key)
								}
								onUrlChange={this.onUrlChange}
							/>
						):""
					}
					<div style={{display: 'flex', gap: '18px'}}>
					{this.props.application_id === 16 ? userAcess.manage ? (
					<AutoCalibrationAdd
						client_id={this.props.client_id}
						totalData={this.state.totalData}
						viewSchedules={() =>
						this.props.history.push(
							getBaseUrl(this.props, "workflow/calibration/schedules"),
						)
						}
					/>
					) : <AntButton disabled icon={<ClockCircleOutlined />}>Auto Schedule</AntButton> : ""}
					{
						userAcess.manage?(
							<div
								className="add-calib"
								onClick={() => this.addBtnClicked()}
							>
								<PlusCircleFilled />
							</div>
						): (
							!isMobile && <div className="add-calibration-disabled-btn">
								<PlusOutlined style={{color: '#fff'}}/>
							</div>
						)
					}
					</div>

				</div>
				<div id="calibration_custom_table">
					{
						isMobile ? (
							<>
							<AntPagination
									className={'rc_mb_list_pagination'}
									{...paginationOptions}
									onChange={(page, pageSize)=>{
										this.setState({page,pageSize},()=>{
											this.serviceData(true)
										})
									}}
								/>
								{this.tableData()?.dataSource ? (
									<div className={'rc_mb_list_cont'}>
										{
											this.tableData()?.dataSource.map(item =>
												<div
													className="each_calib_list_item"
													key={item.id}
												>
													<div className="details_cont"
														 onClick={() => this.openTicketDetails(item.id)}>
														<p className={'fw-500 f-12 fc_80'}>#{item.id}</p>
														<p className={'fw-500 f-13 fc_00'}>{item.entity_name}</p>
														<p className={'fw-500 f-12 fc_80'}>{item.sub_type}</p>
													</div>
													<div className={`status_tag ${item.status}`}>{item.status}</div>
													<div className="action">
														<ActionsOverlay
															onClick={() => {
																console.log("clicked")
															}}
															menuItems={[{
																label: 'Cancel',
																disabled: item.status_code!==0 || !that.checkUserAccess()?.manage,
																action: ()=>that.setState({cancel_calib_data:item})
															}]}
														/>
													</div>
												</div>
											)
										}
									</div>
									) : (
									<NoDataComponent height="60%" page_name="thing" />
									)}
							</>
						):(
							<>
								<CustomizeTable
									{...this.props}
									preferenceKeys={[
										'remote_calibration'
									]}
									tableProps={{
										dataSource: this.tableData()?.dataSource,
										columns:this.columns,
										shadow: false,
										isGrouped: false,
										loading: this.state.table_loading,
										onChange: this.onTableChange,
										sticky: true,
										scroll: { x: 'max-content' },
										pagination: {
											position: ['topRight'],
											pageSizeOptions: [
												10, 20, 25, 50, 100,
											],
											pageSize: this.state.pageSize,
											total: this.state.totalPage,
											current: this.state.page,
											showQuickJumper: true,
											showTotal: (
												total,
												range
											) =>
												`${range[0]}-${range[1]} of ${total} items`
										},
									}}
								/>
							</>
						)
					}
				</div>
				{
					isMobile && userAcess.manage?(
						<AntPopover
							title=""
							visible={true}
							content={
								''
							}
							trigger="click"
							overlayClassName="rc-add-popover"
						>
					<span
						className="open-rc-add-list"
						onClick={this.addBtnClicked}
					>
						<PlusOutlined
							style={{ color: '#fff', fontSize: 22 }}
						/>
					</span>
						</AntPopover>
					):""
				}
				<CalibrationWorkflowDrawer
					ref={this.calibrationWorkflowDrawerRef}
					totalData={totalData}
					client_id={this.props.client_id}
					application_id={this.props.application_id}
					isMobile={isMobile}
					system_template_details={this.state.system_template_details || {}}
					callback={(e) => this.drawerCallback(e)}
				/>
				<AntModal
					visible={this.state.cancel_calib_data}
					width={'100vh'}
					footer={null}
					closable={false}
					className={'remote_calib_upload_file_modal'}
				>
					<div className="device_sync_modal_content">
						<div className="sync_modal_heading_img_cont">
							<DeleteOutlined style={{fontSize: 22, color: 'white'}}/>
						</div>
						<p className={'cancel_import'} style={{fontSize: 16}}>Do you want to cancel calibration?</p>
						<div className="modal_button_cont">
							<AntButton
								className={'sync_modal_btn sync_close_btn'}
								onClick={() => {
									this.setState({
										cancel_calib_data: null
									})
								}}
							>
								Don't cancel
							</AntButton>
							<AntButton
								className={'sync_modal_btn'}
								onClick={this.deActivateTicket}
							>
								Yes, cancel
							</AntButton>
						</div>
					</div>

				</AntModal>
			</div>
		);
	}
}
