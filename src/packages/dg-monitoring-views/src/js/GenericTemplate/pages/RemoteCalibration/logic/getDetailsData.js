import _find from 'lodash/find';
import _filter from 'lodash/filter';
import moment from 'moment-timezone';
import { getServiceAlertsDetails ,getThingConfigsNew} from '@datoms/js-sdk';
import {openNotification} from "../../../logic/CameraControlLogic";
function getSystemTemplate(thing_details,system_template_details){
	let returnObj={
		template_id:0,
		template_name:'',
		last_fuel_calib_data:undefined
	}
	returnObj["last_fuel_calib_data"]=thing_details?.thing_details?.fuel_calib
	thing_details.configurations.forEach(config=>{
		if (!isNaN(config?.system_details?.template_id)){
			returnObj['template_id']=config?.system_details?.template_id
			if (config?.system_details?.template_id && system_template_details){
				returnObj['template_name']=system_template_details[config?.system_details?.template_id]
			}
		}
	})
	return returnObj;
}
export async function getDetailsCalibData() {
	const { client_id, calibrationId,calibData,calibThing,system_template_details } = this.props;
	let thing_calib_data=undefined;
	let [data,thingDetails] = await Promise.all(
		[getServiceAlertsDetails({
		customer_id: client_id,
		service_id: calibrationId,
		}),getThingConfigsNew(
			calibData.customer_id,
			16,
			calibThing
		)]
	);
	if (thingDetails.status==="success"){
		thing_calib_data=getSystemTemplate(thingDetails,system_template_details)
	}else {
		openNotification('error','Unable to Fetch Last Calibration Details')
	}
	this.setState(
		{
			serviceDetails: data,
			deatilsLoading: false,
			thing_calib_data,
			thing_details:thingDetails
		},
		async () => {
			console.log(
				"manierror",thing_calib_data,thingDetails
			)
			if (this.fuelCalibDetails.current){
				this.fuelCalibDetails.current.updateCalibDataFromPayload({details:{data:data?.data?.data}},true)
			}
			this.getHeaderData();
			this.getStages();
			this.tableAndGraphSocketData();
		},
	);
}

export function findThingDetails() {
	const { totalData, calibThing } = this.props;
	const { thing_details } = this.state;
	let findThing = thing_details;
	let analyzerParams;
	if (findThing) {
		analyzerParams =
			findThing?.thing_details?.parts?.[0]?.parameters_measured;
	}
	return {
		analyzerParams: analyzerParams,
		findThing: findThing,
	};
}

export function rawTableData() {
	const { serviceDetails } = this.state;
	let findThingDetails = this.findThingDetails();
	let findThing = findThingDetails?.findThing;
	let findAnalyzerParams = findThingDetails?.analyzerParams;
	let column = [{ title: 'Time', dataIndex: 'time' }];
	let getParams = serviceDetails?.data?.parameters;
	let paramArray = getActualParams(getParams, findAnalyzerParams);
	const allParams=getAllParams(findThing)
	if (paramArray?.length) {
		paramArray.map((key) => {
			let params = _find(allParams, { key: key });
			if (params) {
				column.push(
					{
						title: params.name + ' (' + params.destination_unit + ')',
						dataIndex: params.key,
						width: 180,
					},
					{
						title: 'Deviation' + ' (' + params.destination_unit + ')',
						dataIndex: params.key + '_deviation',
						width: 180,
					},
				);
			}
		});
	}
	return column;
}

export function graphLoopFunction() {
	const { serviceDetails } = this.state;
	let seriesData = [],
		yAxis = [];
	const { dataForGraphArray } = this.state;
	let color_arr = [
		'#7271d1',
		'#07adb1',
		'#c973c2',
		'#f28f3d',
		'#e07575',
		'#9ad8e3',
		'#97e8d4',
		'#85d6a2',
		'#f0e967',
		'#80f24b',
		'#d46846',
		'#c1db5e',
		'#b87a40',
		'#8e75bd',
		'#59739c',
		'#f25a73',
	];
	let findThingDetails = this.findThingDetails();
	let findThing = findThingDetails?.findThing;
	const allParams=getAllParams(findThing)
	let findAnalyzerParams = findThingDetails?.analyzerParams;
	let getParams = serviceDetails?.data?.parameters;
	let paramArray = getActualParams(getParams, findAnalyzerParams);
	if (paramArray?.length) {
		paramArray.map((key, ind) => {
			let params = _find(allParams, { key: key });
			if (params) {
				seriesData.push({
					name: params.name,
					color: color_arr[ind],
					type: 'area',
					data: dataForGraphArray?.[params.key] || [
						[moment().unix() * 1000, null],
					],
					tooltip: {
						valueSuffix: ' ' + params.destination_unit,
					},
				});
				yAxis.push({
					title: {
						text: params.destination_unit,
					},
				});
			}
		});
	}
	return {
		seriesData: seriesData,
		yAxis: yAxis,
	};
}

export function tableAndGraphSocketData(payload) {
	const { stages, serviceDetails } = this.state;
	let findThingDetails = this.findThingDetails();
	let findThing = findThingDetails?.findThing;
	let findAnalyzerParams = findThingDetails?.analyzerParams;
	let getParams = serviceDetails?.data?.parameters;
	let paramArray = getActualParams(getParams, findAnalyzerParams);
	let dataSource = [],
		dataForGraphArray = {};
	let actualData = payload?.details?.data || serviceDetails?.data?.data;
	if (actualData?.length) {
		actualData.map((data) => {
			const newObj = { time: moment.unix(data.t).format('HH:mm:ss') };
			if (paramArray?.length) {
				paramArray.map((key, ind) => {
					if (data[key]) {
						newObj[key] =
							(!isNaN(parseFloat(data[key]?.v)) &&
								parseFloat(data[key]?.v).toFixed(2)) ||
							'-';
						newObj[key + '_deviation'] =
							(!isNaN(parseFloat(data[key]?.d)) &&
								parseFloat(data[key]?.d).toFixed(2)) ||
							'-';
						if (parseFloat(data[key]?.v) >= 0) {
							const newData = [
								data.t * 1000,
								parseFloat(parseFloat(data[key]?.v).toFixed(2)),
							];
							if (dataForGraphArray[key]) {
								dataForGraphArray[key].push(newData);
							} else {
								dataForGraphArray[key] = [newData];
							}
						} else {
							dataForGraphArray[key] = [[data.t * 1000, null]];
						}
					} else {
						dataForGraphArray[key] = [];
					}
				});
			}
			dataSource.push(newObj);
		});
	}
	this.setState({
		dataSource: dataSource,
		dataForGraphArray: dataForGraphArray,
	});
}

export function getStages(payload) {
	const { serviceDetails } = this.state;
	const { calibData } = this.props;
	let stages = [];
	let tableDataUpdates={}

	let actualData;
	if (payload?.details?.stages) {
		actualData = payload?.details?.stages;
	} else if (serviceDetails?.data?.stages) {
		actualData = serviceDetails?.data?.stages;
	}
	if (actualData?.length) {
		actualData.map((stage) => {
			// updating start time, end time and duration
			if (stage.type==='started' && !calibData.start_time){
				tableDataUpdates['start_time']=parseInt(stage.time / 1000)
			}
			if (!calibData.end_time && ["aborted","failed","success"].includes(stage.type)){
				tableDataUpdates['end_time']=parseInt(stage.time / 1000)
				tableDataUpdates['duration']=parseInt(stage.time / 1000)-calibData.start_time
			}
			stages.push({
				user_name:
					payload?.details?.triggered_by_user_name ||
					serviceDetails?.data?.triggered_by_user_name,
				key: stage?.type,
				name:
					stage?.type?.charAt(0).toUpperCase() +
					stage?.type?.slice(1),
				time: stage?.time
					? moment
							.unix(stage.time / 1000)
							.format('DD MMM YYYY, HH:mm')
					: '',
			});
		});
	}
	this.setState({
		stages: stages,
	},()=>{
		if (Object.keys(tableDataUpdates).length){
			this.props.updateCalibration(calibData.id,tableDataUpdates)
		}
	});
}

function getActualParams(getParams, findAnalyzerParams) {
	if (!getParams || getParams === 'all') {
		return findAnalyzerParams;
	} else {
		return Array.isArray(getParams) ? getParams : [getParams];
	}
}

function getAllParams(findThing){
	let allThingParams=[];
	if(Array.isArray(findThing?.configurations)){
		findThing.configurations.forEach(config=>{
			if (Array.isArray(config.parameters)){
				allThingParams=[...allThingParams,...config.parameters]
			}
		})
	}
	return allThingParams;
}

export function getHeaderData(payload) {
	const { totalData,calibData } = this.props;
	const { serviceDetails } = this.state;
	let findThingDetails = this.findThingDetails();
	let findThing = findThingDetails?.findThing;
	const isDg=[18,71].includes(parseInt(findThing.category))
	let findAnalyzerParams = findThingDetails?.analyzerParams;
	let type = payload?.details?.sub_type || serviceDetails?.details?.sub_type;
	let calibrationTypes={
		'span':'Span Calibration',
		'zero':'Zero Calibration',
		'fuel':'Fuel Calibration',
	}
	let findCategory = _find(totalData?.things_categories, {
		id: parseInt(findThing?.category),
	});

	let allThingParams=getAllParams(findThing)
	let rcStatus =
		payload?.details?.rc_status || serviceDetails?.data?.rc_status || (calibData.status===3?'cancelled':'');
	let rcMessage =
		payload?.details?.result?.message ||
		serviceDetails?.data?.result?.message;
	let checkDur = 0,end_time=calibData?.end_time;
	if (!end_time && ["ongoing","aborted","failed","success"].includes(rcStatus)){
		end_time=moment().unix()
	}
	if (calibData?.start_time){
		checkDur=end_time - calibData?.start_time
	}
	let getParams = serviceDetails?.data?.parameters;
	let paramArray = getActualParams(getParams, findAnalyzerParams);
	let getParamsNames = [],
		getParamSensorDrift = [];
	let driftPercent =
		payload?.details?.result?.drift_percent ||
		serviceDetails?.data?.result?.drift_percent;
	if (paramArray?.length) {
		paramArray.map((params) => {
			getParamsNames.push(
				_find(allThingParams, {
					key: params,
				})?.name,
			);
			if (driftPercent?.[params]) {
				getParamSensorDrift.push(
					_find(allThingParams, {
						key: params,
					})?.name +
						' : ' +
						parseFloat(driftPercent[params]).toFixed(2) +
						'%',
				);
			}
		});
	}
	this.setState({
		headerData: {
			id: '#' + serviceDetails?.id,
			type: calibrationTypes[type],
			type_name:type,
			thing_name: findThing?.name,
			category: findCategory?.name,
			category_icon: findThing?.thing_category_icon,
			parameter: getParamsNames,
			durationInSec: checkDur,
			start_time:calibData.start_time,
			duration: checkDur
				? (parseInt(checkDur / 60) < 10
						? `0${parseInt(checkDur / 60)}`
						: parseInt(checkDur / 60)) +
					':' +
					(parseInt(checkDur % 60) < 10
						? `0${parseInt(checkDur % 60)}`
						: parseInt(checkDur % 60))
				: '',
			rc_status: rcStatus,
			rc_status_code: ['triggered', 'started', 'ongoing'].includes(
				rcStatus,
			)
				? 4
				: rcStatus === 'aborted' || rcStatus ==='cancelled'
				? 3
				: rcStatus === 'failed'
				? 2
				: rcStatus === 'success'
				? 1
				: 0,
			rc_status_color: ['triggered', 'started', 'ongoing'].includes(
				rcStatus,
			)
				? 'processing'
				: rcStatus === 'aborted'
				? 'warning'
				: rcStatus === 'failed'
				? 'error'
				: rcStatus === 'cancelled'
				? '#808080'
				: rcStatus === 'success'
				? 'success'
				: '',
			rc_message: rcMessage,
			drifts: getParamSensorDrift,
			showDownload: !isDg,
			show_start: !isDg,
			show_message: !isDg,
			show_status: true,
			show_parameter: !isDg,
		},
	});
}

export function analyzerDetails() {
	const { serviceDetails } = this.state;
	let findThingDetails = this.findThingDetails();
	let findThing = findThingDetails?.findThing;
	let findAnalyzerParams = findThingDetails?.analyzerParams;
	let getParams = serviceDetails?.data?.parameters;
	let findParts = _find(findThing?.thing_details?.parts, function (o) {
		return o.parameters_measured.some((element) =>
			!getParams || getParams === 'all'
				? findAnalyzerParams.includes(element)
				: element === getParams,
		);
	});
	let getParamsNames = [];
	if (findParts?.parameters_measured?.length) {
		findParts.parameters_measured.map((params) => {
			let findParam = _find(findThing?.parameters, {
				key: params,
			});
			getParamsNames.push(findParam?.name);
		});
	}
	let getParamsNamesStr =
		(getParamsNames?.length && getParamsNames.join(',')) || '';
	let type = serviceDetails?.data?.type;

	let otherdoc;
	if (type === 'zero') {
		otherdoc = findThing?.thing_details?.calibration?.['zero']?.docs;
	} else if (type === 'span') {
		if (!getParams || getParams === 'all') {
			findAnalyzerParams.map((params) => {
				if (
					params !== 'zero' &&
					findThing?.thing_details?.calibration?.[params]
				) {
					otherdoc =
						findThing?.thing_details?.calibration?.[params]?.docs;
				}
			});
		} else {
			otherdoc = findThing?.thing_details?.calibration?.[getParams]?.docs;
		}
	}
	return {
		make: findParts?.make,
		model: findParts?.model,
		serial: findParts?.serial_no,
		status: findParts?.status_flag,
		certification: findParts?.certification,
		parameters: getParamsNamesStr,
		docs: otherdoc,
	};
}

export function cylinderDetails() {
	const { serviceDetails } = this.state;
	let findThingDetails = this.findThingDetails();
	let findThing = findThingDetails?.findThing;
	let type = serviceDetails?.data?.type;
	let findAnalyzerParams = findThingDetails?.analyzerParams;
	let getParams = serviceDetails?.data?.parameters;
	let cylinderObject;
	if (type === 'zero') {
		cylinderObject = findThing?.thing_details?.calibration?.['zero'];
	} else if (type === 'span') {
		if (!getParams || getParams === 'all') {
			findAnalyzerParams.map((params) => {
				if (
					params !== 'zero' &&
					findThing?.thing_details?.calibration?.[params]
				) {
					cylinderObject =
						findThing?.thing_details?.calibration?.[params];
				}
			});
		} else {
			cylinderObject = findThing?.thing_details?.calibration?.[getParams];
		}
	}
	let cylinderDue =
		cylinderObject?.cyl_due > 0
			? moment.unix(cylinderObject?.cyl_due).format('DD MMM YYYY, HH:mm')
			: undefined;
	return {
		number: cylinderObject?.cyl_no,
		conc:
			cylinderObject?.cyl_conc_in_ppm *
			(cylinderObject?.cyl_conc_factor || 1),
		due_date: cylinderDue,
		supplier: cylinderObject?.cyl_supl_by,
		certificate: cylinderObject?.cyl_cert,
	};
}
