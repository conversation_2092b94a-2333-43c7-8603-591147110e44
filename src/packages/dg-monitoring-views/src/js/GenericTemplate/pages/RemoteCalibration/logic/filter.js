import _findIndex from 'lodash/findIndex';
import React from "react";
import { retriveThingsList,retriveCustomerList ,getTemplateList,retriveVendorThingsList,retriveApplicationThings} from '@datoms/js-sdk';
import { getThingsAndParameterData } from '../../../../data_handling/thingsListManipulation';
import { filterDginIot } from '../../../../data_handling/DGinIot';
import moment from 'moment-timezone';
import _uniqBy from 'lodash/uniqBy';
import {CALIBRATION_TYPES} from "./drawerData";
import {openNotification} from "../../../logic/CameraControlLogic";
export const isCalibrationSupported=(asset_type)=>{
	const calibration_supported_asset_types = [
		18, // DG SET
		71, // FUEL TANK
		21, // CEMS
		22, // AAQMS,
		102 // Mobile AQ Monitor
	];
	return calibration_supported_asset_types.indexOf(asset_type)>-1
}
export function selectOnBlur(key) {
	const { initialClientId, initialVendorid, clientId, vendorId } = this.state;
	if (this.state[`customersSearchVaue_${key}`]) {
		this.setState(
			{
				[`customersSearchVaue_${key}`]: undefined,
			},
			async () => {
				if (
					key === 'client_id' &&
					JSON.stringify(initialClientId) !== JSON.stringify(clientId)
				) {
					this.customerPage = 1;
					await this.getCustomerList(false);
				} else if (
					key === 'vendor_id' &&
					JSON.stringify(initialVendorid) !== JSON.stringify(vendorId)
				) {
					this.vendorPage = 1;
					await this.getVendorList(false);
				}
			}
		);
	}
}
export async function selectOnDropdownVisibleChange(open, key) {
	const {
		vendorId,
		vendorList,
		vendorLoading,
		clientId,
		customersList,
		custLoading,
		initialVendorid,
		initialClientId,
	} = this.state;
	if (open) {
		if (key === 'vendor_id') {
			console.log('initialVendorid', initialVendorid);
			if (
				JSON.stringify(initialVendorid) === JSON.stringify(vendorId) &&
				!vendorLoading
			) {
				this.vendorPage = 1;
				await this.getVendorList(false);
			}
		}
		if (key === 'client_id') {
			if (
				JSON.stringify(initialClientId) === JSON.stringify(clientId) &&
				!custLoading
			) {
				this.customerPage = 1;
				await this.getCustomerList(false);
			}
		}
	}
}
export async function getCustomerList(append,setState){
	let {customer_list,customersSearchVaue_customer_id,filter_values}=this.state,all_customer_list=[],selected_cust=[]
	if (!Array.isArray(customer_list)){
		customer_list=[]
	}
	if (Array.isArray(filter_values.customer_id)){
		selected_cust=filter_values.customer_id
	}
	let customerListData=await retriveCustomerList(this.props.client_id,
		`?results_per_page=20&page_no=${this.customerPage}&end_customer_only=true${
			customersSearchVaue_customer_id?.length
				? `&search=${customersSearchVaue_customer_id}`
				: ''
		}${
			selected_cust?.length
				? `&clients=[${selected_cust.join(',')}]`
				: ''
		}`
	)
	if (customerListData.status==='success'){
		all_customer_list=customerListData.customers.filter(item=>item.id!==1)
		if (append){
			all_customer_list=[...customer_list,...all_customer_list]
			all_customer_list=_uniqBy(all_customer_list, 'id')
			this.setState({customer_list:all_customer_list,custLoading:false})
		}else if (setState){
			this.setState({customer_list:all_customer_list,custLoading:false})
		}
	}else if (customerListData.message){
		openNotification('error',customerListData.message)
	}
	return all_customer_list
}
export async function getVendorList(append,setState){
	let {vendor_list,customersSearchVaue_vendor_id,vendorLoading,filter_values}=this.state,all_vendor_list=[],selected_vendors=[]
	if (!Array.isArray(vendor_list)){
		vendor_list=[]
	}
	if (Array.isArray(filter_values.vendor_id)){
		selected_vendors=filter_values.vendor_id
	}
	let vendorListData=await retriveCustomerList(this.props.client_id,
		`?results_per_page=20&page_no=${this.vendorPage}&vendor_only=true${
			customersSearchVaue_vendor_id?.length
				? `&search=${customersSearchVaue_vendor_id}`
				: ''
		}${
			selected_vendors?.length
				? `&vendors=[${selected_vendors.join(',')}]`
				: ''
		}`
	)
	if (vendorListData.status==='success'){
		all_vendor_list=vendorListData.customers.filter(item=>item.id!==1)
		if (append){
			all_vendor_list=[...vendor_list,...all_vendor_list]
			all_vendor_list=_uniqBy(all_vendor_list, 'id')
			this.setState({vendor_list:all_vendor_list,vendorLoading:false})
		}else if (setState){
			this.setState({vendor_list:all_vendor_list,vendorLoading:false})
		}
	}else if (vendorListData.message){
		openNotification('error',vendorListData.message)
	}
	this.setState({vendorLoading:false})
	return all_vendor_list
}

export async function getAssetList(){
	let thingsListQuery=`?results_per_page=20&page_no=1${this.thingsPage}`
	let end_customer =
		parseInt(this.props.application_id) !== 12 &&
		parseInt(this.props.application_id) !== 17;
	let query=``
	let assetData=end_customer? await retriveThingsList({client_id: this.props.client_id, application_id: this.props.application_id},''):
		await retriveVendorThingsList({vendor_id:this.props.client_id,application_id: this.props.application_id},'?lite=true')
	return assetData

}
export async function getCustomerData() {
	let stateToUpdate = {},
		customer_list = [],
		end_customer =
			parseInt(this.props.application_id) !== 12 &&
			parseInt(this.props.application_id) !== 17;
	stateToUpdate['customer_list']=[]
	// customersList = await retriveCustomerList(this.props.client_id, '?account_type=production');
	if (end_customer){
		customer_list= [
			{
				id: this.props.client_id,
				name: this.props.client_name,
				applications: [this.props.application_id],
				vendor_id: this.props.vendor_id,
			},
		]
		stateToUpdate['customer_list'] = customer_list
	}else {
		const [customersList,vendorList] = await Promise.all([
			this.getCustomerList(false,false),this.getVendorList(false,false)
		]);
		stateToUpdate['customer_list'] = customersList;
		stateToUpdate['vendor_list'] = vendorList;
	}


	// this.setState(stateToUpdate, () => {});
	return stateToUpdate
}
export async function onPopupScroll(e, key) {
	if (Math.abs((e.target.scrollTop + e.target.offsetHeight) - e.target.scrollHeight) < 1) {
		switch (key) {
			case 'vendor_id':{
				this.vendorPage++;
				this.getVendorList(true)
				break;
			}
			case 'customer_id':{
				this.customerPage++;
				this.getCustomerList(true)
				break;
			}
			case 'entity_id':{
				this.thingsPage++;
				break;
			}
			default:{}
		}
	}
}
export function clientVendorSearch(e, key) {
	const { clientId, vendorId } = this.state;
	this.customerPage = 1;
	this.vendorPage = 1;
	this.setState(
		{
			[`customersSearchVaue_${key}`]: e,
			[key === 'customer_id'
				? 'customersList'
				: key === 'vendor_id'
					? 'vendorList'
					: '']: [],
			[key === 'customer_id'
				? 'custLoading'
				: key === 'vendor_id'
					? 'vendorLoading'
					: '']: true,
		},
		() => {
			clearTimeout(this.delayedSearch);
			this.delayedSearch = setTimeout(async () => {
				if (key === 'customer_id') {
					await this.getCustomerList(false,true);
				} else if (key === 'vendor_id') {
					await this.getVendorList(false,true);
				}
			}, 1200);
		}
	);
}
export async function getSystemTemplates(){
	let stateToUpdate={}, system_template_details={}
	let [templateList18, templateList71] = await Promise.all([
		getTemplateList(
			this.props.client_id,
			16,
			18
		),
		getTemplateList(
			this.props.client_id,
			16,
			71
		)
	]);

	if (Array.isArray(templateList18.template_list)){
		stateToUpdate['system_templates'] = templateList18.template_list
		templateList18.template_list.forEach(temp=>{
			system_template_details[temp.id]=temp
		})
	}

	if (Array.isArray(templateList71.template_list)){
		stateToUpdate['system_templates'] = [
			...(stateToUpdate['system_templates'] || []),
			...templateList71.template_list
		]
		templateList71.template_list.forEach(temp=>{
			system_template_details[temp.id]=temp
		})
	}

	stateToUpdate['system_template_details']=system_template_details
	this.setState(stateToUpdate)
	return stateToUpdate
}

export async function getCategoryData(){
	let stateToUpdate={
		things_categories:{}
	};
	const categoryData = await retriveApplicationThings(this.props.application_id);
	// console.log('categoryData -> ', categoryData);
	if (categoryData.status === 'success') {
		categoryData.things_categories.forEach(cat=>{
			let parameter_names={}
			cat.parameter_details.forEach(parameter=>{
				parameter_names[parameter.key]=parameter
			})
			stateToUpdate['things_categories'][cat.id]={
				name:cat.name,
				parameters:parameter_names
			}
		})
	}

	return stateToUpdate
}
export async function fetchAPIData() {
	let [customerData,servicesData,categoryData,totalData]=
		await Promise.all([
			this.getCustomerData(),
			this.serviceData(),
			this.getCategoryData(),
			this.getAssetList(),
		])

	let stateUpdates=Object.assign({vendorLoading:false,custLoading:false},customerData,categoryData,servicesData)
	let thingsListArray = [],thingTags=[],tagsFilterOptions=[];
	let modifiedResponse = {};
	if (totalData.status === 'success' && Array.isArray(totalData.things)) {
		totalData = filterDginIot.bind(this)(totalData);
		thingsListArray = totalData.things;
		modifiedResponse = getThingsAndParameterData(totalData);
	}


	let thingCategoryOptions = [],things_categories=categoryData.things_categories;
	let catWiseThingsOptions = {};
	let totalThings = [];
	thingsListArray.map((thing) => {
		// add to filter asset type options only if thing type is supported for calibration
		if (!thingCategoryOptions.find(cat_option=>parseInt(cat_option.value)===parseInt(thing.category))){
			if (isCalibrationSupported(parseInt(thing.category))){
				thingCategoryOptions.push({
					value: thing.category,
					title: things_categories[thing.category].name,
				});
			}
		}
		if (!catWiseThingsOptions[thing.category]) {
			let findCategory = this.props.thing_list?.things_categories?.length
				? this.props.thing_list.things_categories.find(
						(category) => category.id === thing.category,
					)
				: {};

			catWiseThingsOptions[thing.category] = [];

		}
		// add to filter options (asset type and assets) only if asset type is supported for calibration
		if (isCalibrationSupported(thing.category)){
			catWiseThingsOptions[thing.category].push({
				value: thing.id,
				title: thing.name,
			});
			totalThings.push({
				value: thing.id,
				title: thing.name,
			});
			if (Array.isArray(thing.tags)){
				thing.tags.forEach(tag=>{
					if (thingTags.indexOf(tag)===-1){
						thingTags.push(tag)
					}
				})
			}
		}

		console.log({thingCategoryOptions})

	});
	let calibrationStatus = [
		{ title: 'Pending', value: 0 },
		{ title: 'Success', value: 1 },
		{ title: 'Failed', value: 2 },
		{ title: 'Cancelled', value: 3 },
		{ title: 'Ongoing', value: 4 },
	];
	let categoryId =
		this.getUrlBreak('category') &&
		!isNaN(parseInt(this.getUrlBreak('category')))
			? parseInt(this.getUrlBreak('category'))
			: undefined;
	let thingId =
		this.getUrlBreak('thing_id') &&
		!isNaN(parseInt(this.getUrlBreak('thing_id')))
			? parseInt(this.getUrlBreak('thing_id'))
			: undefined;
	let calibStatus =
		this.getUrlBreak('status') &&
		!isNaN(parseInt(this.getUrlBreak('status')))
			? parseInt(this.getUrlBreak('status'))
			: undefined;
	this.setState({
		categoryId,
		thingCategoryOptions,
		thingId,
		catWiseThingsOptions,
		totalData: totalData,
		totalThings: totalThings,
		modifiedResponse: modifiedResponse,
		calibrationStatus,
		calibStatus,
		tagsFilterOptions:thingTags.map(item=>{
			return ({
				value: item,
				title: item
			})
		}),
		...stateUpdates
	}, async () => {
		this.getSystemTemplates();
		await this.setFilterConfig();
	});
}

export function setFilterConfig(getConfig){
	let {customer_list,vendor_list,tag_options,thingCategoryOptions,calibrationStatus,totalThings}=this.state,stateToUpdate={}
	let {customersSearchVaue_vendor_id,
		customersSearchVaue_client_id,
		vendorLoading,
		custLoading}=this.state;
	let filterConfig=JSON.parse(JSON.stringify(this.state.filterConfig))
	let application_id=parseInt(this.props.application_id)
	let sub_type_options=[]
	Object.values(CALIBRATION_TYPES).forEach(asset_cat_calib_type=>{
		asset_cat_calib_type.forEach(calib_type=>{
			if (!sub_type_options.find(option=>option.value===calib_type.id)){
				sub_type_options.push({
					title: calib_type.name,
					value: calib_type.id
				})
			}
		})
	})

	console.log({sub_type_options})
	// filter options
	let filter_options={
		vendor_id:[],
		customer_id:[],
		entity_category_id:thingCategoryOptions,
		entity_id:totalThings,
		sub_type:sub_type_options,
		status:calibrationStatus,
		tags:Array.isArray(tag_options)?tag_options.map(tag=>{
			return ({
				title: tag.name,
				value: tag.id
			})
		}):[],
	}

	// set vendor_id options
	if (application_id===12){
		if (vendorLoading) {
			filter_options["vendor_id"].push({
				value: 'loading',
				title: 'Loading...',
			})
		}else {
			filter_options["vendor_id"]=vendor_list.map(vend=>{
				return ({
					title: vend.name,
					value: vend.id
				})
			})
		}

	}
	// set customer_id options
	if ([12,17].includes(application_id)){
		if (custLoading) {
			filter_options["customer_id"].push({
				value: 'loading',
				title: 'Loading...',
			});
		}else {
			filter_options["customer_id"]=customer_list.map(cust=>{
				return ({
					title: cust.name,
					value: cust.id
				})
			})
		}

	}

	filterConfig=filterConfig.map(item=>{
		let returnItem=item
		returnItem.optionData=[]
		if (item.url_name==='customer_id'){
			returnItem['searchValue']= customersSearchVaue_client_id
			returnItem['showEmptyOption']=[12,17].includes(parseInt(application_id))
			returnItem['notFoundContent']= custLoading ? (
				<div style={{ width: 100, height: 100 }}>Loading...</div>
			) : undefined
			returnItem.optionData=filter_options[item.url_name]
		}else if (item.url_name==='vendor_id'){
			returnItem['searchValue']= customersSearchVaue_vendor_id
			returnItem['showEmptyOption']=parseInt(application_id)===12
			returnItem['notFoundContent']= vendorLoading ? (
				<div style={{ width: 100, height: 100 }}>Loading...</div>
			) : undefined
			returnItem.optionData=filter_options[item.url_name]
		}else if (filter_options[item.url_name]){
			returnItem.optionData=filter_options[item.url_name]
		}
		return item;
	})

	if (getConfig){
		return filterConfig
	}
	stateToUpdate['filterConfig']=filterConfig
	stateToUpdate['loading']=false
	stateToUpdate['table_loading']=false
	stateToUpdate['filter_loaded']=true
	this.setState(stateToUpdate)

}
export function onRangeChange(e) {
	this.setState({
		from_time: e[0],
		upto_time: e[1],
	});
}
export function applyFilterSelect(value, key) {
	const { catWiseThingsOptions, totalThings,totalData, calibrationStatus,filter_values,filterConfig } = this.state;
	const {things}=totalData
	let selectedValues = value,total_options={};
	let dependentFields={
		vendor_id:["customer_id","entity_category_id","entity_id"],
		customer_id:["entity_category_id","entity_id"],
		entity_category_id:["entity_id"]
	}
	let changedIndex=filterConfig.findIndex(each_filter=>each_filter.url_name===key)
	if (dependentFields[key]){
		let resetFieldIndex=dependentFields[key].map(item=>filterConfig.findIndex(each_filter=>each_filter.url_name===item))
		selectedValues=selectedValues.map((item,index)=>{
			// if dependent then reset values
			return resetFieldIndex.includes(index)?undefined:item
		})
	}
	if (key === 'entity_category_id') {
		let total_entity_options=[]
		if (Array.isArray(value[changedIndex]) && value[changedIndex]?.length){
			value[changedIndex].forEach(cat_id=>{
				if (Array.isArray(catWiseThingsOptions[cat_id])){
					total_entity_options=[...total_entity_options,...catWiseThingsOptions[cat_id]]
				}
			})
		}else {
			total_entity_options=totalThings
		}
		total_options['entity_id']=total_entity_options
	}

	if (key==='created_on'){
		// selectedValues[changedIndex]=`${moment().subtract(30, 'days').startOf('day').unix()}-${moment().endOf('day').unix()}`
	}
	//entity_id
	return {
		selected_values: selectedValues,
		total_options
	}
}
export const datePickerConfig = {
	placeholder: ['From', 'To'],
	size: 'default',
	//   disabledDate: disabledDate,
	showTime: false,
	separator: ' - ',
	format: 'DD-MMM-YYYY',
};
const isMobileScreen = window.innerWidth <= 900;
export const filterData= [
	{
		optionData: [],
		selectValue: undefined,
		showSearch: true,
		sorted: false,
		allowClear: true,
		label: 'Partner',
		placeholder: 'Select Partner',
		no_outside_label: true,
		multiSelect:true,
		filterType: 'partner',
		url_name: 'vendor_id',
		is_options_dynamic: true,
		filterOption:false,
		autoClearSearchValue: false,
		key: 'vendor_id',
		showSingleOption:true,
		is_outside_filter_drawer: !isMobileScreen,
		is_inside_filter_drawer: isMobileScreen,
	},{
		optionData: [],
		selectValue: undefined,
		showSearch: true,
		sorted: false,
		allowClear: true,
		multiSelect:true,
		label: 'Customer',
		placeholder: 'Select Customer',
		filterType: 'customer',
		is_options_dynamic: true,
		filterOption:false,
		no_outside_label: true,
		autoClearSearchValue: false,
		url_name: 'customer_id',
		showSingleOption:true,
		key: 'customer_id',
		is_outside_filter_drawer: !isMobileScreen,
		is_inside_filter_drawer: isMobileScreen,
	},{
		optionData: [],
		selectValue: undefined,
		showSearch: true,
		sorted: false,
		allowClear: true,
		multiSelect:true,
		label: 'Asset Category',
		placeholder: 'Select Asset Category',
		no_outside_label: true,
		url_name: 'entity_category_id',
		is_outside_filter_drawer: !isMobileScreen,
		is_inside_filter_drawer: isMobileScreen,
	},{
		optionData: [],
		selectValue: undefined,
		showSearch: true,
		sorted: false,
		multiSelect:true,
		allowClear: true,
		label: 'Calibration Type',
		placeholder: 'Select Calibration Type',
		no_outside_label: true,
		url_name: 'sub_type',
		is_outside_filter_drawer: !isMobileScreen,
		is_inside_filter_drawer: isMobileScreen,
	},
	{
		optionData: [],
		selectValue: undefined,
		showSearch: true,
		allowClear: true,
		sorted: false,
		placeholder: 'Select Asset',
		label: 'Asset',
		multiSelect:true,
		no_outside_label: true,
		key: 'entity_id',
		url_name: 'entity_id',
		is_outside_filter_drawer: !isMobileScreen,
		is_inside_filter_drawer: isMobileScreen,
	},
	{
		optionData: [],
		selectValue: undefined,
		showSearch: true,
		allowClear: true,
		sorted: false,
		label: 'Status',
		placeholder: 'Select Status',
		no_outside_label: true,
		multiSelect:true,
		url_name: 'status',
		is_outside_filter_drawer: !isMobileScreen,
		is_inside_filter_drawer: isMobileScreen,
	},
	{
		optionData:  [],
		selectValue: undefined,
		showSearch: true,
		allowClear: true,
		sorted: false,
		label: 'Tags',
		placeholder: 'Select Tags',
		multiSelect:true,
		no_outside_label: true,
		showSingleOption:true,
		url_name: 'tags',
		is_outside_filter_drawer: !isMobileScreen,
		is_inside_filter_drawer: isMobileScreen,
	},
	{
		type: 'date',
		ranges: {
			'This Week': [moment().startOf('week'), moment().endOf('day')],
			'Last 7 days': [
				moment().subtract(7, 'days').startOf('day'),
				moment().endOf('day'),
			],
			'Last 30 Days': [
				moment().subtract(30, 'days').startOf('day'),
				moment().endOf('day'),
			],
			'This Month': [moment().startOf('month'), moment().endOf('month')],
			'Last Month': [
				moment().subtract(1, 'M').startOf('month'),
				moment().subtract(1, 'M').endOf('month'),
			],
			'Last 3 Months': [
				moment().subtract(3, 'M').startOf('day'),
				moment().endOf('day'),
			],
			'Last 120 Days': [
				moment().subtract(120, 'days').startOf('day'),
				moment().endOf('day'),
			],
		},
		datePickerConfig: datePickerConfig,
		selectValue: `${moment().subtract(30, 'days').startOf('day')}-${moment().endOf('day')}`,
		placeholder: 'Select Range',
		label: 'Date Range',
		no_outside_label: true,
		url_name: 'generated_at',
		is_outside_filter_drawer: !isMobileScreen,
		is_inside_filter_drawer: isMobileScreen,
	}
];

export function onUrlChange() {
	let that=this;
	let {filterConfig}=this.state
	let filter_keys=filterConfig.map(item=>item.url_name),filter_values={}
	filter_keys.forEach(key=>{
		filter_values[key]=this.getUrlBreak(key) ? this.getUrlBreak(key) : undefined;
	})
	this.setState({filter_values,table_loading:true,bodyLoading:true,page:1},()=>{
		this.serviceData(true);
	})
}
export function getUrlBreak(value) {
	let geturlData,{filterConfig}=this.state;
	let geturlDataIndex = _findIndex(
		this.props.history?.location?.search?.split(','),
		function (o) {
			return o.includes(value);
		},
	);
	let current_filter=filterConfig.find((item)=>item.url_name===value)
	if (geturlDataIndex > -1) {
		geturlData = this.props.history?.location?.search
			?.split(',')
			[geturlDataIndex].split(':')[1];
	}

	// if multi select
	if (current_filter && current_filter.multiSelect){
		let decodedValue=decodeURIComponent(geturlData)
		if (decodedValue && decodedValue!=="undefined" && decodedValue!=="null"){
			return decodedValue.split(',').map(item=>!isNaN(parseInt(item))?parseInt(item):item)
		}
		return decodedValue;
	}
	console.log("maniii-filter",value,geturlData)
	return geturlData === 'undefined' ? '' : geturlData;
}
export function getSelectedThings(allthings = false) {
	let selectedThings = [];
	const { categoryId, thingId, catWiseThingsOptions } = this.state;
	if (thingId && !allthings) {
		selectedThings.push(parseInt(thingId));
	} else {
		if (
			Array.isArray(catWiseThingsOptions[categoryId]) &&
			catWiseThingsOptions[categoryId].length
		) {
			catWiseThingsOptions[categoryId].map((thing) =>
				selectedThings.push(parseInt(thing.value)),
			);
		}
	}
	return selectedThings;
}
