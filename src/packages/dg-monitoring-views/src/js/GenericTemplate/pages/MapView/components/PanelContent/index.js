import React from 'react';
import './style.less';
import MapCommonHeader from '../MapCommonHeader';
import ACEnergyMeterPanelBody from '../../../../component/ACEnergyMeterPanelBody';
import DGPanelComponent from '../DGMapPanelComponent';
import FlowMeterPanel from '../FlowMeterPanel';
import BorewellPanel from '../BorewellPanel';
import FuelTankPanel from '../FuelTankPanel';
import FleetPanel from '../FleetPanel';
import TankerPanel from '../TankerPanel';
import DcEnergyMeterPanel from '../DcEnergyMeterPanel';
import CompilanceMonitoringPanelBody from '../../../../component/CompilanceMonitoringPanelBody';
import PollutionPanel from '../PollutionPanel';
import AurassurePanel from '../AurassurePanel';
import SolarPanelBody from '../../../../component/SolarPanelBody';
import BatteryPanelBody from '../../../../component/BatteryPanelBody';
import ACElectricalMachinePanelBody from '../../../../component/ACElectricalMachinePanelBody';
import TempHumidPanelBody from '../../../../component/TempHumidPanelBody';
import ProcessAnalyzerPanelBody from '../../../../component/ProcessAnalyzerPanelBody';
import ExhaustFanPanelBody from '../../../../component/ExhaustFanPanelBody';
import PanelBody from '../../../../component/PanelBody';
import SolarInverterMapBody from '../SolarInverterMapBody';
import ElevatorPanelBody from '../../../../component/ElevatorPanelBody';
import SolarPumpPanelBody from '../../../../component/SolarPumpPanelBody';
import { relatedCustomerIds } from '../../../../../../../../../configs/customer-specific-config';
import MRIPanelBody from '../../../../component/MRIPanelBody';

export default class PanelContent extends React.Component {
	constructor(props) {
		super(props);
		this.isDg = props.header_value?.cat_id === 18 || props.header_value?.cat_id === 96;
		this.isFlow = props.header_value?.cat_id === 86;
		this.isBorewell = props.header_value?.cat_id === 63;
		this.isFuelTank = props.header_value?.cat_id === 71;
		this.isFleet =
			props.header_value?.cat_id === 67 ||
			props.header_value?.cat_id === 76;
		this.isTanker = props.header_value?.cat_id === 74;
		this.isElectricalMachines = props.header_value?.cat_id === 78;
		this.isACEnergyMeter = props.header_value?.cat_id === 79 || props.header_value?.cat_id === 101;
		this.isDcEnergyMeter = props.header_value?.cat_id === 77;
		this.isColdStorage = props.header_value?.cat_id === 45;
		this.isPollution = props.pollutionThingCats?.includes(
			props.header_value?.cat_id,
		);
		this.isSolarSystem = props.header_value?.cat_id === 91;
		this.isBattery = props.header_value?.cat_id === 92;
		this.isTempHumidity = props.header_value?.cat_id === 90;
		this.isPortableCompressor = props.header_value?.cat_id === 73;
		this.isGasGenset = props.header_value?.cat_id === 96;
		this.isExhaustFan = props.header_value?.cat_id === 99;
	}
	render() {
		const {
			today_enrg_value,
			panel_data,
			graph_loading,
			all_graphs,
			date_select_channel,
			header_value,
			detailed_view,
			analog_view,
			constantCurrentTime,
			isAurassure,
			solarData,
		} = this.props;
		let pageRender = '';
		let totalPanelBodyData = isAurassure ? (
			<AurassurePanel 
				t={this.props.t}
				data={panel_data?.data} 
			/>
		) : this.isDg ? (
			<DGPanelComponent
				data={panel_data?.data}
				config={panel_data?.config}
			/>
		) : this.isPortableCompressor && relatedCustomerIds[1121].includes(parseInt(this.props.parent_vendor_id)) ? '' : this.isPortableCompressor || this.isGasGenset ? (
			<PanelBody
				data={panel_data}
			/>
		) : this.isFlow ? (
			<FlowMeterPanel
				panel_data={panel_data}
				graph_loading={graph_loading}
				all_graphs={all_graphs}
				date_select_channel={date_select_channel}
				viewTrendClick={() => this.props.viewTrendClick()}
				changeDateChannels={(e) => this.props.changeDateChannels(e)}
			/>
		) : this.isBorewell ? (
			<BorewellPanel
				panel_data={panel_data}
				graph_loading={graph_loading}
				all_graphs={all_graphs}
				date_select_channel={date_select_channel}
				viewTrendClick={() => this.props.viewTrendClick()}
				changeDateChannels={(e) => this.props.changeDateChannels(e)}
			/>
		) : this.isFuelTank ? (
			<FuelTankPanel
				panel_data={panel_data}
				all_graphs={all_graphs}
				graph_loading={graph_loading}
				date_select_channel={date_select_channel}
				viewTrendClick={() => this.props.viewTrendClick()}
				changeDateChannels={(e) => this.props.changeDateChannels(e)}
			/>
		) : this.isTanker ? (
			<TankerPanel
				pathDetailsClick={() => this.props.pathDetailsClick()}
				panel_data={panel_data}
				viewTrendClick={() => this.props.viewTrendClick()}
				changeDateTanker={(e) => this.props.changeDateTanker(e)}
				tanker_date_select_chamber={
					this.props.tanker_date_select_chamber
				}
				tanker_graph_loading_chamber={
					this.props.tanker_graph_loading_chamber
				}
				changeDateChambersTanker={(e) =>
					this.props.changeDateChambersTanker(e)
				}
				tanker_date_select={this.props.tanker_date_select}
				tanker_graph_loading={this.props.tanker_graph_loading}
			/>
		) : this.isFleet ? (
			<FleetPanel
				pathDetailsClick={() => this.props.pathDetailsClick()}
				panel_data={panel_data}
				all_graphs={all_graphs}
				graph_loading={graph_loading}
				date_select_channel={date_select_channel}
				viewTrendClick={() => this.props.viewTrendClick()}
				changeDateChannels={(e) => this.props.changeDateChannels(e)}
			/>
		) : this.isPollution ? (
			<PollutionPanel {...this.props} />
		) : this.isElectricalMachines ? (
			<ACElectricalMachinePanelBody data={panel_data} />
		) : this.isACEnergyMeter ? (
			<ACEnergyMeterPanelBody data={panel_data} />
		) : this.isSolarSystem ? (
			<SolarPanelBody
				data={panel_data}
			/>
		) :  this.props.header_value?.cat_id === 94 ? (
			<ProcessAnalyzerPanelBody data={panel_data}/>
		) :  this.isExhaustFan ? (
			<ExhaustFanPanelBody data={panel_data}/>
		) : this.isBattery ? (
			<BatteryPanelBody
				data={panel_data}
			/>
		) : this.isTempHumidity ? (
			<TempHumidPanelBody
				data={panel_data}
			/>
		) : this.isColdStorage ? (
			<CompilanceMonitoringPanelBody
				data={panel_data}
				isMapView
				client_id={this.props.client_id}
			/>
		)  : this.isDcEnergyMeter ? (
			<DcEnergyMeterPanel
				today_enrg_value={today_enrg_value}
				panel_data={panel_data}
				graph_loading={graph_loading}
				all_graphs={all_graphs}
				date_select_channel={date_select_channel}
				viewTrendClick={() => this.props.viewTrendClick()}
				changeDateChannels={(e) => this.props.changeDateChannels(e)}
				constantCurrentTime={constantCurrentTime}
			/>
		) :  this.props.header_value?.cat_id === 93 ? (
			<SolarInverterMapBody data={panel_data}/>
		) :  this.props.header_value?.cat_id === 100 ? (
			<ElevatorPanelBody view="map" data={panel_data}/>
		) :  this.props.header_value?.cat_id === 103 ? (
			<SolarPumpPanelBody view="map" data={panel_data}/>
		) :  this.props.header_value?.cat_id === 104?(
			<MRIPanelBody data={panel_data} />
		): (
			''
		);
		pageRender = (
			<div className="panel-content-main">
				{window.innerWidth <= 1024 ? (
					''
				) : (
					<MapCommonHeader
						t={this.props.t}
						icon={this.props.icon}
						header_value={header_value}
						closeDrawer={() => this.props.closeDrawer()}
						is_running={true}
						detailed_view={detailed_view}
						analog_view={analog_view}
						no_switch_off={false}
						closeDetailsPanel={() => this.props.closeDetailsPanel()}
						goToPage={this.props.goToPage}
						client_id={this.props.client_id}
						application_id={this.props.application_id}
						socket={this.props?.socket}
						getRemoteLockAccess={this.props.getRemoteLockAccess}
						getRemoteAccess={this.props.getRemoteAccess}
						isAurassure={isAurassure}
						isPortableCompressor={this.isPortableCompressor}
						parent_vendor_id={this.props.parent_vendor_id}
					/>
				)}
				<div
					className={
						'channel-data-container ' +
						(detailed_view || analog_view ? '' : 'no-nav-view ') +
						(this.isDg ||
						this.isFuelTank ||
						this.isFleet ||
						this.isTanker
							? 'dg'
							: '')
					}
				>

					{totalPanelBodyData}
				</div>
			</div>
		);
		return pageRender;
	}
}
