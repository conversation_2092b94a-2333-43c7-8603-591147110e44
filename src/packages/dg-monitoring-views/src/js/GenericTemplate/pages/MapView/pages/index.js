import React from "react";
import {
  disconnectSocketConnection,
  getThingsData,
  retrive<PERSON><PERSON><PERSON>,
  addNew<PERSON>eofence,
  getGeofenceList,
  editGeofence,
  deleteGeofence,
} from "@datoms/js-sdk";
import {
  callSocket,
  realTimeEventsUpdate,
  realTimeUpdateFunc,
  backyardSocketUpdate,
  updateEntityDetails,
} from "../logic/socketCaliing";
import ClusterView from "../../../../components/ClusterView";
import { totalActiveFaultsWithWarnTripStatus } from "../../../../data_handling/getFaultTypeRealtime";
import GeofenceMapIcon from "../../../images/geo-fence-icon.svg";
import Loading from "@datoms/react-components/src/components/Loading";
import AntSwitch from "@datoms/react-components/src/components/AntSwitch";
import AntInput from "@datoms/react-components/src/components/AntInput";
import GraphHighcharts from "@datoms/react-components/src/components/GraphHighcharts";
import _find from "lodash/find";
import _findIndex from "lodash/findIndex";
import _sortBy from "lodash/sortBy";
import _filter from "lodash/filter";
import _merge from "lodash/merge";
import _uniqBy from "lodash/uniqBy";
import _uniq from "lodash/uniq";
import _orderBy from "lodash/orderBy";
import moment from "moment-timezone";
import queryString from "query-string";
import SearchOutlined from "@ant-design/icons/SearchOutlined";
import UpOutlined from "@ant-design/icons/UpOutlined";
import DownOutlined from "@ant-design/icons/DownOutlined";
import ArrowRightOutlined from "@ant-design/icons/ArrowRightOutlined";
import GraphObjectData from "../../../../configuration/GraphObjectData";
import { setPreferences } from "@datoms/js-sdk";
import TripRoute from "../components/TripRoute";
import { Autocomplete } from "@react-google-maps/api";
import { getFaultLatestData } from "../../../logic/usePanel";
/*Components*/
import MapListWithSearch from "../components/MapListWithSearch";
import GoogleMapComponent from "../components/MapComponents/GoogleMapComponent";
import GeoFenceDrawer from "../../../../components/GeoFenceRightDrawer";
import GeoFenceConfigDrawer from "../../../../components/GeoFenceConfigDrawer";
import legendRunning from "../../../images/Legend-Running.svg";
import legendStopped from "../../../images/Legend-Stopped.svg";
import legendNotConnected from "../../../images/Legend-Not-Connected.svg";
import legendOffline from "../../../images/Legend-Offline.svg";

/*Configs*/
import MapConfig from "../../../configs/MapConfig";
import {
  isColdStorage,
  isDgTrue,
  isGasGensetTrue,
  isDCEnergy,
  isFuelTank,
  isFleet,
  isTankerTruck,
  isSolar,
  isAcEnergy,
  isAcElectricalMachines,
  isBattery,
  isTemperatureHumidity,
  isProcessAnalyzer,
  isCompressor,
  isExhaustFan,
  isSolarPump,
} from "../logic/findThingCat";
import {
  getGraphDataParams,
  thingsDataForGraphFunction,
  getGraphConfigFunc,
  getFilteredParam,
  getAllParams,
} from "../logic/getGeneralGraphData";

/*Styles*/
import "./style.less";
import PanelTrend from "../components/PanelTrend";
import { useThingStatusOptions } from "../../../logic/useThingStatusOptions";
import ThingStatus from "../../../component/ThingStatus";
import {
  serviceAlertFetch,
  maintenanceTextGen,
} from "../../../../data_handling/applicationMaintenanceDue";
import MaintenanceImg from "../../../images/maintenance.svg";
import MaintenanceOffLineImg from "../../../images/Group 8163.svg";
import { getAddressFromLat } from "../../../utility";
import PanelContent from "../components/PanelContent";
import MapMobileDrawer from "../components/MapMobileDrawer";
import MobilePageSelection from "../components/MobilePageSelectionTabs";
import DetailedView from "../../DetailedView";
import RealTime from "../../RealTime";
import getDgPanelData from "../logic/dgPanelData";
import { Skeleton } from "antd";
import {
  isRealTime,
  isDetailedView,
} from "../../../logic/RealTimeAndDetailedViewAvailability";
import { getFlowMeterpanelDataFunction } from "../logic/flowMeterPanelData";
import {
  getEnergypanelDataFunction,
  energySumData,
} from "../logic/EnergyPanelData";
import {
  fuelTankDataFunction,
  graphDataFunc,
  getPanelGraph,
  payloadDataFunc,
  getFuelTankPanelData,
} from "../logic/fuelTankPanelData";
import {
  fleetThingsDataFunction,
  fleetGraphDataFunc,
  fleetGraphConfigForPanel,
  getFleetpanelDataFunction,
  fleetPayloadRawDataFunc,
} from "../logic/fleetPanelData";
import {
  tankerTruckDataFunction,
  tankerTruckDataFunctionChamber,
  tankerGraphChamberDataFunc,
  tankerGraphDataFunc,
  getTankerGraphConfigForPanel,
  getTankerGraphDataChambers,
  getTankerpanelDataFunction,
  tankerPayloadRawDataFunc,
  changeDateTanker,
  changeDateChambersTanker,
} from "../logic/tankerTruckPanelData";

import { getBorewellpanelDataFunction } from "../logic/borewellPanelData";
import { coldStoragePanelData } from "../logic/ColdStoragePanelData";
import {
  aurassurePanelFunc,
  aqiLastHourData,
  aqiHourlyData,
  graphAqiDataFunc,
} from "../logic/aurassurePanelData";
import { getPollutionpanelDataFunction } from "../logic/PollutionPanelData";
import GensetStatus from "../../../../components/GensetStatus";
import { getFinalGensetStatusParamManipulationFunction } from "../../../../data_handling/gensetStatusParam";
import { getBaseUrl } from "@datoms/js-utils/src/base-url-logic";
import { ipLatestData } from "../../../logic/IpLatestData";
import { isAurassure, aurassureThingCat } from "../../../logic/isAurassure";
import { aaqmsImage } from "../../../logic/getLocalImageOfAAQMS";
import { getAqiColorStatusForValue } from "../../../logic/aqiParametersToShow";
import { solarPanelData, fetchSolarPanelData } from "../logic/solarPanelData";
import {
  compressorPanelData,
  fetchCompressorPanelData,
} from "../logic/compressorPanelData";
import {
  gasGensetPanelData,
  fetchGasGensetPanelData,
} from "../logic/gasGensetPanelData";
import MapMobileStatus from "../../../../components/MapMobileStatus";
import {
  acEnergyPanelData,
  fetchAcEnergyPanelData,
} from "../logic/ACEnergyPanelData";
import {
  fetchBatteryPanelData,
  batteryPanelData,
} from "../logic/batteryPanelData";
import { tempHumidPanelData } from "../logic/tempHumidPanelData";
import {
  fetchAcElectricalMachinesPanelData,
  acElectricalMachinesPanelData,
} from "../logic/ACElectricalMachinesPanelData";
import {
  fetchExhaustFanPanelData,
  exhaustFanPanelData,
} from "../logic/ExhaustFanPanelData";
import { solarInverterPanelData } from "../logic/solarInverterPanelData";
import { fetchSolarPumpPanelData, solarPumpPanelData } from "../logic/solarPumpPanelData";
import { elevatorPanelData } from "../logic/elevatorPanelData";
import {mriPanelData} from '../logic/mriPanelData';
import {
  closeGeofenceDrawer,
  closeGeofenceConfigDrawer,
  toggleGeofenceConfigDrawer,
  openGeofenceDrawer,
  searchByLatLng,
  renderGeofence,
  saveGeofence,
  editGeofenceOfCustomer,
  handleGeofenceData,
  deleteSelectedGeofence,
  getGeofenceListOfCustomer,
  toggleDrawingMode,
  toggleGeofenceAddMode,
  toggleGeofencesOnMap,
  handleDeselect,
  editSelectedGeofence,
  onAutoCompleteLoad,
  onPlaceChanged,
  latlngHandler,
  focusMapBasedOnLocation,
  handleGeofenceListing,
} from "../logic/geofence";
import { getStatusIcon } from "../../../logic/getStatusIcon";
import getOnlineOfflineOnlyStatus from "../../../logic/status";
import {
  noFuelAtAll,
  isOnlyFuelDG,
  isThingMechanicalDG,
  isMechanicalDG,
} from "../logic/dgConditions";
import FilterIconWithCount from "@datoms/react-components/src/components/FilterIconWithCount";
import {
  fetchCustomersList,
  getFilteredSiteList,
  getThingsList,
  updateDeviceStatus,
  fetchMissionListVehicle,
  fetchMissionList,
  customerFilterThingsFetch,
} from "../logic/listApis";
import { getTerritoryData } from "../../../../../../../webapp-component-user-management/src/components/TerritoryPage/TerritorySelect";
import { relatedCustomerIds } from "../../../../../../../../configs/customer-specific-config";
import { GlobalContext } from '../../../../../../../../store/globalStore';

export default class GenericMapView extends React.Component {
  filterIconRef = React.createRef();
  static contextType = GlobalContext;
  constructor(props) {
    super(props);
    GraphObjectData.graph_data.config.timezone =
      props.user_preferences.timezone;
    this.parsed = queryString.parse(props.location.search);
    this.state = {
      page_loading: true,
      trip_loading: true,
      trip_drawer_visible: false,
      drawer_visible: true,
      geofence_drawer_visible: false,
      geofence_config_drawer_visible: false,
      mobile_visible: false,
      panel_loading: true,
      selected_id: undefined,
      MapConfig: MapConfig.mapdetails,
      mobile_status_drawer_visible: false,
      mobile_render_type: "overview",
      currentAddress: "",
      search_data: "",
      show_graph_modal: false,
      cluster_view: props.user_preferences.map_cluster_view
        ? props.user_preferences.map_cluster_view
        : false,
      icon_clicked: false,
      url_changed: false,
      StartAfter: moment().subtract(1825, "days").startOf("day").unix(),
      StartBefore: moment().endOf("day").unix(),
      client_id: this.props.client_id,
      isDrawingMode: false,
      geofenceConfigData: { geofence_type: "circle" },
      geofenceData: [],
      totalGeofencesCount: 0,
      geofence_loading: false,
      isGeofenceAdding: false,
      showGeofenceOnMap: false,
      selectedGeofence: undefined,
      isEditing: false,
      autocomplete: null,
      autoCompleteLocation: undefined,
      tempLocation: {},
      listingState: { page_no: 1, name_query: "" },
      filterCount: 0,
      total_sites: []
    };
    this.mobileViewEnabled = window.innerWidth <= 1024;
    this.bucketTime = null;
    this.bucket = {
      raw_data: {},
      event_data: [],
    };
    this.callSocket = callSocket.bind(this);
    this.realTimeEventsUpdate = realTimeEventsUpdate.bind(this);
    this.realTimeUpdateFunc = realTimeUpdateFunc.bind(this);
    this.backyardSocketUpdate = backyardSocketUpdate.bind(this);
    this.goToPage = this.goToPage.bind(this);
    this.updateEntityDetails = updateEntityDetails.bind(this);
    this.getFlowMeterpanelDataFunction =
      getFlowMeterpanelDataFunction.bind(this);
    this.getDgPanelData = getDgPanelData.bind(this);
    this.graphDataFunc = graphDataFunc.bind(this);
    this.payloadDataFunc = payloadDataFunc.bind(this);
    this.fuelTankDataFunction = fuelTankDataFunction.bind(this);
    this.getPanelGraph = getPanelGraph.bind(this);
    this.getFuelTankPanelData = getFuelTankPanelData.bind(this);
    this.fleetThingsDataFunction = fleetThingsDataFunction.bind(this);
    this.fleetGraphDataFunc = fleetGraphDataFunc.bind(this);
    this.fleetGraphConfigForPanel = fleetGraphConfigForPanel.bind(this);
    this.getFleetpanelDataFunction = getFleetpanelDataFunction.bind(this);
    this.fleetPayloadRawDataFunc = fleetPayloadRawDataFunc.bind(this);
    this.tankerTruckDataFunction = tankerTruckDataFunction.bind(this);
    this.tankerTruckDataFunctionChamber =
      tankerTruckDataFunctionChamber.bind(this);
    this.tankerGraphChamberDataFunc = tankerGraphChamberDataFunc.bind(this);
    this.tankerGraphDataFunc = tankerGraphDataFunc.bind(this);
    this.getTankerGraphConfigForPanel = getTankerGraphConfigForPanel.bind(this);
    this.getTankerGraphDataChambers = getTankerGraphDataChambers.bind(this);
    this.getTankerpanelDataFunction = getTankerpanelDataFunction.bind(this);
    this.tankerPayloadRawDataFunc = tankerPayloadRawDataFunc.bind(this);
    this.changeDateTanker = changeDateTanker.bind(this);
    this.changeDateChambersTanker = changeDateChambersTanker.bind(this);
    this.coldStoragePanelData = coldStoragePanelData.bind(this);
    this.getPollutionpanelDataFunction =
      getPollutionpanelDataFunction.bind(this);
    this.aurassurePanelFunc = aurassurePanelFunc.bind(this);
    this.aqiLastHourData = aqiLastHourData.bind(this);
    this.aqiHourlyData = aqiHourlyData.bind(this);
    this.graphAqiDataFunc = graphAqiDataFunc.bind(this);
    this.getBorewellpanelDataFunction = getBorewellpanelDataFunction.bind(this);
    this.closeGeofenceConfigDrawer = closeGeofenceConfigDrawer.bind(this);
    this.closeGeofenceDrawer = closeGeofenceDrawer.bind(this);
    this.toggleGeofenceConfigDrawer = toggleGeofenceConfigDrawer.bind(this);
    this.openGeofenceDrawer = openGeofenceDrawer.bind(this);
    this.searchByLatLng = searchByLatLng.bind(this);
    this.renderGeofence = renderGeofence.bind(this);
    this.saveGeofence = saveGeofence.bind(this);
    this.editGeofence = editGeofence.bind(this);
    this.editGeofenceOfCustomer = editGeofenceOfCustomer.bind(this);
    this.handleGeofenceData = handleGeofenceData.bind(this);
    this.addNewGeofence = addNewGeofence.bind(this);
    this.deleteGeofence = deleteGeofence.bind(this);
    this.getGeofenceListOfCustomer = getGeofenceListOfCustomer.bind(this);
    this.toggleDrawingMode = toggleDrawingMode.bind(this);
    this.toggleGeofenceAddMode = toggleGeofenceAddMode.bind(this);
    this.toggleGeofencesOnMap = toggleGeofencesOnMap.bind(this);
    this.handleDeselect = handleDeselect.bind(this);
    this.deleteSelectedGeofence = deleteSelectedGeofence.bind(this);
    this.editSelectedGeofence = editSelectedGeofence.bind(this);
    this.onAutoCompleteLoad = onAutoCompleteLoad.bind(this);
    this.onPlaceChanged = onPlaceChanged.bind(this);
    this.focusMapBasedOnLocation = focusMapBasedOnLocation.bind(this);
    this.handleGeofenceListing = handleGeofenceListing.bind(this);
    this.latlngHandler = latlngHandler.bind(this);
    this.getGraphDataParams = getGraphDataParams.bind(this);
    this.thingsDataForGraphFunction = thingsDataForGraphFunction.bind(this);
    this.getGraphConfigFunc = getGraphConfigFunc.bind(this);
    this.getFilteredParam = getFilteredParam.bind(this);
    this.getAllParams = getAllParams.bind(this);
    this.getEnergypanelDataFunction = getEnergypanelDataFunction.bind(this);
    this.energySumData = energySumData.bind(this);
    this.fetchSolarPanelData = fetchSolarPanelData.bind(this);
    this.solarPanelData = solarPanelData.bind(this);
    this.compressorPanelData = compressorPanelData.bind(this);
    this.fetchCompressorPanelData = fetchCompressorPanelData.bind(this);
    this.gasGensetPanelData = gasGensetPanelData.bind(this);
    this.fetchGasGensetPanelData = fetchGasGensetPanelData.bind(this);
    this.fetchAcEnergyPanelData = fetchAcEnergyPanelData.bind(this);
    this.acEnergyPanelData = acEnergyPanelData.bind(this);
    this.noFuelAtAll = noFuelAtAll.bind(this);
    this.isOnlyFuelDG = isOnlyFuelDG.bind(this);
    this.isThingMechanicalDG = isThingMechanicalDG.bind(this);
    this.isMechanicalDG = isMechanicalDG.bind(this);
    this.isColdStorage = isColdStorage.bind(this);
    this.isDgTrue = isDgTrue.bind(this);
    this.isGasGensetTrue = isGasGensetTrue.bind(this);
    this.isDCEnergy = isDCEnergy.bind(this);
    this.isFuelTank = isFuelTank.bind(this);
    this.isFleet = isFleet.bind(this);
    this.isTankerTruck = isTankerTruck.bind(this);
    this.isSolar = isSolar.bind(this);
    this.isCompressor = isCompressor.bind(this);
    this.isProcessAnalyzer = isProcessAnalyzer.bind(this);
    this.isAcEnergy = isAcEnergy.bind(this);
    this.fetchAcElectricalMachinesPanelData =
      fetchAcElectricalMachinesPanelData.bind(this);
    this.acElectricalMachinesPanelData =
      acElectricalMachinesPanelData.bind(this);
    this.isAcElectricalMachines = isAcElectricalMachines.bind(this);
    this.isExhaustFan = isExhaustFan.bind(this);
    this.fetchExhaustFanPanelData = fetchExhaustFanPanelData.bind(this);
    this.exhaustFanPanelData = exhaustFanPanelData.bind(this);
    this.isBattery = isBattery.bind(this);
    this.fetchBatteryPanelData = fetchBatteryPanelData.bind(this);
    this.batteryPanelData = batteryPanelData.bind(this);
    this.tempHumidPanelData = tempHumidPanelData.bind(this);
    this.solarInverterPanelData = solarInverterPanelData.bind(this);
    this.isSolarPump = isSolarPump.bind(this);
    this.fetchSolarPumpPanelData = fetchSolarPumpPanelData.bind(this);
    this.solarPumpPanelData = solarPumpPanelData.bind(this);
    this.elevatorPanelData = elevatorPanelData.bind(this);
    this.isTemperatureHumidity = isTemperatureHumidity.bind(this);
    this.fetchCustomersList = fetchCustomersList.bind(this);
    this.getThingsList = getThingsList.bind(this);
    this.getFilteredSiteList = getFilteredSiteList.bind(this);
    this.updateDeviceStatus = updateDeviceStatus.bind(this);
    this.fetchMissionListVehicle = fetchMissionListVehicle.bind(this);
    this.fetchMissionList = fetchMissionList.bind(this);
    this.customerFilterThingsFetch = customerFilterThingsFetch.bind(this);
    this.mriPanelData=mriPanelData.bind(this);
  }
  getFilterDataFromUrl() {
    const { filterInUrl, history, dg_in_iot_mode } = this.props;
    return filterInUrl
      ? filterInUrl
      : "?" + history.location.search.split("?")[1];
  }
  async alertData() {
    const { client_id, application_id } = this.props;
    let thingAlerts = await retriveAlerts(
      this.state.client_id,
      application_id,
      parseInt(this.state.selected_id),
    );
    this.setState({
      alerts: thingAlerts,
    });
  }
  onGoogleApiLoaded = (map) => {
    this.setState({ myMap: map });
  };
  async getServiceAlert() {
    let data = await serviceAlertFetch(this.props.client_id);
    this.setState({
      service_alert_fetch: data,
    });
  }
  getMaintenanceText() {
    let text = maintenanceTextGen(
      this.state.service_alert_fetch,
      this.state.selected_id,
    );
    return text;
  }
  changeTab(e) {
    this.setState({
      select_tab: e,
    });
  }

  // check offline-online status
  checkOfflineOnlineStatus(offlineArray, onOffStatus, thing_id) {
    let offlineStatus = _find(offlineArray, {
      thing_id, //: parseInt(this.state.selected_id),
    })?.status;
    return offlineStatus === "offline" ? "2" : onOffStatus;
  }

  //get online-offline status
  getOnlineOfflineArray(totalData) {
    let offlineArray = [];
    if (totalData && totalData.things && totalData.things.length) {
      totalData.things.map((thing) => {
        offlineArray.push({
          thing_id: thing.id,
          status: thing.status,
        });
      });
    }
    return offlineArray;
  }

  pathRealtimeUpdate(payload) {
    const { latLngData, latLngDataWithTime } = this.state;
    let updated_latlng = [],
      updated_latlngDataWithTime = [];
    let findLatestDataIndex = _findIndex(this.state.latestParameterData, {
      thing_id: parseInt(this.state.selected_id),
    });
    let latestParameterData = this.state.latestParameterData;
    if (findLatestDataIndex > -1) {
      if (
        latLngData &&
        latLngData.length &&
        latLngDataWithTime &&
        latLngDataWithTime.length &&
        parseInt(latestParameterData[findLatestDataIndex].data.mc_st) === 1 &&
        this.state.trip_drawer_visible
      ) {
        updated_latlng = [...latLngData];
        updated_latlngDataWithTime = [...latLngDataWithTime];
        if (payload && payload.raw_data && payload.raw_data.data) {
          if (
            payload.raw_data.data.lat &&
            !isNaN(parseFloat(payload.raw_data.data.lat)) &&
            parseFloat(payload.raw_data.data.lat) > 0 &&
            payload.raw_data.data.long &&
            !isNaN(parseFloat(payload.raw_data.data.long)) &&
            parseFloat(payload.raw_data.data.long) > 0
          ) {
            updated_latlng.push({
              lat: parseFloat(payload.raw_data.data.lat),
              lng: parseFloat(payload.raw_data.data.long),
            });
            updated_latlngDataWithTime.push({
              lat: parseFloat(payload.raw_data.data.lat),
              lng: parseFloat(payload.raw_data.data.long),
              time: payload.raw_data.time,
            });
            this.setState(
              {
                latLngData: updated_latlng,
                latLngDataWithTime: updated_latlngDataWithTime,
              },
              async () => {
                this.zoomOnPolyline(this.state.latLngData);
                await this.getAddress(
                  [
                    {
                      lat: parseFloat(payload.raw_data.data.lat),
                      lng: parseFloat(payload.raw_data.data.long),
                      time: payload.raw_data.time,
                    },
                  ],
                  true,
                );
              },
            );
          }
        }
      }
    }
  }

  async getTripLatlngPoints() {
    let trip_id = this.state.selected_trip_id;
    let start_time = null,
      end_time = null;
    if (trip_id === "ongoing") {
      start_time = this.state.last_moving_start_time;
      end_time = moment().unix();
    }
    if (
      (trip_id &&
        this.getTripStartEndTime(trip_id)?.startTime &&
        this.getTripStartEndTime(trip_id)?.endTime) ||
      (start_time && end_time)
    ) {
      let dataPacketRaw = {
        data_type: "raw",
        aggregation_period: 0,
        parameters: ["lat", "long"],
        parameter_attributes: [],
        things: [this.state.selected_id],
        from_time: start_time
          ? start_time
          : this.getTripStartEndTime(trip_id).startTime,
        upto_time: end_time
          ? end_time
          : this.getTripStartEndTime(trip_id).endTime,
      };
      let rawData = await getThingsData(
        dataPacketRaw,
        this.state.client_id,
        this.props.application_id,
      );
      //	let rawData = latlngJson;
      if (rawData.status === "success") {
        let movingDgRawData = this.convertLatLng(rawData.data);
        this.setState(
          {
            latLngData: movingDgRawData.modifiedData,
            latLngDataWithTime: movingDgRawData.modifiedDataWithTime,
            // path_loading: false,
          },
          async () => {
            this.zoomOnPolyline(this.state.latLngData);
            await this.getAddress(this.state.latLngDataWithTime);
          },
        );
      } else {
        this.setState({
          path_loading: false,
          latLngData: undefined,
          latLngDataWithTime: undefined,
          latLngDataWithAddress: undefined,
        });
      }
    } else {
      this.setState({
        path_loading: false,
        latLngData: undefined,
        latLngDataWithTime: undefined,
        latLngDataWithAddress: undefined,
      });
    }
  }

  getTripStartEndTime(selected_trip_id) {
    let tripData = this.state.vehicleMissionList?.response?.Missions;
    let selectedTrip = tripData?.find((trip) => trip.Id === selected_trip_id);
    if (tripData?.length > 0 && selectedTrip) {
      let startTime = selectedTrip.StartDate;
      let endTime =
        selectedTrip.EndDate === "NA" ? moment().unix() : selectedTrip.EndDate;
      return {
        startTime: moment(startTime).unix(),
        endTime: moment(endTime).unix(),
      };
    }
    return null;
  }

  toggleTripDrawer(event) {
    let findLastData = _find(this.state.latestParameterData, {
      thing_id: this.state.selected_id,
    });
    let selected_trip_id = this.state.vehicleMissionList?.response?.Missions
      ?.length
      ? this.state.vehicleMissionList.response.Missions[0].Id
      : undefined;
    if (
      findLastData.OnOffStatus === "1" &&
      parseInt(findLastData?.data?.["start_time"])
    ) {
      selected_trip_id = "ongoing";
    }
    this.setState(
      {
        trip_drawer_visible: event,
        last_is_moving: findLastData.OnOffStatus === "1",
        last_moving_start_time: parseInt(findLastData?.data?.["start_time"]),
        path_loading: event,
        selected_trip_id,
      },
      async () => {
        if (event) {
          await this.getTripLatlngPoints();
        }
      },
    );
  }

  pathDetailsClick() {
    if (!this.state.trip_drawer_visible) {
      this.toggleTripDrawer(true);
    }
  }

  tripSelect(selected_trip_id) {
    this.setState(
      {
        selected_trip_id,
        path_loading: true,
      },
      async () => {
        await this.getTripLatlngPoints();
      },
    );
  }

  getTripHeaderValue() {
    let findLastData = _find(this.state.latestParameterData, {
      thing_id: this.state.selected_id,
    });
    return {
      speed: findLastData?.data?.["speed"],
      distance_travelled: findLastData?.data?.["distance_travelled"],
      fuel_consumption: findLastData?.data?.["fuel_consumption"],
    };
  }

  convertLatLng(data) {
    let modifiedData = [],
      modifiedDataWithTime = [];
    data.map((point) => {
      if (
        point.parameter_values &&
        !isNaN(parseFloat(point.parameter_values.lat)) &&
        parseFloat(point.parameter_values.lat) > 0 &&
        !isNaN(parseFloat(point.parameter_values.long)) &&
        parseFloat(point.parameter_values.long) > 0
      ) {
        modifiedDataWithTime.push({
          lat: parseFloat(point.parameter_values?.lat),
          lng: parseFloat(point.parameter_values?.long),
          time: point.time,
        });
        modifiedData.push({
          lat: parseFloat(point.parameter_values?.lat),
          lng: parseFloat(point.parameter_values?.long),
        });
      }
      //return 1;
    });
    return {
      modifiedData: _uniqBy(modifiedData, (item) => item.lat || item.lng),
      modifiedDataWithTime: _uniqBy(
        modifiedDataWithTime,
        (item) => item.lat || item.lng,
      ),
    };
  }

  splitLocationArray(points) {
    if (points.length <= 50) {
      return this.getPoints(points, 5);
    }
    return this.getPoints(points, 10);
  }

  getPoints(points, maxPoints) {
    const interval = Math.ceil(points.length / (maxPoints - 1));
    const newPoints = [];
    for (let i = 0; i < points.length - 1; i += interval) {
      newPoints.push(points[i]);
    }
    newPoints.push(points[points.length - 1]);
    return newPoints;
  }

  //reverse geocode all lat-lng points
  async getAddress(latLngData, socket = false) {
    let addressArray = [];
    if (socket && this.state.latLngDataWithAddress?.length) {
      addressArray = this.state.latLngDataWithAddress;
    }
    if (
      this.splitLocationArray(latLngData) &&
      this.splitLocationArray(latLngData).length
    ) {
      let firstAddress = await this.reverseGeocode(
        this.splitLocationArray(latLngData)[0],
      );
      let lastAddress = await this.reverseGeocode(
        this.splitLocationArray(latLngData)[
          this.splitLocationArray(latLngData).length - 1
        ],
      );
      if (!socket) {
        addressArray.push(
          {
            lat: this.splitLocationArray(latLngData)[0]?.lat,
            lng: this.splitLocationArray(latLngData)[0]?.lng,
            address: firstAddress,
            time: this.splitLocationArray(latLngData)[0]?.time,
          },
          {
            lat: this.splitLocationArray(latLngData)[
              this.splitLocationArray(latLngData).length - 1
            ]?.lat,
            lng: this.splitLocationArray(latLngData)[
              this.splitLocationArray(latLngData).length - 1
            ]?.lng,
            address: lastAddress,
            time: this.splitLocationArray(latLngData)[
              this.splitLocationArray(latLngData).length - 1
            ]?.time,
          },
        );
      }
      for (const latlng of this.splitLocationArray(latLngData)) {
        if (latlng) {
          let address = await this.reverseGeocode(latlng);
          addressArray.push({
            lat: latlng.lat,
            lng: latlng.lng,
            address: address,
            time: latlng.time,
          });
        }
      }
    }
    addressArray = _uniqBy(addressArray, "time");
    this.setState({
      latLngDataWithAddress: _orderBy(addressArray, ["time"], ["asc"]),
      path_loading: false,
    });
  }

  // get address from lat-lng
  async reverseGeocode(latlng) {
    let address = "",
      getAdrress = "";
    const geocoder = new window.google.maps.Geocoder();
    try {
      const response = await geocoder.geocode({
        location: latlng,
      });
      let findCode = [],
        getCode = "";
      if (
        response &&
        response.results &&
        response.results[0] &&
        response.results[0].address_components
      ) {
        response.results[0].address_components.map((get_address) => {
          if (
            !get_address.types.includes("plus_code") &&
            !get_address.types.includes("street_number") &&
            !get_address.types.includes("country") &&
            !get_address.types.includes("postal_code")
          ) {
            findCode.push(get_address);
          }
        });
      }
      findCode.map((addr, ind) => {
        if (ind === findCode.length - 1) {
          address += addr.long_name;
        } else {
          address += addr.long_name + ", ";
        }
      });
      // address = response?.results?.[0]?.formatted_address;
      // address = address.replace(fingCode, '')
      // let addressArr = address.split(',')
      // getAdrress = addressArr[0] + ', ' + addressArr[1] + ', ' + (addressArr[3] ? addressArr[2].split(' ') && addressArr[4].split(' ')[1] ? addressArr[5].split(' ')[1] : '' : '')
    } catch (err) {}
    return address;
  }

  async fetchAddress(latlng) {
    let address = await this.reverseGeocode(latlng);
    address = address?.split(",").slice(0, 3).join(", ");
    return address;
  }

  //create bounds for polyline
  createBoundsForPolyline(latlngArray) {
    let bounds = new window.google.maps.LatLngBounds();
    latlngArray.map((points) => {
      return bounds.extend(points);
    });
    return bounds;
  }

  getMapDiv() {
    return window.innerWidth > 1024
      ? document.getElementById("map_id")
      : document.getElementsByClassName("map-container-class")[1];
  }

  //zoom on the polyline bound
  zoomOnPolyline(latlngArray) {
    let mapDiv = this.getMapDiv();
    let offsetFactor =
      window.innerWidth > 1440
        ? 554 + 380
        : window.innerWidth > 1024
          ? 380 + 444
          : 0;
    let mapDim = {
      height: mapDiv?.clientHeight,
      width: mapDiv?.clientWidth - offsetFactor,
    };
    let bounds = this.createBoundsForPolyline(latlngArray);
    let zoomLevel = bounds ? this.getBoundsZoomLevel(bounds, mapDim) : 0;
    let centerValue = bounds
      ? bounds.getCenter()
      : new window.google.maps.LatLng(0, 0);
    this.setState(
      {
        zoomLevel: zoomLevel,
        centerValue: centerValue,
      },
      () => {
        if (window.innerWidth > 1024)
          this.state.myMap.panBy(offsetFactor / 2 + 22, 0);
      },
    );
  }

  payloadRawDataFunc(payloadRawData) {
    let thingAllParamDataChemberRaw = this.state.graph_data_channel;
    let findThings = _find(this.state.total_data.things, {
      id: parseInt(this.state.selected_id),
    });
    let chemberArray = this.getFilteredParam(findThings).params;
    if (chemberArray && chemberArray.length) {
      chemberArray.map((params) => {
        if (
          payloadRawData &&
          Object.keys(payloadRawData).length &&
          payloadRawData.thing_id === parseInt(this.state.selected_id) &&
          payloadRawData.parameter_values[params] &&
          thingAllParamDataChemberRaw &&
          thingAllParamDataChemberRaw[this.state.selected_id] &&
          thingAllParamDataChemberRaw[this.state.selected_id][params]
        ) {
          thingAllParamDataChemberRaw[this.state.selected_id][params].push([
            payloadRawData.time * 1000,
            parseFloat(payloadRawData.parameter_values[params]),
          ]);
          thingAllParamDataChemberRaw[this.state.selected_id][params] =
            thingAllParamDataChemberRaw[this.state.selected_id][params].sort(
              function (a, b) {
                return a[0] - b[0];
              },
            );
        }
      });
    }
    let rawParamData = {
      thingAllParamDataChemberRaw: thingAllParamDataChemberRaw,
    };
    return rawParamData;
  }

  offlineTimeOutFunction(
    payload = undefined,
    offlineArray,
    totalData,
    lastdata,
    response,
  ) {
    const { selectedPartner, selectedCustomer } = this.state;
    let latestDataArray = [],
      latestData = {},
      onOffStatus = {};
    response.all_thing_ids.map((things) => {
      let sortedThingsdata = _sortBy(lastdata, ["time"]);
      let filteredLatestData = _filter(sortedThingsdata, {
        thing_id: things,
      });
      let findFromTotalThing = _find(totalData?.things, { id: things });
      if (!latestData[things]) {
        latestData[things] = filteredLatestData[filteredLatestData.length - 1];
      }
      latestData[things]["category"] = findFromTotalThing.category;
      latestData[things]["device_status"] =
        findFromTotalThing?.devices?.[0]?.online_status === 1
          ? "online"
          : "offline";
      latestData[things]["deviceDetails"] = {
        status: latestData[things]["device_status"],
        qr_code: findFromTotalThing?.devices?.[0]?.qr_code,
      }
      latestData[things]["territory_id"] = findFromTotalThing.territory_id;
      latestData[things]["site_id"] = findFromTotalThing.site_id;
      latestData[things]["partner"] = selectedPartner;
      latestData[things]["customer"] = selectedCustomer;
      latestData[things]["name"] = findFromTotalThing.name;
      const hasMcStParam =
        findFromTotalThing?.status !== "offline" &&
        findFromTotalThing &&
        findFromTotalThing.parameters &&
        _find(findFromTotalThing.parameters, { key: "mc_st" })
          ? true
          : false;
      latestData[things]["hasMcStParam"] = hasMcStParam;
      let isMcSt =
        findFromTotalThing?.status === "offline"
          ? "2"
          : hasMcStParam
            ? latestData[things].data["mc_st"]
            : findFromTotalThing.category === 18 ||
                findFromTotalThing.category === 96
              ? "0"
              : "1";
      if (!onOffStatus[things]) {
        onOffStatus[things] = isMcSt;
      }
      let findThings = _find(response.things_list, { id: things });
      if (findThings) {
        onOffStatus[things] = parseInt(isMcSt)
          ? parseInt(isMcSt).toString()
          : "0";
        latestDataArray.push(latestData[things]);
        let findindex = _findIndex(latestDataArray, {
          thing_id: things,
        });
        if (findindex > -1) {
          latestDataArray[findindex]["on_off_moving_status"] =
            this.checkOfflineOnlineStatus(
              offlineArray,
              onOffStatus[things],
              things,
            );
        }
      }
    });
    ipLatestData(totalData, latestDataArray, payload, this.props.client_id);
    return latestDataArray;
  }

  getThingsListDrawerData() {
    let totalThingsList = [];
    if (
      this.state.latestParameterData &&
      this.state.latestParameterData.length
    ) {
      this.state.latestParameterData.map((things) => {
        if (this.isFiltered(things)) {
          let findThing = _find(this.state.total_data.things, {
            id: things.thing_id,
          });
          let findCat = _find(this.state.total_data.things_categories, {
            id: things.category,
          });
          if (findThing) {
            let latestThingValue =
              this.state.lastHourAqi?.[findThing.id]?.[0].parameter_values?.aqi
                ?.value;
            const customerId = findThing.current_assigned_customer ? findThing.current_assigned_customer : findThing.customer_id;
            totalThingsList.push({
              isAurassure: aurassureThingCat(
                things.category,
                this.props.vendor_id,
                this.props.client_id,
              ),
              category: findThing.category,
              category_name: findCat?.name,
              customer_name: this.state.customerNameMap?.[customerId],
              id: findThing.id,
              icon: aaqmsImage(findThing, this.props.vendor_id),
              deviceStatus: things.device_status,
              name: findThing.name,
              aqi: latestThingValue,
              show_switch: findCat?.show_switch,
              show_lock: findCat?.show_lock,
              last_data_recvd_time: things.time,
              no_status: findCat?.no_status,
              status_option_includes_stopped:
                findCat?.status_options?.includes("Stopped"),
              on_off_moving_status: things.on_off_moving_status,
              command_status: findThing?.commands,
              isLockControlEnabled:
                findThing &&
                findThing.thing_details &&
                findThing.thing_details.is_lock_unlock_control_enabled ===
                  "enable"
                  ? true
                  : false,
              isControlEnabled:
                findThing &&
                findThing.thing_details &&
                findThing.thing_details.is_start_stop_control_enabled ===
                  "enable"
                  ? true
                  : false,
              operation_mode:
                findThing && findThing.thing_details
                  ? findThing.thing_details.operation_mode
                  : undefined,
              dg_lock_status: things.data.dg_lock_status || "0",
              faultStatus: totalActiveFaultsWithWarnTripStatus(
                things,
                this.props.vendor_id,
                this.state.total_data,
              )?.fault_status,
            });
          }
        }
      });
    }
    return totalThingsList;
  }

  viewTrendClick(e) {
    this.setState({
      show_graph_modal: true,
    });
  }

  viewTrendClose() {
    this.setState({
      show_graph_modal: false,
      selected_channel: undefined,
    });
  }

  notEqualsMinusOne(lat, lng) {
    return lat > -90 && lat < 90 && lng > -180 && lng < 180;
  }

  getCommonHeaderValue() {
    let findThings = _find(this.state.total_data.things, {
      id: parseInt(this.state.selected_id),
    });
    let header_value = {};
    if (findThings) {
      let getCat = _find(this.state.total_data.things_categories, {
        id: findThings.category,
      });
      let findLastData = _find(this.state.latestParameterData, {
        thing_id: this.state.selected_id,
      });
      const latestFaults = getCat?.show_fault ? getFaultLatestData(findLastData, findThings) : undefined;
      if (findLastData) {
        let thingDetailsArray = [];
        if (findThings?.thing_details) {
          let thingDetailsKeys = Object.keys(findThings.thing_details);
          thingDetailsArray.push({
            key: "make",
            name: "Make: ",
            value: thingDetailsKeys.includes("make")
              ? findThings.thing_details.make
              : "NA",
          });
          thingDetailsArray.push({
            name: "Model: ",
            key: "model",
            value: thingDetailsKeys.includes("model")
              ? findThings.thing_details.model
              : "NA",
          });
          if (thingDetailsKeys.includes("serial_no")) {
            thingDetailsArray.push({
              key: "sl_no",
              name: "Serial No: ",
              value: findThings.thing_details.serial_no,
            });
          }
          if (thingDetailsKeys.includes("kva")) {
            thingDetailsArray.push({
              key: "kva",
              name: "",
              value: findThings.thing_details.kva + " kva",
            });
          }
        }
        const statusIcon = getStatusIcon(
          getCat.id,
          parseInt(findLastData?.data?.mc_st) === 1 ? "on" : "off",
        );
        header_value = {
          location: getCat.no_location
            ? ""
            : this.state.currentAddress
              ? this.state.currentAddress
              : findThings.address,
          cat: getCat ? getCat.name : "",
          cat_id: getCat ? getCat.id : "",
          thing_name:
            this.state.modifiedResponse.thing_name_list[findLastData.thing_id],
          lat:
            parseFloat(findLastData?.data?.["lat"]) &&
            parseFloat(findLastData?.data?.["long"]) &&
            this.notEqualsMinusOne(
              parseFloat(findLastData?.data?.["lat"]),
              parseFloat(findLastData?.data?.["long"]),
            )
              ? parseFloat(findLastData?.data?.["lat"])
              : findThings.latitude,
          lng:
            parseFloat(findLastData?.data?.["lat"]) &&
            parseFloat(findLastData?.data?.["long"]) &&
            this.notEqualsMinusOne(
              parseFloat(findLastData?.data?.["lat"]),
              parseFloat(findLastData?.data?.["long"]),
            )
              ? parseFloat(findLastData?.data?.["long"])
              : findThings.longitude,
          id: this.state.selected_id,
          maintenance: {
            icon:
              findLastData.on_off_moving_status === 2
                ? MaintenanceOffLineImg
                : MaintenanceImg,
            text: this.getMaintenanceText(),
          },
          icon: aaqmsImage(findThings, this.props.vendor_id),
          deviceStatus: findLastData.device_status,
          date:
            findLastData.time === 0
              ? this.props.t? this.props.t('no_data_received'): "No Data Received"
              // ? "No Data Received"
              : moment.unix(findLastData.time).format("DD MMM YYYY, HH:mm"),
          statusIcon: statusIcon,
          on_off_moving_status: findLastData.on_off_moving_status,
          // thing_details: thingDetailsArray,
          command_status: findThings?.commands,
          isLockControlEnabled:
            findThings &&
            findThings.thing_details &&
            findThings.thing_details.is_lock_unlock_control_enabled === "enable"
              ? true
              : false,
          isControlEnabled:
            findThings &&
            findThings.thing_details &&
            findThings.thing_details.is_start_stop_control_enabled === "enable"
              ? true
              : false,
          operation_mode:
            findThings && findThings.thing_details
              ? findThings.thing_details.operation_mode
              : undefined,
          dg_lock_status: findLastData.data.dg_lock_status || "0",
          show_switch: getCat?.show_switch,
          show_lock: getCat?.show_lock,
          latestFaults: latestFaults,
          no_status: getCat?.no_status,
          status_option_includes_stopped:
            getCat?.status_options?.includes("Stopped"),
        };
        return header_value;
      }
    }
  }

  async updateAddress() {
    let selectedThinglatestParam = _find(this.state.latestParameterData, {
      thing_id: parseInt(this.state.selected_id),
    });
    if (parseInt(selectedThinglatestParam?.data?.is_moving) === 1) {
      let findThingList = _find(this.state.total_data.things, {
        id: parseInt(this.state.selected_id),
      });
      let address = await this.fetchAddress(
        getAddressFromLat(selectedThinglatestParam, findThingList),
      );
      if (address && address.length > 1) {
        this.setState({ currentAddress: address });
      }
    }
  }

  changeDateChannels(e) {
    let fromTime = "",
      uptoTime = "",
      dataType = "";
    if (e === "today") {
      fromTime = moment().startOf("day").unix();
      uptoTime = moment().endOf("day").unix();
      dataType = "raw";
    } else if (e === "7_days") {
      fromTime = moment().subtract(7, "day").unix();
      uptoTime = moment().endOf("day").unix();
      dataType = "raw";
    } else if (e === "this_month") {
      fromTime = moment().startOf("month").unix();
      uptoTime = moment().endOf("month").unix();
      dataType = "avg";
    } else if (e === "30_days") {
      fromTime = moment().subtract(30, "day").startOf("day").unix();
      uptoTime = moment().endOf("day").unix();
      dataType = "avg";
    }
    this.setState(
      {
        from_time_channel: fromTime,
        upto_time_channel: uptoTime,
        data_type_channel: dataType,
        date_select_channel: e,
        graph_loading: true,
      },
      async () => {
        if (this.isFuelTank(this.state.selected_id)) {
          await this.fuelTankDataFunction();
        } else if (this.isFleet(this.state.selected_id)) {
          await this.fleetThingsDataFunction();
        } else {
          await this.thingsDataForGraphFunction();
        }
      },
    );
  }

  // lat/lng not equals -1
  notEqualsMinusOne(lat, lng) {
    return lat > -90 && lat < 90 && lng > -180 && lng < 180;
  }

  closeDetailsPanel() {
    let findLatestDataIndex = _findIndex(this.state.latestParameterData, {
      thing_id: parseInt(this.state.selected_id),
    });
    let latestParameterData = this.state.latestParameterData;
    if (findLatestDataIndex > -1) {
      if (
        latestParameterData[findLatestDataIndex].data &&
        Object.keys(latestParameterData[findLatestDataIndex].data).includes(
          "start_time",
        )
      ) {
        delete latestParameterData[findLatestDataIndex].data.start_time;
      }
    }
    this.setState(
      {
        trip_drawer_visible: false,
        last_is_moving: false,
        last_moving_start_time: false,
        panel_loading: true,
        selected_id: undefined,
        zoomLevel: undefined,
        centerValue: undefined,
        latLngData: undefined,
        latLngDataWithAddress: undefined,
        latLngDataWithTime: undefined,
      },
      () => {
        this.getMapZoomOnFilter();
      },
    );
  }

  getThingsWithMap(totalData) {
    if (this.mobileViewEnabled) {
      return totalData.things;
    }
    let getFilterThings = _filter(totalData.things, function (o) {
      return (
        _find(totalData.things_categories, { id: o.category })?.pages?.map !==
        false
      );
    });
    return getFilterThings;
  }

  getStatusWiseMapIcon(status, mapIconsConfig, faultStatus) {
    let mapIcons = mapIconsConfig?.map_icons;
    if (mapIconsConfig?.customer_specific_map_icons?.[this.context?.vendor_id]) {
      mapIcons = mapIconsConfig.customer_specific_map_icons[this.context?.vendor_id];
    }
    return mapIcons?.[`${status}_fault_${faultStatus}`] || mapIcons?.[status];
  }

  mapFunction() {
    let mapConfigData = JSON.parse(JSON.stringify(this.state.MapConfig));
    let mapPointers = [],
      mapLegend = [];
    const { total_data } = this.state;
    let getFilterThings = this.getThingsWithMap(total_data);
    if (
      this.state.latestParameterData &&
      this.state.latestParameterData.length
    ) {
      this.state.latestParameterData.map((things) => {
        if (this.isFiltered(things)) {
          let findThing = _find(total_data.things, {
            id: things.thing_id,
          });
          let filteredList = _filter(getFilterThings, {
            id: things.thing_id,
          });
          const faultStatus = totalActiveFaultsWithWarnTripStatus(
            things,
            this.props.vendor_id,
            this.state.total_data,
          )?.fault_status;
          things["fault_status"] = faultStatus;
          if (findThing) {
            let findCat = _find(total_data.things_categories, {
              id: findThing.category,
            });
            let isThingMoving = findCat?.status_options?.includes("Moving");
            const catNotIncludesStop =
              !findCat?.status_options?.includes("Stopped");
            const mapIconsConfig = findCat?.pages?.map;
            const onlineIcon =
              this.getStatusWiseMapIcon("online", mapIconsConfig, things.fault_status);
            const disconnectedIcon =
              this.getStatusWiseMapIcon("disconnected", mapIconsConfig, things.fault_status);
            const offlineIcon =
              this.getStatusWiseMapIcon("offline", mapIconsConfig, things.fault_status);
            const switchOffIcon =
              this.getStatusWiseMapIcon("switch_off", mapIconsConfig, things.fault_status);
            const getMapIcon = catNotIncludesStop
              ? getOnlineOfflineOnlyStatus(things?.on_off_moving_status) ===
                "online"
                ? onlineIcon
                : things.device_status === "online"
                  ? disconnectedIcon
                  : offlineIcon
              : things?.on_off_moving_status === "2"
                ? things.device_status === "online"
                  ? disconnectedIcon
                  : offlineIcon
                : things?.on_off_moving_status === "1"
                  ? isThingMoving
                    ? ""
                    : onlineIcon
                  : switchOffIcon;
            return filteredList.map((filteredListData) => {
              let latestAqiValue =
                this.state.lastHourAqi?.[filteredListData.id]?.[0]
                  .parameter_values?.aqi?.value;

              return mapPointers.push({
                lat:
                  parseFloat(things?.data?.["lat"]) &&
                  parseFloat(things?.data?.["long"]) &&
                  this.notEqualsMinusOne(
                    parseFloat(things?.data?.["lat"]),
                    parseFloat(things?.data?.["long"]),
                  )
                    ? parseFloat(things?.data?.["lat"])
                    : filteredListData.latitude,
                lng:
                  parseFloat(things?.data?.["long"]) &&
                  parseFloat(things?.data?.["lat"]) &&
                  this.notEqualsMinusOne(
                    parseFloat(things?.data?.["lat"]),
                    parseFloat(things?.data?.["long"]),
                  )
                    ? parseFloat(things?.data?.["long"])
                    : filteredListData.longitude,
                hoverText: isAurassure(
                  this.props.vendor_id,
                  this.props.client_id,
                )
                  ? 
                  `${this.props.t? this.props.t('asset_name') + ": ": 'Asset name:'} ${filteredListData.name}`
                  : this.context?.isSistemaBioCustomer ?
                    `Genset Name: ${filteredListData.name}\nGenset Status: ${
                      things.on_off_moving_status === "1" ? "On" : "Off"
                    }`
                    : `${this.props.t? this.props.t('asset_name') + ": ": 'Asset name:'} ${filteredListData.name}\nDevice status: ${things.device_status}\n${
                        things.device_status === "online"
                          ? findCat?.status_options?.includes("Stopped")
                            ? things.on_off_moving_status === "1"
                              ? "Asset status: Running"
                              : things.on_off_moving_status === "2"
                                ? "Asset status: Disconnected"
                                : "Asset status: Stopped"
                            : things.on_off_moving_status === "1"
                              ? ""
                              : "Asset status: Disconnected"
                          : ""
                      }${
                        faultStatus?.length && things.device_status === "online"
                          ? `\nFault type: ${faultStatus === "not_defined" ? "Not defined" : faultStatus.charAt(0).toUpperCase() + faultStatus.slice(1) + (faultStatus === "both" ? " (Trip & Warning)" : "")}`
                          : ""
                      }`,

                id: filteredListData.id,
                kva:
                  filteredListData.thing_details &&
                  !isNaN(parseFloat(filteredListData.thing_details.kva))
                    ? "(" +
                      (parseFloat(filteredListData.thing_details.kva) +
                        " KVA") +
                      ")"
                    : "",
                isRunning:
                  isThingMoving && things?.on_off_moving_status === "1",
                icon: aurassureThingCat(
                  findCat?.id,
                  this.props.vendor_id,
                  this.props.client_id,
                )
                  ? getAqiColorStatusForValue(
                      latestAqiValue,
                      findThing.category,
                      things?.on_off_moving_status,
                    )?.aqiMapIcon
                  : getMapIcon,
              });
            });
          }
        }
      });
    }
    let getStatus = this.state.total_on_off_status
      .filter((status) => !["Online", "Offline"].includes(status.value))
      .map((sat) => sat.value);
    if (getStatus.includes("Running")) {
      mapLegend.push({
        name: "Running/Online",
        color: "#147437",
      });
    } else {
      mapLegend.push({
        name: "Online",
        color: "#147437",
      });
    }
    if (getStatus.includes("Stopped")) {
      mapLegend.push({
        name: "Stopped",
        color: "#FF0000",
      });
    }
    mapLegend.push({
      name: "Offline",
      color: "#858787",
    });
    mapConfigData.map_legend = mapLegend;
    mapConfigData.map_pointers = mapPointers;
    return mapConfigData;
  }

  getBoundsZoomLevel(bounds, mapDim) {
    let WORLD_DIM = { height: 256, width: 256 };
    let ZOOM_MAX = 21;

    function latRad(lat) {
      let sin = Math.sin((lat * Math.PI) / 180);
      let radX2 = Math.log((1 + sin) / (1 - sin)) / 2;
      return Math.max(Math.min(radX2, Math.PI), -Math.PI) / 2;
    }

    function zoom(mapPx, worldPx, fraction) {
      return Math.floor(Math.log(mapPx / worldPx / fraction) / Math.LN2);
    }

    let ne = bounds.getNorthEast();
    let sw = bounds.getSouthWest();

    let latFraction = (latRad(ne.lat()) - latRad(sw.lat())) / Math.PI;

    let lngDiff = ne.lng() - sw.lng();
    let lngFraction = (lngDiff < 0 ? lngDiff + 360 : lngDiff) / 360;

    let latZoom = zoom(mapDim.height, WORLD_DIM.height, latFraction);
    let lngZoom = zoom(mapDim.width, WORLD_DIM.width, lngFraction);

    return Math.min(latZoom, lngZoom, ZOOM_MAX);
  }

  getLatLongAsPerAvailability() {
    let findThings = _find(this.state.total_data.things, {
      id: this.state.selected_id,
    });
    let findLastData = _find(this.state.latestParameterData, {
      thing_id: this.state.selected_id,
    });
    let getLat = findThings?.latitude;
    if (findLastData && findLastData.data && findLastData.data.lat) {
      if (
        !isNaN(parseFloat(findLastData.data.lat)) &&
        parseFloat(findLastData.data.lat) !== 0 &&
        parseFloat(findLastData.data.lat) !== -1
      ) {
        getLat = findLastData?.data?.["lat"];
      }
    }
    let getLng = findThings?.longitude;
    if (findLastData && findLastData.data && findLastData.data.long) {
      if (
        !isNaN(parseFloat(findLastData.data.long)) &&
        parseFloat(findLastData.data.long) !== 0 &&
        parseFloat(findLastData.data.long) !== -1
      ) {
        getLng = findLastData?.data?.["long"];
      }
    }
    return {
      getLat: getLat,
      getLng: getLng,
    };
  }

  mapOnClick(id) {
    this.closeDrawer();
    const { total_data } = this.state;
    let clientId = this.state.client_id;
    if (this.props.application_id === 17 || this.props.application_id === 12) {
      let thing = null;
      if (total_data?.things?.length) {
        thing = _find(total_data.things, { id: id });
      }
      if (thing) {
        clientId = thing.customer_id;
      }
    }
    let visible_type = "drawer_visible";
    if (window.innerWidth <= 1024) {
      visible_type = "mobile_visible";
    }
    let findThingCat = _find(total_data.things, { id: id })?.category;
    const getThingCat = _find(total_data.things_categories, {
      id: findThingCat,
    });
    this.setState(
      {
        constantCurrentTime: this.getConstLasytRcvdTimeForDc(id),
        select_tab: getThingCat?.pages?.map ? "overview" : "analytics",
        is_dg: findThingCat === 18 || findThingCat === 96,
        selected_id: id,
        [visible_type]: true,
        from_time: moment().startOf("day").unix(),
        upto_time: moment().endOf("day").unix(),
        data_type: "raw",
        from_time_channel: moment().startOf("day").unix(),
        upto_time_channel: moment().endOf("day").unix(),
        data_type_channel: "raw",
        graph_object_data: GraphObjectData,
        date_select: "today",
        date_select_channel: "today",
        trip_loading: true,
        tanker_data_type: "raw",
        tanker_date_select: "today",
        tanker_from_time: moment().startOf("day").unix(),
        tanker_upto_time: moment().endOf("day").unix(),
        tanker_date_select_chamber: "today",
        tanker_from_time_chamber: moment().startOf("day").unix(),
        tanker_upto_time_chamber: moment().endOf("day").unix(),
        tanker_data_type_chamber: "raw",
        client_id: clientId,
        filterIconClicked: false,
      },
      async () => {
        if (window.innerWidth > 1024) {
          this.zoomOnPolyline([
            {
              lat: parseFloat(this.getLatLongAsPerAvailability().getLat),
              lng: parseFloat(this.getLatLongAsPerAvailability().getLng),
            },
          ]);
        }
        await Promise.all([
          this.updateAddress(),
          this.alertData(),
          this.getServiceAlert(),
          aurassureThingCat(
            findThingCat,
            this.props.vendor_id,
            this.props.client_id,
          )
            ? this.aqiHourlyData()
            : "",
          this.isDCEnergy(this.state.selected_id) ? this.energySumData() : "",
          !aurassureThingCat(
            findThingCat,
            this.props.vendor_id,
            this.props.client_id,
          ) &&
          !this.isDgTrue(this.state.selected_id) &&
          !this.isGasGensetTrue(this.state.selected_id) &&
          !this.isFuelTank(this.state.selected_id) &&
          !this.isFleet(this.state.selected_id) &&
          !this.isTankerTruck(this.state.selected_id) &&
          !this.isSolar(this.state.selected_id) &&
          !this.isCompressor(this.state.selected_id) &&
          !this.isProcessAnalyzer(this.state.selected_id) &&
          !this.isAcEnergy(this.state.selected_id) &&
          !this.isAcElectricalMachines(this.state.selected_id) &&
          !this.isExhaustFan(this.state.selected_id) &&
          !this.isBattery(this.state.selected_id) &&
          !this.isSolarPump(this.state.selected_id) &&
          !this.isTemperatureHumidity(this.state.selected_id) &&
          !this.isColdStorage(this.state.selected_id)
            ? this.thingsDataForGraphFunction()
            : "",
          this.isDgTrue(this.state.selected_id) ? this.fetchMissionList() : "",
          this.isFuelTank(this.state.selected_id)
            ? this.fuelTankDataFunction()
            : "",
          this.isFleet(this.state.selected_id)
            ? await this.fleetThingsDataFunction()
            : "",
          this.isTankerTruck(this.state.selected_id)
            ? await this.tankerTruckDataFunction()
            : "",
          this.isTankerTruck(this.state.selected_id)
            ? await this.tankerTruckDataFunctionChamber()
            : "",
          this.isSolar(this.state.selected_id)
            ? await this.fetchSolarPanelData()
            : "",
          this.isCompressor(this.state.selected_id)
            ? await this.fetchCompressorPanelData()
            : "",
          this.isGasGensetTrue(this.state.selected_id)
            ? await this.fetchGasGensetPanelData()
            : "",
          this.isAcEnergy(this.state.selected_id)
            ? await this.fetchAcEnergyPanelData()
            : "",
          this.isAcElectricalMachines(this.state.selected_id)
            ? await this.fetchAcElectricalMachinesPanelData()
            : "",
          this.isExhaustFan(this.state.selected_id)
            ? await this.fetchExhaustFanPanelData()
            : "",
          this.isBattery(this.state.selected_id)
            ? await this.fetchBatteryPanelData()
            : "",
          this.isSolarPump(this.state.selected_id)
            ? await this.fetchSolarPumpPanelData()
            : "",
          this.isFleet(this.state.selected_id) ||
          this.isTankerTruck(this.state.selected_id)
            ? this.fetchMissionListVehicle(this.state.selected_id)
            : "",
        ]).then(() => {
          this.setState({
            panel_loading: false,
          });
        });
      },
    );
  }

  windowResize() {
    this.setState({ screenWidth: window.innerWidth });
  }

  async componentDidMount() {
    this.getFilterCount();
    window.addEventListener("resize", this.windowResize.bind(this));
    if (!this.props.dg_in_iot_mode) {
      if (
        !this.props.location.pathname.search("/map-view") > -1 &&
        !this.props.location.search.includes("filter")
      ) {
        this.props.history.push(
          getBaseUrl(
            this.props,
            "map-view",
            this.props.isGenericView ? true : false,
          ) +
            (this.getFilterDataFromUrl() !== "?undefined"
              ? this.getFilterDataFromUrl()
              : ""),
        );
      }
    }
    if (
      this.props.application_id === 12 &&
      !this.props.location.search.includes("filter")
    ) {
      this.props.history.push(
        getBaseUrl(
          this.props,
          "map-view",
          this.props.isGenericView ? true : false,
        ) + "?filter=partner:1074,customer:326",
      );
    }
    if ([12, 17].includes(this.props.application_id)) {
      await this.fetchCustomersList();
    } else {
      await Promise.all([
        this.getThingsList({
          client_id: this.state.client_id,
          application_id: this.props.application_id,
        }),
        this.fetchTerritoryList()
      ]);
      // added thing list polling to update device status, as it is not available in socket
      this.deviceStatusInterval = setInterval(async () => {
        await this.updateDeviceStatus({
          client_id: this.state.client_id,
          application_id: this.props.application_id,
        });
      }, 60000);
    }
    if (
      this.props.enabled_features?.includes("AccessData:Geofencing") &&
      (this.props.dg_in_iot_mode ||
        this.props.getViewAccess(["GeoFencing:Add", "GeoFencing:View"]))
    ) {
      await this.getGeofenceListOfCustomer(1, "");
    }
  }
  
  async fetchTerritoryList() {
    const territoryResponse = await getTerritoryData(
      this.props.client_id,
      this.props.client_name,
      this.props.enabled_features?.includes("UserManagement:Territory"),
    )

    this.setState({
      territoryList: territoryResponse?.territoryData
    })
  }

  componentWillUnmount() {
    window.removeEventListener("resize", this.windowResize.bind(this));
    disconnectSocketConnection(this.socket);
    clearInterval(this.bucketTime);
    clearInterval(this.deviceStatusInterval);
  }

  drawerIconClick() {
    this.setState({
      drawer_visible: true,
      filterIconClicked: false,
    });
  }

  closeDrawer(kpiClick) {
    let findLatestDataIndex = _findIndex(this.state.latestParameterData, {
      thing_id: parseInt(this.state.selected_id),
    });
    let latestParameterData = this.state.latestParameterData;
    if (findLatestDataIndex > -1) {
      if (
        latestParameterData[findLatestDataIndex].data &&
        Object.keys(latestParameterData[findLatestDataIndex].data).includes(
          "start_time",
        )
      ) {
        delete latestParameterData[findLatestDataIndex].data.start_time;
      }
    }
    this.setState({
      trip_drawer_visible: false,
      last_is_moving: false,
      last_moving_start_time: false,
      drawer_visible: kpiClick ? true : false,
      filterIconClicked: false,
      panel_loading: true,
      selected_id: undefined,
      zoomLevel: undefined,
      centerValue: undefined,
      latLngData: undefined,
      latLngDataWithAddress: undefined,
      latLngDataWithTime: undefined,
      show_graph_modal: false,
      selected_channel: undefined,
      client_id: this.props.client_id,
    });
  }

  getConstLasytRcvdTimeForDc(id) {
    const { total_data, latestParameterData } = this.state;
    let findThing = _find(total_data.things, { id: id });
    let findLatestData = _find(latestParameterData, { thing_id: id });
    if (findLatestData && findLatestData.on_off_moving_status === "1") {
      return findThing?.last_data_received_time;
    } else {
      return moment().unix();
    }
  }

  getFilterCount(e) {
    const { history } = this.props;
    let newArray = history.location.search.includes("filter")
      ? history.location.search.split("filter=")[1]
      : [];
    newArray = newArray?.length ? newArray.split(",") : [];
    this.setState({ filterCount: newArray?.length });
  }

  thingsDrawerClick(id) {
    const { total_data } = this.state;

    const isOjusCompressorOnly =
      total_data?.things.filter((thing) => thing.category === 73)
        .length === this.state.total_data?.things.length &&
        relatedCustomerIds[1121].includes(parseInt(this.props.vendor_id));

    let clientId = this.state.client_id;
    if (this.props.application_id === 17 || this.props.application_id === 12) {
      let thing = null;
      if (total_data?.things?.length) {
        thing = _find(total_data.things, { id: id });
      }
      if (thing) {
        clientId = thing.customer_id;
      }
    }
    let mobile_open = {};
    if (window.innerWidth <= 1024) {
      mobile_open = { mobile_visible: true };
    }
    let findThingCat = _find(total_data.things, { id: id })?.category;
    const getThingCat = _find(total_data.things_categories, {
      id: findThingCat,
    });
    this.setState(
      {
        is_dg: findThingCat === 18 || findThingCat === 96,
        constantCurrentTime: this.getConstLasytRcvdTimeForDc(id),
        selected_id: id,
        select_tab: isOjusCompressorOnly ? "real-time" : getThingCat?.pages?.map ? "overview" : "analytics",
        trip_loading: true,
        ...mobile_open,
        from_time: moment().startOf("day").unix(),
        upto_time: moment().endOf("day").unix(),
        data_type: "raw",
        graph_object_data: GraphObjectData,
        date_select: "today",
        date_select_channel: "today",
        from_time_channel: moment().startOf("day").unix(),
        upto_time_channel: moment().endOf("day").unix(),
        data_type_channel: "raw",
        trip_loading: true,
        tanker_data_type: "raw",
        tanker_date_select: "today",
        tanker_from_time: moment().startOf("day").unix(),
        tanker_upto_time: moment().endOf("day").unix(),
        tanker_date_select_chamber: "today",
        tanker_from_time_chamber: moment().startOf("day").unix(),
        tanker_upto_time_chamber: moment().endOf("day").unix(),
        tanker_data_type_chamber: "raw",
        client_id: clientId,
        filterIconClicked: false,
      },
      async () => {
        if (window.innerWidth > 1024) {
          this.zoomOnPolyline([
            {
              lat: parseFloat(this.getLatLongAsPerAvailability().getLat),
              lng: parseFloat(this.getLatLongAsPerAvailability().getLng),
            },
          ]);
        }
        await Promise.all([
          this.updateAddress(),
          this.alertData(),
          this.getServiceAlert(),
          aurassureThingCat(
            findThingCat,
            this.props.vendor_id,
            this.props.client_id,
          )
            ? this.aqiHourlyData()
            : "",
          this.isDCEnergy(this.state.selected_id) ? this.energySumData() : "",
          !aurassureThingCat(
            findThingCat,
            this.props.vendor_id,
            this.props.client_id,
          ) &&
          !this.isDgTrue(this.state.selected_id) &&
          !this.isGasGensetTrue(this.state.selected_id) &&
          !this.isFuelTank(this.state.selected_id) &&
          !this.isFleet(this.state.selected_id) &&
          !this.isTankerTruck(this.state.selected_id) &&
          !this.isSolar(this.state.selected_id) &&
          !this.isCompressor(this.state.selected_id) &&
          !this.isProcessAnalyzer(this.state.selected_id) &&
          !this.isAcEnergy(this.state.selected_id) &&
          !this.isAcElectricalMachines(this.state.selected_id) &&
          !this.isExhaustFan(this.state.selected_id) &&
          !this.isBattery(this.state.selected_id) &&
          !this.isSolarPump(this.state.selected_id) &&
          !this.isTemperatureHumidity(this.state.selected_id) &&
          !this.isColdStorage(this.state.selected_id)
            ? this.thingsDataForGraphFunction()
            : "",
          this.isDgTrue(this.state.selected_id) ? this.fetchMissionList() : "",
          this.isFuelTank(this.state.selected_id)
            ? this.fuelTankDataFunction()
            : "",
          this.isFleet(this.state.selected_id)
            ? await this.fleetThingsDataFunction()
            : "",
          this.isTankerTruck(this.state.selected_id)
            ? await this.tankerTruckDataFunction()
            : "",
          this.isTankerTruck(this.state.selected_id)
            ? await this.tankerTruckDataFunctionChamber()
            : "",
          this.isSolar(this.state.selected_id)
            ? await this.fetchSolarPanelData()
            : "",
          this.isCompressor(this.state.selected_id)
            ? await this.fetchCompressorPanelData()
            : "",
          this.isGasGensetTrue(this.state.selected_id)
            ? await this.fetchGasGensetPanelData()
            : "",
          this.isAcEnergy(this.state.selected_id)
            ? await this.fetchAcEnergyPanelData()
            : "",
          this.isAcElectricalMachines(this.state.selected_id)
            ? await this.fetchAcElectricalMachinesPanelData()
            : "",
          this.isExhaustFan(this.state.selected_id)
            ? await this.fetchExhaustFanPanelData()
            : "",
          this.isBattery(this.state.selected_id)
            ? await this.fetchBatteryPanelData()
            : "",
          this.isSolarPump(this.state.selected_id)
            ? await this.fetchSolarPumpPanelData()
            : "",
          this.isFleet(this.state.selected_id) ||
          this.isTankerTruck(this.state.selected_id)
            ? this.fetchMissionListVehicle(this.state.selected_id)
            : "",
        ]).then(() => {
          this.setState({
            panel_loading: false,
          });
        });
      },
    );
  }

  closeMobile() {
    this.closeDrawer();
    this.setState({
      mobile_visible: false,
      mobile_render_type: "overview",
      drawer_visible: true,
    });
  }

  changeMobileRenderType(value) {
    this.setState({
      mobile_render_type: value,
    });
  }

  getCategoryOptions(list, things) {
    let categories = [];
    categories = [
      {
        title: "All Assets",
        value: "all",
      },
    ];
    if (things && things.length) {
      let uniqCat = _uniqBy(things, "category");
      if (uniqCat && uniqCat.length) {
        uniqCat.map((cats) => {
          categories.push({
            value: cats.category,
            title: _find(list.things_categories, {
              id: cats.category,
            })?.name,
          });
        });
      }
    }
    return categories;
  }

  getOnlineOfflineOption(category, list) {
    let statusOptions = [];
    if (category === "all" || Array.isArray(category)) {
      let availableThings = [];
      if (list.things && list.things.length) {
        list.things.map((list) => {
          availableThings.push(list.category);
        });
      }
      if (list && list.things_categories && list.things_categories.length) {
        let filterThings = [];
        if (availableThings && availableThings.length) {
          availableThings.map((cats) => {
            const findStatus = _find(list.things_categories, { id: cats });
            if (_find(list.things_categories, { id: cats })) {
              filterThings.push(_find(list.things_categories, { id: cats }));
            }
          });
        }
        filterThings.map((thingCats) => {
          if (
            thingCats?.status_options &&
            (!Array.isArray(category) ||
              (Array.isArray(category) && category.includes(thingCats.id)))
          ) {
            statusOptions.push(...thingCats.status_options);
          }
        });
      }
    } else {
      if (list && list.things_categories && list.things_categories.length) {
        let findSelectedCat = _find(list.things_categories, {
          id: category,
        });
        if (findSelectedCat?.status_options) {
          statusOptions.push(...findSelectedCat.status_options);
        }
      }
    }
    if (isAurassure(this.props.vendor_id, this.props.client_id)) {
      statusOptions = ["Running", "Disconnected"];
    } else {
      statusOptions = [...new Set(statusOptions)];
    }
    const desiredOrder = ["Connected", "Running", "Stopped", "Disconnected"];
    const sortedUniqueValues = desiredOrder.filter((status) =>
      statusOptions.includes(status),
    );
    let options = [];
    if (sortedUniqueValues && sortedUniqueValues.length) {
      sortedUniqueValues.map((onOffOptions) => {
        options.push({
          value: onOffOptions,
          title: isAurassure(this.props.vendor_id, this.props.client_id)
            ? onOffOptions === "Running"
              ? "Online"
              : "Offline"
            : onOffOptions.replace(/_/g, " "),
        });
      });
    }
    return options;
  }

  applyPanelFilterSelect(value, key) {
    const { total_data, customersList, total_sites } = this.state;
    let finalTotalData = total_data;
    let filterThingsAsPerCustomer;
    const deviceStatus = [
      {
        value: "online",
        title: "Online",
      },
      {
        value: "offline",
        title: "Offline",
      },
    ];
    const faultStatus = [
      {
        value: "trip",
        title: "Trip",
      },
      {
        value: "warning",
        title: "Warning",
      },
    ];
    const partnervalue = value[1] || "";
    const customervalue = value[2] || "";
    const optionsNumber = 8;
    if(key === "territories") {
      const siteOptions =
        total_sites?.length && value[0]?.length
          ? total_sites.filter((site) => {
              if (value[0].some((id) => parseInt(id) === site.territory_id)) {
                return true;
              }
              return false;
            })
          : total_sites;
      return {
        selected_values: [value[0]?.length ? value[0] : undefined, 'all', 'all', 'all', 'all', ...value.slice(6).map(i => "")],
        total_options: {
          site: siteOptions,
        },
      }
    }
    if (key === "partner") {
      // this.customerFilterThingsFetch(parseInt(filteredCustOptions?.[0]?.value));
      const filteredCustOptions =
        this.props.application_id === 12 || this.props.client_id === 1819
          ? []
          : [{ value: "all", title: "All Customers" }];
      customersList?.forEach((cust) => {
        if (value[1] && cust.vendor_id !== value[1] && cust.id !== "all") return;
        filteredCustOptions.push({
          value: cust.id,
          title: cust.name,
        });
      });
      let totalCategory = this.getCategoryOptions(
        finalTotalData,
        finalTotalData?.things,
      );
      const categoryIds = [];
      totalCategory.map((cat) => {
        if (cat.value !== "all") categoryIds.push(cat.value);
      });
      let totalOnOffStatus = this.getOnlineOfflineOption(
        categoryIds,
        finalTotalData,
      );
      return {
        selected_values: [
          value[0]?.length ? value[0] : undefined,
          value[1],
          !value[1] && filteredCustOptions?.[1]?.value ? filteredCustOptions?.[1]?.value : filteredCustOptions?.[0]?.value,
          "",
          totalCategory[0]?.value,
          "",
          "",
          "",
          "",
        ],
        total_options: {
          partner: undefined,
          customer: filteredCustOptions,
          site: undefined,
          category: totalCategory,
          status: totalOnOffStatus,
          device_st: deviceStatus,
          fault_st: faultStatus,
        },
      };
    }
    if (key === "customer") {
      // const filterThingsAsPerCustomer = this.customerFilterThingsFetch(parseInt(value[1]));
      // finalTotalData = finalTotalData;
      let totalCategory = this.getCategoryOptions(
        finalTotalData,
        finalTotalData?.things,
      );
      const categoryIds = [];
      totalCategory.map((cat) => {
        if (cat.value !== "all") categoryIds.push(cat.value);
      });
      let totalOnOffStatus = this.getOnlineOfflineOption(
        categoryIds,
        finalTotalData,
      );
      return {
        selected_values: [
          value[0]?.length ? value[0] : undefined,
          partnervalue,
          value[2],
          "",
          totalCategory[0]?.value,
          "",
          "",
          "",
          "",
        ],
        total_options: {
          partner: undefined,
          customer: undefined,
          site: undefined,
          category: totalCategory,
          status: totalOnOffStatus,
          device_st: deviceStatus,
          fault_st: faultStatus,
        },
      };
    }
    if (key === "site") {
      let filterThings =
        value[1] === "all"
          ? finalTotalData.things
          : _filter(finalTotalData.things, { site_id: value[3] });
      let totalCategory = this.getCategoryOptions(finalTotalData, filterThings);
      let categoryIds = [];
      totalCategory.map((cat) => {
        if (cat.value !== "all") categoryIds.push(cat.value);
      });
      let totalOnOffStatus = this.getOnlineOfflineOption(
        categoryIds,
        finalTotalData,
      );
      return {
        selected_values: [
          value[0]?.length ? value[0] : undefined,
          partnervalue,
          customervalue,
          value[3],
          totalCategory[0].value,
          "",
          "",
          "",
          "",
        ],
        total_options: {
          partner: undefined,
          customer: undefined,
          site: undefined,
          category: totalCategory,
          status: totalOnOffStatus,
          device_st: deviceStatus,
          fault_st: faultStatus,
        },
      };
    }
    if (key === "category") {
      let totalOnOffStatus = this.getOnlineOfflineOption(
        value[4],
        finalTotalData,
      );
      return {
        selected_values: [value[0]?.length ? value[0] : undefined, partnervalue, customervalue, value[3], value[4], "", "", "", ""],
        total_options: {
          partner: undefined,
          customer: undefined,
          site: undefined,
          category: undefined,
          status: totalOnOffStatus,
          device_st: deviceStatus,
          fault_st: faultStatus,
        },
      };
    }
    if (key === "status") {
      return {
        selected_values: [
                value[0]?.length ? value[0] : undefined,
                partnervalue,
                customervalue,
                value[3],
                value[4],
                value[5],
                value[6],
                value[7],
                value[8],
              ],
        total_options: {
          partner: undefined,
          customer: undefined,
          site: undefined,
          category: undefined,
          status: undefined,
          device_st: deviceStatus,
          fault_st: faultStatus,
        },
      };
    }
    if (key === "device_st") {
      return {
        selected_values: [
                value[0]?.length ? value[0] : undefined,
                partnervalue,
                customervalue,
                value[3],
                value[4],
                value[5],
                value[6],
                value[7],
                value[8],
              ],
        total_options: {
          partner: undefined,
          customer: undefined,
          site: undefined,
          category: undefined,
          status: undefined,
          device_st: undefined,
          fault_st: faultStatus,
        },
      };
    }
    if (key === "operational_st") {
      return {
        selected_values: [
                value[0]?.length ? value[0] : undefined,
                partnervalue,
                customervalue,
                value[3],
                value[4],
                value[5],
                value[6],
                value[7],
                value[8],
              ],
        total_options: {},
      };
    }
    if (key === "fault_st") {
      return {
        selected_values:
          [
              value[0]?.length ? value[0] : undefined,
                partnervalue,
                customervalue,
                value[3],
                value[4],
                value[5],
                value[6],
                value[7],
                value[8],
              ],
        total_options: {
          partner: undefined,
          customer: undefined,
          site: undefined,
          category: undefined,
          status: undefined,
          device_st: undefined,
          fault_st: undefined,
        },
      };
    }
  }

  changeTabToOverview() {
    this.setState({
      select_tab: "overview",
    });
  }

  getMapZoomOnFilter() {
    const { latestParameterData, total_data } = this.state;
    let finalArray = [];
    latestParameterData.map((latestParam) => {
      if (this.isFiltered(latestParam)) {
        let findThings = _find(total_data.things, {
          id: latestParam.thing_id,
        });
        finalArray.push({
          lat:
            latestParam.data.lat &&
            !isNaN(parseFloat(latestParam.data.lat)) &&
            parseFloat(latestParam.data.lat) !== 0 &&
            parseFloat(latestParam.data.lat) !== -1
              ? parseFloat(latestParam.data.lat)
              : parseFloat(findThings?.latitude),
          lng:
            latestParam.data.long &&
            !isNaN(parseFloat(latestParam.data.long)) &&
            parseFloat(latestParam.data.long) !== 0 &&
            parseFloat(latestParam.data.long) !== -1
              ? parseFloat(latestParam.data.long)
              : parseFloat(findThings?.longitude),
        });
      }
    });
    if (this.getMapDiv() && this.state.myMap) {
      this.zoomOnPolyline(finalArray);
    }
  }

  onUrlChange(thingStatusClicked=false) {
    const { total_data, selectedPartner, selectedCustomer, customersList, territories } =
      this.state;
    let getSiteValue = "all",
      getCategoryValue = "all",
      getStatusValue = "",
      getDeviceStatusValue = "",
      getOperationalStatusValue = "",
      getFaultStatusValue = "";
    const getPartner = this.getUrlBreak("partner")
      ? parseInt(this.getUrlBreak("partner"))
      : this.props.application_id === 17 ? undefined : selectedPartner;
    const getCustomer = this.getUrlBreak("customer")
      ? parseInt(this.getUrlBreak("customer"))
      : this.props.application_id === 16 || thingStatusClicked ? selectedCustomer : "all";
    getSiteValue =
      this.getUrlBreak("site") && this.getUrlBreak("site") !== "all"
        ? parseInt(this.getUrlBreak("site"))
        : "all";
    getCategoryValue =
      this.getUrlBreak("category") && this.getUrlBreak("category") !== "all"
        ? parseInt(this.getUrlBreak("category"))
        : "all";
    getStatusValue =
      this.getUrlBreak("status") &&
      (this.getUrlBreak("status") !== "" || this.getUrlBreak("status") !== null)
        ? this.getUrlBreak("status")
        : "";
    getDeviceStatusValue =
      this.getUrlBreak("device_st") &&
      (this.getUrlBreak("device_st") !== "" ||
        this.getUrlBreak("device_st") !== null)
        ? this.getUrlBreak("device_st")
        : "";
    getOperationalStatusValue =
      this.getUrlBreak("operational_st") &&
      (this.getUrlBreak("operational_st") !== "" ||
        this.getUrlBreak("operational_st") !== null)
        ? this.getUrlBreak("operational_st")
        : "";
    getFaultStatusValue =
      this.getUrlBreak("fault_st") &&
      (this.getUrlBreak("fault_st") !== "" ||
        this.getUrlBreak("fault_st") !== null)
        ? this.getUrlBreak("fault_st")
        : "";

    const filteredSiteList = this.getFilteredSiteList(this.state.total_sites, getOperationalStatusValue);
    
    let filterThings =
      getSiteValue === "all"
        ? total_data.things
        : _filter(total_data.things, { site_id: getSiteValue });
    let totalCategory = this.getCategoryOptions(total_data, filterThings);
    let totalOnOffStatus = this.getOnlineOfflineOption(
      getCategoryValue,
      total_data,
    );
    let getSearchedValue =
      this.getUrlBreak("search") && this.getUrlBreak("search") !== ""
        ? this.getUrlBreak("search")
        : "";
    const filteredCustOptions =
      this.props.application_id === 12 || this.props.client_id === 1819
        ? []
        : [{ value: "all", title: "All Customers" }];
    customersList?.forEach((cust) => {
      if (parseInt(getPartner) && cust.vendor_id !== parseInt(getPartner) && cust.id !== "all") return;
      filteredCustOptions.push({
        value: cust.id,
        title: cust.name,
      });
    });

    let territoryValue = [];
    const decodedValue = decodeURIComponent(this.getUrlBreak("territories"));
    if (
      decodedValue &&
      decodedValue !== "undefined" &&
      decodedValue !== "null"
    ) {
      territoryValue = decodedValue
        .split(",")
        .map((item) => (isNaN(parseInt(item)) ? item : parseInt(item)));
    }
    const isTerritoryUpdated =
      territoryValue?.length !== (territories || []).length ||
      territoryValue?.some((id) => !territories?.includes(id));
    const isPartnerUpdated = (getPartner || selectedPartner) && getPartner !== selectedPartner;
    const isCustomerUpdated = (getCustomer || selectedCustomer) && getCustomer !== selectedCustomer;
    this.setState(
      {
        url_changed: true,
        customerOptions: filteredCustOptions,
        site: getSiteValue,
        total_category: totalCategory,
        category: getCategoryValue,
        total_on_off_status: totalOnOffStatus,
        on_off_status: getStatusValue,
        on_off_device_status: getDeviceStatusValue,
        operational_st: getOperationalStatusValue,
        filteredSiteList: filteredSiteList,
        faultSt: getFaultStatusValue,
        search_data: getSearchedValue,
        selectedAssetStatusBarValue: getStatusValue.replace(/_/g, " "),
        selectedDeviceStatusBarValue: getDeviceStatusValue,
        selectedFaultStatusBarValue: getFaultStatusValue,
        selectedPartner: getPartner,
        selectedCustomer: getCustomer,
        territories: territoryValue?.length ? territoryValue : undefined,
        mapLoading: isPartnerUpdated || isTerritoryUpdated || isCustomerUpdated
      },
      () => {
        if (this.props.application_id === 12 && isPartnerUpdated) {
          this.fetchCustomersList();
        } else if(isPartnerUpdated ||isTerritoryUpdated || isCustomerUpdated) {
          this.getThingsList({
            client_id: getCustomer ||this.props.client_id,
            application_id: 16
          })
        }
      },
    );
  }

  getUrlBreak(value) {
    let geturlData;
    let searchValue = this.props?.history?.location?.search;
    if (
      this.props.dg_in_iot_mode &&
      searchValue?.includes("&end_iot_views=1")
    ) {
      searchValue =
        this.props?.history?.location?.search.split("&end_iot_views=1")[1] ||
        "";
    }
    let geturlDataIndex = _findIndex(searchValue?.split(","), function (o) {
      return o.includes(value);
    });
    if (geturlDataIndex > -1) {
      geturlData = searchValue?.split(",")[geturlDataIndex].split(":")[1];
    }
    return geturlData;
  }

  isFiltered(latestThingData, dontFilterWith) {
    const {
      total_data,
      category,
      on_off_status,
      on_off_device_status,
      operational_st,
      filteredSiteList,
      site,
      search_data,
      selectedPartner,
      selectedCustomer,
    } = this.state;
    const { application_id } = this.props;
    let isFiltered = false;
    let findCat = _find(total_data.things_categories, {
      id: latestThingData.category,
    });
    let statusObj = {
      Connected: ["1", "0", ""],
      Disconnected: ["2"],
    };
    statusObj["Running"] = ["1"];
    statusObj["Gensets_Off"] = ["0", "", "2"];
    statusObj["Gensets_On"] = ["1"];
    if (findCat && findCat.status_options) {
      if (findCat.status_options.includes("Moving")) {
        statusObj["Moving"] = ["1"];
      }
      if (findCat.status_options.includes("Stopped")) {
        statusObj["Stopped"] = ["0", ""];
      }
    }
    const categoryCondition =
      latestThingData.category === category || category === "all" || !category;
    let statusCondition =
      !on_off_status ||
      statusObj[on_off_status]?.includes(latestThingData.on_off_moving_status);
    if(on_off_status === "Disconnected" && !this.context?.isSistemaBioCustomer && !isAurassure(
      this.props.vendor_id,
      this.props.client_id,
    )) {
      statusCondition = statusCondition && latestThingData?.device_status === "online";
    }  
    const deviceCondition =
      !on_off_device_status ||
      latestThingData.device_status.includes(on_off_device_status);
    const siteCondition =
      latestThingData.site_id === site || site === "all" || !site;
    const partnerCondition = latestThingData.partner === selectedPartner;
    const customerCOndition = latestThingData.customer === selectedCustomer;
    let faultStatusCondition = true;
    const operationalStatusCondition = !operational_st || filteredSiteList.includes(latestThingData.site_id);

    if (this.state.faultSt) {
      faultStatusCondition = false;
      const findThing = total_data?.things?.find(
        (o) => o.id === latestThingData?.thing_id,
      );
      if (latestThingData?.data && Object.keys(latestThingData?.data).length) {
        Object.keys(latestThingData?.data).map((paramKey) => {
          if (paramKey.includes("debug_f")) {
            const findFaultParamName = findThing.parameters.find(
              (o) => o.key === paramKey,
            );
            if (
              this.state.faultSt === "trip" &&
              findFaultParamName?.name?.toLowerCase().includes("trip") &&
              findFaultParamName?.value === "1"
            ) {
              faultStatusCondition = true;
            } else if (
              this.state.faultSt === "warning" &&
              findFaultParamName?.name?.toLowerCase().includes("warning") &&
              findFaultParamName?.value === "1"
            ) {
              faultStatusCondition = true;
            }
          }
        });
      }
    }

    let filterCondition = [12, 17].includes(application_id)
      ? partnerCondition &&
        customerCOndition &&
        deviceCondition &&
        categoryCondition &&
        statusCondition &&
        siteCondition &&
        operationalStatusCondition &&
        faultStatusCondition
      : deviceCondition &&
        categoryCondition &&
        statusCondition &&
        siteCondition &&
        operationalStatusCondition &&
        faultStatusCondition;
    if (dontFilterWith) {
      if (dontFilterWith === "category") {
        filterCondition =
          deviceCondition &&
          statusCondition &&
          siteCondition &&
          operationalStatusCondition &&
          faultStatusCondition;
      } else if (dontFilterWith === "status") {
        filterCondition =
          deviceCondition &&
          categoryCondition &&
          siteCondition &&
          operationalStatusCondition &&
          faultStatusCondition;
      } else if (dontFilterWith === "device_status") {
        filterCondition =
          statusCondition &&
          categoryCondition &&
          siteCondition &&
          operationalStatusCondition &&
          faultStatusCondition;
      } else if (dontFilterWith === "site") {
        filterCondition =
          categoryCondition && statusCondition && faultStatusCondition;
      } else if (dontFilterWith === "partner") {
        filterCondition =
          categoryCondition && statusCondition && faultStatusCondition;
      } else if (dontFilterWith === "customer") {
        filterCondition =
          categoryCondition && statusCondition && faultStatusCondition;
      }
    }
    if (
      filterCondition &&
      (search_data === "" ||
        latestThingData.name
          ?.toLowerCase()
          ?.includes(search_data?.toLowerCase()))
    ) {
      isFiltered = true;
    }
    return isFiltered;
  }

  thingStatus() {
    const { latestParameterData } = this.state;
    let getStatusdata = [];
    if (latestParameterData?.length) {
      latestParameterData.forEach((latestParam) => {
        const findThingCat = this.state.total_data.things_categories.find(
          (category) => category.id === latestParam.category,
        );
        let findStatusOptions = findThingCat?.status_options;
        if (this.isFiltered(latestParam, "status")) {
          getStatusdata.push({
            deviceStatus: latestParam.device_status,
            on_off_moving_status: latestParam.on_off_moving_status,
            status_options: findStatusOptions,
          });
        }
      });
    }
    return getStatusdata;
  }

  getGraphData(panel_data) {
    let paramGraphs = [];
    const { graph_loading } = this.state;
    if (panel_data && panel_data.params && panel_data.params.length) {
      panel_data.params.map((params) => {
        if (panel_data.channel_graph.graph_data_param) {
          paramGraphs.push(
            <>
              <div className="title-graph">
                {params.includes("avl_st")
                  ? "Availability Status"
                  : params.includes("volt") || params.includes("mvol")
                    ? "Voltage"
                    : params.includes("curr") || params.includes("mcurr")
                      ? "Current"
                      : params.includes("watt") || params.includes("mt_power")
                        ? "Power"
                        : params.includes("flow")
                          ? "Flow Rate"
                          : params.includes("gw_level")
                            ? "Water Level"
                            : ""}
              </div>
              {graph_loading ? (
                <Skeleton />
              ) : (
                <GraphHighcharts
                  graphData={
                    panel_data.channel_graph.graph_data_param[params].graph_data
                  }
                />
              )}
            </>,
          );
        }
      });
    }
    return {
      paramGraphs: paramGraphs,
    };
  }

  goToPage(pagename, key) {
    this.props.history.push(
      getBaseUrl(
        this.props,
        pagename,
        this.props.isGenericView ? true : false,
      ) +
        "?from=map" +
        (this.getFilterDataFromUrl() !== "?undefined"
          ? this.getFilterDataFromUrl()
          : "") +
        "&thing_id=" +
        key,
    );
  }

  clusterChange() {
    const { cluster_view } = this.state;
    const { user_preferences } = this.props;
    let preferences = {
      map_cluster_view: user_preferences?.map_cluster_view,
    };
    this.setState(
      {
        cluster_view: !cluster_view,
      },
      async () => {
        preferences["map_cluster_view"] = this.state.cluster_view;
        await setPreferences(
          this.props.user_id,
          this.state.client_id,
          this.props.application_id,
          preferences,
        );
      },
    );
  }

  genericFilterRefOnclick() {
    this.setState(
      {
        drawer_visible: true,
        filterIconClicked: true,
        selected_id: undefined,
      },
      () => {
        this.filterIconRef?.current?.click();
      },
    );
  }

  drawerCancelCallback() {
    this.setState({
      drawer_visible: false,
      filterIconClicked: false,
    });
  }

  drawerApplyCallback() {
    this.setState({
      drawer_visible: false,
      filterIconClicked: false,
    });
  }

  componentDidUpdate(prevProps, prevState) {
    if (this.props.location !== prevProps.location) {
      this.getFilterCount();
    }
  }

  onMobileStatusDrawerOpen(e) {
    this.setState({
      mobile_status_drawer_visible: e,
    });
  }

  deviceStatus(noFilter = false) {
    const { latestParameterData } = this.state;

    let onlineDevices = [],
      offlineDevices = [];
      
    if (latestParameterData?.length) {
      latestParameterData.forEach((latestParam) => {
        if(this.isFiltered(latestParam, "device_status") || noFilter) {
          const device = latestParam.deviceDetails;
          if (device.status === "online") {
            onlineDevices.push(device);
          } else {
            offlineDevices.push(device);
          }
        }
      });
    }

    onlineDevices = [..._uniqBy(onlineDevices, "qr_code")];
    offlineDevices = [..._uniqBy(offlineDevices, "qr_code")];
    const finalDeviceStatus = [
      {
        title: "Devices",
        total: true,
        value: [...onlineDevices, ...offlineDevices].length,
      },
      {
        title: "Online",
        value: onlineDevices.length,
      },
      {
        title: "Offline",
        value: offlineDevices.length,
      },
    ];
    return finalDeviceStatus;
  }

  faultStatusFilter() {
    const { latestParameterData, listId } = this.state;
    let tripFault = 0,
      warningFault = 0,
      totalFaults = 0;
    if (latestParameterData?.length) {
      latestParameterData.map((findParamList) => {
        const {faults, fault_status} = totalActiveFaultsWithWarnTripStatus(
          findParamList,
          this.props.vendor_id,
          this.state.total_data,
        );
        if(fault_status) totalFaults++;

        if (faults?.trip?.length) {
          tripFault++;
        }
        if (faults?.warning?.length) {
          warningFault++;
        }
      });
    }
    return [
      {
        title: "Faulty Assets",
        value: totalFaults,
        total: true,
      },
      {
        title: "Trip",
        value: tripFault,
      },
      {
        title: "Warning",
        value: warningFault,
      },
    ];
  }

  onThingStatusClicked(e) {
    this.setState({
      drawer_visible: false,
      selected_id: undefined,
      // filterIconClicked: false,
    });
    const searchUrl = this.props.location.search;
    const titleFor = ["faulty assets", "trip", "warning"].includes(
      e.title.toLowerCase(),
    )
      ? "fault"
      : ["devices", "online", "offline"].includes(e.title.toLowerCase())
        ? "device"
        : "asset";
    const onOffStatus =
      titleFor === "asset" ? e.title : this.state.on_off_status;
    const onOffDeviceStatus =
      titleFor === "device"
        ? e.title.toLowerCase()
        : this.state.on_off_device_status;
    const faultSt =
      titleFor === "fault" ? e.title.toLowerCase() === "faulty assets" ? "faults" : e.title.toLowerCase() : this.state.faultSt;
    let finalUrl = "?filter=";
    if (
      !e.title.toLowerCase().includes("total") &&
      (titleFor === "asset" || searchUrl.includes("status"))
    ) {
      finalUrl += "status:" + onOffStatus.replace(/ /g, "_") + ",";
    }
    if (
      onOffDeviceStatus !== "devices" &&
      (titleFor === "device" || searchUrl.includes("device_st"))
    ) {
      finalUrl += "device_st:" + onOffDeviceStatus + ",";
    }
    if (
      faultSt !== "faults" &&
      (titleFor === "fault" || searchUrl.includes("fault_st"))
    ) {
      finalUrl += "fault_st:" + faultSt + ",";
    }
    if(searchUrl.includes("operational_st") || this.state.operational_st) {
      finalUrl += "operational_st:" + this.state.operational_st + ",";
    }
    if (searchUrl.includes("search")) {
      finalUrl += "&search:" + this.state.search_data + ",";
    }
    finalUrl = finalUrl.replace(/,$/, "");
    if (searchUrl.includes("filter")) {
      let filterData = searchUrl.split("filter=")[1];
      let filterArray = filterData.split(",");
      let filterStatusArrayIndex = filterArray.findIndex((filter) =>
        filter.includes("status"),
      );
      if (filterStatusArrayIndex > -1) {
        filterArray.splice(filterStatusArrayIndex, 1);
      }
      let filterDeviceStatusArrayIndex = filterArray.findIndex((filter) =>
        filter.includes("device_st"),
      );
      if (filterDeviceStatusArrayIndex > -1) {
        filterArray.splice(filterDeviceStatusArrayIndex, 1);
      }
      let filterFaultStatusArrayIndex = filterArray.findIndex((filter) =>
        filter.includes("fault_st"),
      );
      if (filterFaultStatusArrayIndex > -1) {
        filterArray.splice(filterFaultStatusArrayIndex, 1);
      }
      this.setState(
        {
          url_changed: true,
          on_off_status: onOffStatus,
          on_off_device_status: onOffDeviceStatus,
          faultSt: faultSt,
        },
        () => {
          this.props.history.push(
            `${getBaseUrl(
              this.props,
              "map-view",
              this.props.isGenericView ? true : false,
            )}${finalUrl}`,
          );
          this.onUrlChange(true);
        },
      );
    } else {
      this.setState(
        {
          url_changed: true,
          on_off_status: onOffStatus,
          on_off_device_status: onOffDeviceStatus,
          faultSt: faultSt,
        },
        () => {
          this.props.history.push(
            `${getBaseUrl(
              this.props,
              "map-view",
              this.props.isGenericView ? true : false,
            )}${finalUrl}`,
          );
          this.onUrlChange(true);
        },
      );
    }
  }

  render() {
    const {
      selected_id,
      latestParameterData,
      select_tab,
      total_data,
      cluster_view,
      geofence_config_drawer_visible,
      geofence_drawer_visible,
      filterCount,
      total_on_off_status,
    } = this.state;
    const thing_activity_status = useThingStatusOptions(
      this.props.t,
      this.thingStatus(),
      total_on_off_status,
      isAurassure(this.props.vendor_id, this.props.client_id),
      this.deviceStatus(true),
      this.context?.isSistemaBioCustomer
    );

    const isSistemaBioCustomer = this.context.vendor_id === 1184;
    
    const isOjusCompressorOnly =
      this.state.total_data?.things.filter((thing) => thing.category === 73)
        .length === this.state.total_data?.things.length &&
        relatedCustomerIds[1121].includes(parseInt(this.props.vendor_id));

    let pageRender = "";
    if (this.state.page_loading) {
      pageRender = <Loading />;
    } else {
      let drawerData = this.getThingsListDrawerData();
      let findSelectedThing = _find(total_data.things, {
        id: parseInt(selected_id),
      });
      let getCategory = findSelectedThing?.category;
      const getThingCat = _find(total_data.things_categories, {
        id: getCategory,
      });
      let genSetParam = getFinalGensetStatusParamManipulationFunction(
        findSelectedThing?.parameters,
        _find(latestParameterData, {
          thing_id: parseInt(selected_id),
        }),
      );
      let showGenSetStatusDiv = "";
      if (genSetParam?.isGensetStatus) {
        showGenSetStatusDiv = (
          <div className="genset-status-container">
            <GensetStatus
              genset_status_param={genSetParam.realTimeParam}
              load_on_array={genSetParam.loadon}
              alerts={genSetParam.alert}
            />
          </div>
        );
      }
      let commonHeaderData = this.getCommonHeaderValue();
      let panelData = "";
      let pollutionThingCats = isAurassure(
        this.props.vendor_id,
        this.props.client_id,
      )
        ? [21, 23, 44, 85]
        : [21, 22, 102, 23, 44, 85];
      if (getCategory === 18 || getCategory === 96) {
        panelData = this.getDgPanelData(commonHeaderData?.on_off_moving_status);
      } else if (getCategory === 45) {
        panelData = this.coldStoragePanelData();
      } else if (getCategory === 71) {
        panelData = this.getFuelTankPanelData();
      } else if (getCategory === 67 || getCategory === 76) {
        panelData = this.getFleetpanelDataFunction();
      } else if (getCategory === 86) {
        panelData = this.getFlowMeterpanelDataFunction();
      } else if (getCategory === 63) {
        panelData = this.getBorewellpanelDataFunction();
      } else if (getCategory === 74) {
        panelData = this.getTankerpanelDataFunction();
      } else if (pollutionThingCats.includes(getCategory)) {
        panelData = this.getPollutionpanelDataFunction();
      } else if (
        aurassureThingCat(
          getCategory,
          this.props.vendor_id,
          this.props.client_id,
        )
      ) {
        panelData = this.aurassurePanelFunc(this.props.t);
      } else if (getCategory === 91) {
        panelData = this.solarPanelData();
      } else if (getCategory === 73) {
        panelData = this.compressorPanelData();
      } else if (getCategory === 96) {
        panelData = this.gasGensetPanelData();
      } else if (getCategory === 78) {
        panelData = this.acElectricalMachinesPanelData();
      } else if (getCategory === 99) {
        panelData = this.exhaustFanPanelData();
      } else if (getCategory === 79 || getCategory === 101) {
        panelData = this.acEnergyPanelData();
      } else if (getCategory === 92) {
        panelData = this.batteryPanelData();
      } else if (getCategory === 90) {
        panelData = this.tempHumidPanelData();
      } else if (getCategory === 93) {
        panelData = this.solarInverterPanelData();
      } else if (getCategory === 100) {
        panelData = this.elevatorPanelData();
      } else if (getCategory === 103) {
        panelData = this.solarPumpPanelData();
      } else if (getCategory === 104) {
        panelData = this.mriPanelData();
      } else {
        panelData = this.getEnergypanelDataFunction();
      }

      let allGraphs = this.isFuelTank(this.state.selected_id)
        ? this.getPanelGraph()
        : this.isFleet(this.state.selected_id)
          ? this.fleetGraphConfigForPanel()
          : this.getGraphData(panelData);
      let mapData = this.mapFunction();
      let mapRawData = {
        rawData: this.state.latestParameterData,
        modifiedResponseRaw: this.state.modifiedResponse,
      };
      let graphModal = (
        <PanelTrend
          show_graph_modal={this.state.show_graph_modal}
          panel_data={panelData}
          viewTrendClose={() => this.viewTrendClose()}
          graph_loading={this.state.graph_loading}
          date_select_channel={this.state.date_select_channel}
          changeDateChannels={(e) => this.changeDateChannels(e)}
          selected_channel={this.state.selected_channel}
          all_graphs={allGraphs}
        />
      );
      let PageMapRender = this.state.mapLoading ? (
        <div id="generic_map_view">
          <Loading />
        </div>
      ) : this.getThingsWithMap(this.state.total_data)?.length ? (
        <GoogleMapComponent
          cluster_view={cluster_view}
          data={mapRawData}
          mapLegend={[
            <div className="legend-items">
              <img src={legendRunning} />
              Running
            </div>,
            <div className="legend-items">
              <img src={legendStopped} />
              Stopped
            </div>,
            <div className="legend-items">
              <img src={legendNotConnected} />
              Not Connected
            </div>,
            <div className="legend-items">
              <img src={legendOffline} />
              Offline
            </div>,
          ]}
          mapOnClick={this.mapOnClick.bind(this)}
          latLngData={this.state.latLngData}
          trip_drawer_visible={this.state.trip_drawer_visible}
          listId={selected_id}
          {...mapData.config}
          legends={mapData.map_legend}
          mapPointers={mapData.map_pointers}
          zoomLevel={this.state.zoomLevel}
          centerValue={this.focusMapBasedOnLocation()}
          onGoogleApiLoaded={this.onGoogleApiLoaded}
          reverseGeocode={async (latlng) => await this.reverseGeocode(latlng)}
          isAurassure={isAurassure(this.props.vendor_id, this.props.client_id)}
          drawingMode={this.state.isDrawingMode}
          toggleDrawingMode={this.toggleDrawingMode}
          geofenceDrawingTool={this.state.geofenceConfigData.geofence_type}
          geofenceConfig={this.state.geofenceConfigData}
          handleGeofencingData={this.handleGeofenceData}
          showGenfences={this.state.showGeofenceOnMap}
          geofenceList={this.state.geofenceData}
          selectedGeofence={this.state.selectedGeofence}
          editingMode={this.state.isEditing}
          isSistemaBioCustomer={this.context?.isSistemaBioCustomer}
        />
      ) : (
        ""
      );
      let tripRoute = (
        <TripRoute
          missionList={this.state.vehicleMissionList?.response?.Missions}
          latLngDataWithAddress={this.state.latLngDataWithAddress}
          trip_drawer_visible={this.state.trip_drawer_visible}
          last_is_moving={this.state.last_is_moving}
          last_moving_start_time={this.state.last_moving_start_time}
          toggleTripDrawer={this.toggleTripDrawer.bind(this)}
          selected_trip_id={this.state.selected_trip_id}
          tripSelect={this.tripSelect.bind(this)}
          path_loading={this.state.path_loading}
          trip_header_data={this.getTripHeaderValue()}
          panel_data={this.getFleetpanelDataFunction()}
          loading_for_task={this.state.loading_for_task}
          mobileType={window.innerWidth <= 576}
        />
      );
      let locationFindActionHeader = (
        <div className="find-location-actions">
          <div className="location-search">
            <Autocomplete
              onLoad={this.onAutoCompleteLoad}
              onPlaceChanged={this.onPlaceChanged}
            >
              <AntInput
                type="text"
                placeholder="Search Location"
                value={this.state.tempLocation?.address}
                onChange={(e) => {
                  const addDetails = JSON.parse(
                    JSON.stringify(this.state.autoCompleteLocation || {}),
                  );
                  addDetails.address = e.target.value;
                  this.setState({
                    autoCompleteLocation: addDetails,
                  });
                }}
              />
            </Autocomplete>
          </div>
          <div className="lat-lng-search">
            <div>Lat</div>
            <AntInput
              value={this.state.tempLocation?.lat}
              onChange={(e) => this.latlngHandler(e.target.value, "lat")}
            />
            <div>Long</div>
            <AntInput
              value={this.state.tempLocation?.lng}
              onChange={(e) => this.latlngHandler(e.target.value, "lng")}
            />
            <div className="right-icon-view" onClick={this.searchByLatLng}>
              <ArrowRightOutlined style={{ verticalAlign: "middle" }} />
            </div>
          </div>
        </div>
      );
      let mobileRender = "";
      if (this.mobileViewEnabled) {
        mobileRender = (
          <MapMobileDrawer
            latLngDataWithAddress={this.state.latLngDataWithAddress}
            mobile_visible={this.state.mobile_visible}
            header_value={commonHeaderData}
            mobile_render_type={this.state.mobile_render_type}
            closeMobile={() => this.closeMobile()}
            changeMobileRenderType={this.changeMobileRenderType.bind(this)}
            panel_data={panelData}
            tanker_date_select={this.state.tanker_date_select}
            tanker_graph_loading={this.state.tanker_graph_loading}
            tanker_date_select_chamber={this.state.tanker_date_select_chamber}
            tanker_graph_loading_chamber={
              this.state.tanker_graph_loading_chamber
            }
            changeDateTanker={(e) => this.changeDateTanker(e)}
            changeDateChambersTanker={(e) => this.changeDateChambersTanker(e)}
            closeDrawer={() => this.closeDrawer()}
            last_is_moving={this.state.last_is_moving}
            last_moving_start_time={this.state.last_moving_start_time}
            show_graph_modal={this.state.show_graph_modal}
            viewTrendClose={() => this.viewTrendClose()}
            date_select_channel={this.state.date_select_channel}
            changeDateChannels={(e) => this.changeDateChannels(e)}
            selected_channel={this.state.selected_channel}
            viewTrendClick={() => this.viewTrendClick()}
            getRemoteAccess={
              typeof this.props.getRemoteAccess === "function"
                ? this.props.getRemoteAccess()
                : undefined
            }
            getRemoteLockAccess={
              typeof this.props.getRemoteLockAccess === "function"
                ? this.props.getRemoteLockAccess()
                : undefined
            }
            client_id={this.state?.client_id}
            application_id={this.props?.application_id}
            socket={this.socket}
            isAurassure={aurassureThingCat(
              getCategory,
              this.props.vendor_id,
              this.props.client_id,
            )}
          >
            <div
              className={
                "mobile-body-div " +
                (this.isDgTrue(selected_id) &&
                commonHeaderData?.on_off_moving_status !== "2"
                  ? "dg"
                  : "")
              }
            >
              <MobilePageSelection
                changeTab={(e) => this.changeTab(e)}
                select_tab={select_tab}
                overView={getThingCat?.pages?.map}
                detailed_view={
                  isDetailedView(selected_id, this.state.total_data) &&
                  !pollutionThingCats.includes(getCategory)
                }
                analog_view={isRealTime(selected_id, this.state.total_data)}
                isOjusCompressorOnly={isOjusCompressorOnly}
              />
              {this.state.mobile_render_type === "overview" && selected_id ? (
                this.state.panel_loading ? (
                  <Loading />
                ) : select_tab === "overview" ? (
                  <>
                    <PanelContent
                      t={this.props.t}
                      pathDetailsClick={() => this.pathDetailsClick()}
                      constantCurrentTime={this.state.constantCurrentTime}
                      today_enrg_value={this.state.today_enrg_value}
                      mobile_type
                      detailed_view={isDetailedView(
                        selected_id,
                        this.state.total_data,
                      )}
                      analog_view={isRealTime(
                        selected_id,
                        this.state.total_data,
                      )}
                      header_value={commonHeaderData}
                      changeDateTanker={(e) => this.changeDateTanker(e)}
                      changeDateChambersTanker={(e) =>
                        this.changeDateChambersTanker(e)
                      }
                      panel_data={panelData}
                      tanker_date_select={this.state.tanker_date_select}
                      tanker_graph_loading={this.state.tanker_graph_loading}
                      tanker_date_select_chamber={
                        this.state.tanker_date_select_chamber
                      }
                      tanker_graph_loading_chamber={
                        this.state.tanker_graph_loading_chamber
                      }
                      changeDateChannels={(e) => this.changeDateChannels(e)}
                      graph_loading={this.state.graph_loading}
                      date_select_channel={this.state.date_select_channel}
                      user_preferences={this.props.user_preferences}
                      closeDrawer={() => this.closeDrawer()}
                      currentAddress={this.state.currentAddress}
                      viewTrendClick={(e) => this.viewTrendClick(e)}
                      selected_channel={this.state.selected_channel}
                      all_graphs={allGraphs}
                      closeDetailsPanel={() => this.closeDetailsPanel()}
                      total_data={total_data}
                      socket={this.socket}
                      mobileViewEnabled={this.mobileViewEnabled}
                      mobile_view_get={true}
                      selected_id={selected_id}
                      {...this.props}
                      isAurassure={aurassureThingCat(
                        getCategory,
                        this.props.vendor_id,
                        this.props.client_id,
                      )}
                      pollutionThingCats={pollutionThingCats}
                    />
                  </>
                ) : select_tab === "analytics" ? (
                  <DetailedView
                    total_data={total_data}
                    socket={this.socket}
                    mobileViewEnabled={this.mobileViewEnabled}
                    mobile_view_get={true}
                    selected_id={selected_id}
                    panel_data={panelData}
                    {...this.props}
                  />
                ) : (
                  <RealTime
                    total_data={total_data}
                    socket={this.socket}
                    mobileViewEnabled={this.mobileViewEnabled}
                    mobile_view_get={true}
                    selected_id={selected_id}
                    changeTabToOverview={() => this.changeTabToOverview()}
                    {...this.props}
                  />
                )
              ) : (
                PageMapRender
              )}
            </div>
          </MapMobileDrawer>
        );
      }
      let listOpenBtnCond =
        this.getThingsWithMap(this.state.total_data)?.length &&
        this.state.drawer_visible;
      let geofenceDrawerOpenCond = this.state.geofence_drawer_visible;
      const faultStatus = this.faultStatusFilter();
      pageRender = (
        <div
          id="generic_map_view_page"
          className={this.props.bannerToBeShown ? "map-banner" : ""}
        >
          {PageMapRender}
          {window.innerWidth < 576 ? (
            <MapMobileStatus
              clustrConfigs={{
                checked: cluster_view,
                clusterChange: () => this.clusterChange(),
                size: "small",
              }}
              assetStatus={thing_activity_status}
              deviceStatus={this.deviceStatus()}
              faultStatus={faultStatus}
              filterCount={filterCount}
              genericFilterRefOnclick={() => {
                this.genericFilterRefOnclick();
              }}
              onMobileStatusDrawerOpen={(e) => this.onMobileStatusDrawerOpen(e)}
              isAurassure={isAurassure(
                this.props.vendor_id,
                this.props.client_id,
              )}
            />
          ) : (
            <div className="map-header">
              <div className="map-filters">
                {this.mobileViewEnabled ? (
                  ""
                ) : this.state.geofence_drawer_visible ? (
                  locationFindActionHeader
                ) : (
                  <ThingStatus
                    t={this.props.t}
                    data={thing_activity_status}
                    selectedValue={this.state.selectedAssetStatusBarValue}
                    showInfo={
                      isAurassure(this.props.vendor_id, this.props.client_id)
                        ? false
                        : true
                    }
                    onThingStatusClicked={
                      isAurassure(this.props.vendor_id, this.props.client_id)
                        ? undefined
                        : (e) => this.onThingStatusClicked(e)
                    }
                  />
                )}
                {isAurassure(this.props.vendor_id, this.props.client_id) || isSistemaBioCustomer ? (
                  ""
                ) : (
                  <div style={{ "margin-left": 10 }}>
                    <ThingStatus
                      t={this.props.t}
                      data={this.deviceStatus()}
                      showInfo={true}
                      onThingStatusClicked={(e) => this.onThingStatusClicked(e)}
                      selectedValue={this.state.selectedDeviceStatusBarValue}
                    />
                  </div>
                )}
                <FilterIconWithCount
                  visible={true}
                  count={filterCount}
                  backgroundWhite={true}
                  onClick={() => {
                    this.genericFilterRefOnclick();
                  }}
                />
              </div>
              
              <div className="fault-cluster-container">
                {!isSistemaBioCustomer && _find(faultStatus, { total: true })?.value > 0 ? (
                  <div className="map-filters">
                    <div className="status-bar">
                      <ThingStatus
                        t={this.props.t}
                        data={faultStatus}
                        onThingStatusClicked={(e) =>
                          this.onThingStatusClicked(e)
                        }
                        selectedValue={this.state.selectedFaultStatusBarValue}
                        showInfo={true}
                        {...this.props}
                      />
                    </div>
                  </div>
                ) : (
                  ""
                )}

                <ClusterView
                  checked={cluster_view}
                  clusterChange={() => this.clusterChange()}
                  size="small"
                />
              </div>
            </div>
          )}
          {mobileRender}
          {this.isFleet(selected_id) || this.isTankerTruck(selected_id)
            ? tripRoute
            : ""}
          {this.state.show_graph_modal ? graphModal : ""}
          {!selected_id && !this.mobileViewEnabled ? (
            this.props.enabled_features?.includes("AccessData:Geofencing") &&
            ((this.props.dg_in_iot_mode &&
              this.props.client_id !== this.props.iotViewsVendorId) ||
              this.props.getViewAccess([
                "GeoFencing:Add",
                "GeoFencing:View",
              ])) ? (
              <div
                className="geofence-drawer-icon"
                onClick={() =>
                  geofenceDrawerOpenCond
                    ? this.closeGeofenceDrawer()
                    : this.openGeofenceDrawer()
                }
              >
                <img src={GeofenceMapIcon} className="geo-fence-icon" />
              </div>
            ) : (
              ""
            )
          ) : (
            ""
          )}
          {!selected_id &&
          !this.state.filterIconClicked &&
          !this.state.mobile_status_drawer_visible ? (
            <div
              className="map-drawer-icon"
              onClick={() =>
                listOpenBtnCond ? this.closeDrawer() : this.drawerIconClick()
              }
            >
              {this.mobileViewEnabled ? (
                listOpenBtnCond ? (
                  <div>
                    Map View <DownOutlined />
                  </div>
                ) : (
                  <div>
                    List View <UpOutlined />
                  </div>
                )
              ) : (
                <SearchOutlined />
              )}
            </div>
          ) : (
            ""
          )}
          {this.state.mobile_visible ? (
            ""
          ) : (
            <MapListWithSearch
              t={this.props.t}
              url={getBaseUrl(
                this.props,
                "map-view",
                this.props.isGenericView ? true : false,
              )}
              drawerCancelCallback={() => this.drawerCancelCallback()}
              filterIconRef={this.filterIconRef}
              isClosable={this.getThingsWithMap(this.state.total_data)?.length}
              constantCurrentTime={this.state.constantCurrentTime}
              history={this.props.history}
              site={this.state.site}
              total_sites={this.state.total_sites}
              category={this.state.category}
              total_category={this.state.total_category}
              on_off_status={this.state.on_off_status}
              on_off_device_status={this.state.on_off_device_status}
              operational_st={this.state.operational_st}
              faultSt={this.state.faultSt}
              total_on_off_status={this.state.total_on_off_status}
              onUrlChange={() => this.onUrlChange()}
              applyPanelFilterSelect={(a, b) =>
                this.applyPanelFilterSelect(a, b)
              }
              filterIconClicked={this.state.filterIconClicked}
              drawerApplyCallback={(e) => this.drawerApplyCallback(e)}
              isAurassure={aurassureThingCat(
                getCategory,
                this.props.vendor_id,
                this.props.client_id,
              )}
              isAurassureThingCat={isAurassure(
                this.props.vendor_id,
                this.props.client_id,
              )}
              pollutionThingCats={pollutionThingCats}
              detailed_view={isDetailedView(selected_id, this.state.total_data)}
              pathDetailsClick={() => this.pathDetailsClick()}
              analog_view={isRealTime(selected_id, this.state.total_data)}
              search_data={this.state.search_data}
              icon_clicked={this.state.icon_clicked}
              closeDrawer={() => this.closeDrawer()}
              drawer_data={drawerData}
              client_id={this.state?.client_id}
              application_id={this.props?.application_id}
              thingsDrawerClick={(e) => this.thingsDrawerClick(e)}
              drawer_visible={this.state.drawer_visible}
              user_preferences={this.props.user_preferences}
              selected_id={selected_id}
              changeDateTanker={(e) => this.changeDateTanker(e)}
              changeDateChambersTanker={(e) => this.changeDateChambersTanker(e)}
              panel_data={panelData}
              tanker_date_select_chamber={this.state.tanker_date_select_chamber}
              tanker_graph_loading_chamber={
                this.state.tanker_graph_loading_chamber
              }
              tanker_date_select={this.state.tanker_date_select}
              tanker_graph_loading={this.state.tanker_graph_loading}
              header_value={commonHeaderData}
              viewTrendClick={(e) => this.viewTrendClick(e)}
              selected_channel={this.state.selected_channel}
              currentAddress={this.state.currentAddress}
              is_energy_meter={true}
              is_running={true}
              no_switch_off={false}
              closeDetailsPanel={() => this.closeDetailsPanel()}
              panel_loading={this.state.panel_loading}
              show_graph_modal={this.state.show_graph_modal}
              viewTrendClose={() => this.viewTrendClose()}
              graph_loading={this.state.graph_loading}
              socket={this.socket}
              date_select_channel={this.state.date_select_channel}
              changeDateChannels={(e) => this.changeDateChannels(e)}
              all_graphs={allGraphs}
              today_enrg_value={this.state.today_enrg_value}
              goToPage={this.goToPage}
              getRemoteAccess={
                typeof this.props.getRemoteAccess === "function"
                  ? this.props.getRemoteAccess()
                  : undefined
              }
              getRemoteLockAccess={
                typeof this.props.getRemoteLockAccess === "function"
                  ? this.props.getRemoteLockAccess()
                  : undefined
              }
              partnerOptions={this.state.partnerOptions}
              selectedPartner={this.state.selectedPartner}
              customerOptions={this.state.customerOptions}
              selectedCustomer={this.state.selectedCustomer}
              parent_vendor_id={this.props.parent_vendor_id}
              territories={{
                options: this.state.territoryList,
                value: this.state.territories
              }}
              showFaultFilter={faultStatus?.[0]?.value > 0}
              isSistemaBioCustomer={isSistemaBioCustomer}
            />
          )}
          {this.state.mobile_visible ? (
            ""
          ) : (
            <GeoFenceDrawer
              {...this.props}
              visible={geofence_drawer_visible}
              closeGeofence={() => this.closeGeofenceDrawer()}
              toggleConfigDrawer={() => this.toggleGeofenceConfigDrawer()}
              renderGeofence={this.renderGeofence}
              clientId={this.props.client_id}
              isLoading={this.state.geofence_loading}
              geofence_list={this.state.geofenceData}
              totalGeofencesCount={this.state.totalGeofencesCount}
              getGeofenceListOfCustomer={this.getGeofenceListOfCustomer}
              toggleGeofencesOnMap={this.toggleGeofencesOnMap}
              activeGeofence={this.state.selectedGeofence}
              handleDeselect={this.handleDeselect}
              deleteGeofence={this.deleteSelectedGeofence}
              onEditClick={this.editSelectedGeofence}
              handleGeofenceListing={this.handleGeofenceListing}
              listingConditions={this.state.listingState}
              renderGeofencesStatus={this.state.showGeofenceOnMap}
            />
          )}
          {this.state.mobile_visible ? (
            ""
          ) : (
            <GeoFenceConfigDrawer
              configVisible={geofence_config_drawer_visible}
              closeConfigGeofence={() => this.closeGeofenceConfigDrawer()}
              clientId={this.props.client_id}
              saveGeofence={this.saveGeofence}
              isGeofenceAdding={this.state.isGeofenceAdding}
              toggleAddingMode={this.toggleGeofenceAddMode}
              geofenceConfig={this.state.geofenceConfigData}
              geofenceConfigHandler={this.handleGeofenceData}
              editingMode={this.state.isEditing}
              activeGeofence={this.state.selectedGeofence}
              editGeofence={this.editGeofenceOfCustomer}
            />
          )}
        </div>
      );
    }
    return pageRender;
  }
}
