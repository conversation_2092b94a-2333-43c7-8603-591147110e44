#generic_map_view_page {
	height: calc(100vh - 52px);
	width: 100%;
	position: relative;
	.ant-drawer-content-wrapper {
		box-shadow: none !important;
	}
	.custom-drawer-trip {
		z-index: 0;
	}
	.map-header {
		position: absolute;
		margin-top: 16px;
		margin-left: 20px;
		.map-filters {
			align-items: center;
			display: flex;
			#thing_status {
				background-color: rgba(255, 255, 255, 0.8);
				-webkit-backdrop-filter: blur(2em);
				backdrop-filter: blur(2em);
				padding: 0 25px;
				border-radius: 50px;
				line-height: 1.2;
				.status-text:nth-last-child(1) {
					margin-right: 0px;
				}
			}
		}
		.fault-cluster-container {
			display: flex;
			margin-top: 10px;
			align-items: baseline;
		}
		.cluster-view {
			background-color: #fff;
			display: flex;
			width: fit-content;
			padding: 5px 10px;
			align-items: center;
			border-radius: 20px;
			margin-top: 10px;
			margin-left: 10px;
			.cluster-view-title {
				margin-right: 10px;
				font-size: 13px;
			}
		}
	}
	.flex-center {
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.map-drawer-icon {
		position: absolute;
		//padding: 12px 15px;
		height: 45px;
		width: 50px;
		right: 20px;
		top: 34px;
		background-color: #fff;
		border-radius: 50%;
		//box-shadow: 7px 8px 13px #d7dbe085, -12px -12px 24px #ffffff78;
		box-shadow: 0px 3px 6px #00000033;
		cursor: pointer;
		.anticon {
			font-size: 20px;
		}
		.flex-center();
	}

	.find-location-actions {
		display: flex;
		.location-search {
			margin-top: 5px;
			margin-right: 40px;
			.ant-input {
				width: 300px;
				border-radius: 50px !important;
			}
			.ant-input-affix-wrapper .ant-input {
				width: 200px !important;
			}
		}

		.lat-lng-search {
			display: flex;
			margin-top: 5px;
			background-color: #ffffff;
			padding: 5px;
			border-radius: 6px;
			align-items: center;
			margin-right: 5px;

			div {
				margin-right: 7px;
				font-weight: 500;
				color: #232323;
				font-size: 13px;
			}

			.custom-input {
				width: 75px;
				margin-right: 10px;
				height: 24px !important;
			}

			.right-icon-view {
				background-color: #eeeeee;
				width: 30px;
				height: 26px;
				text-align: center;
				border-radius: 5px;
				cursor: pointer;
			}
		}
	}
	.geofence-drawer-icon {
		position: absolute;
		height: 50px;
		width: 50px;
		right: 20px;
		top: 104px;
		background-color: #fff;
		border-radius: 50%;
		box-shadow: 0px 3px 6px #00000033;
		cursor: pointer;
		.anticon {
			font-size: 20px;
		}
		.flex-center();

		.geo-fence-icon {
			height: 24px;
		}
	}
	.filter-section {
		position: absolute;
		padding: 14px;
		//width: 774px;
		width: 674px;
		background: transparent; // rgba(255, 255, 255, 0.79);
		.ant-row {
			justify-content: start;
		}
	}
}

.map-banner {
	height: calc(100vh - 77px) !important;
}

@media (max-width: 1536px) {
	#generic_map_view_page {
		.filter-section {
			width: 546px;
		}
	}
}

@media (max-width: 1366px) {
	#generic_map_view_page {
		.filter-section {
			width: 480px;
		}
	}
}
@media (max-width: 1024px) {
	#generic_map_view_page {
		.gmnoprint {
			display: none;
		}
		.map-header {
			right: 10px;
		}
		.custom-drawer.ant-drawer {
			margin-top: 0px;
		}
		.map-drawer-icon {
			top: auto;
			bottom: 40px;
			left: 50%;
			transform: translateX(-50%);
			width: 170px;
			border-radius: 28px;
			background: #ff8500;
			color: #fff;
			z-index: 100;
			.anticon {
				font-size: 15px;
				margin-left: 8px;
			}
		}
		.mobile-body-div {
			flex: 1;
			.ant-tabs {
				padding: 0 30px;
				.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
					font-weight: 600;
				}
			}
		}
		.channel-data-container .channel-data {
			padding-top: 0px;
		}
		#pomo_panel_body_id {
			padding: 0 14px;
			.real-time-parameters.single-line {
				gap: 20px;
				.values {
					width: 100%;
				}
			}
		}
	}
}

@media (max-width: 576px) {
	#generic_map_view_page {
		height: calc(100vh - 52px - 55px);
		.genset-status-container {
			padding: 0 20px;
			margin: 20px 0;
		}
		.engine-meter-drawer-mobile .ant-drawer-body {
			display: flex;
			flex-direction: column;
			.mobile-body-div {
				overflow: auto;
			}
		}
	}
	.map-banner {
		height: calc(100vh - 52px - 87px) !important;
	}
}
