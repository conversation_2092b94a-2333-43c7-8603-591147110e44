import {
  retriveCustomerList,
  retriveThingsList,
  getThingsAndParameterData,
  retriveTasksData,
  retriveVendorThingsList,
  retrieveSitesList,
} from "@datoms/js-sdk";
import _filter from "lodash/filter";
import moment from "moment";
import { sortedThingsOrderArray } from "../../../logic/ThingsOrder";
import { getFinalThingCat } from "../../../logic/getThingsCats";
import { isAurassure } from "../../../logic/isAurassure";
import { filterDginIot } from "../../../../data_handling/DGinIot";

export async function fetchCustomersList() {
  const { application_id } = this.props;
  let customersList = [];
  let response = await retriveCustomerList(
    this.props.client_id,
    //	'?lite=true'
  );
  customersList = Array.isArray(response.customers) ? response.customers : [];
  const allPartners = _filter(customersList, function (o) {
    return o.is_vendor === 1;
  });
  const partnerOptions = [];
  if (allPartners?.length) {
    allPartners.map((partner) => {
      partnerOptions.push({
        value: partner.id,
        title: partner.name,
      });
    });
  }
  const selectedPartner = this.getUrlBreak("partner")
    ? parseInt(this.getUrlBreak("partner")) === this.props.client_id
      ? undefined
      : parseInt(this.getUrlBreak("partner"))
    : partnerOptions?.[0]?.value;
  const customerNameMap = {};
  const filteredCustomer = _filter(customersList, function (o) {
    customerNameMap[o.id] = o.name;
    const selectedPartnerCond = selectedPartner
      ? o.vendor_id === selectedPartner
      : true;
    return selectedPartnerCond && o.applications.includes(16);
  });
  const customerOptions =
    this.props.application_id === 12 || this.props.client_id === 1819
      ? []
      : [
          {
            value: "all",
            title: this.props.t
              ? this.props.t("all_customers")
              : "All Customers",
          },
        ];
  if (filteredCustomer?.length) {
    filteredCustomer.forEach((customer) => {
      customerOptions.push({
        value: customer.id,
        title: customer.name,
      });
    });
  }
  const selectedCustomer =
    this.props.application_id !== 16
      ? this.getUrlBreak("customer")
        ? parseInt(this.getUrlBreak("customer"))
        : customerOptions?.[0]?.value
      : undefined;
  this.setState(
    {
      customersList: customersList,
      partnerOptions,
      selectedPartner,
      customerOptions,
      customerNameMap,
      selectedCustomer,
      client_id: selectedCustomer,
    },
    async () => {
      await Promise.all([
        this.getThingsList({
          client_id: this.state.selectedCustomer || this.props.client_id,
          application_id:
            selectedCustomer === "all" ? this.props.application_id : 16,
        }),
      ]);
    },
  );
}

export function getFilteredSiteList(siteList, operational_status) {
  return siteList
    .filter((site) => site.operational_status === parseInt(operational_status))
    .map((site) => site.value);
}

export async function getThingsList(data) {
  let totalData;
  let siteList = await retrieveSitesList(
    this.state.client_id,
    `?page_no=${1}&results_per_page=${1000}`,
  );
  let territoryValue = undefined;
  const decodedValue = decodeURIComponent(this.getUrlBreak("territories"));
  if (decodedValue && decodedValue !== "undefined" && decodedValue !== "null") {
    territoryValue = decodedValue
      .split(",")
      .map((item) => (isNaN(parseInt(item)) ? item : parseInt(item)));
  }
  if (this.state.selectedCustomer === "all") {
    const paramList =
      this.props.client_id === 1140
        ? 'parameters=["mc_st","hz","vbat","rpm","rnhr","fuel","temp","press","enrg"]'
        : "parameters=true";
    totalData = await retriveVendorThingsList(
      {
        vendor_id: this.state.selectedPartner || this.props.client_id,
        application_id: 17,
      },
      `?${paramList}&without_device=false&status=active`,
    );
  } else {
    const query = territoryValue
      ? `?territories=${encodeURIComponent(territoryValue)}`
      : "";
    totalData = await retriveThingsList(data, query);
  }
  let thingsOrder = sortedThingsOrderArray(totalData);
  totalData.things = thingsOrder;
  totalData.things_categories = getFinalThingCat(
    isAurassure(this.props.vendor_id, this.props.client_id),
  );
  let getFilterThings = this.getThingsWithMap(totalData);
  totalData.things = this.mobileViewEnabled
    ? totalData.things
    : getFilterThings;
  totalData = filterDginIot.bind(this)(totalData, "map-view");
  const getSiteValue =
    this.getUrlBreak("site") && this.getUrlBreak("site") !== "all"
      ? parseInt(this.getUrlBreak("site"))
      : "all";

  const totalCategories = this.getCategoryOptions(totalData, totalData.things);
  let modifiedResponse = getThingsAndParameterData(totalData);
  let offlineArray = this.getOnlineOfflineArray(totalData);
  let latestParameterData = this.offlineTimeOutFunction(
    undefined,
    offlineArray,
    totalData,
    modifiedResponse.latest_parameter_data,
    modifiedResponse,
  );
  const category =
    this.getUrlBreak("category") && this.getUrlBreak("category") !== "all"
      ? parseInt(this.getUrlBreak("category"))
      : "all";
  let selectedCat = category
    ? category
    : totalCategories && totalCategories.length
      ? totalCategories[0].value
      : "all";
  const totalOnOffStatus = this.getOnlineOfflineOption(selectedCat, totalData);
  let totalSites = [];

  if (this.state.client_id === 1381) {
    totalSites.push(
      {
        title: "All Sites",
        value: "all",
      },
      {
        value: 1,
        title: "Koramangla 3",
      },
      {
        value: 2,
        title: "Ranka Colony",
      },
    );
  } else {
    if (siteList?.data?.length) {
      totalSites = [
        {
          title: "All Sites",
          value: "all",
        },
      ];
      siteList.data.map((sitedata) => {
        totalSites.push({
          value: sitedata.id,
          title: sitedata.name,
          territory_id: sitedata.territory_id,
          operational_status: sitedata.operational_status,
        });
      });
    }
  }
  const defaultOperationalStatus = this.state.client_id === 13853 ? "1" : "";
  const getStatusValue =
    this.getUrlBreak("status") &&
    (this.getUrlBreak("status") !== "" || this.getUrlBreak("status") !== null)
      ? this.getUrlBreak("status")
      : "";
  const getDeviceStatusValue =
    this.getUrlBreak("device_st") &&
    (this.getUrlBreak("device_st") !== "" ||
      this.getUrlBreak("device_st") !== null)
      ? this.getUrlBreak("device_st")
      : "";
  const getOperationalStatusValue =
    this.getUrlBreak("operational_st") &&
    (this.getUrlBreak("operational_st") !== "" ||
      this.getUrlBreak("operational_st") !== null)
      ? this.getUrlBreak("operational_st")
      : defaultOperationalStatus;
  const getFaultStatusValue =
    this.getUrlBreak("fault_st") &&
    (this.getUrlBreak("fault_st") !== "" ||
      this.getUrlBreak("fault_st") !== null)
      ? this.getUrlBreak("fault_st")
      : "";

  const filteredSiteList = getFilteredSiteList(
    totalSites,
    getOperationalStatusValue,
  );
  
  const stateToUpdate = {
    latestParameterData: latestParameterData,
    total_data: totalData,
    modifiedResponse: modifiedResponse,
    offlineArray,
    site: getSiteValue,
    selected_id: undefined,
    total_category: totalCategories,
    total_on_off_status: totalOnOffStatus,
    category: category,
    on_off_status: getStatusValue,
    on_off_device_status: getDeviceStatusValue,
    operational_st: getOperationalStatusValue,
    filteredSiteList,
    selectedAssetStatusBarValue: getStatusValue.replace(/_/g, " "),
    selectedDeviceStatusBarValue: getDeviceStatusValue,
    selectedFaultStatusBarValue: getFaultStatusValue,
    territories: territoryValue,
    mapLoading: false,
  };

  if (siteList.status === "success") {
    stateToUpdate["total_sites"] = totalSites;
  }
  this.setState(stateToUpdate, async () => {
    this.setState({ page_loading: false }, () => this.getMapZoomOnFilter());
    if (isAurassure(this.props.vendor_id, this.props.client_id)) {
      await this.aqiLastHourData();
    }
    this.callSocket();
  });
}

export async function updateDeviceStatus(data) {
  let totalData;
  let territoryValue = undefined;
  const decodedValue = decodeURIComponent(this.getUrlBreak("territories"));
  if (decodedValue && decodedValue !== "undefined" && decodedValue !== "null") {
    territoryValue = decodedValue
      .split(",")
      .map((item) => (isNaN(parseInt(item)) ? item : parseInt(item)));
  }
  if (this.state.selectedCustomer === "all") {
    const paramList =
      this.props.client_id === 1140
        ? 'parameters=["mc_st","hz","vbat","rpm","rnhr","fuel","temp","press","enrg"]'
        : "parameters=true";
    totalData = await retriveVendorThingsList(
      {
        vendor_id: this.state.selectedPartner || this.props.client_id,
        application_id: 17,
      },
      `?${paramList}&without_device=false&status=active`,
    );
  } else {
    const query = territoryValue
      ? `?territories=${encodeURIComponent(territoryValue)}`
      : "";
    totalData = await retriveThingsList(data, query);
  }

  let getFilterThings = this.getThingsWithMap(totalData);
  totalData.things = this.mobileViewEnabled
    ? totalData.things
    : getFilterThings;
  totalData = filterDginIot.bind(this)(totalData, "map-view");
  let modifiedResponse = getThingsAndParameterData(totalData);
  let offlineArray = this.getOnlineOfflineArray(totalData);
  let latestParameterData = this.offlineTimeOutFunction(
    undefined,
    offlineArray,
    totalData,
    modifiedResponse.latest_parameter_data,
    modifiedResponse,
  );
  this.setState({ latestParameterData });
}

export async function fetchMissionListVehicle(listId) {
  let data = {
    OrderBy: "desc",
    client_id: this.state.client_id,
    application_id: 16,
    StartAfter: moment
      .unix(moment().subtract(1825, "days").startOf("day").unix())
      .toISOString(),
    StartBefore: moment.unix(moment().endOf("day").unix()).toISOString(),
    ResultsPerPage: 50,
    task_type: 1,
    thing_list: [listId],
    GetDetails: true,
    get_counts: false,
    get_summary: false,
    get_trends: false,
  };
  let missionList = await retriveTasksData(data);
  this.setState({
    vehicleMissionList: missionList,
    selected_trip_id: missionList?.response?.Missions?.length
      ? missionList.response.Missions[0].Id
      : undefined,
    loading_for_task: false,
  });
}

export async function fetchMissionList() {
  let data = {
    OrderBy: "desc",
    client_id: this.state.client_id,
    application_id: 16,
    StartAfter: moment.unix(this.state.StartAfter).toISOString(),
    StartBefore: moment.unix(this.state.StartBefore).toISOString(),
    ResultsPerPage: 50,
    task_type: 1,
    thing_list: [parseInt(this.state.selected_id)],
    GetDetails: true,
    get_counts: false,
    get_summary: false,
    get_trends: false,
  };
  let missionList = await retriveTasksData(data);
  this.setState({
    missionList: missionList,
  });
}

export function customerFilterThingsFetch(clientId) {
  // return retriveThingsList(
  //   {
  //     client_id: clientId,
  //     application_id: 16,
  //   },
  //   "?without_device=true",
  // );
}
