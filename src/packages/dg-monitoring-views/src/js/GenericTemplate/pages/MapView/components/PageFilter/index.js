import React from "react";
import "./style.less";
import GenericFilter from "@datoms/react-components/src/components/GenericFilter/pages";
// import { useGlobalContext } from '../../../../../../../../../store/globalStore';


export default class PageFilter extends React.Component {

  constructor(props) {
    super(props);
  }
  filterRender() {
    const {
      category,
      on_off_status,
      on_off_device_status,
      operational_st,
      faultSt,
      total_on_off_status,
      total_category,
      total_sites,
      site,
      history,
      search_data,
      isAurassure,
      partnerOptions,
      selectedPartner,
      customerOptions,
      selectedCustomer,
      application_id,
      isSistemaBioCustomer,
      client_id,
    } = this.props;
    let filterData = [];
    
    const isMobileScreen = window.innerWidth < 576;
    let assetStatusOptions = total_on_off_status;
    if(isSistemaBioCustomer) {
      assetStatusOptions = [{
        value: "Gensets_On",
        title: "On",
      }, {
        value: "Gensets_Off",
        title: "Off",
      }]
    }

    let toShowOperationalStatus = client_id === 13853;

    filterData.push(
      {
        type: "tree_select",
        hideField: !this.props.territories?.options?.children?.length,
        label: "Select Territory",
        url_name: "territories",
        key: "territories",
        filter_api: "territories",
        is_options_dynamic: true,
        component_props: {
          treeData: this.props.territories?.options ? [this.props.territories.options] : [],
          value: Array.isArray(this.props.territories?.value) ? this.props.territories?.value : [],
          treeDefaultExpandAll: true,
          treeCheckable: true,
          showCheckedStrategy: "SHOW_PARENT",
          treeCheckStrictly: true,
          maxTagCount: 3,
          maxTagPlaceholder: (omittedValues) =>
            omittedValues.length,
          placeholder: "Select territories",
          filterTreeNode: (search, item) => {
            return item.title.toLowerCase().indexOf(search.toLowerCase()) >= 0;
          },
        },
        is_inside_filter_drawer: true,
      },
      {
        is_inside_filter_drawer: true,
        optionData: partnerOptions,
        label: this.props.t? this.props.t('select_partner'): "Select Partner",
        // label: "Select Partner",
        selectValue: selectedPartner,
        url_name: "partner",
        showSearch: true,
        sorted: false,
        placeholder: this.props.t? this.props.t('select_partner'): "Select Partner",
        //placeholder: "Select Partner",
        hideField: partnerOptions?.length < 2,
        allowClear: application_id !== 12,
      },
      {
        is_inside_filter_drawer: true,
        optionData: customerOptions,
        label: this.props.t? this.props.t('select_customer'): 'Select Customer',
        // label: "Select Customer",
        selectValue: selectedCustomer,
        url_name: "customer",
        showSearch: true,
        sorted: false,
        placeholder: this.props.t? this.props.t('select_customer'): 'Select Customer',
        //placeholder: "Select Customer",
        hideField: customerOptions?.length < 2
      },
      {
        is_inside_filter_drawer: true,
        optionData: total_sites,
        label: "Select Site",
        selectValue: site,
        url_name: "site",
        showSearch: true,
        sorted: false,
        placeholder: "All Site",
        hideField: total_sites?.length < 3
      },
      {
        is_inside_filter_drawer: true,
        optionData: total_category,
        selectValue: category,
        url_name: "category",
        showSearch: true,
        sorted: false,
        placeholder: "All Category",
        label: this.props.t? this.props.t('asset_type'): 'Asset Type',
        // label: "Asset Type",
        hideField: total_category?.length < 3
      },
      {
        is_inside_filter_drawer: true,
        optionData: assetStatusOptions,
        label: isSistemaBioCustomer ? 'Genset Status' : this.props.t? this.props.t('asset_status'): 'Asset Status',
        // label: "Asset Status",
        url_name: "status",
        selectValue: on_off_status,
        allowClear: true,
        showSearch: true,
        sorted: false,
        placeholder: isSistemaBioCustomer ? 'Select Genset Status' : this.props.t? this.props.t('select_asset_status'): "Select Asset status"
        // placeholder: "Select asset status",
      },
      {
        is_inside_filter_drawer: true,
        optionData: [
          {
            value: "online",
            title: "Online",
          },
          {
            value: "offline",
            title: "Offline",
          },
        ],
        label: "Device Status",
        url_name: "device_st",
        selectValue: on_off_device_status,
        allowClear: true,
        showSearch: true,
        sorted: false,
        placeholder: "Select device status",
        hideField: isAurassure || isSistemaBioCustomer
      },
      {
        is_inside_filter_drawer: true,
        optionData: [
          {
            value: "1",
            title: "Operational",
          },
          {
            value: "0",
            title: "Non-Operational",
          },
        ],
        label: "Operational Status",
        url_name: "operational_st",
        selectValue: operational_st,
        allowClear: true,
        showSearch: true,
        sorted: false,
        placeholder: "Select operational status",
        hideField: !toShowOperationalStatus
      },
      {
        is_inside_filter_drawer: true,
        optionData: [
          {
            value: "trip",
            title: "Trip",
          },
          {
            value: "warning",
            title: "Warning",
          },
        ],
        label: "Fault Status",
        url_name: "fault_st",
        selectValue: faultSt,
        allowClear: true,
        showSearch: true,
        sorted: false,
        placeholder: "Select fault status",
        hideField: isAurassure || !this.props.showFaultFilter || isSistemaBioCustomer
      },
    );
    
    let mainDiv = (
      <div className="filter-header">
        <GenericFilter
          t={this.props.t}
          filterIconRef={this.props.filterIconRef}
          default={[undefined, "all", "all", ""]}
          onUrlChange={() => this.props.onUrlChange()}
          panelFilterSelect={(a, b) => this.props.applyPanelFilterSelect(a, b)}
          drawerCancelCallback={() => this.props.drawerCancelCallback()}
          drawerApplyCallback={() => this.props.drawerApplyCallback()}
          url={this.props.url || "/dg-monitoring/generic/map-view"}
          history={history}
          filterData={filterData}
          searchObject={{
            placeholder: this.props.t? this.props.t('search_assets'): "Search Assets",
            // placeholder: "Search Assets",
            size: "default",
            value: search_data,
          }}
        />
      </div>
    );
    return <div className="filter-drawer">{mainDiv}</div>;
  }
  render() {
    return <div id="map_thing_filter">{this.filterRender()}</div>;
  }
}
