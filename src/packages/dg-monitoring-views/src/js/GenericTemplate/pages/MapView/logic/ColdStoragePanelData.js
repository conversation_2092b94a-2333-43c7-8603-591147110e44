import _find from 'lodash/find';
import _filter from 'lodash/filter';

export function coldStoragePanelData() {
    const {
		modifiedResponse,
	} = this.state;
	let findLastData = _find(this.state.latestParameterData, {
		thing_id: this.state.selected_id,
	});
	const RealtimeParams = [];
	const filterTempHumidParams = _filter(modifiedResponse?.param_data, function(o) {
		return (o.key.includes('temperature') || o.key.includes('humidity')) && !o.key.includes('err');
	});
	const paramOrder = ['temperature', 'humidity', 'temperature2', 'humidity2'];
	if(filterTempHumidParams?.length) {
		filterTempHumidParams.map((params) => {
			if(findLastData?.data[params.key]) {
				RealtimeParams.push({
					value: findLastData?.data[params.key],
					unit: params.unit,
					name: params.name,
					key: params.key
				})
			}
		})
		if(this.props.client_id === 13853) {
			RealtimeParams.sort((a, b) => {
				return paramOrder.indexOf(a.key) - paramOrder.indexOf(b.key);
			})
		}
	}

	if(findLastData?.data && findLastData?.data.hasOwnProperty('dr_st')) {
		RealtimeParams.push({
			value: findLastData?.data['dr_st'] === '1' ? 'Open' : findLastData?.data['dr_st'] === '0' ? 'Closed' : 'NA',
			unit: '',
			name: 'Door Status',
			key: 'dr_st'
		})
	}
	// 	{
	// 		value: findLastData?.data['temperature'],
	// 		unit: _find(modifiedResponse?.param_data, { key: 'temperature' })?.unit,
	// 		name: 'Temperature',
	// 		key: 'temperature'
	// 	},
    //     {
	// 		value: findLastData?.data['humidity'],
	// 		unit: _find(modifiedResponse?.param_data, { key: 'humidity' })?.unit,
	// 		name: 'Humidity',
	// 		key: 'humidity'
	// 	},
	// ];
	return {
		real_time_params: RealtimeParams,
	};
}