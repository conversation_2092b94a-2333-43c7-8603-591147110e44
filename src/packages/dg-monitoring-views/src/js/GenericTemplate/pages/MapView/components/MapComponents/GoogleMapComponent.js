/*Libs*/
import React from 'react';
// import { Tooltip } from 'antd';
import {
	GoogleMap,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>ow,
	DrawingManager,
	Circle,
	Polygon,
	OverlayView,
} from '@react-google-maps/api';
import PropTypes from 'prop-types';

/*Components*/

/*Styles*/
import './map-component.less';

/*Styles*/
import locationLatLngIcon from '../../../../images/my-location.svg';
import locationIcon from '../../../../images/location-on.svg';
import start_icon from '../../../../images/start-icon.png';
import stop_icon from '../../../../images/stop-icon.png';
import aqiScale from '../../../../images/aqi_scale.svg';
import editMarker from '../../../../images/marker.svg';
import legendRunning from '../../../../images/Legend-Running.svg';
import legendStopped from '../../../../images/Legend-Stopped.svg';
import legendNotConnected from '../../../../images/Legend-Not-Connected.svg';
import legendOffline from '../../../../images/Legend-Offline.svg';
import legendOnlineSistemaBio from '../../../../images/Legend-Online-SistemaBio.svg';
/*Configs*/
import MapDefaultConfig from './MapDefaultConfig';

/* Data Handling */

// const MapPin = ({ hoverText, colorId, callback }) => (
// 	<Tooltip title={hoverText}>
// 		<div
// 			onClick={() => {
// 				callback(hoverText);
// 			}}
// 			className={'map-pin pin' + colorId}
// 		/>
// 	</Tooltip>
// );

const onLoad = (polyline) => {
	// console.log('polyline: ', polyline)
};

export default class GoogleMapComponent extends React.PureComponent {
	//initializing defaultprops, so that it will provide default configs
	static defaultProps = {
		isPolilineTrue: MapDefaultConfig.is_poliline_true,
		hasOnClick: MapDefaultConfig.has_on_click,
		streetViewControl: MapDefaultConfig.street_view_control,
		className: MapDefaultConfig.class_name,
	};

	//setting props types
	static propTypes = {
		isPolilineTrue: PropTypes.bool,
		hasOnClick: PropTypes.bool,
		streetViewControl: PropTypes.bool,
		className: PropTypes.string,
	};
	constructor(props) {
		super(props);
		this.circleRef = React.createRef();
		this.state = {
			// mapsLoaded: false,
			// map: null,
			// maps: null,
			// mapPointers: this.props.mapPointers,
			geofenceData: [],
		};
		this.clusterOption = {
			imagePath:
				'https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m',
		};
		this.polyLineOptions = {
			strokeColor: '#8EAFE7',
			strokeWeight: 6,
			clickable: false,
			draggable: false,
			editable: false,
			visible: true,
			zIndex: 1,
			//	icons:[durationIcon]
			//strokeOpacity: 0.8,
			//fillColor: '#8EAFE7',
			//fillOpacity: 0.35,
		};
	}

	/*componentDidUpdate(prevProps) {
        if (this.props.mapPointers !== prevProps.mapPointers) {
            console.log('prevProps1');
            this.setState({
                mapPointers: this.props.mapPointers
            });
        }
    }*/

	/*shouldComponentUpdate(nextProps, nextState) {
        console.log('shouldComponentUpdate1_ nextProps', JSON.stringify(nextProps.mapPointers));
        console.log('shouldComponentUpdate1_ this.props', JSON.stringify(this.props.mapPointers));
        if (this.props.mapPointers !== nextProps.mapPointers) {
            return true;
        }
        return false;
    }*/

	mapOnClick(id) {
		if (
			this.props.hasOnClick &&
			this.props.data.rawData &&
			this.props.data.modifiedResponseRaw
		) {
			this.props.mapOnClick(id);
		}
	}

	handleDrawingComplete = async (
		currentGeofenceData,
		currentGeofenceTool,
	) => {
		if (currentGeofenceTool === 'circle') {
			this.props.handleGeofencingData('geofence_center', {
				lat: currentGeofenceData.center.lat(),
				lng: currentGeofenceData.center.lng(),
			});
			const address = await this.props.reverseGeocode(
				this.props.geofenceConfig.geofence_center,
			);
			this.props.handleGeofencingData(
				'geofence_radius',
				currentGeofenceData.radius,
			);
			this.props.handleGeofencingData('geofence_address', address);
			currentGeofenceData.setVisible(false);
		} else if (currentGeofenceTool === 'polygon') {
			const paths = currentGeofenceData.getPath().getArray();

			const coordinates = paths.map((latLng) => ({
				lat: latLng.lat(),
				lng: latLng.lng(),
			}));
			const address = await this.props.reverseGeocode(coordinates[0]);
			this.props.handleGeofencingData(
				'geofence_radius',
				currentGeofenceData.radius,
			);
			this.props.handleGeofencingData('geofence_address', address);
			currentGeofenceData.setVisible(false);
			this.props.handleGeofencingData('geofence_points', coordinates);
		}
		this.props.toggleDrawingMode();
	};
	// onGoogleApiLoaded(map, maps) {
	// 	console.log('map', map);
	// 	this.fitBounds(map, maps);

	// 	this.setState({
	// 		...this.state,
	// 		mapsLoaded: true,
	// 		map: map,
	// 		maps: maps,
	// 	});
	// }

	// fitBounds(map, maps) {
	// 	var bounds = new maps.LatLngBounds();
	// 	for (let marker of this.props.mapPointers) {
	// 		bounds.extend(new maps.LatLng(marker.lat, marker.lng));
	// 	}
	// 	map.fitBounds(bounds);
	// }

	// afterMapLoadChanges() {
	// 	if (this.props.hasOnloadFunction) {
	// 		return (
	// 			<div style={{ display: 'none' }}>
	// 				<Polyline
	// 					map={this.state.map}
	// 					maps={this.state.maps}
	// 					markers={this.props.mapPointers}
	// 				/>
	// 			</div>
	// 		);
	// 	}
	// }

	//getting map zoom

	getBoundsZoomLevel(bounds, mapDim) {
		let WORLD_DIM = { height: 256, width: 256 };
		let ZOOM_MAX = 21;

		function latRad(lat) {
			let sin = Math.sin((lat * Math.PI) / 180);
			let radX2 = Math.log((1 + sin) / (1 - sin)) / 2;
			return Math.max(Math.min(radX2, Math.PI), -Math.PI) / 2;
		}

		function zoom(mapPx, worldPx, fraction) {
			return Math.floor(Math.log(mapPx / worldPx / fraction) / Math.LN2);
		}

		let ne = bounds.getNorthEast();
		let sw = bounds.getSouthWest();

		let latFraction = (latRad(ne.lat()) - latRad(sw.lat())) / Math.PI;

		let lngDiff = ne.lng() - sw.lng();
		let lngFraction = (lngDiff < 0 ? lngDiff + 360 : lngDiff) / 360;

		let latZoom = zoom(mapDim.height, WORLD_DIM.height, latFraction);
		let lngZoom = zoom(mapDim.width, WORLD_DIM.width, lngFraction);

		return Math.min(latZoom, lngZoom, ZOOM_MAX);
	}

	createMarkerForPoint(point) {
		return new window.google.maps.Marker({
			position: new window.google.maps.LatLng(point.lat, point.lng),
		});
	}

	createBoundsForMarkers(markers) {
		let bounds = new window.google.maps.LatLngBounds();
		markers.map((points) => {
			return bounds.extend(points.getPosition());
		});
		return bounds;
	}

	markerLoadHandler(marker, location) {
		return this.setState((prevState) => {
			return {
				markerMap: { ...prevState.markerMap, [location.id]: marker },
			};
		});
	}

	async markerHoverHandler(event, location) {
		if (
			window.innerWidth > 1024 &&
			location.isRunning &&
			!this.props.trip_drawer_visible
		) {
			const address = await this.props.reverseGeocode({
				lat: location.lat,
				lng: location.lng,
			});
			this.setState({ location, showInfoWindow: true, address });
		}
	}

	//get current selected marker with listId
	getCurrentMarker(mapPointers = []) {
		let selectedMarker = null;
		mapPointers.find((point) => {
			if (point.id === this.props.listId) {
				selectedMarker = point;
			}
		});
		return selectedMarker;
	}

	// getMarkerIcon(location) {
	// 	if (location.is_moving) {
	// 		console.log('moving');
	// 		return {
	// 			url:
	// 			'data:image/svg+xml;charset=utf-8,'  +
	// 			encodeURIComponent(ReactDOMServer.renderToStaticMarkup(<AnimatedDgSvg/>))
	// 		};
	// 	} else {
	// 		return location.icon;
	// 	}
	// }

	componentDidMount() {
		//getting map zoom
		//	console.log('my-svg', AnimatedDgSvg);
		let mapDiv = document.getElementById('map_id');
		let mapDim = {
			height: mapDiv.clientHeight,
			width: mapDiv.clientWidth,
		};
		let markers = [];
		if (this.props.mapPointers && this.props.mapPointers.length) {
			this.props.mapPointers.map((points) => {
				return markers.push(this.createMarkerForPoint(points));
			});
		}

		let bounds =
			markers.length > 0 ? this.createBoundsForMarkers(markers) : null;
		let zoomLevel = bounds ? this.getBoundsZoomLevel(bounds, mapDim) : 0;
		let centerValue = bounds
			? bounds.getCenter()
			: new window.google.maps.LatLng(0, 0);
		//	console.log('markers', zoomLevel);
		this.setState({
			zoomLevel: zoomLevel,
			centerValue: centerValue,
		});
	}

	getLatestTripPosition(location) {
		// if (this.props.trip_drawer_visible && this.props.latLngData?.length > 1) {
		// 	return this.props.latLngData[this.props.latLngData.length - 1];
		// } else {
		// 	return location;
		// }
		return location;
	}

	getIcon(location, i) {
		let totalLocation = this.getStartStopPoints(this.props.latLngData);
		if (totalLocation?.length) {
			if (totalLocation?.length > 1) {
				return i === 0 ? start_icon : stop_icon;
			} else {
				return stop_icon;
			}
		} else {
			return location.icon;
		}
	}

	getStartStopPoints(latLngData) {
		let resultArray = [],
			endPoint;
		//return first and last point of latLngData
		if (this.props.trip_drawer_visible && latLngData?.length) {
			let startPoint = latLngData[0];
			resultArray = [startPoint];
			if (latLngData?.length > 1) {
				endPoint = latLngData[latLngData.length - 1];
				if (
					startPoint.lat === endPoint.lat &&
					startPoint.lng === endPoint.lng
				) {
					resultArray = [endPoint];
				} else {
					resultArray = [startPoint, endPoint];
				}
			}
		}
		return resultArray;
	}

	getFinalMapPointers() {
		let finalMapPointers = [];
		if (this.props.mapPointers && this.props.mapPointers) {
			finalMapPointers = this.props.mapPointers;
		}
		if (this.props.trip_drawer_visible && this.props.latLngData?.length) {
			finalMapPointers = this.getStartStopPoints(this.props.latLngData);
		}
		return finalMapPointers;
	}

	onMarkerDragEnd = (index, newLatLng) => {
		console.log(
			`Marker ${index + 1} dragged to: `,
			newLatLng.lat(),
			newLatLng.lng(),
		);
		const newPath = [...this.props.geofenceConfig.geofence_points];
		newPath[index] = { lat: newLatLng.lat(), lng: newLatLng.lng() };
		this.props.handleGeofencingData('geofence_points', newPath);
	};

	render() {
		let mapLegends = [];
		if(this.props.legends?.find(legend => legend.name === 'Stopped')) {
			mapLegends.push(
				<div className="legend-items"><img src={legendRunning}/>Running</div>
			)
			mapLegends.push(
				<div className="legend-items"><img src={legendStopped}/>Stopped</div>
			)
		} else {
			mapLegends.push(
				<div className="legend-items"><img src={legendRunning}/>Online</div>
			)
		}
		mapLegends.push(
			<div className="legend-items"><img src={legendNotConnected}/>Disconnected</div>,
			<div className="legend-items"><img src={legendOffline}/>Offline</div>,
		)

		if(this.props.isSistemaBioCustomer) {
			mapLegends = [
				<div className="legend-items"><img src={legendOnlineSistemaBio}/>Genset On</div>,
				<div className="legend-items"><img src={legendOffline}/>Genset Off</div>,
			]
		}
		const { cluster_view, drawingMode } = this.props;
		return (
			<div id="generic_map_view">
				{(() => {
					return (
						<GoogleMap
							id="map_id"
							mapContainerClassName="map-container-class"
							mapContainerStyle={{ height: '100%' }}
							zoom={this.props.zoomLevel || this.state.zoomLevel}
							center={
								this.props.centerValue || this.state.centerValue
							}
							ref={(ref) => {
								this.map = ref;
							}}
							options={{
								streetViewControl: this.props.streetViewControl,
								//mapTypeId: window.google.maps.MapTypeId.HYBRID,
							}}
							onLoad={(map) =>
								typeof this.props.onGoogleApiLoaded ===
								'function'
									? this.props.onGoogleApiLoaded(map)
									: undefined
							}
						>
							{/* {this.fitFunc()} */}
							<div>
								{drawingMode ? (
									<DrawingManager
										onOverlayComplete={(e) => {
											this.handleDrawingComplete(
												e.overlay,
												this.props.geofenceDrawingTool,
											);
										}}
										options={{
											drawingControl: false,
										}}
										drawingMode={
											this.props.geofenceDrawingTool
										}
									/>
								) : (
									''
								)}
								{this.props.showGenfences ? (
									this.props.geofenceList.map((geoF, ind) => {
										if (geoF.geofence_type === 'circle') {
											return (
												<Circle
													center={
														geoF
															.geofence_configuration
															.center
													}
													radius={parseFloat(
														geoF
															.geofence_configuration
															.radius,
													)}
													options={{
														strokeColor:
															geoF
																.geofence_configuration
																.colour,
														strokeWeight:
															geoF
																.geofence_configuration
																.strokeWidth,
														fillColor:
															geoF
																.geofence_configuration
																.colour,
														fillOpacity:
															parseFloat(
																geoF
																	.geofence_configuration
																	.opacity,
															) / 100,
													}}
												/>
											);
										} else {
											return (
												<Polygon
													paths={
														geoF
															.geofence_configuration
															.points
													}
													options={{
														strokeColor:
															geoF
																.geofence_configuration
																.colour,
														strokeWeight:
															geoF
																.geofence_configuration
																.strokeWidth,
														fillColor:
															geoF
																.geofence_configuration
																.colour,
														fillOpacity:
															parseFloat(
																geoF
																	.geofence_configuration
																	.opacity,
															) / 100,
													}}
												/>
											);
										}
									})
								) : (
									<></>
								)}
								{this.props.geofenceConfig &&
								this.props.geofenceConfig.geofence_center &&
								this.props.geofenceConfig.geofence_radius ? (
									<OverlayView
										position={
											this.props.geofenceConfig
												.geofence_center
										}
										mapPaneName={OverlayView.OVERLAY_LAYER}
										getPixelPositionOffset={() => ({
											x: 0,
											y: 0,
										})}
									>
										<div
											style={{
												background: 'white',
												padding: '8px',
												borderRadius: '3px',
												border: '1px solid #000',
												zIndex: 2,
												fontSize: '16px',
												color: '#000',
												width: 'fit-content',
											}}
										>
											{parseFloat(
												this.props.geofenceConfig
													.geofence_radius,
											).toFixed(2)}
											m
										</div>
									</OverlayView>
								) : (
									<></>
								)}
								{!this.props.geofenceConfig ? (
									''
								) : this.props.geofenceConfig.geofence_type ===
								  'circle' ? (
									<>
										<Circle
											onLoad={(circle) =>
												(this.circleRef = circle)
											}
											center={
												this.props.geofenceConfig
													.geofence_center
											}
											radius={parseFloat(
												this.props.geofenceConfig
													.geofence_radius,
											)}
											onRadiusChanged={() => {
												if (
													this.circleRef &&
													this.circleRef.radius
												) {
													this.props.handleGeofencingData(
														'geofence_radius',
														this.circleRef.radius,
													);
												}
											}}
											onCenterChanged={async () => {
												if (
													this.circleRef &&
													this.circleRef.center &&
													(this.props.geofenceConfig
														.geofence_center.lat !==
														this.circleRef.center.lat() ||
														this.props
															.geofenceConfig
															.geofence_center
															.lng !==
															this.circleRef.center.lng())
												) {
													this.props.handleGeofencingData(
														'geofence_center',
														{
															lat: this.circleRef.center.lat(),
															lng: this.circleRef.center.lng(),
														},
													);
													const address =
														await this.props.reverseGeocode(
															{
																lat: this.circleRef.center.lat(),
																lng: this.circleRef.center.lng(),
															},
														);
													this.props.handleGeofencingData(
														'geofence_address',
														address,
													);
												}
											}}
											options={{
												strokeColor:
													this.props.geofenceConfig
														.geofence_colour,
												strokeWeight:
													this.props.geofenceConfig
														.geofence_border_width,
												fillColor:
													this.props.geofenceConfig
														.geofence_colour,
												fillOpacity:
													this.props.geofenceConfig
														.geofence_opacity / 100,
												clickable: false,
												editable: true,
												draggable: false,
											}}
										/>
									</>
								) : (
									<>
										<Polygon
											path={
												this.props.geofenceConfig
													.geofence_points
											}
											options={{
												strokeColor:
													this.props.geofenceConfig
														.geofence_colour,
												fillColor:
													this.props.geofenceConfig
														.geofence_colour,
												fillOpacity:
													this.props.geofenceConfig
														.geofence_opacity / 100,
												strokeWeight:
													this.props.geofenceConfig
														.geofence_border_width,
											}}
										/>
										{this.props.editingMode &&
											this.props.geofenceConfig
												?.geofence_points &&
											this.props.geofenceConfig.geofence_points?.map(
												(marker, index) => (
													<Marker
														key={index}
														position={marker}
														onClick={() =>
															this.onMarkerClick(
																index,
															)
														}
														icon={editMarker}
														onDragEnd={(e) =>
															this.onMarkerDragEnd(
																index,
																e.latLng,
															)
														}
														draggable
													/>
												),
											)}
									</>
								)}
								{!this.props.editingMode &&
								this.props.selectedGeofence &&
								this.props.selectedGeofence[0] ? (
									this.props.selectedGeofence[0]
										.geofence_type === 'circle' ? (
										<Circle
											center={
												this.props.selectedGeofence[0]
													.geofence_configuration
													.center
											}
											radius={parseFloat(
												this.props.selectedGeofence[0]
													.geofence_configuration
													.radius,
											)}
											options={{
												strokeColor:
													this.props
														.selectedGeofence[0]
														.geofence_configuration
														.colour,
												strokeWeight:
													this.props
														.selectedGeofence[0]
														.geofence_configuration
														.strokeWidth,
												fillColor:
													this.props
														.selectedGeofence[0]
														.geofence_configuration
														.colour,
												fillOpacity:
													this.props
														.selectedGeofence[0]
														.geofence_configuration
														.opacity / 100,
												clickable: false,
												editable:
													this.props.editingMode,
												draggable: false,
											}}
										/>
									) : (
										<Polygon
											paths={
												this.props.selectedGeofence[0]
													.geofence_configuration
													.points
											}
											options={{
												strokeColor:
													this.props
														.selectedGeofence[0]
														.geofence_configuration
														.colour,
												strokeWeight:
													this.props
														.selectedGeofence[0]
														.geofence_configuration
														.strokeWidth,
												fillColor:
													this.props
														.selectedGeofence[0]
														.geofence_configuration
														.colour,
												fillOpacity:
													this.props
														.selectedGeofence[0]
														.geofence_configuration
														.opacity / 100,
												clickable: false,
												editable:
													this.props.editingMode,
												draggable: false,
											}}
										/>
									)
								) : (
									<></>
								)}
							</div>
							{cluster_view ? (
								<MarkerClusterer options={this.clusterOption}>
									{(clusterer) =>
										// this.props.mapPointers
										// 	.filter((loc) => {
										// 		if (
										// 			this.props
										// 				.trip_drawer_visible &&
										// 			this.props.latLngData?.length > 1// &&
										// 			// this.getCurrentMarker( //to be changed
										// 			// 	this.props.mapPointers
										// 			// )?.isRunning
										// 		) {
										// 			return (
										// 				this.props.listId == loc.id
										// 			);
										// 		} else {
										// 			return true;
										// 		}
										// 	})
										this.getFinalMapPointers().map(
											(location, i) => (
												<Marker
													icon={this.getIcon(
														location,
														i,
													)}
													key={i}
													title={location.hoverText}
													position={this.getLatestTripPosition(
														location,
													)}
													clusterer={clusterer}
													onMouseOver={async (
														event,
													) =>
														await this.markerHoverHandler(
															event,
															location,
														)
													}
													onMouseOut={() => {
														if (
															location.isRunning &&
															!this.props
																.trip_drawer_visible
														) {
															this.setState({
																showInfoWindow: false,
																location:
																	undefined,
																address:
																	undefined,
															});
														}
													}}
													onLoad={(marker) =>
														this.markerLoadHandler(
															marker,
															location,
														)
													}
													onClick={
														this.props.data &&
														!(
															this.props
																.trip_drawer_visible &&
															this.props
																.latLngData
																?.length > 1
														)
															? () =>
																	this.mapOnClick(
																		location.id,
																	)
															: {}
													}
												/>
											),
										)
									}
								</MarkerClusterer>
							) : (
								this.getFinalMapPointers().map(
									(location, i) => (
										<Marker
											icon={this.getIcon(location, i)}
											key={i}
											title={location.hoverText}
											position={this.getLatestTripPosition(
												location,
											)}
											onMouseOver={async (event) =>
												await this.markerHoverHandler(
													event,
													location,
												)
											}
											onMouseOut={() => {
												if (
													location.isRunning &&
													!this.props
														.trip_drawer_visible
												) {
													this.setState({
														showInfoWindow: false,
														location: undefined,
														address: undefined,
													});
												}
											}}
											onLoad={(marker) =>
												this.markerLoadHandler(
													marker,
													location,
												)
											}
											onClick={
												this.props.data &&
												!(
													this.props
														.trip_drawer_visible &&
													this.props.latLngData
														?.length > 1
												)
													? () =>
															this.mapOnClick(
																location.id,
															)
													: {}
											}
										/>
									),
								)
							)}
							{window.innerWidth > 1024 &&
							this.state.showInfoWindow &&
							this.state.location ? (
								<InfoWindow
									anchor={
										this.state.markerMap[
											this.state.location.id
										]
									}
									onCloseClick={() =>
										this.setState({ showInfoWindow: false })
									}
								>
									<div className="moving-info-tooltip">
										<div className="moving-tooltip-top">
											<span>
												{this.state.location.hoverText}
											</span>
											<span>
												{` ${this.state.location.kva}`}
											</span>
										</div>
										<div className="moving-tooltip-bottom">
											{/* <section className="moving-btm">
												<p className="moving-top-box">
													<img
														src={speedIcon}
														alt="icon"
														className="moving-left"
													/>
													<span className="moving-right">
														{
															this.state.location
																.speed
														}
													</span>
												</p>
												<p className="moving-top-box">
													<img
														src={distanceIcon}
														alt="icon"
														className="moving-left"
													/>
													<span className="moving-right">
														{
															this.state.location
																.distance
														}
													</span>
												</p>
												<p className="moving-top-box">
													<img
														src={fuelIcon}
														alt="icon"
														className="moving-left"
													/>
													<span className="moving-right">
														{
															this.state.location
																.fuel_consumption
														}
													</span>
												</p>
											</section> */}
											<section className="moving-btm">
												<img
													src={locationLatLngIcon}
													alt="icon"
													className="moving-left"
												/>
												<span className="moving-right">
													{this.state.location?.lat?.toFixed(
														7,
													)}
													{', '}
													{this.state.location?.lng?.toFixed(
														7,
													)}
												</span>
											</section>
											<section className="moving-btm">
												<img
													src={locationIcon}
													alt="icon"
													className="moving-left"
												/>
												<span className="moving-right">
													{this.state.address || ''}
												</span>
											</section>
										</div>
									</div>
								</InfoWindow>
							) : null}
							{(() => {
								// let mapPointArray = [];
								// this.props.mapPointers.map((location, i) => {
								// 	// console.log('location', location);
								// 	return mapPointArray.push({
								// 		lat: location.lat,
								// 		lng: location.lng,
								// 	});
								// });
								//if (this.props.isPolilineTrue) {
								if (
									// this.getCurrentMarker(
									// 	this.props.mapPointers
									// )?.isRunning &&
									this.props.trip_drawer_visible &&
									this.props.latLngData?.length
								) {
									return (
										<Polyline
											//	onLoad={onLoad}
											//path={mapPointArray}
											path={this.props.latLngData}
											options={this.polyLineOptions}
										/>
									);
								}
							})()}
						</GoogleMap>
					);
				})()}
				<div className={'map-legend-div ' + this.props.className}>
					{this.props.isAurassure ? (
						<img src={aqiScale} />
					) : (
						mapLegends
					)}
				</div>
			</div>
		);
	}
}
