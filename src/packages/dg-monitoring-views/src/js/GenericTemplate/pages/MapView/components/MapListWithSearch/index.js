import React from "react";
import AntDrawer from "@datoms/react-components/src/components/AntDrawer";
import Loading from "@datoms/react-components/src/components/Loading";
import NoSearchImage from "../../../../images/no-search.svg";
import "./style.less";
import CloseOutlined from "@ant-design/icons/CloseOutlined";
import LocationArrow from "../../../../images/location-arrow.svg";
import PanelContent from "../PanelContent";
import moment from "moment-timezone";
import OnOffCompositeSwitch from "../../../../component/OnOffComopsiteSwitch";
import getOnlineOfflineOnlyStatus from "../../../../logic/status";
import PageFilter from "../PageFilter";
import { getAqiColorStatusForValue } from "../../../../logic/aqiParametersToShow";
import ImageComponent from "@datoms/react-components/src/components/ImageComponent";
import legendRunning from "../../../../images/Legend-Running.svg";
import legendStopped from "../../../../images/Legend-Stopped.svg";
import legendNotConnected from "../../../../images/Legend-Not-Connected.svg";
import legendOffline from "../../../../images/Legend-Offline.svg";
import SistemaBioOnline from "../../../../images/Legend-Online-SistemaBio.svg"
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import { FixedSizeList as List } from 'react-window';
import AutoSizer from 'react-virtualized/dist/commonjs/AutoSizer';

export default class MapListWithSearch extends React.Component {
  constructor(props) {
    super(props);
  }
  getStatusIcon(status) {
    return status === "1" ? LocationArrow : "";
  }
  renderRow = ({ index, style, data }) => {
    const item = data[index];
    return (
      <div key={index} style={style}>
        {item}
      </div>
    );
  };
  render() {
    let thingsListDropdownItems = [
      <div className="search-with-close">
        <PageFilter
          t={this.props.t}
          drawerCancelCallback={() => this.props.drawerCancelCallback()}
          drawerApplyCallback={() => this.props.drawerApplyCallback()}
          filterIconRef={this.props.filterIconRef}
          isAurassure={this.props.isAurassureThingCat}
          url={this.props.url}
          history={this.props.history}
          site={this.props.site}
          total_sites={this.props.total_sites}
          category={this.props.category}
          total_category={this.props.total_category}
          on_off_status={this.props.on_off_status}
          on_off_device_status={this.props.on_off_device_status}
          operational_st={this.props.operational_st}
          faultSt={this.props.faultSt}
          total_on_off_status={this.props.total_on_off_status}
          onUrlChange={() => this.props.onUrlChange()}
          applyPanelFilterSelect={(a, b) =>
            this.props.applyPanelFilterSelect(a, b)
          }
          search_data={this.props.search_data}
          icon_clicked={this.props.icon_clicked}
          iconClicked={() => this.props.iconClicked()}
          // statusOnClose={() => this.props.statusOnClose()}
          client_id={this.props.client_id}
          filterIconClicked={this.props.filterIconClicked}
          partnerOptions={this.props.partnerOptions}
          selectedPartner={this.props.selectedPartner}
          customerOptions={this.props.customerOptions}
          selectedCustomer={this.props.selectedCustomer}
          territories={this.props.territories}
          showFaultFilter={this.props.showFaultFilter}
          application_id={this.props?.application_id}
          isSistemaBioCustomer={this.props.isSistemaBioCustomer}
        />
      </div>,
    ];
    let drawerThings = [];
    if (
      this.props.drawer_data &&
      this.props.drawer_data &&
      this.props.drawer_data.length
    ) {
      this.props.drawer_data
        .sort((a, b) => {
          if (a.OnOffStatus === "1") {
            return -1;
          } else {
            return 0;
          }
        })
        .map((thingsListData) => {
          drawerThings.push(
            <div className={"things-details"}>
              <div
                className="name-date-icon"
                onClick={(e) => this.props.thingsDrawerClick(thingsListData.id)}
              >
                <div className="icon">
                  {thingsListData.isAurassure ? (
                    <ImageComponent
                      background="transparent"
                      // show_status={true}
                      iconSize="large"
                      src={
                        getAqiColorStatusForValue(
                          thingsListData.aqi,
                          thingsListData.category,
                          thingsListData?.on_off_moving_status,
                        )?.aqiMapIcon
                      }
                      status={
                        thingsListData?.on_off_moving_status === "1"
                          ? "online"
                          : "offline"
                      }
                      deviceStatus={
                        thingsListData?.on_off_moving_status === "1"
                          ? "online"
                          : "offline"
                      }
                    />
                  ) : (
                    <ImageComponent
                      background="transparent"
                      show_status={true}
                      tooltip={true}
                      title={thingsListData?.category_name}
                      category={thingsListData.category}
                      src={thingsListData?.icon}
                      deviceStatus={thingsListData?.deviceStatus}
                      assetHasStatus={
                        thingsListData?.status_option_includes_stopped
                      }
                      status={
                        !thingsListData?.status_option_includes_stopped
                          ? getOnlineOfflineOnlyStatus(
                              thingsListData?.on_off_moving_status,
                            ) === "online"
                            ? "running"
                            : "offline"
                          : thingsListData?.on_off_moving_status === "1"
                            ? "running"
                            : thingsListData?.on_off_moving_status === "2"
                              ? "offline"
                              : "stopped"
                      }
                      faultStatus={thingsListData.faultStatus}
                    />
                  )}
                </div>
              <div style={{
                display: "flex",
                flexDirection: "column",
                gap: 8,
                flex: 1,
                overflow: "hidden",
              }}>
                <div className="name-date">
                  <AntTooltip title={thingsListData?.name}>
                    <div className="thing-name hellip">
                      {thingsListData?.name}
                    </div>
                  </AntTooltip>
                  {thingsListData?.no_status ? (
                    ""
                  ) : (
                    <div className={"time"}>
                      {thingsListData.last_data_recvd_time > 0
                        ? moment
                            .unix(thingsListData.last_data_recvd_time)
                            .format("DD MMM YYYY, HH:mm")
                        : this.props.t? this.props.t('no_data_received'): "No data received"
                      }
                      {!thingsListData?.isAurassure && !this.props?.isSistemaBioCustomer &&
                      thingsListData?.deviceStatus === "offline" ? (
                        <span className="offline-container">Offline</span>
                      ) : (
                        ""
                      )}
                    </div>
                  )}
                </div>
              {thingsListData.customer_name && <div className="offline-container" style={{margin: 0, width: 'fit-content'}}>{thingsListData.customer_name}</div>}
              </div>
            </div>
              {thingsListData?.status_option_includes_stopped && <div className="on-off-lock-button">
                  <OnOffCompositeSwitch
                    deviceStatus={thingsListData?.deviceStatus}
                    dgStatus={thingsListData?.on_off_moving_status}
                    socket={this.props?.socket}
                    client_id={this.props?.client_id}
                    application_id={this.props?.application_id}
                    thingId={thingsListData?.id}
                    commandStatus={thingsListData?.command_status}
                    isLockControlEnabled={thingsListData?.isLockControlEnabled}
                    isControlEnabled={thingsListData?.isControlEnabled}
                    operation_mode={thingsListData?.operation_mode}
                    dg_lock_status={thingsListData?.dg_lock_status}
                    getRemoteLockAccess={
                      thingsListData?.show_lock
                        ? this.props?.getRemoteLockAccess
                        : false
                    }
                    getRemoteAccess={
                      thingsListData?.show_switch
                        ? this.props?.getRemoteAccess
                        : false
                    }
                    category_id={thingsListData?.category}
                  />
              </div>}
              {thingsListData?.aqi ? (
                <div className="aqi-data">
                  <img
                    src={getAqiColorStatusForValue(thingsListData?.aqi)?.drUri}
                  />
                  {`${thingsListData?.aqi} AQI`}
                </div>
              ) : (
                ""
              )}
              {window.innerWidth > 1024 && thingsListData?.aqi ? (
                <div className="aqi-status">
                  {this.props.t
                    ? this.props.t(
                        `${
                          getAqiColorStatusForValue(thingsListData?.aqi)?.status.toLowerCase() === 'very poor'
                            ? 'very_poor'
                            : getAqiColorStatusForValue(thingsListData?.aqi)?.status.toLowerCase()
                        }`
                      )
                    : getAqiColorStatusForValue(thingsListData?.aqi)?.status}
                </div>
              ) : (
                ""
              )}
            </div>,
          );

          return thingsListDropdownItems;
        });
    }
    if (window.innerWidth <= 1024) {
      let statusArray = [];
      if (this.props.status_options && this.props.status_options.length) {
        this.props.status_options.map((status) => {
          statusArray.push(
            <div
              className={
                "status-name " +
                (status === "running"
                  ? "green-dot"
                  : status === "moving"
                    ? "blue-dot"
                    : status === "switched_off"
                      ? "red-dot"
                      : "")
              }
            >
              {status === "running"
                ? "Running"
                : status === "moving"
                  ? "Moving"
                  : status === "switched_off"
                    ? "Stopped"
                    : ""}
            </div>,
          );
        });
      }
      statusArray = [
        <div className="legend-items">
          <img src={legendRunning} />
          Running
        </div>,
        <div className="legend-items">
          <img src={legendStopped} />
          Stopped
        </div>,
        <div className="legend-items">
          <img src={legendNotConnected} />
          Not Connected
        </div>,
        <div className="legend-items">
          <img src={legendOffline} />
          Offline
        </div>,
      ];
      if (this.props.isSistemaBioCustomer) {
        statusArray = [
          <div className="legend-items">
            <img src={SistemaBioOnline} />
            Genset On
          </div>,
          <div className="legend-items">
            <img src={legendOffline} />
            Genset Off
          </div>,
        ];
      }
      thingsListDropdownItems.push(
        <div className="status-container">{statusArray}</div>,
      );
    }
    if (drawerThings && drawerThings.length) {
      thingsListDropdownItems = thingsListDropdownItems.concat(
        <div
          style={{
            opacity: this.props.filterIconClicked ? 0 : 1,
          }}
          className="total-container"
        >
          <AutoSizer>
          {({ height, width }) => (
            <List
              height={height}
              width={width}
              itemCount={drawerThings.length}
              itemSize={this.props.application_id === 16?76:105}
              itemData={drawerThings}
            >
              {this.renderRow}
            </List>
          )}
        </AutoSizer>
        </div>,
      );
    } else {
      thingsListDropdownItems.push(
        <div className="no-search">
          <div className="no-data-icon">
            <div className="image">
              <img src={NoSearchImage} />
            </div>
            <div className="text">
              {this.props.t? this.props.t('no_results') + "! ": 'No results'}
              {/* No results! */}
            </div>
          </div>
        </div>,
      );
    }
    return (
      <div id="map_list_with_search">
        <AntDrawer
          t={this.props.t}
          visible={this.props.drawer_visible}
          onClose={() => this.props.closeDrawer()}
          closable={window.innerWidth > 768 ? true : false}
          rootClassName={"map-with-search-drawer custom-drawer-list"}
          getContainer={false}
          drawerStyle={{
            background: "#FFFFFF 0% 0% no-repeat padding-box",
            boxShadow: "15px 16px 25px #92929229",
          }}
          mask={false}
          width={window.innerWidth > 1024 ? 554 : "100%"}
          height={"100%"}
          placement={"right"}
          destroyOnClose={true}
          styleType={"rounded"}
        >
          {this.props.selected_id !== undefined ? (
            this.props.panel_loading ? (
              <Loading />
            ) : (
              <PanelContent
                t={this.props.t}
                tanker_date_select={this.props.tanker_date_select}
                constantCurrentTime={this.props.constantCurrentTime}
                tanker_graph_loading={this.props.tanker_graph_loading}
                changeDateTanker={(e) => this.props.changeDateTanker(e)}
                changeDateChambersTanker={(e) =>
                  this.props.changeDateChambersTanker(e)
                }
                tanker_date_select_chamber={
                  this.props.tanker_date_select_chamber
                }
                tanker_graph_loading_chamber={
                  this.props.tanker_graph_loading_chamber
                }
                icon={this.props.icon}
                graph_loading={this.props.graph_loading}
                changeDateChannels={(e) => this.props.changeDateChannels(e)}
                detailed_view={this.props.detailed_view}
                analog_view={this.props.analog_view}
                header_value={this.props.header_value}
                panel_data={this.props.panel_data}
                pathDetailsClick={() => this.props.pathDetailsClick()}
                closeDetailsPanel={() => this.props.closeDetailsPanel()}
                user_preferences={this.props.user_preferences}
                closeDrawer={() => this.props.closeDrawer()}
                trip_loading={this.props.trip_loading}
                currentAddress={this.props.currentAddress}
                viewTrendClick={(e) => this.props.viewTrendClick(e)}
                dcEnergyMeterTrendClick={(e) =>
                  this.props.dcEnergyMeterTrendClick(e)
                }
                selected_channel={this.props.selected_channel}
                is_energy_meter={true}
                viewTrendClose={() => this.props.viewTrendClose()}
                date_select_channel={this.props.date_select_channel}
                all_graphs={this.props.all_graphs}
                today_enrg_value={this.props.today_enrg_value}
                goToPage={this.props.goToPage}
                onCloseClick={() => this.onCloseClick()}
                client_id={this.props.client_id}
                application_id={this.props.application_id}
                socket={this.props?.socket}
                getRemoteLockAccess={this.props.getRemoteLockAccess}
                getRemoteAccess={this.props.getRemoteAccess}
                isAurassure={this.props.isAurassure}
                pollutionThingCats={this.props.pollutionThingCats}
                parent_vendor_id={this.props.parent_vendor_id}
              />
            )
          ) : (
            thingsListDropdownItems
          )}
        </AntDrawer>
      </div>
    );
  }
}
