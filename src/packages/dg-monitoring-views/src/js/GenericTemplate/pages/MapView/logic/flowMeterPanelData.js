import _find from 'lodash/find';
import _orderBy from 'lodash/orderBy';
import { ParamUnit } from '../../../../data_handling/ParamNameUnitFind';

export function getFlowMeterpanelDataFunction() {
    let findThings = _find(this.state.total_data.things, {
        id: parseInt(this.state.selected_id),
    });
    if (findThings) {
        let findLastData = _find(this.state.latestParameterData, {
            thing_id: this.state.selected_id,
        });
        if (findLastData) {
            let totalPanelData = [];
            let trFlowParamExist = _find(findThings.parameters, {
                key: 'tr_flow',
            });
            let channelParameters = {
                flow: {
                    value: findLastData.data['flow']
                        ? findLastData.data['flow']
                        : _find(findThings.parameters, {
                                key: 'flow',
                          })
                        ? _find(findThings.parameters, {
                                key: 'flow',
                          }).value
                        : '',
                    unit: _find(findThings.parameters, {
                        key: 'flow',
                    })
                        ? ParamUnit(
                                _find(findThings.parameters, {
                                    key: 'flow',
                                }).unit
                          )
                        : '',
                },
                t_flow: {
                    value: findLastData.data['t_flow']
                        ? findLastData.data['t_flow']
                        : _find(findThings.parameters, {
                                key: 't_flow',
                          })
                        ? _find(findThings.parameters, {
                                key: 't_flow',
                          }).value
                        : '',
                    unit: _find(findThings.parameters, {
                        key: 't_flow',
                    })
                        ? ParamUnit(
                                _find(findThings.parameters, {
                                    key: 't_flow',
                                }).unit
                          )
                        : '',
                },
            };
            if (trFlowParamExist) {
                channelParameters.tr_flow = {
                    value: findLastData.data['tr_flow']
                        ? findLastData.data['tr_flow']
                        : trFlowParamExist.value,
                    unit: trFlowParamExist.unit || '',
                    name: trFlowParamExist.name || 'Total Volume (Resettable)',
                }
            }
            totalPanelData = {
                panel_id: findLastData.thing_id,
                channel_parameters: channelParameters,
                channel_graph: this.getGraphDataParams(),
                params: this.getFilteredParam(findThings).params,
            };
            return totalPanelData;
        }
    }
}