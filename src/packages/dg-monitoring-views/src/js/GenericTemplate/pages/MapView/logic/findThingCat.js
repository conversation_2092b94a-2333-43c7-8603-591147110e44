import _find from 'lodash/find';

export function findCategoryById(total_data, id, category) {
  if (!id) return false;
  
  const foundItem = _find(total_data.things, { id: parseInt(id) });
  return foundItem && foundItem.category === category;
}

export function isColdStorage(id) {
  return findCategoryById(this.state.total_data, id, 45);
}

export function isDgTrue(id) {
  return findCategoryById(this.state.total_data, id, 18) || findCategoryById(this.state.total_data, id, 96);
}

export function isGasGensetTrue(id) {
  return findCategoryById(this.state.total_data, id, 96);
}

export function isAcEnergy(id) {
  return findCategoryById(this.state.total_data, id, 79) || findCategoryById(this.state.total_data, id, 101);
}

export function isAcElectricalMachines(id) {
  return findCategoryById(this.state.total_data, id, 78);
}

export function isExhaustFan(id) {
  return findCategoryById(this.state.total_data, id, 99);
}

export function isDCEnergy(id) {
  return findCategoryById(this.state.total_data, id, 77);
}

export function isFuelTank(id) {
  return findCategoryById(this.state.total_data, id, 71);
}

export function isFleet(id) {
  return (
      findCategoryById(this.state.total_data, id, 67) ||
      findCategoryById(this.state.total_data, id, 76)
  );
}

export function isTankerTruck(id) {
  return findCategoryById(this.state.total_data, id, 74);
}

export function isSolar(id) {
  return findCategoryById(this.state.total_data, id, 91);
}

export function isBattery(id) {
  return findCategoryById(this.state.total_data, id, 92);
}

export function isTemperatureHumidity(id) {
  return findCategoryById(this.state.total_data, id, 90);
}

export function isProcessAnalyzer(id) {
  return findCategoryById(this.state.total_data, id, 94);
}

export function isCompressor(id) {
  return findCategoryById(this.state.total_data, id, 73);
}

export function isSolarPump(id) {
  return findCategoryById(this.state.total_data, id, 103);
}
