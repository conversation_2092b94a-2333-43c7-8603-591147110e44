import { getThingsData } from '@datoms/js-sdk';
import moment from 'moment-timezone';
import _find from 'lodash/find';

export async function fetchBatteryPanelData() {
	const todayPromise = getThingsData({
		data_type: 'aggregate',
		aggregation_period: 86400,
		parameters: ['calculated_c_enrg', 'calculated_d_enrg'],
		parameter_attributes: ['sum'],
		things: [parseInt(this.state.selected_id)],
		from_time: moment().startOf('day').unix(),
		upto_time: moment().endOf('day').unix(),
	}, this.props.client_id, this.props.application_id);

	const thisMonthPromise = getThingsData({
		data_type: 'aggregate',
		aggregation_period: 2592000,
		parameters: ['calculated_c_enrg', 'calculated_d_enrg'],
		parameter_attributes: ['sum'],
		things: [parseInt(this.state.selected_id)],
		from_time: moment().startOf('month').unix(),
		upto_time: moment().endOf('month').unix(),
	}, this.props.client_id, this.props.application_id);
	const [
		batteryTodayData,
		batteryThisMonthData,
	] = await Promise.all([
		todayPromise,
		thisMonthPromise,
	]);
	this.setState({
		batteryTodayData,
		batteryThisMonthData,
	});
}

export function batteryPanelData() {
	const {
		batteryTodayData,
		batteryThisMonthData,
		modifiedResponse,
	} = this.state;
	let findLastData = _find(this.state.latestParameterData, {
		thing_id: this.state.selected_id,
	});
	let thingParams = _find(this.state.total_data.things, {
		id: parseInt(this.state.selected_id),
	})?.parameters || [];
	const RealtimeParams = [
		{
			value: findLastData?.data['t_volt'],
			unit: _find(modifiedResponse?.param_data, { key: 't_volt' })?.unit,
			name: 'Terminal Voltage',
			key: 't_volt'
		},
	];
	const energyUnit = _find(modifiedResponse?.param_data, {key: 'calculated_c_enrg'})?.unit;
	function getParamValue(batteryData, paramKey) {
		const finalData = batteryData?.data?.[0]?.parameter_values?.[paramKey]?.sum;
		return !isNaN(parseFloat(finalData)) ? parseFloat(finalData).toFixed(2) : 'NA'
	}
	const chargingParams = [
		{
			param_duration: 'today',
			param_value: getParamValue(batteryTodayData, 'calculated_c_enrg'),
			unit: energyUnit,
			key: "calculated_c_enrg",
		},
		{
			param_duration: 'this_month',
			param_value: getParamValue(batteryThisMonthData, 'calculated_c_enrg'),
			unit: energyUnit,
			key: "calculated_c_enrg",
		},
	];


	const disChargingParams = [
		{
			param_duration: 'today',
			param_value: getParamValue(batteryTodayData, 'calculated_d_enrg'),
			unit: energyUnit,
			key: "calculated_d_enrg",
		},
		{
			param_duration: 'this_month',
			param_value: getParamValue(batteryThisMonthData, 'calculated_d_enrg'),
			unit: energyUnit,
			key: "calculated_d_enrg",
		},
	]

	const summaryParams = [
		{
			key:"c_enrg",
			params:chargingParams
		},
		{
			key:"d_enrg", 
			params:disChargingParams
		}
	]

	let filteredSummaryParams = [];

	thingParams.forEach((param) => {
		let findParam = _find(summaryParams, {key: param?.key})
		if(findParam) {
			if (!_find(filteredSummaryParams, {key: findParam.params.key})) {
				filteredSummaryParams.push(...findParam.params);
			}
		}
	})
	return {
		real_time_params: RealtimeParams,
		panel_summary_params: filteredSummaryParams,
	};
}
