import { getThingsData } from '@datoms/js-sdk';
import moment from 'moment-timezone';
import _find from "lodash/find";
import { secondsToTimeFormat } from '../../../../data_handling/TimeFormatting';

interface ComponentState {
  total_data: {
    things: Array<{
      id: number;
      parameters: Array<{
        key: string;
        value: string;
        unit?: string;
      }>;
    }>;
  };
  latestParameterData: Array<{
    thing_id: number;
    data: Record<string, string>;
  }>;
  selected_id: string | number;
  solarPumpTodayData?: any;
  solarPumpThisMonthData?: any;
}

const paramsToShow = ["rpm", "rpm_per", "cmd_speed", "curr", "mtr_pow"];
const summaryParams = [
  {key: "calculated_runhour", title: "Run Hour (HH:MM)"},
  {key: "calculated_energy", title: "Energy (kWh)"},
];
const summaryParamDurations = ["today", "this_month"];

export async function fetchSolarPumpPanelData(this: { state: ComponentState; props: any; setState: (state: Partial<ComponentState>) => void }) {
  try {
    const selectedId = parseInt(String(this.state.selected_id));
    const clientId = this.props.client_id;
    const applicationId = this.props.application_id;

    const todayPromise = getThingsData({
      data_type: 'aggregate',
      aggregation_period: 86400,
      parameters: ['calculated_runhour', 'calculated_energy'],
      parameter_attributes: ['sum'],
      things: [selectedId],
      from_time: moment().startOf('day').unix(),
      upto_time: moment().endOf('day').unix(),
    }, clientId, applicationId);

    const thisMonthPromise = getThingsData({
      data_type: 'aggregate',
      aggregation_period: 2592000,
      parameters: ['calculated_runhour', 'calculated_energy'],
      parameter_attributes: ['sum'],
      things: [selectedId],
      from_time: moment().startOf('month').unix(),
      upto_time: moment().endOf('month').unix(),
    }, clientId, applicationId);

    const [solarPumpTodayData, solarPumpThisMonthData] = await Promise.all([
      todayPromise,
      thisMonthPromise,
    ]);

    this.setState({
      solarPumpTodayData,
      solarPumpThisMonthData,
    });
  } catch (error) {
    console.error("Error fetching solar pump data:", error);
  }
}

export function solarPumpPanelData(this: { state: ComponentState }) {
  const selectedId = parseInt(String(this.state.selected_id));
  const solarPumpData: Record<string, any> = {
    today: this.state.solarPumpTodayData,
    this_month: this.state.solarPumpThisMonthData,
  }

  console.log("solar pump today data", solarPumpData.today);
  console.log("solar pump this month data", solarPumpData.this_month);

  let findThings = _find(this.state.total_data.things, {
    id: selectedId,
  });

  let findLastData = _find(this.state.latestParameterData, {
    thing_id: selectedId,
  });

  let parameterData: Record<string, any> = {};

  if (findLastData && findThings) {
    findThings.parameters.forEach((param: { key: string; unit?: string }) => {
      if (paramsToShow.includes(param.key)) {
        let paramValue: string | number = parseFloat(
          findLastData?.data[param.key],
        );

        if (!isNaN(paramValue)) {
          paramValue = paramValue.toFixed(2);
          if (param.key === "rpm_per" && parseInt(String(paramValue)) === 100) {
            paramValue = "100";
          }
          paramValue = paramValue + " " + param?.unit;
        } else {
          paramValue = "NA";
        }

        parameterData[param.key] = paramValue;
      }
    });
  }

  function getParamValue(solarPumpData: { data?: Array<{ parameter_values?: Record<string, { sum: number }> }> }, paramKey: string) {
    let finalData = 0;
    if(solarPumpData?.data?.length) {
      solarPumpData?.data?.forEach((data: { parameter_values?: Record<string, { sum: number }> }) => {
        if(data?.parameter_values?.[paramKey]?.sum) {
          finalData += data.parameter_values[paramKey].sum;
        }
      });
    }
    
    if (!finalData) return 'NA';
    if(paramKey === "calculated_runhour") {
      return secondsToTimeFormat(finalData, "HH:mm");
    }
    const finalValue = parseFloat(String(finalData));
    return isNaN(finalValue) ? 'NA' : finalValue.toFixed(2);
  }

  summaryParams.forEach((param: {key: string, title: string}) => {
    parameterData[param.key] = {title: param.title, today: "NA", this_month: "NA"};
    summaryParamDurations.forEach((duration: string) => {
      parameterData[param.key][duration] = getParamValue(solarPumpData[duration], param.key);
    });
  });

  return parameterData;
}
