import {
	establishSocketConnection,
	subscribeForThingsUpdates,
	subscribeForEventsUpdates,
	subscribeForEntityUpdates,
} from '@datoms/js-sdk';
import _find from 'lodash/find';
import _findIndex from 'lodash/findIndex';
import _merge from 'lodash/merge';
import { getSocketInterval } from '../../../logic/socketUtility';
import Backyard from '@datoms/js-utils/src/Backyard/Backyard_back';

export function callSocket() {
	this.socket = establishSocketConnection();
	this.socket.on('connect', () => {
		subscribeForThingsUpdates(
			this.socket,
			this.state.modifiedResponse.all_thing_ids,
			0
		);
		subscribeForEventsUpdates(
			this.socket,
			this.state.client_id,
			this.props.application_id
		);
		subscribeForEntityUpdates(
			this.socket,
			this.state.modifiedResponse.all_thing_ids,
			'thing'
		);
	});
	this.socket.on('new_event_generated', (payload) => {
		if (payload) {
			this.bucket['event_data'].push(payload);
		}
	});

	const bucketTimeInterval = getSocketInterval(
    this.state.modifiedResponse.all_thing_ids?.length || 0,
    "map",
  );
	this.bucketTime = setInterval(() => {
		if (
			Object.keys(this.bucket.raw_data).length ||
			this.bucket.event_data.length
		) {
			this.realTimeEventsUpdate(this.bucket);
			this.backyardSocketUpdate(this.bucket.raw_data);
		}
		this.bucket = {
			raw_data: {},
			event_data: [],
		};
	}, bucketTimeInterval);
	this.socket.on('new_data_generated_for_station', (payload) => {
		if (payload && payload.type === 'raw') {
			if(parseInt(payload.thing_id) === parseInt(this.state.selected_id)){
				this.realTimeUpdateFunc({
					[payload.thing_id]: payload
				});
			} else {
				this.bucket['raw_data'][payload.thing_id] = payload;
			}
		}
	});
	this.socket.on('details_updated', (payload) => {
		this.updateEntityDetails(payload);
	});
}

export function updateEntityDetails(payload) {
	if (
		payload.entity_type === 'thing' &&
		!isNaN(parseInt(payload.entity_id)) &&
		payload.details
	) {
		const { total_data } = this.state;
		if (total_data.things && total_data.things.length) {
			let totalDataResponse = JSON.parse(JSON.stringify(total_data));
			let thingDetailsIndex = _findIndex(totalDataResponse.things, {
				id: parseInt(payload.entity_id),
			});
			if (
				thingDetailsIndex > -1 &&
				totalDataResponse &&
				totalDataResponse.things
			) {
				totalDataResponse.things[thingDetailsIndex] = _merge(
					{},
					totalDataResponse.things[thingDetailsIndex],
					payload.details
				);
			}
			this.setState(
				{
					total_data: totalDataResponse,
				},
				() => {
					this.getThingsListDrawerData();
				}
			);
		}
	}
}

export function realTimeEventsUpdate(payload) {
	let offlineArray = this.state.offlineArray;
	if (
		payload &&
		payload.event_data &&
		Object.keys(payload.event_data).length &&
		payload.event_data.entity_type === 'thing' &&
		payload.event_data.tags &&
		Array.isArray(payload.event_data.tags)
	) {
		let indexOffline = _findIndex(offlineArray, {
			thing_id: parseInt(payload.event_data.entity_id),
		});
		if (indexOffline > -1) {
			if (payload.event_data.tags.includes('Offline'))
				offlineArray[indexOffline].status = 'offline';
			if (payload.event_data.tags.includes('Online'))
				offlineArray[indexOffline].status = 'online';
		}
		this.setState({
			offlineArray,
		});
	}
}

export function realTimeUpdateFunc(payload) {
	let latestParameterData;
	let realtimeThingsData = this.state.latestParameterData;
	let payloadData,
		payloadDataFuel,
		payloadDataDcEnergy,
		fleetPayloadRawDataFunc,
		tankerPayloadRawDataFunc;
	let rawDataRealTime = {};
	if (payload && Object.keys(payload).length) {
		Object.keys(payload).map((payload_key) => {
			let findIndex = _findIndex(realtimeThingsData, {
				thing_id: parseInt(payload_key),
			});
			let findTotalData = _find(this.state.total_data.things, {
				id: parseInt(payload_key),
			});
			let findParamInd = -1;
			if (findIndex > -1) {
				if (
					Object.keys(payload[payload_key].data) &&
					Object.keys(payload[payload_key].data).length
				) {
					Object.keys(payload[payload_key].data).map((key) => {
						if (
							findTotalData &&
							Array.isArray(findTotalData.parameters)
						) {
							findParamInd = _findIndex(
								findTotalData.parameters,
								{
									key: key,
								}
							);
						}
						if (findTotalData?.category === 18 && key === 'fuel') {
							if (payload[payload_key].data['fuel_raw']) {
								realtimeThingsData[findIndex]['data'][key] =
									payload[payload_key].data['fuel_raw'];
							} else {
								realtimeThingsData[findIndex]['data'][key] =
									payload[payload_key].data[key];
							}
						} else {
							if (findParamInd > -1) {
								realtimeThingsData[findIndex]['data'][key] =
									payload[payload_key].data[key];
							}
						}
						if (payload[payload_key].data['status_changes']) {
							realtimeThingsData[findIndex]['data'][
								'status_changes'
							] = payload[payload_key].data['status_changes'];
						} else {
							realtimeThingsData[findIndex]['data'][
								'status_changes'
							] = undefined;
						}
						if (payload[payload_key].data['start_time']) {
							realtimeThingsData[findIndex]['data'][
								'start_time'
							] = payload[payload_key].data['start_time'];
						} else {
							realtimeThingsData[findIndex]['data'][
								'start_time'
							] = undefined;
						}
					});
				}
				realtimeThingsData[findIndex]['time'] =
					payload[payload_key].time;
			}

			latestParameterData = this.offlineTimeOutFunction(
				payload[payload_key],
				this.state.offlineArray,
				this.state.total_data,
				realtimeThingsData,
				this.state.modifiedResponse
			);
			if (
				payload[payload_key] &&
				payload[payload_key].type === 'raw' &&
				this.state.data_type === 'raw'
			) {
				rawDataRealTime = {
					time: payload[payload_key].time,
					thing_id: parseInt(payload_key),
					parameter_values: payload[payload_key].data,
				};
			}
			if (findTotalData.category === 71) {
				payloadDataFuel = this.payloadDataFunc(rawDataRealTime);
			} else if (
				findTotalData.category === 67 ||
				findTotalData.category === 76
			) {
				fleetPayloadRawDataFunc =
					this.fleetPayloadRawDataFunc(rawDataRealTime);
			} else if (findTotalData.category === 74) {
				tankerPayloadRawDataFunc =
					this.tankerPayloadRawDataFunc(rawDataRealTime);
			} else {
				payloadData = this.payloadRawDataFunc(rawDataRealTime);
			}
		});
		this.setState(
			{
				latestParameterData: latestParameterData,
				fuelTankParameterDataRaw:
					payloadDataFuel?.fuelTankParameterDataRaw,
				graph_data_channel: payloadData?.thingAllParamDataChemberRaw,
				fleet_fuel_graph_data: fleetPayloadRawDataFunc,
				tanker_fuel_graph_data:
					tankerPayloadRawDataFunc?.thingAllParamDataRaw,
				tanker_fuel_graph_data_chamber:
					tankerPayloadRawDataFunc?.thingAllParamDataChemberRaw,
				dc_energy_graph_data_channel:
					payloadDataDcEnergy?.thingAllParamDataChemberRaw,
			},
			() => {
				let findLastData = _find(this.state.latestParameterData, {
					thing_id: this.state.selected_id,
				});
				if (
					findLastData &&
					window.innerWidth > 1024 &&
					!this.state.trip_drawer_visible
				) {
					this.zoomOnPolyline([
						{
							lat: parseFloat(
								this.getLatLongAsPerAvailability().getLat
							),
							lng: parseFloat(
								this.getLatLongAsPerAvailability().getLng
							),
						},
					]);
				}
				if (
					(findLastData && this.isFleet(this.state.selected_id)) ||
					this.isTankerTruck(this.state.selected_id)
				) {
					if (
						findLastData.data.status_changes &&
						findLastData.data.status_changes.mc_st
					) {
						if (
							findLastData.thing_id === this.state.selected_id &&
							findLastData.data.status_changes.mc_st.old ===
								'0' &&
							findLastData.data.status_changes.mc_st.new === '1'
						) {
							this.setState({
								last_is_moving:
									findLastData.OnOffStatus === '1',
								last_moving_start_time: parseInt(
									findLastData?.data?.['start_time']
								),
							});
						} else if (
							findLastData.thing_id === this.state.selected_id &&
							findLastData.data.status_changes.mc_st.old ===
								'1' &&
							findLastData.data.status_changes.mc_st.new === '0'
						) {
							this.setState({
								loading_for_task: true,
							});
							setTimeout(async () => {
								await this.fetchMissionListVehicle(
									this.state.selected_id
								);
								this.toggleTripDrawer(true);
							}, 90000);
						}
					}
				}
				if (this.state.selected_trip_id === 'ongoing') {
					this.pathRealtimeUpdate(this.bucket);
				}
			}
		);
	}
}

export function backyardSocketUpdate(payload) {
	const inputState = {
	  latestParameterData: this.state.latestParameterData,
	  total_data: this.state.total_data,
	  offlineArray: this.state.offlineArray,
	};
	new Backyard({
	  scripts: [
		"https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js",
		"https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.23/moment-timezone.min.js",
		"https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.33/moment-timezone-with-data-1970-2030.min.js",
		"https://cdn.jsdelivr.net/npm/lodash@4.17.15/lodash.min.js",
	  ],
	  input: {
		state: inputState,
		payload: payload,
	  },
	  run: function (ctx, input, cb) {
		/* Function Defination */
		function checkOfflineOnlineStatus(offlineArray, onOffStatus, thing_id) {
			let offlineStatus = ctx["_"].find(offlineArray, {
			  thing_id,
			})?.status;
			return offlineStatus === "offline" ? "2" : onOffStatus;
		  }
		/* End */
		const { state, payload } = input;
		let realtimeThingsData = state.latestParameterData;
		
  
		Object.keys(payload).map((payload_key, index) => {
		  let findIndex = ctx["_"].findIndex(realtimeThingsData, {
			thing_id: parseInt(payload_key),
		  });
		  let findTotalData = ctx["_"].find(state.total_data.things, {
			id: parseInt(payload_key),
		  });
		  let findParamInd = -1;
		  if (findIndex > -1) {
			if (
			  Object.keys(payload[payload_key].data) &&
			  Object.keys(payload[payload_key].data).length
			) {
			  Object.keys(payload[payload_key].data).map((key) => {
				if (findTotalData && Array.isArray(findTotalData.parameters)) {
				  findParamInd = ctx["_"].findIndex(findTotalData.parameters, {
					key: key,
				  });
				}
				if (findTotalData?.category === 18 && key === "fuel") {
				  if (payload[payload_key].data["fuel_raw"]) {
					realtimeThingsData[findIndex]["data"][key] =
					  payload[payload_key].data["fuel_raw"];
				  } else {
					realtimeThingsData[findIndex]["data"][key] =
					  payload[payload_key].data[key];
				  }
				} else {
				  if (findParamInd > -1) {
					realtimeThingsData[findIndex]["data"][key] =
					  payload[payload_key].data[key];
				  }
				}
				if (payload[payload_key].data["status_changes"]) {
				  realtimeThingsData[findIndex]["data"]["status_changes"] =
					payload[payload_key].data["status_changes"];
				} else {
				  realtimeThingsData[findIndex]["data"]["status_changes"] =
					undefined;
				}
				if (payload[payload_key].data["start_time"]) {
				  realtimeThingsData[findIndex]["data"]["start_time"] =
					payload[payload_key].data["start_time"];
				} else {
				  realtimeThingsData[findIndex]["data"]["start_time"] = undefined;
				}
			  });
			}
  
			let isMcSt =
			 findTotalData?.status === "offline"
				? "2"
				: realtimeThingsData[findIndex].hasMcStParam
				  ? payload[payload_key].data["mc_st"]
				  : realtimeThingsData[findIndex].category === 18 ||
					  realtimeThingsData[findIndex].category === 96
					? "0"
					: "1";
			
			realtimeThingsData[findIndex]["on_off_moving_status"] =
			  checkOfflineOnlineStatus(
				state.offlineArray,
				parseInt(isMcSt) ? parseInt(isMcSt).toString() : "0",
				parseInt(payload_key),
			  );
			realtimeThingsData[findIndex]["time"] = payload[payload_key].time;
		  }
		});
  
		cb({
		  latestParameterData: realtimeThingsData,
		});
	  },
	  cb: ({ latestParameterData }) => {
		this.setState({ latestParameterData: latestParameterData });
	  },
	});
  }


