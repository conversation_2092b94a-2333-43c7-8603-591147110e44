/**
 * mriPanelData
 *
 * This function extracts and formats the real-time MRI parameter data for a selected device
 * from the component's internal state (`this.state`). It filters the parameters based on
 * a predefined list of MRI parameter keys (`mriRealTimeParamKeys`) and combines their latest
 * values with corresponding units.
 *
 * It is intended to be used as a method within a class component, and relies on:
 * - `this.state.total_data.things`: An array of MRI device metadata.
 * - `this.state.selected_id`: The currently selected MRI device's ID (as a string or number).
 * - `this.state.latestParameterData`: An array of the latest values keyed by `thing_id`.
 *
 * Uses lodash `_find` to locate matching device and its data.
 *
 * @returns An object with a `real_time_params` array, where each entry contains:
 * - `key`: The parameter key.
 * - `value`: The latest value for the parameter, rounded to 2 decimal places or `"NA"` if missing.
 * - `unit`: The associated unit for the parameter.
 *
 * Example return:
 * ```ts
 * {
 *   real_time_params: [
 *     { key: "helium_pressure", value: "17.68", unit: "PSI" },
 *     { key: "case_temp", value: "NA", unit: "°C" },
 *   ]
 * }
 * ```
 *
 * @note Ensure this function is bound correctly if used in a class component (`this` context is required).
 */
import _find from "lodash/find";
import { mriRealTimeParamKeys } from "../../../configs/MRIMachineConfig";

export function mriPanelData() {
  // Find the selected thing/device from state
  let findThings = _find(this.state.total_data.things, {
    id: parseInt(this.state.selected_id),
  });

  // Find latest data entry for selected thing/device
  let findLastData = _find(this.state.latestParameterData, {
    thing_id: parseInt(this.state.selected_id),
  });

  // Initialize array to store final parameter data
  let parameterData: { key: string; value: string | number; unit: string }[] = [];

  if (findLastData && findThings) {
    findThings.parameters.forEach((param: any) => {
      if (mriRealTimeParamKeys.includes(param.key)) {
        const rawValue = findLastData?.data[param.key];
        let valueToPush: string;
        if (rawValue !== undefined && rawValue !== null && String(rawValue).trim() !== "" && !isNaN(Number(rawValue))) {
          valueToPush = Number(rawValue).toFixed(2);
        } else if (String(rawValue).toUpperCase() === "NA") {
          valueToPush = "NA";
        } else {
          valueToPush = "NA"; // Default for undefined, null, empty string, or other non-numeric values
        }

        parameterData.push({
          key: param.key,
          value: valueToPush,
          unit: param.unit,
        });
      }
    });
  }

  return { real_time_params: parameterData };
}
