import React from 'react';
import './style.less';
import FullscreenOutlined from '@ant-design/icons/FullscreenOutlined';
import GraphDateChange from '../GraphDateChange';
import FlowImage from '../../../../images/Group 3559.svg';

export default class FlowMeterPanel extends React.Component {
	constructor(props) {
		super(props);
	}

	mainpanelReturn() {
		const { panel_data, all_graphs, date_select_channel } = this.props;
		return (
			<div className="channel-data">
				<div className="channel-param">
					<img src={FlowImage} />
					<div className="except-energy">
						<div className="param-container">
							<div className="value">
								{!isNaN(
									parseFloat(
										panel_data?.channel_parameters?.flow
											.value,
									),
								)
									? parseFloat(
											panel_data?.channel_parameters?.flow
												.value,
										).toFixed(2)
									: 'NA'}
							</div>
							<div className="title">Flow Rate</div>
							<div className="title">
								{'(' +
									panel_data?.channel_parameters?.flow.unit +
									')'}
							</div>
						</div>
						<div className="param-container">
							<div className="value">
								{!isNaN(
									parseFloat(
										panel_data?.channel_parameters?.t_flow
											.value,
									),
								)
									? parseFloat(
											panel_data?.channel_parameters
												?.t_flow.value,
										).toFixed(2)
									: 'NA'}
							</div>
							<div className="title">Totalized Flow Volume</div>
							<div className="title">
								{'(' +
									panel_data?.channel_parameters?.t_flow
										.unit +
									')'}
							</div>
						</div>
						{panel_data?.channel_parameters?.tr_flow &&
						 <div className="param-container">
							<div className="value">
								{!isNaN(
									parseFloat(
										panel_data?.channel_parameters?.tr_flow
											.value,
									),
								)
									? parseFloat(
											panel_data?.channel_parameters
												?.tr_flow.value,
										).toFixed(2)
									: 'NA'}
							</div>
							<div className="title">{panel_data?.channel_parameters?.tr_flow.name}</div>
							<div className="title">
								{'(' +
									panel_data?.channel_parameters?.tr_flow
										.unit +
									')'}
							</div>
						</div>}
					</div>
				</div>
				{window.innerWidth <= 1024 ? (
					''
				) : (
					<div className="parameters">
						<div className="title-with-date">
							<div className="title">
								Parameter Trends
								<FullscreenOutlined
									onClick={() => this.props.viewTrendClick()}
								/>
							</div>
							<GraphDateChange
								date_select_channel={date_select_channel}
								changeDateChannels={(e) =>
									this.props.changeDateChannels(e)
								}
							/>
						</div>
						{all_graphs?.paramGraphs}
					</div>
				)}
			</div>
		);
	}

	render() {
		return <div id="flow_meter_panel">{this.mainpanelReturn()}</div>;
	}
}
