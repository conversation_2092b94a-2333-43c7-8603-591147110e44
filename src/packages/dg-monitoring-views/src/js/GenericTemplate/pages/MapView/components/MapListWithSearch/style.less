#map_list_with_search {
	.swap-icon {
		cursor: pointer;
		position: absolute;
		right: 554px;
		padding: 5px 8px;
		background-color: #fff;
		border-top-left-radius: 10px;
		border-bottom-left-radius: 10px;
	}

	.map-with-search-drawer {
		transform: translateX(0px) !important;
		z-index: 1;
		&.custom-drawer-list {
			position: absolute;
			&.ant-drawer-open > .ant-drawer-content-wrapper {
				transform: translateX(0) !important;
				// transition-duration: 0ms !important;
			}
			.ant-drawer-content-wrapper
				.ant-drawer-content
				.ant-drawer-body {
				padding: 0px !important;
			}
		}
		.ant-drawer-content-wrapper {
			box-shadow: none !important;
			.ant-drawer-content {
				border-top-left-radius: 6px;
				// border-bottom-left-radius: 17px;
					.ant-drawer-header-close-only {
						position: absolute;
						right: 0;
						border: 0px;
					}
					.ant-drawer-body {
						background: #fff;
						display: flex;
						flex-direction: column;
						.search-with-close {
							display: flex;
							align-items: center;
							padding: 10px 20px 10px 0;
							justify-content: space-between;
							width: 490px;
							.anticon-close {
								cursor: pointer;
								border-radius: 50%;
								background: #c4c2c250;
								padding: 4px;
								margin-left: 10px;
								font-weight: 600;
							}
							#map_thing_filter {
								width: 100%;
							}
						}
						.search-container {
							margin: 0px;
							width: calc(100% - 40px);
							.search-input {
								padding: 0px;
								width: 95% !important;
								font-size: 13px;
								background-color: transparent;
								.ant-input-prefix {
									left: 10px !important;
									top: 10px;
									width: 14px;
									height: 14px;
								}
							}
							.ant-input-affix-wrapper {
								.ant-input:hover {
									border: none;
									// border: 1px solid #81818177;
									// border-radius: 16px;
									// padding-left: 20px;
								}
								.ant-input {
									width: 100% !important;
									padding-left: 30px;
									font-size: 13px;
									height: 35px;
									font-style: italic;
									color: #232323;
									box-shadow: none;
									border-radius: 6px 0 0 6px;
									background: #fcfbfb 0% 0% no-repeat
										padding-box;
									box-shadow: 2px 4px 12px #00000012;
									border: 3px solid #eaeaea4d;
									width: 100%;
									//box-shadow: 2px 4px 12px #0000004a;
									//padding-bottom: 5px;
									//border-radius: 16px;
									// border: 1px solid #81818177;
								}
								.ant-input:focus {
									border: none;
									border: 1px solid #ff8500;
								}
							}
							// .ant-input-affix-wrapper-focused {
							// 	.ant-input-prefix {
							// 		display: none;
							// 	}
							// }
						}
						.total-container {
							flex: 1;
							overflow: hidden auto;
							.things-details {
								display: flex;
								align-items: center;
								justify-content: space-between;
								padding: 15px 20px;
								border-bottom: 1px solid #c4c2c250;
								gap: 36px;
								.name-date-icon {
									cursor: pointer;
									display: flex;
									align-items: flex-start;
									flex: 1;
									overflow: hidden;
									min-width: 0;
									.status-circle {
										border-radius: 50%;
										height: 12px;
										width: 12px;
										&.online {
											background-color: #147437;
										}
										&.offline {
											background-color: #808080;
										}
										&.switch-off {
											background-color: #ff0000;
										}
									}
									.name-date {
										width: 100%;
										.thing-name {
											color: #232323;
											font-size: 14px;
											font-weight: 600;
											width: 100%;
										}
										.time {
											color: #808080;
											font-size: 13px;
										}
									}
								}
								.offline-container {
									margin-left: 10px;
									background: #c4c2c240;
									color: #232323;
									font-size: 12px;
									padding: 2px 10px;
									border-radius: 5px;
								}
								.status-shown-div {
									color: #808080;
									text-align: left;
									font-size: 12px;
									letter-spacing: 0px;
									opacity: 1;
									.anticon {
										margin-right: 5px;
									}
								}
								.off-thing {
									color: #ff4904;
								}
								.running-thing {
									color: #25c479;
								}
								.aqi-data {
									display: flex;
									align-items: center;
									img {
										width: 18px;
										height: 18px;
										margin-right: 5px;
									}
								}
								.aqi-status {
									width: 100px;
									text-align: center;
								}
							}
						}
						.selected-thing {
							background: #232323;
							.things-name {
								font-weight: 600;
								color: #232323;
							}
						}
						.no-search {
							flex: 1;
							overflow: hidden auto;
							.no-data-icon {
								position: absolute;
								left: 50%;
								top: 50%;
								-webkit-transform: translate(-50%, -50%);
								transform: translate(-50%, -50%);
								.text {
									color: #232323;
									margin-top: 10px;
									font-style: italic;
									text-align: center;
								}
							}
						}
						#pomo_panel_body_id {
							padding: 0 14px;
							.real-time-parameters.single-line {
								gap: 20px;
								.values {
									width: 100%;
								}
							}
						}
					}
				// }
			}
		}
	}
}

@media (max-width: 768px) {
	#map_list_with_search {
		.map-with-search-drawer {
			.ant-drawer-content-wrapper
			.ant-drawer-content
			.ant-drawer-body {
				.search-container {
					width: calc(100% - 60px);
					margin-left: 10px;
					.ant-input {
						background: #f7f1f1 !important;
						border-radius: 20px !important;
					}
				}
				.search-with-close {
					width: 100%;
				}
			}
			.status-container {
				display: flex;
				width: 100%;
				padding: 10px 20px;
				.status-name {
					margin-right: 10px;
					padding: 5px 10px;
					border-radius: 20px;
					border: 1px solid #c4c2c2;
					color: #808080;
					font-size: 11px;
					display: flex;
					height: 25px;
					align-items: center;
				}
				.status-name:before {
					content: '•'; /* bullet point character */
					margin-right: 5px; /* some spacing */
					font-size: 19px;
				}
				.status-name.green-dot:before {
					color: #1cb855;
				}
				.status-name.red-dot:before {
					color: #f65f54;
				}
				.status-name.grey-dot:before {
					color: #858787;
				}
				.status-name.blue-dot:before {
					color: #4669af;
				}
				.legend-items{
					margin-right: 11px;
					display:flex;
					align-items: center;
					font-size: 12px;
					img{
						width: 12px;
						margin-right: 5px;
					}
					
				}
			}
		}
	}
}
