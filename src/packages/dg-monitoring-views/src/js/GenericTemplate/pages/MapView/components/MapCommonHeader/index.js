import React from "react";
import "./style.less";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import OpenMaps from "@datoms/react-components/src/components/OpenMaps";
import ArrowLeftOutlined from "@ant-design/icons/ArrowLeftOutlined";
import CloseOutlined from "@ant-design/icons/CloseOutlined";
import ClockCircleOutlined from "@ant-design/icons/ClockCircleOutlined";
import LayoutOutlined from "@ant-design/icons/LayoutOutlined";
import LocationArrow from "../../../../images/location-arrow.svg";
import showActiveFault from "../../../../component/ShowActiveFaults";
import MaintenanceText from "../../../../component/MaintenanceText";
import OnOffCompositeSwitch from "../../../../component/OnOffComopsiteSwitch";
import getOnlineOfflineOnlyStatus from "../../../../logic/status";
import ImageComponent from "@datoms/react-components/src/components/ImageComponent";
import LocationIcon from "../../../../images/location-icon.svg";
import { getAqiColorStatusForValue } from "../../../../logic/aqiParametersToShow";
import { relatedCustomerIds } from "../../../../../../../../../configs/customer-specific-config";
import { GlobalContext } from "../../../../../../../../../store/globalStore";

export default class MapCommonHeader extends React.Component {
  static contextType = GlobalContext;
  constructor(props) {
    super(props);
    this.mobile = window.innerWidth <= 1024;
  }
  onOffSwitchRender(header_value) {
    return (
      <div className="on-off-lock-button">
        {!header_value?.status_option_includes_stopped ? (
          // getOnlineOfflineOnlyStatus(
          // 	header_value?.on_off_moving_status
          // ) === 'online' ? (
          // 	<span className="online">Online</span>
          // ) : (
          // 	''
          // )
          ""
        ) : (
          <OnOffCompositeSwitch
            deviceStatus={header_value?.deviceStatus}
            dgStatus={header_value?.on_off_moving_status}
            socket={this.props?.socket}
            client_id={this.props?.client_id}
            application_id={this.props?.application_id}
            thingId={header_value?.id}
            commandStatus={header_value?.command_status}
            isLockControlEnabled={header_value?.isLockControlEnabled}
            isControlEnabled={header_value?.isControlEnabled}
            operation_mode={header_value?.operation_mode}
            dg_lock_status={header_value?.dg_lock_status}
            getRemoteLockAccess={
              header_value?.show_lock ? this.props?.getRemoteLockAccess : false
            }
            getRemoteAccess={
              header_value?.show_switch ? this.props?.getRemoteAccess : false
            }
            category_id={header_value?.cat_id}
          />
        )}
      </div>
    );
  }
  getLocationDiv(header_value) {
    let finalDiv = "";
    if (this.props.isAurassure && this.mobile) {
      return;
    }
    if (
      header_value?.location?.length &&
      header_value?.lat &&
      header_value?.lng
    ) {
      if (this.mobile) {
        finalDiv = ''
      } else {
        finalDiv = (
          <div className="location">
            <div className="heading"><img src={LocationIcon}/>
              {this.props.t? this.props.t('current_location'): "Current Location"}
              {/* Current Location */}
              : </div>
            <AntTooltip title={this.props.t? this.props.t('current_location') + ": ": "Current Location: " + header_value?.location}>
              <div className="location-value hellip">
                {header_value?.location}
              </div>
            </AntTooltip>
            {!this.mobile || !this.props.isAurassure ? (
              <div className="open-in-maps">
                <OpenMaps
                  lat={header_value?.lat}
                  lng={header_value?.lng}
                  onlyIcon={true}
                />
              </div>
            ) : (
              ""
            )}
          </div>
        );
      }
    }
    return finalDiv;
  }
  render() {
    const { header_value, detailed_view, analog_view, isAurassure } =
      this.props;
    const isSistemaBioCustomer = this.context?.isSistemaBioCustomer;
    return (
      <div className="panel-header-divider">
        <div className="things-details">
          <div className="name-date-icon">
            <div className="thing-name-close">
              <div>
                <div className="name-flex">
                  <div className="icon">
                    <div className="icon-container">
                      {isAurassure ? (
                        <ImageComponent
                          show_status={false}
                          iconSize = "large"
                          src={getAqiColorStatusForValue(                      
                            undefined, // aqi_value,
                            header_value?.cat_id,
                            header_value?.on_off_moving_status,
                          )?.aqiMapIcon}
                          status={header_value?.on_off_moving_status === "1" ? 'online' : 'offline'}
                          tooltip={true}
                          title={header_value?.cat}
                          deviceStatus={header_value?.on_off_moving_status === "1" ? 'online' : 'offline'}
                        />
                      ) : (
                        <ImageComponent
                          show_status={true}
                          src={header_value?.icon}
                          tooltip={true}
                          title={header_value?.cat}
                          category={header_value?.cat_id}
                          assetHasStatus={
                            header_value?.status_option_includes_stopped
                          }
                          status={
                            !header_value?.status_option_includes_stopped
                              ? getOnlineOfflineOnlyStatus(
                                  header_value?.on_off_moving_status,
                                ) === "online"
                                ? "running"
                                : "offline"
                              : header_value?.on_off_moving_status === "1"
                                ? "running"
                                : header_value?.on_off_moving_status === "2"
                                  ? "offline"
                                  : "stopped"
                          }
                          deviceStatus={header_value?.deviceStatus}
                        />
                      )}
                    </div>
                  </div>
                  <div className="name-date">
                    <AntTooltip title={header_value?.thing_name}>
                      <div className="thing-name hellip">
                        {header_value?.thing_name}
                      </div>
                    </AntTooltip>
                    {header_value?.no_status ? (
                      ""
                    ) : (
                      <div className={"time"}>
                        {header_value?.date}
                        {!isAurassure && !isSistemaBioCustomer &&
                        header_value?.deviceStatus === "offline" ? (
                          <span className="offline-container">Offline</span>
                        ) : (
                          ""
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              {this.mobile ? this.onOffSwitchRender(header_value) : ""}
              {this.mobile && isAurassure ? (
                <div className="open-in-maps">
                  <OpenMaps
                    lat={header_value?.lat}
                    lng={header_value?.lng}
                    onlyIcon={true}
                  />
                </div>
              ) : (
                ""
              )}
              <div className="total-close-with-status">
                {!this.mobile ? this.onOffSwitchRender(header_value) : ""}
                {header_value?.statusIcon ? (
                  <AntTooltip title={header_value.statusIcon.text}>
                    <div className="status-icon">
                      <img src={header_value.statusIcon.icon} />
                    </div>
                  </AntTooltip>
                ) : (
                  ""
                )}
                <CloseOutlined
                  onClick={() => {
                    this.mobile
                      ? this.props.closeDrawer()
                      : this.props.closeDetailsPanel();
                  }}
                  style={{marginLeft: this.mobile ? -20 : 0}}
                />
              </div>
            </div>
            {this.mobile || [12,17].includes(this.props.application_id) ? (
              ""
            ) : analog_view || detailed_view ? (
              <div className="page-links">
                <div className="title">
                  {this.props.t? this.props.t('quick_links'): 'Quick Links'}
                  {/* Quick Links */}
                </div>
                <div className="pages">
                  {detailed_view && !(relatedCustomerIds[1121].includes(parseInt(this.props.parent_vendor_id)) && this.props.isPortableCompressor) ? (
                    <div
                      className="link"
                      onClick={() =>
                        this.props.goToPage("detailed-view", header_value?.id)
                      }
                    >
                      <LayoutOutlined />
                      {this.props.t? this.props.t('detailed_view'): 'Detailed View'}
                      {/* Detailed View */}
                    </div>
                  ) : (
                    ""
                  )}
                  {analog_view ? (
                    <div
                      className="link"
                      onClick={() =>
                        this.props.goToPage("real-time", header_value?.id)
                      }
                    >
                      <ClockCircleOutlined />
                      {this.props.t? this.props.t('analog_view'): 'Analog View'}
                      {/* Analog View */}
                    </div>
                  ) : (
                    ""
                  )}
                </div>
              </div>
            ) : (
              ""
            )}
          </div>
          {header_value?.thing_details?.length ? (
            <div className="thing-details-make-model small-text">
              {header_value?.thing_details?.map((details) => {
                return details.value !== "NA" ? (
                  <AntTooltip title={details.value}>
                    <div className="hellip">
                      {details.name + (details.value ? details.value : "-")}
                    </div>
                  </AntTooltip>
                ) : (
                  <div className="hellip">
                    {details.name + (details.value ? details.value : "-")}
                  </div>
                );
              })}
            </div>
          ) : (
            ""
          )}
          {this.getLocationDiv(header_value)}
          {header_value?.cat_id === 18 &&
          MaintenanceText(header_value?.maintenance)?.length ? (
            <div className="maintenance-fault small-text">
              {MaintenanceText(header_value?.maintenance)}
            </div>
          ) : (
            ""
          )}
          {header_value?.latestFaults && (
            <div className="show-faults">
              {showActiveFault(header_value?.latestFaults)}
            </div>
          )}
        </div>
      </div>
    );
  }
}
