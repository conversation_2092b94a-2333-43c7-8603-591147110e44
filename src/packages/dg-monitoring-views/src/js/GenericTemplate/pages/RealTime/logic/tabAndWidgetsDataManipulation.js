import RealtimePanels from "../../../component/RealtimePanels";
import _find from "lodash/find";
import {
  ParamName,
  ParamUnit,
} from "../../../../data_handling/ParamNameUnitFind";
import AntTabs from "@datoms/react-components/src/components/AntTabs";
import { customerSpecificConfig } from "../../../utility/customerPecificConfigs";
import OjusRealTime from "../../../component/RealtimePanels/components/OjusRealTime";
import { relatedCustomerIds } from "../../../../../../../../configs/customer-specific-config";
import { solarPumpRealTimeParamsConfig } from "../../../configs/SolarPumpConfig";

export function getFinalRealtimeParams(findThingCat, vendor_id) {
  const findCustomerSpecificConfig = _find(customerSpecificConfig(), {
    id: vendor_id,
  });

  const finalRealTimeParams =
  findCustomerSpecificConfig?.id === vendor_id &&
  findCustomerSpecificConfig?.views?.[findThingCat?.id]?.real_time
  ? findCustomerSpecificConfig?.views?.[findThingCat?.id].real_time
  : findThingCat?.pages?.real_time;
  return finalRealTimeParams;
}

export function realTimeParamData() {
  const { selectedTab, totalData, thingId, modifiedResponse } = this.state;
  const selectedThingLatestParam = this.latestDataFunc();
  const tableData = {
    column: [
      { title: "Parameters", dataIndex: "parameters" },
      { title: "Value", dataIndex: "value" },
    ],
    data_source: [],
  };

  const realtimeObj = {
    main: {},
    phase: {},
    table: tableData,
  };

  const findThings = _find(totalData.things, { id: parseInt(thingId) });
  const findThingCat = totalData.things_categories?.find(
    (cat) => cat.id === findThings?.category,
  );
  const finalRealTimeParams = getFinalRealtimeParams(
    findThingCat,
    this.props.parent_vendor_id,
  );

  if(findThings?.thing_details?.dg_type === "single" && finalRealTimeParams?.parameterDetails?.length){
    finalRealTimeParams.parameterDetails = finalRealTimeParams?.parameterDetails.filter(param => !param.phase_values);
  }

  if(findThings?.thing_details?.dg_type === "three" && finalRealTimeParams?.parameterDetails?.length){
    finalRealTimeParams.parameterDetails = finalRealTimeParams?.parameterDetails.filter(param => param?.phase !== "single");
  }

  if (!finalRealTimeParams?.parameterDetails?.length) return realtimeObj;

  finalRealTimeParams.parameterDetails.forEach((realtimeParams) => {
    if (realtimeParams.tab !== selectedTab) return;

    const thingParam = _find(findThings.parameters, function (o) {
      return realtimeParams.key === "fuel"
        ? findThings.category === 71
          ? ["fuel", "fuel_litre"].includes(o.key)
          : ["fuel", "fuel_raw"].includes(o.key)
        : o.key === realtimeParams.key;
    });
    if (!thingParam) return;
    let realtimeValue = "";
    if (!realtimeObj.main[realtimeParams.key]) {
      realtimeObj.main[realtimeParams.key] = {};
    }

    const isRnhr = realtimeParams.key === "calculated_runhour" && selectedThingLatestParam?.data?.["rnhr"];
    if (realtimeParams.key === "fuel") {
      const fuelRawValue = parseFloat(
        selectedThingLatestParam.data["fuel_raw"],
      );
      const fuelLitreValue = parseFloat(
        selectedThingLatestParam.data["fuel_litre"],
      );
      const fuelCapacity = parseFloat(findThings.thing_details?.capacity);

      if (findThings.category === 71) {
        realtimeValue = fuelLitreValue
          ? (fuelLitreValue * 100) / fuelCapacity
          : parseFloat(selectedThingLatestParam.data[realtimeParams.key]);
      } else {
        realtimeValue = fuelRawValue
          ? fuelRawValue
          : parseFloat(selectedThingLatestParam.data[realtimeParams.key]);
      }

      realtimeObj.main[realtimeParams.key]["additional_params"] = {
        capacity: fuelCapacity,
        lastFuelFilledValue: !isNaN(
          findThings.latest_events?.tags?.["Fuel Filled"]?.param_value,
        )
          ? parseFloat(
              findThings.latest_events?.tags?.["Fuel Filled"]?.param_value,
            )?.toFixed(2) + " L"
          : "NA",
        lastFuelFilledTime:
          findThings.latest_events?.tags?.["Fuel Filled"]?.generation_time ||
          "NA",
      };
    } else if (selectedThingLatestParam?.data?.[realtimeParams.key] || isRnhr) {
      if (realtimeParams.key === "calculated_runhour") {
        const runHour = parseFloat(selectedThingLatestParam.data["rnhr"]);
        realtimeValue =
          runHour > 0
            ? runHour * 3600
            : _find(findThings.parameters, { key: "calculated_runhour" })
                ?.aggregated_value?.lifetime?.sum;
      } else {
        realtimeValue = parseFloat(
          selectedThingLatestParam.data[realtimeParams.key],
        ).toFixed(2);
      }
    }
    realtimeObj.phase[realtimeParams.key] = [];
    realtimeObj.main[realtimeParams.key] = {
      ...realtimeObj.main[realtimeParams.key],
      value: realtimeValue,
      unit: realtimeParams?.unit ? realtimeParams.unit : ParamUnit(
        _find(modifiedResponse.param_data, { key: realtimeParams.key })?.unit,
      ),
      name:
        realtimeParams.custom_name ??
        ParamName(realtimeParams.key, modifiedResponse.param_data),
      key: realtimeParams.key,
      type: realtimeParams.type,
      format: realtimeParams.format,
      visualization: realtimeParams.visualization,
      time_in: realtimeParams.time_in,
      conversion_factor: realtimeParams?.conversion_factor || 1,
    };
    if (
      realtimeParams.visualization === "meter" ||
      realtimeParams.visualization === "meter2"
    ) {
      const lowKVACooperDG = this.props?.parent_vendor_id === 1280 && parseInt(findThings?.thing_details?.kva) < 50 && realtimeParams?.key === "rpm";
      realtimeObj.main[realtimeParams.key].limit = lowKVACooperDG ? [] : realtimeParams.ranges;
      realtimeObj.main[realtimeParams.key].min = realtimeParams.min_value;
      realtimeObj.main[realtimeParams.key].max = realtimeParams.max_value;
      realtimeObj.main[realtimeParams.key].graph_size = realtimeParams.type;

      // Flow meter max flow rate condition
      if (findThingCat.id === 86 && realtimeParams.key === "flow") {
        this.processMaxValue({
          findThings,
          property: "max_flow_rate",
          realtimeObj,
          limitPercentage: 10,
          param: "flow",
        });
      }

      if(findThingCat.id===18 && realtimeParams.key === "rpm" && findThings?.thing_details?.no_of_poles==="2"){
        realtimeObj["main"][realtimeParams.key].max = 3250;
        realtimeObj["main"][realtimeParams.key].limit = [
          {
            color: "#ff0000",
            ranges: [3100, 3250],
          },
        ]
      }

      // Solar panel condition
      if (findThingCat.id === 91) {
        if (realtimeParams.key === "watt") {
          this.processMaxValue({
            findThings,
            property: "system_capacity",
            property_unit: "capacity_unit",
            realtimeObj,
            limitPercentage: 15,
            param: "watt",
          });
        }
        if (realtimeParams.key === "volt") {
          this.processMaxValue({
            findThings,
            property: "system_total_voltage",
            realtimeObj,
            limitPercentage: 15,
            param: "volt",
          });
        }
      }

      // Current meter condition for specific categories
      if (
        [78, 79, 101].includes(findThingCat.id) &&
        realtimeParams.key === "mcurr"
      ) {
        this.processMaxValue({
          findThings,
          property: "max_current",
          realtimeObj,
          limitPercentage: 15,
          param: "mcurr",
        });
      }
    }

    if(findThingCat.id === 103 && Object.keys(solarPumpRealTimeParamsConfig).includes(realtimeParams.key)){
      const maxValueObj = {
        findThings,
        property: solarPumpRealTimeParamsConfig[realtimeParams.key].relatedProperty,
        realtimeObj,
        limitPercentage: solarPumpRealTimeParamsConfig[realtimeParams.key].limitPercentage,
        maxValuePercentage: solarPumpRealTimeParamsConfig[realtimeParams.key].maxValuePercentage,
        propertyPresent: true,
        param: realtimeParams.key,
      }
      this.processMaxValue(maxValueObj);
      
      if(solarPumpRealTimeParamsConfig[realtimeParams.key].isNegative){
        this.processMinValueFromMaxValue({
          propertyPresent: maxValueObj.propertyPresent,
          realtimeObj,
          param: realtimeParams.key,
        });
      }
    }

    tableData.data_source.push({
      parameters: ParamName(realtimeParams.key, modifiedResponse.param_data),
      value: selectedThingLatestParam?.data?.[realtimeParams.key]
        ? parseFloat(selectedThingLatestParam.data[realtimeParams.key]).toFixed(
            2,
          ) +
          " " +
          ParamUnit(
            _find(modifiedResponse.param_data, { key: realtimeParams.key })
              ?.unit,
          )
        : "NA",
    });

    if (realtimeParams.phase_values?.length) {
      realtimeParams.phase_values.forEach((phase_value) => {
        tableData.data_source.push({
          parameters: ParamName(phase_value, modifiedResponse.param_data),
          value: selectedThingLatestParam?.data?.[phase_value]
            ? parseFloat(selectedThingLatestParam.data[phase_value]).toFixed(
                2,
              ) +
              " " +
              ParamUnit(
                _find(modifiedResponse.param_data, { key: phase_value }).unit,
              )
            : "NA",
        });
      });

      realtimeObj.phase[realtimeParams.key] = this.getPhaseArrayMajor(
        ...realtimeParams.phase_values,
      );
    }

  });

  return realtimeObj;
}

export function getPhaseArrayMajor(phase_1, phase_2, phase_3) {
  const selectedThingLatestParam = this.latestDataFunc();
  const findThings = _find(this.state.totalData.things, {
    id: parseInt(this.state.thingId),
  });
  const phases = [];

  [phase_1, phase_2, phase_3].forEach((phase, index) => {
    const phaseName = ["(R)", "(Y)", "(B)"];
    const altPhaseName = ["(RY)", "(YB)", "(BR)"];
    if (_find(findThings.parameters, { key: phase })) {
      phases.push({
        name: phase.includes(altPhaseName[index])
          ? altPhaseName[index]
          : phaseName[index],
        value:
          parseFloat(selectedThingLatestParam.data[phase]) >= 0
            ? selectedThingLatestParam.data[phase]
            : "NA",
      });
    }
  });

  return phases;
}

export function getTabItem(findThingCat, on_off_moving_status, thingData) {
  const { vendor_id, user_preferences, parent_vendor_id, firstThingCat } =
    this.props;

  const findCustomerSpecificConfig = _find(customerSpecificConfig(), {
    id: vendor_id,
  });
  const { selectedTab } = this.state;
  const dataObj = this.realTimeParamData();

  const tabRender = (
    <div className="inner-tab-main-section">
      {relatedCustomerIds[1121].includes(parseInt(parent_vendor_id)) && findThingCat.id === 73 ? (
        <OjusRealTime
          timezone={user_preferences.timezone}
          data_obj={dataObj}
          on_off_moving_status={on_off_moving_status}
          vendor_id={vendor_id}
          parent_vendor_id={parent_vendor_id}
          customer_specific_config={findCustomerSpecificConfig}
          selectedTab={selectedTab}
          faultdata={this.faultyParamFunc()}
        />
      ) : (
        <RealtimePanels
          timezone={user_preferences.timezone}
          data_obj={dataObj}
          on_off_moving_status={on_off_moving_status}
          vendor_id={vendor_id}
          parent_vendor_id={parent_vendor_id}
          customer_specific_config={findCustomerSpecificConfig}
        />
      )}
    </div>
  );

  const finalRealTimeParams = getFinalRealtimeParams(
    findThingCat,
    this.props.parent_vendor_id,
  );
  const parameters = thingData.parameters;
  const reqParams = finalRealTimeParams.parameterDetails;

  let configParams = reqParams.filter((param) => {
    return _find(parameters, { key: param.key });
  });

  let tabItems = finalRealTimeParams?.tabs?.map((tabs) => ({
    label: tabs.label,
    key: tabs.key,
    children: tabRender,
    paramCount: 0,
  }));

  configParams && configParams.forEach((param) => {
    tabItems && tabItems.forEach((tab) => {
      if (tab.key === param.tab) {
        tab.paramCount = tab.paramCount + 1;
      }
    });
  });

  tabItems = tabItems?.filter((tab) => {
    return tab.paramCount > 0;
  });

  if (!finalRealTimeParams?.tabs?.length) {
    return <div className="tab-total">{tabRender}</div>;
  }

  return tabItems?.length ? (
    <AntTabs
      className="tab-total"
      onChange={(key) => this.onTabChange(key)}
      type="card"
      items={tabItems}
      value={selectedTab}
    />
  ) : (
    <div className="no-params">No parameter found! </div>
  );
}
