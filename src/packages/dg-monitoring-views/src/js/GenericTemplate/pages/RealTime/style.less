#generic_real_time {
	line-height: 1.5;
	position: relative;
	height: calc(100vh - 52px);
	background-color: #1c1c1e;

	.things-name {
		color: #fff;
	}

	.real-time-graph-container {
		padding: 20px;
		height: 100%;
		overflow: auto;

		.ant-tabs .ant-tabs-content-holder {
			height: calc(100vh - 200px);
			overflow-y: scroll;
		}
	}

	.ant-tabs-tab {
		background: #34383d 0% 0% no-repeat padding-box;
		border-radius: 10px 10px 0px 0px;
		border: none;
		color: #fff;
	}

	.blur-background {
		filter: blur(4px);
	}

	.ant-tabs-tab.ant-tabs-tab-active {
		// background: #FF8500;
		color: #fff;
	}

	.ant-tabs-tab.ant-tabs-tab-active {
		background: #ff8500 !important;

		.ant-tabs-tab-btn {
			color: #fff !important;
		}
	}

	.ant-tabs-nav::before {
		opacity: 0.1;
	}

	.ant-row {
		width: auto;
	}

	.graph-container .highcharts-background {
		fill: transparent;
	}

	.tab-total {
		text-align: center;
		.inner-tab-main-section {
			margin: 20px 0;

			.div-container {
				background: transparent linear-gradient(217deg, #2b2b2b 0%, #141414 100%) 0% 0% no-repeat padding-box;
				padding: 10px;
				border-radius: 24px;

				.div-inner {
					background: transparent linear-gradient(37deg, #2b2b2b 0%, #141414 100%) 0% 0% no-repeat padding-box;
					padding: 10px;
					border-radius: 24px;
					width: 100%;
					height: 100%;

					.total-text-container {
						text-align: center;
						margin-bottom: 15px;

						.value {
							color: #fff;
							font-size: 20px;
						}

						.param-name {
							color: #bdd0f1;
							font-size: 18px;
						}
					}
				}
			}

			.phase-value-container {
				.div-inner {
					background: transparent linear-gradient(45deg, #ffffff40 0%, #141414 100%) 0% 0% no-repeat padding-box;
					;
				}
			}

			.phase-value {
				text-align: center;

				.value {
					color: #fff;
					font-size: 18px;
				}

				.param-name {
					color: #acacac;
					font-size: 14px;
				}
			}
		}
	}

	.no-params{
		color: #fff;
		font-size: 22px;
		display: flex;
		justify-content: center;
		margin-top: 24px;
		font-weight: 500;
	}
}

.realtime-banner {
	height: calc(100vh - 80px) !important;
}

@media (max-width: 2000px) {
	#generic_real_time {
		.tab-total {
			height: calc(100vh - 155px);
			overflow-y: auto;
			// padding-bottom: 20px;

			//	padding: 10px 30px;
			.ant-tabs-content-holder {
				overflow-x: hidden;
			}

			.ant-tabs-content-holder::-webkit-scrollbar-track {
				background-color: transparent;
			}

			.ant-tabs-content-holder::-webkit-scrollbar-thumb {
				background-color: #ffffff20;
			}

			.inner-tab-main-section {
				.div-container {
					padding: 5px;

					.div-inner {
						.total-text-container {
							.value {
								font-size: 16px;
							}

							.param-name {
								font-size: 14px;
							}
						}
					}
				}

				.phase-value {
					.value {
						font-size: 13px;
					}

					.param-name {
						font-size: 11px;
					}
				}
			}
		}
	}
}

@media (max-width: 1024px) {
	#generic_real_time {
		height: calc(100% - 60px);
		overflow-y: auto;
		padding-bottom: 40px;

		.widget-list-switch {
			margin: 30px 0;
			width: 100%;
			display: flex;
			justify-content: center;

			.ant-radio-group {
				padding: 5px;
				background-color: #f1efef;
				border-radius: 10px;

				.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
					display: none;
				}

				.ant-radio-button-wrapper {
					border-radius: 10px;
					width: 150px;
					background-color: #f1efef;
					border: none;
					color: #808080;
					text-align: center;
				}

				.ant-radio-button-wrapper:not(:first-child)::before {
					display: none;
				}

				.ant-radio-button-wrapper-checked {
					background-color: #fff;
					color: #232323;
					box-shadow: 3px 5px 8px 0px #80808050;
				}
			}
		}

		.mobile-activity {
			height: calc(100% - 43px);
		}

		.common-header .inner-common-header .status-toggle {
			margin-right: 0px;
		}

		.ant-tabs-tab {
			border-radius: 7px;
		}

		.ant-tabs-tab.ant-tabs-tab-active {
			background: #fff 0% 0% no-repeat padding-box;
		}

		.tab-total {
			padding: 20px 10px;
		}

		.options-container {
			padding: 20px 20px 0 20px;
			text-align: center;
			background: #1c1c1e;
			position: sticky;
			top: 0px;
			z-index: 99;
			margin-bottom: 10px;
			display: flex;
			justify-content: center;

			.mobile-btn {
				display: flex;

				.option-btn {
					background: #34383d 0 0 no-repeat padding-box;
					border: none;
					border-radius: 2px !important;
					color: #fff;
					font-weight: 500;
				}

				.option-btn.active {
					border-radius: 7px !important;
					background-color: #fff;
					color: orange;
					font-weight: 600;
				}

				.option-divider {
					margin: 0px;
					width: 1px;
					background-color: #c4c2c280;
					height: 18px;
					margin-top: 8px;
				}
			}

			.mobile-btn:last-child {
				.option-divider {
					width: 0px;
				}
			}
		}

		.tab-total {
			.inner-tab-main-section {
				margin-top: 0px;
			}
		}
	}

	.realtime-banner {
		height: calc(100% - 85px) !important;
	}

	.activity-false-drawer-container {
		position: absolute;
		bottom: 180px;
		top: auto;
		padding: 0px;
		background-color: #34383d;
		right: 0px;

		.drawer-icon {
			display: flex;
			align-items: center;
			padding: 10px 15px;
			background-color: #ffffff;
			border-bottom-left-radius: 30px;
			border-top-left-radius: 30px;
			position: absolute;
			transition: all 0.5s linear;
			right: 0;
			top: 65px;
			transition: 2s;
			font-size: 18px;
			transition: 0.5s;

			.new-activities {
				width: 20px;
				height: 20px;
				background: #ff0000;
				text-align: center;
				border-radius: 50%;
				font-size: 11px;
				padding-top: 2px;
				margin-top: -7px;
				margin-left: -10px;
			}

			.anticon-warning {
				color: orange;
				font-size: 26px;
			}
		}

		.drawer-icon.expand {
			right: 300px;
			z-index: 9999;
			transition: 2s;
			border-bottom-left-radius: 0px;
			border-top-left-radius: 0px;
		}
	}

	.activity-false-drawer-container.opened {
		right: calc(100% - 374px);
	}

	.ojus-mobile-analog {
		background-color: #1c1c1e;
		top: -16px !important;
		height: 100vw;
		width: 97vh;
		transform: rotate(90deg);

		.ant-modal-content {
			background-color: #1c1c1e;
			height: 100vw;
			width: 100vh;

			.ant-modal-close {
				color: #fff;
			}


			.ant-modal-body {
				background-color: #1c1c1e;
				height: 100vw;
				width: 100vh;
				overflow: hidden;

				.ant-tabs-tab {
					background: #34383d 0% 0% no-repeat padding-box;
					border-radius: 10px 10px 0px 0px;
					border: none;
					color: #fff;
				}

				.ant-tabs-tab.ant-tabs-tab-active {
					// background: #FF8500;
					color: #fff;
				}

				.ant-tabs-tab.ant-tabs-tab-active {
					background: #ff8500 !important;

					.ant-tabs-tab-btn {
						color: #fff !important;
					}
				}

				.ant-tabs-nav::before {
					opacity: 0.1;
				}
			}
		}


	}
}

@media(max-width: 576px) {
	#generic_real_time {
		height: calc(100vh - 280px);

		.tab-total {
			height: auto;
		}

		.ant-tabs-nav-wrap {
			justify-content: center;
		}

		.ant-tabs-tab {
			padding: 10px;
			margin-bottom: 15px;
		}

		.ant-tabs-nav::before {
			display: none;
		}

		.real-time-graph-container {	
			.ant-tabs .ant-tabs-content-holder {
				height: 100%;
				overflow-y: hidden;
			}
		}
	}
}