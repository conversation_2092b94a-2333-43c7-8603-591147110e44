/*Libs*/
import React from "react";
import Loading from "@datoms/react-components/src/components/Loading";
import AntRow from "@datoms/react-components/src/components/AntRow";
import AntCol from "@datoms/react-components/src/components/AntCol";
import _find from "lodash/find";
import _intersection from "lodash/intersection";
import _orderBy from "lodash/orderBy";
import moment from "moment-timezone";
import queryString from "query-string";
import {
  serviceAlertFetch,
  maintenanceTextGen,
} from "../../../data_handling/applicationMaintenanceDue";
import CommonHeader from "../../component/CommonHeader";

/*Components*/
import DrawerForThingWithStatus from "../../component/DrawerForThing";
/*Styles*/
import "./style.less";
import headObj from "../../../configuration/realTimeConfig/HeaderObject";

/*Data AHndling*/
import { retriveEventsData, disconnectSocketConnection } from "@datoms/js-sdk";
import { fetchAddress, getAddressFromLat } from "../../utility";
import {
  isRealTime,
  isDetailedView,
} from "../../logic/RealTimeAndDetailedViewAvailability";
import { getBaseUrl } from "@datoms/js-utils/src/base-url-logic";
import { getStatusIcon } from "../../logic/getStatusIcon";
import {
  getThingsList,
  getOnlineOfflineArray,
  findThingCatIsDg,
  updateAddress,
  latestDataFunc,
  offlineTimeOutFunction,
  checkOfflineOnlineStatus,
  realTimeDataFunc,
  realTimeDrawerFunc,
  updateEntityDetails,
} from "./logic/thingAndSocketData";
import { headerdataFunction } from "./logic/headerFunction";
import {
  realTimeParamData,
  getPhaseArrayMajor,
  getTabItem,
} from "./logic/tabAndWidgetsDataManipulation";
import ActivityFaultSection from "../../../components/RealTime/components/ActivityFaultSection";
import _filter from "lodash/filter";
import {
  eventChange,
  activityFaultDrawer,
  closeActivityFaultDrawer,
  getActivityData,
  faultSwitchChange,
  faultyParamFunc,
  activityRender,
} from "./logic/activityFaultSectionData";
import AntModal from "@datoms/react-components/src/components/AntModal";
import { relatedCustomerIds } from "../../../../../../../configs/customer-specific-config";

export default class GenericRealTime extends React.Component {
  constructor(props) {
    super(props);
    this.parsed = queryString.parse(props.location.search);
    this.state = {
      screenWidth: window.innerWidth,
      headObj: headObj,
      drawerVisible: false,
      loading: true,
      get_render_type: "widget",
      activityFaultVisible: false,
      activitySelectedTab: 2,
      openOjusMobileModal: true,
    };
    this.listApiData = {
      client_id: props.client_id,
      application_id: props.application_id,
    };
    this.changeView = this.changeView.bind(this);
    this.switchClose = this.switchClose.bind(this);
    this.flagForTimeout = [];
    this.timer = null;
    this.bucketTime = null;
    this.bucket = {
      raw_data: {},
      event_data: [],
    };
    this.isPanelLocation = props.location.search.includes("panel");
    this.isFilter = props.location.search.split("filter=")?.[1]?.split("&")[0];
    this.getThingsList = getThingsList.bind(this);
    this.getOnlineOfflineArray = getOnlineOfflineArray.bind(this);
    this.updateEntityDetails = updateEntityDetails.bind(this);
    this.findThingCatIsDg = findThingCatIsDg.bind(this);
    this.updateAddress = updateAddress.bind(this);
    this.latestDataFunc = latestDataFunc.bind(this);
    this.offlineTimeOutFunction = offlineTimeOutFunction.bind(this);
    this.checkOfflineOnlineStatus = checkOfflineOnlineStatus.bind(this);
    this.realTimeDataFunc = realTimeDataFunc.bind(this);
    this.realTimeDrawerFunc = realTimeDrawerFunc.bind(this);
    this.headerdataFunction = headerdataFunction.bind(this);
    this.realTimeParamData = realTimeParamData.bind(this);
    this.getPhaseArrayMajor = getPhaseArrayMajor.bind(this);
    this.getTabItem = getTabItem.bind(this);
    this.eventChange = eventChange.bind(this);
    this.activityFaultDrawer = activityFaultDrawer.bind(this);
    this.closeActivityFaultDrawer = closeActivityFaultDrawer.bind(this);
    this.getActivityData = getActivityData.bind(this);
    this.faultSwitchChange = faultSwitchChange.bind(this);
    this.faultyParamFunc = faultyParamFunc.bind(this);
    this.activityRender = activityRender.bind(this);
  }

  async getServiceAlert() {
    let data = await serviceAlertFetch(this.props.client_id);
    this.setState({
      service_alert_fetch: data,
    });
  }

  getMaintenanceText() {
    let text = maintenanceTextGen(
      this.state.service_alert_fetch,
      this.state.thingId,
    );
    return text;
  }

  async getEvents() {
    const { totalData, thingId } = this.state;
    const { client_id, application_id } = this.props;
    let findThing = _find(totalData.things, { id: parseInt(thingId) });
    let data = {
      client_id: client_id,
      application_id: application_id,
    };
    let url_string = encodeURI(
      "?get_details=true&generated_after=" +
        moment().subtract(30, "days").startOf("day").unix() +
        "&generated_before=" +
        moment().endOf("day").unix() +
        "&entity_type=thing&entity_id=" +
        thingId,
    );
    let event_response = await retriveEventsData(data, url_string);
    let totalVaiolations = [];
    if (event_response?.response?.status === "success") {
      if (
        event_response.response.events &&
        event_response.response.events.length
      ) {
        event_response.response.events.map((events) => {
          if (events.type === 1) {
            totalVaiolations.push({ name: events.message });
          }
        });
      }
    }
    if (event_response?.response?.status === "success") {
      this.setState({
        events: event_response.response.events,
        event_types: [
          { id: 1, name: "Violations" },
          { id: 2, name: "Activity" },
        ],
        totalVaiolations: totalVaiolations,
        loading: false,
      });
    } else {
      this.setState({
        loading: false,
      });
    }
  }

  findSelectedTab(thingId, totalData) {
    const findThing = _find(totalData.things, {
      id: parseInt(thingId),
    });
    const findThingCat = _find(totalData?.things_categories, {
      id: findThing?.category,
    });
    const selectedTab = findThingCat?.pages?.real_time?.tabs?.length
    ? findThingCat.pages.real_time.tabs[0].key
    : undefined;
    return selectedTab;
  }

  processMaxValue(obj) {
    if (obj.findThings.thing_details[obj.property]) {
      let maxValue = !isNaN(
        parseFloat(obj.findThings.thing_details[obj.property]),
      )
        ? parseFloat(obj.findThings.thing_details[obj.property])
        : undefined;
      if (
        obj.property === "system_capacity" &&
        obj.findThings.thing_details[obj.property_unit] === "W"
      ) {
        maxValue = maxValue / 1000; // This will execute for solar system and convert Watt to kW
      }
      if (maxValue) {
        let maxLimit = maxValue - (maxValue * obj.limitPercentage) / 100;
        if(obj.maxValuePercentage){
          maxValue = maxValue + (maxValue * obj.maxValuePercentage) / 100;
          maxValue = parseFloat(maxValue.toFixed(2));
          maxLimit = parseFloat(maxLimit.toFixed(2));
        }
        obj.realtimeObj["main"][obj.param].max = maxValue;
        obj.realtimeObj["main"][obj.param].limit = [
          {
            color: "#ff0000",
            ranges: [maxLimit, maxValue],
          },
        ];
      }
    }
    else if(obj.propertyPresent){
      obj.propertyPresent = false;
    }
  }

  processMinValueFromMaxValue(obj){
    if(obj.propertyPresent){
      let maxValue = obj.realtimeObj["main"][obj.param].max;
      obj.realtimeObj["main"][obj.param].min = -maxValue;
      const negativeLimits = []
      const existingLimits = obj.realtimeObj["main"][obj.param].limit;
      existingLimits.forEach(limit => {
        negativeLimits.push({
          color: limit.color,
          ranges: [-limit.ranges[1], -limit.ranges[0]],
        });
      });
      obj.realtimeObj["main"][obj.param].limit = existingLimits.concat(negativeLimits);
    }
  }

  windowResize() {
    this.setState({ screenWidth: window.innerWidth });
  }

  async componentDidMount() {
    window.addEventListener("resize", this.windowResize.bind(this));
    await this.getThingsList(this.listApiData);
    await Promise.all([
      this.getEvents(this.listApiData),
      this.getServiceAlert(),
      this.updateAddress(),
    ]);
  }

  componentWillUnmount() {
    disconnectSocketConnection(this.socket);
    clearInterval(this.bucketTime);
  }

  onFilterChange(e, totalPanelData) {
    this.setState(
      {
        selectedOption: e,
      },
      () => this.filterSelection(totalPanelData),
    );
  }
  thingsDrawerClick(e) {
    const { totalData } = this.state;
    this.setState(
      {
        drawerVisible: false,
        thingId: e,
        loading: true,
        selectedTab: this.findSelectedTab(e, totalData),
      },
      async () => {
        if (!this.props.dg_in_iot_mode) {
          this.props.history.push(
            getBaseUrl(this.props, "real-time", true) +
              "?from=" +
              (this.isPanelLocation ? "panel" : "map") +
              (this.isFilter && this.isFilter.length
                ? "?filter=" + this.isFilter
                : "") +
              "&thing_id=" +
              this.state.thingId,
          );
        }
        await this.getThingsList(this.listApiData);
        await Promise.all([
          this.getEvents(this.listApiData),
          this.getServiceAlert(),
          this.updateAddress(),
        ]);
      },
    );
  }
  showDrawer() {
    this.setState({
      drawerVisible: true,
    });
  }
  closeDrawer() {
    this.setState({
      drawerVisible: false,
    });
  }

  changeView() {
    this.props.history.push(
      getBaseUrl(this.props, "detailed-view", true) +
        "?from=" +
        (this.isPanelLocation ? "panel-view" : "map") +
        (this.isFilter && this.isFilter.length
          ? "?filter=" + this.isFilter
          : "") +
        "&thing_id=" +
        this.state.thingId,
    );
  }

  switchClose() {
    let prevPage = this.isPanelLocation ? "panel-view?view=panel" : "map-view";
    this.props.history.push(
      getBaseUrl(this.props, prevPage, true) +
        (this.isFilter && this.isFilter.length
          ? "?filter=" + this.isFilter
          : ""),
    );
  }

  getStatus(getCat) {
    let findLastData = _find(this.state.latestParameterData, {
      thing_id: parseInt(this.state.thingId),
    });
    return getStatusIcon(
      getCat.id,
      parseInt(findLastData?.data?.mc_st) === 1 ? "on" : "off",
    );
  }

  getStatus(getCat) {
    let findLastData = _find(this.state.latestParameterData, {
      thing_id: parseInt(this.state.thingId),
    });
    return getStatusIcon(
      getCat.id,
      parseInt(findLastData?.data?.mc_st) === 1 ? "on" : "off",
    );
  }

  onTabChange(key) {
    this.setState({
      selectedTab: key,
    });
  }

  onOjusAnalogModalClose(isOjusCompressorOnly) {
    this.setState(
      {
        openOjusMobileModal: false,
      },
      () => {
        isOjusCompressorOnly ? this.switchClose() : this.changeView();
      },
    );
  }

  render() {
    const {
      mobile_view_get,
      vendor_id,
      loading_logo,
      user_preferences,
      history,
      client_id,
      application_id,
    } = this.props;
    const {
      thingId,
      totalData,
      latestParameterData,
      modifiedResponse,
      events,
      event_types,
      activitySelectedTab,
    } = this.state;
    
    const isOjusCompressorOnly = relatedCustomerIds[1121].includes(parseInt(vendor_id)) && totalData?.things?.filter((thing) => thing.category === 73).length === totalData?.things?.length;
    let pageRender = "",
      getDrawer = "";

    let thingCatId;

    if (totalData?.things?.length) {
      const thingData = totalData.things.filter(
        (thing) => thing.id === parseInt(thingId),
      )[0];
      thingCatId = thingData?.category;
    }

    const isOjusAnalogMobile =
    relatedCustomerIds[1121].includes(parseInt(vendor_id)) && window.innerWidth < 576 && thingCatId === 73;

    if (this.state.loading) {
      pageRender = (
        <Loading
          show_logo={this.props.loading_logo}
          className="align-center-loading"
        />
      );
    } else if (
      !this.state.totalData.things ||
      (this.state.totalData.things && this.state.totalData.things.length === 0)
    ) {
      pageRender = <div className="no-data-text">No Asset Found</div>;
    } else {
      let findThingAvailibility = _find(this.state.totalData.things, {
        id: parseInt(this.state.thingId),
      });
      if (findThingAvailibility) {
        let findThingCat = this.state.totalData.things_categories?.find(
          (cat) => cat.id === findThingAvailibility.category,
        );
        let headObj = this.headerdataFunction();
        getDrawer = (
          <DrawerForThingWithStatus
            thingId={this.state.thingId}
            drawer_data={headObj}
            drawerVisible={this.state.drawerVisible}
            thingsDrawerClick={(id) => this.thingsDrawerClick(id)}
            closeDrawer={() => this.closeDrawer()}
          />
        );

        const thingData = _find(totalData.things, {
          id: parseInt(this.state.thingId),
        });
        const thingStatus = _find(this.state.latestParameterData, {
          thing_id: parseInt(this.state.thingId),
        });
        const thingDataCat = _find(totalData.things_categories, {
          id: thingData.category,
        });

        const switchProps = {
          getRemoteLockAccess: typeof this.props?.getRemoteLockAccess === "function" && this.props.getRemoteLockAccess(),
          getRemoteAccess: typeof this.props?.getRemoteAccess === "function" && this.props.getRemoteAccess(),
          socket: this.socket,
          is_lock_control_enabled: thingDataCat?.show_lock,
          is_control_enabled: thingDataCat?.show_switch,
          operation_mode: thingData?.thing_details?.operation_mode,
          command_status: thingData?.commands,
          dgStatus: thingStatus?.on_off_moving_status,
          dg_lock_status: thingStatus?.data?.dg_lock_status,
          on_off_moving_status: thingStatus?.on_off_moving_status,
          status_option_includes_stopped:
            thingDataCat.status_options?.includes("Stopped"),
        };

        pageRender = (
          <>
            {mobile_view_get ? (
              ""
            ) : (
              <CommonHeader
                showDrawer={() => this.showDrawer()}
                thingId={this.state.thingId}
                client_id={client_id}
                application_id={application_id}
                head_obj={headObj}
                vendor_id={vendor_id}
                location={
                  !findThingCat?.no_location
                    ? this.state.currentAddress
                    : undefined
                }
                view_switch_value={"real-time"}
                changeView={this.changeView}
                statusIcon={this.getStatus(findThingCat)}
                value={"real-time"}
                is_close={
                  this.state.totalData?.things?.length === 1 &&
                  _find(this.state.totalData?.things, function (o) {
                    return ![67, 74, 76, 77].includes(o.category);
                  })
                    ? false
                    : true
                }
                switchClose={this.switchClose}
                detailed_view={isDetailedView(
                  this.state.thingId,
                  this.state.totalData,
                )}
                analog_view={isRealTime(
                  this.state.thingId,
                  this.state.totalData,
                )}
                devices={thingData?.devices}
                assetStatus={thingData?.status}
                {...switchProps}
              />
            )}
            {window.innerWidth < 576 && this.state.activityFaultVisible ? (
              <div className="mobile-activity">
                <ActivityFaultSection
                  optionChange={(e) => this.eventChange(e)}
                  selectedTab={activitySelectedTab}
                  history={this.props.history}
                  activities={this.getActivityData()}
                  fault_data={this.faultyParamFunc()}
                  viewActiveFault={this.state.viewActiveFault}
                  faultSwitchChange={(e) => this.faultSwitchChange(e)}
                  isGeneric={true}
                />
              </div>
            ) : (
              <AntRow>
                <AntCol
                  className="real-time-graph-container"
                  xl={20}
                  xxl={20}
                  // style={{
                  //   width: isOjusAnalogMobile ? "100vh" : "",
                  // }}
                >
                  {this.getTabItem(
                    findThingCat,
                    thingStatus?.on_off_moving_status,
                    thingData
                  )}
                </AntCol>
                <AntCol xl={4} xxl={4}>
                  {window.innerWidth < 576 ? "" : this.activityRender()}
                </AntCol>
              </AntRow>
            )}
          </>
        );
      } else {
        pageRender = <div className="no-data-text">No Asset Found</div>;
      }
    }
    const styles = {
      height: "100vw",
      width: "100vh",
      transform: "rotate(90deg)",
      // left: "-59%",
      // marginTop: "225px",
      // zIndex: "10",
    };

    return (
      <div
        id="generic_real_time"
        className={this.props.bannerToBeShown ? "realtime-banner" : ""}
      >
        {getDrawer}
        {isOjusAnalogMobile ? (
          <AntModal
            open={this.state.openOjusMobileModal}
            className="ojus-mobile-analog"
            footer={null}
            onCancel={() => this.onOjusAnalogModalClose(isOjusCompressorOnly)}
          >
            {pageRender}
          </AntModal>
        ) : (
          pageRender
        )}
        {window.innerWidth < 576 ? this.activityRender() : ""}
      </div>
    );
  }
}
