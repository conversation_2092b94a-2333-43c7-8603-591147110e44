import React from 'react';
import AntTag from '@datoms/react-components/src/components/AntTag';

export function getTripsCatwiseColumns() {
	const { template_id, client_id, application_id } = this.props;
	const { categoryId, tagWiseData } = this.state;
	const tripDetailsPath = (trip_id) =>
		this.props.location.pathname +
		'/' +
		trip_id +
		'/details' +
		this.props.location.search;
	const tagsColumn = {
		title: 'Tags',
		pdf_title: 'Tags',
		dataIndex: 'tags',
		width: 200,
		// align: 'center',
		render: (_, record) =>
			Array.isArray(record.tag_ids) && record.tag_ids.length ? (
				<div className="tdmv-tags">
					{record.tag_ids.map((tag_id) =>
						!isNaN(parseInt(tag_id)) ? (
							<AntTag
								key={tag_id}
								color={tagWiseData?.[tag_id]?.tag_color}
							>
								{tagWiseData?.[tag_id]?.tag_name || tag_id}
							</AntTag>
						) : (
							''
						),
					)}
				</div>
			) : (
				'-'
			),
	}
	const fuelMobility = [
		{
			title: 'Sl No',
			pdf_title: 'Sl No',
			dataIndex: 'sl_no',
			width: 80,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'Asset',
			pdf_title: 'Asset',
			dataIndex: 'thing_name',
			width: 240,
			render: (val, record) => (
				<span
					className="thing-name-val go-to-dtl"
					onClick={() =>
						this.setState({
							selectedTripId: record.trip_id,
						})
					}
				>
					{val}
				</span>
			),
		},
		{
			title: 'Customer Name',
			pdf_title: 'Customer Name',
			dataIndex: 'customer_name',
			width: 240,
			render: (val, record) => (
				<span
				//className="thing-name-val go-to-dtl"
				>
					{val}
				</span>
			),
		},
		{
			title: 'Partner Name',
			pdf_title: 'Partner Name',
			dataIndex: 'partner_name',
			width: 240,
			render: (val, record) => (
				<span
				//className="thing-name-val go-to-dtl"
				>
					{val}
				</span>
			),
		},
		{
			title: 'Start Time',
			pdf_title: 'Start Time',
			dataIndex: 'on_time',
			sorter_key: 'tsk_actual_start_time',
			sorter: true,
			width: 160,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'End Time',
			pdf_title: 'End Time',
			dataIndex: 'off_time',
			sorter_key: 'tsk_actual_end_time',
			sorter: true,
			width: 160,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		tagsColumn,
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Distance</div>
					<div className="tdmv-col-lg">(Km)</div>
				</section>
			),
			pdf_title: 'Distance (Km)',
			dataIndex: 'distance',
			sorter_key: 'distance_travelled,sum',
			sorter: true,
			width: 180,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Runhour</div>
					<div className="tdmv-col-lg">(HH:MM:SS)</div>
				</section>
			),
			pdf_title: 'Runhour (HH:MM:SS)',
			width: 180,
			align: 'center',
			dataIndex: 'runhour',
			sorter_key: 'calculated_runhour,sum',
			sorter: true,
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Fuel Before Trip</div>
					<div className="tdmv-col-lg">(L)</div>
				</section>
			),
			pdf_title: 'Fuel Before Trip (L)',
			width: 200,
			align: 'center',
			dataIndex: 'start_fuel',
			sorter_key: 'fuel_litre,initial',
			sorter: true,
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Fuel Consumed</div>
					<div className="tdmv-col-lg">(L)</div>
				</section>
			),
			pdf_title: 'Fuel Consumed (L)',
			width: 180,
			align: 'center',
			dataIndex: 'fuel_cons',
			sorter_key: 'fuel_consumption,sum',
			sorter: true,
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Fuel After Trip</div>
					<div className="tdmv-col-lg">(L)</div>
				</section>
			),
			pdf_title: 'Fuel After Trip (L)',
			width: 200,
			align: 'center',
			dataIndex: 'end_fuel',
			sorter_key: 'fuel_litre,snapshot',
			sorter: true,
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Mileage</div>
					<div className="tdmv-col-lg">(Km/L)</div>
				</section>
			),
			pdf_title: 'Mileage (Km/L)',
			width: 190,
			align: 'center',
			dataIndex: 'mileage',
			sorter_key: 'distance_travelled_p_fuel_consumption,snapshot',
			sorter: true,
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Average Speed</div>
					<div className="tdmv-col-lg">(Km/Hr)</div>
				</section>
			),
			pdf_title: 'Average Speed (Km/Hr)',
			width: 190,
			align: 'center',
			dataIndex: 'speed',
			sorter_key: 'speed,avg',
			sorter: true,
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Top Speed</div>
					<div className="tdmv-col-lg">(Km/Hr)</div>
				</section>
			),
			pdf_title: 'Top Speed (Km/Hr)',
			width: 190,
			align: 'center',
			dataIndex: 'top_speed',
			sorter_key: 'speed,max',
			sorter: true,
		},
	];
	const evMobility = [
		{
			title: 'Sl No',
			pdf_title: 'Sl No',
			dataIndex: 'sl_no',
			width: 80,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'Asset',
			pdf_title: 'Asset',
			dataIndex: 'thing_name',
			width: 200,
			render: (val, record) => (
				<span
					className="thing-name-val go-to-dtl"
					onClick={() =>
						this.setState({
							selectedTripId: record.trip_id,
						})
					}
				>
					{val}
				</span>
			),
		},
		{
			title: 'Start Time',
			pdf_title: 'Start Time',
			dataIndex: 'on_time',
			sorter_key: 'tsk_actual_start_time',
			sorter: true,
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'End Time',
			pdf_title: 'End Time',
			dataIndex: 'off_time',
			sorter_key: 'tsk_actual_end_time',
			sorter: true,
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Distance</div>
					<div className="tdmv-col-lg">(Km)</div>
				</section>
			),
			pdf_title: 'Distance (Km)',
			dataIndex: 'distance',
			sorter_key: 'distance_travelled,sum',
			sorter: true,
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Runhour</div>
					<div className="tdmv-col-lg">(HH:MM:SS)</div>
				</section>
			),
			pdf_title: 'Runhour (HH:MM:SS)',
			dataIndex: 'runhour',
			sorter_key: 'calculated_runhour,sum',
			sorter: true,
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Top Speed</div>
					<div className="tdmv-col-lg">(Km/Hr)</div>
				</section>
			),
			pdf_title: 'Top Speed (Km/Hr)',
			align: 'center',
			dataIndex: 'top_speed',
			sorter_key: 'speed,max',
			sorter: true,
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Average Speed</div>
					<div className="tdmv-col-lg">(Km/Hr)</div>
				</section>
			),
			pdf_title: 'Average Speed (Km/Hr)',
			dataIndex: 'speed',
			sorter_key: 'speed,avg',
			sorter: true,
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Battery Consumed</div>
					<div className="tdmv-col-lg">(%)</div>
				</section>
			),
			pdf_title: 'Battery Consumed (%)',
			dataIndex: 'vbat_consumed',
			sorter_key: 'vbat_consumed,sum',
			sorter: true,
		},
	];
	const dgSetColumns = [
		{
			title: 'Sl No',
			pdf_title: 'Sl No',
			dataIndex: 'sl_no',
			width: 80,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'Asset',
			pdf_title: 'Asset',
			dataIndex: 'thing_name',
			width: 240,
			render: (val, record) => (
				<span
					className="thing-name-val go-to-dtl"
					onClick={() =>
						this.setState({
							selectedTripId: record.trip_id,
						})
					}
				>
					{val}
				</span>
			),
		},
		{
			title: 'Customer Name',
			pdf_title: 'Customer Name',
			dataIndex: 'customer_name',
			width: 240,
			render: (val, record) => (
				<span
				//className="thing-name-val go-to-dtl"
				>
					{val}
				</span>
			),
		},
		{
			title: 'Partner Name',
			pdf_title: 'Partner Name',
			dataIndex: 'partner_name',
			width: 240,
			render: (val, record) => (
				<span
				//className="thing-name-val go-to-dtl"
				>
					{val}
				</span>
			),
		},
		{
			title: 'Start Time',
			pdf_title: 'Start Time',
			dataIndex: 'on_time',
			sorter_key: 'tsk_actual_start_time',
			sorter: true,
			width: 160,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'End Time',
			pdf_title: 'End Time',
			dataIndex: 'off_time',
			sorter_key: 'tsk_actual_end_time',
			sorter: true,
			width: 160,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		tagsColumn,
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Runhour</div>
					<div className="tdmv-col-lg">(HH:MM:SS)</div>
				</section>
			),
			pdf_title: 'Runhour (HH:MM:SS)',
			dataIndex: 'runhour',
			sorter_key: 'calculated_runhour,sum',
			sorter: true,
			width: 180,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Start Fuel</div>
					<div className="tdmv-col-lg">(L)</div>
				</section>
			),
			pdf_title: 'Start Fuel (L)',
			dataIndex: 'start_fuel',
			sorter_key: 'fuel_litre,initial',
			sorter: true,
			width: 160,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Fuel Consumed</div>
					<div className="tdmv-col-lg">(L)</div>
				</section>
			),
			pdf_title: 'Fuel Consumed (L)',
			dataIndex: 'fuel_cons',
			sorter_key: 'fuel_consumption,sum',
			sorter: true,
			width: 190,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">End Fuel</div>
					<div className="tdmv-col-lg">(L)</div>
				</section>
			),
			pdf_title: 'End Fuel (L)',
			dataIndex: 'end_fuel',
			sorter_key: 'fuel_litre,snapshot',
			sorter: true,
			width: 160,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Fuel Filled</div>
					<div className="tdmv-col-lg">(L)</div>
				</section>
			),
			pdf_title: 'Fuel Filled (L)',
			dataIndex: 'fuel_filled',
			sorter_key: 'fuel_filled,sum',
			sorter: true,
			width: 160,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Fuel Drained</div>
					<div className="tdmv-col-lg">(L)</div>
				</section>
			),
			pdf_title: 'Fuel Drained (L)',
			dataIndex: 'fuel_theft',
			sorter_key: 'fuel_theft,sum',
			sorter: true,
			width: 160,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Fuel consumption per Hr</div>
					<div className="tdmv-col-lg">(L/Hr)</div>
				</section>
			),
			pdf_title: 'Fuel consumption per Hr (L/Hr)',
			dataIndex: 'fuel_cons_per_hr',
			sorter_key: 'fuel_consumption_p_calculated_runhour,snapshot',
			sorter: true,
			width: 200,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Energy Generated</div>
					<div className="tdmv-col-lg">(kWh)</div>
				</section>
			),
			pdf_title: 'Energy Generated (kWh)',
			dataIndex: 'energy_generated',
			sorter_key: 'calculated_energy,sum',
			sorter: true,
			width: 180,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">
						Fuel Consumed per unit kWh
					</div>
					<div className="tdmv-col-lg">(L/kWh)</div>
				</section>
			),
			pdf_title: 'Fuel Consumed per unit kWh (L/kWh)',
			dataIndex: 'fuel_cons_per_kwh',
			sorter_key: 'fuel_consumption_p_calculated_energy,snapshot',
			sorter: true,
			width: 200,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">
						Energy Generated per unit Litre fuel consumption
					</div>
					<div className="tdmv-col-lg">(kWh/L)</div>
				</section>
			),
			pdf_title:
				'Energy Generated per unit Litre fuel consumption (kWh/L)',
			dataIndex: 'enrg_per_litre',
			sorter_key: 'calculated_energy_p_fuel_consumption,snapshot',
			sorter: true,
			width: 200,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Load</div>
					<div className="tdmv-col-lg">(%)</div>
				</section>
			),
			pdf_title: 'Load %',
			dataIndex: 'load_percentage',
			sorter_key: 'load_percentage,avg',
			sorter: true,
			width: 100,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Peak Load</div>
					<div className="tdmv-col-lg">(%)</div>
				</section>
			),
			pdf_title: 'Peak Load %',
			dataIndex: 'peak_load',
			sorter_key: 'load_percentage,max',
			sorter: true,
			width: 100,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">
						Expected fuel consumption
					</div>
					<div className="tdmv-col-lg">(L)</div>
				</section>
			),
			pdf_title: 'Expected Fuel Consumption (L)',
			dataIndex: 'exp_fuel_consumption',
			// sorter_key: 'th_fuel_consumption,sum',
			sorter: false,
			width: 150,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">
						Expected fuel consumption per Hr
					</div>
					<div className="tdmv-col-lg">(L/Hr)</div>
				</section>
			),
			pdf_title: 'Expected fuel consumption per Hr (L/Hr)',
			dataIndex: 'exp_fuel_consumption_per_hour',
			// sorter_key: 'th_fuel_consumption_p_calculated_runhour,snapshot',
			sorter: false,
			width: 150,
			align: 'center',
		},
		{
			title: 'Make',
			pdf_title: 'Make',
			dataIndex: 'make',
			width: 150,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'Model',
			pdf_title: 'Model',
			dataIndex: 'model',
			width: 150,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'KVA',
			pdf_title: 'KVA',
			dataIndex: 'kva',
			width: 150,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'Fuel Tank Capacity (L)',
			pdf_title: 'Fuel Tank Capacity (L)',
			dataIndex: 'capacity',
			width: 170,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'Genset Type',
			pdf_title: 'Genset Type',
			dataIndex: 'genset_type',
			width: 150,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
	];
	const solarColumns = [
		{
			title: 'Sl No',
			pdf_title: 'Sl No',
			dataIndex: 'sl_no',
			width: 80,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'Asset',
			pdf_title: 'Asset',
			dataIndex: 'thing_name',
			width: 240,
			render: (val, record) => (
				<span
					className="thing-name-val go-to-dtl"
					onClick={() =>
						this.setState({
							selectedTripId: record.trip_id,
						})
					}
				>
					{val}
				</span>
			),
		},
		{
			title: 'Customer Name',
			pdf_title: 'Customer Name',
			dataIndex: 'customer_name',
			width: 240,
			render: (val, record) => (
				<span
					//className="thing-name-val go-to-dtl"
				>
					{val}
				</span>
			),
		},
		{
			title: 'Partner Name',
			pdf_title: 'Partner Name',
			dataIndex: 'partner_name',
			width: 240,
			render: (val, record) => (
				<span
					//className="thing-name-val go-to-dtl"
				>
					{val}
				</span>
			),
		},
		{
			title: 'Start Time',
			pdf_title: 'Start Time',
			dataIndex: 'on_time',
			sorter_key: 'tsk_actual_start_time',
			sorter: true,
			width: 160,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'End Time',
			pdf_title: 'End Time',
			dataIndex: 'off_time',
			sorter_key: 'tsk_actual_end_time',
			sorter: true,
			width: 160,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Generation Hours</div>
					<div className="tdmv-col-lg">(HH:MM:SS)</div>
				</section>
			),
			pdf_title: 'Generation Hours (HH:MM:SS)',
			dataIndex: 'runhour',
			sorter_key: 'calculated_runhour,sum',
			sorter: true,
			width: 180,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Solar Energy</div>
					<div className="tdmv-col-lg">(kWh)</div>
				</section>
			),
			pdf_title: 'Solar Energy  (kWh)',
			dataIndex: 'energy_generated',
			sorter_key: 'calculated_energy,sum',
			sorter: true,
			width: 180,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Average power</div>
					<div className="tdmv-col-lg">(kW)</div>
				</section>
			),
			pdf_title: 'Average power kW',
			dataIndex: 'watt',
			sorter_key: 'watt,avg',
			sorter: true,
			width: 100,
			align: 'center',
		},
	];
	const electricalMachinesColumns = [
		{
			title: 'Sl No',
			pdf_title: 'Sl No',
			dataIndex: 'sl_no',
			width: 80,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'Asset',
			pdf_title: 'Asset',
			dataIndex: 'thing_name',
			width: 240,
			render: (val, record) => (
				<span
					className="thing-name-val go-to-dtl"
					onClick={() =>
						this.setState({
							selectedTripId: record.trip_id,
						})
					}
				>
					{val}
				</span>
			),
		},
		{
			title: 'Customer Name',
			pdf_title: 'Customer Name',
			dataIndex: 'customer_name',
			width: 240,
			render: (val, record) => (
				<span
					//className="thing-name-val go-to-dtl"
				>
					{val}
				</span>
			),
		},
		{
			title: 'Partner Name',
			pdf_title: 'Partner Name',
			dataIndex: 'partner_name',
			width: 240,
			render: (val, record) => (
				<span
					//className="thing-name-val go-to-dtl"
				>
					{val}
				</span>
			),
		},
		{
			title: 'Start Time',
			pdf_title: 'Start Time',
			dataIndex: 'on_time',
			sorter_key: 'tsk_actual_start_time',
			sorter: true,
			width: 160,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'End Time',
			pdf_title: 'End Time',
			dataIndex: 'off_time',
			sorter_key: 'tsk_actual_end_time',
			sorter: true,
			width: 160,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Duration</div>
					<div className="tdmv-col-lg">(HH:MM:SS)</div>
				</section>
			),
			pdf_title: 'Duration (HH:MM:SS)',
			dataIndex: 'runhour',
			sorter_key: 'calculated_runhour,sum',
			sorter: true,
			width: 180,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Energy Consumed</div>
					<div className="tdmv-col-lg">(kWh)</div>
				</section>
			),
			pdf_title: 'Energy Consumed  (kWh)',
			dataIndex: 'mains_energy_generated',
			sorter_key: 'calculated_mains_energy,sum',
			sorter: true,
			width: 180,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Active power</div>
					<div className="tdmv-col-lg">(kW)</div>
				</section>
			),
			pdf_title: 'Active power',
			dataIndex: 'mt_power',
			sorter_key: 'mt_power,avg',
			sorter: true,
			width: 100,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Average Current</div>
					<div className="tdmv-col-lg">(A)</div>
				</section>
			),
			pdf_title: 'Average Current',
			dataIndex: 'mcurr',
			sorter_key: 'mcurr,avg',
			sorter: true,
			width: 100,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Avg L-N Voltage</div>
					<div className="tdmv-col-lg">(V)</div>
				</section>
			),
			pdf_title: 'Avg L-N Voltage',
			dataIndex: 'mvol',
			sorter_key: 'mvol,avg',
			sorter: true,
			width: 100,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Avg L-L Voltage</div>
					<div className="tdmv-col-lg">(V)</div>
				</section>
			),
			pdf_title: 'Avg L-L Voltage',
			dataIndex: 'mvolt_p',
			sorter_key: 'mvolt_p,avg',
			sorter: true,
			width: 100,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml"> Avg Power Factor</div>
					<div className="tdmv-col-lg"></div>
				</section>
			),
			pdf_title: ' Avg Power Factor',
			dataIndex: 'mt_pf',
			sorter_key: 'mt_pf,avg',
			sorter: true,
			width: 100,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Avg Frequency</div>
					<div className="tdmv-col-lg">(Hz)</div>
				</section>
			),
			pdf_title: 'Avg Frequency',
			dataIndex: 'mfreq',
			sorter_key: 'mfreq,avg',
			sorter: true,
			width: 100,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Reactive Energy</div>
					<div className="tdmv-col-lg">(kWh)</div>
				</section>
			),
			pdf_title: 'Reactive Energy',
			dataIndex: 'calculated_mr_energy',
			sorter_key: 'calculated_mr_energy,sum',
			sorter: true,
			width: 100,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml"> Apparent Energy</div>
					<div className="tdmv-col-lg">(kWh)</div>
				</section>
			),
			pdf_title: ' Apparent Energy',
			dataIndex: 'calculated_ma_energy',
			sorter_key: 'calculated_ma_energy,sum',
			sorter: true,
			width: 100,
			align: 'center',
		},
	];
	const coldStorageColumns = [
		{
			title: 'Sl No',
			pdf_title: 'Sl No',
			dataIndex: 'sl_no',
			width: 80,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'Asset',
			pdf_title: 'Asset',
			dataIndex: 'thing_name',
			width: 240,
			render: (val, record) => (
				<span
					className="thing-name-val go-to-dtl"
					onClick={() =>
						this.setState({
							selectedTripId: record.trip_id,
						})
					}
				>
					{val}
				</span>
			),
		},
		{
			title: 'Customer Name',
			pdf_title: 'Customer Name',
			dataIndex: 'customer_name',
			width: 240,
			render: (val, record) => (
				<span
					//className="thing-name-val go-to-dtl"
				>
					{val}
				</span>
			),
		},
		{
			title: 'Partner Name',
			pdf_title: 'Partner Name',
			dataIndex: 'partner_name',
			width: 240,
			render: (val, record) => (
				<span
					//className="thing-name-val go-to-dtl"
				>
					{val}
				</span>
			),
		},
		{
			title: 'Start Time',
			pdf_title: 'Start Time',
			dataIndex: 'on_time',
			sorter_key: 'tsk_actual_start_time',
			sorter: true,
			width: 160,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'End Time',
			pdf_title: 'End Time',
			dataIndex: 'off_time',
			sorter_key: 'tsk_actual_end_time',
			sorter: true,
			width: 160,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Door Opened Duration</div>
					<div className="tdmv-col-lg">(HH:MM:SS)</div>
				</section>
			),
			pdf_title: 'Door Opened Duration',
			dataIndex: 'runhour',
			sorter_key: 'calculated_runhour,sum',
			sorter: true,
			width: 180,
			align: 'center',
		},
	]
	const solarPumpColumns = [
		{
			title: 'Sl No',
			pdf_title: 'Sl No',
			dataIndex: 'sl_no',
			width: 80,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'Asset',
			pdf_title: 'Asset',
			dataIndex: 'thing_name',
			width: 240,
			render: (val, record) => (
				<span
					className="thing-name-val go-to-dtl"
					onClick={() =>
						this.setState({
							selectedTripId: record.trip_id,
						})
					}
				>
					{val}
				</span>
			),
		},
		{
			title: 'Customer Name',
			pdf_title: 'Customer Name',
			dataIndex: 'customer_name',
			width: 240,
			render: (val, record) => (
				<span
					//className="thing-name-val go-to-dtl"
				>
					{val}
				</span>
			),
		},
		{
			title: 'Partner Name',
			pdf_title: 'Partner Name',
			dataIndex: 'partner_name',
			width: 240,
			render: (val, record) => (
				<span
					//className="thing-name-val go-to-dtl"
				>
					{val}
				</span>
			),
		},
		{
			title: 'Start Time',
			pdf_title: 'Start Time',
			dataIndex: 'on_time',
			sorter_key: 'tsk_actual_start_time',
			sorter: true,
			width: 160,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'End Time',
			pdf_title: 'End Time',
			dataIndex: 'off_time',
			sorter_key: 'tsk_actual_end_time',
			sorter: true,
			width: 160,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Runhour</div>
					<div className="tdmv-col-lg">(HH:MM:SS)</div>
				</section>
			),
			pdf_title: 'Runhour (HH:MM:SS)',
			dataIndex: 'runhour',
			sorter_key: 'calculated_runhour,sum',
			sorter: true,
			width: 180,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Energy</div>
					<div className="tdmv-col-lg">(kWh)</div>
				</section>
			),
			pdf_title: 'Energy (kWh)',
			dataIndex: 'energy_generated',
			sorter_key: 'calculated_energy,sum',
			sorter: true,
			width: 180,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Motor Power</div>
					<div className="tdmv-col-lg">(kW)</div>
				</section>
			),
			pdf_title: 'Motor Power (kW)',
			dataIndex: 'mtr_pow',
			width: 180,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Motor Speed</div>
					<div className="tdmv-col-lg">(RPM)</div>
				</section>
			),
			pdf_title: 'Motor Speed (RPM)',
			dataIndex: 'rpm',
			width: 180,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Drive Output Frequency</div>
					<div className="tdmv-col-lg">(Hz)</div>
				</section>
			),
			pdf_title: 'Drive Output Frequency (Hz)',
			dataIndex: 'hz',
			width: 180,
			align: 'center',
		},
	]
	const gasGensetColumns = [
		{
			title: 'Sl No',
			pdf_title: 'Sl No',
			dataIndex: 'sl_no',
			width: 80,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'Asset',
			pdf_title: 'Asset',
			dataIndex: 'thing_name',
			width: 240,
			render: (val, record) => (
				<span
					className="thing-name-val go-to-dtl"
					onClick={() =>
						this.setState({
							selectedTripId: record.trip_id,
						})
					}
				>
					{val}
				</span>
			),
		},
		{
			title: 'Customer Name',
			pdf_title: 'Customer Name',
			dataIndex: 'customer_name',
			width: 240,
			render: (val, record) => (
				<span
					//className="thing-name-val go-to-dtl"
				>
					{val}
				</span>
			),
		},
		{
			title: 'Partner Name',
			pdf_title: 'Partner Name',
			dataIndex: 'partner_name',
			width: 240,
			render: (val, record) => (
				<span
					//className="thing-name-val go-to-dtl"
				>
					{val}
				</span>
			),
		},
		{
			title: 'Start Time',
			pdf_title: 'Start Time',
			dataIndex: 'on_time',
			sorter_key: 'tsk_actual_start_time',
			sorter: true,
			width: 160,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: 'End Time',
			pdf_title: 'End Time',
			dataIndex: 'off_time',
			sorter_key: 'tsk_actual_end_time',
			sorter: true,
			width: 160,
			align: 'center',
			render: (val) => <span className="tdmv-time-render">{val}</span>,
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Runhour</div>
					<div className="tdmv-col-lg">(HH:MM:SS)</div>
				</section>
			),
			pdf_title: 'Runhour (HH:MM:SS)',
			dataIndex: 'runhour',
			sorter_key: 'calculated_runhour,sum',
			sorter: true,
			width: 180,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Energy Generated</div>
					<div className="tdmv-col-lg">(kWh)</div>
				</section>
			),
			pdf_title: 'Energy Generated (kWh)',
			dataIndex: 'energy_generated',
			sorter_key: 'calculated_energy,sum',
			sorter: true,
			width: 180,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Load</div>
					<div className="tdmv-col-lg">(%)</div>
				</section>
			),
			pdf_title: 'Load %',
			dataIndex: 'load_percentage',
			sorter_key: 'load_percentage,avg',
			sorter: true,
			width: 100,
			align: 'center',
		},
		{
			title: (
				<section className="tdmv-col-hd">
					<div className="tdmv-col-nml">Average Power</div>
					<div className="tdmv-col-lg">(kW)</div>
				</section>
			),
			pdf_title: 'Average Power (kW)',
			dataIndex: 'watt',
			sorter_key: 'watt,avg',
			sorter: true,
			width: 100,
			align: 'center',
		},
	]
	const columnsMap = {
		18: dgSetColumns,
		45: coldStorageColumns,
		67: fuelMobility,
		74: fuelMobility,
		76: evMobility,
		88: fuelMobility,
		91: solarColumns,
		78: electricalMachinesColumns,
		96: gasGensetColumns,
		103: solarPumpColumns
	};
	const finalColumns = columnsMap[categoryId]?.filter((col) => {
		if (client_id !== 1 && col.dataIndex === 'partner_name') return false;
		if(application_id === 16 && col.dataIndex === 'customer_name') return false;
		if (this.isGmmco() && col.pdf_title.includes('HH:MM:SS')) {
			col.pdf_title = col.pdf_title.replace('HH:MM:SS', 'SMU');
		}
		if (categoryId === 18) {
			if (
				template_id === 24 &&
				![
					'sl_no',
					'thing_name',
					'on_time',
					'off_time',
					'runhour',
					'start_fuel',
					'end_fuel',
					'energy_generated',
					'load_percentage',
				].includes(col.dataIndex)
			) {
				return false;
			}
		}
		return true;
	});
	// if (categoryId === 67 && this.isTestDrive) {
	// 	finalColumns.push({
	// 		title: 'Tags',
	// 		pdf_title: 'Tags',
	// 		dataIndex: 'tags',
	// 		width: 160,
	// 		align: 'center',
	// 		render: (val, record) => (
	// 			<span
	// 				className={'tdmv-tags-render' + ' ' + record.order_purpose}
	// 			>
	// 				{val}
	// 			</span>
	// 		),
	// 	});
	// }
	// let val = ['Low Fuel', 'High Battery', 'In transit'];
	return finalColumns;
}

const basicMobilitySumm = [
	{ title: 'Total Distance', key: 'total_distance', unit: 'Km' },
	{ title: 'Total Runhour', key: 'total_runhour', unit: 'Hr' },
	{ title: 'Total Fuel Consumed', key: 'total_fuel_consumed', unit: ' L' },
	{ title: 'Average Speed', key: 'avarage_speed', unit: 'Km/Hr' },
	{ title: 'Top Speed', key: 'top_speed', unit: 'Km/Hr' },
	{ title: 'Total Trips', key: 'total_trips' },
	{ title: 'Avg. Trip Distance', key: 'avg_trip_distance', unit: 'Km' },
];

const dgSumm = [
	{ title: 'Total Runhour', key: 'total_runhour', unit: 'Hr' },
	{ title: 'Total Fuel Consumed', key: 'total_fuel_consumed', unit: 'L' },
	{ title: 'Fuel consumption per hr', key: 'fuel_cons_per_hr', unit: 'L/Hr' },
	{ title: 'Total Energy', key: 'total_energy', unit: 'Kwh' },
	{
		title: 'Fuel Consumed Per unit kWh',
		key: 'fuel_cons_per_kwh',
		unit: 'L/Kwh',
	},
	{
		title: 'Energy Generated per Unit Litre Fuel Consumed',
		key: 'energy_gen_per_litre',
		unit: 'Kwh/L',
	},
	{ title: 'Total Trips', key: 'total_trips' },
	//	{ title: 'Load %', key: 'load_percentage' },
];

const solarSum = [
	{ title: 'Total Generation Hours', key: 'total_runhour', unit: 'Hr' },
	{ title: 'Total Solar Energy', key: 'total_energy', unit: 'kWh' },
]

const electricalSum = [
	{ title: 'Total Duration', key: 'total_runhour', unit: 'Hr' },
	{ title: 'Total Energy Consumed', key: 'total_mains_energy', unit: 'kWh' },
]

const coldStorageSum = [
	{ title: 'Total Events', key: 'total_trips' },
	{ title: 'Total Door Opened Duration', key: 'total_runhour', unit: 'Hr' },
]

const solarPumpSum = [
	{ title: 'Total Events', key: 'total_trips' },
	{ title: 'Total Runhour', key: 'total_runhour', unit: 'Hr' },
]

const gasGensetSum = [
	{ title: 'Total Trips', key: 'total_trips' },
	{ title: 'Total Runhour', key: 'total_runhour', unit: 'Hr' },
	{ title: 'Total Energy', key: 'total_energy', unit: 'kWh' },
]

export const catWiseSummary = {
	18: dgSumm,
	45: coldStorageSum,
	67: basicMobilitySumm,
	74: basicMobilitySumm,
	76: basicMobilitySumm.filter((item) => item.key !== 'total_fuel_consumed'),
	88: basicMobilitySumm,
	91: solarSum,
	78: electricalSum,
	103: solarPumpSum,
	96: gasGensetSum,
};

export const catWiseParameters = {
	18: {
		parameters: [
			'fuel_litre',
			'fuel_filled',
			'fuel_theft',
			'th_fuel_consumption',
			'th_fuel_consumption_p_calculated_runhour',
			'calculated_runhour',
			'fuel_consumption',
			'calculated_energy',
			'load_percentage',
			'fuel_consumption_p_calculated_runhour',
			'fuel_consumption_p_calculated_energy',
			'calculated_energy_p_fuel_consumption',
		],
		// calculated_parameters: [
		// 	'start_fuel',
		// 	'end_fuel',
		// 	'fuel_cons_per_hr',
		// 	'fuel_cons_per_kwh',
		// 	'enrg_per_litre',
		// ],
		summary_parameters: [
			'total_trips',
			'total_runhour',
			'total_fuel_consumed',
			'total_energy',
			'fuel_cons_per_hr',
			'fuel_cons_per_kwh',
			'energy_gen_per_litre',
			//	'load_percentage',
		],
	},
	45: {
		parameters: [
			'calculated_runhour',
		],
		summary_parameters: [
			'total_trips',
			'total_runhour',
		],
	},
	67: {
		parameters: [
			'fuel_litre',
			'calculated_runhour',
			'fuel_consumption',
			'distance_travelled',
			'speed',
			'distance_travelled_p_fuel_consumption',
		],
		// calculated_parameters: [
		// 	'start_fuel',
		// 	'end_fuel',
		// 	'mileage',
		// ],
		summary_parameters: [
			'total_trips',
			'total_runhour',
			'total_distance',
			'total_fuel_consumed',
			'avarage_speed',
			'top_speed',
			'avg_trip_distance',
		],
	},
	76: {
		parameters: [
			'calculated_runhour',
			'vbat_consumed',
			'distance_travelled',
			'speed',
		],
		summary_parameters: [
			'total_trips',
			'total_runhour',
			'total_distance',
			'avarage_speed',
			'top_speed',
			'avg_trip_distance',
		],
	},
	91: {
		parameters: [
			'calculated_runhour',
			'calculated_energy',
			'watt',
		],
		summary_parameters: [
			'total_trips',
			'total_runhour',
			'total_energy',
		],
	},
	78: {
		parameters: [
			'calculated_runhour',
			'calculated_mains_energy',
			'mt_power',
			'mcurr',
			'mvol',
			'mvolt_p',
			'mt_pf',
			'mfreq',
			'calculated_mr_energy',
			'calculated_ma_energy',
		],
		summary_parameters: [
			'total_trips',
			'total_runhour',
			'total_mains_energy',
		],
	},
	103: {
		parameters: [
			'calculated_runhour',
			'calculated_energy',
			'mtr_pow',
			'rpm',
			'hz',
		],
		summary_parameters: [
			'total_trips',
			'total_runhour'
		],
	},
	96: {
		parameters: [
			'calculated_runhour',
			'calculated_energy',
			'load_percentage',
			'watt',
		],
		summary_parameters: [
			'total_trips',
			'total_runhour',
			'total_energy',
		],
	},
};
