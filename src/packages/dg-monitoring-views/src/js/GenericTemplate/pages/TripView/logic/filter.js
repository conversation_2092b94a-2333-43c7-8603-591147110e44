import _findIndex from "lodash/findIndex";
import moment from "moment-timezone";
import {
  retriveUsers,
  retriveThingsList,
  retriveVendorThingsList,
  retriveCustomerList,
} from "@datoms/js-sdk";
// import { getThingsAndParameterData } from '../../../../data_handling/thingsListManipulation';
// import { filterDginIot } from '../../../../data_handling/DGinIot';
import { datePickerConfig, typeWiseTimeOptions } from "../configs/time-configs";
import { CONSTANTS as ASSET_CONSTANTS } from "@datoms/webapp-component-thing-management/src/components/ThingList/logic.js";

export async function getSalesExecutiveList() {
  if (this.isTestDrive) {
    const { client_id } = this.props;
    let response = await retriveUsers(client_id, 17);
    if (response.status === "success") {
      if (response.user_details && response.user_details.length) {
        const salesExecutiveList = [];
        response.user_details.map((user) => {
          if (user.role_details?.[0]?.role_type === 7) {
            salesExecutiveList.push({
              value: user.contact_id,
              title:
                user.first_name + (user.last_name ? " " + user.last_name : ""),
            });
          }
        });
        this.setState({ salesExecutiveList });
      }
    }
  }
  return 1;
}
export async function fetchThingsList() {
  const { application_id } = this.props;
  let totalData;
  if ([12, 17].includes(application_id)) {
    totalData = await retriveVendorThingsList(
      {
        vendor_id: this.props.client_id,
        application_id: this.props.application_id,
      },
      "?lite=true&without_device=false&status=active",
    );
  } else {
    totalData = await retriveThingsList({
      client_id: this.props.client_id,
      application_id: this.props.application_id,
    });
  }
  return totalData;
}
export async function fetchCustomersList() {
  const { application_id } = this.props;
  let customersList = [];
  if ([12, 17].includes(application_id)) {
    let response = await retriveCustomerList(
      this.props.client_id,
      //	'?lite=true'
    );
    customersList = Array.isArray(response.customers) ? response.customers : [];
  }
  return customersList;
}
export async function getFilterOptions() {
  let [totalData, customersList] = await Promise.all([
    this.fetchThingsList(),
    this.fetchCustomersList(),
    this.getSalesExecutiveList(),
  ]);
  let thingsListArray = [];
  let modifiedResponse = {};
  if (totalData.status === "success" && Array.isArray(totalData.things)) {
    //	totalData = filterDginIot.bind(this)(totalData);
    thingsListArray = totalData.things;
    //	modifiedResponse = getThingsAndParameterData(totalData);
  }
  let categoryCustomerMap = {};
  let thingCategoryOptions = [];
  let catWiseThingsOptions = {};
  thingsListArray.map((thing) => {
    if (this.supportedThingCats.includes(thing.category)) {
      if (!catWiseThingsOptions[thing.category]) {
        let findCategory = totalData.things_categories?.length
          ? totalData.things_categories.find(
              (category) => category.id === thing.category,
            )
          : {};
        thingCategoryOptions.push({
          value: thing.category,
          title: findCategory?.name,
        });
        catWiseThingsOptions[thing.category] = [];
      }
      catWiseThingsOptions[thing.category].push({
        value: thing.id,
        title: thing.name,
        vendor_id: thing.vendor_id,
        client_id: thing.customer_id,
        current_assigned_customer: thing.current_assigned_customer,
      });
      // Category-Customers mapping
      if (!categoryCustomerMap[thing.category]) {
        categoryCustomerMap[thing.category] = [];
      }
      let finalId = thing.customer_id ? thing.customer_id : thing.vendor_id;
      if (!categoryCustomerMap[thing.category].includes(finalId)) {
        categoryCustomerMap[thing.category].push(finalId);
        if (thing.customer_id) {
          Object.keys(ASSET_CONSTANTS.LINKED_CLIENT_IDS).map((key) => {
            if (ASSET_CONSTANTS.LINKED_CLIENT_IDS[key].includes(finalId)) {
              categoryCustomerMap[thing.category].push(parseInt(key));
            }
          });
        }
      }
      if (!categoryCustomerMap[thing.category].includes(thing.vendor_id)) {
        categoryCustomerMap[thing.category].push(thing.vendor_id);
      }
      if (
        thing.current_assigned_customer &&
        !categoryCustomerMap[thing.category].includes(
          thing.current_assigned_customer,
        )
      ) {
        categoryCustomerMap[thing.category].push(
          thing.current_assigned_customer,
        );

        Object.keys(ASSET_CONSTANTS.LINKED_CLIENT_IDS).map((key) => {
          if (
            ASSET_CONSTANTS.LINKED_CLIENT_IDS[key].includes(
              thing.current_assigned_customer,
            )
          ) {
            categoryCustomerMap[thing.category].push(parseInt(key));
          }
        });
      }
    }
  });
  let vendorsList = [],
    clientList = [];
  if (customersList && customersList.length) {
    customersList.map((customers) => {
      if (customers.id === 1) return;
      if (customers.is_vendor) {
        vendorsList.push({
          value: customers.id,
          title: customers.name,
        });
      } else {
        clientList.push({
          value: customers.id,
          title: customers.name,
          vendor_id: customers.vendor_id,
        });
      }
    });
  }
  let categoryId =
    this.getUrlBreak("category") &&
    !isNaN(parseInt(this.getUrlBreak("category")))
      ? parseInt(this.getUrlBreak("category"))
      : thingCategoryOptions.length
        ? thingCategoryOptions[0].value
        : undefined;
  let vendorId =
    this.getUrlBreak("vendor_id") &&
    !isNaN(parseInt(this.getUrlBreak("vendor_id")))
      ? parseInt(this.getUrlBreak("vendor_id"))
      : undefined;
  let clientId =
    this.getUrlBreak("client_id") &&
    !isNaN(parseInt(this.getUrlBreak("client_id")))
      ? parseInt(this.getUrlBreak("client_id"))
      : undefined;
  let thingId =
    this.getUrlBreak("thing_id") &&
    !isNaN(parseInt(this.getUrlBreak("thing_id")))
      ? parseInt(this.getUrlBreak("thing_id"))
      : undefined;
  let dateRanges = getDatesRanges(this.getUrlBreak("date_range"));
  let make = this.getAdvFieldsValue("make", undefined);
  let model = this.getAdvFieldsValue("model", undefined);
  let kva = this.getAdvFieldsValue("kva", undefined);
  let fuel_tank_capacity = this.getAdvFieldsValue("fuel_tank_capacity", "=");
  let rated_power_factor = this.getAdvFieldsValue("rated_power_factor", "=");
  let fuel_filled = this.getAdvFieldsValue("fuel_filled", "=");
  let fuel_theft = this.getAdvFieldsValue("fuel_theft", "=");
  let dg_phase = this.getAdvFieldsValue("dg_phase", undefined);
  let genset_type = this.getAdvFieldsValue("genset_type", undefined);
  let lifetime_runhour = this.getAdvFieldsValue("lifetime_runhour", "=");
  let runhour = this.getAdvFieldsValue("trip_runhour", ">");
  let load_percentage = this.getAdvFieldsValue("load_percentage", "=");
  let peak_load = this.getAdvFieldsValue("peak_load", "=");
  let fuel_consumption = this.getAdvFieldsValue("fuel_consumption", "=");
  let energy_generated = this.getAdvFieldsValue("energy_generated", "=");
  let tags = this.getAdvFieldsValue("tags", "contains");
  let order_purpose =
    !this.getUrlBreak("order_purpose") ||
    this.getUrlBreak("order_purpose") === "undefined"
      ? undefined
      : this.getUrlBreak("order_purpose");
  let assigned_to =
    this.getUrlBreak("assigned_to") &&
    !isNaN(parseInt(this.getUrlBreak("assigned_to")))
      ? parseInt(this.getUrlBreak("assigned_to"))
      : undefined;
  console.log(
    "catWiseThingsOptions",
    this.getUrlBreak("category"),
    parseInt(this.getUrlBreak("category")),
    categoryId,
  );
  let message = this.validateFilters(categoryId);
  if (this.props.application_id === 17) {
    customersList.push({
      id: this.props.client_id,
      name: this.props.client_name,
    });
  }
  this.setState(
    {
      loading: false,
      message,
      from_time: dateRanges.from_time,
      upto_time: dateRanges.upto_time,
      categoryId,
      defaultCategoryId: categoryId,
      thingCategoryOptions,
      vendorId,
      clientId,
      thingId,
      make,
      model,
      kva,
      fuel_tank_capacity,
      rated_power_factor,
      fuel_filled,
      fuel_theft,
      dg_phase,
      genset_type,
      lifetime_runhour,
      runhour,
      load_percentage,
      peak_load,
      fuel_consumption,
      energy_generated,
      tags,
      order_purpose,
      assigned_to,
      catWiseThingsOptions,
      totalData: totalData,
      modifiedResponse: modifiedResponse,
      clientList,
      vendorsList,
      customersList,
      categoryCustomerMap,
    },
    () => {
      if (!this.state.message) {
        this.createTripsRenderData(false, true);
      }
    },
  );
}
export function getThingOption(
  categoryId = this.state.categoryId,
  vendorId = this.state.vendorId,
  clientId = this.state.clientId,
) {
  const { /*categoryId, clientId, vendorId,*/ catWiseThingsOptions } =
    this.state;
  const thingOptions = [];
  if (catWiseThingsOptions[categoryId]?.length) {
    catWiseThingsOptions[categoryId].map((thing) => {
      if (
        (!vendorId || vendorId === thing.vendor_id) &&
        (!clientId ||
          clientId === thing.client_id ||
          clientId === thing.current_assigned_customer ||
          ASSET_CONSTANTS.LINKED_CLIENT_IDS[clientId]?.includes(
            thing.client_id,
          ) ||
          ASSET_CONSTANTS.LINKED_CLIENT_IDS[clientId]?.includes(
            thing.current_assigned_customer,
          ))
      ) {
        thingOptions.push(thing);
      }
    });
  }
  return thingOptions;
}
export function getClientOption(
  categoryId = this.state.categoryId,
  vendorId = this.state.vendorId,
) {
  const { application_id } = this.props;
  const { categoryCustomerMap, clientList /* categoryId, vendorId*/ } =
    this.state;
  return ![12, 17].includes(application_id)
    ? []
    : categoryCustomerMap?.[categoryId]
      ? clientList.filter(
          (item) =>
            categoryCustomerMap[categoryId].includes(item.value) &&
            (!vendorId || vendorId === item.vendor_id),
        )
      : [];
}
export function getVendorOption(categoryId = this.state.categoryId) {
  const { application_id } = this.props;
  const { categoryCustomerMap, vendorsList /*categoryId*/ } = this.state;
  return ![12].includes(application_id)
    ? []
    : categoryCustomerMap?.[categoryId]
      ? vendorsList.filter((item) =>
          categoryCustomerMap[categoryId].includes(item.value),
        )
      : [];
}
export function getExtraFilterOptions(filter_key) {
  const { categoryId } = this.state;
  const makeOptions = [
    { value: "mahindra", title: "Mahindra" },
    { value: "cummins", title: "Cummins" },
    { value: "kohler_sdmo", title: "Kohler Sdmo" },
    { value: "kohler", title: "Kohler" },
    { value: "tata", title: "Tata" },
    { value: "ashok_leyland", title: "Ashok Leyland" },
    { value: "cat", title: "Cat" },
    { value: "koel", title: "Koel" },
    { value: "Mahindra", title: "Mahindra" },
    { value: "escort", title: "Escort" },
    { value: "volvo_eicher", title: "Volvo Eicher" },
    { value: "tmtl", title: "Tmtl" },
    { value: "baudouin", title: "Baudouin" },
    { value: "eicher", title: "Eicher" },
    { value: "volvo", title: "Volvo" },
    { value: "cooper", title: "Cooper" },
    { value: "john_deere", title: "John Deere" },
    { value: "sterling", title: "Sterling" },
    { value: "stamford", title: "Stamford" },
    { value: "yanmar", title: "Yanmar" },
    { value: "dpk", title: "Dpk" },
    { value: "perkins", title: "Perkins" },
  ];
  const modelOptions = [
    { value: "model_1", title: "Model 1" },
    { value: "model_2", title: "Model 2" },
    { value: "model_3", title: "Model 3" },
  ];
  const kvaOptions = [
    { value: "100", title: "KVA 1" },
    { value: "120", title: "KVA 2" },
    { value: "220", title: "KVA 3" },
  ];
  if (filter_key === "make") {
    return makeOptions;
  }
  if (filter_key === "model") {
    return categoryId === 67 ? modelOptions : [];
  }
  if (filter_key === "kva") {
    return categoryId === 18 ? kvaOptions : [];
  }
}
export function validateFilters(categoryId = this.state.categoryId) {
  let message = "";
  const { pageType } = this.state;
  if (!this.supportedThingCats.includes(categoryId)) {
    message = "Asset Type not supported!";
  }
  if (!["trips", "daily"].includes(pageType)) {
    message = "Page Type not supported!";
  }
  return message;
}
export function applyFilterSelect(value, key) {
  let categoryId, vendorId, clientId;
  if (Array.isArray(value)) {
    categoryId = value[0];
    vendorId = value[1];
    clientId = value[2];
  }
  if (key === "category") {
    let selectedValues = value.map((val, index) => {
      if ([21].includes(index)) {
        return "contains";
      }
      if (index > 7 && index !== 10 && index !== 11) {
        return "=";
      }
      if (![0, 4].includes(index)) {
        return "";
      }
      return val;
    });
    console.log("larkai_category", categoryId);
    return {
      selected_values: selectedValues,
      total_options: {
        category: undefined,
        vendor_id: this.getVendorOption(categoryId),
        client_id: this.getClientOption(categoryId, ""),
        thing_id: this.getThingOption(categoryId, "", ""),
        // client_id: this.getClientOption(categoryId, vendorId),
        // thing_id: this.getThingOption(categoryId, vendorId, clientId),
      },
      hide_field: {
        kva: categoryId !== 18,
        fuel_tank_capacity: categoryId !== 18,
        rated_power_factor: categoryId !== 18,
        fuel_filled: categoryId !== 18,
        fuel_theft: categoryId !== 18,
        dg_phase: categoryId !== 18,
        genset_type: categoryId !== 18,
        lifetime_runhour: categoryId !== 18,
        trip_runhour: categoryId !== 18 && categoryId !== 45,
        load_percentage: categoryId !== 18,
        peak_load: categoryId !== 18,
        fuel_consumption: categoryId !== 18,
        energy_generated: categoryId !== 18,
        order_purpose: categoryId !== 67,
        // assigned_to: categoryId !== 67,
      },
    };
  }
  if (key === "vendor_id") {
    let selectedValues = value.map((val, index) => {
      if ([2, 3].includes(index)) {
        return "";
      }
      return val;
    });
    console.log("GDAI_val: ", selectedValues);
    return {
      selected_values: selectedValues,
      total_options: {
        category: undefined,
        vendor_id: undefined,
        client_id: this.getClientOption(categoryId, vendorId),
        thing_id: this.getThingOption(categoryId, vendorId, ""),
      },
    };
  }
  if (key === "client_id") {
    let selectedValues = value.map((val, index) => {
      if ([3].includes(index)) {
        return "";
      }
      return val;
    });
    console.log("GDAI_val: ", selectedValues);
    return {
      selected_values: selectedValues,
      total_options: {
        thing_id: this.getThingOption(categoryId, vendorId, clientId),
      },
    };
  }
  if (key === "thing_id") {
    let selectedValues = value;
    return {
      selected_values: selectedValues,
      total_options: {},
    };
  }
  if (key === "date_range") {
    let selectedValues = value;
    return {
      selected_values: selectedValues,
      total_options: {},
    };
  }
  const independentKeywords = [
    "make",
    "model",
    "kva",
    "fuel_tank_capacity",
    "rated_power_factor",
    "fuel_filled",
    "fuel_theft",
    "dg_phase",
    "genset_type",
    "lifetime_runhour",
    "trip_runhour",
    "load_percentage",
    "peak_load",
    "fuel_consumption",
    "energy_generated",
    "tags",
    "order_purpose",
    "assigned_to",
  ];
  if (independentKeywords.includes(key)) {
    let selectedValues = value;
    return {
      selected_values: selectedValues,
      total_options: {},
    };
  }
}
export function onUrlChange() {
  const { defaultCategoryId } = this.state;
  let categoryId =
    this.getUrlBreak("category") &&
    !isNaN(parseInt(this.getUrlBreak("category")))
      ? parseInt(this.getUrlBreak("category"))
      : defaultCategoryId
        ? defaultCategoryId
        : undefined;
  let vendorId =
    this.getUrlBreak("vendor_id") &&
    !isNaN(parseInt(this.getUrlBreak("vendor_id")))
      ? parseInt(this.getUrlBreak("vendor_id"))
      : undefined;
  let clientId =
    this.getUrlBreak("client_id") &&
    !isNaN(parseInt(this.getUrlBreak("client_id")))
      ? parseInt(this.getUrlBreak("client_id"))
      : undefined;
  let thingId = this.getUrlBreak("thing_id");
  let dateRanges = getDatesRanges(this.getUrlBreak("date_range"));
  let make = this.getUrlBreak("make");
  let model = this.getUrlBreak("model");
  let kva = this.getUrlBreak("kva");
  let fuel_tank_capacity = this.getAdvFieldsValue("fuel_tank_capacity", "=");
  let rated_power_factor = this.getAdvFieldsValue("rated_power_factor", "=");
  let fuel_filled = this.getAdvFieldsValue("fuel_filled", "=");
  let fuel_theft = this.getAdvFieldsValue("fuel_theft", "=");
  let dg_phase = this.getUrlBreak("dg_phase");
  let genset_type = this.getUrlBreak("genset_type");
  let lifetime_runhour = this.getAdvFieldsValue("lifetime_runhour", "=");
  let runhour = this.getAdvFieldsValue("trip_runhour", ">");
  let load_percentage = this.getAdvFieldsValue("load_percentage", "=");
  let peak_load = this.getAdvFieldsValue("peak_load", "=");
  let fuel_consumption = this.getAdvFieldsValue("fuel_consumption", "=");
  let energy_generated = this.getAdvFieldsValue("energy_generated", "=");
  let tags = this.getAdvFieldsValue("tags", "contains");

  let order_purpose =
    !this.getUrlBreak("order_purpose") ||
    this.getUrlBreak("order_purpose") === "undefined"
      ? undefined
      : this.getUrlBreak("order_purpose");
  let assigned_to =
    this.getUrlBreak("assigned_to") &&
    !isNaN(parseInt(this.getUrlBreak("assigned_to")))
      ? parseInt(this.getUrlBreak("assigned_to"))
      : undefined;
  console.log("kva gdai", kva, runhour, this.getUrlBreak("trip_runhour"));
  const stateToUpdate = {
    from_time: dateRanges.from_time,
    upto_time: dateRanges.upto_time,
    categoryId,
    vendorId,
    clientId,
    thingId,
    make,
    model,
    kva,
    fuel_tank_capacity,
    rated_power_factor,
    fuel_filled,
    fuel_theft,
    dg_phase,
    genset_type,
    lifetime_runhour,
    runhour,
    load_percentage,
    peak_load,
    fuel_consumption,
    energy_generated,
    tags,
    order_purpose,
    assigned_to,
    bodyLoading: true,
    page: 1,
  };
  //	if (this.state.categoryId !== categoryId) {
  // stateToUpdate['bodyLoading'] = true;
  stateToUpdate["sort_key"] = undefined;
  stateToUpdate["sort_order"] = undefined;
  stateToUpdate["apiTripData"] = undefined;
  stateToUpdate["apiDailyData"] = undefined;
  //	}
  this.setState(stateToUpdate, () => this.createTripsRenderData(false, true));
}
function getDatesRanges(dateString) {
  let response = {
    from_time: moment().startOf("day").unix(),
    upto_time: moment().endOf("day").unix(),
  };
  let dateArray =
    dateString &&
    dateString.includes("-") &&
    dateString.split("-")?.length === 2
      ? dateString.split("-")
      : ["", ""];
  if (!isNaN(parseInt(dateArray[0])) && !isNaN(parseInt(dateArray[1]))) {
    response.from_time = parseInt(dateArray[0]);
    response.upto_time = moment
      .unix(parseInt(dateArray[1]))
      .endOf("day")
      .unix();
  }
  return response;
}
export function getUrlBreak(value) {
  let geturlData;
  let geturlDataIndex = _findIndex(
    this.props.history?.location?.search?.split(","),
    function (o) {
      return o.includes(value);
    },
  );
  if (geturlDataIndex > -1) {
    geturlData = this.props.history?.location?.search
      ?.split(",")
      [geturlDataIndex].split(":")[1];
  }
  return geturlData === "undefined" ? "" : geturlData;
}
export function getAdvFieldsValue(key, defaultValue) {
  return !this.getUrlBreak(key) || this.getUrlBreak(key) === "undefined"
    ? defaultValue
    : decodeURIComponent(this.getUrlBreak(key));
}
export function getSelectedThings(allthings = false) {
  let selectedThings = [];
  const { categoryId, thingId, catWiseThingsOptions } = this.state;
  if (thingId && !allthings) {
    selectedThings.push(parseInt(thingId));
  } else {
    if (
      Array.isArray(catWiseThingsOptions[categoryId]) &&
      catWiseThingsOptions[categoryId].length
    ) {
      catWiseThingsOptions[categoryId].map((thing) =>
        selectedThings.push(parseInt(thing.value)),
      );
    }
  }
  return selectedThings;
}

export function createFilterData() {
  const {
    pageType,
    categoryId,
    vendorId,
    clientId,
    thingId,
    make,
    model,
    kva,
    fuel_tank_capacity,
    rated_power_factor,
    fuel_filled,
    fuel_theft,
    dg_phase,
    genset_type,
    lifetime_runhour,
    runhour,
    load_percentage,
    peak_load,
    fuel_consumption,
    energy_generated,
    thingCategoryOptions,
    from_time,
    upto_time,
    // salesExecutiveList,
  } = this.state;
  const isDgCategory = [18].includes(categoryId);
  const isColdStorage = categoryId === 45;
  // const isMobilityCategory = [67, 76].includes(categoryId);
  const isMobileScreen = window.innerWidth <= 900;
  datePickerConfig.disabledDate = this.disabledDate;
  const filterData = [
    {
      optionData: thingCategoryOptions,
      selectValue: categoryId,
      showSearch: true,
      sorted: false,
      placeholder: "Select Asset Category",
      label: "Asset Type",
      no_outside_label: true,
      url_name: "category",
      is_outside_filter_drawer: !isMobileScreen,
      is_inside_filter_drawer: isMobileScreen,
    },
    {
      optionData: this.getVendorOption(),
      selectValue: vendorId,
      showSearch: true,
      allowClear: true,
      sorted: false,
      placeholder: "Select Vendor",
      label: "Vendor",
      no_outside_label: true,
      url_name: "vendor_id",
      is_outside_filter_drawer: !isMobileScreen,
      is_inside_filter_drawer: isMobileScreen,
    },
    {
      optionData: this.getClientOption(),
      selectValue: clientId,
      showSearch: true,
      allowClear: true,
      sorted: false,
      placeholder: "Select Client",
      label: "Client",
      no_outside_label: true,
      url_name: "client_id",
      is_outside_filter_drawer: !isMobileScreen,
      is_inside_filter_drawer: isMobileScreen,
    },
    {
      optionData: this.getThingOption(),
      selectValue: thingId,
      showSearch: true,
      allowClear: true,
      sorted: false,
      placeholder: "Select Asset",
      label: "Asset",
      no_outside_label: true,
      url_name: "thing_id",
      is_outside_filter_drawer: !isMobileScreen,
      is_inside_filter_drawer: isMobileScreen,
    },
    {
      type: "date",
      ranges: typeWiseTimeOptions[pageType].ranges,
      datePickerConfig: datePickerConfig,
      selectValue: from_time + "-" + upto_time,
      placeholder: "Select Range",
      label: "Date Range",
      no_outside_label: true,
      url_name: "date_range",
      is_outside_filter_drawer: !isMobileScreen,
      is_inside_filter_drawer: isMobileScreen,
    },
    {
      optionData: this.state.filterOptions?.make || [],
      is_options_dynamic: true,
      selectValue: make,
      showSearch: true,
      allowClear: true,
      sorted: false,
      placeholder: "Select Make",
      label: "Make",
      no_outside_label: true,
      url_name: "make",
      is_inside_filter_drawer: true,
      hideField: isColdStorage,
    },
    {
      optionData: this.state.filterOptions?.model || [],
      is_options_dynamic: true,
      selectValue: model,
      showSearch: true,
      allowClear: true,
      sorted: false,
      placeholder: "Enter Value",
      label: "Model",
      no_outside_label: true,
      url_name: "model",
      is_inside_filter_drawer: true,
      hideField: isColdStorage,
    },
    {
      optionData: this.state.filterOptions?.kva || [],
      is_options_dynamic: true,
      allowClear: true,
      hideField: !isDgCategory,
      selectValue: kva,
      placeholder: "Enter Value",
      label: "KVA",
      no_outside_label: true,
      url_name: "kva",
      is_inside_filter_drawer: true,
    },
    {
      type: "advance",
      input_type: "number",
      selectBefore: true,
      hideField: !isDgCategory,
      selectValue: fuel_tank_capacity,
      placeholder: "Enter Value",
      label: "Fuel Tank Capacity (L)",
      no_outside_label: true,
      url_name: "fuel_tank_capacity",
      is_inside_filter_drawer: true,
    },
    {
      type: "advance",
      input_type: "number",
      selectBefore: true,
      hideField: !isDgCategory,
      selectValue: rated_power_factor,
      placeholder: "Enter Value",
      label: "Rated Power Factor",
      no_outside_label: true,
      url_name: "rated_power_factor",
      is_inside_filter_drawer: true,
    },
    {
      optionData: [
        { value: 24, title: "CPCB-II" },
        { value: 25, title: "CPCB-IV" },
      ],
      hideField: !isDgCategory,
      selectValue: genset_type,
      showSearch: true,
      allowClear: true,
      sorted: false,
      placeholder: "Select value",
      label: "Genset Type",
      no_outside_label: true,
      url_name: "genset_type",
      is_inside_filter_drawer: true,
    },
    {
      optionData: [
        { value: "single", title: "Single Phase" },
        { value: "three", title: "Three Phase" },
      ],
      hideField: !isDgCategory,
      selectValue: dg_phase,
      showSearch: true,
      allowClear: true,
      sorted: false,
      placeholder: "Select value",
      label: "DG Phase",
      no_outside_label: true,
      url_name: "dg_phase",
      is_inside_filter_drawer: true,
    },
    {
      type: "advance",
      input_type: "number",
      selectBefore: true,
      hideField: !isDgCategory,
      selectValue: lifetime_runhour,
      placeholder: "Enter Value",
      label: "Lifetime Runhour",
      no_outside_label: true,
      url_name: "lifetime_runhour",
      is_inside_filter_drawer: true,
    },
    {
      type: "advance",
      input_type: "number",
      selectBefore: true,
      hideField: !isDgCategory && !isColdStorage,
      selectValue: runhour,
      placeholder: "Enter Value",
      label: isColdStorage
        ? "Door Opened Duration (in Minutes)"
        : "Trip Runhour (Hr)",
      no_outside_label: true,
      url_name: "trip_runhour",
      is_inside_filter_drawer: true,
    },
    {
      type: "advance",
      input_type: "number",
      selectBefore: true,
      hideField: !isDgCategory,
      selectValue: fuel_consumption,
      placeholder: "Enter Value",
      label: "Trip Fuel Consumed",
      no_outside_label: true,
      url_name: "fuel_consumption",
      is_inside_filter_drawer: true,
    },
    {
      type: "advance",
      input_type: "number",
      selectBefore: true,
      hideField: !isDgCategory,
      selectValue: fuel_filled,
      placeholder: "Enter Value",
      label: "Fuel Filled",
      no_outside_label: true,
      url_name: "fuel_filled",
      is_inside_filter_drawer: true,
    },
    {
      type: "advance",
      input_type: "number",
      selectBefore: true,
      hideField: !isDgCategory,
      selectValue: fuel_theft,
      placeholder: "Enter Value",
      label: "Fuel Theft",
      no_outside_label: true,
      url_name: "fuel_theft",
      is_inside_filter_drawer: true,
    },
    {
      type: "advance",
      input_type: "number",
      selectBefore: true,
      hideField: !isDgCategory,
      selectValue: energy_generated,
      placeholder: "Enter Value",
      label: "Trip Energy Generated",
      no_outside_label: true,
      url_name: "energy_generated",
      is_inside_filter_drawer: true,
    },
    {
      type: "advance",
      input_type: "number",
      selectBefore: true,
      hideField: !isDgCategory,
      selectValue: load_percentage,
      placeholder: "Enter Value",
      label: "Trip Load Percentage",
      no_outside_label: true,
      url_name: "load_percentage",
      is_inside_filter_drawer: true,
    },
    {
      type: "advance",
      input_type: "number",
      selectBefore: true,
      hideField: !isDgCategory,
      selectValue: peak_load,
      placeholder: "Enter Value",
      label: "Trip Peak Load",
      no_outside_label: true,
      url_name: "peak_load",
      is_inside_filter_drawer: true,
    },
    {
      optionData:
        this.isTestDrive && false
          ? // pageType === 'trips' &&
            // categoryId === 67
            [
              {
                value: "test_drive",
                title: "Test Drive",
              },
              {
                value: "internal_transfer",
                title: "Internal Transfer",
              },
              {
                value: "in_transit",
                title: "In Transit",
              },
            ]
          : [],
      selectValue: this.state.order_purpose,
      showSearch: true,
      allowClear: true,
      sorted: false,
      placeholder: "Tags",
      label: "Tags",
      no_outside_label: true,
      url_name: "order_purpose",
      is_inside_filter_drawer: true,
    },
    {
      type: "advance",
      input_type: "select",
      select_mode: "multiple",
      optionData: this.state.tagOptions || [],
      is_options_dynamic: true,
      tagWiseData: this.state.tagWiseData,
      selectBefore: true,
      before_options: [
        { title: "contains", value: "contains" },
        { title: "is empty", value: "empty" },
      ],
      // hideField: this.isTestDrive,
      selectValue: this.state.tags,
      placeholder: "Select Value",
      label: "Tags",
      no_outside_label: true,
      url_name: "tags",
      is_inside_filter_drawer: true,
      hideField: this.props.client_id === 13853,
    },
    // {
    // 	optionData:
    // 		this.isTestDrive
    // 		// categoryId === 67
    // 			? salesExecutiveList
    // 			: [],
    // 	selectValue: this.state.assigned_to,
    // 	hideField: categoryId !== 67,
    // 	showSearch: true,
    // 	allowClear: true,
    // 	sorted: false,
    // 	placeholder: 'Sales Executive',
    // 	label: 'Assigned To',
    // 	no_outside_label: true,
    // 	url_name: 'assigned_to',
    // 	is_inside_filter_drawer: true,
    // },
  ];
  return filterData;
}
