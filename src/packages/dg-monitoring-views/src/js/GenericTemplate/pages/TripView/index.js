import React, { Component } from 'react';
import queryString from 'query-string';
import RightOutlined from '@ant-design/icons/RightOutlined';
import ImageComponent from '@datoms/react-components/src/components/ImageComponent';
import AntButton from '@datoms/react-components/src/components/AntButton';
import GenericFilter from '@datoms/react-components/src/components/GenericFilter/pages';
import DownloadModal from '@datoms/react-components/src/components/DownloadModal';
import DownloadOutlined from '@ant-design/icons/DownloadOutlined';
import GraphHighcharts from '@datoms/react-components/src/components/GraphHighcharts';
import SkeletonLoader from '@datoms/react-components/src/components/SkeletonLoader';
import NoDataComponent from '@datoms/react-components/src/components/NoDataComponent';
import AntList from '@datoms/react-components/src/components/AntList';
import AntListItem from '@datoms/react-components/src/components/AntListItem';
import CustomizeTable from '@datoms/react-components/src/components/CustomizeTable';
import TripViewDetails from './components/TripViewDetails';
import {
	applyFilterSelect,
	createFilterData,
	fetchCustomersList,
	fetchThingsList,
	getClientOption,
	getExtraFilterOptions,
	getFilterOptions,
	getSalesExecutiveList,
	getSelectedThings,
	getThingOption,
	getUrlBreak,
	getAdvFieldsValue,
	getVendorOption,
	onUrlChange,
	validateFilters,
} from './logic/filter';
import {
	isGmmco,
	onRangeChange,
	onPageChange,
	onTableChange,
	onPageTypeChange,
	createRenderData,
	getSelectedRangeTitle,
} from './logic/common';
import {
	downloadBtnClicked,
	downloadModalCallback,
	getDownloadRender,
	onExportReady,
	startDownload,
} from './logic/download';
import {
	getTripData,
	createTripsRenderData,
	fetchTripsData,
	fetchAllTripsData,
} from './logic/trips-data';
import { getDailyData, createDailyRenderData } from './logic/daily-data';
import GraphObjectData from '../../../configuration/GraphObjectData';
import { getTripsCatwiseColumns } from './configs/trips-columns';
import { getDailyCatwiseColumns } from './configs/daily-columns';
import { datePickerConfig, disabledDate } from './configs/time-configs';
import { getBaseUrl } from '@datoms/js-utils/src/base-url-logic';
import { filterAndSortColumns } from '@datoms/js-utils/src/customization';
import './style.less';

const categoryIcon = {
	18: 'https://static.datoms.io/images/icons/thing-category/dg-set.svg',
	45: 'https://static.datoms.io/images/icons/thing-category/cold-storage.svg',
	67: 'https://static.datoms.io/images/icons/thing-category/car.svg',
	91: 'https://static.datoms.io/images/icons/thing-category/solar-panel.svg',
	78: 'https://static.datoms.io/images/icons/thing-category/electrical-machine.svg',
	96: 'https://static.datoms.io/images/icons/thing-category/dg-set.svg',
	103: 'https://static.datoms.io/images/icons/thing-category/solar-pump.svg',
};
export default class TripView extends Component {
	invisibleReportRef = React.createRef();
	downloadModalRef = React.createRef();
	constructor(props) {
		super(props);
		this.parsed = queryString.parse(props.location.search);
		this.supportedThingCats = [18, 45, 67, 76, 78, 91, 103, 96];
		this.state = {
			/*Basic Operation State */
			loading: true,
			bodyLoading: true,
			// tripId:
			// 	props.match && props.match.params && props.match.params.trip_id
			// 		? parseInt(props.match.params.trip_id)
			// 		: '',
			// pageType: props.match?.params?.pageType
			// 	? props.match.params.pageType
			// 	: 'trips',
			page: 1,
			pageSize: 25,
			pageType:
				props.location?.pathname &&
				props.location.pathname.split('/trip-view/')?.[1] &&
				props.location.pathname.split('/trip-view/')[1].split('/')?.[0]
					? props.location.pathname
							.split('/trip-view/')[1]
							.split('/')?.[0]
					: 'trips',
			thingCategoryOptions: [],
			catWiseThingsOptions: {},
			// from_time: moment().subtract(7, 'days').startOf('day').unix(),
			// upto_time: moment().endOf('day').unix(),
			//	dateArray: [moment().subtract(7, 'days').startOf('day'), moment().endOf('day')],
			/*Trip Data */
			summaryData: [],
			graphSeriesData: {
				fuel_consumption: [],
				distance: [],
				runhour: [],
				categories: [],
			},
			tableDataSource: [],
			salesExecutiveList: [],
			userPreferences: {
				trip_view: props.user_preferences?.trip_view || {},
			},
		};
		/*Filter Funcitons */
		this.fetchThingsList = fetchThingsList.bind(this);
		this.fetchCustomersList = fetchCustomersList.bind(this);
		this.validateFilters = validateFilters.bind(this);
		this.onPageChange = onPageChange.bind(this);
		this.onTableChange = onTableChange.bind(this);
		this.onPageTypeChange = onPageTypeChange.bind(this);
		this.getUrlBreak = getUrlBreak.bind(this);
		this.getAdvFieldsValue = getAdvFieldsValue.bind(this);
		this.onUrlChange = onUrlChange.bind(this);
		this.getFilterOptions = getFilterOptions.bind(this);
		this.applyFilterSelect = applyFilterSelect.bind(this);
		this.onRangeChange = onRangeChange.bind(this);
		this.isGmmco = isGmmco.bind(this);
		this.getSelectedThings = getSelectedThings.bind(this);
		this.getSalesExecutiveList = getSalesExecutiveList.bind(this);
		this.getThingOption = getThingOption.bind(this);
		this.getClientOption = getClientOption.bind(this);
		this.getVendorOption = getVendorOption.bind(this);
		this.getExtraFilterOptions = getExtraFilterOptions.bind(this);
		this.disabledDate = disabledDate.bind(this);
		this.createFilterData = createFilterData.bind(this);
		/*Download Functions */
		this.getDownloadRender = getDownloadRender.bind(this);
		this.onExportReady = onExportReady.bind(this);
		this.downloadModalCallback = downloadModalCallback.bind(this);
		this.downloadBtnClicked = downloadBtnClicked.bind(this);
		this.startDownload = startDownload.bind(this);
		this.filterAndSortColumns = filterAndSortColumns.bind(this);
		/*Trips Data */
		this.getTripData = getTripData.bind(this);
		this.getDailyData = getDailyData.bind(this);
		this.createRenderData = createRenderData.bind(this);
		this.createTripsRenderData = createTripsRenderData.bind(this);
		this.createDailyRenderData = createDailyRenderData.bind(this);
		this.getTripsCatwiseColumns = getTripsCatwiseColumns.bind(this);
		this.getDailyCatwiseColumns = getDailyCatwiseColumns.bind(this);
		this.fetchTripsData = fetchTripsData.bind(this);
		this.fetchAllTripsData = fetchAllTripsData.bind(this);

		this.isTestDrive =
			typeof props.globalConfig === 'function' &&
			props.globalConfig('testdrive_vendor_id') &&
			Array.isArray(props.globalConfig('testdrive_vendor_id')) &&
			props.globalConfig('testdrive_vendor_id').includes(props.client_id);
	}
	componentDidMount() {
		document.title = 'Event View';
		this.getFilterOptions();
	}
	getGraphObj() {
		const { graphSeries, graphSeriesData, categoryId } = this.state;
		let graphObj = JSON.parse(JSON.stringify(GraphObjectData));
		graphObj.graph_data.config.chart.type = 'column';
		graphObj.graph_data.config.chart.height = 180;
		graphObj.graph_data.config.title.style = {
			color: '#232323',
		};
		graphObj.graph_data.config.legend.enabled = true;
		graphObj.graph_data.config.yAxis = [
			{
				title: {
					text: categoryId === 18 ? 'Runhour (Hr)' : 'Distance (Km)',
				},
				//type: 'logarithmic'
			},
			{
				title: {
					text: 'Fuel Consumption (L)',
				},
				opposite: true,
				//type: 'logarithmic'
			},
		];
		graphObj.graph_data.config.xAxis.categories =
			graphSeriesData.categories;
		graphObj.graph_data.series_data = graphSeries;
		console.log('ffffffggg', graphObj.graph_data);
		return graphObj.graph_data;
	}
	render() {
		const isMobileScreen = window.innerWidth <= 900;
		const { application_id } = this.props;
		const {
			loading,
			bodyLoading,
			tableLoading,
			downloadLoading,
			message,
			pageType,
			categoryId,
			defaultCategoryId,
			summaryData,
			tableDataSource,
			totalData,
			apiTripData,
			clientId,
			thingId,
			from_time,
			upto_time,
			thingCategoryOptions,
			selectedTripId,
		} = this.state;
		// let tripId =
		// 	this.props.location?.pathname &&
		// 	this.props.location.pathname.split('/trip-view/')?.[1] &&
		// 	this.props.location.pathname.split('/trip-view/')[1].split('/')?.[1]
		// 		? this.props.location.pathname
		// 				.split('/trip-view/')[1]
		// 				.split('/')?.[1]
		// 		: '';
		const filterData = this.createFilterData();
		console.log('filter_data: ', filterData);
		// const pageTypeOptions = [
		// 	{ key: 'trips', label: 'Trips' },
		//	{ key: 'daily', label: 'Daily' /*disabled: true*/ },
		//	{ key: 'monthly', label: 'Monthly' /*disabled: true*/ },
		// ];
		console.log('this.getSelectedThings', this.getSelectedThings());
		console.log(
			'tripsData -->',
			'summaryData: ',
			summaryData,
			'\n',
			'tableDataSource: ',
			tableDataSource,
		);
		let pageRender = '';
		let baseUrl = getBaseUrl(
			this.props,
			`trip-view/${pageType}`,
			this.props.isGenericView,
		);
		if (loading) {
			pageRender = <SkeletonLoader count={3} rows={4} />;
		} else if (message) {
			pageRender = <NoDataComponent text={message} height="100%" />;
		} else if (
			// this.props.location.pathname.includes('/details') &&
			selectedTripId &&
			tableDataSource.length &&
			!isNaN(parseInt(selectedTripId))
		) {
			const tableColumns =
				pageType === 'trips'
					? this.getTripsCatwiseColumns()
					: this.getDailyCatwiseColumns();
			const tripDetails = apiTripData.find(
				(trip) => trip.Id === parseInt(selectedTripId),
			);
			let detailsClientId = this.props.client_id;
			if ([12, 17].includes(this.props.application_id)) {
				let findThing = totalData?.things?.find(
					(thing) => thing.id === tripDetails.Devices?.[0],
				);
				console.log('tripDetails thing: ', findThing, tripDetails);
				if (findThing?.customer_id) {
					detailsClientId = findThing.customer_id;
				}
			}
			pageRender = (
				<TripViewDetails
					{...this.props}
					application_id={16}
					client_id={detailsClientId}
					totalData={totalData}
					tripDetails={[tripDetails]}
					goBack={
						() => this.setState({ selectedTripId: undefined })
						// this.props.history.push(
						// 	baseUrl + this.props.location.search
						// )
					}
					isMobileScreen={isMobileScreen}
					columns={tableColumns || []}
					dataSource={tableDataSource}
					showMap={[67, 76].includes(categoryId)}
					thing_category_icon={categoryIcon[categoryId]}
					categoryId={categoryId}
				/>
			);
		} else {
			console.log('trip-columns', loading, message);
			const tableColumns =
				pageType === 'trips'
					? this.getTripsCatwiseColumns()
					: this.getDailyCatwiseColumns();
			datePickerConfig.disabledDate = this.disabledDate;
			let currentCategory = thingCategoryOptions.find(
				(item) => item.value === categoryId,
			);
			let downloadButton = (
				// application_id === 16/* || clientId || thingId*/ ? (
				<div>
					<AntButton
						// type="text"
						disabled={bodyLoading || downloadLoading}
						loading={downloadLoading}
						className="tdmv-dwn-btn"
						onClick={this.startDownload}
						//	onClick={this.downloadBtnClicked}
					>
						<DownloadOutlined
						// style={{ color: '#374375', fontSize: 19 }}
						/>
						{isMobileScreen ? '' : ' Download'}
					</AntButton>
					<DownloadModal
						ref={this.downloadModalRef}
						callback={(e) => this.downloadModalCallback(e)}
						report_name="Trip"
					/>
					{this.state.download_render}
				</div>
			);
			// ) : (
			// 	''
			// );
			let genericFilter = (
				<GenericFilter
					isFlexWrap
					hideIconCount
					backgroundWhite
					history={this.props.history}
					url={baseUrl}
					width={isMobileScreen ? undefined : 525}
					default={[
						'', //defaultCategoryId,
						'',
						'',
						'',
						'', //from_time + '-' + upto_time,
						...filterData.slice(5).map((_) => ''),
					]}
					filterData={filterData}
					panelFilterSelect={(value, key) =>
						this.applyFilterSelect(value, key)
					}
					onUrlChange={this.onUrlChange}
					is_all_disabled={bodyLoading || downloadLoading}
					// isChildrenInsideDrawer={isMobileScreen}
					// children={
					// 	<div className="tdmv-time-selec">
					// 		<SelectWithRangepicker
					// 			fromTime={this.state.from_time}
					// 			uptoTime={this.state.upto_time}
					// 			config={datePickerConfig}
					// 			ranges={
					// 				typeWiseTimeOptions[pageType]
					// 					.ranges
					// 			}
					// 			onRangeChange={this.onRangeChange}
					// 			// onCalendarChange={(val) => {
					// 			// 	this.setState({
					// 			// 		dateArray: val,
					// 			// 	});
					// 			// }}
					// 		/>
					// 	</div>
					// }
				/>
			);
			pageRender = (
				<>
					{/* <section className="tdmv-header">
						<AntRadioGroup
							//	size="small"
							isButtonStyle
							value={pageType}
							onChange={this.onPageTypeChange}
						>
							{pageTypeOptions.map((item) => (
								<AntRadioButton
									value={item.key}
									disabled={item.disabled}
								>
									{item.label}
								</AntRadioButton>
							))}
						</AntRadioGroup>
					</section> */}
					<section className="tdmv-body">
						<div className="tdmv-filters">
							{isMobileScreen ? (
								<div className="tdmv-mb-icon">
									{categoryIcon[categoryId] && <section>
										<ImageComponent
											background="transparent"
											src={categoryIcon[categoryId]}
											show_status={false}
										/>
									</section>}
									<section className="tdmv-mbic-lft">
										<p>{currentCategory?.title || '-'}</p>
										<p>
											{getSelectedRangeTitle(
												from_time,
												upto_time,
											)}
										</p>
									</section>
								</div>
							) : (
								''
							)}
							{isMobileScreen ? (
								<div className="tdmv-mb-fl">
									{downloadButton}
									{genericFilter}
								</div>
							) : (
								<>
									{genericFilter}
									{downloadButton}
								</>
							)}
						</div>
						{bodyLoading ? (
							<SkeletonLoader count={3} rows={4} />
						) : (
							<>
								<div className="tdmv-summary">
									<p className="tdmv-sec-head">Summary</p>
									<section className="tdmv-summ-box">
										<div className="tdmv-summ-kpis">
											{summaryData.map((summary_item) => (
												<div>
													<p className="tdmv-val">
														{summary_item.parameter}
													</p>
													<p className="tdmv-label">
														{summary_item.value}
													</p>
												</div>
											))}
										</div>
										{isMobileScreen ||
										application_id === 12 || ![18, 67, 76].includes(parseInt(categoryId)) ? (
											''
										) : (
											<div className="tdmv-summ-graph">
												<GraphHighcharts
													graphData={this.getGraphObj()}
												/>
											</div>
										)}
									</section>
								</div>
								<div className="tdmv-table">
									<p className="tdmv-sec-head">
										{isMobileScreen
											? 'Trips'
											: 'Detailed List'}
									</p>
									<section className="tdmv-cstm">
										{isMobileScreen ? (
											<AntList
												itemLayout="vertical"
												size="small"
												pagination={{
													position: 'top',
													size: 'small',
													showSizeChanger: true,
													onChange: this.onPageChange,
													pageSize:
														this.state.pageSize,
													total: this.state.totalPage,
													current: this.state.page,
													showTotal: (total, range) =>
														`Showing ${range[0]}-${range[1]} of ${total} items`,
												}}
												dataSource={tableDataSource}
												loading={tableLoading}
												renderItem={(item) => (
													<AntListItem
														key={item.sl_no}
													>
														<section
															className="tdmv-mb-list"
															onClick={() =>
																this.setState({
																	selectedTripId:
																		item.trip_id,
																})
															}
														>
															<div className="mb-list-rg">
																{item.sl_no}
															</div>
															<div className="mb-list-lf">
																<p>
																	<span>
																		{
																			item.on_time
																		}{' '}
																		-{' '}
																		{
																			item.off_time
																		}
																	</span>
																	<RightOutlined />
																</p>
																<p>
																	{
																		item.thing_name
																	}
																</p>
															</div>
														</section>
													</AntListItem>
												)}
											/>
										) : (
											<CustomizeTable
												{...this.props}
												preferenceKeys={[
													'trip_view',
													pageType + '_' + categoryId,
												]}
												tableProps={{
													dataSource: tableDataSource,
													columns: tableColumns || [],
													shadow: false,
													isGrouped: false,
													sticky: true,
													scroll: { x: 'max-content' },
													onChange:
														this.onTableChange,
													loading: tableLoading,
													rowClassName: (record) => {
														if (
															this.props
																.client_id === 1
														) {
															if (
																record.fuel_filled >
																	0 &&
																record.fuel_theft >
																	0
															) {
																return 'fuel-theft-highlight';
															} else if (
																record.fuel_filled >
																0
															) {
																return 'fuel-filled-highlight';
															} else if (
																record.fuel_theft >
																0
															) {
																return 'fuel-theft-highlight';
															}
														}
														return '';
													},
													pagination: {
														position: ['topRight'],
														pageSizeOptions: [
															10, 20, 25, 50, 100,
														],
														pageSize:
															this.state.pageSize,
														total: this.state
															.totalPage,
														current:
															this.state.page,
														//	onChange: this.onPageChange,
														showQuickJumper: true,
														showTotal: (
															total,
															range,
														) =>
															`${range[0]}-${range[1]} of ${total} items`,
													},
												}}
											/>
										)}
									</section>
								</div>
							</>
						)}
					</section>
				</>
			);
		}
		return (
			<main
				id="trips_daily_monthly_view"
				className="basic-page-layout-height"
			>
				{pageRender}
			</main>
		);
	}
}
