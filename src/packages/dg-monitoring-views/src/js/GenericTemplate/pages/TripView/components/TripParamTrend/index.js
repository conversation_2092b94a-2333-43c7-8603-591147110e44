import React, { Component } from 'react';
import _find from 'lodash/find';
import _uniqBy from 'lodash/uniqBy';
import _maxBy from 'lodash/maxBy';
import _minBy from 'lodash/minBy';
import moment from 'moment-timezone';
import { getThingsData, retriveThingsList } from '@datoms/js-sdk';
import AntRow from '@datoms/react-components/src/components/AntRow';
import AntCol from '@datoms/react-components/src/components/AntCol';
import AntCheckbox from '@datoms/react-components/src/components/AntCheckbox';
import SearchInput from '@datoms/react-components/src/components/SearchInput';
import SkeletonLoader from '@datoms/react-components/src/components/SkeletonLoader';
import GraphHighcharts from '@datoms/react-components/src/components/GraphHighcharts';
import GraphObjectData from '../../../../../configuration/GraphObjectData';
import paramRawAggrConfig from '../../../../../configuration/detailedViewConfig/ParamRawAggrConfig';
import { getSelectedparameterDataWithTimestamp } from '../../../../../data_handling/ParameterDataManipulation';
import GraphImg from '../../../../images/graph.svg';
import minGraphImg from '../../../../images/Group 1847.svg';
import maxGraphImg from '../../../../images/Group 1848.svg';
import './style.less';

export default class TripParamTrend extends Component {
	constructor(props) {
		super(props);
		GraphObjectData.graph_data.config.timezone =
			props.user_preferences.timezone;
		this.state = {
			loading: true,
			graphSectionLoading: true,
			paramArray: [],
			paramRawAggrConfig: paramRawAggrConfig,
			GraphObjectData: GraphObjectData,
			selectedParam: {},
			paramChecked: {},
			selectedParamItems: [],
			selectedParamArray: [],
		};
	}
	async componentDidMount() {
		const { client_id, application_id, thing_id } = this.props;
		let totalData = await retriveThingsList({
			client_id: client_id,
			application_id: application_id,
		});

		let findThings = _find(totalData.things, { id: parseInt(thing_id) });
		let paramArray = [];
		if (findThings?.category === 18) {
			let totalArray = JSON.parse(
				JSON.stringify(this.state.paramRawAggrConfig.paramArray),
			);
			let phaseArrayKeys = [];
			totalArray.map((getParams) => {
				if (getParams.phase && getParams.phase.length) {
					getParams.phase.map((phaseParams) => {
						phaseArrayKeys.push(phaseParams);
					});
				}
			});
			if (findThings && findThings.parameters.length) {
				// copy this gdai
				findThings.parameters.map((params) => {
					if (
						params.allowed_visualizations &&
						params.allowed_visualizations.length &&
						!phaseArrayKeys.includes(params.key) &&
						params.type !== 'fault' &&
						//	params.key !== 'mc_st' &&
						// params.key !== 'rnhr' &&
						params.key !== 'rnmin' &&
						params.key !== 'calculated_runhour' &&
						params.key !== 'data_availability' &&
						params.key !== 'mrun_hr' &&
						params.key !== 'ser_due_hr' &&
						params.key !== 'calculated_energy' &&
						params.key !== 'dg_lock_status' &&
						params.key !== 'au_m_stat' &&
						params.key !== 'fuel_consume' &&
						params.key !== 'fuel_theft' &&
						params.key !== 'fuel_filled'
					) {
						totalArray.push({
							key: params.key,
							unit: params.unit,
							name: params.name,
						});
					}
				});
			}
			if (findThings) {
				let btsParams =
					Array.isArray(findThings.parameters) &&
					findThings.parameters
						.filter((params) => params.key.includes('bts'))
						.toString().length > 0
						? findThings.parameters.filter((params) =>
								params.key.includes('bts'),
							)
						: [];
				btsParams.push('rm_temp', 'fan_cur');
				totalArray = totalArray.concat(btsParams);
				totalArray.map((params) => {
					if (params.phase) {
						findThings.parameters.map((paramKeys) => {
							if (params.phase.includes(paramKeys.key)) {
								paramArray.push({
									key: params.key,
									unit: params.unit,
									name: params.name,
									phase: params.phase,
								});
							}
						});
					} else {
						let paramDetails = _find(findThings.parameters, {
							key: params.key,
						});
						if (paramDetails) {
							paramArray.push({
								key: params.key,
								unit: params.unit,
								name: params.name,
							});
						}
					}
				});
			}
			paramArray = _uniqBy(paramArray, 'key');

			const sortedParamOrder = [
				'fuel_level',
				'fuel_raw',
				'mc_st',
				'vbat',
				'enrg',
				'rnhr',
				'load_percentage',
			];
			paramArray.sort((a, b) => {
				if (
					sortedParamOrder.includes(a.key) &&
					sortedParamOrder.includes(b.key)
				) {
					return (
						sortedParamOrder.findIndex((key) => key === a.key) -
						sortedParamOrder.findIndex((key) => key === b.key)
					);
				}
				if (
					sortedParamOrder.includes(a.key) &&
					!sortedParamOrder.includes(b.key)
				) {
					return -1;
				}
				if (
					sortedParamOrder.includes(b.key) &&
					!sortedParamOrder.includes(a.key)
				) {
					return 1;
				}
				return 0;
			});
		} else {
			if (findThings?.parameters?.length) {
				findThings.parameters.map((params) => {
					if (
						params.allowed_visualizations.includes('raw')
					) {
						paramArray.push({
							key: params.key,
							unit: params.unit,
							name: params.name,
						});
					}
				});
			}
		}
		// console.log('paramArray: ', paramArray);
		const selectedParamItems = [];
		const selectedParamArray = [];

		if (
		findThings?.category === 18 &&
		paramArray.find((param) => param.key === "fuel")
		) {
		selectedParamItems.push("fuel");
		selectedParamArray.push("fuel");
		}

		if (
		findThings?.category === 18 &&
		paramArray.find((param) => param.key === "load_percentage")
		) {
		selectedParamItems.push("load_percentage");
		selectedParamArray.push("load_percentage");
		}

		const sliceIndex = findThings?.category === 18 ? 5 : 1;
		if (!selectedParamArray.length) {
		paramArray.slice(0, sliceIndex).forEach((param) => {
			selectedParamItems.push(param.key);
			const allKeys = this.getAllKeys(param.key);
			selectedParamArray.push(...allKeys);
		});
		}

		this.setState(
		{
			paramArray,
			selectedParamItems,
			selectedParamArray,
			thingDetails: findThings,
			loading: false,
			// selectedParam: paramArray[0] ?? {},
		},
		() => this.getParamData(),
		//() => this.onParamCheck(true, paramArray[0].key)
		);
	}

	insertNullForLargeGaps(data) {
		const THREE_MINUTES = 3 * 60 * 1000;
		try {
			Object.keys(data).forEach((key) => {
			Object.keys(data[key]).forEach((param) => {
				const graphData = data[key][param];
				let i = 0;
		
				while (i < graphData.length - 1) {
				const [currentTime, currentValue] = graphData[i];
				const [nextTime, nextValue] = graphData[i + 1];
		
				if (nextTime - currentTime >= THREE_MINUTES) {
					const midpoint = Math.floor((currentTime + nextTime) / 2);
					graphData.splice(i + 1, 0, [midpoint, null]);
				} else {
					i++;
				}
				}
			});
			});
		
			return data;
		} catch (error) {
			console.error('Error in insertNullForLargeGaps:', error);
			return data;
		}
	};

	async getParamData() {
		const {
			selectedParamItems,
			selectedParamArray,
			paramArray,
			thingDetails,
		} = this.state;

		const selectedSingleParam = paramArray.find(
			(p) => p.key === selectedParamItems[0],
		);

		const dataPacketRaw = {
			data_type: 'raw',
			aggregation_period: 0,
			parameters: selectedParamArray,
			parameter_attributes: [],
			things: [parseInt(this.props.thing_id)],
			from_time: parseInt(this.props.start_time),
			upto_time: parseInt(this.props.end_time),
		};
		const rawData = await getThingsData(
			dataPacketRaw,
			this.props.client_id,
			this.props.application_id,
		);
		if (rawData.status === 'success') {
			let actualGraphData = getSelectedparameterDataWithTimestamp(
				rawData.data,
				[parseInt(this.props.thing_id)],
				selectedParamArray,
				this.props.start_time,
				this.props.end_time,
			);
			actualGraphData = this.insertNullForLargeGaps(actualGraphData);
			const findThing = thingDetails;
			// const selectedParamUnit = selectedParam.unit;
			// const selectedParamName = selectedParam.name;
			let paramGraph = JSON.parse(
				JSON.stringify(this.state.GraphObjectData),
			);
			paramGraph.graph_data.config.backgroundColor = 'transparent';
			paramGraph.graph_data.config.plotOptions = {
				series: {
					boostThreshold: 0,
					marker: {
						radius: 1.5,
					},
				},
			};
			// paramGraph.graph_data.config.yAxis.title = {
			// 	//	text: selectedParamUnit,
			// 	style: {
			// 		color: '#7686A1',
			// 	},
			// 	labels: {
			// 		formatter: function () {
			// 			return [this.value.toFixed(2)];
			// 		},
			// 	},
			// };

			paramGraph.graph_data.config.xAxis.title.text = 'Date & Time';

			// const paramArray = JSON.parse(
			// 	JSON.stringify(this.state.paramRawAggrConfig.paramArray)
			// );

			// const phaseArray = _find(paramArray, {
			// 	key: selectedParam.key,
			// })?.phase;
			// if (phaseArray) {
			const { seriesData, yAxisArray } =
				this.getGeneratorMainsPhaseGraphData(
					actualGraphData,
					selectedParamArray, //phaseArray
				);
			paramGraph.graph_data.config.legend.enabled = seriesData?.length > 1;
			paramGraph.graph_data.series_data = seriesData;
			paramGraph.graph_data.config.yAxis = yAxisArray;
			// }
			//  else {
			// 	let thingParameterDataRawFinal = [];
			// 	if (
			// 		actualGraphData &&
			// 		actualGraphData[this.props.thing_id] &&
			// 		actualGraphData[this.props.thing_id][selectedParam.key] &&
			// 		actualGraphData[this.props.thing_id][selectedParam.key]
			// 			.length
			// 	) {
			// 		if (this.state.data_period === 0) {
			// 			actualGraphData[this.props.thing_id][
			// 				selectedParam.key
			// 			].map((data, ind) => {
			// 				if (
			// 					actualGraphData[this.props.thing_id][
			// 						selectedParam.key
			// 					][ind + 1] &&
			// 					actualGraphData[this.props.thing_id][
			// 						selectedParam.key
			// 					][ind + 1][0] -
			// 						actualGraphData[this.props.thing_id][
			// 							selectedParam.key
			// 						][ind][0] >
			// 						(findThing?.offline_timeout &&
			// 						findThing?.offline_timeout > 0
			// 							? parseInt(findThing?.offline_timeout) *
			// 							  1000
			// 							: 900000)
			// 				) {
			// 					thingParameterDataRawFinal.push([
			// 						data[0] +
			// 							(findThing?.offline_timeout &&
			// 							findThing?.offline_timeout > 0
			// 								? parseInt(
			// 										findThing?.offline_timeout
			// 								  ) * 1000
			// 								: 900000),
			// 						null,
			// 					]);
			// 				} else {
			// 					thingParameterDataRawFinal.push([
			// 						data[0],
			// 						parseFloat(data[1]),
			// 					]);
			// 				}
			// 			});
			// 		} else {
			// 			thingParameterDataRawFinal =
			// 				actualGraphData?.[this.props.thing_id]?.[
			// 					selectedParam.key
			// 				];
			// 		}
			// 	}
			// 	paramGraph.graph_data.series_data = [
			// 		{
			// 			name: selectedParamName,
			// 			type: 'area',
			// 			data: thingParameterDataRawFinal,
			// 			color: '#4268A757',
			// 		},
			// 	];
			// }
			// paramGraph.graph_data.config.tooltip.valueSuffix =
			// 	' ' + selectedParamUnit;
			paramGraph.graph_data.config.tooltip.xDateFormat =
				this.state.data_type === 'raw'
					? '%d-%m-%Y, %H:%M:%S'
					: '%d-%m-%Y, %H:%M';
			paramGraph.graph_data.config.chart.height = 250;
			let avgValue = '',
				minValue = '',
				maxValue = '';
			let data =
				actualGraphData?.[this.props.thing_id]?.[
					selectedSingleParam.key
				];
			avgValue = data?.reduce(
				(r, a) => a.map((b, i) => (r[i] || 0) + b),
				[],
			);
			minValue = _minBy(data, function (o) {
				return o[1];
			});
			maxValue = _maxBy(data, function (o) {
				return o[1];
			});
			let dataLength = 0,
				avg = {},
				min = {},
				max = {};
			if (data && data.length) {
				dataLength = data.length;
			}
			if (dataLength > 0 && avgValue && avgValue.length) {
				avg = {
					value: parseFloat(avgValue[1] / dataLength).toFixed(2),
				};
			} else {
				avg = 'NA';
			}
			if (minValue && minValue.length) {
				min = {
					time: minValue[0],
					value: parseFloat(minValue[1]).toFixed(2),
				};
			} else {
				min = {
					time: 0,
					value: 'NA',
				};
			}
			if (maxValue && maxValue.length) {
				max = {
					time: maxValue[0],
					value: parseFloat(maxValue[1]).toFixed(2),
				};
			} else {
				max = {
					time: 0,
					value: 'NA',
				};
			}
			console.log('paramGraph.graph_data : ', paramGraph.graph_data);
			this.setState({
				avg: avg,
				min: min,
				max: max,
				graph_data_config: paramGraph.graph_data,
				no_data: false,
				graphSectionLoading: false,
			});
		} else {
			this.setState({ no_data: true, graphSectionLoading: false });
		}
	}

	getGeneratorMainsPhaseGraphData(thingAllParamDataRaw, phaseArray) {
		const findThings = this.state.thingDetails;
		const seriesData = [],
			graphRenderData = {},
			yAxisArray = [];
		phaseArray.map((paramValue, paramInd) => {
			const findParamDetails = _find(findThings.parameters, {
				key: paramValue,
			});
			console.log("paramValue", paramValue)
			yAxisArray.push({
				title: {
					text: `${findParamDetails?.name} ${
						findParamDetails?.unit
							? '(' + findParamDetails?.unit + ')'
							: ''
					}`,
					style: {
						color: '#7686A1',
					},
				},
				labels: {
					formatter: function () {
						var value = this.value;  // Get the value for the current axis label
						var axisMax = this.axis.max;  // Get the maximum value of the axis
						var axisMin = this.axis.min;  // Get the minimum value of the axis
			
						// Show "Open" for the highest value and "Closed" for the lowest value
						if (paramValue === 'dr_st') {
							if (value === axisMax) {
								return "Open";  // Display "Open" for the highest value
							} else if (value === axisMin) {
								return "Closed";  // Display "Closed" for the lowest value
							}
							return "";  // Return nothing for other values
						}
						return this.value.toFixed(2);  // Default to formatting decimals
					}
				},
				max: paramValue === 'dr_st' ? 1 : undefined,
				opposite: paramInd % 2 === 0 ? false : true,
			});
			if (!graphRenderData[paramValue]) {
				graphRenderData[paramValue] = [];
			}
			let getGraphDataFormFormat = thingAllParamDataRaw;
			if (
				getGraphDataFormFormat[parseInt(this.props.thing_id)] &&
				getGraphDataFormFormat[parseInt(this.props.thing_id)][
					paramValue
				] &&
				getGraphDataFormFormat[parseInt(this.props.thing_id)][
					paramValue
				].length
			) {
				getGraphDataFormFormat[parseInt(this.props.thing_id)][
					paramValue
				].map((rawGraph) => {
					if (rawGraph && rawGraph.length) {
						return graphRenderData[paramValue].push([
							rawGraph[0],
							rawGraph[1],
						]);
					}
				});
			}
			seriesData.push({
			name: findParamDetails?.name || "",
			type: "area",
			data: graphRenderData[paramValue],
			color: this.state.paramRawAggrConfig.phaseColor[paramInd],
			unit: findParamDetails?.unit || "",
			tooltip: {
			pointFormatter:
				paramValue === "dr_st"
				? function () {
					var point = this;
					var status = this.y === 1 ? "Open" : this.y === 0 ? "Closed" : "-";
					return (
						"<div></div> " +
						'<span style="color:' +
						point.color +
						'">' +
						point.series.name +
						"</span>: <b>" +
						status
					);
					}
				: function () {
					var point = this;
					return (
						"<div></div> " +
						'<span style="color:' +
						point.color +
						'">' +
						point.series.name +
						"</span>: <b>" +
						parseFloat(point.y).toFixed(2) +
						" " +
						(findParamDetails?.unit || "")
					);
					},
			},
			yAxis: paramInd,
		});
		});

		return { seriesData, yAxisArray };
	}
	// selectParam(param = this.state.selectedParam) {
	// 	let paramArray = [];
	// 	const paramConfig = JSON.parse(
	// 		JSON.stringify(this.state.paramRawAggrConfig.paramArray)
	// 	);
	// 	const findThings = this.state.thingDetails;
	// 	const phaseArray = _find(paramConfig, { key: param.key })?.phase;
	// 	if (phaseArray) {
	// 		phaseArray.map((phase) => {
	// 			if (
	// 				findThings &&
	// 				findThings.parameters &&
	// 				findThings.parameters.length
	// 			) {
	// 				let findParams = _find(findThings.parameters, {
	// 					key: phase,
	// 				});
	// 				if (findParams) {
	// 					paramArray.push(findParams.key);
	// 				}
	// 			}
	// 		});
	// 	} else {
	// 		paramArray = [param.key];
	// 	}
	// 	this.setState(
	// 		{
	// 			selectedParam: param,
	// 			selectedParamArray: paramArray,
	// 			graphSectionLoading: true,
	// 		},
	// 		() => this.getParamData()
	// 	);
	// }

	getAllKeys(key) {
		const allKeys = [];
		const paramConfig = JSON.parse(
			JSON.stringify(this.state.paramRawAggrConfig.paramArray),
		);
		const findThings = this.state.thingDetails;
		const phaseArray = _find(paramConfig, { key: key })?.phase;
		if (phaseArray) {
			phaseArray.map((phase) => {
				if (
					findThings &&
					findThings.parameters &&
					findThings.parameters.length
				) {
					const findParams = _find(findThings.parameters, {
						key: phase,
					});
					if (findParams) {
						allKeys.push(findParams.key);
					}
				}
			});
		} else {
			allKeys.push(key);
		}
		return allKeys;
	}

	onParamCheck(checked, key) {
		const { selectedParamItems, selectedParamArray } = this.state;

		/*At least one param item should be selected */
		if (selectedParamItems.length === 1 && !checked) return;

		let newSelectedParamItems = JSON.parse(
			JSON.stringify(selectedParamItems),
		);
		let newSelectedParamArray = JSON.parse(
			JSON.stringify(selectedParamArray),
		);

		const allKeys = this.getAllKeys(key);
		if (checked) {
			newSelectedParamItems.push(key);
			newSelectedParamArray.push(...allKeys);
		} else {
			newSelectedParamItems = newSelectedParamItems.filter(
				(param_key) => param_key !== key || checked,
			);

			newSelectedParamArray = newSelectedParamArray.filter(
				(param_key) => !allKeys.includes(param_key) || checked,
			);
		}

		/* Max 5 params can be plotted in graph */
		if (newSelectedParamArray.length > 5) return;

		this.setState(
			{
				selectedParamItems: [...new Set(newSelectedParamItems)],
				selectedParamArray: [...new Set(newSelectedParamArray)],
				graphSectionLoading: true,
			},
			() => this.getParamData(),
		);
	}

	getSelectedParamDetails() {
		const { selectedParamItems, paramArray } = this.state;

		const finalArray = [];
		selectedParamItems.forEach((key) => {
			const findParam = paramArray.find((item) => item.key === key);
			finalArray.push(findParam);
		});
		console.log('finalArray: ', finalArray);
		return finalArray;
	}

	render() {
		const {
			paramArray,
			selectedParamItems,
			selectedParamArray,
			avg,
			min,
			max,
			graph_data_config,
			loading,
			graphSectionLoading,
			no_data,
			param_search,
			paramChecked,
		} = this.state;
		if (loading) {
			return <SkeletonLoader count={2} rows={3} />;
		}
		console.log(
			'selected_gdai ---> ',
			selectedParamItems,
			selectedParamArray,
		);
		const parametersNameArray = [];
		paramArray.map((param) => {
			if (
				param_search &&
				!param.name?.toLowerCase().includes(param_search.toLowerCase())
			)
				return;

			parametersNameArray.push(
				<div
					className={
						'param-name ' +
						(selectedParamItems.includes(param.key)
							? 'selected'
							: '')
						// (selectedParam.key === param.key ? 'selected' : '')
					}
					// onClick={() => this.selectParam(param)}
				>
					<span>
						{param.name}
						{param.unit ? ` (${param.unit})` : ''}
					</span>
					{/* {param.phase?.length ? (
						''
					) : ( */}
					<span>
						<AntCheckbox
							text=""
							checked={selectedParamItems?.includes(param.key)}
							disabled={
								graphSectionLoading ||
								(selectedParamArray?.length +
									(param.phase?.length || 1) >
									5 &&
									!selectedParamItems?.includes(param.key))
							}
							onChange={(e) =>
								this.onParamCheck(e.target.checked, param.key)
							}
						/>
					</span>
					{/* )} */}
				</div>,
			);
		});
		const paramMetrics = (
			<section className="gdv-param-metric">
				<div className="gdv-param-metric-item-container">
					<div className="gdv-param-metric-item">
						<div className="time-icon-name">
							<div className="icon-image">
								<img src={GraphImg} />
							</div>
							<div>Average</div>
						</div>
						<div className="gdv-param-metric-value">
							<div className="value-div">
								{avg?.value ?? 'NA'}
							</div>
						</div>
					</div>
				</div>
				<div className="gdv-param-metric-item-container">
					<div className="gdv-param-metric-item not-avg">
						<div className="time-icon-name">
							<div className="image-name">
								<div className="icon-image">
									<img src={minGraphImg} />
								</div>
							</div>
							<div className="time-div">
								<div>Minimum</div>
								{/* {paramDetails?.min?.time > 0
									? '(On ' +
									  moment(
											paramDetails.min.time
									  ).format('DD MMM, HH:mm)')
									: ''} */}
							</div>
						</div>
						<div className="gdv-param-metric-value">
							<div className="value-div">
								{min?.value ?? 'NA'}
							</div>
						</div>
					</div>
				</div>
				<div className="gdv-param-metric-item-container">
					<div className="gdv-param-metric-item not-avg">
						<div className="time-icon-name">
							<div className="image-name">
								<div className="icon-image">
									<img src={maxGraphImg} />
								</div>
							</div>
							<div className="time-div">
								<div>Maximum</div>
								{/* {paramDetails?.max?.time > 0
									? '(On ' +
									  moment(
											paramDetails.max.time
									  ).format('DD MMM, HH:mm)')
									: ''} */}
							</div>
						</div>
						<div className="gdv-param-metric-value">
							<div className="value-div">
								{max?.value ?? 'NA'}
							</div>
						</div>
					</div>
				</div>
			</section>
		);

		const paramDetails = this.getSelectedParamDetails();
		const graphSection = (
			<div className="graph-section-container">
				<div className="graph-heading">
					{paramDetails.map((param, index) => {
						const isLast = paramDetails.length - 1 === index;
						return (
							<>
								<span>
									{param.name +
										(param.unit ? ` (${param.unit})` : '')}
								</span>
								{isLast ? '' : 'vs'}
							</>
						);
					})}
					{/* {selectedParam.name || ''}{' '}
					{selectedParam.unit ? `(${selectedParam.unit})` : ''} */}
				</div>
				{graphSectionLoading ? (
					<SkeletonLoader count={1} rows={4} />
				) : (
					<>
						{selectedParamArray?.length === 1 && !selectedParamArray?.includes('dr_st') ? paramMetrics : ''}
						<div className="graph-render-div">
							<GraphHighcharts graphData={graph_data_config} />
						</div>
					</>
				)}
			</div>
		);

		return (
			<div id="trip_view_param_trend">
				<div className="down-section">
					<AntRow className="param-select-graph-row">
						<AntCol
							className="param-select-col"
							xs={24}
							sm={24}
							md={9}
							lg={8}
							xl={8}
							xxl={7}
						>
							<div className="param-select-container">
								<div className="heading">Parameters</div>
								<SearchInput
									onSearch={(e) =>
										this.setState({ param_search: e })
									}
								/>
								<div className="param-name-container">
									{parametersNameArray}
								</div>
							</div>
						</AntCol>
						<AntCol
							className="graph-section-col"
							xs={24}
							sm={24}
							md={15}
							lg={16}
							xl={16}
							xxl={17}
						>
							{no_data ? (
								<div className="align-center-loading">
									Failed to load data!
								</div>
							) : (
								graphSection
							)}
						</AntCol>
					</AntRow>
				</div>
			</div>
		);
	}
}
