import moment from 'moment-timezone';
import { getTasksData } from '@datoms/js-sdk';
import Backyard from '@datoms/js-utils/src/Backyard/Backyard_back';
import { catWiseParameters, catWiseSummary } from '../configs/trips-columns';
import { isValidObject } from '@datoms/js-utils/src/basic-utility';

export async function fetchTripsData(
	page = this.state.page,
	pageSize = this.state.pageSize,
	summary = true,
) {
	const {
		categoryId,
		vendorId,
		clientId,
		thingId,
		make,
		model,
		kva,
		fuel_tank_capacity,
		rated_power_factor,
		fuel_filled,
		fuel_theft,
		dg_phase,
		genset_type,
		lifetime_runhour,
		runhour,
		load_percentage,
		peak_load,
		fuel_consumption,
		energy_generated,
		tags,
		order_purpose,
		// assigned_to,
		sort_key,
		sort_order,
	} = this.state;
	const { application_id } = this.props;
	let data = {
		client_id: this.props.client_id,
		application_id: this.props.application_id,
	};
	let query_string = `?page_no=${page}&results_per_page=${pageSize}&tag_options=true&fuel_entries=true&offline_trips=true`;
	query_string += `&application_id=${this.props.application_id}`;
	query_string += `&start_after=${moment
		.unix(this.state.from_time)
		.toISOString()}`;
	query_string += `&start_before=${moment
		.unix(this.state.upto_time)
		.toISOString()}`;
	query_string += `&category_id=${categoryId}`;
	if (vendorId) {
		query_string += `&vendor_id=${vendorId}`;
	}
	if (clientId) {
		query_string += `&client_id=${clientId}`;
	}
	if (thingId) {
		query_string += `&thing_list=${thingId}`;
	}

	if (make) {
		query_string += `&thing_make=is_${make}`;
	}
	if (model) {
		query_string += `&thing_model=is_${model}`;
	}
	if (!isNaN(parseInt(kva))) {
		query_string += `&thing_kva==_${kva}`;
	}
	if (numericalFilterTest(fuel_tank_capacity) !== false) {
		query_string += `&thing_tankCapacity=${numericalFilterTest(
			fuel_tank_capacity,
		)}`;
	}
	if (numericalFilterTest(rated_power_factor) !== false) {
		query_string += `&thing_power_factor=${numericalFilterTest(
			rated_power_factor,
		)}`;
	}
	if (numericalFilterTest(fuel_filled) !== false) {
		query_string += `&fuel_filled=${numericalFilterTest(fuel_filled)}`;
	}
	if (numericalFilterTest(fuel_theft) !== false) {
		query_string += `&fuel_theft=${numericalFilterTest(fuel_theft)}`;
	}
	if (dg_phase) {
		query_string += `&dg_phase=is_${dg_phase}`;
	}
	if (!isNaN(parseInt(genset_type))) {
		query_string += `&genset_type=${genset_type}`;
	}
	if (numericalFilterTest(lifetime_runhour) !== false) {
		query_string += `&lifetime_runhour=${numericalFilterTest(
			lifetime_runhour,
		)}`;
	}
	if (numericalFilterTest(runhour) !== false) {
		const multiplier = categoryId === 45 ? 1/60 : 1;
		query_string += `&calculated_runhour=${numericalFilterTest(runhour, multiplier)}`;
	}
	if (numericalFilterTest(load_percentage) !== false) {
		query_string += `&load_percentage=${numericalFilterTest(
			load_percentage,
		)}`;
	}
	if (numericalFilterTest(peak_load) !== false) {
		query_string += `&peak_load=${numericalFilterTest(peak_load)}`;
	}
	if (numericalFilterTest(fuel_consumption) !== false) {
		query_string += `&fuel_consumption=${numericalFilterTest(
			fuel_consumption,
		)}`;
	}
	if (numericalFilterTest(energy_generated) !== false) {
		query_string += `&calculated_energy=${numericalFilterTest(
			energy_generated,
		)}`;
	}
	if (textFilterTest(tags) !== false) {
		query_string += `&tags=${textFilterTest(tags)}`;
	}
	if (this.isTestDrive) {
		// if (!isNaN(parseInt(assigned_to))) {
		// 	query_string += `&assigned_to=${assigned_to}`;
		// }
		if (order_purpose) {
			query_string += `&order_purpose=${order_purpose}`;
		}
	}
	if (catWiseParameters[categoryId].parameters) {
		query_string += `&parameters=${encodeURIComponent(
			JSON.stringify(catWiseParameters[categoryId].parameters),
		)}`;
	}
	if (summary) {
		query_string += `&trip_graph=${application_id === 12 || ![18, 67, 76].includes(parseInt(categoryId)) ? false : true}`;
		if (catWiseParameters[categoryId].summary_parameters) {
			query_string += `&summary_parameters=${encodeURIComponent(
				JSON.stringify(
					catWiseParameters[categoryId].summary_parameters,
				),
			)}`;
		}
	}
	query_string +=
		'&task_type=1&GetDetails=true&get_counts=false&get_summary=false&get_trends=false';
	if (sort_key && sort_order) {
		query_string += `&order_key=${sort_key}&order_by=${sort_order}`;
	}
	let apiData = await getTasksData(data, query_string);
	return apiData.response;
}

export async function fetchAllTripsData() {
	let page = 1;
	let pageSize = 1000;
	let fetchedItems = 0;
	let total_mission_count = 0;
	let Missions = [];
	let graph_data = [];
	let trip_summary = {};
	let tag_options = [];
	let filter_options = {};

	do {
		console.log('fetchAllTripsData loop: ', page, fetchedItems);
		// Make the API call
		const apiResponse = await this.fetchTripsData(
			page,
			pageSize,
			page === 1,
		);

		// Handle the response data
		if (page === 1) {
			total_mission_count = apiResponse.total_mission_count;
			graph_data = apiResponse.graph_data;
			trip_summary = apiResponse.trip_summary;
			tag_options = apiResponse.tag_options;
			filter_options = apiResponse.filter_options;
		}
		Missions = Missions.concat(apiResponse.Missions);
		fetchedItems += apiResponse.Missions.length;
		page++;
	} while (fetchedItems < total_mission_count);

	console.log('fetchAllTripsData result: ', {
		Missions,
		total_mission_count,
		graph_data,
		trip_summary,
	});
	return {
		Missions,
		total_mission_count,
		graph_data,
		trip_summary,
		tag_options,
		filter_options,
	};
}
export async function getTripData(download = false, getSummary = false) {
	let apiData,
		response = {};
	if (download) {
		apiData = await this.fetchAllTripsData();
	} else {
		apiData = await this.fetchTripsData(undefined, undefined, getSummary);
	}
	console.log('apiTripData 1', apiData);
	if (apiData && apiData.Missions && Array.isArray(apiData.Missions)) {
		if (!download) {
			if (!getSummary) {
				apiData.trip_summary = isValidObject(this.state.tripSummaryData)
					? this.state.tripSummaryData
					: {};
				apiData.graph_data = Array.isArray(this.state.tripGraphData)
					? this.state.tripGraphData
					: [];
			}
			const { tagOptions, tagWiseData } = getTagsOptions(
				apiData.tag_options,
			);
			this.setState({
				apiTripData: apiData.Missions,
				tripSummaryData: apiData.trip_summary,
				tripGraphData: apiData.graph_data,
				totalPage: apiData.total_mission_count,
				tagOptions: tagOptions,
				tagWiseData: tagWiseData,
				filterOptions: apiData.filter_options,
			});
		}
		response = apiData;
	}
	console.log('apiTripData res', response);
	return response;
}
function getTagsOptions(tag_options) {
	const tagOptions = [],
		tagWiseData = {};
	if (Array.isArray(tag_options)) {
		tag_options.forEach((tag) => {
			tagOptions.push({
				title: tag.tag_name,
				value: tag.tag_id,
			});
			tagWiseData[tag.tag_id] = {
				tag_name: tag.tag_name,
				tag_color: tag.tag_color,
			};
		});
	}
	return { tagOptions, tagWiseData };
}
export async function createTripsRenderData(
	download = false,
	getSummary = false,
) {
	const { application_id } = this.props;
	const { pageType, categoryId, page, pageSize, customersList } = this.state;
	console.log('categoryId_test', categoryId);
	let dataResponse = await this.getTripData(download, getSummary);
	let tripGraph = Array.isArray(dataResponse?.graph_data)
		? dataResponse.graph_data
		: [];
	let tripSummary = isValidObject(dataResponse?.trip_summary)
		? dataResponse.trip_summary
		: {};
	const { tagWiseData } = getTagsOptions(dataResponse?.tag_options);
	let missionsArray = dataResponse?.Missions ? dataResponse.Missions : [];
	let totalMissions = dataResponse?.total_mission_count
		? dataResponse.total_mission_count
		: 0;
	let fromTime = this.state.from_time;
	let uptoTime = this.state.upto_time;
	let totalThings = this.state.totalData.things;
	const customizedColumns = this.filterAndSortColumns(
		this.getTripsCatwiseColumns(),
		['trip_view', pageType + '_' + categoryId],
	);
	let headerColumns = customizedColumns.map((item) => ({
		title: item.pdf_title,
		dataIndex: item.dataIndex,
	}));
	let summaryItems = catWiseSummary[categoryId];
	let timeFormat = this.props.user_preferences?.time_format;
	let timeZone = this.props.user_preferences?.timezone;
	let client_name = this.props.client_name;
	new Backyard({
		/* eslint-disable no-unused-expressions */
		scripts: [
			'https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js',
			'https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.23/moment-timezone.min.js',
			'https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.33/moment-timezone-with-data-1970-2030.min.js',
			'https://cdn.jsdelivr.net/npm/lodash@4.17.15/lodash.min.js',
		],
		input: {
			fromTime: fromTime,
			uptoTime: uptoTime,
			headerColumns: headerColumns,
			summaryItems: summaryItems,
			timeFormat: timeFormat,
			page_no: page,
			pageSize: pageSize,
			categoryId: categoryId,
			tripGraph: tripGraph,
			tripSummary: tripSummary,
			missionsArray: missionsArray,
			totalMissions: totalMissions,
			totalThings: totalThings,
			timeZone: timeZone,
			client_name: client_name,
			application_id: application_id,
			isTestDrive: this.isTestDrive,
			tagWiseData,
			customersList,
		},
		run: function (ctx, input, cb) {
			/*Utility functions */
			ctx.moment.tz.setDefault(
				input.timeZone ? input.timeZone : ctx.moment.tz.guess(),
			);
			function TimeFormatterBackyard(timestamp, moment_string, ctx) {
				let finalMoment = '';
				if (input.timeFormat === '12_hr') {
					let momentString = moment_string.replace(
						/HH:mm:ss/g,
						'hh:mm:ss A',
					);
					finalMoment = ctx.moment
						.unix(timestamp)
						.format(momentString);
				} else {
					finalMoment = ctx.moment
						.unix(timestamp)
						.format(moment_string);
				}
				return finalMoment;
			}
			function transformWords(word) {
				// Split the word by underscores
				const words = word.split('_');

				// Capitalize the first letter of each word and convert the rest to lowercase
				const transformedWords = words.map(
					(w) => w.charAt(0).toUpperCase() + w.slice(1).toLowerCase(),
				);

				// Join the transformed words with a space
				const transformedWord = transformedWords.join(' ');

				return transformedWord;
			}
			function getFixedValue(val, unit = false) {
				return !isNaN(parseFloat(val))
					? parseFloat(val).toFixed(2) + (unit ? ' ' + unit : '')
					: '-';
			}
			function getGraphValue(val) {
				return val === 0
					? 0
					: !isNaN(parseFloat(val))
						? parseFloat(parseFloat(val).toFixed(2))
						: 0;
			}
			/*End */
			const {
				fromTime,
				uptoTime,
				headerColumns,
				summaryItems,
				page_no,
				pageSize,
				categoryId,
				tripGraph,
				tripSummary,
				missionsArray,
				totalMissions,
				totalThings,
				application_id,
				tagWiseData,
				customersList,
			} = input;
			let SummaryData = [];
			let graphSeriesData = {
				//	battery_consumed: [],
				fuel_consumption: [],
				distance: [],
				runhour: [],
				categories: [],
			};
			tripGraph.map((day) => {
				graphSeriesData.categories.push(
					TimeFormatterBackyard(day.time, 'DD MMM', ctx),
				);
				if (categoryId === 18) {
					if (day.runhour || day.fuel_consumed) {
						let runhourVal =
							getGraphValue(day.runhour) > 0
								? getGraphValue(
										getGraphValue(day.runhour) / 3600,
									)
								: 0;
						graphSeriesData.runhour.push(runhourVal);
						graphSeriesData.fuel_consumption.push(
							getGraphValue(day.fuel_consumed),
						);
					}
				}
				if (categoryId === 67) {
					if (day.distance_travelled || day.fuel_consumed) {
						graphSeriesData.distance.push(
							getGraphValue(day.distance_travelled),
						);
						graphSeriesData.fuel_consumption.push(
							getGraphValue(day.fuel_consumed),
						);
					}
				}
				if (categoryId === 76) {
					if (day.distance_travelled || day.fuel_consumed) {
						graphSeriesData.distance.push(
							getGraphValue(day.distance_travelled),
						);
						// graphSeriesData.battery_consumed.push(
						// 	getGraphValue(day.battery_consumed)
						// );
					}
				}
			});
			let downloadData = { conf: [], data: [] };
			let pdfMainHeaderOptionDate = {
				// pdf_top_line: true,
				// pdf_bottom_line: true,
				pdf_text_align: 'center',
				textColor: [255, 255, 255],
				pdf_size: 13,
				fill: {
					fill_color: [255, 133, 0],
					y_value: 15,
					top: 1,
				},
			};
			let pushObjDate = pdfMainHeaderOptionDate;
			pushObjDate['compo'] = 'Text';
			pushObjDate['props'] = {
				type: 'bold',
			};
			pushObjDate['col_props'] = {
				span: 24,
			};
			let pdfMainHeaderOption = {
				// pdf_top_line: true,
				// pdf_bottom_line: true,
				pdf_text_align: 'center',
				textColor: [255, 255, 255],
				pdf_size: 10,
				fill: {
					fill_color: [255, 133, 0],
					y_value: 13,
					top: 1,
				},
			};
			let pushObj = pdfMainHeaderOption;
			pushObj['compo'] = 'Text';
			pushObj['props'] = {
				type: 'bold',
			};
			pushObj['col_props'] = {
				span: 24,
			};
			let REPORT_TYPE = {
				text_conf: {
					props: {
						gutter: 5,
					},
					child: [
						{
							pdf_text_align: 'center',
							textColor: [255, 255, 255],
							pdf_size: 16,
							type: 'bold',
							fill: {
								fill_color: [255, 133, 0],
								y_value: 24,
								top: 8,
							},
							compo: 'Text',
							props: {
								type: 'bold',
							},
							col_props: {
								span: 24,
							},
							secStyle: {
								body: {
									font: {
										size: 20,
										bold: true,
									},
								},
							},
						},
						pushObjDate,
						pushObj,
					],
				},
				text_data: [
					{
						textData: ['Trip Report - ' + input.client_name],
					},
					{
						textData: [
							'From: ' +
								TimeFormatterBackyard(
									fromTime,
									'DD MMM YYYY, HH:mm:ss',
									ctx,
								) +
								' to ' +
								TimeFormatterBackyard(
									uptoTime,
									'DD MMM YYYY, HH:mm:ss',
									ctx,
								),
						],
					},
					{
						textData: [
							'Generated on: ' +
								TimeFormatterBackyard(
									ctx.moment().unix(),
									'DD MMM YYYY, HH:mm:ss',
									ctx,
								),
							'* All the times are in ' +
								(input.timeFormat === '12_hr' ? '12' : '24') +
								' Hours time format',
							'',
						],
					},
				],
			};
			downloadData.conf.push(REPORT_TYPE.text_conf);
			downloadData.data.push(REPORT_TYPE.text_data);
			let dataObj = [];
			let sortedFilterArrayLocalTime = missionsArray;
			if (
				sortedFilterArrayLocalTime &&
				sortedFilterArrayLocalTime.length
			) {
				let filterArrayLocalTime = [];
				sortedFilterArrayLocalTime.map(
					(sortedFilterArrayLocalTimeData) => {
						return filterArrayLocalTime.push({
							Trip_id: sortedFilterArrayLocalTimeData.Id,
							Name: sortedFilterArrayLocalTimeData.Name,
							StartDate: ctx
								.moment(
									sortedFilterArrayLocalTimeData.StartDate,
									ctx.moment.ISO_8601,
								)
								.format('DD MMM YYYY'),
							StartTime: TimeFormatterBackyard(
								ctx
									.moment(
										sortedFilterArrayLocalTimeData.StartDate,
										ctx.moment.ISO_8601,
									)
									.unix(),
								'DD MMM, HH:mm:ss',
								ctx,
							),
							EndDate:
								sortedFilterArrayLocalTimeData.EndDate === 'NA'
									? '-'
									: ctx
											.moment(
												sortedFilterArrayLocalTimeData.EndDate,
												ctx.moment.ISO_8601,
											)
											.format('DD MMM YYYY'),
							EndTime:
								sortedFilterArrayLocalTimeData.EndDate === 'NA'
									? '-'
									: TimeFormatterBackyard(
											ctx
												.moment(
													sortedFilterArrayLocalTimeData.EndDate,
													ctx.moment.ISO_8601,
												)
												.unix(),
											'DD MMM, HH:mm:ss',
											ctx,
										),
							Devices: sortedFilterArrayLocalTimeData.Devices,
							Details: sortedFilterArrayLocalTimeData.Details,
							OrderPurpose:
								sortedFilterArrayLocalTimeData.order_purpose,
							AssignedTo:
								sortedFilterArrayLocalTimeData.assigned_to,
							tags: Array.isArray(
								sortedFilterArrayLocalTimeData.tags,
							)
								? sortedFilterArrayLocalTimeData.tags
								: [],
							thing_details: sortedFilterArrayLocalTimeData.thing_details,
						});
					},
				);
				filterArrayLocalTime.map((trip, index) => {
					const findThingList = totalThings.find(
						(curr_thing) =>
							Array.isArray(trip.Devices) &&
							trip.Devices.includes(curr_thing.id),
					);
					const findClient = customersList?.find(
						(curr_cus) =>
							curr_cus.id === findThingList?.customer_id,
					);
					const findVendor = customersList?.find(
						(curr_ven) => curr_ven.id === findThingList?.vendor_id,
					);
					console.log('TripData: ', findThingList, trip);
					if (categoryId === 18) {
						let runhour = '-';
						if (
							trip.Details?.aggregate_data?.calculated_runhour
								?.sum
						) {
							let time = Number(
								trip.Details.aggregate_data.calculated_runhour
									.sum,
							);
							let h =
								Math.floor(time / 3600) < 10
									? '0' + Math.floor(time / 3600)
									: Math.floor(time / 3600);
							let m =
								Math.floor((time % 3600) / 60) < 10
									? '0' + Math.floor((time % 3600) / 60)
									: Math.floor((time % 3600) / 60);
							let s =
								Math.floor((time % 3600) % 60) < 10
									? '0' + Math.floor((time % 3600) % 60)
									: Math.floor((time % 3600) % 60);

							runhour = h + ' : ' + m + ' : ' + s;
						}
						let serial_number =
							totalMissions - pageSize * (page_no - 1) - index;
						dataObj.push({
							trip_id: trip.Trip_id,
							sl_no: '#' + serial_number,
							thing_name: findThingList?.name,
							customer_name: findClient?.name,
							partner_name: findVendor?.name,
							date: trip.StartDate,
							trips: `Trip ${
								filterArrayLocalTime.length - index
							}`,
							on_time: trip.StartTime,
							off_time: trip.EndTime,
							fuel_cons: getFixedValue(
								trip.Details?.aggregate_data?.fuel_consumption
									?.sum,
							),
							start_fuel: getFixedValue(
								trip.Details?.aggregate_data?.fuel_litre
									?.initial,
							),
							end_fuel: getFixedValue(
								trip.Details?.aggregate_data?.fuel_litre
									?.snapshot,
							),
							fuel_cons_per_hr: getFixedValue(
								trip.Details?.aggregate_data
									?.fuel_consumption_p_calculated_runhour
									?.snapshot,
							),
							runhour: runhour,
							energy_generated: getFixedValue(
								trip.Details?.aggregate_data?.calculated_energy
									?.sum,
							),
							fuel_cons_per_kwh: getFixedValue(
								trip.Details?.aggregate_data
									?.fuel_consumption_p_calculated_energy
									?.snapshot,
							),
							enrg_per_litre: getFixedValue(
								trip.Details?.aggregate_data
									?.calculated_energy_p_fuel_consumption
									?.snapshot,
							),
							load_percentage: getFixedValue(
								trip.Details?.aggregate_data?.load_percentage
									?.avg,
							),
							peak_load: getFixedValue(
								trip.Details?.aggregate_data?.load_percentage
									?.max,
							),
							fuel_filled: getFixedValue(
								trip.Details?.aggregate_data?.fuel_filled?.sum,
							),
							fuel_theft: getFixedValue(
								trip.Details?.aggregate_data?.fuel_theft?.sum,
							),
							exp_fuel_consumption: getFixedValue(
								trip.Details?.aggregate_data
									?.th_fuel_consumption?.sum,
							),
							exp_fuel_consumption_per_hour: getFixedValue(
								trip.Details?.aggregate_data
									?.th_fuel_consumption_p_calculated_runhour
									?.snapshot,
							),
							tags: trip.tags
								.map((tag_id) => {
									if (tagWiseData?.[tag_id]?.tag_name) {
										return tagWiseData[tag_id].tag_name;
									} else return tag_id;
								})
								.join(', '),
							tag_ids: trip.tags,
							make: trip.thing_details?.make || '-',
							model: trip.thing_details?.model || '-',
							kva: trip.thing_details?.kva || '-',
							capacity: trip.thing_details?.capacity || '-',
							genset_type: trip.thing_details?.genset_type || '-',
						});
					} else {
						let runhour = undefined;
						if (
							trip.Details?.aggregate_data?.calculated_runhour
								?.sum
						) {
							let time = Number(
								trip.Details.aggregate_data.calculated_runhour
									.sum,
							);
							let h =
								Math.floor(time / 3600) < 10
									? '0' + Math.floor(time / 3600)
									: Math.floor(time / 3600);
							let m =
								Math.floor((time % 3600) / 60) < 10
									? '0' + Math.floor((time % 3600) / 60)
									: Math.floor((time % 3600) / 60);
							let s =
								Math.floor((time % 3600) % 60) < 10
									? '0' + Math.floor((time % 3600) % 60)
									: Math.floor((time % 3600) % 60);

							runhour = h + ' : ' + m + ' : ' + s;
						}
						let serial_number =
							totalMissions - pageSize * (page_no - 1) - index;
						dataObj.push({
							trip_id: trip.Trip_id,
							sl_no: '#' + serial_number,
							thing_name: findThingList?.name,
							customer_name: findClient?.name,
							partner_name: findVendor?.name,
							date: trip.StartDate,
							on_time: trip.StartTime,
							off_time: trip.EndTime,
							fuel_cons: getFixedValue(
								trip.Details?.aggregate_data?.fuel_consumption
									?.sum,
							),
							start_fuel: getFixedValue(
								trip.Details?.aggregate_data?.fuel_litre
									?.initial,
							),
							end_fuel: getFixedValue(
								trip.Details?.aggregate_data?.fuel_litre
									?.snapshot,
							),
							distance: getFixedValue(
								trip.Details?.aggregate_data?.distance_travelled
									?.sum,
							),
							mileage: getFixedValue(
								trip.Details?.aggregate_data
									?.distance_travelled_p_fuel_consumption
									?.snapshot,
							),
							runhour: runhour,
							speed: getFixedValue(
								trip.Details?.aggregate_data?.speed?.avg,
							),
							vbat_consumed: getFixedValue(
								trip.Details?.aggregate_data?.vbat_consumed
									?.sum,
							),
							top_speed: getFixedValue(
								trip.Details?.aggregate_data?.speed?.max,
							),
							tags: input.isTestDrive
								? trip.OrderPurpose
									? transformWords(trip.OrderPurpose)
									: '-'
								: trip.tags
										.map((tag_id) => {
											if (
												tagWiseData?.[tag_id]?.tag_name
											) {
												return tagWiseData[tag_id]
													.tag_name;
											} else return tag_id;
										})
										.join(', '),
							tag_ids: trip.tags,
							order_purpose: trip.OrderPurpose,
							assigned_to: trip.AssignedTo,
							watt:  getFixedValue(
								trip.Details?.aggregate_data?.watt?.avg,
							),
							energy_generated: getFixedValue(
								trip.Details?.aggregate_data?.calculated_energy
									?.sum,
							),
							mains_energy_generated: getFixedValue(
								trip.Details?.aggregate_data?.calculated_mains_energy
									?.sum,
							),
							mt_power: getFixedValue(
								trip.Details?.aggregate_data?.mt_power
									?.avg,
							),
							mcurr: getFixedValue(
								trip.Details?.aggregate_data?.mcurr?.avg,
							),
							mvol: getFixedValue(
								trip.Details?.aggregate_data?.mvol?.avg,
							),
							mvolt_p: getFixedValue(
								trip.Details?.aggregate_data?.mvolt_p?.avg,
							),
							mt_pf: getFixedValue(
								trip.Details?.aggregate_data?.mt_pf?.avg,
							),
							mfreq: getFixedValue(
								trip.Details?.aggregate_data?.mfreq?.avg,
							),
							calculated_mr_energy: getFixedValue(
								trip.Details?.aggregate_data?.calculated_mr_energy?.sum
							),
							calculated_ma_energy: getFixedValue(
								trip.Details?.aggregate_data?.calculated_ma_energy?.sum
							),
							mtr_pow: getFixedValue(
								trip.Details?.aggregate_data?.mtr_pow?.avg,
							),
							rpm: getFixedValue(
								trip.Details?.aggregate_data?.rpm?.avg,
							),
							hz: getFixedValue(
								trip.Details?.aggregate_data?.hz?.avg,
							),
							load_percentage: getFixedValue(
								trip.Details?.aggregate_data?.load_percentage?.avg,
							),
						});
					}
				});
			}
			let pageConfig = {
				//	pdf_force_new_page: thing_ind > 0 ? true : false,
			};
			let textPush = pageConfig;
			textPush['compo'] = 'Text';
			textPush['props'] = {
				type: 'bold',
			};

			textPush['col_props'] = {
				span: 24,
			};
			textPush['pdf_size'] = 13;

			let { text_conf, text_data } = {
				text_conf: {
					props: {
						gutter: 10,
					},
					child: [textPush],
				},
				text_data: [
					{
						textData: [''], //[things.name],
					},
				],
			};
			if (application_id !== 12 && [18, 67, 76].includes(parseInt(categoryId))) {
				downloadData.conf.push(text_conf);
				downloadData.data.push(text_data);
				downloadData.conf.push({
					props: {
						gutter: 10,
						style: {},
						className: 'rowGraph',
					},
					child: [
						{
							compo: 'Graph',
							widget: '',
							classname: 'graph-1',
							props: {
								id: 'graph-id-1',
							},
							col_props: {
								span: 24,
							},
							pdf_force_new_page: true,
							// datatype: {
							// 	'xAxis.categories':
							// 		'datetime::HH:MM',
							// 	'series.data':
							// 		'number::1',
							// },
						},
					],
				});
			}

			const graphSeries = [];
			if (categoryId === 18) {
				graphSeries.push({
					name: 'Runhour (Hr)',
					type: 'column',
					data: graphSeriesData.runhour,
					color: '#FB855A',
					pointPadding: 0,
					yAxis: 0,
				});
			} else {
				graphSeries.push({
					name: 'Distance (Km)',
					type: 'column',
					data: graphSeriesData.distance,
					color: '#FB855A',
					pointPadding: 0,
					yAxis: 0,
				});
			}
			if (categoryId === 76) {
				// graphSeries.push({
				// 	name: 'Battery Consumed (%)',
				// 	type: 'column',
				// 	data: graphSeriesData.battery_consumed,
				// 	color: '#4DBFFF',
				// 	pointPadding: 0,
				// 	yAxis: 1,
				// });
			} else {
				graphSeries.push({
					name: 'Fuel Consumption (L)',
					type: 'column',
					data: graphSeriesData.fuel_consumption,
					color: '#4DBFFF',
					pointPadding: 0,
					yAxis: 1,
				});
			}
			if (application_id !== 12 && [18, 67, 76].includes(parseInt(categoryId))) {
				downloadData.data.push([
					{
						chart: {
							type: 'column',
							width: 1280,
							height: 300,
						},
						title: {
							useHTML: true,
							text: '',
							style: {
								color: '#232323',
							},
						},
						subtitle: {
							text: '',
							useHTML: true,
						},
						xAxis: {
							type: 'datetime',
							crosshair: false,
							title: {
								text: '',
								useHTML: true,
								style: {
									color: 'rgb(118, 134, 161)',
									fontSize: '11px',
								},
							},
							labels: {
								style: {
									color: 'rgba(118, 134, 161, 0.8)',
									fontSize: '10px',
								},
							},
							categories: graphSeriesData.categories,
						},
						yAxis: [
							{
								title: {
									text:
										categoryId === 18
											? 'Runhour (Hr)'
											: 'Distance (Km)',
								},
								//type: 'logarithmic'
							},
							{
								title: {
									text: 'Fuel Consumption (L)',
								},
								opposite: true,
								//type: 'logarithmic'
							},
						],
						legend: {
							enabled: true,
							itemStyle: {
								fontSize: '10px',
							},
						},
						plotOptions: {
							area: {
								marker: {
									enabled: false,
									symbol: 'circle',
									radius: 2,
									states: {
										hover: {
											enabled: true,
										},
									},
								},
							},
							arearange: {
								marker: {
									enabled: false,
									symbol: 'circle',
									radius: 1,
									states: {
										hover: {
											enabled: true,
										},
									},
								},
							},
							line: {
								marker: {
									enabled: true,
									symbol: 'circle',
									radius: 1,
									states: {
										hover: {
											enabled: true,
										},
									},
								},
							},
							spline: {
								marker: {
									enabled: true,
									symbol: 'circle',
									radius: 2,
									states: {
										hover: {
											enabled: true,
										},
									},
								},
							},
							column: {
								pointPadding: 0.2,
								borderWidth: 0,
							},
							series: {
								fillOpacity: 0.3,
								animation: false,
							},
						},
						tooltip: {
							headerFormat:
								'<span style="font-size:10px">{point.key}</span><table>',
							pointFormat:
								'<tr><td style="color:{series.color};padding:0">{series.name}: </td><td style="padding:0"><b>&nbsp;{point.y} ',
							footerFormat: '</table>',
							valueDecimals: 20,
							shared: true,
							useHTML: true,
							backgroundColor: '#fff',
						},
						exporting: {
							enabled: false,
						},
						credits: {
							enabled: false,
						},
						responsive: {
							rules: [
								{
									condition: {
										maxWidth: 600,
									},
									chartOptions: {
										legend: {
											layout: 'horizontal',
											align: 'center',
											verticalAlign: 'bottom',
										},
									},
								},
							],
						},
						timezone: input.timeZone,
						series: graphSeries,
					},
				]);
			}
			if (dataObj && dataObj.length) {
				let summaryViewHeaderConf = {
					pdf_force_new_page: application_id !== 12 && [18, 67, 76].includes(parseInt(categoryId)) ? true : false,
				};
				let summaryText = summaryViewHeaderConf;
				summaryText['compo'] = 'Text';
				summaryText['props'] = {
					type: 'bold',
				};

				summaryText['col_props'] = {
					span: 24,
				};
				summaryText['pdf_size'] = 12;

				let { summary_text_conf, summary_text_data } = {
					summary_text_conf: {
						props: {
							gutter: 10,
						},
						child: [summaryText],
					},
					summary_text_data: [
						{
							textData: ['Summary'],
						},
					],
				};

				downloadData.conf.push(summary_text_conf);
				downloadData.data.push(summary_text_data);
				// tripSummary['total_trips'] = totalMissions;
				summaryItems.map((item) => {
					if (item.key === 'total_runhour') {
						let totalRnhr = !isNaN(parseInt(tripSummary[item.key]))
							? tripSummary[item.key]
							: '-';
						SummaryData.push({
							parameter: item.title,
							value:
								totalRnhr === '-'
									? totalRnhr
									: (Math.floor(totalRnhr / 3600) < 10
											? '0' + Math.floor(totalRnhr / 3600)
											: Math.floor(totalRnhr / 3600)) +
										':' +
										(Math.floor((totalRnhr % 3600) / 60) <
										10
											? '0' +
												Math.floor(
													(totalRnhr % 3600) / 60,
												)
											: Math.floor(
													(totalRnhr % 3600) / 60,
												)) +
										':' +
										(Math.floor(totalRnhr % 60) < 10
											? '0' + Math.floor(totalRnhr % 60)
											: Math.floor(totalRnhr % 60)) +
										' Hrs',
						});
					} else {
						SummaryData.push({
							parameter: item.title,
							value:
								item.key === 'total_trips'
									? tripSummary[item.key]
									: getFixedValue(
											tripSummary[item.key],
											item.unit,
										),
						});
					}
				});
				downloadData.conf.push({
					props: {
						gutter: 10,
						style: {},
						className: 'tableRow',
					},
					child: [
						{
							compo: 'Table',
							widget: '',
							classname: 'tab-1',
							table_new_page: true,
							props: {
								columns: [
									{
										title: 'Parameter',
										dataIndex: 'parameter',
									},
									{
										title: 'Value',
										dataIndex: 'value',
									},
								],
								headerFont: 13,
								size: 'small',
								tabRadius: 0,
								horizontalScroll: true,
								shadow: false,
								breakPoint: 1000,
								breakPoint2: 500,
								largeTable: true,
								mediumTable: false,
								smallTable: false,
							},
							col_props: {
								span: 24,
							},
							pdf_width: 50,
							pdf_table_break: {
								col_no: 6,
								row_no: 12,
							},
						},
					],
				});
				downloadData.data.push([SummaryData]);
				let detailedViewHeaderConf = {
					pdf_force_new_page: true,
				};
				let textPush = detailedViewHeaderConf;
				textPush['compo'] = 'Text';
				textPush['props'] = {
					type: 'bold',
				};

				textPush['col_props'] = {
					span: 24,
				};
				textPush['pdf_size'] = 12;

				let { detailed_text_conf, detailed_text_data } = {
					detailed_text_conf: {
						props: {
							gutter: 10,
						},
						child: [textPush],
					},
					detailed_text_data: [
						{
							textData: ['Detailed View'],
						},
					],
				};

				downloadData.conf.push(detailed_text_conf);
				downloadData.data.push(detailed_text_data);
				downloadData.conf.push({
					props: {
						gutter: 10,
						style: {},
						className: 'tableRow',
					},
					child: [
						{
							compo: 'Table',
							widget: '',
							classname: 'tab-1',
							table_new_page: true,
							hellipRow: true,
							props: {
								columns: headerColumns,
								headerFont: 13,
								size: 'small',
								tabRadius: 0,
								horizontalScroll: true,
								shadow: false,
								breakPoint: 1000,
								breakPoint2: 500,
								largeTable: true,
								mediumTable: false,
								smallTable: false,
							},
							col_props: {
								span: 24,
							},
							pdf_width: 50,
							pdf_table_break: {
								col_no: 15,
								row_no: 20,
							},
						},
					],
				});
				downloadData.data.push([dataObj]);
			} else {
				let noDataConfig = {
					pdf_force_new_page: false,
				};
				let textPush = noDataConfig;
				textPush['compo'] = 'Text';
				textPush['props'] = {
					type: 'bold',
				};

				textPush['col_props'] = {
					span: 24,
				};
				textPush['pdf_size'] = 11;
				let { no_trip_text_conf, no_trip_text_data } = {
					no_trip_text_conf: {
						props: {
							gutter: 10,
						},
						child: [textPush],
					},
					no_trip_text_data: [
						{
							textData: ['No Trip Data Found'],
						},
					],
				};

				downloadData.conf.push(no_trip_text_conf);
				downloadData.data.push(no_trip_text_data);
			}
			// 	});
			// }
			downloadData.file_name = 'Trip Report';
			cb({
				dataObj: dataObj,
				headerColumns: headerColumns,
				SummaryData: SummaryData,
				graphSeries: graphSeries,
				graphSeriesData: graphSeriesData,
				downloadData: downloadData,
			});
		},
		cb: (value) => {
			if (download) {
				this.setState(
					{
						downloadData: value.downloadData,
					},
					() => this.getDownloadRender(),
				);
			} else {
				this.setState({
					tableDataSource: value.dataObj,
					summaryData: value.SummaryData,
					graphSeries: value.graphSeries,
					graphSeriesData: value.graphSeriesData,
					bodyLoading: false,
					tableLoading: false,
				});
			}
		},
	});
}

/*Utilities */
function isValidKva(val) {
	let inputString = val;
	if (!isNaN(parseInt(val))) {
		inputString = '=' + val;
	}
	const symbolRegex = /^[<>=]+/;
	const numberRegex = /\d+$/;

	const symbolMatch = inputString && inputString.match(symbolRegex);
	const numberMatch = inputString && inputString.match(numberRegex);

	if (symbolMatch && numberMatch) {
		const symbol = symbolMatch[0];
		const number = parseInt(numberMatch[0]);

		return !isNaN(number) ? inputString : false;
	}

	return false;
}

function numericalFilterTest(inputString, multiplier = 1) {
    if (['empty', 'notEmpty'].includes(inputString)) {
        return inputString;
    }
    if (
        !inputString ||
        (inputString.includes('between') && !inputString.includes('-')) ||
        (!inputString.includes('between') && inputString.includes('-'))
    ) {
        return false;
    }

    const pattern =
        /^(=|<=|>=|<|>|between)_(((\d+(\.\d+)?)-(\d+(\.\d+)?)|\d+(\.\d+)?))$/;

    if (!pattern.test(inputString)) {
        return false;
    }

	if(multiplier === 1) {
		return pattern.test(inputString)
			? inputString.includes('between')
				? inputString.replace('-', ',')
				: inputString
			: false;
	}

    if (inputString.includes('between')) {
        // Extract and scale the range numbers
        const match = inputString.match(/between_(\d+(\.\d+)?)-(\d+(\.\d+)?)/);
        if (match) {
            const lowerBound = parseFloat(match[1]) * multiplier;
            const upperBound = parseFloat(match[3]) * multiplier;
            return `between_${lowerBound},${upperBound}`;
        }
    } else {
        // Extract and scale the single number
        const match = inputString.match(/^(=|<=|>=|<|>)_(\d+(\.\d+)?)/);
        if (match) {
            const operator = match[1];
            const number = parseFloat(match[2]) * multiplier;
            return `${operator}_${number}`;
        }
    }

    return false;
}

function textFilterTest(inputString) {
	if(typeof inputString !== 'string') {
		return false;
	}
	if (inputString.includes('empty')) {
		return 'empty';
	}
	if (inputString.includes('notEmpty')) {
		return 'notEmpty';
	}
	const pattern = /^(between|contains|notContain|is|isNot)_.+$/;

	return pattern.test(inputString) ? inputString : false;
}
