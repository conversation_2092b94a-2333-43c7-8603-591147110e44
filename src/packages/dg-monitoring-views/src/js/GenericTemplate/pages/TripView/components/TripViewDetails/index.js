import React from 'react';
import queryString from 'query-string';
import moment from 'moment-timezone';
import { getThingsData } from '@datoms/js-sdk';
import { reverseGeocode } from '@datoms/js-utils/src/map-functions';
import { TimeFormatter } from '@datoms/js-utils/src/TimeFormatting.js';
import ImageComponent from '@datoms/react-components/src/components/ImageComponent';
import AntTimeline from '@datoms/react-components/src/components/AntTimeline';
import AntTimelineItem from '@datoms/react-components/src/components/AntTimelineItem';
import Loading from '@datoms/react-components/src/components/Loading';
import AntRow from '@datoms/react-components/src/components/AntRow';
import AntCol from '@datoms/react-components/src/components/AntCol';
import TableList from '@datoms/react-components/src/components/TableList';
import AntDivider from '@datoms/react-components/src/components/AntDivider';
import ReportController from '@datoms/react-components/src/components/ReportController';
import GraphHighcharts from '@datoms/react-components/src/components/GraphHighcharts';
import AntButton from '@datoms/react-components/src/components/AntButton';
import _filter from 'lodash/filter';
import _find from 'lodash/find';
import _uniqBy from 'lodash/uniqBy';
import GoogleMapComponent from '../../../../../Fleet/js/components/MapComponents/GoogleMapComponent';
import { getSelectedparameterDataWithTimestamp } from '../../../../../data_handling/ParameterDataManipulation';
import GraphObjectData from '../../../../../configuration/GraphObjectData';
import './trip-details.less';
import Backyard from '@datoms/js-utils/src/Backyard/Backyard_back';
import CloseOutlined from '@ant-design/icons/CloseOutlined';
import CloseCircleOutlined from '@ant-design/icons/CloseCircleOutlined';
import ArrowLeftOutlined from '@ant-design/icons/ArrowLeftOutlined';
import DownloadOutlined from '@ant-design/icons/DownloadOutlined';
import { filterDginIot } from '../../../../../data_handling/DGinIot';
import { isValidObject } from '@datoms/js-utils/src/basic-utility';
import TripParamTrend from '../TripParamTrend';

export default class TripViewDetails extends React.Component {
	downloadModalRef = React.createRef();
	invisibleReportRef = React.createRef();
	customizeDrawerRef = React.createRef();
	constructor(props) {
		GraphObjectData.graph_data.config.timezone =
			props.user_preferences.timezone;
		super(props);
		this.parsed = queryString.parse(props.location.search);
		this.state = {
			//	trip_no: tripNo,
			graph_object_data: GraphObjectData,
			//	trip_id: tripId,
			thing_id: parseInt(props.tripDetails[0].Devices[0]),
			start_time: moment(
				props.tripDetails[0].StartDate,
				moment.ISO_8601,
			).unix(),
			end_time: moment(
				props.tripDetails[0].EndDate,
				moment.ISO_8601,
			).unix(),
			loading: true,
		};
		this.onExportReady = this.onExportReady(this);
	}

	onExportReady() {
		let { autoDownloadFormat } = this.props;
		if (autoDownloadFormat) {
			this.downloadCallback(autoDownloadFormat);
		}
	}

	downloadCallback() {
		if (this.state.downloadData) {
			this.invisibleReportRef.current.exportPDF({
				header: {
					left: {
						text: '',
						fontType: 'italics',
					},
					right: {
						text: this.props.client_name,
					},
				},
			});
		}
	}

	async apiFetch() {
		let totalData = this.props.totalData;
		totalData = filterDginIot.bind(this)(totalData, 'reports');
		// let filteredThing = _filter(totalData.things, function (o) {
		// 	return o.category === 67 || o.category == 76;
		// });
		// totalData['things'] = filteredThing;
		let currentThing = totalData.things.find(
			(thing) => thing.id === this.state.thing_id,
		);
		let isEv = currentThing && currentThing.category === 76;
		let dataPacketRaw = {
			data_type: 'raw',
			aggregation_period: 0,
			parameters: ['fuel', 'speed', 'lat', 'long'],
			parameter_attributes: [],
			things: [parseInt(this.state.thing_id)],
			from_time: parseInt(this.state.start_time),
			upto_time: parseInt(this.state.end_time),
		};
		let rawData = {};
		if (this.props.showMap) {
			rawData = await getThingsData(
				dataPacketRaw,
				this.props.client_id,
				this.props.application_id,
			);
		}
		let response = this.props.tripDetails;
		let latLngData = [],
			latLngDataWithTime = [];
		let addressArray = [];
		if (rawData.status === 'success') {
			let movingDgRawData = this.convertLatLng(rawData.data);
			latLngData = movingDgRawData.modifiedData;
			latLngDataWithTime = movingDgRawData.modifiedDataWithTime;
		}
		if (
			latLngDataWithTime.length &&
			this.splitLocationArray(latLngDataWithTime) &&
			this.splitLocationArray(latLngDataWithTime).length
		) {
			for (const latlng of this.splitLocationArray(latLngDataWithTime)) {
				let address = await reverseGeocode(latlng);
				addressArray.push({
					lat: latlng.lat,
					lng: latlng.lng,
					address: address,
					time: latlng.time,
				});
			}
		}
		let thingrawParamData = '';
		if (rawData.status === 'success') {
			thingrawParamData = getSelectedparameterDataWithTimestamp(
				rawData.data,
				[parseInt(this.state.thing_id)],
				['fuel', 'speed'],
				this.state.start_time,
				this.state.end_time,
			);
		}
		this.setState(
			{
				total_data: totalData,
				latLngData: latLngData,
				latLngDataWithTime: latLngDataWithTime,
				latLngDataWithAddress: addressArray,
				task_response: response,
				raw_data: rawData,
				raw_graph: thingrawParamData,
				isEv: isEv,
				currentThing: currentThing,
			},
			() => {
				this.getgraphdata();
			},
		);
	}

	getgraphdata() {
		let fuelSeriesData = [],
			speedSeriesData = [];
		if (
			this.state.raw_graph &&
			this.state.raw_graph[this.state.thing_id] &&
			this.state.raw_graph[this.state.thing_id]['fuel'] &&
			this.state.raw_graph[this.state.thing_id]['fuel'].length
		) {
			this.state.raw_graph[this.state.thing_id]['fuel'].map(
				(data, ind) => {
					fuelSeriesData.push([data[0], data[1]]);
				},
			);
		}
		if (
			this.state.raw_graph &&
			this.state.raw_graph[this.state.thing_id] &&
			this.state.raw_graph[this.state.thing_id]['speed'] &&
			this.state.raw_graph[this.state.thing_id]['speed'].length
		) {
			this.state.raw_graph[this.state.thing_id]['speed'].map(
				(data, ind) => {
					speedSeriesData.push([data[0], data[1]]);
				},
			);
		}
		this.setState({
			fuelSeriesData: fuelSeriesData,
			speedSeriesData: speedSeriesData,
		});
	}

	graphRenderData() {
		let findThings = _find(this.state.total_data.things, {
			id: parseInt(this.state.thing_id),
		});
		let fuelGraph = JSON.parse(
			JSON.stringify(this.state.graph_object_data),
		);
		fuelGraph.graph_data.config.yAxis.title.text = 'Fuel Level (%)';
		fuelGraph.graph_data.config.xAxis.title.text = 'Date & time';
		fuelGraph.graph_data.series_data = [
			{
				name: _find(findThings.parameters, { key: 'fuel' })
					? _find(findThings.parameters, { key: 'fuel' }).name
					: '',
				type: 'area',
				data:
					this.state.fuelSeriesData &&
					this.state.fuelSeriesData.length
						? this.state.fuelSeriesData
						: [],
				color: '#FF8500',
				tooltip: {
					pointFormatter: function () {
						var point = this;
						return (
							'<div></div> ' +
							'<span style="color:' +
							point.color +
							'">' +
							point.series.name +
							'</span>: <b>' +
							parseFloat(point.y).toFixed(2) +
							' ' +
							'%'
							// _find(findThings.parameters, {
							// 	key: 'fuel',
							// }).unit
						);
					},
				},
			},
		];
		let speedGraph = JSON.parse(
			JSON.stringify(this.state.graph_object_data),
		);
		speedGraph.graph_data.config.yAxis.title.text = 'Speed (km/hr)';
		speedGraph.graph_data.config.xAxis.title.text = 'Date & time';
		speedGraph.graph_data.series_data = [
			{
				name: _find(findThings.parameters, { key: 'speed' })
					? _find(findThings.parameters, { key: 'speed' }).name
					: '',
				type: 'area',
				data:
					this.state.speedSeriesData &&
					this.state.speedSeriesData.length
						? this.state.speedSeriesData
						: [],
				color: '#35D2DD',
				tooltip: {
					pointFormatter: function () {
						var point = this;
						return (
							'<div></div> ' +
							'<span style="color:' +
							point.color +
							'">' +
							point.series.name +
							'</span>: <b>' +
							parseFloat(point.y).toFixed(2) +
							' ' +
							'Km/Hr'
							// _find(findThings.parameters, {
							// 	key: 'speed',
							// }).unit
						);
					},
				},
			},
		];
		return {
			fuel_graph: fuelGraph,
			speed_graph: speedGraph,
		};
	}

	async componentDidMount() {
		await this.apiFetch();
		this.getDownloadData();
	}

	splitLocationArray(points) {
		if (points.length <= 50) {
			return this.getPoints(points, 5);
		}
		return this.getPoints(points, 10);
	}

	getPoints(points, maxPoints) {
		const interval = Math.ceil(points.length / (maxPoints - 1));
		const newPoints = [];
		for (let i = 0; i < points.length - 1; i += interval) {
			newPoints.push(points[i]);
		}
		newPoints.push(points[points.length - 1]);
		return newPoints;
	}

	onGoogleApiLoaded = (map) => {
		this.setState({ myMap: map });
	};

	zoomOnPolyline(latlngArray) {
		let mapDiv = document.getElementById('map_id');
		console.log('oiuytre', document.getElementById('map_id'));
		if (mapDiv) {
			let offsetFactor =
				window.innerWidth > 1440
					? 554 + 380
					: window.innerWidth > 1024
						? 380 + 444
						: 0;
			// if (window.innerWidth <= 1440) {
			// 	offsetFactor = 380 + 444;
			// } else if (window.innerWidth <= 1024) {
			// 	offsetFactor = 0;
			// }
			let heightFactor = window.innerWidth > 1024 ? 50 : 0;
			let mapDim = {
				height: mapDiv.clientHeight - heightFactor,
				width: mapDiv.clientWidth, //- offsetFactor,
			};
			console.log('mapDim: ', mapDim, mapDiv.clientWidth, offsetFactor);
			let bounds = this.createBoundsForPolyline(latlngArray);
			let zoomLevel = bounds
				? this.getBoundsZoomLevel(bounds, mapDim)
				: 0;
			let centerValue = bounds
				? bounds.getCenter()
				: new window.google.maps.LatLng(0, 0);
			this.setState(
				{
					zoomLevel: zoomLevel,
					centerValue: centerValue,
				},
				() => {
					// if (window.innerWidth > 1024)
					// 	this.state.myMap.panBy(offsetFactor / 2 + 22, 0);
				},
			);
		}
	}

	getBoundsZoomLevel(bounds, mapDim) {
		let WORLD_DIM = { height: 256, width: 256 };
		let ZOOM_MAX = 21;

		function latRad(lat) {
			let sin = Math.sin((lat * Math.PI) / 180);
			let radX2 = Math.log((1 + sin) / (1 - sin)) / 2;
			return Math.max(Math.min(radX2, Math.PI), -Math.PI) / 2;
		}

		function zoom(mapPx, worldPx, fraction) {
			return Math.floor(Math.log(mapPx / worldPx / fraction) / Math.LN2);
		}

		let ne = bounds.getNorthEast();
		let sw = bounds.getSouthWest();

		let latFraction = (latRad(ne.lat()) - latRad(sw.lat())) / Math.PI;

		let lngDiff = ne.lng() - sw.lng();
		let lngFraction = (lngDiff < 0 ? lngDiff + 360 : lngDiff) / 360;

		let latZoom = zoom(mapDim.height, WORLD_DIM.height, latFraction);
		let lngZoom = zoom(mapDim.width, WORLD_DIM.width, lngFraction);

		return Math.min(latZoom, lngZoom, ZOOM_MAX);
	}

	getTripTableData() {
		// let columns = [];
		// if (this.state.isEv) {
		// 	columns = [
		// 		// {
		// 		// 	title: 'Distance(Km)',
		// 		// 	dataIndex: 'distance',
		// 		// },
		// 		// {
		// 		// 	title: 'Runhour(HH:MM:SS)',
		// 		// 	dataIndex: 'runhour',
		// 		// },
		// 		// {
		// 		// 	title: 'Average Speed (Km/Hr)',
		// 		// 	dataIndex: 'speed',
		// 		// },
		// 		// {
		// 		// 	title: 'Battery Consumed (%)',
		// 		// 	dataIndex: 'vbat_consumed',
		// 		// },
		// 		{
		// 			title: 'Total Distance',
		// 			dataIndex: 'distance',
		// 		},
		// 		{
		// 			title: 'Total Runhour',
		// 			dataIndex: 'runhour',
		// 		},
		// 		{
		// 			title: 'Average Speed',
		// 			dataIndex: 'speed',
		// 		},
		// 		{
		// 			title: 'Battery Consumed',
		// 			dataIndex: 'vbat_consumed',
		// 		},
		// 	];
		// } else {
		// 	columns = [
		// 		// {
		// 		// 	title: 'Distance(Km)',
		// 		// 	dataIndex: 'distance',
		// 		// },
		// 		// {
		// 		// 	title: 'Runhour(HH:MM:SS)',
		// 		// 	dataIndex: 'runhour',
		// 		// },
		// 		// {
		// 		// 	title: 'Fuel Level Before Trip(L)',
		// 		// 	dataIndex: 'start_fuel',
		// 		// },
		// 		// {
		// 		// 	title: 'Fuel Consumed (L)',
		// 		// 	dataIndex: 'fuel_cons',
		// 		// },
		// 		// {
		// 		// 	title: 'Fuel Level After Trip(L)',
		// 		// 	dataIndex: 'end_fuel',
		// 		// },
		// 		// {
		// 		// 	title: 'Mileage(L/Km)',
		// 		// 	dataIndex: 'mileage',
		// 		// },
		// 		// {
		// 		// 	title: 'Average Speed (Km/Hr)',
		// 		// 	dataIndex: 'speed',
		// 		// },
		// 		{
		// 			title: 'Total Distance',
		// 			dataIndex: 'distance',
		// 		},
		// 		{
		// 			title: 'Total Runhour',
		// 			dataIndex: 'runhour',
		// 		},
		// 		{
		// 			title: 'Total Fuel Consumed',
		// 			dataIndex: 'fuel_cons',
		// 		},
		// 		{
		// 			title: 'Average Speed',
		// 			dataIndex: 'speed',
		// 		},
		// 		{
		// 			title: 'Top Speed',
		// 			dataIndex: 'top_speed',
		// 		},
		// 	];
		// }
		// let dataSource = [];
		// let findThingList = _find(this.state.total_data.things, {
		// 	id: parseFloat(this.state.thing_id),
		// });
		// console.log('pwqop', this.state.total_data.things);
		// let runhour = undefined,
		// 	startFuel = undefined,
		// 	fuel_consumption = undefined,
		// 	endFuel = undefined,
		// 	distance = undefined,
		// 	mileage = undefined,
		// 	speed = undefined,
		// 	top_speed = undefined,
		// 	batteryConsumed = undefined;
		// if (this.state.task_response && this.state.task_response.length) {
		// 	this.state.task_response.map((missions) => {
		// 		if (missions.Details.aggregate_data.distance_travelled) {
		// 			distance =
		// 				missions.Details.aggregate_data.distance_travelled.sum;
		// 		}
		// 		if (missions.Details.aggregate_data.vbat_consumed) {
		// 			batteryConsumed =
		// 				missions.Details.aggregate_data.vbat_consumed.sum;
		// 		}
		// 		if (
		// 			missions.Details.aggregate_data.calculated_runhour &&
		// 			missions.Details.aggregate_data.calculated_runhour.sum
		// 		) {
		// 			let time = Number(
		// 				missions.Details.aggregate_data.calculated_runhour.sum
		// 			);
		// 			let h =
		// 				Math.floor(time / 3600) < 10
		// 					? '0' + Math.floor(time / 3600)
		// 					: Math.floor(time / 3600);
		// 			let m =
		// 				Math.floor((time % 3600) / 60) < 10
		// 					? '0' + Math.floor((time % 3600) / 60)
		// 					: Math.floor((time % 3600) / 60);
		// 			let s =
		// 				Math.floor((time % 3600) % 60) < 10
		// 					? '0' + Math.floor((time % 3600) % 60)
		// 					: Math.floor((time % 3600) % 60);

		// 			runhour = h + ' : ' + m + ' : ' + s;
		// 		} else {
		// 			runhour = undefined;
		// 		}
		// 		if (
		// 			findThingList &&
		// 			findThingList.thing_details &&
		// 			findThingList.thing_details.capacity &&
		// 			missions.Details.aggregate_data.fuel
		// 		) {
		// 			startFuel =
		// 				(findThingList.thing_details.capacity *
		// 					parseFloat(
		// 						missions.Details.aggregate_data.fuel.initial
		// 					)) /
		// 				100;
		// 		} else {
		// 			startFuel = undefined;
		// 		}
		// 		if (missions.Details.aggregate_data.fuel_consumption) {
		// 			fuel_consumption =
		// 				missions.Details.aggregate_data.fuel_consumption.sum;
		// 		} else {
		// 			fuel_consumption = undefined;
		// 		}
		// 		if (
		// 			findThingList &&
		// 			findThingList.thing_details &&
		// 			findThingList.thing_details.capacity &&
		// 			missions.Details.aggregate_data.fuel
		// 		) {
		// 			endFuel =
		// 				(findThingList.thing_details.capacity *
		// 					parseFloat(
		// 						missions.Details.aggregate_data.fuel.snapshot
		// 					)) /
		// 				100;
		// 		} else {
		// 			endFuel = undefined;
		// 		}
		// 		if (fuel_consumption > 0) {
		// 			mileage = distance / fuel_consumption;
		// 		} else {
		// 			mileage = undefined;
		// 		}
		// 		if (missions.Details.aggregate_data.speed) {
		// 			speed = missions.Details.aggregate_data.speed.avg;
		// 			top_speed = missions.Details.aggregate_data.speed.max;
		// 		} else {
		// 			speed = undefined;
		// 			top_speed = undefined;
		// 		}
		// 	});
		// }
		// dataSource = [
		// 	{
		// 		distance:
		// 			distance === undefined
		// 				? '-'
		// 				: !isNaN(parseFloat(distance).toFixed(2))
		// 				? parseFloat(distance).toFixed(2) + ' Km'
		// 				: '-',
		// 		runhour: runhour === undefined ? '-' : runhour + ' Hrs',
		// 		start_fuel:
		// 			startFuel === undefined
		// 				? '-'
		// 				: !isNaN(parseFloat(startFuel).toFixed(2))
		// 				? parseFloat(startFuel).toFixed(2)
		// 				: '-',
		// 		fuel_cons:
		// 			fuel_consumption === undefined
		// 				? '-'
		// 				: !isNaN(parseFloat(fuel_consumption).toFixed(2))
		// 				? parseFloat(fuel_consumption).toFixed(2) + ' L'
		// 				: '-',
		// 		end_fuel:
		// 			endFuel === undefined
		// 				? '-'
		// 				: !isNaN(parseFloat(endFuel).toFixed(2))
		// 				? parseFloat(endFuel).toFixed(2)
		// 				: '-',
		// 		mileage:
		// 			mileage === undefined
		// 				? '-'
		// 				: !isNaN(parseFloat(mileage).toFixed(2))
		// 				? parseFloat(mileage).toFixed(2)
		// 				: '-',
		// 		speed:
		// 			speed === undefined
		// 				? '-'
		// 				: !isNaN(parseFloat(speed).toFixed(2))
		// 				? parseFloat(speed).toFixed(2) + ' Km/Hr'
		// 				: '-',
		// 		top_speed:
		// 			speed === undefined
		// 				? '-'
		// 				: !isNaN(parseFloat(top_speed).toFixed(2))
		// 				? parseFloat(top_speed).toFixed(2) + ' Km/Hr'
		// 				: '-',
		// 		vbat_consumed:
		// 			batteryConsumed === undefined
		// 				? '-'
		// 				: !isNaN(parseFloat(batteryConsumed).toFixed(2))
		// 				? parseFloat(batteryConsumed).toFixed(2)
		// 				: '-',
		// 	},
		// ];
		const columns = this.props.columns.filter((col) => {
			if (!this.props.isMobileScreen) {
				return [
					'distance',
					'runhour',
					'fuel_cons',
					'speed',
					'top_speed',
				].includes(col.dataIndex);
			}
			return !['sl_no', 'thing_name', 'on_time', 'off_time'].includes(
				col.dataIndex,
			);
		});
		const dataSource = this.props.dataSource.filter(
			(trip) => trip.trip_id === this.props.tripDetails?.[0]?.Id,
		);
		return {
			columns: columns,
			dataSource: dataSource,
		};
	}

	convertLatLng(data) {
		let modifiedData = [],
			modifiedDataWithTime = [];
		data.map((point) => {
			modifiedDataWithTime.push({
				lat: parseFloat(point.parameter_values?.lat),
				lng: parseFloat(point.parameter_values?.long),
				time: point.time,
			});
			modifiedData.push({
				lat: parseFloat(point.parameter_values?.lat),
				lng: parseFloat(point.parameter_values?.long),
			});
			//return 1;
		});
		return {
			modifiedData: _uniqBy(modifiedData, (item) => item.lat || item.lng),
			modifiedDataWithTime: _uniqBy(
				modifiedDataWithTime,
				(item) => item.lat || item.lng,
			),
		};
	}

	createBoundsForPolyline(latlngArray) {
		let bounds = new window.google.maps.LatLngBounds();
		latlngArray.map((points) => {
			return bounds.extend(points);
		});
		return bounds;
	}

	getDownloadData() {
		let rawGraph = this.state.raw_graph;
		let thingId = this.state.thing_id;
		let taskResponse = this.state.task_response;
		let totalData = this.state.total_data;
		let startTime = this.state.start_time;
		let endTime = this.state.end_time;
		let tripNo = this.state.trip_no;
		let fuelSeriesData = this.state.fuelSeriesData;
		let speedSeriesData = this.state.speedSeriesData;
		let isEv = this.state.isEv;
		let timeZone = this.props.user_preferences?.timezone
			? this.props.user_preferences.timezone
			: 'Asia/Calcutta';
		let summaryColumns = this.getTripTableData().columns;
		let summaryObject = this.getTripTableData().dataSource[0];
		console.log('');
		let summaryData = [];
		// if(isValidObject(summaryObject)){
		summaryColumns.map((column) => {
			summaryData.push({
				parameter: column.pdf_title,
				value: summaryObject?.[column.dataIndex] || '-',
			});
		});
		// }
		new Backyard({
			scripts: [
				'https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js',
				'https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.23/moment-timezone.min.js',
				'https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.33/moment-timezone-with-data-1970-2030.min.js',
				'https://cdn.jsdelivr.net/npm/lodash@4.17.15/lodash.min.js',
			],
			input: {
				rawGraph: rawGraph,
				isEv: isEv,
				thingId: thingId,
				taskResponse: taskResponse,
				totalData: totalData,
				startTime: startTime,
				endTime: endTime,
				tripNo: tripNo,
				fuelSeriesData: fuelSeriesData,
				speedSeriesData: speedSeriesData,
				summaryData: summaryData,
				timeZone: timeZone,
			},
			run: function (ctx, input, cb) {
				const { summaryData } = input;
				let downloadData = { conf: [], data: [] };
				let fuelGraphDataAll = {},
					speedGraphDataAll = {};
				let pdfMainHeaderOption = {
					// pdf_top_line: true,
					// pdf_bottom_line: true,
					pdf_text_align: 'center',
					textColor: [255, 255, 255],
					pdf_size: 10,
					fill: {
						fill_color: [255, 133, 0],
						y_value: 13,
						top: 1,
					},
				};
				let pushObj = pdfMainHeaderOption;
				pushObj['compo'] = 'Text';
				pushObj['props'] = {
					type: 'bold',
				};
				pushObj['col_props'] = {
					span: 24,
				};
				let REPORT_TYPE = {
					text_conf: {
						props: {
							gutter: 5,
						},
						child: [
							{
								pdf_text_align: 'center',
								textColor: [255, 255, 255],
								pdf_size: 16,
								type: 'bold',
								fill: {
									fill_color: [255, 133, 0],
									y_value: 24,
									top: 8,
								},
								compo: 'Text',
								props: {
									type: 'bold',
								},
								col_props: {
									span: 24,
								},
								secStyle: {
									body: {
										font: {
											size: 20,
											bold: true,
										},
									},
								},
							},
							pushObj,
						],
					},
					text_data: [
						{
							textData: [
								ctx['_'].find(input.totalData.things, {
									id: parseInt(input.thingId),
								}).name,
							],
							//textData: ['Trip Report - ' + input.client_name],
						},
						{
							textData: [
								'(From ' +
									ctx.moment
										.unix(input.startTime)
										.tz(input.timeZone)
										.format('DD MMM YYYY, HH:mm') +
									' to ' +
									ctx.moment
										.unix(input.endTime)
										.tz(input.timeZone)
										.format('DD MMM YYYY, HH:mm') +
									')',
							],
						},
					],
				};
				downloadData.conf.push(REPORT_TYPE.text_conf);
				downloadData.data.push(REPORT_TYPE.text_data);
				let findThingList = ctx['_'].find(input.totalData.things, {
					id: parseFloat(input.thingId),
				});
				let runhour = undefined,
					startFuel = undefined,
					fuel_consumption = undefined,
					endFuel = undefined,
					distance = undefined,
					mileage = undefined,
					batteryConsumed = undefined,
					speed = undefined;
				if (input.taskResponse && input.taskResponse.length) {
					input.taskResponse.map((missions) => {
						if (
							missions.Details.aggregate_data.distance_travelled
						) {
							distance =
								missions.Details.aggregate_data
									.distance_travelled.sum;
						}
						if (missions.Details.aggregate_data.vbat_consumed) {
							batteryConsumed =
								missions.Details.aggregate_data.vbat_consumed
									.sum;
						}
						if (
							missions.Details.aggregate_data
								.calculated_runhour &&
							missions.Details.aggregate_data.calculated_runhour
								.sum
						) {
							let time = Number(
								missions.Details.aggregate_data
									.calculated_runhour.sum,
							);
							let h =
								Math.floor(time / 3600) < 10
									? '0' + Math.floor(time / 3600)
									: Math.floor(time / 3600);
							let m =
								Math.floor((time % 3600) / 60) < 10
									? '0' + Math.floor((time % 3600) / 60)
									: Math.floor((time % 3600) / 60);
							let s =
								Math.floor((time % 3600) % 60) < 10
									? '0' + Math.floor((time % 3600) % 60)
									: Math.floor((time % 3600) % 60);

							runhour = h + ' : ' + m + ' : ' + s;
						} else {
							runhour = undefined;
						}
						if (
							findThingList &&
							findThingList.thing_details &&
							findThingList.thing_details.capacity &&
							missions.Details.aggregate_data.fuel
						) {
							startFuel =
								(findThingList.thing_details.capacity *
									parseFloat(
										missions.Details.aggregate_data.fuel
											.initial,
									)) /
								100;
						} else {
							startFuel = undefined;
						}
						if (missions.Details.aggregate_data.fuel_consumption) {
							fuel_consumption =
								missions.Details.aggregate_data.fuel_consumption
									.sum;
						} else {
							fuel_consumption = undefined;
						}
						if (
							findThingList &&
							findThingList.thing_details &&
							findThingList.thing_details.capacity &&
							missions.Details.aggregate_data.fuel
						) {
							endFuel =
								(findThingList.thing_details.capacity *
									parseFloat(
										missions.Details.aggregate_data.fuel
											.snapshot,
									)) /
								100;
						} else {
							endFuel = undefined;
						}
						if (fuel_consumption > 0) {
							mileage = distance / fuel_consumption;
						} else {
							mileage = undefined;
						}
						if (missions.Details.aggregate_data.speed) {
							speed = missions.Details.aggregate_data.speed.avg;
						} else {
							speed = undefined;
						}
					});
				}

				// let pageConfigParam = {
				// 	pdf_force_new_page: false,
				// };
				// let textPushParam = pageConfigParam;
				// textPushParam['compo'] = 'Text';
				// textPushParam['props'] = {
				// 	type: 'bold',
				// };

				// textPushParam['col_props'] = {
				// 	span: 24,
				// };
				// textPushParam['pdf_size'] = 12;

				// let { text_conf_param, text_data_param } = {
				// 	text_conf_param: {
				// 		props: {
				// 			gutter: 10,
				// 		},
				// 		child: [textPushParam],
				// 	},
				// 	text_data_param: input.isEv
				// 		? [
				// 				{
				// 					textData: [
				// 						'Distance(Km): ' +
				// 							(distance === undefined
				// 								? '-'
				// 								: !isNaN(
				// 										parseFloat(
				// 											distance
				// 										).toFixed(2)
				// 								  )
				// 								? parseFloat(distance).toFixed(
				// 										2
				// 								  )
				// 								: '-'),
				// 						'Runhour(HH:MM:SS): ' +
				// 							(runhour === undefined
				// 								? '-'
				// 								: runhour),
				// 						'Average Speed (Km/Hr): ' +
				// 							(speed === undefined
				// 								? '-'
				// 								: !isNaN(
				// 										parseFloat(
				// 											speed
				// 										).toFixed(2)
				// 								  )
				// 								? parseFloat(speed).toFixed(2)
				// 								: '-'),
				// 						'Battery Consumed(%): ' +
				// 							(batteryConsumed === undefined
				// 								? '-'
				// 								: !isNaN(
				// 										parseFloat(
				// 											batteryConsumed
				// 										).toFixed(2)
				// 								  )
				// 								? parseFloat(
				// 										batteryConsumed
				// 								  ).toFixed(2)
				// 								: '-'),
				// 					],
				// 				},
				// 		  ]
				// 		: [
				// 				{
				// 					textData: [
				// 						'Distance(Km): ' +
				// 							(distance === undefined
				// 								? '-'
				// 								: !isNaN(
				// 										parseFloat(
				// 											distance
				// 										).toFixed(2)
				// 								  )
				// 								? parseFloat(distance).toFixed(
				// 										2
				// 								  )
				// 								: '-'),
				// 						'Runhour(HH:MM:SS): ' +
				// 							(runhour === undefined
				// 								? '-'
				// 								: runhour),
				// 						'Fuel Level Before Trip(L): ' +
				// 							(startFuel === undefined
				// 								? '-'
				// 								: !isNaN(
				// 										parseFloat(
				// 											startFuel
				// 										).toFixed(2)
				// 								  )
				// 								? parseFloat(startFuel).toFixed(
				// 										2
				// 								  )
				// 								: '-'),
				// 						'Fuel Consumed (L): ' +
				// 							(fuel_consumption === undefined
				// 								? '-'
				// 								: !isNaN(
				// 										parseFloat(
				// 											fuel_consumption
				// 										).toFixed(2)
				// 								  )
				// 								? parseFloat(
				// 										fuel_consumption
				// 								  ).toFixed(2)
				// 								: '-'),
				// 						'Fuel Level After Trip(L): ' +
				// 							(endFuel === undefined
				// 								? '-'
				// 								: !isNaN(
				// 										parseFloat(
				// 											endFuel
				// 										).toFixed(2)
				// 								  )
				// 								? parseFloat(endFuel).toFixed(2)
				// 								: '-'),
				// 						'Mileage(L/Km): ' +
				// 							(mileage === undefined
				// 								? '-'
				// 								: !isNaN(
				// 										parseFloat(
				// 											mileage
				// 										).toFixed(2)
				// 								  )
				// 								? parseFloat(mileage).toFixed(2)
				// 								: '-'),
				// 						'Average Speed (Km/Hr): ' +
				// 							(speed === undefined
				// 								? '-'
				// 								: !isNaN(
				// 										parseFloat(
				// 											speed
				// 										).toFixed(2)
				// 								  )
				// 								? parseFloat(speed).toFixed(2)
				// 								: '-'),
				// 					],
				// 				},
				// 		  ],
				// };
				// downloadData.conf.push(text_conf_param);
				// downloadData.data.push(text_data_param);

				// let summaryData = input.isEv
				// 	? [
				// 			{
				// 				parameter: 'Distance(Km)',
				// 				value:
				// 					distance === undefined
				// 						? '-'
				// 						: !isNaN(
				// 								parseFloat(distance).toFixed(2)
				// 						  )
				// 						? parseFloat(distance).toFixed(2)
				// 						: '-',
				// 			},
				// 			{
				// 				parameter: 'Runhour(HH:MM:SS)',
				// 				value: runhour === undefined ? '-' : runhour,
				// 			},
				// 			{
				// 				parameter: 'Average Speed (Km/Hr)',
				// 				value:
				// 					speed === undefined
				// 						? '-'
				// 						: !isNaN(parseFloat(speed).toFixed(2))
				// 						? parseFloat(speed).toFixed(2)
				// 						: '-',
				// 			},
				// 			{
				// 				parameter: 'Battery Consumed(%)',
				// 				value:
				// 					batteryConsumed === undefined
				// 						? '-'
				// 						: !isNaN(
				// 								parseFloat(
				// 									batteryConsumed
				// 								).toFixed(2)
				// 						  )
				// 						? parseFloat(batteryConsumed).toFixed(2)
				// 						: '-',
				// 			},
				// 	  ]
				// 	: [
				// 			{
				// 				parameter: 'Distance(Km)',
				// 				value:
				// 					distance === undefined
				// 						? '-'
				// 						: !isNaN(
				// 								parseFloat(distance).toFixed(2)
				// 						  )
				// 						? parseFloat(distance).toFixed(2)
				// 						: '-',
				// 			},
				// 			{
				// 				parameter: 'Runhour(HH:MM:SS)',
				// 				value: runhour === undefined ? '-' : runhour,
				// 			},
				// 			{
				// 				parameter: 'Fuel Level Before Trip(L)',
				// 				value:
				// 					startFuel === undefined
				// 						? '-'
				// 						: !isNaN(
				// 								parseFloat(startFuel).toFixed(2)
				// 						  )
				// 						? parseFloat(startFuel).toFixed(2)
				// 						: '-',
				// 			},
				// 			{
				// 				parameter: 'Fuel Consumed (L)',
				// 				value:
				// 					fuel_consumption === undefined
				// 						? '-'
				// 						: !isNaN(
				// 								parseFloat(
				// 									fuel_consumption
				// 								).toFixed(2)
				// 						  )
				// 						? parseFloat(fuel_consumption).toFixed(
				// 								2
				// 						  )
				// 						: '-',
				// 			},
				// 			{
				// 				parameter: 'Fuel Level After Trip(L)',
				// 				value:
				// 					endFuel === undefined
				// 						? '-'
				// 						: !isNaN(parseFloat(endFuel).toFixed(2))
				// 						? parseFloat(endFuel).toFixed(2)
				// 						: '-',
				// 			},
				// 			{
				// 				parameter: 'Mileage(L/Km)',
				// 				value:
				// 					mileage === undefined
				// 						? '-'
				// 						: !isNaN(parseFloat(mileage).toFixed(2))
				// 						? parseFloat(mileage).toFixed(2)
				// 						: '-',
				// 			},
				// 			{
				// 				parameter: 'Average Speed (Km/Hr)',
				// 				value:
				// 					speed === undefined
				// 						? '-'
				// 						: !isNaN(parseFloat(speed).toFixed(2))
				// 						? parseFloat(speed).toFixed(2)
				// 						: '-',
				// 			},
				// 	  ];
				let pushObjSpace = {};
				pushObjSpace['compo'] = 'Text';
				pushObjSpace['props'] = {
					type: 'bold',
				};
				pushObjSpace['col_props'] = {
					span: 24,
				};
				let { textConf, textData } = {
					textConf: {
						props: {
							gutter: 10,
						},
						child: [pushObjSpace],
					},
					textData: [
						{
							textData: ['', ''], //[things.name],
							style: { 'margin-top': 40 },
						},
					],
				};

				downloadData.conf.push(textConf);
				downloadData.data.push(textData);
				downloadData.conf.push({
					props: {
						gutter: 10,
						style: {},
						className: 'tableRow',
					},
					child: [
						{
							compo: 'Table',
							widget: '',
							classname: 'tab-1',
							table_new_page: true,
							props: {
								columns: [
									{
										title: 'Parameter',
										dataIndex: 'parameter',
									},
									{
										title: 'Value',
										dataIndex: 'value',
									},
								],
								headerFont: 13,
								horizontalScroll: true,
								isGrouped: true,
								confColumnLength1: 3,
								confColumnLength2: 3,
								level: 2,
								shadow: false,
								breakPoint: 1000,
								breakPoint2: 500,
								largeTable: true,
								mediumTable: false,
								smallTable: false,
							},
							col_props: {
								span: 24,
							},
							pdf_width: 50,
							pdf_table_break: {
								col_no: 6,
								row_no: 12,
							},
						},
					],
				});
				downloadData.data.push([summaryData]);
				let pageConfig = {
					pdf_force_new_page: true,
				};
				let textPush = pageConfig;
				textPush['compo'] = 'Text';
				textPush['props'] = {
					type: 'bold',
				};

				textPush['col_props'] = {
					span: 24,
				};
				textPush['pdf_size'] = 12;

				let { text_conf, text_data } = {
					text_conf: {
						props: {
							gutter: 10,
						},
						child: [textPush],
					},
					text_data: [
						{
							textData: ['Speed'],
						},
					],
				};
				downloadData.conf.push(text_conf);
				downloadData.data.push(text_data);
				speedGraphDataAll = {
					chart: {
						type: 'area',
						width: 1280,
						height: 300,
					},
					xAxis: {
						title: {
							text: 'Date & time',
						},
						type: 'datetime',
						labels: {
							style: {
								fontSize: 14,
							},
						},
					},
					title: {
						text: '',
					},
					legend: {
						enabled: false,
					},
					yAxis: {
						title: {
							text: 'Speed (km/hr)',
						},
					},
					credits: {
						enabled: false,
					},
					series: [
						{
							name: 'Speed',
							data: input.speedSeriesData,
							color: '#35D2DD',
						},
					],
				};
				downloadData.conf.push({
					props: {
						gutter: 10,
						style: {
							marginTop: 20,
						},
						className: 'rowGraph',
					},
					child: [
						{
							compo: 'Graph',
							widget: '',
							classname: 'graph-1',
							props: {
								id: 'graph-id-B',
							},
							col_props: {
								span: 24,
							},
							pdf_width: 700,
							pdf_height: 250,
							pdf_force_new_page: false,
							datatype: {},
						},
					],
				});
				downloadData.data.push([speedGraphDataAll]);
				let pageConfigFuel = {
					pdf_force_new_page: false,
				};
				if (!input.isEv) {
					let textPushFuel = pageConfigFuel;
					textPushFuel['compo'] = 'Text';
					textPushFuel['props'] = {
						type: 'bold',
					};

					textPushFuel['col_props'] = {
						span: 24,
					};
					textPushFuel['pdf_size'] = 12;
					let { text_conf_fuel, text_data_fuel } = {
						text_conf_fuel: {
							props: {
								gutter: 10,
							},
							child: [textPushFuel],
						},
						text_data_fuel: [
							{
								textData: ['Fuel Level'],
							},
						],
					};
					downloadData.conf.push(text_conf_fuel);
					downloadData.data.push(text_data_fuel);
					fuelGraphDataAll = {
						chart: {
							type: 'area',
							width: 1280,
							height: 300,
						},
						xAxis: {
							title: {
								text: 'Date & time',
							},
							type: 'datetime',
							labels: {
								style: {
									fontSize: 14,
								},
							},
						},
						title: {
							text: '',
						},
						legend: {
							enabled: false,
						},
						yAxis: {
							title: {
								text: 'Fuel Level (%)',
							},
						},
						credits: {
							enabled: false,
						},
						series: [
							{
								name: 'Fuel',
								data: input.fuelSeriesData,
								color: '#FF8500',
							},
						],
					};
					downloadData.conf.push({
						props: {
							gutter: 10,
							style: {
								marginTop: 20,
							},
							className: 'rowGraph',
						},
						child: [
							{
								compo: 'Graph',
								widget: '',
								classname: 'graph-1',
								props: {
									id: 'graph-id-A',
								},
								col_props: {
									span: 24,
								},
								pdf_width: 700,
								pdf_height: 250,
								pdf_force_new_page: false,
								datatype: {},
							},
						],
					});
					downloadData.data.push([fuelGraphDataAll]);
				}

				downloadData.file_name = 'Trip Details';
				cb({
					downloadData: downloadData,
				});
			},
			cb: (value) => {
				this.setState({
					downloadData: value.downloadData,
					loading: true,
				});
				this.getDownloadRender();
			},
		});
	}

	getDownloadRender() {
		let downloadRender = this.state.downloadData ? (
			<div
				style={{
					opacity: 0,
					visibility: '',
					overflow: 'hidden',
					'max-height': 0,
				}}
			>
				<ReportController
					is_white_label={this.props.is_white_label}
					vendor_name={this.props.vendor_name}
					ref={this.invisibleReportRef}
					onExportReady={this.onExportReady}
					key={moment().unix()}
					{...this.state.downloadData}
					parameters={['', '', '', '']}
				/>
			</div>
		) : (
			''
		);
		this.setState(
			{
				download_render: downloadRender,
				loading: false,
			},
			() => {
				if (this.props.showMap) {
					this.zoomOnPolyline(this.state.latLngData);
				}
			},
		);
	}

	render() {
		const { currentThing } = this.state;
		let pageLoading = '';
		if (this.state.loading) {
			pageLoading = <Loading />;
		} else {
			console.log('effef', this.state.downloadData);
			let vehicleRoute = '',
				summaryRoute = '';
			let graphRenderData = this.graphRenderData();
			if (this.state.latLngDataWithAddress?.length) {
				summaryRoute = (
					<div className="summary-vehicle-route">
						<AntTimeline mode="left">
							{this.state.latLngDataWithAddress
								.filter(
									(i, index) =>
										(index === 0 ||
											index ===
												this.state.latLngDataWithAddress
													.length -
													1) &&
										i.address,
								)
								.map((item, index) => (
									<AntTimelineItem
										label={
											<p className="loc-timeline-icon">
												<span />
											</p>
										}
									>
										<p className="timeline-child-head">
											<span>
												{index === 0
													? 'Start: '
													: 'End: '}{' '}
												{moment
													.unix(item.time)
													.format(
														'DD MMM YYYY, HH:mm',
													)}
											</span>
										</p>
										<p className="timeline-child-body">
											{item.address}
										</p>
									</AntTimelineItem>
								))}
						</AntTimeline>
					</div>
				);
				vehicleRoute = (
					<div className="moving-map-path">
						<AntTimeline mode="left">
							{this.state.latLngDataWithAddress
								.filter((i) => i.address)
								.map((item) => (
									<AntTimelineItem
										label={
											<p className="loc-timeline-icon">
												<span />
											</p>
										}
									>
										<p className="timeline-child-head">
											<span>
												{item.address
													.split(',')
													.slice(0, 2)
													.join(', ')}
											</span>
											<span className="point-time">
												{moment(
													item.time * 1000,
												).format('HH:mm')}
											</span>
										</p>
										<p className="timeline-child-body">
											{item.address
												.split(',')
												.slice(2)
												.join()}
										</p>
									</AntTimelineItem>
								))}
						</AntTimeline>
					</div>
				);
			} else {
				vehicleRoute = (
					<div className="no-trip-data">
						<p>No trip data available</p>
					</div>
				);
			}
			let summaryData = this.getTripTableData().dataSource[0];
			if (this.props.isMobileScreen) {
				pageLoading = (
					<main id="trip-details-mb">
						<section className="trip-dtl-mb-tp">
							<p>
								<span>
									{TimeFormatter(
										this.props.user_preferences
											?.time_format,
										this.state.start_time,
										'DD MMM YYYY, HH:mm',
									)}{' '}
									-{' '}
									{TimeFormatter(
										this.props.user_preferences
											?.time_format,
										this.state.end_time,
										'DD MMM YYYY, HH:mm',
									)}
								</span>
								<CloseOutlined
									style={{ color: '#808080', fontSize: 15 }}
									onClick={this.props.goBack}
								/>
							</p>
							<p>{currentThing?.name || 'NA'}</p>
						</section>
						{this.props.showMap ? (
							<section className="trip-dtl-mb-map">
								<GoogleMapComponent
									latLngData={this.state.latLngData}
									reverseGeocode={async (latlng) =>
										await reverseGeocode(latlng)
									}
									trip_drawer_visible="true"
									zoomLevel={this.state.zoomLevel}
									centerValue={this.state.centerValue}
									onGoogleApiLoaded={(map) =>
										this.onGoogleApiLoaded(map)
									}
								/>
							</section>
						) : (
							''
						)}
						<section className="trip-dtl-endpoints">
							{summaryRoute}
						</section>
						<section className="trip-dtl-smmry">
							{this.getTripTableData().columns.map((col) => (
								<div>
									<p className="tdmv-val">{col.title}</p>
									<p className="tdmv-label">
										{summaryData[col.dataIndex]}
									</p>
								</div>
							))}
						</section>
					</main>
				);
			} else {
				pageLoading = (
					<div id="trip_view_details">
						<div className="header">
							<section className="tvd-h-left">
							<ArrowLeftOutlined
									style={{
										color: '#374375',
										fontSize: 20,
										marginRight: 10,
										marginTop: 6,
										height: 'fit-content'
									}}
									onClick={this.props.goBack}
								/>
								{this.props.thing_category_icon && <div className="tvd-cat-icon">
									<ImageComponent
										src={this.props.thing_category_icon}
									/>
								</div>}
								<div>
									<div className="tvd-h-left-tp">
										<span className="tvd-hg">
											{currentThing?.name || 'NA'}
										</span>
										{currentThing?.thing_details?.make ? (
											<span className="tvd-sm-lg">
												Make:{' '}
												{
													currentThing.thing_details
														.make
												}
											</span>
										) : (
											''
										)}
										{currentThing?.thing_details?.model ? (
											<span className="tvd-sm-lg">
												Model:{' '}
												{currentThing.thing_details
													.model || 'NA'}
											</span>
										) : (
											''
										)}
										<span className="tvd-sm-lg">
											{currentThing?.thing_details?.kva
												? currentThing.thing_details
														.kva + ' kva'
												: ''}
										</span>
										{/* <span>Current Location: dahshashabj</span> */}
									</div>
									{
										this.props.categoryId === 45 ?
										<div className="tvd-h-left-btm" style={{display: 'flex', gap: 10, fontSize: 13}}>
										 <div>
										   <span style={{color: '#808080'}}>Door Open Time:{" "}</span>
										   {TimeFormatter(
											 this.props.user_preferences?.time_format,
											 this.state.start_time,
											 "DD MMM YYYY, HH:mm:ss",
										   )}
										 </div>
										 <span style={{color: '#808080'}}>|</span>
										 <div>
										 <span style={{color: '#808080'}}>Door Closing Time:{" "}</span>
										   {TimeFormatter(
											 this.props.user_preferences?.time_format,
											 this.state.end_time,
											 "DD MMM YYYY, HH:mm:ss",
										   )}
										 </div>
									   </div> :
									<p className="tvd-h-left-btm">
										{TimeFormatter(
											this.props.user_preferences
												?.time_format,
											this.state.start_time,
											'DD MMM YYYY, HH:mm',
										)}{' '}
										to{' '}
										{TimeFormatter(
											this.props.user_preferences
												?.time_format,
											this.state.end_time,
											'DD MMM YYYY, HH:mm',
										)}
									</p>
									}
								</div>
							</section>
							<section className="tvd-h-right">
								{this.props.showMap && (
									<AntButton
										type="text"
										className="tdmv-dwn-btn"
										onClick={() => this.downloadCallback()}
									>
										<DownloadOutlined
											style={{
												color: '#374375',
											}}
										/>{' '}
										Download PDF
									</AntButton>
								)}
								{/* <CloseCircleOutlined
									style={{
										color: '#374375',
										fontSize: 20,
										marginTop: 6,
									}}
									onClick={this.props.goBack}
								/> */}
							</section>
						</div>
						{this.props.showMap ? (
							<>
								<div className="table-container">
									{summaryRoute}
									{this.getTripTableData().columns.map(
										(col) => (
											<div>
												<p className="tdmv-val">
													{col.title}
												</p>
												<p className="tdmv-label">
													{summaryData[col.dataIndex]}
												</p>
											</div>
										),
									)}
								</div>
								{this.state.latLngData?.length ? (
									<>
										<AntRow className="map-trip-row">
											<AntCol
												className="map-col"
												xxs={24}
												sm={12}
												md={12}
												lg={12}
												xl={16}
												xxl={16}
											>
												{this.props.showMap ? (
													<GoogleMapComponent
														latLngData={
															this.state
																.latLngData
														}
														reverseGeocode={async (
															latlng,
														) =>
															await reverseGeocode(
																latlng,
															)
														}
														trip_drawer_visible="true"
														zoomLevel={
															this.state.zoomLevel
														}
														centerValue={
															this.state
																.centerValue
														}
														onGoogleApiLoaded={(
															map,
														) =>
															this.onGoogleApiLoaded(
																map,
															)
														}
													/>
												) : (
													''
												)}
											</AntCol>
											<AntCol
												xxs={24}
												sm={12}
												md={12}
												lg={12}
												xl={8}
												xxl={8}
											>
												{vehicleRoute}
											</AntCol>
										</AntRow>
										<AntDivider />
									</>
								) : (
									''
								)}
								<TripParamTrend
									{...this.props}
									thing_id={this.state.thing_id}
									start_time={this.state.start_time}
									end_time={this.state.end_time}
								/>
								{/* <div className="graph-ttitle-container"> // --> Ask Asish
									<div className="title">Speed</div>
									<div>
										<GraphHighcharts
											graphData={
												graphRenderData.speed_graph
													.graph_data
											}
										/>
									</div>
								</div>
								{this.state.isEv ? (
									''
								) : (
									<div className="graph-ttitle-container">
										<div className="title">Fuel</div>
										<div>
											<GraphHighcharts
												graphData={
													graphRenderData.fuel_graph
														.graph_data
												}
											/>
										</div>
									</div>
								)} */}
							</>
						) : (
							<TripParamTrend
								{...this.props}
								thing_id={this.state.thing_id}
								start_time={this.state.start_time}
								end_time={this.state.end_time}
							/>
						)}
						{this.state.download_render}
					</div>
				);
			}
		}
		return pageLoading;
	}
}
