#trips_daily_monthly_view {
	display: flex;
	flex-direction: column;
	.tdmv-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 5px 28px;
		// .tdmv-dwn-btn {
		// 	color: #374375;
		// }
	}
	.tdmv-body {
		flex: 1;
		background-color: #f6f7f8;
		overflow: hidden auto;
		padding-bottom: 30px;
		.tdmv-sec-head {
			font-size: 14px;
			font-weight: 600;
			color: #232323;
			margin-bottom: 8px;
		}
		.tdmv-filters {
			display: flex;
			justify-content: space-between;
			padding-top: 10px;
			margin-bottom: 20px;
			padding: 10px 18px 0;
			.tdmv-time-selec {
				margin-left: 20px;
			}
		}
		.tdmv-summary {
			padding: 0 30px;
			margin-bottom: 36px;
			.tdmv-summ-box {
				display: flex;
				flex-direction: column;
				background-color: #fff;
				border-radius: 18px;
				padding: 20px 22px;
				.tdmv-summ-kpis {
					display: flex;
					flex-wrap: wrap;
					justify-content: space-around;
					> div {
						padding: 10px;
					}
					.tdmv-val {
						font-size: 13px;
						color: #808080;
						margin-bottom: 4px;
					}
					.tdmv-label {
						font-size: 20px;
						color: #232323;
						font-weight: 500;
						margin-bottom: 4px;
					}
				}
			}
		}
		.tdmv-table {
			padding: 0 30px;
			.tdmv-sec-head {
				// padding: 0 25px;
				// margin-bottom: 0;
				margin-bottom: 10px;
			}
			.tdmv-cstm .open-cstm-table.opn-cstbl-abs{
				top: 0;
			}
			.ant-pagination {
				margin-top: 0;
			}
			.ant-table {
				.ant-table-header {
					overflow-y: hidden !important;
					.ant-table-column-sorters {
						padding: 0;
					}
					.tdmv-col-hd {
						text-align: center;
						.tdmv-col-nml {
							overflow: hidden;
							text-overflow: ellipsis;
							display: -webkit-box;
							-webkit-line-clamp: 2;
							-webkit-box-orient: vertical;
						}
						.tdmv-col-lg {
							font-weight: normal;
						}
					}
				}
				.ant-table-body {
					tr.fuel-filled-highlight td {
						background-color: #4dbfff;
					}
					tr.fuel-theft-highlight td {
						background-color: #fcfa62;
					}
					.thing-name-val {
						color: #9e5337 !important;
						font-size: 14px;
						font-weight: 500;
						&.go-to-dtl {
							cursor: pointer;
						}
					}
					.tdmv-time-render {
						color: #808080 !important;
						font-size: 13px;
					}
					.tdmv-tags-render {
						color: #232323 !important;
						font-size: 14px;
						&.in_transit {
							color: #ff3400 !important;
						}
						&.internal_transfer {
							color: #808080 !important;
						}
					}
					.tdmv-tags {
						display: flex;
						flex-wrap: wrap;
						row-gap: 8px;
					}
				}
			}
		}
	}
}

@media (max-width: 900px) {
	#trips_daily_monthly_view {
		.tdmv-body {
			background-color: #fff;
			.tdmv-filters {
				padding: 8px 18px;
				background-color: #f6f7f8;
				.rangepicker-container .ant-select {
					width: 100% !important;
					.ant-select-selector {
						border-radius: 4px !important;
					}
				}
				.tdmv-mb-icon {
					display: flex;
					.tdmv-mbic-lft {
						> p {
							margin-bottom: 0;
							&:first-child {
								color: #232323;
								font-size: 16px;
								font-weight: 500;
							}
							&:last-child {
								color: #808080;
								font-size: 13px;
							}
						}
					}
				}
				.tdmv-mb-fl {
					display: flex;
					.generic-filter-icon .anticon {
						font-size: 16px;
					}
				}
				.tdmv-dwn-btn {
					display: flex;
					background: white;
					align-items: center;
					justify-content: center;
					border-radius: 50% !important;
					height: 35px;
					width: 36px;
					margin-right: 10px;
					&.ant-btn-loading {
						width: unset;
						border-radius: 10px !important;
					}
					> span:last-child {
						display: none;
					}
				}
			}
			.tdmv-summary {
				padding: 0;
				.tdmv-sec-head {
					display: none;
				}
				.tdmv-summ-box {
					padding-top: 0;
					.tdmv-summ-kpis {
						justify-content: unset;
						justify-items: flex-start;
						> div {
							width: 50%;
						}
					}
				}
			}
			.tdmv-table {
				padding: 0;
				.tdmv-sec-head {
					font-size: 18px;
					margin-bottom: 4px;
				}
				.tdmv-cstm {
					.ant-list-pagination {
						margin-top: 0;
						margin-bottom: 5px;
					}
					.ant-pagination-mini {
						display: flex;
						justify-content: end;
						align-items: center;

						.ant-pagination-next {
							height: auto;
						}
					}

					.ant-pagination-item {
						display: none;
					}

					.ant-pagination-item-active {
						display: block;
						width: fit-content;
					}

					.ant-pagination-jump-next {
						display: none;
					}

					.ant-pagination-prev {
						height: auto;
					}

					.ant-pagination-next {
						height: auto;
					}
					.ant-list-vertical .ant-list-item {
						background: #fafafb;
						.tdmv-mb-list {
							display: flex;
							cursor: pointer;
							.mb-list-rg {
								color: #808080;
								font-size: 14px;
								margin-right: 16px;
							}
							.mb-list-lf {
								flex: 1;
								font-size: 14px;
								> p {
									margin-bottom: 0;
								}
								> p:first-child {
									color: #001529;
									display: flex;
									justify-content: space-between;
								}
								> p:last-child {
									color: #9e5337;
								}
							}
						}
					}
				}
			}
		}
	}
}
