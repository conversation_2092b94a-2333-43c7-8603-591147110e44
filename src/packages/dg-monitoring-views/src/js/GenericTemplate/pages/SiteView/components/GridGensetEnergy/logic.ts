import { useEffect, useState } from "react";
import moment from "moment-timezone";
import { getThingsData } from "@datoms/js-sdk";
import { timeDurationMapping } from "../../../DetailedView/logic/getTimePeriod";
import { getPowerSourceData } from "../../logic/headerDataManipulation";

type TimePeriodKey =
  | "last_24_hrs"
  | "today"
  | "yesterday"
  | "last_7_days"
  | "this_week"
  | "last_week"
  | "last_30_days"
  | "this_month"
  | "last_month"
  | "last_3_months"
  | "last_120_days"
  | "custom";

const keyParamLabel: Record<string, string> = {
  no_of_runs:"DG Runs",
  calculated_energy: "Energy Generated",
  fuel_consumption: "Fuel Consumed",
  fuel_filled: "Fuel Filled",
  calculated_runhour: "Total Genset Runhour",
  calculated_energy_p_fuel_consumption:"Fuel Efficiency",
  calculated_mains_energy: "Main Meter Energy",
};

const powerSourceParamKeyMap: Record<string, any> = {
  calculated_mains_energy: {
    grid: "Main Meter Energy",
    load: "Energy",
  },
};

const paramUnitMap: Record<string, string> = {
  no_of_runs:"",
  calculated_energy: "kWh",
  fuel_consumption: "L",
  fuel_filled: "L",
  calculated_runhour: "",
  calculated_energy_p_fuel_consumption:"kWh/L",
  calculated_mains_energy: "kWh",
};

function getValidNumber(value: any, unit?: string) {
  return !isNaN(value) && !isNaN(parseFloat(value))
    ? unit
      ? value.toFixed(2) + " " + unit
      : Number(value.toFixed(2))
    : "-";
}

const useData = (props: any) => {
  const [loading, setLoading] = useState(true);
  const [fromTime, setFromTime] = useState(moment().subtract(30, 'days').startOf('day').unix());
  const [uptoTime, setUptoTime] = useState(moment().endOf('day').unix());
  const [selectedDateRange, setSelectedDateRange] = useState("last_30_days");
  const [aggrPeriod, setAggrPeriod] = useState(86400);
  const [energyData, setEnergyData] = useState<any>({});

  const powerSources = getPowerSourceData(props.siteDetails);

  useEffect(() => {
    getEnergyData();
  }, [fromTime, uptoTime]);

  const getEnergyData = async () => {
    const apiConfigArray: any = [];
    
    powerSources?.grid.forEach((gridItem: any) => {
      apiConfigArray.push({
          purpose: 'grid',
          identifier: gridItem.name || gridItem.id, 
          config: {
              data_type: aggrPeriod === 0 ? "raw" : "aggregate",
              parameter_attributes: aggrPeriod === 0 ? [] : ["sum"],
              parameters: ["calculated_mains_energy"],
              aggregation_period: aggrPeriod,
              things: [gridItem.id], 
              from_time: fromTime,
              upto_time: uptoTime,
              summarize: aggrPeriod === 0 ? ["sum(calculated_mains_energy)"] : [{ query: ["sum(calculated_mains_energy.sum)"] }],
          },
          query: aggrPeriod === 0 ? "?data_source=new" : "",
      });
  });
  

  powerSources?.load.forEach((loadItem: any) => {
      apiConfigArray.push({
          purpose: 'load',
          identifier: loadItem.name || loadItem.id,
          config: {
              data_type: aggrPeriod === 0 ? "raw" : "aggregate",
              parameter_attributes: aggrPeriod === 0 ? [] : ["sum"],
              parameters: ["calculated_mains_energy"],
              aggregation_period: aggrPeriod,
              things: [loadItem.id], 
              from_time: fromTime,
              upto_time: uptoTime,
              summarize: aggrPeriod === 0 ? ["sum(calculated_mains_energy)"] : [{ query: ["sum(calculated_mains_energy.sum)"] }],
          },
          query: aggrPeriod === 0 ? "?data_source=new" : "",
      });
  });
  
  
  powerSources?.genset.forEach((gensetItem: any) => {
      apiConfigArray.push({
          purpose: "genset",
          identifier: gensetItem.name || gensetItem.id,
          config: {
              data_type: aggrPeriod === 0 ? "raw" : "aggregate",
              parameter_attributes: aggrPeriod === 0 ? [] : ["sum","avg"],
              parameters: [
                "no_of_runs",
                "calculated_runhour",
                "calculated_energy",
                "fuel_consumption",
                "calculated_energy_p_fuel_consumption",
                "fuel_filled",
              ],
              aggregation_period: aggrPeriod,
              things: [gensetItem.id], 
              from_time: fromTime,
              upto_time: uptoTime,
              ...(aggrPeriod !== 0 && {
                "summary": [
                    {
                        "parameters": ["calculated_energy_p_fuel_consumption.overall"]
                    }
                ]
              }),
              "categories": [
              18
              ] ,
              summarize:    aggrPeriod === 0
              ? [
                "sum(no_of_runs)",
                "sum(calculated_runhour)",
                "sum(calculated_energy)",
                "sum(fuel_consumption)",
                "sum(energy_gen_per_litre)",
                "sum(fuel_filled)",
              ]
              :[
                {
                    query: [
                      "sum(no_of_runs.sum)",
                      "sum(calculated_runhour.sum)",
                      "sum(calculated_energy.sum)",
                      "sum(fuel_consumption.sum)",
                      "sum(fuel_filled.sum)",
                    ],
                  },
                ],
              },
              query: aggrPeriod === 0 ? "?data_source=new" : "",
            });
          });

    const response = await Promise.all(
      apiConfigArray.map(async (con: any) => {
        const value= await getThingsData(
          con.config,
          props.client_id,
          props.application_id,
          undefined,
          con.query,
        );
        return {response:value,identifier:con.identifier};
      }),
    );
    const data: any = {
      site_consumption: [
        {
          label: "Total Consumed",
          value: "-",
        },
      ],
      trendGraph: {},
      pieGraph: [],
    };
    response.forEach((responseItem, index) => {
      const identifier=responseItem.identifier;
      const res=responseItem.response;
      const summaryArray: any[] = [];
      apiConfigArray[index].config.parameters.forEach((key: string) => {
        let value =
          aggrPeriod === 0
            ? res.summary?.[key]?.sum
            : res.summary?.[0]?.data?.[0]?.parameter_values?.sum?.[key]?.sum;
        if(["calculated_energy_p_fuel_consumption"].includes(key)){
          value = aggrPeriod === 0
          ? res.summary?.["energy_gen_per_litre"]
          : res.summary?.[1]?.[0]?.[0]?.["calculated_energy_p_fuel_consumption_overall"];

        }

        // Updated logic for site_consumption
        const keyCheck = ["calculated_energy", "calculated_mains_energy"].includes(key) && apiConfigArray[index].purpose!=="load";
        const valueCheck = Number.isFinite(value);
        if (keyCheck && valueCheck) {
            data.site_consumption[0].value =
              data.site_consumption[0].value === "-"
                ? value
                : data.site_consumption[0].value + value
        }
        if (key === "calculated_runhour") {
          const hours = Math.floor(value / 3600)
            .toString()
            .padStart(2, "0");
          const minutes = Math.floor((value % 3600) / 60)
            .toString()
            .padStart(2, "0");
          value = !isNaN(value) ? `${hours}:${minutes}` : "-";
        } else {
          value = getValidNumber(value, paramUnitMap[key]);
        }
        summaryArray.push({
          value: res.data?.length ? value : "-",
          label: powerSourceParamKeyMap[key]?.[apiConfigArray[index].purpose] || keyParamLabel[key],
        });
        
      });
      
      (data[apiConfigArray[index].purpose] ??= []).push({name:identifier,parameters:summaryArray});


      if(!data.trendGraph[apiConfigArray[index].purpose]){
        data.trendGraph[apiConfigArray[index].purpose]=[]
      }
      if (apiConfigArray[index].purpose === "grid") {
        const seriesData: any = [];
        res.data.forEach((data: any, index: number) => {
          if (aggrPeriod === 0) {
            const isDataZero = parseFloat(data.parameter_values.calculated_mains_energy) === 0;
            seriesData.push([
              data.time * 1000,
              isDataZero ? null : parseFloat(data.parameter_values.calculated_mains_energy),
            ]);
            if (
              res.data[index + 1] &&
              res.data[index + 1].time - data.time > 300
            ) {
              seriesData.push([data.time * 1000 + 300 * 1000, null]);
            }
          } else {
            seriesData.push([
              data.time * 1000,
              parseFloat(data.parameter_values.calculated_mains_energy.sum),
            ]);
          }
        });
        data.trendGraph[apiConfigArray[index].purpose].push({
          name: identifier,
          data: seriesData,
          // color: "#F8C08D",
        });
      }

      if (apiConfigArray[index].purpose === "load") {
        const seriesData: any = [];
        res.data.forEach((data: any, index: number) => {
          if (aggrPeriod === 0) {
            const isDataZero = parseFloat(data.parameter_values.calculated_mains_energy) === 0;
            seriesData.push([
              data.time * 1000,
              isDataZero ? null : parseFloat(data.parameter_values.calculated_mains_energy),
            ]);
            if (
              res.data[index + 1] &&
              res.data[index + 1].time - data.time > 300
            ) {
              seriesData.push([data.time * 1000 + 300 * 1000, null]);
            }
          } else {
            seriesData.push([
              data.time * 1000,
              parseFloat(data.parameter_values.calculated_mains_energy.sum),
            ]);
          }
        });
        data.trendGraph[apiConfigArray[index].purpose].push({
          name: identifier,
          data: seriesData,
          // color: "#4F9BFF",
        });
      }

      if (apiConfigArray[index].purpose === "genset") {
        const seriesData: any = [];
        res.data.forEach((data: any, index: number) => {
          if (aggrPeriod === 0) {
            const isDataZero = parseFloat(data.parameter_values.calculated_energy) === 0;
            seriesData.push([
              data.time * 1000,
              isDataZero ? null : parseFloat(data.parameter_values.calculated_energy),
            ]);
            if (
              res.data[index + 1] &&
              res.data[index + 1].time - data.time > 300
            ) {
              seriesData.push([data.time * 1000 + 300 * 1000, null]);
            }
          } else {
            seriesData.push([
              data.time * 1000,
              parseFloat(data.parameter_values.calculated_energy.sum),
            ]);
          }
        });
        data.trendGraph[apiConfigArray[index].purpose].push({
          name:identifier,
          data: seriesData,
          // color: "#8B8BC1",
        });
      }
    });

    if (apiConfigArray.length > 1) {
      data.pieGraph.push({
        name: "",
        data: {},
      });
      // Add Genset data if available
      if (powerSources?.genset?.length > 0 && Array.isArray(data.genset)) {
        data.pieGraph[0].data['genset'] ??= [];
        data.genset.forEach((gensetItem:any) => {
          // Check if the specific item has the required parameter
          if (gensetItem?.parameters?.[2]?.value) {
            data.pieGraph[0].data['genset'].push({
              name: gensetItem.name, // Use the name from the current item
              y: gensetItem.parameters[2].value !== "-" ? Number(gensetItem.parameters[2].value.split(" ")[0]) : 0,
            });
          }
        });
      }

      // Add Grid data if available
      if (powerSources?.grid?.length > 0 && Array.isArray(data.grid)) {
        data.pieGraph[0].data['grid'] ??= [];
        data.grid.forEach((gridItem:any) => {
          // Check if the specific item has the required parameter
          if (gridItem?.parameters?.[0]?.value) {
            data.pieGraph[0].data['grid'].push({
              name: gridItem.name, // Use the name from the current item
              y: gridItem.parameters[0].value !== "-" ? Number(gridItem.parameters[0].value.split(" ")[0]) : 0,
            });
          }
        });

      }

      // Add Load data if available
      if (powerSources?.load?.length > 0 && Array.isArray(data.load)) {
        data.pieGraph[0].data['load'] ??= [];
        data.load.forEach((loadItem:any) => {
          // Check if the specific item has the required parameter
          if (loadItem?.parameters?.[0]?.value) {
            data.pieGraph[0].data['load'].push({
              name: loadItem.name, // Use the name from the current item
              y: loadItem.parameters[0].value !== "-" ? Number(loadItem.parameters[0].value.split(" ")[0]) : 0,
            });
          }
        });

      }
    }
    data.site_consumption[0].value = getValidNumber(data.site_consumption[0].value, paramUnitMap['calculated_energy'] || paramUnitMap['calculated_mains_energy']);
    setEnergyData(data);
    setLoading(false);
  };

  const onOk = (value: any) => {
    setFromTime(moment(value[0]).unix());
    setUptoTime(moment(value[1]).unix());
    const timeDiff = moment(value[1]).unix() - moment(value[0]).unix();
    const dataPeriod =
      timeDiff >= 7 * 86400 ? 86400 : timeDiff > 86400 ? 3600 : 0;
    setAggrPeriod(dataPeriod);
    setLoading(true);
  };

  const dateRangeChange = (value: TimePeriodKey) => {
    if (value !== "custom") {
      const timeObj = timeDurationMapping()[value];
      const timeDiff = timeObj.uptoTime - timeObj.fromTime;
      let dataPeriod =
        timeDiff >= 7 * 86400 ? 86400 : timeDiff > 86400 ? 3600 : 0;
      setFromTime(timeObj.fromTime);
      setUptoTime(timeObj.uptoTime);
      setAggrPeriod(dataPeriod);
      setLoading(true);
    }
    setSelectedDateRange(value);
  };

  const customBackBtnClicked = () => {
    setFromTime(moment().subtract(30, 'days').startOf('day').unix());
    setUptoTime(moment().endOf('day').unix());
    setAggrPeriod(86400);
    setSelectedDateRange("last_30_days");
    setLoading(true);
  };

  return {
    loading,
    fromTime,
    uptoTime,
    selectedDateRange,
    aggrPeriod,
    onOk,
    dateRangeChange,
    customBackBtnClicked,
    powerSources,
    energyData,
  };
};

export default useData;
