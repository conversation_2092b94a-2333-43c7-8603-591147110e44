.solar-site-graphs {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 28px 0;
  .graph-block {
    display: flex;
    flex-direction: column;
    gap: 20px;
    background-color: white;
    padding: 12px 10px;
    border-radius: 8px;
    border: 0.5px solid #e9e9e9;
  }
  .header-solar {
    font-weight: 600;
    font-size: 18px;
    padding: 7px;
  }
  .graph-container .highcharts-background {
    fill: transparent;
  }
  .ss-graph-middle {
    width: 100%;
    display: flex;
    margin-top: 20px;
    gap: 20px;
    .ss-middle-trends-graph {
      flex: 1;
      .margin-div {
        margin-top: 20px;
      }
    }
    .ss-middle-pi-graph {
      flex: 0.35;
    }
  }
  .battery-charge-discharge {
    display: flex;
    padding: 0 13px;
    gap: 30px;
    .value {
        display: flex;
        align-items: center;
        gap: 20px;
        strong {
            font-weight: 600;
            font-size: 19px;
        }
    }
  }
}
