import React, { Component } from "react";
import { MenuUnfoldOutlined } from "@ant-design/icons";
import {
  findSiteName,
  getPowerSourceData,
  getHeaderPowerSourceRenderFunc,
} from "../../logic/headerDataManipulation";
import "./style.less";

class SiteViewHeader extends Component {
  constructor(props) {
    super(props);
    this.findSiteName = findSiteName.bind(this);
    this.getPowerSourceData = getPowerSourceData.bind(this);
    this.getHeaderPowerSourceRenderFunc =
      getHeaderPowerSourceRenderFunc.bind(this);
    }
    render() {
      const powerSource = this.getHeaderPowerSourceRenderFunc();
      const allSources = this.getPowerSourceData();

      const sourceCount = (allSources?.solar?.length || 0) +
                          (allSources?.grid?.length || 0) +
                          (allSources?.battery?.length || 0) +
                          (allSources?.genset?.length || 0);
      const title = sourceCount > 1 ? "Power Sources" : "Power Source";
      
      return (
      <div id="site_header">
        <div className="site-name">
          <MenuUnfoldOutlined onClick={() => this.props.drawerIconClick()} />
          <span>{this.findSiteName()}</span>
        </div>
        {powerSource && <div className="power-source">
          <div className="title">{title}</div>
          {powerSource}
        </div>}
      </div>
    );
  }
}

export default SiteViewHeader;
