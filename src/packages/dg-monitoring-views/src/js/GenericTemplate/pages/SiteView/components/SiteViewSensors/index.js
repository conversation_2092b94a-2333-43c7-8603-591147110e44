import React, { Component } from "react";
import "./style.less";
import SelectTimePeriodWithCustom from "../../../../component/SelectTimePeriodWithCustom";
import {
  togglePicker,
  onOk,
  customBackBtnClicked,
  onOpenChange,
  paramRangeChange,
} from "./logic/timePeriodSelectionLogic";
import {
  getDataForGraph,
  sensorThingWiseParameters,
  thingWiseParamArrayRender,
  paramCheckboxChecked,
  graphData,
  getParamGraphConfig,
  doorStGraphData,
  getDrStGraphConfig
} from "./logic/paramGraphDetailsData";
import moment from "moment-timezone";
import AntRow from "@datoms/react-components/src/components/AntRow";
import AntCol from "@datoms/react-components/src/components/AntCol";
import GraphHighcharts from "@datoms/react-components/src/components/GraphHighcharts";
import SkeletonLoader from "@datoms/react-components/src/components/SkeletonLoader";

class SiteViewSensors extends Component {
  constructor(props) {
    super(props);
    this.state = {
      fromTime: moment().subtract(1, "day").unix(),
      uptoTime: moment().unix(),
      selectedParamDateOption: "last_24_hrs",
      dataPeriod: 0,
      dataType: "raw",
      graphLoading: true,
      customOpen: false,
    };
    this.togglePicker = togglePicker.bind(this);
    this.onOk = onOk.bind(this);
    this.customBackBtnClicked = customBackBtnClicked.bind(this);
    this.onOpenChange = onOpenChange.bind(this);
    this.paramRangeChange = paramRangeChange.bind(this);
    this.getDataForGraph = getDataForGraph.bind(this);
    this.sensorThingWiseParameters = sensorThingWiseParameters.bind(this);
    this.thingWiseParamArrayRender = thingWiseParamArrayRender.bind(this);
    this.paramCheckboxChecked = paramCheckboxChecked.bind(this);
    this.graphData = graphData.bind(this);
    this.getParamGraphConfig = getParamGraphConfig.bind(this);
    this.doorStGraphData = doorStGraphData.bind(this);
    this.getDrStGraphConfig = getDrStGraphConfig.bind(this)
  }

  componentDidUpdate(prevProps) {
    if (prevProps.latestParameterData !== this.props.latestParameterData) {
      this.sensorThingWiseParameters(true);
    }
  }

  async componentDidMount() {
    this.sensorThingWiseParameters();
  }

  render() {
    const {
      fromTime,
      uptoTime,
      selectedParamDateOption,
      graphLoading,
      paramData,
      drStSeriesData,
      thingWiseParamArr,
    } = this.state;
    const diffTimeGreaterThan7Days = uptoTime - fromTime > 8 * 86400;
    const showDoor = !diffTimeGreaterThan7Days && drStSeriesData?.length;
    if(!thingWiseParamArr?.length) return null;
    return (
      <div id="site_view_sensor">
        <div className="sensor-header">
          <div className="title">Sensors</div>
          <SelectTimePeriodWithCustom
            from_time={fromTime}
            upto_time={uptoTime}
            togglePicker={() => this.togglePicker()}
            onOk={(e, a) => this.onOk(e, a)}
            customBackBtnClicked={() => {
              this.customBackBtnClicked();
            }}
            onOpenChange={(e) => this.onOpenChange(e)}
            selected_param_date_range={selectedParamDateOption}
            paramRangeChange={(e) => this.paramRangeChange(e)}
            paramRanges={[
              {
                value: "last_24_hrs",
                title: "Last 24 hrs",
              },
              {
                value: "today",
                title: "Today",
              },
              {
                value: "yesterday",
                title: "Yesterday",
              },
              {
                value: "last_7_days",
                title: "Last 7 days",
              },
              {
                value: "this_week",
                title: "This week",
              },
              {
                value: "last_week",
                title: "Last week",
              },
              {
                value: "last_30_days",
                title: "Last 30 days",
              },
              {
                value: "this_month",
                title: "This month",
              },
              {
                value: "last_month",
                title: "Last month",
              },
              {
                value: "last_3_months",
                title: "Last 90 days",
              },
              {
                value: "last_120_days",
                title: "Last 120 days",
              },
              {
                value: "custom",
                title: "Custom",
              },
            ]}
            numberOfOptionstoShow={5}
          />
        </div>
        <AntRow gutter={20}>
          <AntCol xl={8} xxl={6} className={`${showDoor ? "params-with-door": "params-without-door"}`}>{this.thingWiseParamArrayRender()}</AntCol>
          <AntCol xl={16} xxl={18}>
            <div className="sensors-graph">
              {graphLoading ? (
                <SkeletonLoader />
              ) : 
                <div>
                  {paramData ? <GraphHighcharts graphData={this.getParamGraphConfig()} /> : ''}
                  <div className='door-st-graph'>{showDoor ? (
                    <GraphHighcharts graphData={this.getDrStGraphConfig()} />
                  ) : (
                    ""
                  )}</div>
                </div>
              }
            </div>
          </AntCol>
        </AntRow>
      </div>
    );
  }
}

export default SiteViewSensors;
