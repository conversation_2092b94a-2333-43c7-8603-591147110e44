import React, { Component } from "react";
import moment from "moment-timezone";
import CardSummary from "../CardSummary";
import SelectTimePeriodWithCustom from "../../../../component/SelectTimePeriodWithCustom";
import { getPowerSourceData, findCategoryData, findStatusParams } from "../../logic/headerDataManipulation";
import {
  dateRangeChange,
  fetchData,
  getSummaryData,
  getTrendsData,
  fetchDailyData,
  getDailyTrendsData,
} from "./logic";
import SemiCirclePieChart from "./components/Graph";
import EnergyStackedAreaGraphs from "./components/EnergyStackedAreaGraphs";
import SolarSiteGraphs from "./components/SolarSiteGraphs";
import SolarIcon from "../../images/solar.png";
import GridIcon from "../../images/grid.png";
import LoadIcon from "../../images/load.png";
import "./style.less";
import SkeletonLoader from "@datoms/react-components/src/components/SkeletonLoader";
import SolarInverterStatusHeader from "../../../DetailedView/components/SolarInverterComponents/SolarInverterStatusHeader";

export default class EnergySummary extends Component {
  constructor(props) {
    super(props);
    this.state = {
      summary_loading: true,
      trends_loading: true,
      daily_trends_loading: true,
      // last 30 Days
      fromTime: moment().subtract(30, "days").startOf("day").unix(),
      uptoTime: moment().endOf("day").unix(),
      selectedDateRange: "last_30_days",
    };

    this.fetchData = fetchData.bind(this);
    this.getTrendsData = getTrendsData.bind(this);
    this.fetchDailyData = fetchDailyData.bind(this);
    this.getDailyTrendsData = getDailyTrendsData.bind(this);
    this.getSummaryData = getSummaryData.bind(this);
    this.dateRangeChange = dateRangeChange.bind(this);
    this.getPowerSourceData = getPowerSourceData.bind(this);
    this.findCategoryData = findCategoryData.bind(this);
    this.findStatusParams = findStatusParams.bind(this);

    this.paramRanges = [
      {
        value: "last_24_hrs",
        title: "Last 24 hrs",
      },
      {
        value: "today",
        title: "Today",
      },
      {
        value: "yesterday",
        title: "Yesterday",
      },
      {
        value: "last_7_days",
        title: "Last 7 days",
      },
      {
        value: "this_week",
        title: "This week",
      },
      {
        value: "last_week",
        title: "Last week",
      },
      {
        value: "last_30_days",
        title: "Last 30 days",
      },
      {
        value: "this_month",
        title: "This month",
      },
      {
        value: "last_month",
        title: "Last month",
      },
      {
        value: "custom",
        title: "Custom",
      },
    ];
  }

  async componentDidMount() {
    await Promise.all([
      this.getSummaryData(),
      this.getTrendsData(),
      this.getDailyTrendsData(),
    ]);
  }

  togglePicker() {
    this.setState({ custom_range_open: true });
  }

  onOk(value) {
    const startValue = moment(value[0]).unix();
    const endValue = moment(value[1]).unix();
    this.setState(
      {
        fromTime: startValue,
        uptoTime: endValue,
        trends_loading: true,
        daily_trends_loading: true,
      },
      async () => {
        await Promise.all([this.getTrendsData(), this.getDailyTrendsData()]);
      },
    );
  }

  customBackBtnClicked() {
    this.setState(
      {
        fromTime: moment().subtract(30, "days").startOf("day").unix(),
        uptoTime: moment().endOf("day").unix(),
        selectedDateRange: "last_30_days",
        trends_loading: true,
        daily_trends_loading: true,
      },
      async () => {
        await Promise.all([this.getTrendsData(), this.getDailyTrendsData()]);
      },
    );
  }

  render() {
    const {
      summaryData,
      summary_loading,
      trendsData,
      trends_loading,
      daily_trends_loading,
      fromTime,
      uptoTime,
      selectedDateRange,
      categoryIcons,
      dailyTrendsInputData,
      dailyTrendsConsumptionData,
      dailyAggrPeriod,
      solarEnergyTrendData,
      sourceTrendData,
      loadTrendData,
      batteryEnergyTrendData,
    } = this.state;
    const powerSources = this.getPowerSourceData();
    const latestParameterData = this.findCategoryData(93);
    const statusParams = this.findStatusParams(93);

    if (
      powerSources?.solar.length < 1 &&
      powerSources?.grid.length < 1 &&
      powerSources?.load.length < 1
    )
      return null;
    // function getCardTitle(key){
    //   let titleMap = {};
    //   if(isSolarSite){
    //     titleMap = {
    //       "generation": "Source",
    //       "consumption": "Load",
    //     }
    //   } else {
    //     titleMap = {
    //       "generation": "Input (kWh)",
    //       "consumption": "Consumption (kWh)",
    //     }
    //   }
    //   return titleMap[key];
    // }
    const solarUtilTooltipText = (
      <>
        It represents the daily average of Solar Capacity Utilization for the
        total selected duration. <br />
        <br />
        <strong>For a day:</strong> <br />
        Solar capacity utilization =
        <code>
          {" "}
          Total Solar Energy Generation (kWh) / (3 × Solar Capacity (kW) ) × 100
        </code>{" "}
        <br />
        <br />
        <strong>Avg Solar Capacity Utilization =</strong> <br />
        Sum of Capacity utilization / No of Days. <br />
        <br />
        <i>
          Here, we have considered the maximum generation in a day as 3 Hrs.
        </i>
      </>
    );

    return (
      <section className="site_view_energy">
        { (powerSources?.solarInverter?.length > 0 && statusParams?.length > 0) && <SolarInverterStatusHeader
          latestParamData={latestParameterData}
          statusParams={statusParams}
          view = "site"
        />}
        <div className="sv-enrg-summary">
          <p className="sv-enrg-summary_title">Energy Summary</p>
          <div className="sv-enrg-summary_content">
            {powerSources?.solar.length > 0 && (
              <CardSummary
                type="vertical"
                values={summaryData?.lifetime || []}
                loading={summary_loading}
              />
            )}
            {powerSources?.solar.length > 0 && (
              <CardSummary
                title="Solar System Energy (kWh)"
                icon={SolarIcon}
                values={summaryData?.solar || []}
                loading={summary_loading}
              />
            )}
            {powerSources?.grid.length > 0 && (
              <CardSummary
                title="Grid (kWh)"
                icon={GridIcon}
                values={summaryData?.grid || []}
                loading={summary_loading}
              />
            )}
            {powerSources?.load.length > 0 && (
              <CardSummary
                title="Total Load (kWh)"
                icon={LoadIcon}
                values={summaryData?.load || []}
                loading={summary_loading}
              />
            )}
          </div>
        </div>
        <div className="sv-enrg-trends">
          <div className="sv-enrgt-head">
            <span className="sv-enrg-summary_title">Energy Trends</span>
            <SelectTimePeriodWithCustom
              from_time={fromTime}
              upto_time={uptoTime}
              togglePicker={() => this.togglePicker()}
              onOk={(e, a) => this.onOk(e, a)}
              customBackBtnClicked={() => {
                this.customBackBtnClicked();
              }}
              onOpenChange={() => 1}
              selected_param_date_range={selectedDateRange}
              paramRangeChange={this.dateRangeChange}
              paramRanges={this.paramRanges}
              numberOfOptionstoShow={5}
            />
          </div>
          <div className="sv-enrgt-kpi">
            {trendsData?.generation?.length > 0 && (
              <CardSummary
                type="kpi"
                title="Source"
                values={trendsData?.generation || []}
                loading={trends_loading}
              />
            )}
            {powerSources?.load?.length > 0 && (
              <CardSummary
                type="kpi"
                title="Load"
                values={trendsData?.consumption || []}
                loading={trends_loading}
              />
            )}
            {powerSources?.solar.length > 0 && (
              <CardSummary
                type="kpi"
                title="Avg Solar Capacity Utilization"
                info={solarUtilTooltipText}
                values={trendsData?.solar_capacity || []}
                loading={trends_loading}
              />
            )}
            {powerSources?.battery.length > 0 && (
              <CardSummary
                type="kpi"
                title="Avg Battery Utilization"
                info="It represents daily average utilization of Battery in percentage"
                values={trendsData?.battery_capacity || []}
                loading={trends_loading}
              />
            )}
          </div>

          {/* {(!isSolarSite && !trends_loading) ? (
            <div className="sv-enrgt-graph">
              <div>
                <SemiCirclePieChart
                  values={
                    trendsData?.generation?.filter(
                      (i) => i.label !== "Total",
                    ) || []
                  }
                />
              </div>
              <div>
                <SemiCirclePieChart
                  values={
                    trendsData?.consumption?.filter(
                      (i) => i.label !== "Total",
                    ) || []
                  }
                />
              </div>
            </div>
          ) : (
            ""
          )} */}

          {(!daily_trends_loading && !trends_loading) ? (
            <SolarSiteGraphs
              solarEnergyTrendData={solarEnergyTrendData}
              sourceTrendData={sourceTrendData}
              loadTrendData={loadTrendData}
              batteryEnergyTrendData={batteryEnergyTrendData}
              dailyAggrPeriod={dailyAggrPeriod}
              solarGraphValues={trendsData?.solar_graph_data}
            />
          ) : (
            <div className="sv-enrgt-loading">
              <SkeletonLoader rows={2} />
            </div>
          )}

          {/* {(!isSolarSite && !daily_trends_loading) ? (
            <EnergyStackedAreaGraphs
              dailyTrendsInputData={dailyTrendsInputData}
              dailyTrendsConsumptionData={dailyTrendsConsumptionData}
              dailyAggrPeriod={dailyAggrPeriod}
            />
          ) : (
            ""
          )} */}
        </div>
      </section>
    );
  }
}
