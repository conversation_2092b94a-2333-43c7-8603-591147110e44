import React from "react";
import CardSummary from "../CardSummary";
import SelectTimePeriodWithCustom from "../../../../component/SelectTimePeriodWithCustom";
import GraphHighcharts from "@datoms/react-components/src/components/GraphHighcharts";
import SkeletonLoader from "@datoms/react-components/src/components/SkeletonLoader";
import GraphObjectData from "../../../../../configuration/GraphObjectData";
import useData from "./logic";
import "./style.less";

const paramRanges = [
  { value: "last_24_hrs", title: "Last 24 hrs" },
  { value: "today", title: "Today" },
  { value: "yesterday", title: "Yesterday" },
  { value: "last_7_days", title: "Last 7 days" },
  { value: "this_week", title: "This week" },
  { value: "last_week", title: "Last week" },
  { value: "last_30_days", title: "Last 30 days" },
  { value: "this_month", title: "This month" },
  { value: "last_month", title: "Last month" },
  { value: "custom", title: "Custom" },
];

const GridGensetEnergy: React.FC = (props: any) => {
  const {
    loading,
    fromTime,
    uptoTime,
    selectedDateRange,
    aggrPeriod,
    onOk,
    dateRangeChange,
    customBackBtnClicked,
    powerSources,
    energyData,
  } = useData(props);
  if (powerSources?.grid.length < 1 && powerSources?.genset.length < 1 && powerSources?.load.length < 1) 
    return null;
  
  function getTrendGraph(allowedData : string[]) {
    let funcGraphObj = JSON.parse(JSON.stringify(GraphObjectData));
    funcGraphObj.graph_data.config.chart.type =
      aggrPeriod === 0 ? "area" : "column";
    funcGraphObj.graph_data.config.chart.height = 220;
    funcGraphObj.graph_data.config.legend.enabled = true;
    funcGraphObj.graph_data.config.tooltip.valueSuffix = " kWh";
    if (aggrPeriod > 0) {
      funcGraphObj.graph_data.config.plotOptions.column.stacking = "normal";
      funcGraphObj.graph_data.config.yAxis.min = 0;
    }
    else {
      funcGraphObj.graph_data.config.plotOptions.area.marker.enabled = true;
    }
    const filteredTrendGraph: any = allowedData.map((key) => {
      return energyData.trendGraph?.[key] ? energyData.trendGraph[key] : [];
    }).flat();
    
    funcGraphObj.graph_data.series_data = filteredTrendGraph;
    return funcGraphObj;
  }

  function getPieGraph(allowedData:string[],customTotalForPercentage?: number) {
    let funcGraphObj = JSON.parse(JSON.stringify(GraphObjectData));
    funcGraphObj.graph_data.config.chart.type = "pie";
    funcGraphObj.graph_data.config.chart.height = 240;
    funcGraphObj.graph_data.config.legend = {
      enabled: true,
      verticalAlign: 'bottom',
      layout: 'horizontal',
      itemHeight: 8,  
      y: 15,         
      itemStyle: {
        fontSize: '8px',    
        fontWeight: 'normal'
    }

  };
    funcGraphObj.graph_data.config.tooltip.formatter = function (this: any) {
      const value = parseFloat(this.y).toFixed(2);
      let percentageDisplay;

      if (customTotalForPercentage && customTotalForPercentage > 0) {
          const customPercentage = ((this.y / customTotalForPercentage) * 100).toFixed(2);
          percentageDisplay = `(${customPercentage}%)`;
      } else {
          percentageDisplay = `(${this.percentage.toFixed(2)}%)`;
      }
      return `<span style="color: ${this.point.color}">${this.point.name}</span>: <b>${value} kWh </b>${percentageDisplay}<br/>`;
    };
    const dataLabelConfig: any = {
      enabled: true,
      distance: -25,
      style: {
          textOutline: "none",
          opacity: 0.7,
      },
      filter: {
          operator: ">",
          property: "percentage",
          value: 10,
      },
  };

  if (customTotalForPercentage && customTotalForPercentage > 0) {
      dataLabelConfig.formatter = function (this: any) {
          const percentage = (this.y / customTotalForPercentage) * 100;
          return `${percentage.toFixed(1)}%`;
      };
  } else {
      dataLabelConfig.format = "{point.percentage:.1f}%";
  }

  funcGraphObj.graph_data.config.plotOptions.series = {
   allowPointSelect : true,
    size: "90%",
    cursor: "pointer",
    dataLabels: [
      {
        enabled: true,
        distance: 20,
        format: '{point.name}',
        style: {
          fontSize: '8px', 
          textOutline: 'none' 
        },
        filter: {
          property: 'percentage',
          operator: '>',
          value: 0
        }        
      },
      dataLabelConfig 
    ],
  };
  if (energyData.pieGraph && energyData.pieGraph.length > 0 && energyData.pieGraph[0].data) {
      const mergedPieData: any[] = [];

      allowedData.forEach((key) => {
        if (energyData.pieGraph?.[0]?.data?.[key]) {
          mergedPieData.push(...energyData.pieGraph[0].data[key]);
        }
      });
  
      const firstSeries = {
        name: "Energy Sources",
        data: mergedPieData,
        showInLegend: true,
      };

      if (customTotalForPercentage && customTotalForPercentage > 0 && firstSeries.data.length > 0) {
        const displayedSum = firstSeries.data.reduce((sum: number, item: any) => sum + item.y, 0);
        const remainderValue = customTotalForPercentage - displayedSum;
        
        if (remainderValue > 0) { 
            firstSeries.data.push({
                name: 'Unknown',      
                y: parseFloat(remainderValue.toFixed(2)),
                color: '#7086FD'     
            });
        }
    }
      funcGraphObj.graph_data.series_data = [firstSeries];
    } else {
      funcGraphObj.graph_data.series_data = [];
    }
    return funcGraphObj;
  }



const multipleSourcesPresent = 
  (powerSources?.genset?.length ?? 0) + 
  (powerSources?.grid?.length ?? 0) 
  >= 2;

  const dgPresent = powerSources?.genset.length > 0;
  const loadPresent=powerSources?.load.length > 0;
  const energyMeterPresent = powerSources?.grid.length > 0;
  const multipleDGPresent=powerSources?.genset.length>1;

  const siteConsumptionTotal = React.useMemo(() => {
    if (!energyData.site_consumption || energyData.site_consumption.length === 0) {
        return 0;
    }
    const consumptionValue = energyData.site_consumption[0]?.value;
    
    return (typeof consumptionValue === 'number' && !isNaN(consumptionValue))
    ? consumptionValue
    : parseFloat(consumptionValue) || 0;

  }, [energyData.site_consumption]);


  let kpiContainerClass = "sv-enrgt-kpi";
  if(!dgPresent) kpiContainerClass += " sv-energt-kpi-no-dg";
  if(multipleDGPresent && !energyMeterPresent)kpiContainerClass+=" sv-energt-kpi-multiple-dg";
  if(!multipleDGPresent && !energyMeterPresent)kpiContainerClass+=" sv-energt-kpi-no-em";

  let graph = loading ? (
    <div>
      <SkeletonLoader />
    </div> 
  ) : (
    <div className={"sv-enrgt-trends_graph" + (dgPresent ? "" : " sv-energt-trends_graph-no-dg")}>
      {energyData.trendGraph && (
        <div className="sv-enrgt-trend-item">
          <p>Energy Source Trends</p>
          <GraphHighcharts graphData={getTrendGraph(['grid','genset']).graph_data} />
        </div>
      )}
      {energyData.pieGraph?.length && multipleSourcesPresent ?(
        <div className="sv-enrgt-trend-item">
          <p>Source Distribution</p>
          <GraphHighcharts graphData={getPieGraph(['grid','genset']).graph_data} />
        </div>
      ):null}
    </div>
  );

  let loadGraph = loading ? (
    <div>
      <SkeletonLoader />
    </div>
  ) : (
    <div className="sv-enrgt-trends_graph-load">
      <div className="sv-enrgt-trends_graph">
              {energyData.load?.length && (
          <div className="sv-enrgt-trend-item">
            <p>Load Trends</p>
            <div className="load-horizontal-list">
            {energyData.load.map((item:any, index:any) => (
              <div key={index} className="load-item">
                <span className="load-name">{item.name}:</span>
                <span className="load-value">{item.parameters[0].value}</span>
              </div>
            ))}
          </div>
            <GraphHighcharts graphData={getTrendGraph(['load']).graph_data} />
          </div>
        )}
        {energyData.load?.length ? (
          <div className="sv-enrgt-trend-item">
            <p>Load Distribution</p>
            <GraphHighcharts graphData={getPieGraph(['load'],siteConsumptionTotal).graph_data} />
          </div>
        ) : null}
      </div>
    </div>
  );

  return (
    <section className="site_view_energy_new">
      <div className="sv-enrg-trends">
        <div className="sv-enrgt-head">
          <span className="sv-enrg-summary_title">Energy</span>
          <SelectTimePeriodWithCustom
            from_time={fromTime}
            upto_time={uptoTime}
            togglePicker={() => {}}
            onOk={onOk}
            customBackBtnClicked={customBackBtnClicked}
            onOpenChange={() => 1}
            selected_param_date_range={selectedDateRange}
            paramRangeChange={dateRangeChange}
            paramRanges={paramRanges}
            numberOfOptionstoShow={5}
          />
        </div>
        <div className={kpiContainerClass}>
          {multipleSourcesPresent && (
            <CardSummary
              type="kpi_new"
              title="Site Consumption"
              values={energyData.site_consumption || []}
              loading={loading}
            />)}
          {energyMeterPresent && (
            <CardSummary
              type="kpi_new"
              title={energyData.grid ? "Main Meter Energy" : "Energy Meter"}
              values={energyData.grid?.[0].parameters|| []}
              loading={loading}
          />)}
          {dgPresent&& (
              <CardSummary
                type="kpi_new"
                title={energyData.genset?.[0]?.name || "Genset"}
                values={energyData.genset?.[0]?.parameters||[]}
                loading={loading}
              />
          )}
          {!dgPresent && graph}
        </div>
        {multipleDGPresent &&
        <div className="sv_enrg-multipleDG">{
            energyData.genset?.slice(1)?.map((entity:any, index:any) => (
              <CardSummary
                key={index}
                type="kpi_new"
                title={entity.name}
                values={entity.parameters}
                loading={loading}
              />
            ))}
          </div>
            }
        {dgPresent && graph}
        {loadPresent && loadGraph}
      </div>
    </section>
  );
};

export default GridGensetEnergy;
