import _filter from "lodash/filter";
import _find from "lodash/find";
import ImageComponent from "@datoms/react-components/src/components/ImageComponent";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import BatteryIcon from "../components/Header/BatteryIcon";
import GridIcon from "../images/grid.png";
import GensetIcon from "../images/genset.svg";
import {SolarInverterStatusKeys} from "../../../../../js/GenericTemplate/configs/SolarInverterConfig"

export function findCategoryData(categoryId) {
  const paramData = this?.props?.latestParameterData;
  if (!paramData || paramData.length === 0) return null;
  
  const item = paramData.find((entry) => entry.category === categoryId);
  return item || null;
}

export function findStatusParams(categoryId) {
  const parameters = this.props.things.find((entry) => entry.category === categoryId)?.parameters;
  
  if(!parameters) return null;
  
  let statusParams = [];
  if (categoryId === 93 && Array.isArray(parameters)) {
    parameters.forEach((param) => {
      if (SolarInverterStatusKeys.includes(param.key)) {
        statusParams.push({
          key: param.key,
          name: param.name
        });
      }
    });
  }
  
  return statusParams;
}

export function findSiteName() {
  const { siteList, selectedSite } = this.props;
  if (!siteList || siteList?.length === 0) return "";
  const site = siteList.find((site) => site.id === selectedSite);
  return site?.name || "";
}

export function getPowerSourceData(details) {
  let siteDetails = details;
  if (this?.props) {
    const { siteDetails: site_details } = this.props;
    siteDetails = site_details;
  }
  function filterAssetsFromSiteWithPurpose(purpose) {
    const siteDetailsAssets = siteDetails?.assets;
    const filteredArray = siteDetailsAssets.filter((asset) => {
      return purpose.includes(asset.purpose);
    });
    return filteredArray;
  }
  return {
    solar: filterAssetsFromSiteWithPurpose([
      "Solar System",
      "Solar Power",
      "Solar",
    ]),
    grid: filterAssetsFromSiteWithPurpose([
      "Mains/Grid Energy Meter",
      "Grid Input",
      "Grid",
      "Main Energy Meter",
      "Grid Energy Meter",
    ]),
    battery: filterAssetsFromSiteWithPurpose(["Battery"]),
    load: filterAssetsFromSiteWithPurpose(["Consumption Load", "Load"]),
    genset: filterAssetsFromSiteWithPurpose(["Genset"]),
    solarInverter: filterAssetsFromSiteWithPurpose(["Solar Inverter"]),
  };
}

export function getHeaderPowerSourceRenderFunc() {
  const { things, siteDetails, latestParameterData } = this.props;
  function findThingsIcon(category) {
    return _find(things, { category })?.thing_category_icon;
  }
  const getAllSources = this.getPowerSourceData();

  // if ([6].includes(siteDetails?.site_type)) return null;
  if (
    getAllSources?.solar?.length < 1 &&
    getAllSources?.grid?.length < 1 &&
    getAllSources?.battery?.length < 1 &&
    getAllSources?.genset?.length < 1
  )
    return null;
  
  const stateOfCharge = [1,2,9].includes(siteDetails?.site_type) &&
    _find(latestParameterData, {
      thing_id: getAllSources?.battery?.[0]?.id,       // Assuming only one battery asset exists
  })?.data?.soc;

  // let gridCategory = 79;
  // if(siteDetails?.site_type === 9) gridCategory = 101;
  // console.log('getAllSources',getAllSources);
  return (
    <div className="power-source-icons">
      {getAllSources?.solar?.length ? (
        <AntTooltip title="Solar">
          <>
            <ImageComponent background={"#f5f5f5"} src={findThingsIcon(91)} />
          </>
        </AntTooltip>
      ) : (
        ""
      )}
      {getAllSources?.grid?.length ? (
        <AntTooltip title="Grid">
          <>
            <ImageComponent background={"#f5f5f5"} src={GridIcon} />
          </>
        </AntTooltip>
      ) : (
        ""
      )}
      {getAllSources?.battery?.length ? (
        <AntTooltip title="Battery">
          <>
            <ImageComponent background={"#f5f5f5"} src={findThingsIcon(92)} />
          </>
        </AntTooltip>
      ) : (
        ""
      )}
      {stateOfCharge ? (
        <div className="state-of-charge">
          <span>Battery Status</span>
          <BatteryIcon level= {Math.floor(stateOfCharge)}/>
          <span>{Math.floor(stateOfCharge)}%</span>
        </div>
      ) : (
        ""
      )}
      {getAllSources?.genset?.length ? (
        <AntTooltip title="DG Set">
          <>
            <ImageComponent background={"#f5f5f5"} src={findThingsIcon(18)} />
          </>
        </AntTooltip>
      ) : (
        ""
      )}
    </div>
  );
}
