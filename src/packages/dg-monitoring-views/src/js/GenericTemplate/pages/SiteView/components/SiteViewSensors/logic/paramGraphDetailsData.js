import React from "react";
import moment from "moment-timezone";
import AntTree from "@datoms/react-components/src/components/AntTree";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import { DownOutlined } from "@ant-design/icons";
import { getThingsData } from "@datoms/js-sdk";
import _uniq from "lodash/uniq";
import _filter from "lodash/filter";
import _find from "lodash/find";
import GraphObjectData from "../../../../../../configuration/GraphObjectData";
import DoorOpenIcon from "../../../../../images/Door_Open.svg";

const graphColor = [
  "#43429a",
  "#07adb1",
  "#a44a9c",
  "#f4801f",
  "#c14040",
  "#6fccdd",
  "#61c3ab",
  "#56bc7b",
  "#e2da3e",
  "#41ce00",
  "#aa4728",
  "#b3d23c",
  "#a0632a",
  "#7156a3",
  "#3d577f",
  "#ee3352",
];

const doorGraphColor = [
  "#f46f6f",
  "#b3d23c",
  "#a0632a",
  "#7156a3",
  "#3d577f",
  "#ee3352",
];

export async function getDataForGraph() {
  const { dataPeriod, dataType, fromTime, uptoTime, checkedCheckList } =
    this.state;
  let thingsArr = [],
    paramArr = [];
  if (checkedCheckList?.length) {
    checkedCheckList.map((keys) => {
      thingsArr.push(parseInt(keys.split(":")[0]));
      paramArr.push(keys.split(":")[1]);
    });
  }
  thingsArr = _uniq(thingsArr);
  paramArr = _uniq(paramArr);
  const rawType = dataType === "raw";
  const dataConfigParam = {
    data_type: rawType ? "raw" : "aggregate",
    aggregation_period: dataPeriod,
    parameters: paramArr,
    parameter_attributes: rawType ? [] : [dataType],
    things: thingsArr,
    from_time: fromTime,
    upto_time: uptoTime,
  };
  const doorStConfigParam = {
    data_type: "raw",
    aggregation_period: 0,
    parameters: ["dr_st"],
    parameter_attributes: [],
    things: thingsArr,
    from_time: fromTime,
    upto_time: uptoTime,
  };
  const [paramData, drStData] = await Promise.all([
    getThingsData(
      dataConfigParam,
      this.props.client_id,
      this.props.application_id,
    ),
    getThingsData(
      doorStConfigParam,
      this.props.client_id,
      this.props.application_id,
    ),
  ]);
  this.setState(
    {
      paramData: paramData?.data,
      drStData: drStData?.data,
      graphLoading: false,
    },
    () => {
      this.graphData();
      this.doorStGraphData();
    },
  );
}

export function graphData() {
  const { checkedCheckList, paramData, dataType } = this.state;
  const { things } = this.props;
  const seriesData = [];
  if (checkedCheckList?.length) {
    checkedCheckList.map((keys, ind) => {
      const thing = parseInt(keys.split(":")[0]);
      const param = keys.split(":")[1];
      const findThing = _find(things, { id: thing });
      const findParam = _find(findThing?.parameters, { key: param });
      const filterGraphData = _filter(paramData, function (o) {
        return o.thing_id === thing && o?.parameter_values?.[param];
      });
      const dataArr = [];
      if (filterGraphData?.length) {
        filterGraphData.map((data) => {
          dataArr.push([
            data.time * 1000,
            parseFloat(
              parseFloat(
                dataType === "raw"
                  ? data?.parameter_values?.[param]
                  : data?.parameter_values?.[param]?.["avg"],
              ).toFixed(2),
            ) || null,
          ]);
        });
      }
      seriesData.push({
        name: `${findThing?.name} (${findParam?.name})`,
        type: "line",
        yAxis: findParam.key.includes("temperature") ? 0 : 1,
        data: dataArr,
        color: graphColor[ind],
      });
    });
  }
  this.setState({
    paramGraphSeriesData: seriesData,
  });
}

export function getParamGraphConfig() {
  const { paramGraphSeriesData, checkedCheckList } = this.state;
  const filterWithTemp = checkedCheckList.filter((i) =>
    `${i}`.includes("temperature"),
  );
  const filterWithHumid = checkedCheckList.filter((i) =>
    `${i}`.includes("humidity"),
  );
  let graphObjectData = JSON.parse(JSON.stringify(GraphObjectData));
  graphObjectData.graph_data.config.xAxis.title.text = "Date & time";
  graphObjectData.graph_data.config.yAxis = [
    {
      labels: { enablled: true },
      title: { text: filterWithTemp?.length ? "Temperature" : "" },
    },
    {
      labels: { enablled: true },
      title: { text: filterWithHumid?.length ? "Humidity" : "" },
      opposite: filterWithTemp?.length ? true : false,
    },
  ];
  graphObjectData.graph_data.config.legend.enabled = true;
  graphObjectData.graph_data.config.chart.height = 280;
  graphObjectData.graph_data.series_data = paramGraphSeriesData || [];
  return graphObjectData.graph_data;
}

export function doorStGraphData() {
  const { things } = this.props;
  const { drStData } = this.state;
  const seriesData = [];
  const filterColdStorageThing = _filter(things, { category: 45 });
  if (filterColdStorageThing?.length) {
    let graphIndex = 0;
    filterColdStorageThing.map((thing) => {
      const filterData = _filter(drStData, { thing_id: thing.id });
      const dataArr = [];
      if (filterData?.length) {
        filterData.map((data) => {
          dataArr.push([
            data.time * 1000,
            parseInt(data?.parameter_values?.dr_st),
          ]);
        });
      }
      if (dataArr.length > 0) {
        seriesData.push({
          name: thing.name,
          color: doorGraphColor[graphIndex],
          data: dataArr,
        });
        graphIndex++;
      }
    });
  }
  this.setState({
    drStSeriesData: seriesData,
  });
}

export function getDrStGraphConfig() {
  const { drStSeriesData } = this.state;
  let graphObjectData = JSON.parse(JSON.stringify(GraphObjectData));
  graphObjectData.graph_data.config.chart.type = "area";
  graphObjectData.graph_data.config.xAxis.title.text = "Date & time";
  graphObjectData.graph_data.config.yAxis.max = 1;
  graphObjectData.graph_data.config.yAxis.title.text = "Door Status";
  graphObjectData.graph_data.config.legend.enabled =
    drStSeriesData?.length > 1 ? true : false;
  graphObjectData.graph_data.config.yAxis.labels = {
    formatter: function () {
      if (this.value === 0 || this.value === 1) {
        return this.value === 1 ? "Open" : "Closed";
      } else {
        return "";
      }
    },
  };
  graphObjectData.graph_data.config.chart.height = 150;
  graphObjectData.graph_data.series_data = drStSeriesData || [];
  graphObjectData.graph_data.config.tooltip = {
    formatter: function () {
      var status = this.y === 1 ? "Open" : this.y === 0 ? "Closed" : "-";
      return (
        "<b>" + moment(this.x).format("HH:mm, DD MMM") + "</b><br/>" + status
      );
    },
  };
  return graphObjectData.graph_data;
}

export function sensorThingWiseParameters(noDataUpdate) {
  const { siteDetails, things, latestParameterData } = this.props;
  const { checkedCheckList: oldCheckedCheckList } = this.state;
  const filteredColdStorageTempHumidThings = siteDetails.assets.filter(
    function (o) {
      return [45, 90].includes(o.asset_type);
    },
  );
  const finalThingWiseParamArr = [],
    checkedCheckList = oldCheckedCheckList?.length ? oldCheckedCheckList : [];
  if (filteredColdStorageTempHumidThings?.length) {
    filteredColdStorageTempHumidThings.forEach((element) => {
      const findThing = things.find((thing) => {
        return thing.id === element.id;
      });
      if (findThing) {
        const filterParams = findThing.parameters.filter(function (o) {
          return ["temperature", "humidity"].includes(o.type);
        });
        const findLatestData = latestParameterData.find((data) => {
          return data.thing_id === findThing.id;
        });
        let paramArray = [];
        if (filterParams?.length) {
          filterParams.forEach((param) => {
            if (param.type === "temperature" && !oldCheckedCheckList?.length) {
              checkedCheckList.push(`${findThing.id}:${param.key}`);
            }
            const value = !isNaN(parseFloat(findLatestData?.data?.[param.key]))
              ? parseFloat(findLatestData?.data?.[param.key]).toFixed(2)
              : "NA";
            paramArray.push({
              key: `${findThing.id}:${param.key}`,
              title: (
                <AntTooltip
                  placement="topLeft"
                  title={
                    param?.param_details?.description?.length
                      ? `Description: ${param.param_details.description}`
                      : undefined
                  }
                >
                  <div style={{ width: "200", display: "flex" }}>
                    <div
                      style={{ width: 130 }}
                    >{`${param.name} (${param.unit})`}</div>
                    <span
                      style={{
                        "margin-left": 40,
                        "font-size": 16,
                        "font-weight": "bold",
                      }}
                    >
                      {value}
                    </span>
                  </div>
                </AntTooltip>
              ),
            });
          });
        }
        const findLatestTime =
          findLatestData?.time > 0
            ? moment.unix(findLatestData?.time).format("DD MMM YYYY, HH:mm")
            : "No Data Received";
        const findLatestStatusColor =
          findLatestData?.on_off_moving_status === "1" ? "#147437" : "gray";
        const findThingHasDrSt = _find(findThing?.parameters, { key: "dr_st" });
        finalThingWiseParamArr.push({
          title: (
            <div className="thing-name">
              <div style={{ display: "flex", gap: "4px" }}>
                <AntTooltip title={findThing.name}>
                  <span
                    className="thing-name-ac"
                    style={{
                      maxWidth: findThingHasDrSt
                        ? window.innerWidth > 1600
                          ? 276
                          : 226
                        : undefined,
                    }}
                  >
                    {findThing.name}{" "}
                  </span>
                </AntTooltip>
                {findThingHasDrSt ? (
                  <span className="door-st-icon">
                    <img src={DoorOpenIcon} />
                    {parseInt(findLatestData?.data?.["dr_st"]) === 1 ? (
                      <span style={{ color: "#ff0000" }}>Open</span>
                    ): parseInt(findLatestData?.data?.["dr_st"]) === 0 ? (
                      <span style={{ color: "#147437" }}>Closed</span>
                    ) : (
                      <span style={{ color: "#147437" }}>NA</span>
                    )}
                  </span>
                ) : (
                  ""
                )}
              </div>
              <div className="status-time">
                <div
                  className="status"
                  style={{
                    "background-color": findLatestStatusColor,
                  }}
                ></div>
                {findLatestTime}
              </div>
            </div>
          ),
          key: findThing.id,
          children: paramArray,
        });
      }
    });
  }
  this.setState(
    {
      thingWiseParamArr: finalThingWiseParamArr,
      checkedCheckList,
    },
    async () => {
      if (noDataUpdate) return;
      this.getDataForGraph();
    },
  );
}

export function paramCheckboxChecked(e) {
  this.setState(
    {
      checkedCheckList: e?.filter((i) => `${i}`.includes(":")),
      graphLoading: true,
    },
    () => {
      this.getDataForGraph();
    },
  );
}

export function thingWiseParamArrayRender() {
  const { thingWiseParamArr, checkedCheckList } = this.state;
  if (thingWiseParamArr?.length) {
    return (
      <AntTree
        checkable
        checkedKeys={checkedCheckList}
        treeData={thingWiseParamArr}
        onCheck={(selectedKeys) => {
          this.paramCheckboxChecked(selectedKeys);
        }}
        defaultExpandAll
        switcherIcon={<DownOutlined />}
      />
    );
  }
}
