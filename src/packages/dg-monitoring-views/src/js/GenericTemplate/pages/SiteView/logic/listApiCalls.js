import { retrieveSitesList } from "@datoms/js-sdk";
import _find from "lodash/find";
import AntNotification from "@datoms/react-components/src/components/AntNotification";
import AntMessage from "@datoms/react-components/src/components/AntMessage";
import { getBaseUrl } from "@datoms/js-utils/src/base-url-logic";

export function openNotification(type, msg) {
  if (window.innerWidth > 576) {
    AntNotification({
      type: type,
      message: msg,
      placement: "bottomLeft",
      className: "alert-" + type,
    });
  } else {
    AntMessage(type, msg);
  }
}

export async function callSitesList() {
	const {history} = this.props;
  const { client_id } = this.props;
  const siteListResponse = await retrieveSitesList(client_id, `?page_no=${1}&results_per_page=${1000}`)
  if (siteListResponse?.status !== "success") {
    this.openNotification("error", siteListResponse?.message);
    return;
  }
  const siteList = siteListResponse?.data;
  const initialSiteId = !isNaN(parseInt(history?.location?.search?.split('site_id=')?.[1])) ? 
  	parseInt(history.location.search.split('site_id=')[1]) : siteList?.[0]?.id
  this.setState(
    {
      siteList,
      selectedSite: initialSiteId,
    },
    async () => {
      history.push(
        getBaseUrl(this.props, `site-view?site_id=${this.state.selectedSite}`),
      );
	  await this.callThingsList()
    },
  );
}
