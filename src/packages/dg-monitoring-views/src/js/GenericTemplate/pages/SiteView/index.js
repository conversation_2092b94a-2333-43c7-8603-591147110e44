import React, { Component } from "react";
import "./style.less";
import SiteViewHeader from "./components/Header";
import { callSitesList, openNotification } from "./logic/listApiCalls";
import {
  callThingsList,
  getOnlineOfflineArray,
  offlineTimeOutFunction,
  realTimeDataFunc,
} from "./logic/thingsListApiAndSocket";
import { fetchSiteDetails } from "./logic/getDetailsDataApis";
import { drawerSitesList } from "./logic/drawerDataManipulation";
import SkeletonLoader from "@datoms/react-components/src/components/SkeletonLoader";
import { getBaseUrl } from "@datoms/js-utils/src/base-url-logic";
import SiteViewSensors from "./components/SiteViewSensors";
import EnergySummary from "./components/EnergySummary";
import GridGensetEnergy from "./components/GridGensetEnergy";
import DrawerForThingWithStatus from "../../component/DrawerForThing";
import { ErrorBoundary } from "react-error-boundary";
import ErrorFallback from "./components/ErrorFallback";

class SiteView extends Component {
  constructor(props) {
    super(props);
    this.state = {
      siteList: [],
      thingsDetails: [],
      visible: false,
      loading: true,
    };
    this.callSitesList = callSitesList.bind(this);
    this.callThingsList = callThingsList.bind(this);
    this.getOnlineOfflineArray = getOnlineOfflineArray.bind(this);
    this.offlineTimeOutFunction = offlineTimeOutFunction.bind(this);
    this.fetchSiteDetails = fetchSiteDetails.bind(this);
    this.openNotification = openNotification;
    this.drawerSitesList = drawerSitesList.bind(this);
    this.realTimeDataFunc = realTimeDataFunc.bind(this);
    this.bucketTime = null;
    this.bucket = {
      raw_data: {},
    };
  }

  async componentDidMount() {
    await this.callSitesList();
  }

  drawerIconClick() {
    this.setState({
      visible: true,
    });
  }

  onSiteClicked(e) {
    const { history } = this.props;
    history.push(getBaseUrl(this.props, `site-view?site_id=${e}`));
    this.setState(
      { selectedSite: e, visible: false, loading: true },
      async () => {
        await this.callThingsList();
      },
    );
  }

  render() {
    const {
      visible,
      loading,
      siteList,
      selectedSite,
      thingsDetails,
      siteDetails,
      latestParameterData,
    } = this.state;
    const { client_id, application_id } = this.props;
    const things = thingsDetails?.things;
    return loading ? (
      <SkeletonLoader />
    ) : (
      <ErrorBoundary
        FallbackComponent={(props) => (
          <ErrorFallback
            {...props}
            message="Could not load the site details."
          />
        )}
        onReset={() => window.location.reload()}
      >
        <div id="site_view">
          <div className="site-view-body basic-page-layout-height">
            <SiteViewHeader
              siteList={siteList}
              selectedSite={selectedSite}
              siteDetails={siteDetails}
              drawerIconClick={() => this.drawerIconClick()}
              things={thingsDetails?.things}
              latestParameterData={latestParameterData}
            />
            {[1, 6, 7, 8].includes(siteDetails?.site_type) ? (
              <SiteViewSensors
                latestParameterData={latestParameterData}
                things={things}
                siteDetails={siteDetails}
                client_id={client_id}
                application_id={application_id}
              />
            ) : (
              ""
            )}
            <DrawerForThingWithStatus
              theme="light"
              thingId={selectedSite}
              drawerVisible={visible}
              drawer_data={this.drawerSitesList()}
              thingsDrawerClick={(e) => this.onSiteClicked(e)}
              closeDrawer={() => this.setState({ visible: false })}
            />
            {[6, 7, 8 , 10].includes(siteDetails?.site_type) ? (
              <GridGensetEnergy
                {...this.props}
                things={thingsDetails?.things}
                siteDetails={siteDetails}
              />
            ) : (
              ""
            )}

            {[1, 2, 9].includes(siteDetails?.site_type) ? (
              <EnergySummary
                {...this.props}
                siteDetails={siteDetails}
                things={thingsDetails?.things}
                latestParameterData={latestParameterData}
              />
            ) : (
              ""
            )}
          </div>
        </div>
      </ErrorBoundary>
    );
  }
}

export default SiteView;
