import GraphHighcharts from "@datoms/react-components/src/components/GraphHighcharts";
import GraphObjectData from "../../../../../../../configuration/GraphObjectData";
import "./style.less";
import moment from "moment-timezone";

const SolarSiteGraphs = (props: any) => {
  const {
    solarEnergyTrendData,
    sourceTrendData,
    loadTrendData,
    batteryEnergyTrendData,
    dailyAggrPeriod,
    solarGraphValues,
  } = props;

  const isSolarEnergyData = solarEnergyTrendData?.[0]?.data?.length > 0;
  const isSourceData =
    sourceTrendData?.[0]?.data?.length > 0 ||
    sourceTrendData?.[1]?.data?.length > 0;
  const isLoadData = loadTrendData?.[0]?.data?.length > 0;
  const isBatteryData =
    batteryEnergyTrendData?.[0]?.data?.length > 0 ||
    batteryEnergyTrendData?.[1]?.data?.length > 0;

  function getGraphObj() {
    let graphObjectData = JSON.parse(JSON.stringify(GraphObjectData));
    graphObjectData.graph_data.config.plotOptions.area.fillOpacity = 1;
    graphObjectData.graph_data.config.plotOptions.area.marker = {
      enabled: true,
      radius: 2,
    };

    let solarEnergyGraphObj = JSON.parse(JSON.stringify(graphObjectData));
    solarEnergyGraphObj.graph_data.config.chart.type =
      dailyAggrPeriod === 0 ? "area" : "column";
    solarEnergyGraphObj.graph_data.config.yAxis.title.text = "Energy (kWh)";
    solarEnergyGraphObj.graph_data.config.legend.enabled = true;
    solarEnergyGraphObj.graph_data.config.chart.height = 180;
    solarEnergyGraphObj.graph_data.config.tooltip.valueDecimals = 4;
    solarEnergyGraphObj.graph_data.series_data = solarEnergyTrendData;

    let sourceGraphObj = JSON.parse(JSON.stringify(graphObjectData));
    sourceGraphObj.graph_data.config.chart.type = "area";
    sourceGraphObj.graph_data.config.yAxis.title.text = "Source (kWh)";
    sourceGraphObj.graph_data.config.tooltip.shared = true;
    sourceGraphObj.graph_data.config.chart.height = 160;
    sourceGraphObj.graph_data.config.tooltip.valueDecimals = 4;
    sourceGraphObj.graph_data.config.plotOptions.area.stacking = "normal";
    if (dailyAggrPeriod === 3600) {
      sourceGraphObj.graph_data.config.tooltip.formatter = function () {
        let tooltip = `${moment.unix(this.x / 1000).format("DD MMM, HH:mm")} - 
            ${moment.unix(this.x / 1000 + 3600).format("HH:mm")}<br/>`;

        this.points.forEach(function (point: any) {
          tooltip += `<span style={{color: ${point.series.color}}}>${point.series.name}</span>: <b>${parseFloat(point.y).toFixed(2)}</b><br/>`;
        });

        return tooltip;
      };
    }
    sourceGraphObj.graph_data.config.legend.enabled = true;
    sourceGraphObj.graph_data.series_data = sourceTrendData;

    let loadGraphObj = JSON.parse(JSON.stringify(graphObjectData));
    loadGraphObj.graph_data.config.chart.type = "area";
    loadGraphObj.graph_data.config.yAxis.title.text = "Load (kWh)";
    loadGraphObj.graph_data.config.chart.height = 120;
    loadGraphObj.graph_data.config.tooltip.valueDecimals = 4;
    loadGraphObj.graph_data.series_data = loadTrendData;

    let batteryEnergyGraphObj = JSON.parse(JSON.stringify(graphObjectData));
    batteryEnergyGraphObj.graph_data.config.chart.type =
      dailyAggrPeriod === 0 ? "area" : "column";
    batteryEnergyGraphObj.graph_data.config.yAxis.title.text = "Energy (kWh)";
    batteryEnergyGraphObj.graph_data.config.legend.enabled = true;
    batteryEnergyGraphObj.graph_data.config.chart.height = 180;
    batteryEnergyGraphObj.graph_data.config.tooltip.valueDecimals = 4;
    batteryEnergyGraphObj.graph_data.series_data = batteryEnergyTrendData;

    return {
      solarEnergyGraphObj,
      sourceGraphObj,
      loadGraphObj,
      batteryEnergyGraphObj,
    };
  }

  function getPieGraph() {
    if (Object.keys(solarGraphValues?.pi ?? {}).length <= 1) return undefined; // Either none or only one of 'solar'/'grid' is present.

    let funcGraphObj = JSON.parse(JSON.stringify(GraphObjectData));
    funcGraphObj.graph_data.config.chart.type = "pie";
    funcGraphObj.graph_data.config.chart.height = 260;
    funcGraphObj.graph_data.config.legend.enabled = true;
    funcGraphObj.graph_data.config.tooltip.formatter = function () {
      const value = parseFloat(this.y).toFixed(2);
      return `<span style="color: ${this.point.color}">${this.point.name}</span>: <b>${value} kWh</b><br/>`;
    };
    funcGraphObj.graph_data.config.plotOptions.series = {
      allowPointSelect: true,
      size: "90%",
      cursor: "pointer",
      dataLabels: [
        {
          enabled: true,
          distance: 20,
        },
        {
          enabled: true,
          distance: -40,
          format: "{point.percentage:.1f}%<br/>({point.y} kWh)",
          style: {
            textOutline: "none",
            opacity: 0.7,
          },
          filter: {
            operator: ">",
            property: "percentage",
            value: 10,
          },
        },
      ],
    };

    let pieGraph: {
      name: string;
      data: { name: string; y: number; color?: string }[];
    }[] = [];
    pieGraph.push({
      name: "",
      data: [],
    });
    pieGraph[0].data.push(
      {
        name: "Solar",
        y: parseFloat(solarGraphValues?.pi?.solar),
        color: "#F8E4A0",
      },
      {
        name: "Grid",
        y: parseFloat(solarGraphValues?.pi?.grid),
        color: "#8BAAF2",
      },
    );

    funcGraphObj.graph_data.series_data = pieGraph;
    return funcGraphObj;
  }

  const graphObj = getGraphObj();
  const pieGraph = getPieGraph();
  return (
    <div className="solar-site-graphs">
      {isSolarEnergyData ? (
        <div className="solar-energy-trend graph-block">
          <div className="header-solar">Solar Energy Trend</div>
          <GraphHighcharts
            graphData={graphObj.solarEnergyGraphObj.graph_data}
          />
        </div>
      ) : (
        ""
      )}

      {isSourceData || isLoadData ? (
        <div className="ss-graph-middle">
          <div className="ss-middle-trends-graph graph-block">
            <div className="header-solar">Trends</div>
            {(!isSourceData || !isLoadData) && pieGraph ? (
              <div className="margin-div"></div>
            ) : (
              ""
            )}
            {isSourceData ? (
              <GraphHighcharts graphData={graphObj.sourceGraphObj.graph_data} />
            ) : (
              ""
            )}
            {isLoadData ? (
              <GraphHighcharts graphData={graphObj.loadGraphObj.graph_data} />
            ) : (
              ""
            )}
          </div>

          {pieGraph && (
            <div className="ss-middle-pi-graph graph-block">
              <div className="header-solar">Source Distribution</div>
              <GraphHighcharts graphData={pieGraph.graph_data} />
            </div>
          )}
        </div>
      ) : (
        ""
      )}

      {isBatteryData ? (
        <div className="ss-battery-trends graph-block">
          <div className="header-solar">Battery Energy Trends</div>

          <div className="battery-charge-discharge">
            <div className="value">
              <span>Battery Charging (kWh)</span>
              <strong>{solarGraphValues?.battery_cd?.charging}</strong>
            </div>
            <div className="value">
              <span>Battery Discharging (kWh)</span>
              <strong>{solarGraphValues?.battery_cd?.discharging}</strong>
            </div>
          </div>

          <GraphHighcharts
            graphData={graphObj.batteryEnergyGraphObj.graph_data}
          />
        </div>
      ) : (
        ""
      )}
    </div>
  );
};

export default SolarSiteGraphs;
