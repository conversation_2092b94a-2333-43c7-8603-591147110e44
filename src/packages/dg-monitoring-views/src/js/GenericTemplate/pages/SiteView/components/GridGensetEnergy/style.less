.sv-enrgt-trends_graph-load{
  margin-top:30px;
}

.site_view_energy_new {
  margin-top: 20px;
  .sv-enrg-summary_title {
    color: #4f4f4f;
    font-size: 14px;
    font-weight: 600;
  }
  .sv-enrg-summary {
    // margin-bottom: 18px;
    .sv-enrg-summary_content {
      display: flex;
      flex-wrap: wrap;
      gap: 18px;
    }
  }
  .sv-enrg-trends {
    background: #f9fbfc;
    border-radius: 20px;
    padding: 24px 22px;
    margin-top: 20px;
    .sv-enrgt-head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 22px;
      .gdv-param-range {
        width: fit-content;
      }
    }
    .sv_enrg-multipleDG{
      margin-top:-13px;
      display: grid;
      grid-template-columns: 1fr;
      gap: 18px;
      margin-bottom: 32px;
    }
    .sv-enrgt-kpi {
      display: grid;
      grid-template-columns: 1fr 1fr 3fr;
      gap: 18px;
      margin-bottom: 32px;
      &.sv-energt-kpi-multiple-dg{
        grid-template-columns: 1fr 4fr;
      }

      &.sv-energt-kpi-no-em {
        grid-template-columns: 1fr;

        &~.sv-enrgt-trends_graph {
          grid-template-columns: 1fr;
        }
      }
      &.sv-energt-kpi-no-dg {
        grid-template-columns: 1fr 4fr;
        .sv-card-kpi__title{
          padding-left: 26px;
        }
        .sv-card-kpi__content {
          margin-top: 66px;
          justify-content: center;
        }
      }
    }
    .sv-enrgt-trends_graph {
      display: grid;
      grid-template-columns: 3fr 1fr;
      gap: 22px;
      &.sv-energt-trends_graph-no-dg {
        grid-template-columns: 1fr;
      }
      .sv-enrgt-trend-item {
        padding: 17px;
        background: #ffffff;
        border: 1px solid #e7ebf0;
        border-radius: 12px;
        > p {
          color: #232323;
          font-size: 14px;
          font-weight: 600;
          display: inline-block;
          margin-bottom: 32px;
        }
        .load-horizontal-list {
          display: flex;
          gap: 16px;
          flex-wrap: wrap;
          margin-bottom: 8px;
        }
        
        .load-item {
          padding: 4px 10px;
          border-radius: 6px;
          display: flex;
          align-items: baseline; /* ✅ key fix to align text nicely */
          gap: 4px;
        }
        
        .load-name {
          margin-top:-20px;
          font-size: 12px;
          color: #333;
        }
        
        .load-value {
          margin-top:-20px;
          font-weight: bold;
          font-size: 14px;
          color: #000;
        }
        

      }
    }
  }
}
