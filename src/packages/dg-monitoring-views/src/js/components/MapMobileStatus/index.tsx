import React, { useState } from "react";
import "./style.less";
import ClusterView from "../ClusterView";
import _find from "lodash/find";
import AntDrawer from "@datoms/react-components/src/components/AntDrawer";
import AntDivider from "@datoms/react-components/src/components/AntDivider";
import CloseCircleFilled from "@ant-design/icons/CloseCircleFilled";
import FilterIconWithCount from "@datoms/react-components/src/components/FilterIconWithCount";
import legendRunning from "../../GenericTemplate/images/Legend-Running.svg";
import legendStopped from "../../GenericTemplate/images/Legend-Stopped.svg";
import legendNotConnected from "../../GenericTemplate/images/Legend-Not-Connected.svg";
import legendOffline from "../../GenericTemplate/images/Legend-Offline.svg";
import SistemaBioOnline from "../../GenericTemplate/images/Legend-Online-SistemaBio.svg"
import { useGlobalContext } from "../../../../../../store/globalStore";

export default function MapMobileStatus(props: any) {
  const [visible, setVisible] = useState(false);
  const { assetStatus, deviceStatus, filterCount, faultStatus } = props;
  const {isSistemaBioCustomer} = useGlobalContext();

  const findTotalAssets = _find(assetStatus, { total: true })?.value;
  return (
    <div id="filter_kpis">
      <div className="filter-kpi">
        <KpiDrawer />
        <div className="total-assets">
          <div className="asset-count">
            <b>{findTotalAssets}</b>Assets
          </div>
          <div className="detailed-clickable" onClick={() => drawerClicked()}>
            View Detailed kPIs
          </div>
        </div>
        <ClusterView {...props.clustrConfigs} />
      </div>
      <div className="asset-device-filter-btn">
        <FilterIconWithCount
          visible={true}
          count={filterCount}
          backgroundWhite={true}
          onClick={() => {
            props.genericFilterRefOnclick();
          }}
        />
      </div>
    </div>
  );
  function drawerClicked() {
    setVisible(true);
    props.onMobileStatusDrawerOpen(true);
  }
  function drawerClosed() {
    setVisible(false);
    props.onMobileStatusDrawerOpen(false);
  }
  function findAssetAndDeviceStatus() {
    const assetStatusDivs: any[] = [],
      deviceStatusDivs: any[] = [],
      faultStatusDivs: any[] = [];
    if (assetStatus && assetStatus.length > 0) {
      assetStatus.forEach((item: any) => {
        assetStatusDivs.push(
          <div className="value">
            <b>{item.value}</b>
            {item.title}
          </div>,
        );
      });
    }
    if (deviceStatus && deviceStatus.length > 0) {
      deviceStatus.forEach((item: any) => {
        deviceStatusDivs.push(
          <div className="value">
            <b>{item.value}</b>
            {item.title}
          </div>,
        );
      });
    }
    let totalFaults = 0;
    if(faultStatus?.length) {
      faultStatus.forEach((item: any) => {
        if(item.title === 'Faults') {
          totalFaults = parseInt(item.value);
        }
        faultStatusDivs.push(
          <div className="value">
            <b>{item.value}</b>
            {item.title}
          </div>,
        );
      });
      
    }
    return { assetStatusDivs, deviceStatusDivs, faultStatusDivs, totalFaults };
  }
  function KpiDrawer(): JSX.Element {
    const { assetStatusDivs, deviceStatusDivs, faultStatusDivs, totalFaults } = findAssetAndDeviceStatus();
    const toShowFaults = !isSistemaBioCustomer && faultStatus?.length && totalFaults > 0;
    let legendArray = [
      {
        title: "Running",
        image: legendRunning,
      },
      {
        title: "Stopped",
        image: legendStopped,
      },
      {
        title: "Not Connected",
        image: legendNotConnected,
      },
      {
        title: "Offline",
        image: legendOffline,
      },
    ];
    if(isSistemaBioCustomer){
      legendArray = [
        {
          title: "Genset On",
          image: SistemaBioOnline,
        },
        {
          title: "Genset Off",
          image: legendOffline,
        },
      ]
    }
    return (
      <AntDrawer
        visible={visible}
        onClose={() => drawerClosed()}
        headerStyle={{ display: "none" }}
        placement="bottom"
        // mask={false}
        id="drawer_kpi"
        style={{
          position: "absolute",
          bottom: "0",
          top: "auto",
          right: "0",
          "border-top-right-radius": 25,
          "border-top-left-radius": 25,
        }}
      >
        <div className="drawer-content">
          <div className="drawer-header">
            <div className="drawer-header-title">Detailed KPIs</div>
            <div>
              <CloseCircleFilled onClick={() => drawerClosed()} />
            </div>
          </div>
          <div className="drawer-body">
            {assetStatus?.length ? (
              <div className="drawer-body-content">
                <div className="drawer-body-content-title">Asset Status</div>
                <div className="drawer-body-content-value">
                  {assetStatusDivs}
                </div>
                <AntDivider />
              </div>
            ) : (
              ""
            )}
            {!isSistemaBioCustomer && deviceStatus?.length ? (
              <div className="drawer-body-content">
                <div className="drawer-body-content-title">Device Status</div>
                <div className="drawer-body-content-value">
                  {deviceStatusDivs}
                </div>
                <AntDivider />
              </div>
            ) : (
              ""
            )}
            {toShowFaults ? (
              <div className="drawer-body-content">
                <div className="drawer-body-content-title">Faults</div>
                <div className="drawer-body-content-value">
                  {faultStatusDivs}
                </div>
              </div>
            ) : (
              ""
            )}
            {!props.isAurassure ? (
              <div className="drawer-body-content">
                <AntDivider/>
                <div className="drawer-body-content-title">Legends</div>
                <div className="drawer-body-content-value">
                  {legendArray.map((item: any, index: number) => {
                    return (
                      <div key={index} className="legend">
                        <img src={item.image} alt={item.title} />
                        <span>{item.title}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            ) : (
              ""
            )}
          </div>
        </div>
      </AntDrawer>
    );
  }
}
