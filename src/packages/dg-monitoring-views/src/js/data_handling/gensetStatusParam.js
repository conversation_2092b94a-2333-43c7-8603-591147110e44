import React from "react";
import _find from "lodash/find";
import _filter from "lodash/filter";
import { ParamUnit, ParamName } from "./ParamNameUnitFind";
import { secondsToTimeFormat } from "./TimeFormatting";

function allGensetStatusParameters() {
  return [
    "gcu_mode",
    "au_m_stat",
    "mn_hlth_st",
    "min_hlthy_hz",
    "min_hlthy_volt",
    "no_of_starts",
    "no_of_trips",
    "mn_load_st",
    "gt_load_st",
    "genset_normal_stop_status",
    "genset_under_fault",
    "genset_availability",
    "com_shutdown_status",
    "com_electric_trip_status",
    "com_warn_status",
    "com_notification_status",
    "under_maintenance",
    "crnk_attmpt",
    "max_crnk_attmpt",
    "gt_brkr_st",
    "ut_ckt_brkr_st",
    "def_aft_t1",
    "scr_act_st",
    "emsn_ctrl_sys_sevr_st",
    "time_to_fia",
    "time_remain_ind_ovrd",
    "no_of_ovrd_remain",
    "amb_press",
    "amb_temp",
    "def_con",
    "eng_fuel_rate",
    "eng_fuel_eco",
    "eng_avg_fuel_eco",
    "eng_tot_fuel_used",
    "eng_trip_fuel",
    "egr_f_min",
    "egr_hl_min",
    "egr_indu",
  ];
}

function gensetStatusRealtimeParam() {
  return [
    "gcu_mode",
    "au_m_stat",
    "mn_hlth_st",
    "min_hlthy_hz",
    "min_hlthy_volt",
    "no_of_starts",
    "no_of_trips",
    "crnk_attmpt",
    "max_crnk_attmpt",
    "gt_brkr_st",
    "ut_ckt_brkr_st",
    "def_aft_t1",
    "scr_act_st",
    "emsn_ctrl_sys_sevr_st",
    "wt_in_fuel",
    "time_to_fia",
    "time_remain_ind_ovrd",
    "no_of_ovrd_remain",
    "amb_press",
    "amb_temp",
    "def_con",
    "eng_fuel_rate",
    "eng_fuel_eco",
    "eng_avg_fuel_eco",
    "eng_tot_fuel_used",
    "eng_trip_fuel",
    "egr_hl_min",
    "egr_hl_min",
    "egr_indu",
  ];
}

function gensetStatusLoadOn() {
  return ["mn_load_st", "gt_load_st"];
}

function gensetStatusAlert() {
  return [
    "gt_norm_stop_st",
    "gt_under_fault",
    "gt_availability",
    "com_shtdn_st",
    "com_elec_trip_st",
    "com_warn_st",
    "com_notificatn_st",
    "under_maintenance",
  ];
}

function getFinalGensetStatusParamManipulationFunction(
  parameters,
  selectedThinglatestParam,
) {
  let allParams = allGensetStatusParameters(),
    realtimeParams = gensetStatusRealtimeParam(),
    loadOn = gensetStatusLoadOn(),
    alert = gensetStatusAlert();
  let realTimeParam = [],
    alertArray = [],
    loadonArray = [];
  let isGensetStatus = _find(parameters, function (o) {
    return allParams.includes(o.key);
  });
  if (isGensetStatus) {
    realtimeParams.map((params) => {
      if (_find(parameters, { key: params })) {
        let paramValue = undefined;
        if (selectedThinglatestParam?.data?.[params]) {
          if (params === "gcu_mode") {
            if (parseInt(selectedThinglatestParam.data[params]) === 0) {
              paramValue = "Auto";
            } else if (parseInt(selectedThinglatestParam.data[params]) === 1) {
              paramValue = "Manual";
            }
          } else if (params === "au_m_stat") {
            if (selectedThinglatestParam.data[params] === "0") {
              paramValue = "manual";
            } else if (selectedThinglatestParam.data[params] === "1") {
              paramValue = "auto";
            } else if (selectedThinglatestParam.data[params] === "4") {
              paramValue = "manual";
            }  else if (selectedThinglatestParam.data[params] === "5") {
              paramValue = "auto";
            }  else if (selectedThinglatestParam.data[params] === "6") {
              paramValue = "scheduler";
            }  else if (selectedThinglatestParam.data[params] === "7") {
              paramValue = "cyclic";
            }
          } else if (params === "mn_hlth_st") {
            if (parseInt(selectedThinglatestParam.data[params]) === 0) {
              paramValue = "Not available";
            } else if (parseInt(selectedThinglatestParam.data[params]) === 1) {
              paramValue = "Available";
            }
          } else if (params === "egr_indu") {
            if (parseInt(selectedThinglatestParam.data[params]) === 0) {
              paramValue = "Ok";
            } else if (parseInt(selectedThinglatestParam.data[params]) === 1) {
              paramValue = "Fault Notification";
            } else if (parseInt(selectedThinglatestParam.data[params]) === 2) {
              paramValue = "Low Level Warning";
            } else if (parseInt(selectedThinglatestParam.data[params]) === 3) {
              paramValue = "Shutdown Due to Fault";
            }
          } else if (params === "gt_brkr_st" || params === "ut_ckt_brkr_st") {
            if (parseInt(selectedThinglatestParam.data[params]) === 0) {
              paramValue = "Open";
            } else if (parseInt(selectedThinglatestParam.data[params]) === 1) {
              paramValue = "Closed";
            } else if (parseInt(selectedThinglatestParam.data[params]) === 2) {
              paramValue = "Locked Out";
            } else if (parseInt(selectedThinglatestParam.data[params]) === 6) {
              paramValue = "Error";
            } else if (parseInt(selectedThinglatestParam.data[params]) === 7) {
              paramValue = "Not available";
            }
          } else if (
            params === "scr_act_st" ||
            params === "emsn_ctrl_sys_sevr_st" ||
            params === "wt_in_fuel"
          ) {
            if (parseInt(selectedThinglatestParam.data[params]) === 0) {
              paramValue = "No";
            } else if (parseInt(selectedThinglatestParam.data[params]) === 1) {
              paramValue = "Yes";
            }
          } else {
            paramValue = selectedThinglatestParam.data[params];
          }
        }
        if (paramValue) {
          realTimeParam.push({
            key: params,
            name:
              params === "mn_hlth_st"
                ? "Mains"
                : ParamName(params, parameters) +
                  (_find(parameters, { key: params })?.unit?.length
                    ? "(" +
                      ParamUnit(
                        _find(parameters, {
                          key: params,
                        }).unit,
                      ) +
                      ")"
                    : ""),
            value: ["egr_f_min", "egr_hl_min"].includes(params)
              ? secondsToTimeFormat(parseInt(paramValue), "HH:mm")
              : paramValue,
          });
        }
      }
    });
    loadOn.map((params) => {
      if (_find(parameters, { key: params })) {
        if (selectedThinglatestParam?.data?.[params]) {
          if (parseInt(selectedThinglatestParam.data[params]) === 1) {
            loadonArray.push(ParamName(params, parameters));
          }
        }
      }
    });
    alert.map((params) => {
      if (_find(parameters, { key: params })) {
        if (selectedThinglatestParam?.data?.[params]) {
          if (parseInt(selectedThinglatestParam.data[params]) === 1) {
            alertArray.push(ParamName(params, parameters));
          }
        }
      }
    });
  }
  return {
    isGensetStatus:
      realTimeParam?.length || loadonArray?.length || alertArray?.length,
    realTimeParam: realTimeParam,
    loadon: loadonArray,
    alert: alertArray,
  };
}

function cpcb4MobileParamValue(parameters, selectedThinglatestParam) {
  const paramToShow = [
    "crnk_attmpt",
    "max_crnk_attmpt",
    "def_aft_t1",
    "time_to_fia",
    "time_remain_ind_ovrd",
    "no_of_ovrd_remain",
    "amb_temp",
    "def_con",
    "kva",
    "fuel_lt",
    "min_hlthy_hz",
    "min_hlthy_volt",
    "no_of_starts",
    "no_of_trips",
    "under_maintenance",
    "eng_trip_fuel",
    "alt_volt",
    "mrun_min",
    "egr_f_min",
    "egr_hl_min",
    "st_vbat",
    "st_bat_rnhr",
    "st_bat_rnmin",
    "eng_torq_ref",
    "baro_press",
    "doc_out_temp",
    "scr_out_temp",
    "urea",
    "fuel_temp",
    "oil_serv_due",
    "air_serv_due",
    "fuel_serv_due",
    "dp_sens_ver1",
    "urea_con",
    "lub_oil_temp",
    "def_aft_t2",
    "def_press_t2",
    "scr_air_valv_t2",
    "nox_in_t1",
    "nox_out_t1",
    "def_press_t1",
    "scr_air_valv_t1",
    "def_req_quantity_t1",
    "scr_in_temp",
    "nox_in_t2",
    "nox_out_t2",
    "ecu_temp",
    "boot_press",
    "eng_torq_percent",
    "indu_timer_1",
    "indu_timer_2",
  ];
  const filterGensetStatus = _filter(parameters, function (o) {
    return paramToShow.includes(o.key);
  });
  const finalArr = [];
  if (filterGensetStatus?.length) {
    filterGensetStatus.forEach((element) => {
      finalArr.push({
        name: element.name,
        value: !isNaN(parseFloat(selectedThinglatestParam.data[element.key]))
          ? `${parseFloat(selectedThinglatestParam.data[element.key]).toFixed(2)} ${element.unit}`
          : "NA",
      });
    });
  }
  console.log('sdhfkjhjd', finalArr)
  return finalArr;
}

export { getFinalGensetStatusParamManipulationFunction, cpcb4MobileParamValue };
