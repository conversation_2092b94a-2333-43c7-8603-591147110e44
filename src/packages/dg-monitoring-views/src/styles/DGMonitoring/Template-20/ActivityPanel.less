.tab-total-container {
	padding: 10px 15px !important;
	border-radius: 20px;
	box-shadow: 10px 8px 12px #00000012;
	background: transparent linear-gradient(350deg, #f89494 0%, #ffc48f 100%) 0%
		0% no-repeat padding-box;
	.ant-card-body {
		padding: 0px !important;
	}
	.view-more-button-container {
		text-align: center;
		border-top: 1px dashed #ffffff50;
		color: #fff;
		font-style: italic;
		.view-more-class {
			cursor: pointer;
		}
	}
	.btn-container {
		display: flex;
		justify-content: center;
		margin-top: 5px;
		.event-button {
			background-color: transparent;
			border: 1px solid #fff;
			border-radius: 5px;
			color: #232323;
			.event-number {
				margin-left: 10px;
				border-radius: 50%;
				padding: 0 5px;
				font-weight: 600;
				background: #ffdebe60;
				width: 24px;
				height: 24px;
				padding-top: 5px;
			}
		}
		.event-button.activated {
			background-color: #ffe1cd;
			border: 1px solid #ffe1cd;
			.event-number {
				background: #f5c7ab;
				width: 24px;
				height: 24px;
				padding-top: 5px;
			}
		}
		.event-button:hover {
			color: #232323;
		}
		.event-button:active {
			color: #232323;
		}
		.event-button:nth-of-type(2) {
			margin-left: 20px;
		}
	}
	.notification-tab {
		height: 450px;
		overflow: auto;
		margin: 10px;
		position: relative;
		.no-activity-img {
			.no-data-icon {
				img {
					width: 145px;
					height: 96px;
				}
				.no-data-text {
					color: #fff;
					font-size: 16px;
				}
			}
		}
		.tab-data-container {
			padding: 5px 0 !important;
			display: flex;
			align-items: baseline;
			.name-icon {
				width: 10px;
				height: 10px;
				border: 1px solid #fff;
				border-radius: 50%;
				margin-right: 10px;
			}
			.event-container {
				text-align: left;
				letter-spacing: 0px;
				color: #646262;
				font-weight: 600;
				.thing-name {
					font-size: 14px !important;
					font-weight: 600;
				}
				.event-date-container {
					display: flex;
					color: #fff;
					font-weight: normal;
					.event-date {
						font-size: 13px;
						margin-top: 5px;
						display: flex;
						align-items: center;
						div {
							margin-left: 10px;
							background-color: #ffffff;
							color: #ff8500;
							padding: 3px 6px;
							border-radius: 5px;
						}
					}
				}
			}
			.inline-event-name {
				.event-date-container {
					.thing-name {
						color: #fff;
						margin-left: 5px;
					}
				}
				.date-class {
					color: #232323;
				}
			}
		}
		.multi-thing {
			.event-container {
				.thing-name {
					font-weight: normal;
					color: #232323;
					span {
						font-weight: 600;
					}
				}
			}
		}
	}
	.notification-tab::-webkit-scrollbar-thumb {
		background-color: transparent !important;
	}
	.notification-tab:hover::-webkit-scrollbar-thumb {
		background-color: #dfdfdf50 !important;
	}
	.notification-tab::-webkit-scrollbar-track {
		background-color: transparent !important;
	}
	.notify-select {
		color: #232323;
		font-size: 13px;
		font-weight: 600;
		width: 100%;
		border-bottom: 1px solid #ffffff30;
		.ant-select-selector {
			background-color: transparent !important;
			border: none !important;
			box-shadow: none !important;
			.ant-select-selection-item {
				font-size: 14px;
			}
			.event-number {
				margin-left: 10px;
				border-radius: 50%;
				padding: 0 5px;
				background: #ffdebe60;
				width: 24px;
				height: 24px;
				padding-top: 5px;
			}
			.ant-select-selection-item {
				opacity: 1 !important;
			}
		}
		.ant-select-arrow {
			top: 35%;
			color: #232323;
		}
		.ant-select-selector:active {
			box-shadow: none !important;
			border: 1px solid;
		}
	}
}

.ant-select-dropdown {
	.event-number {
		display: none;
	}
}

@media (max-width: 1440px) {
	.tab-total-container {
		.notification-tab {
			.tab-data-container {
				.event-container {
					.thing-name {
						font-size: 13px !important;
					}
					.event-date-container {
						.event-date {
							font-size: 11px;
						}
					}
				}
			}
		}
		.notify-select {
			.ant-select-selector {
				.ant-select-selection-item {
					font-size: 13px;
				}
			}
		}
	}
}

@media (max-width: 1280px) {
	.tab-total-container {
		.notification-tab {
			height: 293px;
		}
	}
}

@media (max-width: 1024px) {
	.tab-total-container {
		.notification-tab {
			height: 321px;
			.tab-data-container {
				.event-container {
					.thing-name {
						font-size: 13px !important;
					}
				}
			}
		}
		.notify-select {
			.ant-select-selector {
				.ant-select-selection-item {
					font-size: 14px;
				}
			}
		}
		.btn-container .event-button {
			font-size: 13px;
		}
	}
}
