.filter-div-container {
	display: flex;
	align-items: center;
	.search-filter {
		margin-left: 20px;
		.search-container {
			margin-top: 0px;
			.ant-input-affix-wrapper {
				.ant-input {
					box-shadow: 4px 4px 8px #e6e6e670;
					border-radius: 14px;
					width: 298px !important;
				}
			}
			.ant-input-affix-wrapper {
				.ant-input:active {
					box-shadow: 4px 4px 8px #e6e6e670;
					border-radius: 14px;
					width: 298px !important;
				}
			}
		}
	}
	.section-filter-wrapper {
		.select-filter {
			width: 35px !important;
			margin-left: 15px;
			.ant-select-selector {
				cursor: pointer;
				border: none;
				box-shadow: 4px 4px 8px hsla(0, 0%, 90.2%, 0.31);
				border-radius: 5px;
				height: 100%;
				.ant-select-selection-search {
					display: none;
				}
				.ant-select-selection-placeholder {
					width: 100%;
				}
				.ant-select-selection-item {
					background: transparent;
					border: none;
					padding-left: 7px;
					.ant-select-selection-item-content {
						margin-right: 0px !important;
					}
				}
			}
			.ant-select-selector::after {
				display: none;
			}
			.ant-select-selection--multiple {
				cursor: pointer;
				border: none;
				box-shadow: 4px 4px 8px #e6e6e650;
				border-radius: 5px;
				.ant-select-selection__choice {
					left: 50px;
					border-radius: 5px;
					border: 1px solid rgb(255, 146, 107);
					background-color: rgba(255, 219, 206, 0.5);
					max-width: 110px;
					color: rgb(255, 115, 0);
					.ant-select-selection__choice__remove {
						display: none !important;
					}
					.ant-select-selection__choice__content {
						font-weight: 600;
					}
				}
				.ant-select-selection__choice:hover {
					.ant-select-selection__choice__remove {
						display: block !important;
					}
				}
			}
			.ant-select-selection__rendered {
				display: none;
			}
			.ant-select-search__field__placeholder {
				left: 10px;
				display: block !important;
				color: #232323;
			}
		}
	}
	.tree-select-div.ant-select-open {
		.ant-select-search__field__placeholder {
			left: 10px;
			display: block !important;
			color: orangered;
		}
	}
	.total-running-dg-div {
		color: #232323;
		font-size: 15px;
	}
	.thing-seperator {
		color: #c4c2c2;
		margin: 0 20px;
	}
	.tree-select-div {
		width: 35px !important;
		margin-left: 15px;
		.ant-select-selector {
			cursor: pointer;
			border: none;
			box-shadow: 4px 4px 8px hsla(0, 0%, 90.2%, 0.31);
			border-radius: 5px;
			height: 100%;
			.ant-select-selection-search {
				display: none;
			}
			.ant-select-selection-placeholder {
				width: 35px;
				margin-top: 5px;
			}
			.ant-select-selection-item {
				background: transparent;
				border: none;
				padding-left: 7px;
				.ant-select-selection-item-content {
					margin-right: 0px !important;
				}
			}
		}
		.ant-select-arrow {
			display: none;
		}
		.ant-select-selector::after {
			display: none;
		}
		.ant-select-selection--multiple {
			cursor: pointer;
			border: none;
			box-shadow: 4px 4px 8px #e6e6e650;
			border-radius: 5px;
			.ant-select-selection__choice {
				left: 50px;
				border-radius: 5px;
				border: 1px solid rgb(255, 146, 107);
				background-color: rgba(255, 219, 206, 0.5);
				max-width: 110px;
				color: rgb(255, 115, 0);
				.ant-select-selection__choice__remove {
					display: none !important;
				}
				.ant-select-selection__choice__content {
					font-weight: 600;
				}
			}
			.ant-select-selection__choice:hover {
				.ant-select-selection__choice__remove {
					display: block !important;
				}
			}
		}
		.ant-select-selection__rendered {
			display: none;
		}
		.ant-select-search__field__placeholder {
			left: 10px;
			display: block !important;
			color: #232323;
		}
	}
	.tree-select-div.ant-select-open {
		.ant-select-search__field__placeholder {
			left: 10px;
			display: block !important;
			color: orangered;
		}
	}
	.tree-selected-values {
		margin-left: 20px;
		.ant-select-selector {
			border: none;
			.ant-select-selection-item {
				border: 1px solid #ff926b;
				background-color: rgba(255, 219, 206, 0.5);
				max-width: 110px;
				color: #ff7300;
				border-radius: 5px;
				.ant-select-selection-item-remove {
					display: none;
				}
			}
			.ant-select-selection-item:hover {
				.ant-select-selection-item-remove {
					display: block;
				}
			}
		}
		.ant-select-arrow {
			display: none;
		}
		.ant-select-selection--multiple {
			border: none;
			box-shadow: none !important;
			.ant-select-selection__choice {
				border-radius: 5px;
				border: 1px solid rgb(255, 146, 107);
				background-color: rgba(255, 219, 206, 0.5);
				max-width: 110px;
				color: rgb(255, 115, 0);
				.ant-select-selection__choice__remove {
					display: none !important;
				}
				.ant-select-selection__choice__content {
					font-weight: 600;
				}
			}
			.ant-select-selection__choice:hover {
				.ant-select-selection__choice__remove {
					display: block !important;
				}
			}
		}
	}
}

.thing-dropdown-tree {
	padding-right: 20px;
	width: 190px !important;
	min-width: 190px !important;
	.ant-select-tree {
		li {
			span.ant-select-tree-switcher {
				display: none;
			}
		}
	}
}

.sub-tree-dropdown {
	display: none;
}

@media (max-width: 1024px) {
	.filter-div-container {
		display: block;
		.tree-selected-values {
			display: flex;
			justify-content: center;
			margin-left: 0px !important;
			margin-top: 5px;
		}
	}
}

@media(max-width: 576px) {
	.thing-dropdown-tree {
		left: 35% !important
	}
}
