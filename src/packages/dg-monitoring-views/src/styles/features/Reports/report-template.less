.schedule-action-modal .body-text {
	width: 100%;
	text-align: center;
	margin-top: 10px;
}

#archive_templates_page {
	background-color: #fff;
	min-height: 100vh;
	.schedule-report-details {
		background: #f8f8f8;
		padding: 10px 15px;
		height: calc(100vh - 76px);
		overflow: auto;
		.header {
			font-size: 13px;
			font-weight: 600;
			background-color: transparent;
		}
		.each-report {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 20px;
			.report-name {
				display: flex;
				img {
					width: 19px;
					height: 22px;
				}
				.report-name-date {
					margin-left: 10px;
					.name-status {
						margin-left: 10px;
						font-size: 13px;
						color: #232323;
						display: flex;
						align-items: center;
						.name {
							width: 150px;
						}
						.paused {
							margin-left: 10px;
							background-color: #d5d5d5;
							font-size: 11px;
							padding: 1px 4px;
							border-radius: 4px;
						}
					}
					.date {
						font-size: 12px;
						color: #808080;
						font-style: italic;
					}
				}
				.report-name-date.paused {
					.name {
						color: #23232380;
					}
					.date {
						color: #80808080;
					}
				}
			}
			.ant-dropdown-menu {
				width: 124px;
				border-radius: 8px;
				.ant-dropdown-menu-item {
					font-size: 13px;
				}
			}
		}
	}
	.contains {
		// margin-top: 53px;
		margin-left: 0px;
		transition: 0.5s;
		background: #fff;
		margin-right: 0;

		.reports-template-page {
			// margin-top: 20px;

			.header-section {
				font-size: 15px;
				height: 50px;
				display: flex;
				align-items: center;
				margin-bottom: 20px;
				border-bottom: 2px solid #f9f2e2;
				padding: 22px;
				padding-bottom: 20px;
				background-color: #f9f2e2;

				.reports-head-with-search {
					display: flex;
					align-items: center;

					.archive-page-header {
						// background-color: #fff;
						display: inline;
						font-weight: 600;
						padding: 6px;
						padding-left: 12px;
						padding-right: 12px;
						// border-bottom: 1.4px solid #000;
					}

					.reports-template-search {
						width: auto;
						display: inline-block;
						padding-bottom: 0px;
						margin-right: 10px;
						// margin-bottom: 15px;
						margin-bottom: -6px;
						transition: 0.75s all;
						margin-left: 30px;

						.filter-search-display {
							border-radius: 8px;
							border: 1px solid transparent;
							transition: 0.5s all;

							input {
								height: 28px;
								font-size: 13px;
								background-color: #edf1f2;
								border: 1px solid transparent;
								border-radius: 8px;
								transition: 0.5s all;
								width: 15vw;
								font-weight: 700 !important;

								&:hover {
									background-color: #edf1f2b3;
									border-color: #1da57ae3;
								}
								&::placeholder {
									font-size: 12px !important;
									font-weight: normal !important;
								}

								&:focus,
								&:active {
									background-color: rgba(237, 241, 242, 0.2);
									border-color: #1da57a;
									box-shadow: unset;
									// width: 35vw;
								}
							}

							i {
								font-size: 12px;
								color: rgba(0, 0, 0, 0.75);
								font-weight: 600;
								margin-top: 1px;
							}
						}

						.active-search {
							input {
								background-color: rgba(237, 241, 242, 0.2);
								border-color: #1da57a;
								font-weight: 500;
							}
						}

						.ant-input-group-addon {
							height: 28px;
							border: 1px solid transparent;
							border-radius: 8px;
							outline: none;
							padding: 0;
							border: none;

							.addon-filter-type-class {
								border: 1px solid transparent;
								height: 28px;
								border-radius: 8px;
								font-size: 12px;
								width: 0 !important;
								overflow: hidden;
								padding: 0;
								margin: 0;
								border: none;
							}
						}
					}
				}

				.back-to-reports-page {
					background: #ffffffb3 /* #c1bdbd14 */;
					display: block;
					float: right;
					border-radius: 20px;
					padding: 3px 14px;
					border: 1px solid transparent /* #8080804f */;
					cursor: pointer;
					cursor: not-allowed;
					color: #2323237d;
				}
			}

			.reports-container {
				//height: calc(100% - 100px);
				height: calc(100vh - 52px);
				padding: 10px 30px;
				overflow: auto;
				display: block;

				.report-type-custom {
					display: inline-flex;
					align-items: center;
					margin-bottom: 15px;
					/* margin-left: 10px; */
					cursor: pointer;
					width: 100%;
					max-width: 338.697px;
					height: 97px;
					position: relative;
					background: linear-gradient(
						140deg,
						#6e84aa 0%,
						#cbc9fd 100%
					);
					border-radius: 10px;

					.report-type-custom-icon {
						img {
							width: 40px;
							height: 90px;
							margin-right: 10px;
						}
					}

					.report-type-custom-text {
						font-size: 16px;
						font-weight: 600;
						color: #fff;
						.sub-text {
							font-size: 11px;
							color: #e2e7f7;
							font-weight: normal;
							line-height: 18px;
						}
					}
					.go-to-btn {
						position: absolute;
						right: 20px;
						bottom: 20px;
						img {
							width: 27px;
							height: 27px;
						}
					}
				}
				.report-template-header {
					font-weight: 600;
					margin-top: 30px;
				}
				.template-type-section {
					margin-bottom: 20px;

					.template-type-section-header {
						display: flex;
						align-items: center;
						margin-bottom: 12px;
						margin-left: 10px;

						.template-type-section-icon {
							margin-bottom: -5px;
							margin-right: 3px;

							svg {
								fill: #0000ff9c;
							}
						}

						.template-type-section-text {
							font-size: 16px;
							font-weight: 500;
							margin-left: 5px;
							color: #004083;
						}
					}

					.template-type-section-body {
						.template-type-section-body-row {
							display: flex;
							align-items: flex-start;
							margin-top: 15px;
							cursor: pointer;
							color: rgb(55, 67, 117);

							.template-type-section-body-icon {
								margin-bottom: -6px;
							}

							.template-type-section-body-text {
								font-size: 14px;
								font-weight: normal;
								margin-left: 10px;
							}
						}
					}
				}

				.add-new-template {
					margin-bottom: 30px;

					.add-new-template-btn-text-container {
						display: flex;
						align-items: center;
						width: 200px;
						cursor: pointer;

						.add-new-template-btn {
							.anticon {
								vertical-align: -2.5px !important;
							}
						}

						.add-new-template-text {
							margin-left: 20px;
							font-size: 14px;
							color: rgb(55, 67, 117);
						}
					}
				}
			}

			.template-menu-devider {
				height: 1.2em !important;
				margin: 0 10px !important;
			}

			.template-application-select,
			.template-category-select {
				width: 200px !important;
				margin-left: 12px !important;

				.ant-select-selection {
					height: 26px !important;
					font-size: 12px !important;

					.ant-select-selection__rendered {
						line-height: 25px !important;
					}
				}
			}

			.template-category-select {
				margin-left: 10px !important;
			}

			.category-devider {
				margin-left: 20px !important;
			}
		}
	}

	.contains.collapsed-side {
		margin-left: 80px !important;
		transition: 0.5s;
	}

	.margin-bottom-4px {
		margin-bottom: 4px !important;
	}
}

@media (max-width: 576px) {
	#archive_templates_page {
		padding: 10px;
		height: calc(100vh - 107px);
		.contains {
			margin-left: 0px !important;
			.ant-tabs-nav {
				margin-bottom: 0px;
			}
			.reports-template-page {
				.reports-container {
					padding: 20px !important;
					height: calc(100vh - 107px - 46px);
					.report-type-custom {
						width: 100%;
					}
				}
			}
		}
		.schedule-report-details {
			height: calc(100vh - 107px - 106px);
			margin: 10px 10px 20px 10px;
			border-radius: 20px;
		}
	}
}
