import React from "react";

function commonHistoryPush() {
  const { url_name, defaultSelectedValues, selectedValues, searchObjectData } =
    this.state;
  const { url, history } = this.props;
  let urlName = [...url_name];
  let changedArray = selectedValues
    .map((data, index) => ({ data, index }))
    .filter(({ data }) => !defaultSelectedValues.includes(data));
  let filterArray = changedArray.map((values) => {
    let finalValues = values.data;
    if (Array.isArray(finalValues)) {
      finalValues = encodeURIComponent(finalValues.toString());
    }
    return urlName[values.index] + ":" + finalValues;
  });
  let searchedValue = searchObjectData?.value;
  let searchInArray = [];
  if (searchedValue && searchedValue.length > 0) {
    let search_url_key = searchObjectData?.url_name
      ? searchObjectData?.url_name
      : "search";
    searchInArray = [search_url_key + ":" + searchedValue];
  }
  filterArray = filterArray.concat(searchInArray);
  console.log("filterArray", filterArray, changedArray, searchedValue)
  if (
    (changedArray && changedArray.length) ||
    (searchedValue && searchedValue.length > 0)
  ) {
    const separator = this.props.replaceSeparator && url.includes("?") ? "&" : "?";
    history.push(url + separator + "filter=" + [...filterArray]);
  } else {
    history.push(url);
  }
  this.props.onUrlChange();
  this.filterCountUpdate();
}
function onSearch(e) {
  const { searchObjectData } = this.state;
  let searchData = searchObjectData;
  searchData.value = e;
  this.setState(
    {
      searchObjectData: searchData,
    },
    () => {
      this.commonHistoryPush();
    },
  );
}
function filterOutSidePanel(selectedValues, index) {
  const { selectedValuesOutsidePanel } = this.state;
  let selectedValueOutsidePanel = [...selectedValuesOutsidePanel];
  if (Array.isArray(selectedValues) && selectedValues.length) {
    selectedValues.map((value, ind) => {
      if (selectedValueOutsidePanel && selectedValueOutsidePanel[ind]) {
        selectedValueOutsidePanel[ind].selectValue = value;
      }
    });
  }
  return selectedValueOutsidePanel;
}
function optionOnChange(e, index, coming_from_filter_with_search) {
  const { initial_state_selected_values_array } = this.state;
  let preSelectedValues = [...this.state.selectedValues];
  if(Array.isArray(e) && e[0] && e[0].hasOwnProperty('halfChecked')) {
    e = e.map(item => item.value)
  }
  preSelectedValues[index] = e === undefined ? "" : e;
  let selectedValues = this.getPanelFilterData(preSelectedValues, index)
    ?.filteredPanelData?.selected_values;
  console.log("gdaivvv", selectedValues);
  this.setState(
    {
      selectedValuesOutsidePanel: this.filterOutSidePanel(
        selectedValues,
        index,
      ),
      selectedValues,
      selected_values_inside_panel: selectedValues,
      selected_values_inside_panel_after_submit: selectedValues,
      panel_filter_data: this.getPanelFilterData(selectedValues, index)
        ?.panelFilterData,
    },
    () => {
      console.log("gdaivvv", this.state.selectedValuesOutsidePanel);
      if (coming_from_filter_with_search) {
        this.commonHistoryPush();
      }
    },
  );
}
function getPanelFilterData(selectedValues, index) {
  const { panel_filter_data } = this.state;
  let filteredPanelData = this.props.panelFilterSelect(
    selectedValues,
    panel_filter_data?.[index]?.url_name,
  );
  let panelFilterData = [...panel_filter_data];
  panelFilterData.map((item) => {
    if (filteredPanelData?.total_options?.[item.url_name]) {
      item["optionData"] = filteredPanelData.total_options[item.url_name];
    }
    console.log(
      "hideField",
      item.url_name,
      filteredPanelData?.hide_field?.[item.url_name],
    );
    if (filteredPanelData?.hide_field) {
      item["hideField"] = filteredPanelData?.hide_field?.[item.url_name];
    }
  });
  return {
    filteredPanelData: filteredPanelData,
    panelFilterData: panelFilterData,
  };
}
function optionOnChangeInsidePanel(e, index) {
  const { selected_values_inside_panel, initial_state_selected_values_array } =
    this.state;
  if(Array.isArray(e) && e[0] && e[0].hasOwnProperty('halfChecked')) {
    e = e.map(item => item.value)
  }
  let selectedValues = [...selected_values_inside_panel];
  selectedValues[index] = e === undefined ? "" : e;
  this.setState({
    panel_filter_data: this.getPanelFilterData(selectedValues, index)
      ?.panelFilterData,
    selected_values_inside_panel: this.getPanelFilterData(selectedValues, index)
      ?.filteredPanelData.selected_values,
    panel_selected_value: selectedValues,
    panel_selected_index: index,
  });
}

function submitButtonClick() {
  const {
    selected_values_inside_panel,
    panel_selected_value,
    panel_selected_index,
  } = this.state;
  this.setState(
    {
      selectedValuesOutsidePanel: this.filterOutSidePanel(
        panel_selected_value,
        panel_selected_index,
      ),
      selectedValues: selected_values_inside_panel,
      selected_values_inside_panel_after_submit: selected_values_inside_panel,
      icon_clicked: false,
    },
    () => {
      this.commonHistoryPush();
      if (typeof this.props.drawerApplyCallback === "function") {
        this.props.drawerApplyCallback();
      }
    },
  );
}

function filterCountUpdate() {
  const { history } = this.props;
  let newArray = history.location.search.includes("filter")
    ? history.location.search.split("filter=")[1]
    : [];
  newArray = newArray && newArray.length ? newArray.split(",") : [];
  this.setState({ changed_array: newArray });
}

export {
  commonHistoryPush,
  optionOnChange,
  optionOnChangeInsidePanel,
  submitButtonClick,
  getPanelFilterData,
  filterCountUpdate,
  filterOutSidePanel,
  onSearch,
};
