import React, { Component } from "react";
import { TreeSelect } from 'antd';
import AntSelect from "@datoms/react-components/src/components/AntSelect";
import AntOption from "@datoms/react-components/src/components/AntOption";
import AntInput from "@datoms/react-components/src/components/AntInput";
import AntButton from "@datoms/react-components/src/components/AntButton";
import AntDrawer from "@datoms/react-components/src/components/AntDrawer";
import AntRangePicker from "@datoms/react-components/src/components/AntRangePicker";
import FilterOutlined from "@ant-design/icons/FilterOutlined";
import FilterSelectWithSearch from "@datoms/react-components/src/components/FilterSelectWithSearch";
import SelectWithRangepicker from "@datoms/react-components/src/components/SelectWithRangepicker";
import AntTag from "@datoms/react-components/src/components/AntTag";
import _filter from "lodash/filter";
import "./style.less";
import {
  commonHistoryPush,
  optionOnChange,
  optionOnChangeInsidePanel,
  submitButtonClick,
  getPanelFilterData,
  filterCountUpdate,
  filterOutSidePanel,
  onSearch,
} from "../logic/filterLogic";
import moment from "moment-timezone";
import FilterIconWithCount from "@datoms/react-components/src/components/FilterIconWithCount"

class GenericFilter extends Component {
  constructor(props) {
    const { filterData, searchObject } = props;
    super(props);
    let filteredDataModified = JSON.parse(JSON.stringify(props?.filterData));
    let selectedValuesInsidePanel = filteredDataModified.map(
      (item) => {
        const { selectValue, type } = item;
        if(type === 'tree_select') {
          return item.component_props?.value
        }
        return selectValue
      },
    );
    this.state = {
      icon_clicked: false,
      selectedValuesOutsidePanel: filteredDataModified,
      selectedValues: filterData.map(({ selectValue }) => selectValue),
      defaultSelectedValues: props?.default || [],
      selected_values_inside_panel: selectedValuesInsidePanel,
      selected_values_inside_panel_after_submit: selectedValuesInsidePanel,
      initial_state_selected_values_array: selectedValuesInsidePanel,
      panel_filter_data: filteredDataModified,
      url_name: filterData.map(({ url_name }) => url_name),
      changed_array: [],
      searchObjectData: searchObject,
    };
    this.commonHistoryPush = commonHistoryPush.bind(this);
    this.optionOnChange = optionOnChange.bind(this);
    this.optionOnChangeInsidePanel = optionOnChangeInsidePanel.bind(this);
    this.submitButtonClick = submitButtonClick.bind(this);
    this.getPanelFilterData = getPanelFilterData.bind(this);
    this.filterCountUpdate = filterCountUpdate.bind(this);
    this.filterOutSidePanel = filterOutSidePanel.bind(this);
    this.onSearch = onSearch.bind(this);
  }
  componentDidMount() {
    this.filterCountUpdate();
  }
  handleClear(e) {}
  parseNumberString(inputString) {
    const beforeArray = [
      "=",
      "<=",
      ">=",
      "<",
      ">",
      "empty",
      "notEmpty",
      "between",
      "contains",
      "notContain",
      "is",
      "isNot",
    ];
    let beforeValue = "",
      inputValue = "",
      afterValue = "";
    if (inputString && inputString.includes("_")) {
      const firstUnderscoreIndex = inputString.indexOf("_");
      beforeValue = inputString.substring(0, firstUnderscoreIndex);
      let secondValue = inputString.substring(firstUnderscoreIndex + 1);
      if (beforeValue.includes("between")) {
        inputValue = secondValue.split("-")[0];
        afterValue = secondValue.split("-")[1] || "";
      } else {
        inputValue = secondValue;
      }
    } else {
      beforeValue = beforeArray.includes(inputString) ? inputString : "";
      inputValue = !isNaN(parseFloat(inputString)) ? inputString : "";
    }

    return { beforeValue, inputValue, afterValue };
  }
  getAllSelects() {
    const { rangePickerConfig } = this.props;
    const { panel_filter_data, selected_values_inside_panel } = this.state;
    let totalSelect = [];
    if (panel_filter_data && panel_filter_data.length) {
      panel_filter_data.forEach((filter, index) => {
        if (filter.hideField) return;
        if (!filter.is_outside_filter_drawer) {
          if (filter.type === "date") {
            let dateValues =
              selected_values_inside_panel[index] &&
              selected_values_inside_panel[index].includes("-")
                ? selected_values_inside_panel[index].split("-")
                : ["", ""];
            let fromTime = !isNaN(parseInt(dateValues[0]))
              ? parseInt(dateValues[0])
              : undefined;
            let uptoTime = !isNaN(parseInt(dateValues[1]))
              ? parseInt(dateValues[1])
              : undefined;
            totalSelect.push(
              <div className="total-select-filter">
                <div className="label">
                  {filter?.label}
                </div>
                <div className="total-select-div">
                  <SelectWithRangepicker
                    fromTime={fromTime}
                    uptoTime={uptoTime}
                    config={
                      rangePickerConfig?.datePickerConfig
                        ? rangePickerConfig.datePickerConfig
                        : filter.datePickerConfig
                    }
                    ranges={
                      rangePickerConfig?.ranges
                        ? rangePickerConfig.ranges
                        : filter.ranges
                    }
                    onRangeChange={(dateArray) => {
                      let finalValue = Array.isArray(dateArray)
                        ? dateArray.join("-")
                        : "";
                      this.optionOnChangeInsidePanel(finalValue, index);
                    }}
                    // onCalendarChange={(val) => {
                    // 	this.setState({
                    // 		dateArray: val,
                    // 	});
                    // }}
                  />
                </div>
              </div>,
            );
          } else if (filter.type === "date_select") {
            let totalValue =
              selected_values_inside_panel[index] &&
              selected_values_inside_panel[index] !== ""
                ? decodeURIComponent(selected_values_inside_panel[index])
                : "";
            let parsedValues = this.parseNumberString(totalValue);
            let beforeValue = parsedValues.beforeValue;
            let inputValue = parsedValues.inputValue;
            let afterValue = parsedValues.afterValue;
            // console.log('parsedValues-date'+filter.url_name, parsedValues);
            const selectBefore = (
              <AntSelect
                value={beforeValue}
                onChange={(val) => {
                  let finalValue;
                  if (["empty", "notEmpty"].includes(val)) {
                    finalValue = val;
                  } else {
                    finalValue =
                      val +
                      "_" +
                      inputValue +
                      (val === "between" ? "-" + afterValue : "");
                  }
                  this.optionOnChangeInsidePanel(finalValue, index);
                }}
              >
                <AntOption value="=">On</AntOption>
                <AntOption value=">">{"After"}</AntOption>
                <AntOption value="<">{"Before"}</AntOption>
                {/*<AntOption value=">=">{'On or After'}</AntOption>*/}
                {/*<AntOption value="<=">{'On or Before'}</AntOption>*/}
                <AntOption value="between">Between</AntOption>
              </AntSelect>
            );
            let inputField = "";
            inputField = (
              <span className="ant-input-group-wrapper custom-input  filter-main-wrapper">
                <span className="ant-input-wrapper ant-input-group">
                  <span className="ant-input-group-addon">{selectBefore}</span>
                  <AntRangePicker
                    allowClear
                    className="sub_drawer_datepicker"
                    format={"DD MMM YYYY"}
                    showNow={false}
                    type={"date_picker_default"}
                    showTime={false}
                    onChange={(date) => {
                      let finalValue =
                        beforeValue +
                        "_" +
                        (date ? date.startOf("day").unix() : "") +
                        (beforeValue === "between" ? "-" + afterValue : "");
                      console.log("finalValue", beforeValue, finalValue);
                      this.optionOnChangeInsidePanel(finalValue, index);
                    }}
                    value={
                      inputValue && inputValue !== ""
                        ? moment.unix(inputValue)
                        : undefined
                    }
                  />
                  {beforeValue === "between" ? (
                    <>
                      <span className="gnrc-input-split">and</span>
                      <AntRangePicker
                        allowClear
                        className="sub_drawer_datepicker"
                        format={"DD MMM YYYY"}
                        showNow={false}
                        type={"date_picker_default"}
                        showTime={false}
                        value={
                          afterValue && afterValue !== ""
                            ? moment.unix(afterValue)
                            : undefined
                        }
                        onChange={(e) => {
                          let finalValue =
                            beforeValue +
                            "_" +
                            inputValue +
                            "-" +
                            (e ? e.endOf("day").unix() : "");
                          this.optionOnChangeInsidePanel(finalValue, index);
                        }}
                      />
                    </>
                  ) : (
                    ""
                  )}
                </span>
              </span>
            );
            totalSelect.push(
              <div className="total-select-filter double-select-input-field">
                <div className="label">
                  2
                  {/* {filter?.label} */}
                </div>
                <div className="total-select-div">
                  <FilterOutlined className="filter-icon" />
                  {inputField}
                  {/* {
											!selected_values_inside_panel[index] &&
										} */}
                  {/* <div>Warning</div> */}
                </div>
              </div>,
            );
          } else if (filter.type === "advance") {
            let totalValue =
              selected_values_inside_panel[index] &&
              selected_values_inside_panel[index] !== ""
                ? decodeURIComponent(selected_values_inside_panel[index])
                : "";
            let parsedValues = this.parseNumberString(totalValue);
            let beforeValue = parsedValues.beforeValue;
            let inputValue = parsedValues.inputValue;
            let afterValue = parsedValues.afterValue;
            console.log("parsedValues", parsedValues);
            try {
              inputValue =
                filter.input_type === "select" &&
                filter.select_mode === "multiple"
                  ? JSON.parse(inputValue)
                  : inputValue;
            } catch (error) {
              //
            }
            let inputDisabled = ["empty", "notEmpty"].includes(beforeValue);
            const selectBefore = (
              <AntSelect
                value={beforeValue}
                onChange={(val) => {
                  let finalValue;
                  if (["empty", "notEmpty"].includes(val)) {
                    finalValue = val;
                  } else {
                    finalValue =
                      val +
                      "_" +
                      inputValue +
                      (beforeValue === "between" ? "-" + afterValue : "");
                  }
                  this.optionOnChangeInsidePanel(finalValue, index);
                }}
              >
                {filter.before_options?.length ? (
                  filter.before_options.map((before) => (
                    <AntOption value={before.value}>{before.title}</AntOption>
                  ))
                ) : filter.input_type === "text" ? (
                  <>
                    <AntOption value="contains">contains</AntOption>
                    <AntOption value="notContain">doesn't contain</AntOption>
                    <AntOption value="is">is</AntOption>
                    <AntOption value="isNot">is not</AntOption>
                  </>
                ) : (
                  <>
                    <AntOption value="=">=</AntOption>
                    <AntOption value=">">{">"}</AntOption>
                    <AntOption value="<">{"<"}</AntOption>
                    <AntOption value=">=">{">="}</AntOption>
                    <AntOption value="<=">{"<="}</AntOption>
                    <AntOption value="between">between</AntOption>
                  </>
                )}
                {filter.before_options?.length ? (
                  ""
                ) : (
                  <>
                    <AntOption value="empty">is empty</AntOption>
                    <AntOption value="notEmpty">is not empty</AntOption>
                  </>
                )}
              </AntSelect>
            );
            let inputField = "";
            if (filter.input_type === "select") {
              let selectOptions = filter.optionData;
              let tagWiseData = filter.tagWiseData;
              if (filter.is_options_dynamic) {
                const findFilterItem = this.props.filterData.find(
                  (fil) => fil.url_name === filter.url_name,
                );
                if (Array.isArray(findFilterItem?.optionData)) {
                  selectOptions = findFilterItem.optionData;
                }
                tagWiseData = findFilterItem.tagWiseData;
              }
              inputField = (
                <span className="ant-input-group-wrapper custom-input  filter-main-wrapper">
                  <span className="ant-input-wrapper ant-input-group ant-input-group-flex">
                    <span className="ant-input-group-addon">
                      {filter?.selectBefore ? selectBefore : undefined}
                    </span>
                    <AntSelect
                      mode={filter.select_mode}
                      disabled={inputDisabled}
                      placeholder={inputDisabled ? "" : filter.placeholder}
                      value={inputValue || undefined}
                      tagRender={(props) => tagRender(props, tagWiseData)}
                      onChange={(value) => {
                        let finalValue =
                          beforeValue +
                          "_" +
                          (value?.length
                            ? encodeURIComponent(JSON.stringify(value))
                            : "");
                        console.log(
                          "finalValue",
                          value,
                          beforeValue,
                          finalValue,
                        );
                        this.optionOnChangeInsidePanel(finalValue, index);
                      }}
                    >
                      {selectOptions.map((item) => (
                        <AntOption value={item.value}>{item.title}</AntOption>
                      ))}
                    </AntSelect>
                  </span>
                </span>
              );
            } else {
              inputField = (
                <>
                  <AntInput
                    type={filter.input_type}
                    disabled={inputDisabled}
                    className={
                      "kva-filter" + !filter?.selectBefore
                        ? " filter-main-wrapper"
                        : ""
                    }
                    placeholder={inputDisabled ? "" : filter.placeholder}
                    defaultValue={inputValue}
                    addonBefore={
                      filter?.selectBefore ? selectBefore : undefined
                    }
                    onClear={(e) => this.handleClear(e)}
                    value={inputValue}
                    onChange={(e) => {
                      let finalValue =
                        beforeValue +
                        "_" +
                        e.target.value +
                        (beforeValue === "between" ? "-" + afterValue : "");
                      console.log("finalValue", beforeValue, finalValue);
                      this.optionOnChangeInsidePanel(finalValue, index);
                    }}
                  />
                  {beforeValue === "between" ? (
                    <>
                      <span className="gnrc-input-split">and</span>
                      <AntInput
                        value={afterValue}
                        type={filter.input_type}
                        placeholder={filter.placeholder}
                        className="gnrc-after-input"
                        onChange={(e) => {
                          let finalValue =
                            beforeValue +
                            "_" +
                            inputValue +
                            "-" +
                            e.target.value;
                          this.optionOnChangeInsidePanel(finalValue, index);
                        }}
                      />
                    </>
                  ) : (
                    ""
                  )}
                </>
              );
            }
            totalSelect.push(
              <div className="total-select-filter double-select-input-field">
                <div className="label">
                  {filter?.label}
                </div>
                <div className="total-select-div">
                  <FilterOutlined className="filter-icon" />
                  {inputField}
                  {/* {
											!selected_values_inside_panel[index] &&
										} */}
                  {/* <div>Warning</div> */}
                </div>
              </div>,
            );
          } else if(filter.type === 'tree_select') {
            console.log("selected_values_inside_panel[index]", selected_values_inside_panel[index])
            totalSelect.push(
              <div className="total-select-filter">
                <div className="label">{filter?.label}</div>
                <div className="total-select-div">
                  <FilterOutlined className="filter-icon" />
                  <TreeSelect
                    {...filter.component_props}
                    {...(filter.inner_component_props && filter.inner_component_props)}
                    value={
                      selected_values_inside_panel[index] &&
                      selected_values_inside_panel[index] !== ""
                        ? selected_values_inside_panel[index]
                        : undefined
                    }
                    showCheckedStrategy={
                      filter.component_props.showCheckedStrategy
                        ? TreeSelect[filter.component_props.showCheckedStrategy]
                        : undefined
                    }
                    onChange={(e) => this.optionOnChangeInsidePanel(e, index)}
                    className="filter-drop"
                  />
                </div>
              </div>,
            );
          } else {
            let selectOptions = filter.optionData;
            if (filter.is_options_dynamic) {
                const findFilterItem = this.props.filterData.find(
                  (fil) => fil.url_name === filter.url_name,
                );
                if (Array.isArray(findFilterItem?.optionData)) {
                  selectOptions = findFilterItem.optionData;
                }
            }
            if (!selectOptions?.length) return;

            totalSelect.push(
              <div className="total-select-filter">
                <div className="label">
                  {filter?.label}
                </div>
                <div className="total-select-div">
                  <FilterOutlined className="filter-icon" />
                  <AntSelect
                    t={this.props.t}
                    allowClear={filter.allowClear}
                    showSearch
                    className={
                      filter.multiSelect ? "generic-filter-multiple-select" : ""
                    }
                    onClear={(e) => this.handleClear(e)}
                    placeholder={filter.placeholder}
                    value={
                      selected_values_inside_panel[index] &&
                      selected_values_inside_panel[index] !== ""
                        ? selected_values_inside_panel[index]
                        : undefined
                    }
                    onChange={(e) => this.optionOnChangeInsidePanel(e, index)}
                    mode={filter.multiSelect ? "multiple" : "single"}
                    filterOption={(input, option) =>
                      (option?.label ?? "")
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                    options={selectOptions.map((options) => {
                      return {
                        value: options.value,
                        label: this.props.t? this.props.t(options.title): options.title,
                      };
                    })}
                  />
                </div>
              </div>,
            );
          }
        }
      });
    }
    return totalSelect;
  }

  onDateRangeChange(dateRange) {
    let startDate = dateRange[0].unix();
    let endDate = dateRange[1].unix();
    this.optionOnChange(`${startDate}-${endDate}`, 5, true);
  }

  filterWithSearchComponentOptions() {
    const { searchObjectData } = this.state;
    const { selectedValuesOutsidePanel } = this.state;
    let filterSelectedValues = JSON.parse(
      JSON.stringify(selectedValuesOutsidePanel),
    );
    filterSelectedValues.forEach((filter_item) => {
      if(filter_item.hideField) return;
      if(filter_item.is_inside_filter_drawer) {
        filter_item.hideField = true;
        return;
      }
      filter_item.label = filter_item.no_outside_label ? "" : filter_item.label;
      if (filter_item.is_options_dynamic) {
        const findFilterItem = this.props.filterData.find(
          (fil) => fil.url_name === filter_item.url_name,
        );
        if (Array.isArray(findFilterItem?.optionData)) {
          filter_item.optionData = findFilterItem.optionData;
        }
        if(findFilterItem?.component_props) {
          filter_item.component_props = findFilterItem.component_props
        }
      }
    });
    return (
      <FilterSelectWithSearch
        t={this.props.t}
        filterData={filterSelectedValues}
        searchObject={searchObjectData}
        onSearch={(e) => this.onSearch(e)}
        applyFilterSelect={(value, index) =>
          this.optionOnChange(value, index, true)
        }
        hideTagContainer={this.props.hideTagContainer}
        backgroundWhite={this.props.backgroundWhite}
        is_all_disabled={this.props.is_all_disabled}
        rangePickerConfig={this.props.rangePickerConfig}
        {...(this.props.onPopupScroll && {
          onPopupScroll: (e, key) => this.props.onPopupScroll(e, key),
        })}
        {...(this.props.selectSearch && {
          selectSearch: (e, key) => this.props.selectSearch(e, key),
        })}
        {...(this.props.selectOnBlur && {
          selectOnBlur: (key) => this.props.selectOnBlur(key),
        })}
        {...(this.props.selectOnDropdownVisibleChange && {
          selectOnDropdownVisibleChange: (open, key) =>
            this.props.selectOnDropdownVisibleChange(open, key),
        })}
      />
    );
  }
  filterIconClicked() {
    if (this.props.is_all_disabled) return;

    const { panel_filter_data } = this.state;
    this.setState({
      icon_clicked: true,
      default_panel_filter_data: JSON.parse(JSON.stringify(panel_filter_data)),
    });
  }
  drawerOnClose() {
    const {
      selected_values_inside_panel_after_submit,
      selected_values_inside_panel,
      default_panel_filter_data,
    } = this.state;
    this.setState(
      {
        icon_clicked: false,
        selected_values_inside_panel: selected_values_inside_panel_after_submit
          ? selected_values_inside_panel_after_submit
          : selected_values_inside_panel,
        panel_filter_data: default_panel_filter_data,
      },
      () => {
        if (typeof this.props.drawerCancelCallback === "function") {
          this.props.drawerCancelCallback();
        }
      },
    );
  }
  render() {
    const { icon_clicked, changed_array } = this.state;
    const { width, height, filterData, hideIconCount, hide_filter_icon } =
      this.props;
    const showDrawerIcon =
      !hide_filter_icon &&
      filterData &&
      filterData.length &&
      filterData.find((option) => !option.is_outside_filter_drawer);
    return (
      <div id="generic_filter">
        <AntDrawer
          visible={icon_clicked}
          className="generic-filter-drawer"
          getContainer={false}
          // mask={false}
          closable={false}
          width={width || "100%"}
          height={height || "100%"}
          destroyOnClose={true}
          drawerStyle={{
            background: "#FFFFFF 0% 0% no-repeat padding-box",
            boxShadow: "none",
          }}
        >
          <div className="gnrc-field-body">
            <div className="title">
              {this.props.t? this.props.t('filter'): 'Filter'}
              {/* Filter */}
            </div>
            {this.getAllSelects()}
          </div>
          <div className="all-button">
            <AntButton
              className="action-button"
              onClick={() => this.drawerOnClose()}
            >
              {this.props.t? this.props.t('cancel'): 'Cancel'}
              {/* Cancel */}
            </AntButton>
            <AntButton
              className="action-button submit"
              onClick={() => this.submitButtonClick()}
              type="primary"
            >
              {this.props.t? this.props.t('apply'): 'Apply'}
              {/* Apply */}
            </AntButton>
          </div>
        </AntDrawer>
        <div
          className={
            "generic-filter-options" +
            (this.props.isFlexWrap ? " gnrc-fil-wrap" : "")
          }
        >
          {this.filterWithSearchComponentOptions()}
          {this.props.children}
          <FilterIconWithCount
            count={changed_array?.length ?? 0}
            onClick={() => this.filterIconClicked()}
            disabled={this.props.is_all_disabled}
			backgroundWhite={this.props.backgroundWhite}
			hideCount={hideIconCount}
			visible={showDrawerIcon}
			filterIconRef={this.props.filterIconRef}
          />
        </div>
      </div>
    );
  }
}

const tagRender = (props, tagWiseData) => {
  const { label, value, closable, onClose } = props;
  const onPreventMouseDown = (event) => {
    event.preventDefault();
    event.stopPropagation();
  };
  return (
    <AntTag
      color={tagWiseData?.[value]?.tag_color}
      onMouseDown={onPreventMouseDown}
      closable={closable}
      onClose={onClose}
      style={{
        marginRight: 3,
      }}
    >
      {label}
    </AntTag>
  );
};

export default GenericFilter;
