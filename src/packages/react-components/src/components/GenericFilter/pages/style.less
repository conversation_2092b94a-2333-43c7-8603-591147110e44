@import '../../../../../../styles/default-color.less';

#generic_filter {
    .generic-filter-drawer {
        position: fixed;
        .ant-drawer-content {
            .ant-drawer-body {
                padding: 0px !important;
                .gnrc-field-body{
                    overflow: hidden auto;
                    padding: 0 25px 80px;
                }
            }
            padding: 20px 0;
            // padding: 20px 25px;
            .ant-select {
                width: 100%;
            }
            .title {
                color: #232323;
                font-weight: 600;
                font-size: 16px;
            }
            .total-select-filter {
                margin-top: 20px;
                .label {
                    font-size: 12px;
                    color: #707070;
                    font-weight: 600;
                }
                .total-select-div {
                    margin-top: 5px;
                    position: relative;
                    .filter-icon {
                        position: absolute;
                        z-index: 1;
                        top: 10px;
                        left: 10px;
                        color: #374375;
                    }
                    .ant-select {
                        .ant-select-selector {
                            padding: 0 30px;
                            background-color: #F8F8F8;
                            color: #7686A1;
                            // .ant-select-selection-search {
                            //     right: 25px;
                            //     left: 30px;
                            // }
                        }
                        &.ant-tree-select  .ant-select-selector .ant-select-selection-placeholder {
                            padding-left: 20px;
                        }
                    }

                    .kva-filter {
                        padding-left: 30px;
                    }
                }
            }
            .double-select-input-field{
                .total-select-div{
                    display: flex;
                    .gnrc-input-split{
                        margin: 0 8px;
                    }
                    .gnrc-after-input{
                        max-width: 160px;
                    }
                }
                .ant-input-group-wrapper{
                    background-color: #F8F8F8;
                    border: 1px solid #d9d9d9;
                    width: 100%;
                    .ant-input-group-addon{
                        border: none;
                        border-right: 1px solid #d9d9d9;
                        .ant-select{
                            width: 90px;
                            .ant-select-selector{
                                background: none;
                                padding-left: 0;
                                padding-right: 10px;
                                border: none;
                                .ant-select-selection-item{
                                    text-align: start;
                                }
                            }
                        }
                    }
                    .ant-picker {
                        border-radius: 0;
                    }
                    .ant-select-multiple{
                        .ant-select-selector{
                            padding: 4px 10px;
                            border: none;
                            border-radius: 0 !important;
                            background-color: #fff;
                            .ant-select-selection-overflow {
                                gap: 4px;
                            }
                        }
                    }
                    .ant-input{
                        border: none;
                    }
                }
                .filter-main-wrapper{
                    background-color: #F8F8F8;
                    border: 1px solid #d9d9d9;
                    padding-left: 30px;
                    &::placeholder{
                        color: #7686A1;
                    }
                    .ant-input-group-flex {
                        display: flex;
                    }
                }
            }
            .all-button {
                display: flex;
                justify-content: center;
                position: absolute;
                bottom: 0;
                width: 100%;
                background: #fff;
                z-index: 10;
                padding: 15px 0 30px;
                .action-button {
                    margin-right: 20px;
                    width: 158px;
                    height: 42px;
                    border-radius: 13px !important;
                }
            }
        }
    }
	.generic-filter-options {
		display: flex;
		align-items: center;
        &.gnrc-fil-wrap{
            flex-wrap: wrap;

        }
        .search-filter-component {
            width: auto;
            .filter-with-search-container .select-filter-wrapper .select-container .select-label {
                padding-left: 0px;
            }
        }
	}
}

@media (max-width: 900px) {
    #generic_filter .generic-filter-drawer .ant-drawer-content .double-select-input-field .total-select-div .gnrc-after-input {
        max-width: 90px;
    }
}