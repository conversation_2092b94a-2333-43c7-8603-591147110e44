#drawer_with_search_container {
	position: absolute;
	width: 100%;
	z-index: 99;
	height: calc(100vh - 50px);
	transition: 0.5s all;

	&.hide {
		width: 0 !important;
	}

	.custom-drawer {
		position: absolute;

		.ant-drawer-content-wrapper {
			box-shadow: none !important;

			.ant-drawer-content {
				border-top-right-radius: 17px;
				border-bottom-right-radius: 17px;

					.ant-drawer-body {
						padding: 0px !important;

						.search-container {
							margin: 10px 0;

							.search-input {
								width: 100% !important;
								font-size: 13px;
								background-color: transparent;

								.ant-input-prefix {
									left: 230px !important;
									width: 14px;
									height: 14px;
								}

								.ant-input-suffix {
									.ant-input-clear-icon {
										margin-top: 35%;
									}
								}
							}
							.ant-input-affix-wrapper {
								.ant-input:hover {
									border: none;
									border: 1px solid #81818177;
									border-radius: 16px;
									padding-left: 20px;
								}

								.ant-input {
									width: 100% !important;
									padding-left: 20px;
									font-size: 13px;
									font-style: italic;
									color: #232323;
									border-radius: 0px;
									padding-bottom: 5px;
									box-shadow: none;
									border-radius: 16px;
									border: 1px solid #81818177;
									width: 100%;
								}

								.ant-input:focus {
									border: none;
									border: 1px solid #ff8500;
								}
							}
						}
						.things-details {
							padding: 10px 30px !important;
							cursor: pointer;
							padding: 5px;
							border-bottom: 1px solid rgba(227, 227, 227, 0.459);

							.things-name {
								text-align: left;
								font-size: 14px;
								letter-spacing: 0px;
								color: #808080;
								opacity: 1;
							}

							.status-shown-div {
								text-align: left;
								font-size: 12px;
								letter-spacing: 0px;
								opacity: 1;
							}

							.off-thing {
								color: #ff4904;
							}

							.running-thing {
								color: #25c479;
							}
						}
						.selected-thing {
							background: #f58740;

							.things-name {
								font-weight: 600;
								color: #fff;
							}
						}
					}
			}
		}
	}
}

@media screen and (max-width: 768px) {
	.custom-drawer .ant-drawer-content-wrapper .ant-drawer-content .ant-drawer-body .search-container .search-input .ant-input-prefix  .ant-input-suffix .ant-input-clear-icon {
		margin-top: 45%;
	}
}
