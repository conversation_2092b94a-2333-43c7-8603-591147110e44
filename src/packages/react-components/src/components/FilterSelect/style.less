.select-filter-component {
	.select-filter-wrapper {
		display: flex;
		flex-wrap: wrap;
		padding-right: 21px;
		align-items: center;
		
		.ant-select-clear {
			color: #000 !important;
			z-index: 2;
		}

		.select-container {
			display: flex;
			align-items: center;
			position: relative;
			margin-bottom: 15px;

			.filter-icon {
				left: 20px;
				position: absolute;
				top: 14px;
				z-index: 3;
				color: #7686A1;
			}

			.placeholder-text {
				position: absolute;
				z-index: 1;
				top: 12px;
				left: 40px;
				color: #7686A1;
			}
		}

		.filter-drop {
			background: #F5F6F8 !important;
			width: 250px;
			margin-top: 4px;
			margin-left: 10px;
			margin-bottom: 10px;
			border-radius: 10px;

			&.selected {
				.ant-select-selector {
					border-color: #FF8500 !important;
				}
			}

			.ant-select-clear {
				background: transparent;
			}

			&:hover {
				.ant-select-selector {
					box-shadow: 0 0 0 2px rgba(245, 135, 64, 0.2) !important;
				}
			}

			.ant-select-selector {
				background: transparent !important;
				border: 1px solid #F5F6F8;
				padding-left: 30px !important;
				transition: all 0.3s;

				.ant-select-selection-item {
					font-weight: 600 !important;
				}

				.ant-select-selection-search-input {
					padding-left: 18px;
				}

				.ant-select-selection-placeholder {
					color: #7686A1 !important;
					opacity: 1 !important;
					right: unset !important;
					position: relative !important;
					top: unset !important;
					left: unset !important;
					transform: unset !important;
				}
			}
		}

		.multiple-select-drop {
			width: 350px !important;
			
			.ant-select-selector {
				padding-left: 24px !important;

				.ant-select-selection-search-input {
					padding-left: 0 !important;
				}

				.ant-select-selection-item {
					border-radius: 13px;
					margin-left: 10px;
					font-size: 12px !important;
					background: #FF8500 !important;
					color: #fff;
				}

				/*.ant-select-selection-item-content, .ant-select-selection-item-remove {
					display: none !important;
				}*/
			}

			&.application {
				.ant-select-selector {
					.ant-select-selection-item {
						background: #374375 !important;
					}
				}
			}

			&.partner {
				.ant-select-selector {
					.ant-select-selection-item {
						background: #9075BC !important;
					}
				}
			}

			&.customer {
				.ant-select-selector {
					.ant-select-selection-item {
						background: #F68F8F !important;
					}
				}
			}

			&.device-type {
				.ant-select-selector {
					.ant-select-selection-item {
						background: #6C8D0F !important;
					}
				}
			}

			&.thing-type {
				.ant-select-selector {
					.ant-select-selection-item {
						background: #439DFB !important;
					}
				}
			}

			&.status {
				.ant-select-selector {
					.ant-select-selection-item {
						background: #916238 !important;
					}
				}
			}
		}

		.ant-input-affix-wrapper {
			.ant-input {
				width: 400px !important;
				height: 31px;
				border-radius: 5px;
				box-shadow: none;
				border: 1px solid #F5F6F8;

				&:focus, &:hover {
					box-shadow: 0 0 0 2px rgba(245, 135, 64, 0.2) !important;
					border: 1px solid #F5F6F8;
				}
			}
		}	
	}

	.select-tag-container {
		display: flex;
		align-items: center;
		flex-wrap: wrap;

		.select-view-container {
			min-height: 22px;
			margin-bottom: 10px;
			width: auto;

			.filter-view-drop {
				.ant-select-selector {
					border: none !important;
					box-shadow: none !important;
					cursor: default !important;
					background: transparent !important;

					.ant-select-selection-item {
						border-radius: 13px !important;
						background: #FF8500;
						color: #fff;
						display: flex;
						align-items: center;

						.ant-select-selection-item-remove {
							height: 15px !important;
							width: 15px !important;
							margin: 5px 0;
							border-radius: 50% !important;
							background: #fff !important;

							.anticon {
								vertical-align: 2px !important;
    							margin-left: 1px !important;
							}
						}
					}

					.ant-select-selection-search {
						display: none !important;
					}
				}

				&.application {
					.ant-select-selector {
						.ant-select-selection-item {
							background: #374375 !important;
						}
					}
				}

				&.partner {
					.ant-select-selector {
						.ant-select-selection-item {
							background: #9075BC !important;
						}
					}
				}

				&.customer {
					.ant-select-selector {
						.ant-select-selection-item {
							background: #F68F8F !important;
						}
					}
				}

				&.device-type {
					.ant-select-selector {
						.ant-select-selection-item {
							background: #6C8D0F !important;
						}
					}
				}

				&.thing-type {
					.ant-select-selector {
						.ant-select-selection-item {
							background: #439DFB !important;
						}
					}
				}

				&.status {
					.ant-select-selector {
						.ant-select-selection-item {
							background: #916238 !important;
						}
					}
				}
			}
		}
	}
}