import React, { Component } from 'react';
import { message } from 'antd';
import ControlOutlined from '@ant-design/icons/ControlOutlined';
import MoreOutlined from '@ant-design/icons/MoreOutlined';
import AntSelect from '@datoms/react-components/src/components/AntSelect';
import AntOption from '@datoms/react-components/src/components/AntOption';
import AntCheckbox from '@datoms/react-components/src/components/AntCheckbox';
import CommonModal from '@datoms/react-components/src/components/CommonModal';
import AntTable from '@datoms/react-components/src/components/AntTable';
import ReorderableListCustom from '../ReorderableList';
import { setPreferences } from '@datoms/js-sdk';
import { filterAndSortColumns } from '@datoms/js-utils/src/customization' 
import './style.less';
export default class CustomizeTable extends Component {
	static defaultProps = {
		preferenceKeys: [],
	};

	constructor(props) {
		super(props);
		const { totalColumns, visibleColumns, hiddenColumns } =
			this.getVisibleAndHiddenColumns(props.tableProps.columns);
		this.state = {
			visible: false,
			totalColumns: totalColumns,
			visibleColumns: visibleColumns,
			hiddenColumns: hiddenColumns,
			fixedColumnsCount: this.getDefaultFixedColCount(),
		};
		this.resetColumns = this.resetColumns.bind(this);
		this.onHideColumn = this.onHideColumn.bind(this);
		this.onShowColumn = this.onShowColumn.bind(this);
		this.toggleSettings = this.toggleSettings.bind(this);
		this.saveTablePreferences = this.saveTablePreferences.bind(this);
		this.filterAndSortColumns = filterAndSortColumns.bind(this);
	}

	componentDidUpdate(prevProps) {
		if (!prevProps.preferenceKeys) return;
		if (prevProps.preferenceKeys[1] !== this.props.preferenceKeys[1]) { 
			
			const { totalColumns, visibleColumns, hiddenColumns } =
				this.getVisibleAndHiddenColumns(this.props.tableProps.columns);
			this.setState({
				totalColumns,
				visibleColumns,
				hiddenColumns,
				fixedColumnsCount: this.getDefaultFixedColCount(),
			});		
		}
	}

	getTotalColumns(defaultColumns) {
		let value = defaultColumns;
		const { currentUserPreferences, preferenceKeys } = this.props;
		const existingPreference = currentUserPreferences?.['page_preferences']
			? JSON.parse(
					JSON.stringify(currentUserPreferences['page_preferences'])
			  )
			: {};
		// Create the page_type key if it doesn't exist
		if (
			existingPreference.hasOwnProperty(preferenceKeys[0]) &&
			Array.isArray(
				existingPreference[preferenceKeys[0]][preferenceKeys[1]]
			) &&
			existingPreference[preferenceKeys[0]][preferenceKeys[1]].length
		) {
			value = existingPreference[preferenceKeys[0]][preferenceKeys[1]];
			defaultColumns.map((df_col) => {
				let findCol = value.find(
					(col) => col.dataIndex === df_col.dataIndex
				);
				if (!findCol) {
					value.push({ ...df_col, checked: false });
				}
			});
		}
		return value;
	}
	getDefaultFixedColCount() {
		const { currentUserPreferences, preferenceKeys, defaultFixedCount } = this.props;
		if(!isNaN(parseInt(defaultFixedCount))) return defaultFixedCount;
		let value = 2;
		const existingPreference = currentUserPreferences?.['page_preferences']
			? JSON.parse(
					JSON.stringify(currentUserPreferences['page_preferences'])
			  )
			: {};
		// Create the page_type key if it doesn't exist
		if (
			existingPreference.hasOwnProperty(preferenceKeys[0]) &&
			existingPreference[preferenceKeys[0]]['fixed_column_count']
		) {
			value = existingPreference[preferenceKeys[0]]['fixed_column_count'];
		}
		return value;
	}
	getVisibleAndHiddenColumns(defaultColumns) {
		let modifiedColumns = defaultColumns
			.filter((col) => col.pdf_title)
			.map((col, index) => ({
				title: col.pdf_title,
				dataIndex: col.dataIndex,
				order: index + 1,
				checked: true,
				not_customizable: col.not_customizable,
			}));
		const totalColumns = this.getTotalColumns(modifiedColumns);
		const visibleColumns = [];
		const hiddenColumns = [];

		totalColumns.forEach((item) => {
			if (item.checked) {
				visibleColumns.push(item);
			} else {
				// Add to hiddenColumns if checked is false
				hiddenColumns.push(item);
			}
		});

		// Sort visibleColumns based on the order property in ascending order
		visibleColumns.sort((a, b) => a.order - b.order);

		console.log({ visibleColumns });
		return {
			totalColumns,
			visibleColumns,
			hiddenColumns,
		};
	}
	resetColumns(toDefault = true) {
		let modifiedColumns = this.props.tableProps.columns.map(
			(col, index) => ({
				title: col.pdf_title,
				dataIndex: col.dataIndex,
				order: index + 1,
				checked: true,
				not_customizable: col.not_customizable,
			})
		);
		const totalColumns = toDefault
			? modifiedColumns
			: this.getTotalColumns(modifiedColumns);
		const visibleColumns = [];
		const hiddenColumns = [];

		totalColumns.forEach((item) => {
			if (item.checked) {
				// Add to visibleColumns if checked is true
				visibleColumns.push(item);
			} else {
				// Add to hiddenColumns if checked is false
				hiddenColumns.push(item);
			}
		});

		// Sort visibleColumns based on the order property in ascending order
		visibleColumns.sort((a, b) => a.order - b.order);

		this.setState({
			totalColumns,
			visibleColumns,
			hiddenColumns,
			fixedColumnsCount: toDefault ? 2 : this.getDefaultFixedColCount(),
		});
	}
	getTotalColumnsToSave() {
		const { visibleColumns, hiddenColumns } = this.state;
		const totalColumns = [];

		if (!visibleColumns.length) {
			message.error('Please select at least one column!');
			return false;
		}
		visibleColumns.map((item, index) => {
			totalColumns.push({
				title: item.title,
				dataIndex: item.dataIndex,
				order: index + 1,
				checked: true,
				not_customizable: item.not_customizable,
			});
		});

		hiddenColumns.map((item) => {
			totalColumns.push({
				title: item.title,
				dataIndex: item.dataIndex,
				checked: false,
			});
		});
		return totalColumns;
	}

	updatePreferenceValue(value) {
		const { currentUserPreferences, preferenceKeys } = this.props;
		const { fixedColumnsCount } = this.state;

		const existingPreference = currentUserPreferences?.['page_preferences']
			? JSON.parse(
					JSON.stringify(currentUserPreferences['page_preferences'])
			  )
			: {};
		// Create the page_type key if it doesn't exist
		if (!existingPreference.hasOwnProperty(preferenceKeys[0])) {
			existingPreference[preferenceKeys[0]] = {};
		}

		// Update the value of the category
		existingPreference[preferenceKeys[0]][preferenceKeys[1]] = value;
		existingPreference[preferenceKeys[0]]['fixed_column_count'] =
			fixedColumnsCount;
		return existingPreference;
	}
	async saveTablePreferences() {
		const totalColumns = this.getTotalColumnsToSave();
		if (totalColumns) {
			this.setState({ loading: true });
			const newPreferences = this.updatePreferenceValue(totalColumns);
			const preferences = {
				page_preferences: newPreferences,
			};
			console.log('Preferences_:', preferences);
			let response = await setPreferences(
				this.props.user_id,
				this.props.client_id,
				this.props.application_id,
				preferences
			);
			if (response.status === 'success') {
				message.success('Column preferences saved successfully!');
				if (
					typeof this.props.updateCurrentUserPreference === 'function'
				) {
					this.props.updateCurrentUserPreference(preferences);
				}
				this.toggleSettings();
			} else {
				message.error(
					response.message || "Couldn't set columns preference!"
				);
			}
			this.setState({ loading: false });
		}
	}

	toggleSettings() {
		this.setState({ visible: !this.state.visible }, () => {
			if (!this.state.visible) {
				this.resetColumns(false);
			}
		});
	}
	onHideColumn(index) {
		const { visibleColumns, hiddenColumns } = this.state;
		const newVisibleArray = this.removeItemFromArray(visibleColumns, index);
		const newHiddenArray = [...hiddenColumns, visibleColumns[index]];
		this.setState({
			visibleColumns: newVisibleArray,
			hiddenColumns: newHiddenArray,
		});
	}
	onShowColumn(index) {
		const { visibleColumns, hiddenColumns } = this.state;
		const newHiddenArray = this.removeItemFromArray(hiddenColumns, index);
		const newVisibleArray = [...visibleColumns, hiddenColumns[index]];
		this.setState({
			visibleColumns: newVisibleArray,
			hiddenColumns: newHiddenArray,
		});
	}
	removeItemFromArray(array, index) {
		if (index < 0 || index >= array.length) {
			// Index out of bounds, return original array
			return array;
		}

		// Create a new array without the item at the specified index
		const newArray = [...array.slice(0, index), ...array.slice(index + 1)];

		return newArray;
	}

	render() {
		const { preferenceKeys } = this.props;
		const {
			visible,
			totalColumns,
			visibleColumns,
			hiddenColumns,
			fixedColumnsCount,
		} = this.state;
		let modalBody = (
			<section className="cstm-tbl-modal-body">
				<h3>
					{this.props.t? this.props.t('select_columns')+" ": "Select columns "}
					{/* Select columns{' '} */}
					<span className="cstm-reset" onClick={this.resetColumns}>
						{this.props.t? this.props.t('reset'): "Reset"}
						{/* Reset */}
					</span>
				</h3>
				<div className="cstm-tbl-modal-body-sec">
					<h4>
						<span className="cstm-label">{this.props.t? this.props.t('fixed_columns') + ": ": "Fixed columns: "}</span>
						<AntSelect
							value={fixedColumnsCount}
							onChange={(value) =>
								this.setState({ fixedColumnsCount: value })
							}
						>
							<AntOption value="1">1</AntOption>
							<AntOption value="2">2</AntOption>
							<AntOption value="3">3</AntOption>
							<AntOption value="4">4</AntOption>
							<AntOption value="5">5</AntOption>
						</AntSelect>
					</h4>
				</div>
				<div className="cstm-tbl-modal-body-sec">
					<h4>
						{this.props.t? this.props.t('visible_columns'): "Visible columns"} - {visibleColumns.length}/
						{totalColumns.length}
					</h4>
					<ReorderableListCustom
						items={visibleColumns.filter((item) => !item.not_customizable).map((item, index) => ({
							...item,
							renderTitle: (
								<div>
									<span>
										<MoreOutlined
											style={{
												color: '#808080',
											}}
										/>
										<MoreOutlined
											style={{
												color: '#808080',
												position: 'relative',
												right: '7px'
											}}
										/>
									</span>
									<AntCheckbox
										checked
										text={
											item.title === 'Device #'
												? this.props.t? (this.props.t('device') + " #"): item.title
												: this.props.t? (this.props.t(item.title?.toLowerCase().replace(/\s+/g, '_').replace(/:/g, '_'))): item.title
										}										
										// text={this.props.t? this.props.t(item.title.toLowerCase().replace(/\s+/g, '_')): item.title}
										// text={item.title}
										onChange={() =>
											this.onHideColumn(index)
										}
									/>
								</div>
							),
						}))}
						setItems={(items) => {
							console.log('visibleColumns:', items);
							this.setState({ visibleColumns: items });
						}}
					/>
				</div>
				<div className="cstm-tbl-modal-body-sec">
					<h4>
						{this.props.t? this.props.t('hidden_columns'): "Hidden columns"} - {hiddenColumns.length}/
						{totalColumns.length}
					</h4>
					<section className="cstm-hdn-cols">
						{hiddenColumns.map((item, index) => (
							<div key={index} className="hidden-cols">
								<AntCheckbox
									checked={false}
									text={
										item.title === 'Device #'
											? this.props.t? (this.props.t('device') + " #"): item.title
											: this.props.t? (this.props.t(item.title?.toLowerCase().replace(/\s+/g, '_'))): item.title
									}
									// text={item.title}
									onChange={() => this.onShowColumn(index)}
								/>
							</div>
						))}
					</section>
				</div>
			</section>
		);

		const customizedColumns = this.filterAndSortColumns(
			this.props.tableProps.columns,
			preferenceKeys
		);
		if (!this.props.disableCustomization) {
			customizedColumns.map((col, index) => {
				if (
					Array.from(
						{ length: fixedColumnsCount },
						(_, ind) => ind
					).includes(index)
				) {
					col.fixed = 'left';
					col.align = 'left';
				}
			});
		}
		return (
			<div className="dtms-customize-table-wrapper">
				{this.props.logged_in_user_client_id === this.props.client_id &&
				!this.props.disableCustomization ? (
					<p
						className={
							'open-cstm-table' +
							(this.props.tableProps?.dataSource?.length
								? ' opn-cstbl-abs'
								: '')
						}
						onClick={this.toggleSettings}
					>
						<ControlOutlined
							style={{
								color: '#374375',
								marginRight: 6,
							}}
						/>
						{this.props.t? this.props.t('customize_table'): "Customize table"}
						{/* Customize table */}
					</p>
				) : (
					''
				)}

				<AntTable
					t={this.props.t}
					{...this.props.tableProps}
					columns={customizedColumns}
				/>
				<CommonModal
					title={true}
					noIcon={true}
					isVisible={visible}
					okTitle={this.props.t? this.props.t('apply'): "Apply"}
					closeLoading={this.state.loading}
					cancelTitle={this.props.t? this.props.t('cancel'): "Cancel"}
					onCancel={this.toggleSettings}
					custom_body_className="cus-table-modal-class"
					customBody={modalBody}
					onOk={this.saveTablePreferences}
					//	closeLoading={save_loading}
				/>
			</div>
		);
	}
}
