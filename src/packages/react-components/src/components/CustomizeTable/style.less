.cus-table-modal-class {
	width: 742px !important;

	&.dtms-common-modal {
		box-shadow: none;

		.ant-modal-header {
			display: none;
		}

		.ant-modal-content {
			background: #fbfbfb;

			.ant-modal-body .password-box {
				padding: 0px 0 24px 0;

				.cstm-tbl-modal-body {
					width: 100%;
					margin-top: 20px;
					padding: 5px 40px 0px;

					> h3 {
						color: #232323;
						font-size: 18px;
						font-weight: 500;
						margin-bottom: 8px;
					}
					
					.cstm-reset {
						color: #aeb9cc;
						font-style: italic;
						cursor: pointer;
						font-size: 13px;
						font-weight: normal;
						margin-left: 10px;
						padding: 3px 8px;
						border: 1px solid #aeb9cc;
						border-radius: 4px;
						display: inline-flex;
						margin-bottom: 15px;
						&:hover{
							filter: brightness(1.1);
						}
					}
					.cstm-tbl-modal-body-sec {
						margin-bottom: 22px;

						> h4 {
							font-size: 14px;
							font-weight: 500;
							margin-bottom: 15px;
							color: #232323;
						}
						.cstm-label {
							font-weight: normal;
						}
						.ant-select {
							margin-left: 20px;
							width: 62px;
							.ant-select-selector {
								align-items: center;
							}
						}
						.cstm-hdn-cols {
							max-height: 170px;
							overflow: auto;
						}

						.hidden-cols {
							margin: 0 12px 12px;
						}

						.ant-checkbox + span {
							color: #374375;
							font-weight: 500;
							margin-left: 10px;
						}
					}
				}
			}
		}
	}
}

.dtms-customize-table-wrapper {
	position: relative;

	.open-cstm-table {
		color: #374375;
		font-style: italic;
		cursor: pointer;
		width: fit-content;
		
		&.opn-cstbl-abs {
			z-index: 99;
			position: absolute;
			top: 10px;
		}
	}
	.ant-table-wrapper {
		.ant-pagination {
			.ant-pagination-prev,
			.ant-pagination-next {
				.ant-pagination-item-link {
					background: none;
					border: none;
					> .anticon {
						font-size: 18px;
						color: #c4cfd6;
					}
				}
			}
		}
		.ant-table .ant-table-container {
			.ant-table-thead > tr {
				> th, > td {
				background: #f1eeeb !important;
			}
		}
			.ant-table-sticky-scroll {
				position: fixed;
			}
		}

		.ant-table {
			.ant-table-header {
				.ctbl-col-hd {
					text-align: center;
					.ctbl-col-nml {
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
					}
					.ctbl-col-lg {
						font-weight: normal;
					}
				}
			}
		}
	}
}
