import React from "react";
import { Skeleton } from "antd";

/**
 * ChartLazyLoader Component
 *
 * A reusable class component that uses the IntersectionObserver API to lazy-load its content.
 * It renders a placeholder until it scrolls into the viewport, at which point it executes
 * the `renderChart` function passed in its props.
 */
export default class ChartLazyLoader extends React.Component {
    constructor(props) {
        super(props);
        
        // The component's internal state to track if it's visible.
        this.state = { isVisible: false };
        
        // A reference to the placeholder div element that we will observe.
        this.placeholderRef = React.createRef();
        
        // The IntersectionObserver instance.
        this.observer = null;
    }

    componentDidMount() {
        // We set up the observer after the component has mounted, ensuring the placeholder div exists in the DOM.
        
        const observerOptions = {
            // `rootMargin` pre-loads the component when it is 200px away from the viewport, creating a smoother user experience.
            rootMargin: '200px 0px'
        };

        this.observer = new IntersectionObserver(([entry]) => {
            // This callback function is executed when the visibility of the observed element changes.
            
            if (entry.isIntersecting) {
                // The placeholder is now visible. Update the state to trigger a re-render.
                this.setState({ isVisible: true });
                
                // The component is loaded, so we no longer need to observe it.
                // This is a crucial performance optimization.
                this.observer.unobserve(this.placeholderRef.current);
            }
        }, observerOptions);

        // Start observing the placeholder div.
        if (this.placeholderRef.current) {
            this.observer.observe(this.placeholderRef.current);
        }
    }

    componentWillUnmount() {
        // This is a critical cleanup step.
        // If the component is removed from the DOM, we must disconnect the observer to prevent memory leaks.
        
        if (this.observer) {
            this.observer.disconnect();
        }
    }

    render() {
        // The style for our placeholder.
        // It MUST have a defined height to be detected by the IntersectionObserver.
        const placeholderStyle = {
            minHeight: this.props.height || '300px', // Use passed height or a default
            width: '100%',
            color: '#999', 
        };

        return (
            // This div is the placeholder that the observer is watching.
            <div ref={this.placeholderRef} style={placeholderStyle}>
                {
                    // Conditional rendering:
                    // If `isVisible` is true, execute the function passed in props.
                    // If false, show the skeleton loader.
                    this.state.isVisible 
                        ? this.props.renderChart() 
                        : <Skeleton active />
                }
            </div>
        );
    }
}

