export default {
	engine: 'pdfmake',
	config: {},
};

export const markerConfig = {
	plotOptions: {
		line: {
			marker: {
				enabled: true,
				radius: 2,
			},
		},
		area: {
			marker: {
				enabled: true,
				radius: 2,
			},
		},
		spline: {
			marker: {
				enabled: true,
				radius: 2,
			},
		},
		
		
	},
}

export const boostConfig = {
	boost: {
	  useGPUTranslations: true,
	  usePreAllocated: true,
	  seriesThreshold: 1, // Enable boost for series with 10+ points
	  allowForceSeriesBoost: true,
	},
	plotOptions: {
	  series: {
		boostThreshold: 10, // Boost individual series with 10+ points
		turboThreshold: 100,
		animation: false,
		enableMouseTracking: false,
		shadow: false,
		// marker: {
		//   enabled: true,
		//   radius: 2,
		// },
		states: {
		  hover: {
			enabled: false,
		  },
		},
	  },
	  ...markerConfig.plotOptions

	//   line: {
	// 	boostThreshold: 10,
	// 	turboThreshold: 0,
	// 	marker: {
	// 	  enabled: false,
	// 	},
	//   },
	//   area: {
	// 	boostThreshold: 10,
	// 	turboThreshold: 0,
	// 	marker: {
	// 	  enabled: false,
	// 	},
	//   },
	//   spline: {
	// 	boostThreshold: 10,
	// 	turboThreshold: 0,
	// 	marker: {
	// 	  enabled: false,
	// 	},
	//   },
	//   arearange: {
	// 	boostThreshold: 10,
	// 	turboThreshold: 0,
	// 	marker: {
	// 	  enabled: false,
	// 	},
	//   },
	},
	tooltip: {
	  enabled: false,
	},
}