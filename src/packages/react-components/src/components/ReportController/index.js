/* React Based */
import React from "react";

/* Proptypes */
import {
  arrayOf,
  bool,
  func,
  number,
  shape,
  string,
  object,
  array,
} from "prop-types";

/* Components */
import { Button, Row, Col, Skeleton, Switch } from "antd";
import Kpis from "../Kpis";
import Text from "../Text";
import HighchartsReact from "highcharts-react-official";
import Highcharts from "highcharts";
import HighchartsExporting from "highcharts/modules/exporting";
import highchartsMore from "highcharts/highcharts-more";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { saveAs } from "file-saver";
import ChartLazyLoader from "./components/ChartLazyLoader";
// temporarily removed to reduce bundle size as this is not used currently
// import JSZip from 'jszip';
import moment from "moment-timezone";
// import Boost from 'highcharts/modules/boost';
/* Styles */
import "./style.less";

/* Configs */
import defaultConfigs, { boostConfig, markerConfig } from "./defaultConfigs";
import TableList from "../TableList";
import { PdfHandler } from "./handlers/pdf.handler";

/* Worker */
import csvWorkerScript from "./workers/csv.worker.js";
import xlsxWorkerScript from "./workers/xlsx.worker.js";
import dataWorkerScript from "./workers/data.worker.js";
import SaveFileToDisk from "./handlers/SaveFileToDisk";

if (typeof Highcharts === "object") {
  // Boost(Highcharts);
  highchartsMore(Highcharts);
  HighchartsExporting(Highcharts);
}

Highcharts.getSVG = function (charts) {
  var svgArr = [],
    top = 0,
    width = 0;

  Highcharts.each(charts, function (chart) {
    var svg = chart.getSVG(),
      // Get width/height of SVG for export
      svgWidth = +svg.match(/^<svg[^>]*width\s*=\s*\"?(\d+)\"?[^>]*>/)[1],
      svgHeight = +svg.match(/^<svg[^>]*height\s*=\s*\"?(\d+)\"?[^>]*>/)[1];

    svg = svg.replace("<svg", '<g transform="translate(0,' + top + ')" ');
    svg = svg.replace("</svg>", "</g>");

    top += svgHeight;
    width = Math.max(width, svgWidth);

    svgArr.push(svg);
  });

  return (
    '<svg height="' +
    top +
    '" width="' +
    width +
    '" version="1.1" xmlns="http://www.w3.org/2000/svg">' +
    svgArr.join("") +
    "</svg>"
  );
};

/**
 * This React Component is a Report Component where Data has to be shown in Tables, Graphs, KPIs and Plaintexts.
 * The Report Component will have the facility to convert the report to either PDF or CSV or XLSX format.
 * When to use:
 * 1 - To Display Tables, Graphs, Texts and KPIs in a desired layout.
 * 2 - To Export the Report Data in CSV, XLSX or PDF
 **/
export default class ReportController extends React.Component {
  // initializing defaultprops, so that it will provide default configs
  static defaultProps = {
    ...defaultConfigs,
  };

  static propTypes = {
    /** Configuration of Visualization Elements */
    conf: arrayOf(
      shape({
        compo: string,
        props: object,
        child: arrayOf(
          shape({
            compo: string,
            widget: string,
            classname: string,
            table_new_page: bool,
            props: object,
            col_props: object,
            datatype: string,
            zone: string,
            pdf_width: number,
            pdf_height: number,
            pdf_table_break: {
              col_no: number,
              row_no: number,
            },
          }),
        ),
      }),
    ),

    /** Global Moment.js supported Timezone for the Component */
    zone: string,

    /** Data Array for the Visualization Components */
    data: array,

    /** To Check if Report is Ready to be Exported */
    onExportReady: func,
  };

  constructor(props) {
    super(props);

    const { g2gSwitch } = props;

    this.state = {
      num: 0,
      isDataManipulated: false,
      data: props.data,
      showTable: g2gSwitch && g2gSwitch.visible ? !g2gSwitch.checked : true,
      showGraph: g2gSwitch && g2gSwitch.visible ? g2gSwitch.checked : true,
    };
    this.exportCSV = this.exportCSV.bind(this);
    this.exportXLSX = this.exportXLSX.bind(this);
    this.exportPDF = this.exportPDF.bind(this);
    this.visualize = this.visualize.bind(this);
    this.fileDownloader = this.fileDownloader.bind(this);
    this.dataManipulator = this.dataManipulator.bind(this);
  }

  allData = {
    Table: [],
    KPI: [],
    Text: [],
    Graph: [],
    GraphIds: [],
    orderedData: [],
    Count: {
      Table: 0,
      KPI: 0,
      Text: 0,
      Graph: 0,
    },
  };

  allGraphImageData = {};

  // temporarily removed to reduce bundle size as this is not used currently
  // downloadBlobAsZip = (blob, file_name) => {
  // 	try {
  // 		var zip = new JSZip();
  // 		zip.file(file_name, blob);
  // 		zip.generateAsync({ type: 'blob', compression: 'DEFLATE' }).then(
  // 			function (content) {
  // 				if (
  // 					window.cordova &&
  // 					window.cordova.platformId !== 'browser'
  // 				) {
  // 					SaveFileToDisk(
  // 						content,
  // 						file_name.split('.')[0] + '.zip',
  // 						'zip'
  // 					);
  // 				} else {
  // 					saveAs(content, file_name.split('.')[0] + '.zip');
  // 				}
  // 			}
  // 		);
  // 	} catch (e) {
  // 		console.log('Zipping Error: downloadBlobAsZip -> ', e);
  // 	}
  // };

  exportCSV(progressUpdate, config) {
    if (!config) config = {};
    const { isZip } = config;
    const ctx = this;
    let { file_name } = this.props;
    const w = new Worker(csvWorkerScript);

    w.onmessage = function (event) {
      const { csv, percent } = event.data;
      if (percent && progressUpdate) {
        progressUpdate(percent);
      }
      if (csv) {
        ctx.fileDownloader(csv, file_name + ".csv", "csv", isZip);
      }
    };

    w.postMessage({
      ordered: true,
      dataSource: this.allData,
    });
  }

  exportXLSX(progressUpdate, config) {
    if (!config) config = {};
    const { isZip } = config;

    const w = new Worker(xlsxWorkerScript);
    const ctx = this;
    let { file_name } = this.props;
    w.onmessage = function (event) {
      const { percent, blob, logger, format } = event.data;
      if (percent && progressUpdate) {
        progressUpdate(percent);
      }
      if (logger) {
        return;
      }
      if (blob) {
        if (isZip) {
          // temporarily removed to reduce bundle size as this is not used currently
          // ctx.downloadBlobAsZip(blob, file_name);
        } else {
          if (window.cordova && window.cordova.platformId !== "browser") {
            SaveFileToDisk(blob, file_name + ".xlsx", "xlsx");
          } else {
            saveAs(blob, file_name);
          }
        }
      }
    };

    w.postMessage({
      dataSource: this.allData,
      xlsxConfig: config,
      ordered: true,
    });
  }

  exportPDF2() {
    /*
		const generateTablePDF = (tableObject) => {
			let { tableData, rowCount, colCount, options } = tableObject;
			const allColumns = tableData.columns;
			const data = tableData.dataSource;

			if (!rowCount) rowCount = data.length;
			if (!colCount) colCount = allColumns.length;

			console.log('tableData', tableData);

			// doc.deletePage(1);
		};

		this.allData.Table.forEach(function (tableData) {
			generateTablePDF({
				tableData: tableData,
				rowCount: tableData.tableConfig.pdf_table_break.row_no,
				colCount: tableData.tableConfig.pdf_table_break.col_no,
			});
		});

		var docDefinition = {
			content: [
				{
					layout: 'lightHorizontalLines', // optional
					table: {
						// headers are automatically repeated if the table spans over multiple pages
						// you can declare how many rows should be treated as headers
						headerRows: 1,
						widths: ['*', 'auto', 100, '*'],

						body: [
							['First', 'Second', 'Third', 'The last one'],
							['Value 1', 'Value 2', 'Value 3', 'Value 4'],
							[
								{ text: 'Bold value', bold: true },
								'Val 2',
								'Val 3',
								'Val 4',
							],
						],
					},
				},
			],
		};
		pdfMake.createPdf(docDefinition).download();*/
  }

  async exportPDF(config) {
    let pdfOrientation = "l";
    let { isZip, fileName } = config;
    let { file_name } = this.props;
    const ctx = this;
    let totalTables = 0;
    const generateTablePDF = (tableObject) => {
      totalTables++;
      let { doc, tableData, rowCount, colCount, options } = tableObject;
      const allColumns = tableData.columns;
      const data = tableData.dataSource;

      if (!rowCount) rowCount = data.length;
      if (!colCount) colCount = allColumns.length;
      if (!doc) doc = new jsPDF("p", "pt");

      const arr1 = [];
      const colJson = {};
      let j1 = 0;

      for (let j = 0; j < allColumns.length; j++) {
        colJson[allColumns[j]["dataIndex"]] = allColumns[j]["title"];
      }

      for (var c in data[0]) {
        arr1[j1] = c;
        j1++;
      }

      var i = 0;
      while (i < data.length) {
        let i1 = 0;
        let i6 = i;
        while (i1 < arr1.length) {
          let columns = [];
          let i3 = i1,
            i2 = 0;
          while (i2 !== colCount && i1 < arr1.length) {
            columns.push({
              title: colJson[arr1[i1]],
              datakey: arr1[i1],
            });
            i2++;
            i1++;
          }
          let i4 = 0,
            i5 = i3;
          let rows = [];
          i = i6;
          while (i4 !== rowCount && i < data.length) {
            let obj = [];
            while (i3 !== i1) {
              var keyProp = arr1[i3];
              obj.push(data[i][keyProp]);
              i3++;
            }
            i3 = i5;
            rows.push(obj);
            i4++;
            i++;
          }

          if (!options)
            options = {
              margin: { horizontal: 20, top: 60 },
              bodyStyles: { valign: "top" },
              columnStyles: { email: { cellWidth: "wrap" } },
              theme: "striped",
              pageBreak: "always",
              didDrawPage: function (data) {
                // Header
                doc.setFontSize(11);
                doc.setTextColor(40);
                doc.text(
                  config.header.title,
                  page_width -
                    data.settings.margin.right -
                    doc.getTextWidth(config.header.title),
                  50,
                );

                // Footer
                var str = "Page " + doc.internal.getNumberOfPages();
                doc.setFontSize(10);

                var pageSize = doc.internal.pageSize;
                var pageHeight = pageSize.height
                  ? pageSize.height
                  : pageSize.getHeight();
                doc.text(str, data.settings.margin.left, pageHeight - 10);
              },
            };

          if (totalTables === 1) {
            options.pageBreak = "auto";
            options.margin = { horizontal: 40, top: 60 };
          }

          autoTable(doc, columns, rows, options);
        }
      }
      // doc.deletePage(1);
      return doc;
    };

    if (!config)
      config = {
        header: {
          title: "Datoms Header Title",
          left: {
            text: "Phoenix Robotix Pvt. Ltd.",
            width: 40,
            height: 15,
          },
        },
      };
    config["pdf_orientation"] = pdfOrientation;
    const pdfHandler = new PdfHandler(config);
    const { page_height, page_width } = pdfHandler.getPageDimensions();
    let doc = pdfHandler.getDoc();
    let headerImg = undefined;
    console.log("iweufyuiwyf", config);
    if (config.header) {
      if (config.header.left && config.header.left.svg_img) {
        headerImg = await this.loadImage(config.header.left.svg_img);
      }
      pdfHandler.addHeaderImageText(config.header, headerImg);
    }

    if (config.footer) {
      pdfHandler.addFooterImageText(config.footer);
    }
    console.log("GRAPH_IDS", this.allData);
    if (this.allData.GraphIds && this.allData.GraphIds.length) {
      this.setAllGraphsImageData((allGraphsData) => {
        this.allData.orderedData.forEach((data_item, i) => {
          const { dataObj, type } = data_item;
          if (type === "text") {
            const data = dataObj.header_text;
            const conf = dataObj.textConfig;
            const pdf_force_new_page = conf.pdf_force_new_page;
            const pdf_top_line = conf.pdf_top_line;
            const pdf_bottom_line = conf.pdf_bottom_line;
            const pdf_text_align = conf.pdf_text_align;
            const textStartY = conf.subtract_text_start_y;
            const fill = conf.fill;
            const textType = conf.type;
            const textColor = conf.textColor;
            let pdf_size = conf.pdf_size;
            if (pdf_force_new_page) {
              pdfHandler.addPage();
            }

            if (fill) {
              pdfHandler.addRect(fill.fill_color, fill.y_value, fill.top);
            }

            if (pdf_top_line) {
              pdfHandler.addLine();
            }
            data.forEach((text) => {
              pdfHandler.addText(
                text,
                pdf_text_align,
                pdf_size,
                textColor,
                textStartY,
                textType,
              );
            });
            if (pdf_bottom_line) {
              pdfHandler.addLine();
            }
          }

          if (type === "kpi") {
          }

          if (type === "table") {
            const data = dataObj.dataSource;
            const conf = dataObj.tableConfig;
            const pdf_force_new_page = conf.pdf_force_new_page;
            const pdf_top_line = conf.pdf_top_line;
            const pdf_bottom_line = conf.pdf_bottom_line;
            if (pdf_force_new_page) {
              pdfHandler.addPage();
            }

            if (pdf_top_line) {
              pdfHandler.addLine();
            }
            pdfHandler.addTable(
              this.props.isAurassure,
              conf,
              conf.props.columns,
              data,
            );
            if (pdf_bottom_line) {
              pdfHandler.addLine();
            }
            if (
              this.allData.orderedData[i + 1] &&
              this.allData.orderedData[i + 1].type === "graph"
            ) {
              pdfHandler.addPage();
            }
          }
          if (type === "graph") {
            const graph_id = dataObj.id;
            const conf = dataObj.graphConfig;
            const pdf_force_new_page = conf.pdf_force_new_page;
            const pdf_top_line = conf.pdf_top_line;
            const pdf_bottom_line = conf.pdf_bottom_line;

            const scale = 0.8 / (page_width / allGraphsData[graph_id].width);
            const scaledWidth = allGraphsData[graph_id].width * scale;
            const scaledHeight = allGraphsData[graph_id].height * scale;
            if (allGraphsData[graph_id]) {
              if (pdf_top_line) {
                pdfHandler.addLine();
              }
              let getWidth = 270;
              let getHeight = 67.5;

              if (this.props.graph_download_width) {
                getWidth = this.props.graph_download_width;
              }
              if (this.props.graph_download_height) {
                getHeight = this.props.graph_download_height;
              }
              if (getHeight > page_height - 15 - pdfHandler.getCurrentY()) {
                pdfHandler.addPage();
              }
              pdfHandler.addImage(
                allGraphsData[graph_id].pngData,
                getHeight,
                getWidth,
                "graph",
              );
              if (pdf_bottom_line) {
                pdfHandler.addLine();
              }
            }
          }
        });
        let pageCount = doc.internal.getNumberOfPages();
        doc.setFontSize(10);
        doc.setTextColor(0);
        for (let i = 0; i < pageCount; i++) {
          doc.setPage(i);
          doc.text(
            page_width - 25,
            page_height - 5,
            "Page " +
              doc.internal.getCurrentPageInfo().pageNumber +
              " of " +
              pageCount,
          );
          doc.text(
            5,
            page_height - 5,
            this.props.is_white_label || this.props.isAurassure
              ? this.props.vendor_name
              : "Datoms - Phoenix Robotix Pvt. Ltd.",
          );
        }
        const savePDF = () => {
          if (!fileName) fileName = file_name + ".pdf";
          if (isZip) {
            // temporarily removed to reduce bundle size as this is not used currently
            // this.downloadBlobAsZip(doc.output('blob'), fileName);
          } else {
            if (window.cordova && window.cordova.platformId !== "browser") {
              SaveFileToDisk(doc.output("blob"), fileName, "pdf");
              return;
            } else {
              doc.save(fileName);
            }
          }
        };

        savePDF();
      });
    } else {
      this.allData.orderedData.forEach((data_item, i) => {
        const { dataObj, type } = data_item;
        if (type === "text") {
          const data = dataObj.header_text;
          const conf = dataObj.textConfig;
          const pdf_force_new_page = conf.pdf_force_new_page;
          const pdf_top_line = conf.pdf_top_line;
          const pdf_bottom_line = conf.pdf_bottom_line;
          const textStartY = conf.subtract_text_start_y;
          const pdf_text_align = conf.pdf_text_align;
          const fill = conf.fill;
          const textType = conf.type;
          const textColor = conf.textColor;
          let pdf_size = conf.pdf_size;
          if (pdf_force_new_page) {
            pdfHandler.addPage();
          }

          if (fill) {
            pdfHandler.addRect(fill.fill_color, fill.y_value, fill.top);
          }
          if (pdf_top_line) {
            pdfHandler.addLine();
          }
          data.forEach((text) => {
            pdfHandler.addText(
              text,
              pdf_text_align,
              pdf_size,
              textColor,
              textStartY,
              textType,
            );
          });
          if (pdf_bottom_line) {
            pdfHandler.addLine();
          }
        }

        if (type === "kpi") {
        }

        if (type === "table") {
          const data = dataObj.dataSource;
          const conf = dataObj.tableConfig;
          const pdf_force_new_page = conf.pdf_force_new_page;
          const pdf_top_line = conf.pdf_top_line;
          const pdf_bottom_line = conf.pdf_bottom_line;

          if (pdf_force_new_page) {
            pdfHandler.addPage();
          }

          if (pdf_top_line) {
            pdfHandler.addLine();
          }
          pdfHandler.addTable(
            this.props.isAurassure,
            conf,
            conf.props.columns,
            data,
          );
          if (pdf_bottom_line) {
            pdfHandler.addLine();
          }
          if (
            this.allData.orderedData[i + 1] &&
            this.allData.orderedData[i + 1].type === "graph"
          ) {
            pdfHandler.addPage();
          }
        }
      });
      let pageCount = doc.internal.getNumberOfPages();
      doc.setFontSize(10);
      doc.setTextColor(0);
      for (let i = 0; i < pageCount; i++) {
        doc.setPage(i);
        doc.text(
          page_width - 25,
          page_height - 5,
          "Page " +
            doc.internal.getCurrentPageInfo().pageNumber +
            " of " +
            pageCount,
        );
        doc.text(
          5,
          page_height - 5,
          this.props.is_white_label || this.props.isAurassure
            ? this.props.vendor_name
            : "Datoms - Phoenix Robotix Pvt. Ltd.",
        );
      }

      const savePDF = () => {
        if (!fileName) fileName = file_name + ".pdf";
        if (isZip) {
          // temporarily removed to reduce bundle size as this is not used currently
          // this.downloadBlobAsZip(doc.output('blob'), fileName);
        } else {
          doc.save(fileName);
        }
      };

      savePDF();
    }
  }

  visualize(conf, zone, data) {
    /* Class Logic:
     * getVizJSX takes conf array -> calls getRow for each item  || It finally returns the Complete Visualization JSX.
     * getRow creates a Row and foreach child creates a Col using getCol
     * getCol creates the Column and calls getVizComp to get the correct Visualization Component
     * getVizCol contains switch case to choose the Appropriate Component vizTable/vizKPI/vizGraph/vizText
     * The Appropriate Component is created with the Manipulated Data
     **/

    /* 1 - (conf[],zone,data[]) => returns JSX[] */
    const getVizJSX = (conf, zone, data) => {
      const vizJSX = [];
      for (let i = 0; i < conf.length; i++) {
        vizJSX.push(getRow(conf[i], zone, data[i]));
      }
      return vizJSX;
    };

    /* 2 - (conf[i], zone, data[i]) => returns JSX */
    const getRow = (row_item, zone, row_data) => {
      const rowJSX = [];

      for (let i = 0; i < row_item["child"].length; i++) {
        rowJSX.push(getCol(row_item["child"][i], zone, row_data[i]));
      }

      return <Row {...row_item.props}>{rowJSX}</Row>;
    };

    /* 3 - (conf[i]['child'][j], zone, data[i][j]) => returns JSX  */
    const getCol = (col_item, zone, col_data) => {
      const colJSX = [];
      if (col_item?.props?.columns?.length === 6) {
        col_item?.props?.columns?.map((item, index) => {
          col_item.props.columns[index].title = this.props.t
            ? item.title.charAt(0).toUpperCase() +
              this.props.t(item.title.toLowerCase()).slice(1)
            : item.title;
        });
      }
      if (col_data?.textData?.[0]) {
        col_data.textData[0] = this.props.t
          ? this.props.t(col_data.textData[0])
          : col_data.textData[0];
      }
      colJSX.push(
        <Col {...col_item.col_props}>
          {/*<Skeleton active={true}/>*/}
          {getVizComp(col_item, zone, col_data)}
        </Col>,
      );

      return colJSX;
    };

    /* 4 - (conf[i]['child'][j], zone, data[i][j]) => returns JSX  */
    const getVizComp = (col_item, zone, col_data) => {
      try {
        const { isDataManipulated, showTable, showGraph } = this.state;
        const { skeleton } = this.props;
        if (!isDataManipulated) {
          if (skeleton) {
            return <Skeleton active />;
          } else {
            return <></>;
          }
        }
        switch (col_item.compo.toLowerCase().trim()) {
          case "table":
            return showTable ? vizTable(col_item, col_data) : <></>;
          case "graph":
            return showGraph ? (
              this.props.vizType === "graph" ? (
                <ChartLazyLoader
                  renderChart={() => vizGraph(col_item, col_data)}
                  height={this.props.graph_height || "300px"}
                />
              ) : (
                vizGraph(col_item, col_data)
              )
            ) : (
              <></>
            );
          case "kpi":
            return vizKPI(col_item, col_data);
          case "text":
            return vizText(col_item, col_data);
          default:
            return (
              <h3>
                <strong>{col_item.compo}</strong>
                {": Visualization Component Not Found"}
              </h3>
            );
        }
      } catch (error) {
        console.log("error abc: ", error);
        return <></>;
      }
    };

    /* 6 - (conf[i]['child'][j], manipulatedData[]) => returns JSX  */
    const vizTable = (col_item, manipulated_data) => {
      const props = col_item.props;
      return (
        <TableList {...props} dataSource={manipulated_data} t={this.props.t} />
      );
    };

    /* 6 - (conf[i]['child'][j], manipulatedData[]) => returns JSX  */
    const vizKPI = (col_item, manipulated_data) => {
      const props = col_item.props;
      return <Kpis {...props} data={manipulated_data} />;
    };

    /* 6 - (conf[i]['child'][j], manipulatedData[]) => returns JSX  */
    const vizText = (col_item, manipulated_data) => {
      const props = col_item.props;
      manipulated_data.textData[0] = this.props.t
        ? this.props.t(manipulated_data.textData[0])
        : manipulated_data.textData[0];

      return (
        <Text
          {...props}
          header_text={manipulated_data.textData}
          type={manipulated_data.type}
        />
      );
    };

    /* 6 - (conf[i]['child'][j], manipulatedData[]) => returns JSX  */
    const vizGraph = (col_item, manipulated_data) => {
      const props = col_item.props;
      const my_ref = props.id;
      const additionalConfig = this.props.downloadingReport
		? boostConfig
		: markerConfig;

      manipulated_data?.series?.map((item, index) => {
        manipulated_data.series[index].name = this.props.t
          ? this.props.t(
              manipulated_data.series[index].name
                .toLowerCase()
                .replace(/[\s-]/g, "_"),
            )
          : manipulated_data.series[index].name;

		if(item?.data?.length > 400 && this.props.downloadingReport) {
			item.marker = {enabled: false}
		}
      });

      // console.log("SUPERB",manipulated_data.series[0].name);

      Highcharts.setOptions({
        time: {
          /**
           * Use moment-timezone.js to return the timezone offset for individual
           * timestamps, used in the X axis labels and the tooltip header.
           */
          getTimezoneOffset: function (timestamp) {
            let getZone = zone ? zone : "Asia/Kolkata",
              timezoneOffset = -moment.tz(timestamp, getZone).utcOffset();
            return timezoneOffset;
          },
        },
      });
	  const finalOptions = Highcharts.merge(manipulated_data, additionalConfig);
      if (props.highcharts) {
        return (
          <HighchartsReact
            {...props}
            options={finalOptions}
            ref={my_ref}
          />
        );
      } else {
        const chartType = manipulated_data?.chart?.type;
        if (["area", "spline"].includes(chartType) && !this.props.downloadingReport) {
          manipulated_data.plotOptions = {
            [chartType]: {
              marker: {
                enabled: true,
                radius: 2,
              },
            },
          };
        }
		const finalOptions = Highcharts.merge(manipulated_data, props, additionalConfig);
        return (
          <HighchartsReact
            id={props.id}
            ref={my_ref}
            highcharts={Highcharts}
            options={finalOptions}
          />
        );
      }
    };

    return getVizJSX(conf, zone, data);
  }

  dataManipulator(conf, zone, data, cb) {
    const w = new Worker(dataWorkerScript);

    w.onmessage = (event) => {
      if (cb) {
        cb(event.data);
      }
    };

    w.postMessage({
      conf,
      zone,
      data,
    });
  }

  fileDownloader(fileString, fileName, fileType, isZip) {
    var exportedFilename = fileName;

    var blob = new Blob([fileString], {
      type: "text/" + fileType + ";charset=utf-8;",
    });
    if (window.cordova && window.cordova.platformId !== "browser") {
      SaveFileToDisk(blob, fileName, fileType);
      return;
    }
    if (isZip) {
      // temporarily removed to reduce bundle size as this is not used currently
      // this.downloadBlobAsZip(blob, fileName + '.' + fileType);
    } else {
      if (navigator.msSaveBlob) {
        // IE 10+
        navigator.msSaveBlob(blob, exportedFilename);
      } else {
        var link = document.createElement("a");
        if (link.download !== undefined) {
          // feature detection
          // Browsers that support HTML5 download attribute
          var url = URL.createObjectURL(blob);
          link.setAttribute("href", url);
          link.setAttribute("download", exportedFilename);
          link.style.visibility = "hidden";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      }
    }
  }

  setAllGraphsImageData(cb) {
    let chart = [],
      svgString = "",
      graphCount = this.allData.GraphIds.length;
    const ctx = this;

    const allGraphImageData = {};
    this.allData.GraphIds.forEach((graphId, i) => {
      chart = ctx.refs[graphId].chart;

      if (!chart) {
        console.log("chart not found for graphId", graphId);
        graphCount--;
        return;
      }

    //   const boostCanvas = chart.container.querySelector(
    //     "canvas.highcharts-boost-canvas",
    //   );

    //   if (boostCanvas) {
    //     console.log(`FAST PATH: Found boost canvas for chart ${graphId}.`);
    //     try {
    //       const pngData = boostCanvas.toDataURL("image/png");

    //       allGraphImageData[graphId] = {
    //         pngData,
    //         graphheight: boostCanvas.height,
    //         graphWidth: boostCanvas.width,
    //       };
    //       if (Object.keys(allGraphImageData).length === graphCount) {
    //         if (cb) {
    //           cb(allGraphImageData);
    //         }
    //       }
    //     } catch (e) {
    //       console.error(
    //         `Error capturing boosted canvas for chart ${graphId}:`,
    //         e,
    //       );
    //       // Decrementing count so the process doesn't hang
    //       graphCount--;
    //     }
	// 	return;
    //   }

      svgString = Highcharts.getSVG([chart]);
      const graphConfig = ctx.allData.Graph[i];
      var svgData =
        "data:image/svg+xml;base64," +
        btoa(unescape(encodeURIComponent(svgString)));

      var canvas = document.createElement("canvas");
      canvas.style.border = "1px solid white";
      var context = canvas.getContext("2d");

      let graphWidth = 1500;
      let graphheight = 420;
      canvas.width = graphWidth;
      canvas.height = graphheight;
      const image = new Image();
      image.onload = () => {
        context.clearRect(0, 0, graphWidth, graphheight);
        context.drawImage(image, 0, 0, graphWidth, graphheight);
        var pngData = canvas.toDataURL("image/png");

        allGraphImageData[graphId] = {
          pngData,
          graphheight,
          graphWidth,
        };

        if (Object.keys(allGraphImageData).length === graphCount) {
          if (cb) {
            cb(allGraphImageData);
          }
        }
      };
      if (0) this.fileDownloader(svgData, "svg", "svg", false);
      image.src = svgData;
    });
  }

  componentDidMount() {
    const { conf, zone, data } = this.props;

    this.dataManipulator(conf, zone, data, (eventData) => {
      this.allData = eventData.allData;

      this.setState({
        isDataManipulated: true,
        data: eventData.data,
      });
    });
  }

  loadImage(imageSource) {
    return new Promise((resolve, reject) => {
      const image = new Image();
      image.onload = () => {
        resolve(image);
      };
      image.onerror = () => {
        reject(new Error("Error while loading the image."));
      };
      image.src = imageSource;
    });
  }

  componentDidUpdate(prevProps, prevState, snapshot) {
    const { onExportReady } = this.props;
    if (onExportReady) {
      onExportReady();
    }
  }

  render() {
    const {
      conf,
      zone,
      showExportOptions,
      progressUpdate,
      g2gSwitch,
      file_name,
    } = this.props;
    const { data } = this.state;

    const vizJSX = () => {
      try {
        if (conf.length && data.length) {
          return this.visualize(conf, zone, data);
        } else {
          return (
            <div>
              <h2>Error:</h2>
              <h4>Config and/or Data is not defined</h4>
            </div>
          );
        }
      } catch (e) {
        return (
          <div>
            <h2>Error:</h2>
            <h4>{e}</h4>
          </div>
        );
      }
    };

    const exportOptionsJSX = (showExportOptions) => {
      if (showExportOptions) {
        const { xlsx, pdf, csv } = showExportOptions;
        const exportButtons = [];
        if (xlsx) {
          const xlsxExportBtn = (
            <Button
              onClick={() => {
                this.exportXLSX(progressUpdate);
              }}
            >
              Export XLSX
            </Button>
          );
          exportButtons.push(xlsxExportBtn);
        }
        if (csv) {
          const csvExportBtn = (
            <Button
              onClick={() => {
                this.exportCSV(progressUpdate);
              }}
            >
              Export CSV
            </Button>
          );
          exportButtons.push(csvExportBtn);
        }
        if (pdf) {
          const pdfExportBtn = (
            <Button
              onClick={() => {
                this.exportPDF2(this);
              }}
            >
              Export PDF
            </Button>
          );
          exportButtons.push(pdfExportBtn);
        }

        return (
          <>
            {exportButtons}
            {xlsx || pdf || csv ? (
              <>
                <br />
                <br />
              </>
            ) : (
              []
            )}
          </>
        );
      } else {
        return [];
      }
    };

    const graphToGridSwitch = () => {
      if (g2gSwitch && g2gSwitch.visible) {
        const { disable, checked } = g2gSwitch;
        const g2gOnChange = (checked) => {
          this.setState({
            showTable: !checked,
            showGraph: checked,
          });
        };
        return (
          <div style={{ textAlign: "right" }}>
            <span className="toggleLabels">Table</span>
            <Switch
              size={"small"}
              disabled={disable}
              defaultChecked={checked}
              onChange={g2gOnChange}
            />
            <span className="toggleLabels">Graph</span>
          </div>
        );
      } else {
        return <></>;
      }
    };

    return (
      <div>
        {exportOptionsJSX(showExportOptions)}
        {graphToGridSwitch()}
        {vizJSX()}
      </div>
    );
  }
}
