import React, { Component } from "react";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import "./style.less";

class ReorderableList extends Component {
  constructor(props) {
    super(props);
  }

  // Helper to reorder items within the same list
  reorder = (list, startIndex, endIndex) => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);
    return result;
  };

  handleDragEnd = (result) => {
    const { source, destination } = result;
    
    // Dropped outside any droppable area
    if (!destination) {
      return;
    }

    const { items } = this.props;
    let newItems = [...items];
    
    // Extract droppable IDs to determine context
    const sourceId = source.droppableId;
    const destId = destination.droppableId;
    
    // Main list reordering
    if (sourceId === "reorderable-list" && destId === "reorderable-list") {
      newItems = this.reorder(items, source.index, destination.index);
    } 
    // Child list reordering (within the same parent)
    else if (sourceId.startsWith("child-list-") && sourceId === destId) {
      const parentIndex = parseInt(sourceId.replace("child-list-", ""));
      const parent = {...newItems[parentIndex]};
      const children = this.reorder(
        parent.children,
        source.index,
        destination.index
      );
      parent.children = children;
      newItems[parentIndex] = parent;
    }
    
    this.props.setItems(newItems);
  };

  renderDraggableItem = (item, index, isDragDisabled = false) => (
    <Draggable
      key={item.dataIndex || item.key || `item-${index}`}
      draggableId={item.dataIndex || item.key || `item-${index}`}
      index={index}
      isDragDisabled={isDragDisabled || (item.not_customizable && item.fixed) || false}
    >
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={
            "reorderable-list-item" +
            (snapshot.isDragging ? " dragging" : "") +
            (item.not_customizable ? " not-customizable" : "") +
            (item.not_customizable && item.fixed ? " item-fixed" : "")
          }
        >
          {item.renderTitle}
          
          {/* Render nested children if they exist */}
          {item.children && item.children.length > 0 && (
            <Droppable droppableId={`child-list-${index}`}>
              {(childProvided) => (
                <div
                  ref={childProvided.innerRef}
                  {...childProvided.droppableProps}
                  className="dtms-reorderable-list-children"
                >
                  {item.children.map((child, childIndex) => (
                    this.renderDraggableItem(child, childIndex, item.fixed)
                  ))}
                  {childProvided.placeholder}
                </div>
              )}
            </Droppable>
          )}
        </div>
      )}
    </Draggable>
  );

  render() {
    const { items } = this.props;
    
    return (
      <DragDropContext onDragEnd={this.handleDragEnd}>
        <Droppable droppableId="reorderable-list">
          {(provided) => (
            <section
              {...provided.droppableProps}
              ref={provided.innerRef}
              className="dtms-reorderable-list"
            >
              {items.map((item, index) => (
                this.renderDraggableItem(item, index)
              ))}
              {provided.placeholder}
            </section>
          )}
        </Droppable>
      </DragDropContext>
    );
  }
}

export default ReorderableList;

// class ReorderableListCustom extends Component {
// 	constructor(props) {
// 		super(props);
// 		this.state = {
// 			items: ['Item 1', 'Item 2', 'Item 3', 'Item 4'],
// 			dragIndex: null,
// 		};
// 	}

// 	handleDragStart = (index, event) => {
// 		event.dataTransfer.effectAllowed = 'move';
// 		event.dataTransfer.setData('text/plain', index.toString());
// 		this.setState({ dragIndex: index });
// 	};

// 	handleDragOver = (index, event) => {
// 		event.preventDefault();

// 		if (index !== this.state.dragIndex) {
// 			const items = [...this.state.items];
// 			const [draggedItem] = items.splice(this.state.dragIndex, 1);
// 			items.splice(index, 0, draggedItem);

// 			this.setState({ items, dragIndex: index });
// 		}
// 	};

// 	handleDragEnd = () => {
// 		this.setState({ dragIndex: null });
// 	};

// 	render() {
// 		return (
// 			<ul className="dtms-reorderable-list">
// 				{this.state.items.map((item, index) => (
// 					<li
// 						key={item}
// 						draggable
// 						onDragStart={(event) =>
// 							this.handleDragStart(index, event)
// 						}
// 						onDragOver={(event) =>
// 							this.handleDragOver(index, event)
// 						}
// 						onDragEnd={this.handleDragEnd}
// 						className="reorderable-list-item"
// 						// style={{
// 						// 	opacity: this.state.dragIndex === index ? 0.5 : 1,
// 						// 	transition: 'opacity 0.3s ease',
// 						// }}
// 					>
// 						{item}
// 					</li>
// 				))}
// 			</ul>
// 		);
// 	}
// }

// export default ReorderableListCustom;
