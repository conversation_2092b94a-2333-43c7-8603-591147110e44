import React, { Component } from "react";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import "./style.less";
import TripIcon from "@datoms/dg-monitoring-views/src/js/GenericTemplate/images/Trip-Icon.svg";
import WarningIcon from "@datoms/dg-monitoring-views/src/js/GenericTemplate/images/Warning-Icon.svg";
import NotDefinedIcon from "@datoms/dg-monitoring-views/src/js/GenericTemplate/images/Not-Defined-Icons.svg";
import BothIcon from "@datoms/dg-monitoring-views/src/js/GenericTemplate/images/Warning&Trip-Icon.svg";
import { GlobalContext } from '../../../../../store/globalStore';

class ImageComponent extends Component {
  static contextType = GlobalContext;
  constructor(props) {
    super(props);
  }

  capitalizeFirstLetter(string) {
    if (string.length === 0) return string;
    if (typeof string !== "string") return string;
    return string.charAt(0).toUpperCase() + string.slice(1);
  }

  render() {
    const {
      tooltip,
      title,
      deviceStatus,
      status,
      asset_name,
      time,
      category,
      background,
      src,
      show_status,
      status_color,
      assetHasStatus,
      faultStatus,
      iconSize
    } = this.props;
    const isSistemaBioCustomer = this.context?.isSistemaBioCustomer;
    const toShowFaults = !isSistemaBioCustomer && faultStatus?.length;

    const wrapper = (
      <div className="rc_img_comp_wrapper">
        <img
          style={{width : iconSize && iconSize === "large" ? "100%" : null}}
          src={
            src ||
            "https://static.datoms.io/images/icons/thing-category/default.svg"
          }
          alt="thing_type"
        />
        {show_status && typeof deviceStatus !== "string" ? (
          <div
            style={this.props.status_color || {}}
            className={
              "status-circle " +
              (this.props.className || "") +
              " " +
              (!this.props.status
                ? ""
                : this.props.status === "online"
                  ? "online"
                  : "offline")
            }
          />
        ) : (
          ""
        )}
        {show_status ?
          !isSistemaBioCustomer ? (
            <div className={`device-status-circle ${deviceStatus || ""}`}>
              {typeof deviceStatus === "string" && deviceStatus !== "offline" && (
                <div
                  style={status_color || {}}
                  className={`asset-status-circle ${assetHasStatus ? "asset-has-status" : ""} ${status || ""}`}
                />
              )}
            </div>
          ) : (
            <div className={`only-asset-status ${status || ""}`} >
              <div
                style={status_color || {}}
                className={`asset-status-circle ${status || ""}`}
              />
            </div>
          )
        : ""}
        {toShowFaults ? (
          <img
            className="fault-icon-image"
            src={
              faultStatus === "both"
                ? BothIcon
                : faultStatus === "trip"
                  ? TripIcon
                  : faultStatus === "warning"
                    ? WarningIcon
                    : faultStatus === "not_defined"
                      ? NotDefinedIcon
                      : ""
            }
          />
        ) : (
          ""
        )}
      </div>
    );

    if (tooltip) {
      return (
        <AntTooltip
          title={
            <div>
              {title?.length && (
                <div>
                  {title}
                </div>
              )}
              {!isSistemaBioCustomer && deviceStatus?.length && (
                <div>{`${this.props.t? this.props.t('device_status'): "Device Status"}: ${this.props.t? this.props.t(deviceStatus): deviceStatus}`}</div>
              )}
              {status?.length && !isSistemaBioCustomer &&
              deviceStatus !== "offline" &&
              (assetHasStatus || status === "offline") ? (
                <div>
                  {this.props.t? this.props.t('asset_status') + ": ": "Asset status: "}
                  {status === "offline"
                    ? "Disconnected"
                    : this.capitalizeFirstLetter(status)}
                </div>
              ) : (
                ""
              )}

              {isSistemaBioCustomer && status?.length && (
                <div>
                  {"Genset status: "}
                  {status === "running"
                    ? "On"
                    : "Off"}
                </div>
              )}
              
              {toShowFaults ? <div>Fault type: {faultStatus === "both"
                ? 'Both (Trip & Warning)'
                : faultStatus === "trip"
                  ? 'Trip'
                  : faultStatus === "warning"
                    ? 'Warning'
                    : faultStatus === "not_defined"
                      ? 'Not defined'
                      : ""}</div>:''}
            </div>}
        >
          {wrapper}
        </AntTooltip>
      );
    }

    if (asset_name) {
      return (
        <div className="rc_img_comp">
          {wrapper}
          <div>
            <div className="rc_img_comp-h1">{asset_name}</div>
            <div className="rc_img_comp-l1">{time}</div>
          </div>
        </div>
      );
    }

    return wrapper;
  }
}

export default ImageComponent;
