.rc_img_comp_wrapper {
  position: relative;
  height: 45px;
  aspect-ratio: 1;
  background: transparent;
  border-radius: 50%;
  display: flex;
  padding: 1px;
  margin-right: 7px;
  justify-content: center;
  align-items: center;
  img{
    width: 50%;
    aspect-ratio: 1;
    margin: auto;
    object-fit: contain;
    z-index: 1;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .fault-icon-image {
    top: auto;
    bottom: 5px;
    left: auto;
    right: 2px;
    transform: none;
    width: 18px;
    height: 18px;
  }
  .status-circle {
    position: absolute;
    bottom: 0;
    right: 0;
    transform: translateX(-27%);
    border: 2.6px solid #e7ecf4;
    border-radius: 50%;
    height: 12px;
    width: 12px;
    &.online {
      background-color: #147437 !important;
    }
    &.offline {
      background-color: gray;
    }
  }
  .green {
    background: #1cb855 !important;
  }
  .device-status-circle {
    width: 30px;
    height: 30px;
    position: absolute;
    border-radius: 50%;
    padding: 20px;
    &.online {
      background-color: #72c264 !important;
    }
    &.offline {
      background-color: #B8B1B1;
    }
    .asset-status-circle {
      width: 81%;
      height: 81%;
      position: absolute;
      top: 4px;
      border-radius: 50%;
      left: 4px;
      &.running {
        background-color: #72c264 !important;
      }
      &.offline {
        background-color: #B8B1B1 !important;
      }
      &.asset-has-status.running {
        background-color: #5bb07a !important;
      }
      &.asset-has-status.stopped {
        background-color: #f65f54 !important;
      }
    }
  }
  .only-asset-status {
    width: 30px;
    height: 30px;
    position: absolute;
    border-radius: 50%;
    padding: 20px;
    &.running {
      background-color: #5bb07a !important;
    }
    &.offline {
      background-color: #d2d2d2 !important;
    }
    &.stopped {
      background-color: #d2d2d2 !important;
    }
    .asset-status-circle {
      width: 81%;
      height: 81%;
      position: absolute; 
      top: 4px;
      border-radius: 50%;
      left: 4px;
      &.running {
        background-color: #5bb07a !important;
      }
      &.offline {
        background-color: #d2d2d2 !important;
      }
      &.stopped {
        background-color: #d2d2d2 !important;
      }
    }
  }
}

.tm_tl_mb_container{
  .status-circle {
    background: #8d8787;
    border-radius: 50%;
    height: 14px;
    width: 14px;
  }
}
.rc_img_comp {
  display: flex;
  gap: 2px;
  .rc_img_comp-h1 {
    font-size: 14px;
    font-weight: 500;
    color: #232323;
  }
  .rc_img_comp-l1 {
    font-size: 12px;
    font-weight: 400;
    color: #808080;
  }
}