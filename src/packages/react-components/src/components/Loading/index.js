/* Libs */
import React, { useState, useEffect } from 'react';
import { Layout } from 'antd';
import { bool } from 'prop-types';

/* Own Libs */

/* Styles */
import './style.less';

/* Configs */
import defaultConfigs from './defaultConfigs';
import { useGlobalContext } from '../../../../../store/globalStore';

/**
 * Loading
 *
 * When To Use:
 *
 * 1. To display a loading dialog before loading of a page.
 **/
// export default class Loading extends React.Component {
// 	// initializing defaultprops, so that it will provide default configs
// 	static defaultProps = {
// 		...defaultConfigs,
// 	};

// 	static propTypes = {
// 		/** Should re-rendering of the component be prevented via `shouldComponentUpdate`. The value specified during mounting the component is considered as final & can't be changed further until the component stays mounted. */
// 		optimizeWithShouldComponentUpdate: bool,

// 		/** Decides whether the logo to be displayed or not. */
// 		show_logo: bool,
// 		is_collapsed: bool,
// 	};

// 	constructor(props) {
// 		super(props);
// 		this.state = {
// 			message: [
// 				'fetching_your_application',
// 				'initializing_engines...',
// 				'loading_your_data...',
// 				'few_seconds_more...',
// 			],
// 			flag: 0,
// 		};

// 		this.optimizeWithShouldComponentUpdate =
// 			props.optimizeWithShouldComponentUpdate;
// 	}

// 	componentDidMount() {
// 		this.v = setInterval(() => this.update(), 5000);
// 	}

// 	componentWillUnmount() {
// 		clearInterval(this.v);
// 	}

// 	update() {
// 		this.setState({ flag: this.state.flag + 1 });

// 		if (this.state.flag > this.state.message.length - 1) {
// 			this.setState({ flag: 0 });
// 		}
// 	}

// 	render() {
// 		console.log("LOADING FUNCTION", this.props);
		
// 		return (
// 			<Layout
// 				className={
// 					'conts' + (this.props.is_collapsed ? ' collapsed-side' : '')
// 				}
// 				style={{ marginTop: '60px' }}
// 			>
// 				<div className="load-center">
// 					<img
// 						className="svg"
// 						src="https://prstatic.phoenixrobotix.com/imgs/index/index-page-loader.svg"
// 						alt=""
// 					/>
// 					<div className="msg">
// 						{this.props.t? this.props.t(this.state.message[this.state.flag]): this.state.message[this.state.flag]}
// 					</div>
// 				</div>
// 				<div className="b-logo">{this.abx()}</div>
// 			</Layout>
// 		);
// 	}
// }


const Loading = ({ optimizeWithShouldComponentUpdate, show_logo, is_collapsed }) => {
  // Define state using useState 
  const context = useGlobalContext();
  const {t, is_white_label} = context;
  
  const [flag, setFlag] = useState(0);
  const message = [
    'fetching_your_application',
    'initializing_engines',
    'loading_your_data',
    'few_seconds_more',
  ];

  // useEffect to mimic componentDidMount and componentWillUnmount
  useEffect(() => {
    const interval = setInterval(() => {
      setFlag((prevFlag) => (prevFlag + 1) % message.length);
    }, 5000);

    return () => clearInterval(interval); // Cleanup on unmount
  }, [message.length]);

  const abx = () => {
    if(!is_white_label && show_logo) {
      return <div ><img src="https://prstatic.phoenixrobotix.com/imgs/datoms/iot-platform/datoms_logo.svg" /> </div>;
    }
    return null;
  };

  return (
    <Layout
      className={`conts${is_collapsed ? ' collapsed-side' : ''}`}
      style={{ marginTop: '60px' }}
    >
      <div className="load-center">
        <img
          className="svg"
          src="https://prstatic.phoenixrobotix.com/imgs/index/index-page-loader.svg"
          alt=""
        />
        <div className="msg">
          {typeof t === 'function' ? t(message[flag]) + "..." : message[flag] + "..."}
        </div>
      </div>
      <div className="b-logo">{abx()}</div>
    </Layout>
  );
};

// Default props
Loading.defaultProps = {
  ...defaultConfigs,
};

// PropTypes validation
Loading.propTypes = {
  /** Should re-rendering of the component be prevented via `shouldComponentUpdate`. The value specified during mounting the component is considered as final & can't be changed further until the component stays mounted. */
  optimizeWithShouldComponentUpdate: bool,

  /** Decides whether the logo to be displayed or not. */
  show_logo: bool,
  is_collapsed: bool,
};

export default Loading;
