/*Libs*/
import React from 'react';
import { bool, string, object, number, func, node } from 'prop-types';

/*Components*/
import { Tooltip } from 'antd';
import Indicator from './js/Indicator';
import LockComponent from './js/LockComponent';
import AntPasswordModal from '../AntPasswordModal';
import AntNotification from '../AntNotification';
import AntButton from '../AntButton';

/*Styles*/
import './style.less';
/*Images*/
import lockIcon from '../../imgs/lock.svg';
import unlockIcon from '../../imgs/unlock.svg';
import lockIconOrange from '../../imgs/lockOrange.svg';
import unlockIconOrange from '../../imgs/unlockOrange.svg';
import { GlobalContext } from '../../../../../store/globalStore';

/**
ControlSwitch is used for performing any trigger functions on an asset
such as ON/OF, lock/unlock, start/stop, etc.
 **/

export default class ControlButton extends React.Component {
	static defaultProps = {
		className: '',
	};
	static contextType = GlobalContext;
	//setting props types
	static propTypes = {
		socket: object,
		client_id: number,
		application_id: number,
		thingCommandStatus: object,
		thing_id: number,
		dgStatus: string,
		isControlEnabled: bool,
		getViewAccess: bool,
		modalTitle: string,
		subTitle: node,
		onCommand: string,
		offCommand: string,
		assetType: string,
		enableTooltip: bool,
		enableSideText: bool,
		triggerCommand: func,
		isRentalDG: bool,
		featureLockUnlock: bool,
		dgLockStatus: string,
		rent_status: string,
		enableBottomText: bool,
	};
	constructor(props) {
		super(props);
		this.updatePassword = this.updatePassword.bind(this);
		this.state = {
			sowPopUp: false,
		};
	}

	executeCommand(intendedCommand, password) {
		if (typeof this.props.triggerCommand === 'function') {
			const applicationId = 
			this.context?.application_id &&
			this.context.client_id === this.props.client_id &&
			this.context.application_id !== this.props.application_id
				? this.context.application_id
				: this.props.application_id;
			this.props.triggerCommand(
				this.props.socket,
				intendedCommand,
				password,
				this.props.thing_id,
				this.props.client_id,
				applicationId,
				(response) => {
					let notificationType;
					if (response.status === 'success') {
						notificationType = 'success';
					} else {
						notificationType = 'error';
					}
					this.openNotification(notificationType, response.message);
					console.log(response.message);
				}
			);
		}
	}

	openNotification(type, msg) {
		AntNotification({
			type,
			message: msg,
			placement: 'bottomLeft',
			className: 'alert-' + type,
		});
	}

	shouldComponentUpdate(nextState) {
		if (this.state.sowPopUp !== nextState.sowPopUp) {
			return true;
		}
		return false;
	}

	onSwitchChange(e) {
		this.setState({
			sowPopUp: true,
		});
	}

	handleOk() {
		const intendedCommand = this.getIntendedCommand();
		this.executeCommand(intendedCommand, this.state.password);
		this.setState({
			sowPopUp: false,
			password: '',
		});
	}

	handleCancel() {
		this.setState({
			sowPopUp: false,
			password: '',
		});
	}

	getIntendedCommand() {
		let intendedCommand = this.props.onCommand;
		if (this.props.featureLockUnlock) {
			if (this.props.dgLockStatus === '1') {
				intendedCommand = this.props.offCommand;
			}
		} else {
			if (this.props.dgStatus === '1') {
				intendedCommand = this.props.offCommand;
			}
		}
		return intendedCommand;
	}

	updatePassword(password) {
		this.setState({
			password: password,
		});
	}

	getDgStatus() {
		let statusText;
		if(this.props.dgStatus === '1'){
			if(this.context?.isSistemaBioCustomer){
				statusText = 'Genset On';
			} else {
				statusText = this.props.runningText ?? 'Running';
			}
		} else {
			if(this.context?.isSistemaBioCustomer){
				statusText = 'Genset Off';
			} else {
				statusText = this.props.stoppedText ?? 'Stopped';
			}
		}
		return statusText;
	}

	getOfflineValue(){
		let isOffline = this.props.dgStatus === '2' ? true : false;
		if(this.context?.isSistemaBioCustomer){
			isOffline = this.props.dgStatus === '1' ? false : true;
		}
		return isOffline;
	}

	getLockIcon() {
		let lockIconToShow = this.props.dgStatus === '2' ? lockIcon : lockIconOrange;
		if(this.context?.isSistemaBioCustomer){
			lockIconToShow = this.props.dgStatus === '1' ? lockIconOrange : lockIcon;
		}
		return lockIconToShow;
	}

	getUnlockIcon() {
		let unlockIconToShow = this.props.dgStatus === '2' ? unlockIcon : unlockIconOrange;
		if(this.context?.isSistemaBioCustomer){
			unlockIconToShow = this.props.dgStatus === '1' ? unlockIconOrange : unlockIcon;
		}
		return unlockIconToShow;
	}

	render() {
		const intendedCommand = this.getIntendedCommand();
		let intendedCommandDetails = {};
		if (
			this.props.thingCommandStatus &&
			this.props.thingCommandStatus[intendedCommand]
		) {
			intendedCommandDetails = this.props.thingCommandStatus[
				intendedCommand
			];
		}
		let commandInProcess =
			intendedCommandDetails['status'] === 'in_process';
		let commandAllowed;
		if (this.props.isRentalDG && this.props.featureLockUnlock) {
			commandAllowed =
				intendedCommandDetails['status'] === 'allowed' &&
				this.props.isControlEnabled &&
				this.props.getViewAccess &&
				this.props.dgStatus !== '2' &&
				this.props.operation_mode === 'manual' &&
				this.props.rent_status !== 'completed';
		} else {
			commandAllowed =
				intendedCommandDetails['status'] === 'allowed' &&
				this.props.isControlEnabled &&
				this.props.getViewAccess &&
				this.props.dgStatus !== '2';
		}
		let switchStatus;
		if (this.props.featureLockUnlock) {
			switchStatus = this.props.dgLockStatus === '1' ? true : false;
		} else {
			switchStatus = this.props.dgStatus === '1' ? true : false;
		}

		let lockRender = this.props.isOnlyLock ? (
			<div>
				<LockComponent
					offline={this.getOfflineValue()}
					onSwitchChange={() => this.onSwitchChange()}
					loading={commandInProcess}
					disabled={!commandAllowed}
					icon={
						switchStatus ? (
							<img
								src={this.getLockIcon()}
								alt="lock"
							/>
						) : (
							<img
								src={this.getUnlockIcon()}
								alt="unlock"
							/>
						)
					}
				/>
			</div>
		) : (
			<div className="dg_control_lock_unlock">
				<AntButton
					style={{
						backgroundColor:
							this.props.dgStatus === '2' ? '#8E8E8E' : '#FF8500',
					}}
					loading={commandInProcess}
					disabled={!commandAllowed}
					onClick={() => this.onSwitchChange()}
					icon={
						switchStatus ? (
							<img src={lockIcon} alt="lock" />
						) : (
							<img src={unlockIcon} alt="unlock" />
						)
					}
				>
					{switchStatus ? 'DG Locked' : 'DG Unlocked'}
				</AntButton>
				{this.props.enableBottomText && (
					<p className="dg_control-status">DG Control status</p>
				)}
			</div>
		);

		if (
			!this.props.isControlEnabled ||
			!this.props.getViewAccess ||
			this.props.dgStatus === '2' ||
			this.props.rent_status === 'completed' ||
			this.props.operation_mode === 'auto'
		) {
			lockRender = (
				<Tooltip
					title={
						commandInProcess
							? 'Loading...'
							: !(
									this.props.isControlEnabled &&
									this.props.getViewAccess
							  )
							? 'Remote Lock/Unlock access not available'
							: this.props.operation_mode === 'auto'
							? 'DG Operation Mode must be configured as manual for the Lock-Unlock feature to be available'
							: this.props.dgStatus === '2'
							? `${
									this.props.assetType || ''
							  } Status: Not Connected`
							: this.props.rent_status === 'completed'
							? 'The order is closed !'
							: `${this.props.assetType || ''} Status: ${
									this.props.dgLockStatus === '1'
										? 'Locked'
										: 'Unlocked'
							  }`
					}
					placement="topLeft"
				>
					{lockRender}
				</Tooltip>
			);
		}
		return (
			<div className="control-switch-container">
				{this.props.featureLockUnlock ? (
					lockRender
				) : (
					<>
						{this.props.enableSideText && (
							<div className="side-text-status">
								{commandInProcess
									? 'Loading...'
									: this.props.dgStatus === '2'
									? this.props.offlineText
										? this.props.offlineText
										: 'Not Connected'
									: this.props.dgStatus === '1'
									? this.props.runningText
										? this.props.runningText
										: 'Running'
									: this.props.stoppedText
									? this.props.stoppedText
									: 'Stopped'}
							</div>
						)}
						<Indicator
							status={switchStatus}
							offline={this.getOfflineValue()}
							dgStatus={this.getDgStatus()}
							assetType={this.props.assetType}
							onSwitchChange={(e) => this.onSwitchChange(e)}
							loading={commandInProcess}
							disabled={!commandAllowed}
							enableTooltip={this.props.enableTooltip}
							isRemoteControlEnabled={
								this.props.isControlEnabled &&
								this.props.getViewAccess
							}
						/>
					</>
				)}

				<AntPasswordModal
					isVisible={this.state.sowPopUp}
					title={this.props.modalTitle || 'Password Authentication'}
					placeholder={'Enter Password'}
					onOk={() => this.handleOk()}
					onCancel={() => this.handleCancel()}
					onPasswordUpdate={(e) => this.updatePassword(e)}
					subTitle={
						this.props.subTitle || (
							<p>
								Please enter your password to{' '}
								<span>
									{intendedCommand === 'dg_start'
										? 'start'
										: 'stop'}{' '}
								</span>
								the DG.
							</p>
						)
					}
				/>
			</div>
		);
	}
}
