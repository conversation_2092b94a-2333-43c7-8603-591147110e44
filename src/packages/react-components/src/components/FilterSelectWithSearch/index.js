/* Libs */
import React from 'react';
import { Select, Input, AutoComplete, Radio, TreeSelect } from 'antd';
import DatePicker from '@datoms/react-components/src/components/AntDatePicker';
import {
	bool,
	arrayOf,
	shape,
	string,
	array,
	func,
	oneOfType,
} from 'prop-types';
import FilterOutlined from '@ant-design/icons/FilterOutlined';
import SearchOutlined from '@ant-design/icons/SearchOutlined';
import SelectWithRangepicker from '@datoms/react-components/src/components/SelectWithRangepicker';
import _sortBy from 'lodash/sortBy';

/* Own Libs */
import isComponentUpdateRequired from '../../libs/IsComponentUpdateRequired';

/* Styles */
import './style.less';

/*Images*/
import clear_all from '../../imgs/clear_all.svg';

/* Configs */
import defaultConfigs from './defaultConfigs';
import { typeWiseTimeOptions } from '@datoms/dg-monitoring-views/src/js/GenericTemplate/pages/TripView/configs/time-configs';

const { Option } = Select;
const { RangePicker } = DatePicker;

/**
 * Represents daily - data in a calendar where each day in the calendar is represented as a certain color as per its data value. Various colors can be specified with each color representing a certain range of values.
 *
 * When To Use:
 *
 * 1. To show daily data in a calendar heat map.
 **/
export default class FilterSelectWithSearch extends React.Component {
	// initializing defaultprops, so that it will provide default configs
	static defaultProps = {
		...defaultConfigs,
	};

	static propTypes = {
		/** Should re-rendering of the component be prevented via `shouldComponentUpdate`. The value specified during mounting the component is considered as final & can't be changed further until the component stays mounted. */
		optimizeWithShouldComponentUpdate: bool,
		filterData: arrayOf(
			shape({
				optionData: arrayOf(
					shape({
						value: string.isRequired,
						title: string.isRequired,
					})
				),
				selectValue: oneOfType([array.isRequired, string.isRequired]),
				multiSelect: bool,
				allowClear: bool,
				showSearch: bool,
				filterType: string,
				className: string,
				placeholder: string,
				viewClassName: string,
			})
		).isRequired,
		applyFilterSelect: func.isRequired,
	};

	constructor(props) {
		super(props);
		this.state = {};

		this.optimizeWithShouldComponentUpdate =
			props.optimizeWithShouldComponentUpdate;
	}

	shouldComponentUpdate(nextProps, nextState) {
		return isComponentUpdateRequired(
			this.optimizeWithShouldComponentUpdate,
			this.props,
			this.state,
			nextProps,
			nextState
		);
	}

	clearAllValue() {
		if (!this.props.is_all_disabled) {
			this.props.clearAllFilter();
		}
	}

	optionGroupFilter(input, option) {
        if (!option) return false;
        const { label, groupLabel } = option;
		if(!label || !groupLabel) return false;
        return (
          label?.toLowerCase().includes(input.toLowerCase()) ||
          groupLabel?.toLowerCase().includes(input.toLowerCase())
        );
      }

	render() {
		const { rangePickerConfig } = this.props;
		let filterContainer = [],
			filterViewContainer = [],
			isValue = false,
			tagSelectValues = [],
			isLabel = false;
		if (this.props.filterData) {
			this.props.filterData.map((filter, index) => {
				if(filter.hideField) return;
				let filterText = '';
				if (filter.filterType) {
					filterText = filter.filterType.replace(/-/g, ' ');
				}

				if (
					!filter.allClearDisabled &&
					(((filter.selectValue || filter.selectValue === 0) &&
						!Array.isArray(filter.selectValue) &&
						filter.selectValue !== '') ||
						(Array.isArray(filter.selectValue) &&
							filter.selectValue.length > 0))
				) {
					isValue = true;
				}
				if (filter.type === 'date' && !filter.hideField) {
					let dateValues =
						filter.selectValue && filter.selectValue.includes('-')
							? filter.selectValue.split('-')
							: [undefined, undefined];
					let fromTime = !isNaN(parseInt(dateValues[0]))
						? parseInt(dateValues[0])
						: undefined;
					let uptoTime = !isNaN(parseInt(dateValues[1]))
						? parseInt(dateValues[1])
						: undefined;
					filterContainer.push(
						<div className="select-container">
							{filter.label && (
								<label className="select-label">
									{filter.label}
								</label>
							)}
							<div
								className={
									'select-input-container' +
									(filter.is_search_type
										? ' select-input-container-search-type'
										: '')
								}
							>
								<SelectWithRangepicker
									t={this.props.t}
									rangeClearDefault
									fromTime={fromTime}
									uptoTime={uptoTime}
									noDefaultValue={filter.noDefaultValue}
									allowClear={filter.allowClear}
									showSearch={filter.showSearch}
									config={rangePickerConfig?.datePickerConfig ? rangePickerConfig.datePickerConfig : filter.datePickerConfig}
									ranges={filter.custom_date_select ? []: rangePickerConfig?.ranges ? rangePickerConfig.ranges : typeWiseTimeOptions.trips.ranges} //{filter.ranges}
									onRangeChange={(dateArray) => {
										let finalValue = Array.isArray(
											dateArray
										)
											? dateArray.join('-')
											: '';
										this.props.applyFilterSelect(
											finalValue,
											index
										);
									}}
									onOpenChange={(e) => 1}
									filterClassName="filter-drop no-filter-icon"
									// onCalendarChange={(val) => {
									// 	this.setState({
									// 		dateArray: val,
									// 	});
									// }}
									filterIcon={
										<FilterOutlined className="filter-icon" />
									}
									is_all_disabled={this.props.is_all_disabled}
									custom_date_select={filter.custom_date_select}
								/>
							</div>
						</div>
					);
				}
				if (filter.type === 'range_picker') {
					let dateValues =
						filter.selectValue && filter.selectValue.includes('-')
							? filter.selectValue.split('-')
							: ['', ''];
					let fromTime = !isNaN(parseInt(dateValues[0]))
						? parseInt(dateValues[0])
						: undefined;
					let uptoTime = !isNaN(parseInt(dateValues[1]))
						? parseInt(dateValues[1])
						: undefined;
					filterContainer.push(
						<div className="select-container">
							{filter.label && (
								<label className="select-label">
									{filter.label}
								</label>
							)}
							<div
								className={
									'select-input-container' +
									(filter.is_search_type
										? ' select-input-container-search-type'
										: '')
								}
							>
								<RangePicker
									picker={filter.picker || 'month'}
									onChange={(dateArray) => {
										let finalValue = Array.isArray(
											dateArray
										)
											? dateArray
													.map((item) => item.unix())
													.join('-')
											: '';
										this.props.applyFilterSelect(
											finalValue,
											index
										);
									}}
									disabled={filter.is_all_disabled}
								/>
							</div>
						</div>
					);
				}

				if (filter.type === "tree_select") {
					isLabel = isLabel || filter.label ? true : false;
					filterContainer.push(
					  <div className="select-container">
						{filter.label && (
						  <label className="select-label">
							{filter.label}
							</label>
						)}
						<div className="select-input-container">
						<FilterOutlined className="filter-icon" />
						  <TreeSelect
							{...filter.component_props}
							showCheckedStrategy={
								filter.component_props.showCheckedStrategy
								? TreeSelect[filter.component_props.showCheckedStrategy]
								: undefined
							}
							onChange={(e) =>
								this.props.applyFilterSelect(e, index)
							}
							className="filter-drop"
						  />
						</div>
					  </div>,
					);
					return;
				}

				if (
					filter.optionData &&
					(filter.optionData.length > 1 ||
						(filter.optionData.length > 0 &&
							filter.showSingleOption) || (filter.optionData.length === 0 &&
								filter.showEmptyOption))
				) {
					isLabel = isLabel || filter.label ? true : false;
					filterContainer.push(
						<div className="select-container">
							{filter.label && (
								<label className="select-label">
									{this.props.t? this.props.t(filter.label): filter.label}
								</label>
							)}
							<div
								className={
									'select-input-container' +
									(filter.is_search_type
										? ' select-input-container-search-type'
										: '')
								}
							>
								{filter.is_search_type ? (
									<SearchOutlined className="filter-icon" />
								) : (
									<FilterOutlined className="filter-icon" />
								)}
								<Select
									showArrow={!filter.is_search_type}
									notFoundContent={this.props.t? this.props.t('no_data'): "No Data"}
									showSearch={
										Object.keys(filter).includes(
											'showSearch'
										)
											? filter.showSearch
											: true
									}
									maxTagCount={0}
									maxTagPlaceholder={
										(filter.optionData &&
										filter.optionData.length === 1
											? 1
											: filter.selectValue &&
											  Array.isArray(filter.selectValue)
											? filter.selectValue.length
											: 0) +
										' ' +
										(filter.filterType
											? filterText
													.toLowerCase()
													.split(' ')
													.map(
														(s) =>
															s
																.charAt(0)
																.toUpperCase() +
															s.substring(1)
													)
													.join(' ')
											: '') +
										' Selected'
									}
									mode={
										filter.multiSelect
											? 'multiple'
											: undefined
									}
									placeholder={
										filter.placeholder
											? this.props.t? this.props.t(filter.placeholder): filter.placeholder
											: undefined
									}
									allowClear={
										filter.allowClear ? true : false
									}
									{...(this.props.onPopupScroll && { onPopupScroll: (e) => this.props.onPopupScroll(e, filter.key) })}
									{...(this.props.selectSearch && { onSearch: (e) => this.props.selectSearch(e, filter.key) })}
									{...(this.props.selectOnBlur && { onBlur: () => this.props.selectOnBlur(filter.key) })}
									{...(this.props.selectOnDropdownVisibleChange && { onDropdownVisibleChange: (open) => this.props.selectOnDropdownVisibleChange(open, filter.key) })}
									searchValue={filter.searchValue}
									notFoundContent={this.props.t? this.props.t('no_data'): "No Data"}
									filterOption={filter.group_options?this.optionGroupFilter:filter.filterOption}
									optionFilterProp={filter.optionFilterProp ? filter.optionFilterProp : "children"}
									optionLabelProp={
										filter.optionLabelProp
											? filter.optionLabelProp
											: 'children'
									}
									value={
										// filter.optionData &&
										// filter.optionData.length === 1
										// 	? filter.optionData[0].value
										// 	:
										filter.selectValue || filter.selectValue === 0
											? filter.selectValue
											: undefined
									}
									className={
										'filter-drop ' +
										(filter.className
											? filter.className
											: ' ') +
										(filter.selectValue &&
										!Array.isArray(filter.selectValue) &&
										filter.selectValue !== ''
											? ' selected '
											: '') +
										(filter.selectValue &&
										Array.isArray(filter.selectValue) &&
										filter.selectValue.length
											? ' selected '
											: '') +
										(filter.multiSelect
											? ' multiple-select-drop ' +
											  (filter.filterType
													? filter.filterType
													: '')
											: '')
									}
									onChange={(e) =>
										this.props.applyFilterSelect(e, index)
									}
									disabled={
										filter.disabled ||
										this.props.is_all_disabled
									}
									optionRender={filter.optionRender}
									options={filter.group_options ? filter.optionData : undefined}
								>
									{(() => {
										if(filter.group_options) {
											return null;
										} else if (filter.optionData) {
											let optionList = filter.optionData;
											if (filter.sorted) {
												optionList = _sortBy(
													filter.optionData,
													[
														function (o) {
															return o.title;
														},
													]
												);
											}

											return optionList.map(
												(optionData) => {
													return (
														<Option
															disabled={
																optionData.disabled
															}
															key={
																optionData.value
															}
															value={
																optionData.value
															}
															selectedLabel={
																filter.selectedLabel
															}
															customLabel={optionData.customLabel}
														>
															{this.props.t? this.props.t(optionData.title): optionData.title}
														</Option>
													);
												}
											);
										}
									})()}
								</Select>
							</div>
						</div>
					);
				}

				if (filter.multiSelect) {
					filterViewContainer.push(
						<div className="select-view-container">
							<Select
								mode="tags"
								open={false}
								value={
									// filter.optionData &&
									// filter.optionData.length === 1
									// 	? filter.optionData[0].value
									// 	:
									filter.selectValue
										? filter.selectValue
										: undefined
								}
								notFoundContent={this.props.t? this.props.t('no_data'): "No Data"}
								className={
									'filter-view-drop ' +
									(filter.viewClassName
										? filter.viewClassName
										: '') +
									' ' +
									(filter.filterType ? filter.filterType : '')
								}
								disabled={
									(filter.optionData &&
										filter.optionData.length === 1) ||
									this.props.is_all_disabled
										? true
										: false
								}
								onChange={(e) =>
									this.props.applyFilterSelect(e, index)
								}
								options={filter.group_options ? filter.optionData : undefined}
							>
								{(() => {
									if(filter.group_options) {
										return null;
									} else if (filter.optionData) {
										let optionList = filter.optionData;

										if (filter.sorted) {
											optionList = _sortBy(
												filter.optionData,
												[
													function (o) {
														return o.title;
													},
												]
											);
										}
										return optionList.map((optionData) => {
											return (
												<Option
													key={optionData.value}
													value={optionData.value}
												>
													{optionData.title}
												</Option>
											);
										});
									}
								})()}
							</Select>
						</div>
					);
					if (filter.selectValue && filter.selectValue.length) {
						tagSelectValues = filter.selectValue;
					}
				}
			});
		}

		let searchContainer;

		if (this.props.searchObject) {
			searchContainer = (
				<div
					className={
						(this.props.searchObject.type &&
						this.props.searchObject.type === 'animated'
							? 'search-container-animated '
							: 'search-box ') +
						(this.props.searchObject.className
							? this.props.searchObject.className
							: '') +
						(this.props.searchObject.value &&
						this.props.searchObject.value !== ''
							? ' selected'
							: '')
					}
				>
					<Input
						placeholder={
							this.props.searchObject.placeholder
								? this.props.t? this.props.t(this.props.searchObject.placeholder): this.props.searchObject.placeholder
								: 'Search here'
						}
						size={
							this.props.searchObject.size
								? this.props.searchObject.size
								: 'default'
						}
						enterButton={
							this.props.searchObject.enterButton
								? this.props.searchObject.enterButton
								: false
						}
						allowClear={true}
						disabled={
							this.props.searchObject.disabled ||
							this.props.is_all_disabled ||
							false
						}
						className="search-input"
						value={this.props.searchObject.value}
						onChange={(e) => {
							this.props.onSearch(e.target.value);
						}}
						prefix={
							this.props.searchObject.prefix ? (
								this.props.searchObject.prefix
							) : (
								<SearchOutlined />
							)
						}
					/>
				</div>
			);
		}

		let searchDropdown;

		if (this.props.searchDropdownObject) {
			let optionList = [];

			if (
				this.props.searchDropdownObject.value &&
				this.props.searchDropdownObject.value !== ''
			) {
				if (this.props.searchDropdownObject.dropdownList) {
					this.props.searchDropdownObject.dropdownList.map((data) => {
						if (
							data
								.toLowerCase()
								.indexOf(
									this.props.searchDropdownObject.value.toLowerCase()
								) !== -1
						) {
							optionList.push(data);
						}
					});
				}
			}

			let prefixIcon = <SearchOutlined />;
			if (this.props.searchDropdownObject.prefixIcon) {
				prefixIcon = this.props.searchDropdownObject.prefixIcon;
			}

			searchDropdown = (
				<div
					className={
						'search-dropdown-container ' +
						(this.props.searchDropdownObject.className
							? this.props.searchDropdownObject.className
							: '')
					}
				>
					<div className="prefix-icon">{prefixIcon}</div>
					<AutoComplete
						allowClear
						onChange={(value) => this.props.onSearchDropdown(value)}
						placeholder={
							this.props.searchDropdownObject.placeholder
								? this.props.searchDropdownObject.placeholder
								: 'Search Here'
						}
						value={
							this.props.searchDropdownObject.value
								? this.props.searchDropdownObject.value
								: ''
						}
						disabled={
							this.props.searchDropdownObject.disabled ||
							this.props.is_all_disabled ||
							false
						}
					>
						{(() => {
							return optionList.map((item) => {
								return (
									<AutoComplete.Option
										key={item}
										value={item}
									>
										{item}
									</AutoComplete.Option>
								);
							});
						})()}
					</AutoComplete>
				</div>
			);
		}

		let filterRadio = <div />;
		if (this.props.filterRadioObject) {
			let radio = [],
				defaultValue = '';
			if (this.props.filterRadioObject.optionList) {
				this.props.filterRadioObject.optionList.map(
					(radioData, index) => {
						if (index == 0) {
							defaultValue = radioData.value;
						}

						radio.push(
							<Radio.Button value={radioData.value}>
								{radioData.title}
							</Radio.Button>
						);
					}
				);
			}

			if (
				this.props.filterRadioObject.optionList &&
				this.props.filterRadioObject.optionList.length !== 1
			) {
				filterRadio = (
					<div
						className={
							'filter-radio-container ' +
							(this.props.filterRadioObject &&
							this.props.filterRadioObject.className
								? this.props.filterRadioObject.className
								: '')
						}
					>
						<Radio.Group
							value={
								this.props.filterRadioObject.radioValue &&
								this.props.filterRadioObject.radioValue !== ''
									? this.props.filterRadioObject.radioValue
									: defaultValue
							}
							buttonStyle="solid"
							onChange={(e) =>
								this.props.onRadioChange(e.target.value)
							}
							size="small"
							disabled={this.props.is_all_disabled}
						>
							{radio}
						</Radio.Group>
					</div>
				);
			}
		}

		let clearAll = <div />;
		if (
			((this.props.searchDropdownObject &&
				this.props.searchDropdownObject.value &&
				this.props.searchDropdownObject.value !== '') ||
				(this.props.searchObject &&
					this.props.searchObject.value &&
					this.props.searchObject.value !== '') ||
				isValue) &&
			this.props.isClear &&
			window.innerWidth > 576
		) {
			clearAll = (
				<div
					className={
						'clear-all-container ' +
						(this.props.is_all_disabled ? 'disabled' : '')
					}
					onClick={() => this.clearAllValue()}
				>
					<img
						src={clear_all}
						className="clear-all"
						height="13"
						width="23"
					/>
					<span>
						{this.props.t? this.props.t('clear_all_filters'): "Clear All Filters"}
						{/* Clear All Filters */}
					</span>
				</div>
			);
		}
		return (
			<div
				className={
					'search-filter-component' +
					(this.props.backgroundWhite ? ' sfc-back-fff' : '')
				}
			>
				<div
					className={`filter-with-search-container ${
						isLabel ? 'filter-box-align-end' : ''
					}`}
				>
					<div
						className={`select-filter-wrapper ${
							isLabel ? 'filter-align-end' : ''
						}`}
					>
						{filterContainer}
						{this.props.datePickerConfig ? (
							<div className="select-range-picker-wrapper">
								<RangePicker
									picker={
										this.props.datePickerConfig.picker ||
										'month'
									}
									onChange={
										this.props.datePickerConfig.onChange
									}
									disabled={this.props.is_all_disabled}
								/>
							</div>
						) : (
							''
						)}
					</div>
					<div className="search-wrapper">{searchContainer}</div>
					<div className="search-dropdown-wrapper">
						{searchDropdown}
					</div>
					{filterRadio}
					{clearAll}
				</div>
				{!this.props.hideTagContainer && <div
					className={
						this.props.noSpaceForFilterTag &&
						!tagSelectValues.length
							? 'select-tag-container no-space-tag'
							: 'select-tag-container'
					}
				>
					{filterViewContainer}
				</div>}
			</div>
		);
	}
}
