@import '../../../../../styles/default-color.less';

.search-filter-component {
	width: 100%;

	.filter-with-search-container {
		display: flex;
		flex-wrap: wrap;
		width: 100%;

		.select-range-picker-wrapper{
			height: 36px;
			margin-bottom: 5px;
			.ant-picker{
				background: #f5f6f8 !important;
				width: 250px;
				margin-top: 4px;
				margin-left: 10px;
				border-radius: 10px;


				input::placeholder{
					color: #7686a1 !important;
					//color: red;
				}
			}
		}
		.select-filter-wrapper {
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			&.filter-align-end {
				align-items: flex-end;
			}

			.ant-select-clear {
				color: #000 !important;
				z-index: 2;
			}

			.select-container {
				display: flex;
				align-items: center;
				flex-direction: column;
				margin-bottom: 5px;
				.select-label {
					width: 100%;
					text-align: left;
					padding-left: 10px;
					font-weight: bold;
					color: #232323;
				}
				.select-input-container {
					position: relative;
					.filter-icon {
						left: 20px;
						position: absolute;
						top: 14px;
						z-index: 3;
						color: #7686a1;
					}
					.rangepicker-container >.ant-picker-range {
						margin-top: 4px;
    					margin-left: 10px;
					}
					.ant-select .ant-select-selector .ant-select-selection-overflow {
						flex: none;
					}
					&.select-input-container-search-type {
						.filter-icon {
							left: 24px;
							top: 16px;
							font-size: 12px;
						}

						.filter-drop.ant-select {
							border-radius: 19px !important;
							background: #f6f8fc !important;
							height: 36px;

							.ant-select-selector {
								border-radius: 19px !important;
								padding-left: 36px !important;
								display: flex;
								align-items: center;
								height: 100%;
								// .ant-select-selection-search-input {
								// 	padding-left: 24px !important;
								// }
							}
						}
					}

					.ant-select-single {
						.ant-select-selection-search {
							margin-left: -18px;
						}

						.ant-select-selector {
							.ant-select-selection-placeholder {
								top: unset !important;
							}
						}
					}
				}

				.placeholder-text {
					position: absolute;
					z-index: 1;
					top: 12px;
					left: 40px;
					color: #7686a1;
				}
			}

			.filter-drop {
				background: #f5f6f8 !important;
				width: 200px;
				margin-top: 4px;
				margin-left: 10px;
				border-radius: 10px;
				&.ant-tree-select {
					width: 260px !important;
					@media (min-width: 1024px) and (max-width: 1024px) {
						width: 200px !important;
					}
					.ant-select-selection-overflow-item.ant-select-selection-overflow-item-suffix {
						// display: none;
						.ant-select-selection-search-input {
							padding-left: 0 !important;
						}
					}
					.ant-select-selector .ant-select-selection-placeholder {
						left: -10px !important;
					}
				}
				&.selected {
					.ant-select-selector {
						border-color: @defaultColor !important;
					}
				}

				.ant-select-clear {
					background: transparent;
				}

				&:hover {
					.ant-select-selector {
						box-shadow: 0 0 0 2px @hoverColor !important;
					}
				}

				.ant-select-selector {
					background: transparent !important;
					border: 1px solid #f5f6f8;
					padding-left: 30px !important;
					transition: all 0.3s;

					.ant-select-selection-item {
						font-weight: 600 !important;
					}

					.ant-select-selection-search-input {
						padding-left: 18px;
					}

					.ant-select-selection-placeholder {
						color: #7686a1 !important;
						opacity: 1 !important;
						right: unset !important;
						position: relative !important;
						top: 3px !important;
						left: unset !important;
						transform: unset !important;
					}
				}
			}

			.multiple-select-drop {
				width: 250px !important;

				.ant-select-selector {
					padding-left: 24px !important;

					.ant-select-selection-search-input {
						padding-left: 0 !important;
					}

					.ant-select-selection-item {
						border-radius: 13px;
						margin-left: 2px;
						font-size: 10px !important;
						background: @defaultColor !important;
						color: #fff;
					}

					.ant-select-selection-item-content {
						font-size: 10px !important;
					}
				}

				&.application {
					.ant-select-selector {
						.ant-select-selection-item {
							background: #374375 !important;
						}
					}
				}

				&.partner {
					.ant-select-selector {
						.ant-select-selection-item {
							background: #9075bc !important;
						}
					}
				}

				&.customer {
					.ant-select-selector {
						.ant-select-selection-item {
							background: #f68f8f !important;
						}
					}
				}

				&.device-type {
					.ant-select-selector {
						.ant-select-selection-item {
							background: #6c8d0f !important;
						}
					}
				}

				&.thing-type {
					.ant-select-selector {
						.ant-select-selection-item {
							background: #439dfb !important;
						}
					}
				}

				&.status {
					.ant-select-selector {
						.ant-select-selection-item {
							background: #916238 !important;
						}
					}
				}
			}

			.ant-input-affix-wrapper {
				.ant-input {
					width: 400px !important;
					height: 31px;
					border-radius: 5px;
					box-shadow: none;
					border: 1px solid #f5f6f8;

					&:focus,
					&:hover {
						box-shadow: 0 0 0 2px @hoverColor !important;
						border: 1px solid #f5f6f8;
					}
				}
			}
		}

		.search-wrapper {
			.search-box {
				width: auto;

				.ant-input-affix-wrapper {
					width: auto !important;
					background-color: transparent !important;
					border: none;
					box-shadow: none;
					position: relative;

					.ant-input-prefix {
						position: absolute;
						z-index: 2;
						top: 14px;
						left: 20px;
						color: #7686a1;
					}

					.ant-input-suffix {
						position: absolute;
						right: 20px;
						top: 10px;
						z-index: 2;

						.ant-input-clear-icon {
							margin-top: 35%;
						}
					}

					.ant-input {
						background: #f5f6f8 !important;
						width: 200px !important;
						transition: all 0.5s;
						padding: 0;
						padding-left: 30px;
						font-size: 12px;
						line-height: 10px;
						height: 31px;
						border-radius: 10px;
						border: 1px solid transparent;
						box-shadow: none !important;

						@media (min-width: 1024px) and (max-width: 1024px) {
							width: 160px !important;
						}

						&::placeholder {
							color: #7686a1 !important;
							opacity: 1 !important;
						}

						&:hover,
						&:focus {
							box-shadow: 0 0 0 2px @hoverColor !important;
							border: 1px solid #f5f6f8;
						}
					}
				}

				&.selected {
					.ant-input-affix-wrapper {
						.ant-input {
							border: 1px solid @defaultColor !important;
						}
					}
				}
			}

			.search-container-animated {
				width: auto;
				margin-top: 20px;

				.ant-input-affix-wrapper {
					width: auto !important;
					border: none;
					box-shadow: none;
					position: relative;

					.ant-input-prefix {
						position: absolute;
						z-index: 2;
						top: 11px;
						left: 20px;
					}

					.ant-input-suffix {
						position: absolute;
						right: 20px;
						top: 11px;
						z-index: 2;
					}

					.ant-input {
						background-color: #fff !important;
						width: 150px !important;
						transition: all 0.5s;
						padding: 0;
						padding-left: 30px;
						font-size: 12px;
						line-height: 10px;
						height: 28px;
						border-radius: 10px;
						border: 1px solid transparent;
						box-shadow: 9px 9px 16px rgb(230, 230, 230);

						&:focus,
						&:active {
							width: 300px !important;
							background-color: #fff !important;
							border: 1px solid @defaultColor;
							box-shadow: 9px 9px 16px rgb(230, 230, 230);
						}

						&:hover {
							border: 1px solid @defaultColor;
							box-shadow: 9px 9px 16px rgb(230, 230, 230);
						}
					}
				}

				&.selected {
					.ant-input-affix-wrapper {
						.ant-input {
							border: 1px solid @defaultColor !important;
						}
					}
				}
			}
		}

		.search-dropdown-wrapper {
			.search-dropdown-container {
				display: flex;
				align-items: center;
				margin-top: 4px;

				.prefix-icon {
					margin-left: 9px;
					position: absolute;
					z-index: 1;
					color: #7686a1 !important;
				}

				.ant-select-auto-complete {
					width: 250px !important;

					&.selected {
						.ant-select-selector {
							border-color: @defaultColor !important;
						}
					}

					.ant-select-clear {
						background: transparent;
					}

					&.ant-select-focused {
						.ant-select-selector {
							box-shadow: 0 0 0 2px @hoverColor !important;
						}
					}

					&:hover,
					&:focus {
						.ant-select-selector {
							box-shadow: 0 0 0 2px @hoverColor !important;
						}
					}

					.ant-select-selector {
						background: #f5f6f8 !important;
						border: 1px solid #f5f6f8;
						padding-left: 30px !important;
						transition: all 0.3s;

						.ant-select-selection-item {
							font-weight: 600 !important;
						}

						.ant-select-selection-placeholder {
							color: #7686a1 !important;
							right: unset !important;
							position: relative !important;
							top: unset !important;
							left: unset !important;
							transform: unset !important;
							font-size: 12px;
						}
					}

					.ant-select-selector {
						padding-left: 30px !important;

						.ant-select-selection-search {
							left: 30px !important;
						}
					}
				}
			}
		}

		.filter-radio-container {
			margin-left: 10px;
			margin-top: 4px;
			background: #f5f6f8 !important;
			padding: 3px;
			border-radius: 6px;
			height: 30px;

			.ant-radio-group-solid {
				.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
					color: @defaultColor !important;
					box-shadow: 4px 0px 10px #0000001a !important;
					border-radius: 6px !important;
					background: #fff !important;
					border: none !important;
					margin-right: 15px !important;
					padding: 0 20px !important;
					white-space: nowrap;
				}
			}

			.ant-radio-group {
				.ant-radio-button-wrapper {
					background: #f5f6f8 !important;
					border: none !important;
					margin-right: 15px !important;
					padding: 0 20px !important;
					box-shadow: none !important;
					border-radius: 6px !important;
					white-space: nowrap;
					&::before {
						display: none !important;
					}

					&:last-child {
						margin-right: 0 !important;
					}

					&:hover {
						color: @defaultColor !important;
					}

					.ant-radio-button-checked {
						color: @defaultColor !important;
					}
				}
			}
		}

		.clear-all-container {
			margin-top: 8px;
			margin-left: 15px;
			display: flex;
			align-items: center;
			height: fit-content;
			cursor: pointer;

			img {
				margin-right: 10px;
			}

			span {
				color: @defaultColor;
			}

			&.disabled {
				cursor: not-allowed !important;
			}
		}
		&.filter-box-align-end {
			align-items: flex-end;
			.search-wrapper {
				padding-bottom: 2px;
			}
			.search-dropdown-wrapper {
				padding-bottom: 5px;
			}
			.filter-radio-container {
				margin-bottom: 5px;
			}
			.clear-all-container {
				margin-bottom: 10px;
			}
		}
	}

	.select-tag-container {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		&.no-space-tag {
			display: none;
		}
		.select-view-container {
			min-height: 22px;
			margin-bottom: 10px;
			width: auto;

			.filter-view-drop {
				.ant-select-arrow {
					display: none;
				}
				.ant-select-selector {
					border: none !important;
					box-shadow: none !important;
					cursor: default !important;
					background: transparent !important;
					padding: 1px 4px;

					.ant-select-selection-item {
						border-radius: 13px !important;
						background: @defaultColor;
						color: #fff;
						display: flex;
						align-items: center;

						.ant-select-selection-item-content {
							white-space: unset;
						}

						.ant-select-selection-item-remove {
							height: 15px !important;
							width: 15px !important;
							margin: 5px 0;
							border-radius: 50% !important;
							background: #fff !important;
							justify-content: center;
							align-items: center;

							.anticon {
								vertical-align: 2px !important;
								margin-left: 1px !important;
							}
						}
					}

					.ant-select-selection-search {
						display: none !important;
					}
				}

				&.application {
					.ant-select-selector {
						.ant-select-selection-item {
							background: #374375 !important;
						}
					}
				}

				&.partner {
					.ant-select-selector {
						.ant-select-selection-item {
							background: #9075bc !important;
						}
					}
				}

				&.customer {
					.ant-select-selector {
						.ant-select-selection-item {
							background: #f68f8f !important;
						}
					}
				}

				&.device-type {
					.ant-select-selector {
						.ant-select-selection-item {
							background: #6c8d0f !important;
						}
					}
				}

				&.thing-type {
					.ant-select-selector {
						.ant-select-selection-item {
							background: #439dfb !important;
						}
					}
				}

				&.status {
					.ant-select-selector {
						.ant-select-selection-item {
							background: #916238 !important;
						}
					}
				}
			}
		}
	}
	&.sfc-back-fff{
		.filter-with-search-container .select-filter-wrapper .filter-drop .ant-select-selector {
			background: #fff !important;
		}
	}
}

@media (max-width: 576px) {
	.search-filter-component .filter-with-search-container {
		.select-filter-wrapper .select-container .select-input-container {
			&.select-input-container-search-type {
				.filter-icon {
					left: 16px;
				}

				.filter-drop.ant-select {
					margin-left: 0 !important;

					.ant-select-selector {
						.ant-select-selection-placeholder {
							font-size: 12px;
						}
					}
				}
			}
		}

		.search-wrapper {
			width: 100%;

			.search-box .ant-input-affix-wrapper {
				width: 100% !important;

				.ant-input-prefix {
					left: 24px;
					top: 16px;
				}
				.ant-input {
					width: 100% !important;
					border-radius: 19px !important;
					background: #f6f8fc !important;
					padding-left: 36px !important;
					height: 36px;
				}
			}
		}
	}
}

@media screen and (max-width: 768px) {
	.search-filter-component .filter-with-search-container .search-wrapper .search-box .ant-input-affix-wrapper .ant-input-suffix .ant-input-clear-icon {
		margin-top: 45%;
	}

	.industry-workflow .workflow-filters .ant-select .ant-select-selector .ant-select-selection-search-input {
		padding-left: unset !important;
	}
}
