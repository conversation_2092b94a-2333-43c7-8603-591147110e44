/* Libs */
import React from 'react';
import HighchartsReact from 'highcharts-react-official';
import Highcharts from 'highcharts';
import Boost from 'highcharts/modules/boost';
import BoostCanvas from 'highcharts/modules/boost-canvas';
import HighchartsExporting from 'highcharts/modules/exporting';
import moment from 'moment-timezone';
import ReactResizeDetector from '@datoms/react-components/src/components/ReactResizeDetector';
// import ReactResizeDetector from 'react-resize-detector';
import { array, bool } from 'prop-types';
import { ErrorBoundary } from 'react-error-boundary';
/* Own Libs */

/* Styles */
import './style.less';

/* Configs */
import defaultConfigs from './defaultConfigs';

if (typeof Highcharts === 'object') {
	BoostCanvas(Highcharts);
	Boost(Highcharts);
	HighchartsExporting(Highcharts);
}

function ErrorFallback({ error, componentStack, resetErrorBoundary }) {
	return <div role="alert"></div>;
}

/**
 * GraphHighCharts.
 *
 * When To Use:
 *
 * 1. To analyze/plot the data in a graph.
 **/
export default class GraphHighcharts extends React.Component {
	// initializing defaultprops, so that it will provide default configs
	static defaultProps = {
		...defaultConfigs,
	};

	static propTypes = {
		/** Should re-rendering of the component be prevented via `shouldComponentUpdate`. The value specified during mounting the component is considered as final & can't be changed further until the component stays mounted. */
		optimizeWithShouldComponentUpdate: bool,

		/** Contains the data to draw the graph. */
		graphData: array.isRequired,
	};

	constructor(props) {
		super(props);
		this.state = {
			// To avoid unnecessary update keep all options in the state.
			chartOptions: {
				time: {
					getTimezoneOffset: function (timestamp) {
						var zone = props.graphData.config.timezone,
							timezoneOffset = -moment
								.tz(timestamp, zone)
								.utcOffset();

						return timezoneOffset;
					},
				},
				isPureConfig: true,
				chart:
					props.graphData && props.graphData.config.chart
						? props.graphData.config.chart
						: {},
				title:
					props.graphData && props.graphData.config.title
						? props.graphData.config.title
						: {},
				subtitle:
					props.graphData && props.graphData.config.subtitle
						? props.graphData.config.subtitle
						: {},
				xAxis:
					props.graphData && props.graphData.config.xAxis
						? {
							...props.graphData.config.xAxis,
							title: {
								...props.graphData.config.xAxis.title,
								text: props.t
								  ? props.t(props.graphData.config.xAxis.title?.text)
								  : props.graphData.config.xAxis.title?.text
							},
						}
					: {},
				yAxis:
					props.graphData && props.graphData.config.yAxis
						? props.graphData.config.yAxis
						: {},
				legend:
					props.graphData && props.graphData.config.legend
						? props.graphData.config.legend
						: {},
				tooltip:
					props.graphData && props.graphData.config.tooltip
						? props.graphData.config.tooltip
						: {},
				credits:
					props.graphData && props.graphData.config.credits
						? props.graphData.config.credits
						: { enabled: false },
				exporting:
					props.graphData && props.graphData.config.exporting
						? props.graphData.config.exporting
						: {},
				plotOptions:
					props.graphData && props.graphData.config.plotOptions
						? props.graphData.config.plotOptions
						: {},
				responsive:
					props.graphData && props.graphData.config.responsive
						? props.graphData.config.responsive
						: {},
				boost: {
					allowForce: true,
					enabled: true,
					useGPUTranslations: true,
					debug: {
						showSkipSummary: true,
					},
				},
				series:
					props.graphData && props.graphData.series_data
						? props.graphData.series_data
						: {},
			},
		};

		this.optimizeWithShouldComponentUpdate =
			props.optimizeWithShouldComponentUpdate;
	}

	componentDidMount() {
		this.chartRef = React.createRef();
	}

	componentDidUpdate(prevProps) {
		if (this.props.graphData.series_data.length) {
			if (
				this.props.graphData.series_data !==
				this.state.chartOptions.series
			) {
				this.setState({
					chartOptions: {
						series: this.props.graphData.series_data,
					},
				});
			}
		}
	}

	shouldComponentUpdate(nextProps, nextState) {
		if (
			this.props.graphData.series_data !==
				nextProps.graphData.series_data ||
			this.state.chartOptions.series !== nextState.chartOptions.series
		) {
			return true;
		}
		return false;
	}

	render() {
		console.log("TEST", this.props);
		
		const { chartOptions } = this.state;
		if (
			this.props.graphData &&
			this.props.graphData.series_data &&
			this.props.graphData.series_data.length
		) {
			return (
				<div className="graph-container">
					<ErrorBoundary
						FallbackComponent={ErrorFallback}
						onReset={() => {
							// reset the state of your app so the error doesn't happen again
						}}
					>
						<ReactResizeDetector handleWidth handleHeight>
							<HighchartsReact
								t={this.props.t}
								highcharts={Highcharts}
								options={chartOptions}
								allowChartUpdate={true}
								immutable={false}
								containerProps={{
									className: 'chart-container',
								}}
								ref={
									this.props.ref
										? this.props.ref
										: this.chartRef
								}
							/>
						</ReactResizeDetector>
					</ErrorBoundary>
				</div>
			);
		} else {
			return (
				<div className="graph-container-no-data">
					{this.props.t? this.props.t('no_chart_data_to_show'): 'No Chart Data to Show'}
				</div>
			);
		}
	}
}
