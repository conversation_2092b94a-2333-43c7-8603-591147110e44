.search-container {
	width: auto;
	margin-top: 20px;

	.ant-input-affix-wrapper {
		width: auto !important;
		background-color: transparent !important;
		border: none;
		box-shadow: none;
		position: relative;

		.ant-input-prefix {
			position: absolute;
			z-index: 2;
			top: 11px;
			left: 20px;
		}

		.ant-input-suffix {
			position: absolute;
			right: 20px;
			top: 8px;
			z-index: 2;
			.ant-input-clear-icon {
				margin-top: 35%;
			}	
		}

		.ant-input {
			background-color: #fff !important;
			width: 250px !important;
			transition: all 0.5s;
			padding: 0;
			padding-left: 30px;
			font-size: 12px;
			line-height: 10px;
			height: 28px;
			border-radius: 10px;
			border: 1px solid transparent;
			box-shadow: 9px 9px 16px rgb(230, 230, 230);

			&:hover,
			&:focus {
				// border: 1px solid #f58740;
				box-shadow: 9px 9px 16px rgb(230, 230, 230);
			}
		}
	}

	&.selected {
		.ant-input-affix-wrapper {
			.ant-input {
				border: 1px solid #f58740 !important;
			}
		}
	}
}

.search-container-animated {
	width: auto;
	margin-top: 20px;

	.ant-input-affix-wrapper {
		width: auto !important;
		border: none;
		box-shadow: none;
		position: relative;

		.ant-input-prefix {
			position: absolute;
			z-index: 2;
			top: 11px;
			left: 20px;
		}

		.ant-input-suffix {
			position: absolute;
			right: 20px;
			top: 11px;
			z-index: 2;
		}

		.ant-input {
			background-color: #fff !important;
			width: 150px !important;
			transition: all 0.5s;
			padding: 0;
			padding-left: 30px;
			font-size: 12px;
			line-height: 10px;
			height: 28px;
			border-radius: 10px;
			border: 1px solid transparent;
			//box-shadow: 9px 9px 16px rgb(230, 230, 230);

			&:focus,
			&:active {
				width: 300px !important;
				background-color: #fff !important;
				border: 1px solid #f58740;
				//box-shadow: 9px 9px 16px rgb(230, 230, 230);
			}

			&:hover {
				border: 1px solid #f58740;
				//box-shadow: 9px 9px 16px rgb(230, 230, 230);
			}
		}
	}

	&.selected {
		.ant-input-affix-wrapper {
			.ant-input {
				border: 1px solid #f58740 !important;
			}
		}
	}
}

.simple-search-container {
	margin: 10px auto;
	width: auto;
	height: 32px;
	.ant-input-affix-wrapper {
		border: none;
		height: 100%;
		border-radius: 20px;
		padding-left: 20px;
		background: #f6f8fc;

		input {
			background: #f6f8fc;
			margin-left: 7px;
		}
		input::placeholder {
			color: #808080;
			font-size: 12px;
			position: relative;
			top: -1px;
		}
	}
}
