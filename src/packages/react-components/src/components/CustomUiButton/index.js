/* Libs */
import React from 'react';
import AntSpin from '../AntSpin';
import { LoadingOutlined } from '@ant-design/icons';
import { bool, string, func, node } from 'prop-types';

/*Components */
import AntDropdown from '../AntDropdown';

/* Own Libs */
import IsComponentUpdateRequired from '../../libs/IsComponentUpdateRequired';

/* Styles */
import './style.less';

/* Configs */
import defaultConfigs from '../Text/defaultConfigs';

/**
 * CustomUiButton
 *
 * When To Use:
 *
 * 1. To display button name on hovering icon and is clickable.
 **/

export default class CustomUiButton extends React.Component {
	static defaultProps = {
		...defaultConfigs,
	};

	static propTypes = {
		/** Should re-rendering of the component be prevented via `shouldComponentUpdate`. The value specified during mounting the component is considered as final & can't be changed further until the component stays mounted. */
		optimizeWithShouldComponentUpdate: bool,
		button_text: string.isRequired,
		custom_icon: node.isRequired,
		is_disabled: bool,
		onCustomUiButtonClick: func.isRequired,
	};

	constructor(props) {
		super(props);
		this.state = {};

		this.optimizeWithShouldComponentUpdate =
			props.optimizeWithShouldComponentUpdate;
	}

	shouldComponentUpdate(nextProps, nextState) {
		return IsComponentUpdateRequired(
			this.optimizeWithShouldComponentUpdate,
			this.props,
			this.state,
			nextProps,
			nextState
		);
	}

	render() {
		const { isMultiple, overlay, placement, is_disabled } = this.props;
		if (isMultiple && !is_disabled) {
			return (
				<AntDropdown
					overlay={overlay}
					overlayClassName="custom-ui-btn-dropdown"
					placement={placement ? placement : 'bottomLeft'}
				>
					<div className="btn-outer-container add-btn-multi">
						<div
							className="btn-inner-container"
						>
							<span className="new-custom-button">
							{this.props.loading? <AntSpin indicator={<LoadingOutlined spin style={{color: '#fff'}}/>} />:this.props.custom_icon}
							</span>
						</div>
					</div>
				</AntDropdown>
			);
		}
		return (
			<div
				className={
					'btn-outer-container' +
					(this.props.is_disabled !== undefined &&
					this.props.is_disabled === true
						? ' btn-outer-container-disable'
						: '')
				}
			>
				<div
					className="btn-inner-container"
					onClick={() => this.props.onCustomUiButtonClick()}
				>
					{/* <span className="width-control-container"> */}
					<span className="new-custom-text">
						{this.props.button_text}
					</span>
					<span className="new-custom-button">
						{this.props.loading? <AntSpin indicator={<LoadingOutlined spin style={{color: '#fff'}}/>} />:this.props.custom_icon}
					</span>
					{/* </span> */}
				</div>
			</div>
		);
	}
}
