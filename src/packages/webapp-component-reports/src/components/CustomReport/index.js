/*Libs*/
import React from 'react';
import CustomReportsPage from './Imports/CustomReportsPage';
import DataViewReport from './Imports/DataViewReport';
import _filter from 'lodash/filter';
import _find from 'lodash/find';
import { retrieveSitesList, getSiteTypes, retriveThingsList, retriveApplicationThings } from '@datoms/js-sdk';
import Loading from '@datoms/react-components/src/components/Loading';
import AntTabs from '@datoms/react-components/src/components/AntTabs';
import AntTabPane from '@datoms/react-components/src/components/AntTabPane';
/* Styles */
import './style.less';

/*importing default config*/
import defaultConfigs from './defaultConfigs';
import headerConfig from './Imports/header/headerConfig';

import { number, string } from 'prop-types';
import { findDownload } from '../../../../dg-monitoring-views/src/js/features/Reports/data_handling/FindDownloadInUrl';

export default class CustomReport extends React.Component {
	static defaultProps = {
		...defaultConfigs,
	};
	static propTypes = {
		client_id: number,
		application_id: number,
		client_name: string,
		application_name: string,
		timeZone: string,
	};

	constructor(props) {
		super(props);
		this.state = {
			headerConfig: headerConfig,
			tabKey: headerConfig.header_options[0].key,
			reportView: false,
			loading: true,
			isDownloadInUrl: findDownload(props?.history),
		};
		this.reportGenerate = this.reportGenerate.bind(this);
		this.tabChange = this.tabChange.bind(this);
	}

	async getInputForDownload() {
		const { isDownloadInUrl } = this.state;
		// let data = {
		// 	tpara: ['vbat'],
		// 	thing_wise_param_selection: {
		// 		5075: ['vbat'],
		// 		5076: ['vbat'],
		// 	},
		// 	table_type: ['summary_table', 'detailed_table'],
		// 	pcategory: 18,
		// 	device_id_list: [5076, 5075],
		// 	dtype: 'average',
		// 	param_type_radio: 'total', //"thing_wise"
		// 	tinterval: 3600,
		// 	graph_view: 'different', //"one",
		// 	from_time: 1692136800,
		// 	upto_time: 1692779588,
		// 	r_view: ['grid', 'graph'],
		// 	type: 'pdf',
		// };
		// let inputDataSet = data;
		if (typeof window.getInputDataSet === 'function') {
			let inputDataSet = await window.getInputDataSet();
			if (isDownloadInUrl) {
				this.setState({
					inputDataSet: inputDataSet,
					reportView: true,
					options: inputDataSet,
				});
			}
		}
	}

	filterDginIot(totalData) {
		let finalData = totalData;
		if (
			this.props.dg_in_iot_mode &&
			totalData.things &&
			totalData.things.length > 0
		) {
			const { filter_client_id } = this.props;
			finalData['things'] = finalData.things.filter((thing) => {
				if (filter_client_id) {
					return thing.current_assigned_customer === filter_client_id;
				}
				return true;
			});
		}
		return finalData;
	}

	async siteList() {
		const {headerConfig, tabKey} = this.state;
		const { client_id } = this.props;
		const siteList = await retrieveSitesList(client_id, `?page_no=${1}&results_per_page=${1000}`)
		const headerConfigCopy = JSON.parse(JSON.stringify(headerConfig));
		if(siteList?.data?.length > 0 && _find(headerConfigCopy.header_options, {key: 'site_wise'}) === undefined) {
			headerConfigCopy.header_options.push({
				key: 'site_wise',
				name: 'Site Wise',
			})
		}
		this.setState({
			siteList: siteList?.data || [],
			headerConfig: headerConfigCopy
		}, async () => {
			if(siteList?.data?.length > 0) {
				await Promise.all([this.siteType(), this.gettingThingsList()]).then(() => {
					this.setState({
						loading: false
					})
				});
			} else {
				await this.gettingThingsList().then(() => {
					this.setState({
						loading: false
					})
				});
			}
		});
	
	}

	async siteType() {
		const { client_id } = this.props;
		const siteTypes = await getSiteTypes(client_id)
		this.setState({
			siteTypes: siteTypes?.data,
		});
	}

	async gettingThingsList() {
		const { client_id, application_id, report_disabled_cats, vendor_id, user_preferences } =
			this.props;
		let totalThingsData = await retriveThingsList({
			client_id: client_id,
			application_id: application_id,
		});
		let filterThingCats = _filter(totalThingsData.things, function (o) {
			return !report_disabled_cats?.includes(o.category);
		});
		let applicationThings = await retriveApplicationThings(application_id);
		totalThingsData.things = filterThingCats;
		totalThingsData = this.filterDginIot(totalThingsData);
		let isAurassure =
			totalThingsData?.things[0]?.category === 42 || vendor_id === 1819;
		const getAllowedViz = user_preferences?.allowed_visualization || [];
		this.setState({
			getAllowedViz,
			totalThingsData: totalThingsData,
			isAurassure: isAurassure,
			applicationThings,
		});
	}

	async componentDidMount() {
		await this.siteList();
	}

	reportGenerate(options) {
		this.setState({
			options: options,
			reportView: true,
		});
	}

	tabChange(key) {
		this.setState({
			tabKey: key,
		});
	}

	render() {
		let pgx = '';
		if (this.state.loading) {
			pgx = <Loading show_logo={this.props.loading_logo} />;
		} else {
			if (this.props.type === 'custom') {
				if (this.state.reportView) {
					if (this.props.data_api.includes('data')) {
						pgx = (
							<DataViewReport
								t={this.props.t}
								isScheduledReport={this.props.isScheduledReport}
								inputDataSet={this.state.inputDataSet}
								dg_in_iot_mode={this.props.dg_in_iot_mode}
								plan_description={this.props.plan_description}
								logged_in_user_client_id={
									this.props.logged_in_user_client_id
								}
								parent_client_id={this.props.parent_client_id}
								parent_application_id={
									this.props.parent_application_id
								}
								type={this.props.type}
								data_api={this.props.data_api}
								isAurassure={this.state.isAurassure}
								timeZone={this.props.timeZone}
								timeFormat={this.props.timeFormat}
								options={this.state.options}
								totalThingsData={this.state.totalThingsData} //source
								siteTypes={this.state.siteTypes}
								client_id={this.props.client_id}
								application_id={this.props.application_id}
								client_name={this.props.client_name}
								application_name={this.props.application_name}
								reportGenerate={this.reportGenerate}
								history={this.props.history}
								location={this.props.location}
								machine_info={this.props.machine_info}
								is_white_label={this.props.is_white_label}
								vendor_name={this.props.vendor_name}
								vendor_id={this.props.vendor_id}
								vendor_logo={this.props.vendor_logo}
								loading_logo={this.props.loading_logo}
								no_application_name={
									this.props.no_application_name
								}
								reportViewFalse={() =>
									this.setState({ reportView: false })
								}
								applicationThings={
									this.state
										.applicationThings
								}
								tabKey={this.state.tabKey}
								siteList={this.state.siteList}
								getViewAccess={this.props.getViewAccess}
								getAllowedViz={this.state.getAllowedViz}
							/>
						);
					}
				} else {
					let tabArray = [];
					if (
						this.state.headerConfig &&
						Array.isArray(this.state.headerConfig.header_options)
					) {
						this.state.headerConfig.header_options.map(
							(options) => {
								tabArray.push(
									<AntTabPane
										tab={this.props.t(options.name)}
										key={options.key}
									>
										<CustomReportsPage
											t={this.props.t}
											tabKey={options.key}
											plan_description={
												this.props.plan_description
											}
											type={this.props.type}
											data_api={this.props.data_api}
											templateReport={
												this.props.templateReport
											}
											client_name={this.props.client_name}
											application_name={
												this.props.application_name
											}
											applicationThings={
												this.state
													.applicationThings
											}
											goBackPage={this.props.goBackPage}
											reportGenerate={this.reportGenerate}
											totalThingsData={
												this.state.totalThingsData
											}
											siteTypes={this.state.siteTypes}
											client_id={this.props.client_id}
											application_id={
												this.props.application_id
											}
											timeFormat={this.props.timeFormat}
											timeZone={this.props.timeZone}
											history={this.props.history}
											location={this.props.location}
											siteList={this.state.siteList}
											getAllowedViz={this.state.getAllowedViz}
											vendor_id={this.props.vendor_id}
											logged_in_user_client_id={this.props.logged_in_user_client_id}
										/>
									</AntTabPane>
								);
							}
						);
					}

					let imageToBeShown = {
						left: (
							<img
								className="header-image"
								src={this.state.headerConfig.limg}
							/>
						),
						right: (
							<img
								className="header-image"
								src={this.state.headerConfig.rimg}
							/>
						),
					};
					pgx = (
						<AntTabs
							className="header-tab"
							centered
							tabBarExtraContent={imageToBeShown}
							defaultValue={this.props.tab_state}
							onChange={(key) => this.tabChange(key)}
						>
							{tabArray}
						</AntTabs>
					);
				}
			}
		}
		return <div id="custom_report">{pgx}</div>;
	}
}
