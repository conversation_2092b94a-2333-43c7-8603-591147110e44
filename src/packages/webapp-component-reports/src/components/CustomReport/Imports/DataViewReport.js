import React from "react";
import _find from "lodash/find";
import _uniq from "lodash/uniq";
import DownloadModal from "./DownloadModal";
import CustomModal from "./CustomModal";
import Loading from "@datoms/react-components/src/components/Loading";
import ReportController from "@datoms/react-components/src/components/ReportController";
import AntSwitch from "@datoms/react-components/src/components/AntSwitch";
import AntTabs from "@datoms/react-components/src/components/AntTabs";
import AntTabPane from "@datoms/react-components/src/components/AntTabPane";
import AntDivider from "@datoms/react-components/src/components/AntDivider";
import ArrowLeftOutlined from "@ant-design/icons/ArrowLeftOutlined";
import SettingOutlined from "@ant-design/icons/SettingOutlined";
import DownloadOutlined from "@ant-design/icons/DownloadOutlined";
import ClockCircleOutlined from "@ant-design/icons/ClockCircleOutlined";
import moment from "moment-timezone";
import { TimeFormatter } from "../../../../../dg-monitoring-views/src/js/data_handling/TimeFormatting";
import ScheduleDrawer from "../../../../../dg-monitoring-views/src/js/features/Reports/components/ScheduleDrawer";
import { getDownloadFileName } from "../../../../../dg-monitoring-views/src/js/features/Reports/data_handling/downloadFilename";
import { getThingsDataFunc, callDataApi } from "../logic/renderDownloadData";
import SkeletonLoader from "@datoms/react-components/src/components/SkeletonLoader";
import AntSpin from "@datoms/react-components/src/components/AntSpin";
import memoizeOne from "memoize-one";

export default class DataViewReport extends React.Component {
  downloadModalRef = React.createRef();
  invisibleReportRef = React.createRef();
  customModalRef = React.createRef();
  scheduleDrawerRef = React.createRef();
  constructor(props) {
    super(props);
    this.state = {
      scheduleDrawerVisible: false,
      loading: true,
      downloaderReportConfig: undefined,
      disabled: props.options.r_view.length === 1 ? true : false,
      selected_thing: props.options.device_id_list[0],
      options: props.options,
      analysisFormatChecked: false,
      inclBlankRows: false,
      isAvg: props.options.dtype === "average",
      isSummaryTab: props.options.dataOfAllAssets,
    };
    this.downloadModalCallback = this.downloadModalCallback.bind(this);
    this.onExportReady = this.onExportReady.bind(this);
    this.getThingsDataFunc = getThingsDataFunc.bind(this);
    this.callDataApi = callDataApi.bind(this);
  }
  isGmmco() {
    if (parseInt(this.props.vendor_id) === 1062) {
      return true;
    } else {
      return false;
    }
  }
  scheduleBtnClicked() {
    this.setState(
      {
        scheduleDrawerVisible: true,
      },
      () => {
        this.scheduleDrawerRef.current.showScheduleDrawer();
      },
    );
  }

  scheduleDrawerClosed() {
    this.setState({
      scheduleDrawerVisible: false,
    });
  }

  getSeletedThingDetails() {
    const { thingArr } = this.state;
    const { totalThingsData } = this.props;
    let seletedThingDetails = [];
    if (Array.isArray(thingArr)) {
      thingArr.map(function (things) {
        if (totalThingsData && Array.isArray(totalThingsData.things)) {
          seletedThingDetails.push(
            _find(totalThingsData.things, {
              id: things,
            }),
          );
        }
      });
    }
    return seletedThingDetails;
  }

  customizeOnOkRender(customOptions) {
    this.setState(
      {
        loading: true,
        options: customOptions,
        selected_thing: customOptions?.device_id_list[0],
        isAvg: customOptions.dtype === "average",
        disabled: customOptions.r_view.length === 1 ? true : false,
        isSummaryTab: customOptions?.dataOfAllAssets,
      },
      async () => {
        await this.callDataApi();
      },
    );
  }

  customiseReport() {
    this.customModalRef.current.showModal();
  }

  async componentDidMount() {
    await this.callDataApi();
  }

  renderReportView(data) {
    console.log("REPORT_DATA", data);
    const { inputDataSet } = this.props;
    if (inputDataSet) {
      return;
    }
    return this.state.dataViewType ? (
      <ReportController
        t={this.props.t}
        is_white_label={this.props.is_white_label}
        isAurassure={this.props.isAurassure}
        vendor_name={this.props.vendor_name}
        key={moment().unix()}
        vizType="graph"
        {...data.graph}
      />
    ) : (
      <ReportController
        t={this.props.t}
        is_white_label={this.props.is_white_label}
        isAurassure={this.props.isAurassure}
        vendor_name={this.props.vendor_name}
        key={moment().unix()}
        {...data.grid}
      />
    );
  }

  inclBlankRowsFunc() {
    const { inclBlankRows } = this.state;
    this.setState(
      {
        inclBlankRows: !inclBlankRows,
        pageLoading: true,
      },
      () => {
        this.getThingsDataFunc();
      },
    );
  }

  g2gOnChange(e) {
    this.setState({
      dataViewType: e,
    });
  }

  downloadBtnClicked() {
    this.downloadModalRef.current.showModal();
  }

  modalDownloadBtnClicked(e) {
    this.setState({ downloadLoading: true, fileFormat: e }, async () => {
      await this.callDataApi(true);
    });
  }

  makeAnalysisFormatFalse() {
    this.setState(
      {
        analysisFormatChecked: false,
      },
      () => {
        this.getThingsDataFunc();
      },
    );
  }

  downloadModalCallback(fileFormat) {
    if (this.state.downloaderReportConfig) {
      if (fileFormat.includes("csv")) {
        this.invisibleReportRef.current.exportCSV(null, {});
      }

      if (fileFormat.includes("xls")) {
        this.invisibleReportRef.current.exportXLSX(null, {
          maxCellMergeCount: 20,
        });
      }

      if (fileFormat.includes("pdf")) {
        this.invisibleReportRef.current.exportPDF({
          header: {
            left: {
              text: "",
              fontType: "italics",
            },
            right: {
              text: "",
            },
          },
        });
      }
    }
  }

  onExportReady() {
    let { autoDownloadFormat } = this.props;
    if (autoDownloadFormat) {
      this.downloadModalCallback(autoDownloadFormat);
    }
  }

  tabChange(e) {
    console.log("TAB_CHANGE", e);
    this.setState(
      {
        dataViewType: this.state.dataViewType,
        selected_thing: e,
        pageLoading: true,
        isSummaryTab: e === "summary",
      },
      async () => {
        await this.callDataApi();
      },
    );
  }

  goBackToForm() {
    this.props.reportViewFalse();
  }

  getDownloadRender() {
    const { inputDataSet } = this.props;
    console.log("downloaderReportConfig", this.state.downloaderReportConfig);
    let downloadRender = this.state.downloaderReportConfig ? (
      <div
        style={{
          opacity: 0,
          visibility: "hidden",
          overflow: "hidden",
          "max-height": 0,
        }}
      >
        <ReportController
          downloadingReport
          t={this.props.t}
          key={moment().unix()}
          {...this.state.downloaderReportConfig}
          ref={this.invisibleReportRef}
          onExportReady={this.onExportReady}
          is_white_label={this.props.is_white_label}
          isAurassure={this.props.isAurassure}
          vendor_name={this.props.vendor_name}
          vendor_logo={this.props.vendor_logo}
        />
      </div>
    ) : (
      ""
    );
    this.setState(
      {
        download_render: downloadRender,
      },
      () => {
        if (inputDataSet) {
          setTimeout(() => {
            this.downloadModalCallback(inputDataSet?.type);
            if (typeof window.reportGenerationCompleted === "function") {
              window.reportGenerationCompleted(
                getDownloadFileName(
                  inputDataSet?.name || "Custom Report",
                  inputDataSet?.type,
                ),
              );
            }
          }, 2000);
        }
        setTimeout(() => {
          this.downloadModalCallback(this.state.fileFormat);
          this.setState({
            downloadLoading: false,
          });
        }, 1000);
      },
    );
  }

  analysisFormatChange(e) {
    const { analysisFormatChecked } = this.state;
    this.setState(
      {
        analysisFormatChecked: !analysisFormatChecked,
      },
      () => {
        this.getThingsDataFunc();
      },
    );
  }

  getMemoizedReport = memoizeOne(
    (isSummaryTab, summaryReportData, reportData, selected_thing, dataViewType) => {
      if (isSummaryTab) {
        return this.renderReportView(summaryReportData);
      }
      return this.renderReportView(reportData[selected_thing]);
    },
  );

  render() {
    const { inputDataSet, totalThingsData } = this.props;
    const {
      pageLoading,
      options,
      downloadLoading,
      selected_thing,
      analysisFormatChecked,
      isAvg,
      isSummaryTab,
    } = this.state;
    let pgx = "";
    if (this.state.loading) {
      pgx = <Loading show_logo={this.props.loading_logo} />;
    } else {
      const isMobile = window.innerWidth <= 576;
      const switchDivContainer = (
        <div
          style={{
            display: "flex",
            justifyContent: isMobile ? "space-around" : "flex-end",
            alignItems: "center",
            gap: 8,
          }}
        >
          {options.dtype === "average" ? (
            <div
              style={{ textAlign: "right", marginRight: !isMobile ? 40 : 0 }}
            >
              <span
                className="toggleLabels"
                style={{
                  fontSize: isMobile ? 13 : undefined,
                }}
              >
                {this.props.t
                  ? this.props.t("Include blank rows")
                  : "Include blank rows"}
                {/* Include blank rows */}
              </span>
              <AntSwitch
                size={"small"}
                checked={this.state.inclBlankRows}
                onChange={() => this.inclBlankRowsFunc()}
                style={{ marginLeft: 8 }}
              />
            </div>
          ) : (
            ""
          )}
          {this.state.options.dtype !== "cumulative" ? (
            <div style={{ textAlign: "right" }}>
              <span
                className="toggleLabels"
                style={{
                  fontSize: isMobile ? 13 : undefined,
                }}
              >
                {this.props.t ? this.props.t("grid") : "Grid"}
                {/* Grid */}
              </span>
              <AntSwitch
                size={"small"}
                disabled={this.state.disabled}
                checked={this.state.dataViewType}
                onChange={(e) => this.g2gOnChange(e)}
                style={{ margin: "0 8px" }}
              />
              <span
                className="toggleLabels"
                style={{
                  fontSize: isMobile ? 13 : undefined,
                }}
              >
                {" "}
                {this.props.t ? this.props.t("graph") : "Graph"}
                {/* Graph */}
              </span>
            </div>
          ) : (
            ""
          )}
        </div>
      );
      let tabPaneArray = [];
      if (options.dataOfAllAssets) {
        tabPaneArray.push(
          <AntTabPane tab="Summary" key="summary" disabled={pageLoading}>
            {switchDivContainer}
          </AntTabPane>,
        );
      }
      if (options.device_id_list) {
        options.device_id_list.map(function (ids) {
          tabPaneArray.push(
            <AntTabPane
              tab={
                _find(totalThingsData.things, {
                  id: parseInt(ids),
                }).name
              }
              disabled={pageLoading}
              key={ids}
            >
              {switchDivContainer}
            </AntTabPane>,
          );
        });
      }
      console.log("Options_Options", this.state.reportData);
      pgx = (
        <div id="view_report_page">
          <div className="header-section">
            <div className="head-title">
              <span>
                <ArrowLeftOutlined onClick={() => this.goBackToForm()} />
              </span>
              <span>
                {
                  (options.dtype === "raw"
                    ? ""
                    : options.dtype === "average"
                      ? options.tinterval === 28800
                        ? "8-Hourly "
                        : options.tinterval === 3600
                          ? "Hourly "
                          : options.tinterval === 900
                            ? "15 Min "
                            : options.tinterval === 7 * 86400
                              ? "Weekly "
                              : options.tinterval === 30 * 86400
                                ? "Monthly "
                                : options.tinterval === 365 * 86400
                                  ? "Yearly "
                                  : ""
                      : options.tinterval / 3600 + " Hr ") +
                    (options.dtype === "raw" || options.dtype === "cumulative"
                      ? options.dtype.charAt(0).toUpperCase() +
                        options.dtype.slice(1)
                      : options.dtype) +
                    (this.props.t ? this.props.t("data_from") : " Data from")
                  // " Data from"
                }
              </span>
              <span className="date-show">
                {TimeFormatter(
                  this.props.timeFormat,
                  moment(options.from_time, "X").tz(this.props.timeZone).unix(),
                  "DD MMM YYYY, HH:mm",
                ) +
                  " to " +
                  TimeFormatter(
                    this.props.timeFormat,
                    moment(options.upto_time, "X")
                      .tz(this.props.timeZone)
                      .unix(),
                    "DD MMM YYYY, HH:mm",
                  )}
              </span>{" "}
            </div>
            <div className="download-customize-icon">
              {this.props.isScheduledReport ? (
                <span
                  className="customise-btn"
                  onClick={() => this.scheduleBtnClicked()}
                >
                  <ClockCircleOutlined />
                  <span className="customise-txt">Schedule</span>
                </span>
              ) : (
                ""
              )}
              {window.innerWidth > 576 ? (
                this.props.application_id === 16 &&
                this.props.logged_in_user_client_id === this.props.client_id &&
                !this.props.getViewAccess(["Reports:Download"]) ? (
                  ""
                ) : (
                  <span className="download-btn">
                    {downloadLoading ? (
                      <AntSpin />
                    ) : (
                      <DownloadOutlined
                        onClick={() => this.downloadBtnClicked()}
                      />
                    )}
                  </span>
                )
              ) : (
                ""
              )}

              <span
                className="customise-btn"
                onClick={() => this.customiseReport()}
              >
                <SettingOutlined />
                <span className="customise-txt">
                  {this.props.t
                    ? this.props.t("customise_your_report")
                    : "Customise your report"}
                </span>
              </span>
            </div>
          </div>
          <AntDivider />

          <AntTabs
            onChange={(e) => this.tabChange(e)}
            defaultActiveKey="1"
            type="card"
            size="small"
          >
            {tabPaneArray}
          </AntTabs>
          <div>
            {pageLoading ? (
              <SkeletonLoader />
            ) : (
              this.getMemoizedReport(
                isSummaryTab,
                this.state.summaryReportData,
                this.state.reportData,
                this.state.selected_thing,
                this.state.dataViewType,
              )
            )}
          </div>
        </div>
      );
    }
    return inputDataSet ? (
      <>{this.state.download_render}</>
    ) : (
      <div>
        {pgx}
        <DownloadModal
          t={this.props.t}
          ref={this.downloadModalRef}
          plan_description={this.props.plan_description}
          callback={this.downloadModalCallback}
          download_loading={this.state.download_loading}
          pdfMaxCol={12}
          analysisFormatChecked={analysisFormatChecked}
          analysisFormatChange={(e) => this.analysisFormatChange(e)}
          makeAnalysisFormatFalse={() => this.makeAnalysisFormatFalse()}
          modalDownloadBtnClicked={(e) => this.modalDownloadBtnClicked(e)}
        />
        <CustomModal
          t={this.props.t}
          plan_description={this.props.plan_description}
          ref={this.customModalRef}
          type={this.props.type}
          timeZone={this.props.timeZone}
          timeFormat={this.props.timeFormat}
          options={options}
          totalThingsData={this.props.totalThingsData}
          customizeOnOkRender={async (options) =>
            await this.customizeOnOkRender(options)
          }
          applicationThings={this.props.applicationThings}
          tabKey={this.props.tabKey}
          siteTypes={this.props.siteTypes}
          application_id={this.props.application_id}
          siteList={this.props.siteList}
          getAllowedViz={this.props.getAllowedViz}
          vendor_id={this.props.vendor_id}
          client_id={this.props.client_id}
          logged_in_user_client_id={this.props.logged_in_user_client_id}
        />
        {this.state.scheduleDrawerVisible && (
          <ScheduleDrawer
            ref={this.scheduleDrawerRef}
            scheduleDrawerClosed={() => this.scheduleDrawerClosed()}
            selected_things={options?.device_id_list}
            all_things_list={this.props.totalThingsData?.things}
            report_type="Custom Report"
            client_id={this.props.client_id}
            parent_client_id={this.props.parent_client_id}
            parent_application_id={this.props.parent_application_id}
            app_id={this.props.application_id}
          />
        )}
        {this.state.download_render}
      </div>
    );
  }
}
