/*Libs*/
import React from "react";
import moment from "moment-timezone";
import {
  getParameters,
  filterThingsListWithCatogryAndPartner,
  getParametersAsPerThings,
  getAggrPeriodAsPerCat,
} from "../DataHandeling/parameterManipulation";
import ParaDrawer from "./ParaDrawer";
import { Form } from "antd";
import AntRow from "@datoms/react-components/src/components/AntRow";
import AntCol from "@datoms/react-components/src/components/AntCol";
import AntSelect from "@datoms/react-components/src/components/AntSelect";
import AntOption from "@datoms/react-components/src/components/AntOption";
import AntButton from "@datoms/react-components/src/components/AntButton";
import AntRadio from "@datoms/react-components/src/components/AntRadio";
import AntRadioGroup from "@datoms/react-components/src/components/AntRadioGroup";
import AntCheckbox from "@datoms/react-components/src/components/AntCheckbox";
import AntCheckboxGroup from "@datoms/react-components/src/components/AntCheckboxGroup";
import AntRangePicker from "@datoms/react-components/src/components/AntRangePicker";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import AntSwitch from "@datoms/react-components/src/components/AntSwitch";
import _uniqBy from "lodash/uniqBy";
import _find from "lodash/find";
import _orderBy from "lodash/orderBy";
import { TimeFormatter } from "../../../../../dg-monitoring-views/src/js/data_handling/TimeFormatting";
import CloseCircleFilled from "@ant-design/icons/CloseCircleFilled";

export default class CustomForm extends React.Component {
  constructor(props) {
    super(props);
    this.formItemLayout = {
      labelCol: { sm: 24, md: 24, lg: 12, xl: 5, xxl: 4 },
      wrapperCol: { sm: 24, md: 24, lg: 12, xl: 15, xxl: 13 },
      colon: false,
    };

    this.formRef = React.createRef();
    this.compare_item = this.compare_item.bind(this);
    this.onCheckAllChange = this.onCheckAllChange.bind(this);
    this.onValuesChange = this.onValuesChange.bind(this);
    this.disabledDate = this.disabledDate.bind(this);

    let initialDeviceIDList = [],
      deviceArray = [],
      totalInterval = [],
      paramKeys = [],
      allParams = [],
      allThingWiseParamKeys = {},
      thingWiseParamKeys = {},
      selectAllChecked = {};
    const {
      totalThingsData,
      application_id,
      getAllowedViz,
      vendor_id,
      logged_in_user_client_id,
      client_id,
    } = this.props;
    const partnersArr = [];
    if (totalThingsData?.things?.length) {
      for (let i = 0; i < totalThingsData.things.length; i++) {
        const thing = totalThingsData.things[i];
        if (
          application_id === 16 &&
          partnersArr.find((vendor) => vendor.value === thing.vendor_id) ===
            undefined
        ) {
          partnersArr.push({
            value: thing.vendor_id,
            label: thing.vendor_name,
          });
        }
      }
    }
    let uniqCategories = _uniqBy(totalThingsData.things, "category");
    let getTotalCategory = [];
    if (uniqCategories && uniqCategories.length) {
      uniqCategories.map((cat) => {
        getTotalCategory.push({
          id: _find(totalThingsData.things_categories, {
            id: cat.category,
          })?.id,
          name:
            cat.category === 95
              ? ""
              : _find(totalThingsData.things_categories, {
                  id: cat.category,
                })?.name,
        });
      });
    }
    getTotalCategory = _orderBy(getTotalCategory, ["id", "desc"]);
    if (totalThingsData && getTotalCategory && getTotalCategory.length) {
      let deviceArr = filterThingsListWithCatogryAndPartner(
        totalThingsData.things,
        getTotalCategory[0]["id"],
      );
      totalInterval = getAggrPeriodAsPerCat(
        totalThingsData?.things_categories,
        getTotalCategory[0]["id"],
      );
      deviceArray = this.getDeviceList(deviceArr);

      let i = 0;
      deviceArray.map((thng) => {
        if (i < 5) {
          initialDeviceIDList.push(thng.value);
          i++;
        }
      });
      let findApplicationThings = _find(
        this.props.applicationThings?.things_categories,
        { id: getTotalCategory[0]["id"] },
      );
      allParams = getParameters(
        totalThingsData.things,
        initialDeviceIDList,
        "average",
        findApplicationThings,
      );
      allThingWiseParamKeys = getParametersAsPerThings(
        totalThingsData.things,
        initialDeviceIDList,
        "average",
        findApplicationThings,
      );
      if (allParams && allParams.length) {
        allParams.map((param) => {
          paramKeys.push(param.value);
        });
      }
      if (allThingWiseParamKeys && Object.keys(allThingWiseParamKeys).length) {
        Object.keys(allThingWiseParamKeys).map((keys) => {
          if (!thingWiseParamKeys[keys]) {
            thingWiseParamKeys[keys] = [];
          }
          if (!selectAllChecked[keys]) {
            selectAllChecked[keys] = true;
          }
          if (allThingWiseParamKeys && allThingWiseParamKeys[keys].length) {
            allThingWiseParamKeys[keys].map((param) => {
              thingWiseParamKeys[keys].push(param.value);
            });
          }
        });
      }
    }
    const isAurassurePartner =
      application_id === 16 && vendor_id === 1819 && application_id !== 12;
    const selectedDataModelOptions = [
      {
        label: this.props.t('processed'),
        // label: "Processed",
        value: "processed",
      },
      {
        label: this.props.t('unprocessed'),
        // label: "Unprocessed",
        value: "unprocessed",
      },
    ];
    const endCustDataModels = [];
    if (!isAurassurePartner) {
      if (getAllowedViz?.options?.length) {
        getAllowedViz.options.map((option) => {
          selectedDataModelOptions.find((selectedOption) => {
            if (selectedOption.value === option) {
              endCustDataModels.push(selectedOption);
            }
          });
        });
      }
    }
    const selectedDataModel = isAurassurePartner
      ? ["processed"]
      : [getAllowedViz?.defaultValue];
    const dtype = selectedDataModel.includes("unprocessed") ? "raw" : "average";
    let options = {
      checked_all: true,
      checked: selectAllChecked,
      visible: false,
      dtype,
      dataOfAllAssets: false,
      tinterval: totalInterval[0],
      drva: 1,
      graph_view: "different",
      total_thing: deviceArray,
      totalInterval: totalInterval,
      partnersArr: partnersArr,
      selectedPartner: [],
      selectedDataModel,
      pcategory:
        getTotalCategory && getTotalCategory.length
          ? getTotalCategory[0]["id"]
          : [],
      device_id_list: initialDeviceIDList,
      param_type_radio: "total",
      tpara: paramKeys,
      selected_thing: initialDeviceIDList?.[0],
      all_params: allParams,
      all_thing_wise_param_keys: allThingWiseParamKeys,
      thing_wise_param_selection: thingWiseParamKeys,
      from_time:
        dtype === "raw"
          ? moment.tz(this.props.timeZone).subtract(24, "hours").unix()
          : moment
              .tz(this.props.timeZone)
              .subtract(7, "days")
              .startOf("day")
              .unix(),
      upto_time: moment.tz(this.props.timeZone).unix(),
      r_view: ["grid", "graph"],
      avgOfAllAsset: false,
    };
    let getOptions = {};
    if (props.prefilledOptions) {
      getOptions = props.prefilledOptions;
    } else {
      getOptions = options;
    }
    this.state = {
      options: getOptions,
      searchString: "",
      getTotalCategory: getTotalCategory,
      selectedDataModelOptions: isAurassurePartner
        ? selectedDataModelOptions
        : endCustDataModels,
      drawerOptions: props.prefilledOptions
        ? JSON.parse(JSON.stringify(props.prefilledOptions))
        : JSON.parse(JSON.stringify(options)),
    };
    moment.tz.setDefault(props.timeZone);
    this.props.customCallBack(getOptions);
  }

  onSearch(e) {
    this.setState({
      searchString: e,
    });
  }

  compare_item(a, b) {
    if (a.label < b.label) {
      return -1;
    } else if (a.label > b.label) {
      return 1;
    } else {
      return 0;
    }
  }

  onlyUnique(value, index, self) {
    return self.indexOf(value) === index;
  }

  onFinish = (values) => {};

  onFinishFailed = (errorInfo) => {};

  reportGenerate = () => {
    this.props.reportGenerate();
  };

  onFormInstanceInit = (formRef) => {
    const { onFormInstanceInit } = this.props;
    if (onFormInstanceInit) {
      onFormInstanceInit(formRef);
    }
    this.formRef = formRef;
  };

  componentDidMount() {
    const { onFormInstanceInit } = this.props;
    if (onFormInstanceInit) {
      onFormInstanceInit(this.formRef);
    }
  }

  disableCondionsForUnprocessed() {
    return this.state.options.selectedDataModel.includes("unprocessed");
  }

  onValuesChange(cf, af) {
    const selectedPartner =
      af.selectedPartner || this.props.prefilledOptions?.selectedPartner;
    let selectedCat = af.things_catogery
      ? af.things_catogery
      : this.props.prefilledOptions.pcategory;
    const selectedDataModel =
      af.data_model || this.state.options.selectedDataModel;
    if (af.data_model?.includes("unprocessed")) {
      this.formRef.current.setFieldsValue({
        data_type: "raw",
      });
    }
    let d_type = af.data_type ? af.data_type : "average";
    let dataOfAllAssets = af.dataOfAllAssets ? af.dataOfAllAssets : false;
    let param_type_radio = af.param_type_radio ? af.param_type_radio : "total";
    let totalInterval = getAggrPeriodAsPerCat(
      this.props.totalThingsData?.things_categories,
      af.things_catogery,
    );
    let t_interval =
      af.data_type === "raw"
        ? 0
        : af.interval
          ? af.interval
          : this.props.prefilledOptions && this.props.prefilledOptions.tinterval
            ? this.props.prefilledOptions.tinterval
            : totalInterval[0];
    let graph_view = af.graph_view
      ? af.graph_view
      : this.props.prefilledOptions && this.props.prefilledOptions.graph_view
        ? this.props.prefilledOptions.graph_view
        : "different";
    let allSelectAll = af.select_all ? af.select_all : false;
    let selectAll = this.state.options.checked;
    selectAll[this.state.options.selected_thing] = af.select_all
      ? af.select_all
      : false;
    let drv = 1; // Default value

    if (
      af.data_type === "raw" ||
      af.interval === 3600 ||
      af.interval === 7 * 86400 ||
      af.interval === 30 * 86400 ||
      af.interval === 365 * 86400
    ) {
      if (af.ddrange === 1 || af.ddrange === 10) {
        drv = af.ddrange;
      } else if (
        this.props.prefilledOptions &&
        this.props.prefilledOptions.drva
      ) {
        drv = this.props.prefilledOptions.drva;
      }
    } else if (af.ddrange) {
      drv = af.ddrange;
    } else if (
      this.props.prefilledOptions &&
      this.props.prefilledOptions.drva
    ) {
      drv = this.props.prefilledOptions.drva;
    }
    let customRange =
      af.custom_range && af.custom_range.length ? af.custom_range : [];
    let rView =
      af.data_type === "cumulative" ? ["grid"] : af.r_view ? af.r_view : [];

    let deviceArr = filterThingsListWithCatogryAndPartner(
      this.props.totalThingsData.things,
      af.things_catogery,
      af.selectedPartner,
      this.state.options.partnersArr,
      af.things.includes("all"),
    );

    let deviceArray = this.getDeviceList(deviceArr);
    let initialDeviceIDList = [],
      i = 0;
    deviceArray.map((thng) => {
      if (i < 5) {
        initialDeviceIDList.push(thng.value);
        i++;
      }
    });

    const thingIds = [];
    if (deviceArray && deviceArray.length) {
      deviceArray.map((data) => {
        if (af.things.includes("all")) {
          thingIds.push(data.value);
        } else {
          if (af.things.includes(data.value)) {
            thingIds.push(data.value);
          }
        }
      });
    }
    let deviceIds = cf.things_catogery
      ? initialDeviceIDList
      : af.things
        ? af.things
        : this.state.options.device_id_list;
    deviceIds = af.things.includes("all")
      ? thingIds
      : cf.selectedPartner
        ? thingIds
        : deviceIds;
    let filteredThings = [];
    if (deviceIds && deviceIds.length) {
      deviceIds.map((ids) => {
        if (_find(this.props.totalThingsData.things, { id: ids })) {
          filteredThings.push(
            _find(this.props.totalThingsData.things, {
              id: ids,
            }),
          );
        }
      });
    }
    let findApplicationThings = _find(
      this.props.applicationThings?.things_categories,
      { id: selectedCat },
    );
    let allParams = getParameters(
      filteredThings,
      deviceIds,
      af.data_type,
      findApplicationThings,
    );
    let allThingWiseParamKeys = getParametersAsPerThings(
      filteredThings,
      deviceIds,
      af.data_type,
      findApplicationThings,
    );
    let tParaValue = this.state.options.tpara;
    let thingWiseParamKeys = {};

    if (cf.data_type || cf.things_catogery || cf.param_type_radio) {
      tParaValue = allParams.map((params) => {
        return params.value;
      });
      if (allThingWiseParamKeys && Object.keys(allThingWiseParamKeys).length) {
        Object.keys(allThingWiseParamKeys).map((keys) => {
          if (!thingWiseParamKeys[keys]) {
            thingWiseParamKeys[keys] = [];
          }
          if (allThingWiseParamKeys && allThingWiseParamKeys[keys].length) {
            if (!thingWiseParamKeys[keys]) {
              thingWiseParamKeys[keys] = [];
            }
            allThingWiseParamKeys[keys].map((param) => {
              thingWiseParamKeys[keys].push(param.value);
            });
          }
        });
      }
    } else if (cf.things) {
      tParaValue = allParams.map((params) => {
        return params.value;
      });
      if (allThingWiseParamKeys && Object.keys(allThingWiseParamKeys).length) {
        Object.keys(allThingWiseParamKeys).map((keys) => {
          if (!thingWiseParamKeys[keys]) {
            thingWiseParamKeys[keys] =
              this.state.options.thing_wise_param_selection[keys];
          }
          if (
            allThingWiseParamKeys &&
            allThingWiseParamKeys[keys].length &&
            thingWiseParamKeys[keys] === undefined
          ) {
            if (!thingWiseParamKeys[keys]) {
              thingWiseParamKeys[keys] = [];
            }
            allThingWiseParamKeys[keys].map((param) => {
              thingWiseParamKeys[keys].push(param.value);
            });
          }
        });
      }
    } else {
      thingWiseParamKeys = this.state.options.thing_wise_param_selection;
      tParaValue = this.state.options.tpara;
    }
    let selectedThing = this.state.options.selected_thing;
    if (deviceIds && deviceIds.length) {
      selectedThing = deviceIds.includes(this.state.options.selected_thing)
        ? this.state.options.selected_thing
        : deviceIds[0];
    }
    if (deviceArray && deviceArray.length) {
      allSelectAll = tParaValue.length === allParams.length ? true : false;
      selectAll[selectedThing] =
        thingWiseParamKeys[selectedThing]?.length ===
        allThingWiseParamKeys[selectedThing]?.length
          ? true
          : false;
      this.formRef.current?.setFieldsValue({
        things: deviceIds,
      });
    } else {
      allSelectAll = false;
      selectAll[selectedThing] = false;
      this.formRef.current?.setFieldsValue({
        things: [],
      });
    }
    let fromTime = this.state.fromTime;
    let uptoTime = this.state.uptoTime;
    if (af.data_type === "raw" || this.disableCondionsForUnprocessed()) {
      if (drv === 1) {
        fromTime = moment.tz(this.props.timeZone).subtract(1, "days").unix();
        uptoTime = moment.tz(this.props.timeZone).unix();
      } else if (drv === 10) {
        fromTime =
          customRange[0] === undefined
            ? moment.tz(this.props.timeZone).subtract(1, "days").unix()
            : moment(customRange[0], "X")
                .tz(this.props.timeZone)
                .seconds(0)
                .unix();
        uptoTime =
          customRange[1] === undefined
            ? moment.tz(this.props.timeZone).unix()
            : moment(customRange[1], "X")
                .tz(this.props.timeZone)
                .seconds(59)
                .unix();
      }
    } else if (af.interval === 7 * 86400) {
      if (drv === 1) {
        fromTime = moment.tz(this.props.timeZone).startOf("week").unix();
        uptoTime = moment.tz(this.props.timeZone).unix();
      } else if (drv === 10) {
        fromTime =
          customRange[0] === undefined
            ? moment.tz(this.props.timeZone).startOf("week").unix()
            : moment(customRange[0], "X")
                .tz(this.props.timeZone)
                .seconds(0)
                .unix();
        uptoTime =
          customRange[1] === undefined
            ? moment.tz(this.props.timeZone).unix()
            : moment(customRange[1], "X")
                .tz(this.props.timeZone)
                .seconds(59)
                .unix();
      }
    } else if (af.interval === 30 * 86400) {
      if (drv === 1) {
        fromTime = moment.tz(this.props.timeZone).startOf("month").unix();
        uptoTime = moment.tz(this.props.timeZone).unix();
      } else if (drv === 10) {
        fromTime =
          customRange[0] === undefined
            ? moment.tz(this.props.timeZone).startOf("month").unix()
            : moment(customRange[0], "X")
                .tz(this.props.timeZone)
                .seconds(0)
                .unix();
        uptoTime =
          customRange[1] === undefined
            ? moment.tz(this.props.timeZone).unix()
            : moment(customRange[1], "X")
                .tz(this.props.timeZone)
                .seconds(59)
                .unix();
      }
    } else if (af.interval === 365 * 86400) {
      if (drv === 1) {
        fromTime = moment.tz(this.props.timeZone).startOf("year").unix();
        uptoTime = moment.tz(this.props.timeZone).unix();
      } else if (drv === 10) {
        fromTime =
          customRange[0] === undefined
            ? moment.tz(this.props.timeZone).startOf("year").unix()
            : moment(customRange[0], "X")
                .tz(this.props.timeZone)
                .seconds(0)
                .unix();
        uptoTime =
          customRange[1] === undefined
            ? moment.tz(this.props.timeZone).unix()
            : moment(customRange[1], "X")
                .tz(this.props.timeZone)
                .seconds(59)
                .unix();
      }
    } else if (drv === 1) {
      fromTime = moment
        .tz(this.props.timeZone)
        .subtract(7, "days")
        .startOf("day")
        .unix();
      uptoTime = moment.tz(this.props.timeZone).unix();
    } else if (drv === 2) {
      fromTime = moment.tz(this.props.timeZone).startOf("week").unix();
      uptoTime = moment.tz(this.props.timeZone).unix();
    } else if (drv === 3) {
      fromTime = moment
        .tz(this.props.timeZone)
        .subtract(7, "days")
        .startOf("week")
        .unix();
      uptoTime = moment
        .tz(this.props.timeZone)
        .subtract(7, "days")
        .endOf("week")
        .unix();
    } else if (drv === 4) {
      fromTime = moment
        .tz(this.props.timeZone)
        .subtract(30, "days")
        .startOf("day")
        .unix();
      uptoTime = moment.tz(this.props.timeZone).unix();
    } else if (drv === 5) {
      fromTime = moment
        .tz(this.props.timeZone)
        .subtract(1, "month")
        .startOf("month")
        .unix();
      uptoTime = moment
        .tz(this.props.timeZone)
        .subtract(1, "month")
        .endOf("month")
        .unix();
    } else if (drv === 6) {
      fromTime = moment.tz(this.props.timeZone).startOf("month").unix();
      uptoTime = moment.tz(this.props.timeZone).unix();
    } else if (drv === 7) {
      fromTime = moment.tz(this.props.timeZone).startOf("quarter").unix();
      uptoTime = moment.tz(this.props.timeZone).endOf("quarter").unix();
    } else if (drv === 8) {
      fromTime = moment
        .tz(this.props.timeZone)
        .subtract(1, "quarter")
        .startOf("quarter")
        .unix();
      uptoTime = moment
        .tz(this.props.timeZone)
        .subtract(1, "quarter")
        .endOf("quarter")
        .unix();
    } else if (drv === 9) {
      fromTime = moment.tz(this.props.timeZone).startOf("year").unix();
      uptoTime = moment.tz(this.props.timeZone).unix();
    } else if (drv === 10) {
      fromTime =
        customRange[0] === undefined
          ? moment.tz(this.props.timeZone).subtract(7, "days").unix()
          : moment(customRange[0], "X")
              .tz(this.props.timeZone)
              .seconds(0)
              .unix();
      uptoTime =
        customRange[1] === undefined
          ? moment.tz(this.props.timeZone).unix()
          : moment(customRange[1], "X")
              .tz(this.props.timeZone)
              .seconds(59)
              .unix();
    }
    let options = {};
    options = {
      tpara: tParaValue,
      thing_wise_param_selection: thingWiseParamKeys,
      table_type: ["summary_table", "detailed_table"],
      selectedPartner,
      partnersArr: this.state.options.partnersArr,
      pcategory: selectedCat,
      total_thing: deviceArray,
      device_id_list: deviceIds,
      dtype: selectedDataModel.includes("unprocessed") ? "raw" : d_type,
      dataOfAllAssets: dataOfAllAssets,
      param_type_radio: param_type_radio,
      drva: drv,
      checked: selectAll,
      checked_all: allSelectAll,
      totalInterval: totalInterval,
      selected_thing: selectedThing,
      idis: d_type === "raw" ? true : false,
      tinterval: t_interval,
      graph_view: graph_view,
      all_params: allParams,
      all_thing_wise_param_keys: allThingWiseParamKeys,
      from_time: fromTime,
      upto_time: uptoTime,
      r_view: rView,
      selected_thing: selectedThing,
      selectedDataModel,
    };

    this.setState(
      {
        options: options,
        drawerOptions: JSON.parse(JSON.stringify(options)),
      },
      () => {
        this.props.customCallBack(this.state.options);
      },
    );
  }

  getThingsCategories() {
    let catList = [];
    if (
      this.props.totalThingsData &&
      this.state.getTotalCategory &&
      this.state.getTotalCategory.length
    ) {
      this.state.getTotalCategory.map((obj) => {
        const myObj = {};
        myObj["label"] = obj["name"];
        myObj["value"] = obj["id"];
        catList.push(myObj);
      });
    }

    return catList;
  }

  getDeviceList(deviceArray) {
    if (deviceArray && deviceArray.length) {
      deviceArray = deviceArray.sort(this.compare_item);
    }
    return deviceArray;
  }

  aggrIntervalFunc() {
    if (this.state.options.dtype === "raw") {
      return (
        <AntSelect defaultValue={86400} disabled>
          <AntOption value={86400}>None</AntOption>
        </AntSelect>
      );
    } else if (this.state.options.dtype === "cumulative") {
      return (
        <AntSelect defaultValue={86400} disabled={true}>
          <AntOption value={86400}>None</AntOption>
        </AntSelect>
      );
    } else if (this.state.options.dtype === "average") {
      return (
        <AntSelect defaultValue={this.state.options.tinterval} disabled={false}>
          {this.state.options?.totalInterval?.length &&
            this.state.options.totalInterval.map((interval) => {
              return (
                <AntOption value={interval}>
                  {interval === 28800
                    ? "8-Hourly"
                    : interval === 900
                      ? '15 ' + this.props.t('minutes')
                      : interval === 3600
                        ? this.props.t('hourly')
                        : interval === 86400
                          ? this.props.t('daily')
                          : interval === 7 * 86400
                            ? this.props.t? this.props.t('weekly'): "Weekly"
                            : interval === 30 * 86400
                              ? this.props.t? this.props.t('monthly'): "Monthly"
                              : interval === 90 * 86400
                                ? "Quarterly"
                                : interval === 365 * 86400
                                  ? this.props.t? this.props.t('yearly'): "Yearly"
                                  : interval}
                </AntOption>
              );
            })}
        </AntSelect>
      );
    }
  }

  getJsx2() {
    let allpara = [];
    if (
      this.state.options.all_thing_wise_param_keys[
        this.state.options.selected_thing
      ]
    ) {
      let l = Math.min(
        8,
        this.state.options.all_thing_wise_param_keys[
          this.state.options.selected_thing
        ].length,
      );
      for (let i = 0; i < l; i++) {
        allpara.push(
          this.state.options.all_thing_wise_param_keys[
            this.state.options.selected_thing
          ][i],
        );
      }
    }

    return allpara;
  }

  allParamKeys() {
    let myjsx2 = this.getJsx2();
    let defPara = myjsx2.map((p) => {
      return p.value;
    });
    return defPara;
  }

  getJsx1() {
    let allpara = [];
    if (this.state.options.all_params) {
      let l = Math.min(8, this.state.options.all_params.length);
      for (let i = 0; i < l; i++) {
        allpara.push(this.state.options.all_params[i]);
      }
    }

    return allpara;
  }

  allParamKeysTotal() {
    let myjsx1 = this.getJsx1();
    let defPara = myjsx1.map((p) => {
      return p.value;
    });
    return defPara;
  }

  onCheckAllChange(e) {
    let defPara = this.state.options.all_params.map((p) => {
      return p.value;
    });
    let defThingWisePara = {};
    Object.keys(this.state.options.all_thing_wise_param_keys).map((keys) => {
      if (!defThingWisePara[keys]) {
        defThingWisePara[keys] = [];
      }
      if (
        this.state.options.all_thing_wise_param_keys &&
        this.state.options.all_thing_wise_param_keys[keys].length
      ) {
        this.state.options.all_thing_wise_param_keys[keys].map((param) => {
          defThingWisePara[keys].push(param.value);
        });
      }
    });
    let { options } = this.state;
    options["checked_all"] = e.target.checked;
    options["checked"][this.state.options.selected_thing] = e.target.checked;
    options["tpara"] = e.target.checked ? defPara : [];
    options["thing_wise_param_selection"][this.state.options.selected_thing] = e
      .target.checked
      ? defThingWisePara[this.state.options.selected_thing]
      : [];
    this.setState(
      {
        options: options,
        drawerOptions: JSON.parse(JSON.stringify(options)),
      },
      () => {
        this.props.customCallBack(this.state.options);
      },
    );
  }
  onCheckAllChangeInDrawer(e) {
    let defPara = this.state.drawerOptions.all_params.map((p) => {
      return p.value;
    });
    let defThingWisePara = {};
    Object.keys(this.state.drawerOptions.all_thing_wise_param_keys).map(
      (keys) => {
        if (!defThingWisePara[keys]) {
          defThingWisePara[keys] = [];
        }
        if (
          this.state.drawerOptions.all_thing_wise_param_keys &&
          this.state.drawerOptions.all_thing_wise_param_keys[keys].length
        ) {
          this.state.drawerOptions.all_thing_wise_param_keys[keys].map(
            (param) => {
              defThingWisePara[keys].push(param.value);
            },
          );
        }
      },
    );
    let { drawerOptions } = this.state;
    drawerOptions["checked_all"] = e.target.checked;
    drawerOptions["checked"][this.state.options.selected_thing] =
      e.target.checked;
    drawerOptions["tpara"] = e.target.checked ? defPara : [];
    drawerOptions["thing_wise_param_selection"][
      this.state.options.selected_thing
    ] = e.target.checked
      ? defThingWisePara[this.state.options.selected_thing]
      : [];
    this.setState(
      {
        drawerOptions: JSON.parse(JSON.stringify(drawerOptions)),
      },
      () => {
        this.props.customCallBack(this.state.options);
      },
    );
  }

  onParamChangeInDrawer(value) {
    let tparaValue = this.state.drawerOptions.tpara.includes(value)
      ? this.state.drawerOptions.tpara.filter((x) => x !== value)
      : [...this.state.drawerOptions.tpara, value];
    let thingWisePara = this.state.drawerOptions.thing_wise_param_selection;
    thingWisePara[this.state.options.selected_thing] =
      this.state.drawerOptions.thing_wise_param_selection?.[
        this.state.options.selected_thing
      ]?.includes(value)
        ? this.state.drawerOptions.thing_wise_param_selection?.[
            this.state.options.selected_thing
          ]?.filter((x) => x !== value)
        : [
            ...this.state.drawerOptions.thing_wise_param_selection?.[
              this.state.options.selected_thing
            ],
            value,
          ];
    let selectedChecked = this.state.drawerOptions.checked;
    selectedChecked[this.state.options.selected_thing] =
      thingWisePara.length ===
      this.state.drawerOptions.all_thing_wise_param_keys[
        this.state.options.selected_thing
      ].length
        ? true
        : false;
    let { drawerOptions } = this.state;
    drawerOptions["checked_all"] =
      tparaValue.length === this.state.drawerOptions.all_params.length
        ? true
        : false;
    drawerOptions["checked"] = selectedChecked;
    drawerOptions["tpara"] = tparaValue;
    drawerOptions["thing_wise_param_selection"] = thingWisePara;
    this.setState(
      {
        drawerOptions: JSON.parse(JSON.stringify(drawerOptions)),
      },
      () => {
        this.props.customCallBack(this.state.drawerOptions);
      },
    );
  }
  onDrawerSetOptions(command) {
    let stateObject =
      command === "ok"
        ? {
            options: JSON.parse(JSON.stringify(this.state.drawerOptions)),
          }
        : {
            drawerOptions: JSON.parse(JSON.stringify(this.state.options)),
          };
    this.setState(stateObject, () => {
      if (command === "ok") {
        this.props.customCallBack(this.state.options);
      }
    });
  }
  onParamChange(value) {
    let tparaValue = this.state.options.tpara.includes(value)
      ? this.state.options.tpara.filter((x) => x !== value)
      : [...this.state.options.tpara, value];
    let thingWisePara = this.state.options.thing_wise_param_selection;
    thingWisePara[this.state.options.selected_thing] =
      this.state.options.thing_wise_param_selection?.[
        this.state.options.selected_thing
      ]?.includes(value)
        ? this.state.options.thing_wise_param_selection?.[
            this.state.options.selected_thing
          ]?.filter((x) => x !== value)
        : [
            ...this.state.options.thing_wise_param_selection?.[
              this.state.options.selected_thing
            ],
            value,
          ];
    let selectedChecked = this.state.options.checked;
    selectedChecked[this.state.options.selected_thing] =
      thingWisePara.length ===
      this.state.options.all_thing_wise_param_keys[
        this.state.options.selected_thing
      ].length
        ? true
        : false;
    let { options } = this.state;
    options["checked_all"] =
      tparaValue.length === this.state.drawerOptions.all_params.length
        ? true
        : false;
    options["checked"] = selectedChecked;
    options["tpara"] = tparaValue;
    options["thing_wise_param_selection"] = thingWisePara;
    this.setState(
      {
        options: options,
        drawerOptions: JSON.parse(JSON.stringify(options)),
      },
      () => {
        this.props.customCallBack(this.state.options);
      },
    );
  }

  showDrawer() {
    this.setState({
      visible: true,
      searchString: "",
    });
  }

  onClose(command) {
    this.onDrawerSetOptions(command);
    this.setState({
      visible: false,
    });
  }

  disabledDate(current) {
    let category = this.state.options?.pcategory;
    if (this.state.options?.tinterval < 365 * 86400) {
      return (
        (current &&
          current <
            moment().startOf("Month").subtract(12, "month").startOf("day")) ||
        current >= moment.tz(this.props.timeZone).endOf("day")
      );
    } else {
      return (
        (current &&
          current <
            moment().startOf("Month").subtract(36, "month").startOf("day")) ||
        current >= moment.tz(this.props.timeZone).endOf("day")
      );
    }
    if ([21, 22, 102, 23].includes(category)) {
      return (
        (current &&
          current <
            moment().startOf("Month").subtract(60, "month").startOf("day")) ||
        current >= moment.tz(this.props.timeZone).endOf("day")
      );
    } else {
      if (this.props.plan_description) {
        return (
          (current &&
            current <
              moment()
                .startOf("Month")
                .subtract(
                  this.props.plan_description
                    .maximum_pre_defined_report_time_aggr_allowed,
                  "month",
                )
                .startOf("day")) ||
          current >= moment.tz(this.props.timeZone).endOf("day")
        );
      } else {
        return (
          (current &&
            current <
              moment().startOf("Month").subtract(12, "month").startOf("day")) ||
          current >= moment.tz(this.props.timeZone).endOf("day")
        );
      }
    }
  }

  getTotalTimeRangeValue() {
    return this.state.options.dtype === "raw" ? (
      <AntSelect
        style={{ width: 200 }}
        defaultValue={this.state.options.drva}
        value={this.state.options.drva}
      >
        <AntOption value={1}>{this.props.t('last_24_hrs')}</AntOption>
        <AntOption value={10}>{this.props.t('custom_max_3_days')}</AntOption>
      </AntSelect>
    ) : this.state.options.tinterval === 3600 ? (
      <AntSelect
        style={{ width: 200 }}
        defaultValue={this.state.options.drva}
        value={this.state.options.drva}
      >
        <AntOption value={1}>{this.props.t('last_7_days')}
        {/* Last 7 days */}</AntOption>
        <AntOption value={10}>Custom (Maximum 1 year)</AntOption>
      </AntSelect>
    ) : this.state.options.tinterval === 7 * 86400 ? (
      <AntSelect style={{ width: 200 }} defaultValue={2} value={2}>
        <AntOption value={1}>This Week</AntOption>
        <AntOption value={10}>Custom (Maximum 1 year)</AntOption>
      </AntSelect>
    ) : this.state.options.tinterval === 30 * 86400 ? (
      <AntSelect
        style={{ width: 200 }}
        defaultValue={this.state.options.drva}
        value={this.state.options.drva}
      >
        <AntOption value={1}>This Month</AntOption>
        <AntOption value={10}>Custom (Maximum 1 year)</AntOption>
      </AntSelect>
    ) : this.state.options.tinterval === 365 * 86400 ? (
      <AntSelect
        style={{ width: 200 }}
        defaultValue={this.state.options.drva}
        value={this.state.options.drva}
      >
        <AntOption value={1}>This Year</AntOption>
        <AntOption value={10}>Custom (Maximum 3 years)</AntOption>
      </AntSelect>
    ) : this.state.options.tinterval === 3600 ? (
      <AntSelect
        style={{ width: 200 }}
        defaultValue={this.state.options.drva}
        value={this.state.options.drva}
      >
        <AntOption value={1}>Last 7 days</AntOption>
        <AntOption value={10}>Custom (Maximum 1 year)</AntOption>
      </AntSelect>
    ) : this.state.options.tinterval === 7 * 86400 ? (
      <AntSelect style={{ width: 200 }} defaultValue={2} value={2}>
        <AntOption value={1}>This Week</AntOption>
        <AntOption value={10}>Custom (Maximum 1 year)</AntOption>
      </AntSelect>
    ) : this.state.options.tinterval === 30 * 86400 ? (
      <AntSelect
        style={{ width: 200 }}
        defaultValue={this.state.options.drva}
        value={this.state.options.drva}
      >
        <AntOption value={1}>This Month</AntOption>
        <AntOption value={10}>Custom (Maximum 1 year)</AntOption>
      </AntSelect>
    ) : this.state.options.tinterval === 365 * 86400 ? (
      <AntSelect
        style={{ width: 200 }}
        defaultValue={this.state.options.drva}
        value={this.state.options.drva}
      >
        <AntOption value={1}>This Year</AntOption>
        <AntOption value={10}>Custom (Maximum 3 years)</AntOption>
      </AntSelect>
    ) : (
      <AntSelect
        style={{ width: 200 }}
        defaultValue={this.state.options.drva}
        value={this.state.options.drva}
      >
        <AntOption value={1}>
          {this.props.t('last_7_days')}
          {/* Last 7 days */}
        </AntOption>
        <AntOption value={2}>
          {this.props.t('this_week')}
          {/* This week */}
        </AntOption>
        <AntOption value={3}>
          {this.props.t('last_week')}
          {/* Last week */}
        </AntOption>
        <AntOption value={4}>
          {this.props.t('last_30_days')}
          {/* Last 30 days */}
        </AntOption>
        <AntOption value={5}>
          {this.props.t('last_month')}
          {/* Last month */}
        </AntOption>
        <AntOption value={6}>
          {this.props.t('this_month')}
          {/* This month */}
        </AntOption>
        <AntOption value={7}>
          {`${this.props.t('this_quarter')} (${moment
            .tz(this.props.timeZone)
            .startOf('quarter')
            .format('MMM')} - ${moment
            .tz(this.props.timeZone)
            .endOf('quarter')
            .format('MMM')})`
          }
        </AntOption>
        <AntOption value={8}>
          {`${this.props.t('last_quarter')} (${moment
            .tz(this.props.timeZone)
            .subtract(1, 'quarter')
            .startOf('quarter')
            .format('MMM')} - ${moment
            .tz(this.props.timeZone)
            .subtract(1, 'quarter')
            .endOf('quarter')
            .format('MMM')})`
          }
        </AntOption>
        <AntOption value={9}>
          {this.props.t('this_year')}
          {/* This year */}
        </AntOption>
        <AntOption value={10}>
          {this.props.t('custom')}
          {/* Custom */}
        </AntOption>
      </AntSelect>
    );
  }

  thingClickForParameters(e) {
    const { options } = this.state;
    options["checked"][e] =
      options.thing_wise_param_selection[e]?.length ===
      options.all_thing_wise_param_keys[e]?.length
        ? true
        : false;
    options["checked_all"] =
      options.tpara.length === options.all_params.length ? true : false;
    options["selected_thing"] = e;
    this.setState({
      options: options,
    });
  }

  deselectAllAssets() {}

  render() {
    const { selectedDataModelOptions } = this.state;
    const { partnersArr } = this.state.options;
    let aggIntervalJsx = this.aggrIntervalFunc();
    let timeRangeValJsx = this.getTotalTimeRangeValue();
    let myjsx2 = this.getJsx2();
    let myjsx1 = this.getJsx1();
    let defPara =
      this.state.options.param_type_radio === "total"
        ? this.allParamKeysTotal()
        : this.allParamKeys();
    let dis =
      this.state.getTotalCategory && this.state.getTotalCategory.length === 1
        ? true
        : false;

    let downoptions = [
      { 
        label: this.props.t('grid'),
        // label: "Grid",
        value: "grid" 
      },
      { 
        label: this.props.t('graph'),
        // label: "Graph", 
        value: "graph" 
      },
    ];
    let drval = false;
    let allParamsCheckBox = [];
    if (this.state.options.param_type_radio === "total") {
      if (myjsx1 && myjsx1.length) {
        myjsx1.map((item, idx) => {
          allParamsCheckBox.push(
            <AntCol xs={24} sm={12} md={8} lg={6} xl={6} xxl={6}>
              <AntTooltip title={item.label.replace(/<[^>]+>/g, "")}>
                <AntCheckbox
                  key={item.value + idx}
                  onChange={() => this.onParamChange(item.value)}
                  checked={this.state.options.tpara.includes(item.value)}
                  text={item.label.replace(/<[^>]+>/g, "")}
                />
              </AntTooltip>
            </AntCol>,
          );
        });
      }
    } else {
      if (myjsx2 && myjsx2.length) {
        myjsx2.map((params, idx) => {
          allParamsCheckBox.push(
            <AntCol xs={24} sm={12} md={8} lg={6} xl={6} xxl={6}>
              <AntTooltip title={params.label.replace(/<[^>]+>/g, "")}>
                {params.value ? (
                  <AntCheckbox
                    key={params.value + idx}
                    onChange={() => this.onParamChange(params.value)}
                    checked={this.state.options.thing_wise_param_selection?.[
                      this.state.options.selected_thing
                    ]?.includes(params.value)}
                    text={params.label.replace(/<[^>]+>/g, "")}
                  />
                ) : (
                  ""
                )}
              </AntTooltip>
            </AntCol>,
          );
        });
      }
    }
    let thingSelectionTabs = [];
    if (
      this.state.options &&
      this.state.options.device_id_list &&
      this.state.options.device_id_list.length
    ) {
      this.state.options.device_id_list.map((things) => {
        thingSelectionTabs.push(
          <AntTooltip
            title={
              _find(this.state.options.total_thing, {
                value: things,
              })?.label
            }
          >
            <div
              className={
                "hellip each-thing-tab " +
                (things === this.state.options.selected_thing ? "active" : "")
              }
              onClick={() => this.thingClickForParameters(things)}
            >
              {
                _find(this.state.options.total_thing, {
                  value: things,
                })?.label
              }
            </div>
          </AntTooltip>,
        );
      });
    }
    let selectedPara =
      this.state.options.param_type_radio === "total"
        ? this.state.options.tpara
        : this.state.options.thing_wise_param_selection?.[
            this.state.options.selected_thing
          ];
    let paramCount =
      selectedPara?.length === 0
        ? ""
        : " (" +
          (selectedPara?.length >= 10
            ? selectedPara?.length
            : "0" + selectedPara?.length) + " " +
          (this.props.t? this.props.t('paramters_selected'): " Parameter Selected)")
    const partnersArray = this.state.options.partnersArr;
    const totalThingCopy =
      this.state.options.total_thing.length > 50
        ? this.state.options.total_thing
        : [
            { label: "Select All", value: "all" },
            ...this.state.options.total_thing,
          ];

    const paramAvailable = this.state.drawerOptions.all_params?.length > 0 ||
    this.state.options.all_thing_wise_param_keys[
      this.state.options.selected_thing
    ]?.length > 0;
    return (
      <div id="container">
        <Form
          className="form-component"
          ref={this.formRef}
          {...this.formItemLayout}
          onValuesChange={this.onValuesChange}
          initialValues={{
            selectedPartner: this.state.options.selectedPartner,
            things_catogery: this.state.options.pcategory,
            things: this.state.options.device_id_list,
            data_type: this.state.options.dtype,
            drva: this.state.options.drva,
            dataOfAllAssets: this.state.options.dataOfAllAssets,
            data_model: this.state.options.selectedDataModel,
            para: 1,
            param_type_radio: this.state.options.param_type_radio,
            r_view: this.state.options.r_view,
            select_all:
              this.state.options.param_type_radio === "total"
                ? this.state.options.check_all
                : this.state.options.checked[this.state.options.selected_thing],
            dpara: defPara,
            m_stat: 1,
            custom_range: this.state.options.drva === 10 ? [moment.unix(this.state.options.from_time), moment.unix(this.state.options.upto_time)] : undefined,
          }}
        >
          {partnersArray?.length > 1 ? (
            <Form.Item label= {this.props.t('select_partners')}
             name="selectedPartner">
              <AntSelect
                t={this.props.t}
                mode="multiple"
                options={partnersArray}
                disabled={dis}
              />
            </Form.Item>
          ) : (
            ""
          )}
          <Form.Item label={this.props.t('select_asset_type')}
               name="things_catogery">
            <AntSelect options={this.getThingsCategories()} disabled={dis} />
          </Form.Item>
          <Form.Item
            label={this.props.t? this.props.t('select_assets'): "Select Assets"}
            name="things"
            rules={[
              {
                required: true,
                message: this.props.t ? this.props.t('please_select_at_least_one_asset'): "Please select at least one asset"
              // message: "Please select at least one asset",
              },
            ]}
          >
            <AntSelect
              t={this.props.t}
              mode="multiple"
              options={totalThingCopy}
              optionFilterProp="label"
              allowClear
            />
          </Form.Item>
          {selectedDataModelOptions?.length ? (
            <Form.Item  label={this.props.t('select_data_model')}
              name="data_model">
              <AntCheckboxGroup options={selectedDataModelOptions} />
            </Form.Item>
          ) : (
            ""
          )}
          <Form.Item  label={this.props.t('select_dataformat')} name="data_type">
            <AntRadioGroup name="radiogroup">
              <AntRadio value={"raw"}>{this.props.t('raw')}</AntRadio>
              <AntRadio
                value={"average"}
                disabled={this.disableCondionsForUnprocessed()}
              >
                {this.props.t('average')}
              </AntRadio>
              <AntRadio
                value={"cumulative"}
                disabled={this.disableCondionsForUnprocessed()}
              >
                Cumulative
              </AntRadio>
            </AntRadioGroup>
          </Form.Item>
          {this.state.options.dtype === "average" &&
          this.state.options.device_id_list?.length > 1 ? (
            <Form.Item label={this.props.t? this.props.t("Include asset summary"): "Include asset summary"} name="dataOfAllAssets">
              <AntSwitch />
            </Form.Item>
          ) : (
            ""
          )}
          <Form.Item label={this.props.t('select_aggregation_period')} name="interval">
            {aggIntervalJsx}
          </Form.Item>
          {paramAvailable && <Form.Item
            label= {this.props.t('select_parameters')}
            name="param_type_radio"
            className="param-selection-dropdown"
            required={true}
          >
            <AntRadioGroup>
              <AntRadio value={"total"}>{this.props.t('search_for_all_assets')}</AntRadio>
              <AntRadio value={"thing_wise"}>{this.props.t('select_asset_wise')}</AntRadio>
            </AntRadioGroup>
          </Form.Item>}
          {this.state.options.param_type_radio !== "total" ? (
            <Form.Item label=" " name="" className="">
              <AntRow>
                <AntCol xs={24} sm={14} md={14} lg={12} xl={15} xxl={20}>
                  <div className="station-tab">{thingSelectionTabs}</div>
                </AntCol>
              </AntRow>
            </Form.Item>
          ) : (
            ""
          )}
          {allParamsCheckBox.length ? (
            this.state.options.all_thing_wise_param_keys[
              this.state.options.selected_thing
            ].length > 0 ? (
              <Form.Item
                label=" "
                name="select_all"
                className="select-all-item"
              >
                <AntCheckbox
                  onChange={this.onCheckAllChange}
                  checked={
                    this.state.options.param_type_radio === "total"
                      ? this.state.options.checked_all
                      : this.state.options.checked[
                          this.state.options.selected_thing
                        ]
                  }
                  text={
                    <span>
                      {this.props.t? this.props.t('select_all'): 'Select all '}{" "}
                      <span
                        style={{
                          "margin-left": 20,
                          "font-weight": "normal",
                          "font-size": 12,
                          color: "#808080",
                          "font-style": "italic",
                        }}
                      >
                        {paramCount}
                      </span>
                    </span>
                  }
                />
              </Form.Item>
            ) : (
              ""
            )
          ) : (
            ""
          )}
          <Form.Item label=" " name="dpara" className="all-params">
            <AntRow>{allParamsCheckBox}</AntRow>
          </Form.Item>
          {this.state.drawerOptions.all_params?.length > 8 ||
          this.state.options.all_thing_wise_param_keys[
            this.state.options.selected_thing
          ]?.length > 8 ? (
            <Form.Item label=" " className="show-more-btn">
              <div
                onClick={() => this.showDrawer()}
                className="show-more-button-1"
              >
                {this.props.t ? this.props.t('show_more'): 'Show more'} {">"}
              </div>
            </Form.Item>
          ) : (
            <div className="show-more-button-1"></div>
          )}
          <ParaDrawer
            t={this.props.t}
            onSearch={(e) => this.onSearch(e)}
            searchString={this.state.searchString}
            visible={this.state.visible}
            onClose={(e) => this.onClose(e)}
            selected_para={
              this.state.options.param_type_radio === "total"
                ? this.state.drawerOptions.tpara
                : this.state.drawerOptions.thing_wise_param_selection[
                    this.state.options.selected_thing
                  ]
            }
            all_para_thing_wise={
              this.state.drawerOptions.all_thing_wise_param_keys[
                this.state.options.selected_thing
              ]
            }
            all_para={this.state.drawerOptions.all_params}
            onCheckAllChange={(e) => this.onCheckAllChangeInDrawer(e)}
            check_all={this.state.drawerOptions.checked_all}
            onParamChange={(e) => this.onParamChangeInDrawer(e)}
            checked={
              this.state.drawerOptions.checked[
                this.state.options.selected_thing
              ]
            }
            param_type_radio={
              this.state.options.param_type_radio === "total" ? true : false
            }
          />
          {paramAvailable && this.state.options.thing_wise_param_selection[
            this.state.options.selected_thing
          ]?.length === 0 && this.state.options.device_id_list.length > 0 ? (
            <AntRow>
              <AntCol xs={9} sm={10} md={10} lg={12} xl={5} xxl={4}></AntCol>
              <AntCol
                xs={13}
                sm={14}
                md={14}
                lg={12}
                xl={15}
                xxl={20}
                style={{
                  "margin-top": "-20px",
                  "margin-bottom": 30,
                  color: "red",
                }}
              >
                {this.props.t? this.props.t('please_select_parameters'): 'Please select parameters !'}
              </AntCol>
            </AntRow>
          ) : (
            ""
          )}
          <Form.Item label={this.props.t('report_time_range')} style={{ marginBottom: 0 }}>
            <Form.Item
              name="ddrange"
              style={{
                display: "inline-block",
                width: "200px",
              }}
            >
              {timeRangeValJsx}
            </Form.Item>
            {this.state.options.drva === 10 ? (drval = false) : (drval = true)}
            <Form.Item
              name="custom_range"
              style={{
                display: "inline-block",
                width: "calc(50% - 8px)",
                margin: "0 8px",
              }}
            >
              <AntRangePicker
                showTime={
                  this.state.options.tinterval <= 86400
                    ? { format: "HH:mm" }
                    : false
                }
                format={
                  this.state.options.tinterval === 7 * 86400
                    ? "DD MMM YYYY"
                    : this.state.options.tinterval > 86400
                      ? this.state.options.tinterval === 365 * 86400
                        ? "YYYY"
                        : "MMM YYYY"
                      : "DD MMM YYYY, HH:mm"
                }
                type="range_picker"
                picker={
                  this.state.options.tinterval === 7 * 86400
                    ? "week"
                    : this.state.options.tinterval === 30 * 86400
                      ? "month"
                      : this.state.options.tinterval === 90 * 86400
                        ? "quarter"
                        : this.state.options.tinterval === 365 * 86400
                          ? "year"
                          : "date" 
                }
                defaultValue={[
                  moment
                    .unix(this.state.options.from_time)
                    .tz(this.props.timeZone),
                  moment
                    .unix(this.state.options.upto_time)
                    .tz(this.props.timeZone),
                ]}
                value={[
                  moment
                    .unix(this.state.options.from_time)
                    .tz(this.props.timeZone),
                  moment
                    .unix(this.state.options.upto_time)
                    .tz(this.props.timeZone),
                ]}
                disabled={drval}
                disabledDate={this.disabledDate}
              />
            </Form.Item>
          </Form.Item>
          {this.state.options.dtype === "raw" &&
          this.state.options.drvs === 10 &&
          this.state.options.upto_time - this.state.options.from_time >
            3 * 86400 ? (
            <AntRow>
              <AntCol xs={9} sm={10} md={10} lg={12} xl={5} xxl={4}></AntCol>
              <AntCol
                xs={13}
                sm={14}
                md={14}
                lg={12}
                xl={15}
                xxl={20}
                style={{
                  "margin-top": "-10px",
                  "margin-bottom": 30,
                  color: "red",
                }}
              >
                {this.props.t? this.props.t('please_select_date_range_withing_3_days'): 'Please select date range within 3 days !'}
              </AntCol>
            </AntRow>
          ) : (
            ""
          )}
          {this.state.options.dtype === "cumulative" ? (
            ""
          ) : (
            <Form.Item
            label={this.props.t('report_view')}
              name="r_view"
              rules={[
                {
                  required: true,
                  message: "Please select a data type view",
                },
              ]}
            >
              <AntCheckboxGroup disabled={false} options={downoptions} />
            </Form.Item>
          )}
          {this.state.options.dtype === "cumulative" ? (
            ""
          ) : (
            <Form.Item label= {this.props.t('graph_view_type')} name="graph_view">
              <AntSelect
                disabled={!this.state.options.r_view.includes("graph")}
                style={{ width: 350 }}
                defaultValue={this.state.options.graph_view}
                value={this.state.options.graph_view}
              >
                <AntOption value="one">{this.props.t('all_parameters_in_one_graph')}</AntOption>
                <AntOption value="different">
                {this.props.t('different_parameters_in_different_graphs')}
                </AntOption>
              </AntSelect>
            </Form.Item>
          )}
        </Form>
      </div>
    );
  }
}
