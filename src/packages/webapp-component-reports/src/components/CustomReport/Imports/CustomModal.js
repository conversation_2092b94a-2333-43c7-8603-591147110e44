import React from 'react';
import AntModal from '@datoms/react-components/src/components/AntModal';
import AntButton from '@datoms/react-components/src/components/AntButton';
import CustomForm from './CustomForm';
import SiteWiseCustomForm from './SiteWiseCustomForm';

export default class CustomModal extends React.PureComponent {
	constructor(props) {
		super(props);
		this.state = {
			visible: false,
		};
		this.customCallBack = this.customCallBack.bind(this);
	}

	handleOk() {
		this.props.customizeOnOkRender(this.state.options);
		this.hideModal();
	}

	handleCancel() {
		this.hideModal();
	}

	showModal() {
		this.setState({
			visible: true,
		});
	}

	hideModal() {
		this.setState({
			visible: false,
		});
	}

	customCallBack(values) {
		this.setState({
			options: values,
			disableState:
				values.device_id_list.length === 0 ||
				values.tpara.length === 0 ||
				values.r_view.length === 0 ||
				(values.dtype === 'raw' && values.drva === 10 &&
					values.upto_time - values.from_time > 3 * 86400),
		});
	}

	render() {
		const { visible } = this.state;
		return (
			<AntModal
				title={this.props.t? this.props.t('report_configuration'): "Report Configuration"}
				visible={visible}
				onOk={() => this.handleOk()}
				onCancel={() => this.handleCancel()}
				width={1280}
				footer={[
					<AntButton key="Cancel" onClick={() => this.handleCancel()}>
						{this.props.t? this.props.t('cancel'): "Cancel"}
						{/* Cancel */}
					</AntButton>,
					<AntButton
						key="Ok"
						type="primary"
						disabled={this.state.disableState}
						onClick={() => this.handleOk()}
					>
						{this.props.t? this.props.t('ok'): "Ok"}
						{/* Ok */}
					</AntButton>,
				]}
			>
				<div id="custom_report">
				{this.props.tabKey === "site_wise" ?
					<SiteWiseCustomForm
						plan_description={this.props.plan_description}
						handleCancel={() => this.handleCancel()}
						timeZone={this.props.timeZone}
						timeFormat={this.props.timeFormat}
						type={this.props.type}
						totalThingsData={this.props.totalThingsData}
						prefilledOptions={this.props.options}
						customCallBack={this.customCallBack}
						applicationThings={this.props.applicationThings}
						siteTypes={this.props.siteTypes}
						siteList={this.props.siteList}
					/>
				:
					<CustomForm
						t={this.props.t}
						plan_description={this.props.plan_description}
						handleCancel={() => this.handleCancel()}
						timeZone={this.props.timeZone}
						timeFormat={this.props.timeFormat}
						type={this.props.type}
						totalThingsData={this.props.totalThingsData}
						prefilledOptions={this.props.options}
						customCallBack={this.customCallBack}
						applicationThings={this.props.applicationThings}
						application_id={this.props.application_id}
						client_id={this.props.client_id}
						getAllowedViz={this.props.getAllowedViz}
						vendor_id={this.props.vendor_id}
						logged_in_user_client_id={this.props.logged_in_user_client_id}
					/>
				}
				</div>
			</AntModal>
		);
	}
}
