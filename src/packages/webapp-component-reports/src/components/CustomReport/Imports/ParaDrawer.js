import React from 'react';
import AntDrawer from '@datoms/react-components/src/components/AntDrawer';
import AntButton from '@datoms/react-components/src/components/AntButton';
import AntCheckbox from '@datoms/react-components/src/components/AntCheckbox';
import SearchInput from '@datoms/react-components/src/components/SearchInput';
import AntRow from '@datoms/react-components/src/components/AntRow';
import AntCol from '@datoms/react-components/src/components/AntCol';
import AntModal from '@datoms/react-components/src/components/AntModal';
import noSearchResult from '../../../imgs/no_search_illustration.png';
import _find from 'lodash/find';
export default class ParaDrawer extends React.Component {
	constructor(props) {
		super(props);
	}

	onSearch(e) {
		this.props.onSearch(e);
	}

	onParamChange(e) {
		this.props.onParamChange(e);
	}

	getSearchParameter() {
		let searchedArray = [];
		let totalParams = this.props.param_type_radio ? this.props.all_para : this.props.all_para_thing_wise
		if (totalParams && totalParams.length) {
			totalParams.map((param, idx) => {
				if (
					param?.label.replace(/<[^>]+>/g, '')
						.toLowerCase()
						.includes(this.props.searchString.toLowerCase())
				) {
					searchedArray.push(
						<AntCol
							xs={24}
							sm={12}
							md={12}
							lg={12}
							xl={12}
							xxl={12}
						>
							<AntCheckbox
								key={param?.value + idx}
								onChange={() => this.onParamChange(param?.value)}
								checked={this.props.selected_para?.includes(
									param?.value
								)}
								text={param?.label.replace(/<[^>]+>/g, '')}
							/>
						</AntCol>
					);
				}
			});
		}
		return {
			searchedArray: searchedArray,
		};
	}

	render() {
		let searchNonSearchedArray = this.getSearchParameter();
		let getRenderSearched = '';
		let searchedStringFoundStr =
			this.props.searchString.length > 0 ? (
				<div className="searched-string">
					{
						(this.props.t ? this.props.t('showing') + " " : "Showing ") +
					// 'Showing ' +
						searchNonSearchedArray.searchedArray.length +
						' parameters out of ' + 
						this.props.selected_para.length + " " +
						(this.props.t ? this.props.t('parameters_for_keyword') + " '" : "parameters for Keyword '") +
						// " parameters for Keyword '" +
						this.props.searchString +
						"'"}
				</div>
			) : (
				<div></div>
			);
		getRenderSearched = (
			<div>
				{searchedStringFoundStr}
				<AntRow className="params-list">
					{searchNonSearchedArray.searchedArray &&
					searchNonSearchedArray.searchedArray.length ? (
						searchNonSearchedArray.searchedArray
					) : (
						<div className="no-search-image-with-text">
							<img src={noSearchResult} />
							<div className="no-search-text">
								{this.props.t? this.props.t('sorry_we_could_not_able_to_find_your_search')+ " ": "Sorry, we could not able to find your search.{' '}"}
								{/* Sorry, we could not able to find your search.{' '} */}
								<div>
									{this.props.t? this.props.t('we_would_suggest_to_try') + " ": "We would suggest you to try{' '}"}
									{/* We would suggest you to try{' '} */}
									<b>
										{this.props.t? this.props.t('search_using_different_keyword'): "search using a different keyword. "}
										{/* search using a different keyword. */}
									</b>
								</div>
							</div>
						</div>
					)}
				</AntRow>
			</div>
		);
		let renderParams = (
			<div className="param-list-with-search">
				{this.props.selected_para?.length === 0 ? (
					<div className="total-param-selected red">
						{this.props.t? this.props.t('please_select_parameters'): 'Please select parameters !'}
					</div>
				) : (
					<div className="total-param-selected">
						{this.props.t? this.props.t('selected_parameters'): 'Selected Parameters: '}
						{/* Selected Parameteres: */}
						<span>{this.props.selected_para?.length}</span>
					</div>
				)}
				<div className="total-searched-div">
					<SearchInput
						className="search-params"
						placeholder={this.props.t? this.props.t('search_here'): "Search here"}
						// placeholder="Search here"
						allowClear
						onSearch={(e) => this.onSearch(e)}
						style={{ width: 145 }}
						value={this.props.searchString}
					/>
					<br />
					{this.props.searchString.length > 0 ? (
						''
					) : (
						<AntCheckbox
							className="select-all-item"
							indeterminate={this.props.indeterminate}
							onChange={this.props.onCheckAllChange}
							checked={this.props.checked}
							text={this.props.t? this.props.t('select_all'): 'Select all'}
							// text={'Select all'}
						/>
					)}
					{getRenderSearched}
				</div>
			</div>
		);
		if (window.innerWidth < 720) {
			return (
				<AntModal
					className="param-modal"
					title={this.props.t? this.props.t('choose_your_parameters'): "Choose your parameters"}
					// title="Choose your parameters"
					visible={this.props.visible}
					onOk={() => this.props.onClose('ok')}
					onCancel={() => this.props.onClose()}
					style-={{ border: '1px solid' }}
					footer={[
						<AntButton
							type="primary"
							key="back"
							onClick={() => this.props.onClose('ok')}
						>
							{this.props.t? this.props.t('ok'): 'Ok'}
						</AntButton>,
					]}
				>
					{renderParams}
				</AntModal>
			);
		} else {
			return (
				<AntDrawer
					className="check-drawer"
					title={this.props.t? this.props.t('choose_your_parameters'): "Choose your parameters"}
					// title="Choose your parameters"
					width={567}
					onClose={() => this.props.onClose()}
					visible={this.props.visible}
					bodyStyle={{ paddingBottom: 80 }}
					footer={
						<div
							style={{
								textAlign: 'right',
							}}
						>
							<AntButton
								onClick={() => this.props.onClose('ok')}
								type="primary"
							>
								Ok
							</AntButton>
						</div>
					}
				>
					{renderParams}
				</AntDrawer>
			);
		}
	}
}
