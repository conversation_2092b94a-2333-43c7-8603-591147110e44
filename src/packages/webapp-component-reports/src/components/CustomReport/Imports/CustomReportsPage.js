/*Libs*/
import React from "react";
import CustomForm from "./CustomForm";
import SiteWiseCustomForm from "./SiteWiseCustomForm";
import _find from "lodash/find";
import AntButton from "@datoms/react-components/src/components/AntButton";
import AntRow from "@datoms/react-components/src/components/AntRow";
import AntCol from "@datoms/react-components/src/components/AntCol";
import ArrowLeftOutlined from "@ant-design/icons/ArrowLeftOutlined";

/**
 * Custom Reports Page
 **/
export default class CustomReportsPage extends React.PureComponent {
  modalRef = React.createRef();
  invisibleReportRef = React.createRef();

  constructor(props) {
    super(props);
    this.state = {
      downloaderReportConfig: undefined,
      disableState: false,
    };
    this.customCallBack = this.customCallBack.bind(this);
    this.downloadModalCallback = this.downloadModalCallback.bind(this);
    this.onExportReady = this.onExportReady.bind(this);
  }

  customFormFunction(values) {
    this.props.customFormFunction(values);
  }

  formRef = null;
  fileFormat = null;

  onFormInstanceInit = (formRef) => {
    this.formRef = formRef;
  };

  reportDownload = () => {
    this.modalRef.current.showModal();
  };

  customCallBack(values) {
    const rawMaxTime = this.props.tabKey === "site_wise" ? 3 * 86400 : 3 * 86400;
    this.setState({
      options: values,
      disableState:
        values.device_id_list.length === 0 ||
        values.tpara.length === 0 ||
        values.r_view.length === 0 ||
        (values.dtype === "raw" && values.drva === 10 &&
          values.upto_time - values.from_time > rawMaxTime),
    });
  }

  getSeletedThingDetails() {
    let seletedThingDetails = [];
    if (Array.isArray(this.state.options.device_id_list)) {
      this.state.options.device_id_list.map((things) => {
        if (
          this.props.totalThingsData &&
          Array.isArray(this.props.totalThingsData.things)
        ) {
          seletedThingDetails.push(
            _find(this.props.totalThingsData.things, {
              id: things,
            }),
          );
        }
      });
    }
    return seletedThingDetails;
  }

  downloadModalCallback(fileFormat) {
    if (fileFormat.includes("csv")) {
      this.invisibleReportRef.current.exportCSV(null, {});
    }

    if (fileFormat.includes("xlsx")) {
      this.invisibleReportRef.current.exportXLSX(null, {
        maxCellMergeCount: 20,
      });
    }

    if (fileFormat.includes("pdf")) {
      this.invisibleReportRef.current.exportPDF({
        pdf_orientation: "l",
        header: {
          left: {
            text: this.props.application_name.toUpperCase(),
            fontType: "italics",
          },
          right: {
            text: "Company Name",
          },
          fontSize: 14,
        },
      });
    }
  }

  onExportReady() {
    let { autoDownloadFormat } = this.props;
    if (autoDownloadFormat) {
      this.downloadModalCallback(autoDownloadFormat);
    }
  }

  goBackPage() {
    if (this.props.application_name) {
      let getApplicationSlug = this.props.application_name.toLowerCase();
      getApplicationSlug = getApplicationSlug.replace(/ /g, "-");
      this.props.history.push("/" + getApplicationSlug + "/reports");
    } else {
      this.props.history.push("/reports");
    }
  }

  render() { 
    return (
      <div className="total-custom-report-body">
        <div className="header-class">
          <ArrowLeftOutlined onClick={() => this.goBackPage()} />
            {this.props.t('select_your_requirements')}
          {/* Select Your Requirements */}
        </div>
        {this.props.tabKey === "site_wise" ? (
          <SiteWiseCustomForm
            t={this.props.t}
            plan_description={this.props.plan_description}
            timeZone={this.props.timeZone}
            timeFormat={this.props.timeFormat}
            type={this.props.type}
            totalThingsData={this.props.totalThingsData}
            prefilledOptions={undefined}
            onFormInstanceInit={this.onFormInstanceInit}
            customCallBack={this.customCallBack}
            applicationThings={this.props.applicationThings}
			      client_id={this.props.client_id}
            siteTypes={this.props.siteTypes}
            siteList={this.props.siteList}
          />
        ) : (
          <CustomForm
            t={this.props.t}
            plan_description={this.props.plan_description}
            timeZone={this.props.timeZone}
            timeFormat={this.props.timeFormat}
            client_id={this.props.client_id}
            type={this.props.type}
            totalThingsData={this.props.totalThingsData}
            prefilledOptions={undefined}
            onFormInstanceInit={this.onFormInstanceInit}
            customCallBack={this.customCallBack}
            applicationThings={this.props.applicationThings}
            application_id={this.props.application_id}
            getAllowedViz={this.props.getAllowedViz}
            vendor_id={this.props.vendor_id}
						logged_in_user_client_id={this.props.logged_in_user_client_id}
          />
        )}

        <AntRow className="report-buttons">
          <AntCol xs={9} sm={10} md={10} lg={12} xl={5} xxl={4}></AntCol>
          <AntCol xs={15} sm={14} md={14} lg={12} xl={15} xxl={20}>
            <AntButton
              className="report-buttons-generate"
              onClick={() => this.props.reportGenerate(this.state.options)}
              disabled={this.state.disableState}
              type="primary"
            >
              {" "}
              {this.props.t('generate_report')}
              {/* Generate Report */}
            </AntButton>
          </AntCol>
        </AntRow>
      </div>
    );
  }
}
