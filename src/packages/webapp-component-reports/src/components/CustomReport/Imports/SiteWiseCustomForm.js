/*Libs*/
import React from "react";
import moment from "moment-timezone";
import {
  getParameters,
  getParametersAsPerThings,
  getAggrPeriodAsPerCat,
} from "../DataHandeling/parameterManipulation";
import ParaDrawer from "./ParaDrawer";
import { Form } from "antd";
import AntRow from "@datoms/react-components/src/components/AntRow";
import AntCol from "@datoms/react-components/src/components/AntCol";
import AntSelect from "@datoms/react-components/src/components/AntSelect";
import AntOption from "@datoms/react-components/src/components/AntOption";
import AntButton from "@datoms/react-components/src/components/AntButton";
import AntRadio from "@datoms/react-components/src/components/AntRadio";
import AntRadioGroup from "@datoms/react-components/src/components/AntRadioGroup";
import AntCheckbox from "@datoms/react-components/src/components/AntCheckbox";
import AntCheckboxGroup from "@datoms/react-components/src/components/AntCheckboxGroup";
import AntRangePicker from "@datoms/react-components/src/components/AntRangePicker";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import _uniqBy from "lodash/uniqBy";
import _find from "lodash/find";
import _filter from "lodash/filter";
import _orderBy from "lodash/orderBy";
import { TimeFormatter } from "../../../../../dg-monitoring-views/src/js/data_handling/TimeFormatting";

export default class SiteWiseCustomForm extends React.Component {
  constructor(props) {
    super(props);
    this.formItemLayout = {
      labelCol: { sm: 24, md: 24, lg: 12, xl: 5, xxl: 4 },
      wrapperCol: { sm: 24, md: 24, lg: 12, xl: 15, xxl: 13 },
      colon: false,
    };

    this.formRef = React.createRef();
    this.compare_item = this.compare_item.bind(this);
    this.onCheckAllChange = this.onCheckAllChange.bind(this);
    this.onValuesChange = this.onValuesChange.bind(this);
    this.disabledDate = this.disabledDate.bind(this);

    let initialDeviceIDList = [],
      deviceArray = [],
      totalInterval = [3600, 86400],
      paramKeys = [],
      allParams = [],
      allThingWiseParamKeys = {},
      thingWiseParamKeys = {},
      selectAllChecked = {};
    const { totalThingsData, siteTypes } = this.props;
    const getTotalSiteTypes = this.getSiteTypesOptions(siteTypes);
    const siteList = this.filterSitesTypeWise();
    let options = {
      checked_all: true,
      checked: selectAllChecked,
      visible: false,
      dtype: "average",
      tinterval: totalInterval[0],
      drva: 1,
      graph_view: "different",
      total_thing: deviceArray,
      totalInterval: totalInterval,
      selectedSiteTypes: getTotalSiteTypes?.length
        ? getTotalSiteTypes[0]["id"]
        : [],
      selectedSites: [],
      siteList: siteList,
      device_id_list: initialDeviceIDList,
      tpara: paramKeys,
      selected_thing: initialDeviceIDList?.[0],
      all_params: allParams,
      all_thing_wise_param_keys: allThingWiseParamKeys,
      thing_wise_param_selection: thingWiseParamKeys,
      from_time: moment
        .tz(this.props.timeZone)
        .subtract(3, "days")
        .startOf("day")
        .unix(),
      upto_time: moment.tz(this.props.timeZone).unix(),
      r_view: ["grid", "graph"],
    };
    let getOptions = {};
    if (props.prefilledOptions) {
      getOptions = props.prefilledOptions;
    } else {
      getOptions = options;
    }
    this.state = {
      options: getOptions,
      searchString: "",
      drawerOptions: props.prefilledOptions
        ? JSON.parse(JSON.stringify(props.prefilledOptions))
        : JSON.parse(JSON.stringify(options)),
    };
    moment.tz.setDefault(props.timeZone);
    this.props.customCallBack(getOptions);
  }

  filterSitesTypeWise(siteType) {
    const { siteList } = this.props;
    const updatedSiteList = !isNaN(parseInt(siteType))
      ? _filter(siteList, { site_type: siteType })
      : siteList;
    const siteListArr = [];
    if (updatedSiteList?.length) {
      updatedSiteList.map((site) => {
        siteListArr.push({
          id: site.id,
          label: site.name,
          value: site.id,
        });
      });
    }
    return siteListArr;
  }

  onSearch(e) {
    this.setState({
      searchString: e,
    });
  }

  compare_item(a, b) {
    if (a.label < b.label) {
      return -1;
    } else if (a.label > b.label) {
      return 1;
    } else {
      return 0;
    }
  }

  onlyUnique(value, index, self) {
    return self.indexOf(value) === index;
  }

  onFinish = (values) => {};

  onFinishFailed = (errorInfo) => {};

  reportGenerate = () => {
    this.props.reportGenerate();
  };

  onFormInstanceInit = (formRef) => {
    const { onFormInstanceInit } = this.props;
    if (onFormInstanceInit) {
      onFormInstanceInit(formRef);
    }
    this.formRef = formRef;
  };

  componentDidMount() {
    const { onFormInstanceInit } = this.props;
    if (onFormInstanceInit) {
      onFormInstanceInit(this.formRef);
    }
  }

  onValuesChange(cf, af) {
    const deviceArray = [],
      deviceIds = [];
    let selectAllChecked = this.state.options.checked;
    const selectedSiteType = af.site_type
      ? af.site_type
      : this.props.prefilledOptions.selectedSiteTypes;
    const updatedSiteList = this.filterSitesTypeWise(selectedSiteType);
    const selectedSite = cf.site_type
      ? []
      : af.sites
        ? af.sites
        : this.state.options.selectedSites;
    this.formRef.current.setFieldsValue({
      sites: selectedSite,
    });
    if (selectedSite?.length) {
      const filteredThings = _filter(
        this.props.totalThingsData.things,
        (thing) => {
          return selectedSite.includes(thing.site_id);
        },
      );
      if (filteredThings?.length) {
        filteredThings.map((thing) => {
          if (!selectAllChecked[thing.id]) {
            selectAllChecked[thing.id] = true;
          }
          deviceArray.push({
            id: thing.id,
            label: thing.name,
            value: thing.id,
          });
          deviceIds.push(thing.id);
        });
      }
    }
    let d_type = af.data_type ? af.data_type : "average";
    let totalInterval = [3600, 86400];
    let t_interval =
      af.data_type === "raw"
        ? 0
        : af.interval
          ? af.interval
          : this.props.prefilledOptions && this.props.prefilledOptions.tinterval
            ? this.props.prefilledOptions.tinterval
            : totalInterval[0];
    let graph_view = af.graph_view
      ? af.graph_view
      : this.props.prefilledOptions && this.props.prefilledOptions.graph_view
        ? this.props.prefilledOptions.graph_view
        : "different";
    let drv =
      af.data_type === "raw"
        ? af.ddrange === 1 || af.ddrange === 10
          ? af.ddrange
          : this.props.prefilledOptions && this.props.prefilledOptions.drva
            ? this.props.prefilledOptions.drva
            : 1
        : af.ddrange
          ? af.ddrange
          : this.props.prefilledOptions && this.props.prefilledOptions.drva
            ? this.props.prefilledOptions.drva
            : 1;
    let customRange = af.custom_range?.length ? af.custom_range : [];
    let rView = af.r_view ? af.r_view : [];
    let filteredThings = [];
    if (deviceIds && deviceIds.length) {
      deviceIds.map((ids) => {
        if (_find(this.props.totalThingsData.things, { id: ids })) {
          filteredThings.push(
            _find(this.props.totalThingsData.things, {
              id: ids,
            }),
          );
        }
      });
    }
    let selectedThing = this.state.options.selected_thing;
    if (deviceIds && deviceIds.length) {
      selectedThing = deviceIds.includes(this.state.options.selected_thing)
        ? this.state.options.selected_thing
        : deviceIds[0];
    }
    let allSelectAll = af.select_all ? af.select_all : false;
    let selectAll = JSON.parse(JSON.stringify(selectAllChecked));
    selectAllChecked[selectedThing] = af.select_all ? af.select_all : false;
    let allParams = getParameters(filteredThings, deviceIds, af.data_type);
    let allThingWiseParamKeys = getParametersAsPerThings(
      filteredThings,
      deviceIds,
      af.data_type,
    );

    let tParaValue = this.state.options.tpara;
    let thingWiseParamKeys = {};
    if (cf.data_type || cf.site_type || cf.sites) {
      tParaValue = allParams.map((params) => {
        return params.value;
      });
      if (allThingWiseParamKeys && Object.keys(allThingWiseParamKeys).length) {
        Object.keys(allThingWiseParamKeys).map((keys) => {
          if (!thingWiseParamKeys[keys]) {
            thingWiseParamKeys[keys] = [];
          }
          if (allThingWiseParamKeys && allThingWiseParamKeys[keys].length) {
            allThingWiseParamKeys[keys].map((param) => {
              thingWiseParamKeys[keys].push(param.value);
            });
          }
        });
      }
    } else {
      thingWiseParamKeys = this.state.options.thing_wise_param_selection;
      tParaValue = this.state.options.tpara;
    }
    if (deviceIds && deviceIds.length) {
      allSelectAll = tParaValue.length === allParams.length ? true : false;
      selectAll[selectedThing] =
        thingWiseParamKeys[selectedThing]?.length ===
        allThingWiseParamKeys[selectedThing]?.length
          ? true
          : false;
    } else {
      allSelectAll = false;
      selectAll[selectedThing] = false;
    }
    let fromTime = this.state.fromTime;
    let uptoTime = this.state.uptoTime;
    if (af.data_type === "raw") {
      if (drv === 1) {
        fromTime = moment.tz(this.props.timeZone).subtract(1, "days").unix();
        uptoTime = moment.tz(this.props.timeZone).unix();
      } else if (drv === 10) {
        fromTime =
          customRange[0] === undefined
            ? moment.tz(this.props.timeZone).subtract(1, "days").unix()
            : moment(customRange[0], "X")
                .tz(this.props.timeZone)
                .seconds(0)
                .unix();
        uptoTime =
          customRange[1] === undefined
            ? moment.tz(this.props.timeZone).unix()
            : moment(customRange[1], "X")
                .tz(this.props.timeZone)
                .seconds(59)
                .unix();
      }
    } else if (drv === 1) {
      fromTime = moment
        .tz(this.props.timeZone)
        .subtract(7, "days")
        .startOf("day")
        .unix();
      uptoTime = moment.tz(this.props.timeZone).unix();
    } else if (drv === 2) {
      fromTime = moment.tz(this.props.timeZone).startOf("week").unix();
      uptoTime = moment.tz(this.props.timeZone).unix();
    } else if (drv === 3) {
      fromTime = moment
        .tz(this.props.timeZone)
        .subtract(7, "days")
        .startOf("week")
        .unix();
      uptoTime = moment
        .tz(this.props.timeZone)
        .subtract(7, "days")
        .endOf("week")
        .unix();
    } else if (drv === 4) {
      fromTime = moment
        .tz(this.props.timeZone)
        .subtract(30, "days")
        .startOf("day")
        .unix();
      uptoTime = moment.tz(this.props.timeZone).unix();
    } else if (drv === 5) {
      fromTime = moment
        .tz(this.props.timeZone)
        .subtract(1, "month")
        .startOf("month")
        .unix();
      uptoTime = moment
        .tz(this.props.timeZone)
        .subtract(1, "month")
        .endOf("month")
        .unix();
    } else if (drv === 6) {
      fromTime = moment.tz(this.props.timeZone).startOf("month").unix();
      uptoTime = moment.tz(this.props.timeZone).unix();
    } else if (drv === 7) {
      fromTime = moment.tz(this.props.timeZone).startOf("quarter").unix();
      uptoTime = moment.tz(this.props.timeZone).endOf("quarter").unix();
    } else if (drv === 8) {
      fromTime = moment
        .tz(this.props.timeZone)
        .subtract(1, "quarter")
        .startOf("quarter")
        .unix();
      uptoTime = moment
        .tz(this.props.timeZone)
        .subtract(1, "quarter")
        .endOf("quarter")
        .unix();
    } else if (drv === 9) {
      fromTime = moment.tz(this.props.timeZone).startOf("year").unix();
      uptoTime = moment.tz(this.props.timeZone).unix();
    } else if (drv === 10) {
      fromTime =
        customRange[0] === undefined
          ? moment.tz(this.props.timeZone).subtract(7, "days").unix()
          : moment(customRange[0], "X")
              .tz(this.props.timeZone)
              .seconds(0)
              .unix();
      uptoTime =
        customRange[1] === undefined
          ? moment.tz(this.props.timeZone).unix()
          : moment(customRange[1], "X")
              .tz(this.props.timeZone)
              .seconds(59)
              .unix();
    }
    const options = {
      tpara: tParaValue,
      thing_wise_param_selection: thingWiseParamKeys,
      table_type: ["summary_table", "detailed_table"],
      total_thing: deviceArray,
      device_id_list: deviceIds,
      dtype: d_type,
      drva: drv,
      selectedSiteTypes: selectedSiteType,
      selectedSites: selectedSite,
      siteList: updatedSiteList,
      checked: selectAll,
      checked_all: allSelectAll,
      totalInterval: totalInterval,
      idis: d_type === "raw" ? true : false,
      tinterval: t_interval,
      graph_view: graph_view,
      all_params: allParams,
      all_thing_wise_param_keys: allThingWiseParamKeys,
      from_time: fromTime,
      upto_time: uptoTime,
      r_view: rView,
      selected_thing: selectedThing,
      param_type_radio: "thing_wise",
      selectedDataModel: [],
    };

    this.setState(
      {
        options: options,
        drawerOptions: JSON.parse(JSON.stringify(options)),
      },
      () => {
        this.props.customCallBack(this.state.options);
      },
    );
  }

  getSiteTypesOptions(siteTypes) {
    let sityType = [
      {
        id: "all",
        label: "All",
        value: "all",
      },
    ];
    if (siteTypes?.length) {
      siteTypes.map((site) => {
        sityType.push({
          id: site.id,
          label: site.name,
          value: site.id,
        });
      });
    }
    return sityType;
  }

  getDeviceList(deviceArray) {
    if (deviceArray && deviceArray.length) {
      deviceArray = deviceArray.sort(this.compare_item);
    }
    return deviceArray;
  }

  aggrIntervalFunc() {
    if (this.state.options.dtype === "raw") {
      return (
        <AntSelect defaultValue={86400} disabled>
          <AntOption value={86400}></AntOption>
        </AntSelect>
      );
    } else if (this.state.options.dtype === "average") {
      return (
        <AntSelect defaultValue={this.state.options.tinterval} disabled={false}>
          {this.state.options?.totalInterval?.length &&
            this.state.options.totalInterval.map((interval) => {
              return (
                <AntOption value={interval}>
                  {interval === 3600
                    ? "Hourly"
                    : interval === 86400
                      ? "Daily"
                      : interval}
                </AntOption>
              );
            })}
        </AntSelect>
      );
    }
  }

  getJsx2() {
    let allpara = [];
    if (
      this.state.options.all_thing_wise_param_keys[
        this.state.options.selected_thing
      ]
    ) {
      let l = Math.min(
        8,
        this.state.options.all_thing_wise_param_keys[
          this.state.options.selected_thing
        ].length,
      );
      for (let i = 0; i < l; i++) {
        allpara.push(
          this.state.options.all_thing_wise_param_keys[
            this.state.options.selected_thing
          ][i],
        );
      }
    }

    return allpara;
  }

  allParamKeys() {
    let myjsx2 = this.getJsx2();
    let defPara = myjsx2.map((p) => {
      return p.value;
    });
    return defPara;
  }

  getJsx1() {
    let allpara = [];
    if (this.state.options.all_params) {
      let l = Math.min(8, this.state.options.all_params.length);
      for (let i = 0; i < l; i++) {
        allpara.push(this.state.options.all_params[i]);
      }
    }

    return allpara;
  }

  allParamKeysTotal() {
    let myjsx1 = this.getJsx1();
    let defPara = myjsx1.map((p) => {
      return p.value;
    });
    return defPara;
  }

  onCheckAllChange(e) {
    let defPara = this.state.options.all_params.map((p) => {
      return p.value;
    });
    let defThingWisePara = {};
    Object.keys(this.state.options.all_thing_wise_param_keys).map((keys) => {
      if (!defThingWisePara[keys]) {
        defThingWisePara[keys] = [];
      }
      if (
        this.state.options.all_thing_wise_param_keys &&
        this.state.options.all_thing_wise_param_keys[keys].length
      ) {
        this.state.options.all_thing_wise_param_keys[keys].map((param) => {
          defThingWisePara[keys].push(param.value);
        });
      }
    });
    let { options } = this.state;
    options["checked_all"] = e.target.checked;
    options["checked"][this.state.options.selected_thing] = e.target.checked;
    options["tpara"] = e.target.checked ? defPara : [];
    options["thing_wise_param_selection"][this.state.options.selected_thing] = e
      .target.checked
      ? defThingWisePara[this.state.options.selected_thing]
      : [];
    this.setState(
      {
        options: options,
        drawerOptions: JSON.parse(JSON.stringify(options)),
      },
      () => {
        this.props.customCallBack(this.state.options);
      },
    );
  }
  onCheckAllChangeInDrawer(e) {
    let defPara = this.state.drawerOptions.all_params.map((p) => {
      return p.value;
    });
    let defThingWisePara = {};
    Object.keys(this.state.drawerOptions.all_thing_wise_param_keys).map(
      (keys) => {
        if (!defThingWisePara[keys]) {
          defThingWisePara[keys] = [];
        }
        if (
          this.state.drawerOptions.all_thing_wise_param_keys &&
          this.state.drawerOptions.all_thing_wise_param_keys[keys].length
        ) {
          this.state.drawerOptions.all_thing_wise_param_keys[keys].map(
            (param) => {
              defThingWisePara[keys].push(param.value);
            },
          );
        }
      },
    );
    let { drawerOptions } = this.state;
    drawerOptions["checked_all"] = e.target.checked;
    drawerOptions["checked"][this.state.options.selected_thing] =
      e.target.checked;
    drawerOptions["tpara"] = e.target.checked ? defPara : [];
    drawerOptions["thing_wise_param_selection"][
      this.state.options.selected_thing
    ] = e.target.checked
      ? defThingWisePara[this.state.options.selected_thing]
      : [];
    this.setState(
      {
        drawerOptions: JSON.parse(JSON.stringify(drawerOptions)),
      },
      () => {
        this.props.customCallBack(this.state.options);
      },
    );
  }

  onParamChangeInDrawer(value) {
    let tparaValue = this.state.drawerOptions.tpara.includes(value)
      ? this.state.drawerOptions.tpara.filter((x) => x !== value)
      : [...this.state.drawerOptions.tpara, value];
    let thingWisePara = this.state.drawerOptions.thing_wise_param_selection;
    thingWisePara[this.state.options.selected_thing] =
      this.state.drawerOptions.thing_wise_param_selection?.[
        this.state.options.selected_thing
      ]?.includes(value)
        ? this.state.drawerOptions.thing_wise_param_selection?.[
            this.state.options.selected_thing
          ]?.filter((x) => x !== value)
        : [
            ...this.state.drawerOptions.thing_wise_param_selection?.[
              this.state.options.selected_thing
            ],
            value,
          ];
    let selectedChecked = this.state.drawerOptions.checked;
    selectedChecked[this.state.options.selected_thing] =
      thingWisePara.length ===
      this.state.drawerOptions.all_thing_wise_param_keys[
        this.state.options.selected_thing
      ].length
        ? true
        : false;
    let { drawerOptions } = this.state;
    drawerOptions["checked_all"] =
      tparaValue.length === this.state.drawerOptions.all_params.length
        ? true
        : false;
    drawerOptions["checked"] = selectedChecked;
    drawerOptions["tpara"] = tparaValue;
    drawerOptions["thing_wise_param_selection"] = thingWisePara;
    this.setState(
      {
        drawerOptions: JSON.parse(JSON.stringify(drawerOptions)),
      },
      () => {
        this.props.customCallBack(this.state.drawerOptions);
      },
    );
  }
  onDrawerSetOptions(command) {
    let stateObject =
      command === "ok"
        ? {
            options: JSON.parse(JSON.stringify(this.state.drawerOptions)),
          }
        : {
            drawerOptions: JSON.parse(JSON.stringify(this.state.options)),
          };
    this.setState(stateObject, () => {
      if (command === "ok") {
        this.props.customCallBack(this.state.options);
      }
    });
  }
  onParamChange(value) {
    let tparaValue = this.state.options.tpara.includes(value)
      ? this.state.options.tpara.filter((x) => x !== value)
      : [...this.state.options.tpara, value];
    let thingWisePara = this.state.options.thing_wise_param_selection;
    thingWisePara[this.state.options.selected_thing] =
      this.state.options.thing_wise_param_selection?.[
        this.state.options.selected_thing
      ]?.includes(value)
        ? this.state.options.thing_wise_param_selection?.[
            this.state.options.selected_thing
          ]?.filter((x) => x !== value)
        : [
            ...this.state.options.thing_wise_param_selection?.[
              this.state.options.selected_thing
            ],
            value,
          ];
    let selectedChecked = this.state.options.checked;
    selectedChecked[this.state.options.selected_thing] =
      thingWisePara.length ===
      this.state.options.all_thing_wise_param_keys[
        this.state.options.selected_thing
      ].length
        ? true
        : false;
    let { options } = this.state;
    options["checked_all"] =
      tparaValue.length === this.state.drawerOptions.all_params.length
        ? true
        : false;
    options["checked"] = selectedChecked;
    options["tpara"] = tparaValue;
    options["thing_wise_param_selection"] = thingWisePara;
    this.setState(
      {
        options: options,
        drawerOptions: JSON.parse(JSON.stringify(options)),
      },
      () => {
        this.props.customCallBack(this.state.options);
      },
    );
  }

  showDrawer() {
    this.setState({
      visible: true,
      searchString: "",
    });
  }

  onClose(command) {
    this.onDrawerSetOptions(command);
    this.setState({
      visible: false,
    });
  }

  disabledDate(current) {
    if (this.props.plan_description) {
      return (
        (current &&
          current <
            moment()
              .startOf("Month")
              .subtract(
                this.props.plan_description
                  .maximum_pre_defined_report_time_aggr_allowed,
                "month",
              )
              .startOf("day")) ||
        current >= moment.tz(this.props.timeZone).endOf("day")
      );
    } else {
      return (
        (current &&
          current <
            moment().startOf("Month").subtract(12, "month").startOf("day")) ||
        current >= moment.tz(this.props.timeZone).endOf("day")
      );
    }
  }

  getTotalTimeRangeValue() {
    return this.state.options.dtype === "raw" ? (
      <AntSelect
        style={{ width: 200 }}
        defaultValue={this.state.options.drva}
        value={this.state.options.drva}
      >
        <AntOption value={1}>Last 24 Hrs</AntOption>
        <AntOption value={10}>Custom (Maximum 3 days)</AntOption>
      </AntSelect>
    ) : (
      <AntSelect
        style={{ width: 200 }}
        defaultValue={this.state.options.drva}
        value={this.state.options.drva}
      >
        <AntOption value={1}>Last 7 days</AntOption>
        <AntOption value={2}>This week</AntOption>
        <AntOption value={3}>Last week</AntOption>
        <AntOption value={4}>Last 30 days</AntOption>
        <AntOption value={5}>Last month</AntOption>
        <AntOption value={6}>This month</AntOption>
        <AntOption value={7}>
          {"This quarter (" +
            moment.tz(this.props.timeZone).startOf("quarter").format("MMM") +
            "-" +
            moment.tz(this.props.timeZone).endOf("quarter").format("MMM") +
            ")"}
        </AntOption>
        <AntOption value={8}>
          {"Last quarter (" +
            moment
              .tz(this.props.timeZone)
              .subtract(1, "quarter")
              .startOf("quarter")
              .format("MMM") +
            "-" +
            moment
              .tz(this.props.timeZone)
              .subtract(1, "quarter")
              .endOf("quarter")
              .format("MMM") +
            ")"}
        </AntOption>
        <AntOption value={9}>This year</AntOption>
        <AntOption value={10}>Custom</AntOption>
      </AntSelect>
    );
  }

  thingClickForParameters(e) {
    const { options } = this.state;
    options["checked"][e] =
      options.thing_wise_param_selection[e]?.length ===
      options.all_thing_wise_param_keys[e]?.length
        ? true
        : false;
    options["checked_all"] =
      options.tpara.length === options.all_params.length ? true : false;
    options["selected_thing"] = e;
    this.setState({
      options: options,
    });
  }

  render() {
    const { siteTypes } = this.props;
    let aggIntervalJsx = this.aggrIntervalFunc();
    let timeRangeValJsx = this.getTotalTimeRangeValue();
    let myjsx2 = this.getJsx2();
    let myjsx1 = this.getJsx1();
    let defPara = this.allParamKeys();
    let downoptions = [
      { label: "Grid", value: "grid" },
      { label: "Graph", value: "graph" },
    ];
    let drval = false;
    let allParamsCheckBox = [];
    if (myjsx2 && myjsx2.length) {
      myjsx2.map((params, idx) => {
        allParamsCheckBox.push(
          <AntCol xs={24} sm={12} md={8} lg={6} xl={6} xxl={6}>
            <AntTooltip title={params.label.replace(/<[^>]+>/g, "")}>
              {params.value ? (
                <AntCheckbox
                  key={params.value + idx}
                  onChange={() => this.onParamChange(params.value)}
                  checked={this.state.options.thing_wise_param_selection?.[
                    this.state.options.selected_thing
                  ]?.includes(params.value)}
                  text={params.label.replace(/<[^>]+>/g, "")}
                />
              ) : (
                ""
              )}
            </AntTooltip>
          </AntCol>,
        );
      });
    }

    let thingSelectionTabs = [];
    if (this.state.options?.device_id_list?.length) {
      this.state.options.device_id_list.map((things) => {
        thingSelectionTabs.push(
          <AntTooltip
            title={
              _find(this.state.options.total_thing, {
                value: things,
              })?.label
            }
          >
            <div
              className={
                "hellip each-thing-tab " +
                (things === this.state.options.selected_thing ? "active" : "")
              }
              onClick={() => this.thingClickForParameters(things)}
            >
              {
                _find(this.state.options.total_thing, {
                  value: things,
                })?.label
              }
            </div>
          </AntTooltip>,
        );
      });
    }
    let selectedPara =
      this.state.options.thing_wise_param_selection?.[
        this.state.options.selected_thing
      ];
    let paramCount =
      selectedPara?.length === 0
        ? ""
        : " (" +
          (selectedPara?.length > 10
            ? selectedPara?.length
            : "0" + selectedPara?.length) +
          " Parameter Selected)";
    return (
      <div id="container">
        <Form
          className="form-component"
          ref={this.formRef}
          {...this.formItemLayout}
          onValuesChange={this.onValuesChange}
          initialValues={{
            site_type: this.state.options.selectedSiteTypes,
            sites: this.state.options.selectedSites,
            data_type: this.state.options.dtype,
            para: 1,
            r_view: this.state.options.r_view,
            select_all:
              this.state.options.checked[this.state.options.selected_thing],
            dpara: defPara,
            m_stat: 1,
            param_type_radio: "thing_wise",
            selectedDataModel: [],
          }}
        >
          <Form.Item label="Select site type" name="site_type">
            <AntSelect options={this.getSiteTypesOptions(siteTypes)} />
          </Form.Item>
          <Form.Item
            label="Select sites"
            name="sites"
            rules={[
              {
                required: true,
                message: "Please select at least one site!",
              },
            ]}
          >
            <AntSelect
              mode="multiple"
              options={this.state.options.siteList}
              optionFilterProp="label"
              maxCount={3}
            />
          </Form.Item>
          <AntRow>
            {" "}
            <AntCol xs={9} sm={10} md={10} lg={12} xl={5} xxl={4}></AntCol>
            <AntCol
              xs={13}
              sm={14}
              md={14}
              lg={12}
              xl={15}
              xxl={20}
              style={{
                "font-weight": "normal",
                "font-size": 12,
                color: "#808080",
                "font-style": "italic",
                "margin-top": "-15px",
                "margin-bottom": "10px",
              }}
            >
              Maximum 3 sites can be selected
            </AntCol>
          </AntRow>

          <Form.Item label="Select dataformat" name="data_type">
            <AntRadioGroup name="radiogroup">
              <AntRadio value={"raw"}>Raw</AntRadio>
              <AntRadio value={"average"}>Average</AntRadio>
            </AntRadioGroup>
          </Form.Item>
          <Form.Item label="Select aggregation period" name="interval">
            {aggIntervalJsx}
          </Form.Item>
          {thingSelectionTabs?.length === 0 ? (
            ""
          ) : (
            <Form.Item
              label="Select Parameters"
              name="para"
              className="param-selection-dropdown"
              required={true}
            >
              <AntRow>
                <AntCol xs={24} sm={14} md={14} lg={12} xl={15} xxl={20}>
                  <div className="station-tab">{thingSelectionTabs}</div>
                </AntCol>
              </AntRow>
            </Form.Item>
          )}
          {allParamsCheckBox.length ? (
            this.state.options.all_thing_wise_param_keys[
              this.state.options.selected_thing
            ].length > 0 ? (
              <Form.Item
                label=" "
                name="select_all"
                className="select-all-item"
              >
                <AntCheckbox
                  onChange={this.onCheckAllChange}
                  checked={
                    this.state.options.checked[
                      this.state.options.selected_thing
                    ]
                  }
                  text={
                    <span>
                      Select all{" "}
                      <span
                        style={{
                          "margin-left": 20,
                          "font-weight": "normal",
                          "font-size": 12,
                          color: "#808080",
                          "font-style": "italic",
                        }}
                      >
                        {paramCount}
                      </span>
                    </span>
                  }
                />
              </Form.Item>
            ) : (
              ""
            )
          ) : (
            ""
          )}
          <Form.Item label=" " name="dpara" className="all-params">
            <AntRow>{allParamsCheckBox}</AntRow>
          </Form.Item>
          {this.state.options.all_thing_wise_param_keys[
            this.state.options.selected_thing
          ]?.length > 8 ? (
            <Form.Item label=" " className="show-more-btn">
              <div
                onClick={() => this.showDrawer()}
                className="show-more-button-1"
              >
                Show more {">"}
              </div>
            </Form.Item>
          ) : (
            <div className="show-more-button-1"></div>
          )}
          <ParaDrawer
            t={this.props.t}
            onSearch={(e) => this.onSearch(e)}
            searchString={this.state.searchString}
            visible={this.state.visible}
            onClose={(e) => this.onClose(e)}
            selected_para={
              this.state.drawerOptions.thing_wise_param_selection[
                this.state.options.selected_thing
              ]
            }
            all_para_thing_wise={
              this.state.drawerOptions.all_thing_wise_param_keys[
                this.state.options.selected_thing
              ]
            }
            all_para={this.state.drawerOptions.all_params}
            onCheckAllChange={(e) => this.onCheckAllChangeInDrawer(e)}
            check_all={this.state.drawerOptions.checked_all}
            onParamChange={(e) => this.onParamChangeInDrawer(e)}
            checked={
              this.state.drawerOptions.checked[
                this.state.options.selected_thing
              ]
            }
          />
          {this.state.options.thing_wise_param_selection[
            this.state.options.selected_thing
          ]?.length === 0 && this.state.options.device_id_list.length > 0 ? (
            <AntRow>
              <AntCol xs={9} sm={10} md={10} lg={12} xl={5} xxl={4}></AntCol>
              <AntCol
                xs={13}
                sm={14}
                md={14}
                lg={12}
                xl={15}
                xxl={20}
                style={{
                  "margin-top": "-20px",
                  "margin-bottom": 30,
                  color: "red",
                }}
              >
                Please select parameters !
              </AntCol>
            </AntRow>
          ) : (
            ""
          )}
          <Form.Item label="Report Time Range" style={{ marginBottom: 0 }}>
            <Form.Item
              name="ddrange"
              style={{
                display: "inline-block",
                width: "200px",
              }}
            >
              {timeRangeValJsx}
            </Form.Item>
            {this.state.options.drva === 10 ? (drval = false) : (drval = true)}
            <Form.Item
              name="custom_range"
              style={{
                display: "inline-block",
                width: "calc(50% - 8px)",
                margin: "0 8px",
              }}
            >
              <AntRangePicker
                showTime={{
                  format: "HH:mm",
                }}
                format={"DD MMM YYYY, HH:mm"}
                type="range_picker"
                defaultValue={[
                  moment
                    .unix(this.state.options.from_time)
                    .tz(this.props.timeZone),
                  moment
                    .unix(this.state.options.upto_time)
                    .tz(this.props.timeZone),
                ]}
                value={[
                  moment
                    .unix(this.state.options.from_time)
                    .tz(this.props.timeZone),
                  moment
                    .unix(this.state.options.upto_time)
                    .tz(this.props.timeZone),
                ]}
                disabled={drval}
                disabledDate={this.disabledDate}
              />
            </Form.Item>
          </Form.Item>
          {this.state.options.dtype === "raw" &&
          this.state.options.upto_time - this.state.options.from_time >
            3 * 86400 ? (
            <AntRow>
              <AntCol xs={9} sm={10} md={10} lg={12} xl={5} xxl={4}></AntCol>
              <AntCol
                xs={13}
                sm={14}
                md={14}
                lg={12}
                xl={15}
                xxl={20}
                style={{
                  "margin-top": "-10px",
                  "margin-bottom": 30,
                  color: "red",
                }}
              >
                Please select date range within 3 days !
              </AntCol>
            </AntRow>
          ) : (
            ""
          )}
          <Form.Item
            label="Report View"
            name="r_view"
            rules={[
              {
                required: true,
                message: "Please select a data type view",
              },
            ]}
          >
            <AntCheckboxGroup disabled={false} options={downoptions} />
          </Form.Item>
          <Form.Item label="Graph View Type" name="graph_view">
            <AntSelect
              disabled={!this.state.options.r_view.includes("graph")}
              style={{ width: 350 }}
              defaultValue={this.state.options.graph_view}
              value={this.state.options.graph_view}
            >
              <AntOption value="one">All parameters in one graph</AntOption>
              <AntOption value="different">
                Different parameters in different graphs
              </AntOption>
            </AntSelect>
          </Form.Item>
        </Form>
      </div>
    );
  }
}
