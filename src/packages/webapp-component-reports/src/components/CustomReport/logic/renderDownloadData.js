import Backyard from "@datoms/js-utils/src/Backyard/Backyard_back";
import ParameterUnitsFormat from "../../../../../dg-monitoring-views/src/js/configuration/ParameterUnitsFormat";
import { things_categories } from "../../../../../dg-monitoring-views/src/js/GenericTemplate/utility/thing_categories";
import { getThingsData } from "@datoms/js-sdk";
import ParseMarkerName from "../DataHandeling/ParseMakeName";
import { useGlobalContext } from "../../../../../../store/globalStore";

export async function callDataApi(isDownload) {
  const { options, selected_thing, isAvg } = this.state;
  const { client_id, application_id } = this.props;

  const thingArr = isDownload
    ? options.device_id_list
    : [parseInt(selected_thing)];
  let dataType = "raw";
  if (options.dtype === "cumulative" || options.dtype === "average") {
    dataType = "aggregate";
  }
  const thingWiseParams = (thingId = selected_thing) => {
    return options.thing_wise_param_selection?.[thingId];
  }
  let reportParams =
    options.param_type_radio === "total"
      ? options.tpara
      : thingWiseParams();
      // : _uniq(getTotalparams);
  let parameterAttributes = [];
  if (options.dtype === "average") {
    parameterAttributes = reportParams.includes("aqi")
      ? reportParams.length > 1
        ? ["min", "max", "avg", "value", "min_at", "max_at"]
        : ["value"]
      : ["min", "max", "avg", "min_at", "max_at"];
  } else if (options.dtype === "cumulative") {
    parameterAttributes = ["sum"];
  }
  let apiDataResult, avgCombinedData;
  const makeDataFetch = await ParseMarkerName(
    client_id,
    application_id,
    options.pcategory,
  );
  if (options.dataOfAllAssets) {
    let parameters = [...options.tpara];
    function getGroupSummary(parameters, aggr) {
      return parameters.map(
        (param) =>
          `${aggr}(${aggr}(${param.replace(".", "#")}.${param === "aqi" ? "value" : aggr}))`,
      );
    }
    const avgCombinedDataPacketTimeWise = {
      data_type: "aggregate",
      aggregation_period: options.tinterval,
      things: options.device_id_list,
      from_time: options.from_time,
      upto_time: options.upto_time,
      categories: [options.pcategory],
      summarize: [
        {
          group_summary: [
            ...getGroupSummary(parameters, "avg"),
            ...getGroupSummary(parameters, "min"),
            ...getGroupSummary(parameters, "max"),
          ],
          query: [
            ...parameters.map(
              (param) =>
                `avg(${param.replace(".", "#")}.${param === "aqi" ? "value" : "avg"})`,
            ),
          ],
          group_by: "time",
        },
      ],
    };
    const avgCombinedDataPromise = [
      await getThingsData(
        avgCombinedDataPacketTimeWise,
        client_id,
        application_id,
      ),
    ];
    [avgCombinedData] = await Promise.all([
      Promise.all(avgCombinedDataPromise),
    ]);
  }
  // if (!options.dataOfAllAssets) {
  const dataPromises = [];
  thingArr.map((thing) => {
    dataPromises.push(
      getThingsData(
        {
          data_type: dataType,
          aggregation_period: options.tinterval,
          parameters: isDownload ? thingWiseParams(thing) : reportParams,
          data_source:
            options.selectedDataModel?.length > 1
              ? ["processed", "unprocessed"]
              : options.selectedDataModel.includes("processed")
                ? "processed"
                : options.selectedDataModel.includes("unprocessed")
                  ? "unprocessed"
                  : undefined,
          parameter_attributes: parameterAttributes,
          things: [thing],
          from_time: options.from_time,
          upto_time: options.upto_time,
        },
        client_id,
        application_id,
      ),
    );
  });
  [apiDataResult] = await Promise.all([Promise.all(dataPromises)]);
  // }
  function getFinalData(results) {
    const resultArr = [];
    if (results?.length) {
      results.map((result, index) => {
        resultArr.push({
          thingId: thingArr[index],
          data: result,
        });
      });
    }
    return resultArr;
  }

  this.setState(
    {
      apiData: getFinalData(apiDataResult),
      fetchedSummaryData: getFinalData(avgCombinedData),
      loading: false,
      ...(!isDownload && {pageLoading: true}),
      makeDataFetch,
      thingArr,
    },
    () => {
      this.getThingsDataFunc(isDownload);
    },
  );
}

export function getThingsDataFunc(isDownload) {
  const {
    options,
    apiData,
    makeDataFetch,
    thingArr,
    analysisFormatChecked,
    fetchedSummaryData,
    inclBlankRows,
    isAvg,
    fileFormat,
  } = this.state;
  const { totalThingsData, client_id } = this.props;
  let timeZone = this.props.timeZone;
  let timeFormat = this.props.timeFormat;
  let applicationName = this.props.application_name;
  let machine_info = this.props.machine_info;
  let isAurassure = this.props.isAurassure;
  let vendorName = this.props.vendor_name;
  let thingCat = things_categories;
  let getTotalparams = [];
  Object.keys(options.thing_wise_param_selection).map((key) => {
    options?.thing_wise_param_selection?.[key]?.map((params) => {
      getTotalparams.push(params);
    });
  });
  let isGmmco = this.isGmmco();
  let findParamUnit = ParameterUnitsFormat;
  let client_name = this.props.client_name;
  let paramName = {};
  options.all_params.map((param) => {
    paramName[param.value] = param.label;
  });
  new Backyard({
    scripts: [
      "https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js",
      "https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.23/moment-timezone.min.js",
      "https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.33/moment-timezone-with-data-1970-2030.min.js",
      "https://cdn.jsdelivr.net/npm/lodash@4.17.15/lodash.min.js",
    ],
    input: {
      totalThingsData: totalThingsData,
      applicationName: applicationName,
      options: options,
      apiData: apiData,
      client_name: client_name,
      timeZone: timeZone,
      machine_info: machine_info,
      isAurassure: isAurassure,
      vendorName: vendorName,
      thingCat: thingCat,
      makeDataFetch: makeDataFetch,
      timeFormat: timeFormat,
      findParamUnit: findParamUnit,
      isGmmco: isGmmco,
      thingArr: thingArr,
      analysisFormatChecked,
      fetchedSummaryData,
      inclBlankRows,
      isAvg,
      isDownload,
      fileFormat,
      paramName,
      clientId: client_id,
    },
    run: function (ctx, input, cb) {
      function getFuelLevelInLitres(thing_id, fuel_level) {
        if (isNaN(parseFloat(fuel_level)) || thing_id !== 11569) {
          return fuel_level;
        }
        return parseFloat(fuel_level);
      }
      
      let totalThingsData = input.totalThingsData;
      let applicationName = input.applicationName;
      let options = input.options;
      let apiData = input.apiData;
      let timeZone = input.timeZone;
      let timeFormat = input.timeFormat;
      let makeDataFetch = input.makeDataFetch;
      let findParamUnit = input.findParamUnit;
      let machine_info = input.machine_info;
      let thingCat = input.thingCat;
      let isAurassure = input.isAurassure;
      let vendorName = input.vendorName;
      let isGmmco = input.isGmmco;
      let client_name = input.client_name;
      let analysisFormatChecked = input.analysisFormatChecked;
      let fetchedSummaryData = input.fetchedSummaryData;
      let inclBlankRows = input.inclBlankRows;
      let isAvg = input.isAvg;
      let isDownload = input.isDownload;
      let fileFormat = input.fileFormat;
      let paramName = input.paramName;
      let dataObj = {};
      let finalData = {};
      let thingArr = input.thingArr;
      let defaultColor = isAurassure ? [36, 151, 170] : [255, 133, 0];
      let color_arr = [
        "#7271d1",
        "#07adb1",
        "#c973c2",
        "#f28f3d",
        "#e07575",
        "#9ad8e3",
        "#97e8d4",
        "#85d6a2",
        "#f0e967",
        "#80f24b",
        "#d46846",
        "#c1db5e",
        "#b87a40",
        "#8e75bd",
        "#59739c",
        "#f25a73",
      ];
      let minmaxColor = [
        "#43429a",
        "#07adb1",
        "#a44a9c",
        "#f4801f",
        "#c14040",
        "#6fccdd",
        "#61c3ab",
        "#56bc7b",
        "#e2da3e",
        "#41ce00",
        "#aa4728",
        "#b3d23c",
        "#a0632a",
        "#7156a3",
        "#3d577f",
        "#ee3352",
      ];
      let characters =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
      const randomCharacter = Array.from({ length: 5 }, () =>
        characters.charAt(Math.floor(Math.random() * characters.length)),
      ).join("");
      let hourMinVariable = timeFormat === "12_hr" ? "hh:mm A" : "HH:mm";
      let hourMinSecVariable =
        timeFormat === "12_hr" ? "hh:mm:ss A" : "HH:mm:ss";
      let graphHourMinVariable = timeFormat === "12_hr" ? "%l:%M %p" : "%H:%M";
      let graphHourMinSecVariable =
        timeFormat === "12_hr" ? "%l:%M:%S %p" : "%H:%M:%S";
      let graphHourMinXAxis =
        timeFormat === "12_hr"
          ? "{value:%e %b, %l:%M}"
          : "{value:%e %b, %H:%M}";
      let parameters = {},
        summaryTableData = {};
      const graphTickInterval = input.clientId === 23 ? 50 : null;
      const createGraphConfig = (data, yAxisValue, plotTitle = "") => ({
        chart: {
          type: "spline",
          zoomType: "x",
          width: isDownload ? 1280 : undefined,
          height: 300,
        },
        timezone: timeZone,
        xAxis: {
          type: "datetime",
          labels: {
            format:
              options.dtype === "raw" ||
              [3600, 900, 28800].includes(options.tinterval)
                ? graphHourMinXAxis
                : "{value:%e %b}",
            align: "left",
          },
          title: { text: "Date Time" },
        },
        title: { text: plotTitle },
        yAxis: yAxisValue,
        tooltip: {
          xDateFormat:
            options.dtype === "raw"
              ? `%A, %B %d, ${graphHourMinSecVariable}`
              : [3600, 900, 28800].includes(options.tinterval)
                ? `%A, %B %d, ${graphHourMinVariable}`
                : `%A, %B %d`,
          shared: true,
        },
        credits: { enabled: false },
        series: data,
        exporting: { enabled: false },
      });
      function avgValueAsPerParam(param, aggr) {
        return param === "aqi" ? "value" : aggr;
      }
      const getDateString = (time) =>
        time > 0
          ? ctx.moment
              .unix(time)
              .tz(timeZone)
              .format("DD MMM YYYY, " + hourMinVariable)
          : "NA";
      const getDateStringAsPerInterval = (time, interval, timeZone) => {
        return [3600, 900, 28800].includes(interval)
          ? `${ctx
              .moment(time, "X")
              .tz(timeZone)
              .format("DD MMM, " + hourMinVariable)} - ${ctx
              .moment(time + interval, "X")
              .tz(timeZone)
              .format("DD MMM YYYY , " + hourMinVariable)}`
          : ctx.moment(time, "X").tz(timeZone).format("DD MMM YYYY");
      };
      let reportData, summaryReportData;
      if (options.dataOfAllAssets) {
        finalData = {
          all: {
            conf: [],
            data: [],
          },
          grid: {
            conf: [],
            data: [],
            zone: timeZone,
          },
          graph: {
            conf: [],
            data: [],
            zone: timeZone,
          },
        };
        let detailedTableColumns = [];
        detailedTableColumns.push({
          title:
            options.dtype === "raw"
              ? "Date & time (DD MMM YYYY, HH:mm:ss)"
              : options.tinterval > 0
                ? "Date & time (DD MMM, HH:mm - DD MMM YYYY, HH:mm)"
                : "",
          dataIndex: "time",
          width: 500,
        });
        let graphData = {},
          summaryColumns = [],
          summaryData = [];
        summaryColumns.push(
          {
            title: "Parameter",
            key: "parameter",
            dataIndex: "parameter",
          },
          {
            title: "Avg",
            key: "avg",
            dataIndex: "avg",
          },
          {
            title: "Min",
            key: "min",
            dataIndex: "min",
          },
          {
            title: "Min At",
            key: "minAt",
            dataIndex: "minAt",
          },
          {
            title: "Max",
            key: "max",
            dataIndex: "max",
          },
          {
            title: "Max At",
            key: "maxAt",
            dataIndex: "maxAt",
          },
        );
        const filterParams = [];
        if (options.tpara?.length) {
          options.tpara.map((tp) => {
            if (ctx["_"].find(options.all_params, { value: tp })) {
              filterParams.push(
                ctx["_"].find(options.all_params, { value: tp }),
              );
            }
          });
        }
        filterParams.map(function (param, param_index) {
          if (!graphData[param.value]) {
            graphData[param.value] = [];
          }
          const paramSummaryData =
            fetchedSummaryData[0]?.data?.["summary"]?.[0]?.summary?.avg?.avg?.[
              param.value
            ]?.[avgValueAsPerParam(param.value, "avg")];
          const findSummaryMinimum =
            fetchedSummaryData[0]?.data?.["summary"]?.[0]?.summary?.min?.min?.[
              param.value
            ];
          const findSummaryMaximum =
            fetchedSummaryData[0]?.data?.["summary"]?.[0]?.summary?.max?.max?.[
              param.value
            ];
          summaryData.push({
            parameter: param.label,
            avg: !isNaN(parseFloat(paramSummaryData))
              ? parseFloat(paramSummaryData).toFixed(2)
              : "NA",
            min: !isNaN(
              parseFloat(
                findSummaryMinimum?.[avgValueAsPerParam(param.value, "min")],
              ),
            )
              ? parseFloat(
                  findSummaryMinimum[avgValueAsPerParam(param.value, "min")],
                ).toFixed(2)
              : "NA",
            minAt: getDateString(findSummaryMinimum?.time),
            max: !isNaN(
              parseFloat(
                findSummaryMaximum?.[avgValueAsPerParam(param.value, "max")],
              ),
            )
              ? parseFloat(
                  findSummaryMaximum[avgValueAsPerParam(param.value, "max")],
                ).toFixed(2)
              : "NA",
            maxAt: getDateString(findSummaryMaximum?.time),
          });
          const data = fetchedSummaryData[0]?.data?.["summary"]?.[0]?.data;
          detailedTableColumns.push({
            title:
              (param.value === "calculated_energy"
                ? "Energy"
                : param.label.replace(/<[^>]+>/g, "")) +
              (findParamUnit[param.unit]?.length || param.unit?.length
                ? " (" +
                  (param.value === "calculated_runhour"
                    ? "HH:mm:ss"
                    : findParamUnit[param.unit]
                      ? findParamUnit[param.unit]
                      : param.unit) +
                  ")"
                : ""),
            dataIndex: param.value,
            width: 200,
          });
          if (options.tinterval > 86400) {
            if (data?.length) {
              data.map(function (paramData) {
                const value = parseFloat(
                  parseFloat(
                    paramData.parameter_values?.avg?.[param.value]?.[
                      avgValueAsPerParam(param.value, "avg")
                    ],
                  )?.toFixed(2),
                );
                graphData[param.value].push([paramData.time, value]);
              });
            }
          } else {
            for (
              let aggrInd = options.from_time;
              aggrInd <= options.upto_time;
              aggrInd += options.tinterval
            ) {
              const index = ctx["_"].findIndex(data, {
                time: aggrInd,
              });
              const value =
                index > -1
                  ? parseFloat(
                      parseFloat(
                        data[index].parameter_values?.avg?.[param.value]?.[
                          avgValueAsPerParam(param.value, "avg")
                        ],
                      )?.toFixed(2),
                    )
                  : null;
              graphData[param.value].push([aggrInd, value]);
            }
          }
          finalData.all.conf.push({
            props: { gutter: 10, style: {}, className: "rowGraph" },
            child: [
              {
                compo: "Graph",
                classname: "graph-1",
                props: {
                  id: `graph-id-${param_index}-${param.key}`,
                },
                col_props: { span: 24 },
                pdf_force_new_page: true,
                datatype: {
                  "xAxis.categories": "datetime::HH:MM",
                  "series.data": "number::1",
                },
              },
            ],
          });
          finalData.all.data.push([
            createGraphConfig(
              [
                {
                  name: "Avg",
                  type: param.key === "aqi" ? "area" : "spline",
                  zIndex: 1,
                  tooltip: {
                    valueSuffix: ` ${findParamUnit[param.unit] || param.unit}`,
                  },
                  dataGrouping: {
                    enabled: true,
                    forced: true,
                    units: [["day", [2]]],
                  },
                  data: graphData[param.value],
                  color: color_arr[param_index % color_arr.length],
                },
              ],
              {
                title: {
                  text: findParamUnit[param.unit] || param.unit,
                },
                tickInterval: graphTickInterval,
              },
              paramName[param.value],
            ),
          ]);

          finalData.graph.conf.push({
            props: { gutter: 10, style: {}, className: "rowGraph" },
            child: [
              {
                compo: "Graph",
                classname: "graph-1",
                props: {
                  id: `graph-id-${param_index}-${param.key}`,
                },
                col_props: { span: 24 },
                pdf_force_new_page: true,
                datatype: {
                  "xAxis.categories": "datetime::HH:MM",
                  "series.data": "number::1",
                },
              },
            ],
          });
          finalData.graph.data.push([
            createGraphConfig(
              [
                {
                  name: "Avg",
                  type: param.key === "aqi" ? "area" : "spline",
                  zIndex: 1,
                  tooltip: {
                    valueSuffix: ` ${findParamUnit[param.unit] || param.unit}`,
                  },
                  dataGrouping: {
                    enabled: true,
                    forced: true,
                    units: [["day", [2]]],
                  },
                  data: graphData[param.value],
                  color: color_arr[param_index % color_arr.length],
                },
              ],
              {
                title: { text: findParamUnit[param.unit] || param.unit },
                tickInterval: graphTickInterval,
              },
              paramName[param.value],
            ),
          ]);
        });
        let detailedTableSortedData = ctx["_"].orderBy(
          fetchedSummaryData[0]?.data?.["summary"]?.[0]?.data,
          ["unixTime"],
          ["desc"],
        );
        let detailedTableData = [];
        detailedTableSortedData.forEach((thing_data) => {
          if (
            thing_data.parameter_values &&
            Object.keys(thing_data.parameter_values).length
          ) {
            let obj = {};
            obj.unixTime = thing_data.time;
            obj.time = getDateStringAsPerInterval(
              thing_data.time,
              options.tinterval,
              timeZone,
            );
            Object.keys(thing_data.parameter_values.avg).forEach((param) => {
              let averageDataFormatValue = avgValueAsPerParam(param, "avg");
              obj[param] =
                options.dtype === "average" &&
                !isNaN(
                  parseFloat(
                    thing_data.parameter_values.avg[param]?.[
                      averageDataFormatValue
                    ],
                  ),
                )
                  ? parseFloat(
                      thing_data.parameter_values.avg[param]?.[
                        averageDataFormatValue
                      ],
                    ).toFixed(2)
                  : analysisFormatChecked
                    ? ""
                    : "";
              if (param === "calculated_runhour") {
                obj[param] = new Date(obj[param] * 1000)
                  .toISOString()
                  .substr(11, 8); // Format to HH:MM:SS
              }
            });
            detailedTableData.push(obj);
          }
        });
        if (inclBlankRows) {
          for (
            let aggrInd = options.from_time;
            aggrInd <= options.upto_time;
            aggrInd += options.tinterval
          ) {
            if (!ctx["_"].find(detailedTableData, { unixTime: aggrInd })) {
              detailedTableData.push({
                unixTime: aggrInd,
                time: getDateStringAsPerInterval(
                  aggrInd,
                  options.tinterval,
                  timeZone,
                ),
              });
            }
          }
        }
        if (!detailedTableData?.length) {
          if (inclBlankRows) {
            detailedTableData = [];
            for (
              let aggrInd = options.from_time;
              aggrInd <= options.upto_time;
              aggrInd += options.tinterval
            ) {
              detailedTableData.push({
                unixTime: aggrInd,
                time: getDateStringAsPerInterval(
                  aggrInd,
                  options.tinterval,
                  timeZone,
                ),
              });
            }
          } else {
            detailedTableData = [];
            detailedTableData.push({});
          }
        }

        detailedTableData = ctx["_"].orderBy(
          detailedTableData,
          ["unixTime"],
          ["desc"],
        );
        let pushObj = {};
        let detailedViewTableConfig = {
          pdf_force_new_page: true,
        };
        pushObj = detailedViewTableConfig;
        pushObj["compo"] = "Text";
        pushObj["type"] = "bold";
        pushObj["pdf_size"] = 11;
        pushObj["col_props"] = {
          span: 24,
        };
        if (!analysisFormatChecked) {
          finalData.all.conf.push({
            props: {
              gutter: 10,
              style: {
                "margin-top": 20,
              },
            },
            child: [pushObj],
          });
          finalData.all.data.push([
            {
              textData: ["Summary Data"],
            },
          ]);
        }

        finalData.grid.conf.push({
          props: {
            gutter: 10,
          },
          child: [pushObj],
        });
        finalData.grid.data.push([
          {
            textData: ["Summary Data"],
            style: { "margin-top": 20 },
            type: "bold",
          },
        ]);
        if (!analysisFormatChecked) {
          finalData.all.conf.push({
            props: {
              gutter: 10,
              className: "tableRow",
            },
            child: [
              {
                compo: "Table",
                widget: "",
                classname: "tab-2",
                table_new_page: true,
                hellipRow: true,
                props: {
                  columns: summaryColumns,
                  headerFont: 13,
                  horizontalScroll: true,
                  isGrouped: true,
                  confColumnLength1: 3,
                  confColumnLength2: 3,
                  level: 2,
                  shadow: false,
                  breakPoint: 1000,
                  breakPoint2: 500,
                  largeTable: true,
                  mediumTable: false,
                  smallTable: false,
                },
                col_props: {
                  span: 24,
                },
                // datatype: detailedTableDataTypes,
                pdf_width: 50,
                pdf_table_break: {
                  col_no: 12,
                  row_no: 20,
                },
              },
            ],
          });
          finalData.all.data.push([summaryData]);
        }
        finalData.grid.conf.push({
          props: {
            gutter: 10,
            style: {
              "margin-top": 20,
              "margin-bottom": 30,
            },
            className: "tableRow",
          },
          child: [
            {
              compo: "Table",
              widget: "",
              classname: "tab-2",
              table_new_page: true,
              hellipRow: true,
              props: {
                columns: summaryColumns,
                headerFont: 13,
                size: "small",
                tabRadius: 0,
                isHorizontalScroll: true,
                horizontalScroll: true,
                shadow: false,
                breakPoint: 1000,
                breakPoint2: 500,
                largeTable: true,
                mediumTable: true,
                smallTable: true,
              },
              col_props: {
                span: 24,
              },
              // datatype: detailedTableDataTypes,
              pdf_width: 50,
              pdf_table_break: {
                col_no: 12,
                row_no: 20,
              },
            },
          ],
        });
        finalData.grid.data.push([summaryData]);
        if (!analysisFormatChecked) {
          finalData.all.conf.push({
            props: {
              gutter: 10,
              style: {
                "margin-top": 20,
              },
            },
            child: [pushObj],
          });
          finalData.all.data.push([
            {
              textData: ["Detailed Data"],
            },
          ]);
        }
        finalData.grid.conf.push({
          props: {
            gutter: 10,
          },
          child: [pushObj],
        });
        finalData.grid.data.push([
          {
            textData: ["Detailed Data"],
            style: { "margin-top": 20 },
            type: "bold",
          },
        ]);

        finalData.all.conf.push({
          props: {
            gutter: 10,
            className: "tableRow",
          },
          child: [
            {
              compo: "Table",
              widget: "",
              classname: "tab-2",
              table_new_page: true,
              hellipRow: true,
              props: {
                columns: detailedTableColumns,
                headerFont: 13,
                horizontalScroll: true,
                isGrouped: true,
                confColumnLength1: 3,
                confColumnLength2: 3,
                level: 2,
                shadow: false,
                breakPoint: 1000,
                breakPoint2: 500,
                largeTable: true,
                mediumTable: false,
                smallTable: false,
              },
              col_props: {
                span: 24,
              },
              // datatype: detailedTableDataTypes,
              pdf_width: 50,
              pdf_table_break: {
                col_no: 12,
                row_no: 20,
              },
            },
          ],
        });
        finalData.all.data.push([detailedTableData]);
        finalData.grid.conf.push({
          props: {
            gutter: 10,
            style: {
              "margin-top": 20,
              "margin-bottom": 30,
            },
            className: "tableRow",
          },
          child: [
            {
              compo: "Table",
              widget: "",
              classname: "tab-2",
              table_new_page: true,
              hellipRow: true,
              props: {
                columns: detailedTableColumns,
                headerFont: 13,
                size: "small",
                tabRadius: 0,
                isHorizontalScroll: true,
                horizontalScroll: detailedTableColumns.length*150, //Summary Tab
                shadow: false,
                breakPoint: 1000,
                breakPoint2: 500,
                largeTable: true,
                mediumTable: true,
                smallTable: true,
              },
              col_props: {
                span: 24,
              },
              // datatype: detailedTableDataTypes,
              pdf_width: 50,
              pdf_table_break: {
                col_no: 12,
                row_no: 20,
              },
            },
          ],
        });
        finalData.grid.data.push([detailedTableData]);
        summaryReportData = finalData;
      }
      if (thingArr?.length) {
        thingArr.map((thingIds, index) => {
          const findThing = ctx["_"].find(totalThingsData?.things, {
            id: thingIds,
          });
          let timeout = findThing?.category === 42 ? 70 : 300;
          let thing_id = findThing?.id;
          if (!parameters[thing_id]) {
            parameters[thing_id] = [];
          }
          if (!summaryTableData[thing_id]) {
            summaryTableData[thing_id] = [];
          }
          if (!dataObj[thing_id]) {
            dataObj[thing_id] = [];
          }
          if (!finalData[thing_id]) {
            finalData[thing_id] = {
              all: {
                conf: [],
                data: [],
              },
              grid: {
                conf: [],
                data: [],
                zone: timeZone,
              },
              graph: {
                conf: [],
                data: [],
                zone: timeZone,
              },
            };
          }

          let summaryThingsObject = {};
          let paramTitlesHashMap = {};

          let detailedTableColumns = [],
            cumulativeColumns = [],
            cumulativeDataTableDataTypes = {};
          let detailedTableDataTypes = {
            time: "String",
            asset_id: "Number",
            asset_name: "String",
          };
          detailedTableColumns.push({
            title:
              options.dtype === "raw"
                ? "Date & time (DD MMM YYYY, HH:mm:ss)"
                : options.tinterval > 0
                  ? "Date & time (DD MMM, HH:mm - DD MMM YYYY, HH:mm)"
                  : "",
            dataIndex: "time",
            width: 500,
          });

          let params =
            options.param_type_radio === "total"
              ? options.tpara
              : options.thing_wise_param_selection[thing_id];
          if (params && params.length) {
            params.map(function (params) {
              parameters[thing_id].push(
                ctx["_"].find(findThing?.parameters, {
                  key: params,
                }),
              );
            });
          }
          const selectedDataModel = options.selectedDataModel;
          parameters[thing_id].map(function (param) {
            if (param) {
              if (selectedDataModel?.length > 1) {
                selectedDataModel.map((model) => {
                  detailedTableColumns.push({
                    title: `${param.name.replace(/<[^>]+>/g, "")}${
                      findParamUnit[param.unit]?.length || param.unit?.length
                        ? `(${
                            findParamUnit[param.unit]
                              ? findParamUnit[param.unit]
                              : param.unit
                          })`
                        : ""
                    }(${model})`,
                    dataIndex:
                      model === "unprocessed"
                        ? `${param.key}_${model}`
                        : param.key,
                    width: 200,
                  });
                });
              } else {
                detailedTableColumns.push({
                  title:
                    (param.key === "calculated_energy"
                      ? "Energy"
                      : param.name.replace(/<[^>]+>/g, "")) +
                    (findParamUnit[param.unit]?.length || param.unit?.length
                      ? " (" +
                        (param.key === "calculated_runhour"
                          ? "HH:mm:ss"
                          : findParamUnit[param.unit]
                            ? findParamUnit[param.unit]
                            : param.unit) +
                        ")"
                      : ""),
                  dataIndex: param.key,
                  width: 200,
                });
              }
              cumulativeColumns.push({
                title:
                  param.name.replace(/<[^>]+>/g, "") +
                  (findParamUnit[param.unit]?.length || param.unit?.length
                    ? " (" +
                      (findParamUnit[param.unit]
                        ? findParamUnit[param.unit]
                        : param.unit) +
                      ")"
                    : ""),
                dataIndex: param.key,
              });

              paramTitlesHashMap[param.key] =
                param.name.replace(/<[^>]+>/g, "") +
                (findParamUnit[param.unit]?.length || param.unit?.length
                  ? " (" +
                    (findParamUnit[param.unit]
                      ? findParamUnit[param.unit]
                      : param.unit) +
                    ")"
                  : "");

              detailedTableDataTypes[param.key] = param.data_type;

              cumulativeDataTableDataTypes[param.key] = param.data_type;
            }
          });
          const findApiData = ctx["_"].find(apiData, {
            thingId: thing_id,
          })?.data;
          if (findApiData?.data?.length) {
            dataObj[thing_id] = ctx["_"].filter(findApiData.data, {
              thing_id: thing_id,
            });
          }
          if (dataObj[thing_id] && dataObj[thing_id].length) {
            dataObj[thing_id] = ctx["_"].orderBy(
              dataObj[thing_id],
              "time",
              "asc",
            );
          }
          let cumulativeTableData = {};
          let view_string = "",
            view_machine_array = [],
            pdf_string1 = "",
            pdf_string2 = "",
            pdf_machine_array = [];
          let findSpecificThingCat = ctx["_"].find(thingCat, {
            id: findThing?.category,
          });
          let machineInfoAvailable =
            findSpecificThingCat &&
            findSpecificThingCat.machine_info &&
            findSpecificThingCat.machine_info.length
              ? findSpecificThingCat.machine_info
              : [
                  {
                    label: "Make",
                    key: "make",
                  },
                  {
                    label: "Model",
                    key: "model",
                  },
                ];
          if (machineInfoAvailable && machineInfoAvailable.length) {
            let lifetimeRnhr = "NA";
            machineInfoAvailable.map(function (info_item, index) {
              let machine_value = "NA";
              if ([18, 96].includes(findThing?.category)) {
                let findCalculatedLifetimeRunhour = "",
                  findRnHr = {},
                  findCalcRnhr = {};
                if (findThing?.parameters?.length) {
                  findRnHr = findThing.parameters.find(function (item) {
                    return item.key === "rnhr";
                  });

                  findCalcRnhr = findThing.parameters.find(function (item) {
                    return item.key === "calculated_runhour";
                  });
                  if (
                    findRnHr &&
                    findRnHr.value &&
                    findRnHr.value !== "" &&
                    parseFloat(findRnHr.value) > 0
                  ) {
                    findCalculatedLifetimeRunhour =
                      parseFloat(findRnHr.value) * 3600;
                  } else if (
                    findCalcRnhr &&
                    findCalcRnhr.aggregated_value &&
                    findCalcRnhr.aggregated_value.lifetime &&
                    findCalcRnhr.aggregated_value.lifetime.sum
                  ) {
                    findCalculatedLifetimeRunhour =
                      findCalcRnhr.aggregated_value.lifetime.sum;
                  }
                }
                if (findCalculatedLifetimeRunhour) {
                  let lifetimehour = Math.floor(
                    findCalculatedLifetimeRunhour / 3600,
                  );
                  let lifetimeMin = Math.floor(
                    (findCalculatedLifetimeRunhour % 3600) / 60,
                  );
                  lifetimeRnhr =
                    (lifetimehour < 10 ? "0" + lifetimehour : lifetimehour) +
                    " : " +
                    (lifetimeMin < 10 ? "0" + lifetimeMin : lifetimeMin) +
                    (isGmmco ? " SMU" : " Hrs");
                }
              }
              if (info_item.key === "address") {
                machine_value =
                  findThing && findThing.address ? findThing.address : "NA";
              } else {
                if (findThing && findThing.thing_details) {
                  machine_value = findThing.thing_details[info_item.key]
                    ? (info_item.key === "make" &&
                      makeDataFetch?.[findThing.thing_details[info_item.key]]
                        ? makeDataFetch[findThing.thing_details[info_item.key]]
                        : findThing.thing_details[info_item.key]) +
                      (info_item.key === "kva" ? " KVA" : "")
                    : "NA";
                }
              }
              let strLength = [18, 96].includes(findThing?.category)
                ? machineInfoAvailable.length + 1
                : machineInfoAvailable.length;
              if (index + 1 !== strLength) {
                pdf_string1 += `${info_item.label} - ${machine_value}  |  `;
                view_string += `${info_item.label} - ${machine_value}  \xa0\xa0|\xa0\xa0  `;
              } else {
                pdf_string2 += `${info_item.label} - ${machine_value}`;
                view_string += `${info_item.label} - ${machine_value}`;
              }
            });
            if ([18, 96].includes(findThing?.category)) {
              pdf_string2 += `Lifetime Runhour - ${lifetimeRnhr}`;
              view_string += `Lifetime Runhour - ${lifetimeRnhr}`;
            }
          }
          let finalString = pdf_string1 + pdf_string2;
          if (options.dtype === "cumulative") {
            if (parameters[thing_id] && parameters[thing_id].length) {
              parameters[thing_id].forEach(function (param) {
                if (param && param.key) {
                  if (!cumulativeTableData[param.key]) {
                    cumulativeTableData[param.key] = {};
                  }

                  const sumValue = ctx["_"].sumBy(
                    dataObj[thing_id],
                    function (o) {
                      return parseFloat(o.parameter_values[param.key]["sum"]);
                    },
                  );

                  if (sumValue) {
                    cumulativeTableData[param.key] = sumValue;

                    // Helper function to format the runhour
                    const formatRunhour = (totalSeconds) => {
                      const hours = Math.floor(totalSeconds / 3600);
                      const minutes = Math.floor((totalSeconds % 3600) / 60);
                      const seconds = (totalSeconds % 3600) % 60;
                      return `${hours < 10 ? "0" : ""}${hours}:${
                        minutes < 10 ? "0" : ""
                      }${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
                    };

                    if (param.key === "calculated_runhour") {
                      cumulativeTableData[param.key] = formatRunhour(
                        cumulativeTableData[param.key],
                      );
                    } else {
                      cumulativeTableData[param.key] =
                        cumulativeTableData[param.key].toFixed(2);
                    }
                  } else {
                    cumulativeTableData[param.key] = "NA";
                  }
                }
              });
            }

            cumulativeTableData = [cumulativeTableData];
            view_machine_array.push(view_string);
            pdf_machine_array.push(finalString);

            const addMachineObj = (confArray, dataArray) => {
              const machineObj = {
                compo: "Text",
                props: { type: "normal" },
                col_props: { span: 24 },
                pdf_size: 10,
              };
              confArray.push({
                props: { style: { "margin-top": 10 } },
                child: [machineObj],
              });
              dataArray.push([{ textData: ["", finalString] }]);
            };

            // Add the table configuration for both 'all' and 'grid'
            const addTableConfig = (confArray, dataArray, tableProps) => {
              confArray.push({
                props: { gutter: 10, className: "tableRow" },
                child: [
                  {
                    compo: "Table",
                    classname: "tab-1",
                    table_new_page: true,
                    props: tableProps,
                    col_props: { span: 24 },
                  },
                ],
              });
              dataArray.push([cumulativeTableData]);
            };

            const cumulativeTableProps = {
              columns: cumulativeColumns,
              headerFont: 13,
              size: "small",
              tabRadius: 0,
              isHorizontalScroll: true,
              horizontalScroll: true,
              shadow: false,
              breakPoint: 1000,
              breakPoint2: 500,
              largeTable: true,
              mediumTable: true,
              smallTable: true,
              pdf_width: 50,
              pdf_table_break: { col_no: 6, row_no: 20 },
            };

            addMachineObj(
              finalData[thing_id].all.conf,
              finalData[thing_id].all.data,
            );
            addMachineObj(
              finalData[thing_id].grid.conf,
              finalData[thing_id].grid.data,
            );

            addTableConfig(
              finalData[thing_id].all.conf,
              finalData[thing_id].all.data,
              {
                ...cumulativeTableProps,
                horizontalScroll: true,
              },
            );
            addTableConfig(
              finalData[thing_id].grid.conf,
              finalData[thing_id].grid.data,
              {
                ...cumulativeTableProps,
                horizontalScroll: false,
              },
            );
          } else {
            if (parameters[thing_id] && parameters[thing_id].length) {
              parameters[thing_id].forEach(function (param) {
                if (param && param.key) {
                  if (!summaryThingsObject[param.key]) {
                    summaryThingsObject[param.key] = {
                      min: "NA",
                      max: "NA",
                      maxAt: 0,
                      minAt: 0,
                      avg: "NA",
                      total: 0,
                      count: 0,
                    };
                  }
                  let avgDataFormatValue = avgValueAsPerParam(param.key, "avg");
                  let minDataFormatValue = avgValueAsPerParam(param.key, "min");
                  let maxDataFormatValue = avgValueAsPerParam(param.key, "max");

                  const updateSummaryData = (
                    dataKey,
                    calculationKey,
                    formatValue,
                  ) => {
                    summaryThingsObject[dataKey][calculationKey] = ctx["_"]
                      .sumBy(dataObj[thing_id], (o) =>
                        o.parameter_values[dataKey]?.[formatValue]
                          ? parseFloat(o.parameter_values[dataKey][formatValue])
                          : 0,
                      )
                      .toFixed(2);
                  };

                  if (options.dtype === "raw") {
                    summaryThingsObject[param.key]["total"] = ctx["_"]
                      .sumBy(
                        dataObj[thing_id],
                        (o) => parseFloat(o.parameter_values[param.key]) || 0,
                      )
                      .toFixed(2);
                    summaryThingsObject[param.key]["count"] = ctx["_"].filter(
                      dataObj[thing_id],
                      (o) => o.parameter_values[param.key],
                    ).length;

                    if (summaryThingsObject[param.key]["count"] > 0) {
                      let total = summaryThingsObject[param.key]["total"];
                      let count = summaryThingsObject[param.key]["count"];
                      let avg = (total / count).toFixed(2);

                      if (["fuel_litre", "fuel_level"].includes(param.key)) {
                        summaryThingsObject[param.key]["avg"] = parseFloat(
                          getFuelLevelInLitres(thing_id, avg),
                        ).toFixed(2);
                      } else {
                        summaryThingsObject[param.key]["avg"] = avg;
                      }
                    }
                    summaryThingsObject[param.key]["min"] = parseFloat(
                      ctx["_"].minBy(dataObj[thing_id], (o) =>
                        parseFloat(o.parameter_values[param.key]),
                      )?.parameter_values[param.key],
                    )
                      ? parseFloat(
                          ctx["_"].minBy(dataObj[thing_id], (o) =>
                            parseFloat(o.parameter_values[param.key]),
                          )?.parameter_values[param.key],
                        )?.toFixed(2)
                      : "NA";
                    const minAtValue = ctx["_"].minBy(dataObj[thing_id], (o) =>
                      parseFloat(o.parameter_values[param.key]),
                    )?.time;
                    summaryThingsObject[param.key]["minAt"] =
                      minAtValue > 0 ? getDateString(minAtValue) : "NA";

                    summaryThingsObject[param.key]["max"] = parseFloat(
                      ctx["_"].maxBy(dataObj[thing_id], (o) =>
                        parseFloat(o.parameter_values[param.key]),
                      )?.parameter_values[param.key],
                    )
                      ? parseFloat(
                          ctx["_"].maxBy(dataObj[thing_id], (o) =>
                            parseFloat(o.parameter_values[param.key]),
                          )?.parameter_values[param.key],
                        )?.toFixed(2)
                      : "NA";
                    const maxAtValue = ctx["_"].maxBy(dataObj[thing_id], (o) =>
                      parseFloat(o.parameter_values[param.key]),
                    )?.time;
                    summaryThingsObject[param.key]["maxAt"] =
                      maxAtValue > 0 ? getDateString(maxAtValue) : "NA";
                  } else {
                    updateSummaryData(param.key, "total", avgDataFormatValue);
                    summaryThingsObject[param.key]["count"] = ctx["_"].filter(
                      dataObj[thing_id],
                      (o) =>
                        o.parameter_values[param.key]?.[avgDataFormatValue],
                    ).length;

                    if (summaryThingsObject[param.key]["count"] > 0) {
                      let total = summaryThingsObject[param.key]["total"];
                      let count = summaryThingsObject[param.key]["count"];
                      summaryThingsObject[param.key]["avg"] = (
                        total / count
                      ).toFixed(2);
                    }
                    const findMin = ctx["_"].minBy(dataObj[thing_id], (o) =>
                      parseFloat(
                        o.parameter_values[param.key]?.[minDataFormatValue],
                      ),
                    );
                    const findMax = ctx["_"].maxBy(dataObj[thing_id], (o) =>
                      parseFloat(
                        o.parameter_values[param.key]?.[minDataFormatValue],
                      ),
                    );
                    summaryThingsObject[param.key]["min"] = findMin
                      ?.parameter_values[param.key]?.[minDataFormatValue]
                      ? parseFloat(
                          findMin?.parameter_values[param.key]?.[
                            minDataFormatValue
                          ],
                        ).toFixed(2)
                      : "NA";
                    const minAtValue =
                      param.key === "aqi"
                        ? findMin?.time
                        : ctx["_"].minBy(dataObj[thing_id], (o) =>
                            parseFloat(o.parameter_values[param.key]?.["min"]),
                          )?.parameter_values[param.key]?.["min_at"];
                    summaryThingsObject[param.key]["minAt"] =
                      minAtValue > 0 ? getDateString(minAtValue) : "NA";

                    summaryThingsObject[param.key]["max"] = findMax
                      ?.parameter_values[param.key]?.[maxDataFormatValue]
                      ? parseFloat(
                          findMax?.parameter_values[param.key]?.[
                            maxDataFormatValue
                          ],
                        ).toFixed(2)
                      : "NA";
                    const maxAtValue =
                      param.key === "aqi"
                        ? findMax?.time
                        : ctx["_"].maxBy(dataObj[thing_id], (o) =>
                            parseFloat(o.parameter_values[param.key]?.["max"]),
                          )?.parameter_values[param.key]?.["max_at"];
                    summaryThingsObject[param.key]["maxAt"] =
                      maxAtValue > 0 ? getDateString(maxAtValue) : "NA";
                  }
                }
              });
            }

            let detailedTableSortedData = ctx["_"].orderBy(
              dataObj[thing_id],
              ["unixTime"],
              ["desc"],
            );

            let detailedTableData = detailedTableSortedData.map(
              (thing_data) => {
                let obj = {};

                Object.keys(thing_data.parameter_values).forEach((param) => {
                  let averageDataFormatValue = avgValueAsPerParam(param, "avg");
                  obj["asset_id"] = thing_id;
                  obj["asset_name"] = findThing?.name;
                  obj[`${param}_unprocessed`] = parseFloat(
                    thing_data.unprocessed_parameter_values?.[param],
                  ).toFixed(2);
                  obj[param] =
                    options.dtype === "raw"
                      ? parseFloat(thing_data.parameter_values[param]).toFixed(
                          2,
                        )
                      : options.dtype === "average" &&
                          !isNaN(
                            parseFloat(
                              thing_data.parameter_values[param]?.[
                                averageDataFormatValue
                              ],
                            ),
                          )
                        ? parseFloat(
                            thing_data.parameter_values[param]?.[
                              averageDataFormatValue
                            ],
                          ).toFixed(2)
                        : analysisFormatChecked
                          ? ""
                          : "";

                  if (
                    options.dtype === "raw" &&
                    ["fuel_litre", "fuel_level"].includes(param)
                  ) {
                    obj[param] = parseFloat(
                      getFuelLevelInLitres(thing_id, obj[param]),
                    ).toFixed(2);
                  }

                  if (param === "calculated_runhour") {
                    obj[param] = new Date(obj[param] * 1000)
                      .toISOString()
                      .substr(11, 8); // Format to HH:MM:SS
                  }
                });
                obj.unixTime = thing_data.time;
                obj.time =
                  options.dtype === "raw"
                    ? ctx
                        .moment(thing_data.time, "X")
                        .tz(timeZone)
                        .format("DD MMM YYYY, " + hourMinSecVariable)
                    : getDateStringAsPerInterval(
                        thing_data.time,
                        options.tinterval,
                        timeZone,
                      );

                return obj;
              },
            );
            if (inclBlankRows) {
              for (
                let aggrInd = options.from_time;
                aggrInd <= options.upto_time;
                aggrInd += options.tinterval
              ) {
                if (!ctx["_"].find(detailedTableData, { unixTime: aggrInd })) {
                  detailedTableData.push({
                    unixTime: aggrInd,
                    time: getDateStringAsPerInterval(
                      aggrInd,
                      options.tinterval,
                      timeZone,
                    ),
                  });
                }
              }
            }
            if (!detailedTableData.length) {
              if (inclBlankRows) {
                detailedTableData = [];
                for (
                  let aggrInd = options.from_time;
                  aggrInd <= options.upto_time;
                  aggrInd += options.tinterval
                ) {
                  detailedTableData.push({
                    unixTime: aggrInd,
                    time:
                      options.dtype === "raw"
                        ? ctx
                            .moment(thing_data.time, "X")
                            .tz(timeZone)
                            .format("DD MMM YYYY, " + hourMinSecVariable)
                        : getDateStringAsPerInterval(
                            aggrInd,
                            options.tinterval,
                            timeZone,
                          ),
                  });
                }
              } else {
                detailedTableData = [];
                detailedTableData.push({});
              }
            }
            detailedTableData = ctx["_"].orderBy(
              detailedTableData,
              ["unixTime"],
              ["desc"],
            );
            view_machine_array.push(view_string);
            pdf_machine_array.push(finalString);

            let machineObj = {}; //source
            machineObj["compo"] = "Text";
            machineObj["props"] = {
              type: "normal",
            };
            machineObj["col_props"] = {
              span: 24,
            };
            machineObj["pdf_size"] = 10;
            if (!analysisFormatChecked) {
              finalData[thing_id].all.conf.push({
                props: {
                  //gutter: 10,
                  style: {
                    "margin-top": 10,
                  },
                },
                child: [machineObj],
              });
              finalData[thing_id].all.data.push([
                {
                  textData: ["", finalString],
                },
              ]);
            }
            finalData[thing_id].grid.conf.push({
              props: {
                gutter: 10,
                style: {
                  color: "#7686A1",
                  "margin-top": 10,
                  "margin-bottom": 20,
                },
              },
              child: [machineObj],
            });
            finalData[thing_id].grid.data.push([
              {
                textData: view_machine_array,
              },
            ]);
            let pushObj = {};
            let summaryDataColumns = [];
            summaryDataColumns.push({
              title: "Parameter",
              dataIndex: "parameter",
            });
            summaryDataColumns.push({
              title: "Avg",
              dataIndex: "avg",
            });
            summaryDataColumns.push({
              title: "Min",
              dataIndex: "min",
            });
            summaryDataColumns.push({
              title: "Min at.",
              dataIndex: "minAt",
            });
            summaryDataColumns.push({
              title: "Max",
              dataIndex: "max",
            });
            summaryDataColumns.push({
              title: "Max at.",
              dataIndex: "maxAt",
            });

            let summaryDataTableDataTypes = {
              avg: "number::2",
              min: "number::2",
              max: "number::2",
              minAt: "String",
              maxAt: "String",
            };
            summaryTableData[thing_id] = Object.keys(summaryThingsObject).map(
              function (param) {
                let summaryDataItem = summaryThingsObject[param];
                summaryDataItem.parameter = paramTitlesHashMap[param];
                return summaryDataItem;
              },
            );

            if (
              summaryTableData[thing_id] &&
              summaryTableData[thing_id].length
            ) {
              summaryTableData[thing_id] = summaryTableData[thing_id];
            } else {
              summaryTableData[thing_id].push({});
            }
            pushObj = options;
            pushObj["compo"] = "Text";
            pushObj["type"] = "bold";
            pushObj["col_props"] = {
              span: 24,
            };
            pushObj["pdf_size"] = 11;
            if (!analysisFormatChecked) {
              finalData[thing_id].all.conf.push({
                props: {
                  gutter: 10,
                  style: {
                    "margin-top": 10,
                  },
                },
                child: [pushObj],
              });
              finalData[thing_id].all.data.push([
                {
                  textData: ["", "Summary Data"],
                },
              ]);
            }

            finalData[thing_id].grid.conf.push({
              props: {
                gutter: 10,
              },
              child: [pushObj],
            });
            finalData[thing_id].grid.data.push([
              {
                textData: ["Summary Data"],
                type:  "bold",
              },
            ]);
            if (!analysisFormatChecked) {
              finalData[thing_id].all.conf.push({
                props: {
                  gutter: 10,
                  className: "tableRow",
                },
                child: [
                  {
                    compo: "Table",
                    widget: "",
                    classname: "tab-1",
                    table_new_page: true,
                    props: {
                      columns: summaryDataColumns,
                      headerFont: 13,
                      horizontalScroll: true,
                      isGrouped: true,
                      confColumnLength1: 3,
                      confColumnLength2: 3,
                      level: 2,
                      shadow: false,
                      breakPoint: 1000,
                      breakPoint2: 500,
                      largeTable: true,
                      mediumTable: false,
                      smallTable: false,
                    },
                    col_props: {
                      span: 24,
                    },
                    datatype: summaryDataTableDataTypes,
                    pdf_width: 50,
                    pdf_table_break: {
                      col_no: 10,
                      row_no: 12,
                    },
                  },
                ],
              });
              finalData[thing_id].all.data.push([summaryTableData[thing_id]]);
            }
            finalData[thing_id].grid.conf.push({
              props: {
                gutter: 10,
                style: {
                  "margin-top": 20,
                  "margin-bottom": 30,
                },
                className: "tableRow",
              },
              child: [
                {
                  compo: "Table",
                  widget: "",
                  classname: "tab-1",
                  table_new_page: true,
                  props: {
                    columns: summaryDataColumns,
                    headerFont: 13,
                    size: "small",
                    tabRadius: 0,
                    isHorizontalScroll: true,
                    horizontalScroll: true,
                    shadow: false,
                    breakPoint: 1000,
                    breakPoint2: 500,
                    largeTable: true,
                    mediumTable: true,
                    smallTable: true,
                    customPagination: {
                      noPagination: true,
                    },
                  },
                  col_props: {
                    span: 24,
                  },
                  datatype: summaryDataTableDataTypes,
                  pdf_width: 50,
                  pdf_table_break: {
                    col_no: 10,
                    row_no: 10,
                  },
                },
              ],
            });
            finalData[thing_id].grid.data.push([summaryTableData[thing_id]]);

            let detailedViewTableConfig = {
              pdf_force_new_page: true,
            };
            pushObj = detailedViewTableConfig;
            pushObj["compo"] = "Text";
            pushObj["type"] = "bold";
            pushObj["pdf_size"] = 11;
            pushObj["col_props"] = {
              span: 24,
            };

            finalData[thing_id].grid.conf.push({
              props: {
                gutter: 10,
              },
              child: [pushObj],
            });

            finalData[thing_id].grid.data.push([
              {
                textData: ["Detailed Data"],
                style: { "margin-top": 20 },
                type: "bold",
              },
            ]);

            finalData[thing_id].grid.conf.push({
              props: {
                gutter: 10,
                style: {
                  "margin-top": 20,
                  "margin-bottom": 30,
                },
                className: "tableRow",
              },
              child: [
                {
                  compo: "Table",
                  widget: "",
                  classname: "tab-2",
                  table_new_page: true,
                  hellipRow: true,
                  props: {
                    columns: detailedTableColumns,
                    headerFont: 13,
                    size: "small",
                    tabRadius: 0,
                    isHorizontalScroll: true,
                    horizontalScroll: detailedTableColumns.length*150, //Asset Tab
                    shadow: false,
                    breakPoint: 1000,
                    breakPoint2: 500,
                    largeTable: true,
                    mediumTable: true,
                    smallTable: true,
                  },
                  col_props: {
                    span: 24,
                  },
                  // datatype: detailedTableDataTypes,
                  pdf_width: 50,
                  pdf_table_break: {
                    col_no: 12,
                    row_no: 20,
                  },
                },
              ],
            });

            finalData[thing_id].grid.data.push([detailedTableData]);

            

            let graphDataReport = {};
            if (!graphDataReport["raw"]) {
              graphDataReport["raw"] = {};
            }
            if (!graphDataReport["unprocessed_raw"]) {
              graphDataReport["unprocessed_raw"] = {};
            }
            if (!graphDataReport["min"]) {
              graphDataReport["min"] = {};
            }
            if (!graphDataReport["max"]) {
              graphDataReport["max"] = {};
            }
            if (!graphDataReport["range"]) {
              graphDataReport["range"] = {};
            }
            if (!graphDataReport["avg"]) {
              graphDataReport["avg"] = {};
            }
            if (!graphDataReport["value"]) {
              graphDataReport["value"] = {};
            }
            let yAxisValue = [];
            let seriesData = [];
            let plotTitle = [];
            const getTime = (time) =>
              ctx.moment(time, "X").tz(timeZone).unix() * 1000;
            const getFuelValue = (thing_id, value) =>
              ["fuel_litre", "fuel_level"].includes(params.key)
                ? parseFloat(getFuelLevelInLitres(thing_id, value))
                : parseFloat(value);

            const pushGraphData = (dataArray, time, data, raw = false, idx= undefined) => {
              const unixTime = getTime(time);
              const numberData = parseFloat(data);
              const dataToPush = [unixTime, raw ? numberData || null : numberData];
              if (idx !== undefined) {
                dataArray[idx] = dataToPush;
              } else {
                dataArray.push(dataToPush);
              }
              // dataArray.sort((a, b) => a[0] - b[0]);
              return dataArray;
            };

            const processTimeRangeData = (data, key) => {
              const dataArr = {
                raw: [],
                unprocessed_raw: [],
              };
              data.forEach((thing_data, ind) => {
                const nextData = data[ind + 1];
                const timeGap = nextData
                  ? nextData.time - thing_data.time
                  : null;
                if (timeGap > timeout) {
                  pushGraphData(
                    dataArr.raw,
                    thing_data.time,
                    thing_data.parameter_values[key],
                    false,
                  );
                  pushGraphData(
                    dataArr.unprocessed_raw,
                    thing_data.time,
                    thing_data.unprocessed_parameter_values[key],
                    true,
                  );
                  pushGraphData(dataArr.raw, thing_data.time + timeout, null);
                  pushGraphData(
                    dataArr.unprocessed_raw,
                    thing_data.time + timeout,
                    null,
                    true,
                  );
                } else {
                  pushGraphData(
                    dataArr.raw,
                    thing_data.time,
                    thing_data.parameter_values[key],
                  );
                  pushGraphData(
                    dataArr.unprocessed_raw,
                    thing_data.time,
                    thing_data.unprocessed_parameter_values[key],
                    true,
                  );
                }
              });
              return dataArr;
            };

            // Single-pass processing for ALL parameters
            const processAllParametersTimeRangeData = (data, paramKeys, timeout) => {
              const allParamData = {};
              
              // Initialize data structures for all parameters
              paramKeys.forEach(key => {
                allParamData[key] = {
                  raw: new Array(data.length),
                  unprocessed_raw: new Array(data.length),
                };
              });

              // Single iteration through the dataset - process ALL parameters together
              data.forEach((thing_data, ind) => {
                const nextData = data[ind + 1];
                const timeGap = nextData ? nextData.time - thing_data.time : null;
                
                paramKeys.forEach(key => {
                  if (timeGap > timeout) {
                    pushGraphData(
                      allParamData[key].raw,
                      thing_data.time,
                      thing_data.parameter_values[key],
                      false,
                      ind,
                    );
                    pushGraphData(
                      allParamData[key].unprocessed_raw,
                      thing_data.time,
                      thing_data.unprocessed_parameter_values[key],
                      true,
                      ind,
                    );
                    pushGraphData(allParamData[key].raw, thing_data.time + timeout, null, false, ind);
                    pushGraphData(
                      allParamData[key].unprocessed_raw,
                      thing_data.time + timeout,
                      null,
                      true,
                      ind,
                    );
                  } else {
                    pushGraphData(
                      allParamData[key].raw,
                      thing_data.time,
                      thing_data.parameter_values[key],
                      false,
                      ind,
                    );
                    pushGraphData(
                      allParamData[key].unprocessed_raw,
                      thing_data.time,
                      thing_data.unprocessed_parameter_values[key],
                      true,
                      ind,
                    );
                  }
                });
              });
              
              return allParamData;
            };

            const processAggregatedData = (
              start,
              end,
              interval,
              key,
              dataKey,
            ) => {
              const data = {};
              if (!data[key]) {
                data[key] = [];
              }
              if (interval > 86400) {
                if (dataObj[thing_id]?.length) {
                  dataObj[thing_id].map(function (paramData) {
                    const value = parseFloat(
                      parseFloat(
                        paramData.parameter_values[key]?.[dataKey],
                      )?.toFixed(2),
                    );
                    data[key].push([getTime(paramData.time), value]);
                  });
                }
              } else {
                for (let aggrInd = start; aggrInd <= end; aggrInd += interval) {
                  const index = ctx["_"].findIndex(dataObj[thing_id], {
                    time: aggrInd,
                  });
                  const value =
                    index > -1
                      ? parseFloat(
                          parseFloat(
                            dataObj[thing_id][index].parameter_values[key]?.[
                              dataKey
                            ],
                          )?.toFixed(2),
                        )
                      : null;
                  data[key].push([getTime(aggrInd), value]);
                }
              }
              return data;
            };

            // Instead of calling processAggregatedData 3 times, process once
            // const processAllAggregatedData = (start, end, interval, key) => {
            //   const data = { avg: [], min: [], max: [] };
              
            //   if (interval > 86400) {
            //     dataObj[thing_id]?.forEach(paramData => {
            //       const avgVal = parseFloat(paramData.parameter_values[key]?.avg)?.toFixed(2);
            //       const minVal = parseFloat(paramData.parameter_values[key]?.min)?.toFixed(2);
            //       const maxVal = parseFloat(paramData.parameter_values[key]?.max)?.toFixed(2);
            //       const time = getTime(paramData.time);
                  
            //       data.avg.push([time, avgVal]);
            //       data.min.push([time, minVal]);
            //       data.max.push([time, maxVal]);
            //     });
            //   } else {
            //     // Create lookup map once
            //     const dataMap = new Map();
            //     dataObj[thing_id]?.forEach(item => dataMap.set(item.time, item));
                
            //     for (let aggrInd = start; aggrInd <= end; aggrInd += interval) {
            //       const item = dataMap.get(aggrInd);
            //       const time = getTime(aggrInd);
                  
            //       data.avg.push([time, item ? parseFloat(item.parameter_values[key]?.avg)?.toFixed(2) : null]);
            //       data.min.push([time, item ? parseFloat(item.parameter_values[key]?.min)?.toFixed(2) : null]);
            //       data.max.push([time, item ? parseFloat(item.parameter_values[key]?.max)?.toFixed(2) : null]);
            //     }
            //   }
            //   return data;
            // };

            const findFromTime = ctx["_"].find(dataObj[thing_id], {
              time: options.from_time,
            });
            const findUptoTime = ctx["_"].find(dataObj[thing_id], {
              time: options.upto_time,
            });
            if (options.graph_view === "one") {
              parameters[thing_id].forEach((param, param_index) => {
                if (!param) return;
                const graphDataReport = {
                  raw: {},
                  unprocessed_raw: {},
                  avg: {},
                };
                if (!graphDataReport["raw"][param.key]) {
                  graphDataReport["raw"][param.key] = [];
                }
                if (!graphDataReport["unprocessed_raw"][param.key]) {
                  graphDataReport["unprocessed_raw"][param.key] = [];
                }
                if (!graphDataReport["avg"][param.key]) {
                  graphDataReport["avg"][param.key] = [];
                }
                
                const color = color_arr[param_index % color_arr.length];
                const paramName = `${param.name} ${selectedDataModel?.length > 1 ? "(Processed)" : ""}`;
                if (selectedDataModel?.length > 1) {
                  selectedDataModel.forEach(
                    () =>
                      yAxisValue.push({
                        labels: { style: { color: "#7686A1" } },
                        title: {
                          text: findParamUnit[param.unit] || param.unit,
                          style: { color: "#7686A1" },
                        },
                        tickInterval: graphTickInterval,
                        ...(input.clientId === 23 && { tickPixelInterval: 30 }),
                        opposite: true,
                        showEmpty: false,
                      }),
                    // plotTitle.push(paramName[param.key]),
                  );
                } else {
                  yAxisValue.push({
                    labels: { style: { color: "#7686A1" } },
                    title: {
                      text: findParamUnit[param.unit] || param.unit,
                      style: { color: "#7686A1" },
                    },
                    tickInterval: graphTickInterval,
                    ...(input.clientId === 23 && { tickPixelInterval: 30 }),
                    showEmpty: false,
                  });
                  // plotTitle.push(paramName[param.key]);
                }
                if (options.dtype === "raw") {
                  if (findFromTime) {
                    pushGraphData(
                      graphDataReport.raw[param.key],
                      findFromTime.time,
                      findFromTime.parameter_values[param.key]?.["raw"],
                    );
                    pushGraphData(
                      graphDataReport.unprocessed_raw[param.key],
                      findFromTime.time,
                      findFromTime.unprocessed_parameter_values?.[param.key]?.[
                        "raw"
                      ],
                      true,
                    );
                  } else {
                    pushGraphData(
                      graphDataReport.raw[param.key],
                      options.from_time,
                      null,
                    );
                    pushGraphData(
                      graphDataReport.unprocessed_raw[param.key],
                      options.from_time,
                      null,
                      true,
                    );
                  }
                  const processedTimeData = processTimeRangeData(dataObj[thing_id], param.key);
                  graphDataReport.raw[param.key] = [
                    ...graphDataReport.raw[param.key],
                    ...processedTimeData?.raw,
                  ];
                  graphDataReport.unprocessed_raw[param.key] = [
                    ...graphDataReport.unprocessed_raw[param.key],
                    ...processedTimeData?.unprocessed_raw,
                  ];
                  if (findUptoTime) {
                    pushGraphData(
                      graphDataReport.raw[param.key],
                      findUptoTime.time,
                      findUptoTime.parameter_values[param.key]["raw"],
                    );
                    pushGraphData(
                      graphDataReport.unprocessed_raw[param.key],
                      findUptoTime.time,
                      findUptoTime.unprocessed_parameter_values?.[param.key]?.[
                        "raw"
                      ],
                      true,
                    );
                  } else {
                    pushGraphData(
                      graphDataReport.raw[param.key],
                      options.upto_time,
                      null,
                    );
                    pushGraphData(
                      graphDataReport.unprocessed_raw[param.key],
                      options.upto_time,
                      null,
                      true,
                    );
                  }
                  seriesData.push({
                    name: paramName,
                    color: color,
                    tooltip: {
                      valueSuffix: ` ${findParamUnit[param.unit] || param.unit}`,
                    },
                    yAxis: param_index,
                    data: graphDataReport["raw"][param.key],
                  });
                  if (selectedDataModel.includes("unprocessed")) {
                    seriesData.push({
                      name: `${param.name} (Unprocessed)`,
                      color: color_arr[(param_index % color_arr.length) + 1],
                      tooltip: {
                        valueSuffix: ` ${findParamUnit[param.unit] || param.unit}`,
                      },
                      yAxis: param_index,
                      data: graphDataReport["unprocessed_raw"][param.key],
                    });
                  }
                } else {
                  graphDataReport["avg"][param.key] = processAggregatedData(
                    options.from_time,
                    options.upto_time,
                    options.tinterval,
                    param.key,
                    avgValueAsPerParam(param.key, "avg"),
                  )?.[param.key];
                  seriesData.push({
                    name: paramName,
                    color: color,
                    tooltip: {
                      valueSuffix: ` ${findParamUnit[param.unit] || param.unit}`,
                    },
                    yAxis: param_index,
                    data: graphDataReport["avg"][param.key],
                  });
                }
              });
              const graphConfig = createGraphConfig(
                seriesData,
                yAxisValue,
                plotTitle,
              );
              finalData[thing_id].all.conf.push({
                props: { gutter: 10, style: {}, className: "rowGraph" },
                child: [
                  {
                    compo: "Graph",
                    classname: "graph-1",
                    props: {
                      id: `graph-id-${thingIds}-${index}-${thingIds}`,
                    },
                    col_props: { span: 24 },
                    pdf_width: 700,
                    pdf_height: 250,
                    pdf_force_new_page: true,
                    datatype: {
                      "xAxis.categories": "datetime::HH:MM",
                      "series.data": "number::1",
                    },
                  },
                ],
              });
              finalData[thing_id].all.data.push([graphConfig]);
              finalData[thing_id].graph.conf.push({
                props: { gutter: 10, style: {}, className: "rowGraph" },
                child: [
                  {
                    compo: "Graph",
                    classname: "graph-1",
                    props: {
                      id: `graph-id-${thingIds}-${index}-${thingIds}`,
                    },
                    col_props: { span: 24 },
                    pdf_force_new_page: true,
                    datatype: {
                      "xAxis.categories": "datetime::HH:MM",
                      "series.data": "number::1",
                    },
                  },
                ],
              });
              finalData[thing_id].graph.data.push([graphConfig]);
            } else {
              const paramKeys = parameters[thing_id].filter(param => param).map(param => param.key);
              
              let allProcessedData = {};
              if (options.dtype === "raw" && paramKeys.length > 0) {
                allProcessedData = processAllParametersTimeRangeData(dataObj[thing_id], paramKeys, timeout);
              }

              parameters[thing_id].forEach((param, param_index) => {
                const graphDataReport = {
                  raw: {},
                  unprocessed_raw: {},
                  avg: {},
                  min: {},
                  max: {},
                  range: {},
                  value: {},
                };
                if (!param) return;
                if (!graphDataReport["raw"][param.key]) {
                  graphDataReport["raw"][param.key] = [];
                }
                if (!graphDataReport["unprocessed_raw"][param.key]) {
                  graphDataReport["unprocessed_raw"][param.key] = [];
                }
                if (!graphDataReport["avg"][param.key]) {
                  graphDataReport["avg"][param.key] = [];
                }
                if (!graphDataReport["min"][param.key]) {
                  graphDataReport["min"][param.key] = [];
                }
                if (!graphDataReport["max"][param.key]) {
                  graphDataReport["max"][param.key] = [];
                }
                if (!graphDataReport["range"][param.key]) {
                  graphDataReport["range"][param.key] = [];
                }
                const avgDataFormatValue = avgValueAsPerParam(param.key, "avg");
                if (!graphDataReport[avgDataFormatValue][param.key]) {
                  graphDataReport[avgDataFormatValue][param.key] = [];
                }
                if (options.dtype === "raw") {
                  if (findFromTime) {
                    pushGraphData(
                      graphDataReport.raw[param.key],
                      findFromTime.time,
                      findFromTime.parameter_values[param.key]?.["raw"],
                    );
                    pushGraphData(
                      graphDataReport.unprocessed_raw[param.key],
                      findFromTime.time,
                      findFromTime.unprocessed_parameter_values?.[param.key]?.[
                        "raw"
                      ],
                      true,
                    );
                  } else {
                    pushGraphData(
                      graphDataReport.raw[param.key],
                      options.from_time,
                      null,
                    );
                    pushGraphData(
                      graphDataReport.unprocessed_raw[param.key],
                      options.from_time,
                      null,
                      true,
                    );
                  }
                  graphDataReport.raw[param.key] = [
                    ...graphDataReport.raw[param.key],
                    ...allProcessedData[param.key]?.raw,
                  ];
                  graphDataReport.unprocessed_raw[param.key] = [
                    ...graphDataReport.unprocessed_raw[param.key],
                    ...allProcessedData[param.key]?.unprocessed_raw,
                  ];
                  if (findUptoTime) {
                    pushGraphData(
                      graphDataReport.raw[param.key],
                      findUptoTime.time,
                      findUptoTime.parameter_values[param.key]["raw"],
                    );
                    pushGraphData(
                      graphDataReport.unprocessed_raw[param.key],
                      findUptoTime.time,
                      findUptoTime.unprocessed_parameter_values?.[param.key]?.[
                        "raw"
                      ],
                      true,
                    );
                  } else {
                    pushGraphData(
                      graphDataReport.raw[param.key],
                      options.upto_time,
                      null,
                    );
                    pushGraphData(
                      graphDataReport.unprocessed_raw[param.key],
                      options.upto_time,
                      null,
                      true,
                    );
                  }
                  const graphSeriesData = [];
                  if (selectedDataModel.length > 1) {
                    if (selectedDataModel.includes("processed")) {
                      graphSeriesData.push({
                        name: `${param.name} (Raw)`,
                        color: color_arr[param_index % color_arr.length],
                        data: graphDataReport.raw[param.key],
                      });
                    }
                    if (selectedDataModel.includes("unprocessed")) {
                      graphSeriesData.push({
                        name: `${param.name} (Unprocessed)`,
                        color: color_arr[param_index % color_arr.length],
                        data: graphDataReport.unprocessed_raw[param.key],
                      });
                    }
                  } else {
                    if (selectedDataModel.includes("unprocessed")) {
                      graphSeriesData.push({
                        name: `${param.name} (Unprocessed)`,
                        color: color_arr[param_index % color_arr.length],
                        data: graphDataReport.unprocessed_raw[param.key],
                      });
                    } else {
                      graphSeriesData.push({
                        name: `${param.name}`,
                        color: color_arr[param_index % color_arr.length],
                        data: graphDataReport.raw[param.key],
                      });
                    }
                  }
                  const graphData = {
                    chart: {
                      zoomType: "x",
                      type: "spline",
                      width: isDownload ? 1280 : undefined,
                      height: 300,
                    },
                    title: { text: param.name.replace(/<[^>]+>/g, "") },
                    xAxis: {
                      type: "datetime",
                      labels: {
                        format:
                          options.dtype === "raw" || options.tinterval === 3600
                            ? graphHourMinXAxis
                            : "{value:%e %b}",
                        align: "left",
                      },
                      title: { text: "Date Time" },
                    },
                    yAxis: {
                      title: {
                        text: findParamUnit[param.unit] || param.unit,
                      },
                      tickInterval: graphTickInterval,
                    },
                    timezone: timeZone,
                    credits: { enabled: false },
                    tooltip: {
                      crosshairs: true,
                      shared: true,
                      valueSuffix: ` ${findParamUnit[param.unit] || param.unit}`,
                      valueDecimals: 2,
                      xDateFormat:
                        options.dtype === "raw"
                          ? `%A, %B %d, ${graphHourMinSecVariable}`
                          : [3600, 900, 28800].includes(options.tinterval)
                            ? `%A, %B %d, ${graphHourMinVariable}`
                            : `%A, %B %d`,
                    },
                    series: graphSeriesData,
                  };
                  finalData[thing_id].all.conf.push({
                    props: { gutter: 10, style: {}, className: "rowGraph" },
                    child: [
                      {
                        compo: "Graph",
                        classname: "graph-1",
                        props: {
                          id: `graph-id-${thingIds}-${param_index}-${param.key}`,
                        },
                        col_props: { span: 24 },
                        pdf_width: 700,
                        pdf_height: 250,
                        pdf_force_new_page: true,
                        datatype: {
                          "xAxis.categories": "datetime::HH:MM",
                          "series.data": "number::1",
                        },
                      },
                    ],
                  });
                  finalData[thing_id].all.data.push([graphData]);
                  finalData[thing_id].graph.conf.push({
                    props: { gutter: 10, style: {}, className: "rowGraph" },
                    child: [
                      {
                        compo: "Graph",
                        classname: "graph-1",
                        props: {
                          id: `graph-id-$${thingIds}-${param_index}-${param.key}`,
                        },
                        col_props: { span: 24 },
                        pdf_force_new_page: true,
                        datatype: {
                          "xAxis.categories": "datetime::HH:MM",
                          "series.data": "number::1",
                        },
                      },
                    ],
                  });

                  finalData[thing_id].graph.data.push([graphData]);
                } else {
                  graphDataReport[avgDataFormatValue][param.key] =
                    processAggregatedData(
                      options.from_time,
                      options.upto_time,
                      options.tinterval,
                      param.key,
                      avgDataFormatValue,
                    )?.[param.key];
                  graphDataReport["min"][param.key] = processAggregatedData(
                    options.from_time,
                    options.upto_time,
                    options.tinterval,
                    param.key,
                    "min",
                  )?.[param.key];
                  graphDataReport["max"][param.key] = processAggregatedData(
                    options.from_time,
                    options.upto_time,
                    options.tinterval,
                    param.key,
                    "max",
                  )?.[param.key];
                  if (graphDataReport["min"][param.key]?.length) {
                    graphDataReport["min"][param.key].map(
                      function (minData, minInd) {
                        graphDataReport["range"][param.key].push([
                          minData[0],
                          minData[1],
                          graphDataReport["max"][param.key][minInd][1],
                        ]);
                      },
                    );
                  }
                  const avgData = createGraphConfig(
                    [
                      {
                        name: "Avg",
                        type: param.key === "aqi" ? "area" : "spline",
                        zIndex: 1,
                        tooltip: {
                          valueSuffix: ` ${findParamUnit[param.unit] || param.unit}`,
                        },
                        dataGrouping: {
                          enabled: true,
                          forced: true,
                          units: [["day", [2]]],
                        },
                        data: graphDataReport[avgDataFormatValue][param.key],
                        color: color_arr[param_index % color_arr.length],
                      },
                      {
                        name: "Min-max range",
                        dataGrouping: {
                          enabled: true,
                          forced: true,
                          units: [["day", [2]]],
                        },
                        tooltip: {
                          valueSuffix: ` ${findParamUnit[param.unit] || param.unit}`,
                        },
                        fillOpacity: 0.3,
                        zIndex: 0,
                        type: "arearange",
                        lineWidth: 0,
                        data: graphDataReport["range"][param.key],
                        color: minmaxColor[param_index % minmaxColor.length],
                      },
                    ],
                    {
                      title: { text: findParamUnit[param.unit] || param.unit },
                      tickInterval: graphTickInterval,
                    },
                    paramName[param.key],
                  );

                  finalData[thing_id].all.conf.push({
                    props: { gutter: 10, style: {}, className: "rowGraph" },
                    child: [
                      {
                        compo: "Graph",
                        classname: "graph-1",
                        props: {
                          id: `graph-id-${thingIds}-${param_index}-${param.key}`,
                        },
                        col_props: { span: 24 },
                        pdf_force_new_page: true,
                        datatype: {
                          "xAxis.categories": "datetime::HH:MM",
                          "series.data": "number::1",
                        },
                      },
                    ],
                  });
                  finalData[thing_id].all.data.push([avgData]);

                  finalData[thing_id].graph.conf.push({
                    props: { gutter: 10, style: {}, className: "rowGraph" },
                    child: [
                      {
                        compo: "Graph",
                        classname: "graph-1",
                        props: {
                          id: `graph-id-${thingIds}-${param_index}-${param.key}`,
                        },
                        col_props: { span: 24 },
                        pdf_force_new_page: true,
                        datatype: {
                          "xAxis.categories": "datetime::HH:MM",
                          "series.data": "number::1",
                        },
                      },
                    ],
                  });

                  finalData[thing_id].graph.data.push([avgData]);
                }
              });
            }
            if (!analysisFormatChecked) {
              finalData[thing_id].all.conf.push({
                props: {
                  gutter: 10,
                  style: {
                    "margin-top": 20,
                  },
                },
                child: [pushObj],
              });
              finalData[thing_id].all.data.push([
                {
                  textData: ["Detailed Data"],
                },
              ]);
            }

            const detailedTableColumnsDownload = [...detailedTableColumns];
            if (analysisFormatChecked) {
              detailedTableColumnsDownload.splice(1, 0, {
                title: "Asset ID",
                dataIndex: "asset_id",
                width: 500,
              });
              detailedTableColumnsDownload.splice(2, 0, {
                title: "Asset Name",
                dataIndex: "asset_name",
                width: 500,
              });
            }
            finalData[thing_id].all.conf.push({
              props: {
                gutter: 10,
                className: "tableRow",
              },
              child: [
                {
                  compo: "Table",
                  widget: "",
                  classname: "tab-2",
                  table_new_page: true,
                  hellipRow: true,
                  props: {
                    columns: detailedTableColumnsDownload,
                    headerFont: 13,
                    horizontalScroll: true,
                    isGrouped: true,
                    confColumnLength1: 3,
                    confColumnLength2: 3,
                    level: 2,
                    shadow: false,
                    breakPoint: 1000,
                    breakPoint2: 500,
                    largeTable: true,
                    mediumTable: false,
                    smallTable: false,
                  },
                  col_props: {
                    span: 24,
                  },
                  // datatype: detailedTableDataTypes,
                  pdf_width: 50,
                  pdf_table_break: {
                    col_no: 12,
                    row_no: 20,
                  },
                },
              ],
            });
            finalData[thing_id].all.data.push([detailedTableData]);
          }
        });
        reportData = finalData;
      }

      let downloaderReportConfig = {
        conf: [],
        zone: timeZone,
        data: [],
        parameters: options.tpara,
      };
      let totalThings = thingArr.length;

      let pdfMainHeaderOptionDate = {
        pdf_text_align: "center",
        textColor: [255, 255, 255],
        pdf_size: 13,
        fill: {
          fill_color: defaultColor,
          y_value: 15,
          top: 1,
        },
      };
      let pushObjDate = pdfMainHeaderOptionDate;
      pushObjDate["compo"] = "Text";
      pushObjDate["props"] = {
        type: "bold",
      };
      pushObjDate["col_props"] = {
        span: 24,
      };
      let pdfMainHeaderOption = {
        pdf_text_align: "center",
        textColor: [255, 255, 255],
        pdf_size: 10,
        fill: {
          fill_color: defaultColor,
          y_value: 13,
          top: 1,
        },
      };
      let pushObj = pdfMainHeaderOption;
      pushObj["compo"] = "Text";
      pushObj["props"] = {
        type: "bold",
      };
      pushObj["col_props"] = {
        span: 24,
      };
      let REPORT_TYPE = {
        text_conf: {
          props: {
            gutter: 5,
          },
          child: [
            {
              pdf_text_align: "center",
              textColor: [255, 255, 255],
              pdf_size: 16,
              type: "bold",
              fill: {
                fill_color: defaultColor,
                y_value: 24,
                top: 8,
              },
              compo: "Text",
              props: {
                type: "bold",
              },
              col_props: {
                span: 24,
              },
              secStyle: {
                body: {
                  font: {
                    size: 20,
                    bold: true,
                  },
                },
              },
            },
            pushObjDate,
            pushObj,
          ],
        },
        text_data: [
          {
            textData: [
              options.dtype.toUpperCase() + " Report - " + client_name,
            ],
          },
          {
            textData: [
              "From: " +
                ctx
                  .moment(options.from_time, "X")
                  .tz(timeZone)
                  .format("DD MMM YYYY, " + hourMinVariable) +
                " to " +
                ctx
                  .moment(options.upto_time, "X")
                  .tz(timeZone)
                  .format("DD MMM YYYY, " + hourMinVariable),
            ],
          },
          {
            textData: [
              "Generated on: " +
                ctx.moment
                  .tz(timeZone)
                  .format("DD MMM YYYY, " + hourMinVariable),
              "* All the times are in " +
                (timeFormat === "12_hr" ? "12" : "24") +
                " Hours time format",
              "",
            ],
          },
        ],
      };

      downloaderReportConfig.conf.push(REPORT_TYPE.text_conf);
      downloaderReportConfig.data.push(REPORT_TYPE.text_data);
      let downloadFileName =
        (isAurassure ? vendorName : "DATOMS") +
        " - " +
        (options.dtype === "average"
          ? (options.tinterval === 28800
              ? "8-Hourly "
              : options.tinterval === 900
                ? "15 Min "
                : options.tinterval === 86400
                  ? "Daily"
                  : "1 Hr") + " Average Report"
          : options.dtype === "raw"
            ? "Raw Report"
            : "Cumulative Report") +
        ", " +
        ctx.moment.tz(timeZone).format("DD MMM YYYY, " + hourMinVariable);

      if (options.dataOfAllAssets) {
        pageConfig = {
          pdf_force_new_page: false,
          secStyle: {
            body: {
              font: {
                size: 16,
                bold: true,
              },
            },
          },
        };
        if (!(fileFormat === "csv" && analysisFormatChecked)) {
          let textPush = pageConfig;
          textPush["compo"] = "Text";
          textPush["type"] = "bold";
          textPush["col_props"] = {
            span: 24,
          };
          textPush["pdf_size"] = 11;
          textPush["excelNewSheet"] = true;
          let { text_conf, text_data } = {
            text_conf: {
              props: {
                gutter: 10,
              },
              child: [textPush],
            },
            text_data: [
              {
                textData: ["", "Summary"],
              },
            ],
          };

          downloaderReportConfig.conf.push(text_conf);
          downloaderReportConfig.data.push(text_data);
        }
        let { all } = summaryReportData;
        let { conf, data, zone, file_name } = all;
        conf.forEach(function (conf_item) {
          downloaderReportConfig.conf.push(conf_item);
        });

        data.forEach(function (data_item) {
          downloaderReportConfig.data.push(data_item);
        });
        downloaderReportConfig.zone = timeZone;
        downloaderReportConfig.file_name = downloadFileName;
      }
      if (thingArr?.length) {
        thingArr.map((thing, i) => {
          const findThing = ctx["_"].find(totalThingsData?.things, {
            id: thing,
          });
          if (reportData && findThing) {
            let { id, name } = findThing;
            let { all } = reportData[id];
            let { conf, data, zone, file_name } = all;
            let pageConfig = {
              pdf_force_new_page: false,
              secStyle: {
                body: {
                  font: {
                    size: 16,
                    bold: true,
                  },
                },
              },
            };
            if (i === 0) {
              pageConfig["pdf_force_new_page"] = options.dataOfAllAssets
                ? true
                : false;
            }
            if (i > 0) {
              pageConfig = {
                pdf_force_new_page: true,
                secStyle: {
                  body: {
                    font: {
                      size: 16,
                      bold: true,
                    },
                  },
                },
              };
            }
            if (!(fileFormat === "csv" && analysisFormatChecked)) {
              let textPush = pageConfig;
              textPush["compo"] = "Text";
              textPush["type"] = "bold";
              textPush["col_props"] = {
                span: 24,
              };
              textPush["pdf_size"] = 11;
              textPush["excelNewSheet"] = true;
              let { text_conf, text_data } = {
                text_conf: {
                  props: {
                    gutter: 10,
                  },
                  child: [textPush],
                },
                text_data: [
                  {
                    textData: ["", "Asset: " + name],
                  },
                ],
              };

              downloaderReportConfig.conf.push(text_conf);
              downloaderReportConfig.data.push(text_data);
            }

            conf.forEach(function (conf_item) {
              downloaderReportConfig.conf.push(conf_item);
            });

            data.forEach(function (data_item) {
              downloaderReportConfig.data.push(data_item);
            });
            downloaderReportConfig.zone = timeZone;
            downloaderReportConfig.file_name = downloadFileName;
          }
        });
      }
      // cb({
      //   summaryReportData: cleanDataForSerialization(summaryReportData),
      //   reportData: cleanDataForSerialization(reportData),
      //   downloaderReportConfig: cleanDataForSerialization(downloaderReportConfig),
      // });

      return {
        summaryReportData: summaryReportData,
        reportData: reportData,
        downloaderReportConfig: downloaderReportConfig,
      };
    },
    cb: (value) => {
      console.log("value->", value);
const { summaryReportData, reportData, downloaderReportConfig } = value?.result || {};
      this.setState(
        {
          ...(!isDownload ? {
              dataViewType:
                options.r_view.length === 1
                  ? options.r_view[0] === "graph"
                    ? true
                    : false
                  : false,
              disabled: options.r_view.length === 1 ? true : false,
              reportData: reportData || {},
              summaryReportData: summaryReportData || {},
              pageLoading: false,
          }: {
              downloaderReportConfig: downloaderReportConfig,
              // downloadLoading: false,
          })
        },
        () => {
          if (isDownload) {
            this.getDownloadRender();
          }
        },
      );
    },
  });
}
