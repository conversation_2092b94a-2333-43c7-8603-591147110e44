'use strict';

// Object.defineProperty(exports, '__esModule', {
// 	value: true,
// });
// exports.default = void 0;

function _newArrowCheck(innerThis, boundThis) {
	if (innerThis !== boundThis) {
		throw new TypeError('Cannot instantiate an arrow function');
	}
}

function _classCallCheck(instance, Constructor) {
	if (!(instance instanceof Constructor)) {
		throw new TypeError('Cannot call a class as a function');
	}
}

function _classPrivateFieldLooseBase(receiver, privateKey) {
	if (!Object.prototype.hasOwnProperty.call(receiver, privateKey)) {
		throw new TypeError('attempted to use private field on non-instance');
	}
	return receiver;
}

var id = 0;

function _classPrivateFieldLooseKey(name) {
	return '__private_' + id++ + '_' + name;
}

var _worker = _classPrivateFieldLooseKey('worker');

var _autoTerminate = _classPrivateFieldLooseKey('autoTerminate');

var _runInBackyard = _classPrivateFieldLooseKey('runInBackyard');

var Backyard = function Backyard(_config) {
	var _this = this;

	_classCallCheck(this, Backyard);

	Object.defineProperty(this, _worker, {
		writable: true,
		value: null,
	});
	Object.defineProperty(this, _autoTerminate, {
		writable: true,
		value: true,
	});

	this.terminate = function () {
		_newArrowCheck(this, _this);

		console.log('Worker Terminated');

		_classPrivateFieldLooseBase(this, _worker)[_worker].terminate();
	}.bind(this);

	Object.defineProperty(this, _runInBackyard, {
		writable: true,
		value: function value(config) {
			var _this2 = this;

			_newArrowCheck(this, _this);

			var cb = config.cb,
				run = config.run,
				scripts = config.scripts,
				autoTerminate = config.autoTerminate,
				input = config.input;

			if (autoTerminate != null) {
				_classPrivateFieldLooseBase(this, _autoTerminate)[
					_autoTerminate
				] = autoTerminate;
			}

			// var workercode = function workercode() {
			// 	var onmessage = function onmessage(e) {
			// 		console.log('onmessage recieved');
			// 		var data = e.data;
			// 		var workerFunction = data.workerFunction,
			// 			input = data.input;

			// 		var cb = function cb(obj) {
			// 			obj = JSON.parse(JSON.stringify(obj));
			// 			postMessage(obj);
			// 		};

			// 		var myFunc = function myFunc(a, b, c) {
			// 			return eval(workerFunction)(a, b, c);
			// 		};

			// 		var returnedValue = myFunc(this, input, cb);

			// 		if (returnedValue != null) {
			// 			postMessage({
			// 				result: returnedValue,
			// 			});
			// 		}
			// 	};
			// };

			var code=`var onmessage=function(e){console.log('onmessage received');var data=e.data;var workerFunction=data.workerFunction,input=data.input;var cb=function(obj){postMessage(obj)};var myFunc=function(a,b,c){return eval(workerFunction)(a,b,c)};var returnedValue=myFunc(this,input,cb);if(null!=returnedValue){postMessage({result:returnedValue})}};`;

			// var code = workercode.toString();
			var transCode = '';
			transCode +=
				'function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } };';
			transCode +=
				'function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } };';
			transCode +=
				'function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; };';
			var importScriptCode = '';

			for (var i = 0; i < scripts.length; i++) {
				importScriptCode += "importScripts('" + scripts[i] + "');";
			}

			code =
				transCode +
				importScriptCode +
				code;
				// code.substring(code.indexOf('{') + 1, code.lastIndexOf('}'));
			/* Converting to a Blob of type js file */

			var blob = new Blob([code], {
				type: 'application/javascript',
			});
			/* Creating a URL for the Blob Object */

			var worker_script = URL.createObjectURL(blob);
			var w = new Worker(worker_script);

			w.onmessage = function (event) {
				_newArrowCheck(this, _this2);

				var result = event.data.result;
				if (
					_classPrivateFieldLooseBase(this, _autoTerminate)[
						_autoTerminate
					] &&
					result != null
				)
					this.terminate();
				if (cb) cb(event.data);
			}.bind(this);

			w.postMessage({
				workerFunction: '('.concat(run) + ').bind(this)',
				input: input,
			});
			_classPrivateFieldLooseBase(this, _worker)[_worker] = w;
		}.bind(this),
	});

	if (_config) {
		var cb = _config.cb,
			run = _config.run,
			scripts = _config.scripts,
			autoTerminate = _config.autoTerminate,
			input = _config.input;

		if (!scripts) {
			scripts = [];
		}

		if (run && cb && scripts) {
			_classPrivateFieldLooseBase(this, _runInBackyard)[_runInBackyard]({
				cb: cb,
				run: run,
				scripts: scripts,
				autoTerminate: autoTerminate,
				input: input,
			});
		} else {
			console.log('Backyard Error: Invalid Configuration');
		}
	} else {
		console.log('Backyard Error: No Configuration!!');
	}
};
/* EXAMPLE USAGE */

/*
// EXAMPLE 1

new Backyard({
    input: {
        message: "Hi from Frontend!!"
    },
    run: (ctx, input) => {

        for (let i = 0; i < 1000; i++) {
            console.log("i:", i, input.message);
            for (let j = 0; j < 5000000; j++) {
                const x = (i*j)*(i+j);
            }
        }

        ctx.postMessage({
            result: 1024
        });
    },
    cb: (data) => {
        const { result } = data;
        console.log("result", result);
    }
});

// EXAMPLE 2

new Backyard({
      input: {
        message: "Hi from Frontend!!"
      },
      run: (ctx, input, cb) => {

        for (let i = 0; i < 1000; i++) {
          console.log("i:", i, input.message);
          for (let j = 0; j < 5000000; j++) {
            const x = (i*j)*(i+j);
          }
        }


        cb({
          result: 1024
        });
      },
      cb: (data) => {
        console.log(data);
      }
    });

// EXAMPLE 3
new Backyard({
            scripts: [],
            input: {
                message: "Hi from Frontend Which went to te Backyard!!",
                time: moment().unix()
            },
            run: (ctx, input, cb) => {

                for (let i = 0; i < 1000; i++) {
                    console.log("i:", i, input.time);
                    for (let j = 0; j < 10000000; j++) {
                        const x = (i*j)*(i+j);
                    }
                }

                return "Returned 121"
            },
            cb: (data) => {
                console.log(data);
            }
        })
*/

export default Backyard;
