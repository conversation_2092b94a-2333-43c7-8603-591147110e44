const MIXPANEL_ENABLED = import.meta.env.VITE_BUILD_MODE === "production" //&& !import.meta.env.VITE_REVIEW_PROD

  export function mixPanelRegisterUser(data) {
    try {
      if (!MIXPANEL_ENABLED) return;
  
      if (window.cordova) {
        if (window.cordova.platformId === "ios") {
          return;
        } else {
          window.cordova.plugins.cordovaDatoms.registerUser(
            data,
            (a) => console.log("succ", a),
            (b) => console.log("error", b)
          );
        }
      } else if (window.mixpanel?.__loaded && data?.Email && data?.Customer) {
        window.mixpanel.identify(data.Email);
        window.mixpanel.people.set({ customer: data.Customer, Name: data.Name, Email: data.Email });
        window.mixpanel.register(data);
      } else {
        console.warn('Mixpanel not loaded, or data missing:', data);
      }
    } catch (e) {
      console.log("mixPanelRegisterUser Error", e);
    }
  }
  
  export function mixPanelTrackUser(name, data = {}) {
    try {
      if (!MIXPANEL_ENABLED) return;
  
      if (window.cordova) {
        if (window.cordova.platformId === "ios") {
          return;
        } else {
          window.cordova.plugins.cordovaDatoms.trackUser(
            name,
            data,
            (a) => console.log("succ", a),
            (b) => console.log("error", b)
          );
        }
      } else if (window.mixpanel?.__loaded) {
        window.mixpanel.track(name, data);
      }
    } catch (e) {
      console.log("mixPanelTrackUser Error", e);
    }
  }
  
  export function mixPanelResetUser() {
    try {
      if (!MIXPANEL_ENABLED) return;
  
      if (window.cordova) {
        if (window.cordova.platformId === "ios") {
          return;
        } else {
          window.cordova.plugins.cordovaDatoms.resetUser(
            (a) => console.log("succ", a),
            (b) => console.log("error", b)
          );
        }
      } else if (window.mixpanel?.__loaded) {
        window.mixpanel.reset();
      }
    } catch (e) {
      console.log("mixPanelResetUser Error", e);
    }
  }

  let sessionStartTime = null;

  export function mixPanelStartSession() {
    try {
      if (!MIXPANEL_ENABLED) return;
      sessionStartTime = Date.now();
      mixPanelTrackUser("Session Start", {
        timestamp: new Date().toISOString()
      });
    } catch (e) {
      console.log("mixPanelStartSession Error", e);
    }
  }

  export function mixPanelEndSession() {
    try {
      if (!MIXPANEL_ENABLED || !sessionStartTime) return;
      const endTime = Date.now();
      const durationInSeconds = Math.floor((endTime - sessionStartTime) / 1000);

      mixPanelTrackUser("Session End", {
        timestamp: new Date().toISOString(),
        session_duration: durationInSeconds  // 👈 MUST be this key for Mixpanel to show duration
      });

      sessionStartTime = null;
    } catch (e) {
      console.log("mixPanelEndSession Error", e);
    }
  }