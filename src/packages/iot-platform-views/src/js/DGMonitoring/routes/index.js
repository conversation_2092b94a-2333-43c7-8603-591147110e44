import React, { Component, Suspense } from "react";
import Loading from "@datoms/react-components/src/components/Loading";
import { MessageComponent } from "../components";
/*Lazy imports */
const DgSetRoutes = React.lazy(() => import("./DgSetRoutes"));
const CompRoutes = React.lazy(() => import("./CompressorRoutes"));
const FuelTankRoutes = React.lazy(() => import("./FuelTankRoutes"));
const FleetRoutes = React.lazy(() => import("./FleetRoutes"));
const TankerRoutes = React.lazy(() => import("./TankerRoutes"));
const EnergyMeterRoutes = React.lazy(() => import("./EnergyMeterRoutes"));
const ElectricalMachinesRoutes = React.lazy(
  () => import("./ElectricalMachinesRoutes"),
);
const ReportsRoutes = React.lazy(() => import("./ReportsRoutes"));
const GenericRoutes = React.lazy(() => import("./GenericRoutes"));
const DgMapView = React.lazy(
  () => import("../../../../../dg-monitoring-views/src/js/components/MapView"),
);
const AssetDashboard = React.lazy(
  () =>
    import(
      "../../../../../dg-monitoring-views/src/js/GenericTemplate/pages/AssetDashboard"
    ),
);

const catIdsGeneric = [
  21, 22, 89, 23, 44, 85, 67, 74, 76, 80, 77, 79, 83, 69, 45, 86, 99, 102, 101
];

const clientIdsForGeneric = [
  2156, 1770, 2064, 1381, 1561, 2285, 2301, 1511
];
const isGenericVendorId = [1280, 1121, 14814];

export default class ApplicationPage extends Component {
  constructor(props) {
    super(props);
    this.state = {};
    this.thing_type_map = {
      18: DgSetRoutes,
      96: DgSetRoutes,
      73: GenericRoutes,
      71: FuelTankRoutes,
      67: FleetRoutes,
      74: TankerRoutes,
      76: FleetRoutes,
      77: EnergyMeterRoutes,
      78: ElectricalMachinesRoutes,
    };
  }
  getFinalPage() {
    const { page_name, thing_type, url_params, template_id, parent_vendor_id, client_id } =
      this.props;
    let final_type =
      url_params && url_params["thing_type"]
        ? !isNaN(parseInt(url_params["thing_type"]))
          ? parseInt(url_params["thing_type"])
          : thing_type
        : thing_type;
    if (page_name === "asset-dashboard") {
      return AssetDashboard;
    } else if (page_name.includes("reports")) {
      return ReportsRoutes;
    } else if (
      (catIdsGeneric.includes(final_type) ||
        template_id === 29 ||
        isGenericVendorId.includes(parseInt(parent_vendor_id)) ||
				clientIdsForGeneric.includes(client_id)) &&
      [
        "map-view",
        "detailed-view",
        "real-time",
        "panel-view",
        "list-view",
        "trip-view",
      ].includes(page_name)
    ) {
      return GenericRoutes;
    } else if (
      page_name.includes("map-view") &&
      [73, 18, 71].includes(final_type) &&
      template_id !== 24
    ) {
      return DgMapView;
    } else if (this.thing_type_map[final_type]) {
      return this.thing_type_map[final_type];
    } else {
      return MessageComponent;
    }
  }
  render() {
    const FinalPage = this.getFinalPage();
    console.log("FINAL_PAGE_PROPS", this.props);
    return (
      <Suspense fallback={<Loading />}>
        <FinalPage {...this.props} />
      </Suspense>
    );
  }
}
