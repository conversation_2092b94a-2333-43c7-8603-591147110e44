import React, { Component, Suspense } from 'react';
import Loading from '@datoms/react-components/src/components/Loading';
import { MessageComponent } from '../components';
/*lazy imports */
const MapView = React.lazy(() =>
	import('../../../../../dg-monitoring-views/src/js/GenericTemplate/pages/MapView/pages')
);
const DGDashboard = React.lazy(() =>
	import('../../../../../dg-monitoring-views/src/js/DGMonitoring/Template-17/Dashboard')
);
const DetailedView = React.lazy(() =>
	import('../../../../../dg-monitoring-views/src/js/GenericTemplate/pages/DetailedView')
);
const RealTime = React.lazy(() =>
	import('../../../../../dg-monitoring-views/src/js/GenericTemplate/pages/RealTime')
);
const Panel = React.lazy(() =>
	import('../../../../../dg-monitoring-views/src/js/GenericTemplate/pages/Panel')
);
const AssetDashboard = React.lazy(() =>
	import('../../../../../dg-monitoring-views/src/js/GenericTemplate/pages/AssetDashboard')
);
const TripView = React.lazy(() =>
	import('../../../../../dg-monitoring-views/src/js/GenericTemplate/pages/TripView')
);

const ListView = React.lazy(() =>
	import('../../../../../../containers/views/ListView')
);

// const Workflow = React.lazy(() =>
// 	import('../../../../../dg-monitoring-views/src/js/GenericTemplate/pages/Workflow')
// );

export default class GenericRoutes extends Component {
	constructor(props) {
		super(props);
		this.state = {};
		this.page_map = {
			'map-view': MapView,
			dashboard: DGDashboard,
			'detailed-view': DetailedView,
			'real-time': RealTime,
			'panel-view': Panel,
			'list-view': ListView,
			'asset-dashboard': AssetDashboard,
			'trip-view': TripView,
		};
	}
	getFinalPage() {
		if (this.page_map[this.props.page_name]) {
			return this.page_map[this.props.page_name];
		} else {
			return MessageComponent;
		}
	}
	render() {
		const FinalPage = this.getFinalPage();
		return (
			<Suspense fallback={<Loading />}>
				<FinalPage {...this.props} />
			</Suspense>
		);
	}
}
