import React, { Component } from 'react';
import FilterOutlined from '@ant-design/icons/FilterOutlined';
import EnvironmentOutlined from '@ant-design/icons/EnvironmentOutlined';
import AreaChartOutlined from '@ant-design/icons/AreaChartOutlined';
import LayoutOutlined from '@ant-design/icons/LayoutOutlined';
import ClockCircleOutlined from '@ant-design/icons/ClockCircleOutlined';
import MinusSquareOutlined from '@ant-design/icons/MinusSquareOutlined';
import FileTextOutlined from '@ant-design/icons/FileTextOutlined';
import DownOutlined from '@ant-design/icons/DownOutlined';
import ProfileOutlined from '@ant-design/icons/ProfileOutlined';
import FilterSelectWithSearch from '@datoms/react-components/src/components/FilterSelectWithSearch';
import AntDropdown from '@datoms/react-components/src/components/AntDropdown';
import AntMenu from '@datoms/react-components/src/components/AntMenu';
import AntMenuItem from '@datoms/react-components/src/components/AntMenuItem';
import AntDrawer from '@datoms/react-components/src/components/AntDrawer';
import AntButton from '@datoms/react-components/src/components/AntButton';
import MobileFilterBox from '@datoms/react-components/src/components/MobileFilterBox';

import './style.less';

export default class HeaderFilter extends Component {
	constructor(props) {
		super(props);
		this.state = {};
		this.icon_map = {
			'map-view': EnvironmentOutlined,
			dashboard: AreaChartOutlined,
			'detailed-view': LayoutOutlined,
			'real-time': ClockCircleOutlined,
			'panel-view': MinusSquareOutlined,
			reports: FileTextOutlined,
			'oem-reports': FileTextOutlined,
			'trip-view': ProfileOutlined,
		};
		this.toggleFilter = this.toggleFilter.bind(this);
		this.afterFilterVisible = this.afterFilterVisible.bind(this);
		this.applyClick = this.applyClick.bind(this);
		if(this.props.template_id === 29){
			delete this.icon_map['panel-view'];
		}
	}

	isMobile() {
		return window.innerWidth <= 960;
	}
	openPage(page_name) {
		const { app_name } = this.props;
		this.props.history.push(`/${app_name}/dg-monitoring/${page_name}`);
	}
	toggleFilter() {
		if (this.state.visible) {
			const { onFilterChange } = this.props;
			const { prevFilterData } = this.state;
			prevFilterData.forEach((item, index) => {
				onFilterChange(item.selectValue, index);
			});
		}
		this.setState({
			visible: !this.state.visible,
		});
	}

	afterFilterVisible(visible) {
		if (visible) {
			const { filterData } = this.props;
			this.setState({ prevFilterData: [...filterData] });
		}
	}
	applyClick() {
		const { filterEffect } = this.props;
		filterEffect(true);
		this.setState({ visible: false });
	}
	multiplePages() {
		const { page_name_map } = this.props;
		return (
			Object.keys(page_name_map) && Object.keys(page_name_map).length > 1
		);
	}
	render() {
		const {
			page_name_map,
			page_name,
			filterData,
			onFilterChange,
			clubbed,
		} = this.props;
		let filterDrawer = (
			<AntDrawer
				title="Filters"
				placement="bottom"
				onClose={this.toggleFilter}
				visible={this.state.visible}
				width={476}
				className="dg-in-iot-filter-drawer"
				closable={false}
				//getContainer={false}
				style={{ position: 'absolute' }}
				maskStyle={{
					'backdrop-filter': 'blur(20px)',
					background: '#ffffff50',
				}}
				afterVisibleChange={this.afterFilterVisible}
			>
				<div className="mobile-line" onTouchMove={this.toggleFilter} />
				<FilterSelectWithSearch
					t={this.props.t}
					filterData={filterData}
					filterRadioObject={{}}
					applyFilterSelect={onFilterChange}
				/>
				<div className="drawer-actions-box">
					<AntButton
						type="primary"
						onClick={this.applyClick}
						className="apply-btn"
					>
						Apply filters
					</AntButton>
				</div>
			</AntDrawer>
		);
		return clubbed ? (
			<MobileFilterBox onClick={this.toggleFilter}>
				{filterDrawer}
			</MobileFilterBox>
		) : (
			<div className="dg-in-iot-header">
				<span className="dg-in-iot-page-title">
					{this.isMobile() && this.multiplePages() ? (
						<AntDropdown
							overlay={
								<AntMenu selectedKeys={[page_name]}>
									{Object.keys(page_name_map).map((key) => {
										let Icon = this.icon_map[key];
										return (
											<AntMenuItem
												key={key}
												onClick={() =>
													this.openPage(key)
												}
											>
												<Icon style={{ marginRight: 8 }}/>
												<span>
													{this.props.t? this.props.t(page_name_map[key]?.toLowerCase().replace(/\s+/g, '_')): page_name_map[key]}
												</span>
											</AntMenuItem>
										);
									})}
								</AntMenu>
							}
							overlayClassName="dg-in-iot-header-overlay"
							trigger={['click']}
							placement="bottomLeft"
						>
							<span className="dg-in-iot-page-title-span">
								{this.props.t? this.props.t(page_name_map[page_name]?.toLowerCase().replace(/\s+/g, '_')): page_name_map[page_name]}
								<DownOutlined style={{ marginLeft: 10 }} />
							</span>
						</AntDropdown>
					) : (
						this.props.t? this.props.t(page_name_map[page_name]?.toLowerCase().replace(/\s+/g, '_')): page_name_map[page_name]
					)}
				</span>
				{this.props.no_filter ? (
					''
				) : this.isMobile() ? (
					<span className="dg-iot-filter-icon">
						<FilterOutlined onClick={this.toggleFilter} />
					</span>
				) : (
					<FilterSelectWithSearch
						t={this.props.t}
						filterData={filterData}
						filterRadioObject={{}}
						applyFilterSelect={onFilterChange}
					/>
				)}
				{this.isMobile() && filterDrawer}
			</div>
		);
	}
}
