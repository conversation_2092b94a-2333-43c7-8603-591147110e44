import {
	getUserOptions,
	retriveCustomerList,
	retriveVendorThingsList,
} from '@datoms/js-sdk';
import moment from 'moment-timezone';
import _intersection from 'lodash/intersection';
import { getPageName, isRentalEnabled, onlyRental } from './filter-data';
import { defaultPlanDetails } from './config-data';

export function getFilteredData(thingList, customerList) {
	let categoryMap = {};
	let availableThingCats = [];
	let final_thing_type = this.state.thing_type;
	thingList.things.map((thing) => {
		if(thing.is_iot_enabled){
			if (!categoryMap[thing.category]) {
				categoryMap[thing.category] = [];
			}
			let finalId = thing.customer_id ? thing.customer_id : thing.vendor_id;
			if (!categoryMap[thing.category].includes(finalId)) {
				categoryMap[thing.category].push(finalId);
			}
	
			if (
				thing.current_assigned_customer &&
				!categoryMap[thing.category].includes(
					thing.current_assigned_customer
				)
			) {
				categoryMap[thing.category].push(thing.current_assigned_customer);
			}
	
			if (!availableThingCats.includes(thing.category)) {
				availableThingCats.push(thing.category);
			}
		}
	});
	let things_categories = thingCategory(
		thingList.things_categories,
		availableThingCats
	);
	if (!final_thing_type && things_categories[0]) {
		final_thing_type = things_categories[0].value;
	}
	let customer_data = this.filterCustomers(
		final_thing_type,
		customerList,
		categoryMap
	);
	return {
		categoryMap,
		things_categories,
		final_thing_type,
		customer_data,
	};
}
export function filterCustomers(
	final_thing_type = this.state.thing_type,
	customerList = this.state.customerList,
	categoryMap = this.state.categoryMap
) {
	let customer_data = [];
	if (customerList && customerList.length) {
		customerList.map((customers) => {
			if (
				this.isCustomerValid(customers) &&
				(getPageName(this.props) === 'reports' || final_thing_type === 'all' ||
					categoryMap[final_thing_type]?.includes(customers.id))
			) {
				customer_data.push({
					value: customers.id,
					title: customers.name,
					applications: customers.applications,
				});
			}
		});
	}
	return customer_data;
}
export function isCustomerValid(customer) {
	const { application_id } = this.props;
	// let page_name = getPageName(this.props),
	let	validAppId =
			customer.applications?.includes(16) ||
			/*page_name === 'reports'*/ (1 &&
				customer.applications?.includes(6)),
		rentalPartner =
		//	customer.customer_type?.length === 1 &&
			customer.customer_type.includes(4);

	if (application_id === 12) {
		return validAppId || rentalPartner;
	} else {
		return validAppId;
	}
}
function thingCategory(things_categories, availableThingCats) {
	let categories = [];
	let supported_thing_cat = [
		21,
		22,
		102,
		23,
		44,
		85,
		67,
		74,
		76,
		80,
		77,
		79,
		101,
		83,
		69,
		45,
		86,
		18,
		71,
		73,
		78,
		42, 
		89,
		96
	]//[18, 67, 71, 73, 74, 76, 77, 78, 42]; no views --> 81, 82, 84, 87, 88, 89
	let visited_categories = {};
	things_categories.map((thing_cat) => {
		if (
			supported_thing_cat.includes(thing_cat.id) &&
			!visited_categories[thing_cat.id] &&
			availableThingCats.includes(thing_cat.id)
		) {
			visited_categories[thing_cat.id] = true;
			categories.push({
				value: thing_cat.id,
				title: thing_cat.name,
			});
		}
	});
	return categories;
}

export async function getViewsData(pageChange = false) {
	const { app_name } = this.props;
	let [customersList, thingList] = await Promise.all([
		retriveCustomerList(this.props.client_id, '?lite=true'),
		retriveVendorThingsList(
			{
				vendor_id: this.props.client_id,
				application_id: this.props.application_id,
			},
			'?lite=true&without_device=true&status=active'
		),
	]);
	if (customersList.status === 'success') {
		const {
			categoryMap,
			things_categories,
			final_thing_type,
			customer_data,
		} = this.getFilteredData(thingList, customersList.customers);
		let client_id = this.state.client_id,
			option_client_id = this.state.client_id;
		if (
			(!client_id || pageChange) &&
			customer_data &&
			customer_data.length
		) {
			client_id =
				app_name === 'datoms-x' ||
				(!onlyRental(this.props) &&
					getPageName(this.props) !== 'map-view')
					? customer_data[0].value
					: this.props.client_id;
			option_client_id = customer_data[0].value;
		} else if (isRentalEnabled(this.props) && !customer_data.length) {
			client_id = this.props.client_id;
			option_client_id = this.props.client_id;
		}
		this.setState(
			{
				loading: false,
				categoryMap: categoryMap,
				things_categories: things_categories,
				thing_type: final_thing_type,
				customerDataOptions: customer_data,
				customerList: customersList.customers,
				client_id: client_id,
				noCustomers: !customer_data.length,
				message:
					customer_data && customer_data.length
						? ''
						: isRentalEnabled(this.props)
						? ''
						: 'Please add a customer first !',
			},
			async () => {
				if (!this.state.message) {
					this.validateFilters();
					await this.getOptions(option_client_id);
				}
				this.setState({ innerLoading: false });
			}
		);
	} else {
		this.setState({
			error: true,
			loading: false,
			innerLoading: false,
		});
	}
}
function getFinalResponse(response) {
	if (
		(response.plan_details && Object.keys(response.plan_details)?.length) ||
		response.application_id === 6
	)
		return response;
	let finalResponse = {
		...response,
		plan_details: JSON.parse(JSON.stringify(defaultPlanDetails)),
		template_id: response.template_id === 11 ? 29 : response.template_id,
	};
	return finalResponse;
}
export async function getOptions(option_client_id) {
	let app_name = 'dg-monitoring';
	const { customerDataOptions, page_name } = this.state;
	if (customerDataOptions?.length) {
		let selectedClient = customerDataOptions.find(
			(cus) => cus.value === option_client_id
		);
		if (selectedClient) {
			app_name =
				//	page_name === 'reports' &&
				selectedClient.applications?.includes(6)
					? 'environment-monitoring'
					: 'dg-monitoring';
		}
	}
	let apiResponse = await getUserOptions(option_client_id, app_name);
	let response = getFinalResponse(apiResponse);
	if (response.status === 'success') {
		let user_preferences = response.user_preferences
			? response.user_preferences
			: {};
		moment.tz.setDefault(
			response.user_preferences && response.user_preferences.timezone
				? response.user_preferences.timezone
				: moment.tz.guess()
		);
		user_preferences['timezone'] =
			response.user_preferences && response.user_preferences.timezone
				? response.user_preferences.timezone
				: moment.tz.guess();
		this.setState({
			application_id: response.application_id,
			application_name: response.application_name,
			user_id: response.logged_in_user,
			user_preferences: user_preferences,
			customer_access: response.access_list,
			enabled_features: response.features,
			is_white_label: response.is_white_label,
			customizations: response.customizations,
			plan_description: response.plan_details,
			client_logo: response.client_logo,
			vendor_logo: response.vendor_logo,
			template_id: response.template_id,
			user_name: response.logged_in_user_name
				? response.logged_in_user_name
				: 'User',
			client_name: response.client_name,
			vendor_name: response.vendor_name,
			vendor_id: response.parent_vendor_id === 1819 ? response.parent_vendor_id : response.vendor_id,
			logged_in_user_client_id: response.logged_in_user_client_id,
			logged_in_user_role_type: response.logged_in_user_role_type,
			parent_vendor_id: response.parent_vendor_id
		});
	}
	this.setState({ innerLoading: false });
}

export function createDGProps() {
	const {
		is_rental,
		client_id,
		functional_status,
		rental_status,
		customerDataOptions
	} = this.state;
	const { app_name, application_id } = this.props;
	let rental_enabled = isRentalEnabled(this.props);
	/*application id manipulate*/
	let changed_application_id = this.state.application_id;
	if (customerDataOptions?.length) {
		let selectedClient = customerDataOptions.find(
			(cus) => cus.value === client_id
		);
		if (selectedClient) {
			changed_application_id =
				//	page_name === 'reports' &&
				selectedClient.applications?.includes(6)
					? 6
					: this.state.application_id;
		}
	}
	/******/
	return {
		dg_in_iot_mode: true,
		vendor_id: this.state.vendor_id,
		application_id: changed_application_id,//this.state.application_id,
		application_name: this.state.application_name,
		app_name: this.state.application_name,
		user_id: this.state.user_id,
	//	user_preferences: this.state.user_preferences,
		customer_access: this.state.customer_access,
		enabled_features: this.state.enabled_features,
		is_white_label: this.state.is_white_label,
		plan_description:
			this.state.template_id === 24
				? undefined
				: this.state.plan_description,
		client_logo: this.state.client_logo,
		vendor_logo: this.state.vendor_logo,
		template_id: this.state.template_id,
		user_name: this.state.user_name,
		client_name: this.state.client_name,
		vendor_name: this.state.vendor_name,
		logged_in_user_client_id: this.state.logged_in_user_client_id,
		logged_in_user_role_type: this.state.logged_in_user_role_type,
		getViewAccess: this.getViewAccess,
		getRemoteAccess: this.getRemoteAccess,
		getRemoteLockAccess: this.getRemoteLockAccess,
		client_id: client_id,
		is_rental_selected:
			application_id === 12 ? 'all' : is_rental === 'true',
		// client_id:
		// 	rental_enabled &&
		// 	is_rental === 'true' &&
		// 	getPageName(this.props) !== 'map-view'
		// 		? this.props.client_id
		// 		: client_id,
		// filter_client_id:
		// 	rental_enabled &&
		// 	is_rental === 'true' &&
		// 	client_id !== this.props.client_id &&
		// 	getPageName(this.props) !== 'map-view'
		// 		? client_id
		// 		: undefined,
		fetch_vendor_list: client_id === this.props.client_id,
		thing_type: this.state.thing_type,
		is_rental: rental_enabled ? is_rental : 'false',
		functional_status: rental_enabled ? functional_status : undefined,
		rental_status: rental_enabled ? rental_status : undefined,
		page_name: getPageName(this.props),
		message: this.state.message,
		host_app_name: app_name,
		iotViewsVendorId: this.props.client_id,
		user_preferences: this.props.user_preferences,
		parent_vendor_id: this.state.parent_vendor_id,
	};
}

export function getViewAccess(access_key_arr) {
	if (access_key_arr && access_key_arr.length) {
		if (this.state.customer_access) {
			if (this.state.customer_access === '*') {
				return true;
			} else if (this.state.customer_access.length) {
				let found_key = _intersection(
					this.state.customer_access,
					access_key_arr
				);
				if (found_key.length) {
					return true;
				}
				return false;
			}
		}
	}
}

export function getRemoteAccess() {
	if (this.state.plan_description.remote_control) {
		return true;
	} else if (
		this.state.enabled_features.includes('RemoteControl:RemoteControl')
	) {
		return true;
	} else {
		return false;
	}
}

export function getRemoteLockAccess() {
	if (this.state.plan_description.remote_lock) {
		return true;
	} else if (this.state.enabled_features.includes('RemoteLock:RemoteLock')) {
		return true;
	} else {
		return false;
	}
}
