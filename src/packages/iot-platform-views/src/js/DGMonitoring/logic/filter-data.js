const functional_status_map = [
  "functional",
  "maintenance",
  "breakdown",
  "disposed",
];
const rental_status_map = {
  available: "Available",
  unavailable: "Unavailable",
  on_rent: "On Rent",
};
export function getPages() {
  const { supportedViewsPage } = this.props;
  let page_name = getPageName(this.props);
  let page_name_map = {
    // 'map-view': 'Map View',
    // dashboard: 'Dashboard',
    // 'detailed-view': 'Detailed View',
    // 'real-time': 'Real Time',
    // 'panel-view': 'Panel View',
    // reports: 'Reports',
  };
  console.log("supportedViewsPage", typeof supportedViewsPage);
  //if (isMobileScreen()) {
  if (
    this.props.application_id === 17 &&
    isMobileScreen() &&
    (page_name === "reports" || page_name === "oem-reports")
  ) {
    if (
      // this.props.enabled_features &&
      // this.props.enabled_features.includes(
      // 	'AccessThingData:AccessThingData'
      // )
      this.props.viewDataAccess()
    ) {
      page_name_map = {
        "oem-reports": "Partner Reports",
        reports: "Assets Reports",
      };
    } else {
      page_name_map = {
        "oem-reports": "Partner Reports",
      };
    }
  } else if (this.props.clubbed) {
    page_name_map = {
      dashboard: "Dashboard",
      "asset-dashboard": "Asset Dashboard",
    };
  } else if (page_name === "dashboard") {
    page_name_map = {
      dashboard: "Dashboard",
    };
  } else if (page_name === "reports") {
    page_name_map = {
      reports: "Reports",
    };
  } else {
    if (
      this.props.application_id === 17 &&
      typeof supportedViewsPage === "function"
    ) {
      // if (supportedViewsPage('map-view')) {
      // 	page_name_map['map-view'] = 'Map View';
      // }
      if (supportedViewsPage("detailed-view")) {
        page_name_map["detailed-view"] = "Detailed View";
      }
      if (supportedViewsPage("real-time")) {
        page_name_map["real-time"] = "Analog";
      }

      if (
        supportedViewsPage("panel-view") &&
        (!isMobileScreen() || this.state.template_id !== 29)
      ) {
        page_name_map["panel-view"] = "Panel View";
      }
      if (supportedViewsPage("list-view") && this.state.template_id === 29) {
        page_name_map["list-view"] = "List View";
      }
      if (supportedViewsPage("asset-dashboard") && !isMobileScreen()) {
        page_name_map["asset-dashboard"] = "Asset Dashboard";
      }
      if (supportedViewsPage("trip-view")) {
        page_name_map["trip-view"] = "Event View";
      }
    } else {
      page_name_map = {
        'map-view': 'Map View',
				'detailed-view': 'detailed_view',
				'real-time': 'Analog',
				'panel-view': 'panel_view',
        'list-view': 'list_view'
      };
      // if (!isMobileScreen()) {
      // 	page_name_map['asset-dashboard'] = 'Asset Dashboard';
      // }
    }
  }
  //}
  return page_name_map;
}
function isMobileScreen() {
  return window.innerWidth < 576;
}
export function getPageName(props) {
  return props.clubbed
    ? props.clubbed
    : props.match && props.match.params && props.match.params.page_name
      ? props.match.params.page_name
      : "map-view";
}
export function isRentalEnabled(props) {
  return props.customer_type && props.customer_type.includes(4);
  // return (
  // 	props.enabled_features &&
  // 	props.enabled_features.includes('ThingsRental:ThingsRental')
  // );
}
export function onlyRental(props) {
  return (
    props.customer_type &&
    props.customer_type.includes(4) &&
    props.customer_type.length === 1
  );
}
export function createFilterOptions() {
  let page_name = this.state.page_name,
    rental_enabled = isRentalEnabled(this.props);
  const { app_name } = this.props;
  let filterData = [
    {
      optionData:
        this.state.things_categories && (page_name === "panel-view" || page_name === "list-view")
          ? [
              { value: "all", title: "All Asset Type" },
              ...this.state.things_categories,
            ]
          : this.state.things_categories,
      selectValue: this.state.thing_type,
      allowClear: false,
      sorted: false,
      showSearch: true,
      //	placeholder: 'Asset type',
      label: "Asset Type",
      // hideField: page_name === "panel-view"
    },
    {
      optionData: [
        // { value: 'true', title: 'Rental' },
        // { value: 'false', title: 'Sellable' },
      ],
      selectValue: this.state.is_rental,
      allowClear: false,
      sorted: true,
      showSearch: true,
      //	placeholder: 'Assets',
      label: "Assets",
    },
    {
      optionData: functional_status_map.map((status) => {
        return {
          value: status,
          title: makeUpperCase(status),
        };
      }),
      selectValue: this.state.functional_status,
      allowClear: true,
      sorted: true,
      showSearch: true,
      placeholder: "functional status",
      label: "Functional Status",
      disabled: this.state.is_rental === "false",
    },
    {
      optionData: Object.keys(rental_status_map).map((status) => {
        return {
          value: status,
          title: rental_status_map[status],
        };
      }),
      selectValue: this.state.rental_status,
      allowClear: true,
      sorted: true,
      showSearch: true,
      placeholder: "rental status",
      label: "Rental Status",
      disabled: this.state.is_rental === "false",
    },
    {
      optionData: this.state.customerDataOptions,
      selectValue:
        this.state.client_id === this.props.client_id
          ? undefined
          : this.state.client_id,
      allowClear:
        // app_name === 'datoms-x' ||
        // (!onlyRental(this.props) &&
        // 	page_name !== 'map-view')
        !rental_enabled ? false : true,
      sorted: true,
      showSearch: true,
      placeholder: "All Customers",
      label: "Customers",
      showSingleOption: true,
    },
  ];
  //['Asset Type', 'Assets', 'Functional Status', 'Rental Status', 'Customers'];
  if (!rental_enabled || app_name === "datoms-x") {
    if (page_name === "reports") {
      //	filterData.splice(0, 4);
      filterData[0].optionData = [];
      filterData[1].optionData = [];
      filterData[2].optionData = [];
      filterData[3].optionData = [];
    } else {
      //filterData.splice(1, 3);
      filterData[1].optionData = [];
      filterData[2].optionData = [];
      filterData[3].optionData = [];
    }
  } else if (rental_enabled) {
    if (page_name === "reports") {
      //	filterData = [filterData[1], filterData[4]];
      if (onlyRental(this.props)) {
        filterData[0].optionData = [];
        filterData[1].optionData = [];
        filterData[2].optionData = [];
        filterData[3].optionData = [];
      } else {
        filterData[0].optionData = [];
        filterData[2].optionData = [];
        filterData[3].optionData = [];
      }
    } else if (page_name === "map-view") {
      if (onlyRental(this.props)) {
        filterData[1].optionData = [];
      }
    } else {
      //	filterData.splice(2, 2);
      if (onlyRental(this.props)) {
        filterData[1].optionData = [];
        filterData[2].optionData = [];
        filterData[3].optionData = [];
      } else {
        filterData[2].optionData = [];
        filterData[3].optionData = [];
      }
    }
  }
  return filterData;
}
function makeUpperCase(s) {
  return s[0].toUpperCase() + s.slice(1);
}
const filter_keys_map = {
  0: "thing_type",
  1: "is_rental",
  2: "functional_status",
  3: "rental_status",
  4: "client_id",
};
export function onFilterChange(value, index) {
  let filter_to_be_changed = "",
    filter_value = value;
  filter_to_be_changed = filter_keys_map[index];
  let filter_state = { [filter_to_be_changed]: filter_value };
  if (filter_to_be_changed === "is_rental" && filter_value === "false") {
    filter_state = {
      ...filter_state,
      rental_status: undefined,
      functional_status: undefined,
    };
  }
  if (filter_to_be_changed === "client_id" && !value) {
    filter_state["client_id"] = this.props.client_id;
  }
  let that = this;
  if (filter_to_be_changed === "thing_type") {
    if (this.state.customerList?.length) {
      let finalOptions = this.filterCustomers(value);
      filter_state["customerDataOptions"] = finalOptions;
    }
    if (
      this.state.categoryMap?.[value] &&
      !this.state.categoryMap[value].includes(this.state.client_id)
    ) {
      filter_state["client_id"] =
        this.state.page_name === "map-view" && this.props.application_id !== 12
          ? this.props.client_id
          : filter_state["customerDataOptions"]?.[0]?.value;
      //	: this.state.categoryMap[value][0];
    }
  }
  this.setState(filter_state, async () => {
    if (window.innerWidth > 960) {
      await that.filterEffect(filter_to_be_changed);
    }
  });
}
export async function filterEffect(filter_to_be_changed) {
  if (!this.validateFilters(true)) {
    const { client_id, /*customerDataOptions,*/ is_rental } = this.state;
    let option_client_id = client_id;
    if (
      is_rental === "false" &&
      client_id !== this.props.client_id &&
      (filter_to_be_changed === "client_id" ||
        filter_to_be_changed === "is_rental" ||
        filter_to_be_changed)
    ) {
      // if (client_id === vendor_id) {
      // 	option_client_id = customerDataOptions[0].value;
      // }
      await this.getOptions(option_client_id);
    }
  }
  await new Promise((r) => setTimeout(r, 2000));
  this.setState({ innerLoading: false });
}
// const page_thing_type_map = {
// 	'map-view': [18, 67, 71, 73, 74, 76, 77, 78],
// 	dashboard: [18, 71, 73, 42],
// 	'real-time': [18, 78],
// 	'detailed-view': [18, 73],
// 	'panel-view': [18],
// 	reports: [18, 67, 71, 73, 74, 76, 77, 42],
// };
// const catIdsGeneric = [
// 	21, 22, 23, 44, 85, 67, 74, 76, 80, 77, 79, 83, 69, 45, 86,
// ];
// const page_thing_type_map = {
// 	'map-view': [18, 71, 73, 67, 74, 76, 77, 78, 79, 80, 81, 82, 69, 45],
// 	dashboard: [18, 71, 73, 42],
// 	'real-time': [18, 78, 80, 79, 83, 69, 86, 73, 81, 82],
// 	'detailed-view': [
// 		18, 73, 80, 79, 83, 69, 45, 86, 21, 22, 23, 44, 85, 78, 81, 82,
// 	],
// 	'panel-view': [
// 		18, 71, 67, 74, 21, 22, 23, 44, 85, 76, 80, 79, 83, 69, 45, 86, 73, 78,
// 		77, 81, 82,
// 	],
// 	reports: [18, 67, 71, 73, 74, 76, 77, 42],
// };

const catIdsGeneric = [
  21, 22, 102, 89, 23, 44, 85, 67, 74, 76, 80, 77, 79, 83, 69, 45, 86, 99, 101,
];
export function onPageChange() {
  const { client_id, customerDataOptions } = this.state;
  let page_name = getPageName(this.props),
    final_client_id = client_id;
  if (
    this.props.client_id !== 1 &&
    page_name === "map-view" &&
    client_id !== this.props.client_id
  ) {
    final_client_id = this.props.client_id;
  } else if (
    !onlyRental(this.props) &&
    page_name !== "map-view" &&
    client_id === this.props.client_id &&
    customerDataOptions?.length
  ) {
    final_client_id = customerDataOptions[0].value;
  }
  this.setState(
    {
      client_id: final_client_id,
      page_name: page_name,
      innerLoading: true,
    },
    () => this.validateFilters(),
  );
}

const page_thing_type_map = {
  "map-view": [18, 96, 89, 71, 73, 67, 74, 76, 77, 78, 79, 80, 81, 82, 69, 45, 99],
  dashboard: [18, 96, 71, 73, 42, 86],
  "real-time": [18, 78, 80, 79, 83, 69, 86, 73, 81, 82, 96, 101],
  "detailed-view": [
    18, 22, 89, 73, 80, 79, 83, 69, 45, 86, 21, 102, 23, 44, 85, 78, 81, 82, 99, 96, 101
  ],
  "panel-view": [
    18, 22, 89, 71, 67, 74, 21, 102, 23, 44, 85, 76, 80, 79, 83, 69, 45, 86, 73,
    78, 77, 81, 82, 99, 96, 101
  ],
  "list-view": [
    18, 22, 89, 71, 67, 74, 21, 102, 23, 44, 85, 76, 80, 79, 83, 69, 45, 86, 73,
    78, 77, 81, 82, 99, 96, 101
  ],
  "asset-dashboard": [18, 71, 73, 42, 86],
  "trip-view": [18, 67, 76],
  reports: [
    18, 67, 71, 73, 74, 76, 77, 42, 21, 22, 102, 23, 44, 85, 67, 74, 76, 80, 77, 79,101,
    83, 69, 45, 86, 89, 96
  ],
};

const getPageThingTypeMap = () => {
  if (window.innerWidth < 576) {
    Object.keys(page_thing_type_map).map((page_name) => {
      if (["detailed-view", "real-time", "panel-view", "list-view"].includes(page_name)) {
        page_thing_type_map[page_name] = page_thing_type_map[page_name].filter(
          (catId) => !catIdsGeneric.includes(catId),
        );
      }
      if (page_name === "map-view") {
        page_thing_type_map[page_name] = [
          ...new Set([...page_thing_type_map[page_name], ...catIdsGeneric]),
        ];
      }
    });
  }
  return page_thing_type_map;
};

export function validateFilters(removeEndParams = false) {
  const pageThingTypeMap = getPageThingTypeMap();
  this.setState({ innerLoading: true });
  let final_path = "",
    message = "",
    page_name = getPageName(this.props),
    page_name_map = this.getPages(),
    rental_enabled = isRentalEnabled(this.props);
  const {
    thing_type,
    things_categories,
    is_rental,
    functional_status,
    rental_status,
    client_id,
    customerDataOptions,
  } = this.state;
  const { app_name } = this.props;
  let path_name =
    this.props.history &&
    this.props.history.location &&
    this.props.history.location.pathname
      ? this.props.history.location.pathname
      : `/${app_name}/dg-monitoring/${page_name}`;
  if (non_dg_pages.includes(page_name)) {
    this.props.history.push(path_name);
    this.setState({
      message,
    });
  } else {
    let valid_thing_type =
      thing_type === "all" ||
      (things_categories &&
        things_categories.find((item) => item.value === thing_type));
    let valid_customer =
      customerDataOptions &&
      customerDataOptions.find((item) => item.value === client_id);
    if (!Object.keys(page_name_map).includes(page_name)) {
      message = `${this.props.t? this.props.t('Invalid Page !'): "Invalid Page !"}`;
			// message = `Invalid Page !`;
    } else if (
      page_name !== "reports" &&
      !valid_thing_type &&
      things_categories?.length
    ) {
      message = !things_categories?.length
        ? "No assets found !"
        : `Asset type is invalid !`;
    } else if (!["true", "false"].includes(is_rental)) {
      message = `Rental filter is invalid !`;
    } else if (
      app_name !== "datoms-x" &&
      rental_enabled &&
      page_name === "map-view" &&
      functional_status &&
      !functional_status_map.includes(functional_status)
    ) {
      message = `Functional status is invalid !`;
    } else if (
      app_name !== "datoms-x" &&
      rental_enabled &&
      page_name === "map-view" &&
      rental_status &&
      !Object.keys(rental_status_map).includes(rental_status)
    ) {
      message = `Rental status is invalid !`;
    } else if (
      (client_id || app_name === "datoms-x") &&
      client_id !== this.props.client_id &&
      !valid_customer
    ) {
      message = `Customer Id is invalid !`;
    }
    // else if (page_name === 'reports' && client_id === this.props.client_id) {
    // 	message = `Select a customer to view reports !`;
    // }
    // else if (
    // 	app_name !== 'datoms-x' &&
    // 	client_id === this.props.client_id &&
    // 	is_rental === 'false'
    // ) {
    // 	message = `You have selected sellable option. Please select a customer. Or select rental option !`;
    // }
    else if (
      !pageThingTypeMap[page_name].includes(thing_type) &&
      thing_type !== "all"
    ) {
      message = `${page_name_map[page_name]} is not supported for selected asset type !`;
    }

    let final_client_id =
      client_id === this.props.client_id ? undefined : client_id;

    // let path_name =
    // 	this.props.history &&
    // 	this.props.history.location &&
    // 	this.props.history.location.pathname
    // 		? this.props.history.location.pathname
    // 		: `/${app_name}/dg-monitoring/${page_name}`;
    // let thing_id =
    // 	this.parsed && this.parsed['thing_id'] && initial
    // 		? `&thing_id=${this.parsed['thing_id']}`
    // 		: '';
    if (app_name === "datoms-x" || !rental_enabled) {
      if (page_name === "reports") {
        final_path = `${path_name}?client_id=${final_client_id}`;
      } else {
        final_path = `${path_name}?thing_type=${thing_type}&client_id=${final_client_id}`;
      }
    } else if (rental_enabled) {
      if (page_name === "map-view") {
        final_path = `${path_name}?thing_type=${thing_type}&is_rental=${is_rental}&client_id=${final_client_id}&functional_status=${functional_status}&rental_status=${rental_status}`;
      } else if (page_name === "reports") {
        final_path = `${path_name}?is_rental=${is_rental}&client_id=${final_client_id}`;
      } else {
        final_path = `${path_name}?thing_type=${thing_type}&is_rental=${is_rental}&client_id=${final_client_id}`;
      }
    }
    //	final_path += thing_id;
    final_path += "&end_iot_views=1";
    if (this.props.location.search && !removeEndParams) {
      let splitArray = this.props.location.search.split("&end_iot_views=1");
      if (splitArray[1]) {
        final_path += splitArray[1];
      }
    }
    console.log("finalPush", final_path);
    this.props.history.push(final_path);
    this.setState({
      message,
    });
  }
  return message;
}

export const non_dg_pages = ["oem-reports", "trip-view"];
