import React from 'react';
import '@ant-design/compatible/assets/index.css';
import AntLayout from '@datoms/react-components/src/components/AntLayout';
import AntRow from '@datoms/react-components/src/components/AntRow';
import AntCol from '@datoms/react-components/src/components/AntCol';
import AntNotification from '@datoms/react-components/src/components/AntNotification';
import AntSpin from '@datoms/react-components/src/components/AntSpin';
import AntSelect from '@datoms/react-components/src/components/AntSelect';
import AntTabs from '@datoms/react-components/src/components/AntTabs';
import AntTabPane from '@datoms/react-components/src/components/AntTabPane';
import AntContent from '@datoms/react-components/src/components/AntContent';
import AntTag from '@datoms/react-components/src/components/AntTag';
import {
	retriveCustomerDetails,
	retriveVendorThingsList,
	retriveCustomerList,
} from '@datoms/js-sdk';
import _find from 'lodash/find';
import _filter from 'lodash/filter';
import './style.less';
import { getApplicationDataWithManipulatedData } from '@datoms/js-utils/src/ApplicationsDataManipulation';
import ReportIcon from './assets/report-icon.svg';
import DGReportTemplates from '@datoms/dg-monitoring-views/src/js/features/Reports/ReportTemplates';
import { getOptions, createDGProps, getViewAccess } from './logic/entry-data';
import ReportsRoutes from './ReportsRoutes';
import ScheduledList from '@datoms/dg-monitoring-views/src/js/features/Reports/components/ScheduledList';
import ConfigureParams from '@datoms/dg-monitoring-views/src/js/features/Reports/ReportsParamConfigure';
import moment from 'moment-timezone';

export default class ReportTemplates extends React.Component {
	constructor(props) {
		super(props);
		const params = new URLSearchParams(
			props.history.location.search.split('?')[1]
		);
		const clientId = params.get('client_id');
		this.state = {
			loading: true,
			show_template: true,
			selected_application_id: null,
			client_id:
				isNaN(parseInt(clientId)) || this.isOnlyRental(props)
					? props.client_id
					: parseInt(clientId),
			innerLoading: true,
			is_rental: this.isOnlyRental(props) ? 'true' : 'false',
		};
		this.data = {
			client_id: props.client_id,
			application_id: props.application_id,
		};
		this.retriveApplicationThingsFunction(props.application_id);
		this.getOptions = getOptions.bind(this);
		this.createDGProps = createDGProps.bind(this);
		this.getViewAccess = getViewAccess.bind(this);
		this.basePath =
			props.application_id == 12
				? '/datoms-x/reports'
				: '/iot-platform/reports';
	}

	isOnlyRental(props) {
		return (
			props.customer_type &&
			props.customer_type.includes(4) &&
			props.customer_type.length === 1
		);
	}

	async retriveApplicationThingsFunction(application_id) {
		let response_data = await getApplicationDataWithManipulatedData(
			application_id
		);
		console.log('response_data', response_data);
		if (response_data.response.status === 'success') {
			this.setState({
				//loading: false,
				data: response_data.response,
				application_data: response_data.modified_data.applications,
				category_data: response_data.modified_data.categories,
			});
		} else {
			this.openNotification(
				'error',
				'Something went wrong, Refresh the page'
			);
		}
	}

	// async getThingsList() {
	// 	const { client_id, application_id, customer_type } = this.props;
	// 	let isDG = false;
	// 	if (customer_type && Array.isArray(customer_type)) {
	// 		let totalData = await retriveVendorThingsList({
	// 			vendor_id: client_id,
	// 			application_id: application_id,
	// 		});
	// 		if (totalData && totalData.things && totalData.things.length) {
	// 			let findDg = _find(totalData.things, { category: 18 });
	// 			if (findDg) {
	// 				isDG = true;
	// 			}
	// 		}
	// 	}
	// 	this.setState({
	// 		isDG: isDG,
	// 	});
	// }

	async componentDidMount() {
		const { customer_type, application_id } = this.props;
		try {
			//   if (this.props.application_id !== 12) {
			await Promise.all([
				this.getCustomerList(),
				this.getCustomerDetails(),
				// this.getThingsList(),
			]);
		} catch (error) {
			console.log(error);
		} finally {
			this.setState({ loading: false });
		}
	}

	/**
	 * This function opens the notification in render.
	 */
	openNotification(type, msg) {
		AntNotification[type]({
			message: msg,
			// description: msg,
			placement: 'bottomLeft',
			className: 'alert-' + type,
		});
	}

	collapseState(collapse) {
		this.setState({
			collapse: collapse,
		});
	}

	onSelectApplication(e) {
		console.log('onSelectApplication', e);
	}

	onSelectThingCategory(e) {
		console.log('onSelectThingCategory', e);
	}

	onChange(value) {
		console.log('selectedvalue', value);
	}

	openEditPage() {
		this.setState({
			open_form: true,
			show_template: false,
		});
	}

	goToPartnerReports(pageName) {
		let basePath =
			this.props.application_id == 12
				? '/datoms-x/partner-reports'
				: '/iot-platform/partner-reports';
		this.props.history.push(`${basePath}/${pageName}`);
	}

	async getCustomerDetails() {
		let customerDetails = await retriveCustomerDetails(
			this.props.client_id
		);
		let showAlertDeliveryReport = false;
		if (
			customerDetails &&
			customerDetails.application_details &&
			customerDetails.application_details.length &&
			_find(customerDetails.application_details, {
				application_id: this.props.application_id,
			}) &&
			_find(customerDetails.application_details, {
				application_id: this.props.application_id,
			}).alert_delivery_media &&
			_find(customerDetails.application_details, {
				application_id: this.props.application_id,
			}).alert_delivery_media.length
		) {
			if (
				_find(customerDetails.application_details, {
					application_id: this.props.application_id,
				}).alert_delivery_media.includes('sms') ||
				_find(customerDetails.application_details, {
					application_id: this.props.application_id,
				}).alert_delivery_media.includes('whatsapp')
			) {
				showAlertDeliveryReport = true;
			}
		}

		this.setState({
			showAlertDeliveryReport: showAlertDeliveryReport,
		});
	}

	customerSelection(e) {
		this.setState(
			{
				client_id: e,
				dgReportLoading: true,
			},
			async () => {
				await this.getOptions();
				this.props.history.push(
					`${this.basePath}?client_id=${this.state.client_id}`
				);
				this.setState({
					dgReportLoading: false,
				});
			}
		);
	}

	async getCustomerList() {
		const { client_id } = this.props;
		let finalCustomer;
		let customerList = await retriveCustomerList(client_id, '?lite=true');
		if (customerList?.customers?.length) {
			finalCustomer = _filter(
				customerList?.customers,
				(obj) => obj.id !== 1
			);
		}
		let getCustomersList = [];
		if (finalCustomer?.length) {
			finalCustomer.map((customers) => {
				getCustomersList.push({
					applications: customers.applications,
					title: customers.name,
					label: customers.name,
					value: customers.id,
				});
			});
		}
		const searchParams = new URLSearchParams(
			this.props.history?.location?.search
		);
		const clientId = searchParams.get('client_id');
		this.setState(
			{
				customerList: getCustomersList,
				client_id: clientId
					? parseInt(clientId)
					: this.isOnlyRental(this.props)
					? this.props.client_id
					: getCustomersList?.[0]?.value,
				dgReportLoading: true,
			},
			async () => {
				await this.getOptions();
				this.props.history.push(
					`${this.props.location.pathname}?client_id=${this.state.client_id}`
				);
				this.setState({
					dgReportLoading: false,
				});
			}
		);
	}

	onSearch() {}

	isMobileView() {
		return window.innerWidth < 576;
	}

	getDGReport() {
		const { customerList, client_id, dgReportLoading } = this.state;
		console.log('sfgkfjg', moment().format('DD MMM YYYY, HH:mm'));
		return (
			<div className="rcb">
				{this.isMobileView() ? (
					''
				) : (
					<div className="report-template-header">
						{this.props.t? this.props.t('asset_reports'): "Asset Reports"}
						{/* Asset Reports */}
					</div>
				)}
				<div className="customer-selection">
					{this.props.t? this.props.t('customer'): "Customer"}
					{/* Customer */}
					<AntSelect
						showSearch
						onChange={(e) => this.customerSelection(e)}
						value={
							client_id === this.props.client_id
								? undefined
								: client_id
						}
						options={customerList}
						placeholder={'Select Customer'}
						onSearch={() => this.onSearch()}
						filterOption={(input, option) =>
							(option?.label ?? '')
								.toLowerCase()
								.includes(input.toLowerCase())
						}
					/>
				</div>
				{dgReportLoading ? (
					<AntSpin className="align-center-loading" />
				) : (
					<DGReportTemplates
						{...this.props}
						new_partner_reports={true}
						{...this.createDGProps()}
					/>
				)}
			</div>
		);
	}

	isScheduleReport() {
		return (
			this.props.client_id === 1 ||
			this.props.enabled_features?.includes('Reports:Scheduled')
		);
	}

	componentWillUnmount() {
		moment.tz.setDefault(
			this.props.user_preferences && this.props.user_preferences.timezone
				? this.props.user_preferences.timezone
				: moment.tz.guess()
		);
	}

	render() {
		const { dg_in_iot_mode, customer_type } = this.props;
		const { dgSetType, isDG, innerLoading } = this.state;
		return (
			<div id="datoms_x_report_template">
				{(() => {
					if (this.state.loading) {
						return (
							<AntLayout className={'contains'}>
								<AntSpin className="align-center-loading" />
							</AntLayout>
						);
					} else {
						if (
							!this.props.location.pathname.endsWith(
								'/reports'
							) &&
							!this.props.location.pathname.endsWith('/reports/')
						) {
							if (innerLoading) {
								return (
									<AntLayout className={'contains'}>
										<AntSpin className="align-center-loading" />
									</AntLayout>
								);
							} else {
								return (
									<>
										<ReportsRoutes
											new_partner_reports={true}
											basePath={this.basePath}
											{...this.props}
											{...this.createDGProps()}
											isScheduledReport={this.isScheduleReport()}
											parent_client_id={
												this.props.client_id
											}
											parent_application_id={
												this.props.application_id
											}
										/>
									</>
								);
							}
						}
						let machineReportsView = [];
						if (
							customer_type &&
							Array.isArray(customer_type) &&
							customer_type.indexOf(4) > -1
						) {
							machineReportsView.push(
								<div className="template-type-section">
									<div className="template-type-section-body">
										<div
											className="template-type-section-body-row" /* onClick={() => {this.changeURLForReports('run-hour-report')}} */
										>
											<div className="template-type-section-body-icon">
												<img src={ReportIcon} />
											</div>
											<div
												className="template-type-section-body-text"
												onClick={() =>
													this.goToPartnerReports(
														'machine-info-report'
													)
												} /*temporary name according to dg*/
											>
												MachineInfo Report
											</div>
										</div>
									</div>
								</div>
							);
						}
						if (this.state.showAlertDeliveryReport) {
							machineReportsView.push(
								<div className="template-type-section">
									<div className="template-type-section-body">
										<div
											className="template-type-section-body-row" /* onClick={() => {this.changeURLForReports('run-hour-report')}} */
										>
											<div className="template-type-section-body-icon">
												<img src={ReportIcon} />
											</div>
											<div
												className="template-type-section-body-text"
												onClick={() =>
													this.goToPartnerReports(
														'alert-delivery-report'
													)
												} /*temporary name according to dg*/
											>
												{this.props.t? this.props.t('alert_usage_report'): "Alert Usage Report"}
												{/* Alert Usage Report */}
											</div>
										</div>
									</div>
								</div>
							);
						}
						if (this.props.application_id === 12) {
							machineReportsView.push(
								<div className="template-type-section">
									<div className="template-type-section-body">
										<div
											className="template-type-section-body-row" /* onClick={() => {this.changeURLForReports('run-hour-report')}} */
										>
											<div className="template-type-section-body-icon">
												<img src={ReportIcon} />
											</div>
											<div
												className="template-type-section-body-text"
												onClick={() =>
													this.goToPartnerReports(
														'fuel-delivery-report'
													)
												} /*temporary name according to dg*/
											>
												Fuel Delivery Report
											</div>
										</div>
									</div>
								</div>
							);
						}
						if (this.props.application_id === 12 || this.props.enabled_features?.includes('Reports:DataAvailability')) {
							machineReportsView.push(
								<div className="template-type-section">
									<div className="template-type-section-body">
										<div
											className="template-type-section-body-row" /* onClick={() => {this.changeURLForReports('run-hour-report')}} */
										>
											<div className="template-type-section-body-icon">
												<img src={ReportIcon} />
											</div>
											<div
												className="template-type-section-body-text"
												onClick={() =>
													this.goToPartnerReports(
														'data-availability-report'
													)
												} /*temporary name according to dg*/
											>
												Data Availability Report
											</div>
										</div>
									</div>
								</div>
							);
						}
						if (
							customer_type &&
							Array.isArray(customer_type)
							// &&  customer_type.indexOf(1) > -1
						) {
							machineReportsView.push(
								<div className="template-type-section">
									<div className="template-type-section-body">
										<div
											className="template-type-section-body-row" /* onClick={() => {this.changeURLForReports('run-hour-report')}} */
										>
											<div className="template-type-section-body-icon">
												<img src={ReportIcon} />
											</div>
											<div
												className="template-type-section-body-text"
												onClick={() =>
													this.goToPartnerReports(
														'asset-status-report'
													)
												} /*temporary name according to dg*/
											>
												{this.props.t? this.props.t('asset_status_report'): "Asset Status Report"}
												{/* Asset Status Report */}
											</div>
										</div>
									</div>
								</div>
							);
						}
						if (this.props.application_id === 12) {
							machineReportsView.push(
								<div className="template-type-section">
									<div className="template-type-section-body">
										<div
											className="template-type-section-body-row" /* onClick={() => {this.changeURLForReports('run-hour-report')}} */
										>
											<div className="template-type-section-body-icon">
												<img src={ReportIcon} />
											</div>
											<div
												className="template-type-section-body-text"
												onClick={() =>
													this.goToPartnerReports(
														'device-health-report'
													)
												} 
											>
												Device Health Report 
											</div >
											<div style={{marginLeft: '7px'}}>
												<AntTag color="success">New</AntTag>
											</div>
										</div>
									</div>
								</div>
							);
						}
						return (
							<AntLayout className={'contains'}>
								<AntContent>
									{(() => {
										if (this.state.show_template) {
											return this.isMobileView() ? 
											this.props.logged_in_user_role_type === 10 ? '' :(
												<AntTabs
													defaultActiveKey="1"
													tabPosition="top"
													type="card"
												>
													<AntTabPane
														tab="Asset Reports"
														key="1"
													>
														<div className="reports-template-page">
															<div className="reports-container">
																{this.getDGReport()}
															</div>
														</div>
													</AntTabPane>
													{machineReportsView?.length ? (
														<AntTabPane
															// tab={this.props.t? this.props.t('partner_report'): "Partner Report"}
															tab="Partner Report"
															key="2"
														>
															<div className="reports-template-page">
																<div className="reports-container">
																	<div className="rcb">
																		{
																			machineReportsView
																		}
																	</div>
																</div>
															</div>
														</AntTabPane>
													) : null}
													{this.isScheduleReport() ? (
														<AntTabPane
															tab="Scheduled Reports"
															key="3"
														>
															<div className="reports-template-page">
																<div className="reports-container">
																	<ScheduledList
																		client_id={
																			this
																				.props
																				.client_id
																		}
																		application_id={
																			this.createDGProps()
																				?.application_id
																		}
																	/>
																</div>
															</div>
														</AntTabPane>
													) : null}
												</AntTabs>
											) : (
												<AntRow className="reports-template-page">
													<AntRow
														gutter={20}
														className="reports-container"
													>
														{this.props.logged_in_user_role_type === 10 ? '' 
														: <AntCol span={8}>
															{this.getDGReport()}
														</AntCol>}
														{machineReportsView?.length ? (
															<AntCol span={8}>
																<div className="rcb">
																	<div className="report-template-header">
																		{this.props.t? this.props.t('partner_report'): "Partner Report"}
																		{/* Partner
																		Report */}
																	</div>
																	{
																		machineReportsView
																	}
																</div>
															</AntCol>
														) : (
															''
														)}
														{this.isScheduleReport() ? (
															<AntCol span={8}>
																<ScheduledList
																	client_id={
																		this
																			.props
																			.client_id
																	}
																	application_id={
																		this.createDGProps()
																			?.application_id
																	}
																	parent_application_id={
																		this
																			.props
																			.application_id
																	}
																/>
															</AntCol>
														) : (
															''
														)}
													</AntRow>
												</AntRow>
											);
										}
									})()}
								</AntContent>
							</AntLayout>
						);
					}
				})()}
			</div>
		);
	}
}
