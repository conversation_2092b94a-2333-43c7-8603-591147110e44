import React, { Component, Suspense } from 'react';
import { Route } from 'react-router-dom';
import Loading from '@datoms/react-components/src/components/Loading';
/*lazy imports */

const FuelDeliveryReport = React.lazy(() =>
  import(
    "../FuelDeliveryReport"
  )
);

const AlertsSubscriptionCustomers = React.lazy(() =>
  import(
    "../alerSubscriptionCustomers/pages"
  )
);

const MachineInforeport = React.lazy(() =>
  import(
    "../MachineInfoReport/MachineInforeport"
  )
);

const DgStatusReport = React.lazy(() =>
  import(
    "../DgStatusReport"
  )
);

const DataAvailabilityReport = React.lazy(() =>
  import(
    "../DataAvailabilityReport"
  )
);

const DeviceHealthReport = React.lazy(() =>
  import(
    "../DeviceHealthReport"
  )
);

const CustomReport = React.lazy(() => import('../../../../../webapp-component-reports/src/components/CustomReport'));

export default class PartnerReportsRoutes extends Component {
	constructor(props) {
		super(props);
		this.state = {};
	}
	render() {
		// const basePath = '/datoms-x/reports'
		const {basePath} = this.props
		return (
			<Suspense fallback={<Loading />}>
				<Route
					exact
					path={`${basePath}/fuel-delivery-report`}
					render={() => <FuelDeliveryReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/alert-delivery-report`}
					render={() => <AlertsSubscriptionCustomers {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/asset-status-report`}
					render={() => <DgStatusReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/machine-info-report`}
					render={() => <MachineInforeport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/data-availability-report`}
					render={() => <DataAvailabilityReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/device-health-report`}
					render={() => <DeviceHealthReport {...this.props} />}
				/>
				{/* <Route
					exact
					path='*'//{`${basePath}/:not_found`}
					render={() => (
						<MessageComponent message="Page not found !" />
					)}
				/> */}
			</Suspense>
		);
	}
}
