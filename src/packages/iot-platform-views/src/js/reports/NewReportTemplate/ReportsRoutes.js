import React, { Component, Suspense } from 'react';
import { Route } from 'react-router-dom';
import Loading from '@datoms/react-components/src/components/Loading';
/*lazy imports */
const ReportTemplates = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/ReportTemplates'
	)
);
const AlertSubscriptionThingsReport = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/alerSubscription/pages'
	)
);
const DailyReports = React.lazy(() =>
	import(
		'../../../../../../containers/reports/DailyReports/DgDailyReport'	
	)
);
const GasGensetDailyReports = React.lazy(
	() =>
	  import(
		"../../../../../dg-monitoring-views/src/js/features/Reports/DGReport/pages/DailyReports"
	  ),
);
const GasGensetMultiAssetReports = React.lazy(
	() =>
	  import(
		"../../../../../../containers/reports/MultiAssetReports/GasGensetMultiAssetReport"
	  ),
);
const CriticalReports = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/CriticalTrendsReport'
	)
);
const DgRunReports = React.lazy(() =>
	import(
		'../../../../../../containers/reports/TripReports/DgTripReport'
	)
);
const GasGensetTripReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/TripReports/GasGensetTripReport'
	)
);
const DGMultiAssetReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/MultiAssetReports/DgMultiAssetReport'
	)
);
const DGSnapshotReport = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/DGReport/pages/SnapshotReport'
	)
);
const DataAvailabilityReport = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/DataAvailabilityReport'
	)
);
const DGFaultReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/FaultReports/DgFaultReport'
	)
);
const GasGensetFaultReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/FaultReports/GasGensetFaultReport'
	)
);
const ElevatorFaultReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/FaultReports/ElevatorFaultReport'
	)
);
const SolarPumpFaultReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/FaultReports/SolarPumpFaultReport'
	)
);
const LifetimeReports = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/summary-reports/pages/LifetimeReports'
	)
);
const ParameterReports = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/summary-reports/pages/ParameterReports'
	)
);
const FuelTankDailyReports = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/fuel-tank-reports/pages/DailyReports'
	)
);
const FuelTankFillReport = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/fuel-tank-reports/pages/FuelTankFillReport'
	)
);
const FuelTankSnapShotReports = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/fuel-tank-reports/pages/SnapshotReports'
	)
);
const CompDailyReports = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/compressor-reports/DailyReports'
	)
);
const CompFaultReports = React.lazy(() =>
	import(
		'../../../../../../containers/reports/FaultReports/CompressorFaultReport'
	)
);
const CompDgRunReports = React.lazy(() =>
	import(
		'../../../../../../containers/reports/TripReports/CompressorTripReport'
	)
);
const CompressorMultiAssetReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/MultiAssetReports/CompressorMultiAssetReport'
	)
);
const FleetDailyReports = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/FleetReports/DailyReports'
	)
);
const FleetTripReports = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/FleetReports/TripReports'
	)
);
const FleetTripDetails = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/FleetReports/TripDetails'
	)
);

const TankerTruckFuelTankerDailyReports = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/TankerTruckDailyReports/FuelTankerDailyReports'
	)
);

const TankerTruckVehicleDailyReports = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/TankerTruckDailyReports/VehicleReports'
	)
);

const TankerTruckFuelFilledDispenseReports = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/TankerTruckDailyReports/FuelFilledDispenseReports'
	)
);
const TankerTruckTripReports = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/TankerTruckDailyReports/TripReports'
	)
);
const TankerTruckTripDetails = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/fleet-reports/pages/TankerTruckDailyReports/TripDetails'
	)
);
const EnergyMeterDailyReport = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/energy-meter-reports/pages/DailyReports'
	)
);
const InverterDailyReport = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/energy-meter-reports/pages/DailyReports'
	)
);
const SolarPowerDailyReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/DailyReports/SolarPowerDailyReport'
	)
);
const SolarPumpDailyReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/DailyReports/SolarPumpDailyReport'
	)
);
const ProcessAnalyzerDailyReport = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/process-analyzer-reports/pages/DailyReports'
	)
);
const SolarPowerTripReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/TripReports/SolarPowerTripReport'
	)
);
const SolarPowerMultiAssetReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/MultiAssetReports/SolarPowerMultiAssetReport'
	)
);
const SolarPumpTripReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/TripReports/SolarPumpTripReport'
	)
);
const SolarPumpMultiAssetReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/MultiAssetReports/SolarPumpMultiAssetReport'
	)
);
const AcEnergyDailyReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/DailyReports/AcEnergyMeterDailyReport'
	)
);
const AcEnergyMultiAsserReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/MultiAssetReports/AcEnergyMeterMultiAssetReport'
	)
);
const GridEnergyMeterDailyReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/DailyReports/GridEnergyMeterDailyReport'
	)
);
const GridEnergyMeterMultiAssetReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/MultiAssetReports/GridEnergyMeterMultiAssetReport'
	)
);
const DcEnergyDailyReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/DailyReports/DcEnergyMeterDailyReport'
	)
);
const DcEnergyMultiAsserReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/MultiAssetReports/DcEnergyMeterMultiAssetReport'
	)
);
const BatteryDailyReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/DailyReports/BatteryDailyReport'
	)
);
const BatteryMultiAsserReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/MultiAssetReports/BatteryMultiAssetReport'
	)
);
const AcElectricalMachinesDailyReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/DailyReports/AcElectricalMachineDailyReport'
	)
);
const AcElectricalMachinesTripReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/TripReports/AcElectricalMachineTripReport'
	)
);
const AcElectricalMachinesMultiAsserReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/MultiAssetReports/AcElectricalMachineMultiAssetReport'
	)
);
const ExhaustFanDailyReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/DailyReports/ExhaustFanDailyReport'
	)
);
const ExhaustFanTripReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/TripReports/ExhaustFanTripReport'
	)
);
const ExhaustFanMultiAsserReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/MultiAssetReports/ExhaustFanMultiAssetReport'
	)
);
const TempHumidDailyReport = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/temp-humid-report/pages/DailyReports'
	)
);
const EnergyMeterAvailabilityReport = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/energy-meter-reports/pages/AvailabilityReport'
	)
);
const FuelFillDrainReport = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/dg-new-reports/pages/FuelFillDrainReport'
	)
);
const SummaryReports = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/SummaryReports'
	)
);
const YesterdayDailyFaultReport = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/YesterdayDailyFaultReport'
	)
);
const FlowMeterDailyReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/DailyReports/FlowMeterDailyReport'
	)
);
const FlowMeterHourlyReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/HourlyReports/FlowMeterHourlyReport'
	)
);
const FlowMeterMultiAssetReport = React.lazy(() =>
	import(
		'../../../../../../containers/reports/MultiAssetReports/FlowMeterMultiAssetReport'
	)
);
const FlowMeterSiteConsumptionReport = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/flow-meter-reports/pages/SiteConsumption'
	)
);
const GensetFuelTankSiteReport = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/DGReport/pages/GensetFuelTankSiteReport'
	)
);
const EnergySummaryReport = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/WarehouseSiteReport/EnergySummaryReport'
	)
);
const SensorSummaryReport = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/WarehouseSiteReport/SensorSummaryReport'
	)
);
const PollutionParamRangeSummaryReport = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/PollutionMonitoringReports/pages/ParamRangeReport'
	)
);
const SMSSummaryReport = React.lazy(() =>
	import(
		'../../../../../dg-monitoring-views/src/js/features/Reports/SMSSummaryReport'
	)
);

const CustomReport = React.lazy(() =>
	import(
		'../../../../../webapp-component-reports/src/components/CustomReport'
	)
);

const DgStatusReport = React.lazy(() =>
	import(
		'../../reports/DgStatusReport'
	)
);

const DataAvailability = React.lazy(() =>
	import(
		'../../../../../../containers/reports/DataAvailabilityReport'
	)
);


export default class ReportsRoutes extends Component {
	constructor(props) {
		super(props);
		this.state = {};
	}
	render() {
		// const basePath = '/datoms-x/reports'
		const { basePath } = this.props;
		return (
			<Suspense fallback={<Loading />}>
				<Route
					exact
					path={`${basePath}/alert-delivery-summary-reports`}
					render={() => (
						<AlertSubscriptionThingsReport {...this.props} />
					)}
				/>
				<Route
					exact
					path={`${basePath}/template-reports`}
					render={() => <DailyReports {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/gas-genset/daily-report`}
					render={() => <GasGensetDailyReports {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/gas-genset/multi-asset-reports`}
					render={() => <GasGensetMultiAssetReports {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/critical-trends-reports`}
					render={() => <CriticalReports {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/dg-run-reports`}
					render={() => <DgRunReports {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/gas-genset/run-reports`}
					render={() => <GasGensetTripReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/dg-multi-assets-report`}
					render={() => <DGMultiAssetReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/dg-snapshot-report`}
					render={() => <DGSnapshotReport {...this.props} />}
				/>
				<Route
					exact
					path={`${this.props.match.path}/data-availability-report`}
					render={() => <DataAvailabilityReport {...this.props} />}
				/>
				<Route
					exact
					path={`${this.props.match.path}/data-availability`}
					render={() => <DataAvailability {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/asset-status-report`}
					render={() => <DgStatusReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/fault-reports`}
					render={() => <DGFaultReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/gas-genset/fault-reports`}
					render={() => <GasGensetFaultReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/elevator/fault-reports`}
					render={() => <ElevatorFaultReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/lifetime-reports`}
					render={() => <LifetimeReports {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/parameter-reports`}
					render={() => <ParameterReports {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/fuel-tank/daily-reports`}
					render={() => <FuelTankDailyReports {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/fuel-tank-fill`}
					render={() => <FuelTankFillReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/fuel-tank/snapshot-reports`}
					render={() => <FuelTankSnapShotReports {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/compressor/daily-reports`}
					render={() => <CompDailyReports {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/compressor/fault-reports`}
					render={() => <CompFaultReports {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/compressor/run-reports`}
					render={() => <CompDgRunReports {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/compressor/multi-asset-reports`}
					render={() => <CompressorMultiAssetReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/fleet/daily-reports`}
					render={() => <FleetDailyReports {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/fleet/trip-reports`}
					render={() => <FleetTripReports {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/fleet/trip-reports/details`}
					render={() => <FleetTripDetails {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/tanker-truck/daily-reports`}
					render={() => (
						<TankerTruckFuelTankerDailyReports {...this.props} />
					)}
				/>
				<Route
					exact
					path={`${basePath}/vehicle/daily-reports`}
					render={() => (
						<TankerTruckVehicleDailyReports {...this.props} />
					)}
				/>
				<Route
					exact
					path={`${basePath}/fuel-fill-dispense`}
					render={() => (
						<TankerTruckFuelFilledDispenseReports {...this.props} />
					)}
				/>
				<Route
					exact
					path={`${basePath}/tanker-truck/trip-reports`}
					render={() => <TankerTruckTripReports {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/tanker-truck/trip-reports/details`}
					render={() => <TankerTruckTripDetails {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/energy-meter/daily-reports`}
					render={() => <EnergyMeterDailyReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/inverter/daily-reports`}
					render={() => <InverterDailyReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/solar-power/daily-reports`}
					render={() => <SolarPowerDailyReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/solar-pump/daily-reports`}
					render={() => <SolarPumpDailyReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/process-analyzer/daily-reports`}
					render={() => <ProcessAnalyzerDailyReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/solar-power/trip-reports`}
					render={() => <SolarPowerTripReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/solar-power/multi-asset-reports`}
					render={() => <SolarPowerMultiAssetReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/solar-pump/fault-reports`}
					render={() => <SolarPumpFaultReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/solar-pump/trip-reports`}
					render={() => <SolarPumpTripReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/solar-pump/multi-asset-reports`}
					render={() => <SolarPumpMultiAssetReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/ac-energy-meter/daily-reports`}
					render={() => <AcEnergyDailyReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/ac-energy-meter/multi-asset-reports`}
					render={() => <AcEnergyMultiAsserReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/grid-energy-meter/daily-reports`}
					render={() => <GridEnergyMeterDailyReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/grid-energy-meter/multi-asset-reports`}
					render={() => <GridEnergyMeterMultiAssetReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/dc-energy-meter/daily-reports`}
					render={() => <DcEnergyDailyReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/dc-energy-meter/multi-asset-reports`}
					render={() => <DcEnergyMultiAsserReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/battery/daily-reports`}
					render={() => <BatteryDailyReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/battery/multi-asset-reports`}
					render={() => <BatteryMultiAsserReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/ac-energy-meter/daily-reports`}
					render={() => <AcElectricalMachinesDailyReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/ac-energy-meter/daily-reports`}
					render={() => <AcElectricalMachinesTripReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/ac-energy-meter/multi-asset-reports`}
					render={() => <AcElectricalMachinesMultiAsserReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/exhaust-fan/daily-reports`}
					render={() => <ExhaustFanDailyReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/exhaust-fan/trip-reports`}
					render={() => <ExhaustFanTripReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/exhaust-fan/multi-asset-reports`}
					render={() => <ExhaustFanMultiAsserReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/temp-humid/daily-reports`}
					render={() => <TempHumidDailyReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/energy-meter/availability-reports`}
					render={() => (
						<EnergyMeterAvailabilityReport {...this.props} />
					)}
				/>
				<Route
					exact
					path={`${basePath}/fuel-fill-drain`}
					render={() => <FuelFillDrainReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/summary-reports`}
					render={() => <SummaryReports {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/yesterday-data-reports`}
					render={() => <YesterdayDailyFaultReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/dg-test-reports`}
					render={() => <FuelFillDrainReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/flow-meter/daily-reports`}
					render={() => <FlowMeterDailyReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/flow-meter/hourly-reports`}
					render={() => <FlowMeterHourlyReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/flow-meter/multi-asset-reports`}
					render={() => <FlowMeterMultiAssetReport {...this.props} />}
				/>
				<Route
					exact
					path={`${this.props.match.path}/flow-meter/site-consumption-reports`}
					render={() => <FlowMeterSiteConsumptionReport {...this.props} />}
				/>
				<Route
					exact
					path={`${this.props.match.path}/genset-fuel-tank-site-report`}
					render={() => <GensetFuelTankSiteReport {...this.props} />}
				/>
				<Route
					exact
					path={`${this.props.match.path}/energy-summary-report`}
					render={() => <EnergySummaryReport {...this.props} />}
				/>
				<Route
					exact
					path={`${this.props.match.path}/sensor-summary-report`}
					render={() => <SensorSummaryReport {...this.props} />}
				/>
				<Route
					exact
					path={`${this.props.match.path}/pollution-monitoring/param-range-report`}
					render={() => <PollutionParamRangeSummaryReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/violation-summary-report`}
					render={() => <SMSSummaryReport {...this.props} />}
				/>
				<Route
					exact
					path={`${basePath}/custom-reports`}
					render={() => (
						<CustomReport
							{...this.props}
							type="custom"
							data_api={['data']}
							templateReport={true}
							timeZone={
								this.props.user_preferences &&
								this.props.user_preferences.timezone
									? this.props.user_preferences.timezone
									: 'Asia/Kolkata'
							}
							application_name={`${this.props.host_app_name}`}
							no_application_name={true}
						/>
					)}
				/>
				{/* <Route
					exact
					path='*'//{`${basePath}/:not_found`}
					render={() => (
						<MessageComponent message="Page not found !" />
					)}
				/> */}
			</Suspense>
		);
	}
}
