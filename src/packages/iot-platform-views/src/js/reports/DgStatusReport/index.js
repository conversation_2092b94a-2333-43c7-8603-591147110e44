import React, { Children, Component } from "react";
import "./styles.less";
import Backyard from "@datoms/js-utils/src/Backyard/Backyard_back";
import ReportController from "@datoms/react-components/src/components/ReportController";
import Loading from "@datoms/react-components/src/components/Loading";
import DownloadModal from "./download-modal/index";
import ReportsHeader from "./reports-header/index";
import CustomizeTable from "@datoms/react-components/src/components/CustomizeTable";
import AntDivider from "@datoms/react-components/src/components/AntDivider";
import AntTreeSelect from "@datoms/react-components/src/components/AntTreeSelect";
import AntSelect from "@datoms/react-components/src/components/AntSelect";
import AntOption from "@datoms/react-components/src/components/AntOption";
import AntSwitch from "@datoms/react-components/src/components/AntSwitch";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import {
  retriveVendorThingsList,
  retriveCustomerList,
  retrieveTerritoryList,
  retriveThingsList,
} from "@datoms/js-sdk";
import _find from "lodash/find";
import _filter from "lodash/filter";
import _uniqBy from "lodash/uniqBy";
import moment from "moment-timezone";
import CustomizeModal from "./CustomizeModal/index";
import { reportConfigs } from "@datoms/dg-monitoring-views/src/js/features/Reports/ReportsParamConfigure/report-configs";
import { getFinalPreference } from "@datoms/dg-monitoring-views/src/js/features/Reports/ReportsParamConfigure/logic";
import SkeletonLoader from "@datoms/react-components/src/components/SkeletonLoader";
import { totalActiveFaultsWithWarnTripStatus } from "@datoms/dg-monitoring-views/src/js/data_handling/getFaultTypeRealtime";

class DgStatusReport extends Component {
  constructor(props) {
    super(props);
    this.state = {
      things: [],
      summaryThings: [],
      summaryThingsRes: {},
      loading: true,
      downloadData: null,
      machine_info_labels: {},
      config_object: this.mergeParamConfig(props),
      isDownloadInUrl: this.findDownload(props?.history),
      deviceFilterVal: "all",
      vendors: [],
    };
    this.downloadModalRef = React.createRef();
    this.customizeDrawerRef = React.createRef();
    this.invisibleReportRef = React.createRef();
    this.generateDataViewConfig = this.generateDataViewConfig.bind(this);
    this.goBackPage = this.goBackPage.bind(this);
    this.onExportReady = this.onExportReady(this);
  }

  findDownload(history) {
    return history?.location?.search?.includes("download=true");
  }

  mergeParamConfig(props) {
    let defaultConfig = JSON.parse(
      JSON.stringify(
        reportConfigs(props.vendor_id, props.client_id)["dg_status_report"],
      ),
    );
    let finalConfig;
    let final_preference = getFinalPreference(
      props.user_preferences,
      "dg_status_report",
    );
    if (final_preference) {
      let existingConfig = JSON.parse(JSON.stringify(final_preference));
      finalConfig = {
        table: { ...defaultConfig.table, ...existingConfig.table },
      };
    } else {
      finalConfig = defaultConfig;
    }
    return finalConfig;
  }
  async initialApis() {
    const { client_id, application_id, category } = this.props;
    const [customerList, territoryList, thingsList] = await Promise.all([
      retriveCustomerList(client_id),
      retrieveTerritoryList(client_id),
      application_id === 16
        ? retriveThingsList(
            {
              client_id,
              application_id,
            },
            `?lite=true&without_device=true&parameters=false`,
          )
        : retriveVendorThingsList(
            {
              vendor_id: client_id,
              application_id: application_id,
            },
            "?lite=true&parameters==false",
          ),
    ]);
    const allCats = [];
    const selectedCat = category || thingsList?.things?.[0]?.category;
    const uniqCats = [];
    if (thingsList?.things?.length) {
      thingsList.things.forEach((thing) => {
        if (!uniqCats.includes(thing.category)) {
          uniqCats.push(thing.category);
        }
      });
    }
    if (uniqCats?.length) {
      uniqCats.forEach((cat) => {
        const findCat = _find(thingsList?.things_categories, { id: cat });
        if (findCat) {
          allCats.push({
            value: cat,
            label: _find(thingsList?.things_categories, { id: cat })?.name,
          });
        }
      });
    }
    const buildTree = (data, parentId = 0) => {
      return data
        .filter((item) => item.parent_id === parentId)
        .map((item) => ({
          title: item.name,
          value: item.id.toString(),
          children: buildTree(data, item.id),
        }));
    };

    const territoriesTree = buildTree(territoryList.data);
    const filteredThingList = thingsList.things.filter(
      (thing) =>
        thing.category === selectedCat && thing.active_status === "active",
    );
    const partnersArr = [];
    filteredThingList.forEach((thing) => {
      if (
        application_id === 16 &&
        partnersArr.find((vendor) => vendor.value === thing.vendor_id) ===
          undefined
      ) {
        partnersArr.push({
          value: thing.vendor_id,
          label: thing.vendor_name,
        });
      }
    });
    this.setState(
      {
        territoryData: territoryList.data,
        territoriesTree,
        customers: customerList.customers,
        allCats,
        selectedCat,
        liteThingsList: thingsList,
        selectedPageNo: 1,
        selectedPageSize: 100,
        deviceFilterVal: "all",
        partnersArr,
      },
      async () => {
        await this.allThingsApi();
      },
    );
  }
  async thingsListApiForSummary() {
    this.setState({
      pageLoading: true,
    });
    const { client_id, application_id } = this.props;
    const {
      selectedCat,
      territoryId,
      deviceFilterVal,
      dg_type,
      onboarding_status,
      fromTime,
      uptoTime,
    } = this.state;
    const url = `?status=active&without_device=true&parameters=false&territories=${[territoryId]}&thing_category=${selectedCat}&onboarding_status=${onboarding_status}&cpcb_type=${dg_type === "all" ? "" : dg_type}&added_from=${fromTime}&added_upto=${uptoTime}&device_attached=${deviceFilterVal === "withoutDevice" ? false : deviceFilterVal === "withDevice" ? true : ""}`;
    const thingsList =
      application_id === 16
        ? await retriveThingsList(
            {
              client_id,
              application_id,
            },
            url,
          )
        : await retriveVendorThingsList(
            {
              vendor_id: client_id,
              application_id: application_id,
            },
            url,
          );
    this.setState(
      {
        summaryThingsRes: thingsList,
        summaryThings: thingsList.things,
      },
      async () => {
        if (this.state.isDownloadInUrl) {
          await Promise.all([this.filesDownload(), this.thingsListApi()]);
        } else {
          await this.thingsListApi();
        }
      },
    );
  }
  async thingsListApi(tableOnlyLoading) {
    this.setState({
      tableOnlyLoading: tableOnlyLoading,
    });

    const { client_id, application_id } = this.props;
    const {
      selectedCat,
      territoryId,
      selectedPageNo,
      selectedPageSize,
      deviceFilterVal,
      dg_type,
      onboarding_status,
      fromTime,
      uptoTime,
    } = this.state;
    const baseUrl = `?status=active&without_device=true&parameters=true&territories=${[territoryId]}&thing_category=${selectedCat}&onboarding_status=${onboarding_status}&page_no=${selectedPageNo}&results_per_page=${selectedPageSize}&cpcb_type=${dg_type === "all" ? "" : dg_type}&added_from=${fromTime}&added_upto=${uptoTime}&device_attached=${deviceFilterVal === "withoutDevice" ? false : deviceFilterVal === "withDevice" ? true : ""}`;

    const fetchFunction =
      application_id === 16 ? retriveThingsList : retriveVendorThingsList;
    const apiParams =
      application_id === 16
        ? { client_id, application_id }
        : { vendor_id: client_id, application_id };
    const response = await fetchFunction(apiParams, baseUrl);
    const allThings = response.things;
    this.setState(
      {
        thingsApiData: response,
        things: allThings,
        things_clone: allThings,
      },
      () => {
        this.generateDataViewConfig();
      },
    );
  }

  async filesDownload() {
    const { client_id, application_id } = this.props;
    const {
      selectedCat,
      territoryId,
      deviceFilterVal,
      dg_type,
      onboarding_status,
      fromTime,
      uptoTime,
    } = this.state;
  
    const baseUrl = `?status=active&without_device=true&parameters=true&territories=${[territoryId]}&thing_category=${selectedCat}&onboarding_status=${onboarding_status}&cpcb_type=${dg_type === "all" ? "" : dg_type}&added_from=${fromTime}&added_upto=${uptoTime}&device_attached=${deviceFilterVal === "withoutDevice" ? false : deviceFilterVal === "withDevice" ? true : ""}`;
    
    const fetchFunction =
      application_id === 16 ? retriveThingsList : retriveVendorThingsList;
    const apiParams =
      application_id === 16
        ? { client_id, application_id }
        : { vendor_id: client_id, application_id };
    
    const fetchPage = async (pageNo, resultsPerPage=100) => {
      const url = `${baseUrl}&page_no=${pageNo}&results_per_page=${resultsPerPage}`;
      const response = await fetchFunction(apiParams, url);
      return response;
    };
  
    let allThings = [];
    let totalPages = 1;
  
    // Fetch the first page to get total counts
    const firstPageResponse = await fetchPage(1,1);
    totalPages = Math.ceil(firstPageResponse.total_counts / 100);
  
    const fetchInBatches = async (pages) => {
      const results = [];
      for (let i = 0; i < pages.length; i += 5) {
        const batch = pages.slice(i, i + 5); // Get a batch of 5 pages
        const batchResults = await Promise.all(batch.map(page => page()));
        results.push(...batchResults);
      }
      return results;
    };
  
    // Create an array of fetch functions for all pages
    const pages = Array.from({ length: totalPages }, (_, i) => () => fetchPage(i + 1));
  
    // Execute API calls in batches of 5
    const results = await fetchInBatches(pages);
  
    results.forEach((result) => {
      if (result.things.length > 0) {
        allThings = allThings.concat(result.things);
      }
    });
  
    this.setState(
      {
        downloadedThings: allThings,
      },
      () => {
        this.generateDataViewConfigDownload();
      }
    );
  }
  

  async allThingsApi() {
    try {
      await this.thingsListApiForSummary();
    } catch (error) {}
  }
  async componentDidMount() {
    await this.initialApis();
  }

  isMobile() {
    return window.innerWidth < 576;
  }

  tableHeader() {
    const { selectedCat } = this.state;
    const commonColumnConfig = [
      {
        title: this.props.t? this.props.t('asset_name'): "Asset Name",
        // title: "Asset Name",
        pdf_title: "Asset Name",
        dataIndex: "thing_name",
        width: 200,
        fixed: (isMobile) => (isMobile ? false : "left"),
      },
      {
        title: "Engine Sr.No",
        pdf_title: "Engine Sr.No",
        dataIndex: "engine_sr_no",
        width: 200,
        fixed: (isMobile) => (isMobile ? false : "left"),
      }]

      if(this.props.client_id === 1280) {
        commonColumnConfig.push({
          title: "Genset Sr.No",
          pdf_title: "Genset Sr.No",
          dataIndex: "genset_sr_no",
          width: 200,
        })
      }

     commonColumnConfig.push({
        title: "KVA",
        pdf_title: "KVA",
        dataIndex: "kva",
        width: 200,
      },
      {
        title: "Engine Model",
        pdf_title: "Engine Model",
        dataIndex: "model",
        width: 200,
      },
      {
        title: this.props.t? this.props.t('device') + " #": "Device #",
        // title: "Device #",
        pdf_title: "Device #",
        dataIndex: "device_name",
        width: 200,
      },
      {
        title: this.props.t? this.props.t('territory'): "Territory",
        // title: "Territory",
        pdf_title: "Territory",
        dataIndex: "territory",
        width: 200,
        render: (text) =>
          text ? (
            <AntTooltip title={text}>{text.split(" -> ")[0] || "-"}</AntTooltip>
          ) : (
            "-"
          ),
      },
      {
        title: this.props.t? this.props.t('customer_name'): "Customer Name",
        // title: "Customer Name",
        pdf_title: "Customer Name",
        dataIndex: "customer_name",
        width: 200,
      },
      {
        title: "Lifetime Runhour",
        pdf_title: "Lifetime Runhour",
        dataIndex: "lifetime_runhour",
        width: 200,
      },
      {
        title: this.props.t? this.props.t('asset_added_at'): "Asset added at",
        // title: "Asset added at",
        pdf_title: "Asset added at",
        dataIndex: "asset_added_at",
        width: 200,
      },
      {
        title: this.props.t? this.props.t('asset_added_by'): "Asset added by",
        // title: "Asset added by",
        pdf_title: "Asset added by",
        dataIndex: "asset_added_by",
        width: 200,
      },
      {
        title: "Commissioning date",
        pdf_title: "Commissionind date",
        dataIndex: "commissioning_date",
        width: 200,
      },
      {
        title: this.props.t? this.props.t('onboard_status'): "Onboard status",
        // title: "Onboard status",
        pdf_title: "Onboard status",
        dataIndex: "onboard_status",
        width: 200,
      },
      {
        title: this.props.t? this.props.t('onboarding_date'): "Onboarding Date",
        // title: "Onboarding Date",
        pdf_title: "Onboarding Date",
        dataIndex: "onboarding_date",
        width: 200,
      },
      {
        title: this.props.t? this.props.t('device_status'): "Device Status",
        // title: "Device status",
        pdf_title: "Device status",
        dataIndex: "device_status",
        width: 200,
        render: (text) => (
          <div style={{ color: text === "online" ? "#1cb855" : "" }}>
            {text}
          </div>
        ),
      },
      {
        title: this.props.t? this.props.t('offline_since'): "Offline since",
        // title: "Offline since",
        pdf_title: "Offline since",
        dataIndex: "offline_since",
        width: 200,
      },
      {
        title: this.props.t? this.props.t('activity_status'): "Activity status",
        // title: "Activity status",
        pdf_title: "Activity status",
        dataIndex: "activity_status",
        width: 200,
        render: (text) => (
          <div
            style={{
              color:
                text === "Running"
                  ? "#1cb855"
                  : text === "Switched Off"
                    ? "#ff0000"
                    : "",
            }}
          >
            {text}
          </div>
        ),
      },
      {
        title: "Genset state",
        pdf_title: "Genset state",
        dataIndex: "genset_state",
        width: 200,
      },
      {
        title: "Fault Status",
        pdf_title: "Fault Status",
        dataIndex: "fault_status",
        width: 200,
      },
      {
        title: "Fault Names",
        pdf_title: "Fault Names",
        dataIndex: "fault_names",
        width: 200,
      },
    )

    // Final headers with adjusted columns for download
    let headerNameKey = commonColumnConfig.map((column) => ({
      ...column,
      fixed:
        typeof column.fixed === "function" ? column.fixed(false) : column.fixed,
    }));

    const territoryIndex = commonColumnConfig.findIndex(
      (column) => column.dataIndex === "territory"
    );

    let headernameKeyForDownload = [
      ...commonColumnConfig.slice(0, territoryIndex), // First n columns are common
      {
        title: "Territory - L1",
        pdf_title: "Territory - L1",
        dataIndex: "territory_l1",
        width: 200,
      },
      {
        title: "Territory - L2",
        pdf_title: "Territory - L2",
        dataIndex: "territory_l2",
        width: 200,
      },
      {
        title: "Territory - L3",
        pdf_title: "Territory - L3",
        dataIndex: "territory_l3",
        width: 200,
      },
      {
        title: "Territory - L4",
        pdf_title: "Territory - L4",
        dataIndex: "territory_l4",
        width: 200,
      },
      {
        title: "Territory - L5",
        pdf_title: "Territory - L5",
        dataIndex: "territory_l5",
        width: 200,
      },
      ...commonColumnConfig.slice(territoryIndex), // Skip customer_name which is already in common config
    ].map((column) => ({
      ...column,
      render: undefined,
      fixed: undefined,
    }));
    function removedColsExceptDG(headerNameKey) {
      return headerNameKey.filter((data) => {
        return ![
          "model",
          "engine_sr_no",
          "genset_sr_no",
          "kva",
          "genset_state",
          "lifetime_runhour",
          "commissioning_date",
          "fault_status",
          "fault_names",
        ].includes(data.dataIndex);
      });
    }
    if (selectedCat !== 18) {
      headerNameKey = removedColsExceptDG(headerNameKey);
      headernameKeyForDownload = removedColsExceptDG(headernameKeyForDownload);
    }
    function removedCols(headerNameKey) {
      return headerNameKey.filter((data) => {
        return ![
          "customer_name",
          "territory",
          "territory_l1",
          "territory_l2",
          "territory_l3",
          "territory_l4",
          "territory_l5",
          "commissioning_date",
          "onboard_status",
          "onboarding_date",
        ].includes(data.dataIndex);
      });
    }
    if (this.props.application_id === 16) {
      headerNameKey = removedCols(headerNameKey);
      headernameKeyForDownload = removedCols(headernameKeyForDownload);
    }
    headernameKeyForDownload = headernameKeyForDownload.filter((data) => {
      return data.dataIndex !== "territory";
    });
    return {
      downloadHeader: headernameKeyForDownload,
      viewData: headerNameKey,
    };
  }

  getFaultType() {
    const { things } = this.state;
    let findFaultType = {};
    if (things?.length) {
      things.map((item) => {
        if (!findFaultType[item.id]) {
          findFaultType[item.id] = "";
        }
        const thingWithLatestData = {
          thing_id: item.id,
          time: item.last_data_received_time,
          data: item.parameters.reduce((obj, data) => {
            obj[data.key] = data.value;
            return obj;
          }, {}),
        };
        findFaultType[item.id] = totalActiveFaultsWithWarnTripStatus(
          thingWithLatestData,
          this.props.vendor_id,
          { things: things },
        )?.fault_status;
      });
    }
    return findFaultType;
  }

  generateDataViewConfigDownload() {
    const {
      things,
      downloadedThings,
      customers,
      territoryData,
      config_object,
      territoryId,
      deviceFilterVal,
      vendors,
      summaryThingsRes,
      summaryThings,
    } = this.state;
    const { user_preferences, application_id, client_id } = this.props;
    const downloadedTableHeader = this.tableHeader().downloadHeader;
    const faultType = this.getFaultType();
    new Backyard({
      scripts: [
        "https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js",
        "https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.23/moment-timezone.min.js",
        "https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.33/moment-timezone-with-data-1970-2030.min.js",
        "https://cdn.jsdelivr.net/npm/lodash@4.17.15/lodash.min.js",
      ],
      input: {
        things,
        downloadedThings,
        customers,
        territoryData,
        config_object,
        user_preferences,
        territoryId,
        deviceFilterVal,
        vendors,
        downloadedTableHeader,
        application_id,
        faultType,
        summaryThingsRes,
        summaryThings,
        client_id,
      },
      run: function (ctx, input, cb) {
        let {
          things,
          downloadedThings,
          customers,
          territoryData,
          config_object,
          user_preferences,
          territoryId,
          deviceFilterVal,
          vendors,
          downloadedTableHeader,
          application_id,
          faultType,
          summaryThingsRes,
          summaryThings,
          client_id,
        } = input;
        const finalThings = downloadedThings || things;
        let downloadData = { conf: [], data: [] };
        let summaryData = [];
        let pdfMainHeaderOption = {
          // pdf_top_line: true,
          // pdf_bottom_line: true,
          pdf_text_align: "center",
          textColor: [255, 255, 255],
          pdf_size: 11,
          fill: {
            fill_color: [245, 135, 65],
            y_value: 38,
          },
        };
        let pushObj = pdfMainHeaderOption;
        pushObj["compo"] = "Text";
        pushObj["props"] = {
          type: "bold",
        };
        pushObj["col_props"] = {
          span: 24,
        };
        let REPORT_TYPE = {
          text_conf: {
            props: {
              gutter: 10,
            },
            child: [pushObj],
          },
          text_data: [
            {
              textData: ["DG Status Report", ""],
            },
          ],
        };
        downloadData.conf.push(REPORT_TYPE.text_conf);
        downloadData.data.push(REPORT_TYPE.text_data);
        let pageConfigGenerated = {
          pdf_force_new_page: true,
        };
        let textPushGenerated = pageConfigGenerated;
        textPushGenerated["compo"] = "Text";
        textPushGenerated["props"] = {
          type: "normal",
        };

        textPushGenerated["col_props"] = {
          span: 24,
        };
        textPushGenerated["pdf_size"] = 5;
        let reportGeneratedeText =
          "Report Generated at: " +
          ctx.moment.tz(user_preferences.timezone).format("DD MMM YYYY, HH:mm");
        let { text_conf_generated, text_data_generated } = {
          text_conf_generated: {
            props: {
              gutter: 10,
            },
            child: [textPushGenerated],
          },
          text_data_generated: [
            {
              textData: [reportGeneratedeText],
            },
          ],
        };
        downloadData.conf.push(text_conf_generated);
        downloadData.data.push(text_data_generated);
        let pageConfigDetailed = {
          pdf_force_new_page: true,
        };
        let textPushDetailed = pageConfigDetailed;
        textPushDetailed["compo"] = "Text";
        textPushDetailed["props"] = {
          type: "bold",
        };

        textPushDetailed["col_props"] = {
          span: 24,
        };
        textPushDetailed["pdf_size"] = 12;
        let { text_conf_detailed, text_data_detailed } = {
          text_conf_detailed: {
            props: {
              gutter: 10,
            },
            child: [textPushDetailed],
          },
          text_data_detailed: [
            {
              textData: ["Detailed Report"],
            },
          ],
        };
        downloadData.conf.push(text_conf_detailed);
        downloadData.data.push(text_data_detailed);
        let tableIndexMap = {
          thing_name: "thing_name",
          device_name: "device_name",
          engine_sr_no: "engine_sr_no",
          genset_sr_no: "genset_sr_no",
          kva: "kva",
          model: "model",
          territory: "territory",
          customer_name: "customer_name",
          device_status: "device_status",
          lifetime_runhour: "lifetime_runhour",
          last_data_rcvd: "last_data_rcvd",
          dg_state: "dg_state",
          fault_status: "fault_status",
          fault_names: "fault_names",
        };
        // let finalFilteredColumn = [];
        // downloadedTableHeader.map(function (row) {
        //   if (
        //     config_object["table"][tableIndexMap[row.dataIndex]] ||
        //     !tableIndexMap[row.dataIndex]
        //   ) {
        //     finalFilteredColumn.push(row);
        //   }
        // });
        // downloadedTableHeader = finalFilteredColumn;
        function getLastUpdatedTime(last_data_received_time) {
          return last_data_received_time
            ? ctx
                .moment(last_data_received_time * 1000)
                .format("DD MMM YYYY, HH:mm")
            : "-";
        }
        function deviceListForThings(deviceArray) {
          let deviceNameArr = [];
          if (deviceArray && deviceArray.length) {
            deviceArray.map((devices) => {
              deviceNameArr.push(devices.qr_code);
            });
          }
          return deviceNameArr?.join(", ");
        }
        function territoryTreeFunc(territories) {
          let tree = [];
          territories.forEach((item) => {
            let level = tree;
            Object.keys(item)
              .sort()
              .forEach((key) => {
                let found = level.find((el) => el.value === item[key].id);
                if (!found) {
                  let newLevel = {
                    value: item[key].id,
                    title: item[key].name,
                    children: [],
                  };
                  level.push(newLevel);
                  level = newLevel.children;
                } else {
                  level = found.children;
                }
              });
          });
          return tree;
        }
        const findTerritory = (territory_id) => {
          console.log("ldiff", territory_id);
          let currentTerritory = ctx["_"].find(territoryData, {
            id: territory_id,
          });
          let territories = {},
            territoryIds = [],
            treeTeritories = {};
          let maxDepth = 0;
          let tempTerritory = currentTerritory;
          while (tempTerritory && tempTerritory.parent_id !== 0) {
            maxDepth++;
            tempTerritory = ctx["_"].find(territoryData, {
              id: tempTerritory.parent_id,
            });
          }
          while (currentTerritory && currentTerritory.parent_id !== 0) {
            let count = maxDepth - Object.keys(territories).length;
            territories[`territory_l${count}`] = currentTerritory.name;
            treeTeritories[`territory_l${count}`] = {
              id: currentTerritory.id,
              name: currentTerritory.name,
            };
            territoryIds.push(currentTerritory.id);
            currentTerritory = ctx["_"].find(territoryData, {
              id: currentTerritory.parent_id,
            });
          }
          return { territories, territoryIds, treeTeritories };
        };
        let MiTableData = [],
          totalTerritories = [];
        finalThings.forEach((item, ind) => {
          let findCustomers = ctx["_"].find(customers, {
            id: item.customer_id,
          });
          console.log("dslkjflkjskdjfj", findCustomers);
          let findCalculatedLifetimeRunhour = "";
          let findRnHr = ctx["_"].find(item.parameters, {
            key: "rnhr",
          });
          let findCalcRnhr = ctx["_"].find(item.parameters, {
            key: "calculated_runhour",
          });
          if (
            findRnHr &&
            findRnHr.value &&
            findRnHr.value !== "" &&
            parseFloat(findRnHr.value) > 0
          ) {
            findCalculatedLifetimeRunhour = parseFloat(findRnHr.value) * 3600;
          } else if (findCalcRnhr?.aggregated_value?.lifetime?.sum) {
            findCalculatedLifetimeRunhour =
              findCalcRnhr.aggregated_value.lifetime.sum;
          }
          let lifetimehour = Math.floor(findCalculatedLifetimeRunhour / 3600);
          let lifetimeMin = Math.floor(
            (findCalculatedLifetimeRunhour % 3600) / 60,
          );
          let lifeTimeRunHourValue =
            (lifetimehour < 10 ? "0" + lifetimehour : lifetimehour) +
            " : " +
            (lifetimeMin < 10 ? "0" + lifetimeMin : lifetimeMin);
          let paramDetails = ctx["_"].filter(item.parameters, {
            type: "fault",
          });
          let activeFault = [];
          if (Array.isArray(paramDetails) && paramDetails.length) {
            paramDetails.map((paramDetails) => {
              let findFaultParam = ctx["_"].find(item.parameters, {
                key: paramDetails.key,
              });
              if (parseInt(findFaultParam.value) === 1) {
                activeFault.push(paramDetails.name);
              }
            });
          }
          const activityHasStopped = [18, 67, 76, 73, 74].includes(
            item.category,
          );
          const activityStatus = ctx["_"].find(item.parameters, {
            key: "mc_st",
          })?.value;
          const gensetStat = ctx["_"].find(item.parameters, {
            key: "au_m_stat",
          })?.value;
          const isFirstDataReceived = item.first_data_time > 0;
          const deviceStatus =
            item.devices?.[0]?.online_status === 1 ? "online" : "offline";
          let isDeviceOfflineSince = "-";
          if (deviceStatus === "offline") {
            const isDeviceLastOnlineTime = item.devices?.[0]?.last_online_time;
            if (isDeviceLastOnlineTime > 0) {
              let offlineSince = ctx
                .moment()
                .diff(ctx.moment.unix(isDeviceLastOnlineTime), "hours");
              if (offlineSince > 24) {
                let days = Math.floor(offlineSince / 24);
                let remainingHours = offlineSince % 24;
                isDeviceOfflineSince = `${days} days, ${remainingHours} hours`;
              } else {
                isDeviceOfflineSince = `${offlineSince} hours`;
              }
            }
          }
          const finalFaultType = faultType[item.id]?.length
            ? `(${faultType[item.id] === "not_defined" ? "Not defined" : faultType[item.id] === "both" ? "Both (trip & warning)" : faultType[item.id] === "trip" ? "Trip" : faultType[item.id] === "warning" ? "Warning" : ""})`
            : "";
          MiTableData.push({
            thing_name: item.name,
            device_name: deviceListForThings(item.devices),
            engine_sr_no: item?.thing_details?.engine_sl_no || "-",
            genset_sr_no: item?.thing_details?.genset_sl_no || "-",
            kva: item?.thing_details?.kva || "-",
            model: item?.thing_details?.model || "-",
            customer_name: findCustomers?.name,
            lifetime_runhour: lifeTimeRunHourValue,
            asset_added_at: ctx.moment
              .unix(item.added_at)
              .format("DD MMM YYYY, HH:mm"),
            asset_added_by: item.added_by || "-",
            commissioning_date: item?.thing_details?.commissioning_date
              ? ctx.moment
                  .unix(
                    parseInt(
                      ctx
                        .moment(item.thing_details.commissioning_date)
                        .format("X"),
                    ),
                  )
                  .format("DD MMM YYYY, HH:mm")
              : "-",
            onboard_status: isFirstDataReceived ? "Success" : "Pending",
            onboarding_date: isFirstDataReceived
              ? ctx.moment
                  .unix(item.first_data_time)
                  .format("DD MMM YYYY, HH:mm")
              : "-",
            device_status: deviceStatus || "-",
            offline_since: isDeviceOfflineSince,
            activity_status:
              isFirstDataReceived && deviceStatus === "online"
                ? activityHasStopped
                  ? parseInt(activityStatus) === 1
                    ? "Running"
                    : "Stopped"
                  : item.status === "offline"
                    ? "DisConnected"
                    : "-"
                : "-",
            genset_state:
              gensetStat === "0" ? "Manual" : gensetStat === "1" ? "Auto" : "-",
            fault_status:
              activeFault.length > 0
                ? `Active Faults ${finalFaultType}`
                : "No Fault",
            fault_names: activeFault.join(", "),
            vendor_id: item.vendor_id,
          });
          const getTerritoryCustomer =
            findCustomers?.vendor_id !== client_id
              ? ctx["_"].find(customers, {
                  id: findCustomers?.vendor_id,
                })
              : ctx["_"].find(customers, {
                  id: item.customer_id,
                });
          const findActualTerritory = findTerritory(
            getTerritoryCustomer?.territory_id,
          );
          const territory = findActualTerritory.territories;
          const territoryIdsArr = findActualTerritory.territoryIds;
          const treeTeritories = findActualTerritory.treeTeritories;
          totalTerritories.push(findActualTerritory.treeTeritories);
          MiTableData[ind] = {
            ...MiTableData[ind],
            ...territory,
            territoryIdsArr,
            ...findActualTerritory.treeTeritories.id,
          };
          let territoryStr = "";
          if (Object.values(treeTeritories).length) {
            for (
              let i = 0;
              i <= Object.values(treeTeritories).length - 1;
              i++
            ) {
              if (i !== Object.values(treeTeritories).length - 1) {
                territoryStr += Object.values(treeTeritories)[i].name + " -> ";
              } else {
                territoryStr += Object.values(treeTeritories)[i].name;
              }
            }
          }
          MiTableData[ind]["territory"] = territoryStr;
        });

        const onlineThingsArr = ctx["_"].filter(summaryThings, (thing) => {
          return thing.devices?.[0]?.online_status === 1;
        });
        const offlineThingsArr = ctx["_"].filter(summaryThings, (thing) => {
          return thing.devices?.[0]?.online_status !== 1;
        });
        const assetsWithDevices = ctx["_"].filter(summaryThings, (thing) => {
          return thing.devices?.length;
        });
        const assetsWithoutDevices = ctx["_"].filter(summaryThings, (thing) => {
          return thing.devices?.length === 0;
        });
        const onboardedThings = ctx["_"].filter(summaryThings, (thing) => {
          return thing.first_data_time > 0;
        });
        const pendingThings = ctx["_"].filter(summaryThings, (thing) => {
          return !thing.first_data_time > 0;
        });
        summaryData = [
          {
            parameter: "Total Assets",
            value: summaryThings?.length || 0,
          },
          {
            parameter: "Online Assets",
            value: onlineThingsArr?.length || 0,
          },
          {
            parameter: "Offline Assets",
            value: offlineThingsArr?.length || 0,
          },
        ];
        if (application_id === 16) {
          if (assetsWithDevices?.length > 0) {
            summaryData.push({
              parameter: "Assets With Devices",
              value: assetsWithDevices?.length || 0,
            });
          }
          if (assetsWithoutDevices?.length > 0) {
            summaryData.push({
              parameter: "Assets Without Devices",
              value: assetsWithoutDevices?.length || 0,
            });
          }
        } else {
          summaryData.push(
            {
              parameter: "Onboarded Successfully",
              value: onboardedThings?.length || 0,
            },
            {
              parameter: "Onboard Pending",
              value: pendingThings?.length || 0,
            },
          );
        }
        downloadData.conf.push({
          pdf_force_new_page: false,
          props: {
            gutter: 10,
            style: {},
            className: "tableRow",
          },
          child: [
            {
              compo: "Table",
              widget: "",
              classname: "tab-1",
              table_new_page: true,
              props: {
                columns: [
                  {
                    title: "Summary",
                    dataIndex: "parameter",
                  },
                  {
                    title: " ",
                    dataIndex: "value",
                  },
                ],
                headerFont: 13,
                size: "small",
                tabRadius: 0,
                horizontalScroll: true,
                shadow: false,
                breakPoint: 1000,
                breakPoint2: 500,
                largeTable: true,
                mediumTable: false,
                smallTable: false,
              },
              col_props: {
                span: 24,
              },
              pdf_width: 50,
              pdf_table_break: {
                col_no: 7,
                row_no: 15,
              },
            },
          ],
        });
        downloadData.data.push([summaryData]);

        downloadData.conf.push({
          props: {
            gutter: 10,
            style: {},
          },
          child: [
            {
              compo: "Table",
              widget: "",
              classname: "tab-1",
              table_new_page: true,
              props: {
                columns: downloadedTableHeader,
                headerFont: 13,
                size: "small",
                tabRadius: 0,
                horizontalScroll: true,
                shadow: false,
                breakPoint: 1000,
                breakPoint2: 500,
                largeTable: true,
                mediumTable: false,
                smallTable: false,
              },
              col_props: {
                span: 24,
              },
              pdf_width: 50,
              pdf_table_break: {
                col_no: 5,
                row_no: 20,
              },
            },
          ],
        });
        downloadData.data.push([MiTableData]);
        downloadData.file_name = "DG Status Report";
        cb({
          downloadData,
        });
      },
      cb: (value) => {
        this.setState(
          {
            downloadData: value.downloadData,
            loading: false,
            download_loading: false,
          },
          () => {
            this.getDownloadRender();
          },
        );
      },
    });
  }

  generateDataViewConfig() {
    const {
      things,
      downloadedThings,
      customers,
      territoryData,
      config_object,
      territoryId,
      deviceFilterVal,
      vendors,
      summaryThingsRes,
      summaryThings,
    } = this.state;
    let t = this.props.t;
    const translations = {
      onboarded_successfully: t? t('onboarded_successfully'): "Onboarded Successfully",
      total_assets: t? t('total_assets'): "Total Assets",
      online_assets: t? t('online_assets'): "Online Assets",
      offline_assets: t? t('offline_assets'): "Offline Assets",
      onboard_pending: t? t('onboard_pending'): "Onboard Pending"
    }
    const { user_preferences, application_id, client_id } = this.props;
    const downloadedTableHeader = this.tableHeader().downloadHeader;
    const faultType = this.getFaultType();
    new Backyard({
      scripts: [
        "https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js",
        "https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.23/moment-timezone.min.js",
        "https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.33/moment-timezone-with-data-1970-2030.min.js",
        "https://cdn.jsdelivr.net/npm/lodash@4.17.15/lodash.min.js",
      ],
      input: {
        things,
        downloadedThings,
        customers,
        territoryData,
        config_object,
        user_preferences,
        territoryId,
        deviceFilterVal,
        vendors,
        downloadedTableHeader,
        application_id,
        faultType,
        summaryThingsRes,
        summaryThings,
        client_id,
        translations
      },
      run: function (ctx, input, cb) {
        let {
          things,
          downloadedThings,
          customers,
          territoryData,
          config_object,
          user_preferences,
          territoryId,
          deviceFilterVal,
          vendors,
          downloadedTableHeader,
          application_id,
          faultType,
          summaryThingsRes,
          summaryThings,
          client_id,
        } = input;
        const finalThings = downloadedThings || things;
        let downloadData = { conf: [], data: [] };
        let summaryData = [];
        let pdfMainHeaderOption = {
          // pdf_top_line: true,
          // pdf_bottom_line: true,
          pdf_text_align: "center",
          textColor: [255, 255, 255],
          pdf_size: 11,
          fill: {
            fill_color: [245, 135, 65],
            y_value: 38,
          },
        };
        let pushObj = pdfMainHeaderOption;
        pushObj["compo"] = "Text";
        pushObj["props"] = {
          type: "bold",
        };
        pushObj["col_props"] = {
          span: 24,
        };
        let REPORT_TYPE = {
          text_conf: {
            props: {
              gutter: 10,
            },
            child: [pushObj],
          },
          text_data: [
            {
              textData: ["DG Status Report", ""],
            },
          ],
        };
        downloadData.conf.push(REPORT_TYPE.text_conf);
        downloadData.data.push(REPORT_TYPE.text_data);
        let pageConfigGenerated = {
          pdf_force_new_page: true,
        };
        let textPushGenerated = pageConfigGenerated;
        textPushGenerated["compo"] = "Text";
        textPushGenerated["props"] = {
          type: "normal",
        };

        textPushGenerated["col_props"] = {
          span: 24,
        };
        textPushGenerated["pdf_size"] = 5;
        let reportGeneratedeText =
          "Report Generated at: " +
          ctx.moment.tz(user_preferences.timezone).format("DD MMM YYYY, HH:mm");
        let { text_conf_generated, text_data_generated } = {
          text_conf_generated: {
            props: {
              gutter: 10,
            },
            child: [textPushGenerated],
          },
          text_data_generated: [
            {
              textData: [reportGeneratedeText],
            },
          ],
        };
        downloadData.conf.push(text_conf_generated);
        downloadData.data.push(text_data_generated);
        let pageConfigDetailed = {
          pdf_force_new_page: true,
        };
        let textPushDetailed = pageConfigDetailed;
        textPushDetailed["compo"] = "Text";
        textPushDetailed["props"] = {
          type: "bold",
        };

        textPushDetailed["col_props"] = {
          span: 24,
        };
        textPushDetailed["pdf_size"] = 12;
        let { text_conf_detailed, text_data_detailed } = {
          text_conf_detailed: {
            props: {
              gutter: 10,
            },
            child: [textPushDetailed],
          },
          text_data_detailed: [
            {
              textData: ["Detailed Report"],
            },
          ],
        };
        downloadData.conf.push(text_conf_detailed);
        downloadData.data.push(text_data_detailed);
        let tableIndexMap = {
          thing_name: "thing_name",
          device_name: "device_name",
          engine_sr_no: "engine_sr_no",
          genset_sr_no: "genset_sr_no",
          kva: "kva",
          model: "model",
          territory: "territory",
          customer_name: "customer_name",
          device_status: "device_status",
          lifetime_runhour: "lifetime_runhour",
          last_data_rcvd: "last_data_rcvd",
          dg_state: "dg_state",
          fault_status: "fault_status",
          fault_names: "fault_names",
        };
        // let finalFilteredColumn = [];
        // downloadedTableHeader.map(function (row) {
        //   if (
        //     config_object["table"][tableIndexMap[row.dataIndex]] ||
        //     !tableIndexMap[row.dataIndex]
        //   ) {
        //     finalFilteredColumn.push(row);
        //   }
        // });
        // downloadedTableHeader = finalFilteredColumn;
        function getLastUpdatedTime(last_data_received_time) {
          return last_data_received_time
            ? ctx
                .moment(last_data_received_time * 1000)
                .format("DD MMM YYYY, HH:mm")
            : "-";
        }
        function deviceListForThings(deviceArray) {
          let deviceNameArr = [];
          if (deviceArray && deviceArray.length) {
            deviceArray.map((devices) => {
              deviceNameArr.push(devices.qr_code);
            });
          }
          return deviceNameArr?.join(", ");
        }
        function territoryTreeFunc(territories) {
          let tree = [];
          territories.forEach((item) => {
            let level = tree;
            Object.keys(item)
              .sort()
              .forEach((key) => {
                let found = level.find((el) => el.value === item[key].id);
                if (!found) {
                  let newLevel = {
                    value: item[key].id,
                    title: item[key].name,
                    children: [],
                  };
                  level.push(newLevel);
                  level = newLevel.children;
                } else {
                  level = found.children;
                }
              });
          });
          return tree;
        }
        const findTerritory = (territory_id) => {
          let currentTerritory = ctx["_"].find(territoryData, {
            id: territory_id,
          });
          let territories = {},
            territoryIds = [],
            treeTeritories = {};
          let maxDepth = 0;
          let tempTerritory = currentTerritory;
          while (tempTerritory && tempTerritory.parent_id !== 0) {
            maxDepth++;
            tempTerritory = ctx["_"].find(territoryData, {
              id: tempTerritory.parent_id,
            });
          }
          while (currentTerritory && currentTerritory.parent_id !== 0) {
            let count = maxDepth - Object.keys(territories).length;
            territories[`territory_l${count}`] = currentTerritory.name;
            treeTeritories[`territory_l${count}`] = {
              id: currentTerritory.id,
              name: currentTerritory.name,
            };
            territoryIds.push(currentTerritory.id);
            currentTerritory = ctx["_"].find(territoryData, {
              id: currentTerritory.parent_id,
            });
          }
          return { territories, territoryIds, treeTeritories };
        };
        let MiTableData = [],
          totalTerritories = [];
        finalThings.forEach((item, ind) => {
          let findCustomers = ctx["_"].find(customers, {
            id: item.customer_id,
          });

          let findCalculatedLifetimeRunhour = "";
          let findRnHr = ctx["_"].find(item.parameters, {
            key: "rnhr",
          });
          let findCalcRnhr = ctx["_"].find(item.parameters, {
            key: "calculated_runhour",
          });
          if (
            findRnHr &&
            findRnHr.value &&
            findRnHr.value !== "" &&
            parseFloat(findRnHr.value) > 0
          ) {
            findCalculatedLifetimeRunhour = parseFloat(findRnHr.value) * 3600;
          } else if (findCalcRnhr?.aggregated_value?.lifetime?.sum) {
            findCalculatedLifetimeRunhour =
              findCalcRnhr.aggregated_value.lifetime.sum;
          }
          let lifetimehour = Math.floor(findCalculatedLifetimeRunhour / 3600);
          let lifetimeMin = Math.floor(
            (findCalculatedLifetimeRunhour % 3600) / 60,
          );
          let lifeTimeRunHourValue =
            (lifetimehour < 10 ? "0" + lifetimehour : lifetimehour) +
            " : " +
            (lifetimeMin < 10 ? "0" + lifetimeMin : lifetimeMin);
          let paramDetails = ctx["_"].filter(item.parameters, {
            type: "fault",
          });
          let activeFault = [];
          if (Array.isArray(paramDetails) && paramDetails.length) {
            paramDetails.map((paramDetails) => {
              let findFaultParam = ctx["_"].find(item.parameters, {
                key: paramDetails.key,
              });
              if (parseInt(findFaultParam.value) === 1) {
                activeFault.push(paramDetails.name);
              }
            });
          }
          const activityHasStopped = [18, 67, 76, 73, 74].includes(
            item.category,
          );
          const activityStatus = ctx["_"].find(item.parameters, {
            key: "mc_st",
          })?.value;
          const gensetStat = ctx["_"].find(item.parameters, {
            key: "au_m_stat",
          })?.value;
          const isFirstDataReceived = item.first_data_time > 0;
          const deviceStatus =
            item.devices?.[0]?.online_status === 1 ? "online" : "offline";
          let isDeviceOfflineSince = "-";
          if (deviceStatus === "offline") {
            const isDeviceLastOnlineTime = item.devices?.[0]?.last_online_time;
            if (isDeviceLastOnlineTime > 0) {
              let offlineSince = ctx
                .moment()
                .diff(ctx.moment.unix(isDeviceLastOnlineTime), "hours");
              if (offlineSince > 24) {
                let days = Math.floor(offlineSince / 24);
                let remainingHours = offlineSince % 24;
                isDeviceOfflineSince = `${days} days, ${remainingHours} hours`;
              } else {
                isDeviceOfflineSince = `${offlineSince} hours`;
              }
            }
          }
          const finalFaultType = faultType[item.id]?.length
            ? `(${faultType[item.id] === "not_defined" ? "Not defined" : faultType[item.id] === "both" ? "Both (trip & warning)" : faultType[item.id] === "trip" ? "Trip" : faultType[item.id] === "warning" ? "Warning" : ""})`
            : "";
          MiTableData.push({
            thing_name: item.name,
            device_name: deviceListForThings(item.devices),
            engine_sr_no: item?.thing_details?.engine_sl_no || "-",
            genset_sr_no: item?.thing_details?.genset_sl_no || "-",
            kva: item?.thing_details?.kva || "-",
            model: item?.thing_details?.model || "-",
            customer_name: findCustomers?.name,
            lifetime_runhour: lifeTimeRunHourValue,
            asset_added_at: ctx.moment
              .unix(item.added_at)
              .format("DD MMM YYYY, HH:mm"),
            asset_added_by: item.added_by || "-",
            commissioning_date: item?.thing_details?.commissioning_date
              ? ctx.moment
                  .unix(
                    parseInt(
                      ctx
                        .moment(item.thing_details.commissioning_date)
                        .format("X"),
                    ),
                  )
                  .format("DD MMM YYYY, HH:mm")
              : "-",
            onboard_status: isFirstDataReceived ? "Success" : "Pending",
            onboarding_date: isFirstDataReceived
              ? ctx.moment
                  .unix(item.first_data_time)
                  .format("DD MMM YYYY, HH:mm")
              : "-",
            device_status: deviceStatus || "-",
            offline_since: isDeviceOfflineSince,
            activity_status:
              isFirstDataReceived && deviceStatus === "online"
                ? activityHasStopped
                  ? parseInt(activityStatus) === 1
                    ? "Running"
                    : "Stopped"
                  : item.status === "offline"
                    ? "DisConnected"
                    : "-"
                : "-",
            genset_state:
              gensetStat === "0" ? "Manual" : gensetStat === "1" ? "Auto" : "-",
            fault_status:
              activeFault.length > 0
                ? `Active Faults ${finalFaultType}`
                : "No Fault",
            fault_names: activeFault.join(", "),
            vendor_id: item.vendor_id,
          });
          const getTerritoryCustomer =
            findCustomers?.vendor_id !== client_id
              ? ctx["_"].find(customers, {
                  id: findCustomers?.vendor_id,
                })
              : ctx["_"].find(customers, {
                  id: item.customer_id,
                });
          const findActualTerritory = findTerritory(
            getTerritoryCustomer?.territory_id,
          );
          const territory = findActualTerritory.territories;
          const territoryIdsArr = findActualTerritory.territoryIds;
          const treeTeritories = findActualTerritory.treeTeritories;
          totalTerritories.push(findActualTerritory.treeTeritories);
          MiTableData[ind] = {
            ...MiTableData[ind],
            ...territory,
            territoryIdsArr,
            ...findActualTerritory.treeTeritories.id,
          };
          let territoryStr = "";
          if (Object.values(treeTeritories).length) {
            for (
              let i = 0;
              i <= Object.values(treeTeritories).length - 1;
              i++
            ) {
              console.log("treeTeritories", treeTeritories);
              if (i !== Object.values(treeTeritories).length - 1) {
                territoryStr += Object.values(treeTeritories)[i].name + " -> ";
              } else {
                territoryStr += Object.values(treeTeritories)[i].name;
              }
            }
          }
          MiTableData[ind]["territory"] = territoryStr;
        });

        const onlineThingsArr = ctx["_"].filter(summaryThings, (thing) => {
          return thing.devices?.[0]?.online_status === 1;
        });
        const offlineThingsArr = ctx["_"].filter(summaryThings, (thing) => {
          return thing.devices?.[0]?.online_status !== 1;
        });
        const assetsWithDevices = ctx["_"].filter(summaryThings, (thing) => {
          return thing.devices?.length;
        });
        const assetsWithoutDevices = ctx["_"].filter(summaryThings, (thing) => {
          return thing.devices?.length === 0;
        });
        const onboardedThings = ctx["_"].filter(summaryThings, (thing) => {
          return thing.first_data_time > 0;
        });
        const pendingThings = ctx["_"].filter(summaryThings, (thing) => {
          return !thing.first_data_time > 0;
        });
        summaryData = [
          {
            parameter: input.translations.total_assets,
            // parameter: "Total Assets",
            value: summaryThings?.length || 0,
          },
          {
            parameter: input.translations.online_assets,
            // parameter: "Online Assets",
            value: onlineThingsArr?.length || 0,
          },
          {
            parameter: input.translations.offline_assets,
            // parameter: "Offline Assets",
            value: offlineThingsArr?.length || 0,
          },
        ];
        if (application_id === 16) {
          if (assetsWithDevices?.length > 0) {
            summaryData.push({
              parameter: "Assets With Devices",
              value: assetsWithDevices?.length || 0,
            });
          }
          if (assetsWithoutDevices?.length > 0) {
            summaryData.push({
              parameter: "Assets Without Devices",
              value: assetsWithoutDevices?.length || 0,
            });
          }
        } else {
          summaryData.push(
            {
              parameter: input.translations.onboarded_successfully,
              // parameter: "Onboarded Successfully",
              value: onboardedThings?.length || 0,
            },
            {
              parameter: input.translations.onboard_pending,
              // parameter: "Onboard Pending",
              value: pendingThings?.length || 0,
            },
          );
        }
        downloadData.conf.push({
          pdf_force_new_page: false,
          props: {
            gutter: 10,
            style: {},
            className: "tableRow",
          },
          child: [
            {
              compo: "Table",
              widget: "",
              classname: "tab-1",
              table_new_page: true,
              props: {
                columns: [
                  {
                    title: "Summary",
                    dataIndex: "parameter",
                  },
                  {
                    title: " ",
                    dataIndex: "value",
                  },
                ],
                headerFont: 13,
                size: "small",
                tabRadius: 0,
                horizontalScroll: true,
                shadow: false,
                breakPoint: 1000,
                breakPoint2: 500,
                largeTable: true,
                mediumTable: false,
                smallTable: false,
              },
              col_props: {
                span: 24,
              },
              pdf_width: 50,
              pdf_table_break: {
                col_no: 7,
                row_no: 15,
              },
            },
          ],
        });
        downloadData.data.push([summaryData]);

        downloadData.conf.push({
          props: {
            gutter: 10,
            style: {},
          },
          child: [
            {
              compo: "Table",
              widget: "",
              classname: "tab-1",
              table_new_page: true,
              props: {
                columns: downloadedTableHeader,
                headerFont: 13,
                size: "small",
                tabRadius: 0,
                horizontalScroll: true,
                shadow: false,
                breakPoint: 1000,
                breakPoint2: 500,
                largeTable: true,
                mediumTable: false,
                smallTable: false,
              },
              col_props: {
                span: 24,
              },
              pdf_width: 50,
              pdf_table_break: {
                col_no: 5,
                row_no: 20,
              },
            },
          ],
        });
        downloadData.data.push([MiTableData]);
        downloadData.file_name = "DG Status Report";
        cb({
          downloadData,
          MiTableData,
          summaryData,
        });
      },
      cb: (value) => {
        this.setState({
          data_source: value.MiTableData,
          loading: false,
          pageLoading: false,
          tableOnlyLoading: false,
          summaryData: value.summaryData,
        });
      },
    });
  }

  customDrawerCallback(data) {
    const { dg_type, fromTime, uptoTime, onboarding_status, territoryId } =
        data,
      { things_clone } = this.state;
    let things = things_clone;
    if (dg_type && dg_type !== "all") {
      things = things.filter((thing) => {
        return Array.isArray(thing.tags) && thing.tags.includes(dg_type);
      });
    }
    if (fromTime && uptoTime) {
      things = things.filter((thing) => {
        return thing.added_at >= fromTime && thing.added_at <= uptoTime;
      });
    }
    if (onboarding_status) {
      if (onboarding_status === "success") {
        things = things.filter((thing) => {
          return thing.first_data_time > 0;
        });
      } else if (onboarding_status === "pending") {
        things = things.filter((thing) => {
          return !thing.first_data_time > 0;
        });
      } else {
        things = things;
      }
    }
    this.setState(
      {
        things,
        territoryId,
        onboarding_status,
        dg_type,
        fromTime,
        uptoTime,
        selectedPageNo: 1,
      },
      async () => {
        await this.allThingsApi();
      },
    );
  }

  onExportReady() {
    let { autoDownloadFormat } = this.props;
    if (autoDownloadFormat) {
      this.downloadModalCallback(autoDownloadFormat);
    }
  }

  getDownloadRender() {
    const { fileFormat } = this.state;
    let downloadRender = this.state.downloadData ? (
      <div
        style={{
          opacity: 0,
          visibility: "hidden",
          overflow: "hidden",
          "max-height": 0,
        }}
      >
        <ReportController
          is_white_label={this.props.is_white_label}
          onExportReady={this.onExportReady}
          key={moment().unix()}
          ref={this.invisibleReportRef}
          {...this.state.downloadData}
          graph_download_height={100}
          graph_download_width={160}
          parameters={[1, 2, 3, 4, 5, 6, 7, 8]}
        />
      </div>
    ) : (
      ""
    );
    this.setState(
      {
        download_render: downloadRender,
        loading: false,
      },
      () => {
        if (this.state.isDownloadInUrl) {
          // if (typeof window.getInputDataSet === 'function') {
          setTimeout(() => {
            this.downloadModalCallback("xlsx");
            if (typeof window.reportGenerationCompleted === "function") {
              window.reportGenerationCompleted("DG Status Report.xlsx");
            }
          }, 2000);
          // }
        } else {
          setTimeout(() => {
            this.downloadModalCallback(fileFormat);
          }, 1000);
        }
      },
    );
  }

  downloadModalCallback(fileFormat) {
    if (this.state.downloadData) {
      if (fileFormat.includes("csv")) {
        this.invisibleReportRef.current.exportCSV(null, {});
      }

      if (fileFormat.includes("xlsx")) {
        this.invisibleReportRef.current.exportXLSX(null, {
          maxCellMergeCount: 20,
        });
      }

      if (fileFormat.includes("pdf")) {
        this.invisibleReportRef.current.exportPDF({
          header: {
            left: {
              text: this.props.is_white_label
                ? this.props.application_name
                : "DATOMS for " + this.props.application_name,
              fontType: "italics",
            },
            right: {
              text: this.props.client_name,
            },
          },
        });
      }
    }
  }
  downloadBtnClicked() {
    this.downloadModalRef.current.showModal();
  }
  modalDownloadBtnClicked(e) {
    this.setState({ download_loading: true, fileFormat: e }, async () => {
      await this.filesDownload();
    });
  }
  customizeBtnClicked() {
    this.customizeDrawerRef.current.showDrawer();
  }
  goBackPage() {
    if (this.props.dg_in_iot_mode) {
      this.props.history.push(
        `/${this.props.host_app_name}/dg-monitoring/reports`,
      );
    } else {
      this.props.history.push(`/${this.props.app_name}/reports`);
    }
  }
  territoryChanged(e) {
    this.setState(
      {
        territoryId: e,
        selectedPageNo: 1,
      },
      async () => {
        await this.allThingsApi();
      },
    );
  }
  withOrWithoutDevice(e) {
    this.setState(
      {
        deviceFilterVal: e,
        selectedPageNo: 1,
      },
      async () => {
        await this.allThingsApi();
      },
    );
  }
  categorySelection(e) {
    this.setState(
      {
        selectedCat: e,
        selectedPageNo: 1,
      },
      async () => {
        await this.allThingsApi();
      },
    );
  }
  partnerSelect(e) {
    const vendors = [];
    if (e.length) {
      for (let i = 0; i < e.length; i++) {
        const value = e[i];
        vendors.push({
          ...value,
          label:
            value.label?.length > 10
              ? value.label.slice(0, 10) + "..."
              : e.label,
        });
      }
    }
    this.setState(
      {
        vendors,
        selectedPageNo: 1,
      },
      async () => {
        await this.allThingsApi();
      },
    );
  }

  onPageChange(e) {
    this.setState(
      {
        selectedPageNo: e,
      },
      async () => await this.thingsListApi(true),
    );
  }
  onPageSizeChange(current, size) {
    this.setState(
      {
        selectedPageNo: 1,
        selectedPageSize: size,
      },
      async () => await this.thingsListApi(true),
    );
  }
  onSearch() {}
  render() {
    const {
      totalThings,
      onlineThings,
      offlineThings,
      onboardedThings,
      pendingThings,
      loading,
      data_source,
      reportGeneratedeText,
      territoriesTree,
      territoryId,
      onboarding_status,
      partnersArr,
      vendors,
      deviceFilterVal,
      assetsWithDevices,
      assetsWithoutDevices,
      allCats,
      selectedCat,
      pageLoading,
      thingsApiData,
      selectedPageNo,
      tableOnlyLoading,
      summaryData,
    } = this.state;
    const { application_id } = this.props;
    let pageRender = "";
    if (loading) {
      pageRender = <Loading />;
    } else {
      let summaryRender = [];
      if (summaryData?.length) {
        summaryData.forEach((sd) => {
          summaryRender.push(
            <div style={{ display: "flex" }}>
              <div className="data">
                <div>{sd?.value}</div>
                <span>{sd?.parameter}</span>
              </div>
              ,
              <AntDivider type="vertical" />
            </div>,
          );
        });
      }
      // let summaryData = (
      //   <div className="summary">
      //     <div className="header">Summary</div>
      //     <div className="total-max-data">
      //       <div className="values">
      //         <div className="data">
      //           <div>{totalThings}</div>
      //           <span>Total Assets</span>
      //         </div>
      //         <AntDivider type="vertical" />
      //         <div className="data">
      //           <div>{onlineThings}</div>
      //           <span>Online Assets</span>
      //         </div>
      //         <AntDivider type="vertical" />
      //         <div className="data">
      //           <div>{offlineThings}</div>
      //           <span>Offline Assets</span>
      //         </div>
      //         <AntDivider type="vertical" />
      //         {application_id === 16 ? (
      //           <>
      //             {assetsWithDevices > 0 ? (
      //               <div className="data">
      //                 <div>{assetsWithDevices}</div>
      //                 <span>Assets With Devices</span>
      //               </div>
      //             ) : (
      //               ""
      //             )}
      //             {assetsWithDevices > 0 ? <AntDivider type="vertical" /> : ""}
      //             {assetsWithoutDevices > 0 ? (
      //               <div className="data">
      //                 <div>{assetsWithoutDevices}</div>
      //                 <span>Assets Without Devices</span>
      //               </div>
      //             ) : (
      //               ""
      //             )}
      //           </>
      //         ) : (
      //           <>
      //             <div className="data">
      //               <div>{onboardedThings}</div>
      //               <span>Onboarded Successfully</span>
      //             </div>
      //             <AntDivider type="vertical" />
      //             <div className="data">
      //               <div>{pendingThings}</div>
      //               <span>Onboard Pending</span>
      //             </div>
      //           </>
      //         )}
      //       </div>
      //     </div>
      //   </div>
      // );
      let detailedData = (
        <div className="detailed-data">
          <div className="header">
            {this.props.t? this.props.t('detailed_data'): "Detailed data"}
            {/* Detailed data */}
          </div>
          <CustomizeTable
            {...this.props}
            preferenceKeys={["dg_status_report", "table"]}
            defaultFixedCount={this.isMobile() ? 0 : 2}
            tableProps={{
              columns: this.tableHeader().viewData,
              dataSource: data_source,
              shadow: false,
              isGrouped: false,
              loading: tableOnlyLoading,
              sticky: true,
              scroll: { x: "max-content" },
              pagination: {
                position: ["topRight"],
                defaultPageSize: 100,
                defaultCurrent: selectedPageNo,
                total: thingsApiData.total_counts,
                onChange: (e) => this.onPageChange(e),
                showQuickJumper: true,
                onShowSizeChange: (current, size) => {
                  this.onPageSizeChange(current, size);
                },
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} of ${thingsApiData.total_counts} items`,
              },
            }}
          />
        </div>
      );
      const showPartners = application_id === 16 && partnersArr?.length > 1;
      pageRender = (
        <>
          <ReportsHeader
            t={this.props.t}
            goBackPage={this.goBackPage}
            downloadBtnClicked={() => this.downloadBtnClicked()}
            customizeBtnClicked={() => this.customizeBtnClicked()}
            report_type_title={this.props.t? this.props.t("DG Status Report"): "DG Status Report"}
            date_selection={false}
            territoryId={territoryId}
            download_loading={this.state.download_loading}
            territoriesTree={territoriesTree}
            territoryChanged={(e) => this.territoryChanged(e)}
            disabledDownload={
              this.props.application_id === 16 &&
              this.props.logged_in_user_client_id === this.props.client_id &&
              !this.props.getViewAccess(["Reports:Download"])
            }
          />
          <div className="report-generation">
            {reportGeneratedeText}
            {application_id !== 16 ? (
              <AntTreeSelect
                t={this.props.t}
                filterData={{
                  tree_data: territoriesTree,
                  tree_value: territoryId,
                  placeholder: this.props.t? this.props.t('please_select_a_territory'): "Please select a territory",
                  // placeholder: "Please select a territory",
                }}
                treeNodeFilterProp="title"
                showSearch
                style={{ width: "100%" }}
                dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
                allowClear
                onChange={(e) => this.territoryChanged(e)}
              />
            ) : (
              ""
            )}
            {showPartners ? (
              <AntSelect
                labelInValue
                style={{ width: 320, "margin-left": 20 }}
                options={partnersArr}
                value={vendors}
                showSearch
                onSearch={() => this.onSearch()}
                maxTagCount={2}
                placeholder="Select a partner"
                mode="multiple"
                onChange={(e) => this.partnerSelect(e)}
              />
            ) : (
              ""
            )}
            <AntSelect
              style={{ width: 200, "margin-left": showPartners ? 10 : 20 }}
              options={[
                { value: "all", label: this.props.t? this.props.t("all"): "All" },
                { value: "withDevice", label: this.props.t? this.props.t("assets_with_devices"): "Assets with devices" },
                { value: "withoutDevice", label: this.props.t? this.props.t("assets_without_devices"): "Assets without devices" },
              ]}
              value={deviceFilterVal}
              onChange={(e) => this.withOrWithoutDevice(e)}
            />
            <AntSelect
              style={{ width: 200, "margin-left": showPartners ? 10 : 20 }}
              options={allCats}
              value={selectedCat}
              onChange={(e) => this.categorySelection(e)}
            />
          </div>
          {pageLoading ? (
            <SkeletonLoader style={{ "margin-top": 20 }} />
          ) : (
            <>
              {/* {summaryData} */}
              <div className="summary">
                <div className="header">
                  {this.props.t? this.props.t('summary'): "Summary"}
                  {/* Summary */}
                </div>
                <div className="total-max-data">
                  <div className="values">{summaryRender}</div>
                </div>
              </div>
              {detailedData}
            </>
          )}

          <DownloadModal
            t={this.props.t}
            ref={this.downloadModalRef}
            callback={(e) => this.downloadModalCallback(e)}
            label={this.props.t? this.props.t('dg_status'): "DG Status"}
            // label="DG Status"
            modalDownloadBtnClicked={(e) => this.modalDownloadBtnClicked(e)}
          />
          <CustomizeModal
            ref={this.customizeDrawerRef}
            callback={(e) => this.customDrawerCallback(e)}
            all_things_list={this.state.things}
            onboard_status={this.state.onboarding_status}
            territoriesTree={territoriesTree}
            territoryId={territoryId}
            goBackPage={() => this.goBackPage()}
            {...this.props}
          />
          {this.state.download_render}
        </>
      );
    }
    return <div className={"iot_status_report"}>{pageRender}</div>;
  }
}

export default DgStatusReport;
