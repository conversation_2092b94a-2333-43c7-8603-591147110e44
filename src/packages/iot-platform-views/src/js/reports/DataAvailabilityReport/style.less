#data_avilability_reports{
    overflow: hidden auto;
    .da-avl-rprt-hdr{
        display: flex;
        gap: 20px;
        justify-content: space-between;
        padding: 20px 20px 10px;
        .da-avl-rprt-hdr-btns{
            padding-top: 4px;
            display: flex;
            gap: 16px;
        }
    }
    .da-avl-rprt-table{
        padding: 10px 15px 20px;
        .ant-pagination {
            margin-top: 0;
        }
        .ant-table-sticky-header {
            overflow: hidden !important;
        }
        .ant-table-tbody > tr > td {
            padding: 0 !important;
            .dar-tcell{
                padding: 10px 14px;
                font-weight: 500;
                &.dar-colored-text{
                    color: #9e5337!important;
                    font-size: 14px;
                }
                &.bg_FF0000{
                    background-color: #FF0000;
                }
                &.bg_FFC000{
                    background-color: #FFC000;
                }
                &.bg_92D050{
                    background-color: #92D050;
                }
                &.dar-device-cell{
                    padding: 0;
                    display: flex;
                    flex-direction: column;
                    > div{
                        flex: 1;
                        border-bottom: 1px solid #e8e8e880;
                        padding: 5px 14px;
                        &:last-child{
                            border-bottom: none;
                        }
                    }
                }
            }
        }
    }
}