// import { siteResponse } from '../data/list-data';
import { retriveThingsList, retrieveSitesList } from '@datoms/js-sdk';

export async function getSitesData() {
	let response = await retrieveSitesList(this.props.client_id, `?page_no=${1}&results_per_page=${1000}`);
	// let response = siteResponse;
	if (response.status === 'success') {
		let sitesData = response.data.map((site) => {
			return {
				id: site.id,
				name: site.name,
			};
		});
		this.setState({ sitesData });
	}
}

export async function getThingsData() {
	const { client_id, application_id } = this.props;
	let response = await retriveThingsList({ client_id, application_id });
	if (response.status === 'success') {
		let thingsData = response.things.map((thing) => {
			return {
				id: thing.id,
				name: thing.name,
				territory_id: thing.territory_id,
			};
		});
		this.setState({ thingsData });
	}
}
