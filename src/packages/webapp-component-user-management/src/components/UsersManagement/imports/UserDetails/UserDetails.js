import React, {Component} from 'react';
import './style.less'
import Arrow<PERSON><PERSON>tOutlined from "@ant-design/icons/ArrowLeftOutlined";
import SettingOutlined from "@ant-design/icons/SettingOutlined";
import DeleteOutlined from "@ant-design/icons/DeleteOutlined";
import MoreOutlined from '@ant-design/icons/MoreOutlined';
import RedoOutlined from '@ant-design/icons/RedoOutlined';
import CheckCircleOutlined from '@ant-design/icons/CheckCircleOutlined';
import EditOutlined from '@ant-design/icons/EditOutlined';
import AntTag from '@datoms/react-components/src/components/AntTag'
import Loading from '@datoms/react-components/src/components/Loading'
import EmailVerificationStatus from '../EmailVerificationStatus';
import deactivateUserImage from '../../../../imgs/deactivate-user-white.svg';
import activateUserImage from '../../../../imgs/activate-user-white.svg';
class UserDetails extends Component {
    constructor(props) {
        super(props);
        this.state={
            user:null
        }
        this.getApplicationName=this.getApplicationName.bind(this)
        this.getRoleName=this.getRoleName.bind(this)
        this.goBackToList=this.goBackToList.bind(this)
        this.getTerritoryName=this.getTerritoryName.bind(this)
    }

    componentDidUpdate(prevProps, prevState, snapshot) {
        if (Array.isArray(this.props.dataUser) && prevProps.dataUser !== this.props.dataUser){
            let {user_id}=this.props
            let selectedUser=this.props.dataUser.find(item=>item.id == user_id)
            this.setState({
                user:selectedUser
            })
        }
    }

    componentDidMount() {
        if (Array.isArray(this.props.dataUser)){
            let {user_id}=this.props
            let selectedUser=this.props.dataUser.find(item=>item.id == user_id)
            this.setState({
                user:selectedUser
            })
        }
    }

    goBackToList(){
        if (this.props.closeUserDraw){
            this.props.closeUserDraw()
        }
        return;
        if (
            this.props.location.pathname.search(
                '/customer-management'
            ) > -1
        ) {
            this.props.history.push(
                this.platform_slug +
                '/customer-management/' +
                this.state.client_id +
                '/applications/' +
                this.props.app_drop +
                '/users/view'
            );
        } else {
            this.props.history.push(
                this.platform_slug + this.user_slug + '/users/view'
            );
        }
    }

    getRoleName(){
        let role=""
        let {all_roles_list}=this.props
        let role_details=this.state.user?.role_details
        if (Array.isArray(role_details) && role_details.length){
            role=all_roles_list.find(item=>item.id==role_details[0].role_id)?.name
        }
        return role;
    }
    getApplicationName(id){
        let role=""
        let {all_app_list}=this.props
        role = all_app_list.find(item=>item.id==id)?.name
        return role;
    }
    getTerritoryName(id){
        let role=""
        let {territoryData}=this.props
        if (Array.isArray(territoryData) && territoryData.length){
            role = territoryData.find(item=>item.id==id)?.name
        }
        return role;
    }
    render() {
        return (
            <>
                {
                    this.state.user ?(
                        <div className={"um_ud_main_cont"} id={this.props.isDesktop?"um_ud_main_dt_cont":""}>
                            <div className="users-quick-add-header">
                                <span className="title">
						<span className={'thing-name'}>
                            <span
                                className="icon-outer-cont"
                                onClick={this.goBackToList}
                            >
						<ArrowLeftOutlined
                            style={{ color: '#232323', fontSize: 21 }}
                        />

					</span>
							{this.state?.user?.name}
						</span>

					</span>
                                <div className="actions-cont">
						<span
                            className="icon-outer-cont"
                            onClick={()=>{
                                if (this.props.editUser){
                                    this.props.editUser(this.state.user)
                                }
                            }}
                        >
							<EditOutlined style={{ color: '#FFFFFF' }} />
						</span>
                                    <span
                                        className="icon-outer-cont"
                                        onClick={()=>{
                                            if (this.props.showStatusConfirm){
                                                this.props.showStatusConfirm(this.state.user)
                                            }
                                        }}
                                    >
                            <img src={this.state.user?.status==1?deactivateUserImage:activateUserImage} alt="De-Activate User"/>
						</span>
                                    <span
                                        className="icon-outer-cont"
                                        onClick={()=>{
                                            if (this.props.showDeleteConfirm){
                                                this.props.showDeleteConfirm(this.state.user)
                                            }
                                        }}
                                        // onClick={this.showDeleteConfirmation}
                                    >
							<DeleteOutlined style={{ color: '#FFFFFF' }} />
						</span>
                                </div>
                            </div>
                            <div className="tm_td_mb_detail">
                                {
                                    this.props.isDesktop?(
                                        <div className="each_detail">
                                            <span>Name</span>
                                            <div>
                                                {this.state?.user?.name}

                                            </div>
                                        </div>
                                    ):""
                                }
                                <div className="each_detail">
                                    <span>Email</span>
                                    <EmailVerificationStatus
                                        t={this.props.t}
                                        email={this.state.user?.email_id}
                                        isVerified={this.state.user?.email_verified}
                                        showResend={this.state.user?.email_verification_resend_allowed}
                                        deliveryStatus={this.state.user?.email_verification_delivery_status}
                                        failureReason={this.state.user?.email_verification_failure_reason}
                                        onResendClick={this.props.resendLink}
                                        resendLoading={
                                            this.props.resend_loading?.[this.state.user?.id]?.['email']
                                        }
                                        userData={this.state.user || {}}
                                    />
                                    {/* <div className={"email_cont"}>
                                        {this.state.user?.email_id}
                                        <div className="resend-invitation">
                                        {
                                            this.state.user.email_verified ? (
                                                <>
                                                    <CheckCircleOutlined className="resend-icon green" />
                                                    <div className="resend-sec-text">
                                                        {' '}Verified
                                                    </div>
                                                </>
                                            ):(
                                                <>
                                                    <>
                                                        <RedoOutlined className="resend-icon" />{' '}
                                                        <div className="resend-sec-text" onClick={()=>{
                                                            if (this.props.resendLink){
                                                                this.props.resendLink(this.state.user,'email')
                                                            }
                                                        }}>
                                                            Resend Verification
                                                        </div>
                                                    </>
                                                </>
                                            )

                                        }
                                        </div>

                                    </div> */}
                                </div>
                                <div className="each_detail">
                                    <span>Phone</span>
                                    <div className={"email_cont"}>{this.state.user && this.state.user.mobile_no && this.state.user.mobile_no.length ? this.state.user.mobile_no[0]:""}
                                        <div className="resend-invitation">
                                            {
                                                this.state.user.mobile_verified ? (
                                                    <>
                                                        <CheckCircleOutlined className="resend-icon green" />
                                                        <div className="resend-sec-text">
                                                            {' '}Verified
                                                        </div>
                                                    </>
                                                ):(
                                                    <>
                                                        <RedoOutlined className="resend-icon" />{' '}
                                                        <div className="resend-sec-text" onClick={()=>{
                                                            if (this.props.resendLink){
                                                                this.props.resendLink(this.state.user,'mobile')
                                                            }
                                                        }}>
                                                            Resend Verification
                                                        </div>
                                                    </>
                                                )

                                            }
                                        </div>
                                    </div>
                                </div>
                                <div className="each_detail">
                                    <span>Role</span>
                                    <div>{this.getRoleName()}</div>
                                </div>
                                <div className="each_detail">
                                    <span>Last Login</span>
                                    <div>{this.state.user?.login}</div>
                                </div>
                                {
                                    this.state.user && this.state.user.desgination && this.state.user.desgination!="" ?(
                                        <div className="each_detail">
                                            <span>Designation</span>
                                            <div>{this.state.user?.designation}</div>
                                        </div>
                                    ):""
                                }
                                <div className="each_detail">
                                    <span>Applications</span>
                                    <div>{this.state.user && this.state.user.applications &&
                                        this.state.user.applications.map(applicationId=>
                                            <AntTag
                                                color={''}
                                            >
                                                {this.getApplicationName(applicationId)}
                                            </AntTag>
                                        )
                                    }</div>
                                </div>
                                {
                                    this.state.user.territory_ids && Array.isArray(this.state.user.territory_ids) && this.state.user.territory_ids.length ? (
                                        <div className="each_detail">
                                            <span>Territories</span>
                                            <div>{this.state.user.territory_ids &&
                                                this.state.user.territory_ids.map(applicationId=>
                                                    <AntTag
                                                        color={''}
                                                    >
                                                        {this.getTerritoryName(applicationId)}
                                                    </AntTag>
                                                )
                                            }</div>
                                        </div>
                                    ):""
                                }

                            </div>
                        </div>
                    ):<Loading/>
                }

            </>
        );
    }
}

export default UserDetails;