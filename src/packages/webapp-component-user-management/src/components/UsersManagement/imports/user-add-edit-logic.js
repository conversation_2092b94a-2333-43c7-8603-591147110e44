import React from "react";
import { retrieveSitesList, retrieveSiteDetails } from "@datoms/js-sdk";
import AntTag from "@datoms/react-components/src/components/AntTag";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";

export async function fetchSitesList() {
  if (!this.showSiteField)
    return { sitesList: [], siteNames: {}, siteWiseAssets: {} };

  if (this.state.sitesList?.length) {
    return {
      siteNames: this.state.siteNames,
      sitesList: this.state.sitesList,
      siteWiseAssets: this.state.siteWiseAssets,
    };
  }

  const query = `?show_things=true&page_no=${1}&results_per_page=${1000}`;

  const response = await retrieveSitesList(this.state.client_id, query);

  const siteNames = {};
  const siteWiseAssets = {};
  const sitesList = [{ value: "*", label: "All" }];
  if (response?.status === "success" && response.data) {
    response.data.forEach((site) => {
      siteNames[site.id] = site.name;
      sitesList.push({ value: site.id, label: site.name });
      siteWiseAssets[site.id] = site.things;
    });
  }
  return { sitesList, siteNames, siteWiseAssets };
}

export function onSiteChange(value = [], isEditFill = false) {
  if (value === "*") {
    value = ["*"];
  }
  const latestValue = value[value.length - 1];
  const stateToUpdate = {};
  if (latestValue === "*") {
    stateToUpdate.selectedSites = ["*"];
  } else {
    stateToUpdate.selectedSites = value.filter((val) => val !== "*");
  }
  this.setState(stateToUpdate, async () => {
    if (isEditFill) {
      this.filterTerritoryCustomers();
    } else {
      this.setFilteredSiteAndAssets();
    }
  });
}

export function getFinalSelectedThings(
  selectedThings = [],
  siteAssets = [],
  territory_things = [],
) {
  if (selectedThings[0] === "*") return ["*"];
  console.log("territory_things", territory_things);
  return [...new Set([...selectedThings, ...siteAssets, ...territory_things])];
}

export function getFinalSelectedSites(
  selectedSites = [],
  territory_sites = [],
) {
  if (selectedSites[0] === "*") return ["*"];
  return [...new Set([...selectedSites, ...territory_sites])];
}

export function getSiteNames(siteIds, siteNames = {}, territoryIds = []) {
  console.log("siteIds", siteIds, siteNames);
  if (siteIds === "*") return <AntTag>All</AntTag>;

  const resultArray = [];
  const finalSiteIds = siteIds || [];
  // console.log("territoryId", territoryId, this.state.territoryWiseSites)
  // if (this.state.territoryWiseSites?.[territoryId]?.length) {
  //   finalSiteIds.push(...this.state.territoryWiseSites[territoryId]);
  // }
  if (territoryIds === "*") {
    Object.keys(this.state.territoryWiseSites || {}).forEach((territoryId) => {
      if (this.state.territoryWiseSites?.[territoryId]?.length) {
        finalSiteIds.push(...this.state.territoryWiseSites[territoryId]);
      }
    });
  } else if (territoryIds.length) {
    territoryIds.forEach((territoryId) => {
      if (this.state.territoryWiseSites?.[territoryId]?.length) {
        finalSiteIds.push(...this.state.territoryWiseSites[territoryId]);
      }
    });
  }
  if (Array.isArray(finalSiteIds)) {
    let excessSites = "";
    let excessSitesCount = 0;
    [...new Set(finalSiteIds)].map((site) => {
      console.log("siteIndex", site, siteNames[site]);
      if (siteNames[site] && resultArray.length < 4) {
        resultArray.push(<AntTag>{siteNames[site]}</AntTag>);
      } else if (siteNames[site]) {
        excessSites += `${excessSitesCount > 0 ? ", " : ""}${siteNames[site]}`;
        excessSitesCount++;
      }
    });
    if (excessSitesCount > 0) {
      resultArray.push(
        <AntTooltip title={excessSites}>
          <AntTag>{`+ ${excessSitesCount} more`}</AntTag>
        </AntTooltip>,
      );
    }
  }

  console.log("resultArray", resultArray);
  return resultArray;
}

export function setFilteredSiteAndAssets() {
  const { siteWiseAssets } = this.props;
  const {
    selectedSites = [],
    selected_things_arr,
    application_details_arr,
    territory_sites,
    territory_things = [],
  } = this.state;

  const finalSelectedSites =
    // selectedSites[0] !== "*" &&
    // selectedSites.length &&
    // Array.isArray(territory_sites) &&
    // territory_sites.length
    //   ? [...new Set([...territory_sites, ...selectedSites])]
    //   :
    selectedSites;

  let selectedThings = selected_things_arr;
  let appDetailsArrayCopy, findApp;

  // if in customer management, take thing_ids from application details
  if (
    this.props.location.pathname.includes("/customer-management") &&
    application_details_arr.length
  ) {
    console.log("appDetailsArrayCopy", application_details_arr, selectedThings);
    appDetailsArrayCopy = JSON.parse(JSON.stringify(application_details_arr));
    findApp = appDetailsArrayCopy.find((app) => app.application_id === 16);
    selectedThings =
      findApp?.thing_ids === "*"
        ? ["*"]
        : Array.isArray(findApp?.thing_ids)
          ? findApp.thing_ids
          : [];
  }

  const siteAssets = [];
  const siteIdsArray =
    finalSelectedSites[0] === "*"
      ? Object.keys(siteWiseAssets)
      : finalSelectedSites;
  siteIdsArray.forEach((siteId) => {
    if (siteWiseAssets[siteId]) {
      siteAssets.push(...siteWiseAssets[siteId]);
    }
  });

  const stateToUpdate = {
    siteAssets,
    selectedSites: territory_sites?.length
      ? finalSelectedSites.filter((site) => !territory_sites.includes(site))
      : finalSelectedSites,
  };

  // if all assets are selected, no need to process site details
  if (selectedThings[0] !== "*") {
    if (!this.props.location.pathname.includes("/customer-management")) {
      stateToUpdate.selected_things_arr = selectedThings.filter(
        (thing_id) =>
          !siteAssets.includes(thing_id) &&
          !territory_things.includes(thing_id),
      );
    }

    if (appDetailsArrayCopy && findApp) {
      findApp.thing_ids = selectedThings.filter(
        (thing_id) =>
          !siteAssets.includes(thing_id) &&
          !territory_things.includes(thing_id),
      );
      stateToUpdate.application_details_arr = appDetailsArrayCopy;
    }
  }

  this.setState(stateToUpdate);
}

export function getTerritoryResources(
  territoryId,
  resourceNames,
  territoryArray,
) {
  const resourceIds = {};

  // Find the element with the given id
  const element = territoryArray.find(
    (item) => item.id === parseInt(territoryId),
  );

  for (let resourceName of resourceNames) {
    if (!resourceIds[resourceName]) {
      resourceIds[resourceName] = [];
    }
    if (
      element &&
      Array.isArray(element[resourceName]) &&
      element[resourceName].length
    ) {
      // Add the element's resources to the result
      resourceIds[resourceName].push(...element[resourceName]);
    }
  }

  if (element && Array.isArray(element.children) && element.children.length) {
    // Recursively get the customer_ids of the children
    for (let childId of element.children) {
      const childResourceIds = getTerritoryResources(
        childId,
        resourceNames,
        territoryArray,
      );
      for (let resourceName of resourceNames) {
        if (childResourceIds[resourceName]) {
          resourceIds[resourceName].push(...childResourceIds[resourceName]);
        }
      }
    }
  }

  return resourceIds;
}
