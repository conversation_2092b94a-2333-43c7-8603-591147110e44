import React from 'react';
import AntList from '@datoms/react-components/src/components/AntList';
import AntListItem from '@datoms/react-components/src/components/AntListItem';
import AntTooltip from '@datoms/react-components/src/components/AntTooltip';
import AntDropdown from '@datoms/react-components/src/components/AntDropdown';
import AntMenu from '@datoms/react-components/src/components/AntMenu';
import AntMenuItem from '@datoms/react-components/src/components/AntMenuItem';
import AntSpin from '@datoms/react-components/src/components/AntSpin';
import AntTag from '@datoms/react-components/src/components/AntTag';
import _find from 'lodash/find';
import MoreOutlined from '@ant-design/icons/MoreOutlined';
import RedoOutlined from '@ant-design/icons/RedoOutlined';
import DeleteOutlined from '@ant-design/icons/DeleteOutlined';
import LockOutlined from '@ant-design/icons/LockOutlined';
import CheckCircleOutlined from '@ant-design/icons/CheckCircleOutlined';
import CloseCircleOutlined from '@ant-design/icons/CloseCircleOutlined';
import EditOutlined from '@ant-design/icons/EditOutlined';
import EmailVerificationStatus from '../EmailVerificationStatus';
import deactivateUserImage from '../../../../imgs/deactivate-user.svg';
import activateUserImage from '../../../../imgs/activate-user.svg';
import './style.less';

export default class MobileUserList extends React.Component {
	constructor(props) {
		super(props);
		this.state = {};
	}

	doNothing() {}

	render() {
		return (
			<div id="mobile_user_list">
				<AntList
					itemLayout="vertical"
					size="small"
					pagination={{
						position: 'top',
						size: 'small',
						pageSize: 20,
						showTotal: (total, range) =>
							`Showing ${range[0]}-${range[1]} of ${total} items`,
					}}
					dataSource={this.props.userListData}
					renderItem={(user) => {
						let statusIcon = (
								<img
									src={activateUserImage}
									alt="logo"
									className="logo-img"
								/>
							),
							statusText = 'Activate';

						if (user.status == 1) {
							statusText = 'Deactivate';
							statusIcon = (
								<img
									src={deactivateUserImage}
									alt="logo"
									className="logo-img"
								/>
							);
						}

						let actionMenu = (
							<AntDropdown
								disabled={
									this.props.getViewAccess([
										'UserManagement:Deactivate',
									], true) ||
									this.props.getViewAccess([
										'UserManagement:Edit',
									], true) ||
									this.props.getViewAccess([
										'UserManagement:Delete',
									], true) ||
									this.props.accessToVendorManager()
										? false
										: true
								}
								overlayClassName="users-actions-overlay"
								overlay={(() => {
									if (
										this.props.getViewAccess([
											'UserManagement:Deactivate',
										], true) ||
										this.props.getViewAccess([
											'UserManagement:Edit',
										], true) ||
										this.props.getViewAccess([
											'UserManagement:Delete',
										], true) ||
										this.props.accessToVendorManager()
									) {
										return (
											<AntMenu>
												<AntMenuItem
													key="action-1"
													onClick={() =>
														this.props.editUser(
															user
														)
													}
												>
													<div className="resend-invitation-butn">
														<EditOutlined className="resend-icon" />{' '}
														<div className="resend-sec-text">
															Edit
														</div>
													</div>
												</AntMenuItem>
												<AntMenuItem
													key="action-2"
													onClick={() =>
														this.props.showStatusConfirm(
															user
														)
													}
												>
													<div className="resend-invitation-butn">
														{statusIcon}{' '}
														<div className="resend-sec-text">
															{statusText}
														</div>
													</div>
												</AntMenuItem>
												<AntMenuItem
													key="action-3"
													onClick={() =>
														this.props.showDeleteConfirm(
															user
														)
													}
												>
													<div className="resend-invitation-butn">
														<DeleteOutlined className="resend-icon" />{' '}
														<div className="resend-sec-text">
															Delete
														</div>
													</div>
												</AntMenuItem>
											</AntMenu>
										);
									} else {
										return (
											<AntMenu>
												<AntMenuItem
													className="block"
													key="action-1"
												>
													<div className="resend-invitation-butn">
														<EditOutlined className="resend-icon" />{' '}
														<div className="resend-sec-text">
															Edit
														</div>
													</div>
												</AntMenuItem>
												<AntMenuItem
													className="block"
													key="action-2"
												>
													<div className="resend-invitation-butn">
														{statusIcon}{' '}
														<div className="resend-sec-text">
															{statusText}
														</div>
													</div>
												</AntMenuItem>
												<AntMenuItem
													className="block"
													key="action-3"
												>
													<div className="resend-invitation-butn">
														<DeleteOutlined className="resend-icon" />{' '}
														<div className="resend-sec-text">
															Delete
														</div>
													</div>
												</AntMenuItem>
											</AntMenu>
										);
									}
								})()}
								trigger={['click']}
								placement="bottomLeft"
							>
								{/*<DotsVertical className="menu-drop"/>*/}
								{(() => {
									if (
										this.props.getViewAccess([
											'UserManagement:Deactivate',
										], true) ||
										this.props.getViewAccess([
											'UserManagement:Edit',
										], true) ||
										this.props.getViewAccess([
											'UserManagement:Delete',
										], true) ||
										this.props.accessToVendorManager()
									) {
										return (
											<MoreOutlined className="menu-drop" />
										);
									} else {
										return (
											<AntTooltip title="No access">
												{' '}
												<MoreOutlined className="menu-drop disabled" />{' '}
											</AntTooltip>
										);
									}
								})()}
							</AntDropdown>
						);

						let mob_no = '',
							email = '';
						if (user.mobile_no && user.mobile_no.length) {
							mob_no = user.mobile_no[0];
						}
						if (user.email_id && user.email_id !== '') {
							email = user.email_id;
						}
						// if (user.email_id != '' && mob_no != '') {
						// 	email = user.email_id + ', ' + mob_no;
						// } else if (user.email_id != '' && mob_no == '') {
						// 	email = user.email_id;
						// } else if (user.email_id == '' && mob_no != '') {
						// 	email = mob_no;
						// }

						let resendEmail = '',
							resendMobile = '';

						if (
							this.props.resend_loading &&
							this.props.resend_loading[user.id] &&
							this.props.resend_loading[user.id]['mobile']
						) {
							resendMobile = (
								<div className="resend-invitation-butn load">
									<AntSpin size="small" />
								</div>
							);
						} else {
							if (user.mobile_verified) {
								resendMobile = (
									<div className="resend-invitation">
										<CheckCircleOutlined className="resend-icon green" />{' '}
										<div className="resend-sec-text">
											Verified
										</div>
									</div>
								);
							} else {
								if (
									user.mobile_verified ||
									user.email_verified
								) {
									resendMobile = (
										<div className="resend-invitation">
											<CloseCircleOutlined className="resend-icon red" />{' '}
											<div className="resend-sec-text">
												Unverified
											</div>
										</div>
									);
								} else {
									resendMobile = (
										<div
											className="resend-invitation-butn"
											onClick={() =>
												this.props.resendLink(
													user,
													'mobile'
												)
											}
										>
											<RedoOutlined className="resend-icon" />{' '}
											<div className="resend-sec-text">
												Resend Verification
											</div>
										</div>
									);
								}
							}
						}

						if (
							this.props.resend_loading &&
							this.props.resend_loading[user.id] &&
							this.props.resend_loading[user.id]['email']
						) {
							resendEmail = (
								<div className="resend-invitation-butn load">
									<AntSpin size="small" />
								</div>
							);
						} else {
							if (user.email_verified) {
								resendEmail = (
									<div className="resend-invitation">
										<CheckCircleOutlined className="resend-icon green" />{' '}
										<div className="resend-sec-text">
											Verified
										</div>
									</div>
								);
							} else {
								if (
									user.mobile_verified ||
									user.email_verified
								) {
									resendEmail = (
										<div className="resend-invitation">
											<CloseCircleOutlined className="resend-icon red" />{' '}
											<div className="resend-sec-text">
												Unverified
											</div>
										</div>
									);
								} else {
									resendEmail = (
										<div
											className="resend-invitation-butn"
											onClick={() =>
												this.props.resendLink(
													user,
													'email'
												)
											}
										>
											<RedoOutlined className="resend-icon" />{' '}
											<div className="resend-sec-text">
												Resend Verification
											</div>
										</div>
									);
								}
							}
						}

						let comma = ',';

						if (mob_no == '') {
							resendMobile = '';
							comma = '';
						}

						if (email == '') {
							resendEmail = '';
							comma = '';
						}

						return (
							<div>
								<AntListItem key={user.id}>
									<div className="total-list-container">
										<div className="user-list-details">
											<div>
												{(() => {
													if (user.status == 1) {
														return (
															<div
																className="dspl-inline-flx"
																onClick={() => {
																	if (
																		this
																			.props
																			.viewUser
																	) {
																		this.props.viewUser(
																			user.id
																		);
																	}
																}}
																style={{
																	alignItems:
																		'center',
																	gap: 12
																}}
															>
																<span className="font-orange">
																	{user.name}
																</span>
																{user.mfa_enabled ? (
																	<AntTooltip
																		title="MFA"
																		onClick={(
																			e
																		) =>
																			e?.stopPropagation?.()
																		}
																	>
																		<LockOutlined
																			style={{
																				color: 'green',
																			}}
																		/>
																	</AntTooltip>
																) : null}
																<div className="role-name">
																	{(() => {
																		// console.log('app_role_', this.props.all_role_datas);
																		// console.log('app_role_ user', user);
																		let app_role =
																			[];
																		if (
																			user.role_details &&
																			user
																				.role_details
																				.length &&
																			this
																				.props
																				.all_role_datas
																		) {
																			user.role_details.map(
																				(
																					role
																				) => {
																					let role_arr =
																						_find(
																							this
																								.props
																								.all_role_datas
																								.roles_list,
																							{
																								id: role.role_id,
																							}
																						);
																					if (
																						role_arr
																					) {
																						app_role.push(
																							{
																								role_name:
																									role_arr.name,
																							}
																						);
																					}
																				}
																			);
																		}

																		if (
																			app_role.length
																		) {
																			return app_role.map(
																				(
																					details
																				) => {
																					return (
																						<div className="text-center">
																							<span>
																								(
																							</span>
																							{
																								details.role_name
																							}
																							<span>
																								)
																							</span>
																						</div>
																					);
																				}
																			);
																		}
																	})()}
																</div>
															</div>
														);
													} else {
														return (
															<AntTooltip title="Inactive">
																<div className="dspl-inline-flx">
																	{user.name}
																	<AntTag
																		className="deactive-tag"
																		color="red"
																	>
																		Inactive
																	</AntTag>
																	<div className="role-name">
																		{(() => {
																			console.log(
																				'app_role_',
																				this
																					.props
																					.all_role_datas
																			);
																			console.log(
																				'app_role_ user',
																				user
																			);
																			let app_role =
																				[];
																			if (
																				user.role_details &&
																				user
																					.role_details
																					.length &&
																				this
																					.props
																					.all_role_datas
																			) {
																				user.role_details.map(
																					(
																						role
																					) => {
																						let role_arr =
																							_find(
																								this
																									.props
																									.all_role_datas
																									.roles_list,
																								{
																									id: role.role_id,
																								}
																							);
																						if (
																							role_arr
																						) {
																							app_role.push(
																								{
																									role_name:
																										role_arr.name,
																								}
																							);
																						}
																					}
																				);
																			}

																			if (
																				app_role.length
																			) {
																				return app_role.map(
																					(
																						details
																					) => {
																						return (
																							<div className="text-center">
																								<span>
																									(
																								</span>
																								{
																									details.role_name
																								}
																								<span>
																									)
																								</span>
																							</div>
																						);
																					}
																				);
																			}
																		})()}
																	</div>
																</div>
															</AntTooltip>
														);
													}
												})()}
												<div>
														<EmailVerificationStatus
															t={this.props.t}
															email={user.email_id}
															isVerified={user.email_verified}
															showResend={user.email_verification_resend_allowed}
															deliveryStatus={user.email_verification_delivery_status}
															failureReason={user.email_verification_failure_reason}
															onResendClick={this.props.resendLink}
															resendLoading={
																this.props.resend_loading?.[user.id]?.['email']
															}
															userData={user}
														/>
														{/* {user.login_allowed ? (
															<span className="mar-rt-10">
																{resendEmail}
															</span>
														) : (
															''
														)} */}
													<div>
														{mob_no}
														{user.login_allowed ? (
															<span className="mar-rt-10">
																{resendMobile}
															</span>
														) : (
															''
														)}
													</div>
												</div>
											</div>
										</div>
										<div className="action-icon">
											{actionMenu}
										</div>
									</div>
								</AntListItem>
								{/*<div
									className="add-user"
									onClick={() => this.props.addNewUser()}
								>
									<PlusOutlined />
								</div>*/}
								{/* <AddPopover
									toggleAdd={() => this.props.addNewUser()}
								/> */}
							</div>
						);
					}}
				/>
			</div>
		);
	}
}
