import React from "react";
import AntSpin from "@datoms/react-components/src/components/AntSpin";
import RedoOutlined from "@ant-design/icons/RedoOutlined";
import CheckCircleOutlined from "@ant-design/icons/CheckCircleOutlined";
import successIcon from "./icons/success.svg";
import failedIcon from "./icons/failure.svg";
import processingIcon from "./icons/processing.svg";
import temporaryIcon from "./icons/temporary.svg";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import "./style.less";

interface EmailVerificationStatusProps {
  t: (key: string) => string;
  email: string;
  isVerified: boolean;
  showResend: boolean | number;
  resendLoading: boolean;
  onResendClick: (userData: any, type: string) => void;
  failureReason?: string;
  deliveryStatus: number;
  userData: any;
}

const EmailVerificationStatus: React.FC<EmailVerificationStatusProps> = ({
  t,
  email,
  isVerified,
  showResend,
  resendLoading,
  onResendClick,
  failureReason,
  deliveryStatus,
  userData,
}) => {
  const getStatusIcon = () => {
    if (deliveryStatus === 1) {
      return (
        <AntTooltip title="Invitation sent successfully">
          <img src={successIcon} alt="Success" />
        </AntTooltip>
      );
    } else if (deliveryStatus === 0) {
      return (
        <AntTooltip title="Invitation in progress">
          <img src={processingIcon} alt="Processing" />
        </AntTooltip>
      );
    } else if (showResend) {
      return (
        <AntTooltip
          title={
            <div>
              <span>Temporary email send failure</span>
              {failureReason && (
                <p style={{ fontSize: 11 }}>
                  <span style={{ color: "#c7c7c7" }}>Reason:</span>{" "}
                  {failureReason}
                </p>
              )}
            </div>
          }
        >
          <img src={temporaryIcon} alt="Failed" />
        </AntTooltip>
      );
    } else {
      return (
        <AntTooltip
          title={
            <div>
              <span>Failed to send invitation</span>
              {failureReason && (
                <p style={{ fontSize: 11 }}>
                  <span style={{ color: "#c7c7c7" }}>Reason:</span>{" "}
                  {failureReason}
                </p>
              )}
            </div>
          }
        >
          <img src={failedIcon} alt="Failed" />
        </AntTooltip>
      );
    }
  };

  const getResendButton = () => {
    if (resendLoading) {
      return <AntSpin size="small" />;
    }
    console.log("resendLoading -> ", userData);
    if (showResend) {
      return (
        <div
          className="evs-resend-btn"
          onClick={() => onResendClick(userData, "email")}
        >
          <AntTooltip title="Email">
            <RedoOutlined className="resend-icon" />{" "}
            <span>
              {typeof t === "function"
                ? t("resend_verification")
                : "Resend Verification"}
            </span>
          </AntTooltip>
        </div>
      );
    }
    return null;
  };

  if (isVerified) {
    return (
      <div className="email-verification-status">
        <span>{email}</span>
        <div className="evs-verified">
          <AntTooltip title="Email">
            <CheckCircleOutlined style={{ color: "green" }} />{" "}
            <span>{typeof t === "function" ? t("verified") : "Verified"}</span>
          </AntTooltip>
        </div>
      </div>
    );
  }

  return (
    <div className="email-verification-status">
      <span>{email}</span>
      {getStatusIcon()}
      {getResendButton()}
    </div>
  );
};

export default EmailVerificationStatus;
