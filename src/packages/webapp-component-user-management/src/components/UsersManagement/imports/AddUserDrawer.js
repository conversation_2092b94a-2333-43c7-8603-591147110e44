import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import AntButton from '@datoms/react-components/src/components/AntButton';
import Loading from '@datoms/react-components/src/components/Loading';
import InputPhone from '@datoms/react-components/src/components/InputPhone';
import AntDrawer from '@datoms/react-components/src/components/AntDrawer';
import AntRow from '@datoms/react-components/src/components/AntRow';
import AntCol from '@datoms/react-components/src/components/AntCol';
import AntAlert from '@datoms/react-components/src/components/AntAlert';
import AntInput from '@datoms/react-components/src/components/AntInput';
import AntNotification from '@datoms/react-components/src/components/AntNotification';
import AntMessage from '@datoms/react-components/src/components/AntMessage';
import AntSelect from '@datoms/react-components/src/components/AntSelect';
import AntOption from '@datoms/react-components/src/components/AntOption';
import AntTreeSelect from '@datoms/react-components/src/components/AntTreeSelect';
import OkCloseFooter from '@datoms/react-components/src/components/OkCloseFooter';
import AntTooltip from '@datoms/react-components/src/components/AntTooltip';

import CheckCircleOutlined from '@ant-design/icons/CheckCircleOutlined';
import CloseCircleOutlined from '@ant-design/icons/CloseCircleOutlined';
import { TreeSelect } from 'antd';
import _find from 'lodash/find';
import _findIndex from 'lodash/findIndex';
import _isEmpty from 'lodash/isEmpty';
import _uniqBy from 'lodash/uniqBy';
import { addUser, editUser, sendAuthOtp, verifyAccount } from '@datoms/js-sdk';
import { emailValidation } from '@datoms/js-utils/src/EmailValidation';
import {
	// fetchSiteDetails,
	fetchSitesList,
	getFinalSelectedSites,
	getFinalSelectedThings,
	onSiteChange,
	setFilteredSiteAndAssets,
} from './user-add-edit-logic';

const AddUserDrawer = Form.create()(
	class AddUserDrawer extends React.Component {
		constructor(props) {
			super(props);
			this.state = {
				add_user_form: this.props.add_user_form,
				contact_id: [],
				show_add_btn: true,
				selected_user_first_name: '',
				selected_user_last_name: '',
				selected_user_phone: '',
				selected_user_email: '',
				selected_user_designation: '',
				all_selected_app: false,
				all_selected_thing: false,
				application_id_arr: props.location.pathname.includes(
					'/customer-management/'
				)
					? []
					: [props.application_id],
				butn_load: false,
				customer_list: [],
				territory_customers: [],
				territory_ids: [],
				applicaiton_wise_role_check: {},
				employee_id: '',
				tracking_id: '',
			};
			this.onSiteChange = onSiteChange.bind(this);
			this.fetchSitesList = fetchSitesList.bind(this);
			this.setFilteredSiteAndAssets = setFilteredSiteAndAssets.bind(this);
			// this.fetchSiteDetails = fetchSiteDetails.bind(this);
		}

		componentDidMount() {
			if (!this.props.show_add_draw && this.props.selected_user_data) {
				this.fillEditForm(this.props.selected_user_data);
			}

			if (this.props.show_add_draw) {
				this.addNewForm();
			}
		}

		componentDidUpdate(prevProps) {
			// console.log('prevprops', prevProps);
			if (
				prevProps.show_add_draw != this.props.show_add_draw &&
				this.props.selected_user_data
			) {
				this.fillEditForm(this.props.selected_user_data);
			}
		}

		/**
		 * This function closes the slider and and sets the values to defaults.
		 */
		onUserDrawerClose() {
			// this.props.history.push(this.props.platform_slug + '/users/' + this.props.history.location.search);
			let add_user_form = this.state.add_user_form;
			add_user_form.customer_details_form[0].initial_value = undefined;
			add_user_form.customer_details_form[1].initial_value = undefined;
			add_user_form.customer_details_form[2].initial_value = undefined;
			add_user_form.customer_details_form[3].initial_value = undefined;
			add_user_form.customer_details_form[4].initial_value = undefined;
			// console.log('closeAddEditModal');
			this.setState(
				{
					user_id: '',
					add_user: false,
					edit_user: false,
					set_status_user: false,
					add_user_form: add_user_form,
					selected_user_id: null,
					selected_user_first_name: undefined,
					selected_user_last_name: undefined,
					selected_user_email: undefined,
					selected_user_phone: undefined,
					selected_user_status: null,
					selected_user_designation: '',
					contact_id: null,
					application_id_arr: this.state.selected_application_id_arr,
					role_id: null,
					selected_user_group: [],
					unauthorised_access: false,
					unauthorised_access_msg: '',
					show_add_form: false,
					block_add_form: false,
					application_details_arr: [],
					application_access_arr: [],
					customer_list: [],
					customer_set_list: [],
					all_selected_app: false,
					all_selected_thing: false,
					show_add_btn: true,
					all_selected_industry: false,
					all_selected_set: false,
					super_admin: false,
					enable_email: false,
					enable_mobile: false,
					show_mobile_otp: false,
					show_email_otp: false,
				},
				() => {
					this.props.form.resetFields();
					this.props.onClose();
				}
			);
		}

		/*
		 * This function fills the edit form with existing data.
		 * @param  {Object} user user data.
		 */
		fillEditForm(user) {
			console.log('table_1', user);
			// this.props.history.push(this.platform_slug + '/users/' + user.id + '/edit/' + this.props.history.location.search);
			let app_id_arr = [],
				application_access_arr = user.applications,
				customer_list = user.industries,
				super_admin = false,
				things_list = [];
			if (this.props.location.pathname.includes('/customer-management')) {
				if (user.all_role_details && user.all_role_details.length) {
					user.all_role_details.map((role) => {
						if (role.application_id) {
							app_id_arr.push(role.application_id);
						}
					});
				}
			} else {
				if (user.role_details && user.role_details.length) {
					user.role_details.map((role) => {
						if (role.application_id) {
							app_id_arr.push(role.application_id);
						}
					});
				}
			}

			if (
				(this.props.location.pathname.includes('/datoms-x') ||
					this.props.location.pathname.includes('/iot-platform')) &&
				!this.props.location.pathname.includes('/customer-management')
			) {
				if (user.all_role_details && user.all_role_details.length) {
					if (
						this.props.all_roles_list &&
						this.props.all_roles_list.length
					) {
						let found = _find(this.props.all_roles_list, {
							id: parseInt(user.all_role_details[0].role_id),
						});
						// console.log('found1_', found);
						if (found) {
							if (
								found.access == '*' &&
								found.is_deletable == false
							) {
								super_admin = true;
								customer_list = ['*'];
								if (
									this.props.all_app_list &&
									this.props.all_app_list.length
								) {
									application_access_arr = [];
									this.props.all_app_list.map((apps) => {
										application_access_arr.push(apps.id);
									});
								}
							}
						}
					}
				}
			}

			let selected_role = _find(this.props.all_roles_list, {
				id: user.role_details[0].role_id,
			});
			if (
				selected_role &&
				selected_role.role_type &&
				selected_role.role_type == 1
			) {
				super_admin = true;
			}

			let disabled = false;
			// console.log('thing_list_obj_', this.props.thing_list_obj);
			// console.log('thing_list_obj_ 1', user.things);
			// console.log('thing_list_obj_ 2', Array.isArray(user.things));
			if (user.things == '*') {
				disabled = true;
				things_list = user.things;
			}

			if (
				user.things &&
				Array.isArray(user.things) &&
				user.things.length &&
				this.props.things_list &&
				this.props.location.pathname.includes('/user-management')
			) {
				this.props.things_list.map((thingDet) => {
					if (user.things.includes(thingDet.id)) {
						things_list.push(thingDet.id);
					}
				});
			}

			let add_user_form = this.state.add_user_form;
			add_user_form.customer_details_form[0].initial_value =
				user.first_name + ' ' + user.last_name;
			add_user_form.customer_details_form[1].initial_value = '';
			add_user_form.customer_details_form[2].initial_value =
				user.designation;
			add_user_form.customer_details_form[3].initial_value =
				user.email_id;
			add_user_form.customer_details_form[4].initial_value =
				user.mobile_no[0];

			let validateStatus = '';
			if (user.email_id && emailValidation(user.email_id)) {
				validateStatus = 'success';
			} else {
				validateStatus = 'error';
			}
			console.log('validateStatus_2', validateStatus);

			// console.log('app_id_arr', app_id_arr);
			this.setState(
				{
					validate_email_status: validateStatus,
					show_add_draw: false,
					show_add_form: true,
					drawCreateVisible: true,
					selected_user_id: user.id,
					add_user_form: add_user_form,
					all_selected_thing: disabled,
					selected_user_first_name:
						user.first_name + ' ' + user.last_name, //user.first_name,
					selected_user_last_name: '', //user.last_name,
					selected_user_email: user.email_id,
					selected_user_email_backup: user.email_id,
					selected_user_phone: user.mobile_no[0],
					selected_user_phone_backup: user.mobile_no[0],
					selected_things_arr: things_list,
					selected_user_designation: user.designation,
					application_id_arr: app_id_arr,
					role_edit_details: user.all_role_details,
					role_id: this.props.location.pathname.includes(
						'/user-management'
					)
						? user.role_details[0].role_id
						: user.all_role_details[0].role_id,
					application_details_arr:
						this.props.location.pathname.includes(
							'/user-management'
						)
							? JSON.parse(JSON.stringify(user.role_details))
							: JSON.parse(JSON.stringify(user.all_role_details)),
					application_access_arr: application_access_arr,
					customer_list: customer_list,
					customer_set_list: user.industry_sets,
					all_selected_industry:
						user.industries && user.industries.includes('*')
							? true
							: false,
					all_selected_set:
						user.industry_sets && user.industry_sets.includes('*')
							? true
							: false,
					super_admin: super_admin,
					territory_ids: this.isTerritoryAdmin()
						? []
						: this.hasAllTerritory(user),
					employee_id: user.employee_id,
					tracking_id: user.tracking_id,
				},
				() => {
					console.log('checkingg_1', this.hasAllTerritory(user));
					console.log(
						'checkingg_',
						this.state.selected_user_phone_backup
					);
					console.log(
						'checkingg_ selected_user_phone',
						this.state.selected_user_phone
					);
					console.log('checkingg_ mobile_no', user.mobile_no[0]);
					this.isAdmin();
					if (
						this.props.location.pathname.includes(
							'/customer-management'
						)
					) {
						user.all_role_details.map((role_details) => {
							this.role_select(
								role_details.role_id,
								role_details.application_id,
								false,
								true
							);
						});
					}
					
					if(user.sites?.length) {
						this.onSiteChange(user.sites, this.props.isTerritoryEnabled);
					} else if (this.props.isTerritoryEnabled) {
						this.filterTerritoryCustomers();
					}
					// console.log('selected_things_arr_', this.state.super_admin);
				}
			);
		}

		addNewForm() {
			let roleId = '',
				appArr = [],
				super_admin = false;
			if (
				false /*this.props.user_table_data && this.props.user_table_data.length == 0 && this.props.all_roles_list && 	this.props.all_roles_list.length && ((this.props.vendor_type && !this.props.vendor_type.includes(4)) || ((this.props.customer_type && !this.props.customer_type.includes(5))))*/
			) {
				this.props.all_roles_list.map((role) => {
					if (role.role_type == 1) {
						roleId = role.id;
						super_admin = true;
					}
				});

				if (this.props.location.pathname.includes('/user-management')) {
					appArr.push({
						application_id: this.props.application_id,
						role_id: roleId,
						reports_to: 0,
					});
				}
			}

			console.log('role_idd_', roleId);
			console.log('role_idd_ all_roles_list', this.props.all_roles_list);
			console.log(
				'role_idd_ user_table_data',
				this.props.user_table_data
			);
			console.log('role_idd_ appArr', appArr);
			this.props.form.resetFields();
			this.setState(
				{
					show_add_form: true,
					block_add_form: false,
					show_add_btn: false,
					selected_user_first_name: '',
					selected_user_last_name: '',
					selected_user_email: '',
					selected_user_phone: '',
					selected_user_designation: '',
					application_id_arr: this.props.location.pathname.includes(
						'/customer-management'
					)
						? []
						: this.state.application_id_arr,
					application_access_arr: [],
					customer_list: [],
					customer_set_list: [],
					all_selected_industry: false,
					all_selected_set: false,
					role_id: roleId,
					application_details_arr: appArr,
					super_admin: super_admin,
				},
				() => {
					this.isAdmin();
				}
			);
		}

		contact_select(cont_id, index) {
			// console.log('contact_select', cont_id);
			let that = this;
			let block = false;
			if (cont_id) {
				block = true;
			}

			this.setState(
				{
					contact_id: [cont_id],
					show_add_form: block,
					block_add_form: block,
					show_add_btn: !block,
					application_id_arr: this.props.location.pathname.includes(
						'/customer-management'
					)
						? []
						: this.state.application_id_arr,
				},
				() => {
					// console.log('contact_id_', this.state.contac_id);
					this.fillCustomerForm(cont_id);
				}
			);
		}

		fillCustomerForm(contact_id) {
			if (
				this.props.all_contact_details &&
				this.props.all_contact_details.length
			) {
				let contact_details = _find(this.props.all_contact_details, {
					contact_id: parseInt(contact_id),
				});
				let add_user_form = this.state.add_user_form;
				if (contact_details) {
					add_user_form.customer_details_form[0].initial_value =
						contact_details.first_name;
					add_user_form.customer_details_form[1].initial_value =
						contact_details.last_name;
					add_user_form.customer_details_form[2].initial_value =
						contact_details.designation;
					add_user_form.customer_details_form[3].initial_value =
						contact_details.email;
					add_user_form.customer_details_form[4].initial_value =
						contact_details.mobile_no[0];

					let validateStatus = '';
					if (
						contact_details.email &&
						emailValidation(contact_details.email)
					) {
						validateStatus = 'success';
					} else {
						validateStatus = 'error';
					}
					console.log('validateStatus_1', validateStatus);

					this.setState({
						validate_email_status: validateStatus,
						selected_user_first_name: contact_details.first_name,
						selected_user_last_name: contact_details.last_name,
						selected_user_email: contact_details.email,
						selected_user_phone: contact_details.mobile_no,
						selected_user_designation: contact_details.designation,
						add_user_form: add_user_form,
					});
				} else {
					add_user_form.customer_details_form[0].initial_value = '';
					add_user_form.customer_details_form[1].initial_value = '';
					add_user_form.customer_details_form[2].initial_value = '';
					add_user_form.customer_details_form[3].initial_value = '';
					add_user_form.customer_details_form[4].initial_value = '';
					this.setState({
						selected_user_first_name: '',
						selected_user_last_name: '',
						selected_user_email: '',
						selected_user_phone: '',
						selected_user_designation: '',
						add_user_form: add_user_form,
					});
				}
			}
		}

		openNotification(type, msg) {
			if (window.innerWidth < 576) {
				AntMessage(type, msg);
			} else {
				AntNotification({
					type: type,
					message: msg,
					// description: 'This is success notification',
					placement: 'bottomLeft',
					className: 'alert-' + type,
				});
			}
		}

		handleChange(e, key) {
			let add_user_form = this.state.add_user_form;

			console.log(
				'checkingg_ handleChangegg__',
				this.state.selected_user_phone_backup
			);
			console.log('checkingg_ handleChangegg__ e', e);

			if (key == 'first_name') {
				add_user_form.customer_details_form[0].initial_value =
					e.target.value;
				this.setState({
					selected_user_first_name: e.target.value,
					add_user_form: add_user_form,
				});
			} else if (key == 'last_name') {
				add_user_form.customer_details_form[1].initial_value =
					e.target.value;
				this.setState({
					selected_user_last_name: e.target.value,
					add_user_form: add_user_form,
				});
			} else if (key == 'email') {
				add_user_form.customer_details_form[3].initial_value = e;
				this.setState({
					selected_user_email: e,
					add_user_form: add_user_form,
					enable_email:
						e !== this.state.selected_user_email_backup &&
						this.props.location.pathname.includes('/edit') &&
						parseInt(this.props.logged_in_user_id) ===
							parseInt(this.props.match.params.user_id)
							? true
							: false,
				});
			} else if (key == 'phone_no') {
				add_user_form.customer_details_form[4].initial_value = e;
				let curValue = '';
				if (e !== '') {
					curValue = '+' + e;
				}
				console.log('checkingg_ curValue', curValue);
				console.log(
					'checkingg_ logged_in_user_id',
					this.props.logged_in_user_id
				);
				console.log(
					'checkingg_ user_id',
					this.props.match.params.user_id
				);
				this.setState({
					selected_user_phone: e,
					add_user_form: add_user_form,
					enable_mobile:
						curValue &&
						curValue !== this.state.selected_user_phone_backup &&
						this.props.location.pathname.includes('/edit') &&
						parseInt(this.props.logged_in_user_id) ===
							parseInt(this.props.match.params.user_id)
							? true
							: false,
				});
			} else if (key == 'designation') {
				add_user_form.customer_details_form[2].initial_value =
					e.target.value;
				this.setState({
					selected_user_designation: e.target.value,
					add_user_form: add_user_form,
				});
			}

			// console.log('add_user_form', add_user_form);
		}

		accessApplicationSelect(app_id_arr) {
			console.log('app_id_arr__', app_id_arr);
			let app_ids = app_id_arr,
				disabled = false;
			if (app_id_arr.includes('*')) {
				app_ids = ['*'];
				disabled = true;
			}
			this.setState(
				{
					application_access_arr: app_ids,
					all_selected_app: disabled,
				},
				() => {
					// console.log('all_selected_app_', this.state.all_selected_app);
				}
			);
		}

		thingsSelect(thing_id_arr = []) {
			let thing_ids = Array.isArray(thing_id_arr) ? thing_id_arr.filter(
					(item) => !this.state.siteAssets?.includes(item) && !this.state.territory_things?.includes(item)
				) : thing_id_arr,
				disabled = false;
			if (thing_id_arr && thing_id_arr.includes('*')) {
				thing_ids = ['*'];
				disabled = true;
			}
			this.setState(
				{
					selected_things_arr: thing_ids,
					all_selected_thing: disabled,
				},
				() => {
					// console.log('all_selected_thing_', this.state.all_selected_thing);
				}
			);
		}

		customerSelect(cust_id_arr) {
			let cust_ids = cust_id_arr,
				disabled = false;
			if (cust_id_arr.includes('*')) {
				cust_ids = ['*'];
				disabled = true;
			}
			this.setState({
				customer_list: cust_ids.filter(
					(item) => !this.state.territory_customers.includes(item)
				),
				all_selected_industry: disabled,
			});
		}

		submitUserForm() {
			let that = this;
			that.props.form.validateFieldsAndScroll((err, values) => {
				if (!err) {
					that.setState(
						{
							butn_load: true,
						},
						() => {
							if (that.props.show_add_draw) {
								that.handleSubmitAdd();
							} else {
								that.handleSubmitEdit();
							}
						}
					);
					// console.log('Received values of form: ', values);
				}
			});
		}

		async applicationSelect(application_id_arr) {
			let application_details_arr = [];

			this.setState(
				{
					application_id_arr: application_id_arr,
				},
				() => {
					console.log('app_id_arrr', application_id_arr);
					let role_det = {};
					if (application_id_arr.length) {
						role_det = _find(this.props.role_list, {
							application_id:
								application_id_arr[
									application_id_arr.length - 1
								],
						});
					}
					console.log('role_det__', role_det);
					let role_id = 0;
					/*if (
						role_det &&
						role_det.role_details &&
						role_det.role_details.length &&
						role_det.role_details[0]
					) {
						if (this.props.user_table_data && this.props.user_table_data.length == 0 && this.props.all_roles_list && 	this.props.all_roles_list.length) {
								this.props.all_roles_list.map((role) => {
									role_det.role_details.map((role_details) => {
										if (role.id == role_details.role_id && role.role_type == 1) {
											role_id = role.id;
										}
									});
								});
						} else {
							if (role_det.role_details) {
								role_det.role_details.map((rolee) => {
									if (this.props.all_roles_list) {
										let selRole = _find(this.props.all_roles_list, {id: rolee.role_id});
										console.log('selRole_', selRole);
										application_id_arr.map((app) => {
											console.log('selRole_ plan', this.props.plan_appwise_map[app]);
											if (selRole && this.props.plan_appwise_map && this.props.plan_appwise_map[app] && this.props.plan_appwise_map[app].pre_defined_role_type_ids && this.props.plan_appwise_map[app].pre_defined_role_type_ids.includes(selRole.role_type)) {
												console.log('selRole_ true');
												role_id = rolee.role_id;
											}
										});
									}
								});
							}
						}
						console.log('role_dett_', role_det);
						console.log('role_dett_ all_role', this.props.all_roles_list);
						console.log('role_dett_ role_id', role_id);
						this.role_select(
							role_id,
							application_id_arr[application_id_arr.length - 1]
						);
					}*/
					let roleObj = {};
					application_id_arr.map((app_id) => {
						console.log('appppp_id_', app_id);
						role_id = 0;

						console.log(
							'selRole_ application_details_arr',
							this.state.application_details_arr
						);
						if (
							this.state.application_details_arr &&
							this.state.application_details_arr.length
						) {
							this.state.application_details_arr.map(
								(app_det) => {
									if (app_det.application_id == app_id) {
										application_details_arr.push({
											app_det,
										});
									}
								}
							);
						}

						if (
							role_det &&
							role_det.role_details &&
							role_det.role_details.length &&
							role_det.role_details[0]
						) {
							console.log('role_dett_', this.props);
							if (
								false /*this.props.user_table_data && this.props.user_table_data.length == 0 && this.props.all_roles_list && 	this.props.all_roles_list.length && ((this.props.vendor_type && !this.props.vendor_type.includes(4)) || ((this.props.customer_type && !this.props.customer_type.includes(5))))*/
							) {
								this.props.all_roles_list.map((role) => {
									if (role.role_type == 1) {
										role_id = role.id;
									}
								});
							} else {
								if (role_det.role_details) {
									role_det.role_details.map((rolee) => {
										if (
											this.props.all_roles_list &&
											role_id == 0
										) {
											let selRole = _find(
												this.props.all_roles_list,
												{ id: rolee.role_id }
											);
											console.log('selRole_', selRole);
											console.log(
												'selRole_ plan',
												this.props.plan_appwise_map
											);
											if (
												selRole &&
												this.props.plan_appwise_map &&
												this.props.plan_appwise_map[
													app_id
												] &&
												this.props.plan_appwise_map[
													app_id
												].pre_defined_role_type_ids &&
												this.props.plan_appwise_map[
													app_id
												].pre_defined_role_type_ids.includes(
													selRole.role_type
												)
											) {
												console.log('selRole_ true');
												role_id = rolee.role_id;
											} else if (
												selRole &&
												(!this.props.plan_appwise_map ||
													!this.props
														.plan_appwise_map[
														app_id
													] ||
													!this.props
														.plan_appwise_map[
														app_id
													].pre_defined_role_type_ids)
											) {
												role_id = rolee.role_id;
											}
										}
									});
								}
							}

							this.role_select(
								role_id,
								application_id_arr[
									application_id_arr.length - 1
								]
							);
						}
						console.log('role_dett_ 1', role_det);
						console.log('role_dett_ 1 role_id', role_id);
					});
				}
			);
		}

		role_select(role_id, app_id, is_select = true, edit_fill = false) {
			let arr = [],
				prev_arr_new = [],
				application_access_arr = [],
				territory_ids = this.state.territory_ids,
				territory_customers = this.state.territory_customers,
				customer_list = this.state.customer_list,
				super_admin = false,
				all_selected_industry = false,
				applicaiton_wise_role_check =
					this.state.applicaiton_wise_role_check,
				selected_things_arr = this.state.selected_things_arr;
			// application_access_arr = this.state.application_access_arr;
			if (
				(this.props.location.pathname.search('/datoms-x') > -1 ||
					this.props.location.pathname.search('/iot-platform') >
						-1) &&
				this.props.location.pathname.includes('/customer-management')
			) {
				let clearThingsField = false;
				arr.push({
					application_id: app_id,
					role_id: role_id,
					reports_to: 0,
				});

				console.log('arr__', arr);
				console.log('arr__ 1', this.state.application_details_arr);
				let prev_arr = this.state.application_details_arr
					? JSON.parse(
							JSON.stringify(this.state.application_details_arr)
					  )
					: [];
				console.log('prev_arr', prev_arr);
				if (_find(prev_arr, { application_id: app_id })) {
					console.log(
						'application_details_arr',
						_findIndex(prev_arr, { application_id: app_id })
					);
					if (
						prev_arr &&
						prev_arr[
							_findIndex(prev_arr, { application_id: app_id })
						]
					) {
						let currentRoleId =
							prev_arr[
								_findIndex(prev_arr, { application_id: app_id })
							]['role_id'];
						console.log('clearThingsField0', currentRoleId);
						clearThingsField = this.isRoleAdmin(currentRoleId);
						prev_arr[
							_findIndex(prev_arr, { application_id: app_id })
						]['role_id'] = role_id;
					}
				} else {
					prev_arr = prev_arr.concat(arr);
				}

				prev_arr_new = [];
				if (
					this.state.application_id_arr &&
					this.state.application_id_arr.length
				) {
					this.state.application_id_arr.map((app_id, index) => {
						let app_dtl_arr_indx = _findIndex(prev_arr, {
							application_id: app_id,
						});
						if (app_dtl_arr_indx > -1) {
							prev_arr_new.push(prev_arr[app_dtl_arr_indx]);
						}
					});
				}
				applicaiton_wise_role_check[app_id] = false;
				if (is_select) {
					let thingsIndexReset = _findIndex(prev_arr_new, {
						application_id: app_id,
					});
					if (thingsIndexReset > -1) {
						let thingArray = [];
						if (this.props.thing_list_obj[app_id]) {
							this.props.thing_list_obj[app_id].map((thing) =>
								thingArray.push(thing.id)
							);
						}
						console.log('clearThingsField', clearThingsField);
						if (clearThingsField) {
							prev_arr_new[thingsIndexReset]['thing_ids'] =
								!this.props.plan_appwise_map[app_id]
									.resorce_alocation && app_id !== 17
									? '*'
									: [];
						}
					}
				}
				if (role_id != null || role_id != '') {
					if (
						this.props.all_roles_list &&
						this.props.all_roles_list.length
					) {
						let found = _find(this.props.all_roles_list, {
							id: parseInt(role_id),
						});
						console.log('foundd_', found);
						if (found) {
							if (
								found.access == '*' &&
								found.is_deletable == false
							) {
								super_admin = true;
								customer_list = ['*'];
								territory_ids = ['*'];
								all_selected_industry = true;
								applicaiton_wise_role_check[app_id] = true;
								if (app_id != 17) {
									let thingsIndex = _findIndex(prev_arr_new, {
										application_id: app_id,
									});
									if (thingsIndex > -1) {
										let thingArray = [];
										if (this.props.thing_list_obj[app_id]) {
											this.props.thing_list_obj[
												app_id
											].map((thing) =>
												thingArray.push(thing.id)
											);
										}
										prev_arr_new[thingsIndex]['thing_ids'] =
											'*';
									}
								}
								if (
									this.props.all_app_list &&
									this.props.all_app_list.length
								) {
									this.props.all_app_list.map((apps) => {
										application_access_arr.push(apps.id);
									});
								}
							} else {
								if (!edit_fill) {
									application_access_arr = [];
									customer_list = [];
									territory_customers = [];
									territory_ids = [];
								}
								all_selected_industry = false;
								// application_access_arr = this.state
								// 	.application_access_arr;
							}
						}
					}
				}

				/*if (prev_arr_new && prev_arr_new.length) {
					prev_arr_new.map((arr) => {
						if (arr.application_id == 17) {
							application_access_arr = [1,4,6,16,18,19,20,23,24,17];
						}
					});
				}*/
			} else {
				prev_arr_new.push({
					application_id: app_id,
					role_id: role_id,
					reports_to: 0,
				});

				if (role_id != null || role_id != '') {
					if (
						this.props.all_roles_list &&
						this.props.all_roles_list.length
					) {
						let found = _find(this.props.all_roles_list, {
							id: parseInt(role_id),
						});
						console.log('foundd_', found);
						if (found) {
							if (
								found.access == '*' &&
								found.is_deletable == false
							) {
								super_admin = true;
								customer_list = ['*'];
								territory_ids = ['*'];
								all_selected_industry = true;
								if (
									this.props.all_app_list &&
									this.props.all_app_list.length
								) {
									this.props.all_app_list.map((apps) => {
										if (
											this.props.location.pathname.includes(
												'/iot-platform'
											)
										) {
											if (apps.id !== 17) {
												application_access_arr.push(
													apps.id
												);
											}
										} else {
											application_access_arr.push(
												apps.id
											);
										}
									});
								}
							} else {
								application_access_arr = [];
								customer_list = [];
								territory_customers = [];
								territory_ids = [];
								all_selected_industry = false;
								// application_access_arr = this.state
								// 	.application_access_arr;
								if (this.isRoleAdmin()) {
									selected_things_arr = [];
								}
							}
						}
					}
				}
			}

			// prev_arr = prev_arr.concat(arr);
			this.setState(
				{
					role_id: role_id,
					application_details_arr: prev_arr_new,
					super_admin: super_admin,
					siteAssets: [],
					selectedSites: super_admin ? ['*'] : [],
					application_access_arr: application_access_arr,
					customer_list: customer_list,
					territory_customers: territory_customers,
					all_selected_industry: all_selected_industry,
					applicaiton_wise_role_check,
					territory_ids: territory_ids,
					selected_things_arr: selected_things_arr,
				},
				() => {
					this.isAdmin();
					console.log(
						'application_details_arr',
						this.state.application_details_arr
					);
				}
			);
		}

		reportsTo(user_id, app_id) {
			// console.log('reportsTo user_id', user_id);
			// console.log('reportsTo app_id', app_id);
			let applicationDetailsArr = this.state.application_details_arr;
			if (applicationDetailsArr.length) {
				let index = _findIndex(applicationDetailsArr, {
					application_id: app_id,
				});
				if (index != -1) {
					applicationDetailsArr[index].reports_to =
						user_id != undefined ? user_id : 0;
				}
			}
			this.setState(
				{
					// selected_user: user_id,
					application_details_arr: applicationDetailsArr,
				},
				() => {
					// console.log('application_details_arr__', this.state.application_details_arr);
				}
			);
		}

		selectThings(things_id_arr, app_id) {
			// console.log('reportsTo user_id', user_id);
			// console.log('reportsTo app_id', app_id);
			let thing_ids = things_id_arr;
			if(app_id === 16){
				thing_ids = things_id_arr.filter(
					(item) => !this.state.siteAssets?.includes(item) && !this.state.territory_things?.includes(item)
				)
			}
			let applicationDetailsArr = this.state.application_details_arr;
			if (applicationDetailsArr.length) {
				let index = _findIndex(applicationDetailsArr, {
					application_id: app_id,
				});
				if (index != -1) {
					applicationDetailsArr[index].thing_ids = thing_ids
						? thing_ids.includes('*')
							? '*'
							: thing_ids
						: [];
				}
			}
			this.setState(
				{
					// selected_user: user_id,
					application_details_arr: applicationDetailsArr,
				},
				() => {
					this.appDetailsArr = applicationDetailsArr;
					console.log(
						'application_details_arr__',
						this.state.application_details_arr
					);
				}
			);
		}

		isAdmin() {
			let selected_role = _find(this.props.all_roles_list, {
				id: this.state.role_id,
			});
			let selected_type_id = 0;
			if (
				selected_role &&
				selected_role.role_type &&
				selected_role.role_type == 1
			) {
				selected_type_id = selected_role.role_type;
			}
			if (selected_type_id === 1) {
				this.setState({
					selected_things_arr: ['*'],
					all_selected_thing: true,
				});
			} /*else if (
				selected_type_id === 1 &&
				this.props.things_list &&
				this.props.things_list.length == 1
			) {
				this.setState({
					selected_things_arr: [this.props.things_list[0].id],
				});
			}*/ else {
				this.setState(
					{
						//selected_things_arr: [],
						selected_things_arr:
							this.props.location.pathname.includes(
								'/users/' +
									this.props.match.params.user_id +
									'/edit'
							)
								? this.state.selected_things_arr
								: [],
						all_selected_thing: false,
					},
					() => {
						this.thingsSelect(this.state.selected_things_arr);
					}
				);
			}
		}

		formatAddData() {
			if (
				(this.props.location.pathname.includes('/datoms-x') ||
					this.props.location.pathname.includes('/iot-platform')) &&
				this.props.location.pathname.includes('/user-management')
			) {
				console.log('role_id_', this.state.role_id);
				if (
					!this.state.selected_user_first_name ||
					this.state.selected_user_first_name === null ||
					this.state.selected_user_first_name === undefined ||
					this.state.selected_user_first_name === ''
				) {
					this.openNotification('error', 'Please enter full name');
					return false;
				} else if (
					!this.isRoleTracking() &&
					(!this.state.selected_user_email ||
						this.state.selected_user_email === null ||
						this.state.selected_user_email === undefined ||
						this.state.selected_user_email === '') &&
					(!this.state.selected_user_phone ||
						this.state.selected_user_phone === null ||
						this.state.selected_user_phone === undefined ||
						this.state.selected_user_phone.length == 0)
				) {
					if (
						this.props.client_id == 392 ||
						this.props.vendor_id == 392 ||
						this.props.customer_vendor_id == 392
					) {
						this.openNotification(
							'error',
							'Please enter either email or mobile number'
						);
						return false;
					} else {
						this.openNotification(
							'error',
							'Please enter either email or mobile number'
						);
						return false;
					}
				} else if (
					(this.state.enable_email &&
						this.state.validate_email_status == 'success') ||
					this.state.show_email_otp
				) {
					this.openNotification('error', 'Please verify email id');
					return false;
				} else if (
					this.state.enable_mobile ||
					this.state.show_mobile_otp
				) {
					if (
						this.props.client_id == 392 ||
						this.props.vendor_id == 392 ||
						this.props.customer_vendor_id == 392
					) {
						this.openNotification(
							'error',
							'Please verify mobile number'
						);
						return false;
					} else {
						this.openNotification(
							'error',
							'Please verify mobile number'
						);
						return false;
					}
				} else if (
					this.isRoleFuelDelivery() &&
					(!this.state.selected_user_phone ||
						this.state.selected_user_phone === null ||
						this.state.selected_user_phone === undefined ||
						this.state.selected_user_phone.length == 0)
				) {
					if (
						this.props.client_id == 392 ||
						this.props.vendor_id == 392 ||
						this.props.customer_vendor_id == 392
					) {
						this.openNotification(
							'error',
							'Please enter mobile number'
						);
						return false;
					} else {
						this.openNotification(
							'error',
							'Please enter mobile number'
						);
						return false;
					}
				} else if (
					this.isRoleTracking() &&
					!this.state.employee_id &&
					!this.state.tracking_id
				) {
					this.openNotification(
						'error',
						'Please enter either tracking or employee id'
					);
					return false;
				} else if (
					!this.state.role_id ||
					this.state.role_id === null ||
					this.state.role_id === undefined ||
					this.state.role_id === ''
				) {
					this.openNotification(
						'error', 
						this.props.t('please_select_a_role')
						// 'Please select a role'
					);
					return false;
				} else if (
					!this.state.application_access_arr ||
					(this.state.application_access_arr &&
						!(
							this.state.application_access_arr instanceof Array
						)) ||
					this.state.application_access_arr.length == 0
				) {
					this.openNotification(
						'error',
						'Please select an application'
					);
					return false;
				} else {
					return true;
				}
			} else {
				if (!this.state.show_add_form) {
					this.openNotification('error', 'Please select a contact');
					return false;
				} else if (
					!this.state.selected_user_first_name ||
					this.state.selected_user_first_name === null ||
					this.state.selected_user_first_name === undefined ||
					this.state.selected_user_first_name === ''
				) {
					this.openNotification('error', 'Please enter full name');
					return false;
				} else if (
					this.isRoleFuelDelivery() &&
					(!this.state.selected_user_phone ||
						this.state.selected_user_phone === null ||
						this.state.selected_user_phone === undefined ||
						this.state.selected_user_phone.length == 0)
				) {
					if (
						this.props.client_id == 392 ||
						this.props.vendor_id == 392 ||
						this.props.customer_vendor_id == 392
					) {
						this.openNotification(
							'error',
							'Please enter mobile number'
						);
						return false;
					} else {
						this.openNotification(
							'error',
							'Please enter mobile number'
						);
						return false;
					}
				} else if (
					(this.state.enable_email &&
						this.state.validate_email_status == 'success') ||
					this.state.show_email_otp
				) {
					this.openNotification('error', 'Please verify email id');
					return false;
				} else if (
					this.state.enable_mobile ||
					this.state.show_mobile_otp
				) {
					if (
						this.props.client_id == 392 ||
						this.props.vendor_id == 392 ||
						this.props.customer_vendor_id == 392
					) {
						this.openNotification(
							'error',
							'Please verify mobile number'
						);
						return false;
					} else {
						this.openNotification(
							'error',
							'Please verify mobile number'
						);
						return false;
					}
				} else if (
					!this.isRoleTracking() &&
					(!this.state.selected_user_email ||
						this.state.selected_user_email === null ||
						this.state.selected_user_email === undefined ||
						this.state.selected_user_email === '') &&
					(!this.state.selected_user_phone ||
						this.state.selected_user_phone === null ||
						this.state.selected_user_phone === undefined ||
						this.state.selected_user_phone.length == 0)
				) {
					if (
						this.props.client_id == 392 ||
						this.props.vendor_id == 392 ||
						this.props.customer_vendor_id == 392
					) {
						this.openNotification(
							'error',
							'Please enter either email or mobile number'
						);
						return false;
					} else {
						this.openNotification(
							'error',
							'Please enter either email or mobile number'
						);
						return false;
					}
				} else if (
					this.isRoleTracking() &&
					!this.state.employee_id &&
					!this.state.tracking_id
				) {
					console.log('tracking id: ', this.state.employee_id);
					this.openNotification(
						'error',
						'Please enter either tracking or employee id'
					);
					return false;
				} else if (
					!this.state.application_id_arr ||
					(this.state.application_id_arr &&
						!(this.state.application_id_arr instanceof Array)) ||
					this.state.application_id_arr.length == 0
				) {
					this.openNotification(
						'error',
						'Please select an application'
					);
					return false;
				} else if (
					!this.state.role_id ||
					this.state.role_id === null ||
					this.state.role_id === undefined ||
					this.state.role_id === ''
				) {
					this.openNotification('error', 'Please select role');
					return false;
				} else {
					return true;
				}
			}
		}

		/**
		 * This function calls the API to add a new user.
		 */
		async handleSubmitAdd(user) {
			let that = this;
			// console.log('user_del', user);
			let application_arr = [];
			let cont_id = null;
			if (this.state.contact_id != '') {
				cont_id = parseInt(this.state.contact_id);
			}

			let valid_all_field = this.formatAddData();
			console.log('valid_', valid_all_field);
			if (valid_all_field) {
				let application_access = [],
					cust_list;
				if (
					that.state.application_id_arr.includes(17) &&
					that.props.location.pathname.includes(
						'/customer-management'
					)
				) {
					let found = _find(that.props.selected_all_app_list, {
						application_id: 17,
					});
					if (found) {
						application_access = found.applications;
					}
				} else {
					application_access =
						that.state.application_access_arr &&
						that.state.application_access_arr.length
							? that.state.application_access_arr
							: undefined;
				}
				console.log(
					'hsd1',
					that.props,
					that.state.application_access_arr,
					application_access
				);

				if (
					that.state.customer_list &&
					that.state.customer_list.length
				) {
					if (that.state.customer_list.includes('*')) {
						cust_list = '*';
					} else {
						cust_list = that.state.customer_list.filter(
							(item) =>
								!this.state.territory_customers.includes(item)
						);
					}
				} else {
					cust_list = [];
				}

				let industry_sets = that.state.customer_set_list
						? that.state.customer_set_list
						: [],
					things = that.state.selected_things_arr
						? that.state.selected_things_arr
						: [];

				if (industry_sets.includes('*')) {
					industry_sets = '*';
				}

				if (things.includes('*')) {
					things = '*';
				}

				if (
					!that.props.location.pathname.includes('/datoms-x') &&
					!that.props.location.pathname.includes('/iot-platform') &&
					that.props.location.pathname.includes('/user-management') &&
					that.props.plan_description &&
					that.props.plan_description.resorce_alocation !=
						undefined &&
					that.props.plan_description.resorce_alocation == false
				) {
					things = '*';
				}

				console.log('things_', things);

				that.setState({
					loading: true,
				});
				let territory_ids = this.state.territory_ids;
				if (
					this.props.treeData &&
					this.state.territory_ids.length > 0
				) {
					territory_ids = this.filterTerritoryIds(
						[this.props.treeData],
						this.state.territory_ids
					);
				}
				let user_phone = '';
				if (Array.isArray(that.state.selected_user_phone)) {
					user_phone = that.state.selected_user_phone[0];
				} else {
					user_phone = that.state.selected_user_phone;
				}
				if (user_phone !== '' && !user_phone.includes('+')) {
					user_phone = '+' + user_phone;
				}
				// console.log(
				// 	'hsd2',
				// 	that.props,
				// 	that.state.application_access_arr,
				// 	application_access
				// );
				let data_obj = {
					contact_id: cont_id,
					first_name: that.state.selected_user_first_name,
					last_name: that.state.selected_user_last_name,
					email: that.state.selected_user_email,
					mobile_no: user_phone,
					designation: that.state.selected_user_designation,
					application_details: that.state.application_details_arr,
					applications: application_access
						? application_access
						: [this.props.application_id],
					industries: cust_list,
					industry_sets: industry_sets,
					things: things,
					territory_ids: this.getSubmitTerritoryIds(territory_ids),
					employee_id: that.state.employee_id,
					tracking_id: that.state.tracking_id,
					sites: this.state.selectedSites?.length
						? this.state.selectedSites.includes('*')
							? '*'
							: this.state.selectedSites
						: undefined,
				};
				console.log('console_nows', data_obj);

				let response = await addUser(data_obj, that.props.client_id);
				if (response.status === 403) {
					that.setState({
						loading: false,
						butn_load: false,
						unauthorised_access: true,
						unauthorised_access_msg: response.message,
					});
				} else if (response.status === 'success') {
					that.setState(
						{
							loading: false,
							butn_load: false,
							unauthorised_access: false,
						},
						() => {
							that.openNotification(
								'success',
								'User added successfully'
							);
							that.props.fetchUserData(
								that.props.client_id,
								that.props.app_drop
							);
							setTimeout(() => {
								that.props.fetchUserData(
									that.props.client_id,
									that.props.app_drop,
									false
								);
							}, 5000);
							// that.props.fetchCustomerDetails();
							that.onUserDrawerClose();
						}
					);
				} else {
					that.openNotification('error', response.message);
					that.setState({
						unauthorised_access: false,
						loading: false,
						error_API: true,
						butn_load: false,
						error_API_msg: response.message,
					});
				}
			} else {
				this.setState({
					butn_load: false,
				});
			}
		}

		/**
		 * This function calls the API to edit an existing user.
		 */
		async handleSubmitEdit(user) {
			let that = this;
			let response_status,
				user_phone = '',
				cust_list;
			// console.log('application_details_arr_', this.state.application_details_arr);
			if (Array.isArray(this.state.selected_user_phone)) {
				if (
					this.state.selected_user_phone[0].length &&
					!this.state.selected_user_phone[0].includes('+')
				) {
					user_phone = '+' + this.state.selected_user_phone[0];
				} else {
					user_phone = this.state.selected_user_phone[0];
				}
			} else {
				if (
					this.state.selected_user_phone.length &&
					!this.state.selected_user_phone.includes('+')
				) {
					user_phone = '+' + this.state.selected_user_phone;
				} else {
					user_phone = this.state.selected_user_phone;
				}
			}

			if (that.state.customer_list && that.state.customer_list.length) {
				if (that.state.customer_list.includes('*')) {
					cust_list = '*';
				} else {
					cust_list = that.state.customer_list.filter(
						(item) => !this.state.territory_customers.includes(item)
					);
				}
			} else {
				cust_list = [];
			}

			let valid_all_field = this.formatAddData();
			if (valid_all_field) {
				let application_access = [];
				// console.log('application_id_arr', that.state.application_id_arr);
				if (
					that.state.application_id_arr.includes(17) &&
					that.props.location.pathname.includes(
						'/customer-management'
					)
				) {
					let found = _find(that.props.selected_all_app_list, {
						application_id: 17,
					});
					if (found) {
						application_access = found.applications;
					}
				} else {
					application_access = that.state.application_access_arr;
				}
				// console.log('application_access', application_access);
				that.setState({
					loading: true,
				});

				let industry_sets = that.state.customer_set_list
						? that.state.customer_set_list
						: [],
					things = that.state.selected_things_arr
						? that.state.selected_things_arr
						: [];

				if (industry_sets.includes('*')) {
					industry_sets = '*';
				}

				if (things.includes('*')) {
					things = '*';
				}

				//	console.log('things_', things);
				let territory_ids = this.state.territory_ids;
				if (
					this.props.treeData &&
					this.state.territory_ids.length > 0
				) {
					territory_ids = this.filterTerritoryIds(
						[this.props.treeData],
						this.state.territory_ids
					);
				}
				if (
					!that.props.location.pathname.includes('/datoms-x') &&
					!that.props.location.pathname.includes('/iot-platform') &&
					that.props.location.pathname.includes('/user-management') &&
					that.props.plan_description &&
					that.props.plan_description.resorce_alocation !=
						undefined &&
					that.props.plan_description.resorce_alocation == false
				) {
					things = '*';
				}

				if (
					this.props.location.pathname.includes(
						'/customer-management'
					)
				) {
					things = null;
				}
				let data_obj = {
					contact_id: that.state.selected_user_id,
					first_name: that.state.selected_user_first_name,
					last_name: that.state.selected_user_last_name,
					email: that.state.selected_user_email,
					mobile_no: user_phone,
					designation: that.state.selected_user_designation,
					application_details: that.state.application_details_arr,
					applications: application_access
						? application_access
						: [this.props.application_id],
					industries: cust_list,
					industry_sets: industry_sets,
					things: things,
					territory_ids: this.getSubmitTerritoryIds(territory_ids),
					employee_id: that.state.employee_id,
					tracking_id: that.state.tracking_id,
					sites: this.state.selectedSites?.length
						? this.state.selectedSites.includes('*')
							? '*'
							: this.state.selectedSites
						: undefined,
				};

				let response = await editUser(
					data_obj,
					that.props.client_id,
					that.state.selected_user_id
				);
				if (response.status === 403) {
					that.setState({
						unauthorised_access: true,
						butn_load: false,
						unauthorised_access_msg: response.message,
					});
				} else if (response.status === 'success') {
					that.setState(
						{
							unauthorised_access: false,
							loading: false,
							butn_load: false,
						},
						() => {
							that.openNotification(
								'success',
								'User details updated successfully'
							);
							that.props.fetchUserData(
								that.props.client_id,
								that.props.app_drop
							);
							setTimeout(() => {
								that.props.fetchUserData(
									that.props.client_id,
									that.props.app_drop,
									false
								);
							}, 5000);
							that.onUserDrawerClose();
						}
					);
				} else {
					that.openNotification('error', response.message);
					that.setState({
						unauthorised_access: false,
						loading: false,
						butn_load: false,
						error_API: true,
						error_API_msg: response.message,
					});
				}
			} else {
				this.setState({
					butn_load: false,
				});
			}
		}

		validateEmail(value, type) {
			let validateStatus = '';
			if (value && emailValidation(value)) {
				validateStatus = 'success';
			} else {
				validateStatus = 'error';
			}
			console.log('validateStatus_', validateStatus);
			this.setState(
				{
					validate_email_status: validateStatus,
				},
				() => {
					this.handleChange(value, type);
				}
			);
		}

		//territory field access
		handleTerritoryChange() {
			return (
				(this.props.location.pathname.includes('/datoms-x') &&
					!this.props.location.pathname.includes(
						'/customer-management'
					)) ||
				(this.props.location.pathname.includes('/datoms-x') &&
					this.props.location.pathname.includes(
						'/customer-management'
					) &&
					this.props.isTerVendor === 1) ||
				(this.props.location.pathname.includes('/iot-platform') &&
					!this.props.location.pathname.includes(
						'/customer-management'
					))
			);
		}

		//filter out child territory ids by comparing treeData and selected_territory_ids
		filterTerritoryIds(treeData, selected_territory_ids) {
			let that = this;
			let filtered_territory_ids = [];
			let selected_territory_array = selected_territory_ids;
			if (treeData && treeData.length > 0) {
				treeData.map((item) => {
					console.log(
						'filtered-ter-hook',
						selected_territory_array,
						item.key,
						selected_territory_array.includes(item.key)
					);
					if (selected_territory_array.includes(item.key)) {
						selected_territory_array =
							selected_territory_array.filter(
								(i) => i !== item.key
							);
						filtered_territory_ids.push(item.key);
						console.log('filtered-terrdata', item.key);
					} else if (item.children && item.children.length > 0) {
						filtered_territory_ids = filtered_territory_ids.concat(
							that.filterTerritoryIds(
								item.children,
								selected_territory_array
							)
						);
					}
				});
			}
			return filtered_territory_ids;
		}

		//filter territory customers
		filterTerritoryCustomers() {
			if (
				this.props.territoryData &&
				this.props.territoryData.length &&
				this.state.territory_ids &&
				this.state.territory_ids.length
			) {
				console.log('territory-data', this.props.territoryData);
				const territory_customers = [];
				const territory_sites = [];
				const territory_things = [];
				const allTerritoryIds = this.getChildTerritoryIds(
					this.props.treeData,
					this.state.territory_ids
				);
				console.log('allTerritoryIds', allTerritoryIds);
				this.props.territoryData.map((item) => {
					if (
						allTerritoryIds.includes(item.id)
					) {
						if (Array.isArray(item.customer_ids) && item.customer_ids.length > 0) {
							territory_customers.push(...item.customer_ids);
						}
						if(Array.isArray(item.site_ids) && item.site_ids.length > 0) {
							territory_sites.push(...item.site_ids);
						}
						console.log('territory_things 1', item.thing_ids);
						if(Array.isArray(item.thing_ids) && item.thing_ids.length > 0) {
							territory_things.push(...item.thing_ids);
						}
					}
				});
				console.log('territory_things 2',  [...new Set(territory_things)]);
				this.setState({
					territory_customers: [...new Set(territory_customers)],
					territory_sites: [...new Set(territory_sites)],
					territory_things: [...new Set(territory_things)],
				}, this.setFilteredSiteAndAssets());
			} else {
				this.setState({
					territory_customers: [],
					territory_sites: [],
					territory_things: [],
				},() => this.setFilteredSiteAndAssets());
			}
		}

		//get a node from treedata fro a given id
		getNodeFromTreeData(treeData, matchingId) {
			let that = this;
			if (treeData.key == matchingId) {
				return treeData;
			} else if (treeData.children != null) {
				var i;
				var result = null;
				for (
					i = 0;
					result == null && i < treeData.children.length;
					i++
				) {
					result = that.getNodeFromTreeData(
						treeData.children[i],
						matchingId
					);
				}
				return result;
			}
			return null;
		}

		//child ids of a given node
		getIdsFromNode(treeData) {
			let that = this;
			let child_territory_ids = [];
			if (treeData && treeData.length > 0) {
				treeData.map((item) => {
					if (item) {
						child_territory_ids.push(item.key);
						if (item.children && item.children.length > 0) {
							child_territory_ids = child_territory_ids.concat(
								that.getIdsFromNode(item.children)
							);
						}
					}
				});
			}
			return child_territory_ids;
		}

		// get all the child territory ids from treeData of passed parent_ids array
		getChildTerritoryIds(treeData, parent_ids) {
			let that = this;
			let child_territory_ids = [];
			if (parent_ids && parent_ids.length) {
				parent_ids.map((parent_id) => {
					child_territory_ids = child_territory_ids.concat(
						that.getIdsFromNode(
							[that.getNodeFromTreeData(treeData, parent_id)],
							parent_id
						)
					);
				});
			}
			return child_territory_ids;
		}

		// getChildTerritoryIds(parent_ids) {
		// 	let that = this;
		// 	let child_territory_ids = [];
		// 	if (Array.isArray(parent_ids) && parent_ids && parent_ids.length) {
		// 		parent_ids.map((item) => {
		// 			if (
		// 				that.props.territoryData &&
		// 				that.props.territoryData.length
		// 			) {
		// 				that.props.territoryData.map((territory) => {
		// 					if (territory.parent_id === item) {
		// 						child_territory_ids.push(territory.id);
		// 					}
		// 				});
		// 			}
		// 		});
		// 	}

		// 	return [...new Set([...parent_ids, ...child_territory_ids])];
		// }

		//get array of values from array of objects
		getArrayOfValues(array, key) {
			let values = [];
			array.map((item) => {
				values.push(item[key]);
			});
			return values;
		}

		// has all territory (*)
		hasAllTerritory(user) {
			let result_territories = [];
			if (user.territory_ids) {
				result_territories =
					user.territory_ids == '*'
						? [this.props.topNodeId]
						: user.territory_ids;
			}
			return result_territories;
		}

		//get submit territory ids
		getSubmitTerritoryIds(territory_ids) {
			let result_territories = [];
			if (territory_ids) {
				result_territories =
					territory_ids.includes(this.props.topNodeId) ||
					this.isTerritoryAdmin()
						? '*'
						: territory_ids;
			}
			return result_territories;
		}
		// hasAllTerritory(user) {
		// 	let result_territories = [];
		// 	if (user.territory_ids) {
		// 		result_territories =
		// 			user.territory_ids === '*'
		// 				? this.getArrayOfValues(
		// 						this.props.territoryData,
		// 						'id'
		// 				  ).filter((e) => e !== this.props.topNodeId)
		// 				: user.territory_ids;
		// 	}
		// 	return result_territories;
		// }

		isTerritoryAdmin() {
			let selected_role = _find(this.props.all_roles_list, {
				id: this.state.role_id,
			});
			let selected_type_id = 0;
			if (
				selected_role &&
				selected_role.role_type &&
				selected_role.role_type == 1
			) {
				selected_type_id = selected_role.role_type;
			}
			if (
				// this.props.customer_list_arr &&
				// this.props.customer_list_arr.length > 1 &&
				selected_type_id == 1
			) {
				return true;
			}
			return false;
		}

		isRoleAdmin(roleId = this.state.role_id) {
			let selected_role = _find(this.props.all_roles_list, {
				id: roleId,
			});
			let selected_type_id = 0;
			if (
				selected_role &&
				selected_role.role_type &&
				selected_role.role_type == 1
			) {
				return true;
			}
			return false;
		}

		isRoleTracking() {
			let selected_role = _find(this.props.all_roles_list, {
				id: this.state.role_id,
			});
			if (
				selected_role &&
				selected_role.role_type &&
				selected_role.role_type == 5
			) {
				return true;
			}
			return false;
		}
		isRoleFuelDelivery() {
			let selected_role = _find(this.props.all_roles_list, {
				id: this.state.role_id,
			});
			if (
				selected_role &&
				selected_role.role_type &&
				selected_role.role_type == 6 /*Fuel Buddy role type id */
			) {
				return true;
			}
			return false;
		}
		getThingArrayValue(app_id) {
			let role_det = {},
				thingArr = [];

			this.appDetailsArr = this.state.application_details_arr;

			role_det = _find(this.state.application_details_arr, {
				application_id: app_id,
			});
			console.log('thing_list_obj_', this.state.application_details_arr);
			console.log('thing_list_obj_ 1', role_det);
			if (role_det && role_det.thing_ids && role_det.thing_ids == '*') {
				thingArr = role_det.thing_ids;
			}

			if (
				role_det &&
				role_det.thing_ids &&
				Array.isArray(role_det.thing_ids) &&
				role_det.thing_ids.length &&
				this.props.thing_list_obj &&
				Object.keys(this.props.thing_list_obj).length &&
				this.props.location.pathname.includes('/customer-management')
			) {
				Object.keys(this.props.thing_list_obj).map((appId) => {
					if (
						this.props.thing_list_obj[appId] &&
						this.props.thing_list_obj[appId].length
					) {
						this.props.thing_list_obj[appId].map((thing) => {
							if (role_det.thing_ids.includes(thing.id)) {
								thingArr.push(thing.id);
							}
						});
					}
				});

				Object.keys(this.props.thing_list_obj).map((appId) => {
					this.appDetailsArr.map((det) => {
						if (det.application_id == appId) {
							det.thing_ids = thingArr;
						}
					});
				});
			}
			console.log('thingArrthingArr_', thingArr);
			console.log('thingArrthingArr_ 1', this.appDetailsArr);
			return thingArr === '*' ? ['*'] : thingArr;
		}
		changeTrackingId(e) {
			this.setState({ tracking_id: e.target.value });
		}
		changeEmployeeId(e) {
			this.setState({ employee_id: e.target.value });
		}

		async verifyNow(type, isVerify = false) {
			console.log('verifyNow_', type);
			console.log(
				'verifyNow_ user_mobile',
				this.state.selected_user_phone
			);
			console.log(
				'verifyNow_ user_mobile_backup',
				this.state.selected_user_phone_backup
			);
			let that = this;
			let data_to_be_posted = { query_text: '?purpose=VERIFY_CONTACT' },
				response_status;

			if (type == 'email') {
				if (
					this.state.selected_user_email !==
						this.state.selected_user_email_backup ||
					isVerify
				) {
					data_to_be_posted['email_id'] =
						this.state.selected_user_email;

					let otpResponse = await sendAuthOtp(data_to_be_posted);
					if (otpResponse.status === 'success') {
						that.setState(
							{
								show_email_otp: true,
								enable_email: false,
							},
							() => {
								that.openNotification(
									'success',
									'OTP sent successfully'
								);
							}
						);
					} else {
						that.openNotification('error', otpResponse.message);
					}
				} else {
					that.openNotification(
						'error',
						'Email id should be different from current!'
					);
				}
			}

			if (type == 'mobile') {
				if (
					this.state.selected_user_phone !==
						this.state.selected_user_phone_backup ||
					isVerify
				) {
					let userMobile = this.state.selected_user_phone;
					if (!this.state.selected_user_phone.includes('+')) {
						userMobile = '+' + this.state.selected_user_phone;
					}
					data_to_be_posted['mobile_no'] = userMobile;

					let otpResponse = await sendAuthOtp(data_to_be_posted);
					if (otpResponse.status === 'success') {
						that.setState(
							{
								show_mobile_otp: true,
								enable_mobile: false,
							},
							() => {
								that.openNotification(
									'success',
									'OTP sent successfully'
								);
							}
						);
					} else {
						that.openNotification('error', otpResponse.message);
					}
				} else {
					if (this.props.client_id == 392) {
						that.openNotification(
							'error',
							'Mobile number should be different from current!'
						);
					} else {
						that.openNotification(
							'error',
							'Mobile number should be different from current!'
						);
					}
				}
			}
		}

		async verify(type) {
			console.log('verifyNow_', type);
			let that = this;
			let data_to_be_posted = {},
				response_status;

			if (type == 'mobile') {
				let userMobile = this.state.selected_user_phone;
				if (!this.state.selected_user_phone.includes('+')) {
					userMobile = '+' + this.state.selected_user_phone;
				}
				data_to_be_posted['mobile_no'] = userMobile;
				data_to_be_posted['otp'] = parseInt(this.state.mobile_otp);
			} else {
				data_to_be_posted['email_id'] = this.state.selected_user_email;
				data_to_be_posted['otp'] = parseInt(this.state.email_otp);
			}

			let otpResponse = await verifyAccount(data_to_be_posted);
			if (otpResponse.status === 'success') {
				if (type == 'email') {
					that.setState(
						{
							show_email_otp: false,
							email_otp: '',
							remove_profile_load: false,
						},
						() => {
							that.openNotification(
								'success',
								'Email Id Updated'
							);
							that.props.fetchUserData(
								that.props.client_id,
								that.props.app_drop
							);
						}
					);
				} else if (type == 'mobile') {
					that.setState(
						{
							show_mobile_otp: false,
							mobile_otp: '',
							remove_profile_load: false,
						},
						() => {
							that.openNotification(
								'success',
								'Mobile Number Updated'
							);
							that.props.fetchUserData(
								that.props.client_id,
								that.props.app_drop
							);
						}
					);
				}
			} else {
				that.openNotification('error', otpResponse.message);
			}
		}

		onEditCancel(type) {
			let add_user_form = this.state.add_user_form;

			if (type == 'email') {
				add_user_form.customer_details_form[3].initial_value =
					this.state.selected_user_email_backup;
				this.setState({
					enable_email: false,
					selected_user_email: this.state.selected_user_email_backup,
					add_user_form: add_user_form,
				});
			} else if (type == 'mobile') {
				add_user_form.customer_details_form[4].initial_value =
					this.state.selected_user_phone_backup;
				this.setState({
					enable_mobile: false,
					selected_user_phone: this.state.selected_user_phone_backup,
					add_user_form: add_user_form,
				});
			}
			this.props.form.resetFields();
		}

		onOtpChange(e, type) {
			console.log('onOtpChange_', e.target.value);
			if (type == 'email') {
				this.setState({
					email_otp: e.target.value,
				});
			} else if (type == 'mobile') {
				this.setState({
					mobile_otp: e.target.value,
				});
			}
			this.props.form.resetFields();
		}

		onOtpCancel(type) {
			let add_user_form = this.state.add_user_form;

			if (type == 'email') {
				add_user_form.customer_details_form[3].initial_value =
					this.state.selected_user_email_backup;
				this.setState({
					show_email_otp: false,
					email_otp: '',
					selected_user_email: this.state.selected_user_email_backup,
					add_user_form: add_user_form,
				});
			} else if (type == 'mobile') {
				add_user_form.customer_details_form[4].initial_value =
					this.state.selected_user_phone_backup;
				this.setState({
					show_mobile_otp: false,
					mobile_otp: '',
					selected_user_phone: this.state.selected_user_phone_backup,
					add_user_form: add_user_form,
				});
			}
			this.props.form.resetFields();
		}

		render() {
			const { getFieldDecorator } = this.props.form;
			let submitButton = JSON.parse(
				JSON.stringify(this.props.add_user_form.draw_buttons.submit)
			);
			let cancelButton = JSON.parse(
				JSON.stringify(this.props.add_user_form.draw_buttons.cancel)
			);
			submitButton[0].loading = this.state.butn_load;
			cancelButton[0].disabled = this.state.butn_load;
			// console.log(
			// 	'filtered-ter',
			// 	this.filterTerritoryIds([this.props.treeData], [2, 6, 4, 5])
			// );
			const treeProps = {
				treeData: this.isTerritoryAdmin()
					? [{ title: 'All Territories', key: '*', value: '*' }]
					: [this.props.treeData],
				value: this.isTerritoryAdmin()
					? ['*']
					: this.state.territory_ids,
				onChange: (val) => {
					this.setState(
						{ territory_ids: this.getArrayOfValues(val, 'value') },
						() => this.filterTerritoryCustomers()
					);
				},
				treeDefaultExpandAll: true,
				treeCheckable: true,
				showCheckedStrategy: TreeSelect.SHOW_PARENT,
				treeCheckStrictly: true,
				maxTagCount: 2,
				maxTagPlaceholder:
					'+ ' +
					(this.state.territory_ids.length - 2).toString() +
					' more',
				placeholder: this.props.t? this.props.t('select_territories'): "Select Territories",
				// placeholder: 'Select territories',
				disabled: this.isTerritoryAdmin() ? true : false,
				filterTreeNode: (search, item) => {
					return (
						item.title
							.toLowerCase()
							.indexOf(search.toLowerCase()) >= 0
					);
				},
			};

			let saveButnEmail = '',
				cancelButnEmail = '',
				saveButnMobile = '',
				cancelButnMobile = '',
				spanValueEmail = 24,
				spanValueMobile = 24,
				otpEmail = '',
				otpMobile = '';

			let tooltipTitle = 'Mobile',
				placeholderText = 'Please enter mobile no',
				label = 'Mobile No';
			if (this.props.client_id == 392) {
				tooltipTitle = 'Mobile';
				placeholderText = 'Please enter mobile no';
				label = 'Mobile No';
			}

			console.log('checkingg_ enable_mobile_', this.state.enable_mobile);

			if (
				this.state.enable_email &&
				this.state.validate_email_status == 'success'
			) {
				spanValueEmail = 17;
				saveButnEmail = (
					<AntCol span="3" className="mar-top--6">
						<div
							className="resend-invitation-butn"
							onClick={() => this.verifyNow('email')}
						>
							<AntTooltip title="Email">
								<CheckCircleOutlined className="resend-icon" />{' '}
								<div className="resend-sec-text">Save</div>
							</AntTooltip>
						</div>
					</AntCol>
				);

				cancelButnEmail = (
					<AntCol span="4" className="mar-top--6">
						<div
							className="resend-invitation-butn"
							onClick={() => this.onEditCancel('email')}
						>
							<AntTooltip title="Email">
								<CloseCircleOutlined className="resend-icon" />{' '}
								<div className="resend-sec-text">Cancel</div>
							</AntTooltip>
						</div>
					</AntCol>
				);
			}

			if (this.state.enable_mobile) {
				spanValueMobile = 17;
				saveButnMobile = (
					<AntCol span="3" className="mar-top--6">
						<div
							className="resend-invitation-butn"
							onClick={() => this.verifyNow('mobile')}
						>
							<AntTooltip title={tooltipTitle}>
								<CheckCircleOutlined className="resend-icon" />{' '}
								<div className="resend-sec-text">Save</div>
							</AntTooltip>
						</div>
					</AntCol>
				);

				cancelButnMobile = (
					<AntCol span="4" className="mar-top--6">
						<div
							className="resend-invitation-butn"
							onClick={() => this.onEditCancel('mobile')}
						>
							<AntTooltip title={tooltipTitle}>
								<CloseCircleOutlined className="resend-icon" />{' '}
								<div className="resend-sec-text">Cancel</div>
							</AntTooltip>
						</div>
					</AntCol>
				);
			}

			if (this.state.show_mobile_otp) {
				otpMobile = (
					<AntRow
						// className="form-section"
						align="middle"
						className="mar-bot-40"
					>
						<AntCol span="17">
							<div className="profile-form-lebel">OTP</div>
							<AntInput
								value={this.state.mobile_otp}
								onChange={(value) =>
									this.onOtpChange(value, 'mobile')
								}
								type={'number'}
							/>
						</AntCol>
						<AntCol span="3" className="mar-top-17">
							<div
								className="resend-invitation-butn"
								onClick={() => this.verify('mobile')}
							>
								<AntTooltip title={tooltipTitle}>
									<CheckCircleOutlined className="resend-icon" />{' '}
									<div className="resend-sec-text">
										Verify
									</div>
								</AntTooltip>
							</div>
						</AntCol>
						<AntCol span="4" className="mar-top-17">
							<div
								className="resend-invitation-butn"
								onClick={() => this.onOtpCancel('mobile')}
							>
								<AntTooltip title={tooltipTitle}>
									<CloseCircleOutlined className="resend-icon" />{' '}
									<div className="resend-sec-text">
										Cancel
									</div>
								</AntTooltip>
							</div>
						</AntCol>
					</AntRow>
				);
			}

			if (this.state.show_email_otp) {
				otpEmail = (
					<AntRow
						// className="form-section"
						align="middle"
						className="mar-bot-40"
					>
						<AntCol span="17">
							<div className="profile-form-lebel">OTP</div>
							<AntInput
								value={this.state.email_otp}
								onChange={(value) =>
									this.onOtpChange(value, 'email')
								}
								type={'number'}
							/>
						</AntCol>
						<AntCol span="3" className="mar-top-17">
							<div
								className="resend-invitation-butn"
								onClick={() => this.verify('email')}
							>
								<AntTooltip title="Email">
									<CheckCircleOutlined className="resend-icon" />{' '}
									<div className="resend-sec-text">
										Verify
									</div>
								</AntTooltip>
							</div>
						</AntCol>
						<AntCol span="4" className="mar-top-17">
							<div
								className="resend-invitation-butn"
								onClick={() => this.onOtpCancel('email')}
							>
								<AntTooltip title="Email">
									<CloseCircleOutlined className="resend-icon" />{' '}
									<div className="resend-sec-text">
										Cancel
									</div>
								</AntTooltip>
							</div>
						</AntCol>
					</AntRow>
				);
			}

			let siteFormItem;
			if (this.props.showSiteField) {
				siteFormItem = (
					<Form.Item className="font-600" label="Sites">
						<AntSelect
							mode="multiple"
							showSearch
							optionFilterProp="label"
							maxTagCount={5}
							placeholder="Select Sites"
							loading={this.state.loadingSites}
							options={
								this.props.sitesList?.map((item) => ({
											...item,
											disabled: this.state.super_admin || this.state.territory_sites?.includes(item.value) ? true : false,
									  }))
							}
							value={getFinalSelectedSites(this.state.selectedSites, this.state.territory_sites)}
							onChange={this.onSiteChange}
						/>
					</Form.Item>
				);
			}
			let territoryFormItem = (isSelfEditAdmin) => !this.props.isTerritoryEnabled ? "" : <AntRow
						gutter={
							16
						}
					>
						<AntCol
							span={
								24
							}
							className="wid-100"
						>
							<Form.Item
								className="font-600"
								label={this.props.t? this.props.t('select_territory'): "Select Territory"}
								// label="Select Territory"
							>
								<AntTreeSelect
									t={this.props.t}
									default_type
									{...treeProps}
									disabled={
										isSelfEditAdmin
									}
								/>
							</Form.Item>
						</AntCol>
					</AntRow>
		
			return (
				<AntDrawer
					title={
						this.props.show_add_draw != undefined
							? this.props.show_add_draw != false
								? this.props.t('add_new_user')
								: (this.props.t? this.props.t('edit_user'): "Edit User")
							: ''
					}
					className={
						'add-edit-user-drawer ' +
						(this.props.location.pathname.includes('/edit')
							? 'user-edit-draw'
							: '')
					}
					width={589}
					placement="right"
					closable={false}
					onClose={() => this.onUserDrawerClose()}
					visible={true}
					destroyOnClose={true}
					mask={true}
					maskClosable={false}
					// getContainer={true}
				>
					{(() => {
						if (this.state.add_user_form) {
							let other_contact = this.other_contact,
								total_users = 0,
								app_list_arr = [],
								role_list = [],
								role_arr = [];

							if (
								this.props.location.pathname.includes(
									'/user-management'
								) &&
								(this.props.location.pathname.includes(
									'/datoms-x'
								) ||
									this.props.location.pathname.includes(
										'/iot-platform'
									))
							) {
								// console.log('checked__');
								let role_details = [];
								if (
									this.props.all_roles_list &&
									this.props.all_roles_list.length
								) {
									this.props.all_roles_list.map((roles) => {
										// console.log('rolesss', roles);
										role_details.push({
											role_id: roles.id,
											role_name: roles.name,
										});
									});
								}

								if (
									this.props.location.pathname.includes(
										'/datoms-x'
									)
								) {
									role_arr.push({
										application_id: 12,
										role_details: role_details,
									});
								} else if (
									this.props.location.pathname.includes(
										'/iot-platform'
									)
								) {
									role_arr.push({
										application_id: 17,
										role_details: role_details,
									});
								}

								if (
									this.props.all_app_list &&
									this.props.all_app_list.length
								) {
									this.props.all_app_list.map((app) => {
										if (
											this.props.location.pathname.includes(
												'/iot-platform'
											)
										) {
											if (app.id !== 17) {
												app_list_arr.push({
													id: app.id,
													name: app.name,
												});
											}
										} else {
											app_list_arr.push({
												id: app.id,
												name: app.name,
											});
										}
									});
								}
							} else {
								if (
									this.props.selected_all_app_list &&
									this.props.selected_all_app_list.length > 0
								) {
									this.props.selected_all_app_list.map(
										(application_detail) => {
											// console.log('application_detail_', application_detail);
											let appl_list = _find(
												this.props.all_app_list,
												{
													id: application_detail.application_id,
												}
											);
											// console.log('appl_list_', appl_list);
											if (appl_list) {
												if (
													application_detail.application_id !=
													12
												) {
													app_list_arr.push({
														id: appl_list.id,
														name: appl_list.name,
													});
												}
											}

											let app_role_details = [],
												r_detail = {};
											if(this.props.client_id === 3725) {
												app_role_details =
													application_detail.role_details;
											} else if (
												this.props.plan_description &&
												this.props.plan_description
													.pre_defined_role_type_ids &&
												this.props.plan_description
													.pre_defined_role_type_ids
													.length
											) {
												if (
													application_detail.role_details &&
													application_detail
														.role_details.length
												) {
													application_detail.role_details.map(
														(r) => {
															r_detail = {};
															if (
																this.props
																	.all_roles_list &&
																this.props
																	.all_roles_list
																	.length
															) {
																r_detail =
																	_find(
																		this
																			.props
																			.all_roles_list,
																		{
																			id: r.role_id,
																		}
																	);
																if (
																	_isEmpty(
																		r_detail
																	) ==
																		false &&
																	this.props.plan_description.pre_defined_role_type_ids.includes(
																		r_detail.role_type
																	)
																) {
																	app_role_details.push(
																		r
																	);
																}
															}
														}
													);
												}
											} else {
												app_role_details =
													application_detail.role_details;
											}
											role_arr.push({
												application_id:
													application_detail.application_id,
												role_details: app_role_details,
											});

											role_list.push({
												application_id:
													application_detail.application_id,
												role_details:
													application_detail.role_details,
											});
										}
									);

									app_list_arr = _uniqBy(app_list_arr, 'id');
								}
							}
							/*console.log('app_list_arr__', app_list_arr);*/
							/*console.log('app_list_arr__ application_id_arr', this.state.application_id_arr);*/

							if (
								this.props.show_add_draw &&
								app_list_arr &&
								app_list_arr.length == 1 &&
								this.state.application_id_arr &&
								this.state.application_id_arr.length == 0
							) {
								this.applicationSelect([app_list_arr[0].id]);
							}

							if (
								app_list_arr &&
								app_list_arr.length == 1 &&
								this.state.application_access_arr &&
								this.state.application_access_arr.length == 0
							) {
								if (
									(this.props.location.pathname.includes(
										'/datoms-x'
									) ||
										this.props.location.pathname.includes(
											'/iot-platform'
										)) &&
									!this.props.location.pathname.includes(
										'/customer-management'
									)
								) {
									this.accessApplicationSelect([
										app_list_arr[0].id,
									]);
								}
							}

							let isSelfEditAdmin = false,
								isDisable = false,
								roleDet = {};

							roleDet = _find(
								this.state.application_details_arr,
								{
									application_id: this.props.application_id,
								}
							);
							console.log('roleDett_', roleDet);
							if (
								parseInt(this.props.logged_in_user_id) ===
									parseInt(this.props.match.params.user_id) &&
								roleDet &&
								roleDet.role_type &&
								roleDet.role_type !== 1
							) {
								isSelfEditAdmin = true;
							}

							console.log('roleDett_ 1', isSelfEditAdmin);

							return (
								<div className="content-body">
									<Form
										autoComplete="off"
										layout="vertical"
										hideRequiredMark
									>
										{/* {(() => {
											if (this.props.show_add_draw) {
												return (
													<AntRow
														gutter={16}
														className="border-bot"
													>
														<AntCol
															span={14}
															className="wid-100 select-contact"
														>
															<Form.Item label="">
																<span className="mar-rt-20 bold">
																	{this.props.t(
																		'contact'
																	)}
																</span>
																<AntSelect
																	type={
																		'normal'
																	}
																	className="contact-select"
																	select_value={
																		this
																			.state
																			.contact_id
																	}
																	onSelectChange={(
																		value,
																		index
																	) =>
																		this.contact_select(
																			value,
																			index
																		)
																	}
																	select_option={
																		this
																			.state
																			.add_user_form
																			.contact_select
																	}
																/>
															</Form.Item>
														</AntCol>
														<AntCol
															span={10}
															className="wid-100 select-contact"
														>
															{(() => {
																if (
																	this.state
																		.show_add_btn
																) {
																	return (
																		<span
																			className="show-form-link"
																			onClick={() =>
																				this.addNewForm()
																			}
																		>
																			{'+ ' +
																				this.props.t(
																					'add_new_user'
																				)}
																		</span>
																	);
																}
															})()}
														</AntCol>
													</AntRow>
												);
											}
										})()} */}
										{(() => {
											if (this.state.show_add_form) {
												// console.log('selected_user_first_name', this.state.selected_user_first_name);
												return (
													<div className="form-container">
														<AntRow gutter={16}>
															{(() => {
																if (
																	this.state
																		.add_user_form
																		.customer_details_form &&
																	this.state
																		.add_user_form
																		.customer_details_form
																		.length
																) {
																	return this.state.add_user_form.customer_details_form
																		.filter(
																			(
																				ei
																			) =>
																				ei.key !==
																				'last_name'
																		)
																		.map(
																			(
																				form
																			) => {
																				console.log(
																					'phone_no',
																					form
																				);
																				if (
																					form.key ===
																					'phone_no'
																				) {
																					console.log(
																						'form.initial_value',
																						form.initial_value
																					);
																					// let form_value = form.initial_value.includes('+') ? form.initial_value : '+91 ' + form.initial_value;
																					return (
																						<AntRow
																							align="middle"
																							className="email-mob-container"
																						>
																							<AntCol
																								span={
																									spanValueMobile
																								}
																								className="wid-100"
																							>
																								<Form.Item
																									className="font-600 phone-input"
																									label={
																										form.label
																									}
																								>
																									{getFieldDecorator(
																										form.key,
																										{
																											rules: [
																												{
																													required:
																														form.required,
																													message:
																														form.message,
																												},
																											],
																											initialValue:
																												form.initial_value,
																											onChange:
																												(
																													e
																												) =>
																													this.handleChange(
																														e,
																														form.key
																													),
																										}
																									)(
																										<InputPhone
																											country={
																												'in'
																											}
																											keyName={
																												'phone_no'
																											}
																											value={
																												this
																													.state
																													.selected_user_phone
																											}
																											onChange={(
																												e
																											) =>
																												this.handleChange(
																													e,
																													form.key
																												)
																											}
																											placeholder={
																												form.message
																											}
																										/>
																									)}
																								</Form.Item>
																							</AntCol>
																							{
																								saveButnMobile
																							}
																							{
																								cancelButnMobile
																							}
																							{
																								otpMobile
																							}
																						</AntRow>
																					);
																				} else if (
																					form.key ===
																					'email'
																				) {
																					return (
																						<AntRow
																							align="middle"
																							className="email-mob-container"
																						>
																							<AntCol
																								span={
																									spanValueEmail
																								}
																								className="wid-100"
																							>
																								<Form.Item
																									className="font-600"
																									label={
																										form.label
																									}
																									validateStatus={
																										this
																											.state
																											.validate_email_status
																									}
																									hasFeedback={
																										true
																									}
																								>
																									{getFieldDecorator(
																										form.key,
																										{
																											rules: [
																												{
																													required:
																														form.required,
																													message:
																														form.message,
																												},
																												{
																													pattern:
																														/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
																													message:
																														this.props.t? this.props.t('enter_valid_email'): "Enter a valid email id"
																														// 'Enter a valid email id',
																												},
																											],
																											initialValue:
																												form.initial_value,
																											onChange:
																												(
																													e
																												) =>
																													this.validateEmail(
																														e
																															.target
																															.value,
																														form.key
																													),
																										}
																									)(
																										<AntInput
																											type={
																												form.type
																													? form.type
																													: 'text'
																											}
																											disabled={
																												this
																													.state
																													.block_add_form
																													? true
																													: false
																											}
																											placeholder={
																												form.message
																											}
																										/>
																									)}
																								</Form.Item>
																							</AntCol>
																							{
																								saveButnEmail
																							}
																							{
																								cancelButnEmail
																							}
																							{
																								otpEmail
																							}
																						</AntRow>
																					);
																				} else {
																					return (
																						<AntCol
																							span={
																								24
																							}
																							className="wid-100"
																						>
																							<Form.Item
																								className="font-600"
																								label={
																									form.label
																								}
																							>
																								{getFieldDecorator(
																									form.key,
																									{
																										rules: [
																											{
																												required:
																													form.required,
																												message:
																													form.message,
																											},
																										],
																										initialValue:
																											form.initial_value,
																										onChange:
																											(
																												e
																											) =>
																												this.handleChange(
																													e,
																													form.key
																												),
																									}
																								)(
																									<AntInput
																										type={
																											form.type
																												? form.type
																												: 'text'
																										}
																										disabled={
																											this
																												.state
																												.block_add_form
																												? true
																												: false
																										}
																										placeholder={
																											form.message
																										}
																									/>
																								)}
																							</Form.Item>
																						</AntCol>
																					);
																				}
																			}
																		);
																}
															})()}
														</AntRow>
														{/*<Row gutter={16}>
															<Col span={12} className="wid-100">
																<Form.Item label="Designation">
																	{getFieldDecorator('designation', {
																		initialValue: this.state.selected_user_designation,
																		onChange: (e) => this.handleChange(e, 'designation')
																	})(<AntInput disabled={this.state.block_add_form ? true : false} placeholder="Please enter designation" />
																	)}
																</Form.Item>
															</Col>
														</Row>
														<Row gutter={16} className="border-bot mar-bot-30">
															<Col span={12} className="wid-100">
																<Form.Item label="Email *">
																	{getFieldDecorator('email', {
																		rules: [{ required: true, message: 'Please enter email id' }],
																		initialValue: this.state.selected_user_email,
																		onChange: (e) => this.handleChange(e, 'email')
																	})(<AntInput disabled={this.state.block_add_form ? true : false} placeholder="Please enter Email id" />)}
																</Form.Item>
															</Col>
															<Col span={12} className="wid-100">
																<Form.Item label="Contact No">
																	{getFieldDecorator('phone_no', {
																		rules: [{ required: true, message: 'Please enter contact no.' }],
																		initialValue: this.state.selected_user_phone,
																		onChange: (e) => this.handleChange(e, 'phone_no')
																	})(<AntInput type="number" disabled={this.state.block_add_form ? true : false} placeholder="Please enter contact no." />)}
																</Form.Item>
															</Col>
														</Row>*/}
														<div className='user-add-edit-role'>
														{(() => {
															if (
																(this.props.location.pathname.includes(
																	'/datoms-x'
																) ||
																	this.props.location.pathname.includes(
																		'/iot-platform'
																	)) &&
																this.props.location.pathname.includes(
																	'/customer-management'
																) &&
																app_list_arr.length >
																	1
															) {
																return (
																	<AntRow
																		gutter={
																			16
																		}
																	>
																		<AntCol
																			span={
																				24
																			}
																			className="wid-100"
																		>
																			<Form.Item label="Applications *">
																				{getFieldDecorator(
																					'select',
																					{
																						rules: [
																							{
																								required: true,
																								message:
																									'Please select application!',
																							},
																						],
																						initialValue:
																							this
																								.state
																								.application_id_arr,
																						onChange:
																							(
																								e
																							) =>
																								this.applicationSelect(
																									e
																								),
																					}
																				)(
																					<AntSelect
																						mode="multiple"
																						showSearch
																						optionFilterProp="title"
																					>
																						{(() => {
																							if (
																								app_list_arr.length >
																								0
																							) {
																								return app_list_arr.map(
																									(
																										app
																									) => {
																										return (
																											<AntOption
																												key={
																													app.id
																												}
																												title={
																													app.name
																												}
																												value={
																													app.id
																												}
																											>
																												{
																													app.name
																												}
																											</AntOption>
																										);
																									}
																								);
																							}
																						})()}
																					</AntSelect>
																				)}
																			</Form.Item>
																		</AntCol>
																	</AntRow>
																);
															}
														})()}
														{(() => {
															if (
																this.state
																	.application_id_arr &&
																this.state
																	.application_id_arr
																	.length
															) {
																console.log(
																	'application_id_arr_',
																	this.state
																		.application_id_arr
																);
																return this.state.application_id_arr.map(
																	(
																		app_id
																	) => {
																		let app_details =
																			_find(
																				this
																					.props
																					.all_app_list,
																				{
																					id: app_id,
																				}
																			);
																		let role_det =
																			{};
																		let role_id =
																				'',
																			user_id =
																				undefined,
																			thingArr =
																				[],
																			is_disabled = true;
																		role_det =
																			_find(
																				this
																					.state
																					.application_details_arr,
																				{
																					application_id:
																						app_id,
																				}
																			);
																		if (
																			role_det &&
																			role_det.thing_ids
																		) {
																			thingArr =
																				role_det.thing_ids;
																		}

																		console.log(
																			'role_det_1',
																			role_det
																		);
																		console.log(
																			'role_det_1 application_details_arr',
																			this
																				.state
																				.application_details_arr
																		);
																		console.log(
																			'role_det_1 all_roles_list',
																			this
																				.props
																				.all_roles_list
																		);
																		console.log(
																			'role_det_1 roles_list',
																			this
																				.props
																				.roles_list
																		);
																		console.log(
																			'role_det_1 user_table_data',
																			this
																				.props
																				.user_table_data
																		);
																		let selectedRoleDetail =
																			_find(
																				this
																					.props
																					.role_list,
																				{
																					application_id:
																						app_id,
																				}
																			);
																		if (
																			false /*selectedRoleDetail &&	this.props.all_roles_list && 	this.props.all_roles_list.length && this.props.user_table_data && this.props.user_table_data.length == 0 && ((this.props.vendor_type && !this.props.vendor_type.includes(4)) || ((this.props.customer_type && !this.props.customer_type.includes(5))))*/
																		) {
																			this.props.all_roles_list.map(
																				(
																					role
																				) => {
																					selectedRoleDetail.role_details.map(
																						(
																							role_details
																						) => {
																							if (
																								role.id ==
																									role_details.role_id &&
																								role.role_type ==
																									1
																							) {
																								role_id =
																									role.id;
																							}
																						}
																					);
																				}
																			);
																		}

																		if (
																			role_det
																		) {
																			if (
																				role_id ==
																				''
																			) {
																				role_id =
																					role_det.role_id;

																				if (
																					role_det.role_type !=
																					1
																				) {
																					is_disabled = false;
																				}
																			}

																			user_id =
																				role_det.reports_to !=
																				0
																					? role_det.reports_to
																					: undefined;
																		}
																		console.log(
																			'role_det_1 role_id',
																			role_id
																		);
																		console.log(
																			'role_det_1 role_det',
																			role_det
																		);
																		console.log(
																			'role_det_1 is_disabled',
																			is_disabled
																		);
																		let roleLabel =
																			'Role *';
																		if (
																			this
																				.props
																				.role_list &&
																			this
																				.props
																				.role_list
																				.length >
																				1
																		) {
																			roleLabel =
																				app_details.name +
																				' *';
																		}
																		/*let user_lists = [];
																		if (
																			this
																				.props
																				.user_table_data &&
																			this
																				.props
																				.user_table_data
																				.length
																		) {
																			this.props.user_table_data.map(
																				(
																					user
																				) => {
																					// console.log('user_table_data_', user);
																					let found_app = _find(
																						user.all_role_details,
																						{
																							application_id: app_id,
																						}
																					);
																					// console.log('found_app_', found_app);
																					if (
																						found_app
																					) {
																						if (
																							this
																								.props
																								.match
																								.params
																								.user_id
																						) {
																							if (
																								this
																									.props
																									.match
																									.params
																									.user_id !=
																								user.id
																							) {
																								user_lists.push(
																									{
																										id:
																											user.id,
																										name:
																											user.name,
																									}
																								);
																							}
																						} else {
																							user_lists.push(
																								{
																									id:
																										user.id,
																									name:
																										user.name,
																								}
																							);
																						}
																					}
																				}
																			);
																		}*/

																		return (
																			<AntRow
																				gutter={
																					16
																				}
																			>
																				<AntCol
																					span={
																						24
																					}
																					className="wid-100 select-role"
																				>
																					<Form.Item label="">
																						{(() => {
																							if (
																								(this.props.location.pathname.includes(
																									'/datoms-x'
																								) ||
																									this.props.location.pathname.includes(
																										'/iot-platform'
																									)) &&
																								this.props.location.pathname.includes(
																									'/customer-management'
																								)
																							) {
																								return (
																									<span className="lable-txt mar-rt-20 font-600">
																										{
																											roleLabel
																										}
																									</span>
																								);
																							} else {
																								return (
																									<span className="lable-txt mar-rt-20 font-600">
																										{this.props.t(
																											'role'
																										) +
																											' *'}
																									</span>
																								);
																							}
																						})()}
																						{getFieldDecorator(
																							'selected_role ' +
																								app_id,
																							{
																								rules: [
																									{
																										required: true,
																										message:
																											this.props.t('please_select_a_role')
																											// 'Please select a role',
																									},
																								],
																								initialValue:
																									role_id,
																							}
																						)(
																							<AntSelect
																								t={this.props.t}
																								showSearch
																								value={
																									role_id
																								}
																								optionFilterProp="title"
																								placeholder="Select a Role"
																								onChange={(
																									e
																								) =>
																									this.role_select(
																										e,
																										app_id
																									)
																								}
																							>
																								{(() => {
																									return role_arr.map(
																										(
																											role
																										) => {
																											console.log(
																												'role__',
																												role
																											);
																											if(this.props.client_id === 3725) {
																												const { role_details } = role;
																												if(role_details && role_details.length) {
																													return role_details.map(role_detail => {
																														if([2, 226, 285, 363, 413].includes(role_detail.role_id)) {
																															return <AntOption
																																key={
																																	role_detail.role_id
																																}
																																title={
																																	role_detail.role_name
																																}
																																value={
																																	role_detail.role_id
																																}
																																disabled={
																																	isSelfEditAdmin 
																																}
																															>
																																{
																																	role_detail.role_name
																																}
																															</AntOption>
																														}
																													})
																												}
																											} else if (
																												role.application_id ==
																												app_id
																											) {
																												if (
																													role
																														.role_details
																														.length >
																													0
																												) {
																													return role.role_details
																														.filter(
																															(
																																fil
																															) => {
																																let r_detail =
																																	_find(
																																		this
																																			.props
																																			.all_roles_list,
																																		{
																																			id: fil.role_id,
																																		}
																																	);
																																console.log(
																																	'role__ 1',
																																	r_detail
																																);
																																console.log(
																																	'role__ 2',
																																	this
																																		.props
																																		.plan_appwise_map
																																);

																																if (
																																	r_detail &&
																																	this
																																		.props
																																		.plan_appwise_map[
																																		app_id
																																	]
																																		.pre_defined_role_type_ids &&
																																	!this.props.plan_appwise_map[
																																		app_id
																																	].pre_defined_role_type_ids.includes(
																																		r_detail.role_type
																																	)
																																) {
																																	return false;
																																}
																																return true;
																															}
																														)
																														.map(
																															(
																																role_detail
																															) => {
																																if (
																																	!this.props.accessToVendorManager() &&
																																	this
																																		.props
																																		.logged_in_user_role_type !=
																																		1 &&
																																	this
																																		.props
																																		.all_roles_list &&
																																	this
																																		.props
																																		.all_roles_list
																																		.length
																																) {
																																	let selectedRoleDet =
																																		_find(
																																			this
																																				.props
																																				.all_roles_list,
																																			{
																																				id: role_detail.role_id,
																																			}
																																		);

																																	if (
																																		selectedRoleDet &&
																																		(selectedRoleDet.role_type !=
																																			1 ||
																																			this.props.location.pathname.includes(
																																				'/customer-management'
																																			))
																																	) {
																																		console.log(
																																			'selectedRoleDet_',
																																			this
																																				.props
																																				.vendor_type
																																		);
																																		return (
																																			<AntOption
																																				key={
																																					role_detail.role_id
																																				}
																																				title={
																																					role_detail.role_name
																																				}
																																				value={
																																					role_detail.role_id
																																				}
																																				disabled={
																																					isSelfEditAdmin /*(is_disabled && (this.props.user_table_data && this.props.user_table_data.length == 0 || (this.props.user_table_data && this.props.user_table_data.length == 1 && this.props.location.pathname.includes('/edit'))) && ((this.props.vendor_type && !this.props.vendor_type.includes(4)) || ((this.props.customer_type && !this.props.customer_type.includes(5)))) ? true : false)*/
																																				}
																																			>
																																				{
																																					// Here
																																					this.props.t? this.props.t(role_detail.role_name): role_detail.role_name
																																				}
																																			</AntOption>
																																		);
																																	}
																																} else {
																																	console.log(
																																		'selectedRoleDet_',
																																		this
																																			.props
																																			.vendor_type
																																	);
																																	return (
																																		<AntOption
																																			key={
																																				role_detail.role_id
																																			}
																																			title={
																																				role_detail.role_name
																																			}
																																			value={
																																				role_detail.role_id
																																			}
																																			disabled={
																																				isSelfEditAdmin /*(is_disabled && (this.props.user_table_data && this.props.user_table_data.length == 0 || (this.props.user_table_data && this.props.user_table_data.length == 1 && this.props.location.pathname.includes('/edit'))) && ((this.props.vendor_type && !this.props.vendor_type.includes(4)) || ((this.props.customer_type && !this.props.customer_type.includes(5)))) ? true : false)*/
																																			}
																																		>
																																			{
																																				role_detail.role_name
																																			}
																																		</AntOption>
																																	);
																																}
																															}
																														);
																												}
																											}
																										}
																									);
																								})()}
																							</AntSelect>
																						)}
																					</Form.Item>
																				</AntCol>
																				{this.props.location.pathname.includes(
																							'/customer-management'
																						) &&
																						app_id !=
																							17 && (
																						territoryFormItem(isSelfEditAdmin)
																				)}
																				{this.props.location.pathname.includes(
																							'/customer-management'
																						) &&
																						app_id !=
																							17 && (
																					<AntCol
																						span={
																							16
																						}
																						className="wid-100"
																					>
																						{
																							siteFormItem
																						}
																					</AntCol>
																				)}
																				{(() => {
																					// gdaiComeback
																					if (
																						this.props.location.pathname.includes(
																							'/customer-management'
																						) &&
																						app_id !=
																							17 &&
																						this
																							.props
																							.plan_appwise_map[
																							app_id
																						]
																							.resorce_alocation
																					) {
																						return (
																							<AntCol
																								span={
																									16
																								}
																								className="wid-100 select-role"
																							>
																								<Form.Item label="">
																									<span className="lable-txt mar-rt-20 font-600">
																										Select
																										Assets
																									</span>
																									{/* {getFieldDecorator(
																										'selected_thing ' +
																											app_id,
																										{
																											rules: [
																												{
																													required: false,
																													message:
																														'Please select assets',
																												},
																											],
																											initialValue: this.getThingArrayValue(
																												app_id
																											),
																										}
																									)( */}
																									<AntSelect
																										showSearch
																										mode="multiple"
																										value={getFinalSelectedThings(
																											this.getThingArrayValue(
																												app_id
																											),
																											this
																												.state
																												.siteAssets,
																											this.state.territory_things
																										)}
																										optionFilterProp="title"
																										placeholder="Select asset"
																										disabled={
																											isSelfEditAdmin
																										}
																										onChange={(
																											e
																										) =>
																											this.selectThings(
																												e,
																												app_id
																											)
																										}
																									>
																										{/*(() => {
																											let thingArrAll = [];
																											if (
																												this
																													.props
																													.thing_list_obj &&
																												this
																													.props
																													.thing_list_obj[
																													app_id
																												]
																											) {
																												thingArrAll = this
																													.props
																													.thing_list_obj[
																													app_id
																												];
																											}
																											if (
																												//	thingArrAll.length &&
																												this
																													.props
																													.logged_in_user_role_type &&
																												(parseInt(
																													this
																														.props
																														.logged_in_user_role_type
																												) ==
																													1 ||
																													this.props.accessToVendorManager())
																											) {
																												return (
																													
																												);
																											}
																										})()*/}
																										<AntOption
																											key="*"
																											title="all"
																											disabled={
																												this
																													.state
																													.applicaiton_wise_role_check?.[
																													app_id
																												]
																											}
																											value="*"
																										>
																											All
																										</AntOption>
																										{(() => {
																											let thingArrOption =
																												[];
																											if (
																												this
																													.props
																													.thing_list_obj &&
																												this
																													.props
																													.thing_list_obj[
																													app_id
																												]
																											) {
																												thingArrOption =
																													this
																														.props
																														.thing_list_obj[
																														app_id
																													];
																											}
																											return thingArrOption.map(
																												(
																													thingDet
																												) => {
																													return (
																														<AntOption
																															key={
																																thingDet.id
																															}
																															title={
																																thingDet.name
																															}
																															value={
																																thingDet.id
																															}
																															disabled={thingArr.includes(
																																'*'
																															) ||
																															this.state.siteAssets?.includes(
																																thingDet.id
																															) ||
																															this.state.territory_things?.includes(
																																thingDet.id
																															)}
																														>
																															{
																																thingDet.name
																															}
																														</AntOption>
																													);
																												}
																											);
																										})()}
																									</AntSelect>
																									{/* )} */}
																								</Form.Item>
																							</AntCol>
																						);
																					}
																				})()}
																			</AntRow>
																		);
																	}
																);
															}
														})()}
														{this.isRoleTracking() && (
															<AntRow gutter={16}>
																<AntCol
																	span={24}
																	className="wid-100"
																>
																	<Form.Item label="Employee Id">
																		{getFieldDecorator(
																			'employee_id',
																			{
																				rules: [
																					{
																						required: false, //!this.state.tracking_id,
																						message:
																							'Please enter employee id',
																					},
																				],
																				initialValue:
																					this
																						.state
																						.employee_id,
																				onChange:
																					(
																						e
																					) =>
																						this.changeEmployeeId(
																							e
																						),
																			}
																		)(
																			<AntInput placeholder="Enter employee id" />
																		)}
																	</Form.Item>
																</AntCol>
																<AntCol
																	span={24}
																	className="wid-100"
																>
																	<Form.Item label="Tracking Id ">
																		{getFieldDecorator(
																			'tracking_id',
																			{
																				rules: [
																					{
																						required: false, //!this.state.employee_id,
																						message:
																							'Please enter tracking id',
																					},
																				],
																				initialValue:
																					this
																						.state
																						.tracking_id,
																				onChange:
																					(
																						e
																					) =>
																						this.changeTrackingId(
																							e
																						),
																			}
																		)(
																			<AntInput placeholder="Enter tracking id" />
																		)}
																	</Form.Item>
																</AntCol>
															</AntRow>
														)}
														{(() => {
															if (
																(this.props.location.pathname.includes(
																	'/datoms-x'
																) ||
																	this.props.location.pathname.includes(
																		'/iot-platform'
																	)) &&
																!this.props.location.pathname.includes(
																	'/customer-management'
																) &&
																app_list_arr.length >
																	1
															) {
																return (
																	<AntRow
																		gutter={
																			16
																		}
																	>
																		<AntCol
																			span={
																				24
																			}
																			className="wid-100"
																		>
																			<Form.Item
																				className="font-600"
																				label="Applications *"
																			>
																				<AntSelect
																					mode="multiple"
																					showSearch
																					disabled={
																						this
																							.state
																							.super_admin ||
																						isSelfEditAdmin
																					}
																					optionFilterProp="title"
																					value={
																						this
																							.state
																							.application_access_arr
																					}
																					placeholder="Select Applications"
																					onChange={(
																						e
																					) =>
																						this.accessApplicationSelect(
																							e
																						)
																					}
																				>
																					{(() => {
																						if (
																							app_list_arr.length >
																							0
																						) {
																							return app_list_arr.map(
																								(
																									app
																								) => {
																									return (
																										<AntOption
																											key={
																												app.id
																											}
																											title={
																												app.name
																											}
																											disabled={
																												this
																													.state
																													.all_selected_app
																											}
																											value={
																												app.id
																											}
																										>
																											{
																												app.name
																											}
																										</AntOption>
																									);
																								}
																							);
																						}
																					})()}
																				</AntSelect>
																			</Form.Item>
																		</AntCol>
																	</AntRow>
																);
															}
														})()}
														{(() => {
															console.log(
																'territory_ids__',
																this.state
																	.territory_ids
															);
															if (
																// this.props
																// 	.isTerritoryEnabled
																// 	 &&
																this.handleTerritoryChange() ||
																this.props
																.application_id ===
																16
															) {
																return territoryFormItem(isSelfEditAdmin);
															}
														})()}

														{this.props
															.application_id ===
															16 && (
															<AntRow gutter={16}>
																<AntCol
																	span={24}
																	className="wid-100"
																>
																	{siteFormItem}
																</AntCol>
															</AntRow>
														)}
														{(() => {
															let label =
																this.props.t? this.props.t('assets'): "Assets";
																// 'Assets';
															if (
																this.props.location.pathname.includes(
																	'/delivery-tracking/'
																)
															) {
																label =
																	this.props.t(
																		'loggers'
																	);
															}
															if (
																!this.props.location.pathname.includes(
																	'/datoms-x'
																) &&
																!this.props.location.pathname.includes(
																	'/iot-platform'
																) &&
																this.props.location.pathname.includes(
																	'/user-management'
																) &&
																((this.props
																	.plan_description &&
																	this.props
																		.plan_description
																		.resorce_alocation !=
																		undefined &&
																	this.props
																		.plan_description
																		.resorce_alocation ==
																		true) ||
																	(this.props
																		.plan_description &&
																		this
																			.props
																			.plan_description
																			.resorce_alocation ==
																			undefined) ||
																	!this.props
																		.plan_description)
															) {
																return (
																	<AntRow
																		gutter={
																			16
																		}
																	>
																		<AntCol
																			span={
																				24
																			}
																			className="wid-100"
																		>
																			<Form.Item
																				className="font-600"
																				label={
																					label
																				}
																			>
																				<AntSelect
																					t={this.props.t}
																					mode="multiple"
																					showSearch
																					value={getFinalSelectedThings(
																						this
																							.state
																							.selected_things_arr,
																						this
																							.state
																							.siteAssets,
																						this.state.territory_things
																					)}
																					optionFilterProp="title"
																					showArrow={
																						true
																					}
																					placeholder= {this.props.t('select_assets')}
																					// placeholder="Select Assets"
																					disabled={
																						isSelfEditAdmin
																					}
																					onChange={(
																						e
																					) =>
																						this.thingsSelect(
																							e
																						)
																					}
																				>
																					<AntOption
																						key="*"
																						title="all"
																						disabled={
																							this
																								.state
																								.super_admin
																						}
																						value="*"
																					>
																						{this.props.t? this.props.t('all'): "All"}
																						{/* All */}
																					</AntOption>
																					{(() => {
																						if (
																							this
																								.props
																								.things_list &&
																							this
																								.props
																								.things_list
																								.length >
																								0
																						) {
																							return this.props.things_list.map(
																								(
																									things
																								) => {
																									return (
																										<AntOption
																											key={
																												things.id
																											}
																											title={
																												things.name
																											}
																											disabled={
																												this
																													.state
																													.all_selected_thing ||
																												this.state.siteAssets?.includes(
																													things.id
																												) ||
																												this.state.territory_things?.includes(
																													things.id
																												)
																											}
																											value={
																												things.id
																											}
																										>
																											{
																												things.name
																											}
																										</AntOption>
																									);
																								}
																							);
																						}
																					})()}
																				</AntSelect>
																			</Form.Item>
																		</AntCol>
																	</AntRow>
																);
															}
														})()}
														{(() => {
															if (
																this.handleTerritoryChange()
															) {
																return (
																	<AntRow
																		gutter={
																			16
																		}
																	>
																		<AntCol
																			span={
																				24
																			}
																			className="wid-100"
																		>
																			<Form.Item
																				className="font-600"
																				label={this.props.t? this.props.t('customers'): "Customers"}
																				// label="Customers"
																			>
																				<AntSelect
																					t={this.props.t}
																					mode="multiple"
																					showSearch
																					// disabled={
																					// 	this
																					// 		.state
																					// 		.super_admin
																					// }
																					disabled={
																						isSelfEditAdmin
																					}
																					optionFilterProp="title"
																					maxTagCount={
																						5
																					}
																					maxTagPlaceholder={
																						'+ ' +
																						(
																							[
																								...new Set(
																									[
																										...this
																											.state
																											.customer_list,
																										...this
																											.state
																											.territory_customers,
																									]
																								),
																							]
																								.length -
																							5
																						).toString() +
																						' more'
																					}
																					value={
																						this.isTerritoryAdmin() ||
																						this
																							.state
																							.all_selected_industry
																							? this
																									.state
																									.customer_list
																							: [
																									...new Set(
																										[
																											...this
																												.state
																												.customer_list,
																											...this
																												.state
																												.territory_customers,
																										]
																									),
																							  ]
																					}
																					placeholder={this.props.t? this.props.t('select_customers'): "Select Customers"}
																					// placeholder="Select Customers"
																					showArrow={
																						true
																					}
																					onChange={(
																						e
																					) =>
																						this.customerSelect(
																							e
																						)
																					}
																				>
																					{/*(() => {
																						// let selected_role = _find(
																						// 	this
																						// 		.props
																						// 		.all_roles_list,
																						// 	{
																						// 		id: this
																						// 			.state
																						// 			.role_id,
																						// 	}
																						// );
																						// let selected_type_id = 0;
																						// if (
																						// 	selected_role &&
																						// 	selected_role.role_type &&
																						// 	selected_role.role_type ==
																						// 		1
																						// ) {
																						// 	selected_type_id =
																						// 		selected_role.role_type;
																						// }
																						if (
																							this
																								.props
																								.customer_list_arr &&
																							this
																								.props
																								.customer_list_arr
																								.length &&
																							this
																								.props
																								.logged_in_user_role_type !=
																								undefined &&
																							parseInt(
																								this
																									.props
																									.logged_in_user_role_type
																							) ==
																								1
																							// selected_type_id ==
																							// 	1
																						) {
																							return (
																								
																							);
																						}
																					})()*/}
																					<AntOption
																						key="*"
																						title="all"
																						disabled={
																							this
																								.state
																								.super_admin
																						}
																						value="*"
																					>
																						{this.props.t? this.props.t('all'): "All"}
																						{/* All */}
																					</AntOption>
																					{/* {(() => {
																						return (
																							<AntOption
																								key="*"
																								title="all"
																								disabled={
																									this
																										.state
																										.super_admin
																								}
																								value="*"
																							>
																								All
																							</AntOption>
																						);
																					})()} */}
																					{(() => {
																						if (
																							this
																								.props
																								.isTerritoryEnabled &&
																							!this
																								.state
																								.all_selected_industry &&
																							this
																								.props
																								.customer_list_arr &&
																							this
																								.props
																								.customer_list_arr
																								.length >
																								0 &&
																							this
																								.state
																								.territory_customers &&
																							this
																								.state
																								.territory_customers
																								.length &&
																							!this.isTerritoryAdmin()
																						) {
																							return this.props.customer_list_arr
																								.filter(
																									(
																										ter
																									) =>
																										this.state.territory_customers.includes(
																											ter.id
																										)
																								)
																								.map(
																									(
																										cust
																									) => {
																										return (
																											<AntOption
																												key={
																													cust.id
																												}
																												disabled={
																													true
																												}
																												title={
																													cust.name
																												}
																												value={
																													cust.id
																												}
																											>
																												{
																													cust.name
																												}
																											</AntOption>
																										);
																									}
																								);
																						}
																					})()}
																					{(() => {
																						if (
																							this
																								.props
																								.customer_list_arr &&
																							this
																								.props
																								.customer_list_arr
																								.length >
																								0
																						) {
																							return this.props.customer_list_arr
																								.filter(
																									(
																										ter
																									) => {
																										if (
																											this
																												.props
																												.isTerritoryEnabled &&
																											!this
																												.state
																												.all_selected_industry &&
																											!this.isTerritoryAdmin() &&
																											this
																												.state
																												.territory_customers &&
																											this
																												.state
																												.territory_customers
																												.length &&
																											this.state.territory_customers.includes(
																												ter.id
																											)
																										) {
																											return false;
																										}
																										return true;
																									}
																								)
																								.map(
																									(
																										cust
																									) => {
																										return (
																											<AntOption
																												key={
																													cust.id
																												}
																												disabled={
																													this
																														.state
																														.all_selected_industry &&
																													!isSelfEditAdmin
																												}
																												title={
																													cust.name
																												}
																												value={
																													cust.id
																												}
																											>
																												{
																													cust.name
																												}
																											</AntOption>
																										);
																									}
																								);
																						}
																					})()}
																				</AntSelect>
																			</Form.Item>
																		</AntCol>
																	</AntRow>
																);
															}
														})()}
														</div>
														{/*(() => {
												if ((this.props.location.pathname.includes('/datoms-x') || this.props.location.pathname.includes('/iot-platform')) && !this.props.location.pathname.includes('/customer-management')) {
													return <Row gutter={16}>
														<Col span={14} className="wid-100">
															<Form.Item label="Industry Sets">
																<AntSelect
																	mode="multiple"
																	showSearch
																	optionFilterProp="title"
																	value={this.state.customer_set_list}
																	placeholder="Select Industry Set"
																	onChange={(e) => this.customer_set_select(e)}
																>
																	{(() => {
																		if (this.state.customer_list_arr && this.state.customer_list_arr.length > 0) {
																			return <AntOption key='*' title='all' value='*'>All</AntOption>
																		}
																	})()}
																	{(() => {
																		if (this.state.customer_set_list_arr && this.state.customer_set_list_arr.length > 0) {
																			return this.state.customer_set_list_arr.map((set) => {
																				return <AntOption key={set.id} disabled={this.state.all_selected_set} title={set.name} value={set.id}>{set.name}</AntOption>
																			});
																		}
																	})()}
																</AntSelect>
															</Form.Item>
														</Col>
													</Row>
												}
											})()*/}
													</div>
												);
											}
										})()}
									</Form>
								</div>
							);
						} else if (this.state.unauthorised_access) {
							return (
								<AntRow
									type="flex"
									justify="space-around"
									className="device-details"
								>
									<div className="no-data-text">
										<AntAlert
											message="Access Denied"
											description={
												this.state
													.unauthorised_access_msg
											}
											type="error"
										/>
									</div>
								</AntRow>
							);
						} else {
							return (
								<div className="content-body">
									<Loading />
								</div>
							);
						}
					})()}
					{(() => {
						if (window.innerWidth > 576) {
							return (
								<div
									className="drawer-actions-box"
									style={{
										display: 'flex',
										padding: '26px 40px 0px 16px',
										float: 'right',
									}}
								>
									<AntButton
										type={'custom'}
										buttons={cancelButton}
										onButtonClick={() =>
											this.onUserDrawerClose()
										}
									/>
									<AntButton
										type={'custom'}
										buttons={submitButton}
										onButtonClick={() =>
											this.submitUserForm()
										}
									/>
								</div>
							);
						} else {
							return (
								<OkCloseFooter
									okText="Submit"
									onOk={() => this.submitUserForm()}
									okLoading={this.state.butn_load}
									onClose={() => this.onUserDrawerClose()}
								/>
							);
						}
					})()}
				</AntDrawer>
			);
		}
	}
);

export default AddUserDrawer;
