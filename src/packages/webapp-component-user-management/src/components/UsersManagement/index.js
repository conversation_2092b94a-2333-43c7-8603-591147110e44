import React, { Suspense, lazy } from 'react';
import MoreOutlined from '@ant-design/icons/MoreOutlined';
import RedoOutlined from '@ant-design/icons/RedoOutlined';
import DeleteOutlined from '@ant-design/icons/DeleteOutlined';
import CheckCircleOutlined from '@ant-design/icons/CheckCircleOutlined';
import CloseCircleOutlined from '@ant-design/icons/CloseCircleOutlined';
import AntButton from '@datoms/react-components/src/components/AntButton';
import Loading from '@datoms/react-components/src/components/Loading';
import AntRow from '@datoms/react-components/src/components/AntRow';
import AntCol from '@datoms/react-components/src/components/AntCol';
import AntLayout from '@datoms/react-components/src/components/AntLayout';
import AntContent from '@datoms/react-components/src/components/AntContent';
import AntAlert from '@datoms/react-components/src/components/AntAlert';
import AntMenu from '@datoms/react-components/src/components/AntMenu';
import AntMenuItem from '@datoms/react-components/src/components/AntMenuItem';
import AntTag from '@datoms/react-components/src/components/AntTag';
import AntDivider from '@datoms/react-components/src/components/AntDivider';
import Text from '@datoms/react-components/src/components/Text';
import AntDropdown from '@datoms/react-components/src/components/AntDropdown';
import SearchInput from '@datoms/react-components/src/components/SearchInput';
import AntNotification from '@datoms/react-components/src/components/AntNotification';
import AntModal from '@datoms/react-components/src/components/AntModal';
import AntSelect from '@datoms/react-components/src/components/AntSelect';
import AntOption from '@datoms/react-components/src/components/AntOption';
import AntConfirmModal from '@datoms/react-components/src/components/AntConfirmModal';
import AntSpin from '@datoms/react-components/src/components/AntSpin';
import AntTooltip from '@datoms/react-components/src/components/AntTooltip';
import AntPasswordModal from '@datoms/react-components/src/components/AntPasswordModal';
import NoDataComponent from '@datoms/react-components/src/components/NoDataComponent';
import AddPopover from '@datoms/react-components/src/components/AddPopover';
import AntDrawer from '@datoms/react-components/src/components/AntDrawer';
import WithSuspense from '@datoms/react-components/src/components/WithSuspense';
import {
	retriveCustomerDetails,
	retriveCustomerList,
	retriveThingsList,
	retriveUsers,
	deleteUser,
	updateUserStatus,
	retriveRoles,
	retrieveTerritoryList,
	resendInvitation,
} from '@datoms/js-sdk';
import { TimeFormatter } from '@datoms/js-utils/src/TimeFormatting.js';
import UserManagementObjectData from './configuration/defaultConfigs';
import MobileUserList from './imports/MobileUserList/index';
import UserDetails from './imports/UserDetails/UserDetails';
import deactivateUserWhiteImage from '../../imgs/deactivate-user-white.svg';
import activateUserWhiteImage from '../../imgs/activate-user-white.svg';
import _find from 'lodash/find';
import _filter from 'lodash/filter';
import _uniqBy from 'lodash/uniqBy';
import _some from 'lodash/some';
import moment from 'moment';
import 'moment-timezone';
import './style.less';
import { Form } from '@ant-design/compatible';
import { fetchSitesList, getSiteNames, getTerritoryResources } from './imports/user-add-edit-logic';
import EmailVerificationStatus from './imports/EmailVerificationStatus';
import _ from 'lodash';

const AddUserDrawer = lazy(() => import('./imports/AddUserDrawer'));
const RoleListDrawer = lazy(() => import('./imports/RoleListDrawer'));
const CustomTable = lazy(() => import('./imports/CustomTable'));

import queryString from 'query-string';

export default class UsersManagement extends React.Component {
	constructor(props) {
		super(props);

		let app_list_arr = [],
			app_drop = 'all',
			show_add_edit_role = true;

		if (
			props.details_customer_data &&
			props.details_customer_data.application_details &&
			props.details_customer_data.application_details.length > 0
		) {
			props.details_customer_data.application_details.map(
				(application_detail) => {
					let appl_list = _find(props.all_app_list, {
						id: application_detail.application_id,
					});
					if (appl_list) {
						app_list_arr.push({
							id: appl_list.id,
							name: appl_list.name,
						});
					}
				}
			);
		}

		if (app_list_arr.length > 1) {
			app_drop = 'all';
		} else if (app_list_arr.length == 1) {
			app_drop = app_list_arr[0].id;
		}

		if (this.props.location.pathname.search('/customer-management') > -1) {
			if (props.match.params.app_id != 'all') {
				app_drop = parseInt(props.match.params.app_id);
			}
		}

		if (app_drop == 'all') {
			show_add_edit_role = false;
		}

		this.parsed_search = queryString.parse(props.location.search);

		this.state = {
			client_id: props.location.pathname.includes('/customer-management/')
				? props.match.params.customer_id
				: props.client_id,
			app_drop: props.location.pathname.includes('/customer-management/')
				? app_drop
				: props.application_id,
			UserManagementObjectData: UserManagementObjectData,
			loading: false,
			active_application: props.match.params.application_id
				? props.match.params.application_id
				: null,
			collapse: true,
			view_user: false,
			view_user_id:
				props.match.params && props.match.params.user_id
					? props.match.params.user_id
					: 0,
			selected_application_id_arr: props.location.pathname.includes(
				'/customer-management/'
			)
				? []
				: [props.application_id],
			selected_user: [],
			all_app_list: [],
			selected_application: [],
			table_data: [],
			show_add_edit_role: props.location.pathname.includes(
				'/user-management/'
			)
				? true
				: show_add_edit_role,
			access_key: [
				'UserManagement:UserManagement',
				'UserManagement:ManageUser',
			],
			show_add_draw: props.location.pathname.includes('/add')
				? true
				: undefined,
			roleDetailsVisible: false,
			table_load: true,
			search_value:
				this.parsed_search &&
				Object.values(this.parsed_search).length &&
				this.parsed_search.user_search
					? this.parsed_search.user_search
					: '',
			page_type: 'users',
			plan_description: this.props.plan_description,
			plan_appwise_map: {},
			resend_loading: {},
			show_user_details: this.props.location.pathname.includes(
				'/users/' + this.props.match.params.user_id + '/view'
			),
		};
		this.isTerritoryEnabled = (props.enabled_features?.includes(
			'UserManagement:Territory'
		) || (props.client_id === 1 && props.location.pathname.includes('/user-management'))) && props.getViewAccess(['TerritoryManagement:View']);
		console.log("Territory Test ==> ", props.enabled_features, props.getViewAccess(['TerritoryManagement:View']))
		this.showSiteField =
			(props.application_id === 16 || this.state.app_drop === 16) &&
			props.enabled_features?.includes('SiteManagement:SiteManagement');
		this.fetchSitesList = fetchSitesList.bind(this);
		this.getSiteNames = getSiteNames.bind(this);

		let application_slug = '';
		this.platform_slug = '';
		if (import.meta.env.VITE_MOBILE || import.meta.env.VITE_DESKTOP) {
			this.platform_slug = '/' + this.props.app_name;
		} else {
			if (import.meta.env.VITE_BUILD_MODE !== 'development') {
				application_slug = window.location.pathname.split('/')[3];
				if (application_slug && typeof application_slug === 'string') {
					this.platform_slug = '/' + application_slug;
				}
			} else {
				application_slug = window.location.pathname.split('/')[1];
				if (application_slug && typeof application_slug === 'string') {
					this.platform_slug = '/' + application_slug;
				}
			}
		}

		this.iotBasePath = 'https://app.datoms.io/enterprise/1/datoms-x';

		if (
			this.props.application_id == 16 &&
			props?.logged_in_user_client_id === props?.client_id &&
			!import.meta.env.VITE_MOBILE &&
			!import.meta.env.VITE_DESKTOP
		) {
			this.platform_slug = '';
			application_slug = '';

			if (props.location.pathname.includes('/users/view')) {
				if (this.state.search_value !== '') {
					props.history.push(
						'/user-management/users/view?user_search=' +
							this.state.search_value
					);
				} else {
					props.history.push('/user-management/users/view');
				}
			} else if (props.location.pathname.includes('/add')) {
				props.history.push('/user-management/users/add');
			} else if (props.location.pathname.includes('/roles/view')) {
				props.history.push('/user-management/roles/view');
			} else {
				let page = window.location.pathname.split('users')[1];
				props.history.push('/user-management/users' + page);
			}
		}

		if (
			!import.meta.env.VITE_MOBILE &&
			!import.meta.env.VITE_DESKTOP &&
			typeof window !== undefined &&
			!window.location.href.includes('localhost')
		) {
			this.iotBasePath =
				window.location.protocol +
				'//' +
				window.location.host +
				'/enterprise/1/datoms-x';
		} else if (
			typeof window !== undefined &&
			window.location.href.includes('localhost')
		) {
			this.iotBasePath = 'http://localhost:3000/datoms-x';
		}

		// this.platform_slug = props.location && props.location.pathname.includes('/datoms-x/') ? '/datoms-x' : (props.location.pathname.includes('/iot-platform/') ? '/iot-platform' : props.location.pathname.includes('/dg-monitoring/') ? '/dg-monitoring' : '/delivery-tracking');
		/*if (props.location.pathname.includes('/iot-platform') || props.location.pathname.includes('/datoms-x')) {
			this.user_slug = '/settings/user-management';
		} else {
			this.user_slug = '/user-management';
		}*/
		this.user_slug = '/user-management';

		console.log('user_mgt_props', this.props);
		// console.log('UserManagementObjectData', UserManagementObjectData);
		this.closeRoleDrawer = this.closeRoleDrawer.bind(this);
		this.editUser = this.editUser.bind(this);
		this.viewUser = this.viewUser.bind(this);
		this.showStatusConfirm = this.showStatusConfirm.bind(this);
		this.onUserDetailsDrawerClose =
			this.onUserDetailsDrawerClose.bind(this);
		this.showDeleteConfirm = this.showDeleteConfirm.bind(this);
		this.resendLink = this.resendLink.bind(this);
		this.closeUserDraw = this.closeUserDraw.bind(this);
		this.accessToVendorManager = this.accessToVendorManager.bind(this);
	}

	changeLanguage(lng, LoggerObjectData) {
		const { t, i18n } = this.props;
		i18n.changeLanguage(lng);
	}

	isLoginAllowed(role_details) {
		return role_details?.[0]
			? Object.keys(role_details[0]) &&
			  Object.keys(role_details[0]).includes('login_allowed')
				? role_details[0].login_allowed
					? true
					: false
				: true
			: true;
	}

	translateFunction(t, UserManagementObjectData) {
		let menuItemsArray = [];
		let headDataArray = [];
		if (
			UserManagementObjectData &&
			UserManagementObjectData.application_user_table &&
			UserManagementObjectData.application_user_table.head_data &&
			UserManagementObjectData.application_user_table.head_data.length
		) {
			UserManagementObjectData.application_user_table.head_data.map(
				(head_data) => {
					headDataArray.push({
						title:
							head_data.key === 'name'
								? t('name')
								: head_data.key === 'role'
								? t('role')
								: head_data.key === 'things'
								? this.props.application_id == 20
									? t('loggers')
									: t("assets")
								: head_data.key === 'report_to'
								? t('reports_to')
								: head_data.key === 'action'
								? t('action')
								: head_data.title,
						dataIndex: head_data.dataIndex,
						key: head_data.key,
						sorter: head_data.sorter,
						width: head_data.width,
						align: head_data.align,
						render: head_data.render,
					});
				}
			);
			UserManagementObjectData.application_user_table.head_data =
				headDataArray;
		}
		let customerDetailsForm = [];
		if (
			UserManagementObjectData &&
			UserManagementObjectData.add_user_form &&
			UserManagementObjectData.add_user_form.customer_details_form &&
			UserManagementObjectData.add_user_form.customer_details_form.length
		) {
			UserManagementObjectData.add_user_form.customer_details_form.map(
				(customer_details_form) => {
					customerDetailsForm.push({
						key: customer_details_form.key,
						label:
							customer_details_form.key === 'first_name'
								? t('full_name') + " *"
								: customer_details_form.key === 'last_name'
								? t('last_name')
								: customer_details_form.key === 'designation'
								? t('designation')
								: customer_details_form.key === 'email'
								? t('email')
								: customer_details_form.key === 'phone_no'
								? this.props.client_id == 392
									? t('mobile')
									: t('mobile')
								: '',
						required: customer_details_form.required,
						message:
							customer_details_form.key === 'first_name'
								? t('please_enter_first_name')
								: customer_details_form.key === 'last_name'
								? t('please_enter_last_name')
								: customer_details_form.key === 'designation'
								? t('please_enter_designation')
								: customer_details_form.key === 'email'
								? t('please_enter_email')
								: customer_details_form.key === 'phone_no'
								? this.props.client_id == 392
									? t('Mobile No')
									: t('whatsapp_no')
								: '',
						initial_value: customer_details_form.initial_value,
					});
				}
			);
			UserManagementObjectData.add_user_form.customer_details_form =
				customerDetailsForm;
		}
		if (
			UserManagementObjectData &&
			UserManagementObjectData.add_user_form &&
			UserManagementObjectData.add_user_form.draw_buttons
		) {
			if (
				UserManagementObjectData.add_user_form.draw_buttons.submit &&
				UserManagementObjectData.add_user_form.draw_buttons.submit
					.length
			) {
				UserManagementObjectData.add_user_form.draw_buttons.submit[0].text =
					t('submit');
			}
			if (
				UserManagementObjectData.add_user_form.draw_buttons.cancel &&
				UserManagementObjectData.add_user_form.draw_buttons.cancel
					.length
			) {
				UserManagementObjectData.add_user_form.draw_buttons.cancel[0].text =
					t('cancel');
			}
		}
		if (
			UserManagementObjectData &&
			UserManagementObjectData.user_add_button
		) {
			UserManagementObjectData.user_add_button[0].text = t('add_user');
		}
		if (UserManagementObjectData && UserManagementObjectData.role_button) {
			UserManagementObjectData.role_button[0].text = t('roles');
		}
		if (
			UserManagementObjectData &&
			UserManagementObjectData.role_view_draw &&
			UserManagementObjectData.role_view_draw.add_button &&
			UserManagementObjectData.role_view_draw.add_button.text
		) {
			UserManagementObjectData.role_view_draw.add_button.text =
				t('new_role');
		}
		if (
			UserManagementObjectData &&
			UserManagementObjectData.role_view_draw &&
			UserManagementObjectData.role_view_draw.add_role_form &&
			UserManagementObjectData.role_view_draw.add_role_form.draw_buttons
		) {
			if (
				UserManagementObjectData.role_view_draw.add_role_form
					.draw_buttons.submit &&
				UserManagementObjectData.role_view_draw.add_role_form
					.draw_buttons.submit.length
			) {
				UserManagementObjectData.role_view_draw.add_role_form.draw_buttons.submit[0].text =
					t('submit');
			}
			if (
				UserManagementObjectData.role_view_draw.add_role_form
					.draw_buttons.cancel &&
				UserManagementObjectData.role_view_draw.add_role_form
					.draw_buttons.cancel.length
			) {
				UserManagementObjectData.role_view_draw.add_role_form.draw_buttons.cancel[0].text =
					t('cancel');
			}
		}
	}

	//get territory data
	async getTerritoryData() {
		let response = await retrieveTerritoryList(this.state.client_id); //territoryList;
		if (
			response.status === 'success' &&
			response.data &&
			response.data.length
		) {
			let treeData = this.convertTerritoryDataToTreeData(response.data, this.props.t);
			this.setState({
				territoryData: response.data,
				treeData,
			});
		} else {
			let territoryData =
					//terDataDummy.data,
					[
						{
							id: 1,
							name: '',
							parent_id: 0,
							user_ids: [],
							customer_ids: [],
							total_customer_count: 0,
						},
					],
				treeData = this.convertTerritoryDataToTreeData(territoryData, this.props.t);
			this.setState({
				territoryData,
				treeData,
			});
		}
	}

	//convert territory data to tree data
	convertTerritoryDataToTreeData(territoryData) {
		const territoryWiseSites = {};
		const territoryWiseAssets = {};
		let responseData = JSON.parse(JSON.stringify(territoryData)),
			parent_id,
			parentNodeIds = this.getParentNodeIds(responseData),
			treeData = responseData.map((item, index) => {
				parent_id = parentNodeIds.nodeIds.find(
					(id) => id === item.parent_id
				);
				console.log('parent_id', parent_id);
				if (item.parent_id === 0) {
					parent_id = 0;
				} else if (!parent_id) {
					parent_id = parentNodeIds.topNodeId;
				}

				const territoryResources = getTerritoryResources(item.id, ["site_ids", "thing_ids"], responseData);
				console.log("territoryResources", item.id, territoryResources)
				territoryWiseSites[item.id] = territoryResources.site_ids || [];
				territoryWiseAssets[item.id] = territoryResources.thing_ids || [];

				return {
					title: item.name, //`${item.name} (${item.total_customer_count})`,
					key: item.id,
					value: item.id,
					parent_id,
					user_ids: item.user_ids,
					customer_ids: item.customer_ids,
					disabled:
						item.parent_id == 0 && !this.isUserAdmin()
							? true
							: false,
				};
			});
		this.setState({ topNodeId: parentNodeIds.topNodeId, territoryWiseSites, territoryWiseAssets });
		//create a id-index mapping for reference later in the code
		const idIndexMap = treeData.reduce((acc, el, i) => {
			acc[el.key] = i;
			return acc;
		}, {});
		let root;
		console.log('tree-data', treeData, parentNodeIds);
		treeData.forEach((element) => {
			// condition for root element
			if (element.parent_id === 0) {
				root = element;
				return;
			}
			// using id-index mapping to locate the parent element in converted data array
			const parentNode = treeData[idIndexMap[element.parent_id]];
			// Add our current element to its parent's `children` array
			parentNode.children = [...(parentNode.children || []), element];
		});
		return root;
	}

	// get parent node ids
	getParentNodeIds(territoryData) {
		let parentIds = [],
			nodeIds = [],
			topNodeId = null;
		territoryData.forEach((element) => {
			if (element.parent_id === 0) {
				topNodeId = element.id;
			}
			parentIds.push(element.parent_id);
			nodeIds.push(element.id);
		});
		return { parentIds, nodeIds, topNodeId };
	}

	// disable actions if logged-in user is not admin and the user-to-be-edited is admin
	disableActionsIfUserIsNotAdmin(row_data) {
		let role_id = row_data?.role_details?.[0]?.role_id;
		if (role_id) {
			return !this.isUserAdmin(false) && this.isEditUserAdmin(role_id);
		}
	}

	isEditUserAdmin(role_id) {
		let selected_role = _find(this.state.all_roles_list, {
			id: role_id,
		});
		console.log('cccselected_role', selected_role);
		if (
			selected_role &&
			selected_role.role_type &&
			selected_role.role_type == 1
		) {
			return true;
		}
		return false;
	}

	async componentDidMount() {
		const { t } = this.props;
		// this.getApplicationsDataFunction();
		// document.title = 'User Management';
		if (this.props.location.pathname.includes('/user-management/')) {
			this.fetchRoleData(this.state.client_id, this.state.app_drop);
		} else if (
			this.props.location.pathname.includes('/customer-management') &&
			this.state.app_drop &&
			// this.state.app_drop == 17 &&
			this.props.table_data &&
			this.props.table_data.key
		) {
			this.fetchRoleData(this.props.table_data.key, this.state.app_drop);
		}

		if (
			this.state.app_drop &&
			this.state.app_drop == 17 &&
			this.props.location.pathname.includes('/customer-management/')
		) {
			this.fetchCustomerList();
		} else if (
			this.state.app_drop &&
			this.state.app_drop != 17 &&
			this.props.location.pathname.includes('/customer-management/')
		) {
			this.fetchCustomerDetails();
		}
	}

	collapseState(collapse) {
		this.setState({
			collapse: collapse,
		});
	}

	async resendLink(row_data, type) {
		let that = this;
		let resendLoading = that.state.resend_loading;

		if (resendLoading[row_data.id]) {
			resendLoading[row_data.id][type] = true;
		} else {
			resendLoading[row_data.id] = {};
			resendLoading[row_data.id][type] = true;
		}

		that.setState(
			{
				resend_loading: resendLoading,
			},
			async () => {
				let response = await resendInvitation(
					that.state.client_id,
					row_data.id,
					type
				);

				let updatedResendLoading = that.state.resend_loading;

				if (response.status === 403) {
					updatedResendLoading[row_data.id][type] = false;

					that.setState({
						unauthorised_access: true,
						unauthorised_access_msg: response.message,
						resend_loading: updatedResendLoading,
					});
				} else if (response.status === 'success') {
					updatedResendLoading[row_data.id][type] = false;

					that.setState({
						resend_loading: updatedResendLoading,
					});
					if(type === "email") {
						console.log("Refresh 0")
						setTimeout(() => {
							console.log("Refresh 1")
							that.fetchUserData(
								that.state.client_id,
								that.state.app_drop,
								false,
							)
						}, 5000);
						that.openNotification(
							'success',
							'Email request in progress, awaiting confirmation'
						);
					} else {
						that.openNotification(
							'success',
							'Invitation sent successfully'
						);
					}
				} else {
					updatedResendLoading[row_data.id][type] = false;

					that.openNotification('error', response.message);
					that.setState({
						unauthorised_access: false,
						resend_loading: updatedResendLoading,
						error_API: true,
						error_API_msg: response.message,
					});
				}
			}
		);
	}

	isActionEnabled(accessKey, roleType) {
		if(this.props.logged_in_user_role_type === 10 &&
			!this.props.location.pathname.includes('/customer-management')) {
			return false;
		}
		return (
			(this.props.getViewAccess(accessKey, true) ||
				this.accessToVendorManager()) &&
			(this.props.logged_in_user_role_type == 1 || roleType !== 1)
		);
	}

	/*
	 * This function calls the API to get user list.
	 * @param  int, int i:e client_id and application_id.
	 */
	async fetchUserData(client_id, app_id, loading=true) {
		let that = this;
		if(loading) {
			this.setState({
				table_load: true,
			});
		}
		let application_id = app_id;
		if (that.props.location.pathname.includes('/customer-management/')) {
			application_id = 'all';
		}
		const [response, sitesResponse] = await Promise.all([
			retriveUsers(client_id, application_id),
			this.fetchSitesList(),
		]);

		if (response.status === 403) {
			that.setState(
				{
					unauthorised_access: true,
					table_load: false,
					unauthorised_access_msg: response.message,
				},
				async () => {
					if (this.isTerritoryEnabled) {
						await this.getTerritoryData();
					}
				}
			);
		} else if (response.status === 'success') {
			let dataUser = [];

			if (response.user_details && response.user_details.length) {
				response.user_details.map((user) => {
					let temp_users = [],
						userStatus = 0;

					let last_login =
						!isNaN(user.last_login) && user.last_login > 0
							? TimeFormatter(
									this.props.user_preferences?.time_format,
									user.last_login,
									'HH:mm, DD MMM YYYY'
							  )
							  : this.props.t? this.props.t('never'): "Never";
							// : 'Never';
					let timeFormat =
						this.props.details_customer_data &&
						this.props.details_customer_data.client_time_format &&
						this.props.details_customer_data.client_time_format ==
							'12_hr'
							? 'hh:mm A, DD MMM YYYY'
							: 'HH:mm, DD MMM YYYY';
					if (
						this.props.details_customer_data &&
						this.props.details_customer_data.client_time_zone
					) {
						last_login =
							!isNaN(user.last_login) && user.last_login > 0
								? moment
										.unix(user.last_login)
										.tz(
											this.props.details_customer_data
												.client_time_zone
										)
										.format(timeFormat)
									: this.props.t? this.props.t('never'): "Never";
					}

					if (
						user.activation_status_details &&
						user.activation_status_details.length
					) {
						if (
							this.props.location.pathname.includes(
								'/user-management'
							)
						) {
							let found = _find(user.activation_status_details, {
								application_id: app_id,
							});
							if (found) {
								userStatus = found.is_active;
							}
						} else {
							user.activation_status_details.map(
								(status_details) => {
									if (status_details.is_active) {
										userStatus = 1;
									}
								}
							);
						}
					}

					dataUser.push({
						key: user.contact_id,
						name: user.first_name + ' ' + user.last_name,
						login_allowed: this.isLoginAllowed(user.role_details),
						login: last_login,
						email_verified: user.email_verified ? true : false,
						mobile_verified: user.mobile_verified ? true : false,
						things: user.things ? user.things : [],
						id: user.contact_id,
						first_name: user.first_name,
						last_name: user.last_name,
						designation: user.designation,
						email_id: user.email,
						mobile_no: user.mobile_no,
						status: user.status,
						role_details: user.role_details,
						applications: user.applications,
						industries: user.industries,
						industry_sets: user.industry_sets,
						activation_status_details:
							user.activation_status_details,
						status: userStatus,
						territory_ids: user.territory_ids,
						employee_id: user.employee_id,
						tracking_id: user.tracking_id,
						mfa_enabled: user.mfa_enabled,
						sites: user.sites,
						email_verification_delivery_status: user.email_verification_delivery_status,
						email_verification_failure_reason: user.email_verification_failure_reason,
						email_verification_resend_allowed: user.email_verification_resend_allowed,
					});
				});
			}
			that.other_contact = [];

			if (
				that.state.all_contact_details &&
				that.state.all_contact_details.length > 0
			) {
				if (response.user_details && response.user_details.length) {
					that.state.all_contact_details.map((contact) => {
						let found = _find(response.user_details, {
							contact_id: contact.contact_id,
						});
						if (found == undefined) {
							that.other_contact.push(contact);
						}
					});
				} else {
					that.other_contact = that.state.all_contact_details;
				}
			}

			let UserManagementObjectData = this.state.UserManagementObjectData;
			if (this.props.location.pathname.includes('/user-management')) {
				if (
					this.props.location.pathname.includes('/datoms-x') ||
					this.props.location.pathname.includes('/iot-platform')
				) {
					
					UserManagementObjectData.iot_user_table.head_data[2].render =
						(data, row_data) => (
							<div>
								{(() => {
									// console.log('row_data_', row_data);
									let app_role = [];
									if (
										row_data.role_details &&
										row_data.role_details.length &&
										this.state.all_role_datas
									) {
										row_data.role_details.map((role) => {
											let role_arr = _find(
												this.state.all_role_datas
													.roles_list,
												{ id: role.role_id }
											);
											if (role_arr) {
												app_role.push({
													role_name: role_arr.name,
												});
											}
										});
									}

									if (app_role.length) {
										return app_role.map((details) => {
											return (
												<div className="text-center">
													{details.role_name}
												</div>
											);
										});
									}
								})()}
							</div>
						);

					UserManagementObjectData.iot_user_table.head_data[3].render =
						(data, row_data) => (
							<div className="dsp-flex">
								{(() => {
									let app_dets = [];
									if (
										row_data.applications &&
										row_data.applications.length
									) {
										row_data.applications.map((app_id) => {
											let app_arr = _find(
												this.state
													.application_name_array,
												{ id: app_id }
											);
											if (app_arr) {
												app_dets.push(app_arr.name);
											}
										});
									}

									if (app_dets.length) {
										return app_dets.map((details) => {
											return (
												<div className="app-role-sec">
													<div>{details}</div>
												</div>
											);
										});
									}
								})()}
							</div>
						);

					UserManagementObjectData.iot_user_table.head_data[
						UserManagementObjectData.iot_user_table.head_data
							.length - 1
					].render = (data, row_data, index) => (
						<AntDropdown
							disabled={
								this.props.getViewAccess([
									'UserManagement:Deactivate',
								], true) ||
								this.props.getViewAccess([
									'UserManagement:Edit',
								], true) ||
								this.props.getViewAccess([
									'UserManagement:Delete',
								], true) ||
								this.accessToVendorManager()
									? false
									: true
							}
							overlay={
								<AntMenu>
									{(() => {
										let options = [],
											roleType = 0,
											selected = {};
										if (row_data.role_details) {
											selected = _find(
												row_data.role_details,
												{
													application_id:
														this.props
															.application_id,
												}
											);
										}

										if (selected && selected.role_type) {
											roleType = selected.role_type;
										}
										if (
											this.isActionEnabled(
												['UserManagement:Deactivate'],
												roleType
											)
										) {
											options.push(
												<AntMenuItem
													key="action-3"
													onClick={() =>
														this.showStatusConfirm(
															row_data
														)
													}
												>
													{row_data.status == 1
														? this.props.t('deactivate')
														: this.props.t('activate')}
												</AntMenuItem>
											);
										} else {
											options.push(
												<AntMenuItem
													className="block"
													key="action-3"
												>
													{row_data.status == 1
														? this.props.t('deactivate')
														: this.props.t('activate')}
												</AntMenuItem>
											);
										}

										if (
											this.isActionEnabled(
												['UserManagement:Edit'],
												roleType
											)
										) {
											options.push(
												<AntMenuItem
													key="action-1"
													onClick={() =>
														this.editUser(row_data)
													}
												>
													{this.props.t('edit')}
												</AntMenuItem>
											);
										} else {
											options.push(
												<AntMenuItem
													className="block"
													key="action-1"
												>
													{this.props.t('edit')}
												</AntMenuItem>
											);
										}

										if (
											this.isActionEnabled(
												['UserManagement:Delete'],
												roleType
											)
										) {
											options.push(
												<AntMenuItem
													key="action-2"
													onClick={() =>
														this.showDeleteConfirm(
															row_data
														)
													}
												>
													{this.props.t('delete')}
												</AntMenuItem>
											);
										} else {
											options.push(
												<AntMenuItem
													className="block"
													key="action-2"
												>
													{this.props.t('delete')}
												</AntMenuItem>
											);
										}
										return options;
									})()}
								</AntMenu>
							}
							trigger={['click']}
							placement="bottomLeft"
						>
							{/*<DotsVertical className="menu-icon"/>*/}
							{(() => {
								if (
									this.props.getViewAccess([
										'UserManagement:Deactivate',
									], true) ||
									this.props.getViewAccess([
										'UserManagement:Edit',
									], true) ||
									this.props.getViewAccess([
										'UserManagement:Delete',
									], true) ||
									this.accessToVendorManager()
								) {
									return (
										<MoreOutlined className="menu-icon" />
									);
								} else {
									return (
										<AntTooltip title={this.props.t? this.props.t('no_access'): "No access"}>
											{' '}
											<MoreOutlined className="menu-icon disabled" />{' '}
										</AntTooltip>
									);
								}
							})()}
						</AntDropdown>
					);
				} else {
					UserManagementObjectData.application_user_table.head_data[2].render =
						(data, row_data) => (
							<div>
								{(() => {
									// console.log('row_data_', row_data);
									let app_role = [];
									if (
										row_data.role_details &&
										row_data.role_details.length &&
										this.state.all_role_datas
									) {
										row_data.role_details.map((role) => {
											let role_arr = _find(
												this.state.all_role_datas
													.roles_list,
												{ id: role.role_id }
											);
											if (role_arr) {
												app_role.push({
													role_name: role_arr.name,
												});
											}
										});
									}

									if (app_role.length) {
										return app_role.map((details) => {
											return (
												<div className="text-center">
													{details.role_name}
												</div>
											);
										});
									}
								})()}
							</div>
						);

					let assetIndex = 3;
					if (this.showSiteField) {
						assetIndex = 4;
						if (
							!_find(
								UserManagementObjectData.application_user_table
									.head_data,
								{ key: 'sites' }
							)
						) {
							UserManagementObjectData.application_user_table.head_data.splice(
								3,
								0,
								{
									title: 'Sites',
									dataIndex: 'sites',
									key: 'sites',
									width: '20%',
									render: (data, row_data) => (
										<div style={{display: 'flex', rowGap: '8px', flexWrap: 'wrap'}}>
											{this.getSiteNames(
												row_data.sites,
												sitesResponse.siteNames,
												row_data.territory_ids
											)}
										</div>
									),
								}
							);
						}
						UserManagementObjectData.application_user_table.head_data[
							assetIndex
						].width = '30%'
					}

					if (
						this.props.location.pathname.includes(
							'/delivery-tracking/'
						)
					) {
						UserManagementObjectData.application_user_table.head_data[
							assetIndex
						].title = (
							<span
								className=""
								dangerouslySetInnerHTML={{ __html: 'Loggers' }}
							/>
						);
					}

					UserManagementObjectData.application_user_table.head_data[
						assetIndex
					].render = (data, row_data) => (
						<div>
							{(() => {
								const siteAssets = [];
								if (row_data.things === '*') {
									return <AntTag>All</AntTag>;
								} else{

									if(row_data.territory_ids === "*") {	
										Object.keys(this.state.territoryWiseAssets || {}).forEach((territoryId) => {
											siteAssets.push(...this.state.territoryWiseAssets[territoryId]);
										});
									} else if(row_data.territory_ids?.length) {
										row_data.territory_ids.forEach((territoryId) => {
											if(this.state.territoryWiseAssets?.[territoryId]){
												siteAssets.push(...this.state.territoryWiseAssets[territoryId]);
											}
										});
									}

									const finalSiteIds = row_data.sites || [];
									
									if (finalSiteIds === '*') {
										Object.keys(
											sitesResponse.siteWiseAssets
										).forEach((siteId) => {
											if (
												siteId === '*' ||
												!sitesResponse.siteWiseAssets[
													siteId
												]
											)
												return;
											siteAssets.push(
												...sitesResponse.siteWiseAssets[
													siteId
												]
											);
										});
									} else if (Array.isArray(finalSiteIds)) {
										finalSiteIds.forEach((siteId) => {
											if (
												!sitesResponse.siteWiseAssets[
													siteId
												]
											)
												return;
											siteAssets.push(
												...sitesResponse.siteWiseAssets[
													siteId
												]
											);
										});
									}
								} 
								const assetsArray = siteAssets.length
									? [
											...new Set([
												...row_data.things,
												...siteAssets,
											]),
									  ]
									: row_data.things;
								let thing_names = [];
								for (let i = 0; i <= 3; i++) {
									if (assetsArray[i]) {
										let found = _find(
											this.state.things_list,
											{ id: assetsArray[i] }
										);
										if (found) {
											thing_names.push(found.name);
										}
									}
								}

								if (assetsArray.length > 4) {
									thing_names.push('+ ' + (data.length - 4));
								}

								if (thing_names.length) {
									return thing_names.map((thing) => {
										return (
											<AntTag style={{ marginBottom: 4 }}>
												{thing}
											</AntTag>
										);
									});
								} else if (assetsArray === '*') {
									return <AntTag>All</AntTag>;
								} else {
									return <span>-</span>;
								}
							})()}
						</div>
					);

					UserManagementObjectData.application_user_table.head_data[
						UserManagementObjectData.application_user_table
							.head_data.length - 1
					].render = (data, row_data, index) => (				
						<AntDropdown
							disabled={
								this.props.getViewAccess([
									'UserManagement:Deactivate',
								], true) ||
								this.props.getViewAccess([
									'UserManagement:Edit',
								], true) ||
								this.props.getViewAccess([
									'UserManagement:Delete',
								], true) ||
								this.accessToVendorManager()
									? false
									: true
							}
							overlay={
								<AntMenu>
									{(() => {
										let options = [],
											roleType = 0,
											selected = {};
										if (row_data.role_details) {
											selected = _find(
												row_data.role_details,
												{
													application_id:
														this.props
															.application_id,
												}
											);
										}

										if (selected && selected.role_type) {
											roleType = selected.role_type;
										}
										console.log('roww_datta_', row_data);
										if (
											this.isActionEnabled(
												[
													'UserManagement:Deactivate'
												],
												roleType
											)
										) {
											options.push(
												<AntMenuItem
													key="action-3"
													onClick={() =>
														this.showStatusConfirm(
															row_data
														)
													}
												>
													{row_data.status == 1
														? this.props.t('deactivate')
														: this.props.t('activate')}
												</AntMenuItem>
											);
										} else {
											options.push(
												<AntMenuItem
													className="block"
													key="action-3"
												>
													{row_data.status == 1
														? this.props.t('deactivate')
														: this.props.t('activate')}
												</AntMenuItem>
											);
										}

										if (
											this.isActionEnabled(
												['UserManagement:Edit'],
												roleType
											)
										) {
											options.push(
												<AntMenuItem
													key="action-1"
													onClick={() =>
														this.editUser(row_data)
													}
												>
													{this.props.t('edit')}
												</AntMenuItem>
											);
										} else {
											options.push(
												<AntMenuItem
													className="block"
													key="action-1"
												>
													{this.props.t('edit')}
												</AntMenuItem>
											);
										}

										if (
											this.isActionEnabled(
												['UserManagement:Delete'],
												roleType
											)
										) {
											options.push(
												<AntMenuItem
													key="action-2"
													onClick={() =>
														this.showDeleteConfirm(
															row_data
														)
													}
												>
													{this.props.t('delete')}
												</AntMenuItem>
											);
										} else {
											options.push(
												<AntMenuItem
													className="block"
													key="action-2"
												>
													{this.props.t('delete')}
												</AntMenuItem>
											);
										}
										return options;
									})()}
								</AntMenu>
							}
							trigger={['click']}
							placement="bottomLeft"
						>
							{(() => {
								if (
									this.props.getViewAccess([
										'UserManagement:Deactivate',
									], true) ||
									this.props.getViewAccess([
										'UserManagement:Edit',
									], true) ||
									this.props.getViewAccess([
										'UserManagement:Delete',
									], true) ||
									this.accessToVendorManager()
								) {
									return (
										<MoreOutlined className="menu-icon" />
									);
								} else {
									return (
										<AntTooltip title={this.props.t? this.props.t('no_access'): "No access"}>
											{' '}
											<MoreOutlined className="menu-icon disabled" />{' '}
										</AntTooltip>
									);
								}
							})()}
						</AntDropdown>
					);
				}
			} else {
				UserManagementObjectData.user_table.head_data[2].render = (
					data,
					row_data
				) => (
					<div className="dsp-flex">
						{(() => {
							let app_role = [];
							if (
								row_data.role_details &&
								row_data.role_details.length &&
								this.state.selected_all_roles
							) {
								row_data.role_details.map((role) => {
									let app_arr = _find(
										this.state.all_app_list,
										{ id: role.application_id }
									);
									let role_arr = _find(
										this.state.selected_all_roles,
										{ id: role.role_id }
									);
									if (app_arr && role_arr) {
										app_role.push({
											application_name: app_arr.name,
											role_name: role_arr.name,
										});
									}
								});
							}

							if (app_role.length) {
								return app_role.map((details) => {
									return (
										<div className="app-role-sec">
											<div className="app-sec">
												{details.application_name}
											</div>
											<div className="role-sec">
												{this.props.t? this.props.t(details.role_name): details.role_name}
											</div>
										</div>
									);
								});
							}
						})()}
					</div>
				);

				UserManagementObjectData.user_table.head_data[
					UserManagementObjectData.user_table.head_data.length - 1
				].render = (data, row_data, index) => (
					<AntDropdown
						disabled={
							this.props.getViewAccess([
								'UserManagement:Deactivate',
							], true) ||
							this.props.getViewAccess(['UserManagement:Edit'], true) ||
							this.props.getViewAccess([
								'UserManagement:Delete',
							], true) ||
							this.accessToVendorManager()
								? false
								: true
						}
						overlay={
							<AntMenu>
								{(() => {
									let options = [],
										roleType = 0,
										selected = {};
									if (row_data.role_details) {
										selected = _find(
											row_data.role_details,
											{
												application_id:
													this.props.application_id,
											}
										);
									}

									if (selected && selected.role_type) {
										roleType = selected.role_type;
									}
									if (
										this.isActionEnabled(
											[
												'UserManagement:Deactivate'
											],
											roleType
										)
									) {
										options.push(
											<AntMenuItem
												key="action-3"
												onClick={() =>
													this.showStatusConfirm(
														row_data
													)
												}
											>
												{row_data.status == 1
													? this.props.t('deactivate')
													: this.props.t('activate')}
											</AntMenuItem>
										);
									} else {
										options.push(
											<AntMenuItem
												className="block"
												key="action-3"
											>
												{row_data.status == 1
													? this.props.t('deactivate')
													: this.props.t('activate')}
											</AntMenuItem>
										);
									}

									if (
										this.isActionEnabled(
											['UserManagement:Edit'],
											roleType
										)
									) {
										console.log(
											'roleType Gdai',
											roleType,
											this.props.application_id,
											row_data.role_details
										);
										options.push(
											<AntMenuItem
												key="action-1"
												onClick={() =>
													this.editUser(row_data)
												}
											>
												{this.props.t('edit')}
											</AntMenuItem>
										);
									} else {
										options.push(
											<AntMenuItem
												className="block"
												key="action-1"
											>
												{this.props.t('edit')}
											</AntMenuItem>
										);
									}

									if (
										this.isActionEnabled(
											['UserManagement:Delete'],
											roleType
										)
									) {
										options.push(
											<AntMenuItem
												key="action-2"
												onClick={() =>
													this.showDeleteConfirm(
														row_data
													)
												}
											>
												{this.props.t('delete')}
											</AntMenuItem>
										);
									} else {
										options.push(
											<AntMenuItem
												className="block"
												key="action-2"
											>
												{this.props.t('delete')}
											</AntMenuItem>
										);
									}
									return options;
								})()}
							</AntMenu>
						}
						trigger={['click']}
						placement="bottomLeft"
					>
						{(() => {
							if (
								this.props.getViewAccess([
									'UserManagement:Deactivate',
								], true) ||
								this.props.getViewAccess([
									'UserManagement:Edit',
								], true) ||
								this.props.getViewAccess([
									'UserManagement:Delete',
								], true) ||
								this.accessToVendorManager()
							) {
								return <MoreOutlined className="menu-icon" />;
							} else {
								return (
									<AntTooltip title={this.props.t? this.props.t('no_access'): "No access"}>
										{' '}
										<MoreOutlined className="menu-icon disabled" />{' '}
									</AntTooltip>
								);
							}
						})()}
					</AntDropdown>
				);
			}

			that.setState(
				{
					unauthorised_access: false,
					all_user_data: response,
					user_list: response.user_details,
					dataUser: dataUser,
					sitesList: sitesResponse.sitesList,
					siteNames: sitesResponse.siteNames,
					siteWiseAssets: sitesResponse.siteWiseAssets,
				},
				async () => {
					if (
						that.props.location.pathname.includes(
							'/user-management'
						) &&
						!that.props.location.pathname.includes('/datoms-x') &&
						!that.props.location.pathname.includes('/iot-platform')
					) {
						that.retriveThingsListFunction(
							that.state.client_id,
							that.state.app_drop
						);
					} else {
						that.filterTableData(that.state.app_drop);
					}
					if (this.isTerritoryEnabled) {
						await this.getTerritoryData();
					}
				}
			);
		} else {
			that.openNotification('error', response.message);
			that.setState({
				unauthorised_access: false,
				loading: true,
				table_load: false,
				error_API: true,
				error_API_msg: response.message,
			});
		}
	}

	filterTableData(value) {
		console.log('filterTableData', value);
		if (this.state.dataUser && this.state.dataUser.length) {
			let user_table_data = [];
			console.log('data_user_', this.state.dataUser);
			if (value == 'all') {
				this.state.dataUser.map((user) => {
					console.log('dataUser1', user);
					if (user.role_details && user.role_details.length) {
						user_table_data.push({
							key: user.key,
							name: user.first_name + ' ' + user.last_name,
							login: user.login,
							login_allowed: this.isLoginAllowed(
								user.role_details
							),
							email_verified: user.email_verified ? true : false,
							mobile_verified: user.mobile_verified
								? true
								: false,
							id: user.id,
							first_name: user.first_name,
							last_name: user.last_name,
							designation: user.designation,
							email_id: user.email_id,
							mobile_no: user.mobile_no,
							status: user.status,
							role_details: user.role_details,
							all_role_details: user.role_details,
							activation_status_details:
								user.activation_status_details,
							applications: user.applications,
							industries: user.industries,
							industry_sets: user.industry_sets,
							things: user.things,
							employee_id: user.employee_id,
							tracking_id: user.tracking_id,
							mfa_enabled: user.mfa_enabled,
							sites: user.sites,
							territory_ids: user.territory_ids,
							email_verification_delivery_status: user.email_verification_delivery_status,
							email_verification_failure_reason: user.email_verification_failure_reason,
							email_verification_resend_allowed: user.email_verification_resend_allowed,
						});
					}
				});

				this.setState(
					{
						user_table_data: user_table_data,
					},
					() => {
						console.log(
							'user_table_data__',
							this.state.user_table_data
						);
						this.getFilteredTableData(
							this.state.search_value,
							this.state.user_table_data
						);
					}
				);
			} else {
				console.log('filterTableData else');
				let filter_data = [];
				this.state.dataUser.map((user) => {
					let found = _find(user.activation_status_details, {
						application_id: parseInt(value),
					});
					let status = 0;
					if (found) {
						if (found.is_active) {
							console.log('dataUser2', found);
							status = 1;
						}
					}
					if (user.role_details && user.role_details.length) {
						user.role_details.map((app) => {
							if (app.application_id == parseInt(value)) {
								let role_det = _filter(user.role_details, {
									application_id: parseInt(value),
								});
								console.log('role_det_', status);
								if (role_det) {
									user_table_data.push({
										key: user.key,
										name:
											user.first_name +
											' ' +
											user.last_name,
										login: user.login,
										login_allowed: this.isLoginAllowed(
											user.role_details
										),
										email_verified: user.email_verified
											? true
											: false,
										mobile_verified: user.mobile_verified
											? true
											: false,
										id: user.id,
										first_name: user.first_name,
										last_name: user.last_name,
										designation: user.designation,
										email_id: user.email_id,
										mobile_no: user.mobile_no,
										status: status,
										role_details: role_det,
										all_role_details: user.role_details,
										activation_status_details:
											user.activation_status_details,
										applications: user.applications,
										industries: user.industries,
										industry_sets: user.industry_sets,
										things: user.things,
										territory_ids: user.territory_ids,
										employee_id: user.employee_id,
										tracking_id: user.tracking_id,
										mfa_enabled: user.mfa_enabled,
										sites: user.sites,
										email_verification_delivery_status: user.email_verification_delivery_status,
										email_verification_failure_reason: user.email_verification_failure_reason,
										email_verification_resend_allowed: user.email_verification_resend_allowed,
									});
								}
							}
						});
					}
				});

				this.setState(
					{
						user_table_data: user_table_data,
					},
					() => {
						console.log(
							'user_table_data_',
							this.state.user_table_data
						);
						this.getFilteredTableData(
							this.state.search_value,
							this.state.user_table_data
						);
						if (
							this.props.location.pathname.search(
								'/users/' +
									this.props.match.params.user_id +
									'/edit'
							) > -1
						) {
							let data = _find(this.state.user_table_data, {
								key: parseInt(this.props.match.params.user_id),
							});
							console.log('dataaa__', data);
							if (data) {
								this.editUser(data);
							}
						}
					}
				);
			}
		} else {
			this.setState(
				{
					user_table_data: [],
				},
				() => {
					this.getFilteredTableData(
						this.state.search_value,
						this.state.user_table_data
					);
				}
			);
		}
	}

	/*
	 * This function calls the API to get things list.
	 * @param  int, int i:e client_id and application_id.
	 */
	async retriveThingsListFunction(client_id, app_id) {
		let that = this;
		let data = {
			client_id: client_id,
			application_id: app_id,
		};

		let response = await retriveThingsList(data);
		if (response.status === 'success') {
			let things_list = [];
			if (response.things && response.things.length) {
				response.things.map((thing) => {
					things_list.push({
						id: thing.id,
						name: thing.name,
					});
				});
			}

			that.setState(
				{
					things_all_data: response.things,
					things_list: things_list,
				},
				() => {
					that.filterTableData(that.state.app_drop);
					// console.log('logger_data', this.state.logger_all_data);
				}
			);
		} else if (response.status == 403) {
			that.setState(
				{
					loading_icon: true,
					table_load: false,
					unauthorised_access_msg: response.message,
				},
				() => {
					that.openNotification(
						'error',
						that.state.unauthorised_access_msg
					);
				}
			);
		}
	}

	/*
	 * This function calls the API to get roles list.
	 * @param  int, int i:e client_id and application_id.
	 */
	async fetchRoleData(client_id, app_id) {
		let that = this;
		let response = await retriveRoles(client_id, app_id);
		if (response.status === 403) {
			that.setState({
				unauthorised_access: true,
				unauthorised_access_msg: response.message,
			});
		} else if (response.status === 'success') {
			// console.log('user_roles_data', response);
			// console.log('response status code1', response_status);
			// response.roles_list =[];

			that.setState(
				{
					// unauthorised_access: false,
					all_role_datas: response,
					all_roles_list: response.roles_list,
					// filtered_stations: response.all_stations,
				},
				() => {
					if (
						that.props.location.pathname.includes(
							'/user-management'
						) &&
						(that.props.location.pathname.includes('/datoms-x') ||
							that.props.location.pathname.includes(
								'/iot-platform'
							))
					) {
						that.fetchCustomerList();
					} else {
						that.fetchCustomerDetails();
					}
				}
			);
		} else {
			that.openNotification('error', response.message);
			that.setState({
				unauthorised_access: false,
				loading: true,
				error_API: true,
				error_API_msg: response.message,
			});
		}
	}

	/*
	 * This function calls the API to get a customers list and applicaion access liat.
	 */
	async fetchCustomerList() {
		let that = this;
		let clientId = this.state.client_id;
		if (that.props.location.pathname.includes('/customer-management/')) {
			clientId = this.props.customer_id;
		}
		let response = await retriveCustomerList(clientId);
		if (response.status === 403) {
			that.setState({
				unauthorised_access: true,
				unauthorised_access_msg: response.message,
			});
		} else if (response.status === 'success') {
			// console.log('option_data1', response);

			let application_name_array = [],
				customer_list = [];
			if (response.applications && response.applications.length) {
				response.applications.map((applications) => {
					application_name_array.push({
						id: applications.id,
						name: applications.name,
					});
				});
			}

			if (response.customers && response.customers.length) {
				response.customers.map((customer) => {
					if (customer.id !== 1) {
						customer_list.push(customer);
					}
				});
			}

			that.setState(
				{
					unauthorised_access: false,
					loaded_data: true,
					application_name_array: application_name_array,
					all_customer_data: customer_list,
					all_app_list: response.applications,
				},
				() => {
					that.fetchCustomerDetails();
				}
			);
		} else {
			that.openNotification('error', response.message);
			that.setState({
				unauthorised_access: false,
				loading: true,
				error_API: true,
				error_API_msg: response.message,
			});
		}
	}

	/*
	 * This function calls the API to get a customer's details.
	 * @param  integer .id of the client.
	 */
	async fetchCustomerDetails() {
		let that = this;
		let response = await retriveCustomerDetails(this.state.client_id);
		if (response.status === 403) {
			that.setState({
				unauthorised_access: true,
				unauthorised_access_msg: response.message,
			});
		} else if (response.status === 'success') {
			// console.log('customer_data2', response);
			let selected_all_roles = [],
				modified_application_details = [],
				all_app_list_details = [],
				plan_appwise_map = {};
			if (
				response.application_details &&
				response.application_details.length
			) {
				response.application_details.map((application) => {
					if (
						application.plan_details &&
						Object.keys(application.plan_details).length
					) {
						plan_appwise_map[application.application_id] =
							this.getModifiedPlanRoleIds(
								application.plan_details,
								application.features_list,
								{
									fuel_delivery:
										response.fuel_delivery?.length,
								}
							);
						// .resorce_alocation
						// 	? true
						// 	: false;
					} else {
						plan_appwise_map[application.application_id] = {
							resorce_alocation: true,
						};
					}
					if (
						application.role_details &&
						application.role_details.length
					) {
						application.role_details.map((roles) => {
							selected_all_roles.push({
								id: roles.role_id,
								name: roles.role_name,
							});
						});
					}
					if (application.application_id == 17) {
						let iot_app_detail = application;
						if (application.applications) {
							iot_app_detail['applications'] = Object.keys(
								application.applications
							).map(Number);
						}
						modified_application_details.push(iot_app_detail);
					} else {
						modified_application_details.push(application);
					}

					all_app_list_details.push({
						id: application.application_id,
						name: application.application_name,
					});
				});
			}

			let nonUserContactList = [];

			if (response.contact_details && response.contact_details.length) {
				response.contact_details.map((contact) => {
					if (!contact.is_user) {
						nonUserContactList.push({
							value: contact.contact_id,
							name: contact.first_name + ' ' + contact.last_name,
						});
					}
				});
			}

			let UserManagementObjectData = this.state.UserManagementObjectData;
			// console.log('add_user_form__', add_user_form);
			UserManagementObjectData.add_user_form.contact_select[0].options =
				nonUserContactList;

			selected_all_roles = _uniqBy(selected_all_roles, 'id');
			// console.log('selected_all_roles_', selected_all_roles);
			let thingListObj = this.state.thing_list_obj
				? this.state.thing_list_obj
				: {};
			if (
				that.props.location.pathname.includes('/customer-management') &&
				response.application_details &&
				response.application_details.length
			) {
				response.application_details.map(async (app_det) => {
					if (!thingListObj[app_det.application_id]) {
						let data = {
							client_id: this.state.client_id,
							application_id: app_det.application_id,
						};
						let response = await retriveThingsList(data);
						if (response.status === 'success') {
							let things_list = [];
							if (response.things && response.things.length) {
								response.things.map((thing) => {
									things_list.push({
										id: thing.id,
										name: thing.name,
									});
								});
							}
							thingListObj[app_det.application_id] = things_list;
						}
					}
				});
			}

			this.setState(
				{
					thing_list_obj: thingListObj,
					vendor_type: response.vendor_type
						? response.vendor_type
						: [],
					customer_type: response.customer_type
						? response.customer_type
						: [],
					unauthorised_access: false,
					all_contact_details: response.contact_details,
					selected_all_roles: selected_all_roles,
					selected_all_app_list: modified_application_details,
					all_app_list:
						this.state.all_app_list.length == 0
							? all_app_list_details
							: this.state.all_app_list,
					UserManagementObjectData: UserManagementObjectData,
					plan_appwise_map,
					customer_features: {
						fuel_delivery: response.fuel_delivery?.length,
					},
				},
				() => {
					that.fetchUserData(
						this.state.client_id,
						this.state.app_drop
					);
				}
			);
		} else {
			that.openNotification('error', response.message);
			that.setState({
				unauthorised_access: false,
				loading: true,
				error_API: true,
				error_API_msg: response.message,
			});
		}
	}
	getModifiedPlanRoleIds(
		plan_description,
		enabled_features = this.props.enabled_features,
		customer_features = this.state.customer_features
	) {
		let finalPlan = plan_description;
		if (finalPlan && finalPlan.pre_defined_role_type_ids?.length) {
			if (
				!customer_features?.fuel_delivery &&
				!enabled_features?.includes('FuelManagement:FuelManagement')
			) {
				finalPlan.pre_defined_role_type_ids =
					finalPlan.pre_defined_role_type_ids.filter(
						(id) => id !== 6
					);
			}
			if (!enabled_features?.includes('UserManagement:Tracking')) {
				finalPlan.pre_defined_role_type_ids =
					finalPlan.pre_defined_role_type_ids.filter(
						(id) => id !== 5
					);
			}
		}
		return finalPlan;
	}
	/*
	 * This function calls the API to delete the user.
	 * @param  {Object} user user data.
	 */
	async deleteUserFunction(user = this.state.selected_user_del) {
		let that = this;
		this.setState({
			deleteLoading: true,
		});
		// console.log('user_del', user);
		let application_arr = [];
		if (this.state.app_drop == 'all') {
			if (user.role_details && user.role_details.length) {
				user.role_details.map((role) => {
					application_arr.push(role.application_id);
				});
			}
		} else {
			application_arr.push(parseInt(this.state.app_drop));
		}

		let response = await deleteUser(
			application_arr,
			this.state.client_id,
			user.id
		);
		if (response.status === 403) {
			that.setState({
				unauthorised_access: true,
				unauthorised_access_msg: response.message,
				delete_user_modal: false,
				selected_user_del: null,
				deleteLoading: false,
			});
		} else if (response.status === 'success') {
			that.setState(
				{
					delete_user_modal: false,
					selected_user_del: null,
					deleteLoading: false,
				},
				() => {
					that.openNotification(
						'success',
						'User deleted successfully'
					);
					that.fetchUserData(
						that.state.client_id,
						that.state.app_drop
					);
					that.fetchCustomerDetails();
				}
			);
		} else {
			that.openNotification('error', response.message);
			that.setState({
				unauthorised_access: false,
				loading: true,
				error_API: true,
				error_API_msg: response.message,
				delete_user_modal: false,
				selected_user_del: null,
				deleteLoading: false,
			});
		}
	}

	editUser(user) {
		this.setState(
			{
				drawCreateVisible: true,
				add_user: false,
				edit_user: true,
				show_add_draw: false,
				selected_user_data: user,
			},
			() => {
				if (
					this.props.location.pathname.search(
						'/customer-management'
					) > -1
				) {
					if (this.state.app_drop == 'all') {
						this.props.history.push(
							this.platform_slug +
								'/customer-management/' +
								this.state.client_id +
								'/applications/all/users/' +
								user.id +
								'/edit'
						);
					} else {
						this.props.history.push(
							this.platform_slug +
								'/customer-management/' +
								this.state.client_id +
								'/applications/' +
								parseInt(this.state.app_drop) +
								'/users/' +
								user.id +
								'/edit'
						);
					}
				} else {
					this.props.history.push(
						this.platform_slug +
							this.user_slug +
							'/users/' +
							user.id +
							'/edit'
					);
				}
			}
		);
	}

	/*
	 * This function calls the API to change the status of the user.
	 * @param  {Object} user user data.
	 */
	async setStatusUser(user = this.state.selected_user_status) {
		// console.log('setStatusUser', user);
		this.setState({
			statusLoading: true,
		});
		let that = this,
			application_ids = [];
		if (this.props.location.pathname.includes('/customer-management')) {
			if (this.state.app_drop == 'all') {
				if (user.activation_status_details.length) {
					user.activation_status_details.map((status_details) => {
						application_ids.push(status_details.application_id);
					});
				}
			} else {
				application_ids = [this.state.app_drop];
			}
		}

		let data = {
			client_id: this.state.client_id,
			user_id: user.id,
			application_details: this.props.location.pathname.includes(
				'/user-management'
			)
				? [this.state.app_drop]
				: application_ids,
			is_active: user.status == 1 ? 0 : 1,
		};

		// console.log('setStatusUser data', data);

		let response = await updateUserStatus(data);
		if (response.status === 403) {
			that.setState({
				unauthorised_access: true,
				unauthorised_access_msg: response.message,
				status_user_modal: false,
				selected_user_status: null,
				statusLoading: false,
			});
		} else if (response.status === 'success') {
			that.setState(
				{
					status_user_modal: false,
					selected_user_status: null,
					statusLoading: false,
				},
				() => {
					that.openNotification(
						'success',
						'User status updated successfully'
					);
					that.fetchUserData(
						that.state.client_id,
						that.state.app_drop
					);
					that.fetchCustomerDetails();
				}
			);
		} else {
			that.openNotification('error', response.message);
			that.setState({
				unauthorised_access: false,
				loading: true,
				error_API: true,
				error_API_msg: response.message,
				status_user_modal: false,
				selected_user_status: null,
				statusLoading: false,
			});
		}
	}

	openNotification(type, msg) {
		AntNotification({
			type: type,
			message: msg,
			// description: 'This is success notification',
			placement: 'bottomLeft',
			className: 'alert-' + type,
		});
	}

	/*
	 * This function shows an confirm modal for status update.
	 * @param  {Object} user user data.
	 */
	showStatusConfirm(user) {
		// let that = this;
		// console.log('showStatusConfirm', user);
		/*AntConfirmModal({
			title:
				'Do you want to ' +
				(user.status == 1 ? 'deactivate' : 'activate') +
				' ?',
			content: user.first_name + ' ' + user.last_name,
			okText: this.props.t('yes'),
			cancelText: this.props.t('no'),
			onOk: () => that.setStatusUser(user),
		});*/

		this.setState({
			selected_user_status: user,
			status_user_modal: true,
		});
	}

	viewUser(id) {
		this.setState(
			{ show_user_details: true, view_user: true, view_user_id: id },
			() => {
				if (
					this.props.location.pathname.search(
						'/customer-management'
					) > -1
				) {
					// console.log('platform_slug', this.platform_slug);
					this.props.history.push(
						this.platform_slug +
							'/customer-management/' +
							this.state.client_id +
							'/applications/' +
							this.state.app_drop +
							'/users/' +
							id +
							'/view'
					);
				} else {
					this.props.history.push(
						this.platform_slug +
							this.user_slug +
							'/users/' +
							id +
							'/view'
					);
				}
			}
		);
	}

	/*
	 * This function opens the Drawer to Add or Edit the user.
	 */
	addNewUser() {
		// this.props.history.push(this.platform_slug + '/users/add/' + this.props.history.location.search);
		this.setState(
			{
				drawCreateVisible: true,
				add_user: true,
				edit_user: false,
				show_add_draw: true,
				set_status_user: false,
				all_selected_industry: false,
				all_selected_set: false,
			},
			() => {
				if (
					this.props.location.pathname.search(
						'/customer-management'
					) > -1
				) {
					// console.log('platform_slug', this.platform_slug);
					this.props.history.push(
						this.platform_slug +
							'/customer-management/' +
							this.state.client_id +
							'/applications/' +
							this.state.app_drop +
							'/users/add'
					);
				} else {
					this.props.history.push(
						this.platform_slug + this.user_slug + '/users/add'
					);
				}
			}
		);
	}

	closeUserDraw() {
		this.setState(
			{
				drawCreateVisible: false,
				add_user: true,
				edit_user: false,
				show_add_draw: false,
				selected_user_data: undefined,
				show_user_details: false,
			},
			() => {
				if (
					this.props.location.pathname.search(
						'/customer-management'
					) > -1
				) {
					this.props.history.push(
						this.platform_slug +
							'/customer-management/' +
							this.state.client_id +
							'/applications/' +
							this.state.app_drop +
							'/users/view'
					);
				} else {
					this.props.history.push(
						this.platform_slug + this.user_slug + '/users/view'
					);
				}
			}
		);
	}

	/*
	 * This function shows an confirm modal for status update.
	 * @param  {Object} user user data.
	 */
	showDeleteConfirm(user) {
		// that.props.history.push(that.platform_slug + '/users/' + user.id + '/delete/' + that.props.history.location.search);
		// console.log('showDeleteConfirm_ 2', user);
		// console.log('showDeleteConfirm_ 3', content_text);
		/*AntConfirmModal({
			title: this.props.t('confirm_delete_msg'),
			content: content_text,
			okText: this.props.t('yes'),
			cancelText: this.props.t('no'),
			onOk: () => that.deleteUserFunction(user),
		});*/
		this.setState({
			delete_user_modal: true,
			selected_user_del: user,
		});
	}

	closeModal() {
		this.setState({
			selected_user_status: null,
			status_user_modal: false,
			delete_user_modal: false,
			selected_user_del: null,
			deleteLoading: false,
			statusLoading: false,
		});
	}

	/*
	 * This function opens the Drawer to show list of roles.
	 */
	openRoleDraw() {
		// console.log('this.platform_slug', this.platform_slug);
		this.setState(
			{
				roleDetailsVisible: true,
			},
			() => {
				if (
					this.props.location.pathname.search(
						'/customer-management'
					) > -1
				) {
					if (this.state.app_drop == 'all') {
						this.props.history.push(
							this.platform_slug +
								'/customer-management/' +
								this.props.match.params.customer_id +
								'/applications/all/roles/view'
						);
					} else {
						this.props.history.push(
							this.platform_slug +
								'/customer-management/' +
								this.props.match.params.customer_id +
								'/applications/' +
								parseInt(this.state.app_drop) +
								'/roles/view'
						);
					}
				} else {
					if (import.meta.env.VITE_MOBILE || import.meta.env.VITE_DESKTOP) {
						this.props.history.push(this.user_slug + '/roles/view');
					} else {
						this.props.history.push(
							this.platform_slug + this.user_slug + '/roles/view'
						);
					}
				}
			}
		);
	}

	/*
	 * This function called when the button is blocked.
	 */
	blockedButton() {}

	/*
	 * This function generates user list object for table.
	 * @param  {Object} user user data.
	 */
	filterTableData(value) {
		// console.log('filterTableData', value);
		if (this.state.dataUser && this.state.dataUser.length) {
			let user_table_data = [];
			// console.log('data_user_', this.state.dataUser);
			if (value == 'all') {
				this.state.dataUser.map((user) => {
					// console.log('dataUser1', user);
					if (user.role_details && user.role_details.length) {
						user_table_data.push({
							key: user.key,
							name: user.first_name + ' ' + user.last_name,
							login_allowed: this.isLoginAllowed(
								user.role_details
							),
							login: user.login,
							email_verified: user.email_verified ? true : false,
							mobile_verified: user.mobile_verified
								? true
								: false,
							id: user.id,
							first_name: user.first_name,
							last_name: user.last_name,
							designation: user.designation,
							email_id: user.email_id,
							mobile_no: user.mobile_no,
							status: user.status,
							role_details: user.role_details,
							all_role_details: user.role_details,
							activation_status_details:
								user.activation_status_details,
							applications: user.applications,
							industries: user.industries,
							industry_sets: user.industry_sets,
							things: user.things,
							territory_ids: user.territory_ids,
							employee_id: user.employee_id,
							tracking_id: user.tracking_id,
							mfa_enabled: user.mfa_enabled,
							sites: user.sites,
							email_verification_delivery_status: user.email_verification_delivery_status,
							email_verification_failure_reason: user.email_verification_failure_reason,
							email_verification_resend_allowed: user.email_verification_resend_allowed,
						});
					}
				});

				this.setState(
					{
						user_table_data: user_table_data,
						table_load: false,
					},
					() => {
						this.getFilteredTableData(
							this.state.search_value,
							this.state.user_table_data
						);
						// console.log('user_table_data', this.state.user_table_data);
					}
				);
			} else {
				// console.log('filterTableData else');
				let filter_data = [];
				this.state.dataUser.map((user) => {
					let found = _find(user.activation_status_details, {
						application_id: parseInt(value),
					});
					let status = 0;
					if (found) {
						if (found.is_active) {
							// console.log('dataUser2', found);
							status = 1;
						}
					}
					if (user.role_details && user.role_details.length) {
						let role_det = [];
						user.role_details.map((app) => {
							if (app.application_id == parseInt(value)) {
								role_det = _filter(user.role_details, {
									application_id: parseInt(value),
								});
								// console.log('role_det_', status);
							}
						});
						if (role_det.length) {
							user_table_data.push({
								key: user.key,
								name: user.first_name + ' ' + user.last_name,
								login_allowed: this.isLoginAllowed(
									user.role_details
								),
								login: user.login,
								email_verified: user.email_verified
									? true
									: false,
								mobile_verified: user.mobile_verified
									? true
									: false,
								id: user.id,
								first_name: user.first_name,
								last_name: user.last_name,
								designation: user.designation,
								email_id: user.email_id,
								mobile_no: user.mobile_no,
								status: status,
								role_details: role_det,
								all_role_details: user.role_details,
								activation_status_details:
									user.activation_status_details,
								applications: user.applications,
								industries: user.industries,
								industry_sets: user.industry_sets,
								things: user.things,
								territory_ids: user.territory_ids,
								employee_id: user.employee_id,
								tracking_id: user.tracking_id,
								mfa_enabled: user.mfa_enabled,
								sites: user.sites,
								email_verification_delivery_status: user.email_verification_delivery_status,
								email_verification_failure_reason: user.email_verification_failure_reason,
								email_verification_resend_allowed: user.email_verification_resend_allowed,
							});
						}
					}
				});

				let UserManagementObjectData =
					this.state.UserManagementObjectData;
				if (this.props.location.pathname.includes('/user-management')) {
					if (
						this.props.location.pathname.includes('/datoms-x') ||
						this.props.location.pathname.includes('/iot-platform')
					) {
						UserManagementObjectData.iot_user_table.row_data =
							user_table_data.length
								? user_table_data
								: undefined;
					} else {
						UserManagementObjectData.application_user_table.row_data =
							user_table_data.length
								? user_table_data
								: undefined;
					}
				} else {
					UserManagementObjectData.user_table.row_data =
						user_table_data.length ? user_table_data : undefined;
				}

				this.setState(
					{
						user_table_data: user_table_data,
						table_load: false,
						UserManagementObjectData: UserManagementObjectData,
					},
					() => {
						console.log(
							'UserManagementObjectData',
							this.state.UserManagementObjectData
						);
						this.getFilteredTableData(
							this.state.search_value,
							this.state.user_table_data
						);
						if (
							this.props.location.pathname.search(
								'/users/' +
									this.props.match.params.user_id +
									'/edit'
							) > -1
						) {
							let data = _find(this.state.user_table_data, {
								key: parseInt(this.props.match.params.user_id),
							});
							// console.log('dataaa__', data);
							if (data) {
								this.editUser(data);
							}
						}
					}
				);
			}
		} else {
			let UserManagementObjectData = this.state.UserManagementObjectData;
			if (this.props.location.pathname.includes('/user-management')) {
				if (
					this.props.location.pathname.includes('/datoms-x') ||
					this.props.location.pathname.includes('/iot-platform')
				) {
					UserManagementObjectData.iot_user_table.row_data =
						undefined;
				} else {
					UserManagementObjectData.application_user_table.row_data =
						undefined;
				}
			} else {
				UserManagementObjectData.user_table.row_data = undefined;
			}
			this.setState(
				{
					user_table_data: [],
					table_load: false,
					UserManagementObjectData: UserManagementObjectData,
				},
				() => {
					this.getFilteredTableData(
						this.state.search_value,
						this.state.user_table_data
					);
				}
			);
		}
	}

	validateTerritoryAccess() {
		return true;
		let is_territory_access = false;
		if (
			this.props.logged_in_user_role_type == 1 ||
			this.props.logged_in_user_role_type == 2
		) {
			is_territory_access = true;
		}
		return (
			window.innerWidth >= 1080 &&
			this.handleTerritoryChange(is_territory_access)
		);
	}

	isUserAdmin(territory = true) {
		let is_territory_access = false;
		if (
			this.props.logged_in_user_role_type == 1 ||
			(!territory && this.accessToVendorManager())
		) {
			is_territory_access = true;
		}
		return is_territory_access;
	}

	accessToVendorManager() {
		if (
			this.props.logged_in_user_role_type == 1 &&
			((this.props.logged_in_user_client_id == 1 &&
				this.props.client_id != 1) ||
				(this.props.location.pathname.includes(
					'/customer-management'
				) &&
					(this.props.logged_in_user_client_id ==
						this.props.client_id ||
						this.props.logged_in_user_client_id == 1)) ||
				this.props.logged_in_user_client_id == this.props.vendor_id)
		) {
			return true;
		}
		return false;
	}
	//territory field access
	handleTerritoryChange(is_territory_access) {
		return (
			(this.props.location.pathname.includes('/datoms-x') &&
				!this.props.location.pathname.includes(
					'/customer-management'
				) &&
				is_territory_access) ||
			// (this.props.location.pathname.includes('/datoms-x') &&
			// 	this.props.location.pathname.includes('/customer-management') &&
			// 	this.props.isTerVendor === 1)
			// 	 ||
			(this.props.location.pathname.includes('/iot-platform') &&
				!this.props.location.pathname.includes(
					'/customer-management'
				) &&
				is_territory_access)
		);
	}
	openTerritoryPage() {
		let isDatomsXCustomerManagement =
			this.props.location.pathname.includes('/customer-management');
		if (isDatomsXCustomerManagement) {
			this.props.history.push(
				this.platform_slug +
					'/customer-management/' +
					this.props.match.params.customer_id +
					'/territory/view',
				'_blank'
			);
		} else {
			this.props.history.push(
				this.platform_slug + this.user_slug + '/territory/view'
			);
		}
	}
	openRoleDrawer() {
		// console.log('this.platform_slug', this.platform_slug);
		this.setState(
			{
				roleDetailsVisible: true,
			},
			() => {
				if (
					this.props.location.pathname.search(
						'/customer-management'
					) > -1
				) {
					if (this.state.app_drop == 'all') {
						this.props.history.push(
							this.platform_slug +
								'/customer-management/' +
								this.props.match.params.customer_id +
								'/applications/all/roles/view'
						);
					} else {
						this.props.history.push(
							this.platform_slug +
								'/customer-management/' +
								this.props.match.params.customer_id +
								'/applications/' +
								parseInt(this.state.app_drop) +
								'/roles/view'
						);
					}
				} else {
					this.props.history.push(
						this.platform_slug + this.user_slug + '/roles/view'
					);
				}
			}
		);
	}

	closeRoleDrawer(roleAllData) {
		this.setState(
			{
				roleDetailsVisible: false,
				all_role_datas: roleAllData,
				all_roles_list: roleAllData.roles_list,
			},
			() => {
				if (
					this.props.location.pathname.search(
						'/customer-management'
					) > -1
				) {
					if (this.state.app_drop == 'all') {
						this.props.history.push(
							this.platform_slug +
								'/customer-management/' +
								this.props.match.params.customer_id +
								'/applications/all/users/view'
						);
					} else {
						this.props.history.push(
							this.platform_slug +
								'/customer-management/' +
								this.props.match.params.customer_id +
								'/applications/' +
								parseInt(this.state.app_drop) +
								'/users/view'
						);
					}
				} else {
					this.props.history.push(
						this.platform_slug + this.user_slug + '/users/view'
					);
				}
			}
		);
	}

	blockClick() {}

	searchBoxFilter(value) {
		this.setState(
			{
				search_value: value,
			},
			() => {
				this.getFilteredTableData(value, this.state.user_table_data);
				this.parseURL(value);
			}
		);
	}

	parseURL(parsed) {
		// console.log('entered to parse_2', parsed);
		let parsed_url = queryString.parse(this.props.location.search);
		let query_url = '';
		const urlParams = new URLSearchParams(this.props.location.search);
		if (parsed != '') {
			parsed_url['user_search'] = parsed;
			const userSearch = urlParams.get('search');
			if (urlParams.has('search')) {
				query_url =
					'?search=' +
					userSearch +
					'&user_search=' +
					parsed_url['user_search'];
			} else {
				query_url = '?user_search=' + parsed_url['user_search'];
			}
		} else {
			delete parsed_url['user_search'];
		}
		if (this.props.location.pathname.search('/customer-management') > -1) {
			this.props.history.push(
				this.platform_slug +
					'/customer-management/' +
					this.state.client_id +
					'/applications/' +
					this.state.app_drop +
					'/users/view' +
					query_url
			);
		} else {
			this.props.history.push(
				this.platform_slug + this.user_slug + '/users/view' + query_url
			);
		}
	}

	getFilteredTableData(search_value, user_table_data) {
		if (user_table_data && user_table_data.length) {
			let user_list = [],
				filtered_user_list = [];
			user_table_data.map((config, index) => {
				user_list.push(config);
			});

			user_table_data.map((config, index) => {
				// Text Search
				// console.log('getFilteredTableData mission_det_', config);
				let name_data_string = config.name,
					email_data_string = config.email_id,
					mobile_data_string =
						config.mobile_no && config.mobile_no[0]
							? config.mobile_no[0]
							: '';

				// console.log('getFilteredTableData name_data_string', name_data_string);
				if (search_value != '') {
					if (
						_some(user_list, config) &&
						!(
							search_value.toLowerCase() == '' ||
							name_data_string
								.toLowerCase()
								.search(search_value.toLowerCase()) > -1 ||
							email_data_string
								.toLowerCase()
								.search(search_value.toLowerCase()) > -1 ||
							mobile_data_string
								.toLowerCase()
								.search(search_value.toLowerCase()) > -1
						)
					) {
						user_list.splice(user_list.indexOf(config), 1);
					}
				}
			});

			filtered_user_list = user_list.filter(Boolean);

			this.setState({
				filtered_user_list: filtered_user_list,
			});
		} else {
			this.setState({
				filtered_user_list: [],
			});
		}
	}

	applicationDrop(value) {
		console.log('applicationDrop', value);
		let show = true;
		if (value == 'all') {
			show = false;
		}
		this.setState(
			{
				app_drop: value,
				show_add_edit_role: show,
				page_type: value == 'all' ? 'users' : this.state.page_type,
			},
			() => {
				this.filterTableData(this.state.app_drop);
				// this.fetchUserData();
				if (this.state.page_type == 'users') {
					if (
						this.props.location.pathname.search(
							'/customer-management'
						) > -1
					) {
						if (value == 'all') {
							this.props.history.push(
								this.platform_slug +
									'/customer-management/' +
									this.props.match.params.customer_id +
									'/applications/all/users/view'
							);
						} else {
							this.props.history.push(
								this.platform_slug +
									'/customer-management/' +
									this.props.match.params.customer_id +
									'/applications/' +
									parseInt(value) +
									'/users/view'
							);
						}
					} else {
						this.props.history.push(
							this.platform_slug + this.user_slug + '/users/view'
						);
					}
				} else if (this.state.page_type == 'groups') {
					if (
						this.props.location.pathname.search(
							'/customer-management'
						) > -1
					) {
						if (value == 'all') {
							this.props.history.push(
								this.platform_slug +
									'/customer-management/' +
									this.props.match.params.customer_id +
									'/applications/all/users/view'
							);
						} else {
							this.props.history.push(
								this.platform_slug +
									'/customer-management/' +
									this.props.match.params.customer_id +
									'/applications/' +
									parseInt(value) +
									'/user-groups/view'
							);
						}
					} else {
						this.props.history.push(
							this.platform_slug +
								this.user_slug +
								'/user-groups/view'
						);
					}
				}
			}
		);
	}

	onUserDetailsDrawerClose() {
		this.setState({ show_user_details: false });
	}
	render() {
		const { t } = this.props;
		this.translateFunction(t, this.state.UserManagementObjectData);
		let tooltipTitle = 'Mobile';
		if (this.props.client_id == 392) {
			tooltipTitle = 'Mobile';
		}

		let iotUserTableHeadData =
			this.state.UserManagementObjectData.iot_user_table.head_data;
		let applicationUserTableHeadData =
			this.state.UserManagementObjectData.application_user_table
				.head_data;
		let userTableHeadData =
			this.state.UserManagementObjectData.user_table.head_data;

		iotUserTableHeadData[0].render = (value, row_data, index) => (
			<div>
				{(() => {
					let mob_no = '',
						email = '';
					if (row_data.mobile_no && row_data.mobile_no.length) {
						mob_no = row_data.mobile_no[0];
					}
					if (row_data.email_id && row_data.email_id !== '') {
						email = row_data.email_id;
					}

					let resendEmail = '',
						resendMobile = '';

					if (
						this.state.resend_loading &&
						this.state.resend_loading[row_data.id] &&
						this.state.resend_loading[row_data.id]['email']
					) {
						resendEmail = (
							<div className="resend-invitation-butn load">
								<AntSpin size="small" />
							</div>
						);
					} else {
						if (row_data.email_verified) {
							resendEmail = (
								<div className="resend-invitation">
									<AntTooltip title="Email">
										<CheckCircleOutlined className="resend-icon green" />{' '}
										<div className="resend-sec-text">
											{this.props.t('verified')}
										</div>
									</AntTooltip>
								</div>
							);
						} else {
							if (
								row_data.mobile_verified ||
								row_data.email_verified
							) {
								resendEmail = (
									<div className="resend-invitation">
										<AntTooltip title="Email">
											<CloseCircleOutlined className="resend-icon red" />{' '}
											<div className="resend-sec-text">
												{this.props.t('unverified')}
											</div>
										</AntTooltip>
									</div>
								);
							} else {
								resendEmail = (
									<div
										className="resend-invitation-butn"
										onClick={() =>
											this.resendLink(row_data, 'email')
										}
									>
										<AntTooltip title="Email">
											<RedoOutlined className="resend-icon" />{' '}
											<div className="resend-sec-text">
												{this.props.t? this.props.t('resend_verification'): "Resend Verification"}
											</div>
										</AntTooltip>
									</div>
								);
							}
						}
					}

					if (
						this.state.resend_loading &&
						this.state.resend_loading[row_data.id] &&
						this.state.resend_loading[row_data.id]['mobile']
					) {
						resendMobile = (
							<div className="resend-invitation-butn load">
								<AntSpin size="small" />
							</div>
						);
					} else {
						if (row_data.mobile_verified) {
							resendMobile = (
								<div className="resend-invitation">
									<AntTooltip title={tooltipTitle}>
										<CheckCircleOutlined className="resend-icon green" />{' '}
										<div className="resend-sec-text">
											{this.props.t('verified')}
										</div>
									</AntTooltip>
								</div>
							);
						} else {
							if (
								row_data.mobile_verified ||
								row_data.email_verified
							) {
								resendMobile = (
									<div className="resend-invitation">
										<AntTooltip title={tooltipTitle}>
											<CloseCircleOutlined className="resend-icon red" />{' '}
											<div className="resend-sec-text">
												{this.props.t('unverified')}
											</div>
										</AntTooltip>
									</div>
								);
							} else {
								resendMobile = (
									<div
										className="resend-invitation-butn"
										onClick={() =>
											this.resendLink(row_data, 'mobile')
										}
									>
										<AntTooltip title={tooltipTitle}>
											<RedoOutlined className="resend-icon" />{' '}
											<div className="resend-sec-text">
												{this.props.t? this.props.t('resend_verification'): "Resend Verification"}
											</div>
										</AntTooltip>
									</div>
								);
							}
						}
					}

					let comma = ',';

					if (mob_no == '') {
						resendMobile = '';
						comma = '';
					}

					if (email == '') {
						resendEmail = '';
						comma = '';
					}
					return (
						<div>
							{(() => {
								if (row_data.status == 1) {
									return (
										<div
											className="font-orange cusr-point dspl-inline-flx"
											onClick={() =>
												this.viewUser(row_data.id)
											}
										>
											{value}
										</div>
									);
								} else {
									return (
										<AntTooltip title="Inactive">
											<div className="dspl-inline-flx">
												{value}
												<AntTag
													className="deactive-tag"
													color="red"
												>
													Inactive
												</AntTag>
											</div>
										</AntTooltip>
									);
								}
							})()}
							<div>{row_data.designation}</div>
							<div>
								{/* <span>{row_data.email_id}</span> */}
								{/* <span>{resendEmail}</span> */}
								<EmailVerificationStatus
								   t={this.props.t}
								   email={row_data.email_id}
								   isVerified={row_data.email_verified}
								   showResend={row_data.email_verification_resend_allowed}
								   deliveryStatus={row_data.email_verification_delivery_status}
								   failureReason={row_data.email_verification_failure_reason}
								   onResendClick={this.resendLink}
								   resendLoading={
									   this.state.resend_loading?.[row_data.id]?.["email"]
								   }
								   userData={row_data}
								/>
								{/*{row_data.login_allowed ? (
								) : (
									''
								)}*/}
							</div>
							<div>
								<span>{mob_no}</span>
								<span>{resendMobile}</span>
								{/*{row_data.login_allowed ? (
								) : (
									''
								)}*/}
							</div>
							{row_data.login_allowed ? (
								<div className="last-login">
									{this.props.t('last_login') + " : " + row_data.login}
									{/*{resendButn}*/}
								</div>
							) : (
								''
							)}
						</div>
					);
				})()}
			</div>
		);

		applicationUserTableHeadData[0].render = (value, row_data, index) => (
			<div>
				{(() => {
					let mob_no = '',
						email = '';
					if (row_data.mobile_no && row_data.mobile_no.length) {
						mob_no = row_data.mobile_no[0];
					}
					if (row_data.email_id && row_data.email_id !== '') {
						email = row_data.email_id;
					}
					{
						/*if (row_data.email_id != '' && mob_no != '') {
						email = row_data.email_id + ', ' + mob_no;
					} else if (row_data.email_id != '' && mob_no == '') {
						email = row_data.email_id;
					} else if (row_data.email_id == '' && mob_no != '') {
						email = mob_no;
					}

					let resendButn = <span />;
					if (row_data.login == 'Invitation Sent') {
						if (
							this.state.resend_loading &&
							this.state.resend_loading[row_data.id]
						) {
							resendButn = (
								<div className="resend-invitation-butn load">
									<AntSpin size="small" />
								</div>
							);
						} else {
							resendButn = (
								<div
									className="resend-invitation-butn"
									onClick={() => this.emailResend(row_data)}
								>
									<MailOutlined className="resend-icon" />{' '}
									<div className="resend-sec-text">
										Resend Invitation
									</div>
								</div>
							);
						}
					}*/
					}

					let resendEmail = '',
						resendMobile = '';

					if (
						this.state.resend_loading &&
						this.state.resend_loading[row_data.id] &&
						this.state.resend_loading[row_data.id]['email']
					) {
						resendEmail = (
							<div className="resend-invitation-butn load">
								<AntSpin size="small" />
							</div>
						);
					} else {
						if (row_data.email_verified) {
							resendEmail = (
								<div className="resend-invitation">
									<AntTooltip title="Email">
										<CheckCircleOutlined className="resend-icon green" />{' '}
										<div className="resend-sec-text">
											{this.props.t('verified')}
										</div>
									</AntTooltip>
								</div>
							);
						} else {
							if (
								row_data.email_verified ||
								row_data.mobile_verified
							) {
								resendEmail = (
									<div className="resend-invitation">
										<AntTooltip title="Email">
											<CloseCircleOutlined className="resend-icon red" />{' '}
											<div className="resend-sec-text">
												{this.props.t('unverified')}
											</div>
										</AntTooltip>
									</div>
								);
							} else {
								resendEmail = (
									<div
										className="resend-invitation-butn"
										onClick={() =>
											this.resendLink(row_data, 'email')
										}
									>
										<AntTooltip title="Email">
											<RedoOutlined className="resend-icon" />{' '}
											<div className="resend-sec-text">
												{this.props.t? this.props.t('resend_verification'): "Resend Verification"}
												{/* Resend Verification */}
											</div>
										</AntTooltip>
									</div>
								);
							}
						}
					}

					if (
						this.state.resend_loading &&
						this.state.resend_loading[row_data.id] &&
						this.state.resend_loading[row_data.id]['mobile']
					) {
						resendMobile = (
							<div className="resend-invitation-butn load">
								<AntSpin size="small" />
							</div>
						);
					} else {
						if (row_data.mobile_verified) {
							resendMobile = (
								<div className="resend-invitation">
									<AntTooltip title={tooltipTitle}>
										<CheckCircleOutlined className="resend-icon green" />{' '}
										<div className="resend-sec-text">
											{this.props.t('verified')}
										</div>
									</AntTooltip>
								</div>
							);
						} else {
							if (
								row_data.mobile_verified ||
								row_data.email_verified
							) {
								resendMobile = (
									<div className="resend-invitation">
										<AntTooltip title="Mobile">
											<CloseCircleOutlined className="resend-icon red" />{' '}
											<div className="resend-sec-text">
												{this.props.t('unverified')}
											</div>
										</AntTooltip>
									</div>
								);
							} else {
								resendMobile = (
									<div
										className="resend-invitation-butn"
										onClick={() =>
											this.resendLink(row_data, 'mobile')
										}
									>
										<AntTooltip title={tooltipTitle}>
											<RedoOutlined className="resend-icon" />{' '}
											<div className="resend-sec-text">
												{this.props.t? this.props.t('resend_verification'): "Resend Verification"}
											</div>
										</AntTooltip>
									</div>
								);
							}
						}
					}

					let comma = ',';

					if (mob_no == '') {
						resendMobile = '';
						comma = '';
					}

					if (email == '') {
						resendEmail = '';
						comma = '';
					}

					return (
						<div>
							{(() => {
								if (row_data.status == 1) {
									return (
										<div className="font-orange cusr-point dspl-inline-flx">
											{value}
										</div>
									);
								} else {
									return (
										<AntTooltip title="Inactive">
											<div className="dspl-inline-flx">
												{value}
												<AntTag
													className="deactive-tag"
													color="red"
												>
													Inactive
												</AntTag>
											</div>
										</AntTooltip>
									);
								}
							})()}
							<div>{row_data.designation}</div>
							<div>
								{/* <span>{row_data.email_id}</span>
								<span>{resendEmail}</span> */}
								<EmailVerificationStatus
								   t={this.props.t}
								   email={row_data.email_id}
								   isVerified={row_data.email_verified}
								   showResend={row_data.email_verification_resend_allowed}
								   deliveryStatus={row_data.email_verification_delivery_status}
								   failureReason={row_data.email_verification_failure_reason}
								   onResendClick={this.resendLink}
								   resendLoading={
									   this.state.resend_loading?.[row_data.id]?.["email"]
								   }
								   userData={row_data}
								/>
								{/*{row_data.login_allowed ? (
								) : (
									''
								)}*/}
							</div>
							<div>
								<span>{mob_no}</span>
								<span>{resendMobile}</span>
								{/*{row_data.login_allowed ? (
								) : (
									''
								)}*/}
							</div>
							{row_data.login_allowed ? (
								<div className="last-login">
									{this.props.t('last_login') + " : " + row_data.login}
									{/*{resendButn}*/}
								</div>
							) : (
								''
							)}
						</div>
					);
				})()}
			</div>
		);

		userTableHeadData[0].render = (value, row_data, index) => (
			<div>
				{(() => {
					let mob_no = '',
						email = '';
					if (row_data.mobile_no && row_data.mobile_no.length) {
						mob_no = row_data.mobile_no[0];
					}
					if (row_data.email_id && row_data.email_id !== '') {
						email = row_data.email_id;
					}
					{
						/*if (row_data.email_id != '' && mob_no != '') {
						email = row_data.email_id + ', ' + mob_no;
					} else if (row_data.email_id != '' && mob_no == '') {
						email = row_data.email_id;
					} else if (row_data.email_id == '' && mob_no != '') {
						email = mob_no;
					}

					let resendButn = <span />;
					if (row_data.login == 'Invitation Sent') {
						if (
							this.state.resend_loading &&
							this.state.resend_loading[row_data.id]
						) {
							resendButn = (
								<div className="resend-invitation-butn load">
									<AntSpin size="small" />
								</div>
							);
						} else {
							resendButn = (
								<div
									className="resend-invitation-butn"
									onClick={() => this.emailResend(row_data)}
								>
									<MailOutlined className="resend-icon" />{' '}
									<div className="resend-sec-text">
										Resend Verification
									</div>
								</div>
							);
						}
					}*/
					}

					let resendEmail = '',
						resendMobile = '';

					if (
						this.state.resend_loading &&
						this.state.resend_loading[row_data.id] &&
						this.state.resend_loading[row_data.id]['email']
					) {
						resendEmail = (
							<div className="resend-invitation-butn load">
								<AntSpin size="small" />
							</div>
						);
					} else {
						if (row_data.email_verified) {
							resendEmail = (
								<div className="resend-invitation">
									<AntTooltip title="Email">
										<CheckCircleOutlined className="resend-icon green" />{' '}
										<div className="resend-sec-text">
											{this.props.t('verified')}
										</div>
									</AntTooltip>
								</div>
							);
						} else {
							if (
								row_data.email_verified ||
								row_data.mobile_verified
							) {
								resendEmail = (
									<div className="resend-invitation">
										<AntTooltip title="Email">
											<CloseCircleOutlined className="resend-icon red" />{' '}
											<div className="resend-sec-text">
												{this.props.t('unverified')}
											</div>
										</AntTooltip>
									</div>
								);
							} else {
								resendEmail = (
									<div
										className="resend-invitation-butn"
										onClick={() =>
											this.resendLink(row_data, 'email')
										}
									>
										<AntTooltip title="Email">
											<RedoOutlined className="resend-icon" />{' '}
											<div className="resend-sec-text">
												{this.props.t? this.props.t('resend_verification'): "Resend Verification"}
											</div>
										</AntTooltip>
									</div>
								);
							}
						}
					}

					if (
						this.state.resend_loading &&
						this.state.resend_loading[row_data.id] &&
						this.state.resend_loading[row_data.id]['mobile']
					) {
						resendMobile = (
							<div className="resend-invitation-butn load">
								<AntSpin size="small" />
							</div>
						);
					} else {
						if (row_data.mobile_verified) {
							resendMobile = (
								<div className="resend-invitation">
									<AntTooltip title={tooltipTitle}>
										<CheckCircleOutlined className="resend-icon green" />{' '}
										<div className="resend-sec-text">
											{this.props.t('verified')}
										</div>
									</AntTooltip>
								</div>
							);
						} else {
							if (
								row_data.mobile_verified ||
								row_data.email_verified
							) {
								resendMobile = (
									<div className="resend-invitation">
										<AntTooltip title="Mobile">
											<CloseCircleOutlined className="resend-icon red" />{' '}
											<div className="resend-sec-text">
												{this.props.t('unverified')}
											</div>
										</AntTooltip>
									</div>
								);
							} else {
								resendMobile = (
									<div
										className="resend-invitation-butn"
										onClick={() =>
											this.resendLink(row_data, 'mobile')
										}
									>
										<AntTooltip title={tooltipTitle}>
											<RedoOutlined className="resend-icon" />{' '}
											<div className="resend-sec-text">
												{this.props.t? this.props.t('resend_verification'): "Resend Verification"}
											</div>
										</AntTooltip>
									</div>
								);
							}
						}
					}

					let comma = ',';

					if (mob_no == '') {
						resendMobile = '';
						comma = '';
					}

					if (email == '') {
						resendEmail = '';
						comma = '';
					}

					return (
						<div>
							{(() => {
								if (row_data.status == 1) {
									return (
										<div className="font-orange cusr-point dspl-inline-flx">
											{value}
										</div>
									);
								} else {
									return (
										<AntTooltip title="Inactive">
											<div className="dspl-inline-flx">
												{value}
												<AntTag
													className="deactive-tag"
													color="red"
												>
													Inactive
												</AntTag>
											</div>
										</AntTooltip>
									);
								}
							})()}
							<div>{row_data.designation}</div>
							<div>
								{/* <span>{row_data.email_id}</span>
								<span>{resendEmail}</span> */}
								<EmailVerificationStatus
								   t={this.props.t}
								   email={row_data.email_id}
								   isVerified={row_data.email_verified}
								   showResend={row_data.email_verification_resend_allowed}
								   deliveryStatus={row_data.email_verification_delivery_status}
								   failureReason={row_data.email_verification_failure_reason}
								   onResendClick={this.resendLink}
								   resendLoading={
									   this.state.resend_loading?.[row_data.id]?.["email"]
								   }
								   userData={row_data}
								/>
								{/*{row_data.login_allowed ? (
								) : (
									''
								)}*/}
							</div>
							<div>
								<span>{mob_no}</span>
								<span>{resendMobile}</span>
								{/*{row_data.login_allowed ? (
								) : (
									''
								)}*/}
							</div>
							{row_data.login_allowed ? (
								<div className="last-login">
									{this.props.t('last_login') + " : " + row_data.login}
									{/*{resendButn}*/}
								</div>
							) : (
								''
							)}
						</div>
					);
				})()}
			</div>
		);

		let other_contact = this.other_contact,
			total_users = 0,
			app_list_arr = [],
			role_list = [],
			role_arr = [];

		if (
			this.props.location.pathname.includes('/user-management') &&
			(this.props.location.pathname.includes('/datoms-x') ||
				this.props.location.pathname.includes('/iot-platform'))
		) {
			// console.log('checked__');
			let role_details = [];
			if (this.state.all_roles_list && this.state.all_roles_list.length) {
				this.state.all_roles_list.map((roles) => {
					// console.log('rolesss', roles);
					role_details.push({
						role_id: roles.id,
						role_name: roles.name,
					});
				});
			}

			if (this.props.location.pathname.includes('/datoms-x')) {
				role_arr.push({
					application_id: 12,
					role_details: role_details,
				});

				role_list.push({
					application_id: 12,
					role_details: role_details,
				});
			} else if (this.props.location.pathname.includes('/iot-platform')) {
				role_arr.push({
					application_id: 17,
					role_details: role_details,
				});

				role_list.push({
					application_id: 17,
					role_details: role_details,
				});
			}

			if (this.state.all_app_list && this.state.all_app_list.length) {
				this.state.all_app_list.map((app) => {
					app_list_arr.push({
						id: app.id,
						name: app.name,
					});
				});
			}
		} else {
			if (
				this.state.selected_all_app_list &&
				this.state.selected_all_app_list.length > 0
			) {
				this.state.selected_all_app_list.map((application_detail) => {
					console.log('application_detail_', application_detail);
					let appl_list = _find(this.state.all_app_list, {
						id: application_detail.application_id,
					});
					console.log('appl_list_', appl_list);
					if (appl_list) {
						if (application_detail.application_id != 12) {
							app_list_arr.push({
								id: appl_list.id,
								name: appl_list.name,
							});
						}
					}

					role_arr.push({
						application_id: application_detail.application_id,
						role_details: application_detail.role_details,
					});

					role_list.push({
						application_id: application_detail.application_id,
						role_details: application_detail.role_details,
					});
				});

				app_list_arr = _uniqBy(app_list_arr, 'id');
			}
		}

		this.role_list = JSON.parse(JSON.stringify(role_list));

		let content_text = '';

		if (this.state.selected_user_del) {
			if (this.state.app_drop == 'all') {
				content_text =
					this.state.selected_user_del.first_name +
					' ' +
					this.state.selected_user_del.last_name +
					' from all applications';
			} else {
				let app_detail = _find(this.state.all_app_list, {
					id: parseInt(this.state.app_drop),
				});
				if (app_detail) {
					content_text =
						this.state.selected_user_del.first_name +
						' ' +
						this.state.selected_user_del.last_name +
						' from ' +
						app_detail.name;
				} else if (this.state.app_drop === 12) {
					content_text =
						this.state.selected_user_del.first_name +
						' ' +
						this.state.selected_user_del.last_name;
				}
			}
		}

		let deleteModal = (
			<AntPasswordModal
				customBody={<div className="body-text">{content_text}</div>}
				isVisible={this.state.delete_user_modal}
				title={this.props.t('confirm_delete_msg')}
				okTitle={this.props.t('yes')}
				custom_body_className={`user-delete-modal`}
				onOk={() => this.deleteUserFunction()}
				closeLoading={this.state.deleteLoading}
				onCancel={() => this.closeModal()}
				icon={
					<DeleteOutlined
						style={{ color: '#ffffff', fontSize: '24px' }}
					/>
				}
			/>
		);

		let statusIcon = activateUserWhiteImage;

		if (
			this.state.selected_user_status &&
			this.state.selected_user_status.status == 1
		) {
			statusIcon = deactivateUserWhiteImage;
		}

		let statusModal = (
			<AntPasswordModal
				customBody={
					<div className="body-text">
						{this.state.selected_user_status
							? this.state.selected_user_status.first_name +
							  ' ' +
							  this.state.selected_user_status.last_name
							: ''}
					</div>
				}
				isVisible={this.state.status_user_modal}
				title={
					this.state.selected_user_status
						? (this.props.t? this.props.t('do_you_want') : "Do you want to ") + 
						// ? 'Do you want to ' +
						  (this.state.selected_user_status.status == 1
								? this.props.t('deactivate')
								: this.props.t('activate')) +
						  ' ?'
						: ''
				}
				okTitle={this.props.t('yes')}
				closeLoading={this.state.statusLoading}
				custom_body_className={`user-delete-modal`}
				onOk={() => this.setStatusUser()}
				onCancel={() => this.closeModal()}
				icon={<img src={statusIcon} alt="logo" />}
			/>
		);

		return (
			// <Suspense
			// 	fallback={
			// 		<div>
			// 			<AntLayout className={'contains'}>
			// 				<AntSpin className="align-center-loading" />
			// 			</AntLayout>
			// 		</div>
			// 	}
			// >
				<div id="user_management">
					{window.innerWidth < 576 && this.state.show_user_details ? (
						<UserDetails
							{...this.props}
							dataUser={this.state.dataUser}
							editUser={this.editUser}
							app_drop={this.state.app_drop}
							user_id={this.state.view_user_id}
							resendLink={this.resendLink}
							closeUserDraw={this.closeUserDraw}
							showStatusConfirm={this.showStatusConfirm}
							showDeleteConfirm={this.showDeleteConfirm}
							all_app_list={this.state.all_app_list}
							territoryData={this.state.territoryData}
							all_roles_list={this.state.all_roles_list}
						/>
					) : (
						<>
							{(() => {
								if (this.state.filtered_user_list) {
									let subscribed_user = 0;
									if (
										this.state.all_user_data &&
										this.state.all_user_data
											.total_subscribed_users
									) {
										subscribed_user =
											this.state.all_user_data
												.total_subscribed_users;
									}
									let userAddButton =
										this.state.UserManagementObjectData
											.user_add_button;
									if(
										this.props.logged_in_user_role_type === 10 &&
										!this.props.location.pathname.includes('/customer-management')
									) {
										userAddButton[0].disabled = true;
									}else if (
										this.props.getViewAccess([
											'UserManagement:Add',
										], true) ||
										this.accessToVendorManager()
									) {
										userAddButton[0].disabled = false;
									} else {
										userAddButton[0].disabled = true;
									}
									return (
										<AntLayout className={'contains'}>
											<AntContent className="user-content">
												{window.innerWidth < 576 ? (
													<>
														{!(
															this.state
																.user_table_data &&
															this.state
																.user_table_data
																.length
														) ? (
															<NoDataComponent
																text="No user added!"
																height="100%"
															/>
														) : (
															''
														)}
														<AddPopover
															toggleAdd={() =>
																this.addNewUser()
															}
														/>
													</>
												) : (
													''
												)}
												{!(
													this.state
														.user_table_data &&
													this.state.user_table_data
														.length
												) && window.innerWidth < 576 ? (
													''
												) : (
													<AntRow>
														<AntCol
															span={24}
															gutter={20}
															justify="space-between"
															className="table-container"
														>
															<AntRow
																type="flex"
																align="middle"
															>
																<AntCol
																	className="user-butn-container"
																	md={12}
																	xs={24}
																>
																	{/* {(() => {
															if (
																this.state
																	.UserManagementObjectData
																	.user_add_button
															) {
																return (
																	<AntButton
																		className="user-add-butn"
																		type={
																			'rounded'
																		}
																		onButtonClick={() =>
																			this.addNewUser()
																		}
																		buttons={
																			userAddButton
																		}
																	/>
																);
															}
														})()} */}
																	<SearchInput
																		placeholder =  {
																			t("search_users_by_name_or_email_or_mobile")
																		}
																		// placeholder={
																		// 	'Search Users by Name or Email or Mobile'
																		// }
																		size={
																			'default'
																		}
																		enterButton={
																			false
																		}
																		allowClear={
																			true
																		}
																		value={
																			this
																				.state
																				.search_value
																		}
																		onSearch={(
																			value
																		) =>
																			this.searchBoxFilter(
																				value
																			)
																		}
																		className={
																			'search-user-box'
																		}
																	/>
																	{/* <AntDivider type="vertical" />
														<div className="user-count">
															{t('max_users')}:{' '}
															<span>
																{
																	subscribed_user
																}
															</span>
														</div> */}
																</AntCol>
																<AntCol
																	className="role-butn-container"
																	md={12}
																	xs={4}
																>
																	{(() => {
																		if (
																			this.isTerritoryEnabled 
																			// &&
																			// !this.props.location?.pathname?.includes(
																			// 	'/customer-management'
																			// )
																			// &&
																			// this.validateTerritoryAccess()
																		) {
																			return (
																				<div
																					className="ter-go-to-btn"
																					onClick={() =>
																						this.openTerritoryPage()
																					}
																				>
																					{this.props.t? this.props.t('territory'): "Territory"}
																					{/* Territory */}
																				</div>
																			);
																		}
																	})()}
																	{(() => {
																		if (
																			this
																				.state
																				.UserManagementObjectData
																				.role_button &&
																			window.innerWidth >=
																				576
																		) {
																			return (
																				<div className="user-role-button-container">
																					<AntButton
																						onButtonClick={() =>
																							this.openRoleDrawer()
																						}
																						type={
																							'rounded'
																						}
																						buttons={
																							this
																								.state
																								.UserManagementObjectData
																								.role_button
																						}
																						//className={'user-role-button-container'}
																					/>
																				</div>
																			);
																		}
																	})()}
																	{(() => {
																		if (
																			this
																				.state
																				.UserManagementObjectData
																				.user_add_button &&
																			window.innerWidth >=
																				576
																		) {
																			return (
																				<div className="user-add-button-container">
																					<AntButton
																						//className="user-add-butn"
																						type={
																							'rounded'
																						}
																						onButtonClick={() =>
																							this.addNewUser()
																						}
																						buttons={
																							userAddButton
																						}
																						//className={'user-add-button-container'}
																					/>
																				</div>
																			);
																		}
																	})()}
																</AntCol>
															</AntRow>
															<AntRow className="app-selection">
																<AntCol
																	span={24}
																>
																	{(() => {
																		if (
																			this.props.location.pathname.search(
																				'/customer-management'
																			) >
																			-1
																		) {
																			if (
																				app_list_arr &&
																				app_list_arr.length >
																					1
																			) {
																				return (
																					<AntSelect
																						showSearch
																						style={{
																							width: 300,
																						}}
																						disabled={
																							app_list_arr &&
																							app_list_arr.length ==
																								1
																								? true
																								: false
																						}
																						value={
																							this
																								.state
																								.app_drop
																						}
																						className="application-drop"
																						onChange={(
																							e
																						) =>
																							this.applicationDrop(
																								e
																							)
																						}
																					>
																						{(() => {
																							if (
																								app_list_arr &&
																								app_list_arr.length >
																									1
																							) {
																								return (
																									<AntOption
																										key="all"
																										value="all"
																									>
																										All
																									</AntOption>
																								);
																							}
																						})()}
																						{(() => {
																							return app_list_arr.map(
																								(
																									application
																								) => {
																									console.log(
																										'applicationnn_',
																										application
																									);
																									return (
																										<AntOption
																											key={
																												application.id
																											}
																											value={
																												application.id
																											}
																										>
																											{
																												application.name
																											}
																										</AntOption>
																									);
																								}
																							);
																						})()}
																					</AntSelect>
																				);
																			} else if (
																				app_list_arr &&
																				app_list_arr.length ==
																					0 &&
																				this
																					.props
																					.application_id ==
																					12
																			) {
																				{
																					/*return (
                                                                                        <AntSelect
                                                                                            showSearch
                                                                                            style={{
                                                                                                width: 300,
                                                                                            }}
                                                                                            disabled={
                                                                                                true
                                                                                            }
                                                                                            value={
                                                                                                'no data'
                                                                                            }
                                                                                            className="application-drop"
                                                                                        >
                                                                                            <AntOption
                                                                                                key="no data"
                                                                                                value="no data"
                                                                                            >
                                                                                                No
                                                                                                Application
                                                                                            </AntOption>
                                                                                        </AntSelect>
                                                                                    );*/
																				}
																				return false;
																			}
																		}
																	})()}
																</AntCol>
															</AntRow>
															{/* <AntRow className="search-section">
													<AntCol span={12}> </AntCol>
													{(() => {
														if (
															(this.state
																.UserManagementObjectData
																.iot_user_table
																.row_data ||
																this.state
																	.UserManagementObjectData
																	.user_table ||
																this.state
																	.UserManagementObjectData
																	.application_user_table
																	.row_data) &&
															this.props.location.pathname.includes(
																'/customer-management'
															)
														) {
															return (
																<AntCol
																	className="text-container"
																	span={12}
																>
																	<div className="value">
																		{(() => {
																			let total_user = 0,
																				subscribed_user = 0;
																			if (
																				this
																					.state
																					.all_user_data &&
																				this
																					.state
																					.all_user_data
																					.total_added_users
																			) {
																				total_user = this
																					.state
																					.all_user_data
																					.total_added_users;
																			}
																			if (
																				this
																					.state
																					.all_user_data &&
																				this
																					.state
																					.all_user_data
																					.total_subscribed_users
																			) {
																				subscribed_user = this
																					.state
																					.all_user_data
																					.total_subscribed_users;
																			}
																			return (
																				<div>
																					<span>
																						{
																							total_user
																						}
																					</span>{' '}
																					/{' '}
																					<span>
																						{
																							subscribed_user
																						}
																					</span>
																				</div>
																			);
																		})()}
																	</div>
																	<Text
																		className="lable"
																		header_text={
																			this
																				.state
																				.UserManagementObjectData
																				.subscription_text
																		}
																	/>
																</AntCol>
															);
														}
													})()}
												</AntRow> */}
															{/* {(() => {
													if (
														(this.state
															.UserManagementObjectData
															.iot_user_table
															.row_data ||
															this.state
																.UserManagementObjectData
																.user_table ||
															this.state
																.UserManagementObjectData
																.application_user_table
																.row_data) &&
														this.props.location.pathname.includes(
															'/customer-management'
														)
													) {
														return (
															<AntRow
																className="subscription-text"
																type="flex"
																justify="start"
																align="middle"
															></AntRow>
														);
													}
												})()} */}
															{(() => {
																if (1) {
																	if (
																		window.innerWidth <
																		576
																	) {
																		return (
																			<MobileUserList
																				t={this.props.t}
																				userListData={
																					this
																						.state
																						.filtered_user_list
																				}
																				access_key={
																					this
																						.state
																						.access_key
																				}
																				all_role_datas={
																					this
																						.state
																						.all_role_datas
																				}
																				viewUser={
																					this
																						.viewUser
																				}
																				resendLink={(
																					user,
																					type
																				) =>
																					this.resendLink(
																						user,
																						type
																					)
																				}
																				resend_loading={
																					this
																						.state
																						.resend_loading
																				}
																				disableActionsIfUserIsNotAdmin={(
																					user
																				) =>
																					this.disableActionsIfUserIsNotAdmin(
																						user
																					)
																				}
																				getViewAccess={this.props.getViewAccess}
																				accessToVendorManager={() =>
																					this.accessToVendorManager()
																				}
																				showStatusConfirm={(
																					user
																				) =>
																					this.showStatusConfirm(
																						user
																					)
																				}
																				editUser={(
																					user
																				) =>
																					this.editUser(
																						user
																					)
																				}
																				showDeleteConfirm={(
																					user
																				) =>
																					this.showDeleteConfirm(
																						user
																					)
																				}
																				addNewUser={() =>
																					this.addNewUser()
																				}
																			/>
																		);
																	} else {
																		return (
																			<div
																				className={
																					this
																						.props
																						.application_id ==
																					17
																						? 'iot-user-table'
																						: 'user-table'
																				}
																			>
																				<WithSuspense>
																					<CustomTable
																						t={this.props.t}
																						of={t(
																							'of'
																						)}
																						items={t(
																							'items'
																						)}
																						loading={
																							this
																								.state
																								.table_load
																						}
																						config_data={
																							this.props.location.pathname.includes(
																								'/user-management'
																							)
																								? this.props.location.pathname.includes(
																										'/datoms-x'
																								) ||
																								this.props.location.pathname.includes(
																										'/iot-platform'
																								)
																									? this
																											.state
																											.UserManagementObjectData
																											.iot_user_table
																											.config
																									: this
																											.state
																											.UserManagementObjectData
																											.application_user_table
																											.config
																								: this
																										.state
																										.UserManagementObjectData
																										.user_table
																										.config
																						}
																						columns={
																							this.props.location.pathname.includes(
																								'/user-management'
																							)
																								? this.props.location.pathname.includes(
																										'/datoms-x'
																								) ||
																								this.props.location.pathname.includes(
																										'/iot-platform'
																								)
																									? iotUserTableHeadData
																									: applicationUserTableHeadData
																								: userTableHeadData
																						}
																						dataSource={
																							this
																								.state
																								.filtered_user_list
																						}
																					/>
																				</WithSuspense>
																			</div>
																		);
																	}
																} else {
																	return (
																		<>
																			<NoDataComponent
																				text="No user added!"
																				height="100%"
																			/>
																			{(() => {
																				if (
																					window.innerWidth <
																					576
																				) {
																					return (
																						<AddPopover
																							toggleAdd={() =>
																								this.addNewUser()
																							}
																						/>
																					);
																				}
																			})()}
																		</>
																	);
																}
															})()}
														</AntCol>
													</AntRow>
												)}
												{(() => {
													let roleType = 0,
														selected = null;
													if (
														this.state
															.selected_user_data
															?.role_details
													) {
														selected = _find(
															this.state
																.selected_user_data
																.role_details,
															{
																application_id:
																	this.props
																		.application_id,
															}
														);
													}

													if (
														selected &&
														selected.role_type
													) {
														roleType =
															selected.role_type;
													}

													// console.log('UserManagementObjectData__', this.state.UserManagementObjectData);
													if (
														this.state
															.UserManagementObjectData
															.add_user_form &&
														((this.props.location.pathname.includes(
															'/users/add'
														) &&
															(this.props.getViewAccess(
																[
																	'UserManagement:Add',
																],
																true
															) ||
																this.accessToVendorManager())) ||
															(this.isActionEnabled(
																[
																	'UserManagement:Edit',
																],
																roleType
															) &&
																this.props.location.pathname.includes(
																	'/users/' +
																		this
																			.props
																			.match
																			.params
																			.user_id +
																		'/edit'
																)))
													) {
														return (
															<WithSuspense>
																<AddUserDrawer
																	t={t}
																	things_list={
																		this.state
																			.things_list
																	}
																	vendor_type={
																		this.state
																			.vendor_type
																	}
																	customer_type={
																		this.state
																			.customer_type
																	}
																	location={
																		this.props
																			.location
																	}
																	history={
																		this.props
																			.history
																	}
																	all_contact_details={
																		this.state
																			.all_contact_details
																	}
																	platform_slug={
																		this
																			.platform_slug
																	}
																	user_slug={
																		this
																			.user_slug
																	}
																	add_user_form={
																		this.state
																			.UserManagementObjectData
																			.add_user_form
																	}
																	fetchUserData={(
																		client_id,
																		app_drop,
																		loading=true
																	) =>
																		this.fetchUserData(
																			client_id,
																			app_drop,
																			loading
																		)
																	}
																	app_drop={
																		this.state
																			.app_drop
																	}
																	client_id={
																		this.state
																			.client_id
																	}
																	customer_vendor_id={
																		this.props
																			.client_id
																	}
																	vendor_id={
																		this.props
																			.vendor_id
																	}
																	all_app_list={
																		this.state
																			.all_app_list
																	}
																	show_add_draw={
																		this.state
																			.show_add_draw
																	}
																	selected_all_app_list={
																		this.state
																			.selected_all_app_list
																	}
																	application_id_arr={
																		this.state
																			.application_id_arr
																	}
																	selected_user_data={
																		this.state
																			.selected_user_data
																	}
																	user_lists={
																		this.state
																			.user_lists
																	}
																	user_table_data={
																		this.state
																			.user_table_data
																	}
																	match={
																		this.props
																			.match
																	}
																	all_roles_list={
																		this.state
																			.all_roles_list
																	}
																	application_id={
																		this.props
																			.application_id
																	}
																	customer_list_arr={
																		this.state
																			.all_customer_data
																	}
																	onClose={() =>
																		this.closeUserDraw()
																	}
																	role_list={
																		this
																			.role_list
																	}
																	fetchCustomerDetails={() =>
																		this.fetchCustomerDetails()
																	}
																	thing_list_obj={
																		this.state
																			.thing_list_obj
																	}
																	isTerritoryEnabled={this.isTerritoryEnabled}
																	treeData={
																		this.state
																			.treeData ||
																		[]
																	}
																	territoryData={
																		this.state
																			.territoryData ||
																		[]
																	}
																	isTerVendor={
																		this.props
																			.isTerVendor
																	}
																	topNodeId={
																		this.state
																			.topNodeId
																	}
																	logged_in_user_role_type={
																		this.props
																			.logged_in_user_role_type
																	}
																	plan_description={this.getModifiedPlanRoleIds(
																		this.props
																			.plan_description
																	)}
																	plan_appwise_map={
																		this.state
																			.plan_appwise_map
																	}
																	accessToVendorManager={
																		this
																			.accessToVendorManager
																	}
																	logged_in_user_id={
																		this.props
																			.user_id
																	}
																	enabled_features={
																		this.props
																			.enabled_features
																	}
																	sitesList={
																		this.state
																			.sitesList ||
																		[]
																	}
																	siteWiseAssets={
																		this.state
																			.siteWiseAssets ||
																		{}
																	}
																	showSiteField={this.showSiteField}
																/>
															</WithSuspense>
														);
													}
												})()}
												{(() => {
													//Role list
													if (
														this.state
															.UserManagementObjectData
															.role_button &&
														window.innerWidth >=
															576 &&
														this.state
															.UserManagementObjectData
															.role_view_draw &&
														(this.props.location.pathname.includes(
															'/roles/view'
														) ||
															this.props.location.pathname.includes(
																'/roles/add'
															) ||
															this.props.location.pathname.includes(
																'/roles/' +
																	this.props
																		.match
																		.params
																		.role_id +
																	'/edit'
															))
													) {
														return (
															<WithSuspense>
																<RoleListDrawer
																	plan_description={this.getModifiedPlanRoleIds(
																		this.props
																			.plan_description
																	)}
																	t={t}
																	location={
																		this.props
																			.location
																	}
																	history={
																		this.props
																			.history
																	}
																	platform_slug={
																		this
																			.platform_slug
																	}
																	user_slug={
																		this
																			.user_slug
																	}
																	app_drop={
																		this.state
																			.app_drop
																	}
																	client_id={
																		this.state
																			.client_id
																	}
																	match={
																		this.props
																			.match
																	}
																	role_view_draw={
																		this.state
																			.UserManagementObjectData
																			.role_view_draw
																	}
																	getViewAccess={this.props.getViewAccess}
																	closeRoleDrawer={
																		this
																			.closeRoleDrawer
																	}
																	fetchCustomerDetails={() =>
																		this.fetchCustomerDetails()
																	}
																	show_add_edit_role={
																		this.state
																			.show_add_edit_role
																	}
																	application_id={
																		this.props
																			.application_id
																	}
																	isActionEnabled={(
																		accessKey,
																		roleType
																	) =>
																		this.isActionEnabled(
																			accessKey,
																			roleType
																		)
																	}
																	logged_in_user_role_type={
																		this.props
																			.logged_in_user_role_type
																	}
																/>
															</WithSuspense>
														);
													}
												})()}
											</AntContent>
										</AntLayout>
									);
								} else if (this.state.unauthorised_access) {
									return (
										<AntLayout className={'contains'}>
											<AntLayout>
												<AntContent className="contain">
													<AntRow
														type="flex"
														justify="space-around"
														className="device-details"
													>
														<div className="no-data-text">
															<AntAlert
																message="Access Denied"
																description={
																	this.state
																		.unauthorised_access_msg
																}
																type="error"
															/>
														</div>
													</AntRow>
												</AntContent>
											</AntLayout>
										</AntLayout>
									);
								} else {
									return (
										<AntLayout className={'contains'}>
											<Loading show_logo={false} />
										</AntLayout>
									);
								}
							})()}
						</>
					)}
					{deleteModal}
					{statusModal}
					<AntDrawer
						title="User Details"
						className={
							'add-edit-user-drawer ' +
							(this.props.location.pathname.includes('/edit')
								? 'user-edit-draw'
								: '')
						}
						width={589}
						placement="right"
						closable={true}
						onClose={this.closeUserDraw}
						visible={
							window.innerWidth > 576 &&
							this.state.show_user_details
						}
						destroyOnClose={true}
						mask={true}
						maskClosable={false}
						// getContainer={true}
					>
						{/* <h2>User Details</h2> */}
						<UserDetails
							{...this.props}
							dataUser={this.state.dataUser}
							editUser={this.editUser}
							isDesktop={true}
							app_drop={this.state.app_drop}
							user_id={this.state.view_user_id}
							resendLink={this.resendLink}
							closeUserDraw={this.closeUserDraw}
							showStatusConfirm={this.showStatusConfirm}
							showDeleteConfirm={this.showDeleteConfirm}
							all_app_list={this.state.all_app_list}
							territoryData={this.state.territoryData}
							all_roles_list={this.state.all_roles_list}
						/>
					</AntDrawer>
				</div>
			// </Suspense>
		);
	}
}
