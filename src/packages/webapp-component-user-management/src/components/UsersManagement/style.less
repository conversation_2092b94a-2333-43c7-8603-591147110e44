@import '../../../../../styles/default-color.less';

.users-actions-overlay {
	width: 224px;
	box-shadow: 0px 9px 16px #0000001a;
	border-radius: 11px;
	.ant-dropdown-menu {
		border-radius: 11px;
		padding: 10px 0;
		box-shadow: none;
		.ant-dropdown-menu-item {
			color: #808080;
			padding: 8px 26px;
			font-size: 16px;
			.resend-icon,
			.logo-img {
				margin-right: 10px;
			}
		}
	}
}
#user_management {
	background: #fff;
	color: #232323;
	font-size: 13px;

	.green {
		color: green !important;
	}

	.red {
		color: red !important;
	}

	.mar-rt-10 {
		margin-right: 10px;
	}

	.deactive-tag {
		margin-left: 15px;
		border-radius: 10px;
		font-size: 9px;
	}

	.contains {
		// margin-top: 53px;
		// margin-left: 180px;
		// transition: 0.5s;
		background: #fff;
		height: calc(100vh - 80px) !important;
		overflow: hidden;
		overflow-y: auto;
		position: relative;
		z-index: 2;

		.resend-invitation-butn {
			width: fit-content;
			cursor: pointer;
			font-size: 11px;
			display: inline-block;
			margin-left: 10px;
			font-style: italic;
			transition: all 0.3s;

			.resend-icon {
				font-size: 11px;
			}

			.resend-sec-text {
				display: inline;
			}

			&:hover {
				color: @defaultColor;
			}

			&.load {
				cursor: default !important;
				margin-left: 25px;
			}
		}

		.resend-invitation {
			width: fit-content;
			font-size: 11px;
			display: inline-block;
			margin-left: 8px;
			font-style: italic;

			.resend-icon {
				font-size: 11px;
			}

			.resend-sec-text {
				display: inline;
			}

			&.load {
				cursor: default !important;
				margin-left: 25px;
			}
		}

		.user-content {
			.category-select {
				.select-container {
					justify-content: left !important;

					.flex-label {
						display: flex;
						align-items: center;
						width: 25% !important;

						.header-text {
							font-size: 14px;
						}
					}
				}
			}

			.table-container {
				.dsp-flex {
					display: flex;
					flex-wrap: wrap;
				}

				.status-btn {
					right: 100px !important;
				}

				.ant-pagination-mini .ant-pagination-item {
					margin: 0 3px !important;
				}

				.user-butn-container {
					display: flex;
					align-items: center;
					margin: 0 auto;

					.search-user-box {
						margin-top: 0px;
						width: 100%;
						margin: 0 auto;

						.ant-input-affix-wrapper {
							width: 450px !important;
							margin: 0 auto;

							.ant-input-prefix {
								top: 12px;
								color: #808080 !important;
							}

							.ant-input {
								width: 100% !important;
								margin: 0 auto;
								background-color: #f6f8fc !important;
								box-shadow: none !important;
								height: 30px !important;

								&::placeholder {
									color: #808080;
									opacity: 1;
								}
							}
						}
					}

					.ant-divider-vertical {
						height: 20px !important;
					}

					.button-container {
						margin-left: 10px;

						.ant-btn {
							border-radius: 20px;
							box-shadow: 5px 5px 10px rgb(230, 230, 230);
						}

						&.block-btn {
							.ant-btn {
								box-shadow: none !important;
							}
						}
					}
				}

				.role-butn-container {
					margin-top: 10px;
					display: flex;
					justify-content: flex-end;
					align-items: center;

					.ter-go-to-btn {
						display: flex;
						align-items: center;
						justify-content: center;
						width: 84px;
						height: 32px;
						margin-right: 10px;
						color: @defaultColor;
						border: 1px solid @defaultColor;
						border-radius: 25px;
						cursor: pointer;

						&:hover {
							filter: brightness(1.4);
						}
					}

					.rounded-button-container {
						float: right;
						margin-right: 5px;
						flex-direction: row-reverse;

						.button-ring {
							margin-left: 10px !important;
						}

						.ant-btn {
							border-radius: 20px !important;
						}
					}

					.user-role-button-container {
						.rounded-button-container {
							.ant-btn {
								background: #fff !important;
								color: @defaultColor !important;
								border: 1px solid @defaultColor;

								&:hover {
									filter: brightness(1.2);
								}
							}
						}
					}

					.user-add-button-container {
						.rounded-button-container {
							.ant-btn {
								color: #fff !important;
								background: @defaultColor !important;
							}
						}
					}
				}

				.app-selection {
					margin-left: 10px;
				}

				.search-section {
					.text-container {
						padding-right: 20px;

						.value {
							font-size: 20px;
							font-weight: 500;
							text-align: right;
						}

						.head-text {
							.header-text {
								text-align: right;
							}
						}
					}
				}

				.link-button {
					cursor: pointer;
					display: flex;
					align-items: center;
					transition: all 0.5s;
					margin-top: 20px;
					margin-left: 10px;
					position: absolute;
					z-index: 2;
					font-size: 14px;

					&:hover {
						color: @defaultColor;
					}

					.link-icon {
						margin-right: 5px;
					}
				}

				.normal-button {
					margin-top: 10px;
					position: absolute;
					z-index: 2;
					font-size: 14px;
				}

				.category-select {
					position: relative;
					padding-right: 10px;
					margin-left: 10px;

					.category-button-container {
						display: flex;
						justify-content: flex-end;
					}
				}

				.user-table,
				.iot-user-table {
					// padding-right: 5px;
					position: relative;
					margin: 0 10px;

					.antD-table-class .ant-table {
						box-shadow: none !important;
					}

					&.no-data {
						text-align: center;
						margin-top: 50px;
						font-weight: 500;
					}

					.head-name {
						font-weight: bold;
						margin-right: 40px;
					}

					.tot-user {
						margin-left: 40px;
					}

					.menu-icon {
						font-size: 20px;
						// width: 9px;
						cursor: pointer;
						transition: 0.5s all;

						&:hover {
							color: @defaultColor;
						}

						&.disabled {
							cursor: not-allowed !important;

							&:hover {
								color: rgba(0, 0, 0, 0.85) !important;
							}
						}
					}

					.app-role-sec {
						display: flex;
						border: 1px solid #808080;
						font-size: 10px;
						padding: 4px;
						color: #808080;
						border-radius: 8px;
						margin-right: 15px;
						margin-bottom: 4px;
						width: fit-content;
						align-items: center;

						.app-sec {
							padding-right: 5px;
							border-right: 1px solid #808080;
						}

						.role-sec {
							padding-left: 5px;
						}
					}

					.ant-table-container {
						// max-height: calc(100vh - 230px) !important;
						// overflow: auto !important;

						.ant-table-thead {
							background: #f2eeee !important;
							  .ant-table-column-has-sorters{
								background: #f2eeee !important;
   							  }
							// .ant-table-column-sorters-with-tooltip {
							// 	background: #f2eeee !important;
							// }
						}

						.font-orange {
							color: @defaultColor;
						}

						.cusr-point{
							cursor: pointer;
						}

						.last-login {
							font-size: 11px;
							color: #c4c2c2;
						}
					}
				}
			}
		}
	}

	.contains.collapsed-side {
		margin-left: 80px !important;
		transition: 0.5s;
	}
}

.user-delete-modal {
	.body-text {
		text-align: center;
		margin: 0 auto;
	}
}

.user-edit-draw {
	.content-body {
		.form-container {
			height: calc(100vh - 180px) !important;
		}
	}
}

.add-edit-user-drawer {
	.ant-drawer-header .ant-drawer-title {
		padding-left: 20px;
		color: #232323;
		font-weight: 600;
	}

	.ant-drawer-body {
		padding: 8px 0 24px 0;
		overflow: hidden;

		.form-container {
			.ant-legacy-form-item {
				.ant-input,
				.ant-select .ant-select-selector {
					min-height: 35px;
				}

				.ant-tree-select {
					.ant-select-selector {
						border-radius: 4px !important;
					}
				}
			}
		}

		.phone-input {
			.react-tel-input .form-control {
				width: 100%;
			}
		}

		.drawer-actions-box {
			.button-container {
				&:first-child {
					.button-ring {
						.ant-btn {
							border: 1px solid #808080 !important;
							border-radius: 5px;
							color: #808080 !important;
						}
					}
				}

				&:nth-child(2) {
					.button-ring {
						.ant-btn {
							background: @defaultColor 0% 0% no-repeat padding-box !important;
							border-radius: 5px;
						}
					}
				}
			}
		}
	}
}

.add-edit-user-drawer,
.add-edit-usergroup-drawer,
.add-edit-role-drawer {

	.content-body {
		height: calc(100vh - 160px);
		overflow: hidden;
		overflow-y: auto;

		.has-error {
			.ant-legacy-form-explain,
			.ant-legacy-form-split {
				font-weight: 600;
			}
		}

		.mar-top-17 {
			margin-top: 17px;
		}

		.mar-top--6 {
			margin-top: -6px;
		}

		.mar-bot-40 {
			margin-bottom: 40px;
		}

		.email-mob-container {
			padding: 0 8px;
		}

		.resend-invitation-butn {
			width: fit-content;
			cursor: pointer;
			font-size: 11px;
			display: inline-block;
			margin-left: 10px;
			font-style: italic;
			transition: all 0.3s;

			.resend-icon {
				font-size: 11px;
			}

			.resend-sec-text {
				display: inline;
			}

			&:hover {
				color: @defaultColor;
			}

			&.load {
				cursor: default !important;
				margin-left: 25px;
			}
		}

		.ant-legacy-form-item-label {
			font-weight: 600 !important;
		}

		.ant-drawer-close {
			display: none !important;
		}

		.ant-drawer-content-wrapper {
			width: 50% !important;
		}

		.access-tree {
			overflow: hidden;
			overflow-y: auto;
			height: calc(100vh - 390px);
		}

		.form-container {
			overflow: hidden;
			overflow-y: auto;
			//height: calc(100vh - 270px);
			height: calc(100vh - 180px);
			padding: 0;
			> .ant-row, .user-add-edit-role {
				padding: 0 22px 0 44px;
			}
			
			.user-add-edit-role {
				background: #FBFBFB;
				padding-top: 16px;
			}
		}

		//.select-contact,
		.select-role {
			.lable-txt {
				// width: 50%;
				color: #232323;
				display: inline-block;
				padding-bottom: 8px;
			}

			// .contact-select {
			// 	width: 100%;
			// }

			// .ant-legacy-form-item-children {
			// 	display: flex !important;
			// 	align-items: center;
			// }

			// .show-form-link {
			// 	color: @defaultColor;
			// 	opacity: 0.6;
			// 	transition: all 0.5s;
			// 	cursor: pointer;
			// 	width: 40%;
			// 	margin-left: 20px;

			// 	&:hover {
			// 		opacity: 1;
			// 	}
			// }
		}
	}
}

.add-edit-user-drawer {
	.content-body {
		height: calc(100vh - 136px);
		.form-container {
			height: calc(100vh - 144px);
		}
	}
	.drawer-actions-box {
		padding-top: 7px !important;
	}
}

.role-details {
	transform: translateX(0px) !important;

	.top-section {
		display: flex;
		position: relative;
		margin-bottom: 10px;
	}

	.ant-drawer-content-wrapper .ant-drawer-content {
		padding-bottom: 0 !important;
	}

	.ant-drawer-body {
		overflow-y: auto !important;
		height: calc(100vh - 60px) !important;
	}

	.collapse {
		margin-top: 60px;

		.ant-collapse-content {
			color: #232323 !important;

			.ant-collapse-content-box {
				padding: 5px !important;
				margin-left: 35px;
			}
		}

		.ant-collapse-header {
			display: flex !important;
			align-items: center !important;
			>.ant-collapse-header-text {
				flex: none;
			}
		}

		.collapse-header-sec {
			display: flex;
			align-items: center;
			margin-left: 10px;

			.role-name {
				width: 300px;
			}

			.divider {
				width: 1px;
				height: 40px;
				background-color: #c4c2c2;
				margin: 0 40px;
			}

			.desc-icon {
				transition: all 0.3s;

				&:hover {
					color: @defaultColor;
				}
			}

			.action-icon {
				margin-left: 20px;
				transition: all 0.3s;

				&:hover {
					color: @defaultColor;
				}
			}

			.app-tag {
				font-size: 10px;
				padding: 5px;
				border: 1px solid;
				border-radius: 10px;
				margin-left: 30px;
			}
		}

		.ant-collapse
			> .ant-collapse-item
			> .ant-collapse-header
			.ant-collapse-arrow {
			line-height: 55px;
		}
	}
}

.add-edit-role-drawer {
	z-index: 1202 !important;
	.ant-drawer-body {
		overflow: hidden;
	}
	.ant-legacy-form {
		height: calc(100vh - 180px) !important;
		overflow: hidden !important;
		overflow-y: auto !important;
		margin-top: 0;
	}

	.role-access-tree {
		.ant-tree-checkbox-checked {
			.ant-tree-checkbox-inner {
				background-color: @defaultColor !important;
				border-color: @defaultColor !important;
			}
		}

		.ant-tree-checkbox:hover {
			.ant-tree-checkbox-inner {
				border-color: @defaultColor !important;
			}
		}
	}
}

@media (max-width: 1500px) {
	#user_management {
		.contains {
			.user-content {
				.table-container {
					.iot-user-table {
						.ant-table-container {
							.ant-table-body {
								overflow-x: auto !important;

								/*table {
									width: 1600px !important;
								}*/
							}

							.ant-table-content::-webkit-scrollbar {
								height: 8px !important;
							}
						}
					}

					.user-table {
						.ant-table-container {
							.ant-table-body {
								overflow-x: auto !important;

								// table {
								// 	width: 1600px !important;
								// }
							}

							.ant-table-content::-webkit-scrollbar {
								height: 8px !important;
							}
						}
					}
				}
			}
		}
	}
}

@media (max-width: 1024px) {
	#user_management {
		.contains {
			margin-left: 0px !important;
			margin-right: 0px !important;

			&.collapsed-side {
				margin-left: 0 !important;
			}

			.user-content {
				.table-container {
					.user-table,
					.iot-user-table {
						padding-right: 0 !important;

						.menu-icon {
							margin-left: -5%;
							margin-top: -10px;
						}

						.ant-table-container {
							.ant-table-body {
								table {
									width: 1800px !important;
								}
							}
						}
					}
				}
			}
		}
	}

	.role-details,
	.add-edit-user-drawer {
		.ant-drawer-content-wrapper {
			width: 100% !important;
		}
	}

	.add-edit-role-drawer {
		.ant-drawer-content-wrapper {
			width: 80% !important;
		}
	}
}

/*@media (max-width: 800px) {
	#user_management {
		.contains {
			.table-container {
				.user-table {
					.ant-table-body {
						table {
							width: 1800px !important;
						}
					}
				}
			}
		}
	}
}*/

@media (max-width: 576px) {
	#user_management {
		.contains {
			.user-content {
				.table-container {
					.search-container .ant-input-affix-wrapper .ant-input-suffix .ant-input-clear-icon {
				 		margin-top: 65%;
 					}
					
					.user-butn-container {
						.search-user-box {
							margin: 12px 0 22px 0;
							//width: 95% !important;

							.ant-input-affix-wrapper {
								width: 100% !important;

								.ant-input-prefix {
									left: 24px;
									top: 16px;
								}

								.ant-input {
									width: 100% !important;
									border-radius: 19px !important;
									background: #f6f8fc !important;
									padding-left: 36px !important;
									height: 36px !important;
								}
							}
						}
					}
					#mobile_user_list {
						.ant-list-items{
							padding-bottom: 30px;
						}
						.ant-list-pagination {
							margin-bottom: 16px;
						}
					}
				}
			}
		}
	}

	.add-edit-user-drawer {
		.ant-drawer-body {
			padding: 0 0 0 20px !important;
		}

		.content-body {
			height: calc(100vh - 130px) !important;

			.form-container {
				height: calc(100vh - 150px) !important;
			}
		}

		.datoms-ok-close-footer {
			padding: 15px 24px !important;
		}
	}
}

@media (max-width: 520px) {
	#user_management {
		.contains {
			height: calc(100vh - 110px) !important;

			.user-content {
				.table-container {
					.role-butn-container {
						.ter-go-to-btn {
							display: none;
						}

						.user-role-button-container {
							display: none;
						}
					}
				}
			}

			/*.table-container {
				.user-table {
					.ant-table-scroll {
						width: 350% !important;
					}
				}
			}*/
		}
	}

	.role-details {
		.collapse {
			.collapse-header-sec {
				margin-left: 0 !important;

				.action-buttons {
					display: flex;
				}
			}
		}
	}

	.role-details
		.collapse
		.ant-collapse
		> .ant-collapse-item
		> .ant-collapse-header
		.ant-collapse-arrow {
		line-height: 0 !important;
	}
}

@media (max-width: 499px) {
	.role-details {
		.collapse {
			.collapse-header-sec {
				.role-name {
					width: 184px;
				}
			}
		}
	}
}


@media (max-width: 425px) {
	#user_management {
		.contains {
			.user-content {
				.table-container {
					.user-table,
					.iot-user-table {
						.menu-icon {
							margin-left: 80%;
						}
					}
				}
			}
		}
	}
}

@media (max-width: 399px) {
	.role-details {
		.collapse {
			.collapse-header-sec {
				.role-name {
					width: 155px;
				}
			}
		}
	}
}

@media (max-width: 359px) {
	.role-details {
		.collapse {
			.collapse-header-sec {
				.role-name {
					width: 115px;
				}
			}
		}
	}
}

@media (max-width: 329px) {
	.role-details {
		.collapse {
			.collapse-header-sec {
				.role-name {
					width: 85px;
				}
			}
		}
	}
}

@media (max-width: 320px) {
	#user_management {
		.contains {
			.user-content {
				.table-container {
					.user-butn-container {
						width: 100% !important;
					}

					.role-butn-container {
						width: 100% !important;
					}
				}
			}
		}
	}
}
