#order_details {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: calc(100vh - 52px);
	margin: 0 !important;
	.th-fltr-select {
		display: inline-block;
		margin-left: 8px;
		.ant-select {
			width: 240px !important;
		}
	}
	.head-section {
		padding: 12px 24px;
		background: #fff;
		.order-dtl-light {
			font-size: 14px;
			color: #808080;
			font-weight: normal;
		}
		.inner-head {
			display: flex;
			justify-content: space-between;
			align-items: center;
			max-width: 1620px;
			margin: auto;
			.close-page-order {
				display: flex;
			}
			.customer-details-section {
				display: flex;
				.customer-details-right {
					.cust-name {
						font-size: 20px;
						font-weight: 600;
					}

					.cust-details {
						display: flex;
						.details-span {
							font-size: 14px;
							color: #808080;
							padding-right: 16px;
							&.rent-br-left {
								padding: 0 16px;
								border-left: 1.4px solid #808080;
								.rent-doc-status-component {
									margin-left: 0;
								}
								&.btn {
									font-style: italic;
									cursor: pointer;
									color: #7686a1;
									.rent-arrow {
										//transform: rotate(315deg);
										//	padding-right: 4px;
										padding-left: 3px;
									}
								}
							}
							&.rent-tag-br {
								color: #bf5823;
								font-weight: 500;
								font-style: italic;
							}
						}
					}
				}
			}
			.close-page-order {
				display: flex;
				align-items: center;
				
				.close-order {
					//margin-right: 60px;
					margin-left: 42px;
					.close-label {
						color: #232323;
						font-size: 14px;
						margin-right: 8px;
					}
					.ant-btn-primary {
						background: #ff8500 !important;
					}
					.ant-btn-primary[disabled] {
						background: #f5f5f5 !important;
					}
				}
				.order-status {
					padding: 7px 20px;
					border-radius: 9px;
					color: #ff8500;
					font-size: 16px;
					font-weight: 500;
					margin-right: 32px;
					display: flex;
					flex-direction: column;
					align-items: center;
					.update-text {
						font-size: 12px;
						color: #808080;
					}
				}
				.approval_pending {
					color: #ff8500;
				}
				.delayed {
					color: #ff0900;
				}
				.completed {
					color: #0eae33;
				}
				.vendor_cancelled {
					color: #c4c2c2;
				}
				.close-icon {
					font-size: 28px;
					cursor: pointer;
					color: #808080;
				}
			}
		}
	}
	.body-section {
		padding: 12px 24px;
		background: #f6f7f8;
		flex: 1;
		overflow: hidden auto;
		display: flex;
		flex-direction: column;
	}
	.sections {
		background: #fff;
		box-shadow: 8px 12px 24px #0000000a;
		border-radius: 10px;
		max-width: 1620px;
	}
	.kpi-section {
		display: flex;
		justify-content: space-around;
		align-items: center;
		flex-wrap: wrap;
		margin: 10px auto 0 auto;
		padding: 18px 28px;
		width: 100%;

		.kpi {
			padding: 0 10px;
			text-align: center;

			.value {
				font-size: 20px;
				font-weight: 500;
				color: #2f2e41;
			}

			.title {
				font-size: 14px;
				color: #808080;
			}
			.oddtl-asset-name {
				font-weight: normal;
			}
			.odrdtl-address {
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				max-width: 208px;
				color: #2f2e41;
				font-size: 14px;
				font-weight: normal;
			}
			#datoms_maps_opener {
				margin: auto;
				justify-content: center;
				.maps-opener-text {
					font-size: 14px;
					font-weight: normal;
				}
				> span {
					color: #607eff;
				}
			}
		}
	}
	.notes-section{
		// margin-top: 22px;
		margin: 22px auto 0 auto;
		width: 100%;
		padding: 16px 32px !important;
		> p{
			color: #232323;
			font-size: 16px;
			font-weight: 600;
		//	margin-bottom: 24px;
		}
		.rental-notes-body {
			justify-content: flex-start;
			margin: 0 !important;
			padding: 28px 0 0 !important;
		}
	}
	.table-section {
		width: 100%;
		height: 409px;
		margin: 22px auto 0 auto;
		padding: 18px 28px;
		.table-header {
			display: flex;
			justify-content: space-between;
			margin-bottom: 8px;
			.table-header-left {
				font-size: 16px;
				font-weight: 600;
				.thing-wise-filter {
					display: inline-block;
					margin-left: 8px;
					padding: 5px 5px 4px 5px;
					box-shadow: 0px 3px 5px #00000021;
					border-radius: 5px;
					cursor: pointer;
					position: relative;
					.abs-filter-count {
						position: absolute;
						right: -110%;
						transform: translate(-50%, -50%);
						background: #ff8500 0% 0% no-repeat padding-box;
						border-radius: 10px;
						color: #fff;
						font-size: 12px;
						padding: 1px 6px;
					}
				}
			}
			.table-header-right {
				.switch-label {
					font-size: 16px;
					color: #808080;
					&.active {
						color: #232323;
					}
				}
				.ant-switch.ant-switch-small {
					margin: 0 8px;
				}
			}
		}
		.one-time-header {
			margin-bottom: 6px;
			color: #808080;
			span {
				font-size: 14px;
				color: #000000;
				font-weight: 600;
			}
		}
		.graph-container {
			margin-top: 32px;
		}
		.ant-table-wrapper {
			.ant-pagination.ant-table-pagination.ant-table-pagination-right {
				margin-bottom: 0;
			}
			.ant-table {
				.ant-table-container {
					border: 1px solid #e8e8e8;
					border-radius: 8px;
					// .ant-table-header .ant-table-thead {
					// 	background: #f1eeeb;
					// 	.ant-table-cell {
					// 		padding: 12px 16px;
					// 		color: #808080 !important;
					// 		font-weight: normal !important;
					// 	}
					// }
					// .ant-table-body {
					.ant-table-tbody {
						.ant-table-row {
							.ant-table-cell {
								font-size: 14px !important;
								padding: 8px 14px !important;
							}
						}
						tr[aria-hidden='true'] {
							td {
								padding: 0 !important;
							}
						}
					}
					//	}
				}
			}
		}
	}
	.bottom-section {
		width: 100%;
		display: flex;
		justify-content: space-between;
		margin: 22px auto 0 auto;
		max-width: 1620px;
		flex: 1;
		.bottom-section-left {
			width: 80%;
			display: flex;
			margin-right: 18px;
			min-height: 300px;
			.thing-list-box {
				width: 295px;
				padding-bottom: 14px;
				background: transparent
					linear-gradient(147deg, #fefeff 0%, #f5f4fc 100%) 0% 0%
					no-repeat padding-box;
				border-radius: 10px 0 0 10px;
				.thing-header {
					font-size: 16px;
					font-weight: 600;
					padding: 14px;
				}
				.thing-list {
					max-height: 340px;
					overflow: hidden auto;
					.thing-li {
						cursor: pointer;
						color: #232323;
						font-size: 14px;
						padding: 14px 14px 14px 24px;
						white-space: nowrap;
						text-overflow: ellipsis;
						overflow: hidden;
						&.active {
							background-color: #fff;
							padding-left: 20px;
							border-left: 4px solid #ff8500;
						}
						span {
							display: inline-block;
							height: 8px;
							width: 8px;
							border-radius: 50%;
							margin-right: 6px;
							&.thing-off {
								background-color: #808080;
							}
							&.thing-on {
								background-color: green;
							}
						}
					}
				}
			}
			.thing-details-box {
				padding: 24px 14px 14px;
				flex: 1;
				.thing-details-kpi {
					display: flex;
					justify-content: space-around;
					flex-wrap: wrap;
					&.rent-justity-start {
						justify-content: flex-start;
						> .kpi {
							padding: 0 30px;
						}
					}
					.kpi {
						padding: 0 10px;
						text-align: center;

						.value {
							font-size: 20px;
							font-weight: bold;
							color: #2f2e41;
						}
						.title {
							color: #808080;
							font-size: 14px;
						}
					}
				}
				.thing-rnhr-graph {
					margin: 30px 10px 0;
					padding: 14px;
					background-color: #f6f7f8;
					border-radius: 10px;
					.title {
						color: #232323;
						font-weight: 600;
						margin-bottom: 10px;
						padding-left: 12px;
					}
					.graph-box {
						display: grid;
						place-items: center;
						height: 217px;
						.graph-container {
							width: 100%;
							.highcharts-background {
								fill: transparent;
							}
						}
					}
				}
			}
		}
		.bottom-section-right {
			width: 20%;
			padding: 14px;
			.item-header {
				font-size: 16px;
				font-weight: 600;
				padding-bottom: 14px;
			}
			.item-ul {
				max-height: 326px;
				overflow: hidden auto;
				.item-li {
					padding: 6px;
					display: flex;
					justify-content: space-between;
					border-bottom: 1px solid #808080;
					span {
						&:first-child {
							flex: 1;
							white-space: nowrap;
							text-overflow: ellipsis;
							overflow: hidden;
						}
						&:last-child {
							width: fit-content;
						}
					}
				}
			}
		}
	}
}
.close-rental-modal {
	margin: auto;
	.password-title .ant-divider {
		display: none;
	}
	.text {
		width: 100%;
		text-align: center;
		margin-top: 18px;
	}
}
.thing-wise-overlay {
	border-radius: 6px;
	.ant-dropdown-menu {
		padding: 6px;
		.ant-dropdown-menu-item {
			padding: 8px 12px;
		}
	}
	.close-thing-filter-item {
		text-align: right;
		.close-thing-filter {
			color: #fff;
			background: #ff8500 0% 0% no-repeat padding-box;
			box-shadow: 0px 8px 9px #ff850029;
			border-radius: 8px;
			padding: 6px 14px;
			cursor: pointer;
		}
	}
}

@media (max-width: 1400px) {
	#order_details .bottom-section .bottom-section-left .thing-list-box {
		width: 209px;
	}
}
@media (max-width: 1200px) {
	#order_details .bottom-section {
		.bottom-section-left {
			width: 70%;
			.thing-list-box {
				width: 209px;
			}
			.kpi,
			.thing-control-section {
				margin: 4px;
			}
		}
		.bottom-section-right {
			width: 30%;
		}
	}
}
@media (max-width: 1024px) {
	#order_details
		.table-section
		.ant-table-wrapper
		.ant-table
		.ant-table-container
		.ant-table-tbody
		.ant-table-row
		.ant-table-cell {
		padding: 16px 14px !important;
	}
}
@media (max-width: 960px) {
	#order_details {
		.bottom-section {
			flex-direction: column;
			.bottom-section-left {
				width: 100%;
			}
			.bottom-section-right {
				margin: 18px auto;
				width: 295px;
			}
		}
	}
}
@media (max-width: 880px) {
	#order_details {
		.kpi-section {
			.kpi {
				margin: 4px 0;
			}
		}
	}
}
@media (max-width: 576px) {
	#order_details.mobile {
		height: calc(100vh - 52px - 55px);
		.th-fltr-select {
			width: 100%;
			margin: 10px 0 0 0;
			display: flex;
			justify-content: center;
			.ant-select {
				width: 200px !important;
				.ant-select-selection-placeholder {
					text-align: center;
				}
			}
		}
		.head-section {
			background: #f8f8fa;
			padding: 0;
			height: 57px;
			.inner-head {
				padding: 8px 10px;
				width: 100%;
				.customer-details-right {
					// text-align: center;
					//	min-width: 69%;
					flex: 1;
					// padding: 0 4px;
					padding: 0 4px 0 16px;
					.cust-name {
						font-size: 15px;
						color: #7686a1;
						font-weight: bold;
						white-space: nowrap;
						text-overflow: ellipsis;
						overflow: hidden;
					}
					.cust-details {
						color: #808080;
						font-style: italic;
						font-size: 11px;
					}
					.rent-tag-br {
						color: #bf5823;
						font-size: 11px;
						font-style: italic;
					}
				}
				.close-page-order .close-order {
					// display: flex;
					// flex-direction: column;
					// align-items: center;
					margin-left: 0;
					// .close-label {
					// 	color: #808080;
					// 	font-size: 11px;
					// 	margin-right: 0;
					// }
					// .ant-switch {
					// 	width: fit-content;
					// }
				}
			}
		}
		.rent-mobile-body {
			.ant-tabs-nav-wrap {
				padding-left: 12px;
				justify-content: center;
			}
			.ant-tabs-tabpane {
				padding: 0 22px 0 26px;
				&:nth-child(2),
				&:nth-child(3) {
					padding: 0;
				}
				.tab-body {
					height: calc(100vh - 228px);
					overflow: hidden auto;
					scrollbar-width: none; /* Firefox */
					.tab-order-things {
						padding: 0 12px;
					}
					&::-webkit-scrollbar {
						display: none; /* Safari and Chrome */
					}
					.rent-label {
						color: #808080;
					}
					.rent-value {
						color: #2f2e41;
						font-weight: bold;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}
					.str-label {
						color: #ceb190;
						margin-top: 16px;
					}
					.rent-align-end {
						text-align: end;
					}
					.ant-table-tbody tr td {
						font-size: 12px !important;
						white-space: nowrap;
					}
					.ant-select {
						width: 100%;
					}
					.thing-rnhr-graph {
						.title-header {
							display: flex;
							justify-content: space-between;
							flex-wrap: wrap;
							span:first-child {
								color: #232323;
								font-weight: 600;
							}
						}
						.graph-box {
							display: grid;
							place-items: center;
							.graph-container {
								width: 100%;
								.highcharts-background {
									fill: transparent;
								}
							}
						}
					}
					.odrdtl-address {
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						//	max-width: 208px;
						color: #2f2e41;
						font-size: 14px;
						font-weight: normal;
					}
					#datoms_maps_opener {
						margin: auto;
						//	justify-content: center;
						.maps-opener-text {
							font-size: 14px;
							font-weight: normal;
						}
						> span {
							color: #607eff;
						}
					}
					.notes-section{
						padding: 16px 0px !important;
					}
				}
			}
		}
	}

	#order_details .head-section .inner-head .close-page-order .ant-btn {
		padding-left: 0;

		.anticon-download {
			margin-left: 0 !important;
		}
	}
}
