let datomsxFilterData = [
	{
		optionData: [],
		selectValue: undefined,
		showSearch: true,
		allowClear: true,
		allClearDisabled: true,
		placeholder: 'Account Type',
	},
	{
		type: 'tree_select',
		hideField: true,
		placeholder: 'Territory',
		component_props: {
			treeData: [],
			value: [],
			treeDefaultExpandAll: true,
			treeCheckable: true,
			showCheckedStrategy: "SHOW_PARENT",
			treeCheckStrictly: true,
			maxTagCount: 0,
			maxTagPlaceholder: (omittedValues) =>
				omittedValues.length +
				` territor${omittedValues.length === 1 ? "y" : "ies"} selected`,
			placeholder: 'Select territories',
			filterTreeNode: (search, item) => {
				return (
					item.title
						.toLowerCase()
						.indexOf(search.toLowerCase()) >= 0
				);
			},
		},
	},
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		showSearch: true,
		sorted: true,
		placeholder: 'All Partners',
	},
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		showSearch: true,
		sorted: true,
		placeholder: 'All Clients',
	},
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		sorted: true,
		showSearch: true,
		placeholder: 'All Asset Types',
	},
	{
		optionData: [
			{
				title: 'Data Availability 100%',
				value: '100',
			},
			{
				title: 'Data Availability > 90%',
				value: '>90',
			},
			{
				title: 'Data Availability 60 - 90%',
				value: '60-90',
			},
			{
				title: 'Data Availability < 60%',
				value: '<60',
			},
		],
		selectValue: undefined,
		allowClear: true,
		showSearch: true,
		placeholder: 'Data Availability',
	},
	{
		optionData: [
			{
				title: 'Active',
				value: 'active',
			},
			{
				title: 'Inactive',
				value: 'inactive',
			},
			{
				title: 'Online',
				value: 'online',
			},
			{
				title: 'Offline',
				value: 'offline',
			},{
				title: 'IoT',
				value:1,
			},{
				title: 'Non-IoT',
				value: 0,
			},
		],
		selectValue: [],
		multiSelect: true,
		allowClear: true,
		showSearch: true,
		filterType: 'status',
		placeholder: 'Asset Status',
	},{
		optionData: [],
		selectValue: [],
		allowClear: true,
		showSearch: true,
		placeholder: 'DG Type',
	},{
		optionData: [
			{
				label: <span>Configured</span>,
				title: 'Configured',
				options: [
				  {
					label: <span>KVA</span>,
					value: 'configured_fields-kva',
				  },
				  {
					label: <span>Fuel Tank Capacity</span>,
					value: 'configured_fields-capacity',
				  },
				  {
					label: <span>DG Phase</span>,
					value: 'configured_fields-dg_type',
				  },
				  {
					label: <span>Fuel Sensor Type</span>,
					value: 'configured_fields-fuel_sensor_type',
				  },
				  {
					label: <span>Min. Fuel Fill</span>,
					value: 'configured_fields-min_fuel_fill',
				  },
				  {
					label: <span>Location</span>,
					value: 'configured_fields-location',
				  },
				],
			  },
			  {
				label: <span>Not Configured</span>,
				title: 'Not Configured',
				options: [
				  {
					label: <span>KVA</span>,
					value: 'unconfigured_fields-kva',
				  },
				  {
					label: <span>Fuel Tank Capacity</span>,
					value: 'unconfigured_fields-capacity',
				  },
				  {
					label: <span>DG Phase</span>,
					value: 'unconfigured_fields-dg_type',
				  },
				  {
					label: <span>Fuel Sensor Type</span>,
					value: 'unconfigured_fields-fuel_sensor_type',
				  },
				  {
					label: <span>Min. Fuel Fill</span>,
					value: 'unconfigured_fields-min_fuel_fill',
				  },
				  {
					label: <span>Location</span>,
					value: 'unconfigured_fields-location',
				  },
				],
			},
		],
		group_options: true,
		selectValue: [],
		multiSelect: true,
		allowClear: true,
		showSearch: true,
		filterType: 'Field(s)',
		placeholder: 'Onboarding Status',
	}
];

let iotFilterData = [
	{
		type: 'tree_select',
		hideField: true,
		placeholder: 'Territory',
		component_props: {
			treeData: [],
			value: [],
			treeDefaultExpandAll: true,
			treeCheckable: true,
			showCheckedStrategy: "SHOW_PARENT",
			treeCheckStrictly: true,
			maxTagCount: 0,
			maxTagPlaceholder: (omittedValues) =>
				omittedValues.length +
				` territor${omittedValues.length === 1 ? "y" : "ies"} selected`,
			placeholder: 'Select territories',
			filterTreeNode: (search, item) => {
				return (
					item.title
						.toLowerCase()
						.indexOf(search.toLowerCase()) >= 0
				);
			},
		},
	},
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		showSearch: true,
		sorted: true,
		placeholder: 'All Partners',
	},
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		sorted: true,
		showSearch: true,
		placeholder: 'All Clients',
	},
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		sorted: true,
		showSearch: true,
		placeholder: 'All Asset Types',
	},
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		sorted: true,
		showSearch: true,
		placeholder: 'All Asset',
	},
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		sorted: true,
		showSearch: true,
		placeholder: 'Functional Status',
	},
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		sorted: true,
		showSearch: true,
		placeholder: 'Rental Status',
	},
	{
		optionData: [],
		selectValue: [],
		allowClear: true,
		showSearch: true,
		placeholder: 'DG Type',
	},{
		hideField: true,
		optionData: [
			{
				label: <span>Configured</span>,
				title: 'Configured',
				options: [
				  {
					label: <span>KVA</span>,
					value: 'configured_fields-kva',
				  },
				  {
					label: <span>Fuel Tank Capacity</span>,
					value: 'configured_fields-capacity',
				  },
				  {
					label: <span>DG Phase</span>,
					value: 'configured_fields-dg_type',
				  },
				  {
					label: <span>Fuel Sensor Type</span>,
					value: 'configured_fields-fuel_sensor_type',
				  },
				  {
					label: <span>Min. Fuel Fill</span>,
					value: 'configured_fields-min_fuel_fill',
				  },
				  {
					label: <span>Location</span>,
					value: 'configured_fields-location',
				  },
				],
			  },
			  {
				label: <span>Not Configured</span>,
				title: 'Not Configured',
				options: [
				  {
					label: <span>KVA</span>,
					value: 'unconfigured_fields-kva',
				  },
				  {
					label: <span>Fuel Tank Capacity</span>,
					value: 'unconfigured_fields-capacity',
				  },
				  {
					label: <span>DG Phase</span>,
					value: 'unconfigured_fields-dg_type',
				  },
				  {
					label: <span>Fuel Sensor Type</span>,
					value: 'unconfigured_fields-fuel_sensor_type',
				  },
				  {
					label: <span>Min. Fuel Fill</span>,
					value: 'unconfigured_fields-min_fuel_fill',
				  },
				  {
					label: <span>Location</span>,
					value: 'unconfigured_fields-location',
				  },
				],
			},
		],
		group_options: true,
		selectValue: [],
		multiSelect: true,
		allowClear: true,
		showSearch: true,
		filterType: 'Field(s)',
		placeholder: 'Onboarding Status',
	}
	// {
	// 	optionData: [
	// 		{
	// 			title: 'Data Availability 100%',
	// 			value: '100',
	// 		},
	// 		{
	// 			title: 'Data Availability > 90%',
	// 			value: '>90',
	// 		},
	// 		{
	// 			title: 'Data Availability 60 - 90%',
	// 			value: '60-90',
	// 		},
	// 		{
	// 			title: 'Data Availability < 60%',
	// 			value: '<60',
	// 		},
	// 	],
	// 	selectValue: undefined,
	// 	allowClear: true,
	// 	showSearch: true,
	// 	placeholder: 'Data Availability',
	// },
	// {
	// 	optionData: [
	// 		{
	// 			title: 'Active',
	// 			value: 'active',
	// 		},
	// 		{
	// 			title: 'Inactive',
	// 			value: 'inactive',
	// 		},
	// 		{
	// 			title: 'Online',
	// 			value: 'online',
	// 		},
	// 		{
	// 			title: 'Offline',
	// 			value: 'offline',
	// 		},
	// 	],
	// 	selectValue: [],
	// 	multiSelect: true,
	// 	allowClear: true,
	// 	showSearch: true,
	// 	filterType: 'status',
	// 	placeholder: 'Asset Status',
	// },
];

let applicationFilterData = [
	{
		optionData: [
			{
				title: 'Data Availability 100%',
				value: '100',
			},
			{
				title: 'Data Availability > 90%',
				value: '>90',
			},
			{
				title: 'Data Availability 60 - 90%',
				value: '60-90',
			},
			{
				title: 'Data Availability < 60%',
				value: '<60',
			},
		],
		selectValue: undefined,
		allowClear: true,
		showSearch: true,
		placeholder: 'Data Availability',
	},
	{
		optionData: [
			{
				title: 'Active',
				value: 'active',
			},
			{
				title: 'Inactive',
				value: 'inactive',
			},
			{
				title: 'Online',
				value: 'online',
			},
			{
				title: 'Offline',
				value: 'offline',
			},
		],
		selectValue: [],
		multiSelect: true,
		allowClear: true,
		showSearch: true,
		filterType: 'status',
		placeholder: 'Asset Status',
	},
];

let searchObject = {
	placeholder: 'Search Assets',
	size: 'default',
	value: '',
};

export default {
	datomsxFilterData: datomsxFilterData,
	iotFilterData: iotFilterData,
	applicationFilterData: applicationFilterData,
	searchObject: searchObject,
};
