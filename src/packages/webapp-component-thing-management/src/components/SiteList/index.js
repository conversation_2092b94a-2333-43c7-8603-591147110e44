import React, {Component} from 'react';
import { Link } from 'react-router-dom';
import MoreOutlined from '@ant-design/icons/MoreOutlined';
import CustomizeTable from '@datoms/react-components/src/components/CustomizeTable';
import Loading from '@datoms/react-components/src/components/Loading';
import AntTag from '@datoms/react-components/src/components/AntTag';
import GenericFilter from '@datoms/react-components/src/components/GenericFilter/pages';
import AntMenu from '@datoms/react-components/src/components/AntMenu';
import AntMenuItem from '@datoms/react-components/src/components/AntMenuItem';
import AntDropdown from '@datoms/react-components/src/components/AntDropdown';
import './styles.less'
import moment from 'moment-timezone'
import {
    applyFilterSelect,
    checkAccess,
    fetchAPIData,
    getListQuery,
    getSiteList, getUrlBreak,
    onTableChange,
    onUrlChange, setFilterConfig,
    goToPage,
    checkValidString,
    handleDelete,
    toggleOperationalStatus
} from "./logic";
import PlusOutlined from '@ant-design/icons/PlusOutlined';
import SettingOutlined from '@ant-design/icons/SettingOutlined';
import DeleteOutlined from '@ant-design/icons/DeleteOutlined';
import {getCustomersList, getSiteTypesList} from "../SiteConfiguration/logic";
import defaultConfig from "./defaultConfig";

class SiteList extends Component {
    constructor(props) {
        super(props);
        this.state={
            loading:true,
            table_data:[],
            filtered_table_data:[],
            filter_values:{},
            customer_names:{},
            table_loading:false,
            show_filter:false,
            filterConfig:[...defaultConfig.datomsxFilterData],
            searchObject:defaultConfig.searchObject,
            pageSize:20,
            sort_key:'',
            sort_order:'ASC',
            // totalPage: {},
            site_type_names:{},
            delete_modal_btn_loading:false,
            page:1,
            end_customer:parseInt(props.application_id) !== 12 && parseInt(props.application_id) !== 17
        }
        this.checkAccess=checkAccess.bind(this)
        this.onTableChange=onTableChange.bind(this)
        this.getCustomersList=getCustomersList.bind(this)
        this.getSiteTypesList=getSiteTypesList.bind(this)
        this.getSiteList=getSiteList.bind(this)
        this.onUrlChange=onUrlChange.bind(this)
        this.fetchAPIData=fetchAPIData.bind(this)
        this.setFilterConfig=setFilterConfig.bind(this)
        this.applyFilterSelect=applyFilterSelect.bind(this)
        this.getUrlBreak=getUrlBreak.bind(this)
        this.getListQuery=getListQuery.bind(this)
        this.goToPage=goToPage.bind(this)
        this.handleDelete=handleDelete.bind(this)
        this.toggleOperationalStatus = toggleOperationalStatus.bind(this)
        let application_slug = '';
        if (import.meta.env.VITE_MOBILE) {
            this.platform_slug = '/' + this.props.app_name;
        } else {
            if (import.meta.env.VITE_BUILD_MODE !== 'development') {
                application_slug = window.location.pathname.split('/')[3];
                if (application_slug && typeof application_slug === 'string') {
                    if (application_slug == 'iframe.html') {
                        application_slug = 'datoms-x';
                    }
                    this.platform_slug = '/' + application_slug;
                }
            } else {
                application_slug = window.location.pathname.split('/')[1];
                if (application_slug && typeof application_slug === 'string') {
                    if (application_slug == 'iframe.html') {
                        application_slug = 'datoms-x';
                    }
                    this.platform_slug = '/' + application_slug;
                }
            }

            this.host_app_name = '/' + application_slug;
        }
        if (this.props.app_name) {
            this.platform_slug = '/' + this.props.app_name;
        }
        this.columns=[
            {
                title: 'Site Name',
                sorter_key:'name',
                sorter:true,
                pdf_title:'Site Name',
                dataIndex: 'name',
                key: 'name',
                width: 300,
                render: (name,row_data) => (
                    <div className={'td_name_cont'}>
                        <div className="name_cont">
                            <p onClick={()=>this.goToPage('Details',row_data)}>{name}</p>
                            {
                                row_data.created_on?(
                                    <p>Added on: {moment(row_data.created_on,'X').format('Do MMM YYYY, HH:mm')}</p>
                                ):""
                            }
                        </div>
                        <div className="action_cont">
                            {
                                this.checkAccess('configuration')?(
                                    <Link to={this.goToPage('Configure',row_data, true)} target={window.innerWidth > 1024 ? '_blank' : '_self'}>
                                        <SettingOutlined />
                                    </Link>
                                ):""
                            }
                            {
                                this.checkAccess('delete')?(
                                    <DeleteOutlined onClick={()=>this.handleDelete(row_data)}/>
                                ):""
                            }
                        </div>
                    </div>
                ),
            },{
                title: 'Customer',
                sorter_key:'customer_id',
                sorter:true,
                pdf_title:'Customer',
                dataIndex: 'customer_id',
                key: 'customer_id',
                width: 200,
                render: (customer,row_data) => (
                    <div className={'td_customer'}>
                        {this.state.customer_names[customer] || ""}
                        {
                            checkValidString(row_data.vendor_name)?(
                                <AntTag>{row_data.vendor_name}</AntTag>
                            ):""
                        }

                    </div>
                ),
            },{
                title: 'Site Type',
                sorter_key:'site_type',
                sorter:true,
                pdf_title:'Site Type',
                dataIndex: 'site_type',
                align: 'center',
                key: 'site_type',
                width: 200,
                render: (site_type) => (
                    <div>{this.state.site_type_names[site_type] || ''}</div>
                ),
            },{
                title: 'Assets',
                sorter_key:'asset_count',
                sorter:true,
                pdf_title:'Assets',
                dataIndex: 'asset_count',
                key: 'asset_count',
                align: 'center',
                width: 200,
                render: (asset_count) => (
                    <div>{asset_count}</div>
                ),
            },
            {
                title: 'Operational Status',
                sorter_key:'operational_status',
                sorter:true,
                pdf_title:'Operational Status',
                dataIndex: 'operational_status',
                key: 'operational_status',
                align: 'center',
                width: 200,
                render: (operational_status) => (
                    <div>{operational_status}</div>
                ),
            }
        ]
        if (this.state.end_customer){
            this.columns=this.columns.filter(item=>item.key!=='customer_id')
        }

        if(this.checkAccess('configuration')) {
            this.columns.push({
                title: 'Action',
                key: 'action',
                width: 100,
                align: 'center',
                render: (action, row_data) => (
                    <div className="site-action-menu">
                        <AntDropdown
                            overlay={
                                <AntMenu>
                                    <AntMenuItem 
                                        key="operational-status" 
                                        onClick={() => this.toggleOperationalStatus(row_data)}
                                    >
                                        {row_data.operational_status === 'Operational' 
                                            ? 'Set to Non-Operational' 
                                            : 'Set to Operational'}
                                    </AntMenuItem>
                                </AntMenu>
                            }
                            overlayClassName="site-action-menu-overlay"
                            trigger={['click']}
                            placement="bottomLeft"
                        >
                            <MoreOutlined 
                                style={{
                                    fontSize: 20,
                                    color: '#000000',
                                    cursor: 'pointer'
                                }}
                            />
                        </AntDropdown>
                    </div>
                ),
            })
        }
    }

    componentDidMount() {
        let {filterConfig}=this.state
        let filter_keys=filterConfig.map(item=>item.url_name)
        let filter_values={}
        filter_keys.forEach(key=>{
            filter_values[key]=this.getUrlBreak(key) ? this.getUrlBreak(key) : undefined;
        })
        this.setState({filter_values},()=>{
            console.log("mani-filter",filter_values)
            this.fetchAPIData()
        })
        // this.fetchAPIData()
    }

    render() {
        let {filterConfig,filter_values}=this.state,updated_filter_config=filterConfig
        updated_filter_config=updated_filter_config.map(item=>{
            let val=filter_values[item.url_name]
            if (!isNaN(val)){
                val=parseInt(val)
            }
            if (typeof val!=="undefined" && val!=='undefined'){
                item['selectValue']=val
            }
            return item;
        })
        if (this.state.loading){
            return <Loading/>
        }
        return (
            <div className={'site_sl_main_cont'}>
                <div className="site_sl_add_cont">
                    <div className="site_sl_add_filter_cont">
                        {
                            this.state.show_filter?(
                                <GenericFilter
                                    isFlexWrap
                                    hideIconCount
                                    history={this.props.history}
                                    url={this.props.location.pathname}
                                    width={false ? undefined : 525}
                                    default={[
                                        '',
                                        '',
                                        '',
                                        ''
                                    ]}
                                    searchObject={this.state.searchObject}
                                    filterData={updated_filter_config}
                                    panelFilterSelect={(value, key) =>
                                        this.applyFilterSelect(value, key)
                                    }
                                    onUrlChange={this.onUrlChange}
                                    is_all_disabled={this.state.bodyLoading}
                                    />
                            ):""
                        }
                    </div>
                    <div className="site_sl_add_btn_cont">
                        {
                            this.checkAccess('addition')? (
                                <div className="site_sl_site_add_btn"
                                     onClick={()=>this.goToPage('Add')}
                                >
                                    <PlusOutlined style={{fontSize: 22, color: '#fff'}}/>
                                </div>
                            ) : (
                                <div className="sm_sl_add_disabled-btn">
                                    <PlusOutlined style={{color: '#fff'}}/>
                                </div>
                            )
                        }
                    </div>
                </div>
                <div className="site_sl_add_table_cont">
                    <CustomizeTable
                        {...this.props}
                        preferenceKeys={[
                            'site_management'
                        ]}
                        key={this.state.activeKey}
                        tableProps={{
                            dataSource: this.state.filtered_table_data,
                            columns:this.columns,
                            shadow: false,
                            isGrouped: false,
                            loading: this.state.table_loading,
                            onChange: this.onTableChange,
                            sticky: true,
                            scroll: { x: 'max-content' },
                            pagination: {
                                position: ['topRight'],
                                pageSizeOptions: [
                                    10, 20, 25, 50, 100,
                                ],
                                pageSize: this.state.pageSize,
                                total: this.state.totalPage || 0,
                                current: this.state.page,
                                showQuickJumper: true,
                                showTotal: (
                                    total,
                                    range
                                ) =>
                                    `${range[0]}-${range[1]} of ${total} items`
                            },
                        }}
                    />
                </div>
            </div>
        );
    }
}

export default SiteList;
