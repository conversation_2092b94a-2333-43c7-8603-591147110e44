import moment from 'moment-timezone';
export const datePickerConfig = {
	placeholder: ['From', 'To'],
	size: 'default',
	//   disabledDate: disabledDate,
	showTime: false,
	separator: ' - ',
	format: 'DD-MMM-YYYY',
};
let datomsxFilterData = [
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		showSearch: true,
		sorted: true,
		placeholder: 'Partners',
		url_name: 'vendor_id',
		is_outside_filter_drawer:true

	},
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		showSearch: true,
		sorted: true,
		placeholder: 'Customers',
		url_name: 'customer_id',
		is_outside_filter_drawer:true

	},{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		showSearch: true,
		placeholder: 'Operational Status',
		url_name: 'operational_status',
		is_outside_filter_drawer:true

	},{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		showSearch: true,
		sorted: true,
		placeholder: 'Site Type',
		url_name: 'site_type',
		is_outside_filter_drawer:true

	}
];
let templateFilterData = [
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		showSearch: true,
		sorted: true,
		url_name: 'vendor_id',
		placeholder: 'Vendor',
		is_outside_filter_drawer:true
	},
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		sorted: true,
		showSearch: true,
		placeholder: 'Thing Type',
		url_name: 'thing_type',
		is_outside_filter_drawer:true
	},
	{
		optionData: [
			{
				value:'monthly',
				title:'Monthly'
			},{
				value:'quarterly',
				title:'Quarterly'
			},{
				value:'half_yearly',
				title:'Half Yearly'
			},{
				value:'annual',
				title:'Annual'
			},
		],
		selectValue:undefined,
		allowClear: true,
		showSearch: true,
		placeholder: 'Billing Frequency',
		url_name: 'billing_freq',
		is_outside_filter_drawer:true
	},
	{
		optionData: [
			{
				title: 'Immediate',
				value: 'immediate',
			},
			{
				title: 'Fixed Date',
				value: 'fixed',
			}
		],
		selectValue:undefined,
		allowClear: true,
		showSearch: true,
		placeholder: 'Invoice Date',
		url_name: 'invoice_date',
		is_outside_filter_drawer:true
	},
];
let alertsFilterData = [
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		showSearch: true,
		sorted: true,
		placeholder: 'Payee',
	},
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		showSearch: true,
		sorted: true,
		placeholder: 'Customer',
	},
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		sorted: true,
		showSearch: true,
		placeholder: 'Thing Type',
	},
	{
		optionData: [
			{
				value:'monthly',
				title:'Monthly'
			},{
				value:'quarterly',
				title:'Quarterly'
			},{
				value:'half_yearly',
				title:'Half Yearly'
			},{
				value:'annual',
				title:'Annual'
			},
		],
		selectValue:undefined,
		allowClear: true,
		showSearch: true,
		placeholder: 'Billing Frequency',
	},
	{
		optionData: [
			{
				title: 'Due',
				value: 'Due',
			},
			{
				title: 'Overdue',
				value: 'Overdue',
			},
			{
				title: 'Due Within',
				value: 'due_within',
			}
		],
		selectValue: undefined,
		allowClear: true,
		showSearch: true,
		placeholder: 'Status',
	},

];

let iotFilterData = [
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		sorted: true,
		showSearch: true,
		placeholder: 'All Clients',
	},
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		sorted: true,
		showSearch: true,
		placeholder: 'All Thing Types',
	},
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		sorted: true,
		showSearch: true,
		placeholder: 'All Things',
	},
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		sorted: true,
		showSearch: true,
		placeholder: 'Functional Status',
	},
	{
		optionData: [],
		selectValue: undefined,
		allowClear: true,
		sorted: true,
		showSearch: true,
		placeholder: 'Rental Status',
	},
	// {
	// 	optionData: [
	// 		{
	// 			title: 'Data Availability 100%',
	// 			value: '100',
	// 		},
	// 		{
	// 			title: 'Data Availability > 90%',
	// 			value: '>90',
	// 		},
	// 		{
	// 			title: 'Data Availability 60 - 90%',
	// 			value: '60-90',
	// 		},
	// 		{
	// 			title: 'Data Availability < 60%',
	// 			value: '<60',
	// 		},
	// 	],
	// 	selectValue: undefined,
	// 	allowClear: true,
	// 	showSearch: true,
	// 	placeholder: 'Data Availability',
	// },
	// {
	// 	optionData: [
	// 		{
	// 			title: 'Active',
	// 			value: 'active',
	// 		},
	// 		{
	// 			title: 'Inactive',
	// 			value: 'inactive',
	// 		},
	// 		{
	// 			title: 'Online',
	// 			value: 'online',
	// 		},
	// 		{
	// 			title: 'Offline',
	// 			value: 'offline',
	// 		},
	// 	],
	// 	selectValue: [],
	// 	multiSelect: true,
	// 	allowClear: true,
	// 	showSearch: true,
	// 	filterType: 'status',
	// 	placeholder: 'Thing Status',
	// },
];

let applicationFilterData = [
	{
		optionData: [
			{
				title: 'Data Availability 100%',
				value: '100',
			},
			{
				title: 'Data Availability > 90%',
				value: '>90',
			},
			{
				title: 'Data Availability 60 - 90%',
				value: '60-90',
			},
			{
				title: 'Data Availability < 60%',
				value: '<60',
			},
		],
		selectValue: undefined,
		allowClear: true,
		showSearch: true,
		placeholder: 'Data Availability',
	},
	{
		optionData: [
			{
				title: 'Active',
				value: 'active',
			},
			{
				title: 'Inactive',
				value: 'inactive',
			},
			{
				title: 'Online',
				value: 'online',
			},
			{
				title: 'Offline',
				value: 'offline',
			},
		],
		selectValue: [],
		multiSelect: true,
		allowClear: true,
		showSearch: true,
		filterType: 'status',
		placeholder: 'Thing Status',
	},
];

let searchObject = {
	placeholder: 'Search Sites',
	size: 'default',
	url_name:'site_name',
	value: '',
};

export default {
	datomsxFilterData: datomsxFilterData,
	iotFilterData: iotFilterData,
	applicationFilterData: applicationFilterData,
	templateFilterData:templateFilterData,
	alertsFilterData:alertsFilterData,
	searchObject: searchObject,
};
