@import '../../../../../styles/default-color.less';

.site_sl_main_cont{
  padding: 0px 20px 50px;
  margin-top: 20px;
  overflow: hidden;
  overflow-y: auto;
  height: calc(100vh - 50px);
  .site_sl_add_cont {
    position: relative;
    min-height: 50px;
    display: flex;
    width: 100%;
    justify-content: space-between;
    margin-bottom: 15px;

    .site_sl_add_filter_cont {
      width: 90%;
    }
    .site_sl_add_btn_cont{
      position: relative;
    }
    .site_sl_site_add_btn{
      padding: 8px;
      color: #fff;
      border: 1px solid @defaultColor;
      background: @defaultColor;
      height: 40px;
      aspect-ratio: 1;
      border-radius: 50%;
      cursor: pointer;
      margin-right: 25px;

      &:hover{
        box-shadow: 4px 6px 10px 1px rgba(35, 35, 35, 0.13);
      }
    }
    

  }
  .site_sl_add_table_cont{
    width: 100%;
    position: relative;

    .ant-table-wrapper {
      .ant-pagination {
        margin-top: 0;
      }
    }
    .ant-table-container{

      //background: red;
    }
    .ant-table-column-sorters, .ant-table-tbody tr td > div{
      padding: 4px;
    }

    .td_customer{
      display: flex;
      flex-direction: column;

      .ant-tag{
        color: #89A9DF;
        border: 1px solid #95B1E2;
        background: #E7ECF4;
        width: fit-content;
      }
    }

    .td_name_cont{
      display: flex;
      justify-content: space-between;
      align-items: center;
      p{
        margin-bottom: 0;
        font-size: 13px;
        font-weight: 400;
      }
      .name_cont{

        p:first-child{
          margin-bottom: 4px;
          color: #000000;
          cursor: pointer;
        }
        p:last-child{
          color: #808080;
        }
      }
      .action_cont{

        & > span, & a > span {
          color: #374375;
          cursor: pointer;
          font-size: 16px;
          margin-right: 6px;
        }
      }
    }
  }
}

.site-action-menu-overlay {
  .ant-dropdown-menu {
    padding: 6px;
  }
}