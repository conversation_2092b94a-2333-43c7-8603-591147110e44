import {retrieveSitesList,deleteSite, patchSite} from '@datoms/js-sdk'
import {  mixPanelTrackUser } from '@datoms/js-utils/src/mix-panel';
import React from 'react';
import {openNotification} from "../ThingAddV2/logic";
import {default_error} from '../SiteConfiguration/logic';
import _findIndex from 'lodash/findIndex';
import AntConfirmModal from '@datoms/react-components/src/components/AntConfirmModal';

export function checkAccess(key){
    let {
        getViewAccess
    } = this.props;
    let accessObj=  {
        addition: getViewAccess(['SiteManagement:Add']),
        configuration: getViewAccess(['SiteManagement:Edit']),
        delete:getViewAccess(['SiteManagement:Delete']),
    }
    return accessObj[key]
}
export function getListQuery(){
    let query='',{sort_key,sort_order,filter_values}=this.state;
    //sort query
    if (sort_key && sort_order && sort_key!==''){
        query=query+`&order_key=${sort_key}&order_by=${sort_order}`
    }

    if (filter_values && Object.keys(filter_values).length) {
        Object.keys(filter_values).forEach(filter_key => {
            if (filter_values[filter_key] && filter_values[filter_key] !== 'undefined') {
                let split_filter=filter_values[filter_key].split('_')
                if (split_filter && (typeof split_filter[1]==='undefined' || (typeof split_filter[1]!=="undefined" && split_filter[1]!==''))) {
                    let final_val = filter_values[filter_key]
                    query=query+`&${filter_key}=${final_val}`
                }
            }
        })
    }
    query=query.split('-').join(',')
    console.log("mani-filter2",query)
    return query
}
export async function getSiteList(setState,customer_sites_only){
    let stateToUpdate={},table_data=[],query='';
    let {page,pageSize,sort_key,sort_order}=this.state
    query=`?page_no=${page}&results_per_page=${pageSize}`
    query=query+this.getListQuery()
    console.log({query})
    let siteList=await retrieveSitesList(this.props.client_id,query)
    if (siteList.status !== 'success' ) {
        openNotification('error',siteList?.message || default_error)
        return {};
    }
    siteList.data.forEach(site=>{
        if("operational_status" in site) {
            site.operational_status = site.operational_status === 1 ? 'Operational' : 'Non-Operational'
        }
        table_data.push(site)
    })
    stateToUpdate['table_data']=table_data
    stateToUpdate['filtered_table_data']=table_data
    stateToUpdate['table_loading']=false
    stateToUpdate['totalPage'] = siteList.total_count
    if (setState){
        this.setState(stateToUpdate)
    }
    return stateToUpdate;
}
export async function fetchAPIData(){
    let {client_id}=this.props
    let [customersData,siteTypesData,siteListData]=await Promise.all([this.getCustomersList(),this.getSiteTypesList(client_id),this.getSiteList()])
    this.setState(Object.assign({loading:false},customersData,siteTypesData,siteListData),()=>{
        this.setFilterConfig()
        console.log(this.state)
    })
}

export async function setFilterConfig(){
    let {filterConfig,customers_list,vendor_list,site_types}=this.state
    let optionsList={
        vendor_id:vendor_list,
        customer_id:customers_list,
        site_type:site_types,
        operational_status:[{
            name: 'Operational',
            id: 1,
        },
        {
            name: 'Non-Operational',
            id: 0,
        },]
    }
    filterConfig=filterConfig.map(each_filter=>{
        let updated_filter=each_filter
        updated_filter['optionData']=[]
        updated_filter['selectValue']=undefined
        if (Array.isArray(optionsList[each_filter.url_name])){
            updated_filter.optionData=optionsList[each_filter.url_name].map(vend=>{
                return ({
                    title: vend.name,
                    value: vend.id
                })
            })
        }
        return updated_filter
    })
    this.setState({filterConfig,show_filter:true},()=>{
        console.log("filterConfig",this.state.filterConfig,customers_list)
    })
}
export function onTableChange(pagination,b,sorter){
    let page = pagination?.current ? pagination.current : 1;
    let pageSize = pagination?.pageSize ? pagination.pageSize : 25;
    let sort_key = sorter?.column?.sorter_key ? sorter.column.sorter_key : '';
    let sort_order = sorter?.order ? sorter.order : '';
    console.log('sorter_', sorter);
    this.setState(
        {
            page,
            pageSize,
            sort_key,
            sort_order:
                sort_order === 'ascend'
                    ? 'ASC'
                    : sort_order === 'descend'
                        ? 'DESC'
                        : '',
            table_loading: true
        },
        async () => {
            await this.getSiteList(true);
        }
    );
}
export function getUrlBreak(value) {
    let {filterConfig}=this.state,updated_filter_config=[]
    let geturlData;
    let geturlDataIndex = _findIndex(
        this.props.history?.location?.search?.split(','),
        function (o) {
            return o.includes(value);
        }
    );
    if (geturlDataIndex > -1) {
        geturlData = this.props.history?.location?.search
            ?.split(',')
            [geturlDataIndex].split(':')[1];
    }
    return geturlData;
}
export async function onUrlChange(){
    let filter_data=this.state.filterConfig,searchObject=this.state.searchObject,search_key='site_name',that=this;
    let filter_keys=filter_data.map(item=>item.url_name)
    if (searchObject && searchObject.url_name){
        filter_keys.push(searchObject.url_name)
    }
    let filter_values={}
    filter_keys.forEach(key=>{
        filter_values[key]=this.getUrlBreak(key) ? this.getUrlBreak(key) : undefined;
    })

    that.setState({filter_values,page:1,table_loading:true,show_filter:true},()=>{
        that.getSiteList(true);
    })
    // debouncing for search
    // debounce(()=>{
    //     console.log("Workingg")
    //
    // },1000)

}

export function checkValidString(str){
    return typeof str==="string" && str!==""
}
function debounce(func, timeout = 300){
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => { func.apply(this, args); }, timeout);
    };
}
export function applyFilterSelect(value,key, index){
    return {
        selected_values: value,
        total_options:{},
    };
}

export function goToPage(page,data={}, getLink=false){
    const {site_type_names}=this.state
    let link='',mix_panel_event='',mix_panel_data={}
    switch (page){
        case 'Add':{
            link=`${this.platform_slug}/sites/add`
            mix_panel_event='Site Add Link'
            break;
        }
        case 'Configure':{
            let { app_name } = this.props;
            let { customer_id, id,site_type } = data;
            if (!app_name) {
                app_name = 'datoms-x';
            }
            link =
                this.platform_slug +
                '/customers/' +
                customer_id +
                '/sites/' +
                id +
                '/configuration';
            mix_panel_event='Site Configure Link'
            mix_panel_data={"Site Type Name":site_type_names[site_type]}
            break;
        }
        case 'Details':{
            let { app_name } = this.props;
            let { customer_id, id,site_type } = data;
            if (!app_name) {
                app_name = 'datoms-x';
            }
            link =
                this.platform_slug +
                '/customers/' +
                customer_id +
                '/sites/' +
                id +
                '/details';
            mix_panel_event='Site Details'
            mix_panel_data={"Site Type Name":site_type_names[site_type]}

            break;
        }
        default:{}
    }
    if (link!==''){
        if (mix_panel_event!=='') {
            mixPanelTrackUser(mix_panel_event,mix_panel_data)
        }
        if(getLink){
            return link
        }
        this.props.history.push(link);
    }
}

export function handleDelete(site){
    let that=this,{id,customer_id,name}=site;
    AntConfirmModal({
        title: (
            <div>
                <span>
                        Delete  {name} ?
                    </span>
            </div>
        ),
        content: (
            <div>
                    <span>
                      Are you sure you want to delete this site and it's configuration?
                    </span>
            </div>
        ),
        okText: 'Delete',
        cancelText: 'Cancel',
        className: 'subscription-edit-modal',
        onOk: async () => {
            that.setState({table_loading:true})
            let deleteData=await deleteSite(customer_id,id)
            if (deleteData.status === 'success' ) {
                openNotification('success', 'Site Deleted Successfully')
                that.getSiteList(true)
            }else {
                openNotification('error',deleteData?.message || default_error)
                that.setState({table_loading:false})

            }
        },
        onCancel:()=>{

        }
    })
    return;
}

export function checkUserAccess() {
    let {
        getViewAccess
    } = this.props;
    return  {
        addition: getViewAccess(['SiteManagement:Add']),
        configuration: getViewAccess(['SiteManagement:Edit']),
        delete:getViewAccess(['SiteManagement:Delete']),
    }
}

export function toggleOperationalStatus(site) {
    let that = this;
    const { id, customer_id, name, operational_status } = site;
    const newStatus = operational_status === 'Operational' ? 0 : 1;
    const statusText = newStatus === 1 ? 'Operational' : 'Non-Operational';
    
    AntConfirmModal({
        title: (
            <div>
                <span>
                    Change Status for {name}?
                </span>
            </div>
        ),
        content: (
            <div>
                <span>
                    Are you sure you want to set this site to {statusText}?
                </span>
            </div>
        ),
        okText: `Set to ${statusText}`,
        cancelText: 'Cancel',
        className: 'site-status-update-modal',
        onOk: async () => {
            that.setState({ table_loading: true });
            const updateData = await patchSite(customer_id, id, { operational_status: newStatus });
            
            if (updateData.status === 'success') {
                openNotification('success', `Site status updated to ${statusText} successfully`);
                that.getSiteList(true);
            } else {
                openNotification('error', updateData?.message || default_error);
                that.setState({ table_loading: false });
            }
        },
        onCancel: () => {
            // Do nothing on cancel
        }
    });
}
