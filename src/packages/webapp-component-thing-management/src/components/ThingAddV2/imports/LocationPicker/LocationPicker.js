import React, {Component} from 'react';
import {
    <PERSON>M<PERSON>,
    <PERSON><PERSON>, Autocomplete,
} from '@react-google-maps/api';
import { getLocationData } from '@datoms/js-sdk';
import AntTextArea from '@datoms/react-components/src/components/AntTextArea';
import AntInput from '@datoms/react-components/src/components/AntInput';
import AntButton from '@datoms/react-components/src/components/AntButton';
import AntMessage from '@datoms/react-components/src/components/AntMessage';
import AntModal from '@datoms/react-components/src/components/AntModal';
import ExclamationCircleOutlined from '@ant-design/icons/ExclamationCircleOutlined'
import SearchOutlined from '@ant-design/icons/SearchOutlined';
import mapIcon from '../../../../imgs/map.svg';
import location from '../../../../imgs/location.png';
import fullscreen from '../../../../imgs/v2/fullscreen.svg'
import './style.less';
import {validateLocation} from '../../../ThingAddNew/Mobile/logic'
import ViewComponent from "../ViewComponent/ViewComponent";


const EDITABLE_PIN_CODE_CUSTOMER_ID = [14500];


class LocationPicker extends Component {
    constructor(props) {
        super(props);
        this.state={
            centerValue:new window.google.maps.LatLng(51, 0),
            zoom:7,
            autocomplete:null,
            location:null,
            address:this.props.allFields && this.props.allFields.address ? this.props.allFields.address:'',
            latitude:this.props.allFields && this.props.allFields.latitude ? this.props.allFields.latitude:'',
            longitude:this.props.allFields && this.props.allFields.longitude ? this.props.allFields.longitude:'',
            map_modal:false,
            valid:false,
            temp_location:{
                latitude:'',
                longitude:'',
                address:'',
            }
        };
        this.geocodeTimeout;
        this.map=React.createRef(null)
        this.searchRef=React.createRef(null)
        this.autoCompleteRef=React.createRef(null)
        this.onLoad=this.onLoad.bind(this)
        this.onPlaceChanged=this.onPlaceChanged.bind(this)
        this.mapOnClick=this.mapOnClick.bind(this)
        this.validateLocation=this.validateLocation.bind(this)
        this.updateLocationFromProps=this.updateLocationFromProps.bind(this)
        this.geocodePosition=this.geocodePosition.bind(this)
    }

    onLoad (autocomplete) {
        console.log('autocomplete: ', autocomplete)
        this.setState({autocomplete})
    }
    onSelectClicked(addressComponents=undefined) {
        const { autocomplete } = this.state;
        let { latitude, longitude, address } = this.state.temp_location;
        this.setState({ latitude, longitude, address }, () => {
          if (this.props.setLocation) {
            const stateToUpDate = {
              latitude,
              longitude,
              address,
            };
            const { adr_city, adr_province, adr_country_code, adr_pin_code, formatted_address } =
            addressComponents
                  ? addressComponents
                  : this.getLocationDetails(
                      autocomplete?.getPlace()?.address_components,
                    );
            if (this.props.extraAddressFields) {
              stateToUpDate["adr_city"] = adr_city;
              stateToUpDate["adr_province"] = adr_province;
              stateToUpDate["adr_country_code"] = adr_country_code;
              stateToUpDate["adr_pin_code"] = adr_pin_code;
            }
            if(formatted_address) {
                stateToUpDate["address"] = formatted_address;
            }
            this.props.setLocation(stateToUpDate);
          }
        });
      }
    onPlaceChanged () {
        // address will not be changed if some address already exists and user changes it by adjusting marker position
        let {autocomplete}=this.state
        if (autocomplete !== null) {
            let lat=autocomplete.getPlace().geometry.location.lat()
            let lon=autocomplete.getPlace().geometry.location.lng()
            let address=autocomplete.getPlace().formatted_address
            console.log(autocomplete.getPlace(),lat,lon)
            let location=new window.google.maps.LatLng(lat,lon)
            let temp_location={
                latitude:lat,
                longitude:lon,
                address
            }
            let stateToUpdate={
                temp_location,
                centerValue:location,
                temp_marker:location,
            }
            if (!this.state.map_modal){
                stateToUpdate['location']=location
                stateToUpdate['zoom']=18
            }
            this.setState(stateToUpdate,()=>{
                if (!this.state.map_modal){
                    this.onSelectClicked()
                }
                // this.map.panTo(new window.google.maps.LatLng(12.5726, 77.3639))
            })

        } else {
            console.log('Autocomplete is not loaded yet!')
        }
    }

    getLocationDetails(addressComponents=[]) {
        let adr_city, adr_province, adr_country_code, adr_pin_code;
        
        try {
            for(let index = addressComponents.length - 1; index >= 0; index--) {
                if (addressComponents[index].types.includes('locality')) {
                    adr_city = addressComponents[index].long_name;
                } else if (addressComponents[index].types.includes('administrative_area_level_1')) {
                    adr_province = addressComponents[index].long_name;
                } else if (addressComponents[index].types.includes('country')) {
                    adr_country_code = addressComponents[index].short_name;
                } else if (addressComponents[index].types.includes('postal_code')) {
                    adr_pin_code = addressComponents[index].long_name;
                }
                if(adr_city && adr_province && adr_country_code && adr_pin_code) {
                    break;
                }
            }
        } catch (error) {
            console.error('Error in getting location details', error);
        }
    
        return { adr_city, adr_province, adr_country_code, adr_pin_code };
    }

    geocodePosition(position) {
        // Geocoder instance
        const geocoder = new window.google.maps.Geocoder();

        return new Promise((resolve, reject) => {
            geocoder.geocode({ location: position }, (results, status) => {
                if (status === window.google.maps.GeocoderStatus.OK) {
                    resolve(results);
                } else {
                    reject(status);
                }
            });
        });
    }

    validateLocation(lat,lon){
        let {latitude,longitude}=this.state
        if (lat && lon){
            latitude=parseFloat(lat)
            longitude=parseFloat(lon)
        }
        const isLatitude = (num) => isFinite(num) && Math.abs(num) <= 90;
        const isLongitude = (num) => isFinite(num) && Math.abs(num) <= 180;

        if (!latitude || !isLatitude(latitude)){
            return false;
        }

        if (!longitude || !isLongitude(longitude)){
            return false
        }

        return true;
    }


    async getLocation(position){
        // let stringLocation=await this.geocodePosition(position)
        // let final_location,types_order=["street_address",'route','locality']
        // if (Array.isArray(stringLocation)){
        //     types_order.forEach(type=>{
        //         if (!final_location){
        //             final_location=stringLocation.find(item=>(item.types.indexOf(type)>-1))?.formatted_address
        //         }
        //     })
        // }
        // if (!final_location){
        //     final_location=stringLocation[0]?.formatted_address
        // }
        const locationData = await getLocationData("?latitude=" + position.lat + "&longitude=" + position.lng);
        return locationData?.data || {};
    }
    mapOnClick(e,updateCentre){
        let lat=e.latLng.lat()
        let lng=e.latLng.lng()
        let position = { lat, lng };
        let mainState={},location=new window.google.maps.LatLng(lat,lng)
        if (this.state.map_modal) {
            mainState['centerValue'] = location
            mainState['temp_marker'] = location
        }else {
            mainState['location'] = location
        }
        this.setState(mainState,async ()=>{
            const { 
                city,
                province,
                pin_code,
                country_code,
                formatted_address, 
                } = await this.getLocation(position)
            let temp_location={
                latitude:lat,
                longitude:lng,
                address:this.state.address
            }
            temp_location['address']=formatted_address
            const addressComponents = {
                adr_city: city,
                adr_province: province,
                adr_country_code: country_code,
                adr_pin_code: pin_code,
                formatted_address,
            }
            let stateToUpdate={temp_location}
            if (updateCentre){
                stateToUpdate['centerValue']=new window.google.maps.LatLng(lat, lng)
            }
            this.setState(stateToUpdate,()=>{
                if (!this.state.map_modal){
                    this.onSelectClicked(addressComponents)
                }
            })
        })

    }
    updateLocationFromProps(updateAddress){
        let stateToUpdate={
            centerValue:new window.google.maps.LatLng(this.props.allFields.latitude, this.props.allFields.longitude),
            location:new window.google.maps.LatLng(this.props.allFields.latitude, this.props.allFields.longitude),
            zoom:16,
            latitude:this.props.allFields.latitude,
            longitude:this.props.allFields.longitude,
        }

        this.setState(
            stateToUpdate,async ()=>{
                if (!this.state.address || this.state.address==="" || updateAddress){
                    // let final_location=await this.getLocation({lat:this.props.allFields.latitude,lng:this.props.allFields.longitude})
                    // this.setState({address:final_location},()=>{
                    //     this.props.setLocation({address:final_location})
                    // })
                }
            })
    }
    componentDidMount() {
        if (this.props.allFields && this.props.allFields.longitude && this.props.allFields.latitude){
            console.log("here")
            this.updateLocationFromProps()

        } else if (this.props.isThingAdd || (this.props.allFields && validateLocation(this.props.allFields))){
            console.log("isThingAdd-inside")
            if (window.navigator.geolocation) {
                console.log("geolocation-inside")
                window.navigator.geolocation.getCurrentPosition((position) => {
                    console.log("getCurrentPosition-inside")
                    this.setState({centerValue:new window.google.maps.LatLng(position.coords.latitude,position.coords.longitude),zoom:16},()=>{
                        console.log("setstate-inside",this.state)
                    })
                }, (e) => {
                    console.log("Geolocation Error", e);
                    // AntMessage('error','Unable to fetch your location')
                })
            }
        }
        // this.autoCompleteRef.current=new window.google.maps.places.Autocomplete(
        //     this.searchRef.current,
        //     {}
        // )
        // this.autoCompleteRef.current.addListener("place_changed", async function () {
        //     const place = await this.autoCompleteRef.current.getPlace();
        //     console.log({ place });
        // });
    }


    onLocationInputChange(e,name){

        if(name==='adr_city' || name==='adr_province' || name==='adr_country_code' || name==='adr_pin_code') {
            if (this.props.setLocation){
                this.props.setLocation({
                    [name]: e.currentTarget ? e.currentTarget.value : e,
                })
            }
            return;
        }

        let temp_location = this.state.temp_location,that=this,val=e.currentTarget.value;
        let {latitude,longitude} = this.state,stateUpdate={}
        if (name==="latitude"){
            latitude=val
        }
        if (name==='longitude'){
            longitude=val
        }
        stateUpdate={latitude,longitude,zoom:16}
        if (this.validateLocation(latitude,longitude)){
            let center=new window.google.maps.LatLng(parseFloat(latitude),parseFloat(longitude))
            stateUpdate['centerValue']=center
            stateUpdate['location']=center
            this.getAddressFromLocation(latitude,longitude)
        }
        this.setState(stateUpdate,async ()=>{
            that.props.setLocation({latitude,longitude})
        })
    }

    getAddressFromLocation(latitude, longitude) {
        clearTimeout(this.geocodeTimeout);
        this.geocodeTimeout = setTimeout(async () => {
            const locationData = await getLocationData("?latitude=" + latitude + "&longitude=" + longitude);
            if(locationData.status === "success" && locationData?.data) {
                const { formatted_address, city, province, pin_code, country_code } = locationData.data;
                const stateUpdate = {
                    address: formatted_address,
                };
                if (this.props.extraAddressFields) {
                    stateUpdate["adr_city"] = city;
                    stateUpdate["adr_province"] = province;
                    stateUpdate["adr_country_code"] = country_code;
                    stateUpdate["adr_pin_code"] = pin_code;
                }
                this.setState({
                    address: formatted_address,
                }, () => {
                    if (this.props.setLocation) {
                        this.props.setLocation(stateUpdate);
                    }
                });
            }
        }, 900);
    }

    getCurrentLocation(){

    }

    render() {
        let mapContent= import.meta.env.VITE_DESKTOP ? null : (
            <div className="location_picker_modal_content">
                <GoogleMap
                    id="map_id"
                    mapContainerClassName="map-container-class"
                    mapContainerStyle={{ height: '100%',zIndex:2,position:'relative' }}
                    zoom={this.state.zoom}
                    center={
                        this.state.centerValue
                    }
                    ref={(ref) => {
                        this.map = ref;
                    }}
                    options={{
                        streetViewControl:false,
                        fullscreenControl:false,
                        clickableIcons: false,
                        mapTypeControl:false,
                        zoomControl: true,
                        zoomControlOptions: {
                            position: window.google.maps.ControlPosition.TOP_RIGHT,
                        },
                    }}
                    // onLoad={(map) =>{console.log(map.getZoom())}}
                    onClick={this.mapOnClick}
                >
                    <Autocomplete
                        onLoad={this.onLoad}
                        onPlaceChanged={this.onPlaceChanged}
                    >
                        <div className={`${this.state.map_modal?"tm_tc_lp_mdl_auto_complete_cont":"tm_tc_lp_auto_complete_cont"}`}>
                            <input
                                type="text"
                                placeholder={this.props.t? this.props.t('search_location'): "Search Location"}
                                className={'tm_tc_lp_search'}
                            />
                        </div>
                    </Autocomplete>
                    {
                        (this.state.location || this.state.temp_marker) ?(
                            <Marker
                                icon={mapIcon}
                                key={"location"}
                                position={this.state.map_modal?this.state.temp_marker:this.state.location}
                                onLoad={(marker) =>
                                {}
                                }
                                onClick={
                                    ()=>{}
                                }
                            />
                        ):""
                    }
                    <div className="tm_tc_lp_icon_cont tm_tc_lp_locate_me_cont" onClick={()=>{
                        if (window.navigator.geolocation) {
                            window.navigator.geolocation.getCurrentPosition((position)=>{
                                console.log(position)
                                this.mapOnClick({
                                    latLng:{
                                        lat:function () {
                                            return position.coords.latitude
                                        },
                                        lng:function () {
                                            return position.coords.longitude
                                        },
                                    }
                                },true)
                            },(e)=>{
                                console.log("Geolocation Error",e);
                                AntMessage('error','Unable to fetch your location')
                            });
                        } else {
                            console.log("Geolocation is not supported by this browser.");
                        }
                    }}>
                        <img src={location} alt=""/>
                    </div>
                    <div className="tm_tc_lp_icon_cont tm_tc_lp_full_screen_cont" onClick={()=>{
                        this.setState({map_modal:true})
                    }}>
                        <img src={fullscreen} alt=""/>
                    </div>
                </GoogleMap>
            </div>
        )
        return (
            <div className={`tm_tc_location_picker_cont ${this.props.className?this.props.className:""}`}>
                <div className="tm_tc_location_right">
                    <div className="tm_tc_location_cont">
                        <div className="label_cont">
                            <p>
                                {this.props.t? this.props.t('location'): "Location"}
                                {/* Location */}
                            </p>
                        </div>
                        <div className="location_form">
                            <ViewComponent type={'input'} edit_mode={this.props.edit_mode}>
                                <AntInput placeholder={this.props.t? this.props.t('enter_latitude'): 'Enter Latitude'}
                                          type={'number'}
                                          onChange={(e)=>this.onLocationInputChange(e,'latitude')}
                                          value={this.state.latitude}
                                />
                            </ViewComponent>
                            <ViewComponent type={'input'} edit_mode={this.props.edit_mode}>
                                <AntInput value={this.state.longitude} placeholder={this.props.t? this.props.t('enter_longitude'): "Enter Longitude"} type={'number'} onChange={(e)=>this.onLocationInputChange(e,'longitude')}/>
                            </ViewComponent>
                        </div>
                    </div>
                    {this.props.locationFieldExtra ? this.props.locationFieldExtra : null}
                    <div className="tm_tc_location_cont">
                        <div className="label_cont">
                            <p>
                                {this.props.t? this.props.t('address'): "Address"}
                                {/* Address */}
                            </p>
                        </div>
                        <div className="location_form">
                            <ViewComponent type={'input'} edit_mode={this.props.edit_mode}>
                                <AntTextArea rows={2} autoSize={{
                                    minRows: 5, maxRows: 8
                                }} placeholder={this.props.t? this.props.t('address'): "Address"}  value={this.state.address} onChange={e=>{
                                    let stateUpdate={address:e.target.value}
                                    this.setState(stateUpdate,()=>{
                                        if (this.props.setLocation){
                                            this.props.setLocation(stateUpdate)
                                        }
                                    })
                                }}/>
                            </ViewComponent>
                        </div>
                    </div>
                    { this.props.extraAddressFields ?
                    <>
                    <div className="tm_tc_location_cont">
                        <div className="label_cont">
                            <p>
                                {this.props.t? this.props.t('city'): "City"}
                                {/* City */}
                            </p>
                        </div>
                        <div className="location_form">
                            <ViewComponent type={'input'} edit_mode={this.props.edit_mode}>
                                <AntInput 
                                    placeholder={this.props.t? this.props.t('enter_city'): "Enter city"}
                                    onChange={(e)=>this.onLocationInputChange(e,'adr_city')}
                                    value={this.props.extraAddressFields.adr_city}
                                />
                            </ViewComponent>
                        </div>
                    </div>
                    <div className="tm_tc_location_cont">
                        <div className="label_cont">
                            <p>
                                {this.props.t? this.props.t('province'): "Province"}
                                {/* Province */}
                            </p>
                        </div>
                        <div className="location_form">
                            <ViewComponent type={'input'} edit_mode={this.props.edit_mode}>
                                <AntInput 
                                    placeholder={this.props.t? this.props.t('enter_province'): "Enter province"}
                                    onChange={(e)=>this.onLocationInputChange(e,'adr_province')}
                                    value={this.props.extraAddressFields.adr_province}
                                />
                            </ViewComponent>
                        </div>
                    </div>
                    <div className="tm_tc_location_cont">
                        <div className="label_cont">
                            <p>
                                {/* Country Code */}
                                {this.props.t? this.props.t('country_code'): "Country Code"}
                            </p>
                        </div>
                        <div className="location_form">
                            <ViewComponent type={'input'} edit_mode={this.props.edit_mode}>
                                <AntInput 
                                    placeholder= {this.props.t? this.props.t('enter_country_code'): "Enter country code"} 
                                    onChange={(e)=>this.onLocationInputChange(e,'adr_country_code')}
                                    value={this.props.extraAddressFields.adr_country_code}
                                />
                            </ViewComponent>
                        </div>
                    </div>
                    <div className="tm_tc_location_cont">
                        <div className="label_cont">
                            <p>
                                {this.props.t? this.props.t('pin_code'): "Pin Code"}
                                {/* Pin Code */}
                            </p>
                        </div>
                        <div className="location_form">
                            <ViewComponent type={'input'} edit_mode={this.props.edit_mode}>
                                <AntInput
                                    disabled={!EDITABLE_PIN_CODE_CUSTOMER_ID.includes(this.props?.allFields?.customerName)}
                                    placeholder=""
                                    onChange={(e)=>this.onLocationInputChange(e,'adr_pin_code')}
                                    value={this.props.extraAddressFields.adr_pin_code}
                                />
                            </ViewComponent>
                        </div>
                    </div>
                    </>:null}
                </div>
                <div className="tm_tc_location_left">
                    {mapContent}
                </div>
                <AntModal
                    visible={this.state.map_modal}
                    width={'100vh'}
                    footer={null}
                    closable={false}
                    className={'location_picker_modal'}
                >
                    {mapContent}
                    <div className="modal_button_cont">
                        <AntButton
                            className={'sync_close_btn'}
                            onClick={()=>{
                                let{latitude,longitude}=this.state
                                let location=new window.google.maps.LatLng(latitude,longitude)
                                this.setState({centerValue:location,temp_marker:null,map_modal:false})
                            }}
                        >
                            Cancel
                        </AntButton>
                        <AntButton
                            className={'sync_modal_btn'}
                            onClick={()=>{
                                let{latitude,longitude,address}=this.state.temp_location
                                let location=new window.google.maps.LatLng(latitude,longitude)
                                this.setState({map_modal:false,temp_marker:null,location,latitude,longitude,address},()=>{
                                    if (this.props.setLocation){
                                        this.props.setLocation({latitude,longitude,address})
                                    }
                                })
                            }}
                        >
                            Save
                        </AntButton>
                    </div>
                </AntModal>
            </div>
        );
    }
}

export default LocationPicker;