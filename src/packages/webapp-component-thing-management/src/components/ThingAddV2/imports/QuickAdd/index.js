import React, {Component} from 'react';
import AntOption from '@datoms/react-components/src/components/AntOption';
import AntInput from '@datoms/react-components/src/components/AntInput';
import AntSelect from '@datoms/react-components/src/components/AntSelect';
import AntRangePicker from '@datoms/react-components/src/components/AntRangePicker';
import FormController from '@datoms/react-components/src/components/FormController';
import NoDataComponent from '@datoms/react-components/src/components/NoDataComponent';
import TerritorySelect from "../../../../../../webapp-component-user-management/src/components/TerritoryPage/TerritorySelect";
import './styles.less'
import {getDevicevalues, handleScanQR} from "../../logic";
import LocationPicker from "../LocationPicker/LocationPicker";
import qrcodeImg from '../../../../imgs/v2/qrcode.svg'
import partner from "../../../../imgs/v2/partner.svg";
import customer from "../../../../imgs/v2/customer.svg";
import ViewComponent from "../ViewComponent/ViewComponent";
import moment from 'moment-timezone'
import FormView from "../ViewComponent/FormView";

class QuickAdd extends Component {
    constructor(props) {
        super(props);
        this.state={
            rental_asset_type:false
        }
        this.getDevicevalues=getDevicevalues.bind(this);
        this.handleScanQR=handleScanQR.bind(this);
    }

    componentDidMount() {
        this.checkRentalAssetType()
    }

    componentDidUpdate(prevProps, prevState, snapshot) {
        if (prevProps?.thing_types?.length!==this.props.thing_types?.length || prevProps?.allFields?.thingType!==this.props?.allFields?.thingType){
            this.checkRentalAssetType()
        }
    }


    checkRentalAssetType(){
        const {thing_types,allFields}=this.props;
        let rental_asset_type=false
        try {
            const {thingType}=allFields
            rental_asset_type=thing_types.find(item=>parseInt(item.id)===parseInt(thingType))?.is_available_for_rent
        }catch (e) {
            console.log("Rental Asset Type Checking Error",e)
        }

        this.setState({rental_asset_type})
    }

    getCustomerValue() {
        if(
            // this.props.isCustomerOptional && 
            this.props.assetWithoutCustomer 
            // &&
            // !this.props.is_rental 
            // &&
            // this.props.allFields?.customerName === this.props.allFields?.vendorName
        ){
            return undefined;
        }
        return this.props?.allFields?.customerName;
    }

    render() {  
        const {api_status, isTerritoryEnabled}=this.props
        const selectedAssetTemplete = this.props.allFields?.thingTemplate;
        const deviceLoading = this.props?.allFields?.thingType && this.props.api_status?.device;
        const assetTemplateLoading = !this.props.isThingAdd && deviceLoading;
        let selectDevice=(
            <>
                <div className={'tm_tc_mb_select'}>
                    <p>
                        {this.props.t("add_device")}
                    </p>
                    {/* <p>Add Device</p> */}
                        <div className={'tm_tc_device_select'}>
                            <ViewComponent type={'options-data-select'} loading={api_status?.device} edit_mode={this.props.edit_mode}>
                                <AntSelect
                                    t={this.props.t}
                                    placeholder={
                                        `${this.props.t("select_device")}`
                                    }
                                    // placeholder={'Select Device'}
                                    showSearch={true}
                                    mode={'single'}
                                    options_data={this.props.selectdata}
                                    value={this.getDevicevalues(
                                        this.props.device_config_values
                                    )}
                                    disabled={!this.props?.allFields?.thingType}
                                    loading={deviceLoading}
                                    allowClear={true}
                                    onChange={this.props.handleChangeDeviceForm}
                                />
                            </ViewComponent>
                            {
                                this.props.isMobile?(
                                    <img src={qrcodeImg} alt="" onClick={this.handleScanQR}/>
                                ):""
                            }
                        </div>
                </div>
            </>
        ),selectSystemTemplate=(
            <>
            {
                (Array.isArray(this.props.quick_system_template_options) && this.props.quick_system_template_options.length>0)?(
                    <div className={'tm_tc_mb_select'}>
                        <p>
                            {this.props.t? this.props.t('system_template'): "System Template"}
                            {/* System Template */}
                        </p>
                        <ViewComponent
                            type={'multi-select'}
                            option_data={this.props.quick_system_template_options}
                            edit_mode={this.props.edit_mode}
                        >
                            <AntSelect
                                t={this.props.t}
                                className={'machine_template_select'}
                                placeholder={this.props.t? this.props.t('select_system_templates'): "Select System Templates"}
                                // placeholder={'Select System Template'}
                                showSearch={true}
                                filterOption={(input, option) =>
                                    option.children
                                        .toLowerCase()
                                        .indexOf(input.toLowerCase()) >=
                                    0
                                }
                                loading={this.props.api_status?.system_templates}
                                onChange={(e) => {
                                    this.props.onQuickAddValuesChange(
                                        e,
                                        'system_template',
                                        'select'
                                    )
                                }
                                }
                                mode={'multiple'}
                                disabled={!this.props.show_quick_add.system || (selectedAssetTemplete && !isNaN(parseInt(selectedAssetTemplete)))}
                                // value={
                                //     !this.props.quick_add && this.props.quick_system_template_options.length==0?'template-custom':this.props?.allFields?.system_template
                                // }
                                value={
                                    this.props?.allFields?.system_template
                                }
                            >
                                {
                                    this.props.quick_system_template_options.length===0?(
                                        <AntOption value={'template-custom'} key={'template-custom'}>
                                            Custom
                                        </AntOption>
                                    ):""
                                }
                                {
                                    this.props.quick_system_template_options.map(
                                        (item) => (
                                            <AntOption value={'' + item.value} key={item.value}>
                                                {item.label}
                                            </AntOption>
                                        )
                                    )
                                }
                            </AntSelect>
                        </ViewComponent>
                    </div>
                ):""
            }
            </>
        ),templateSelect=(
                <div className={'tm_tc_mb_select'}>
                    <p>Asset Template *</p>
                    <ViewComponent
                        type={'select'}
                        edit_mode={this.props.edit_mode}
                        loading={assetTemplateLoading}
                    >
                        <AntSelect  
                            t={this.props.t}
                            className={'machine_template_select'}
                            placeholder={assetTemplateLoading ? 'Loading templates...' : 'Select Asset Template'}
                            showSearch={true}
                            filterOption={(input, option) =>
                                option.children
                                    .toLowerCase()
                                    .indexOf(input.toLowerCase()) >=
                                0
                            }
                            onChange={(e) =>
                                this.props.onQuickAddValuesChange(
                                    e,
                                    'thingTemplate',
                                    'select'
                                )
                            }
                            value={
                                assetTemplateLoading ? undefined : this.props?.allFields?.thingTemplate
                            }
                            loading={assetTemplateLoading}
                            disabled={assetTemplateLoading}
                        >
                            <AntOption value={'custom'} key={'custom'}>
                                Custom
                            </AntOption>
                            {this.props.thingtemplate_options.map(
                                (item) => (
                                    <AntOption value={item.id}>
                                        {item.name}
                                    </AntOption>
                                )
                            )}
                        </AntSelect>
                    </ViewComponent>
                </div>
        );

        let territoryField = "";
        if(isTerritoryEnabled &&  this.props.allFields?.customerName){
            territoryField = <div className={'tm_tc_mb_select'}>
            <p>Territory</p>
            <ViewComponent
                type="custom"
                options_data_name="territoryNames"
                options_value_key="id"
                edit_mode={this.props.edit_mode}
            >
                <TerritorySelect
                    disabled={this.props?.allFields?.site_territory_id}
                    territoryData={this.props.territoryData}
                    territoryNames={this.props.territoryNames}
                    value={this.props?.allFields?.territory_id}
                    territoryId={this.props?.allFields?.territory_id}
                    setTerritoryId={(e) =>
                        this.props.onQuickAddValuesChange(e, "territory_id", "select")
                    }
                />
            </ViewComponent>
            </div>
        }
        const date_option = {
            format: 'DD MMM YYYY',
            showNow: false,
            type: 'date_picker_default',
            showTime: false,
        };
        const start_date_option = {
            placeholder: 'Select Date',
            ...date_option,
        };
        let show_single_asset_info_label=this.props.isThingAdd && Array.isArray(this.props.thing_types) && this.props.thing_types.length==1;
        let is_asset_template_available=(this.props.thingtemplate_options && Array.isArray(this.props.thingtemplate_options) && this.props.thingtemplate_options.length>0)
        return (
            <div className={`tm_ta_quick_form_cont ${!this.props.quick_add?"tm_ta_general_form_cont":""}`}>
                {
                    this.props.section_key && this.props.section_key ==="device_config"?(
                        <>
                        {selectDevice}
                        {!this.props.advanced_device_config?selectSystemTemplate:""}
                        </>
                    ):(
                        <>
                        {
                            (show_single_asset_info_label)?(
                                <div className={'tm_tc_mb_select'} style={this.props.isMobile?{}:{width:'100%'}}>
                                    <p>Asset Type : {this.props.thing_types[0]?.name}</p>
                                </div>
                            ):""
                        }
                            <div className={'tm_tc_mb_select'}>
                                <p>
                                    {this.props.t("asset_name")} *
                                </p>
                                {/* <p>Asset Name *</p> */}
                                <ViewComponent
                                    type={'input'}
                                    edit_mode={this.props.edit_mode}
                                >
                                    <AntInput
                                        placeholder={
                                            `${this.props.t("enter_asset_name")}`
                                        }
                                        // placeholder={'Enter Asset Name'}
                                        value={this.props?.allFields?.thingName}
                                        onChange={(e) =>
                                            this.props.onQuickAddValuesChange(
                                                e,
                                                'thingName',
                                                'input'
                                            )
                                        }
                                    />
                                </ViewComponent>
                            </div>
                            {
                                ((!this.props.is_rental || (this.props.is_rental && parseInt(this.props.application_id)===12)) && parseInt(this.props.application_id)!==16) ?(
                                    <div className={'tm_tc_mb_select'}>
                                        <p>{this.props.t? this.props.t('customer'): 'Customer'} {this.props.isCustomerOptional ? '' : '*'}</p>
                                        {
                                            this.props.allFields?.new_customer ?(
                                                <AntInput
                                                    value={this.props?.allFields?.new_customer}
                                                    onChange={(e) =>
                                                        this.props.onQuickAddValuesChange(
                                                            e,
                                                            'new_customer',
                                                            'input'
                                                        )
                                                    }
                                                />
                                            ):(
                                                <ViewComponent
                                                    type={'select'}
                                                    edit_mode={this.props.edit_mode}
                                                >
                                                    <AntSelect
                                                        t={this.props.t}
                                                        className={'machine_template_select'}
                                                        placeholder={
                                                            `${this.props.t("select_customer")}`
                                                        }
                                                        // placeholder={'Select Customer'}
                                                        allowClear={this.props.isCustomerOptional}
                                                        showSearch={true}
                                                        loading={this.props.customer_list_loading}
                                                        disabled={/*!this.props.assetWithoutCustomer &&*/ !this.props.isThingAdd}
                                                        onChange={(e) =>
                                                            this.props.onQuickAddValuesChange(
                                                                e,
                                                                'customerName',
                                                                'select'
                                                            )
                                                        }
                                                        onScrollEnd={()=>{
                                                            this.props.getCustomers(true,true)
                                                        }}
                                                        onSearch={(e)=>{
                                                            this.props.updateMainState({customer_search:e,customer_list_loading:true},()=>{
                                                                if (this.getCustomerTimeOut){
                                                                    clearTimeout(this.getCustomerTimeOut)
                                                                };
                                                                this.getCustomerTimeOut=setTimeout(()=>{
                                                                    this.props.getCustomers(true)
                                                                },800)
                                                            })
                                                        }}
                                                        filterOption={false}
                                                        notFoundContent={<NoDataComponent text={'No Customers found'}/>}
                                                        // onSearch={(val)=>{
                                                        //     this.setState({customer_search:val})
                                                        // }}
                                                        value={
                                                            // this.props?.allFields?.customerName
                                                            this.getCustomerValue()
                                                        }
                                                    >
                                                        {/*{*/}
                                                        {/*    this.state.customer_search?(*/}
                                                        {/*        <AntOption*/}
                                                        {/*            value={this.state.customer_search}*/}
                                                        {/*            className="rent-option-item"*/}
                                                        {/*            key="new_customer"*/}
                                                        {/*        >*/}
                                                        {/*            {this.state.customer_search +" (click to add customer)"}*/}
                                                        {/*        </AntOption>*/}
                                                        {/*    ):""*/}
                                                        {/*}*/}
                                                        {(this.props.customer_search===''?this.props.customers_list:this.props.search_customers_list).map(
                                                            (item) => (
                                                                <AntOption value={item.id} key={item.id}>
                                                                    <img style={{width:25,height:18,marginRight:10}} src={item.is_vendor?partner:customer} alt=""/> {item.name}
                                                                </AntOption>
                                                            )
                                                        )}
                                                    </AntSelect>
                                                </ViewComponent>
                                            )
                                        }
                                    </div>
                                ):""
                            }
                        {
                            !show_single_asset_info_label && (
                                <div className={'tm_tc_mb_select'}>
                                    <p>
                                        {this.props.t("asset_type")} *
                                    </p>
                                    {/* <p>Asset Type *</p> */}
                                    <ViewComponent
                                        type={'select'}
                                        edit_mode={this.props.edit_mode}
                                    >
                                        <AntSelect
                                            t={this.props.t}
                                            className={'machine_template_select'}
                                            placeholder={
                                                `${this.props.t('select_asset_type')}`
                                            }
                                            // placeholder={'Select Asset Type'}
                                            filterOption={(input, option) =>
                                                option.children
                                                    .toLowerCase()
                                                    .indexOf(input.toLowerCase()) >=
                                                0
                                            }
                                            showSearch={true}
                                            disabled={!this.props.isThingAdd || (!this.props.isCustomerOptional && !this.props?.allFields?.customerName)}
                                            onChange={(e) =>
                                                this.props.onQuickAddValuesChange(
                                                    e,
                                                    'thingType',
                                                    'select'
                                                )
                                            }
                                            value={this.props?.allFields?.thingType}
                                        >
                                            {this.props.thing_types.map((item) => (
                                                <AntOption value={item.id} key={item.id}>
                                                    {item.name}
                                                </AntOption>
                                            ))}
                                        </AntSelect>
                                    </ViewComponent>
                                </div>
                            )
                        }
                            {
                                ((parseInt(this.props.application_id)===12 || this.props.enabled_features?.includes(
                                    'IndustryManagement:Dealers'
                                )) && !this.props.is_rental) ?(
                                    <div className={'tm_tc_mb_select'}>
                                        <p>Partner</p>
                                        <ViewComponent
                                            type={'select'}
                                            edit_mode={this.props.edit_mode}
                                        >
                                            <AntSelect
                                                t={this.props.t}
                                                className={'machine_template_select'}
                                                placeholder={'Select Partner'}
                                                disabled={!this.props.isThingAdd || !this.props?.allFields?.customerName}
                                                filterOption={(input, option) =>
                                                    option.name?.toLowerCase()
                                                        .indexOf(input.toLowerCase()) >=
                                                    0
                                                }
                                                showSearch={true}
                                                onChange={(e) =>
                                                    this.props.onQuickAddValuesChange(
                                                        e,
                                                        'vendorName',
                                                        'select'
                                                    )
                                                }
                                                value={this.props?.allFields?.vendorName}
                                            >
                                                {this.props.vendor_options.map((item) => (
                                                    <AntOption value={item.id} key={item.id} name={item.name}>
                                                        {item.customName || item.name}
                                                    </AntOption>
                                                ))}
                                            </AntSelect>
                                        </ViewComponent>
                                    </div>
                                ):""
                            }
                            {
                                is_asset_template_available?(
                                    templateSelect
                                ):""
                            }
                            {
                                (!this.props.isThingAdd && this.props.is_rental && this.props.allFields && this.state.rental_asset_type && (parseInt(this.props.application_id)===12 || parseInt(this.props.application_id)===17))?(
                                    <div className={'tm_tc_mb_select'}>
                                        <p>Rented to</p>
                                        <ViewComponent
                                            type={'select'}
                                            edit_mode={this.props.edit_mode}
                                        >
                                            <AntSelect
                                                t={this.props.t}
                                                className={'machine_template_select'}
                                                placeholder={'Select Customer'}
                                                filterOption={(input, option) =>
                                                    option.children
                                                        .toLowerCase()
                                                        .indexOf(input.toLowerCase()) >=
                                                    0
                                                }
                                                disabled={this.props.thing_details && !this.props.thing_details.is_customer_change_allowed}
                                                showSearch={true}
                                                onChange={(e) =>
                                                    this.props.onQuickAddValuesChange(
                                                        e,
                                                        'rented_to',
                                                        'select'
                                                    )
                                                }
                                                value={this.props?.allFields?.rented_to}
                                            >
                                                {this.props.rented_options.map((item) => (
                                                    <AntOption value={item.id} key={item.id}>
                                                        {item.name}
                                                    </AntOption>
                                                ))}
                                            </AntSelect>
                                        </ViewComponent>
                                    </div>
                                ):""
                            }
                            {
                                ( this.props.allFields && parseInt(this.props.allFields.thingType)===18) ?(
                                    <>

                                        <div className={'tm_tc_mb_select'}>
                                            <p>Engine Serial No.</p>
                                            <ViewComponent
                                                type={'input'}
                                                edit_mode={this.props.edit_mode}
                                            >
                                                <AntInput
                                                    placeholder={'Enter Serial No.'}
                                                    value={this.props?.allFields?.engine_sl_no}
                                                    onChange={(e) =>
                                                        this.props.onQuickAddValuesChange(
                                                            e,
                                                            'engine_sl_no',
                                                            'input'
                                                        )
                                                    }
                                                />
                                            </ViewComponent>
                                        </div>
                                        <div className={'tm_tc_mb_select'}>
                                            <p>Genset Serial No.</p>
                                            <ViewComponent
                                                type={'input'}
                                                edit_mode={this.props.edit_mode}
                                            >
                                                <AntInput
                                                    placeholder={'Enter Serial No.'}
                                                    value={this.props?.allFields?.genset_sl_no}
                                                    disabled={(parseInt(this.props.application_id)!==12 && parseInt(this.props.application_id)!==17) && parseInt(this.props.vendor_id)===1062}
                                                    onChange={(e) =>
                                                        this.props.onQuickAddValuesChange(
                                                            e,
                                                            'genset_sl_no',
                                                            'input'
                                                        )
                                                    }
                                                />
                                            </ViewComponent>
                                        </div>
                                    </>
                                ):""
                            }
                            {this.props.allFields && <>
                                {
                                this.props.checkMachineInfoInQuickAdd("commissioning_date")?(
                                    <div className={'tm_tc_mb_select'}>
                                        <p>Commissioning Date</p>
                                        <ViewComponent
                                            type={'date'}
                                            edit_mode={this.props.edit_mode}
                                        >
                                            <AntRangePicker
                                                allowClear
                                                className="sub_drawer_datepicker"
                                                value={this.props?.allFields?.commissioning_date?moment(this.props?.allFields?.commissioning_date,'X'):""}
                                                // value={moment(1698855244,'X')}
                                                {...start_date_option}
                                                onChange={(e) =>
                                                    this.props.onQuickAddValuesChange(
                                                        e,
                                                        'commissioning_date',
                                                        'date'
                                                    )
                                                }
                                            />
                                        </ViewComponent>
                                    </div>
                                ):""
                            }
                            {
                                this.props.checkMachineInfoInQuickAdd("capacity")?(
                                    <div className={'tm_tc_mb_select'}>
                                        <p>Fuel Tank Capacity(L) *</p>
                                        <ViewComponent
                                            type={'input'}
                                            edit_mode={this.props.edit_mode}
                                        >
                                            <AntInput
                                                preventScrollEffect
                                                type="number"
                                                value={this.props?.allFields?.capacity}
                                                placeholder="Enter Fuel Tank Capacity"
                                                onChange={(e) =>
                                                    this.props.onQuickAddValuesChange(
                                                        e,
                                                        'capacity',
                                                        'input'
                                                    )
                                                }
                                            />
                                        </ViewComponent>
                                    </div>
                                ):""
                            }

                            {
                                this.props.checkMachineInfoInQuickAdd("serial")?(
                                    <div className={'tm_tc_mb_select'}>
                                        <p>Serial No.</p>
                                        <ViewComponent
                                            type={'input'}
                                            edit_mode={this.props.edit_mode}
                                        >
                                            <AntInput
                                                value={this.props?.allFields?.serial}
                                                placeholder="Serial No."
                                                onChange={(e) =>
                                                    this.props.onQuickAddValuesChange(
                                                        e,
                                                        'serial',
                                                        'input'
                                                    )
                                                }
                                            />
                                        </ViewComponent>
                                    </div>
                                ):""
                            }

                            {
                                this.props.checkMachineInfoInQuickAdd("type")?(
                                    <div className={'tm_tc_mb_select'}>
                                        <p>Type</p>
                                        <ViewComponent
                                            type={'select'}
                                            edit_mode={this.props.edit_mode}
                                        >
                                            <AntSelect
                                                t={this.props.t}
                                                className={'machine_template_select'}
                                                placeholder="Select Type"
                                                showSearch={true}
                                                filterOption={(input, option) =>
                                                    option.children
                                                        .toLowerCase()
                                                        .indexOf(input.toLowerCase()) >=
                                                    0
                                                }
                                                onChange={(e) =>
                                                    this.props.onQuickAddValuesChange(
                                                        e,
                                                        'type',
                                                        'select'
                                                    )
                                                }
                                                value={this.props?.allFields?.type}
                                            >
                                                {[
                                                    { label: "Ambient Room", value: "Ambient Room" },
                                                    { label: "Chiller", value: "Chiller" },
                                                    { label: "Freezer", value: "Freezer" },
                                                    { label: "Dry Room", value: "Dry Room" },
                                                    { label: "Milk/Batter Room", value: "Milk/Batter Room" },
                                                    { label: "MRI Room", value: "MRI Room" },
                                                    { label: "MRI Panel", value: "MRI Panel" },
                                                    { label: "CT Scan Room", value: "CT Scan Room" },
                                                    { label: "Freezer - 1", value: "Freezer - 1" },
                                                    { label: "Freezer - 2", value: "Freezer - 2" },
                                                    { label: "Freezer - 3", value: "Freezer - 3" },
                                                    { label: "Freezer - 4", value: "Freezer - 4" },
                                                    { label: "Freezer - 5", value: "Freezer - 5" },
                                                    { label: "Freezer - 6", value: "Freezer - 6" },
                                                    { label: "Freezer - 7", value: "Freezer - 7" },
                                                    { label: "Freezer - 8", value: "Freezer - 8" },
                                                    { label: "Chiller - 1", value: "Chiller - 1" },
                                                    { label: "Chiller - 2", value: "Chiller - 2" },
                                                    { label: "Chiller - 3", value: "Chiller - 3" },
                                                    { label: "Chiller - 4", value: "Chiller - 4" },
                                                    { label: "Chiller - 5", value: "Chiller - 5" },
                                                    { label: "Chiller - 6", value: "Chiller - 6" },
                                                    { label: "Chiller - 7", value: "Chiller - 7" },
                                                    { label: "Chiller - 8", value: "Chiller - 8" }
                                                  ].map((item, index) => (
                                                    <AntOption key={index} value={item.value}>
                                                        {item.label}
                                                    </AntOption>
                                                ))}
                                            </AntSelect>
                                        </ViewComponent>
                                    </div>
                                ):""
                            }
                            
                            </>}
                            {
                                this.props.is_rental?(
                                    <p className="helper_text">
                                        This Rental Asset
                                        {
                                            this.props.isThingAdd?" Will be ":" was "
                                        }
                                        added directly to Rental Partner
                                    </p>
                                ):""
                            }
                            {
                                this.props.quick_add?(
                                    <>
                                        {
                                            !this.props.allFields.thingType && false?(
                                                <p className={'tm_ta_qf_error_msg'}>Select "Asset Type" first</p>
                                            ):(
                                                <>
                                                    {
                                                        (!is_asset_template_available || (this.props?.allFields?.thingTemplate==='custom')) && this.props.mandatory_json?
                                                            <FormView edit_mode={this.props.edit_mode} t={this.props.t}>
                                                                <FormController
                                                                    json={JSON.stringify(this.props.mandatory_json)}
                                                                    initialValues={this.props.machine_info_values && this.props.machine_info_values.mandatory ?this.props.machine_info_values.mandatory : {}}
                                                                    onParamsChange={
                                                                        (changedFields,allFields,deletedField,fieldStats)=>this.props.onMachineInfoParamsChange('mandatory',changedFields,allFields,deletedField,fieldStats)
                                                                    }/>
                                                            </FormView>:""
                                                    }
                                                </>
                                            )
                                        }
                                        <div className="tm_ta_qf_divider">
                                        {/*    <span>Device Config</span>*/}
                                        {/*    <div></div>*/}
                                        </div>
                                        {selectDevice}
                                        {territoryField}
                                        {selectSystemTemplate}
                                        <div className="tm_ta_qf_divider">
                                        {/*    <span>Location & Address</span>*/}
                                        {/*    <div></div>*/}
                                        </div>
                                        {
                                            this.props.show_location_picker && Array.isArray(this.props.device_config_values) && this.props.device_config_values.length?(
                                                <LocationPicker t={this.props.t} edit_mode={this.props.edit_mode}  isThingAdd={this.props.isThingAdd} allFields={this.props.allFields} setLocation={this.props.setLocation} extraAddressFields={this.props.extraAddressFields}/>
                                            ):""
                                        }
                                    </>
                                ):territoryField
                            }
                        </>
                    )
                }
            </div>
        );
    }
}

export default QuickAdd;
