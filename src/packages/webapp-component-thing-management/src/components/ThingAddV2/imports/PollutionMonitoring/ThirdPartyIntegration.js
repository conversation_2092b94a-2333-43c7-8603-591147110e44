import React, { Component } from 'react';
import AntInput from '@datoms/react-components/src/components/AntInput';
import AntOption from '@datoms/react-components/src/components/AntOption';
import AntSelect from '@datoms/react-components/src/components/AntSelect';
import AntSwitch from '@datoms/react-components/src/components/AntSwitch';
import ViewComponent from "../ViewComponent/ViewComponent";
import {openNotification} from "../../logic";

class ThirdPartyIntegration extends Component {
	constructor(props) {
		super(props);
		this.state = {

			drawer: false,
		};
		this.spcb_data_push_medium = [
			{ value: '0', label: this.props.t? this.props.t('server'): 'Server' },
			{ value: '1', label: this.props.t? this.props.t('device'): 'Device' },
		];
		this.wrd_data_push_medium=[
			{ value: '1', label: 'Device' }
		]
		this.spcb_protocol_list = [
			{
				value: '0',
				label: this.props.t? this.props.t('Disabled'): 'Disabled',
			},
			{
				value: '1',
				label: 'REST API',
			},
			{
				value: '2',
				label: 'Orissa ISO - 7168 API',
			},
			{
				value: '3',
				label: 'Assam PCB',
			},
			{
				value: '4',
				label: 'Madhya Pradesh SPCB'
			},
			{
				value: '5',
				label: 'Haryana (REST)',
			},
			{
				value: '8',
				label: 'Punjab (REST)',
			},{
				value: '9',
				label: 'Rajasthan (CSV)',
			},{
				value: '7',
				label: 'Maharashtra PCB',
			},{
				value: '14',
				label: 'Gujarat SPCB',
			},{
				value: '17',
				label: 'Bihar PCB',
			}
		];

		if(this.props.isAurassure) {
			this.spcb_protocol_list.splice(1, 0, {
				value: '13',
				label: 'DPCC',
			});
		}

		this.wrd_protocols=[
			{
				value: '1',
				label: 'Odisha NIC',
			}
		]
		this.getTableInputValues = this.getTableInputValues.bind(this);
	}

	onValueChange(e, name, type, board, isAnalyser) {
		let that = this,val,state_update={};
		// handle if analayser serial number is empty
		if (board==='cpcb' && (!name || name==='')){
			openNotification('error','Please Enter Analyser Serial No in asset info')
			return;
		}
		let {cpcb_data_push_details,spcb_data_push_details,updateMainState,wrd_data_push_details}=this.props
		if (type == 'file') {
			let file = e.target.files[0];
			let supportedTyes=['.pem','.ppk','.txt']
			let textTypePem = '.pem';
			let textTypePpk = '.ppk';
			let textTypeTxt = '.ppk';
			if (
				supportedTyes.some(item=>file.name.includes(item))
			) {
				let reader = new FileReader();
				reader.readAsText(file);

				reader.onload = function (e) {
					console.log(reader.result)
					if (board == 'wrd') {
						updateMainState(
							{
								wrd_data_push_details: {
									...wrd_data_push_details,
									[name]: reader.result,
								},
							},
							() => {}
						);
					} else{
						updateMainState(
							{
								spcb_data_push_details: {
									...spcb_data_push_details,
									[name]: reader.result,
								},
							},
							() => {}
						);
					}

				};
			}
		} else {
			if (type == 'input') {
				val = e.currentTarget.value;
			} else if (type == 'radio') {
				val = e.target.value;
			} else if (type == 'select' || type == 'switch') {
				val = e;
			}

			if (isAnalyser) {
				state_update={
					cpcb_data_push_details: {
						...cpcb_data_push_details,
						analyzers: {
							...cpcb_data_push_details.analyzers,
							[name]: val,
						},
					},
				}
			} else if (board == 'cpcb') {
				state_update={
					cpcb_data_push_details: {
						...cpcb_data_push_details,
						[name]: val,
					}
				}
			} else if (board == 'spcb') {
				state_update={
					spcb_data_push_details: {
						...spcb_data_push_details,
						[name]: val,
					},
				};
			}else if (board == 'wrd') {
				state_update={
					wrd_data_push_details: {
						...wrd_data_push_details,
						[name]: val,
					},
				};
			}

			updateMainState(state_update)

		}

	}

	onParamDetailsChange(e, param, name) {
		let that = this,
			findParam,
			parameter;
		let spcb_data_push_details = this.props.spcb_data_push_details;
		let val = e.currentTarget.value;
		let param_details = spcb_data_push_details.parameter_details || [];
		findParam = spcb_data_push_details.parameter_details?.find(
			(pd) => pd.name == param
		);
		console.log({ findParam });
		if (!findParam) {
			param_details.push({
				name: param,
			});
		}
		param_details = param_details.map((item, index) => {
			parameter = item;
			if (parameter.name == param) {
				parameter[name] = val;
			}
			return parameter;
		});
		this.props.updateMainState(
			{
				spcb_data_push_details: {
					...spcb_data_push_details,
					parameter_details: param_details,
				},
			},
			() => {
				// console.log('manni', that.state.spcb_data_push_details);
			}
		);
	}

	getParamName(paramKey) {
		let { all_analyser_params } = this.props;
		let name = all_analyser_params.find(
			(item) => item.destination_key == paramKey
		)?.name;
		return name || '';
	}
	getParamNameArray(params) {
		let { all_analyser_params } = this.props;
		let returnString = '',
			name;
		params.forEach((paramKey) => {
			name = all_analyser_params.find(
				(item) => item.destination_key == paramKey
			)?.name;
			if (name) {
				returnString = returnString + name + ', ';
			}
		});
		return returnString.slice(0, returnString.length - 2);
	}

	getTableInputValues(board, name, param) {
		let that = this;
		let val = '',
			parameter_details = [],
			foundParam;
		let cpcb_data_push_details_analyzers = this.props.cpcb_data_push_details
				.analyzers,
			spcb_data_push_details = this.props.spcb_data_push_details;
		if (board == 'spcb' && param) {
			parameter_details = spcb_data_push_details.parameter_details;
			foundParam = parameter_details?.find((item) => item.name == param);
			if (foundParam) {
				val = foundParam[name];
			}
		} else if (board == 'cpcb') {
			val = cpcb_data_push_details_analyzers?.[name];
		}
		return val;
	}

	render() {
		return (
			<>
				<div className={`tm_ta_3rd_party_cont`}>
					<p className="helper_text">
						{this.props.t? this.props.t('please_fill_3rd_party_data'): "Please fill 3rd Party Data Transmission details"}
						{/* Please fill 3rd Party Data Transmission details */}
					</p>
					{
						parseInt(this.props.thing_type)===86?(
							<>
								<p className="content_heading">WRD Details</p>
								<div className={'tm_ta_pollution_form'}>
									<div className="mformItem">
										<div className="tc_pollution_mi_form_label">
											Data Push Medium:
										</div>
										<ViewComponent
											type={'select'}
											edit_mode={this.props.edit_mode}
										>
											<AntSelect
												className={
													'tc_pollution_analyzer_status_select'
												}
												placeholder={'Select Medium'}
												// mode={'multiple'}
												value={
													this.props.wrd_data_push_details
														?.data_push_medium
												}
												onChange={(e) =>
													this.onValueChange(
														e,
														'data_push_medium',
														'select',
														'wrd'
													)
												}
												// style={{ width: 120 }}
											>
												{this.wrd_data_push_medium.map(
													(item) => (
														<AntOption value={item.value}>
															{item.label}
														</AntOption>
													)
												)}
											</AntSelect>
										</ViewComponent>
									</div>
									<div className="mformItem">
										<div className="tc_pollution_mi_form_label">
											Protocol:
										</div>
										<ViewComponent
											type={'select'}
											edit_mode={this.props.edit_mode}
										>
											<AntSelect
												className={
													'tc_pollution_analyzer_status_select'
												}
												placeholder={'Select Protocol'}
												// mode={'multiple'}
												value={
													this.props.wrd_data_push_details
														?.server_type
												}
												onChange={(e) =>
													this.onValueChange(
														e,
														'server_type',
														'select',
														'wrd'
													)
												}
												// style={{ width: 120 }}
											>
												{this.wrd_protocols.map((item) => (
													<AntOption value={item.value}>
														{item.label}
													</AntOption>
												))}
											</AntSelect>
										</ViewComponent>
									</div>
									<div className="mformItem">
										<div className="tc_pollution_mi_form_label">
											Device ID:
										</div>
										<div className="ant-form-item-control-input">
											<ViewComponent
												type={'input'}
												edit_mode={this.props.edit_mode}
											>
												<AntInput
													placeholder={
														'Enter Device ID'
													}
													value={
														this.props
															.wrd_data_push_details
															?.device_id
													}
													onChange={(e) =>
														this.onValueChange(
															e,
															'device_id',
															'input',
															'wrd'
														)
													}
												/>
											</ViewComponent>
										</div>
									</div>

									<div className="mFullFormItem">
										<div className="tc_pollution_mi_form_label">
											Scramble Code
										</div>
										<div className="ant-form-item-control-input">
											<input
												type="file"
												accept=".txt"
												onChange={(e) =>
													this.onValueChange(
														e,
														'scramble_code',
														'file',
														'wrd'
													)
												}
											/>
										</div>
									</div>
									<div className="mFullFormItem">
										<div className="tc_pollution_mi_form_label"></div>
										<div className="ant-form-item-control-input">
												<textarea
													className={
														'pollution_key_text_area'
													}
													disabled={true}
													value={
														this.props
															.wrd_data_push_details
															?.scramble_code
													}
												></textarea>
										</div>
									</div>
								</div>
							</>
						):(
							<>
								<p className="content_heading">
									{this.props.t? this.props.t('SPCB Details'): "SPCB Details"}
									{/* SPCB Details */}
								</p>
								<div className={'tm_ta_pollution_form'}>
									<div className="mformItem">
										<div className="tc_pollution_mi_form_label">
										{this.props.t? this.props.t('data_push_medium')+":": "Data Push Medium:"}
											{/* Data Push Medium: */}
										</div>
										<ViewComponent
											type={'select'}
											edit_mode={this.props.edit_mode}
										>
											<AntSelect
												className={
													'tc_pollution_analyzer_status_select'
												}
												placeholder={this.props.t? this.props.t('Select Medium'): 'Select Medium'}
												// mode={'multiple'}
												value={
													this.props.spcb_data_push_details
														?.data_push_medium
												}
												onChange={(e) =>
													this.onValueChange(
														e,
														'data_push_medium',
														'select',
														'spcb'
													)
												}
												// style={{ width: 120 }}
											>
												{this.spcb_data_push_medium.map(
													(item) => (
														<AntOption value={item.value}>
															{item.label}
														</AntOption>
													)
												)}
											</AntSelect>
										</ViewComponent>
									</div>
									<div className="mformItem">
										<div className="tc_pollution_mi_form_label">
											{this.props.t? this.props.t('protocol')+":": "Protocol: "}
											{/* Protocol: */}
										</div>
										<ViewComponent
											type={'select'}
											edit_mode={this.props.edit_mode}
										>
											<AntSelect
												className={
													'tc_pollution_analyzer_status_select'
												}
												placeholder={this.props.t? this.props.t('Select Protocol'): 'Select Protocol'}
												// mode={'multiple'}
												value={
													this.props.spcb_data_push_details
														?.server_type
												}
												onChange={(e) =>
													this.onValueChange(
														e,
														'server_type',
														'select',
														'spcb'
													)
												}
												// style={{ width: 120 }}
											>
												{this.spcb_protocol_list.map((item) => (
													<AntOption value={item.value}>
														{item.label}
													</AntOption>
												))}
											</AntSelect>
										</ViewComponent>
									</div>
									{this.props.spcb_data_push_details
										?.server_type === '13' && (
										<>
											<div className="mformItem">
												<div className="tc_pollution_mi_form_label">
													Site ID:
												</div>
												<div className="ant-form-item-control-input">
													<ViewComponent
														type={'input'}
														edit_mode={this.props.edit_mode}
													>
														<AntInput
															placeholder={
																'Enter Site ID'
															}
															value={
																this.props
																	.spcb_data_push_details
																	?.site_id
															}
															onChange={(e) =>
																this.onValueChange(
																	e,
																	'site_id',
																	'input',
																	'spcb'
																)
															}
														/>
													</ViewComponent>
												</div>
											</div>
											<div className="mformItem">
												<div className="tc_pollution_mi_form_label">
													Token:
												</div>
												<div className="ant-form-item-control-input">
													<ViewComponent
														type={'input'}
														edit_mode={this.props.edit_mode}
													>
														<AntInput
															placeholder={
																'Enter Token'
															}
															value={
																this.props
																	.spcb_data_push_details
																	?.token
															}
															onChange={(e) =>
																this.onValueChange(
																	e,
																	'token',
																	'input',
																	'spcb'
																)
															}
														/>
													</ViewComponent>
												</div>
											</div>
										</>
									)}
									{this.props.spcb_data_push_details
										?.server_type === '1' && (
										<>
											<div className="mformItem">
												<div className="tc_pollution_mi_form_label">
													Station ID:
												</div>
												<div className="ant-form-item-control-input">
													<ViewComponent
														type={'input'}
														edit_mode={this.props.edit_mode}
													>
														<AntInput
															placeholder={
																'Enter Station ID'
															}
															value={
																this.props
																	.spcb_data_push_details
																	?.station_id
															}
															onChange={(e) =>
																this.onValueChange(
																	e,
																	'station_id',
																	'input',
																	'spcb'
																)
															}
														/>
													</ViewComponent>
												</div>
											</div>
											<div className="mformItem">
												<div className="tc_pollution_mi_form_label">
													Organization ID:
												</div>
												<div className="ant-form-item-control-input">
													<ViewComponent
														type={'input'}
														edit_mode={this.props.edit_mode}
													>
														<AntInput
															placeholder={
																'Enter Organization ID'
															}
															value={
																this.props
																	.spcb_data_push_details
																	?.organization_id
															}
															onChange={(e) =>
																this.onValueChange(
																	e,
																	'organization_id',
																	'input',
																	'spcb'
																)
															}
														/>
													</ViewComponent>
												</div>
											</div>
											<div className="mFullFormItem">
												<div className="tc_pollution_mi_form_label">
													Data Push Token NO:
												</div>
												<div className="ant-form-item-control-input">
													<ViewComponent
														type={'input'}
														edit_mode={this.props.edit_mode}
													>
														<AntInput
															placeholder={'Enter Token'}
															value={
																this.props
																	.spcb_data_push_details
																	?.data_push_token
															}
															onChange={(e) =>
																this.onValueChange(
																	e,
																	'data_push_token',
																	'input',
																	'spcb'
																)
															}
														/>
													</ViewComponent>
												</div>
											</div>
										</>
									)}
									{(this.props.spcb_data_push_details
											?.server_type === '5' ||
										this.props.spcb_data_push_details
											?.server_type === '8' || this.props.spcb_data_push_details
											?.server_type === '17' || this.props.spcb_data_push_details
											?.server_type === '7' ||
											this.props.spcb_data_push_details
											?.server_type === '14') && (
										<>
											<div className="mformItem">
												<div className="tc_pollution_mi_form_label">
													Industry ID
												</div>
												<div className="ant-form-item-control-input">
													<ViewComponent
														type={'input'}
														edit_mode={this.props.edit_mode}
													>
														<AntInput
															placeholder={'Enter ID'}
															value={
																this.props
																	.spcb_data_push_details
																	?.industry_id
															}
															onChange={(e) =>
																this.onValueChange(
																	e,
																	'industry_id',
																	'input',
																	'spcb'
																)
															}
														/>
													</ViewComponent>
												</div>
											</div>
											<div className="mformItem">
												<div className="tc_pollution_mi_form_label">
													Station ID
												</div>
												<div className="ant-form-item-control-input">
													<ViewComponent
														type={'input'}
														edit_mode={this.props.edit_mode}
													>
														<AntInput
															placeholder={'Enter ID'}
															value={
																this.props
																	.spcb_data_push_details
																	?.station_id
															}
															onChange={(e) =>
																this.onValueChange(
																	e,
																	'station_id',
																	'input',
																	'spcb'
																)
															}
														/>
													</ViewComponent>
												</div>
											</div>
										</>
									)}
									{(this.props.spcb_data_push_details
											?.server_type === '7' || this.props.spcb_data_push_details
											?.server_type === '14') &&
									<div className="mFullFormItem">
										<div className="tc_pollution_mi_form_label">
											AES Encryption Key
										</div>
										<div className="ant-form-item-control-input">
											<ViewComponent
												type={'input'}
												edit_mode={this.props.edit_mode}
											>
												<AntInput
													placeholder={'Enter Key'}
													value={
														this.props
															.spcb_data_push_details
															?.aes_encryption_key
													}
													onChange={(e) =>
														this.onValueChange(
															e,
															'aes_encryption_key',
															'input',
															'spcb'
														)
													}
												/>
											</ViewComponent>
										</div>
									</div>
									}
									{(this.props.spcb_data_push_details
											?.server_type === '2' ||
										this.props.spcb_data_push_details
											?.server_type === '3' || this.props.spcb_data_push_details
											?.server_type === '4') && (
										<>
											<div className="mformItem">
												<div className="tc_pollution_mi_form_label">
													Site ID
												</div>
												<div className="ant-form-item-control-input">
													<ViewComponent
														type={'input'}
														edit_mode={this.props.edit_mode}
													>
														<AntInput
															placeholder={'Enter Site ID'}
															value={
																this.props
																	.spcb_data_push_details
																	?.site_id
															}
															onChange={(e) =>
																this.onValueChange(
																	e,
																	'site_id',
																	'input',
																	'spcb'
																)
															}
														/>
													</ViewComponent>
												</div>
											</div>
											<div className="mformItem">
												<div className="tc_pollution_mi_form_label">
													Station Name
												</div>
												<div className="ant-form-item-control-input">
													<ViewComponent
														type={'input'}
														edit_mode={this.props.edit_mode}
													>
														<AntInput
															placeholder={'Enter Name'}
															value={
																this.props
																	.spcb_data_push_details
																	?.station_name
															}
															onChange={(e) =>
																this.onValueChange(
																	e,
																	'station_name',
																	'input',
																	'spcb'
																)
															}
														/>
													</ViewComponent>
												</div>
											</div>
											{this.props.spcb_data_push_details
											?.server_type !== '4' && <div className="mformItem">
												<div className="tc_pollution_mi_form_label">
													Station Long Name (workflow)
												</div>
												<div className="ant-form-item-control-input">
													<ViewComponent
														type={'input'}
														edit_mode={this.props.edit_mode}
													>
														<AntInput
															placeholder={'Enter Name'}
															value={
																this.props
																	.spcb_data_push_details
																	?.station_long_name
															}
															onChange={(e) =>
																this.onValueChange(
																	e,
																	'station_long_name',
																	'input',
																	'spcb'
																)
															}
														/>
													</ViewComponent>
												</div>
											</div>}
											<div className="mFullFormItem">
												<div className="tc_pollution_mi_form_label">
													AES Encryption Key
												</div>
												<div className="ant-form-item-control-input">
													<ViewComponent
														type={'input'}
														edit_mode={this.props.edit_mode}
													>
														<AntInput
															placeholder={'Enter Key'}
															value={
																this.props
																	.spcb_data_push_details
																	?.aes_encryption_key
															}
															onChange={(e) =>
																this.onValueChange(
																	e,
																	'aes_encryption_key',
																	'input',
																	'spcb'
																)
															}
														/>
													</ViewComponent>
												</div>
											</div>
											<div className="mFullFormItem">
												<div className="tc_pollution_mi_form_label">
													Site Private Key
												</div>
												<div className="ant-form-item-control-input">
													<input
														type="file"
														accept=".ppk"
														onChange={(e) =>
															this.onValueChange(
																e,
																'site_private_key',
																'file',
																'spcb'
															)
														}
													/>
												</div>
											</div>
											<div className="mFullFormItem">
												<div className="tc_pollution_mi_form_label"></div>
												<div className="ant-form-item-control-input">
												<textarea
													className={
														'pollution_key_text_area'
													}
													disabled={true}
													value={
														this.props
															.spcb_data_push_details
															?.site_private_key
													}
												></textarea>
												</div>
											</div>
											{this.props.spcb_data_push_details
											?.server_type !== '4' && <><div className="mFullFormItem">
												<div className="tc_pollution_mi_form_label">
													Site Public Key
												</div>
												<div className="ant-form-item-control-input">
													<input
														type="file"
														accept=".pem"
														onChange={(e) =>
															this.onValueChange(
																e,
																'site_public_key',
																'file',
																'spcb'
															)
														}
													/>
												</div>
											</div>
											<div className="mFullFormItem">
												<div className="tc_pollution_mi_form_label"></div>
												<div className="ant-form-item-control-input">
												<textarea
													className={
														'pollution_key_text_area'
													}
													disabled={true}
													value={
														this.props
															.spcb_data_push_details
															?.site_public_key
													}
												></textarea>
												</div>
											</div></>}
											<div className="mFullFormItem">
												<div className="tc_pollution_mi_form_label">
													Server Public Key
												</div>
												<div className="ant-form-item-control-input">
													<input
														type="file"
														accept=".pem"
														onChange={(e) =>
															this.onValueChange(
																e,
																'server_public_key',
																'file',
																'spcb'
															)
														}
													/>
												</div>
											</div>
											<div className="mFullFormItem">
												<div className="tc_pollution_mi_form_label"></div>
												<div className="ant-form-item-control-input">
												<textarea
													className={
														'pollution_key_text_area'
													}
													disabled={true}
													value={
														this.props
															.spcb_data_push_details
															?.server_public_key
													}
												></textarea>
												</div>
											</div>
										</>
									)}
									{
										this.props.spcb_data_push_details?.server_type==='9'?(
											<>
												<div className="mformItem">
													<div className="tc_pollution_mi_form_label">
														Site ID
													</div>
													<div className="ant-form-item-control-input">
														<ViewComponent
															type={'input'}
															edit_mode={this.props.edit_mode}
														>
															<AntInput
																placeholder={'Enter Site ID'}
																value={
																	this.props
																		.spcb_data_push_details
																		?.site_id
																}
																onChange={(e) =>
																	this.onValueChange(
																		e,
																		'site_id',
																		'input',
																		'spcb'
																	)
																}
															/>
														</ViewComponent>
													</div>
												</div>
												<div className="mformItem">
													<div className="tc_pollution_mi_form_label">
														Monitoring ID
													</div>
													<div className="ant-form-item-control-input">
														<ViewComponent
															type={'input'}
															edit_mode={this.props.edit_mode}
														>
															<AntInput
																placeholder={'Enter Monitoring ID'}
																value={
																	this.props
																		.spcb_data_push_details
																		?.monitoring_id
																}
																onChange={(e) =>
																	this.onValueChange(
																		e,
																		'monitoring_id',
																		'input',
																		'spcb'
																	)
																}
															/>
														</ViewComponent>
													</div>
												</div>
											</>
										):""
									}
									{(this.props.spcb_data_push_details
											?.server_type === '2' ||
										this.props.spcb_data_push_details
											?.server_type === '3' ||
										this.props.spcb_data_push_details
											?.server_type === '9' ||
										this.props.spcb_data_push_details
											?.server_type === '5' ||
										this.props.spcb_data_push_details
											?.server_type === '8' ||
											this.props.spcb_data_push_details
											?.server_type === '17' ||
											this.props.spcb_data_push_details
											?.server_type === '7' || 
											this.props.spcb_data_push_details
											?.server_type === '14' ||
											this.props.spcb_data_push_details
											?.server_type === '4') &&
									this.props.analyser_details &&
									this.props.analyser_details[0] &&
									this.props.analyser_details[0].params &&
									this.props.analyser_details[0].params.length ? (
										<div className="mFullFormItem">
											<div className="tc_pollution_mi_form_label">
												Set Analyser ID for Parameters:
											</div>
											<table className="tc_pollution_table">
												<thead className="thead">
												<tr>
													<th className="text-center">
														{' '}
														Parameter Name{' '}
													</th>
													<th>Analayzer Id</th>
													{this.props
															.spcb_data_push_details
															?.server_type ===
														'2' && (
															<>
																<th>
																	Parameter Id
																	(workflow)
																</th>
																<th>
																	Long Name
																	(workflow)
																</th>
															</>
														)}
													{this.props
															.spcb_data_push_details
															?.server_type ===
														'9' && (
															<>
																<th>
																	Parameter Id
																</th>
																<th>
																	Long Name
																</th>
																<th>
																	Unit ID
																</th>
															</>
														)}
														{(this.props
															.spcb_data_push_details
															?.server_type ===
														'7' || this.props.spcb_data_push_details
														?.server_type === '14')&& (
															<>
																<th>
																	Parameter Id
																</th>
																<th>
																	Unit ID
																</th>
															</>
														)}
												</tr>
												</thead>
												<tbody>
												{this.props.analyser_details.map(
													(analyser, index) => {
														return analyser.params.map(
															(param) => (
																<tr
																	key={
																		index +
																		param
																	}
																>
																	<td className="text-center">
																		{' '}
																		{this.getParamName(
																			param
																		)}{' '}
																	</td>
																	<td>
																		<ViewComponent
																			type={'input'}
																			edit_mode={this.props.edit_mode}
																		>
																			<AntInput
																				placeholder={
																					'Enter Analyzer ID'
																				}
																				value={this.getTableInputValues(
																					'spcb',
																					'analyzer_id',
																					param
																				)}
																				onChange={(
																					e
																				) =>
																					this.onParamDetailsChange(
																						e,
																						param,
																						'analyzer_id'
																					)
																				}
																			/>
																		</ViewComponent>
																	</td>
																	{this.props
																		.spcb_data_push_details
																		?.server_type ===
																	'2' ? (
																		<>
																			<td>
																				<ViewComponent
																					type={'input'}
																					edit_mode={this.props.edit_mode}
																				>
																					<AntInput
																						placeholder={
																							'Enter Parameter ID'
																						}
																						value={this.getTableInputValues(
																							'spcb',
																							'parameter_id',
																							param
																						)}
																						// value={
																						// 	this.state
																						// 		.cpcb_data_push_details &&
																						// 	this.state
																						// 		.cpcb_data_push_details
																						// 		.station_id
																						// }
																						onChange={(
																							e
																						) =>
																							this.onParamDetailsChange(
																								e,
																								param,
																								'parameter_id'
																							)
																						}
																					/>
																				</ViewComponent>
																			</td>
																			<td>
																				<ViewComponent
																					type={'input'}
																					edit_mode={this.props.edit_mode}
																				>
																					<AntInput
																						placeholder={
																							'Enter Long Name'
																						}
																						value={this.getTableInputValues(
																							'spcb',
																							'long_name',
																							param
																						)}
																						// value={
																						// 	this.state
																						// 		.cpcb_data_push_details &&
																						// 	this.state
																						// 		.cpcb_data_push_details
																						// 		.station_id
																						// }
																						onChange={(
																							e
																						) =>
																							this.onParamDetailsChange(
																								e,
																								param,
																								'long_name'
																							)
																						}
																					/>
																				</ViewComponent>
																			</td>
																		</>
																	) : (
																		''
																	)}
																	{this.props
																		.spcb_data_push_details
																		?.server_type ===
																	'9' ? (
																		<>
																			<td>
																				<ViewComponent
																					type={'input'}
																					edit_mode={this.props.edit_mode}
																				>
																					<AntInput
																						placeholder={
																							'Enter Parameter ID'
																						}
																						value={this.getTableInputValues(
																							'spcb',
																							'id',
																							param
																						)}
																						// value={
																						// 	this.state
																						// 		.cpcb_data_push_details &&
																						// 	this.state
																						// 		.cpcb_data_push_details
																						// 		.station_id
																						// }
																						onChange={(
																							e
																						) =>
																							this.onParamDetailsChange(
																								e,
																								param,
																								'id'
																							)
																						}
																					/>
																				</ViewComponent>
																			</td>
																			<td>
																				<ViewComponent
																					type={'input'}
																					edit_mode={this.props.edit_mode}
																				>
																					<AntInput
																						placeholder={
																							'Enter Long Name'
																						}
																						value={this.getTableInputValues(
																							'spcb',
																							'long_name',
																							param
																						)}
																						// value={
																						// 	this.state
																						// 		.cpcb_data_push_details &&
																						// 	this.state
																						// 		.cpcb_data_push_details
																						// 		.station_id
																						// }
																						onChange={(
																							e
																						) =>
																							this.onParamDetailsChange(
																								e,
																								param,
																								'long_name'
																							)
																						}
																					/>
																				</ViewComponent>

																			</td>
																			<td>
																				<ViewComponent
																					type={'input'}
																					edit_mode={this.props.edit_mode}
																				>
																					<AntInput
																						placeholder={
																							'Enter Unit ID'
																						}
																						value={this.getTableInputValues(
																							'spcb',
																							'unit_id',
																							param
																						)}
																						// value={
																						// 	this.state
																						// 		.cpcb_data_push_details &&
																						// 	this.state
																						// 		.cpcb_data_push_details
																						// 		.station_id
																						// }
																						onChange={(
																							e
																						) =>
																							this.onParamDetailsChange(
																								e,
																								param,
																								'unit_id'
																							)
																						}
																					/>
																				</ViewComponent>
																			</td>
																		</>
																	) : (
																		''
																	)}
																		{(this.props
																		.spcb_data_push_details
																		?.server_type ===
																	'7' || this.props.spcb_data_push_details
																	?.server_type === '14') ? (
																		<>
																			<td>
																				<ViewComponent
																					type={'input'}
																					edit_mode={this.props.edit_mode}
																				>
																					<AntInput
																						placeholder={
																							'Enter Parameter ID'
																						}
																						value={this.getTableInputValues(
																							'spcb',
																							'parameter_id',
																							param
																						)}
																						// value={
																						// 	this.state
																						// 		.cpcb_data_push_details &&
																						// 	this.state
																						// 		.cpcb_data_push_details
																						// 		.station_id
																						// }
																						onChange={(
																							e
																						) =>
																							this.onParamDetailsChange(
																								e,
																								param,
																								'parameter_id'
																							)
																						}
																					/>
																				</ViewComponent>
																			</td>
																			<td>
																				<ViewComponent
																					type={'input'}
																					edit_mode={this.props.edit_mode}
																				>
																					<AntInput
																						placeholder={
																							'Enter Unit ID'
																						}
																						value={this.getTableInputValues(
																							'spcb',
																							'unit_id',
																							param
																						)}
																						// value={
																						// 	this.state
																						// 		.cpcb_data_push_details &&
																						// 	this.state
																						// 		.cpcb_data_push_details
																						// 		.station_id
																						// }
																						onChange={(
																							e
																						) =>
																							this.onParamDetailsChange(
																								e,
																								param,
																								'unit_id'
																							)
																						}
																					/>
																				</ViewComponent>
																			</td>
																		</>
																	) : (
																		''
																	)}
																</tr>
															)
														);
													}
												)}
												</tbody>
											</table>
										</div>
									) : (
										''
									)}
								</div>
								{!this.props.isAurassure ? <>
								<p className="content_heading">CPCB Details</p>
								<div className={'tm_ta_pollution_form'}>
									<div className="mformItem mSwitchFromItem">
										<div className="tc_pollution_mi_form_label">
											<span>Data publish:</span>
											<ViewComponent
												type={'switch'}
												edit_mode={this.props.edit_mode}
											>
												<AntSwitch
													checked={
														this.props
															.cpcb_data_push_details &&
														this.props
															.cpcb_data_push_details
															.data_published
													}
													disabled={
														this.props
															.cpcb_data_push_details_clone &&
														this.props
															.cpcb_data_push_details_clone
															.data_published
													}
													onChange={(e) =>
														this.onValueChange(
															e,
															'data_published',
															'switch',
															'cpcb'
														)
													}
												></AntSwitch>
											</ViewComponent>

										</div>
									</div>
									<div className="mformItem mSwitchFromItem">
										<div className="tc_pollution_mi_form_label">
											<span>Send Alerts:</span>
											<ViewComponent
												type={'switch'}
												edit_mode={this.props.edit_mode}
											>
												<AntSwitch
													checked={
														this.props
															.cpcb_data_push_details &&
														this.props
															.cpcb_data_push_details
															.send_alerts
													}
													disabled={
														this.props
															.cpcb_data_push_details_clone &&
														this.props
															.cpcb_data_push_details_clone
															.send_alerts
													}
													onChange={(e) =>
														this.onValueChange(
															e,
															'send_alerts',
															'switch',
															'cpcb'
														)
													}
												></AntSwitch>
											</ViewComponent>
										</div>
									</div>
									<div className="mformItem mSwitchFromItem">
										<div className="tc_pollution_mi_form_label">
											<span>Push Data:</span>
											<ViewComponent
												type={'switch'}
												edit_mode={this.props.edit_mode}
											>
												<AntSwitch
													checked={
														this.props
															.cpcb_data_push_details &&
														this.props
															.cpcb_data_push_details
															.push_data
													}
													onChange={(e) =>
														this.onValueChange(
															e,
															'push_data',
															'switch',
															'cpcb'
														)
													}
												/>
											</ViewComponent>
										</div>
									</div>
									<div className="mformItem">
										<div className="tc_pollution_mi_form_label">
											Inudstry ID:
										</div>
										<div className="ant-form-item-control-input">
											<ViewComponent
												type={'input'}
												edit_mode={this.props.edit_mode}
											>
												<AntInput
													placeholder={'Enter ID'}
													value={
														this.props
															.cpcb_data_push_details &&
														this.props
															.cpcb_data_push_details
															.industry_id
													}
													onChange={(e) =>
														this.onValueChange(
															e,
															'industry_id',
															'input',
															'cpcb'
														)
													}
												/>
											</ViewComponent>
										</div>
									</div>
									<div className="mformItem">
										<div className="tc_pollution_mi_form_label">
											Station ID:
										</div>
										<div className="ant-form-item-control-input">
											<ViewComponent
												type={'input'}
												edit_mode={this.props.edit_mode}
											>
												<AntInput
													placeholder={'Enter ID'}
													value={
														this.props
															.cpcb_data_push_details &&
														this.props
															.cpcb_data_push_details
															.station_id
													}
													onChange={(e) =>
														this.onValueChange(
															e,
															'station_id',
															'input',
															'cpcb'
														)
													}
												/>
											</ViewComponent>
										</div>
									</div>
									{this.props.analyser_details &&
									this.props.analyser_details.length ? (
										<div className="mFullFormItem">
											<table className="tc_pollution_table">
												<thead className="thead">
												<tr>
													<th className="text-center">
														{' '}
														Make / Model{' '}
													</th>
													<th>Serial No.</th>
													<th>Parameters</th>
													<th>Device Id</th>
												</tr>
												</thead>
												{this.props.analyser_details.map(
													(analyser, index) => (
														<tr key={index}>
															<td className="text-center">
																{' '}
																{
																	analyser.make
																} / {analyser.model}{' '}
															</td>
															<td>
																{analyser.serial_no}
															</td>
															<td>
																{this.getParamNameArray(
																	analyser.params
																)}
															</td>
															<td>
																<ViewComponent
																	type={'input'}
																	edit_mode={this.props.edit_mode}
																>
																	<AntInput
																		placeholder={
																			'Enter Device ID'
																		}
																		value={this.getTableInputValues(
																			'cpcb',
																			analyser.serial_no
																		)}
																		// value={
																		// 	this.state
																		// 		.cpcb_data_push_details &&
																		// 	this.state
																		// 		.cpcb_data_push_details
																		// 		.station_id
																		// }
																		onChange={(e) =>
																			this.onValueChange(
																				e,
																				analyser.serial_no,
																				'input',
																				'cpcb',
																				true
																			)
																		}
																	/>
																</ViewComponent>
															</td>
														</tr>
													)
												)}
												<tbody></tbody>
											</table>
										</div>
									) : (
										''
									)}
								</div>
								</> : null}
							</>
						)
					}
				</div>
			</>
		);
	}
}

export default ThirdPartyIntegration;
