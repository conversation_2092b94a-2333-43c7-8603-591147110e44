import { flushSync } from "react-dom";
import {
  deviceLinkedtoThing,
  retriveCustomerList,
  retriveApplicationThings,
  devicesList,
  deviceLists,
  getThingCategoryDetails,
  getThingTemplates,
  getMachineInfoTemplates,
  getProtocolData,
  getProtocolsList,
  getTemplateData,
  getTemplateList,
  addThing,
  assignDevice,
  checkEngineGensetSerialNo,
  getThingConfigsNew,
  uploadFile,
  saveThingConfigsNew,
  retriveCustomerDetails,
  retriveApplications,
} from "@datoms/js-sdk";
import { isCustomerValid } from "@datoms/js-utils/src/access-functions";
import { mixPanelTrackUser } from "@datoms/js-utils/src/mix-panel";
import _ from "lodash";
import moment from "moment-timezone";
import defaultParams from "./configuration/DefaultParams";
import { location_param } from "./configuration/formJson";
import AntNotification from "@datoms/react-components/src/components/AntNotification";
import AntMessage from "@datoms/react-components/src/components/AntMessage";
import { validateFileType } from "@datoms/js-utils/src/file-type-validation";
import {
  generalForm,
  nonDatomsgeneralForm,
} from "../ThingConfigurationNew/configuration/formJson";
import { aurassureInfo } from "./configuration/formJson";
// import { getTerritoryData } from ''
import d_green from "../../imgs/v2/done-green.svg";
import d_orange from "../../imgs/v2/done-orange.svg";
import d_black from "../../imgs/v2/done-black.svg";
import params from "../CustomerThingAdd/params";
// import { getTerritoryData } from '../../../../webapp-component-user-management/src/components/TerritoryPage/TerritorySelect';
let moment_convertions = [
  "commissioning_date",
  "lifetime_runhour_manual_record_time",
  "installation_date",
  "activation_date",
  "last_maintenance_date",
];

export const CONSTANTS = {
  PHOENIX_VENDOR: {
    id: 1,
    name: "Phoenix Robotix",
  },
  data_units: [
    { value: "ppm", label: "ppm" },
    { value: "mg/Nm3", label: "mg/Nm3" },
    { value: "μg/Nm3", label: "μg/Nm3" },
    { value: "%", label: "%" },
    { value: "m/s", label: "m/s" },
    { value: "mm", label: "mm" },
    { value: "Wh", label: "Wh" },
    { value: "V", label: "V" },
    { value: "A", label: "A" },
    { value: "Hz", label: "Hz" },
    { value: "Watt", label: "Watt" },
    { value: "W", label: "W" },
    { value: "kW", label: "kW" },
    { value: "kWh", label: "kWh" },
    { value: "μg/m3", label: "μg/m3" },
    { value: "mg/m3", label: "mg/m3" },
    { value: "mg/l", label: "mg/l" },
    { value: "m3/hr", label: "m3/hr" },
    { value: "pH", label: "pH" },
    { value: "Bar", label: "Bar" },
    { value: "kwh", label: "kwh" },
    { value: "degree", label: "degree" },
    { value: "ppb", label: "ppb" },
    { value: "mBar", label: "mBar" },
    { value: "mmHg", label: "mmHg" },
    { value: "mmwc", label: "mmwc" },
    { value: "mmH2O", label: "mmH2O" },
    { value: "°C", label: "°C" },
    { value: "hpa", label: "hpa" },
    { value: "mVolt", label: "mVolt" },
    { value: "vpm", label: "vpm" },
    { value: "w/m2", label: "w/m2" },
    { value: "mph", label: "mph" },
    { value: "l", label: "l" },
    { value: "m3", label: "m3" },
    { value: "l/hr", label: "l/hr" },
    { value: "Nm3/h", label: "Nm3/h" },
    { value: "m", label: "m" },
    { value: "ft", label: "ft" },
    { value: "/L", label: "/L" },
    { value: "dB(A)", label: "dB(A)" },
    { value: "mW/cm2", label: "mW/cm2" },
    { value: "lux", label: "lux" },
    { value: "km/h", label: "km/h" },
    { value: "inch", label: "inch" },
    { value: "cm", label: "cm" },
    { value: "mt", label: "mt" },
    { value: "°F", label: "°F" },
    { value: "atm", label: "atm" },
    { value: "/m3", label: "/m3" },
    { value: "/ft3", label: "/ft3" },
    { value: "VA", label: "VA" },
    { value: "kVA", label: "kVA" },
    { value: "VAR", label: "VAR" },
    { value: "kVAR", label: "kVAR" },
    { value: "VAh", label: "VAh" },
    { value: "kVAh", label: "kVAh" },
    { value: "kPa", label: "kPa" },
    { value: "psi", label: "psi" },
    { value: "%LEL", label: "%LEL" },
  ],
  data_handling: [
    { value: "disabled", label: "Disabled" },
    { value: "prev_value", label: "Send Previous Value" },
    { value: "nan", label: "Send NAN" },
    { value: "0", label: "Send Zero Value" },
    { value: "last_hr_avg", label: "Last Hour Average Value" },
    { value: "range_min", label: "Send Min Value" },
    { value: "range_max", label: "Send Max Value" },
    { value: "range_min_max", label: "Send Min/Max Value" },
  ],
  fuel_sensors: {
    18: [
      {
        value: "0",
        label: "None [Fuel Sensor]",
      },
      {
        value: "1",
        label: "Analog (0-10V) [Fuel Sensor]",
      },
      {
        value: "2",
        label: "Sozi RS232 [Fuel Sensor]",
      },
      {
        value: "3",
        label: "Capvel Modbus [Fuel Sensor]",
      },
      {
        value: "4",
        label: "Holykel Modbus [Fuel Sensor]",
      },
      {
        value: "5",
        label: "Analog (0-10V) Cylindrical [Fuel Sensor]",
      },
      {
        value: "6",
        label: "Holykel Modbus Cylindrical [Fuel Sensor]",
      },
      {
        value: "7",
        label: "Escort TD-150 [Fuel Sensor]",
      },
      {
        value: "8",
        label: "DLC1001 [Fuel Sensor]",
      },
      {
        value: "9",
        label: "Sozi (RS485) [Fuel Sensor]",
      },
      {
        value: "10",
        label: "SAM FUEL RS485 [Fuel Sensor]",
      },
      {
        value: "11",
        label: "ITALON RS485 [Fuel Sensor]",
      },
      {
        value: "12",
        label: "MECHATRONICS RS485 [Fuel Sensor]",
      },
    ],
  },
  client_asset_type: {
    1140: [18],
    1062: [18],
  },
  default_system_template: {
    67: 143,
  },
  derived_params: {
    // main:derived
    fuel_level: "fuel",
    fuel_volt: "fuel",
  },
  gps_devices: {
    1: ["9411", "9412"],
    60: ["9512", "9512G", "9513G"],
    51: true,
    53: true,
    54: true,
    55: true,
    56: true,
    58: true,
    59: true,
    61: true,
    62: true,
    63: true,
    64: true,
    65: true,
    66: true,
    67: true,
    68: true,
    70: true,
    78: true,
  },
  quick_add_machine_info: {
    18 : {
      commissioning_date: "*",
      capacity: [2120],
    },
    45: {
      type: "*"
    },
    103: {
      commissioning_date: "*",
      serial: "*"
    }
  },
};
export function openNotification(type, msg, placement = "bottomLeft") {
  if (window.innerWidth > 576) {
    AntNotification({
      type: type,
      message: msg,
      // description: 'This is success notification',
      placement: placement,
      className: "alert-" + type,
    });
  } else {
    AntMessage(type, msg);
  }
}

export function readCookie(key) {
  let result;
  return (result = new RegExp(
    "(?:^|; )" + encodeURIComponent(key) + "=([^;]*)",
  ).exec(document.cookie))
    ? result[1]
    : null;
}
export function getParameterSource(allParams, key) {
  let source = "";
  try {
    source = allParams.find((item) => item.destination_key == key).source;
  } catch (e) {
    source = "";
  }
  return source;
}

export function onQuickAddRadioChange(e) {
  this.setState({ device_config_tab: e, quick_add: e === "quick" }, () => {
    if (e === "quick") {
      this.initialiseIntersectionObserver(e === "quick");
    }
  });
}
export function goBackToList() {
  // this.props.history.goBack(-1);
  if (this.props.is_application_filter) {
    this.props.history.push(
      this.platform_slug +
        "/customer-management/" +
        this.props.customer_id +
        "/applications/" +
        this.state.application_id +
        "/things/list",
    );
  } else {
    this.props.history.push(this.platform_slug + "/things");
  }
}

export function getDevicevalues(device_config_values) {
  if (!device_config_values) return undefined;
  let returnArray = [],
    isZeroDeviceId = false;
  device_config_values.forEach((item) => {
    if (!isZeroDeviceId) {
      isZeroDeviceId = parseInt(item.devices) == 0;
    }
    returnArray.push(item.devices);
  });
  return isZeroDeviceId ? undefined : returnArray;
}
export async function changeDevice(newId) {
  let {
    activeTab,
    device_config_values,
    checked_params,
    template_details,
    system_details,
    device_type,
  } = this.state;
  let stateToUpdate = {};
  const thingTemplate = this.state.allFields.thingTemplate;
  let oldId = activeTab;
  if (parseInt(activeTab) === parseInt(oldId)) {
    activeTab = newId;
  }
  device_type[newId] = this.getDeviceType(newId);
  stateToUpdate["activeTab"] = activeTab;
  stateToUpdate["device_type"] = device_type;
  if (
    device_config_values &&
    Array.isArray(device_config_values) &&
    device_config_values.length
  ) {
    device_config_values = device_config_values.map((item) => {
      let returnObj = item;
      if (
        item /*&& item.devices && */ &&
        parseInt(item.devices) === parseInt(oldId)
      ) {
        returnObj["devices"] = newId;
      }
      return returnObj;
    });
  }
  stateToUpdate["device_config_values"] = device_config_values;

  if (system_details && system_details[oldId]) {
    let new_system_details = system_details;
    new_system_details[newId] = system_details[oldId];
    delete new_system_details[oldId];
    stateToUpdate["system_details"] = new_system_details;
  }
  this.setState(stateToUpdate, () => {
    const finalSystemTemp = this.state.allFields?.system_template;
    if (this.state.isThingAdd || !newId || parseInt(oldId)) return;

    if (thingTemplate && thingTemplate !== "" && thingTemplate !== "custom") {
      this.renderThingTemplate(thingTemplate);
    } else if (
      finalSystemTemp?.length &&
      !finalSystemTemp.includes("template-custom")
    ) {
      this.onQuickAddValuesChange(finalSystemTemp, "system_template", "select");
    }
  });
}
export function handleChangeDeviceForm(deviceId) {
  let oldData = this.state.device_config_values;
  let quick_add = this.state.quick_add;
  let device_type = {},
    that = this;
  let devices = [],
    deviceIds = [deviceId],
    thingTemplate,
    category = this.state.allFields.thingType;
  thingTemplate = this.state.allFields.thingTemplate;
  let checked_params = this.state.checked_params;
  let template_details = this.state.template_details;
  let oldDeviceId;
  if (
    checked_params &&
    Object.keys(checked_params) &&
    Object.keys(checked_params).length
  ) {
    oldDeviceId = Object.keys(checked_params)[0];
  }
  let client_id = this.state.allFields.customerName;
  deviceIds.forEach((item) => {
    devices.push({ devices: item });
  });
  let updated_checked_params = {};
  let stateUpdate = {
    device_config_values: devices,
    checked_params: {},
    device_type,
    activeTab: deviceId,
  };

  //when current device is removed
  if (!deviceId) {
    this.changeDevice(0);
    return;
  }
  // if (!this.state.isThingAdd) {
    let oldDevice;
    if (oldData && oldData[0] && parseInt(oldData[0].devices) >= 0) {
      oldDevice = parseInt(oldData[0].devices);
    }
    //when device is changed
    // case1: from zero config to device
    // case2: device to device
    if (
      parseInt(oldDevice) >= 0 &&
      deviceId &&
      parseInt(oldDevice) !== parseInt(deviceId)
    ) {
      this.changeDevice(deviceId);
      return;
    }
  // }

  const onDeviceChange = () => {
    deviceIds.forEach((eachDeviceId) => {
      device_type[eachDeviceId] = this.getDeviceType(eachDeviceId);
    });
    console.log({ device_type });
    stateUpdate["device_type"] = device_type;
    stateUpdate["is_gps_available"] = this.getDeviceGpsStatus(deviceId);
    stateUpdate["checked_params"] = updated_checked_params;
    let all_analyser_params = getAllCheckedParams(updated_checked_params);
    stateUpdate["all_analyser_params"] = all_analyser_params;
    // if (
    // 	that.state.device_config_values &&
    // 	deviceId &&
    // 	checked_params &&
    // 	checked_params[oldDeviceId]
    // ) {
    // 	stateUpdate['checked_params'] = {
    // 		[deviceId]: checked_params[oldDeviceId],
    // 	};
    // }
    that.setState(stateUpdate, async () => {
      if (
        thingTemplate &&
        thingTemplate !== "" &&
        thingTemplate !== "custom" &&
        (!oldData || oldData.length == 0) &&
        deviceId
      ) {
        that.renderThingTemplate(thingTemplate);
      } else if (thingTemplate && thingTemplate === "custom") {
        let vendorId = that.state.customers_list.find(
          (item) =>
            parseInt(item.id) === parseInt(that.state.allFields.customerName),
        )?.vendor_id;
        let parent_vendor_id = this.state.parent_vendor_id;
        if (!isNaN(parseInt(parent_vendor_id)) && parent_vendor_id !== 1) {
          vendorId = parent_vendor_id;
        }

        if (isNaN(parseInt(vendorId)) && this.props.vendor_id !== 1) {
          vendorId = this.props.vendor_id;
        }

        if (Object.keys(defaultParams).indexOf(`${vendorId}`) > -1) {
          let default_params = {
            general: [],
            fault: [],
          };
          default_params.general =
            that.state.all_params.general_parameters.filter(
              (item) =>
                defaultParams[vendorId].general.indexOf(item.destination_key) >=
                0,
            );
          default_params.fault = that.state.all_params.fault_parameters.filter(
            (item) =>
              defaultParams[vendorId].fault.indexOf(item.destination_key) >= 0,
          );
          let checked_params = {
            [deviceId]: default_params,
          };
          that.setState({ checked_params });
        }
      }

      if (CONSTANTS.default_system_template[category]) {
        this.onQuickAddValuesChange(
          ["template-" + CONSTANTS.default_system_template[category]],
          "system_template",
          "select",
        );
      } else if (this.state.quick_system_template_options.length === 0) {
        // this.onQuickAddValuesChange(
        //     ['template-custom'],
        //     'system_template',
        //     'select'
        // )
      }
      if (!quick_add) {
        let deviceLink = await deviceLinkedtoThing(client_id, deviceId);
        if (deviceLink.status === "success") {
          this.setState({ linked_device: deviceLink.is_linked });
        }
      }
    });
  };
  if (deviceId) {
    onDeviceChange();
  } else {
    this.setState({ device_config_values: undefined });
  }
}

export function getFieldValue(e, type) {
  let val = "";
  if (type === "input") {
    val = e.currentTarget ? e.currentTarget.value : e;
  } else if (type === "file") {
    val = e.target.files;
  } else if (type == "date") {
    val = e.unix();
  } else if (type === "radio") {
    val = e.target.value;
  } else if (type === "select" || type === "switch") {
    val = e;
  }
  return val;
}
export async function resetData() {
  let stateToUpdate = {},
    { allFields, show_quick_add } = this.state;
  allFields["system_template"] = undefined;
  stateToUpdate["system_details"] = {};
  stateToUpdate["checked_params"] = {};
  stateToUpdate["system_params"] = {};
  stateToUpdate["machine_info_template"] = null;
  stateToUpdate["show_quick_add"] = show_quick_add;
  show_quick_add.system = true;
  stateToUpdate["allFields"] = allFields;
  this.setState(stateToUpdate);
}
export function setFuelSensorType(is_fuel_sensor, fuel_sensor_type) {
  let { machine_info_json, machine_info_values, thing_details } = this.state;
  if (!machine_info_json["advanced"]) return;
  const is_calibrated = thing_details?.thing_details?.fuel_calib?.id;
  let advancedJson = JSON.parse(machine_info_json["advanced"]);
  let form_state = {
    fuel_sensor_type:
      is_fuel_sensor && fuel_sensor_type ? fuel_sensor_type : "",
    disable_fuel_sensor_type: is_fuel_sensor && fuel_sensor_type,
    show_calibration:
      (is_fuel_sensor && fuel_sensor_type && fuel_sensor_type === "pressure") ||
      fuel_sensor_type === "capacitive",
  };

  // if calibrated from new don't show calibration options
  if (is_calibrated) {
    form_state["fuel_sensor_type"] = "none";
    form_state["show_calibration"] = false;
  }
  advancedJson.Form.state = {
    ...advancedJson.Form.state,
    ...form_state,
  };
  if (machine_info_values["advanced"]) {
    machine_info_values["advanced"]["fuel_sensor_type"] = fuel_sensor_type;
  }
  machine_info_json["advanced"] = JSON.stringify(advancedJson);
  this.setState({ machine_info_values, machine_info_json });

  // if form is already mounted then change form state
  if (
    this.advancedFormRef &&
    this.advancedFormRef.current &&
    this.advancedFormRef.current.setState
  ) {
    console.log(this.advancedFormRef.current);
    this.advancedFormRef.current.setState(form_state);

    //setFieldsValue
    if (
      this.advancedFormRef.current.formRef &&
      this.advancedFormRef.current.formRef.current &&
      this.advancedFormRef.current.formRef.current.setFieldsValue
    ) {
      this.advancedFormRef.current.formRef.current.setFieldsValue({
        fuel_sensor_type: form_state.fuel_sensor_type,
      });
    }
  }
}

export async function getResellersList(fetchList = true) {
  if (!fetchList)
    return {
      resellersList: this.state.resellersList,
      customerTypes: this.state.customerTypes,
    };
  let resellersList, customerTypes;
  if (this.isDealerManagement) {
    [resellersList, customerTypes] = await Promise.all([
      getVendors(this.props.client_id),
      this.getCustomerTypes(),
    ]);
  }
  return { resellersList, customerTypes };
}

export async function onQuickAddValuesChange(e, name, type, options) {
  let end_customer =
      parseInt(this.props.application_id) !== 12 &&
      parseInt(this.props.application_id) !== 17,
    application_id = parseInt(this.props.application_id);
  let val = getFieldValue(e, type),
    locationParams = [],
    applications = [],
    vendorname_options = [],
    {
      allFields,
      sections,
      active_section,
      param_settings,
      is_rental,
      isThingAdd,
    } = this.state;
  let oldFields = { ...allFields };
  allFields[name] = val;
  let stateToUpdate = {};
  if (name === "customerName") {
    let thingTypes = [],
      app_id_thing_type = {},
      vendor_id;
    if (isNaN(parseInt(val))) {
      allFields["new_customer"] = val;
      applications = [16];

      // customer deselected
      stateToUpdate["assetWithoutCustomer"] = true;
    } else {
      allFields["new_customer"] = undefined;

      // valid customer selected
      stateToUpdate["assetWithoutCustomer"] = false;
    }
    // stateToUpdate['loading']=true
  } else if (name === "new_customer") {
    if (!val || val.length < 2) {
      allFields["customerName"] = undefined;
      allFields["new_customer"] = undefined;
      stateToUpdate["customer_search"] = undefined;
    }
  } else if (name === "thingType") {
    allFields["system_template"] = undefined;
    stateToUpdate["system_details"] = {};
    stateToUpdate["checked_params"] = {};
    stateToUpdate["system_params"] = {};
    stateToUpdate["advanced_device_config"] = false;
    sections = this.getModifiedSections(val);
    if (this.state.is_rental) {
      allFields["vendorName"] = this.props.client_id;
    }
  }
  stateToUpdate["allFields"] = allFields;
  stateToUpdate["sections"] = sections;
  this.setState(stateToUpdate, async () => {
    this.setActiveSection("basic");
    let { vendor_id, allFields } = this.state;
    if (name === "thingType") {
      let applicationId = this.state.app_id_thing_type[val];
      let params = {},
        allParams = [],
        locationParams = [];
      allParams = this.state.things_categories.find(
        (item) => parseInt(item.id) === parseInt(val),
      ).parameter_details;
      let all_param_details = {};
      allParams.forEach((param) => {
        all_param_details[param.destination_key] = param;
      });
      params["general_parameters"] = allParams.filter(
        (item) =>
          item.type !== "fault" &&
          item.source !== "derived_optional" &&
          item.destination_key !== "lat" &&
          item.destination_key !== "long",
      );
      locationParams = allParams.filter(
        (item) =>
          item.destination_key == "lat" || item.destination_key == "long",
      );
      if (locationParams.length) {
        // params['general_parameters'].push(location_param);
      }
      params["fault_parameters"] = allParams.filter(
        (item) => item.type === "fault" && item.source !== "derived_optional",
      );
      params["derived_parameters"] = allParams.filter(
        (item) => item.source === "derived_optional",
      );
      this.setState(
        { all_params: params, locationParams, all_param_details },
        () => {
          // for a rental thing customer shouldn't be selected even if there is only single customer
          if (
            (!isThingAdd || (isThingAdd && !is_rental)) &&
            Array.isArray(this.state.customers_list) &&
            this.state.customers_list.length === 1 &&
            !this.isCustomerOptional
          ) {
            this.onQuickAddValuesChange(
              this.state.customers_list[0]?.id,
              "customerName",
              "select",
            );
          } else {
            this.getAPIData();
          }
        },
      );
    } else if (name === "customerName") {
      let customerStateUpdates = {};
      const vendorname_options = [];
      if (this.props.client_id === 1) {
        vendorname_options.push(CONSTANTS.PHOENIX_VENDOR);
      } else {
        vendorname_options.push({
          id: this.props.client_id,
          name: this.props.client_name,
        });
      }

      let selectedCusomer =
        (await this.getCustomerdetails(
          isNaN(parseInt(val)) ? vendorname_options[0].id : val,
        )) || {};
      console.log("allowclear", selectedCusomer, val);
      let is_rental =
        selectedCusomer &&
        Array.isArray(selectedCusomer.customer_type) &&
        selectedCusomer.customer_type.includes(4);

      if (isNaN(parseInt(val))) {
        if (this.state.resellersList?.length) {
          this.state.resellersList.forEach((res) => {
            vendorname_options.push({
              id: res.id,
              customName: (
                <div>
                  {res.name}{" "}
                  <i
                    style={{
                      display: "inline-block",
                      marginLeft: 6,
                      color: "#808080",
                    }}
                  >
                    (
                    {res.customer_type?.[0] &&
                    this.state.customerTypes?.[res.customer_type[0]]
                      ? this.state.customerTypes?.[res.customer_type[0]]
                      : "reseller"}
                    )
                  </i>
                </div>
              ),
              name: res.name,
            });
          });
        }
        customerStateUpdates["loading"] = false;
        customerStateUpdates["is_rental"] = is_rental;
        customerStateUpdates["vendor_id"] = vendorname_options[0].id;
        customerStateUpdates["vendorname_options"] = vendorname_options;
        customerStateUpdates["vendor_list"] = [...vendorname_options];
        allFields["vendorName"] = vendorname_options[0].id;
        allFields["customerName"] = vendorname_options[0].id;
        this.setState(customerStateUpdates, () => {
          this.getAPIData();
        });
        return;
      }

      let vendor_id = selectedCusomer?.vendor_id || 0;
      customerStateUpdates["loading"] = false;
      customerStateUpdates["is_rental"] = is_rental;
      customerStateUpdates["vendor_id"] = vendor_id;

      // get all vendor details of possible vendor ids
      // vendor_id -> datoms-x, client id -> for iot and end customer
      let vendor_ids = [vendor_id, parseInt(this.props.client_id)].filter(
        (item) => item && item !== 1,
      );
      vendor_ids = vendor_ids.filter(
        (item, index) => vendor_ids.indexOf(item) === index,
      );

      let vendor_list = [];
      if (
        !(vendor_ids.includes(this.props.client_id) && vendor_ids.length === 1)
      ) {
        vendor_list = await getVendors(this.props.client_id, vendor_ids);
      }

      vendor_list.forEach((vendor) => {
        vendorname_options.push({
          id: vendor.id,
          name: vendor.name,
        });
      });
      customerStateUpdates["vendorname_options"] = vendorname_options;
      customerStateUpdates["vendor_list"] = vendor_list;
      if (parseInt(vendor_id) > 0) {
        allFields["vendorName"] = vendor_id;
      }
      if (vendorname_options.length === 1 && application_id == 12) {
        allFields["vendorName"] = vendorname_options[0].id;
      }
      // customerStateUpdates['territoryData'] = territoryData;
      // customerStateUpdates['territoryNames'] = territoryNames;
      customerStateUpdates["allFields"] = allFields;
      this.setState(customerStateUpdates, () => {
        this.getAPIData();
      });
    } else if (name === "vendorName") {
      let customer = this.state.allFields.customerName;
      let app_id_thing_type = this.state.app_id_thing_type;
      if (!app_id_thing_type) {
        app_id_thing_type = {};
      }
      if (this.state.is_rental || end_customer) {
        customer = this.props.client_id;
      }
      let thingType = this.state.allFields.thingType;
      let applicationId = this.state.app_id_thing_type[thingType] || 16;
      // this.showLoading();
      if (this.state.assetWithoutCustomer) {
        customer = val;
        flushSync(() => {
          this.setState({ allFields: { ...allFields, customerName: val } });
        });
      }
      await this.getCustomerdetails();
      let promiseResult = await Promise.all([
        this.getDeviceList(customer, applicationId, parseInt(val)),
        this.getTemplates(parseInt(thingType), parseInt(val)),
      ]);
      // this.hideLoading();
    } else if (name === "system_template") {
      this.setActiveSection("device_config");
      let thingType = this.state.allFields.thingType,
        isCustom = false;
      let device,
        system_details,
        quick_add = this.state.quick_add;
      if (
        this.state.device_config_values &&
        Array.isArray(this.state.device_config_values) &&
        this.state.device_config_values[0] &&
        parseInt(this.state.device_config_values[0].devices) >= 0
      ) {
        device = this.state.device_config_values[0].devices;
      }

      // if device is not available then get device id from active device
      if (!device && this.state.activeTab) {
        device = this.state.activeTab;
      }

      // device can be 0 or positive number
      // possible values
      // undefined -> true
      // 0 -> true
      // 11313 ->true
      if (typeof device === "undefined") {
        openNotification("error", "Please Select a Device");
        allFields["system_template"] = undefined;
        this.setState({ allFields });
        return;
      }
      // if (val.length>2 || (val.length>1 && val.every(item=>item.split('-')[0]==val[0].split('-')[0]))){
      //     openNotification('error','You can only select one DG set template and one Fuel Sensor template at max')
      //     allFields['system_template']=oldFields['system_template']
      //     this.setState({allFields})
      //     return;
      // }
      if (Array.isArray(val)) {
        if (!system_details) {
          system_details = {};
        }
        if (!system_details[device]) {
          system_details[device] = {};
        }
        for await (const sys_temp of val) {
          let index = val.indexOf(sys_temp);
          let type = sys_temp.split("-")[0];
          let template = sys_temp.split("-")[1];
          if (!system_details[device][index + 1]) {
            system_details[device][index + 1] = {};
          }
          if (!system_details[device][index + 1]["system_config"]) {
            system_details[device][index + 1]["system_config"] = {
              sampling_interval: this.checkPollutionApplication() ? 45 : 60,
            };
          }
          if (type == "template") {
            let template_params = {
                general: [],
                fault: [],
              },
              current_template,
              priority = 0,
              current_system_params = { ...template_params },
              param_values = { general: {}, fault: {} };
            if (parseInt(template) > 0) {
              let templateAPIdata = await this.getProtocolDetails(
                template,
                "template",
              );
              if (templateAPIdata.status !== "success") {
                if (this.props.hideLoading) {
                  this.props.hideLoading();
                }
                // this.setState({ loading_modal: false });
                return;
              }
              current_template = JSON.parse(
                JSON.stringify(templateAPIdata.template_details),
              );
              if (
                current_template &&
                current_template.param_values &&
                current_template.param_values.general &&
                Array.isArray(
                  Object.keys(current_template.param_values.general),
                )
              ) {
                template_params.general = Object.keys(
                  current_template.param_values.general,
                );
                param_values.general = current_template.param_values.general;
              }
              if (
                current_template &&
                current_template.param_values &&
                current_template.param_values.fault &&
                Array.isArray(Object.keys(current_template.param_values.fault))
              ) {
                template_params.fault = Object.keys(
                  current_template.param_values.fault,
                );
                param_values.fault = current_template.param_values.fault;
              }
              if (
                current_template &&
                typeof current_template.priority !== "undefined"
              ) {
                priority = parseInt(current_template.priority);
              }

              // set param settings
              if (
                current_template &&
                current_template.param_settings &&
                typeof current_template.param_settings == "object"
              ) {
                Object.entries(current_template.param_settings).forEach(
                  ([key, data]) => {
                    if (typeof data === "object" && Object.keys(data)?.length) {
                      if (!param_settings) {
                        param_settings = {};
                      }
                      if (!param_settings[key]) {
                        param_settings[key] = {};
                      }
                      param_settings[key] = { ...param_settings[key], ...data };
                    }
                  },
                );
              }

              // set fuel sensor type
              this.setFuelSensorType(
                current_template.is_fuel_sensor,
                current_template.fuel_sensor_type,
              );
            }
            if (template == "custom") {
              template_params = {
                general: this.state.all_params.general_parameters.map(
                  (item) => item.destination_key,
                ),
                fault: this.state.all_params.fault_parameters.map(
                  (item) => item.destination_key,
                ),
              };
              if (!isCustom) {
                isCustom = true;
              }
            }

            //get all params assigned to all systems except current
            let remain_system = JSON.parse(JSON.stringify(system_details));
            delete remain_system[index + 1];
            let { all_general_params, all_fault_params, priorities } =
              getAllParamsFromAllSystems(remain_system, {
                general: [],
                fault: [],
              });
            let max_priority = Math.max(...Object.values(priorities));

            if (
              template_params.general.find(
                (item) => item == "lat" || item == "long",
              )
            ) {
              template_params.general = template_params.general.filter(
                (item) => item !== "lat" || item !== "long",
              );
              // template_params.general.push(location_param.destination_key);
            }
            current_system_params.general =
              this.state.all_params.general_parameters.filter(
                (item) =>
                  template_params.general.indexOf(item.destination_key) >= 0 &&
                  (all_general_params.indexOf(item.destination_key) === -1 ||
                    priority > max_priority),
              );
            current_system_params.fault =
              this.state.all_params.fault_parameters.filter(
                (item) =>
                  template_params.fault.indexOf(item.destination_key) >= 0 &&
                  (all_fault_params.indexOf(item.destination_key) === -1 ||
                    priority > max_priority),
              );
            console.log({ current_system_params });
            system_details[device][index + 1]["assigned"] =
              current_system_params;
            system_details[device][index + 1]["param_values"] = param_values;

            // set protocol_config from template
            if (current_template?.protocol_config?.protocol) {
              system_details[device][index + 1]["protocol_config"] =
                current_template.protocol_config;
            }
            // set hardware_interface from template
            if (current_template?.hardware_interface?.hardware_interface) {
              system_details[device][index + 1]["hardware_interface"] =
                current_template.hardware_interface;
            }

            //other system config details
            let template_sys_config_details = {};
            if (
              current_template?.system_config &&
              typeof current_template?.system_config === "object"
            ) {
              template_sys_config_details = current_template?.system_config;
            }
            system_details[device][index + 1]["system_config"] = {
              ...system_details[device]["1"]["system_config"],
              ...template_sys_config_details,
              template_id: template,
            };
            system_details[device][index + 1].priority = priority;
            // for modifing system details with priority
            system_details[device] = getModifiedSystemDetailswithTemplate(
              system_details[device],
              current_system_params,
            );
          }
          if (type == "fuel") {
            system_details[device]["1"]["system_config"]["fuel_sensor_name"] =
              template;
          }
        }
      }
      this.setState(
        { allFields: { ...allFields, system_template: val, param_settings } },
        () => {},
      );
      let update_checked_params = options?.update_checked_params;
      this.setSystemDetails(
        system_details[device],
        true,
        typeof update_checked_params !== "undefined"
          ? update_checked_params
          : isCustom
            ? false
            : true,
        param_settings,
      );
    } else if (name === "thingTemplate") {
      let { thing_templates } = this.state;
      let machine_info_templates = this.state.mahchineinfo_template_data;
      this.renderThingTemplate(val);
    }
  });
}
export function getModifiedSystemDetailswithTemplate(
  system_data,
  current_system_params,
) {
  let system_details = system_data,
    param_types = ["general", "fault"],
    max_priority_params = { general: [], fault: [] };
  // delete system params from other systems if this system has higher priority
  let priorityData = getAllParamsFromAllSystems(system_details, {
    general: [],
    fault: [],
  });
  let max_prio = Math.max(...Object.values(priorityData.priorities));
  Object.entries(system_details).forEach(([sys_key, sys_detail]) => {
    if (typeof sys_detail.priority === "undefined") {
      sys_detail.priority = 0;
    }
    if ([max_prio].includes(sys_detail.priority)) {
      param_types.forEach((param_type) => {
        if (!max_priority_params[param_type]) {
          max_priority_params[param_type] = [];
        }
        if (
          system_details[sys_key]["assigned"] &&
          Array.isArray(system_details[sys_key]["assigned"][param_type])
        ) {
          max_priority_params[param_type] = [
            ...max_priority_params[param_type],
            ...system_details[sys_key]["assigned"][param_type].map(
              (item) => item.destination_key,
            ),
          ];
        }
      });
      return;
    }
  });
  Object.entries(system_details).forEach(([sys_key, sys_detail]) => {
    if (typeof sys_detail.priority === "undefined") {
      sys_detail.priority = 0;
    }
    // remove param values of param if param is not in assigned section of system
    param_types.forEach((param_type) => {
      if (
        system_details[sys_key]["assigned"] &&
        system_details[sys_key]["assigned"][param_type]
      ) {
        let all_params = system_details[sys_key]["assigned"][param_type].map(
          (item) => item.destination_key,
        );
        if (
          system_details[sys_key]["param_values"] &&
          system_details[sys_key]["param_values"][param_type]
        ) {
          Object.keys(
            system_details[sys_key]["param_values"][param_type],
          ).forEach((param_key) => {
            if (
              all_params.indexOf(param_key) === -1 &&
              system_details[sys_key]["param_values"][param_type][param_key]
            ) {
              delete system_details[sys_key]["param_values"][param_type][
                param_key
              ];
            }
          });
        }
      }
    });

    if ([max_prio].includes(sys_detail.priority)) return;

    // delete param froom other system if same param is added in max priority system
    if (sys_detail.priority < max_prio) {
      console.log("mani-system-bug2", max_priority_params);
      param_types.forEach((param_type) => {
        current_system_params[param_type].forEach((item) => {
          if (max_priority_params[param_type].includes(item.destination_key)) {
            system_details[sys_key]["assigned"][param_type] = system_details[
              sys_key
            ]["assigned"][param_type].filter(
              (parm) => parm.destination_key !== item.destination_key,
            );
            delete system_details[sys_key]["param_values"][param_type][
              item.destination_key
            ];
            // console.log(item)
          }
        });
      });
    }
  });
  return system_details;
}

export function setAnalyserDetails(details) {
  let { calibration, calibration_params } = this.state,
    statetoUpdate = { analyser_details: details },
    all_selected_analser_params = ["zero"];

  // for filtering calibration details if param is removed from analyser
  if (details && Array.isArray(details)) {
    details.forEach((detail) => {
      if (detail && detail.params && Array.isArray(detail.params)) {
        all_selected_analser_params = [
          ...all_selected_analser_params,
          ...detail.params,
        ];
      }
    });
  }
  let updated_calibration = {};
  if (calibration) {
    Object.keys(calibration).forEach((key) => {
      if (all_selected_analser_params.includes(key)) {
        updated_calibration[key] = calibration[key];
      }
    });
  }

  if (Object.keys(updated_calibration).length === 1) {
    updated_calibration = {};
  }
  calibration_params = calibration_params.filter((param) =>
    all_selected_analser_params.includes(param),
  );
  statetoUpdate["calibration"] = updated_calibration;
  statetoUpdate["calibration_params"] = calibration_params;
  this.setState(statetoUpdate, () => {
    this.setActiveSection("asset_info");
    console.log("anala", this.state.analyser_details);
  });
}
export async function setCalibrationDetails(e, name, type, param) {
  let { calibration } = this.state,
    fileUploadResponse;
  let val = getFieldValue(e, type);
  console.log(val, typeof val, Array.isArray(val));
  if (
    name == "check_duration_sec" &&
    (parseInt(val) < 0 || parseInt(val) > 59)
  ) {
    openNotification("error", "Please Enter Value Between 0 and 60");
    return;
  }
  if (!calibration[param]) {
    calibration[param] = {};
  }
  if (!calibration[param][name]) {
    calibration[param][name] = {};
  }
  if (name === "cyl_cert" || name === "docs") {
    let fileArray = [];
    for await (const file of val) {
      console.log(file);
      let isFileValid = await validateFileType(file, [
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/msword",
        "application/pdf",
      ]);
      if (isFileValid) {
        // fileArray.push({name:file.name,link:'https://s3.ap-south-1.amazonaws.com/datoms-files-storage/datoms/device-management/firmwares/image/upload-XCX6IktEh4.svg'})
        // continue;
        if (!fileUploadResponse) {
          fileUploadResponse = await uploadFile();
        }
        if (fileUploadResponse.status === "success") {
          let uploadedResonse = await uploadFiletoS3(fileUploadResponse, file);
          if (uploadedResonse && uploadedResonse.link) {
            fileArray.push(uploadedResonse);
          }
        } else if (fileUploadResponse.message) {
          openNotification("error", fileUploadResponse.message);
        }
      }
    }
    if (name === "cyl_cert") {
      if (fileArray[0]) {
        val = fileArray[0];
      } else {
        val = {};
      }
    } else {
      val = [];
      if (Array.isArray(calibration[param][name])) {
        val = [...calibration[param][name]];
      }
      if (Array.isArray(fileArray)) {
        val = [...val, ...fileArray];
      }
    }
  }
  console.log(val);
  calibration[param][name] = val;
  this.setState({ calibration }, () => {
    this.setActiveSection("calibration");
  });
}
export async function getAPIData() {
  let end_customer =
      parseInt(this.props.application_id) !== 12 &&
      parseInt(this.props.application_id) !== 17,
    application_id = parseInt(this.props.application_id);
  let { allFields, device_config_tab, app_id_thing_type } = this.state;
  if (!app_id_thing_type) {
    app_id_thing_type = {};
  }
  let customer = this.state.allFields.customerName;
  if ((this.state.is_rental && application_id === 17) || end_customer) {
    customer = this.props.client_id;
  }
  let thingType = this.state.allFields.thingType;
  let applicationId = app_id_thing_type[thingType] || 16;
  if (
    [12, 17].includes(application_id) &&
    (!customer || !applicationId || !thingType)
  )
    return;
  // this.showLoading();
  let promiseResult = await Promise.all([
    this.getThingsCategoryDetails(thingType, allFields),
    this.getDeviceList(customer, applicationId),
    this.getTemplates(parseInt(thingType)),
    this.getSystemTemplates(customer, applicationId, thingType),
  ]);
  // this.hideLoading();
  if (device_config_tab === "advanced") {
    this.initialiseIntersectionObserver();
  }
}
function isCustomerAllowed(customer, app_id) {
  if (app_id !== 12) {
    return true;
  }
  if (customer.is_vendor && customer.vendor_id !== 1) {
    return false;
  }
  if (!customer.is_vendor && customer.parent_vendor_id) {
    return false;
  }
  return true;
}
export async function getVendors(client_id, vendors) {
  // http://localhost:3000/-/api/iot-platform/v1.1.0/vendors/1/customers/list?results_per_page=20&page_no=1&vendor_only=true&vendors=[1,3317]
  let vendor_details = [];
  let query = "";
  if (vendors?.length) {
    query = `?results_per_page=${client_id === 1 ? "1" : "20"}&page_no=1&vendor_only=true&clients=${vendors}`;
  } else {
    query += `?lite=true&vendor_only=true`;
  }
  const customersList = await retriveCustomerList(client_id, query);
  if (customersList.status === "success") {
    vendor_details = customersList.customers;
  } else if (customersList.message) {
    openNotification("error", customersList.message);
  }
  return vendor_details;
}
export async function getCustomers(append, isScroll) {
  // fetch next page data on only scroll to end
  this.customerPage = isScroll ? this.customerPage + 1 : 1;
  let { customers_list, search_customers_list, customer_search } = this.state,
    valid_customer_list = [],
    is_customer_search = customer_search && customer_search !== "";
  if (!customers_list) {
    customers_list = [];
  }
  let query = `?results_per_page=20&page_no=${this.customerPage}`;
  if (is_customer_search) {
    query = query + `&search=${encodeURIComponent(customer_search)}`;
  }
  const customersList = await retriveCustomerList(this.props.client_id, query);
  if (customersList.status === "success") {
    customersList.customers.forEach((customer) => {
      // customer validation
      if (
        isCustomerValid(customer.status) &&
        isCustomerAllowed(customer, parseInt(this.props.application_id))
      ) {
        // End customer or Rental Partner
        if (
          !customer.is_vendor ||
          (customer.is_vendor &&
            Array.isArray(customer.customer_type) &&
            customer.customer_type.includes(4))
        ) {
          valid_customer_list.push(customer);
        }
      }
    });
    if (append) {
      if (is_customer_search) {
        if (isScroll) {
          valid_customer_list = [
            ...search_customers_list,
            ...valid_customer_list,
          ];
        }
        this.setState({
          search_customers_list: valid_customer_list,
          customer_list_loading: false,
        });
        return;
      }
      valid_customer_list = [...customers_list, ...valid_customer_list];
      this.setState({
        search_customers_list: [],
        customers_list: valid_customer_list,
        customer_list_loading: false,
      });
    }
  } else if (customersList.message) {
    openNotification("error", customersList.message);
  }
  return valid_customer_list;
}
export async function getCustomersList() {
  let vendorname_options = [];
  if (this.props.application_id !== 12 && this.props.application_id !== 17) {
    let customer_list_options = [
      {
        compo: "Option",
        props: {
          value: this.props.client_id,
        },
        child: this.props.client_name,
      },
    ];

    let vendor_list_options = [
      {
        id: this.props.vendor_id,
        name: this.props.vendor_name,
      },
    ];
    this.setState({
      customername_options: customer_list_options,
      vendorname_options: vendor_list_options,
      customers_list: [
        {
          id: this.props.client_id,
          name: this.props.client_name,
          applications: [this.props.application_id],
          vendor_id: this.props.vendor_id,
        },
      ],
      vendor_id: this.props.vendor_id,
      vendor_list: [
        {
          id: this.props.vendor_id,
          name: this.props.vendor_name,
        },
      ],
    });
    return "end_customer";
  }

  let customers_list = await this.getCustomers();
  let stateToUpdate = { customers_list, customer_list_loading: false };
  this.setState(stateToUpdate);
  return stateToUpdate;
}
//to get customer details
export async function getCustomerdetails(
  client_id,
  fetchResellersList = false,
) {
  let { application_id } = this.props,
    customer = client_id,
    { customer_details, isThingAdd } = this.state;
  if (!customer) {
    customer = this.state.allFields.customerName;
  }
  console.log(
    "customersSelected",
    this.state.allFields.customerName,
    this.state.vendor_id,
  );
  if (!customer) {
    customer = this.state.vendor_id;
  }
  // if(parseInt(client_id)===12) return;
  let customerDetails = {},
    resellersList = this.state.resellersList,
    customerTypes = this.state.customerTypes,
    resellersResponse = {};
  if (customer_details && customer_details[customer]) {
    customerDetails = customer_details[customer];
  } else {
    [customerDetails, resellersResponse] = await Promise.all([
      retriveCustomerDetails(customer),
      this.getResellersList(fetchResellersList ? true : false),
    ]);
    resellersList = resellersResponse.resellersList;
    customerTypes = resellersResponse.customerTypes;
  }
  moment.tz.setDefault(customerDetails.client_time_zone || "Asia/Calcutta");
  if (customerDetails.status === "success") {
    customer_details[customer] = customerDetails;
    if (customerDetails) {
      let stateToUpdate = {
        allowed_categories: customerDetails.allowed_thing_categories,
        parent_vendor_id: customerDetails.parent_vendor_id,
        client_time_zone: customerDetails.client_time_zone,
        customer_details,
      };
      if (this.isCustomerOptional || !isThingAdd) {
        const vendorNameOptions = [];
        if (this.props.client_id === 1) {
          vendorNameOptions.push(CONSTANTS.PHOENIX_VENDOR);
        } else if (this.props.client_id !== customerDetails.vendor_id) {
          vendorNameOptions.push({
            id: this.props.client_id,
            name: this.props.client_name,
          });
        }
        if (customerDetails.vendor_id !== 1) {
          vendorNameOptions.push({
            id: customerDetails.vendor_id,
            name: customerDetails.vendor_name || "",
          });
        }
        stateToUpdate["vendorname_options"] = vendorNameOptions;
      }

      console.log("CustomerTypes ==> ", customerTypes);
      if (stateToUpdate["vendorname_options"] && resellersList?.length) {
        resellersList.forEach((res) => {
          console.log("CustomerTypes ==> ", res.customer_type);
          stateToUpdate["vendorname_options"].push({
            id: res.id,
            customName: (
              <div>
                {res.name}{" "}
                <i
                  style={{
                    display: "inline-block",
                    marginLeft: 6,
                    color: "#808080",
                  }}
                >
                  (
                  {res.customer_type?.[0] &&
                  customerTypes?.[res.customer_type[0]]
                    ? customerTypes?.[res.customer_type[0]]
                    : "reseller"}
                  )
                </i>
              </div>
            ),
            name: res.name,
          });
        });
      }

      stateToUpdate.resellersList = resellersList;

      if (!isThingAdd) {
        stateToUpdate["customer_list_loading"] = false;
        stateToUpdate["customers_list"] = [
          {
            id: parseInt(customer),
            name: customerDetails.customer_name,
            is_vendor:
              Array.isArray(customerDetails.customer_type) &&
              !customerDetails.customer_type.includes(5),
          },
        ];
        return stateToUpdate;
      }
      this.setState(stateToUpdate, () => {
        this.setThingTypeOptions(true);
      });
      return customerDetails;
    }
  } else if (customerDetails.message) {
    this.openNotification("error", customerDetails.message);
  }
}

//function to get selected parameters for custom template
export function getSelectedParams(details, allParams) {
  let checked_params = {},
    extra_params = {},
    system_details = {},
    param_config = {},
    param_settings = {},
    locationParams = [],
    fuel_sensor_name = 0;
  details.configurations.forEach((config, index) => {
    let backend_params = [],
      backend_params2 = [];
    if (!Array.isArray(config.parameters)) {
      config.parameters = [];
    }
    config.parameters.forEach((configParam) => {
      let found_param = allParams.find(
        (item) => item.destination_key === configParam.destination_key,
      );
      if (!found_param) {
        try {
          extra_params[config.source_id].push(configParam);
        } catch (e) {
          extra_params = {
            ...extra_params,
            [config.source_id]: [configParam],
          };
        }
        return;
      }
      // if (!system_details[config.source_id]) {
      // 	system_details[config.source_id] = {};
      // }
      // if (!system_details[config.source_id]['1']){
      // 	system_details[config.source_id]['1']={}
      // }
      // param_config = {};
      // this.param_config_props.forEach((key) => {
      // 	if (configParam[key]) {
      // 		param_config[key] = configParam[key];
      // 	}
      // });
      // system_details[config.source_id] = {
      // 	...system_details[config.source_id],
      // 	[configParam.destination_key]: {
      // 		...param_config,
      // 	},
      // };
      if (
        typeof configParam["is_active"] == "undefined" ||
        configParam["is_active"]
      ) {
        backend_params.push(found_param);
      }
    });
    backend_params = backend_params.filter(
      (item) => item && item.destination_key,
    );
    if (!checked_params) {
      checked_params = {
        general: [],
        fault: [],
      };
    }
    if (!checked_params["general"]) {
      checked_params["general"] = [];
    }
    if (!checked_params["fault"]) {
      checked_params["fault"] = [];
    }
    checked_params = {
      general: [
        ...checked_params["general"],
        ...backend_params.filter(
          (item) =>
            item.type !== "fault" &&
            item.source !== "derived_optional" &&
            item.destination_key !== "lat" &&
            item.destination_key !== "long",
        ),
      ],
      fault: [
        ...checked_params["fault"],
        ...backend_params.filter(
          (item) => item.type === "fault" && item.source !== "derived_optional",
        ),
      ],
    };
    locationParams = backend_params.filter(
      (item) => item.destination_key == "lat" || item.destination_key == "long",
    );
    if (locationParams.length) {
      // checked_params['general'].push(
      //     location_param
      // );
    }

    if (!system_details[config.source_id]) {
      system_details[config.source_id] = {};
    }
    // if (!system_details[config.source_id][index+1]){
    // 	system_details[config.source_id][index+1]={}
    // }
    let backendSystemDetails = config.system_details;

    if (Array.isArray(backendSystemDetails)) {
      backendSystemDetails = config.system_details[0];
    }
    if (
      backendSystemDetails &&
      backendSystemDetails.fuel_sensor_name &&
      parseInt(backendSystemDetails.fuel_sensor_name)
    ) {
      fuel_sensor_name = backendSystemDetails.fuel_sensor_name;
      delete backendSystemDetails["fuel_sensor_name"];
    }
    let structed_system_details = structureSystemDetails(
      backendSystemDetails,
      config.parameters,
    );
    system_details[config.source_id][index + 1] =
      structed_system_details.system_details;
    param_settings = {
      ...param_settings,
      ...structed_system_details.param_settings,
    };
  });
  // let updated_system_details = {};
  // Object.keys(system_details).forEach((deviceId) => {
  // 	updated_system_details[deviceId] = {
  // 		'1': {
  // 			param_setting: {
  // 				general: system_details[deviceId],
  // 			},
  // 		},
  // 	};
  // });
  let returnObj = {
    checked_params,
    system_details,
    param_settings,
    fuel_sensor_name,
  };
  console.log("maaasystem_details", system_details);
  return returnObj;
}

export async function getThingsCategoryData(app_id) {
  const categoryData = await retriveApplicationThings(app_id);
  // console.log('categoryData -> ', categoryData);
  if (categoryData.status === "success") {
    let stateToUpdate = {
      things_categories: categoryData.things_categories,
      applications_details_list: categoryData.applications,
    };
    this.setState(
      {
        things_categories: categoryData.things_categories,
        applications_details_list: categoryData.applications,
        application_details: categoryData.applications,
        api_loaded: this.state.isThingAdd,
      },
      () => {
        if (!this.state.isThingAdd) {
          this.setThingTypeOptions(1);
        }
      },
    );
    return stateToUpdate;
  }
  /*
        Get things type and parameter data
        Parameter data will be as per thing type

     */
}
export async function setThingTypeOptions(initial = false) {
  const {
    is_rental,
    things_categories,
    applications_details_list,
    isThingAdd,
    api_loaded,
    allowed_categories,
  } = this.state;
  let thingTypes = [],
    thingtype_options = [],
    app_id_thing_type = {};
  if (true) {
    let applications = [16],
      restricted_types,
      is_aurassure = this.checkAurassureCustomer();
    if (Array.isArray(allowed_categories)) {
      restricted_types = allowed_categories;
      if (restricted_types[0] === "*") {
        restricted_types = "*";
      }
    }
    applications_details_list.forEach((application) => {
      if (applications.indexOf(parseInt(application.id)) > -1) {
        thingTypes = [...thingTypes, ...application.categories];
        app_id_thing_type[application.id] = application.categories;
      }
    });

    console.log("mani-asset type", thingTypes);
    const getCategoryName = (name) => {
      if (is_aurassure) {
        switch (name) {
          case "IAQMS": {
            return "Indoor AQ Monitor";
          }
          case "AAQMS": {
            return "Outdoor AQ Monitor";
          }
          default: {
            return name;
          }
        }
      }
      return name;
    };
    things_categories.forEach((category) => {
      if (
        thingTypes.indexOf(parseInt(category.id)) > -1 &&
        (!is_rental || (is_rental && category.is_available_for_rent)) &&
        (!Array.isArray(restricted_types) ||
          (Array.isArray(restricted_types) &&
            restricted_types.indexOf(category.id) >= 0))
      ) {
        thingtype_options.push({
          name: getCategoryName(category.name),
          id: category.id,
          is_available_for_rent: category.is_available_for_rent,
        });
      }
    });
  }

  if (initial) {
    this.setState({
      app_id_thing_type,
    });
  }
  let allFields = this.state.allFields || {};
  if (thingtype_options.length == 1 && thingtype_options[0].id) {
    // allFields['thingType']=thingtype_options[0].props.value
    // this.generalFormRef.current.formRef.current.setFieldsValue({
    // 	thingType: allFields['thingType'],
    // });
    // this.onParamsChange(allFields, allFields);
    // this.generalFormRef.current.setState({
    // 	thingType: allFields['thingType']
    // });
  }
  this.setState(
    {
      thingtype_options: thingtype_options.sort((a, b) => {
        const nameA = a.name.toUpperCase();
        const nameB = b.name.toUpperCase();
        if (nameA < nameB) {
          return -1;
        }
        if (nameA > nameB) {
          return 1;
        }
        return 0;
      }),
      allFields,
      api_loaded: api_loaded ? api_loaded : isThingAdd,
    },
    () => {
      if (
        isThingAdd &&
        thingtype_options.length == 1 &&
        thingtype_options[0].id
      ) {
        console.log("During thing type options");
        this.onQuickAddValuesChange(
          thingtype_options[0].id,
          "thingType",
          "select",
        );
      }
      if (isThingAdd && this.state.api_loaded && !this.state.quick_add) {
      }
      console.log(
        "maniiiallFields",
        this.state.allFields,
        this.state.thingtype_options,
      );
    },
  );
}
function getMachineInfoSplitFormData(details, asset_info_form_items) {
  let returnObj = {};
  Object.keys(asset_info_form_items).forEach((formKey) => {
    if (!returnObj[formKey]) {
      returnObj[formKey] = {};
    }
    if (Object.keys(asset_info_form_items[formKey]).length) {
      asset_info_form_items[formKey].forEach((itemKey) => {
        if (
          typeof details[itemKey] !== "undefined" &&
          details[itemKey] !== null
        ) {
          returnObj[formKey][itemKey] = details[itemKey];
        }
      });
    }
  });
  return returnObj;
}

function getModifiedCalibrationDetails(calibration_datails) {
  let updated_calibration_datails = {};
  Object.keys(calibration_datails).forEach((paramKey) => {
    if (!updated_calibration_datails[paramKey]) {
      updated_calibration_datails[paramKey] = {};
    }
    Object.keys(calibration_datails[paramKey]).forEach((formItem) => {
      let val = calibration_datails[paramKey][formItem];
      if (formItem === "check_duration" && parseInt(val) > 0) {
        val = parseInt(val);
        let min = parseInt(val / 60);
        updated_calibration_datails[paramKey]["check_duration_min"] = min;
        updated_calibration_datails[paramKey]["check_duration_sec"] =
          val - min * 60;
      }
      updated_calibration_datails[paramKey][formItem] = val;
    });
  });
  return updated_calibration_datails;
}

export function getModifiedSections(thingType) {
  let { sections, isMobile, advanced_thing_config } = this.state;
  let third_party_section = Object.assign({}, this.state.third_party_section);
  let calibration_section = Object.assign({}, this.state.calibration_section);
  if (isMobile) {
    third_party_section["show_section"] = advanced_thing_config;
    calibration_section["show_section"] = advanced_thing_config;
  }
  let section_keys = sections.map((item) => item.key);
  if (
    this.checkPollutionApplication({ thingType }) ||
    parseInt(thingType) == 86
  ) {
    // add only if section is not present already
    if (!section_keys.includes(third_party_section.key)) {
      sections.splice(sections.length - 2, 0, third_party_section);
    }
    if (parseInt(thingType) === 21 || parseInt(thingType) == 22 || parseInt(thingType) == 102) {
      // add only if section is not present already
      if (!section_keys.includes(calibration_section.key)) {
        sections.splice(sections.length - 1, 0, calibration_section);
      }
    } else {
      sections = sections.filter(
        (item) => item.key !== calibration_section.key,
      );
    }
  } else {
    sections = sections.filter((item) => item.key !== third_party_section.key);
    sections = sections.filter((item) => item.key !== calibration_section.key);
  }
  return sections;
}

// function to get system template values for multi select
function getSystemTemplateValue(allFields, system_details, activeTab) {
  let system_template = [];
  let all_system_templates = getAllSystemTemplates(system_details, activeTab);
  // let first_fuel_template=getFirstFuelTemplate(system_details,activeTab)
  if (parseInt(allFields.thingType) === 18 || true) {
    all_system_templates.forEach((item) => {
      if (parseInt(item) >= 0 || item === "custom") {
        system_template.push("template-" + item);
      }
    });
  }
  return system_template;
}
export async function processData(assetDetails, isReset) {
  const thingDetails = isReset
    ? JSON.parse(JSON.stringify(assetDetails))
    : assetDetails;
  let {
    thing_templates,
    mahchineinfo_template_data,
    machine_info_values,
    sections,
    vendor_id,
    spcb_data_push_details,
    cpcb_data_push_details,
    wrd_data_push_details,
    machineInfoDateValues,
  } = this.state;
  let calibration = {},
    extra_machine_info_data = {},
    calibration_params = [],
    cpcb_data_push_details_clone = cpcb_data_push_details,
    fuel_sensor_name = 0;
  let thingTemplate = thingDetails.thing_templates.thing_template || "custom";
  if (parseInt(thingTemplate) === 0) {
    thingTemplate = "custom";
  }
  thingTemplate = isNaN(parseInt(thingTemplate))
    ? "custom"
    : parseInt(thingTemplate);
  try {
    let that = this,
      system_details = {};
    let thing_details = thingDetails.thing_details;
    // moment conversions
    let convertion_fields = {},
      format = "x";
    moment_convertions.forEach((item) => {
      if (thing_details[item] && typeof thing_details[item] !== "object") {
        // convertion_fields[item] = moment(thing_details[item]);
        thing_details[item] = moment(thing_details[item]).format("X");
        // delete thing_details[item]
      }
    });
    console.log({ convertion_fields });
    // splitting machine info values into sections
    if (thing_details && Object.keys(thing_details).length) {
      machine_info_values = getMachineInfoSplitFormData(
        thing_details,
        this.state.asset_info_form_items,
      );
    }

    //extra thing calib Data
    if (thing_details.fuel_calib) {
      extra_machine_info_data = { fuel_calib: thing_details.fuel_calib };
    }

    let allFields = {
      customerName: parseInt(that.state.customer_id),
      applicationName: parseInt(that.state.application_id),
      thingName: thingDetails.name,
      thingType: thingDetails.category,
      thingTemplate: thingTemplate,
      latitude: thingDetails.latitude,
      longitude: thingDetails.longitude,
      address: thingDetails.address,
      engine_sl_no: thing_details.engine_sl_no,
      vendorName: thingDetails.vendor_id,
      genset_sl_no: thing_details.genset_sl_no,
      territory_id: thingDetails.territory_id,
      site_territory_id: thingDetails.site_territory_id,
      is_iot_enabled: parseInt(thingDetails.is_iot_enabled) == 1 ? true : false,
      adr_city: thing_details.adr_city,
      adr_province: thing_details.adr_province,
      adr_country_code: thing_details.adr_country_code,
      adr_pin_code: thing_details.adr_pin_code,
      gcp_push: thing_details.gcp_push,
    };
    if (!allFields["vendorName"] && vendor_id) {
      allFields["vendorName"] = vendor_id;
    }
    if (thingDetails.rented_to) {
      allFields["rented_to"] = thingDetails.rented_to;
    }
    if (
      this.checkMachineInfoInQuickAdd("commissioning_date") &&
      thing_details.commissioning_date
    ) {
      console.log(
        "mani commissioning",
        thing_details.commissioning_date,
        moment(thing_details.commissioning_date, "X"),
      );
      allFields["commissioning_date"] = thing_details.commissioning_date;
    }

    if (this.checkMachineInfoInQuickAdd("capacity") && thing_details.capacity) {
      allFields["capacity"] = thing_details.capacity;
    }

    if (this.checkMachineInfoInQuickAdd("serial") && thing_details.serial) {
      allFields["serial"] = thing_details.serial;
    }

    if (this.checkMachineInfoInQuickAdd("type") && thing_details.type) {
      allFields["type"] = thing_details.type;
    }

    // modify sections based on thing type
    sections = this.getModifiedSections(thingDetails.category);
    console.log("maniasa-final", sections);

    let activeTab;
    if (
      thingDetails.configurations &&
      thingDetails.configurations.length &&
      thingDetails.configurations[0] &&
      parseInt(thingDetails.configurations[0]?.source_id) >= 0
    ) {
      activeTab = `${thingDetails.configurations[0].source_id}`;
    }
    let temp_types_options = [],
      temp_categories = [];
    this.state.application_details.forEach((application) => {
      if (parseInt(application.id) === parseInt(this.state.application_id)) {
        temp_categories = application.categories;
      }
    });
    if (this.props.application_id === 17) {
      let vendorId = this.state.customers_list.find(
        (item) => parseInt(item.id) === parseInt(that.state.customer_id),
      )?.vendor_id;
      if (parseInt(vendorId) !== 722) {
        temp_categories = temp_categories.filter(
          (item) => parseInt(item) !== 71,
        );
      }
    }

    let params = {},
      allParams = [],
      locationParams = [];
    allParams = this.state.things_categories.find(
      (item) => parseInt(item.id) === parseInt(thingDetails.category),
    ).parameter_details;
    allParams = allParams.sort((a, b) => a.order - b.order);
    let all_param_details = {};
    allParams.forEach((param) => {
      all_param_details[param.destination_key] = param;
    });
    params["general_parameters"] = allParams.filter(
      (item) =>
        item.type !== "fault" &&
        item.source !== "derived_optional" &&
        item.destination_key !== "lat" &&
        item.destination_key !== "long",
    );
    locationParams = allParams.filter(
      (item) => item.destination_key == "lat" || item.destination_key == "long",
    );
    if (locationParams.length) {
      // params['general_parameters'].push(location_param);
    }
    params["fault_parameters"] = allParams.filter(
      (item) => item.type === "fault" && item.source !== "derived_optional",
    );
    params["derived_parameters"] = allParams.filter(
      (item) => item.source === "derived_optional",
    );
    let default_params = {
      general: [],
      fault: [],
    };

    let selectedParams = {},
      checked_params = {},
      param_settings = {},
      template_details = {},
      general_params = {},
      fault_params = {},
      checked_params2 = {},
      analyser_details = [],
      system_params = {},
      all_analyser_params = [];
    if (parseInt(activeTab) >= 0 && parseInt(thingTemplate) !== 0) {
      let returnObj = this.getSelectedParams(thingDetails, allParams);
      checked_params = returnObj.checked_params;
      system_details = returnObj.system_details;
      if (returnObj.fuel_sensor_name) {
        fuel_sensor_name = returnObj.fuel_sensor_name;
      }
      param_settings = returnObj.param_settings;
      all_analyser_params = getAllCheckedParams(checked_params);
      if (thingTemplate != "custom" && thing_templates[thingTemplate]) {
        selectedParams = thing_templates[thingTemplate].parameters;
        default_params.general = params.general_parameters.filter(
          (item) => selectedParams.general?.indexOf(item.destination_key) >= 0,
        );
        default_params.fault = params.fault_parameters.filter(
          (item) =>
            !("fault" in selectedParams) ||
            selectedParams.fault?.indexOf(item.destination_key) >= 0,
        );
        checked_params2 = checked_params;
        // checked_params2['general'] = checked_params2.general.map(
        // 	(item) => item.destination_key
        // );
        // checked_params2['fault'] = checked_params2.fault.map(
        // 	(item) => item.destination_key
        // );
        general_params = checked_params2.general.map(
          (item) => item.destination_key,
        );
        fault_params = checked_params2.fault.map(
          (item) => item.destination_key,
        );
        general_params = general_params.sort();
        fault_params = fault_params.sort();
      }
    }

    let mc_template =
      thingDetails.thing_templates.machine_info_template || undefined;
    if (parseInt(mc_template) === 0) {
      mc_template = undefined;
    }
    let machine_info_template_values = mahchineinfo_template_data[mc_template];
    console.log({ checked_params, general_params, fault_params });
    let template_modified = false;

    // checking machine info modified fields
    if (mc_template && machine_info_template_values) {
      let parsed_machine_info_template_values = JSON.parse(
        machine_info_template_values,
      );
      delete parsed_machine_info_template_values["engine_sl_no"];
      delete parsed_machine_info_template_values["genset_sl_no"];
      Object.keys(parsed_machine_info_template_values).forEach((item) => {
        if (
          !machine_info_values[item] ||
          parsed_machine_info_template_values[item] != machine_info_values[item]
        ) {
          template_modified = true;
        }
      });
    }
    // // checking modified parameters
    if (
      parseInt(thingDetails.category) == 18 &&
      !template_modified &&
      general_params &&
      fault_params &&
      selectedParams["general"] &&
      selectedParams["fault"]
    ) {
      if (
        JSON.stringify(general_params) !==
          JSON.stringify(selectedParams["general"].sort()) ||
        JSON.stringify(fault_params) !==
          JSON.stringify(selectedParams["fault"].sort())
      ) {
        template_modified = true;
      }
    }

    if (this.checkPollutionApplication(allFields)) {
      let all_selected_params = [];
      // analyser Details
      if (thingDetails.thing_details && thingDetails.thing_details.parts) {
        thingDetails.thing_details.parts.forEach((part) => {
          all_selected_params = [
            ...all_selected_params,
            ...part.parameters_measured,
          ];
        });
        let unselected_params = all_analyser_params.filter(
            (item) => all_selected_params.indexOf(item.destination_key) == -1,
          ),
          selected_params = [],
          foundParam;
        thingDetails.thing_details.parts.forEach((part) => {
          selected_params = [];
          part.parameters_measured.forEach((paramKey) => {
            foundParam = allParams.find(
              (item) => item.destination_key == paramKey,
            );
            if (foundParam) {
              selected_params.push(foundParam);
            }
          });
          analyser_details.push({
            status_flag: part.status_flag,
            certification: part.certification,
            make: part.make,
            model: part.model,
            serial_no: part.serial_no,
            vendor: part.vendor,
            params: part.parameters_measured,
            unselected_params: [...unselected_params, ...selected_params],
          });
        });
      }

      // 3rd party integration details
      if (
        thingDetails.thing_details &&
        thingDetails.thing_details.spcb_data_push_details
      ) {
        spcb_data_push_details =
          thingDetails.thing_details.spcb_data_push_details;
      }

      if (
        thingDetails.thing_details &&
        thingDetails.thing_details.cpcb_data_push_details
      ) {
        cpcb_data_push_details =
          thingDetails.thing_details.cpcb_data_push_details;
        cpcb_data_push_details_clone = cpcb_data_push_details;
      }

      // calibration Details
      if (
        thingDetails.thing_details &&
        thingDetails.thing_details.calibration
      ) {
        calibration = getModifiedCalibrationDetails(
          thingDetails.thing_details.calibration,
        );
        calibration_params = Object.keys(calibration).filter(
          (item) => item !== "zero",
        );
      }
    }

    //wrd data push details for flow meter
    if (
      thingDetails.thing_details &&
      thingDetails.thing_details.wrd_data_push_details
    ) {
      wrd_data_push_details = thingDetails.thing_details.wrd_data_push_details;
    }
    let unassigned_params = {
      general: [],
      fault: [],
    };

    // set system templates in quick add form
    allFields["system_template"] = getSystemTemplateValue(
      allFields,
      system_details,
      activeTab,
    );
    this.setState(
      {
        thing_details: thingDetails,
        // thing_details_clone: thingDetails,
        resetTimestamp: moment().format("x"),
        locationParams,
        machine_info_values: machine_info_values,
        machineInfoDateValues: isReset ? {} : machineInfoDateValues,
        allFields: allFields,
        assetWithoutCustomer: thingDetails.thing_customer_type === 1,
        all_param_details,
        template_modified,
        activeTab,
        system_details,
        calibration,
        calibration_params,
        convertion_fields,
        cpcb_data_push_details,
        spcb_data_push_details,
        cpcb_data_push_details_clone,
        wrd_data_push_details,
        extra_machine_info_data,
        machine_info_full_list: true,
        device_config_tab: thing_details.config_mode || "advanced",
        quick_add: (thing_details.config_mode || "advanced") === "quick",
        all_params: params,
        all_analyser_params: all_analyser_params,
        all_analyser_params_clone: all_analyser_params,
        analyser_details: analyser_details,
        checked_params,
        template_details,
        param_settings,
        api_loaded: true,
        sections,
        machine_info_template: mc_template,
        system_params,
        unassigned_params,
      },
      async () => {
        // modify location Data
        if (
          isReset &&
          this.locationPicker &&
          this.locationPicker.current &&
          this.locationPicker.current
        ) {
          console.log(this.locationPicker.current);
          this.locationPicker.current.updateLocationFromProps(true);
        }
        // console.log("mani-param",param_settings)
        // set selected params from system template in thing template
        let getParamsFromConfig = () => {
          let all_param_destination = {
            general: params.general_parameters.map(
              (item) => item.destination_key,
            ),
            fault: params.fault_parameters.map((item) => item.destination_key),
          };
          let all_configured_params = [];
          thingDetails.configurations.forEach((config) => {
            all_configured_params = [
              ...all_configured_params,
              ...config.parameters,
            ];
          });
          // add location param if lat or lng exists
          let configured_location_params = all_configured_params.filter(
            (item) =>
              item.destination_key == "lat" || item.destination_key == "long",
          );
          if (configured_location_params.length) {
            all_configured_params = all_configured_params.filter(
              (item) =>
                item.destination_key !== "lat" &&
                item.destination_key !== "long",
            );
            // all_configured_params.push(location_param)
          }
          system_params = {
            general: all_configured_params
              .filter(
                (item) =>
                  all_param_destination["general"].indexOf(
                    item.destination_key,
                  ) > -1,
              )
              .map((item) => item.destination_key),
            fault: all_configured_params
              .filter(
                (item) =>
                  all_param_destination["fault"].indexOf(item.destination_key) >
                  -1,
              )
              .map((item) => item.destination_key),
          };
          unassigned_params.general = params.general_parameters.filter(
            (item) =>
              system_params["general"].indexOf(item.destination_key) === -1 &&
              item.destination_key !== "location",
          );
          unassigned_params.fault = params.fault_parameters.filter(
            (item) =>
              system_params["fault"].indexOf(item.destination_key) === -1,
          );
        };
        let first_system_template = getFirstSystemTemplate(
          system_details,
          activeTab,
        );
        if (first_system_template === "custom") {
          getParamsFromConfig();
          this.setState(
            { unassigned_params, system_params, api_loaded: true },
            () => {
              // this.setMachineInfoFormValues();
            },
          );
        } else if (activeTab) {
          console.log("maaasystem_details", activeTab);
          await this.setSystemDetails(system_details[activeTab], true, false);
          if (fuel_sensor_name) {
            let { system_templates, allFields } = this.state;
            let { thingType, system_template } = allFields;
            if (
              Array.isArray(system_templates) &&
              system_templates.length > 0
            ) {
              let fuel_key = CONSTANTS.fuel_sensors[thingType].find(
                (item) => item.value == fuel_sensor_name,
              )?.value;
              let fuel_template = system_templates.find(
                (item) => item.template_key === fuel_key,
              )?.id;
              system_template.push("template-" + fuel_template);
              this.onQuickAddValuesChange(
                system_template,
                "system_template",
                "select",
              );
              // alert(fuel_template)
            }
          }
        }

        // if (first_system_template==="custom"){
        //     getParamsFromConfig()
        // } else {
        //     let thingSystemDetails=await this.getSystemDetailsAndParamsFromThingTemplate(thingTemplate,getFirstSystemTemplate(system_details,activeTab))
        //     if (thingSystemDetails==='invalid_template'){
        //         getParamsFromConfig()
        //     } else {
        //         system_params=thingSystemDetails.params
        //     }
        // }
        // console.log("maniii-system_params",system_params,thingTemplate,getFirstSystemTemplate(system_details,activeTab))
        // this.setState(
        //     { unassigned_params,system_params,allFields:{...allFields,system_template:first_system_template} ,api_loaded:true},
        //     () => {
        //         // this.setMachineInfoFormValues();
        //     }
        // );
        let formSetState = { thingType: thingDetails.category };
        // if (!that.showDeviceConfig()) {
        //     formSetState['disabled'] = true;
        // }
        // if (thingDetails.is_rental && !thingDetails.is_customer_change_allowed){
        //     formSetState['disabledRental'] = true;
        // }
        // formSetState['disableVendor'] =
        //     parseInt(this.props.application_id) !== 12;
        // formSetState['is_iot_enabled'] =
        //     parseInt(thingDetails.is_iot_enabled) == 1
        //         ? true
        //         : false;
        // await this.generalFormRef.current.setState(formSetState);
        // this.generalFormRef.current.formRef.current.setFieldsValue(
        // 	allFields
        // );
        this.initialiseIntersectionObserver();
        // this.getThingsCategoryDetails(thingDetails.category,allFields);
      },
    );
  } catch (err) {
    console.log("error", err);
    openNotification("error", "Something Went Wrong");
  }
}

export function handleMobileSections() {
  return;
  let { sections, advanced_thing_config, machine_info_json } = this.state;
  let active_section = [];
  let advancedIndex = sections.findIndex(
    (item) => item.key === "mobile_advanced",
  );
  sections = sections.map((item, index) => {
    if (index > advancedIndex) {
      item.show_section = advanced_thing_config;
      if (item.key == "advanced") {
        item.show_section =
          machine_info_json &&
          machine_info_json["advanced"] &&
          advanced_thing_config;
      }
      if (item.key == "remote_control") {
        item.show_section =
          machine_info_json &&
          machine_info_json["remote_control"] &&
          advanced_thing_config;
      }
    } else if (item.show_section) {
      active_section.push(item.key);
    }
    return item;
  });
  this.setState({ sections, active_section });
}

export function structureSystemDetails(system_config, parameters) {
  let system_details = {};
  let param_config = {},
    general_config = {},
    locationParams = [];
  let item_keys = {
    system_config: ["template_id", "fuel_sensor_name", "sampling_interval"],
    helper_parameters: [
      "fault_registers",
      "intermediary_parameters",
    ],
    hardware_interface: [
      "hardware_interface",
      "serial_port",
      "port",
      "baud_rate",
      "data_bits",
      "stop_bits",
      "parity",
      "ip",
      "port",
    ],
    protocol_config: [
      "slave_id",
      "protocol",
      "address_type",
      "enable_chunk_reading",
      "node_id",
      "di_ip",
      "enable_ping",
      "ping_interval",
      "ping_timeout",
      "ping_retry_count",
      "ping_retry_interval",
    ],
  };
  let param_config_props = [
    "description",
    "source_unit",
    "destination_unit",
    "range_min",
    "range_max",
    "offset",
    "source",
    "multiplier",
    "range_filter_handling",
    "garbage_data_handling",
    "data_type",
  ];
  let sys_config = JSON.parse(JSON.stringify(system_config));
  if (sys_config && sys_config.template) {
    sys_config["template_id"] = sys_config.template;
    delete sys_config["template"];
  }
  if (sys_config && !sys_config.template_id) {
    sys_config["template_id"] = "custom";
  }

  system_details["priority"] = sys_config.priority || 0;
  console.log("mani-template", sys_config);
  Object.keys(item_keys).forEach((formName) => {
    item_keys[formName].forEach((key) => {
      if (typeof sys_config[key] !== "undefined") {
        system_details[formName] = {
          ...system_details[formName],
          [key]: sys_config[key],
        };
      }
    });
  });
  let param_setting_config = {},
    param_values_config = {};
  let param_config_keys = {
    param_values: [
      "pin",
      "conc_adr",
      "input_type",
      "param_type",
      "expr",
      "res_adr",
      "src_multiplier",
      "src_offset",
      "function_code",
      "address",
      "src_data_type",
      "byte_swap",
      "word_swap",
      "input_min",
      "input_max",
      "output_min",
      "output_max",
    ],
    param_setting: [
      "multiplier",
      "offset",
      "source_unit",
      "destination_unit",
      "range_min",
      "range_max",
      "range_filter_handling",
      "garbage_data_handling",
      "zero_data_handling",
      "no_data_handling",
      "data_type",
      "description",
    ],
  };
  if (Array.isArray(parameters) && parameters.length > 0) {
    parameters.forEach((param) => {
      let param_obj = {};
      Object.keys(param).forEach((pram_key) => {
        if (param_config_keys.param_setting.indexOf(pram_key) > -1) {
          if (!param_setting_config[param.destination_key]) {
            param_setting_config[param.destination_key] = {};
          }
          param_setting_config[param.destination_key] = {
            ...param_setting_config[param.destination_key],
            [pram_key]: param[pram_key],
          };
        } else if (param_config_keys.param_values.indexOf(pram_key) > -1) {
          if (!param_values_config[param.destination_key]) {
            param_values_config[param.destination_key] = {};
          }
          param_values_config[param.destination_key] = {
            ...param_values_config[param.destination_key],
            [pram_key]: param[pram_key],
          };
        }
      });
    });
  }

  // if (system_config && system_config.parameters && Array.isArray(system_config.parameters)){
  //     system_config.parameters.forEach(param=>{
  //         let updated_param=JSON.parse(JSON.stringify(param))
  //         delete updated_param['key']
  //         param_config={...param_config,[param.key]:updated_param}
  //     })
  //     if (Object.keys(param_config).length){
  //         system_details['param_setting']={
  //             general:param_config
  //         }
  //     }
  // }

  // system_details['param_setting']={
  //     general:param_setting_config
  // }
  system_details["assigned"] = {
    general: [],
    fault: [],
  };
  system_details["assigned"]["general"] = parameters.filter(
    (item) =>
      item.type !== "fault" &&
      item.source !== "derived_optional" &&
      item.destination_key !== "lat" &&
      item.destination_key !== "long",
  );
  system_details["assigned"]["fault"] = parameters.filter(
    (item) => item.type === "fault" && item.source !== "derived_optional",
  );
  // add location param if lat or lng exists
  locationParams = parameters.filter(
    (item) => item.destination_key == "lat" || item.destination_key == "long",
  );
  if (locationParams.length) {
    // system_details['assigned']['general'].push(location_param)
  }

  system_details["param_values"] = {
    general: param_values_config,
  };
  console.log("system32", param_setting_config);

  return { system_details, param_settings: param_setting_config };
}
export async function getThingsDetails(customer_id, app_id, thing_id) {
  try {
    let thingDetails = await getThingConfigsNew(customer_id, app_id, thing_id);
    if (thingDetails.status === "success") {
      let stateToUpdate = {
        thing_details: thingDetails,
        thing_details_clone: JSON.parse(JSON.stringify(thingDetails)),
        vendor_id: thingDetails.vendor_id,
        is_rental: thingDetails.is_rental ? thingDetails.is_rental : false,
      };
      console.log({ stateToUpdate });
      return stateToUpdate;
    } else {
      openNotification("error", thingDetails.message);
    }
  } catch (err) {
    openNotification("error", err);
  }
}
export function checkUserisEndCustomer() {
  let applicationId = this.props.application_id;
  return applicationId !== 17 && applicationId !== 12;
}
export async function getVendorsCustomers(thing_details) {
  let rented_options = [],
    general_form_json =
      this.props.location.pathname &&
      this.props.location.pathname.includes("/datoms-x")
        ? generalForm
        : nonDatomsgeneralForm;
  let updatedJsonCont = {},
    statetoUpdate = {},
    application_id = parseInt(this.props.application_id);
  // if (this.checkUserisEndCustomer() && parseInt(this.props.vendor_id)===1062 && general_form_json.Form.state && typeof (general_form_json.Form.state.disable_genset)!==undefined){
  //     general_form_json.Form.state.disable_genset=true
  // }
  if (
    thing_details.is_rental &&
    (application_id == 17 || application_id == 12)
  ) {
    let vendorId = this.props.client_id;
    if (application_id == 12) {
      vendorId = this.state.customer_id;
    }
    const customersList = await retriveCustomerList(
      vendorId,
      `?end_customer_only=true&vendors=[${vendorId}]`,
    );
    // console.log('customersList -> ', customersList);
    if (customersList.status === "success") {
      rented_options = customersList.customers.map((customer) => {
        return {
          id: customer.id,
          name: customer.name,
        };
      });
    }
    let rented_to_formItem = {
      rented_item: {
        compo: "Form.Item",
        props: {
          name: "rented_to",
          className: "formItem",
          label: "Rented to",
        },
        child: {
          selectItem1: {
            compo: "Select",
            props: {
              fillOptionsKey: "rented_options",
              placeholder: "Select Customer",
              showSearch: true,
              allowClear: true,
              disabled: "(context.state.disabledRental)",
              seval: ["disabled"],
              filterOption:
                "{(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 || option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0}",
            },
          },
        },
      },
    };
    try {
      if (
        this.props.location.pathname &&
        this.props.location.pathname.includes("/datoms-x")
      ) {
        Object.entries(general_form_json.Form.child.item6.child).forEach(
          ([key, value], index) => {
            updatedJsonCont = { ...updatedJsonCont, ...{ [key]: value } };
            if (index === 0) {
              updatedJsonCont = { ...updatedJsonCont, ...rented_to_formItem };
            }
          },
        );
        general_form_json.Form.child.item6.child = updatedJsonCont;
      } else {
        if (
          (parseInt(thing_details.category) > 0 &&
            parseInt(thing_details.category) !== 18) ||
          !thing_details.is_iot_enabled
        ) {
          Object.entries(general_form_json.Form.child.item9.child).forEach(
            ([key, value], index) => {
              if (index === 0) {
                updatedJsonCont = { ...updatedJsonCont, ...rented_to_formItem };
              }
              updatedJsonCont = { ...updatedJsonCont, ...{ [key]: value } };
            },
          );
          general_form_json.Form.child.item9.child = updatedJsonCont;
        } else {
          Object.entries(general_form_json.Form.child.item5.child).forEach(
            ([key, value], index) => {
              if (index === 0) {
                updatedJsonCont = { ...updatedJsonCont, ...rented_to_formItem };
              }
              updatedJsonCont = { ...updatedJsonCont, ...{ [key]: value } };
            },
          );
          general_form_json.Form.child.item5.child = updatedJsonCont;
        }
      }
    } catch (e) {
      console.log("Rental form modification error", e);
    }
  }
  this.setState({ rented_options, general_form_json });
}

/*
        Get Device list for device selection
     */
export async function getDeviceList(client_id, app_id, vendorId) {
  let selectdata = {},
    stateToUpdate = {},
    activeTab,
    device_config_values = [],
    device_type = {},
    api_status = this.state.api_status;
  const { isThingAdd, thing_details } = this.state;
  // let deviceListData = {};
  let deviceList = [],
    all_devices = {
      Assigned: [],
      Unassigned: [],
    },
    unassignedList = [],
    oemUnassignedIds = [];
  let vendor_id = parseInt(this.state.vendor_id);
  if (this.state.is_rental) {
    vendor_id =
      parseInt(this.props.application_id) === 12
        ? client_id
        : this.props.client_id;
  }
  if (vendorId) {
    vendor_id = vendorId;
  }
  if (parseInt(this.props.application_id) === 17) {
    vendor_id = this.props.client_id;
  }
  const compareVendorId = vendorId ? vendorId : this.state.allFields.vendorName;
  if (
    this.isDealerManagement &&
    compareVendorId &&
    compareVendorId !== this.props.client_id
  ) {
    vendor_id = compareVendorId;
  }

  let assignedClientId = client_id;

  let deviceQuery = "unassigned";

  if (
    (this.props.application_id === 17 && this.props.vendor_id !== 1) ||
    (this.props.client_id !== 1 && this.props.client_id !== vendor_id)
  ) {
    deviceQuery += "&show_vendor_unassigned_devices=true";
  }

  let [deviceListData, unassignedDeviceList] = await Promise.all([
    devicesList(assignedClientId, app_id, "?lite=true"),
    deviceLists(deviceQuery, vendor_id, "&lite=true"),
  ]);
  if (parseInt(assignedClientId) > 0) {
    // deviceListData = await devicesList(client_id, app_id);
    // console.log('deviceList -> ', deviceListData);
    if (deviceListData.status === "success") {
      if (deviceListData && deviceListData.devices) {
        deviceListData.devices.map((device) => {
          if (!(this.isDealerManagement && device.vendor_id !== vendor_id)) {
            deviceList.push({
              id: device.id,
              name: device.qr_code ? device.qr_code : device.name,
            });
            all_devices.Assigned.push({
              id: device.id,
              type: device.type_id,
              ...device,
            });
          }
        });
      }
    }
  }
  // const unassignedDeviceList = await deviceLists('unassigned', vendor_id);
  console.log({ deviceList });
  if (unassignedDeviceList.status === "success") {
    if (unassignedDeviceList && unassignedDeviceList.devices) {
      unassignedDeviceList.devices.forEach((device) => {
        if (vendor_id !== device.vendor_id) {
          oemUnassignedIds.push(device.id);
        }
        unassignedList.push({
          id: device.id,
          name: device.qr_code ? device.qr_code : device.name,
        });
        all_devices.Unassigned.push({
          id: device.id,
          type_id: device.type_id,
          ...device,
        });
        //type_id
      });
    }
  }
  console.log("mani all_devices", all_devices);

  selectdata["Assigned"] = deviceList;
  selectdata["Unassigned"] = unassignedList.filter(
    (device) => !oemUnassignedIds.includes(device.id),
  );
  if (oemUnassignedIds.length) {
    selectdata["OEM Unassigned"] = unassignedList.filter((device) =>
      oemUnassignedIds.includes(device.id),
    );
  }
  api_status["device"] = false;
  stateToUpdate = {
    devices_options: deviceList,
    all_devices,
    unassigned_devices_options: unassignedList,
    selectdata: selectdata,
    api_status,
  };
  if (deviceList.length || unassignedList.length) {
    stateToUpdate["selectdata"] = selectdata;
  }

  // add options in asset configuration
  if (!isThingAdd) {
    // set device type and device dropdown values for selected device
    thing_details.configurations.forEach((item) => {
      if (
        device_config_values.every(
          (item2) => parseInt(item.source_id) !== parseInt(item2.devices),
        )
      ) {
        let pushItem = { devices: `${item.source_id}` };
        device_config_values.push(pushItem);
        device_type[item.source_id] = this.getDeviceType(
          item.source_id,
          all_devices,
        );
      }
    });
    activeTab = thing_details?.configurations[0]?.source_id || 0;
    stateToUpdate["device_config_values"] = device_config_values;
    stateToUpdate["device_type"] = device_type;
    stateToUpdate["is_gps_available"] = this.getDeviceGpsStatus(
      activeTab,
      all_devices,
    );
  }

  flushSync(() => {
    this.setState(stateToUpdate);
  });
}

export async function getTemplates(thingType, vendorName) {
  let stateToUpdate = {},
    thing_templates = {},
    mahchineinfo_template_data = {},
    mahchineinfo_templates = [],
    thingtemplate_options = [],
    thingTemplates,
    machineInfoTemplates,
    that = this;
  let {
    customer_id,
    vendor_id,
    allFields,
    show_quick_add,
    api_status,
    isThingAdd,
    sections,
    isMobile,
    advanced_thing_config,
  } = this.state;
  let { customerName } = allFields;
  let { client_id, application_id } = this.props;
  let configData = {
    customer_id: vendor_id || client_id,
    application_id,
  };
  if (vendorName) {
    vendor_id = vendorName;
    configData.customer_id = vendorName;
  }
  if (this.props.application_id !== 12 && this.props.application_id !== 17) {
    configData = {
      customer_id: that.props.client_id,
      application_id: that.props.application_id,
    };
  }
  if (that.state.is_rental) {
    // alert(parseInt(application_id)==12)
    configData = {
      customer_id:
        parseInt(application_id) == 12
          ? isThingAdd
            ? customerName
            : vendor_id
          : that.props.client_id,
      application_id: that.props.application_id,
    };
  }
  [thingTemplates, machineInfoTemplates] = await Promise.all([
    getThingTemplates(customer_id || configData.customer_id, thingType),
    getMachineInfoTemplates(customer_id || configData.customer_id, thingType),
  ]);
  if (machineInfoTemplates.status === "success") {
    if (machineInfoTemplates.template_list.length > 0) {
      machineInfoTemplates.template_list.forEach((item) => {
        mahchineinfo_templates.push({
          name: item.name,
          value: `${item.id}`,
        });
        mahchineinfo_template_data[item.id] = item.data;
      });
    }
    stateToUpdate["mahchineinfo_templates"] = mahchineinfo_templates;
    stateToUpdate["mahchineinfo_template_data"] = mahchineinfo_template_data;
  }
  if (thingTemplates.status === "success") {
    if (thingTemplates.template_list.length > 0) {
      thingTemplates.template_list.forEach((item) => {
        thingtemplate_options.push({
          name: item.name,
          id: item.id,
        });
        thing_templates[item.id] = item.data;
      });
      allFields["thingTemplate"] = undefined;
      stateToUpdate["thing_templates"] = thing_templates;
      stateToUpdate["thingtemplate_options"] = thingtemplate_options;
      show_quick_add.thing = true;
    } else {
      show_quick_add.thing = false;
      show_quick_add.system = true;
    }
  }
  stateToUpdate["show_quick_add"] = show_quick_add;
  api_status["templates"] = false;
  api_status["allFields"] = allFields;
  stateToUpdate["api_status"] = api_status;
  flushSync(() => {
    this.setState(stateToUpdate, () => {});
  });
  // return stateToUpdate;
}

//get System templates and protocols
export async function getSystemTemplates(
  customerId,
  applicationId,
  thingType,
  vendorId,
) {
  let customer_id = this.state.customer_id,
    system_templates;
  let end_customer =
    parseInt(this.props.application_id) !== 12 &&
    parseInt(this.props.application_id) !== 17;
  let application_id = this.state.application_id;
  let thing_type = this.state.allFields.thingType;
  let { quick_add, api_status, show_quick_switch, device_config_tab } =
    this.state;
  if (this.state.is_rental) {
    customer_id =
      parseInt(application_id) == 12 ? customerId : this.props.client_id;
  }
  if (customerId) {
    customer_id = customerId;
  }
  if (quick_add && !this.state.is_rental) {
    customer_id = this.props.client_id;
  }
  let vendor_id = parseInt(this.state.vendor_id);
  if (this.state.is_rental) {
    vendor_id =
      parseInt(this.props.application_id) === 12
        ? customer_id
        : this.props.client_id;
  }
  if (vendorId) {
    vendor_id = vendorId;
  }
  if (parseInt(this.props.application_id) === 17 || end_customer) {
    vendor_id = this.props.client_id;
  }
  if (applicationId) {
    application_id = applicationId;
  }
  if (thingType) {
    thing_type = thingType;
  }
  let template_options = quick_add
      ? []
      : [
          {
            compo: "Option",
            props: {
              value: "custom",
            },
            child: "Custom",
          },
        ],
    protocol_options = [],
    quick_system_template_options = [];
  let [templateList, protocolList] = await Promise.all([
    getTemplateList(vendor_id, application_id, thing_type),
    getProtocolsList(vendor_id, application_id),
  ]);
  if (protocolList.status === "success") {
    protocolList.protocol_list.forEach((item) => {
      protocol_options.push({
        compo: "Option",
        props: {
          value: `${item.id}`,
        },
        child: item.name,
      });
    });
  }
  if (templateList.status == "success") {
    templateList = templateList.template_list;
    system_templates = templateList;
    let pushItem = {
      id: 145,
      name: "Test-Fuel-Sensor",
      template_key: "TEST_FUEL",
    };
    show_quick_switch = system_templates.length > 0;
    if (system_templates.length == 0) {
      device_config_tab = "advanced";
    }
    // system_templates.push(pushItem)
    system_templates.forEach((item) => {
      quick_system_template_options.push({
        label: item.name,
        value: "template-" + item.id,
      });
      template_options.push({
        compo: "Option",
        props: {
          value: `${item.id}`,
        },
        child: item.name,
      });
    });
  }

  if (
    false &&
    CONSTANTS.fuel_sensors[thingType] &&
    Array.isArray(CONSTANTS.fuel_sensors[thingType])
  ) {
    CONSTANTS.fuel_sensors[thingType].forEach((item) => {
      quick_system_template_options.push({
        label: item.label,
        value: "fuel-" + item.value,
      });
    });
  }
  api_status["system_templates"] = false;
  flushSync(() => {
    this.setState({
      device_config_tab,
      quick_add: device_config_tab === "quick",
      show_quick_switch,
      protocol_options,
      system_templates,
      api_status,
      system_template_options: template_options,
      quick_system_template_options,
    });
  });
}
const removeFormItems = (json, is_mahindra) => {
  let removeItems = Array.isArray(is_mahindra)
    ? is_mahindra
    : is_mahindra
      ? ["commissioning_date"]
      : [];
  // let removeItems=['']
  try {
    Object.entries(json["Form"]["child"]).forEach(([key, val]) => {
      if (val && val.props.name && removeItems.includes(val.props.name)) {
        delete json["Form"]["child"][key];
      }
    });
    console.log({ json });
    return JSON.stringify(json);
  } catch (e) {
    console.log("JSON modification error");
    return JSON.stringify(json);
  }
};
export async function getThingsCategoryDetails(type, allFields) {
  let {
    is_rental,
    quick_add,
    is_add,
    sections,
    api_status,
    advanced_thing_config,
    isThingAdd,
    isMobile,
  } = this.state;
  let stateUpdate = {},
    checkMahindra = this.checkMachineInfoInQuickAdd("get_fields_array");
  let formJson,
    machine_info_json = {};
  this.setState({ show_machine_forms: false });
  let allformJson;
  let application = this.state.applications_details_list.find(
    (item) => item.categories.indexOf(parseInt(type)) > -1,
  );
  let customer_id = allFields.customerName;
  // if (is_rental) {
  //     customer_id=this.props.client_id
  //     await this.getTemplates(type);
  //     await this.getDeviceList(this.props.client_id, application.id);
  // }
  if (type) {
    let thingCategoryDetails = {};
    thingCategoryDetails = await getThingCategoryDetails(
      allFields.customerName || this.props.client_id,
      allFields.applicationName || 16,
      type,
    );
    sections = sections.map((item) => {
      if (item.key == "advanced" || item.key == "remote_control") {
        item.show_section = false;
      }
      return item;
    });
    if (thingCategoryDetails.status === "success") {
      allformJson = thingCategoryDetails.thing_category_details;
      console.log({ allformJson });
      if (allformJson && allformJson.mandatory && allformJson.mandatory.Form) {
        machine_info_json["mandatory"] = removeFormItems(
          allformJson.mandatory,
          checkMahindra,
        );
      }
      if (allformJson && allformJson.basic && allformJson.basic.Form) {
        machine_info_json["basic"] = removeFormItems(
          allformJson.basic,
          checkMahindra,
        );
      }
      if (allformJson && allformJson.advanced && allformJson.advanced.Form) {
        machine_info_json["advanced"] = removeFormItems(
          allformJson.advanced,
          checkMahindra,
        );
        sections = sections.map((item) => {
          if (item.key == "advanced") {
            item.show_section = true;
          }
          return item;
        });
      }
      if (
        allformJson &&
        allformJson.remote_control &&
        allformJson.remote_control.Form
      ) {
        machine_info_json["remote_control"] = removeFormItems(
          allformJson.remote_control,
          checkMahindra,
        );
        sections = sections.map((item) => {
          if (item.key == "remote_control") {
            item.show_section = true;
          }
          return item;
        });
      }

      if (
        allformJson &&
        !allformJson.mandatory &&
        !allformJson.basic &&
        !allformJson.advanced
      ) {
        machine_info_json["mandatory"] = removeFormItems(
          allformJson.v2,
          checkMahindra,
        );
      }
      stateUpdate["machine_info_json"] = machine_info_json;
    }
    if (allformJson && allformJson.v2) {
      this.getMandatoryFormJson(allformJson.v2, checkMahindra, type);
    } else {
      stateUpdate["mandatory_json"] = null;
    }
    // this.getSystemTemplates(customer_id,application.id,type)
  }

  let asset_info_form_items = {};
  const getFieldNames = (item, form) => {
    // console.log(item,item.props);
    if (item && item.props && item.compo === "Form.Item" && item.props.name) {
      if (!asset_info_form_items[form]) {
        asset_info_form_items[form] = [];
      }
      asset_info_form_items[form].push(item.props.name);
      // return;
    } else if (Object.values(item).length) {
      Object.values(item).forEach((val) => {
        if (typeof val == "object") {
          getFieldNames(val, form);
        }
      });
    }
  };
  Object.keys(machine_info_json).forEach((form) => {
    if (machine_info_json[form]) {
      getFieldNames(JSON.parse(machine_info_json[form]), form);
    }
  });
  stateUpdate["asset_info_form_items"] = asset_info_form_items;
  stateUpdate["show_machine_forms"] = true;
  stateUpdate["sections"] = sections;
  api_status["category"] = false;
  stateUpdate["api_status"] = api_status;
  if (is_rental) {
    stateUpdate["selected_application"] = 16;
  }
  flushSync(() => {
    this.setState(stateUpdate, () => {
      console.log(
        "maniiiireturnObjasset_info_form_items",
        this.state.asset_info_form_items,
      );
    });
  });
}
// export function checkMahindraCustomer(){
//     let special_customers=[1140],client_id=parseInt(this.props.client_id),vendor_id=parseInt(this.state.vendor_id);
//     return special_customers.some(item=>[client_id,vendor_id].includes(item))
// }

export function checkMachineInfoInQuickAdd(fieldName) {
  const client_id = parseInt(this.props.client_id);
  let vendor_id = parseInt(this.state.vendor_id);
  const parent_vendor_id = parseInt(this.state.parent_vendor_id);
  const quickAddMachineInfo = CONSTANTS.quick_add_machine_info[this.state.allFields?.thingType] || CONSTANTS.quick_add_machine_info[this.state.thing_details?.category];
  if (
    (isNaN(vendor_id) || isNaN(parent_vendor_id)) &&
    parseInt(this.props.vendor_id) !== 1
  ) {
    vendor_id = parseInt(this.props.vendor_id);
  }
  if (fieldName === "get_fields_array") {
    if (!quickAddMachineInfo) return [];
    return Object.keys(quickAddMachineInfo).filter((item) => {
      return (
        quickAddMachineInfo[item] === "*" ||
        quickAddMachineInfo[item].some((item) =>
          [client_id, vendor_id, parent_vendor_id].includes(item),
        )
      );
    });
  }

  return (
    quickAddMachineInfo?.[fieldName] === "*" ||
    quickAddMachineInfo?.[fieldName]?.some((item) =>
      [client_id, vendor_id, parent_vendor_id].includes(item),
    )
  );
}

export function checkAurassureCustomer() {
  return [
    this.props.client_id,
    this.props.vendor_id,
    this.state.vendor_id,
    this.state.parent_vendor_id,
    this.state.allFields?.vendorName,
  ].includes(1819);
}

export function getMandatoryFormJson(data, removeItems = [], thingType) {
  let json;
  let form_items = [];
  const getMandatoryFields = (item) => {
    // console.log(item,item.props);
    // alert(this.state.vendor_id)
    // alert(this.props.client_id)
    if (
      item &&
      item.props &&
      item.props.name &&
      item.props.label &&
      (item.props.label.includes("*") ||
        item.props.label === "Make" ||
        item.props.label === "Model" ||
        (thingType === 73 && item.props.name === "commissioning_date"))
    ) {
      if (
        (!removeItems?.length || !removeItems.includes(item.props.name)) &&
        !form_items.find(
          (fi) =>
            fi.props &&
            fi.props.name &&
            item.props &&
            item.props.name &&
            fi.props.name === item.props.name,
        )
      ) {
        form_items.push(item);
      }
      return;
    } else if (Object.values(item).length) {
      Object.values(item).forEach((val) => {
        if (typeof val == "object") {
          getMandatoryFields(val);
        }
      });
    }
  };
  getMandatoryFields(data);
  if (form_items.length) {
    json = {
      Form: {
        compo: "Form",
        state: {},
        props: {
          name: "machineInfoMandatoryForm",
          className: "machineInfoMandatoryForm",
          onValuesChange:
            "(changedFields, allFields) => { if(allFields){console.log(allFields)}}",
          ref: "context.formRef",
        },
        child: {},
      },
    };
    form_items.forEach((formItem, ind) => {
      json.Form.child["selectItem" + ind] = formItem;
    });
    this.setState({ mandatory_json: json }, () => {
      console.log("maniid", this.state.mandatory_json, form_items);
    });
  }
}
//get if GPS is available for selected device
export function getDeviceGpsStatus(deviceId, all_devices_list) {
  let is_gps_available = false,
    is_assigned_device = false,
    device_details = {};
  // get device details
  let { all_devices, device_type } = this.state;
  if (all_devices_list) {
    all_devices = all_devices_list;
  }
  all_devices.Assigned.forEach((device) => {
    if (parseInt(device.id) === parseInt(deviceId)) {
      device_details = device;
    }
  });
  if (!is_assigned_device) {
    all_devices.Unassigned.forEach((device) => {
      if (parseInt(device.id) === parseInt(deviceId)) {
        device_details = device;
      }
    });
  }
  // get type_id and modal
  let { type_id, product_model } = device_details;
  if (CONSTANTS.gps_devices[type_id]) {
    if (
      Array.isArray(CONSTANTS.gps_devices[type_id]) &&
      product_model &&
      product_model !== ""
    ) {
      is_gps_available =
        CONSTANTS.gps_devices[type_id].indexOf(product_model) > -1;
    } else {
      is_gps_available = true;
    }
  }
  return is_gps_available;
}
//get device is assigned or unassigned
export function getDeviceType(eachDeviceId, all_device_list) {
  let device_type = "",
    type_id = 0,
    device_details,
    { all_devices } = this.state;
  const { devices_options, unassigned_devices_options } = this.state;
  if (all_device_list) {
    all_devices = all_device_list;
  }
  all_devices.Assigned.forEach((device) => {
    if (parseInt(device.id) === parseInt(eachDeviceId)) {
      device_type = "assigned";
      type_id = device.type;
    }
  });
  all_devices.Unassigned.forEach((device) => {
    if (parseInt(device.id) === parseInt(eachDeviceId)) {
      device_type = "unassigned";
      type_id = device.type;
    }
  });
  console.log({ device_type, type_id });
  return { device_type, type_id };
}

export async function getProtocolDetails(id, type) {
  let {
    protocol_details,
    system_template_details,
    isThingAdd,
    app_id_thing_type,
    quick_add,
  } = this.state;
  let customerId = this.state.customer_id,
    thingType = this.state.allFields.thingType;
  let application_id = app_id_thing_type[thingType] || 16;
  if (isThingAdd || quick_add) {
    customerId = this.state.allFields.customerName;
  }
  if (this.state.is_rental) {
    customerId = this.props.client_id;
  }
  if (type == "template") {
    if (
      system_template_details &&
      system_template_details[id] &&
      system_template_details[id].status === "success"
    ) {
      return system_template_details[id];
    } else {
      let templateAPIdata = await getTemplateData(
        customerId,
        application_id,
        this.state.allFields.thingType,
        parseInt(id),
      );
      if (!system_template_details) {
        system_template_details = {};
      }
      if (templateAPIdata.status === "success") {
        system_template_details[id] = templateAPIdata;
        this.setState({ system_template_details });
      }
      return templateAPIdata;
    }
  } else {
    if (
      protocol_details &&
      protocol_details[id] &&
      protocol_details[id].status === "success"
    ) {
      return protocol_details[id];
    } else {
      let protocolData = await getProtocolData(
        customerId,
        application_id,
        parseInt(id),
      );
      if (!protocol_details) {
        protocol_details = {};
      }
      if (protocolData.status === "success") {
        protocol_details[id] = protocolData;
        this.setState({ protocol_details });
      }
      return protocolData;
    }
  }
}

export function setParamsFromSystemTemplate(
  selectedParams,
  active_system,
  is_first_render,
  systemDetails,
) {
  let { system_details, quick_add } = this.state;
  let { unassigned_params } = this.state;
  let default_params = {
      general: [],
      fault: [],
    },
    device = this.state.device_config_values[0].devices,
    param_types = ["general", "fault"];
  if (!system_details) {
    system_details = {};
  }
  if (!system_details[device]) {
    system_details[device] = {};
  }
  if (!system_details[device][active_system[device]]) {
    system_details[device][active_system[device]] = {};
  }
  if (systemDetails) {
    system_details[device] = systemDetails;
  }
  let current_system_details =
    system_details[device][active_system[device]] || {};
  console.log(system_details, system_details[device], active_system[device]);
  // return;

  //get all params assigned to all systems
  let { all_general_params, all_fault_params } = getAllParamsFromAllSystems(
    system_details[device],
    unassigned_params,
  );

  // get add selected params from template and filter if location is there
  // add to default params only if that param is not added in any other system
  if (selectedParams.general.find((item) => item == "lat" || item == "long")) {
    selectedParams.general = selectedParams.general.filter(
      (item) => item !== "lat" || item !== "long",
    );
    // selectedParams.general.push(location_param.destination_key);
  }

  default_params.general = this.state.all_params.general_parameters.filter(
    (item) =>
      selectedParams.general.indexOf(item.destination_key) >= 0 &&
      all_general_params.indexOf(item.destination_key) === -1,
  );
  default_params.fault = this.state.all_params.fault_parameters.filter(
    (item) =>
      selectedParams.fault.indexOf(item.destination_key) >= 0 &&
      all_fault_params.indexOf(item.destination_key) === -1,
  );

  console.log({ all_general_params, all_fault_params });

  //add params to assigned section of selected system
  current_system_details.assigned = default_params;
  system_details[device][active_system[device]] = current_system_details;

  // get all params that are selected in system
  let checked_params = {},
    system_params = {};
  Object.keys(system_details).forEach((device_id) => {
    if (!system_params) {
      system_params = {};
    }
    param_types.forEach((type) => {
      if (!checked_params[type]) {
        checked_params[type] = [];
      }
      if (!system_params[type]) {
        system_params[type] = [];
      }
    });

    Object.keys(system_details[device_id]).forEach((system_id) => {
      let curr_sys_details = system_details[device_id][system_id];
      console.log("mani after1", curr_sys_details, system_params);
      param_types.forEach((type) => {
        if (
          curr_sys_details &&
          curr_sys_details["assigned"] &&
          curr_sys_details["assigned"][type] &&
          Array.isArray(curr_sys_details["assigned"][type])
        ) {
          checked_params[type] = [
            ...checked_params[type],
            ...curr_sys_details["assigned"][type],
          ];
          curr_sys_details["assigned"][type].forEach((param) => {
            system_params[type].push(param.destination_key);
          });
        }
      });
    });
  });
  // let all_analyser_params = getAllCheckedParams(checked_params);
  // console.log({checked_params})
  console.log("mani after", selectedParams, system_params);
  let statetoUpdate = { system_params };
  if (!is_first_render) {
    statetoUpdate["checked_params"] = checked_params;
  }
  if (quick_add || systemDetails) {
    statetoUpdate["system_details"] = system_details;
  }
  this.setState(statetoUpdate, () => {
    if (!is_first_render) {
      if (this.deviceConfigRef && this.deviceConfigRef.current) {
        // this.deviceConfigRef.current.setState({system_details:system_details[device]})
      }
    }
    // this.modifySystemParams(device)
  });
}

//get all params assigned to all systems and unassinged
export function getAllParamsFromAllSystems(system_details, unassigned_params) {
  let all_general_params = [],
    all_fault_params = [],
    priorities = {};
  console.log("detailed", system_details, Object.values(system_details));
  if (system_details && Object.keys(system_details).length) {
    Object.entries(system_details).forEach(([sys_id, detail]) => {
      priorities[sys_id] = detail.priority || 0;
    });

    Object.values(system_details).forEach((detail) => {
      if (detail["assigned"] && detail["assigned"]["general"]) {
        console.log("detailed", detail["assigned"]["general"]);
        all_general_params = [
          ...all_general_params,
          ...detail["assigned"]["general"].map((item) => item.destination_key),
        ];
      }
      if (detail["assigned"] && detail["assigned"]["fault"]) {
        all_fault_params = [
          ...all_fault_params,
          ...detail["assigned"]["fault"].map((item) => item.destination_key),
        ];
      }
    });
  }

  if (unassigned_params && unassigned_params["general"]) {
    all_general_params = [
      ...all_general_params,
      ...unassigned_params["general"].map((item) => item.destination_key),
    ];
  }
  if (unassigned_params && unassigned_params["fault"]) {
    all_fault_params = [
      ...all_fault_params,
      ...unassigned_params["fault"].map((item) => item.destination_key),
    ];
  }
  return { all_general_params, all_fault_params, priorities };
}

export function modifySystemParams() {
  let system_details = JSON.parse(window.localStorage.getItem("data"));
  let max_priority = 0,
    curr_priority,
    updated_system_details = {};
  Object.values(system_details).forEach((detail) => {
    if (detail.priority && detail.priority > max_priority) {
      max_priority = detail.priority;
    }
  });
  curr_priority = max_priority;
  while (curr_priority >= 0) {
    Object.entries(system_details).forEach(([key, detail]) => {
      if (typeof detail.priority === "undefined") {
        detail.priority = 0;
      }
      if (detail.priority === max_priority) {
        curr_priority = curr_priority - 1;
        updated_system_details[key] = detail;
      }
    });
  }
  console.log("modify", system_details, updated_system_details);
}
export function getDataPushDetails() {
  let { spcb_data_push_details, cpcb_data_push_details, analyser_details } =
    this.state;
  let allParamKeys = [];

  let analyzers = {},
    srno = "",
    trimmed_data = {};
  try {
    analyser_details?.forEach((analyser) => {
      analyser.params?.forEach((param) => {
        allParamKeys.push(param);
      });
    });
    console.log(
      "spcb_data_push_details",
      spcb_data_push_details,
      cpcb_data_push_details,
    );
    spcb_data_push_details.parameter_details =
      spcb_data_push_details.parameter_details?.filter(
        (detail) =>
          detail &&
          detail.name &&
          detail.name !== "" &&
          allParamKeys.indexOf(detail.name) > -1,
      );
      analyser_details?.forEach((analyser) => {
        srno = analyser.serial_no;
      // console.log("mani serial",cpcb_data_push_details['analyzers'][srno])
      if (
        typeof cpcb_data_push_details["analyzers"]?.[srno] == "string" &&
        cpcb_data_push_details["analyzers"][srno] !== ""
      ) {
        analyzers[srno] = cpcb_data_push_details["analyzers"][srno].trim();
      }
    });
    Object.keys(spcb_data_push_details).forEach((key) => {
      if (typeof spcb_data_push_details[key] == "string") {
        spcb_data_push_details[key] = spcb_data_push_details[key].trim();
      } else if (Array.isArray(spcb_data_push_details[key])) {
        spcb_data_push_details[key].forEach((arrayItem, index) => {
          trimmed_data = {};
          if (typeof arrayItem === "object") {
            Object.keys(arrayItem).forEach((objKey) => {
              if (typeof arrayItem[objKey] == "string") {
                trimmed_data[objKey] = arrayItem[objKey].trim();
              } else {
                trimmed_data[objKey] = arrayItem[objKey];
              }
            });
            spcb_data_push_details[key][index] = trimmed_data;
          }
        });
      }
    });
    Object.keys(cpcb_data_push_details).forEach((key) => {
      if (typeof cpcb_data_push_details[key] == "string") {
        cpcb_data_push_details[key] = cpcb_data_push_details[key].trim();
      }
    });
  } catch (e) {
    console.log("dataPushDetails error", e);
  }

  let returnObj = {
    spcb_data_push_details,
    cpcb_data_push_details: {
      ...cpcb_data_push_details,
      analyzers: analyzers,
    },
  };
  return returnObj;
}

export async function getAssetTags(thing) {
  let tags = [],
    templateAPIdata,
    current_template,
    { system_template_details } = this.state;
  // if no changes made to system and tags already exists
  if (
    Object.keys(system_template_details).length === 0 &&
    thing?.tags?.length
  ) {
    return Array.isArray(thing.tags) ? thing.tags : [];
  }

  let all_template_ids = [];
  thing.configurations.forEach((config) => {
    let template_id = parseInt(config?.system_details?.template_id);
    if (!isNaN(template_id)) {
      all_template_ids.push(template_id);
    }
  });
  for await (const each_template_id of all_template_ids) {
    templateAPIdata = await this.getProtocolDetails(
      each_template_id,
      "template",
    );
    if (templateAPIdata.status === "success") {
      current_template = JSON.parse(
        JSON.stringify(templateAPIdata.template_details),
      );
      if (Array.isArray(current_template?.thing_tags)) {
        current_template.thing_tags.forEach((thing_tag) => {
          if (!tags.includes(thing_tag)) {
            tags.push(thing_tag);
          }
        });
      }
    }
  }
  console.log({ all_template_ids, tags });
  return tags;
}

export function getDigitalDisplayConfigData() {
  try {
    let digitalDisplayConfigData =
      this?.digitalDisplayRef?.current?.getFieldsValue();

    digitalDisplayConfigData = digitalDisplayConfigData
      ? digitalDisplayConfigData
      : this.state.thing_details?.thing_details?.display_configs;

    digitalDisplayConfigData?.advanceMode === false &&
      digitalDisplayConfigData?.pages.forEach((page) => {
        page.page_timeout = digitalDisplayConfigData.page_timeout;
      });

    const calculateYAxis = (fontSize, height, totalLines, lineNumber) => {
      if (fontSize === "small") {
        return totalLines == height * 2
          ? lineNumber * 16
          : ((height * 32) % 20) / 2 + lineNumber * 20;
      } else {
        return lineNumber * 32;
      }
    };

    const fontSize = digitalDisplayConfigData?.font_size;
    const height = digitalDisplayConfigData?.height;
    const totalLines = digitalDisplayConfigData?.total_lines;

    digitalDisplayConfigData?.pages.forEach((page) => {
      page?.lines.forEach((line) => {
        line.y_axis = calculateYAxis(
          fontSize,
          height,
          totalLines,
          line.line_number,
        );
      });
    });
    return digitalDisplayConfigData;
  } catch (e) {}
}

export async function saveThingsData(check_serial_no) {
  let that = this;
  let formValues = this.state.allFields,
    system_details = this.state.system_details;
  let {
    thing_templates,
    all_analyser_params,
    extra_machine_info_data,
    thing_details,
    quick_add,
    param_settings,
    isThingAdd,
    convertion_fields,
  } = this.state;
  // console.log({ system_details });
  let param_config = getParamConfiguration(system_details);
  console.log({ param_config });

  if (
    isThingAdd &&
    check_serial_no !== "no" &&
    ((formValues.engine_sl_no && formValues.engine_sl_no != "") ||
      (formValues.genset_sl_no && formValues.genset_sl_no != ""))
  ) {
    let isLinked = await this.checkSerialNo();
    if (isLinked) {
      this.setState({ linked_sl_no: true });
      return;
    }
  }

  let assetType = _.find(this.state.things_categories, {
    id: formValues.thingType,
  });

  try {
    if (isThingAdd) {
      mixPanelTrackUser("Asset Added", { "Asset Type": assetType.name });
    } else {
      mixPanelTrackUser("Asset Configured", { "Asset Type": assetType.name });
    }

    this.setState({ loading: true });
    let {
      checked_params,
      all_params,
      device_config_values,
      template_details,
      device_type,
      all_param_details,
      vendor_id,
      device_config_tab,
      machine_info_values,
      wrd_data_push_details,
    } = this.state;
    let application_id = parseInt(this.props.application_id);
    let machineInfoValues = {},
      thing_name = "",
      that = this;
    let dataToSave = {};
    let parameterData = [],
      thingTemplate = formValues.thingTemplate,
      configurationData = [];
    thing_name = formValues.thingName;

    const digitalDisplayConfigData = this.getDigitalDisplayConfigData();

    machineInfoValues = {
      ...machine_info_values.mandatory,
      ...machine_info_values.basic,
      ...machine_info_values.advanced,
      ...machine_info_values.remote_control,
      ...convertion_fields,
      ...extra_machine_info_data,
      adr_city: formValues.adr_city,
      adr_province: formValues.adr_province,
      adr_country_code: formValues.adr_country_code,
      adr_pin_code: formValues.adr_pin_code,
    };
    if (
     this.checkAurassureCustomer()
    ) {
      machineInfoValues.gcp_push = formValues.gcp_push ? 1 : 0;
    }
    moment_convertions.forEach((item) => {
      if (
        machineInfoValues[item] &&
        typeof machineInfoValues[item] === "object"
      ) {
        machineInfoValues[item] = machineInfoValues[item].toISOString();
      } else if (
        machineInfoValues[item] &&
        moment(machineInfoValues[item], "X", true).isValid()
      ) {
        machineInfoValues[item] = moment(
          machineInfoValues[item],
          "X",
        ).toISOString();
      }
    });
    if (
      this.checkMachineInfoInQuickAdd("commissioning_date") &&
      formValues.commissioning_date
    ) {
      machineInfoValues["commissioning_date"] = moment(
        formValues.commissioning_date,
        "X",
      ).toISOString();
    }

    if (this.checkMachineInfoInQuickAdd("capacity") && formValues.capacity) {
      machineInfoValues["capacity"] = parseFloat(formValues.capacity);
    }

    if (this.checkMachineInfoInQuickAdd("serial") && formValues.serial) {
      machineInfoValues["serial"] = formValues.serial;
    }

    if (this.checkMachineInfoInQuickAdd("type") && formValues.type) {
      machineInfoValues["type"] = formValues.type;
    }
    // console.log({machineInfoValues,convertion_fields})
    // return;
    if (!thing_name || thing_name === "") {
      if (formValues.engine_sl_no && formValues.engine_sl_no != "") {
        thing_name = "ESN-" + formValues.engine_sl_no;
      } else if (formValues.genset_sl_no && formValues.genset_sl_no != "") {
        thing_name = "GSN-" + formValues.genset_sl_no;
      }
    }
    if (
      parseInt(formValues.thingType) == 18 &&
      !quick_add &&
      (!thingTemplate || !formValues.is_iot_enabled) &&
      !that.checkProperConfig()
    ) {
      this.showLoading();
      if (formValues.is_iot_enabled) {
        machineInfoValues["engine_sl_no"] = formValues.engine_sl_no || "";
        machineInfoValues["genset_sl_no"] = formValues.genset_sl_no || "";
      }
      const thingDetailsOld =
        !isThingAdd && thing_details?.thing_details
          ? thing_details.thing_details
          : {};
      dataToSave = {
        things: [
          {
            name: thing_name,
            latitude: formValues.latitude,
            longitude: formValues.longitude,
            category: formValues.thingType,
            vendor_id:
              that.state.is_rental && application_id === 12
                ? formValues.customerName
                : formValues.vendorName || vendor_id,
            offline_timeout: isThingAdd
              ? ""
              : thing_details.offline_timeout || "",
            parameter_details: isThingAdd
              ? {}
              : thing_details.parameter_details || {},
            address: formValues.address || "",
            configurations: configurationData,
            thing_details: {
              ...thingDetailsOld,
              ...machineInfoValues,
              display_configs: digitalDisplayConfigData,
            },
            is_rental: that.state.is_rental,
            is_iot_enabled: isThingAdd ? 1 : formValues.is_iot_enabled ? 1 : 0,
            territory_id: formValues.territory_id || undefined,
          },
        ],
      };
      // return;
      let app_id = 16,
        customer_id = formValues.customerName;
      if (this.state.is_rental) {
        Object.keys(this.state.app_id_thing_type).find((id) => {
          if (
            this.state.app_id_thing_type[id].includes(
              parseInt(formValues.thingType),
            )
          ) {
            app_id = id;
            return true;
          }
        });
        customer_id =
          application_id === 12 ? customer_id : this.props.client_id;
      }
      let saveThingData = {};
      if (isThingAdd) {
        saveThingData = await addThing(
          dataToSave,
          getCustomerValue(customer_id, that),
          app_id || this.state.app_id_thing_type[formValues.thingType],
        );
      } else {
        saveThingData = await saveThingConfigsNew(
          this.state.customer_id,
          this.state.application_id,
          this.state.thing_id,
          dataToSave.things[0],
        );
      }

      if (saveThingData.status === "success") {
        openNotification(
          "success",
          `Thing ${isThingAdd ? "Add" : "Configur"}ed successfully`,
        );
        if (isThingAdd) {
          that.goBackToList();
        } else {
          that.setState({ edit_mode: false });
        }
      } else {
        openNotification("error", saveThingData.message);
      }
      this.hideLoading();
      return;
    }
    console.log({ machineInfoValues });
    //check mandatory fields for machineinfo
    if (
      parseInt(formValues.thingType) === 18 &&
      (typeof machineInfoValues.kva === "undefined" ||
        machineInfoValues.kva === "" ||
        typeof machineInfoValues.capacity === "undefined" ||
        machineInfoValues.capacity === "" ||
        !machineInfoValues.dg_type ||
        machineInfoValues.dg_type === "")
    ) {
      openNotification("error", "Mandatory Fields are not filled");
      this.hideLoading();
      return;
    }
    if (thingTemplate && parseInt(formValues.thingType) === 18) {
      machineInfoValues["engine_sl_no"] = formValues.engine_sl_no || "";
      machineInfoValues["genset_sl_no"] = formValues.genset_sl_no || "";
    }

    //for assigning un-assigned devices
    // device_type = 'unassigned'
    let deviceLists = [];
    if (Array.isArray(device_config_values)) {
      device_config_values.forEach((item) => {
        let deviceId = item.devices;
        if (device_type[deviceId]?.["device_type"] == "unassigned") {
          deviceLists.push({
            id: deviceId,
            serial_no: that.getTabName({}, deviceId),
          });
        }
      });
    }
    console.log("mani-device", deviceLists, device_type);
    if (deviceLists.length) {
      let request_data = {
        device_ids: deviceLists,
        customer_id: formValues.customerName,
        application_id: formValues.applicationName || 16,
        device_vendor_id: formValues.vendorName,
        convert_device_to_things: this.props.client_id == 392,
      };
      console.log({ request_data });
      // return;
      if (this.state.is_rental) {
        request_data.customer_id =
          application_id === 12
            ? formValues.customerName
            : that.props.client_id;
        request_data.application_id = that.state.selected_application;
      }
      let assignDeviceResponse = await assignDevice(
        request_data,
        application_id === 12 ? formValues.customerName : that.props.client_id,
      );
      if (assignDeviceResponse.status !== "success") {
        openNotification("error", assignDeviceResponse.message);
        that.hideLoading();
        return;
      } else {
        // device is assigned change status
        deviceLists.forEach((assigned_device) => {
          device_type[assigned_device.id]["device_type"] = "assigned";
        });
        this.setState({ device_type });
      }

      //{
      //   "6917": {
      //     "device_type": "unassigned"
      //   }
      // }
    }
    let all_selected_params = getAllCheckedParams(checked_params);
    if (Array.isArray(all_selected_params)) {
      all_selected_params = all_selected_params.map(
        (item) => item.destination_key,
      );
    }
    if (all_selected_params.indexOf(location_param.destination_key) > -1) {
      // all_selected_params=[...all_selected_params,'lat','long']
    }
    console.log("mani-issue", system_details);
    let valid_system_details = true,
      are_location_params_added = false;
    if (all_params && system_details) {
      let derived_params = this.checkAllDerviredParams();
      Object.keys(system_details).forEach((sourceId) => {
        let all_system_details = [];
        if (system_details && system_details[sourceId]) {
          let template_array = [];
          template_array = getModifiedSystemDetails(system_details[sourceId]);
          all_system_details = template_array;
        } else if (thingTemplate && thingTemplate !== "custom") {
          all_system_details = thing_templates[thingTemplate].system_details;
        } else if (thingTemplate === "custom") {
          all_system_details = system_details[sourceId];
        }
        if (
          !Array.isArray(all_system_details) &&
          typeof all_system_details === "string"
        ) {
          valid_system_details = false;
          openNotification("error", all_system_details);
          return;
        }
        console.log("maniii", { all_system_details });
        all_system_details.forEach((system, ind) => {
          let eachConfig = {};
          eachConfig["source_id"] = parseInt(sourceId) || 0;
          eachConfig["parameters"] = [];
          eachConfig["source_type"] = "device";
          eachConfig["parameters"] = [];
          eachConfig["system_details"] = {};
          let updated_each_system_details = JSON.parse(JSON.stringify(system));
          delete updated_each_system_details["general"];
          delete updated_each_system_details["fault"];
          let system_params = [];
          if (Array.isArray(system.general)) {
            system_params = [...system_params, ...system.general];
          }
          if (Array.isArray(system.fault)) {
            system_params = [...system_params, ...system.fault];
          }

          // add extra derived parameters to system
          console.log("herq1111", derived_params);
          if (
            derived_params &&
            derived_params[sourceId] &&
            Array.isArray(derived_params[sourceId])
          ) {
            derived_params[sourceId].forEach((derived_param) => {
              if (
                !system_params.find(
                  (sys_param) =>
                    sys_param.destination_key === derived_param.destination_key,
                )
              ) {
                system_params.push(derived_param);
              }
            });
            // system_params=[...system_params,...derived_params[sourceId]]
            delete derived_params[sourceId];
          }
          console.log("herq1112");
          // add location params if gps is available and location params are not added
          if (this.state.is_gps_available && !are_location_params_added) {
            system_params = system_params.concat(...that.state.locationParams);
            are_location_params_added = true;
          }
          let param_details = {},
            param_config_details = {},
            extra_param_details = {},
            config_params = [];
          system_params.forEach((parameterRowData) => {
            param_details = {};
            extra_param_details = {};
            param_config_details = {};
            if (
              param_settings &&
              param_settings[parameterRowData.destination_key]
            ) {
              param_details = param_settings[parameterRowData.destination_key];
            }
            if (
              param_config &&
              param_config[parameterRowData.destination_key]
            ) {
              param_config_details =
                param_config[parameterRowData.destination_key];
            }
            console.log(parameterRowData.destination_key, param_config_details);
            config_params.push({
              ...all_param_details[parameterRowData.destination_key],
              ...parameterRowData,
              source_key: parameterRowData.source_key,
              destination_key: parameterRowData.destination_key,
              name: parameterRowData.name,
              source_unit: parameterRowData.default_unit,
              destination_unit: parameterRowData.default_unit,
              type: parameterRowData.type,
              data_type: parameterRowData.data_type,
              source:
                all_param_details[parameterRowData.destination_key]["source"],
              allowed_visualizations: parameterRowData.allowed_visualizations,
              is_active:
                ["lat", "long"].includes(parameterRowData.destination_key) &&
                are_location_params_added
                  ? 1
                  : all_selected_params.indexOf(
                        parameterRowData.destination_key,
                      ) > -1
                    ? 1
                    : 0,
              ...param_details, /// parameter settings in parameter section
              ...param_config_details, // parameter configuration in device config section
              ...extra_param_details,
            });
          });
          console.log("herq1115");
          // return;
          console.log("herq1114", config_params);
          config_params = config_params.sort((a, b) => {
            return (
              all_param_details[a.destination_key]?.order -
              all_param_details[b.destination_key]?.order
            );
          });
          // config_params=config_params.sort((a,b)=>a.order-b.order)
          eachConfig["parameters"] = config_params;
          eachConfig["system_details"] = updated_each_system_details;
          if (
            this.state.system_template_details?.[system.template_id]
              ?.template_details?.param_values?.fault_register
          ) {
            eachConfig["system_details"].fault_registers =
              this.state.system_template_details[
                system.template_id
              ].template_details.param_values.fault_register;
          }
          if (
            this.state.system_template_details?.[system.template_id]
              ?.template_details?.param_values?.intermediary
          ) {
            eachConfig["system_details"].intermediary_parameters =
              this.state.system_template_details[
                system.template_id
              ].template_details.param_values.intermediary;
          }
          configurationData.push(eachConfig);
        });
      });
    }
    if (!valid_system_details) {
      return;
    }

    // condition to check atleast one paramter is added to each device
    // let validDeviceAndParams = true;
    // if (device_config_values && Array.isArray(device_config_values)) {
    // 	device_config_values.forEach((item) => {
    // 		if (
    // 			!configurationData.find(
    // 				(configurationItem) =>
    // 					parseInt(configurationItem.source_id) ==
    // 					parseInt(item.devices)
    // 			)
    // 		) {
    // 			validDeviceAndParams = false;
    // 		}
    // 	});
    // }
    // if (validDeviceAndParams) {
    // 	validDeviceAndParams = configurationData.every(
    // 		(item) =>
    // 			item.source_id &&
    // 			item.parameters &&
    // 			Array.isArray(item.parameters) &&
    // 			item.parameters.length > 0
    // 	);
    // }
    // if (!validDeviceAndParams) {
    // 	this.openNotification(
    // 		'error',
    // 		'Please select atleast one parameter for each device'
    // 	);
    // 	return;
    // }
    const thingDetailsOld =
      !isThingAdd && thing_details?.thing_details
        ? thing_details.thing_details
        : {};
    let thing = {
      name: thing_name,
      latitude: formValues.latitude,
      longitude: formValues.longitude,
      category: formValues.thingType,
      thing_templates: {
        thing_template: formValues.thingTemplate,
        machine_info_template: that.state.machine_info_template,
      },
      offline_timeout: isThingAdd ? "" : thing_details.offline_timeout || "",
      vendor_id:
        that.state.is_rental && application_id === 12
          ? formValues.customerName
          : formValues.vendorName || vendor_id,
      address: formValues.address || "",
      parameter_details: isThingAdd
        ? {}
        : thing_details.parameter_details || {},
      configurations: configurationData,
      thing_details: {
        ...thingDetailsOld,
        ...machineInfoValues,
        display_configs: digitalDisplayConfigData,
      },
      is_rental: this.state.is_rental,
      is_iot_enabled: quick_add ? 1 : formValues.is_iot_enabled ? 1 : 0,
      territory_id: formValues.territory_id || undefined,
    };

    console.log(thing, "thingabcde");

    if (this.state.is_rental && formValues.rented_to) {
      thing["rented_to"] = formValues.rented_to;
    }
    if (this.checkPollutionApplication()) {
      let part_details = [],
        dataPushDeatils = {};
      this.state.analyser_details.forEach((analyser) => {
        let item = {};
        item.parameters_measured = analyser.params.map((key) => key);
        item.status_flag = analyser.status_flag;
        item.certification = analyser.certification;
        item.make = analyser.make;
        item.model = analyser.model;
        item.serial_no = analyser.serial_no;
        item.vendor = analyser.vendor;
        part_details.push(item);
      });

      dataPushDeatils = this.getDataPushDetails();
      let camera_id = { camera_id: "" };

      if (parseInt(formValues.thingType) == 23) {
        camera_id = {
          camera_id: this.state.camera_id,
        };
      }
      thing.thing_details = {
        ...thing.thing_details,
        parts: part_details,
        ...camera_id,
        ...dataPushDeatils,
      };
      if (
        parseInt(formValues.thingType) == 21 ||
        parseInt(formValues.thingType) == 22 ||
        parseInt(formValues.thingType) == 102
      ) {
        let { calibration } = this.state,
          fileUploadResponse;
        let updated_calibration_details = {},
          uploaded_files = {};
        //get all uploaded files
        for await (const param_key of Object.keys(calibration)) {
          if (!uploaded_files[param_key]) {
            uploaded_files[param_key] = {};
          }
          for await (const fieldKey of Object.keys(uploaded_files[param_key])) {
          }
        }
        for await (const param_key of Object.keys(calibration)) {
          if (!updated_calibration_details[param_key]) {
            updated_calibration_details[param_key] = {};
          }
          for await (const fieldKey of Object.keys(calibration[param_key])) {
            let val = calibration[param_key][fieldKey];
            if (
              fieldKey == "cyl_conc_in_ppm" ||
              fieldKey == "cyl_conc_factor"
            ) {
              val = parseFloat(val);
            } else if (fieldKey == "cyl_channel_no") {
              val = parseInt(val);
            }

            updated_calibration_details[param_key][fieldKey] = val;
          }
          // converting check_duration to seconds
          let check_duration = 0;
          if (parseInt(calibration[param_key]["check_duration_min"]) >= 0) {
            check_duration =
              check_duration +
              parseInt(calibration[param_key]["check_duration_min"] * 60);
          }
          if (parseInt(calibration[param_key]["check_duration_sec"]) >= 0) {
            check_duration =
              check_duration +
              parseInt(calibration[param_key]["check_duration_sec"]);
          }
          if (check_duration) {
            updated_calibration_details[param_key]["check_duration"] =
              check_duration;
          }
          console.log("here3333-1");

          delete updated_calibration_details[param_key]["check_duration_min"];
          delete updated_calibration_details[param_key]["check_duration_sec"];
        }

        thing.thing_details = {
          ...thing.thing_details,
          calibration: updated_calibration_details,
        };
      }
    }
    //wrd details for flow meter
    if (parseInt(formValues.thingType) === 86) {
      thing.thing_details = {
        ...thing.thing_details,
        wrd_data_push_details: wrd_data_push_details,
      };
    }

    // add config_mode in thing details
    thing.thing_details = {
      ...thing.thing_details,
      config_mode: device_config_tab,
    };

    // add tags for asset
    thing["tags"] = await this.getAssetTags(thing);
    if (!thingTemplate && parseInt(formValues.thingType) !== 18) {
      delete thing["thing_templates"];
    }
    dataToSave = {
      things: [thing],
    };
    // return;
    let app_id = 16,
      customer_id = formValues.customerName;
    if (this.state.is_rental) {
      Object.keys(this.state.app_id_thing_type).find((id) => {
        if (
          this.state.app_id_thing_type[id].includes(
            parseInt(formValues.thingType),
          )
        ) {
          app_id = id;
          return true;
        }
      });
      customer_id = application_id === 12 ? customer_id : that.props.client_id;
    }
    let saveThingsData = {};
    if (isThingAdd) {
      saveThingsData = await addThing(
        dataToSave,
        getCustomerValue(customer_id, that),
        app_id || this.state.app_id_thing_type[formValues.thingType],
      );
    } else {
      saveThingsData = await saveThingConfigsNew(
        this.state.customer_id,
        this.state.application_id,
        this.state.thing_id,
        thing,
      );
    }
    // return;
    if (saveThingsData.status === "success") {
      openNotification(
        "success",
        `Asset ${isThingAdd ? "Add" : "Configur"}ed successfully`,
      );
      if (
        this.state.device_config_values &&
        Array.isArray(this.state.device_config_values) &&
        this.state.device_config_values.length &&
        this.state.device_config_values[0].devices &&
        that.socket &&
        that.socket.emit
      ) {
        this.state.device_config_values.forEach((item) => {
          that.socket.emit(
            "sync_settings_to_device",
            JSON.stringify({
              device_id: item.devices,
            }),
          );
        });
      }
      if (isThingAdd) {
        that.goBackToList();
      } else {
        const machine_info_values = this.state.machine_info_values;
        if (machine_info_values) {
          Object.keys(that.state.machineInfoDateValues || {}).forEach(
            (formKey) => {
              if (!machine_info_values[formKey]) {
                machine_info_values[formKey] = {};
              }
              machine_info_values[formKey] = {
                ...machine_info_values[formKey],
                ...that.state.machineInfoDateValues[formKey],
              };
            },
          );
        }
        that.setState({ edit_mode: false, machine_info_values });
      }
    } else {
      openNotification("error", saveThingsData.message);
    }
  } catch (e) {
    console.log("error-some error", e, typeof e);
    openNotification("error", "Something Went Wrong");
  } finally {
    this.setState({ loading: false });
  }
}

function getCustomerValue(customer_id, that) {
  if (
    // that.isCustomerOptional &&
    that.state.assetWithoutCustomer
    // &&
    // !this.state.is_rental
    //  &&
    // that.state.allFields?.customerName === that.state.allFields?.vendorName
  ) {
    return undefined;
  }
  return customer_id;
}
function randomString(length) {
  let chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
  var result = "";
  for (var i = length; i > 0; --i)
    result += chars[Math.floor(Math.random() * chars.length)];
  return result;
}

function uploadFiletoS3(fileUploadResponse, file) {
  const promise = new Promise((resolve, reject) => {
    let data = new FormData();
    // data.append('user', 'hubot');
    // data.append('Content-Type', 'multipart/form-data');
    let file_ext = file.name.split(".")[1];
    // data.append('key', that.file_for_upload.file_path);
    let file_path =
      "datoms/device-management/firmwares/image/" +
      file.name.split(".")[0] +
      "-" +
      randomString(10) +
      "." +
      file_ext;
    data.append("key", file_path);
    console.log("test_s3_data_ file_path", file_path);
    data.append(
      "X-Amz-Credential",
      fileUploadResponse.data.fields["X-Amz-Credential"],
    );
    data.append(
      "X-Amz-Algorithm",
      fileUploadResponse.data.fields["X-Amz-Algorithm"],
    );
    data.append("X-Amz-Date", fileUploadResponse.data.fields["X-Amz-Date"]);
    data.append(
      "X-Amz-Security-Token",
      fileUploadResponse.data.fields["X-Amz-Security-Token"],
    );
    data.append("Policy", fileUploadResponse.data.fields["Policy"]);
    data.append(
      "X-Amz-Signature",
      fileUploadResponse.data.fields["X-Amz-Signature"],
    );
    data.append("file", file);
    var xhr = new XMLHttpRequest();
    xhr.withCredentials = true;
    let s3_url = "https://s3.ap-south-1.amazonaws.com/datoms-files-storage/";
    xhr.open("POST", s3_url);
    xhr.setRequestHeader("cache-control", "no-cache");
    xhr.setRequestHeader("Access-Control-Allow-Origin", "*");
    xhr.setRequestHeader("Access-Control-Allow-Credentials", false);
    xhr.setRequestHeader("Access-Control-Allow-Headers", "x-requested-with");
    xhr.setRequestHeader("Access-Control-Allow-Methods", "POST");
    xhr.setRequestHeader(
      "Access-Control-Allow-Origin",
      "http://127.0.0.1:9090",
    );
    xhr.setRequestHeader(
      "Access-Control-Allow-Origin",
      "https://datoms.phoenixrobotix.com",
    );
    xhr.setRequestHeader("crossorigin", "anonymous");
    xhr.send(data);
    xhr.onreadystatechange = () => {
      if (xhr.readyState === 4) {
        if (xhr.status === 200 || xhr.status === 204) {
          console.log("file-path", file_path);
          resolve({ name: file.name, link: s3_url + file_path });
        } else {
          openNotification("error", "Upload Failed!");
          // resolve({name:file.name,link:'https://s3.ap-south-1.amazonaws.com/datoms-files-storage/datoms/device-management/firmwares/image/upload-XCX6IktEh4.svg'})
          reject("error", "upload failed");
          // that.handleSubmitContact(single);
        }
      }
    };
  });

  return promise;
}
export function getParamConfiguration(system_details) {
  let param_config = {};
  const findParamConfig = (obj, key) => {
    let eachSystemDetail = obj;
    let paramForms = ["param_setting", "param_values"];
    paramForms.forEach((formName) => {
      if (
        eachSystemDetail &&
        eachSystemDetail[formName] &&
        eachSystemDetail[formName] &&
        eachSystemDetail[formName]["general"]
      ) {
        Object.keys(eachSystemDetail[formName]["general"]).forEach(
          (paramKey) => {
            if (!param_config[paramKey]) {
              param_config[paramKey] =
                eachSystemDetail[formName]["general"][paramKey];
            } else {
              param_config[paramKey] = {
                ...param_config[paramKey],
                ...eachSystemDetail[formName]["general"][paramKey],
              };
            }
          },
        );
        // param_config=eachSystemDetail['param_setting']['general']
      }
      if (eachSystemDetail?.[formName]?.["fault"]) {
        console.log("detail -> ",eachSystemDetail[formName]);
        Object.keys(eachSystemDetail[formName]["fault"]).forEach(
          (paramKey) => {
            if (!param_config[paramKey]) {
              param_config[paramKey] =
                eachSystemDetail[formName]["fault"][paramKey];
            } else {
              param_config[paramKey] = {
                ...param_config[paramKey],
                ...eachSystemDetail[formName]["fault"][paramKey],
              };
            }
          },
        );
      }
    });

    // if (key == 'general') {
    // 	console.log("maniii system4",obj[key])
    // 	param_config = { ...param_config, ...obj[key] };
    // } else {
    // 	if (obj[key] && typeof obj[key] === 'object' && Object.keys(obj[key]) && Object.keys(obj[key])[0]){
    // 		Object.keys(obj[key]).forEach(insideKey=>{
    // 			findParamConfig(obj[key], insideKey);
    // 		})
    // 	}
    // }
  };
  // if (system_details && typeof system_details === 'object') {
  //     Object.keys(system_details).forEach((key) => {
  //         findParamConfig(system_details, key);
  //     });
  // }

  if (system_details && typeof system_details === "object") {
    Object.keys(system_details).forEach((key) => {
      if (system_details[key] && typeof system_details[key] === "object") {
        Object.keys(system_details[key]).forEach((each_system_key) => {
          if (system_details[key][each_system_key]) {
            findParamConfig(
              system_details[key][each_system_key],
              each_system_key,
            );
          }
        });
      }
    });
  }
  return param_config;
}

export async function checkSerialNo() {
  let formValues = this.state.allFields;
  let request_data = {};
  if (formValues.engine_sl_no && formValues.engine_sl_no != "") {
    request_data["engine_serial_no"] = formValues.engine_sl_no;
  }
  if (formValues.genset_sl_no && formValues.genset_sl_no != "") {
    request_data["genset_serial_no"] = formValues.genset_sl_no;
  }
  let checkSlNo = await checkEngineGensetSerialNo(
    formValues.customerName,
    formValues.applicationName || 16,
    request_data,
  );
  if (checkSlNo.status === "success") {
    return checkSlNo.is_linked;
  }
  return false;
}

export function showLoading() {
  this.setState({ loading: true });
}

export function hideLoading() {
  this.setState({ loading: false });
}

export function checkProperConfig(allFields) {
  let formValues = allFields || this.state.allFields;
  if (parseInt(formValues.thingType) !== 18) {
    if (!(formValues.thingName && formValues.thingName !== "")) {
      return true;
    }
    return false;
  }
  if (
    !(
      (formValues.thingName && formValues.thingName !== "") ||
      (formValues.genset_sl_no && formValues.genset_sl_no !== "") ||
      (formValues.engine_sl_no && formValues.engine_sl_no !== "")
    )
  ) {
    return true;
  }

  return false;
}
export function getTabName(value, deviceId) {
  let id;
  if (deviceId) {
    id = deviceId;
  } else {
    id = Object.values(value)[0];
  }
  let name = "";
  this.state.devices_options.forEach((device) => {
    if (parseInt(device.id) === parseInt(id)) {
      name = device.name;
    }
  });
  if (name == "") {
    this.state.unassigned_devices_options.forEach((device) => {
      if (parseInt(device.id) === parseInt(id)) {
        name = device.name;
      }
    });
  }
  if (this.state.linked_device) {
    name = name + "*";
  }
  return name;
}

export function getAllCheckedParams(checked_params) {
  let all_analyser_params = [];
  if (Array.isArray(checked_params["general"])) {
    all_analyser_params = [
      ...all_analyser_params,
      ...checked_params["general"],
    ];
  }
  if (Array.isArray(checked_params["fault"])) {
    all_analyser_params = [...all_analyser_params, ...checked_params["fault"]];
  }
  return all_analyser_params;
}
export function onSectionChange(key, current) {
  let { active_section } = this.state;
  this.setState({ active_section: key }, () => {
    if (key && key[key.length - 1]) {
      this.setActiveSection(key[key.length - 1]);
    }
  });
}
export function checkAllDerviredParams() {
  let return_obj = {};
  let { activeTab } = this.state;
  if (
    !this.state.all_params ||
    !this.state.checked_params ||
    !this.state.checked_params["general"] ||
    this.state.all_params.derived_parameters.length === 0
  ) {
    return return_obj;
  }
  let checked_params = this.state.checked_params["general"]
    .map((item) => item.destination_key)
    .filter((item) => typeof item !== "undefined");
  console.log("mani-derived2", checked_params);
  let all_params = [];
  if (!checked_params[0] || !checked_params) {
    return return_obj;
  }
  checked_params.forEach((param_obj) => {
    all_params.push(param_obj);
  });

  let devices_params = checked_params;
  return_obj[activeTab] = [];
  this.state.all_params.derived_parameters.forEach((derived_param) => {
    let return_key = true;
    derived_param.derived_from.forEach((dependent_param) => {
      if (all_params.indexOf(dependent_param) === -1) {
        return_key = false;
      }
    });
    if (return_key) {
      return_obj[activeTab].push(derived_param);
    }
  });
  console.log("mani-derived3", return_obj);
  return return_obj;
}

export function getModifiedSystemDetails(all_system_details) {
  let returnObj = [],
    channel_protocols = [8, 9, 10],
    invalid_system_details = false,
    restriction = {};
  let protocol_restrictions = {
    8: { name: "Channel No.", key: "address" },
    9: { name: "Channel No.", key: "address" },
    10: { name: "Channel No.", key: "address" },
    14: { name: "Pin", key: "pin" },
  };
  console.log(all_system_details);
  Object.values(all_system_details).forEach((system_details, index) => {
    if (
      !invalid_system_details &&
      system_details.protocol_config &&
      system_details.protocol_config.protocol &&
      Object.keys(protocol_restrictions).includes(
        system_details.protocol_config.protocol + "",
      )
    ) {
      let all_params_channels = [];
      restriction =
        protocol_restrictions[
          parseInt(system_details.protocol_config.protocol)
        ];
      console.log({ restriction });
      if (system_details.param_values && system_details.param_values.general) {
        all_params_channels = Object.values(
          system_details.param_values.general,
        ).map((item) => item[restriction.key]);
      }
      if (all_params_channels.length) {
        console.log(all_params_channels);
        // let unique_channel_address=all_params_channels.filter((item,ind,arr)=>arr.indexOf(item)===index)
        let unique_channel_address = Array.from(new Set(all_params_channels));
        console.log(unique_channel_address);
        if (
          unique_channel_address.filter((item) => item).length !==
          all_params_channels.filter((item) => item).length
        ) {
          invalid_system_details = true;
        }
      }
    }
  });
  if (invalid_system_details) {
    return (
      "Please Enter Unique " + restriction.name + " for different Parameters"
    );
  }
  Object.values(all_system_details).forEach((system_details, index) => {
    let each_system_detail = {};
    each_system_detail = {
      ...each_system_detail,
      ...(system_details.system_config || {}),
    };
    each_system_detail = {
      ...each_system_detail,
      ...(system_details.helper_parameters || {}),
    };
    each_system_detail = {
      ...each_system_detail,
      ...(system_details.hardware_interface || {}),
    };
    each_system_detail = {
      ...each_system_detail,
      ...(system_details.protocol_config || {}),
    };
    each_system_detail = {
      ...each_system_detail,
      ...(system_details.assigned || []),
    };
    each_system_detail["priority"] = system_details.priority || 0;
    returnObj.push(each_system_detail);
  });
  return returnObj;
}

export function checkPollutionApplication(allFields) {
  let formValues = allFields || this.state.allFields;
  if (!formValues.thingType) return false;
  return (
    parseInt(formValues.thingType) == 1 ||
    parseInt(formValues.thingType) == 21 ||
    parseInt(formValues.thingType) == 22 ||
    parseInt(formValues.thingType) == 102 ||
    parseInt(formValues.thingType) == 23 || 
    parseInt(formValues.thingType) == 89
  );
}
export function showParameterSettings(allFields) {
  return true;
  // let formValues = allFields || this.state.allFields;
  // if (!formValues.thingType) return false;
  // return (
  //     [18].indexOf(parseInt(formValues.thingType))===-1
  // );
}
export function onMachineInfoParamsChange(
  formKey,
  changedFields,
  allFields,
  deletedField,
  fieldStats,
) {
  // return;
  if (allFields && formKey) {
    console.log("many times", changedFields);
    console.log("this function called", formKey);
    let fields = {};
    let getClonedObject = (obj) => {
      return _.cloneDeep(obj);
    };
    let machine_info_values_updated = getClonedObject(
      this.state.machine_info_values,
    );
    let allFieldsClone = getClonedObject(allFields);
    console.log(
      "machine_info_values",
      allFieldsClone,
      machine_info_values_updated[formKey],
    );
    let convertion_fields = {},
      format = "X";
    const machineInfoDateValues = this.state.machineInfoDateValues
      ? getClonedObject(this.state.machineInfoDateValues)
      : {};
    if (!machineInfoDateValues[formKey]) {
      machineInfoDateValues[formKey] = {};
    }
    moment_convertions.forEach((item) => {
      if (allFieldsClone[item]) {
        convertion_fields[item] = moment(
          allFieldsClone[item].format(format),
          format,
        );
        machineInfoDateValues[formKey][item] = allFieldsClone[item].unix();
        delete allFieldsClone[item];
      }
    });
    // console.log("machineInfoDateValues", machineInfoDateValues, convertion_fields)
    // machine_info_values[formKey]= {...allFieldsClone,commissioning_date:convertion_fields}
    // console.log("heree",convertion_fields.commissioning_date.toString())
    machine_info_values_updated[formKey] = allFieldsClone;
    // console.log("heree",machine_info_values_updated['basic'].commissioning_date)
    this.setState(
      {
        machine_info_values: machine_info_values_updated,
        convertion_fields,
        machineInfoDateValues,
        form_changed: true,
      },
      () => {
        // console.log("heree",convertion_fields.commissioning_date.toString())
        // console.log("heree",machine_info_values_updated['basic'].commissioning_date.toString())
        // console.log("heree",this.state.machine_info_values['basic'].commissioning_date.toString())
        this.setActiveSection(
          ["advanced", "remote_control"].includes(formKey)
            ? formKey
            : "asset_info",
        );
        // this.setMachineInfoFormValues();
        // console.log(
        //     'machine_info_values',
        //     allFields.lifetime_runhour_manual_record_time,
        //     this.state.machine_info_values['basic'].lifetime_runhour_manual_record_time
        // );
      },
    );
  }
}
export function handleNavItemClick(item) {
  let { sections, active_section } = this.state;
  if (active_section.indexOf(item.key) == -1) {
    active_section.push(item.key);
  }
  sections = sections.map((each_section) => {
    let returnItem = each_section;
    returnItem.is_active = false;
    if (returnItem.name === item.name) {
      returnItem.is_active = true;
    }
    return returnItem;
  });
  this.setState({ sections, active_section }, () => {
    let scrollItems = document.getElementsByClassName(
      "tm_ta_scroll_ref-" + item.key,
    );
    if (scrollItems && scrollItems.length) {
      scrollItems[0].scrollIntoView({
        behavior: "smooth",
        block: "start",
        inline: "nearest",
      });
    }
    console.log("mani-scroll", scrollItems);
  });
}

export async function setLocation(locationFields) {
  let allFields = this.state.allFields;
  Object.keys(locationFields).forEach((key) => {
    allFields[key] = locationFields[key];
  });
  this.setState({ allFields }, () => {
    if (this.setActiveSection) {
      this.setActiveSection("location");
    }
  });
}

export function checkRentalThing(props) {
  let { customer_type, application_id } = props;
  if (parseInt(application_id) !== 17) {
    return false;
  } else if (
    !customer_type ||
    !Array.isArray(customer_type) ||
    customer_type.indexOf(5) > -1
  ) {
    return false;
  } else if (customer_type.length == 1 && customer_type[0] == 4) {
    return true;
  } else if (customer_type.length > 1 && customer_type.indexOf(4) > -1) {
    return true;
  }
}

export function showParameter(type, param) {
  let { edit_mode } = this.state;
  return edit_mode ? true : this.checkCheckBox(param, type);
}

// to check select all checkbox
export function checkSelectAllCheckBox(array, type) {
  let { checked_params, system_params } = this.state;
  if (
    !checked_params ||
    !checked_params[type] ||
    !system_params ||
    !system_params[type]
  ) {
    return false;
  }
  return (
    checked_params[type].length &&
    checked_params[type].length === system_params[type].length
  );
}

export function handleCheckParams(value, type) {
  if (!this.state.edit_mode) return;
  let checked_params = this.state.checked_params;
  let checked_device_params_key = checked_params,
    checked_device_params;
  if (!checked_device_params_key) {
    checked_params = {};
    checked_params[type] = [];
  }
  checked_device_params = checked_params[type];
  if (!checked_device_params) {
    checked_device_params = [];
  }

  if (checked_device_params.indexOf(value) > -1) {
    checked_device_params = checked_device_params.filter(
      (item) => item !== value,
    );
  } else {
    checked_device_params.push(value);
  }
  checked_params[type] = checked_device_params;
  let all_analyser_params = getAllCheckedParams(checked_params);
  this.setState(
    {
      checked_params: checked_params,
      form_changed: true,
      template_modified: true,
      all_analyser_params: all_analyser_params,
    },
    () => {
      this.setActiveSection("parameters");
      this.modifyAnalyserDetails();
    },
  );
}

export function modifyAnalyserDetails() {
  console.log("hweww");
  if (!this.checkPollutionApplication()) return;
  let {
      analyser_details,
      all_analyser_params,
      calibration_params,
      calibration,
    } = this.state,
    foundParam,
    all_selected_params = [],
    stateToUpdate = {},
    selected_params = [];
  let remainingParams = [];
  let all_params_keys = all_analyser_params.map((item) => item.destination_key);
  analyser_details.forEach((analser) => {
    remainingParams = analser.params.filter(
      (item) => all_params_keys.indexOf(item) > -1,
    );
    all_selected_params = [...all_selected_params, ...remainingParams];
  });
  // updated_analyser_details.forEach(analser=>{
  // 	analser.params.forEach(param=>{
  // 		selected_params.push(param)
  // 	})
  // })
  let unselected_params = all_analyser_params.filter(
    (item) => all_selected_params.indexOf(item.destination_key) == -1,
  );
  let updated_analyser_details = analyser_details.map((analser) => {
    selected_params = [];
    let item = analser;
    item.params.forEach((paramKey) => {
      foundParam = all_analyser_params.find(
        (item2) => item2.destination_key == paramKey,
      );
      if (foundParam) {
        selected_params.push(foundParam);
      }
    });
    item.unselected_params = [...unselected_params, ...selected_params];
    item.params = item.params.filter(
      (item2) => all_params_keys.indexOf(item2) > -1,
    );
    // item.unselected_params=item.unselected_params.filter(item=>all_params_keys.indexOf(item.destination_key)>-1)
    return item;
  });
  // if (analyser_details.length == 1) {
  // 	updated_analyser_details[0].unselected_params = this.props.all_analyser_params;
  // }

  // delete calibration params if param is uncchecked in parameter selection

  calibration_params = calibration_params.filter(
    (item) => all_params_keys.indexOf(item) > -1,
  );

  // delete calibration details if param is uncchecked in parameter selection
  Object.keys(calibration).forEach((paramKey) => {
    if (all_params_keys.indexOf(paramKey) === -1) {
      delete calibration[paramKey];
    }
  });

  // delete calibration details if all params are unchecked
  if (
    Object.keys(calibration).length == 1 &&
    Object.keys(calibration)[0] == "zero"
  ) {
    calibration = {};
  }
  stateToUpdate["analyser_details"] = updated_analyser_details;
  stateToUpdate["calibration_params"] = calibration_params;
  stateToUpdate["calibration"] = calibration;
  this.setState(stateToUpdate, () => {
    console.log({ updated_analyser_details, calibration });
  });
}
export function handleSelectAll(array, type, e) {
  let { checked_params, system_params } = this.state,
    is_checked = e.target.checked;
  let select_params = [];
  checked_params[type] = [];
  if (is_checked) {
    system_params[type].forEach((sys_param) => {
      let found_param = array.find((item) => item.destination_key == sys_param);
      if (found_param) {
        checked_params[type].push(found_param);
      }
    });
  }
  let all_analyser_params = getAllCheckedParams(checked_params);
  this.setState(
    {
      checked_params: checked_params,
      all_analyser_params,
      form_changed: true,
      template_modified: true,
    },
    () => {
      this.modifyAnalyserDetails();
      this.setActiveSection("parameters");
      // this.modifySystemParams(key)
    },
  );
}

export function showViewMoreBtn(type) {
  let edit_mode = this.state.edit_mode;
  if (edit_mode) {
    return (
      this.state.system_params &&
      this.state.system_params[type]?.length - 8 <= 0
    );
  }
  return (
    this.state.checked_params &&
    this.state.checked_params[type]?.length - 8 <= 0
  );
}
export function checkCheckBox(value, type) {
  let checked_params = this.state.checked_params;
  if (!checked_params[type]) {
    return false;
  }
  let checked_device_params = checked_params[type];
  if (!checked_device_params) {
    return false;
  } else {
    return checked_device_params.indexOf(value) > -1;
  }
}

export function checkDerivedParam(param) {
  if (
    !this.state.checked_params ||
    !this.state.checked_params["general"] ||
    Object.keys(this.state.checked_params).length == 0
  ) {
    return false;
  }
  let checked_params = Object.values(
    this.state.checked_params["general"],
  ).filter((item) => typeof item !== "undefined");
  let all_params = [];
  if (!checked_params[0] || !checked_params) {
    return false;
  }
  checked_params.forEach((param_obj) => {
    all_params.push(param_obj.destination_key);
  });
  let return_key = true;
  param.derived_from.forEach((dependent_param) => {
    if (all_params.indexOf(dependent_param) === -1) {
      return_key = false;
    }
  });
  return return_key;
}

export function handleParamSettingChange(val_param_key, e, name, type) {
  let { param_settings, selected_parameter, isMobile } = this.state;
  let val = getFieldValue(e, type);
  val = val === undefined ? "" : val;
  let param_key = val_param_key;
  if (!param_settings) {
    param_settings = {};
  }
  if (isMobile && !param_key && selected_parameter) {
    param_key = selected_parameter;
  }
  if (!param_settings[param_key]) {
    param_settings[param_key] = {};
  }

  param_settings[param_key] = { ...param_settings[param_key], [name]: val };
  this.setState({ param_settings }, () => {
    this.setActiveSection("parameters");
  });
}
export function getParamSettingValue(val_param_key, name) {
  let val = undefined;
  let param_key = val_param_key;
  let { param_settings, isMobile, selected_parameter } = this.state;
  if (isMobile && !param_key && selected_parameter) {
    param_key = selected_parameter;
  }
  if (
    param_settings &&
    param_settings[param_key] &&
    param_settings[param_key] &&
    typeof param_settings[param_key][name] !== "undefined"
  ) {
    return param_settings[param_key][name];
  }
  return val;
}

export async function getSystemDetailsAndParamsFromThingTemplate(
  thingTemplate,
  system_template,
) {
  let { thing_templates } = this.state,
    template;
  let returnObj = {
      system_details: {},
      params: {
        general: [],
        fault: [],
      },
    },
    template_params = {
      general: [],
      fault: [],
    };
  if (
    thingTemplate &&
    thing_templates &&
    thing_templates[thingTemplate] &&
    thing_templates[thingTemplate].system_details
  ) {
    let current_system_detail = {};
    if (
      Array.isArray(thing_templates[thingTemplate].system_details) &&
      thing_templates[thingTemplate].system_details.length > 0
    ) {
      current_system_detail = thing_templates[thingTemplate].system_details[0];
    } else {
      current_system_detail = thing_templates[thingTemplate].system_details;
    }
    delete current_system_detail["system_id"];
    returnObj.system_details = current_system_detail;
    template = current_system_detail.template_id;
  } else if (system_template) {
    template = system_template;
  }
  if (template && parseInt(template) > 0) {
    let templateAPIdata = await this.getProtocolDetails(template, "template");
    if (templateAPIdata.status == "success") {
      let current_template = templateAPIdata.template_details;
      if (
        current_template &&
        current_template.param_values &&
        current_template.param_values.general &&
        Array.isArray(Object.keys(current_template.param_values.general))
      ) {
        template_params.general = Object.keys(
          current_template.param_values.general,
        );
      }
      if (
        current_template &&
        current_template.param_values &&
        current_template.param_values.fault &&
        Array.isArray(Object.keys(current_template.param_values.fault))
      ) {
        template_params.fault = Object.keys(
          current_template.param_values.fault,
        );
      }
      returnObj.params = template_params;
    } else {
      return "invalid_template";
    }
  } else {
    return "invalid_template";
  }
  return returnObj;
}

export function renderThingTemplate(thingTemplate) {
  console.log("render template");
  if (thingTemplate === "custom") {
    this.resetData();
    return;
  }
  let {
    thing_templates,
    quick_add,
    show_quick_add,
    device_config_values,
    allFields,
  } = this.state;
  let machine_info_templates = this.state.mahchineinfo_template_data;
  let mandatory_json_backup = this.state.mandatory_json;
  let machine_info_template = null;
  if (
    !(
      device_config_values &&
      Array.isArray(device_config_values) &&
      device_config_values.length
    ) ||
    !thingTemplate ||
    !thing_templates[thingTemplate]
  )
    return;
  this.setState({ loading: true }, async () => {
    let selectedParams = {},
      default_params,
      checked_params,
      system_details,
      update_checked_params = true,
      deviceId = device_config_values[0].devices,
      machineInfoValues = {},
      stateToUpdate = {};
    let template_params = {
      general: [],
      fault: [],
    };
    default_params = {
      general: [],
      fault: [],
    };
    selectedParams = thing_templates[thingTemplate].parameters;
    const isFaultParamKeyPresent =
      selectedParams &&
      Object.getOwnPropertyNames(selectedParams).includes("fault");
    if (!selectedParams) {
      selectedParams = {};
    }
    if (!selectedParams.general) {
      selectedParams.general = [];
    }
    if (!selectedParams.fault) {
      selectedParams.fault = [];
    }
    update_checked_params =
      Object.keys(selectedParams.general).length ||
      Object.keys(selectedParams.fault).length;
    if (thing_templates[thingTemplate].system_details) {
      let current_system_detail = {},
        templates = [];
      if (
        Array.isArray(thing_templates[thingTemplate].system_details) &&
        thing_templates[thingTemplate].system_details.length > 0
      ) {
        templates = thing_templates[thingTemplate].system_details.map(
          (item) => "template-" + item.template_id,
        );
      } else if (thing_templates[thingTemplate].system_details.template_id) {
        templates = [
          "template-" +
            thing_templates[thingTemplate].system_details.template_id,
        ];
      }
      show_quick_add.system = false;
      this.onQuickAddValuesChange(templates, "system_template", "select", {
        update_checked_params: !update_checked_params,
      });
    }
    if (
      selectedParams.general.find((item) => item == "lat" || item == "long")
    ) {
      selectedParams.general = selectedParams.general.filter(
        (item) => item !== "lat" || item !== "long",
      );
      // selectedParams.general.push(location_param.destination_key);
    }
    if (
      parseInt(thing_templates[thingTemplate].machine_info_template) > 0 &&
      machine_info_templates[
        thing_templates[thingTemplate].machine_info_template
      ]
    ) {
      machineInfoValues = JSON.parse(
        machine_info_templates[
          thing_templates[thingTemplate].machine_info_template
        ],
      );
      machine_info_template =
        "" + thing_templates[thingTemplate].machine_info_template;
      if (quick_add) {
        show_quick_add.machine = false;
      }
    } else if (
      thing_templates[thingTemplate].machine_info &&
      typeof thing_templates[thingTemplate].machine_info == "object"
    ) {
      machineInfoValues = getMachineInfoSplitFormData(
        thing_templates[thingTemplate].machine_info,
        this.state.asset_info_form_items,
      );
      if (quick_add) {
        show_quick_add.machine = true;
        show_quick_add.mandatory = true;
      }
      machine_info_template = "custom";
    } else if (quick_add) {
      machine_info_template = "custom";
      if (quick_add) {
        show_quick_add.machine = true;
        show_quick_add.mandatory = true;
      }
    }
    default_params.general = this.state.all_params.general_parameters.filter(
      (item) => selectedParams.general.indexOf(item.destination_key) >= 0,
    );
    default_params.fault = this.state.all_params.fault_parameters.filter(
      (item) =>
        !isFaultParamKeyPresent ||
        selectedParams.fault.indexOf(item.destination_key) >= 0,
    );
    console.log("mani params", selectedParams, default_params);
    checked_params = default_params;
    let all_analyser_params = getAllCheckedParams(checked_params);
    console.log({ allFields });
    stateToUpdate = {
      ...stateToUpdate,
      mandatory_json: null,
      show_machine_forms: false,
      machine_info_values: machineInfoValues,
      machine_info_template,
      all_analyser_params,
      show_quick_add,
    };
    if (update_checked_params) {
      stateToUpdate["checked_params"] = checked_params;
    }
    this.setState(stateToUpdate, () => {
      // this.modifySystemParams(deviceId)
      // if (this.setParamsFromSystemTemplate){
      // this.setParamsFromSystemTemplate(template_params,{[deviceId]:'1'},true)
      // }
      console.log({ machineInfoValues });
      this.setState(
        {
          show_machine_forms: true,
          loading: false,
          form_changed: false,
          mandatory_json: mandatory_json_backup,
        },
        () => {
          // this.setMachineInfoFormValues(true);
        },
      );
    });
  });
}

export function handleMachineInfoTemplateChange(e) {
  let machineInfoValues,
    asset_info_form_items = this.state.asset_info_form_items;
  let machine_info_templates = this.state.mahchineinfo_template_data;
  if (e === "custom") {
    machineInfoValues = {};
  } else {
    machineInfoValues = JSON.parse(machine_info_templates[e]);
  }
  moment_convertions.forEach((item) => {
    if (
      machineInfoValues[item] &&
      typeof machineInfoValues[item] === "string"
    ) {
      machineInfoValues[item] = moment(machineInfoValues[item]);
    }
  });
  // this.machineInfoFormRef.formRef.current.setFieldsValue(
  // 	machineInfoValues
  // );
  let stateToUpdate = {
    machine_info_values: getMachineInfoSplitFormData(
      machineInfoValues,
      asset_info_form_items,
    ),
    machine_info_template: e,
    show_machine_forms: false,
    loading: true,
  };
  if (e === "custom") {
    stateToUpdate["machine_info_full_list"] = true;
  }
  this.setState(stateToUpdate, () => {
    console.log("state", this.state.machine_info_values);
    this.setState({ show_machine_forms: true, loading: false });
  });
}

export function setMachineInfoFormValues(reset) {
  return;
  let machineInfoValues = this.state.machine_info_values;
  moment_convertions.forEach((item) => {
    if (
      machineInfoValues[item] &&
      typeof machineInfoValues[item] !== "object"
    ) {
      machineInfoValues[item] = moment(machineInfoValues[item]);
    }
  });
  if (
    this.machineInfoFormRef &&
    this.machineInfoFormRef.current &&
    this.machineInfoFormRef.current.formRef &&
    this.machineInfoFormRef.current.formRef.current
  ) {
    if (reset) {
      this.machineInfoFormRef.current.formRef.current.resetFields();
    }
    this.machineInfoFormRef.current.formRef.current.setFieldsValue(
      machineInfoValues,
    );
  }
}

export async function setSystemDetails(
  details,
  update_system_params,
  update_checked_params,
  template_param_settings,
) {
  return new Promise(async (resolve, reject) => {
    let {
      activeTab,
      system_details,
      allFields,
      analyser_details,
      param_settings,
    } = this.state;
    if (!system_details) {
      system_details = {};
    }
    system_details[activeTab] = details;
    let first_system_template = getSystemTemplateValue(
      allFields,
      system_details,
      activeTab,
    );
    allFields["system_template"] = first_system_template;
    if (
      template_param_settings &&
      typeof template_param_settings === "object"
    ) {
      param_settings = { ...param_settings, ...template_param_settings };
    }
    this.setState({ system_details, allFields, param_settings }, () => {
      console.log(
        "mani-system-bug",
        this.state.allFields.system_template,
        first_system_template,
      );
      if (update_system_params) {
        let state_checked_params = this.state.checked_params;
        let state_system_params = this.state.system_params;
        let { system_params, checked_params } = this.getAllSystemParams(
          system_details[activeTab],
        );
        let stateUpdate = { system_params };
        if (!state_checked_params["general"]) {
          state_checked_params["general"] = [];
        }
        if (!state_checked_params["fault"]) {
          state_checked_params["fault"] = [];
        }
        if (!state_system_params["general"]) {
          state_system_params["general"] = [];
        }
        if (!state_system_params["fault"]) {
          state_system_params["fault"] = [];
        }
        let updatedCheckedParams = {
          general: state_checked_params.general.filter(
            (item) => system_params.general.indexOf(item.destination_key) >= 0,
          ),
          fault: state_checked_params.fault.filter(
            (item) => system_params.fault.indexOf(item.destination_key) >= 0,
          ),
        };
        let ischeckedParamschanged =
          updatedCheckedParams.general.length !==
            checked_params.general.length ||
          updatedCheckedParams.fault.length !== checked_params.fault.length;
        let isParamAdded =
          system_params.general.length !== state_system_params.general.length ||
          system_params.fault.length !== state_system_params.fault.length;
        if (update_checked_params) {
          stateUpdate["checked_params"] = checked_params;
          stateUpdate["all_analyser_params"] =
            getAllCheckedParams(checked_params);
        } else if (ischeckedParamschanged || isParamAdded) {
          stateUpdate["checked_params"] = updatedCheckedParams;
          stateUpdate["all_analyser_params"] =
            getAllCheckedParams(updatedCheckedParams);
        }
        this.setState(stateUpdate, () => {
          if (
            (update_checked_params || ischeckedParamschanged || isParamAdded) &&
            this.checkPollutionApplication()
          ) {
            this.modifyAnalyserDetails();
          }
          resolve();
          // console.log('system_details',system_params, checked_params);
        });
      } else {
        resolve();
      }
    });
  });
}
// function to get all system params and update in main component
export function getAllSystemParams(system_details) {
  let param_types = ["general", "fault"];
  let { all_params, allFields, all_param_details } = this.state;
  let checked_params = {};
  let system_params = {
    general: [],
    fault: [],
  };
  if (system_details) {
    Object.keys(system_details).forEach((system_id) => {
      let curr_sys_details = system_details[system_id];
      param_types.forEach((type) => {
        if (
          curr_sys_details &&
          curr_sys_details["assigned"] &&
          curr_sys_details["assigned"][type] &&
          Array.isArray(curr_sys_details["assigned"][type])
        ) {
          curr_sys_details["assigned"][type].forEach((param) => {
            system_params[type].push(param.destination_key);
          });
        }
      });
    });

    //sort system parameters based on order
    param_types.forEach((type) => {
      system_params[type] = system_params[type].sort((a, b) => {
        return all_param_details[a]?.order - all_param_details[b]?.order;
      });
    });
  }
  param_types.forEach((type) => {
    if (!checked_params[type]) {
      checked_params[type] = [];
    }
    checked_params[type] = all_params[
      type == "general" ? "general_parameters" : "fault_parameters"
    ].filter((item) => system_params[type].indexOf(item.destination_key) > -1);
  });
  let parentStateUpdate = {
    system_params,
    checked_params,
  };
  return parentStateUpdate;
  console.log("mani system params", system_params, checked_params);
  // if (this.props.updateMainState){
  //     // this.props.updateMainState(parentStateUpdate)
  // }
}
export function setUnassignedParams(params) {
  this.setState({ unassigned_params: params });
}

export function updateMainState(updateObj, callback) {
  let stateToUpdate = {};
  if (typeof updateObj === "object") {
    Object.keys(updateObj).forEach((key) => {
      stateToUpdate[key] = updateObj[key];
    });
    console.log("updated", stateToUpdate);
    this.setState(stateToUpdate, () => {
      if (typeof callback === "function") {
        callback();
      }
    });
  }
}
export function getAllSystemTemplates(system_details, device) {
  let templates = [];
  if (system_details && system_details[device]) {
    Object.values(system_details[device]).forEach((sys_detail) => {
      if (
        sys_detail["system_config"] &&
        sys_detail["system_config"]["template_id"]
      ) {
        templates.push(sys_detail["system_config"]["template_id"]);
      }
    });
  }
  return templates;
  // return system_details && system_details[device] && system_details[device]['1'] && system_details[device]['1']['system_config'] && system_details[device]['1']['system_config']['template_id'] && system_details[device]['1']['system_config']['template_id']
}
export function getFirstSystemTemplate(system_details, device) {
  return (
    system_details &&
    system_details[device] &&
    system_details[device]["1"] &&
    system_details[device]["1"]["system_config"] &&
    system_details[device]["1"]["system_config"]["template_id"] &&
    system_details[device]["1"]["system_config"]["template_id"]
  );
}
export function getFirstFuelTemplate(system_details, device) {
  return (
    system_details &&
    system_details[device] &&
    system_details[device]["1"] &&
    system_details[device]["1"]["system_config"] &&
    system_details[device]["1"]["system_config"]["fuel_sensor_name"] &&
    system_details[device]["1"]["system_config"]["fuel_sensor_name"]
  );
}

export function validateLocation(allFields) {
  let { latitude, longitude } = allFields;
  const isLatitude = (num) => isFinite(num) && Math.abs(num) <= 90;
  const isLongitude = (num) => isFinite(num) && Math.abs(num) <= 180;
  if (!latitude || !longitude) {
    return false;
  } else if (latitude && longitude) {
    return isLatitude(latitude) && isLongitude(longitude);
  }
  return false;
}

export function getSectionStatusImg(key) {
  let status = 0,
    that = this;
  let { allFields } = this.state;
  switch (key) {
    case "basic": {
      if (
        allFields.customerName &&
        allFields.thingType &&
        allFields.thingName &&
        allFields.thingName !== ""
      ) {
        status = 2;
      }
      break;
    }
    case "location": {
      if (validateLocation(allFields)) {
        status = 2;
      }
      break;
    }
    case "asset_info": {
      let { machine_info_values, analyser_details } = this.state;
      if (this.checkPollutionApplication()) {
        if (
          Array.isArray(analyser_details) &&
          analyser_details.some(
            (item) => Array.isArray(item.params) && item.params.length,
          )
        ) {
          status = status + 1;
        }
        if (
          Array.isArray(analyser_details) &&
          analyser_details.some(
            (item) => item.serial_no && item.serial_no !== "",
          )
        ) {
          status = status + 1;
        }
        break;
      }
      if (machine_info_values && Object.keys(machine_info_values).length) {
        status = status + 1;
      }
      if (
        (machine_info_values &&
          parseInt(allFields.thingType) === 18 &&
          (!machine_info_values.kva ||
            machine_info_values.kva === "" ||
            !machine_info_values.capacity ||
            machine_info_values.capacity === "" ||
            !machine_info_values.dg_type ||
            machine_info_values.dg_type === "")) ||
        (parseInt(allFields.thingType) == 18 && status == 1)
      ) {
        status = status + 1;
      }
      break;
    }
    case "device_config": {
      let { device_config_values } = this.state;
      if (
        device_config_values &&
        Array.isArray(device_config_values) &&
        device_config_values.length
      ) {
        status = status + 1;
      }
      if (allFields && allFields.system_template) {
        status = status + 1;
      }
      break;
    }
    case "parameters": {
      let { checked_params } = this.state;
      status = 0;
      if (!checked_params) {
        break;
      }
      if (
        (checked_params["general"] && checked_params["general"].length) ||
        (checked_params["fault"] && checked_params["fault"].length)
      ) {
        status = 2;
      }
      break;
    }
    default: {
    }
  }

  switch (status) {
    case 2: {
      return d_green;
    }
    case 1: {
      return d_orange;
    }
    case 0: {
      return d_black;
    }
    default: {
      return d_black;
    }
  }
}

export function handleObserver(entities, observer) {
  entities.forEach((entity) => {
    if (entity.isIntersecting) {
      let { sections, active_section } = this.state;
      let targetClassName =
        entity.target.classList[entity.target.classList.length - 1];
      let sectionkey = targetClassName.split("-")[1];
      if (active_section.indexOf(sectionkey) > -1) {
        console.log(entity.intersectionRatio, sectionkey);
        this.setActiveSection(sectionkey);
      }
      // this.handleScrolling(
      //     entity.target.className.split(' ')[1],
      //     'scroll'
      // );
    }
  });
}
export function setActiveSection(sectionkey) {
  let { sections } = this.state;
  sections = sections.map((item) => {
    item.is_active = item.key === sectionkey;
    return item;
  });
  this.setState({ sections });
}

export function initialiseIntersectionObserver(unobserve) {
  let { device_config_tab } = this.state;
  // modify sections for mobile
  if (this.state.isMobile) {
    this.handleMobileSections();
  }
  this.state.sections.forEach((each_section, ind) => {
    let targetElements = document.getElementsByClassName(
      "tm_ta_scroll_ref-" + each_section.key,
    );
    console.log("maniasa-final", targetElements);
    if (this.observer && targetElements && targetElements[0]) {
      if (unobserve) {
        this.observer.unobserve(targetElements[0]);
        return;
      }
      this.observer.observe(targetElements[0]);
    }
  });
}
export function handleScanQR() {
  // console.log(this)
  // this.props.onQRCodeScanned('nataraj-2-86')
  let that = this,
    QRScanner = window.QRScanner;
  const handleError = (err) => {
    if (that.props.updateMainState) {
      that.props.updateMainState({ scan_mode: false });
    }
    document.getElementsByTagName("body")[0].id = "camera-view2";
    // alert('error'+err)
  };
  function displayContents(err, text) {
    if (err) {
      QRScanner.destroy(function (status) {
        handleError(status);
      });
      // document.getElementsByTagName('body')[0].id='camera-view2'
      // an error occurred, or the scan was canceled (error code `6`)
    } else {
      QRScanner.destroy(function (status) {
        handleError(status);
      });
      document.getElementsByTagName("body")[0].id = "camera-view2";
      // The scan completed, display the contents of the QR code:
      // alert('scan-success'+text)
      that.props.onQRCodeScanned(text);
      //check if that device is in device list
    }
  }
  var done = function (err, status) {
    if (err) {
      handleError("prepare-err" + err);
    } else {
      console.log("prepare-succ", "QRScanner is initialized. Status:", status);
      QRScanner.scan(displayContents);
      document.getElementsByTagName("body")[0].id = "camera-view";
      // Make the webview transparent so the video preview is visible behind it.
      QRScanner.show();
    }
  };
  // console.log(this)
  if (QRScanner && that.props.updateMainState) {
    that.props.updateMainState({ scan_mode: true });
    QRScanner.prepare(done);
  }
}

export function onQRCodeScanned(qr) {
  let selectdata = this.state.selectdata;
  if (!selectdata) {
    selectdata = {};
  }
  if (!selectdata["Assigned"]) {
    selectdata["Assigned"] = [];
  }
  if (!selectdata["Unassigned"]) {
    selectdata["Unassigned"] = [];
  }
  //{id: 9020, name: "TestDevice_4"}
  let allDevices = [...selectdata["Assigned"], ...selectdata["Unassigned"]];
  let selectedDevice = allDevices.find((item) => item.name === qr);
  if (selectedDevice && selectedDevice.id) {
    this.handleChangeDeviceForm(selectedDevice.id);
  } else {
    openNotification(
      "error",
      "Scanned Device Not Found or Not Assigned to selected customer",
    );
  }
}
export function onParamSearch(e) {
  let SearchData = this.state.SearchObjectData;
  SearchData.value = e;
  this.setState({
    SearchObjectData: SearchData,
  });
}

export async function getCustomerTypes() {
  if (this.state.customerTypes) return this.state.customerTypes;
  const response = await retriveApplications();
  const customerTypes = {};
  if (response.customer_types?.length) {
    response.customer_types.forEach((type) => {
      customerTypes[type.id] = type.name;
    });
  }

  this.setState({
    customerTypes: customerTypes,
  });
  return customerTypes;
}
