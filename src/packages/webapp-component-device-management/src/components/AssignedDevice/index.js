import React, { Suspense, lazy } from "react";
import AntLayout from "@datoms/react-components/src/components/AntLayout";
import AntContent from "@datoms/react-components/src/components/AntContent";
import AntRow from "@datoms/react-components/src/components/AntRow";
import AntCol from "@datoms/react-components/src/components/AntCol";
import AntTooltip from "@datoms/react-components/src/components/AntTooltip";
import AntMenu from "@datoms/react-components/src/components/AntMenu";
import AntMenuItem from "@datoms/react-components/src/components/AntMenuItem";
import AntDropdown from "@datoms/react-components/src/components/AntDropdown";
import AntSpin from "@datoms/react-components/src/components/AntSpin";
import AntSelect from "@datoms/react-components/src/components/AntSelect";
import AntOption from "@datoms/react-components/src/components/AntOption";
import AntInput from "@datoms/react-components/src/components/AntInput";
import AntModal from "@datoms/react-components/src/components/AntModal";
import AntConfirmModal from "@datoms/react-components/src/components/AntConfirmModal";
import AntMessage from "@datoms/react-components/src/components/AntMessage";
import AntNotification from "@datoms/react-components/src/components/AntNotification";
import AntProgress from "@datoms/react-components/src/components/AntProgress";
import AntTreeSelect from "@datoms/react-components/src/components/AntTreeSelect";
import AntTable from "@datoms/react-components/src/components/AntTable";
import AntDrawer from "@datoms/react-components/src/components/AntDrawer";
// import AntTag from '@datoms/react-components/src/components/AntTag';
import FilterSelectWithSearch from "@datoms/react-components/src/components/FilterSelectWithSearch";
import HeirarchyPopover from "@datoms/react-components/src/components/HeirarchyPopover";
import AntSwitch from "@datoms/react-components/src/components/AntSwitch";
import SearchInput from "@datoms/react-components/src/components/SearchInput";
import AntPasswordModal from "@datoms/react-components/src/components/AntPasswordModal";
import AntRadio from "@datoms/react-components/src/components/AntRadio";
import AntRadioGroup from "@datoms/react-components/src/components/AntRadioGroup";
import AntButton from "@datoms/react-components/src/components/AntButton";
import TwinTabs from "@datoms/react-components/src/components/TwinTabs";
import NoDataComponent from "@datoms/react-components/src/components/NoDataComponent";
import UserAddOutlined from "@ant-design/icons/UserAddOutlined";
import DownOutlined from "@ant-design/icons/DownOutlined";
import CopyOutlined from "@ant-design/icons/CopyOutlined";
import StopOutlined from "@ant-design/icons/StopOutlined";
import _some from "lodash/some";
import _find from "lodash/find";
import _isEqual from "lodash/isEqual";
import _map from "lodash/map";
import _filter from "lodash/filter";
import _orderBy from "lodash/orderBy";
import _indexOf from "lodash/indexOf";
import _isString from "lodash/isString";
import _sortBy from "lodash/sortBy";
import io from "socket.io-client";
import moment from "moment-timezone";
import {
  deviceLists,
  deviceStatusUpdate,
  unassignDevice,
  devicesList,
  retriveCustomerList,
  updateDeviceSimDetails,
  retriveVendorFirmwareList,
  retriveVendorFirmwareDetails,
  getSecurityToken,
} from "@datoms/js-sdk";
import { getTerritoryData } from "../../../../webapp-component-user-management/src/components/TerritoryPage/TerritorySelect";
import {
  insertDealer,
  disableTreeUpdate,
} from "../../../../webapp-component-user-management/src/components/TerritoryPage/logic/customer";
import DeviceDebug from "../../../../webapp-component-thing-management/src/components/DeviceDebug";
import defaultConfig from "./defaultConfig";
import MobileDeviceList from "../Mobile/MobileDeviceList";
import ThingCreated from "../../imgs/ThingCreated.svg";
import upto_date from "../../imgs/up_to_date.svg";
import update from "../../imgs/update.svg";
import ethernet from "../../imgs/ethernet.svg";
import ethernet_inactive from "../../imgs/ethernet_inactive.svg";
import configuration from "../../imgs/configuration.svg";
import debug from "../../imgs/debug.svg";
import custom_command from "../../imgs/custom_command.svg";
import unassign from "../../imgs/unassign.svg";
import update_status from "../../imgs/update_status.svg";
import update_firmware from "../../imgs/update_firmware.svg";
import ActiveIcon from "../../imgs/active_icon.svg";
import InactiveIcon from "../../imgs/inactive_icon.svg";
import power_on from "../../imgs/power_on.svg";
import power_off from "../../imgs/power.svg";
import power_inactive from "../../imgs/power_inactive.svg";
import lighting_inactive from "../../imgs/lighting_inactive.svg";
import lighting_active from "../../imgs/lighting_active.svg";
import WifiSignalRounded from "../../imgs/wifi_signal_rounded.svg";
import WifiSignalRounded1 from "../../imgs/wifi-signal/wifi_signal_rounded_1.svg";
import WifiSignalRounded2 from "../../imgs/wifi-signal/wifi_signal_rounded_2.svg";
import WifiSignalRounded3 from "../../imgs/wifi-signal/wifi_signal_rounded_3.svg";
import GprsSignalRounded from "../../imgs/gprs_signal_rounded.svg";
import GprsSignalRounded2 from "../../imgs/gprs-signal/gprs_signal_rounded_2.svg";
import GprsSignalRounded4 from "../../imgs/gprs-signal/gprs_signal_rounded_4.svg";
import GprsSignalRounded5 from "../../imgs/gprs-signal/gprs_signal_rounded_5.svg";
import DevicePopKey from "../../imgs/device_pop_key.svg";
import raw_log_icon from "../../imgs/raw_log_icon.svg";
import CloudFirmware from "../../imgs/cloud_firmware.svg";
import SimMobileIcon from "../../imgs/sim-modal.svg";
import EditOutlined from "@ant-design/icons/EditOutlined";
import "./style.less";
import "../style.less";
import sim from "../../imgs/sim.svg";
import unAssignImage2 from "../../imgs/unassign-device.svg";
import { accountTypeOptions } from "../../../../iot-platform-views/src/js/DatomsX/Template-1/imports/logic/customer-add-edit";
import { CONSTANTS as ASSET_CONSTANTS, getCustomerTypes } from "../../../../webapp-component-thing-management/src/components/ThingList/logic";
import { circuitExceptionDevices } from "../imports/config.js";
import { getDeviceDetails } from "../logic.js";

const AddForm = lazy(() => import("../imports/AddForm"));
import queryString from "query-string";
export default class AssignedDevice extends React.Component {
  constructor(props) {
    super(props);
    this.parsed = queryString.parse(props.location.search);
    this.search_value = this.parsed.search ? this.parsed.search : "";

    this.deviceDetails = {
      customer_id: this.parsed.customer_id ? this.parsed.customer_id : "",
      application_id: this.parsed.application_id
        ? this.parsed.application_id
        : "",
      device_id: this.parsed.device_id ? this.parsed.device_id : "",
      device_qr_code: this.parsed.device_qr_code
        ? this.parsed.device_qr_code
        : "",
    };
    this.debug_type_name = this.parsed.type_name ? this.parsed.type_name : "";
    // console.log('parsed__', this.search_value);
    this.customer = this.parsed.customer
      ? JSON.parse(this.parsed.customer)
      : [];
    this.customer_selected = [];
    if (this.customer && this.customer.length) {
      this.customer.map((val, ind) => {
        this.customer_selected.push("0-1-" + val);
      });
    }
    this.type = this.parsed.type ? JSON.parse(this.parsed.type) : [];
    this.type_selected = [];
    if (this.type && this.type.length) {
      this.type.map((val, ind) => {
        this.type_selected.push("0-1-" + val);
      });
    }
    this.application = this.parsed.application
      ? JSON.parse(this.parsed.application)
      : [];
    this.application_selected = [];
    if (this.application && this.application.length) {
      this.application.map((val, ind) => {
        this.application_selected.push("0-1-" + val);
      });
    }

    let head_side_object_data = this.props.head_side_object_data;
    head_side_object_data.header_component.header.page_name = "Devices";
    this.updateSimDetails = this.updateSimDetails.bind(this);
    this.getDeviceDetails = getDeviceDetails.bind(this);
    this.state = {
      unassign_mobile_devices_modal: false,
      value: this.customer_selected,
      table_data: [],
      table_search:
        this.parsed_search &&
        Object.values(this.parsed_search).length &&
        this.parsed_search.search
          ? this.parsed_search.search
          : "",
      application_filter_ids: this.application,
      device_type_filter_ids: this.type,
      head_side_object_data: head_side_object_data,
      customer_filter_ids: this.customer,
      deviceTypeFilter: [],
      applicationFilter: [],
      sim_details_drawer: false,
      selected_device_sim: null,
      customerFilter: [],
      filtered_city_ids: [],
      client_id: props.client_id,
      applicationFilterValue: this.application_selected,
      deviceTypeFilterValue: this.type_selected,
      customerFilterValue: this.customer_selected,
      table_view_options: {},
      tableVisible: true,
      show_no_data: false,
      drawAddVisible: false,
      status: null,
      id: null,
      adding_status: null,
      city_id: document.getElementById("city_id")
        ? document.getElementById("city_id").value
        : 32, //'all',
      city_id_exclude: ["all", "unconfigured", "no_data_received"],
      search_value: this.search_value,
      modal_view: false,
      modal_sync_view: false,
      sync_index: null,
      sorted: false,
      sort_keys: null,
      mobile_drawer: false,
      date_time_online: false,
      filter_keys: {
        city_id: "0",
        connection: "0",
        sync: "0",
        health: "0",
        data_sending: "0",
        unconfigured: "0", //false
        device_archive_status: "active",
        no_data_received: "0", //false
        publicly_accessible: "0",
      },
      health_status: {
        0: "Any",
        1: "OK",
        2: "Power Failure",
        3: "Code Restart",
        4: "Modem Restart",
        5: "Other Error",
        6: "Battery Voltage Down",
        7: "Network Signal Low",
        8: "Debug Data Not Sent",
      },
      modem_types: {
        1: "GPRS",
        2: "WIFI",
        3: "Ethernet",
      },
      add_station: false,
      city_id: "",
      station_name: "",
      stations_device: "",
      station_lat: "",
      station_long: "",
      current_header_menu:
        this.props.location &&
        Object.keys(this.props.location).length &&
        this.props.location.pathname
          ? this.props.location.pathname.includes("sim-management") ||
            this.props.location.pathname.includes("production-inventory")
            ? "production"
            : this.props.location.pathname.includes("client-management")
              ? "client"
              : this.props.location.pathname.includes("/devices/")
                ? "device"
                : ""
          : "",
      update_firmware: false,
      selected_devices: [],
      device_updated_firmware_id: 0,
      filter_type: "any",
      bulk_status: [],
      visible_debug_drawer: false,
      selected_row: [],
      selected_rows: [],
      selected_row_keys: [],
      device_lists: [],
      access_key: ["DeviceManagement:DeviceManagement"],
      selected_app:
        this.props.application_select_list &&
        this.props.application_select_list.length
          ? this.props.application_select_list[0].value
          : "",
      third_party_row_value: "",
      third_party_modal_visible: false,
      release_firmware_list: [],
      dev_firmware_list: [],
      selected_radio_value: "release",
      firmware_value: undefined,
      firmware_update_modal_loading: false,
      single_update: false,
      accepted_type_ids: [1, 2, 11, 12, 51, 60],
      selected_account_type:
        props.application_id === 12 ? "production" : undefined,
      isTerritoryEnabled:
        this.props.application_id !== 16 &&
        (props.client_id === 1 ||
          props.enabled_features?.includes("UserManagement:Territory")) &&
        props.getViewAccess(["TerritoryManagement:View"]),
    };
    this.device_details = null;
    this.device_name = "";
    this.device_table_pagination_config = {
      position: ["topRight", "bottomRight"],
      defaultPageSize: 20,
      showSizeChanger: true,
      size: "small",
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
    };
    this.platform_slug = "";
    if (import.meta.env.VITE_BUILD_MODE !== "development") {
      let application_slug = window.location.pathname.split("/")[3];
      if (application_slug && typeof application_slug === "string") {
        this.platform_slug = "/" + application_slug;
      }
    } else {
      let application_slug = window.location.pathname.split("/")[1];
      if (application_slug && typeof application_slug === "string") {
        this.platform_slug = "/" + application_slug;
      }
    }
    this.currently_viewing_device_ids = [];
    this.firmwareSelectionOptions = [];

    this.iotBasePath = "https://app.datoms.io";

    // this.iotBasePath = this.props.location
    // 	? this.props.location.pathname &&
    // 	  this.props.location.pathname.includes('/datoms-x')
    // 		? '/datoms-x'
    // 		: this.props.location.pathname.includes('/iot-platform')
    // 		? '/iot-platform'
    // 		: '/datoms-x'
    // 	: '';

    if (this.props.app_name) {
      this.platform_slug = "/" + this.props.app_name;
    }

    if (
      !import.meta.env.VITE_MOBILE &&
      typeof window !== undefined &&
      !window.location.href.includes("localhost")
    ) {
      this.iotBasePath = window.location.protocol + "//" + window.location.host;
    }
    //array comparision method
    // Warn if overriding existing method
    if (Array.prototype.equals)
      console.warn(
        "Overriding existing Array.prototype.equals. Possible causes: New API defines the method, there's a framework conflict or you've got double inclusions in your code.",
      );
    // attach the .equals method to Array's prototype to call it on any array
    Array.prototype.equals = function (array) {
      // if the other array is a falsy value, return
      if (!array) return false;

      // compare lengths - can save a lot of time
      if (this.length != array.length) return false;

      for (var i = 0, l = this.length; i < l; i++) {
        // Check if we have nested arrays
        if (this[i] instanceof Array && array[i] instanceof Array) {
          // recurse into the nested arrays
          if (!this[i].equals(array[i])) return false;
        } else if (this[i] !== array[i]) {
          // Warning - two different object instances will never be equal: {x:20} != {x:20}
          return false;
        }
      }
      return true;
    };
    // Hide method from for-in loops
    Object.defineProperty(Array.prototype, "equals", { enumerable: false });
    /*if (this.props.history.location.pathname.includes('/datoms-x')) {
			this.props.setPageType();
		}*/
    // console.log('device_props_', this.props);
  }

  readCookie(key) {
    let result;
    return (result = new RegExp(
      "(?:^|; )" + encodeURIComponent(key) + "=([^;]*)",
    ).exec(document.cookie))
      ? result[1]
      : null;
  }

  getDeviceSyncStatus(deviceDetails) {
    if (
      deviceDetails.device_sync_status === "not_in_sync" ||
      deviceDetails.pending_messages_found === "true" ||
      deviceDetails.pending_messages_found === true
    ) {
      if (
        deviceDetails.pending_messages_found === "true" ||
        deviceDetails.pending_messages_found === true
      ) {
        if (deviceDetails.connection_status === "online") {
          return "syncing";
        } else {
          return "waiting_for_device";
        }
      } else {
        return "not_in_sync";
      }
    } else {
      return "in_sync";
    }
  }

  async fetchDeviceList() {
    let that = this;
    this.setState({
      loading: true,
      remove_loading: false,
    });
    let response;
    if (this.props.is_application_filter) {
      if (parseInt(this.state.selected_app) == 17) {
        response = await deviceLists("", this.props.customer_id);
      } else {
        response = await devicesList(
          this.props.customer_id,
          this.state.selected_app,
        );
      }
    } else {
      if (
        this.props.location &&
        Object.keys(this.props.location).length &&
        this.props.location.pathname &&
        (this.props.location.pathname.includes("/iot-platform") ||
          this.props.location.pathname.includes("/datoms-x"))
      ) {
        let query_text = this.state.selected_account_type
          ? "assigned&account_type=" + this.state.selected_account_type
          : "assigned";
        if (this.state.selected_territory_ids) {
          const territories = this.state.selected_territory_ids.filter(
            (id) => !`${id}`.includes("isDealer-"),
          );
          query_text +=
            (query_text ? "&" : "?") +
            "territories=" +
            encodeURIComponent(territories);
        }
        query_text += "&goem_devices=true"
        response = await deviceLists(query_text, this.props.client_id);
      } else {
        response = await devicesList(
          this.props.client_id,
          this.props.application_id,
        );
      }
    }
    if (response.status === 403) {
      that.setState({
        loading: false,
        unauthorised_access: true,
        unauthorised_access_msg: response.message,
      });
    } else if (response.status === "success") {
      // console.log('device_types__', response);
      let data = response;
      data.devices = _orderBy(response.devices, ["created_at"], ["desc"]);
      //create device type filter
      let child_data = [],
        deviceTypeFilter = [],
        applicationFilter = [],
        customerFilter = [];
      /*if (data.device_types && data.device_types.length) {
				data.device_types.map((type) => {
					console.log('type1_', type)
					deviceTypeFilter.push({
						'title': type.name,
						'value': '0-1' + '-' + type.id,
						'key': '0-1' + '-' + type.id,
					});
				});
			}

			child_data = [];
			if (data.applications && data.applications.length) {
				data.applications.map((app) => {
					applicationFilter.push({
						'title': app.name,
						'value': '0-1' + '-' + app.id,
						'key': '0-1' + '-' + app.id,
					});
				});
			}

			child_data = [];
			if (data.clients && data.clients.length) {
				data.clients.map((client) => {
					customerFilter.push({
						'title': client.name,
						'value': '0-1' + '-' + client.id,
						'key': '0-1' + '-' + client.id,
					});
				});
			}
			customerFilter.push({
				title: 'All Customers',
				value: '0-1',
				key: '0-1',
				children: child_data
			});*/
      this.setState({
        // deviceTypeFilter: deviceTypeFilter,
        // customerFilter: customerFilter,
        // applicationFilter: applicationFilter,
        device_lists: data,
        device_type: data.device_types,
        remove_loading: true,
        show_no_data: data.devices && data.devices.length == 0,
      });
      that.device_details = data.devices;
      that.filterTableData();
    } else {
      that.openNotification("error", response.message);
      that.setState({
        unauthorised_access: false,
        loading: true,
        error_API: true,
        remove_loading: true,
        error_API_msg: response.message,
      });
    }
  }

  changeFiterType(value) {
    let that = this;
    // console.log('selectBefore', value);
    that.setState(
      {
        filter_type: value,
      },
      () => {
        // console.log('selectBefore_state', that.state.filter_type);
        that.filterTableData();
      },
    );
  }

  /**
   * This method is called right after when an instance of a component is being created and inserted into the DOM.
   */
  async componentDidMount() {
    // document.title = 'Device Management';
    if (
      !this.props.location.pathname.includes("/assigned") &&
      !this.props.location.pathname.includes("/customer-management/")
    ) {
      this.props.history.push(this.platform_slug + "/devices/assigned");
    }
    //fetch devices list from REST API
    const [territoryResponse, dealersData] = await Promise.all([
      getTerritoryData(
        this.props.client_id,
        this.props.client_name,
        this.state.isTerritoryEnabled,
      ),
      this.getCustomerList(),
      this.fetchDeviceList(),
    ]);

    if (territoryResponse?.territoryData && dealersData?.length) {
      territoryResponse.territoryData = insertDealer(
        territoryResponse.territoryData,
        dealersData,
      );
    }

    if (territoryResponse) {
      this.setState({
        territoryData: territoryResponse?.territoryData,
      });
    }
    /**
     * Socket is used to sync the data between client and server in real time.
     */
    this.socket = io(import.meta.env.VITE_IOT_PLATFORM_SOCKET_BASEPATH, {
      query: {
        s_token: getSecurityToken(),
        // s_token: 'osan0gp21kqnf08nittt233vh0',
      },
    });
    this.socket.on("connect", () => {
      this.socket.emit("connect_dashboard_to_socket");
      // console.log('connected to socket');
    });

    this.socket.on("dashboard_successfully_connected", () => {
      /*this.socket.emit('get_list_of_devices', JSON.stringify({
				// city_id: this.state.city_id
				city_id: 5
			}));*/
      if (this.currently_viewing_device_ids.length) {
        this.subscribeForDeviceUpdates(true);
      }
      // console.log('dashboard successfully connected');
    });

    this.socket.on("device_firmware_updated", () => {
      // console.log('device_firmware_updated');
      this.openNotification(
        "success",
        "Devices firmware update queued successfully.",
      );
    });

    /*this.socket.on('update_list_of_devices', (payload) => {
			let data = JSON.parse(payload);
			let child_data = [];
			if (data.cities && Object.keys(data.cities).length) {
				Object.keys(data.cities).map((ct_id) => {
					child_data.push({
						'title': data.cities[ct_id],
						'value': '0-1' + '-' + ct_id,
						'key': '0-1' + '-' + ct_id,
					});
				});
			}
			userFilter.push({
				title: 'City',
				value: '0-1',
				key: '0-1',
				children: child_data
			});
			console.log('unassigned_devices', data);
			this.device_details = data.devices;
			this.setState({
				device_cities: data.cities,
				unassigned_devices: data.unassigned_devices
			}, () => {
				this.getFilteredStations(this.state.search_value)
				console.log('update_list_of_devices', this.device_details);
				console.log('unassigned_devices', this.state.unassigned_devices);
			});
		});*/

    // this.socket.on('update_device_details', (payload) => {
    // 	let device_data = JSON.parse(payload);
    // 	let deviceData = this.device_details;
    // 	if (deviceData && deviceData.length) {
    // 		this.device_details.map((data, index) => {
    // 			if (data.id == parseInt(device_data.device_id)) {
    // 				/*if (device_data.connection_status) {
    // 					deviceData[index].status_code =
    // 						device_data.connection_status == 'online'
    // 							? 1
    // 							: 0;
    // 				}*/
    // 				if (parseInt(device_data.last_data_receive_time)) {
    // 					deviceData[index].last_data_receive_time = parseInt(
    // 						device_data.last_data_receive_time
    // 					);
    // 				}
    // 				// if (data.id == '4445') {
    // 				// 	console.log('update_device_details_ device_data', device_data);
    // 				// 	console.log('update_device_details_ deviceData', deviceData);
    // 				// }
    // 			}
    // 		});
    // 	}
    // 	this.device_details = deviceData;
    // 	this.filterTableData();
    // });

    this.socket.on("update_location_details_in_dashboard", (payload) => {
      let data_status = JSON.parse(payload);
      if (this.device_details) {
        let status_change = this.device_details;
        status_change.map((stat, index) => {
          if (stat && data_status.device_id == stat.id) {
            Object.keys(data_status).map((key) => {
              if (key != "device_id") {
                status_change[index][key] = data_status[key];
              }
            });
          }
        });
        this.device_details = status_change;
        this.updateFilter();
        // this.setState({device_details: status_change}, () => this.updateFilter());
      }
      // console.log('update_location_details_in_dashboard', this.device_details);
      // console.log('Dashboard successfully connected to socket.');
      // console.log('payload', data_status);
    });

    this.socket.on("new_station_added_successfully", (payload) => {
      let add_new_device = JSON.parse(payload);
      let device_details = this.device_details;
      device_details.push(add_new_device);
      this.device_details = device_details;
      this.filterTableData();
      /*this.setState({
				device_details: device_details
			}, () => this.getFilteredStations(this.state.search_value));*/
      // console.log('new_station_added_successfully', add_new_device);
      // showPopup('success', 'New device added successfully!');
    });
    this.socket.on("unsubscribe_from_devices_successful", (payload) => {
      // console.log('unsubscribe successful');
      this.socket.emit(
        "subscribe_to_devices",
        JSON.stringify({
          device_ids: this.currently_viewing_device_ids,
        }),
      );
    });
    this.socket.on("subscribe_to_devices_successful", (payload) => {
      // console.log('subscribe successful');
    });
    // if ((this.city_name === 'admin') || (this.city_name === '127')) {
    // } else {
    // 	document.title = '404 Not Found - Aurassure';
    // }
  }

  componentDidUpdate(prevProps, prevState, screenshots) {
    // console.log('componentDidUpdate prevProps --> ', prevProps);
    // console.log('componentDidUpdate this.props --> ', this.props);
    // console.log('componentDidUpdate this.state --> ', this.state);
    // console.log('componentDidUpdate prevState --> ', prevState);
    // console.log('componentDidUpdate screenshots --> ', screenshots);

    if (
      prevState.current_header_menu == "" &&
      this.props.location &&
      Object.keys(this.props.location).length &&
      this.props.location.pathname &&
      (this.props.location.pathname.includes("sim-management") ||
        this.props.location.pathname.includes("production-inventory") ||
        this.props.location.pathname.includes("client-management") ||
        this.props.location.pathname.includes("/devices/"))
    ) {
      this.setState({
        current_header_menu:
          this.props.location &&
          Object.keys(this.props.location).length &&
          this.props.location.pathname
            ? this.props.location.pathname.includes("sim-management") ||
              this.props.location.pathname.includes("production-inventory")
              ? "production"
              : this.props.location.pathname.includes("client-management")
                ? "client"
                : this.props.location.pathname.includes("/devices/")
                  ? "device"
                  : ""
            : "",
      });
    }
  }

  changeGroup(key /*, update = false*/) {
    // console.log('in changeGroup', key);
    /*console.log('in changeGroup',update);*/
    this.props.history.push(this.platform_slug + "/devices/" + key);
    /*let search = this.state.table_search;
		this.setState({
			tab_toggle: key,
			table_search: update ? '' : search
		}, () => {
			if (this.state.tab_toggle == 'user_group') {
				this.props.history.push(this.platform_slug + '/customer-management/'+ this.state.customer_id +'/user-groups/');
			} else if (this.state.tab_toggle == 'user') {
				this.props.history.push(this.platform_slug + '/customer-management/'+ this.state.customer_id +'/users/');
			} else if (this.state.tab_toggle == 'role') {
				this.props.history.push(this.platform_slug + '/customer-management/'+ this.state.customer_id +'/roles/');
			}
		});*/
  }

  /**
   * This function calls the notification alert.
   * @param  {String}	type
   * @param  {String}	msg
   * @return {void}
   */
  openNotification(type, msg) {
    if (window.innerWidth < 576) {
      AntMessage(type, msg);
    } else {
      AntNotification({
        type: type,
        message: msg,
        // description: 'This is success notification',
        placement: "bottomLeft",
        className: "alert-" + type,
      });
    }
  }

  /**
   * This Perform any necessary cleanup in this method, such as invalidating timers, canceling network requests, or cleaning up any DOM elements that were created in componentDidMount.
   */
  componentWillUnmount() {
    if (typeof this.socket?.close === "function") {
      this.socket.close();
      // console.log('socket disconnected');
    }
  }

  updateFilter(options) {
    if (options) {
      this.setState({ filter_keys: options }, () => this.filterTableData());
    } else {
      this.filterTableData();
    }
    // console.log('options', options);
  }

  async getCustomerList() {
    let that = this;
    let response, customerTypes = {};
    const isDealerManagement = this.props.enabled_features?.includes(
      "IndustryManagement:Dealers",
    );
    if (that.platform_slug.includes("datoms-x")) {
      response = await retriveCustomerList(1);
    } else {
      [response, customerTypes] = await Promise.all([retriveCustomerList(that.props.client_id), getCustomerTypes(isDealerManagement)]);
    }
    if (response.status === 403) {
      that.setState({
        loading: false,
        unauthorised_access: true,
        unauthorised_access_msg: response.message,
      });
    } else if (response.status === "success") {
      let vendorList = [],
        customerList = [];
      const customerNameMap = {};
      const dealersData = [];
      const isPartnerFilter =
        that.platform_slug.includes("datoms-x") ||
        that.props.enabled_features?.includes("IndustryManagement:Dealers");
      if (isPartnerFilter && response.customers && response.customers.length) {
        response.customers.map((customer) => {
          customerNameMap[customer.id] = customer.name;
          if (customer.is_vendor) {
            vendorList.push(customer);
            if (
              isDealerManagement &&
              customer.customer_type?.[0] &&
              customerTypes[customer.customer_type?.[0]]
            ) {
              customer.customerTypeName =
                customerTypes[customer.customer_type[0]];
            }
          } else {
            customerList.push(customer);
          }
          if (!that.platform_slug.includes("datoms-x") && customer.is_vendor) {
            dealersData.push({
              id: `isDealer-${customer.id}`,
              name: customer.name,
              territory_id: customer.territory_id,
              is_dealer: true,
            });
          }
        });
      }
      that.setState({
        vendor_list: vendorList,
        client_list: isPartnerFilter ? customerList : response.customers,
        application_list: response.applications,
        customerNameMap: customerNameMap,
        dealersData: dealersData,
      });
      return dealersData;
      // that.fetchDeviceList();
    } else {
      that.openNotification("error", response.message);
      that.setState({
        loading: false,
        error_API: true,
        error_API_msg: response.message,
      });
    }
  }

  getFilteredStations(search_value) {
    if (
      this.state.filtered_table_data &&
      this.state.filtered_table_data.length
    ) {
      const table_data = [];
      const filtered_stations_list = [];
      if (
        this.state.filtered_table_data &&
        this.state.filtered_table_data.length
      ) {
        this.state.filtered_table_data.map((st, index) => {
          if (
            search_value &&
            !st.qr_code?.toLowerCase().includes(search_value.toLowerCase())
          )
            return;

          let type_det = _find(this.state.device_lists.device_types, {
            id: st.type_id,
          });
          let client_det = _find(this.state.device_lists.clients, {
            id: st.client_id,
          });
          let app_det = _find(this.state.application_list, {
            id: st.application_id,
          });
          filtered_stations_list.push(st);
          table_data.push({
            key: st.id,
            id: st.id,
            qr: st.qr_code,
            qr_code: st.qr_code,
            station_id: st.station_id,
            created_at: st.created_at,
            station: st.name,
            station_name: st.name && st.name.length ? st.name[0] : "",
            name: st.name,
            customer_id: st.client_id,
            client_id: st.client_id,
            customer: client_det ? client_det.name : "",
            application_id: st.application_id,
            application: app_det ? app_det.name : "",
            // city: this.state.device_cities[st.city_id],
            active: st.last_data_receive_time,
            sync: this.getDeviceSyncStatus(st),
            connect: st.device_modem_type,
            device_modem_type: st.device_modem_type,
            health: this.state.health_status[st.device_error_status],
            device_error_status: st.device_error_status,
            sim_details: st.sim_details,
            date: moment
              .unix(st.last_data_receive_time)
              .tz("Asia/Kolkata")
              .format("HH:mm, DD MMM"),
            timestamp: st.last_data_receive_time,
            network: st.network,
            percent: st.online_percentage,
            online_percentage: st.online_percentage,
            availability: st.data_availability,
            data_availability: st.data_availability,
            tags: st.tags,
            vendor_id: st.vendor_id,
            is_editable: st.is_editable,
            vendor_name: st.vendor_name,
            device_auth_token: st.device_auth_token,
            device_key:
              st.device_auth_token && st.device_auth_token !== ""
                ? st.device_auth_token
                : "NA",
            type_id: st.type_id,
            type_name: type_det ? type_det.name : "",
            device_config: st,
            firmware_version: st.firmware_version ? st.firmware_version : "",
            firmware_update_time: st.firmware_update_time
              ? st.firmware_update_time
              : 0,
            firmware_update_request_time: st.firmware_update_request_time
              ? st.firmware_update_request_time
              : 0,
            description: st.description,
            last_data_receive_time: st.last_data_receive_time,
            status_code: st.status_code,
            device_sim_slot: st.device_sim_slot ? st.device_sim_slot : 0,
            device_error_list: st.device_error_list ? st.device_error_list : [],
            device_charging_status: st.device_charging_status
              ? st.device_charging_status
              : null,
            device_battery_percent: st.device_battery_percent
              ? st.device_battery_percent
              : null,
            device_power_status: st.device_power_status
              ? st.device_power_status
              : null,
            device_battery_status: st.device_battery_status
              ? st.device_battery_status
              : null,
            device_signal_strength: st.device_signal_strength
              ? st.device_signal_strength
              : null,
            device_status:
              st.status_code && st.status_code !== 7 ? "active" : "inactive",
            connectivity_status:
              st.status_code && st.status_code === 1 ? "online": "offline",
            device_auth_protocols: st.device_auth_protocols
              ? st.device_auth_protocols
              : {},
            product_model: st.product_model, //firmware-new-changes
            circuit_version: st.circuit_version, //firmware-new-changes
            parent_vendor_id:
              this.props.application_id === 12 &&
              st.vendor_id !== st.parent_vendor_id
                ? st.parent_vendor_id
                : false,
            goem_id: st.goem_id,
          });
        });
      }

      this.filtered_stations = filtered_stations_list;
      this.setState(
        {
          search_value: search_value,
          table_data: table_data,
          table_search: search_value,
        },
        () => this.subscribeForDeviceUpdates(),
      );
    } else {
      this.setState({
        table_data: [],
        search_value: search_value,
        table_search: search_value,
      });
    }
  }

  handleTableDataChange(pagination, filters, sorter, extra) {
    // console.log('pagination -> ', pagination);
    // console.log('filters -> ', filters);
    // console.log('sorter -> ', sorter);
    // console.log('extra -> ', extra);
    this.setState(
      {
        table_view_options: {
          pagination: pagination,
          filters: filters,
          sorter: sorter,
          extra: extra,
        },
      },
      () => this.subscribeForDeviceUpdates(),
    );
  }

  subscribeForDeviceUpdates(forceSubscribe) {
    // console.log('socket_ this.filtered_stations', this.filtered_stations);
    // console.log('socket_ subscribeForDeviceUpdates is called');
    let filtered_stations = this.filtered_stations;
    //convert stations names & city to lowercase for better comparision & sorting
    filtered_stations.map((station, i) => {
      filtered_stations[i].station =
        station.station && station.station.length
          ? station.station[0].toLowerCase()
          : [];
      filtered_stations[i].qr = station.qr_code.toLowerCase();
    });

    //remove archived stations
    let sorting_config = this.state.table_view_options.sorter,
      pagination_config = this.state.table_view_options.pagination,
      view_start_index,
      view_end_index;
    if (sorting_config !== undefined && !_isEqual(sorting_config, {})) {
      let sort_order = "asc";
      if (sorting_config.order === "descend") {
        sort_order = "desc";
      }
      if (sorting_config.columnKey === "station") {
        filtered_stations = _orderBy(filtered_stations, ["name"], [sort_order]);
      }
      if (sorting_config.columnKey === "qr") {
        filtered_stations = _orderBy(
          filtered_stations,
          ["qr_code"],
          [sort_order],
        );
      }
      if (sorting_config.columnKey === "active") {
        filtered_stations = _orderBy(
          filtered_stations,
          ["last_data_receive_time"],
          [sort_order],
        );
      }
      if (sorting_config.columnKey === "percent") {
        filtered_stations = _orderBy(
          filtered_stations,
          ["online_percentage"],
          [sort_order],
        );
      }
      if (sorting_config.columnKey === "availability") {
        filtered_stations = _orderBy(
          filtered_stations,
          ["data_availability"],
          [sort_order],
        );
      }
    }
    if (pagination_config !== undefined && !_isEqual(pagination_config, {})) {
      view_start_index =
        (pagination_config.current - 1) * pagination_config.pageSize;
      if (view_start_index > filtered_stations.length) {
        view_start_index =
          filtered_stations.length -
          (filtered_stations.length % pagination_config.pageSize);
      }
      view_end_index = view_start_index + pagination_config.pageSize;
    } else {
      //default pagination options
      view_start_index = 0;
      view_end_index =
        view_start_index + this.device_table_pagination_config.defaultPageSize;
    }
    // console.log('socket_ filtered_stations', filtered_stations);
    // console.log('socket_ view_start_index', view_start_index);
    // console.log('socket_ view_end_index', view_end_index);
    // console.log('socket_ pagination_config', pagination_config);
    filtered_stations = filtered_stations.slice(
      view_start_index,
      view_end_index,
    );
    // console.log('socket_ filtered_stations 1', filtered_stations);

    let currently_viewing_device_ids = _map(filtered_stations, (n) => n.id);

    // console.log(
    // 	'socket_ currently_viewing_device_ids',
    // 	currently_viewing_device_ids
    // );
    // console.log(
    // 	'socket_ this.currently_viewing_device_ids',
    // 	this.currently_viewing_device_ids
    // );
    //subscribe only if any changes in the device list is observed from the last one, otherwise this continuosly emits due to any changes in the state
    if (
      forceSubscribe ||
      !currently_viewing_device_ids.equals(this.currently_viewing_device_ids)
    ) {
      // console.log('socket_ called unsubscribe');
      if (typeof this.socket?.emit === "function") {
        this.socket.emit(
          "unsubscribe_from_devices",
          JSON.stringify({
            device_ids: this.currently_viewing_device_ids,
          }),
        );
      }
      this.currently_viewing_device_ids = currently_viewing_device_ids;
    }
    // console.log('filtered_stations after -> ', filtered_stations);
    // console.log(
    // 	'currently_viewing_device_ids -> ',
    // 	currently_viewing_device_ids
    // );
  }

  searchBoxFilter(value) {
    let that = this;
    if (value && value !== "") {
      that.getFilteredStations(value);
      // let parsed_url = queryString.parse(that.props.location.search);
      that.parseURL("search", value);
    } else {
      that.setState(
        {
          table_search: "",
          search_value: "",
        },
        () => {
          that.parseURL("search", "");
          that.getFilteredStations("");
        },
      );
    }
  }

  parseURL(filter, parsed) {
    // console.log('entered to parse', filter);
    // console.log('entered to parse_2', parsed);
    let parsed_url = queryString.parse(this.props.location.search);
    if (filter == "type") {
      // console.log('entered to parse_114');
      if (parsed != "") {
        parsed_url["type"] = "[" + parsed + "]";
      } else {
        delete parsed_url["type"];
      }
    }
    if (filter == "application") {
      // console.log('entered to parse_113');
      if (parsed != "") {
        parsed_url["application"] = "[" + parsed + "]";
      } else {
        delete parsed_url["application"];
      }
    }
    if (filter == "customer") {
      // console.log('entered to parse_112');
      if (parsed != "") {
        parsed_url["customer"] = "[" + parsed + "]";
      } else {
        delete parsed_url["customer"];
      }
    }
    if (filter == "search") {
      // console.log('entered to parse_111');
      if (parsed != "") {
        parsed_url["search"] = parsed;
      } else {
        delete parsed_url["search"];
      }
    }
    let query_url = "";
    if (
      parsed_url["type"] &&
      parsed_url["application"] &&
      parsed_url["customer"] &&
      parsed_url["search"]
    ) {
      // console.log('entered to parse_3');
      query_url =
        "type=" +
        parsed_url["type"] +
        "&" +
        "application=" +
        parsed_url["application"] +
        "&" +
        "customer=" +
        parsed_url["customer"] +
        "&" +
        "search=" +
        parsed_url["search"];
    } else if (
      parsed_url["type"] &&
      parsed_url["application"] &&
      parsed_url["customer"]
    ) {
      // console.log('entered to parse_4');
      query_url =
        "type=" +
        parsed_url["type"] +
        "&" +
        "application=" +
        parsed_url["application"] +
        "&" +
        "customer=" +
        parsed_url["customer"];
    } else if (
      parsed_url["type"] &&
      parsed_url["application"] &&
      parsed_url["search"]
    ) {
      // console.log('entered to parse_6');
      query_url =
        "type=" +
        parsed_url["type"] +
        "&" +
        "application=" +
        parsed_url["application"] +
        "&" +
        "search=" +
        parsed_url["search"];
    } else if (parsed_url["type"] && parsed_url["application"]) {
      // console.log('entered to parse_5');
      query_url =
        "type=" +
        parsed_url["type"] +
        "&" +
        "application=" +
        parsed_url["application"];
    } else if (
      parsed_url["type"] &&
      parsed_url["customer"] &&
      parsed_url["search"]
    ) {
      // console.log('entered to parse_8');
      query_url =
        "type=" +
        parsed_url["type"] +
        "&" +
        "customer=" +
        parsed_url["customer"] +
        "&" +
        "search=" +
        parsed_url["search"];
    } else if (parsed_url["type"] && parsed_url["customer"]) {
      // console.log('entered to parse_7');
      query_url =
        "type=" +
        parsed_url["type"] +
        "&" +
        "customer=" +
        parsed_url["customer"];
    } else if (parsed_url["type"] && parsed_url["search"]) {
      // console.log('entered to parse_10');
      query_url =
        "type=" + parsed_url["type"] + "&" + "search=" + parsed_url["search"];
    } else if (parsed_url["type"]) {
      // console.log('entered to parse_9');
      query_url = "type=" + parsed_url["type"];
    } else if (
      parsed_url["application"] &&
      parsed_url["customer"] &&
      parsed_url["search"]
    ) {
      // console.log('entered to parse_14');
      query_url =
        "application=" +
        parsed_url["application"] +
        "&" +
        "customer=" +
        parsed_url["customer"] +
        "&" +
        "search=" +
        parsed_url["search"];
    } else if (parsed_url["application"] && parsed_url["customer"]) {
      // console.log('entered to parse_13');
      query_url =
        "application=" +
        parsed_url["application"] +
        "&" +
        "customer=" +
        parsed_url["customer"];
    } else if (parsed_url["application"] && parsed_url["search"]) {
      // console.log('entered to parse_12');
      query_url =
        "application=" +
        parsed_url["application"] +
        "&" +
        "search=" +
        parsed_url["search"];
    } else if (parsed_url["application"]) {
      // console.log('entered to parse_11');
      query_url = "application=" + parsed_url["application"];
    } else if (parsed_url["customer"] && parsed_url["search"]) {
      // console.log('entered to parse_16');
      query_url =
        "customer=" +
        parsed_url["customer"] +
        "&" +
        "search=" +
        parsed_url["search"];
    } else if (parsed_url["customer"]) {
      // console.log('entered to parse_15');
      query_url = "customer=" + parsed_url["customer"];
    } else if (parsed_url["search"]) {
      // console.log('entered to parse_17');
      query_url = "search=" + parsed_url["search"];
    }
    if (this.props.is_application_filter) {
      this.props.history.push(
        this.platform_slug +
          "/customer-management/" +
          this.props.customer_id +
          "/applications/" +
          this.state.selected_app +
          "/devices/list?" +
          query_url,
      );
    } else {
      this.props.history.push(
        this.platform_slug + "/devices/assigned?" + query_url,
      );
    }
  }

  onChange(value) {
    // console.log('onChange ', value);
    this.setState({ value });
  }

  closeAddStationModal(e) {
    // console.log(e);
    this.setState({
      add_station: false,
      city_id: "",
      station_name: "",
      stations_device: "",
      station_lat: "",
      station_long: "",
    });
  }

  changeCity(e) {
    this.setState({
      city_id: e,
    });
  }

  changeStnName(e) {
    this.setState({
      station_name: e,
    });
  }

  changeDevice(e) {
    this.setState({
      stations_device: e,
    });
  }

  changeLat(e) {
    this.setState({
      station_lat: e,
    });
  }

  changeLong(e) {
    this.setState({
      station_long: e,
    });
  }

  addNewDevice() {
    this.setState({ add_station: true });
    // console.log(
    // 	'aaavvvccc',
    // 	JSON.stringify({
    // 		city_id: this.state.city_id,
    // 		name: this.state.station_name,
    // 		device_id: this.state.stations_device,
    // 		latitude: this.state.station_lat,
    // 		longitude: this.state.station_long,
    // 	})
    // );
    this.closeAddStationModal();
    // this.socket.emit('add_new_station', JSON.stringify({
    // 	city_id: this.state.city_id,
    // 	station_name: this.state.station_name,
    // 	stations_device: this.state.stations_device,
    // 	station_lat: this.state.station_lat,
    // 	station_long: this.state.station_long
    // }));
    // this.closeAddStationModal();
    // console.log(this.state.city_id, this.state.device_cities[this.state.city_id]);
  }

  applyDeviceTypeFilter(val, label, extra) {
    // console.log('applyDeviceTypeFilter', val);
    let device_type_filter_ids = [];
    if (val && val.length) {
      val.map((data_key) => {
        device_type_filter_ids.push(parseInt(data_key.split("-")[2]));
      });
    }
    this.setState(
      {
        deviceTypeFilterValue: val,
        // selected_devices: [],
        // selected_rows: [],
        device_type_filter_ids: device_type_filter_ids,
      },
      () => {
        this.parseURL("type", device_type_filter_ids);
        this.filterTableData();
      },
    );
  }

  applyApplicationFilter(val, label, extra) {
    let application_filter_ids = [];
    if (val && val.length) {
      val.map((data_key) => {
        application_filter_ids.push(parseInt(data_key.split("-")[2]));
      });
    }
    this.setState(
      {
        applicationFilterValue: val,
        // selected_devices: [],
        // selected_rows: [],
        application_filter_ids: application_filter_ids,
      },
      () => {
        this.parseURL("application", application_filter_ids);
        this.filterTableData();
      },
    );
  }

  updateFirmware() {
    this.setState(
      {
        update_firmware: true,
        firmware_update_modal_loading: this.state.selected_row[0]
          ?.application_id
          ? true
          : false,
      },
      () => {
        if (this.state.single_update) {
          if (
            this.state.selected_row.length &&
            this.state.selected_row[0].application_id
          ) {
            this.getFirmwareList(this.state.selected_row[0]);
          }
        } else {
          if (this.state.selected_rows.length) {
            this.getFirmwareList(this.state.selected_rows[0]);
          }
        }
      },
    );
  }

  closeupdateFirmware() {
    this.setState({
      single_update: false,
      update_firmware: false,
      selected_radio_value: "release",
      firmware_value: undefined,
      selected_version_features: [],
      firmware_update_modal_loading: false,
      firm_application_value: undefined,
    });
  }

  updateDeviceFirmware() {
    // console.log(
    // 	'updateDeviceFirmware',
    // 	this.state.device_updated_firmware_id
    // );
    if (this.validateFirmwareFields()) {
      console.log("validate_firmware", this.state.firmware_value);
      this.socket.emit(
        "update_device_firmware",
        JSON.stringify({
          firmware_id: this.state.device_updated_firmware_id,
          device_ids: this.state.single_update
            ? this.state.selected_device
            : this.state.selected_devices,
        }),
      );
      this.setState({
        update_firmware: false,
        single_update: false,
        selected_radio_value: "release",
        firmware_value: undefined,
        firmware_update_modal_loading: false,
      });
    }
  }

  selectFirmware(value) {
    this.setState(
      {
        selected_version_features: ["..."],
        firmware_value: value,
        device_updated_firmware_id: value,
      },
      async () => {
        let response = await retriveVendorFirmwareDetails(
          this.props.client_id,
          value,
        );
        if (response.status === "success") {
          this.setState({
            selected_version_features: response.release_log.features,
          });
        } else {
          this.setState({
            selected_version_features: [],
          });
        }
      },
    );
  }

  onSearchFirmware(val) {
    // console.log('search:', val);
  }

  clearSearchValue() {
    this.setState(
      {
        table_search: "",
        search_value: "",
      },
      () => {
        this.parseURL("search", "");
        this.getFilteredStations("");
      },
    );
  }

  openDebug(row_data) {
    // console.log('openDebug', row_data);
    this.setState({
      // visible_debug_drawer: true,
      row_data: row_data,
    });
    if (this.props.is_application_filter) {
      this.props.history.push(
        this.platform_slug +
          "/customer-management/" +
          this.props.customer_id +
          "/applications/" +
          this.state.selected_app +
          "/devices/" +
          row_data.id +
          "/debug",
      );
    } else {
      this.props.history.push(
        this.platform_slug + "/devices/assigned/" + row_data.id + "/debug",
      );
    }
  }

  closeDebugDrawer() {
    this.setState({
      visible_debug_drawer: false,
    });
    if (this.props.is_application_filter) {
      this.props.history.push(
        this.platform_slug +
          "/customer-management/" +
          this.props.customer_id +
          "/applications/" +
          this.state.selected_app +
          "/devices/list",
      );
    } else {
      this.props.history.push(this.platform_slug + "/devices/assigned/");
    }
  }

  openAssigntoCustomerDrawer() {
    this.setState({
      add_device_to_customer: true,
    });
  }

  async unassignDeviceFunction(data) {
    let clientId = this.props.client_id;
    /*if (this.state.selected_vendor_id) {
			clientId = this.state.selected_vendor_id;
		}*/
    let that = this;
    let response = await unassignDevice(data, clientId);
    if (response.status === 403) {
      that.setState({
        drawer_unauthorised_access: true,
        drawer_unauthorised_access_msg: response.message,
      });
    } else if (response.status === "success") {
      that.openNotification("success", "Device/s unassigned successfully");
      that.fetchDeviceList();
      that.setState({
        selectedRows: undefined,
        selectedRowKeys: undefined,
        selected_devices: undefined,
        selected_rows: undefined,
        selected_row_keys: [],
      });
    } else {
      that.openNotification("error", response.message);
      that.setState({
        drawer_unauthorised_access: false,
        error_API: true,
        error_API_msg: response.message,
      });
    }
  }

  unassignedOnCancel() {
    this.setState({
      unassign_mobile_devices_modal: false,
    });
  }

  unassignedOnOk() {
    if (this.state.selected_rows && this.state.selected_rows.length) {
      let deviceLists = [];
      this.state.selected_rows.map((row) => {
        deviceLists.push({
          id: row.id,
          customer_id: row.customer_id ? row.customer_id : row.vendor_id,
          serial_no: row.qr,
        });
      });
      let data = {
        devices: deviceLists,
        client_id: this.props.client_id,
      };
      this.setState({
        unassign_mobile_devices_modal: false,
        mobile_drawer: false,
      });
      this.unassignDeviceFunction(data);
    }
  }

  unassignDevice(a, b) {
    let that = this;
    that.setState(
      {
        selected_rows: b ? a : this.state.selected_rows,
        unassign_mobile_devices_modal: true,
      },
      () => {
        if (window.innerWidth > 576) {
          AntConfirmModal({
            title: "Do you want to un-assign all selected device/s",
            content: "",
            onOk: () => that.unassignedOnOk(),
          });
        }
      },
    );
  }

  async updateStatusFunction(data) {
    // console.log('updateStatusFunction_', data);
    let that = this;
    let response = await deviceStatusUpdate(data);
    if (response.status === 403) {
      that.setState({
        drawer_unauthorised_access: true,
        drawer_unauthorised_access_msg: response.message,
      });
    } else if (response.status === "success") {
      this.setState(
        {
          remove_loading: false,
        },
        () => {
          that.fetchDeviceList();
          that.openNotification(
            "success",
            "Device/s status updated successfully",
          );
        },
      );
    } else {
      that.openNotification("error", response.message);
      that.setState({
        drawer_unauthorised_access: false,
        error_API: true,
        error_API_msg: response.message,
      });
    }
  }

  updateStatusOnOk(key, selectedDevice = []) {
    // console.log('updateStatusOnOk_', selectedDevice);
    if (selectedDevice.length) {
      let data = {
        is_blocked: key == "block" ? true : false,
        device_ids: selectedDevice,
        client_id: this.props.client_id,
      };

      this.updateStatusFunction(data);
    } else if (this.state.selected_rows && this.state.selected_rows.length) {
      let deviceIds = [];
      this.state.selected_rows.map((row) => {
        deviceIds.push(row.id);
      });
      let data = {
        is_blocked: key == "block" ? true : false,
        device_ids: deviceIds,
        client_id: this.props.client_id,
      };

      this.updateStatusFunction(data);
    }
  }

  updateStatus(key) {
    let that = this;
    AntConfirmModal({
      title: "Do you want to " + key + " all selected device/s",
      content: "",
      onOk: () => that.updateStatusOnOk(key),
    });
  }

  openCustomCommand(deviceId, row_data) {
    /*if (this.props.is_application_filter) {
			this.props.history.push(
				this.platform_slug +
					'/customer-management/' +
					this.props.customer_id +
					'/applications/' +
					this.state.selected_app +
					'/devices/' +
					deviceId +
					'/custom-command'
			);
		} else {
			this.props.history.push(
				this.platform_slug +
					'/devices/assigned/' +
					deviceId +
					'/custom-command'
			);
		}*/

    let routeLink = "";
    if (this.props.is_application_filter) {
      routeLink =
        this.iotBasePath +
        "/enterprise/" +
        this.props.client_id +
        this.platform_slug +
        "/customer-management/" +
        this.props.customer_id +
        "/applications/" +
        this.state.selected_app +
        "/devices/" +
        deviceId +
        "/custom-command?type_id=" +
        row_data.type_id;
    } else {
      routeLink =
        this.iotBasePath +
        "/enterprise/" +
        this.props.client_id +
        this.platform_slug +
        "/devices/assigned/" +
        deviceId +
        "/custom-command?type_id=" +
        row_data.type_id;
    }
    window.open(routeLink, "_blank");
  }

  applicationDrop(e) {
    this.setState(
      {
        selected_app: e,
        remove_loading: false,
      },
      () => {
        this.generateTableData();
      },
    );
  }

  openDeviceDebug(row_data, raw_log = false) {
    this.setState({
      //show_device_debug: true,
      selected_row_data: row_data,
    });
    // let deviceDetails = {
    // 	customer_id: this.state.selected_row_data.customer_id,
    // 	application_id: this.state.selected_row_data.application_id,
    // 	device_id: this.state.selected_row_data.id,
    // 	device_qr_code: this.state.selected_row_data.qr,
    // };
    let routeLink = "",
      query = "";
    if (raw_log) {
      query = "&tab=raw_log";
    }
    if (this.props.is_application_filter) {
      routeLink =
        this.iotBasePath +
        "/enterprise/" +
        this.props.client_id +
        this.platform_slug +
        "/customer-management/" +
        this.props.customer_id +
        "/applications/" +
        this.state.selected_app +
        "/devices/" +
        row_data.id +
        "/debug?" +
        "customer_id=" +
        row_data.customer_id +
        "&application_id=" +
        row_data.application_id +
        "&device_id=" +
        row_data.id +
        "&device_qr_code=" +
        row_data.qr +
        "&type_name=" +
        row_data.type_name +
        query;
    } else {
      routeLink =
        this.iotBasePath +
        "/enterprise/" +
        this.props.client_id +
        this.platform_slug +
        "/devices/assigned/" +
        row_data.id +
        "/debug?" +
        "customer_id=" +
        row_data.customer_id +
        "&application_id=" +
        row_data.application_id +
        "&device_id=" +
        row_data.id +
        "&device_qr_code=" +
        row_data.qr +
        "&type_name=" +
        row_data.type_name +
        query;
    }
    window.open(routeLink, "_blank");
  }

  closeDeviceDebug() {
    this.setState({
      show_device_debug: false,
      selected_row_data: undefined,
    });
  }

  async generateTableData() {
    let response = await devicesList(
      this.props.customer_id,
      this.state.selected_app,
    );
    if (response.status === "success") {
      // console.log('device_list__', response);
      let data = response;
      //create device type filter
      let child_data = [],
        deviceTypeFilter = [],
        applicationFilter = [],
        customerFilter = [];
      /*if (data.device_types && data.device_types.length) {
				data.device_types.map((type) => {
					console.log('type1_', type)
					deviceTypeFilter.push({
						'title': type.name,
						'value': '0-1' + '-' + type.id,
						'key': '0-1' + '-' + type.id,
					});
				});
			}

			child_data = [];
			if (data.applications && data.applications.length) {
				data.applications.map((app) => {
					applicationFilter.push({
						'title': app.name,
						'value': '0-1' + '-' + app.id,
						'key': '0-1' + '-' + app.id,
					});
				});
			}

			child_data = [];
			if (data.clients && data.clients.length) {
				data.clients.map((client) => {
					customerFilter.push({
						'title': client.name,
						'value': '0-1' + '-' + client.id,
						'key': '0-1' + '-' + client.id,
					});
				});
			}
			customerFilter.push({
				title: 'All Customers',
				value: '0-1',
				key: '0-1',
				children: child_data
			});*/
      this.setState({
        // deviceTypeFilter: deviceTypeFilter,
        // customerFilter: customerFilter,
        // applicationFilter: applicationFilter,
        device_type: data.device_types,
        device_lists: data,
        remove_loading: true,
      });
      this.device_details = data.devices;
      this.filterTableData();
    }
  }

  filterTableData() {
    let tableData = [...this.device_details];

    if (this.state.selected_vendor_id) {
      tableData = tableData.filter(
        (device) =>
          device.vendor_id === this.state.selected_vendor_id ||
          device.parent_vendor_id === this.state.selected_vendor_id ||
          device.goem_id === this.state.selected_vendor_id,
      );
      // tableData = _filter(tableData, {
      // 	vendor_id: this.state.selected_vendor_id,
      // });
    }

    if (this.state.selectedDealers?.length) {
      tableData = tableData.filter((device) =>
        this.state.selectedDealers.includes(device.vendor_id),
      );
    }

    if (this.state.selected_client_id) {
      const linkedClientIds =
        ASSET_CONSTANTS.LINKED_CLIENT_IDS[this.state.selected_client_id] || [];
      const selectedClientIds = [
        this.state.selected_client_id,
        ...linkedClientIds,
      ];
      tableData = tableData.filter((row) =>
        selectedClientIds.includes(row.client_id),
      );
    }

    if (this.state.selected_app_id && this.state.selected_app_id.length) {
      tableData = _filter(
        tableData,
        (v) => _indexOf(this.state.selected_app_id, v.application_id) !== -1,
      );
    }

    if (this.state.selected_type && this.state.selected_type.length) {
      /*this.state.selected_type.map((typeId) => {
				tableData = _filter(tableData, {type_id: typeId});
			});*/
      tableData = _filter(
        tableData,
        (v) => _indexOf(this.state.selected_type, v.type_id) !== -1,
      );
    }

    if (this.state.selected_connectivity_status) {
      if (this.state.selected_connectivity_status === 1) {
        tableData = _filter(tableData, {
          status_code: this.state.selected_connectivity_status,
        });
      } else {
        tableData = _filter(
          tableData,
          (v) => _indexOf([2, 3, 4, 5, 6, 7], v.status_code) !== -1,
        );
      }
      // console.log('selected_connectivity_status_', this.state.selected_connectivity_status);
    }

    if (this.state.selected_device_status) {
      // console.log('selected_connectivity_status_ selected_device_status', this.state.selected_device_status);
      if (this.state.selected_device_status === 7) {
        tableData = _filter(tableData, {
          status_code: this.state.selected_device_status,
        });
      } else {
        tableData = _filter(
          tableData,
          (v) => _indexOf([1, 2, 3, 4, 5, 6], v.status_code) !== -1,
        );
      }
    }

    // console.log('table_data_1', tableData);
    this.setState(
      {
        filtered_table_data: tableData,
        loading: false,
      },
      () => {
        this.getFilteredStations(this.state.search_value);
      },
    );
  }

  async getClientCustomerList() {
    let customerListResult = await retriveCustomerList(
      this.state.selected_vendor_id,
    );

    if (customerListResult && customerListResult.status == "success") {
      this.setState({
        vendor_client_list: customerListResult.customers,
        vendor_application_list: customerListResult.applications,
      });
    }

    this.filterTableData();
  }

  getClientApplicationList() {
    let applicationList = [],
      applicationArr = this.state.application_list,
      customerArr = this.state.client_list,
      selectedId = this.state.selected_client_id;
    if (this.state.selected_vendor_id && !this.state.selected_client_id) {
      applicationArr = this.state.vendor_application_list;
      customerArr = this.state.vendor_client_list;
      selectedId = this.state.selected_vendor_id;
    }

    let selectedCustDetails = _find(customerArr, { id: selectedId });
    // console.log('selectedCustDetails_', selectedCustDetails);
    // console.log('selectedCustDetails_ 1', applicationArr);
    if (selectedCustDetails) {
      applicationArr.map((appDet) => {
        if (
          selectedCustDetails.applications.includes(
            appDet.id, //.toString()
          ) ||
          selectedCustDetails.access_applications.includes(
            appDet.id, //.toString()
          )
        ) {
          applicationList.push(appDet);
        }
      });
    }

    this.setState({
      client_application_list: applicationList,
    });
    this.filterTableData();
  }

  singleDeviceFirmwareUpdate(selectedRow) {
    // if (selectedRow[0].application_id == 17) return;
    console.log("singleDeviceFirmwareUpdate_", selectedRow);
    let that = this;
    that.setState(
      {
        //firmware_selected_row: selectedRow,
        selected_device: [selectedRow[0].id],
        selected_row: selectedRow,
        single_update: true,
      },
      () => {
        // console.log('singleDeviceFirmwareUpdate_ 1', that.state.selected_devices);
        // console.log('singleDeviceFirmwareUpdate_ 2', that.state.selected_row);

        if (
          that.state.device_lists &&
          that.state.selected_row &&
          that.state.selected_row.length
        ) {
          // let firmwares = that.state.device_lists.firmwares.reverse();
          let firmwares = _orderBy(
            that.state.device_lists.firmwares,
            ["id"],
            ["desc"],
          );
          that.firmwareSelectionOptions = firmwares
            .map((firmware) => {
              if (
                that.state.selected_row[0].type_id === firmware.device_type_id
              ) {
                return (
                  <AntOption value={firmware.id}>{firmware.version}</AntOption>
                );
              }
            })
            .filter(Boolean);
        }
        that.updateFirmware();
      },
    );
  }

  openConfiguration(routeLink) {
    // this.props.history.push(routeLink);
    // console.log('openConfiguration_', routeLink);
    // this.props.history.push(routeLink);
    window.open(routeLink, "_blank");
  }

  clearAllFilter() {
    this.setState(
      {
        selected_vendor_id: undefined,
        selected_client_id: undefined,
        selected_app_id: [],
        selected_type: [],
        search_value: "",
        selected_connectivity_status: undefined,
        selected_device_status: undefined,
        loading: true,
      },
      () => {
        this.filterTableData();
      },
    );
  }

  enableCopyToClip(value = false, responseText) {
    if (value) {
      return (
        <div
          className="copy-btn"
          onClick={() => this.copyToClipboard(value, responseText)}
        >
          <CopyOutlined className="mar-rt-5" />
          <span className="copy-text">Copy</span>
        </div>
      );
    } else {
      return (
        <div className="copy-btn disabled">
          <CopyOutlined className="mar-rt-5" />
          <span className="copy-text">Copy</span>
        </div>
      );
    }
  }

  processProtocolData(device_auth_protocols) {
    let protocolOptions = Object.keys(device_auth_protocols),
      renderProtocol = "";
    const tab_option = {
      showSearch: false,
      allowClear: false,
      showArrow: true,
      value: this.state.selectedProtocol
        ? this.state.selectedProtocol
        : undefined,
      placeholder: "Select a protocol",
    };
    if (protocolOptions && protocolOptions.length) {
      renderProtocol = (
        <>
          <div className="data-container">
            <span className="data-value-selector">
              <AntSelect
                {...tab_option}
                onChange={(value) => this.setState({ selectedProtocol: value })}
              >
                {protocolOptions.length &&
                  protocolOptions.map((e) => (
                    <AntOption value={e}>{e}</AntOption>
                  ))}
              </AntSelect>
            </span>
          </div>
          {(() => {
            let selectedPacket =
              this.state.selectedProtocol &&
              device_auth_protocols &&
              device_auth_protocols[this.state.selectedProtocol]
                ? device_auth_protocols[this.state.selectedProtocol]
                : {};
            return (
              <>
                <div className="data-container">
                  <span className="data-label">Domain : </span>
                  <span className="data-value  key-value">
                    {selectedPacket.domain || "NA"}
                  </span>
                  {this.enableCopyToClip(
                    selectedPacket.domain,
                    "Domain copied to clipboard",
                  )}
                </div>
                <div className="data-container">
                  <span className="data-label">IP : </span>
                  <span className="data-value  key-value">
                    {selectedPacket.ip || "NA"}
                  </span>
                  {this.enableCopyToClip(
                    selectedPacket.ip,
                    "IP copied to clipboard",
                  )}
                </div>
                <div className="data-container">
                  <span className="data-label">Port : </span>
                  <span className="data-value  key-value">
                    {selectedPacket.port || "NA"}
                  </span>
                  {this.enableCopyToClip(
                    selectedPacket.port,
                    "Port copied to clipboard",
                  )}
                </div>
                <div className="data-packet-title">Authentication Packet :</div>
                <div className="data-container data-container-packet">
                  <div className="packet-val">
                    {selectedPacket.auth_packet || "NA"}
                  </div>
                  {this.enableCopyToClip(
                    selectedPacket.auth_packet,
                    "Authentication Packet copied to clipboard",
                  )}
                </div>
              </>
            );
          })()}
        </>
      );
    }
    return renderProtocol;
  }

  async getFirmwareList(firmware_row) {
    let app_id =
        firmware_row.application_id === 17
          ? 16
          : firmware_row.application_id || this.state.firm_application_value,
      device_type_id = firmware_row.type_id,
      product_model = parseInt(firmware_row.product_model),
      circuit_version = parseInt(firmware_row.circuit_version);

    let application_query = "application=" + app_id;
    let response = await retriveVendorFirmwareList(
      this.props.client_id,
      application_query,
    );

    if (response.status === 403) {
      this.setState({
        unauthorised_access: true,
        unauthorised_access_msg: response.message,
        firmware_update_modal_loading: false,
      });
    } else if (response.status === "success") {
      let release_firmware_list = [],
        dev_firmware_list = [];
      if (response.device_types && response.device_types.length) {
        let total_firmware_list = _find(response.device_types, function (o) {
          return o.id == device_type_id;
        });
        if (
          total_firmware_list &&
          total_firmware_list.firmwares &&
          total_firmware_list.firmwares.length
        ) {
          total_firmware_list.firmwares.map((firm) => {
            if (
              (device_type_id !== 1 ||
                firm.product_models?.includes(product_model)) &&
              (!this.state.accepted_type_ids.includes(device_type_id) ||
                firm.circuit_versions?.includes(circuit_version) ||
                circuitExceptionDevices.includes(firmware_row.id))
            ) {
              if (firm.is_release == 1) {
                release_firmware_list.push(firm);
              } else if (firm.is_release == 0) {
                dev_firmware_list.push(firm);
              }
            }
          });
        }
      }
      this.setState({
        release_firmware_list: release_firmware_list,
        dev_firmware_list: dev_firmware_list,
        firmware_update_modal_loading: false,
      });
    } else {
      this.openNotification("error", response.message);
      this.setState({
        unauthorised_access: false,
        error_API: true,
        error_API_msg: response.message,
        firmware_update_modal_loading: false,
      });
    }
  }

  onRadioFirmwareChange(value) {
    this.setState({
      selected_version_features: [],
      selected_radio_value: value,
      firmware_value: undefined,
    });
  }

  selectFirmApplication(value) {
    this.setState(
      {
        selected_version_features: [],
        firm_application_value: value,
        selected_radio_value: "release",
        firmware_value: undefined,
        firmware_update_modal_loading: true,
      },
      () => {
        if (this.state.single_update) {
          if (this.state.selected_row.length) {
            this.getFirmwareList(this.state.selected_row[0]);
          }
        } else {
          if (this.state.selected_rows && this.state.selected_rows.length) {
            this.getFirmwareList(this.state.selected_rows[0]);
          }
        }
      },
    );
  }

  getfirmwareModalBody(selected_row) {
    let firmwareOptions = [];
    const applicationOptions = [];
    if (this.state.selected_radio_value == "release") {
      if (this.state.release_firmware_list.length) {
        let sorted_release_firmware_list = _orderBy(
          this.state.release_firmware_list,
          ["id"],
          ["desc"],
        );
        firmwareOptions = sorted_release_firmware_list.map((firm) => {
          return <AntOption value={firm.id}>{firm.version}</AntOption>;
        });
      }
    } else if (this.state.selected_radio_value == "dev") {
      if (this.state.dev_firmware_list.length) {
        let sorted_dev_firmware_list = _orderBy(
          this.state.dev_firmware_list,
          ["id"],
          ["desc"],
        );
        firmwareOptions = sorted_dev_firmware_list.map((firm) => {
          return (
            <AntOption value={firm.id}>
              {firm.version}
              {" (Build no: "}
              {firm.build_no}
              {")"}
            </AntOption>
          );
        });
      }
    }
    if (
      !selected_row.application_id &&
      this.state.device_lists.applications &&
      this.state.device_lists.applications.length
    ) {
      let sorted_applications = _orderBy(
        this.state.device_lists.applications,
        ["name"],
        ["asc"],
      );
      let app_array = null,
        curr_device_type = null;
      if (this.state.device_type && this.state.device_type.length) {
        curr_device_type = _find(this.state.device_type, {
          id: selected_row.type_id,
        });
        app_array = curr_device_type ? curr_device_type.apps : null;
      }
      console.log(
        "GdaiDevices ==> ",
        sorted_applications,
        this.state.device_types,
        selected_row,
      );
      sorted_applications.map((app) => {
        if (app.id !== 17) {
          if (app_array && app_array.length && app_array.includes(app.id)) {
            applicationOptions.push({
              label: app.name,
              value: app.id,
            });
          }
        }
      });
    }
    if (selected_row.application_id) {
      applicationOptions.push({
        label: selected_row.application,
        value: selected_row.application_id,
      });
    }
    return (
      <div className="firmware-modal-body">
        <AntRow gutter={[10, 20]}>
          <AntCol xs={24} sm={9} className="firmware-label">
            Application* :{" "}
          </AntCol>
          <AntCol xs={24} sm={15}>
            <AntSelect
              value={
                selected_row.application_id || this.state.firm_application_value
              }
              disabled={selected_row.application_id ? true : false}
              options={applicationOptions}
              placeholder="Select application"
              onChange={(e) => this.selectFirmApplication(e)}
            />
          </AntCol>
          <AntCol xs={24} sm={9} className="firmware-label">
            Build Type*
          </AntCol>
          <AntCol xs={24} sm={15}>
            <AntRadioGroup
              value={this.state.selected_radio_value}
              disabled={this.state.firmware_update_modal_loading}
              onChange={(e) => this.onRadioFirmwareChange(e.target.value)}
            >
              <AntRadio value={"release"}>Release</AntRadio>
              <AntRadio value={"dev"} style={{ marginLeft: "20px" }}>
                Dev
              </AntRadio>
            </AntRadioGroup>
          </AntCol>
          <AntCol xs={24} sm={9} className="firmware-label">
            Firmware Version* :{" "}
          </AntCol>
          <AntCol xs={24} sm={15}>
            <AntSelect
              showSearch
              placeholder="Select firmware version"
              optionFilterProp="children"
              loading={this.state.firmware_update_modal_loading}
              disabled={this.state.firmware_update_modal_loading}
              value={this.state.firmware_value}
              onChange={(e) => this.selectFirmware(e)}
              filterOption={(input, option) => {
                const label = Array.isArray(option.props.children)
                  ? React.Children.toArray(option.props.children)
                      .map(child => {
                        if (typeof child === 'string') {
                          return child;
                        }
                        return React.isValidElement(child) ? child.props.children : '';
                      })
                      .join(' ')
                      .toLowerCase()
                  : String(option.props.children).toLowerCase();
                return label.includes(input.toLowerCase());
              }}
            >
              {firmwareOptions}
            </AntSelect>
          </AntCol>
          <AntCol xs={24} sm={9}>
            Firmware Features:
          </AntCol>
          <AntCol xs={24} sm={15}>
            <section className="firmware-features">
              {this.state.selected_version_features?.[0]
                ? this.state.selected_version_features[0]
                : "NA"}
            </section>
          </AntCol>
        </AntRow>
      </div>
    );
    //}
  }

  //validate firmware mandatory fields
  validateFirmwareFields() {
    return this.state.firmware_value ? true : false;
  }

  getmodalCustomBody(row_value) {
    return (
      <div className="device-form">
        <div className="data-container">
          <span className="data-label">Device Type : </span>
          <span className="data-value type-name">{row_value.type_name}</span>
        </div>
        <div className="data-container">
          <span className="data-label">Serial Id : </span>
          <span className="data-value qr-value">{row_value.qr}</span>
        </div>
        <div className="data-container">
          <span className="data-label">Key : </span>
          <span className="data-value key-value">{row_value.device_key}</span>
          {(() => {
            if (row_value.device_key !== "NA") {
              return (
                <div
                  className="copy-btn"
                  onClick={() =>
                    this.copyToClipboard(
                      row_value.device_key,
                      "Device key copied to clipboard",
                    )
                  }
                >
                  <CopyOutlined className="mar-rt-5" />
                  <span className="copy-text">Copy</span>
                </div>
              );
            } else {
              return (
                <div className="copy-btn disabled">
                  <CopyOutlined className="mar-rt-5" />
                  <span className="copy-text">Copy</span>
                </div>
              );
            }
          })()}
        </div>
        {this.processProtocolData(row_value.device_auth_protocols)}
        {/* <div className="data-container data-container-packet">
					<div className="data-packet-title">
						Authentication Packet Field :{' '}
					</div>
					<div className="packet-val"></div>
				</div> */}
      </div>
    );
  }
  showDeviceKey(row_value) {
    // let confirm = {
    // 	title: (
    // 		<div className="display-flex aln-cntr font-bold">
    // 			<img src={third_party_grey} height={20} width={20} />
    // 			<span className="mar-lt-20">3rd Party Device</span>
    // 		</div>
    // 	),
    // 	className: 'device-key-modal',
    // 	maskClosable: false,
    // 	content: (
    // 		<div className="device-form">
    // 			<div className="data-container">
    // 				<span className="data-label">Device Type : </span>
    // 				<span className="data-value type-name">
    // 					{row_value.type_name}
    // 				</span>
    // 			</div>
    // 			<div className="data-container">
    // 				<span className="data-label">Serial Id : </span>
    // 				<span className="data-value qr-value">
    // 					{row_value.qr}
    // 				</span>
    // 			</div>
    // 			<div className="data-container">
    // 				<span className="data-label">Key : </span>
    // 				<span className="data-value key-value">
    // 					{row_value.device_key}
    // 				</span>
    // 				{(() => {
    // 					if (row_value.device_key !== 'NA') {
    // 						return (
    // 							<div
    // 								className="copy-btn"
    // 								onClick={() =>
    // 									this.copyToClipboard(
    // 										row_value.device_key
    // 									)
    // 								}
    // 							>
    // 								<CopyOutlined className="mar-rt-5" />
    // 								<span className="copy-text">Copy</span>
    // 							</div>
    // 						);
    // 					} else {
    // 						return (
    // 							<div className="copy-btn disabled">
    // 								<CopyOutlined className="mar-rt-5" />
    // 								<span className="copy-text">Copy</span>
    // 							</div>
    // 						);
    // 					}
    // 				})()}
    // 			</div>
    // 		</div>
    // 	),
    // 	okText: '',
    // 	cancelText: 'Close',
    // };

    // AntConfirmModal(confirm);
    let defaultProtocol = "";
    Object.keys(row_value.device_auth_protocols).map((protocols) => {
      if (row_value.device_auth_protocols[protocols].is_default === 1) {
        defaultProtocol = protocols;
      }
    });
    this.setState(
      {
        third_party_row_value: row_value,
        selectedProtocol: defaultProtocol,
      },
      () => this.toggle3rdPartyModal(),
    );
  }
  toggle3rdPartyModal() {
    this.setState((p) => {
      return {
        ...p,
        third_party_modal_visible: !p.third_party_modal_visible,
      };
    });
  }

  copyToClipboard(deviceKey, responseText = "Copied to clipboard") {
    let el = document.createElement("textarea");
    el.value = deviceKey;
    el.setAttribute("readonly", "");
    el.style.position = "absolute";
    el.style.left = "-9999px";
    document.body.appendChild(el);
    el.select();
    document.execCommand("copy");
    document.body.removeChild(el);
    this.openNotification("success", responseText);
  }

  applyFilterSelect(value, index) {
    let that = this;
    // console.log('applyFilter_ value', value);
    // console.log('applyFilter_ label', label);
    // console.log('applyFilter_ extra', extra);
    // console.log('applyFilter_ index', index);

    if (
      this.props.location &&
      Object.keys(this.props.location).length &&
      this.props.location.pathname &&
      this.props.location.pathname.includes("/datoms-x")
    ) {
      if (index === 0) {
        that.setState(
          {
            selected_account_type: value,
            selected_territory_ids: undefined,
            selected_vendor_id: undefined,
            selected_client_id: undefined,
            selected_app_id: [],
            selected_type: [],
            loading: true,
          },
          () => {
            that.fetchDeviceList();
          },
        );
      } else if (index === 1) {
        that.setState(
          {
            selected_territory_ids: Array.isArray(value)
              ? value.map((item) => item.value)
              : undefined,
            selected_vendor_id: undefined,
            selected_client_id: undefined,
            selected_app_id: [],
            selected_type: [],
            loading: true,
          },
          () => {
            that.fetchDeviceList();
          },
        );
      } else if (index === 2) {
        that.setState(
          {
            selected_vendor_id: value,
            selected_client_id: undefined,
            selected_app_id: [],
            selected_type: [],
            loading: true,
          },
          () => {
            // if (value) {
            // 	that.getClientCustomerList();
            // } else {
            that.filterTableData();
            // }
          },
        );
      } else if (index === 3) {
        that.setState(
          {
            selected_client_id: value,
            selected_app_id: [],
            selected_type: [],
            loading: true,
          },
          () => {
            if (value) {
              that.getClientApplicationList();
            } else {
              that.filterTableData();
            }
          },
        );
      } else if (index === 4) {
        that.setState(
          {
            selected_app_id: value,
            selected_type: [],
            loading: true,
          },
          () => {
            that.filterTableData();
          },
        );
      } else if (index === 5) {
        that.setState(
          {
            selected_type: value,
            loading: true,
          },
          () => {
            that.filterTableData();
          },
        );
      } else if (index === 6) {
        that.setState(
          {
            selected_connectivity_status: value,
            loading: true,
          },
          () => {
            that.filterTableData();
          },
        );
      } else if (index === 7) {
        that.setState(
          {
            selected_device_status: value,
            loading: true,
          },
          () => {
            that.filterTableData();
          },
        );
      }
    } else if (
      this.props.location &&
      Object.keys(this.props.location).length &&
      this.props.location.pathname &&
      this.props.location.pathname.includes("/iot-platform")
    ) {
      if (index === 0) {
        let selectedTerritories = undefined;
        let selectedDealers = undefined;
        let updatedTreeData = this.state.territoryData;
        let nonDealers = false;
        if (Array.isArray(value)) {
          selectedTerritories = [];
          selectedDealers = [];
          value.forEach((item) => {
            selectedTerritories.push(item.value);
            if (`${item.value}`.includes("isDealer-")) {
              selectedDealers.push(parseInt(item.value.split("-")[1]));
            } else {
              nonDealers = true;
            }
          });

          updatedTreeData = disableTreeUpdate(
            updatedTreeData,
            selectedDealers?.length ? true : false,
            nonDealers ? true : false,
          );
        }
        that.setState(
          {
            selected_territory_ids: selectedTerritories,
            selectedDealers: selectedDealers,
            selected_vendor_id: undefined,
            selected_client_id: undefined,
            selected_app_id: [],
            selected_type: [],
            loading: true,
          },
          () => {
            if (selectedDealers?.length) {
              that.filterTableData();
            } else {
              that.fetchDeviceList();
            }
          },
        );
      } else if (index === 1) {
        that.setState(
          {
            selected_vendor_id: value,
            selected_client_id: undefined,
            selected_app_id: [],
            selected_type: [],
            loading: true,
          },
          () => {
            // if (value) {
            // 	that.getClientCustomerList();
            // } else {
            that.filterTableData();
            // }
          },
        );
      } else if (index === 2) {
        that.setState(
          {
            selected_client_id: value,
            selected_app_id: [],
            selected_type: [],
            loading: true,
          },
          () => {
            if (value) {
              that.getClientApplicationList();
            } else {
              that.filterTableData();
            }
          },
        );
      } else if (index === 3) {
        that.setState(
          {
            selected_app_id: value,
            selected_type: [],
            loading: true,
          },
          () => {
            that.filterTableData();
          },
        );
      } else if (index === 4) {
        that.setState(
          {
            selected_type: value,
            loading: true,
          },
          () => {
            that.filterTableData();
          },
        );
      } else if (index === 5) {
        that.setState(
          {
            selected_connectivity_status: value,
            loading: true,
          },
          () => {
            that.filterTableData();
          },
        );
      } /*else if (index === 4) {
				that.setState(
					{
						selected_device_status: value,
						loading: true,
					},
					() => {
						that.filterTableData();
					}
				);
			}*/
    } else {
      if (index === 0) {
        that.setState(
          {
            selected_type: value,
            loading: true,
          },
          () => {
            that.filterTableData();
          },
        );
      } else if (index === 1) {
        that.setState(
          {
            selected_connectivity_status: value,
            loading: true,
          },
          () => {
            that.filterTableData();
          },
        );
      } else if (index === 2) {
        that.setState(
          {
            selected_device_status: value,
            loading: true,
          },
          () => {
            that.filterTableData();
          },
        );
      }
    }
  }

  onUpdateStatus(checked, event, row_data) {
    let text = <span className="red">Inactive</span>,
      icon = InactiveIcon,
      key = "block";
    if (row_data.status_code == 7) {
      text = <span className="green">Active</span>;
      icon = ActiveIcon;
      key = "active";
    }
    AntConfirmModal({
      title: (
        <div>
          <img
            src={icon}
            width={25}
            height={row_data.status_code == 7 ? 30 : 25}
          />
          <span>{row_data.status_code == 7 ? "Active" : "Inactive"}</span>
        </div>
      ),
      content: (
        <div>
          <span>{"Are you sure you want to make " + row_data.qr + " "}</span>{" "}
          {text} <span>{" ?"}</span>
        </div>
      ),
      okText: row_data.status_code == 7 ? "Active" : "Inactive",
      cancelText: "Cancel",
      className: "device-status-update-modal",
      onOk: () => this.updateStatusOnOk(key, [row_data.id]),
    });
  }

  onShowChecked(checked, event) {
    this.setState({
      show_checked: checked,
    });
  }

  deselectAll() {
    this.setState({
      show_checked: false,
      selected_devices: [],
      selected_rows: [],
      selected_row_keys: [],
    });
  }

  async updateSimDetails() {
    let { selected_device_sim, sim_details, table_data } = this.state;
    let updated_table_data = table_data.map((item) => {
      if (item.id == selected_device_sim) {
        return { ...item, sim_details };
      }
      return item;
    });
    let response = await updateDeviceSimDetails(
      this.props.client_id,
      selected_device_sim,
      { sim_details },
    );
    if (response.status == "success") {
      this.setState({
        table_data: updated_table_data,
        sim_details_drawer: false,
      });
      this.openNotification("success", "Sim Details Updated Successfully");
    } else if (response.status == "failure" && response.message) {
      this.openNotification("error", response.message);
    }
  }

  onChangeSimDetails(id, name, value) {
    // console.log('onChangeSimDetails_ id', id);
    // console.log('onChangeSimDetails_ name', name);
    // console.log('onChangeSimDetails_ value', value);
    let { sim_details } = this.state;
    let sim_details_obj = {
      serial_no: "",
      operator: "",
      sim_slot: id,
    };

    if (!Array.isArray(sim_details)) {
      sim_details = [];
    }
    let find_sim_details_obj = sim_details.find((item) => item.sim_slot == id);
    if (!find_sim_details_obj) {
      if (id == 2) {
        sim_details = [
          {
            serial_no: "",
            operator: "",
            sim_slot: 1,
          },
          {
            ...sim_details_obj,
            [name]: value,
          },
        ];
      } else {
        sim_details = [
          ...sim_details,
          {
            ...sim_details_obj,
            [name]: value,
          },
        ];
      }
    } else {
      sim_details = sim_details.map((item) => {
        if (item.sim_slot == id) {
          return {
            ...find_sim_details_obj,
            [name]: value,
          };
        }
        return item;
      });
    }
    this.setState({ sim_details });
  }

  renderSimDetails(sim_details, row_value) {
    return (
      <div className="sim-icon-holder">
        {window.innerWidth < 576 ? (
          <div className="sim-container">
            <EditOutlined
              onClick={() => {
                this.setState({
                  selected_device_sim: row_value.id,
                  sim_details: sim_details,
                  sim_details_drawer: true,
                });
              }}
            />
            <div>
              {sim_details && sim_details[0] && sim_details[0].serial_no ? (
                <div className="sim-icon-tooltip-each-line">
                  SIM1 : {sim_details[0]?.serial_no} ({sim_details[0]?.operator}
                  )
                </div>
              ) : (
                "NA"
              )}
            </div>
            <div>
              {sim_details && sim_details[1] && sim_details[1].serial_no ? (
                <div className="sim-icon-tooltip-each-line">
                  ,SIM2 : {sim_details[1]?.serial_no} (
                  {sim_details[1]?.operator})
                </div>
              ) : (
                ""
              )}
            </div>
          </div>
        ) : (
          <AntTooltip
            title={
              <div className={"sim-icon-tooltip-container"}>
                {sim_details && sim_details[0] && sim_details[0].serial_no ? (
                  <div className="sim-icon-tooltip-each-line">
                    SIM1 : {sim_details[0]?.serial_no} (
                    {sim_details[0]?.operator})
                  </div>
                ) : (
                  "NA"
                )}
                {sim_details && sim_details[1] && sim_details[1].serial_no ? (
                  <div className="sim-icon-tooltip-each-line">
                    SIM2 : {sim_details[1]?.serial_no} (
                    {sim_details[1]?.operator})
                  </div>
                ) : (
                  ""
                )}
              </div>
            }
          >
            <div
              className="icon-holder"
              onClick={() => {
                this.setState({
                  selected_device_sim: row_value.id,
                  sim_details: sim_details,
                  sim_details_drawer: true,
                });
              }}
            >
              <img src={sim} alt="" />
            </div>
          </AntTooltip>
        )}
      </div>
    );
  }

  /**
   * Predefined function of ReactJS to render the component.
   * @return {Object}
   */
  mobileDrawerClicked(e) {
    this.setState({
      mobile_drawer: e,
    });
  }
  render() {
    const { startValue, endValue, endOpen, formLayout, third_party_row_value } =
      this.state;

    const tableRowSelectionOptions = {
      selectedRowKeys: this.state.selected_row_keys,
      onChange: (selectedRowKeys, selectedRows) => {
        console.log("selectedRows_", selectedRows);

        this.setState(
          {
            show_checked: selectedRows.length ? this.state.show_checked : false,
            selected_devices: selectedRowKeys,
            selected_rows: selectedRows,
            selected_row_keys: selectedRowKeys,
          },
          () => {
            if (
              this.state.device_lists &&
              this.state.selected_rows &&
              this.state.selected_rows.length
            ) {
              // let firmwares = this.state.device_lists.firmwares.reverse();
              let firmwares = _orderBy(
                this.state.device_lists.firmwares,
                ["id"],
                ["desc"],
              );
              this.firmwareSelectionOptions = firmwares
                .map((firmware) => {
                  if (
                    this.state.selected_rows[0].type_id ===
                    firmware.device_type_id
                  ) {
                    return (
                      <AntOption value={firmware.id}>
                        {firmware.version}
                      </AntOption>
                    );
                  }
                })
                .filter(Boolean);
            }
          },
        );
        // console.log('selectedRowKeys -> ', selectedRowKeys);
        // console.log('selectedRows -> ', selectedRows);
      },
    };

    let modalCustomBody = third_party_row_value
      ? this.getmodalCustomBody(third_party_row_value)
      : "";
    console.log(
      "getFirmwareBody1",
      this.state.selected_row,
      this.state.selected_rows,
    );
    let firmwareModalBody = "";
    //if(this.state.update_firmware) {
    firmwareModalBody = this.state.single_update
      ? this.state.selected_row && this.state.selected_row.length
        ? this.getfirmwareModalBody(this.state.selected_row[0])
        : ""
      : this.state.selected_rows && this.state.selected_rows.length
        ? this.getfirmwareModalBody(this.state.selected_rows[0])
        : "";
    //}

    let deviceListData = this.state.table_data;

    if (this.state.show_checked) {
      deviceListData = this.state.selected_rows;
    }

    let columns = [];
    // console.log('this.props.location', this.props.location);
    if (
      this.props.location &&
      Object.keys(this.props.location).length &&
      this.props.location.pathname &&
      this.props.location.pathname.includes("/datoms-x")
    ) {
      // console.log('this.props.location', 'entered_1');
      if (!this.props.is_application_filter) {
        if (this.props.client_id == 392) {
          columns = [
            {
              title: "Device QR",
              key: "qr",
              width: "25%",
              dataIndex: "qr",
              sorter: (a, b) => a.qr.localeCompare(b.qr),
              render: (qr, row_value) => (
                <div>
                  <div className="device-name">
                    <AntTooltip title={qr}>{qr}</AntTooltip>
                  </div>
                  <div>{row_value.type_name}</div>
                </div>
              ),
            },
            {
              title: this.props.t? this.props.t('customer_name'): "Customer Name",
              // title: "Customer Name",
              key: "customer",
              width: "25%",
              dataIndex: "customer",
              sorter: (a, b) => a.customer.localeCompare(b.customer),
              render: (customer) => (
                <div>
                  {(() => {
                    if (customer != "" && customer != null) {
                      return (
                        <span>
                          <span className="">{customer}</span>
                        </span>
                      );
                    } else {
                      return <span>-</span>;
                    }
                  })()}
                </div>
              ),
            },
            {
              title: "Status",
              dataIndex: "active",
              width: "10%",
              // align: 'center',
              key: "active",
              sorter: (a, b) => a.active - b.active,
              render: (timestamp) => (
                <AntTooltip title="Last data received">
                  {(() => {
                    let current_time = moment().unix(),
                      less_fifteen_online = current_time - timestamp,
                      fifteen_minute = 900,
                      online_icon_class = "",
                      status_text = "";
                    if (less_fifteen_online <= fifteen_minute) {
                      online_icon_class = " online";
                      status_text = "Online";
                    } else {
                      online_icon_class = " offline";
                      status_text = "Offline";
                    }
                    // return <span className={'dot' + online_icon_class}></span>;
                    return (
                      <span className={"table-status-text" + online_icon_class}>
                        {timestamp == "" ||
                        timestamp == null ||
                        timestamp == undefined ||
                        timestamp == 0
                          ? "Never"
                          : status_text}
                      </span>
                    );
                  })()}
                  <span className="date-time">
                    {timestamp == "" ||
                    timestamp == null ||
                    timestamp == undefined ||
                    timestamp == 0
                      ? "Never"
                      : moment
                          .unix(timestamp)
                          .tz("Asia/Kolkata")
                          .format("DD MMM YYYY, HH:mm")}
                  </span>
                </AntTooltip>
              ),
            },
            {
              title: "Device Status",
              width: "15%",
              align: "center",
              key: "device_status",
              render: (row_data) => (
                <div className="device-data-center">
                  {(() => {
                    if (row_data.status_code == 7) {
                      return <span>Blocked</span>;
                    } else {
                      return <span>Active</span>;
                    }
                  })()}
                </div>
              ),
            },
            {
              title: "Description",
              key: "description",
              width: "25%",
              dataIndex: "description",
            },
          ];
        } else {
          columns = [
            /*Actual Col: DatomsX*/
            {
              title: this.props.t? this.props.t('device_serial_id'): "Device Serial ID",
              width: "16%",
              key: "qr",
              dataIndex: "qr",
              sorter: (a, b) => a.qr.localeCompare(b.qr),
              render: (qr, row_value) => (
                <div>{this.getDeviceDetails(qr, row_value, this.props.t)}</div>
              ),
            },
            {
              title: this.props.t? this.props.t('created_date'): "Created Date",
              width: "9%",
              align: "center",
              key: "created_at",
              dataIndex: "created_at",
              sorter: (a, b) => a.created_at - b.created_at,
              render: (created_at, row_value) => (
                <div>
                  {!isNaN(created_at) && created_at > 0
                    ? moment
                        .unix(created_at)
                        .tz("Asia/Kolkata")
                        .format("DD MMM YYYY, HH:mm")
                    : "-"}
                </div>
              ),
            },
            {
              title: this.props.t? this.props.t('firmware'): "Firmware",
              width: "8%",
              align: "center",
              key: "version",
              render: (row_value) => (
                <div>
                  {(() => {
                    let firm_ver = _filter(this.state.device_lists.firmwares, {
                      device_type_id: row_value.type_id,
                    });

                    if (firm_ver.length && row_value.firmware_version != "") {
                      if (
                        row_value.firmware_version ===
                        firm_ver[firm_ver.length - 1].version
                      ) {
                        return (
                          <div className="display-flex aln-cntr just-cntr">
                            <span>{row_value.firmware_version + " "}</span>
                            <img
                              src={upto_date}
                              height="15"
                              width="17"
                              className="updated"
                            />
                          </div>
                        );
                      } else {
                        return (
                          <div className="display-flex aln-cntr just-cntr">
                            <span>{row_value.firmware_version + " "}</span>
                            {(() => {
                              if (
                                this.props.application_id &&
                                this.props.application_id !== 17
                              ) {
                                return (
                                  <img
                                    style={{
                                      cursor:
                                        row_value.application_id === 17
                                          ? "not-allowed"
                                          : "pointer",
                                    }}
                                    src={update}
                                    height="15"
                                    width="17"
                                    className="not-updated"
                                    onClick={() =>
                                      this.singleDeviceFirmwareUpdate([
                                        row_value,
                                      ])
                                    }
                                  />
                                );
                              } else {
                                return (
                                  <img
                                    src={update}
                                    height="15"
                                    width="17"
                                    className="mar-left-10"
                                  />
                                );
                              }
                            })()}
                          </div>
                        );
                      }
                    } else {
                      return (
                        <img
                          src={update}
                          style={{
                            cursor: "pointer",
                          }}
                          onClick={() => {
                            this.singleDeviceFirmwareUpdate([row_value]);
                          }}
                          height="15"
                          width="17"
                          className="mar-left-10"
                        />
                      );
                    }
                  })()}
                </div>
              ),
            },
            {
              title: this.props.t? this.props.t('assets'): "Assets",
              width: "9%",
              key: "station",
              dataIndex: "station",
              sorter: (a, b) => a.station_name.localeCompare(b.station_name),
              render: (name, row_value) => (
                <div className="break-word">
                  {(() => {
                    if (name && Array.isArray(name) && name.length) {
                      let more_text = "";
                      if (name.length > 1) {
                        let allNames = [name[1]];
                        name.map((thingName, index) => {
                          if (index > 1) {
                            allNames.push(", " + thingName);
                          }
                        });
                        more_text = ", +" + (name.length - 1) + " more";
                        return (
                          <span>
                            <AntTooltip title={allNames}>
                              <span className="">{name[0] + more_text}</span>
                            </AntTooltip>
                          </span>
                        );
                      } else {
                        return (
                          <span>
                            <span className="">{name[0]}</span>
                          </span>
                        );
                      }
                    } else if (name && _isString(name) && name !== "") {
                      return (
                        <span>
                          <span className="">{name}</span>
                        </span>
                      );
                    } else {
                      return <span>-</span>;
                    }
                  })()}
                </div>
              ),
            },
            /*{
							title: 'Application',
							width: '8%',
							key: 'application',
							dataIndex: 'application',
							sorter: (a, b) =>
								a.application.localeCompare(b.application),
							render: (application) => (
								<div>
									{(() => {
										if (
											application != '' &&
											application != null
										) {
											return (
												<span>
													<span className="">
														{application}
													</span>
												</span>
											);
										} else {
											return <span>-</span>;
										}
									})()}
								</div>
							),
						},*/
            {
              // title: "Customer Name",
              title: this.props.t? this.props.t('customer_name'): "Customer Name",
              width: "10%",
              key: "customer",
              dataIndex: "customer",
              sorter: (a, b) => a.customer.localeCompare(b.customer),
              render: (customer, row_value) => (
                <div className="break-word">
                  {(() => {
                    if (customer != "" && customer != null) {
                      return (
                        <span>
                          <span className="">{customer}</span>
                        </span>
                      );
                    } else {
                      return <span>-</span>;
                    }
                  })()}
                  {(() => {
                    if (
                      row_value.vendor_id &&
                      row_value.vendor_id !== 1
                      //  &&
                      // row_value.customer_id !== row_value.vendor_id
                    ) {
                      return (
                        <div style={{ display: "flex", alignItems: "center" }}>
                          <div
                            className="vendor-tag"
                            style={{ marginRight: 8 }}
                          >
                            {row_value.parent_vendor_id &&
                            this.state.customerNameMap?.[
                              row_value.parent_vendor_id
                            ]
                              ? this.state.customerNameMap[
                                  row_value.parent_vendor_id
                                ]
                              : row_value.vendor_name}
                          </div>
                          {row_value.parent_vendor_id ? (
                            <HeirarchyPopover
                              items={[
                                row_value.customer_id !== row_value.vendor_id ? customer : '-',
                                row_value.vendor_name,
                                this.state.customerNameMap?.[
                                  row_value.goem_id
                                ],
                                this.state.customerNameMap?.[
                                  row_value.parent_vendor_id
                                ],
                              ]}
                            />
                          ) : (
                            ""
                          )}
                        </div>
                      );
                    }
                  })()}
                  <div className="font-12 col-808080">
                    {(() => {
                      if (
                        row_value.application != "" &&
                        row_value.application != null
                      ) {
                        return (
                          <span>
                            <span className="">{row_value.application}</span>
                          </span>
                        );
                      } else {
                        return <span>-</span>;
                      }
                    })()}
                  </div>
                </div>
              ),
            },
            /*{
							title: 'Connectivity Status',
							width: '10%',
							dataIndex: 'active',
							key: 'active',
							sorter: (a, b) => a.active - b.active,
							render: (timestamp, row_value) => (
								<AntTooltip title="Last data received">
									{(() => {
										let status_text = 'Offline',
											online_icon_class = ' offline';
										if (
											row_value.connectivity_status ===
											'online'
										) {
											online_icon_class = ' online';
											status_text = 'Online';
										}
										// return <span className={'dot' + online_icon_class}></span>;
										return (
											<span
												className={
													'table-status-text' +
													online_icon_class
												}
											>
												{timestamp == '' ||
												timestamp == null ||
												timestamp == undefined ||
												timestamp == 0
													? 'Never'
													: status_text}
											</span>
										);
									})()}
									<span className="date-time">
										{timestamp == '' ||
										timestamp == null ||
										timestamp == undefined ||
										timestamp == 0
											? 'Never'
											: moment
													.unix(timestamp)
													.tz('Asia/Kolkata')
													.format('DD MMM YYYY, HH:mm')}
									</span>
								</AntTooltip>
							),
						},*/
            {
              title: this.props.t? this.props.t('network_status'): "Network Status",
              width: "9%",
              key: "network_percent",
              align: "center",
              dataIndex: "network_percent",
              render: (percent, row_value) => (
                <div className="percentage-holder">
                  {/*<AntTooltip title="Data Availability">
										<AntProgress className="percent-icon" type="circle" percent={row_value.availability && row_value.availability != null ? row_value.availability : 0} width={35} strokeColor="#139547"/>
									</AntTooltip>*/}
                  <div className="percent-icon">
                    {(() => {
                      if (row_value.device_config) {
                        let simNo = "";
                        if (row_value.device_sim_slot) {
                          simNo = "Sim " + row_value.device_sim_slot;
                        }
                        if (
                          row_value.device_config.device_modem_type == "gprs"
                        ) {
                          let gprsIcon = GprsSignalRounded;

                          let networkStrengthText = "";
                          if (row_value.connectivity_status === "online") {
                            if (row_value.device_signal_strength > 20) {
                              networkStrengthText =
                                "Strength: " +
                                row_value.device_signal_strength +
                                " (Excellent)";
                              gprsIcon = GprsSignalRounded5;
                            } else if (
                              row_value.device_signal_strength >= 13 &&
                              row_value.device_signal_strength <= 20
                            ) {
                              networkStrengthText =
                                "Strength: " +
                                row_value.device_signal_strength +
                                " (Good)";
                              gprsIcon = GprsSignalRounded4;
                            } else if (
                              row_value.device_signal_strength >= 6 &&
                              row_value.device_signal_strength <= 12
                            ) {
                              networkStrengthText =
                                "Strength: " +
                                row_value.device_signal_strength +
                                " (Poor)";
                              gprsIcon = GprsSignalRounded2;
                            } else if (row_value.device_signal_strength < 6) {
                              networkStrengthText = "No Signal";
                            }
                          } else {
                            if (row_value.device_signal_strength > 20) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
                            } else if (
                              row_value.device_signal_strength >= 13 &&
                              row_value.device_signal_strength <= 20
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('good') : "Good"})`;
                            } else if (
                              row_value.device_signal_strength >= 6 &&
                              row_value.device_signal_strength <= 12
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('poor') : "Poor"})`;
                            } else if (row_value.device_signal_strength < 6) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;
                            }
                          }

                          return (
                            <div className="display-flex">
                              <AntTooltip title={networkStrengthText}>
                                <img
                                  className="mar-right-10"
                                  src={gprsIcon}
                                  height={20}
                                  width={20}
                                />
                              </AntTooltip>
                              <div>
                                <div>GPRS</div>
                                <div>{simNo}</div>
                              </div>
                            </div>
                          );
                        } else if (
                          row_value.device_config.device_modem_type == "wifi"
                        ) {
                          let wifiIcon = WifiSignalRounded;

                          let networkStrengthText = "";
                          if (row_value.connectivity_status === "online") {
                            if (row_value.device_signal_strength > 20) {
                              networkStrengthText =
                                "Strength: " +
                                row_value.device_signal_strength +
                                " (Excellent)";
                              wifiIcon = WifiSignalRounded3;
                            } else if (
                              row_value.device_signal_strength >= 13 &&
                              row_value.device_signal_strength <= 20
                            ) {
                              networkStrengthText =
                                "Strength: " +
                                row_value.device_signal_strength +
                                " (Good)";
                              wifiIcon = WifiSignalRounded2;
                            } else if (
                              row_value.device_signal_strength >= 6 &&
                              row_value.device_signal_strength <= 12
                            ) {
                              networkStrengthText =
                                "Strength: " +
                                row_value.device_signal_strength +
                                " (Poor)";
                              wifiIcon = WifiSignalRounded1;
                            } else if (row_value.device_signal_strength < 6) {
                              networkStrengthText = "No Signal";
                            }
                          } else {
                            if (row_value.device_signal_strength > 20) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
                            } else if (
                              row_value.device_signal_strength >= 13 &&
                              row_value.device_signal_strength <= 20
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('good') : "Good"})`;
                            } else if (
                              row_value.device_signal_strength >= 6 &&
                              row_value.device_signal_strength <= 12
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('poor') : "Poor"})`;
                            } else if (row_value.device_signal_strength < 6) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;
                            }
                          }
                          return (
                            <div>
                              <AntTooltip title={networkStrengthText}>
                                <img
                                  className="mar-right-10"
                                  src={wifiIcon}
                                  height={20}
                                  width={20}
                                />
                              </AntTooltip>
                              <span>Wi-Fi</span>
                            </div>
                          );
                        } else if (
                          row_value.device_config.device_modem_type ==
                          "ethernet"
                        ) {
                          if (row_value.connectivity_status === "online") {
                            return (
                              <div>
                                <img
                                  className="mar-right-10"
                                  src={ethernet}
                                  height={20}
                                  width={20}
                                />
                                <span>Ethernet</span>
                              </div>
                            );
                          } else {
                            return (
                              <div>
                                <img
                                  className="mar-right-10"
                                  src={ethernet_inactive}
                                  height={20}
                                  width={20}
                                />
                                <span>Ethernet</span>
                              </div>
                            );
                          }
                        }
                      }
                    })()}
                  </div>
                </div>
              ),
            },
            {
              title: this.props.t? this.props.t('power_status'): "Power Status",
              width: "8%",
              key: "power_status",
              align: "center",
              dataIndex: "power_status",
              render: (power_status, row_value) => (
                <div className="percentage-holder power-container">
                  <div className="icon-holder">
                    {(() => {
                      if (
                        row_value.device_power_status !== null &&
                        row_value.device_power_status !== false
                      ) {
                        let icon = power_inactive,
                          text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('On'): "Last Reported Status: On"}`;
                        if (row_value.connectivity_status === "online") {
                          icon = power_on;
                          text = `${this.props.t? this.props.t('power_status') +" "+ this.props.t('on'): "Power Status: On"}`;
                          // text = "Power Status: On";
                        }
                        return (
                          <AntTooltip title={text}>
                            <img src={icon} height={22} width={22} />
                          </AntTooltip>
                        );
                      } else if (row_value.device_power_status === false) {
                        let icon = power_inactive,
                          text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('Off'): "Last Reported Status: Off"}`;
                        if (row_value.connectivity_status === "online") {
                          icon = power_off;
                          text = "Power Status: Off";
                        }
                        return (
                          <AntTooltip title={text}>
                            <img src={icon} height={22} width={22} />
                          </AntTooltip>
                        );
                      } else {
                        let text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('unknown'): "Last Reported Status: Unknown"}`;
                        if (row_value.connectivity_status === "online") {
                          text = `${this.props.t? this.props.t('power_status') +": "+ this.props.t('unknown'): "Power Status: Unknown"}`;
                        }
                        return (
                          <AntTooltip title={text}>
                            <img src={power_inactive} height={22} width={22} />
                          </AntTooltip>
                        );
                      }
                    })()}
                  </div>
                  <div className="battery-container">
                    {(() => {
                      if (row_value.device_battery_percent) {
                        return (
                          <div className="battery mar-left-5">
                            <div className="minus-icon">-</div>
                            <div className="bat-body">
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent > 0 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 20 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 30 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 40 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 50 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 60 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 70 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 80 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 90 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 100 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                            </div>
                            <div className="bat-top"></div>
                            <div className="plus-icon">+</div>
                          </div>
                        );
                      }
                    })()}
                    <div className="bat-charge-details">
                      <div className="icon-holder">
                        {(() => {
                          if (
                            row_value.device_battery_percent !== null &&
                            row_value.device_battery_percent !== false &&
                            row_value.connectivity_status === "online"
                          ) {
                            return (
                              <span>
                                {"(" +
                                  (row_value.device_battery_percent !== 0
                                    ? row_value.device_battery_percent.toFixed(
                                        2,
                                      )
                                    : 0) +
                                  "%)"}
                              </span>
                            );
                          }
                        })()}
                      </div>
                      <div className="icon-holder mar-left-5">
                        {(() => {
                          if (
                            row_value.device_charging_status !== null &&
                            row_value.device_battery_percent !== null
                          ) {
                            if (
                              row_value.device_charging_status &&
                              row_value.connectivity_status === "online"
                            ) {
                              return (
                                <img
                                  src={lighting_active}
                                  height={15}
                                  width={15}
                                />
                              );
                            } else {
                              return (
                                <img
                                  src={lighting_inactive}
                                  height={15}
                                  width={15}
                                />
                              );
                            }
                          }
                        })()}
                      </div>
                    </div>
                  </div>
                </div>
              ),
            },
            {
              title: "SIM",
              width: "5%",
              key: "sim_details",
              align: "center",
              dataIndex: "sim_details",
              render: (sim_details, row_value) =>
                this.renderSimDetails(sim_details, row_value),
            },
            {
              title: "Online Percentage",
              width: "9%",
              key: "percent",
              align: "center",
              // sorter: (a, b) => a.percent - b.percent,
              dataIndex: "percent",
              render: (percent, row_value) => (
                <div className="percentage-holder">
                  <AntTooltip title="This month online %">
                    <AntProgress
                      className="percent-icon"
                      type="circle"
                      percent={
                        row_value.percent && row_value.percent != null
                          ? row_value.percent
                          : 0
                      }
                      width={35}
                      strokeColor="#21A1DB"
                    />
                  </AntTooltip>
                </div>
              ),
            },
            /*{
							title: 'Debug',
							width: '8%',
							align: 'center',
							key: 'health',
							render: (config, row_data) => (
								<div>
									<div className="icon-holder">
										<AntTooltip title="Open Debug">
											<img
												src={debug}
												className="cursor-pointer"
												height={28}
												width={32}
												onClick={() =>
													this.openDeviceDebug(row_data)
												}
											/>
										</AntTooltip>
									</div>
									<div className="device-errors-tags">
										{(() => {
											let errors = [],
												extraTypes = '';
											if (
												row_data.device_error_list &&
												row_data.device_error_list.length
											) {
												row_data.device_error_list.map(
													(errorType, index) => {
														if (index === 0) {
															errors.push(
																<AntTooltip
																	title={moment
																		.unix(
																			row_data.timestamp
																		)
																		.tz(
																			'Asia/Kolkata'
																		)
																		.format(
																			'DD MMM YYYY HH:mm'
																		)}
																>
																	<span className="mar-lt-5">
																		{errorType +
																			', '}
																	</span>
																</AntTooltip>
															);
														} else if (index === 1) {
															errors.push(
																<AntTooltip
																	title={moment
																		.unix(
																			row_data.timestamp
																		)
																		.tz(
																			'Asia/Kolkata'
																		)
																		.format(
																			'DD MMM YYYY HH:mm'
																		)}
																>
																	<span className="mar-lt-5">
																		{errorType}
																	</span>
																</AntTooltip>
															);
														} else {
															if (
																index ===
																row_data
																	.device_error_list
																	.length -
																	1
															) {
																extraTypes = extraTypes.concat(
																	errorType +
																		' - ' +
																		moment
																			.unix(
																				row_data.timestamp
																			)
																			.tz(
																				'Asia/Kolkata'
																			)
																			.format(
																				'DD MMM YYYY HH:mm'
																			)
																);
															} else {
																extraTypes = extraTypes.concat(
																	errorType +
																		' - ' +
																		moment
																			.unix(
																				row_data.timestamp
																			)
																			.tz(
																				'Asia/Kolkata'
																			)
																			.format(
																				'DD MMM YYYY HH:mm'
																			) +
																		', '
																);
															}
														}
													}
												);

												if (
													row_data.device_error_list
														.length > 2
												) {
													errors.push(
														<AntTooltip
															title={extraTypes}
														>
															<AntTag
																className="mar-lt-5"
																color="red"
															>
																{'+ ' +
																	(row_data
																		.device_error_list
																		.length -
																		2) +
																	' more'}
															</AntTag>
														</AntTooltip>
													);
												}
											}
											return errors;
										})()}
									</div>
								</div>
							),
						},*/
            {
              title: "Debug & Configure",
              width: "9%",
              align: "center",
              key: "configure",
              render: (config, row_data) => (
                <div className="display-flex aln-cntr just-cntr">
                  <div className="icon-holder mar-right-10">
                    <AntTooltip title={this.props.t? this.props.t('debug'): "Debug"}>
                      <img
                        src={debug}
                        className="cursor-pointer"
                        height={28}
                        width={32}
                        onClick={() => this.openDeviceDebug(row_data)}
                      />
                    </AntTooltip>
                  </div>
                  <div className="icon-holder mar-right-10">
                    <AntTooltip title="Raw log">
                      <img
                        src={raw_log_icon}
                        className="cursor-pointer"
                        height={28}
                        width={32}
                        onClick={() => this.openDeviceDebug(row_data, true)}
                      />
                    </AntTooltip>
                  </div>
                  {(() => {
                    if (/*this.props.application_id !== 17*/ true) {
                      if (true) {
                        /*let routeLink =
													this.iotBasePath +
													'/enterprise/' +
													this.props.client_id +
													this.platform_slug +
													'/devices/assigned/' +
													(config.device_config &&
													config.device_config
														.station_id &&
													config.device_config.station_id
														.length
														? config.device_config
																.station_id[0]
														: row_data.vendor_id
														? row_data.vendor_id
														: 0) +
													'/communication?app_id=' +
													row_data.application_id;*/
                        let routeLink =
                          this.iotBasePath +
                          "/enterprise/" +
                          this.props.client_id +
                          this.platform_slug +
                          "/devices/assigned/" +
                          row_data.id +
                          "/communication?type_id=" +
                          row_data.type_id;
                        if (this.props.is_application_filter) {
                          /*routeLink =
														this.platform_slug +
														'/customer-management/' +
														this.props.customer_id +
														'/applications/' +
														this.state.selected_app +
														'/devices/' +
														(config.device_config &&
														config.device_config
															.station_id &&
														config.device_config
															.station_id.length
															? config.device_config
																	.station_id[0]
															: row_data.vendor_id
															? row_data.vendor_id
															: 0) +
														'/communication?app_id=' +
														row_data.application_id;*/

                          routeLink =
                            this.iotBasePath +
                            "/enterprise/" +
                            this.props.client_id +
                            this.platform_slug +
                            "/customer-management/" +
                            this.props.customer_id +
                            "/applications/" +
                            this.state.selected_app +
                            "/devices/" +
                            row_data.id +
                            "/communication?type_id=" +
                            row_data.type_id;
                        }
                        if (
                          row_data.type_id == 11 ||
                          row_data.type_id == 12 ||
                          row_data.type_id == 51
                        ) {
                          return (
                            <AntTooltip title={this.props.t? this.props.t('configure_device'): "Configure Device"}>
                              <img
                                className="mar-right-10 cursor-pointer"
                                src={configuration}
                                height={28}
                                width={32}
                                onClick={() =>
                                  this.openConfiguration(routeLink)
                                }
                              />
                            </AntTooltip>
                          );
                        }
                      } else {
                        if (
                          row_data.type_id == 11 ||
                          row_data.type_id == 12 ||
                          row_data.type_id == 51
                        ) {
                          return (
                            <AntTooltip title="Configure Device">
                              <img
                                className="mar-right-10 cursor-block"
                                src={configuration}
                                height={28}
                                width={32}
                              />
                            </AntTooltip>
                          );
                        }
                      }
                    }
                  })()}
                  {(() => {
                    if (
                      this.props.application_id == 12 ||
                      (this.props.enabled_features &&
                        this.props.enabled_features.includes(
                          "DeviceManagement:CustomCommand",
                        ))
                    ) {
                      return (
                        <AntTooltip title="Custom Command">
                          <img
                            src={custom_command}
                            className="cursor-pointer"
                            height={28}
                            width={32}
                            onClick={() =>
                              this.openCustomCommand(row_data.id, row_data)
                            }
                          />
                        </AntTooltip>
                      );
                    }
                  })()}
                </div>
              ),
            },
            {
              title: "Device Status",
              width: "8%",
              align: "center",
              key: "device_status",
              render: (row_data) => (
                <div>
                  <AntTooltip
                    title={row_data.status_code == 7 ? "Inactive" : "Active"}
                  >
                    <AntSwitch
                      size="medium"
                      checkedChildren="Active"
                      unCheckedChildren="Inactive"
                      checked={row_data.status_code == 7 ? false : true}
                      onChange={(checked, event) =>
                        this.onUpdateStatus(checked, event, row_data)
                      }
                    ></AntSwitch>
                  </AntTooltip>
                </div>
              ),
            },
          ];
        }
      } else {
        if (this.props.customer_id == 392 || this.props.client_id == 392) {
          columns = [
            {
              title: "Device QR",
              key: "qr",
              width: "25%",
              dataIndex: "qr",
              sorter: (a, b) => a.qr.localeCompare(b.qr),
              render: (qr, row_value) => (
                <div>
                  <div className="device-name">
                    <AntTooltip title={qr}>{qr}</AntTooltip>
                  </div>
                  <div>{row_value.type_name}</div>
                </div>
              ),
            },
            {
              // title: "Customer Name",
              title: this.props.t? this.props.t('customer_name'): "Customer Name",
              key: "customer",
              width: "25%",
              dataIndex: "customer",
              sorter: (a, b) => a.customer.localeCompare(b.customer),
              render: (customer) => (
                <div>
                  {(() => {
                    if (customer != "" && customer != null) {
                      return (
                        <span>
                          <span className="">{customer}</span>
                        </span>
                      );
                    } else {
                      return <span>-</span>;
                    }
                  })()}
                </div>
              ),
            },
            {
              title: "Status",
              dataIndex: "active",
              width: "10%",
              // align: 'center',
              key: "active",
              sorter: (a, b) => a.active - b.active,
              render: (timestamp) => (
                <AntTooltip title="Last data received">
                  {(() => {
                    let current_time = moment().unix(),
                      less_fifteen_online = current_time - timestamp,
                      fifteen_minute = 900,
                      online_icon_class = "",
                      status_text = "";
                    if (less_fifteen_online <= fifteen_minute) {
                      online_icon_class = " online";
                      status_text = "Online";
                    } else {
                      online_icon_class = " offline";
                      status_text = "Offline";
                    }
                    // return <span className={'dot' + online_icon_class}></span>;
                    return (
                      <span className={"table-status-text" + online_icon_class}>
                        {timestamp == "" ||
                        timestamp == null ||
                        timestamp == undefined ||
                        timestamp == 0
                          ? "Never"
                          : status_text}
                      </span>
                    );
                  })()}
                  <span className="date-time">
                    {timestamp == "" ||
                    timestamp == null ||
                    timestamp == undefined ||
                    timestamp == 0
                      ? "Never"
                      : moment
                          .unix(timestamp)
                          .tz("Asia/Kolkata")
                          .format("DD MMM YYYY, HH:mm")}
                  </span>
                </AntTooltip>
              ),
            },
            {
              title: "Device Status",
              width: "15%",
              align: "center",
              key: "device_status",
              render: (row_data) => (
                <div className="device-data-center">
                  {(() => {
                    if (row_data.status_code == 7) {
                      return <span>Blocked</span>;
                    } else {
                      return <span>Active</span>;
                    }
                  })()}
                </div>
              ),
            },
            {
              title: "Description",
              key: "description",
              width: "25%",
              dataIndex: "description",
            },
          ];
        } else {
          columns = [
            {
              title: this.props.t? this.props.t('device_serial_id'): "Device Serial ID",
              width: "16%",
              key: "qr",
              dataIndex: "qr",
              sorter: (a, b) => a.qr.localeCompare(b.qr),
              render: (qr, row_value) => (
                <div>{this.getDeviceDetails(qr, row_value, this.props.t)}</div>
              ),
            },
            {
              title: this.props.t? this.props.t('created_date'): "Created Date",
              width: "9%",
              align: "center",
              key: "created_at",
              dataIndex: "created_at",
              sorter: (a, b) => a.created_at - b.created_at,
              render: (created_at, row_value) => (
                <div>
                  {!isNaN(created_at) && created_at > 0
                    ? moment
                        .unix(created_at)
                        .tz("Asia/Kolkata")
                        .format("DD MMM YYYY, HH:mm")
                    : "-"}
                </div>
              ),
            },
            {
              title: this.props.t? this.props.t('firmware'): "Firmware",
              width: "10%",
              align: "center",
              key: "version",
              render: (row_value) => (
                <div>
                  {(() => {
                    let firm_ver = _filter(this.state.device_lists.firmwares, {
                      device_type_id: row_value.type_id,
                    });

                    if (firm_ver.length && row_value.firmware_version != "") {
                      if (
                        row_value.firmware_version ===
                        firm_ver[firm_ver.length - 1].version
                      ) {
                        return (
                          <div className="display-flex aln-cntr just-cntr">
                            <span>{row_value.firmware_version + " "}</span>
                            <img
                              src={upto_date}
                              height="15"
                              width="17"
                              className="updated"
                            />
                          </div>
                        );
                      } else {
                        return (
                          <div className="display-flex aln-cntr just-cntr">
                            <span>{row_value.firmware_version + " "}</span>
                            {(() => {
                              if (
                                this.props.application_id &&
                                this.props.application_id !== 17
                              ) {
                                return (
                                  <img
                                    style={{
                                      cursor:
                                        row_value.application_id === 17
                                          ? "not-allowed"
                                          : "pointer",
                                    }}
                                    src={update}
                                    height="15"
                                    width="17"
                                    className="not-updated"
                                    onClick={() =>
                                      this.singleDeviceFirmwareUpdate([
                                        row_value,
                                      ])
                                    }
                                  />
                                );
                              } else {
                                return (
                                  <img
                                    src={update}
                                    height="15"
                                    width="17"
                                    className="mar-left-10"
                                  />
                                );
                              }
                            })()}
                          </div>
                        );
                      }
                    } else {
                      return "-";
                    }
                  })()}
                </div>
              ),
            },
            {
              title: this.props.t? this.props.t('assets'): "Assets",
              width: "10%",
              key: "station",
              dataIndex: "station",
              sorter: (a, b) => a.station_name.localeCompare(b.station_name),
              render: (name, row_value) => (
                <div className="break-word">
                  {(() => {
                    if (name && Array.isArray(name) && name.length) {
                      let more_text = "";
                      if (name.length > 1) {
                        let allNames = [name[1]];
                        name.map((thingName, index) => {
                          if (index > 1) {
                            allNames.push(", " + thingName);
                          }
                        });
                        more_text = ", +" + (name.length - 1) + " more";
                        return (
                          <span>
                            <AntTooltip title={allNames}>
                              <span className="">{name[0] + more_text}</span>
                            </AntTooltip>
                          </span>
                        );
                      } else {
                        return (
                          <span>
                            <span className="">{name[0]}</span>
                          </span>
                        );
                      }
                    } else if (name && _isString(name) && name !== "") {
                      return (
                        <span>
                          <span className="">{name}</span>
                        </span>
                      );
                    } else {
                      return <span>-</span>;
                    }
                  })()}
                </div>
              ),
            },
            /*{
							title: 'Connectivity Status',
							width: '10%',
							dataIndex: 'active',
							key: 'active',
							sorter: (a, b) => a.active - b.active,
							render: (timestamp, row_value) => (
								<AntTooltip title="Last data received">
									{(() => {
										let status_text = 'Offline',
											online_icon_class = ' offline';
										if (
											row_value.connectivity_status ===
											'online'
										) {
											online_icon_class = ' online';
											status_text = 'Online';
										}
										// return <span className={'dot' + online_icon_class}></span>;
										return (
											<span
												className={
													'table-status-text' +
													online_icon_class
												}
											>
												{timestamp == '' ||
												timestamp == null ||
												timestamp == undefined ||
												timestamp == 0
													? 'Never'
													: status_text}
											</span>
										);
									})()}
									<span className="date-time">
										{timestamp == '' ||
										timestamp == null ||
										timestamp == undefined ||
										timestamp == 0
											? 'Never'
											: moment
													.unix(timestamp)
													.tz('Asia/Kolkata')
													.format(
														'DD MMM YYYY, HH:mm'
													)}
									</span>
								</AntTooltip>
							),
						},*/
            {
              title: "Online Percentage",
              width: "10%",
              key: "percent",
              align: "center",
              // sorter: (a, b) => a.percent - b.percent,
              dataIndex: "percent",
              render: (percent, row_value) => (
                <div className="percentage-holder">
                  <AntTooltip title="This month online %">
                    <AntProgress
                      className="percent-icon"
                      type="circle"
                      percent={
                        row_value.percent && row_value.percent != null
                          ? row_value.percent
                          : 0
                      }
                      width={35}
                      strokeColor="#21A1DB"
                    />
                  </AntTooltip>
                </div>
              ),
            },
            {
              title: this.props.t? this.props.t('network_status'): "Network Status",
              width: "9%",
              key: "network_percent",
              align: "center",
              dataIndex: "network_percent",
              render: (percent, row_value) => (
                <div className="percentage-holder">
                  {/*<AntTooltip title="Data Availability">
										<AntProgress className="percent-icon" type="circle" percent={row_value.availability && row_value.availability != null ? row_value.availability : 0} width={35} strokeColor="#139547"/>
									</AntTooltip>*/}
                  <div className="percent-icon">
                    {(() => {
                      if (row_value.device_config) {
                        let simNo = "";
                        if (row_value.device_sim_slot) {
                          simNo = "Sim " + row_value.device_sim_slot;
                        }
                        if (
                          row_value.device_config.device_modem_type == "gprs"
                        ) {
                          let gprsIcon = GprsSignalRounded;

                          let networkStrengthText = "";
                          if (row_value.connectivity_status === "online") {
                            if (row_value.device_signal_strength > 20) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t? this.props.t('Excellent'): "Excellent"})`;
                              gprsIcon = GprsSignalRounded5;
                            } else if (
                              row_value.device_signal_strength >= 13 &&
                              row_value.device_signal_strength <= 20
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t? this.props.t('Good'): "Good"})`;
                                // " (Good)";
                              gprsIcon = GprsSignalRounded4;
                            } else if (
                              row_value.device_signal_strength >= 6 &&
                              row_value.device_signal_strength <= 12
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t? this.props.t('Poor'): "Poor"})`;
                                // " (Poor)";
                              gprsIcon = GprsSignalRounded2;
                            } else if (row_value.device_signal_strength < 6) {
                              networkStrengthText = "No Signal";
                            }
                          } else {
                            if (row_value.device_signal_strength > 20) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
                            } else if (
                              row_value.device_signal_strength >= 13 &&
                              row_value.device_signal_strength <= 20
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('good') : "Good"})`;
                            } else if (
                              row_value.device_signal_strength >= 6 &&
                              row_value.device_signal_strength <= 12
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t? this.props.t('poor'): "Poor"})`;
                            } else if (row_value.device_signal_strength < 6) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;
                            }
                          }

                          return (
                            <div className="display-flex">
                              <AntTooltip title={networkStrengthText}>
                                <img
                                  className="mar-right-10"
                                  src={gprsIcon}
                                  height={20}
                                  width={20}
                                />
                              </AntTooltip>
                              <div>
                                <div>GPRS</div>
                                <div>{simNo}</div>
                              </div>
                            </div>
                          );
                        } else if (
                          row_value.device_config.device_modem_type == "wifi"
                        ) {
                          let wifiIcon = WifiSignalRounded;

                          let networkStrengthText = "";
                          if (row_value.connectivity_status === "online") {
                            if (row_value.device_signal_strength > 20) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t? this.props.t('Excellent'): "Excellent"})`;
                              wifiIcon = WifiSignalRounded3;
                            } else if (
                              row_value.device_signal_strength >= 13 &&
                              row_value.device_signal_strength <= 20
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('good') : "Good"})`;
                              wifiIcon = WifiSignalRounded2;
                            } else if (
                              row_value.device_signal_strength >= 6 &&
                              row_value.device_signal_strength <= 12
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t? this.props.t('poor'): "Poor"})`;
                              wifiIcon = WifiSignalRounded1;
                            } else if (row_value.device_signal_strength < 6) {
                              networkStrengthText = "No Signal";
                            }
                          } else {
                            if (row_value.device_signal_strength > 20) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
                            } else if (
                              row_value.device_signal_strength >= 13 &&
                              row_value.device_signal_strength <= 20
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('good') : "Good"})`;
                            } else if (
                              row_value.device_signal_strength >= 6 &&
                              row_value.device_signal_strength <= 12
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('poor') : "Poor"})`;
                            } else if (row_value.device_signal_strength < 6) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;
                            }
                          }
                          return (
                            <div>
                              <AntTooltip title={networkStrengthText}>
                                <img
                                  className="mar-right-10"
                                  src={wifiIcon}
                                  height={20}
                                  width={20}
                                />
                              </AntTooltip>
                              <span>Wi-Fi</span>
                            </div>
                          );
                        } else if (
                          row_value.device_config.device_modem_type ==
                          "ethernet"
                        ) {
                          if (row_value.connectivity_status === "online") {
                            return (
                              <div>
                                <img
                                  className="mar-right-10"
                                  src={ethernet}
                                  height={20}
                                  width={20}
                                />
                                <span>Ethernet</span>
                              </div>
                            );
                          } else {
                            return (
                              <div>
                                <img
                                  className="mar-right-10"
                                  src={ethernet_inactive}
                                  height={20}
                                  width={20}
                                />
                                <span>Ethernet</span>
                              </div>
                            );
                          }
                        }
                      }
                    })()}
                  </div>
                </div>
              ),
            },
            {
              title: this.props.t? this.props.t('power_status'): "Power Status",
              width: "9%",
              key: "power_status",
              align: "center",
              dataIndex: "power_status",
              render: (power_status, row_value) => (
                <div className="percentage-holder power-container">
                  <div className="icon-holder">
                    {(() => {
                      if (
                        row_value.device_power_status !== null &&
                        row_value.device_power_status !== false
                      ) {
                        let icon = power_inactive,
                          text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('On'): "Last Reported Status: On"}`;
                        if (row_value.connectivity_status === "online") {
                          icon = power_on;
                          text = "Power Status: On";
                        }
                        return (
                          <AntTooltip title={text}>
                            <img src={icon} height={22} width={22} />
                          </AntTooltip>
                        );
                      } else if (row_value.device_power_status === false) {
                        let icon = power_inactive,
                          text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('Off'): "Last Reported Status: Off"}`;
                        if (row_value.connectivity_status === "online") {
                          icon = power_off;
                          text = "Power Status: Off";
                        }
                        return (
                          <AntTooltip title={text}>
                            <img src={icon} height={22} width={22} />
                          </AntTooltip>
                        );
                      } else {
                        let text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('unknown'): "Last Reported Status: Unknown"}`;
                        if (row_value.connectivity_status === "online") {
                          text = `${this.props.t? this.props.t('power_status') +": "+ this.props.t('unknown'): "Power Status: Unknown"}`;
                        }
                        return (
                          <AntTooltip title={text}>
                            <img src={power_inactive} height={22} width={22} />
                          </AntTooltip>
                        );
                      }
                    })()}
                  </div>
                  <div className="battery-container">
                    {(() => {
                      if (row_value.device_battery_percent) {
                        return (
                          <div className="battery mar-left-5">
                            <div className="minus-icon">-</div>
                            <div className="bat-body">
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent > 0 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 20 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 30 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 40 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 50 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 60 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 70 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 80 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 90 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 100 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                            </div>
                            <div className="bat-top"></div>
                            <div className="plus-icon">+</div>
                          </div>
                        );
                      }
                    })()}
                    <div className="bat-charge-details">
                      <div className="icon-holder">
                        {(() => {
                          if (
                            row_value.device_battery_percent !== null &&
                            row_value.device_battery_percent !== false &&
                            row_value.connectivity_status === "online"
                          ) {
                            return (
                              <span>
                                {"(" +
                                  (row_value.device_battery_percent !== 0
                                    ? row_value.device_battery_percent.toFixed(
                                        2,
                                      )
                                    : 0) +
                                  "%)"}
                              </span>
                            );
                          }
                        })()}
                      </div>
                      <div className="icon-holder mar-left-5">
                        {(() => {
                          if (
                            row_value.device_charging_status !== null &&
                            row_value.device_battery_percent !== null
                          ) {
                            if (
                              row_value.device_charging_status &&
                              row_value.connectivity_status === "online"
                            ) {
                              return (
                                <img
                                  src={lighting_active}
                                  height={15}
                                  width={15}
                                />
                              );
                            } else {
                              return (
                                <img
                                  src={lighting_inactive}
                                  height={15}
                                  width={15}
                                />
                              );
                            }
                          }
                        })()}
                      </div>
                    </div>
                  </div>
                </div>
              ),
            },
            {
              title: "SIM",
              width: "8%",
              key: "sim_details",
              align: "center",
              dataIndex: "sim_details",
              render: (sim_details, row_value) =>
                this.renderSimDetails(sim_details, row_value),
            },
            /*{
							title: 'Debug',
							width: '10%',
							align: 'center',
							key: 'health',
							render: (config, row_data) => (
								<div>
									<div className="icon-holder">
										<AntTooltip title="Open Debug">
											<img
												src={debug}
												className="cursor-pointer"
												height={28}
												width={32}
												onClick={() =>
													this.openDeviceDebug(
														row_data
													)
												}
											/>
										</AntTooltip>
									</div>
									<div className="device-errors-tags">
										{(() => {
											let errors = [],
												extraTypes = '';
											if (
												row_data.device_error_list &&
												row_data.device_error_list
													.length
											) {
												row_data.device_error_list.map(
													(errorType, index) => {
														if (index === 0) {
															errors.push(
																<AntTooltip
																	title={moment
																		.unix(
																			row_data.timestamp
																		)
																		.tz(
																			'Asia/Kolkata'
																		)
																		.format(
																			'DD MMM YYYY HH:mm'
																		)}
																>
																	<span className="mar-lt-5">
																		{errorType +
																			', '}
																	</span>
																</AntTooltip>
															);
														} else if (
															index === 1
														) {
															errors.push(
																<AntTooltip
																	title={moment
																		.unix(
																			row_data.timestamp
																		)
																		.tz(
																			'Asia/Kolkata'
																		)
																		.format(
																			'DD MMM YYYY HH:mm'
																		)}
																>
																	<span className="mar-lt-5">
																		{
																			errorType
																		}
																	</span>
																</AntTooltip>
															);
														} else {
															if (
																index ===
																row_data
																	.device_error_list
																	.length -
																	1
															) {
																extraTypes = extraTypes.concat(
																	errorType +
																		' - ' +
																		moment
																			.unix(
																				row_data.timestamp
																			)
																			.tz(
																				'Asia/Kolkata'
																			)
																			.format(
																				'DD MMM YYYY HH:mm'
																			)
																);
															} else {
																extraTypes = extraTypes.concat(
																	errorType +
																		' - ' +
																		moment
																			.unix(
																				row_data.timestamp
																			)
																			.tz(
																				'Asia/Kolkata'
																			)
																			.format(
																				'DD MMM YYYY HH:mm'
																			) +
																		', '
																);
															}
														}
													}
												);

												if (
													row_data.device_error_list
														.length > 2
												) {
													errors.push(
														<AntTooltip
															title={extraTypes}
														>
															<AntTag
																className="mar-lt-5"
																color="red"
															>
																{'+ ' +
																	(row_data
																		.device_error_list
																		.length -
																		2) +
																	' more'}
															</AntTag>
														</AntTooltip>
													);
												}
											}
											return errors;
										})()}
									</div>
								</div>
							),
						},*/
            {
              title: "Debug & Configure",
              width: "10%",
              align: "center",
              key: "configure",
              render: (config, row_data) => (
                <div className="display-flex aln-cntr just-cntr">
                  <div className="icon-holder mar-right-10">
                    <AntTooltip title={this.props.t? this.props.t('debug'): "Debug"}>
                      <img
                        src={debug}
                        className="cursor-pointer"
                        height={28}
                        width={32}
                        onClick={() => this.openDeviceDebug(row_data)}
                      />
                    </AntTooltip>
                  </div>
                  <div className="icon-holder mar-right-10">
                    <AntTooltip title="Raw log">
                      <img
                        src={raw_log_icon}
                        className="cursor-pointer"
                        height={28}
                        width={32}
                        onClick={() => this.openDeviceDebug(row_data, true)}
                      />
                    </AntTooltip>
                  </div>
                  {(() => {
                    if (/*this.props.application_id !== 17*/ true) {
                      if (true) {
                        /*let routeLink =
													this.iotBasePath +
													'/enterprise/' +
													this.props.client_id +
													this.platform_slug +
													'/devices/assigned/' +
													(config.device_config &&
													config.device_config
														.station_id &&
													config.device_config.station_id
														.length
														? config.device_config
																.station_id[0]
														: row_data.vendor_id
														? row_data.vendor_id
														: 0) +
													'/communication?app_id=' +
													row_data.application_id;*/
                        let routeLink =
                          this.iotBasePath +
                          "/enterprise/" +
                          this.props.client_id +
                          this.platform_slug +
                          "/devices/assigned/" +
                          row_data.id +
                          "/communication?type_id=" +
                          row_data.type_id;
                        if (this.props.is_application_filter) {
                          /*routeLink =
														this.platform_slug +
														'/customer-management/' +
														this.props.customer_id +
														'/applications/' +
														this.state.selected_app +
														'/devices/' +
														(config.device_config &&
														config.device_config
															.station_id &&
														config.device_config
															.station_id.length
															? config.device_config
																	.station_id[0]
															: row_data.vendor_id
															? row_data.vendor_id
															: 0) +
														'/communication?app_id=' +
														row_data.application_id;*/

                          routeLink =
                            this.iotBasePath +
                            "/enterprise/" +
                            this.props.client_id +
                            this.platform_slug +
                            "/customer-management/" +
                            this.props.customer_id +
                            "/applications/" +
                            this.state.selected_app +
                            "/devices/" +
                            row_data.id +
                            "/communication?type_id=" +
                            row_data.type_id;
                        }
                        if (
                          row_data.type_id == 11 ||
                          row_data.type_id == 12 ||
                          row_data.type_id == 51
                        ) {
                          return (
                            <AntTooltip title={this.props.t? this.props.t('configure_device'): "Configure Device"}>
                              <img
                                className="mar-right-10 cursor-pointer"
                                src={configuration}
                                height={28}
                                width={32}
                                onClick={() =>
                                  this.openConfiguration(routeLink)
                                }
                              />
                            </AntTooltip>
                          );
                        }
                      } else {
                        if (
                          row_data.type_id == 11 ||
                          row_data.type_id == 12 ||
                          row_data.type_id == 51
                        ) {
                          return (
                            <AntTooltip title="Configure Device">
                              <img
                                className="mar-right-10 cursor-block"
                                src={configuration}
                                height={28}
                                width={32}
                              />
                            </AntTooltip>
                          );
                        }
                      }
                    }
                  })()}
                  {(() => {
                    if (
                      this.props.application_id == 12 ||
                      (this.props.enabled_features &&
                        this.props.enabled_features.includes(
                          "DeviceManagement:CustomCommand",
                        ))
                    ) {
                      return (
                        <AntTooltip title="Custom Command">
                          <img
                            src={custom_command}
                            className="cursor-pointer"
                            height={28}
                            width={32}
                            onClick={() =>
                              this.openCustomCommand(row_data.id, row_data)
                            }
                          />
                        </AntTooltip>
                      );
                    }
                  })()}
                </div>
              ),
            },
            {
              title: "Device Status",
              width: "9%",
              align: "center",
              key: "device_status",
              render: (row_data) => (
                <div>
                  <AntTooltip
                    title={row_data.status_code == 7 ? "Inactive" : "Active"}
                  >
                    <AntSwitch
                      size="medium"
                      checkedChildren="Active"
                      unCheckedChildren="Inactive"
                      checked={row_data.status_code == 7 ? false : true}
                      onChange={(checked, event) =>
                        this.onUpdateStatus(checked, event, row_data)
                      }
                    ></AntSwitch>
                  </AntTooltip>
                </div>
              ),
            },
          ];
        }
      }
    } else if (
      this.props.location &&
      Object.keys(this.props.location).length &&
      this.props.location.pathname &&
      this.props.location.pathname.includes("/iot-platform")
    ) {
      if (!this.props.is_application_filter) {
        if (this.props.client_id == 392) {
          columns = [
            {
              title: "Device QR",
              key: "qr",
              width: "25%",
              dataIndex: "qr",
              sorter: (a, b) => a.qr.localeCompare(b.qr),
              render: (qr, row_value) => (
                <div>
                  <div className="device-name">
                    <AntTooltip title={qr}>{qr}</AntTooltip>
                  </div>
                  <div>{row_value.type_name}</div>
                </div>
              ),
            },
            {
              title: this.props.t? this.props.t('customer_name'): "Customer Name",
              // title: "Customer Name",
              key: "customer",
              width: "25%",
              dataIndex: "customer",
              sorter: (a, b) => a.customer.localeCompare(b.customer),
              render: (customer) => (
                <div>
                  {(() => {
                    if (customer != "" && customer != null) {
                      return (
                        <span>
                          <span className="">{customer}</span>
                        </span>
                      );
                    } else {
                      return <span>-</span>;
                    }
                  })()}
                </div>
              ),
            },
            {
              title: "Status",
              dataIndex: "active",
              width: "10%",
              // align: 'center',
              key: "active",
              sorter: (a, b) => a.active - b.active,
              render: (timestamp) => (
                <AntTooltip title="Last data received">
                  {(() => {
                    let current_time = moment().unix(),
                      less_fifteen_online = current_time - timestamp,
                      fifteen_minute = 900,
                      online_icon_class = "",
                      status_text = "";
                    if (less_fifteen_online <= fifteen_minute) {
                      online_icon_class = " online";
                      status_text = "Online";
                    } else {
                      online_icon_class = " offline";
                      status_text = "Offline";
                    }
                    // return <span className={'dot' + online_icon_class}></span>;
                    return (
                      <span className={"table-status-text" + online_icon_class}>
                        {timestamp == "" ||
                        timestamp == null ||
                        timestamp == undefined ||
                        timestamp == 0
                          ? "Never"
                          : status_text}
                      </span>
                    );
                  })()}
                  <span className="date-time">
                    {timestamp == "" ||
                    timestamp == null ||
                    timestamp == undefined ||
                    timestamp == 0
                      ? "Never"
                      : moment
                          .unix(timestamp)
                          .tz("Asia/Kolkata")
                          .format("DD MMM YYYY, HH:mm")}
                  </span>
                </AntTooltip>
              ),
            },
            {
              title: "Device Status",
              width: "15%",
              align: "center",
              key: "device_status",
              render: (row_data) => (
                <div className="device-data-center">
                  {(() => {
                    if (row_data.status_code == 7) {
                      return <span>Blocked</span>;
                    } else {
                      return <span>Active</span>;
                    }
                  })()}
                </div>
              ),
            },
            {
              title: "Description",
              key: "description",
              width: "25%",
              dataIndex: "description",
            },
          ];
        } else {
          console.log("----gdai-class-----");
          columns = [
            /*Actual Col: Iot */
            {
              title: this.props.t? this.props.t('device_serial_id'): "Device Serial ID",
              width: "16%",
              key: "qr",
              dataIndex: "qr",
              sorter: (a, b) => a.qr.localeCompare(b.qr),
              render: (qr, row_value) => (
                <div>{this.getDeviceDetails(qr, row_value, this.props.t)}</div>
              ),
            },
            {
              title: this.props.t? this.props.t('created_date'): "Created Date",
              width: "9%",
              align: "center",
              key: "created_at",
              dataIndex: "created_at",
              sorter: (a, b) => a.created_at - b.created_at,
              render: (created_at, row_value) => (
                <div>
                  {!isNaN(created_at) && created_at > 0
                    ? moment
                        .unix(created_at)
                        .tz("Asia/Kolkata")
                        .format("DD MMM YYYY, HH:mm")
                    : "-"}
                </div>
              ),
            },
            {
              title: this.props.t? this.props.t('firmware'): "Firmware",
              width: "9%",
              align: "center",
              key: "version",
              render: (row_value) => (
                <div>
                  {(() => {
                    let firm_ver = _filter(this.state.device_lists.firmwares, {
                      device_type_id: row_value.type_id,
                    });
                    if (firm_ver.length && row_value.firmware_version != "") {
                      if (
                        row_value.firmware_version ===
                        firm_ver[firm_ver.length - 1].version
                      ) {
                        return (
                          <div className="display-flex aln-cntr just-cntr">
                            <span>{row_value.firmware_version}</span>
                            {this.props.enabled_features?.includes(
                              "DeviceManagement:OTA",
                            ) && (
                              <img
                                src={upto_date}
                                height="15"
                                width="17"
                                className="updated"
                              />
                            )}
                          </div>
                        );
                      } else {
                        return (
                          <div className="display-flex aln-cntr just-cntr">
                            <span>{row_value.firmware_version}</span>
                            {this.props.enabled_features?.includes(
                              "DeviceManagement:OTA",
                            ) && (
                              <img
                                style={{
                                  cursor: "pointer",
                                }}
                                src={update}
                                height="15"
                                width="17"
                                className="not-updated"
                                onClick={() =>
                                  this.singleDeviceFirmwareUpdate([row_value])
                                }
                              />
                            )}
                          </div>
                        );
                      }
                    } else {
                      if (
                        this.props.enabled_features?.includes(
                          "DeviceManagement:OTA",
                        )
                      ) {
                        return (
                          <img
                            src={update}
                            style={{
                              cursor: "pointer",
                            }}
                            onClick={() => {
                              this.singleDeviceFirmwareUpdate([row_value]);
                            }}
                            height="15"
                            width="17"
                            className="mar-left-10"
                          />
                        );
                      } else {
                        return "-";
                      }
                    }
                  })()}
                </div>
              ),
            },
            {
              title: this.props.t? this.props.t('assets'): "Assets",
              width: "13%",
              key: "station",
              dataIndex: "station",
              sorter: (a, b) => a.station_name.localeCompare(b.station_name),
              render: (name, row_value) => (
                <div className="break-word">
                  {(() => {
                    if (name && Array.isArray(name) && name.length) {
                      let more_text = "";
                      if (name.length > 1) {
                        let allNames = [name[1]];
                        name.map((thingName, index) => {
                          if (index > 1) {
                            allNames.push(", " + thingName);
                          }
                        });
                        more_text = ", +" + (name.length - 1) + " more";
                        return (
                          <span>
                            <AntTooltip title={allNames}>
                              <span className="">{name[0] + more_text}</span>
                            </AntTooltip>
                          </span>
                        );
                      } else {
                        return (
                          <span>
                            <span className="">{name[0]}</span>
                          </span>
                        );
                      }
                    } else if (name && _isString(name) && name !== "") {
                      return (
                        <span>
                          <span className="">{name}</span>
                        </span>
                      );
                    } else {
                      return <span>-</span>;
                    }
                  })()}
                </div>
              ),
            },
            /*{
							title: 'Application',
							width: '8%',
							key: 'application',
							dataIndex: 'application',
							sorter: (a, b) =>
								a.application.localeCompare(b.application),
							render: (application) => (
								<div>
									{(() => {
										if (
											application != '' &&
											application != null
										) {
											return (
												<span>
													<span className="">
														{application}
													</span>
												</span>
											);
										} else {
											return <span>-</span>;
										}
									})()}
								</div>
							),
						},*/
            {
              title: this.props.t? this.props.t('customer_name'): "Customer Name",
              // title: "Customer Name",
              width: "13%",
              key: "customer",
              dataIndex: "customer",
              sorter: (a, b) => a.customer.localeCompare(b.customer),
              render: (customer, row_value) => (
                <div className="break-word">
                  {(() => {
                    if (customer != "" && customer != null) {
                      console.log("check device", row_value)
                      return (
                        <span>
                          <span className="">{row_value.goem_id === this.props.client_id && row_value.vendor_id !== row_value.customer_id ? "" : customer}</span>
                        </span>
                      );
                    } else {
                      return <span>-</span>;
                    }
                  })()}
                  {(() => {
                    if (
                      row_value.vendor_id &&
                      row_value.vendor_id !== 1 &&
                      (row_value.customer_id !== row_value.vendor_id || (row_value.goem_id && row_value.vendor_id !== this.props.client_id))
                    ) {// gdai now
                      return (
                        <div style={{ display: "flex", alignItems: "center" }}>
                          <div className="vendor-tag" style={{ marginRight: 8 }}>
                            {row_value.goem_id !== this.props.client_id && row_value.goem_id && this.state.customerNameMap?.[
                                    row_value.goem_id
                                  ] ? this.state.customerNameMap?.[
                                    row_value.goem_id
                                  ] : row_value.vendor_name }
                          </div>
                          {row_value.goem_id !== this.props.client_id && row_value.goem_id && row_value.vendor_id !== this.props.client_id ? (
                              <HeirarchyPopover
                                items={[
                                  row_value.customer_id !== row_value.vendor_id ? customer : '-',
                                  row_value.vendor_name,
                                  this.state.customerNameMap?.[
                                    row_value.goem_id
                                  ],
                                ]}
                              />
                            ) : (
                              ""
                            )}
                        </div>
                      );
                    }
                  })()}
                  <div className="font-12 col-808080">
                    {(() => {
                      if (
                        row_value.application != "" &&
                        row_value.application != null
                      ) {
                        return (
                          <span>
                            <span className="">{row_value.application}</span>
                          </span>
                        );
                      } else {
                        return <span>-</span>;
                      }
                    })()}
                  </div>
                </div>
              ),
            },
            /*{
							title: 'Connectivity Status',
							width: '10%',
							dataIndex: 'active',
							key: 'active',
							sorter: (a, b) => a.active - b.active,
							render: (timestamp, row_value) => (
								<AntTooltip title="Last data received">
									{(() => {
										let status_text = 'Offline',
											online_icon_class = ' offline';
										if (
											row_value.connectivity_status ===
											'online'
										) {
											online_icon_class = ' online';
											status_text = 'Online';
										}
										// return <span className={'dot' + online_icon_class}></span>;
										return (
											<span
												className={
													'table-status-text' +
													online_icon_class
												}
											>
												{timestamp == '' ||
												timestamp == null ||
												timestamp == undefined ||
												timestamp == 0
													? 'Never'
													: status_text}
											</span>
										);
									})()}
									<span className="date-time">
										{timestamp == '' ||
										timestamp == null ||
										timestamp == undefined ||
										timestamp == 0
											? 'Never'
											: moment
													.unix(timestamp)
													.tz('Asia/Kolkata')
													.format('DD MMM YYYY, HH:mm')}
									</span>
								</AntTooltip>
							),
						},*/
            {
              title: this.props.t? this.props.t('network_status'): "Network Status",
              width: "12%",
              key: "network_percent",
              align: "center",
              dataIndex: "network_percent",
              render: (percent, row_value) => (
                <div className="percentage-holder">
                  {/*<AntTooltip title="Data Availability">
										<AntProgress className="percent-icon" type="circle" percent={row_value.availability && row_value.availability != null ? row_value.availability : 0} width={35} strokeColor="#139547"/>
									</AntTooltip>*/}
                  <div className="percent-icon">
                    {(() => {
                      if (row_value.device_config) {
                        let simNo = "";
                        if (row_value.device_sim_slot) {
                          simNo = "Sim " + row_value.device_sim_slot;
                        }
                        if (
                          row_value.device_config.device_modem_type == "gprs"
                        ) {
                          let gprsIcon = GprsSignalRounded;

                          let networkStrengthText = "";
                          if (row_value.connectivity_status === "online") {
                            if (row_value.device_signal_strength > 20) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t? this.props.t('Excellent'): "Excellent"})`;
                              gprsIcon = GprsSignalRounded5;
                            } else if (
                              row_value.device_signal_strength >= 13 &&
                              row_value.device_signal_strength <= 20
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('good') : "Good"})`;
                              gprsIcon = GprsSignalRounded4;
                            } else if (
                              row_value.device_signal_strength >= 6 &&
                              row_value.device_signal_strength <= 12
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t? this.props.t('poor'): "Poor"})`;
                              gprsIcon = GprsSignalRounded2;
                            } else if (row_value.device_signal_strength < 6) {
                              networkStrengthText = "No Signal";
                            }
                          } else {
                            if (row_value.device_signal_strength > 20) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
                            } else if (
                              row_value.device_signal_strength >= 13 &&
                              row_value.device_signal_strength <= 20
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('good') : "Good"})`;
                            } else if (
                              row_value.device_signal_strength >= 6 &&
                              row_value.device_signal_strength <= 12
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('poor') : "Poor"})`;
                            } else if (row_value.device_signal_strength < 6) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;
                            }
                          }

                          return (
                            <div className="display-flex">
                              <AntTooltip title={networkStrengthText}>
                                <img
                                  className="mar-right-10"
                                  src={gprsIcon}
                                  height={20}
                                  width={20}
                                />
                              </AntTooltip>
                              <div>
                                <div>GPRS</div>
                                <div>{simNo}</div>
                              </div>
                            </div>
                          );
                        } else if (
                          row_value.device_config.device_modem_type == "wifi"
                        ) {
                          let wifiIcon = WifiSignalRounded;

                          let networkStrengthText = "";
                          if (row_value.connectivity_status === "online") {
                            if (row_value.device_signal_strength > 20) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t? this.props.t('Excellent'): "Excellent"})`;
                              wifiIcon = WifiSignalRounded3;
                            } else if (
                              row_value.device_signal_strength >= 13 &&
                              row_value.device_signal_strength <= 20
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('good') : "Good"})`;
                              wifiIcon = WifiSignalRounded2;
                            } else if (
                              row_value.device_signal_strength >= 6 &&
                              row_value.device_signal_strength <= 12
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t? this.props.t('poor'): "Poor"})`;
                              wifiIcon = WifiSignalRounded1;
                            } else if (row_value.device_signal_strength < 6) {
                              networkStrengthText = "No Signal";
                            }
                          } else {
                            if (row_value.device_signal_strength > 20) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
                            } else if (
                              row_value.device_signal_strength >= 13 &&
                              row_value.device_signal_strength <= 20
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('good') : "Good"})`;
                            } else if (
                              row_value.device_signal_strength >= 6 &&
                              row_value.device_signal_strength <= 12
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('poor') : "Poor"})`;
                            } else if (row_value.device_signal_strength < 6) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;
                            }
                          }
                          return (
                            <div>
                              <AntTooltip title={networkStrengthText}>
                                <img
                                  className="mar-right-10"
                                  src={wifiIcon}
                                  height={20}
                                  width={20}
                                />
                              </AntTooltip>
                              <span>Wi-Fi</span>
                            </div>
                          );
                        } else if (
                          row_value.device_config.device_modem_type ==
                          "ethernet"
                        ) {
                          if (row_value.connectivity_status === "online") {
                            return (
                              <div>
                                <img
                                  className="mar-right-10"
                                  src={ethernet}
                                  height={20}
                                  width={20}
                                />
                                <span>Ethernet</span>
                              </div>
                            );
                          } else {
                            return (
                              <div>
                                <img
                                  className="mar-right-10"
                                  src={ethernet_inactive}
                                  height={20}
                                  width={20}
                                />
                                <span>Ethernet</span>
                              </div>
                            );
                          }
                        }
                      }
                    })()}
                  </div>
                </div>
              ),
            },
            {
              title: this.props.t? this.props.t('power_status'): "Power Status",
              width: "12%",
              key: "power_status",
              align: "center",
              dataIndex: "power_status",
              render: (power_status, row_value) => (
                <div className="percentage-holder power-container">
                  <div className="icon-holder">
                    {(() => {
                      if (
                        row_value.device_power_status !== null &&
                        row_value.device_power_status !== false
                      ) {
                        let icon = power_inactive,
                          text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('On'): "Last Reported Status: On"}`;
                        if (row_value.connectivity_status === "online") {
                          icon = power_on;
                          text = "Power Status: On";
                        }
                        return (
                          <AntTooltip title={text}>
                            <img src={icon} height={22} width={22} />
                          </AntTooltip>
                        );
                      } else if (row_value.device_power_status === false) {
                        let icon = power_inactive,
                          text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('Off'): "Last Reported Status: Off"}`;
                        if (row_value.connectivity_status === "online") {
                          icon = power_off;
                          text = "Power Status: Off";
                        }
                        return (
                          <AntTooltip title={text}>
                            <img src={icon} height={22} width={22} />
                          </AntTooltip>
                        );
                      } else {
                        let text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('unknown'): "Last Reported Status: Unknown"}`;
                        if (row_value.connectivity_status === "online") {
                          text = `${this.props.t? this.props.t('power_status') +": "+ this.props.t('unknown'): "Power Status: Unknown"}`;
                        }
                        return (
                          <AntTooltip title={text}>
                            <img src={power_inactive} height={22} width={22} />
                          </AntTooltip>
                        );
                      }
                    })()}
                  </div>
                  <div className="battery-container">
                    {(() => {
                      if (row_value.device_battery_percent) {
                        return (
                          <div className="battery mar-left-5">
                            <div className="minus-icon">-</div>
                            <div className="bat-body">
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent > 0 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 20 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 30 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 40 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 50 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 60 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 70 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 80 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 90 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 100 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                            </div>
                            <div className="bat-top"></div>
                            <div className="plus-icon">+</div>
                          </div>
                        );
                      }
                    })()}
                    <div className="bat-charge-details">
                      <div className="icon-holder">
                        {(() => {
                          if (
                            row_value.device_battery_percent !== null &&
                            row_value.device_battery_percent !== false &&
                            row_value.connectivity_status === "online"
                          ) {
                            return (
                              <span>
                                {"(" +
                                  (row_value.device_battery_percent !== 0
                                    ? row_value.device_battery_percent.toFixed(
                                        2,
                                      )
                                    : 0) +
                                  "%)"}
                              </span>
                            );
                          }
                        })()}
                      </div>
                      <div className="icon-holder mar-left-5">
                        {(() => {
                          if (
                            row_value.device_charging_status !== null &&
                            row_value.device_battery_percent !== null
                          ) {
                            if (
                              row_value.device_charging_status &&
                              row_value.connectivity_status === "online"
                            ) {
                              return (
                                <img
                                  src={lighting_active}
                                  height={15}
                                  width={15}
                                />
                              );
                            } else {
                              return (
                                <img
                                  src={lighting_inactive}
                                  height={15}
                                  width={15}
                                />
                              );
                            }
                          }
                        })()}
                      </div>
                    </div>
                  </div>
                </div>
              ),
            },
            {
              title: "SIM",
              width: "8%",
              key: "sim_details",
              align: "center",
              dataIndex: "sim_details",
              render: (sim_details, row_value) =>
                this.renderSimDetails(sim_details, row_value),
            },
            /*{
							title: 'Online Percentage',
							width: '8%',
							key: 'percent',
							align: 'center',
							sorter: (a, b) => a.percent - b.percent,
							dataIndex: 'percent',
							render: (percent, row_value) => (
								<div className="percentage-holder">
									<AntTooltip title="This month online %">
										<AntProgress
											className="percent-icon"
											type="circle"
											percent={
												row_value.percent &&
												row_value.percent != null
													? row_value.percent
													: 0
											}
											width={35}
											strokeColor="#21A1DB"
										/>
									</AntTooltip>
								</div>
							),
						},*/
            /*{
							title: 'Debug',
							width: '8%',
							align: 'center',
							key: 'health',
							render: (config, row_data) => (
								<div>
									<div className="icon-holder">
										<AntTooltip title="Open Debug">
											<img
												src={debug}
												className="cursor-pointer"
												height={28}
												width={32}
												onClick={() =>
													this.openDeviceDebug(row_data)
												}
											/>
										</AntTooltip>
									</div>
									<div className="device-errors-tags">
										{(() => {
											let errors = [],
												extraTypes = '';
											if (
												row_data.device_error_list &&
												row_data.device_error_list.length
											) {
												row_data.device_error_list.map(
													(errorType, index) => {
														if (index === 0) {
															errors.push(
																<AntTooltip
																	title={moment
																		.unix(
																			row_data.timestamp
																		)
																		.tz(
																			'Asia/Kolkata'
																		)
																		.format(
																			'DD MMM YYYY HH:mm'
																		)}
																>
																	<span className="mar-lt-5">
																		{errorType +
																			', '}
																	</span>
																</AntTooltip>
															);
														} else if (index === 1) {
															errors.push(
																<AntTooltip
																	title={moment
																		.unix(
																			row_data.timestamp
																		)
																		.tz(
																			'Asia/Kolkata'
																		)
																		.format(
																			'DD MMM YYYY HH:mm'
																		)}
																>
																	<span className="mar-lt-5">
																		{errorType}
																	</span>
																</AntTooltip>
															);
														} else {
															if (
																index ===
																row_data
																	.device_error_list
																	.length -
																	1
															) {
																extraTypes = extraTypes.concat(
																	errorType +
																		' - ' +
																		moment
																			.unix(
																				row_data.timestamp
																			)
																			.tz(
																				'Asia/Kolkata'
																			)
																			.format(
																				'DD MMM YYYY HH:mm'
																			)
																);
															} else {
																extraTypes = extraTypes.concat(
																	errorType +
																		' - ' +
																		moment
																			.unix(
																				row_data.timestamp
																			)
																			.tz(
																				'Asia/Kolkata'
																			)
																			.format(
																				'DD MMM YYYY HH:mm'
																			) +
																		', '
																);
															}
														}
													}
												);

												if (
													row_data.device_error_list
														.length > 2
												) {
													errors.push(
														<AntTooltip
															title={extraTypes}
														>
															<AntTag
																className="mar-lt-5"
																color="red"
															>
																{'+ ' +
																	(row_data
																		.device_error_list
																		.length -
																		2) +
																	' more'}
															</AntTag>
														</AntTooltip>
													);
												}
											}
											return errors;
										})()}
									</div>
								</div>
							),
						},*/
            {
              title: this.props.t? this.props.t('debug'): "Debug",
              // title: "Debug",
              width: "8%",
              align: "center",
              key: "configure",
              render: (config, row_data) => (
                <div className="display-flex aln-cntr just-cntr">
                  <div className="icon-holder mar-right-10">
                    <AntTooltip title={this.props.t? this.props.t('debug'): "Debug"}>
                      <img
                        src={debug}
                        className="cursor-pointer"
                        height={28}
                        width={32}
                        onClick={() => this.openDeviceDebug(row_data)}
                      />
                    </AntTooltip>
                  </div>
                  {this.props.enabled_features?.includes(
                    "DeviceManagement:Rawlog",
                  ) && (
                    <div className="icon-holder mar-right-10">
                      <AntTooltip title="Raw log">
                        <img
                          src={raw_log_icon}
                          className="cursor-pointer"
                          height={28}
                          width={32}
                          onClick={() => this.openDeviceDebug(row_data, true)}
                        />
                      </AntTooltip>
                    </div>
                  )}
                  {(() => {
                    if (/*this.props.application_id !== 17*/ true) {
                      if (true) {
                        /*let routeLink =
													this.iotBasePath +
													'/enterprise/' +
													this.props.client_id +
													this.platform_slug +
													'/devices/assigned/' +
													(config.device_config &&
													config.device_config
														.station_id &&
													config.device_config.station_id
														.length
														? config.device_config
																.station_id[0]
														: row_data.vendor_id
														? row_data.vendor_id
														: 0) +
													'/communication?app_id=' +
													row_data.application_id;*/
                        let routeLink =
                          this.iotBasePath +
                          "/enterprise/" +
                          this.props.client_id +
                          this.platform_slug +
                          "/devices/assigned/" +
                          row_data.id +
                          "/communication?type_id=" +
                          row_data.type_id;
                        if (this.props.is_application_filter) {
                          /*routeLink =
														this.platform_slug +
														'/customer-management/' +
														this.props.customer_id +
														'/applications/' +
														this.state.selected_app +
														'/devices/' +
														(config.device_config &&
														config.device_config
															.station_id &&
														config.device_config
															.station_id.length
															? config.device_config
																	.station_id[0]
															: row_data.vendor_id
															? row_data.vendor_id
															: 0) +
														'/communication?app_id=' +
														row_data.application_id;*/

                          routeLink =
                            this.iotBasePath +
                            "/enterprise/" +
                            this.props.client_id +
                            this.platform_slug +
                            "/customer-management/" +
                            this.props.customer_id +
                            "/applications/" +
                            this.state.selected_app +
                            "/devices/" +
                            row_data.id +
                            "/communication?type_id=" +
                            row_data.type_id;
                        }
                        if (
                          row_data.type_id == 11 ||
                          row_data.type_id == 12 ||
                          row_data.type_id == 51
                        ) {
                          return (
                            <AntTooltip title={this.props.t? this.props.t('configure_device'): "Configure Device"}>
                              <img
                                className="mar-right-10 cursor-pointer"
                                src={configuration}
                                height={28}
                                width={32}
                                onClick={() =>
                                  this.openConfiguration(routeLink)
                                }
                              />
                            </AntTooltip>
                          );
                        }
                      } else {
                        if (
                          row_data.type_id == 11 ||
                          row_data.type_id == 12 ||
                          row_data.type_id == 51
                        ) {
                          return (
                            <AntTooltip title="Configure Device">
                              <img
                                className="mar-right-10 cursor-block"
                                src={configuration}
                                height={28}
                                width={32}
                              />
                            </AntTooltip>
                          );
                        }
                      }
                    }
                  })()}
                  {(() => {
                    if (
                      this.props.application_id == 12 ||
                      (this.props.enabled_features &&
                        this.props.enabled_features.includes(
                          "DeviceManagement:CustomCommand",
                        ))
                    ) {
                      return (
                        <AntTooltip title="Custom Command">
                          <img
                            src={custom_command}
                            className="cursor-pointer"
                            height={28}
                            width={32}
                            onClick={() =>
                              this.openCustomCommand(row_data.id, row_data)
                            }
                          />
                        </AntTooltip>
                      );
                    }
                  })()}
                </div>
              ),
            },
            /*{
							title: 'Device Status',
							width: '9%',
							align: 'center',
							key: 'device_status',
							render: (row_data) => (
								<div>
									<AntTooltip
										title={
											row_data.status_code == 7
												? 'Inactive'
												: 'Active'
										}
									>
										<AntSwitch
											size="medium"
											checkedChildren="Active"
											unCheckedChildren="Inactive"
											checked={
												row_data.status_code == 7
													? false
													: true
											}
											onChange={(checked, event) =>
												this.onUpdateStatus(
													checked,
													event,
													row_data
												)
											}
										></AntSwitch>
									</AntTooltip>
								</div>
							),
						},*/
          ];
        }
      } else {
        if (this.props.customer_id == 392 || this.props.client_id == 392) {
          columns = [
            {
              title: "Device QR",
              key: "qr",
              width: "25%",
              dataIndex: "qr",
              sorter: (a, b) => a.qr.localeCompare(b.qr),
              render: (qr, row_value) => (
                <div>
                  <div className="device-name">
                    <AntTooltip title={qr}>{qr}</AntTooltip>
                  </div>
                  <div>{row_value.type_name}</div>
                </div>
              ),
            },
            {
              title: this.props.t? this.props.t('customer_name'): "Customer Name",
              // title: "Customer Name",
              key: "customer",
              width: "25%",
              dataIndex: "customer",
              sorter: (a, b) => a.customer.localeCompare(b.customer),
              render: (customer) => (
                <div>
                  {(() => {
                    if (customer != "" && customer != null) {
                      return (
                        <span>
                          <span className="">{customer}</span>
                        </span>
                      );
                    } else {
                      return <span>-</span>;
                    }
                  })()}
                </div>
              ),
            },
            {
              title: "Status",
              dataIndex: "active",
              width: "10%",
              // align: 'center',
              key: "active",
              sorter: (a, b) => a.active - b.active,
              render: (timestamp) => (
                <AntTooltip title="Last data received">
                  {(() => {
                    let current_time = moment().unix(),
                      less_fifteen_online = current_time - timestamp,
                      fifteen_minute = 900,
                      online_icon_class = "",
                      status_text = "";
                    if (less_fifteen_online <= fifteen_minute) {
                      online_icon_class = " online";
                      status_text = "Online";
                    } else {
                      online_icon_class = " offline";
                      status_text = "Offline";
                    }
                    // return <span className={'dot' + online_icon_class}></span>;
                    return (
                      <span className={"table-status-text" + online_icon_class}>
                        {timestamp == "" ||
                        timestamp == null ||
                        timestamp == undefined ||
                        timestamp == 0
                          ? "Never"
                          : status_text}
                      </span>
                    );
                  })()}
                  <span className="date-time">
                    {timestamp == "" ||
                    timestamp == null ||
                    timestamp == undefined ||
                    timestamp == 0
                      ? "Never"
                      : moment
                          .unix(timestamp)
                          .tz("Asia/Kolkata")
                          .format("DD MMM YYYY, HH:mm")}
                  </span>
                </AntTooltip>
              ),
            },
            {
              title: "Device Status",
              width: "15%",
              align: "center",
              key: "device_status",
              render: (row_data) => (
                <div className="device-data-center">
                  {(() => {
                    if (row_data.status_code == 7) {
                      return <span>Blocked</span>;
                    } else {
                      return <span>Active</span>;
                    }
                  })()}
                </div>
              ),
            },
            {
              title: "Description",
              key: "description",
              width: "25%",
              dataIndex: "description",
            },
          ];
        } else {
          columns = [
            {
              title: this.props.t? this.props.t('device_serial_id'): "Device Serial ID",
              width: "16%",
              key: "qr",
              dataIndex: "qr",
              sorter: (a, b) => a.qr.localeCompare(b.qr),
              render: (qr, row_value) => (
                <div>{this.getDeviceDetails(qr, row_value, this.props.t)}</div>
              ),
            },
            {
              title: this.props.t? this.props.t('created_date'): "Created Date",
              width: "10%",
              align: "center",
              key: "created_at",
              dataIndex: "created_at",
              sorter: (a, b) => a.created_at - b.created_at,
              render: (created_at, row_value) => (
                <div>
                  {!isNaN(created_at) && created_at > 0
                    ? moment
                        .unix(created_at)
                        .tz("Asia/Kolkata")
                        .format("DD MMM YYYY, HH:mm")
                    : "-"}
                </div>
              ),
            },
            {
              title: this.props.t? this.props.t('firmware'): "Firmware",
              width: "11%",
              align: "center",
              key: "version",
              render: (row_value) => (
                <div>
                  {(() => {
                    let firm_ver = _filter(this.state.device_lists.firmwares, {
                      device_type_id: row_value.type_id,
                    });

                    if (firm_ver.length && row_value.firmware_version != "") {
                      if (
                        row_value.firmware_version ===
                        firm_ver[firm_ver.length - 1].version
                      ) {
                        return (
                          <div className="display-flex aln-cntr just-cntr">
                            <span>{row_value.firmware_version}</span>
                            {/* <img
															src={upto_date}
															height="15"
															width="17"
															className="updated"
														/> */}
                          </div>
                        );
                      } else {
                        return (
                          <div className="display-flex aln-cntr just-cntr">
                            <span>{row_value.firmware_version}</span>
                            {/* <img
															src={update}
															height="15"
															width="17"
															className="mar-left-10"
														/> */}
                          </div>
                        );
                      }
                    } else {
                      return "-";
                    }
                  })()}
                </div>
              ),
            },
            {
              title: this.props.t? this.props.t('assets'): "Assets",
              width: "14%",
              key: "station",
              dataIndex: "station",
              sorter: (a, b) => a.station_name.localeCompare(b.station_name),
              render: (name, row_value) => (
                <div className="break-word">
                  {(() => {
                    if (name && Array.isArray(name) && name.length) {
                      let more_text = "";
                      if (name.length > 1) {
                        let allNames = [name[1]];
                        name.map((thingName, index) => {
                          if (index > 1) {
                            allNames.push(", " + thingName);
                          }
                        });
                        more_text = ", +" + (name.length - 1) + " more";
                        return (
                          <span>
                            <AntTooltip title={allNames}>
                              <span className="">{name[0] + more_text}</span>
                            </AntTooltip>
                          </span>
                        );
                      } else {
                        return (
                          <span>
                            <span className="">{name[0]}</span>
                          </span>
                        );
                      }
                    } else if (name && _isString(name) && name !== "") {
                      return (
                        <span>
                          <span className="">{name}</span>
                        </span>
                      );
                    } else {
                      return <span>-</span>;
                    }
                  })()}
                </div>
              ),
            },
            /*{
							title: 'Connectivity Status',
							width: '10%',
							dataIndex: 'active',
							key: 'active',
							sorter: (a, b) => a.active - b.active,
							render: (timestamp, row_value) => (
								<AntTooltip title="Last data received">
									{(() => {
										let status_text = 'Offline',
											online_icon_class = ' offline';
										if (
											row_value.connectivity_status ===
											'online'
										) {
											online_icon_class = ' online';
											status_text = 'Online';
										}
										// return <span className={'dot' + online_icon_class}></span>;
										return (
											<span
												className={
													'table-status-text' +
													online_icon_class
												}
											>
												{timestamp == '' ||
												timestamp == null ||
												timestamp == undefined ||
												timestamp == 0
													? 'Never'
													: status_text}
											</span>
										);
									})()}
									<span className="date-time">
										{timestamp == '' ||
										timestamp == null ||
										timestamp == undefined ||
										timestamp == 0
											? 'Never'
											: moment
													.unix(timestamp)
													.tz('Asia/Kolkata')
													.format(
														'DD MMM YYYY, HH:mm'
													)}
									</span>
								</AntTooltip>
							),
						},*/
            /*{
							title: 'Online Percentage',
							width: '10%',
							key: 'percent',
							align: 'center',
							sorter: (a, b) => a.percent - b.percent,
							dataIndex: 'percent',
							render: (percent, row_value) => (
								<div className="percentage-holder">
									<AntTooltip title="This month online %">
										<AntProgress
											className="percent-icon"
											type="circle"
											percent={
												row_value.percent &&
												row_value.percent != null
													? row_value.percent
													: 0
											}
											width={35}
											strokeColor="#21A1DB"
										/>
									</AntTooltip>
								</div>
							),
						},*/
            {
              title: this.props.t? this.props.t('network_status'): "Network Status",
              width: "13%",
              key: "network_percent",
              align: "center",
              dataIndex: "network_percent",
              render: (percent, row_value) => (
                <div className="percentage-holder">
                  {/*<AntTooltip title="Data Availability">
										<AntProgress className="percent-icon" type="circle" percent={row_value.availability && row_value.availability != null ? row_value.availability : 0} width={35} strokeColor="#139547"/>
									</AntTooltip>*/}
                  <div className="percent-icon">
                    {(() => {
                      if (row_value.device_config) {
                        let simNo = "";
                        if (row_value.device_sim_slot) {
                          simNo = "Sim " + row_value.device_sim_slot;
                        }
                        if (
                          row_value.device_config.device_modem_type == "gprs"
                        ) {
                          let gprsIcon = GprsSignalRounded;

                          let networkStrengthText = "";
                          if (row_value.connectivity_status === "online") {
                            if (row_value.device_signal_strength > 20) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t? this.props.t('Excellent'): "Excellent"})`;
                              gprsIcon = GprsSignalRounded5;
                            } else if (
                              row_value.device_signal_strength >= 13 &&
                              row_value.device_signal_strength <= 20
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('good') : "Good"})`;
                              gprsIcon = GprsSignalRounded4;
                            } else if (
                              row_value.device_signal_strength >= 6 &&
                              row_value.device_signal_strength <= 12
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                                row_value.device_signal_strength +
                                " (Poor)";
                              gprsIcon = GprsSignalRounded2;
                            } else if (row_value.device_signal_strength < 6) {
                              networkStrengthText = "No Signal";
                            }
                          } else {
                            if (row_value.device_signal_strength > 20) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
                            } else if (
                              row_value.device_signal_strength >= 13 &&
                              row_value.device_signal_strength <= 20
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('good') : "Good"})`;
                            } else if (
                              row_value.device_signal_strength >= 6 &&
                              row_value.device_signal_strength <= 12
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('poor') : "Poor"})`;
                            } else if (row_value.device_signal_strength < 6) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;
                            }
                          }

                          return (
                            <div className="display-flex">
                              <AntTooltip title={networkStrengthText}>
                                <img
                                  className="mar-right-10"
                                  src={gprsIcon}
                                  height={20}
                                  width={20}
                                />
                              </AntTooltip>
                              <div>
                                <div>GPRS</div>
                                <div>{simNo}</div>
                              </div>
                            </div>
                          );
                        } else if (
                          row_value.device_config.device_modem_type == "wifi"
                        ) {
                          let wifiIcon = WifiSignalRounded;

                          let networkStrengthText = "";
                          if (row_value.connectivity_status === "online") {
                            if (row_value.device_signal_strength > 20) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t? this.props.t('Excellent'): "Excellent"})`;
                              wifiIcon = WifiSignalRounded3;
                            } else if (
                              row_value.device_signal_strength >= 13 &&
                              row_value.device_signal_strength <= 20
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('good') : "Good"})`;
                              wifiIcon = WifiSignalRounded2;
                            } else if (
                              row_value.device_signal_strength >= 6 &&
                              row_value.device_signal_strength <= 12
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t? this.props.t('poor'): "Poor"})`;
                              wifiIcon = WifiSignalRounded1;
                            } else if (row_value.device_signal_strength < 6) {
                              networkStrengthText = "No Signal";
                            }
                          } else {
                            if (row_value.device_signal_strength > 20) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
                            } else if (
                              row_value.device_signal_strength >= 13 &&
                              row_value.device_signal_strength <= 20
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('good') : "Good"})`;
                            } else if (
                              row_value.device_signal_strength >= 6 &&
                              row_value.device_signal_strength <= 12
                            ) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                                row_value.device_signal_strength +
                                ` (${this.props.t ? this.props.t('poor') : "Poor"})`;
                            } else if (row_value.device_signal_strength < 6) {
                              networkStrengthText =
                                `${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;
                            }
                          }
                          return (
                            <div>
                              <AntTooltip title={networkStrengthText}>
                                <img
                                  className="mar-right-10"
                                  src={wifiIcon}
                                  height={20}
                                  width={20}
                                />
                              </AntTooltip>
                              <span>Wi-Fi</span>
                            </div>
                          );
                        } else if (
                          row_value.device_config.device_modem_type ==
                          "ethernet"
                        ) {
                          if (row_value.connectivity_status === "online") {
                            return (
                              <div>
                                <img
                                  className="mar-right-10"
                                  src={ethernet}
                                  height={20}
                                  width={20}
                                />
                                <span>Ethernet</span>
                              </div>
                            );
                          } else {
                            return (
                              <div>
                                <img
                                  className="mar-right-10"
                                  src={ethernet_inactive}
                                  height={20}
                                  width={20}
                                />
                                <span>Ethernet</span>
                              </div>
                            );
                          }
                        }
                      }
                    })()}
                  </div>
                </div>
              ),
            },
            {
              title: this.props.t? this.props.t('power_status'): "Power Status",
              width: "13%",
              key: "power_status",
              align: "center",
              dataIndex: "power_status",
              render: (power_status, row_value) => (
                <div className="percentage-holder power-container">
                  <div className="icon-holder">
                    {(() => {
                      if (
                        row_value.device_power_status !== null &&
                        row_value.device_power_status !== false
                      ) {
                        let icon = power_inactive,
                          text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('On'): "Last Reported Status: On"}`;
                        if (row_value.connectivity_status === "online") {
                          icon = power_on;
                          text = `${this.props.t? this.props.t('power_status') +": " + this.props.t('On'): "Power Status: On"}`;
                        }
                        return (
                          <AntTooltip title={text}>
                            <img src={icon} height={22} width={22} />
                          </AntTooltip>
                        );
                      } else if (row_value.device_power_status === false) {
                        let icon = power_inactive,
                          text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('Off'): "Last Reported Status: Off"}`;
                        if (row_value.connectivity_status === "online") {
                          icon = power_off;
                          text = `${this.props.t? this.props.t('power_status') +": " + this.props.t('Off'): "Power Status: Off"}`;
                        }
                        return (
                          <AntTooltip title={text}>
                            <img src={icon} height={22} width={22} />
                          </AntTooltip>
                        );
                      } else {
                        let text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('unknown'): "Last Reported Status: Unknown"}`;
                        if (row_value.connectivity_status === "online") {
                          text = `${this.props.t? this.props.t('power_status') +": " + this.props.t('unknown'): "Power Status: Unknown"}`;
                        }
                        return (
                          <AntTooltip title={text}>
                            <img src={power_inactive} height={22} width={22} />
                          </AntTooltip>
                        );
                      }
                    })()}
                  </div>
                  <div className="battery-container">
                    {(() => {
                      if (row_value.device_battery_percent) {
                        return (
                          <div className="battery mar-left-5">
                            <div className="minus-icon">-</div>
                            <div className="bat-body">
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent > 0 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 20 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 30 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 40 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 50 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 60 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 70 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 80 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 90 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                              <div
                                className={
                                  "bar" +
                                  (row_value.device_battery_percent !== null &&
                                  row_value.device_battery_percent >= 100 &&
                                  row_value.connectivity_status === "online"
                                    ? " active"
                                    : " deactive")
                                }
                              ></div>
                            </div>
                            <div className="bat-top"></div>
                            <div className="plus-icon">+</div>
                          </div>
                        );
                      }
                    })()}
                    <div className="bat-charge-details">
                      <div className="icon-holder">
                        {(() => {
                          if (
                            row_value.device_battery_percent !== null &&
                            row_value.device_battery_percent !== false &&
                            row_value.connectivity_status === "online"
                          ) {
                            return (
                              <span>
                                {"(" +
                                  (row_value.device_battery_percent !== 0
                                    ? row_value.device_battery_percent.toFixed(
                                        2,
                                      )
                                    : 0) +
                                  "%)"}
                              </span>
                            );
                          }
                        })()}
                      </div>
                      <div className="icon-holder mar-left-5">
                        {(() => {
                          if (
                            row_value.device_charging_status !== null &&
                            row_value.device_battery_percent !== null
                          ) {
                            if (
                              row_value.device_charging_status &&
                              row_value.connectivity_status === "online"
                            ) {
                              return (
                                <img
                                  src={lighting_active}
                                  height={15}
                                  width={15}
                                />
                              );
                            } else {
                              return (
                                <img
                                  src={lighting_inactive}
                                  height={15}
                                  width={15}
                                />
                              );
                            }
                          }
                        })()}
                      </div>
                    </div>
                  </div>
                </div>
              ),
            },
            {
              title: "SIM",
              width: "10%",
              key: "sim_details",
              align: "center",
              dataIndex: "sim_details",
              render: (sim_details, row_value) =>
                this.renderSimDetails(sim_details, row_value),
            },
            /*{
							title: 'Debug',
							width: '10%',
							align: 'center',
							key: 'health',
							render: (config, row_data) => (
								<div>
									<div className="icon-holder">
										<AntTooltip title="Open Debug">
											<img
												src={debug}
												className="cursor-pointer"
												height={28}
												width={32}
												onClick={() =>
													this.openDeviceDebug(
														row_data
													)
												}
											/>
										</AntTooltip>
									</div>
									<div className="device-errors-tags">
										{(() => {
											let errors = [],
												extraTypes = '';
											if (
												row_data.device_error_list &&
												row_data.device_error_list
													.length
											) {
												row_data.device_error_list.map(
													(errorType, index) => {
														if (index === 0) {
															errors.push(
																<AntTooltip
																	title={moment
																		.unix(
																			row_data.timestamp
																		)
																		.tz(
																			'Asia/Kolkata'
																		)
																		.format(
																			'DD MMM YYYY HH:mm'
																		)}
																>
																	<span className="mar-lt-5">
																		{errorType +
																			', '}
																	</span>
																</AntTooltip>
															);
														} else if (
															index === 1
														) {
															errors.push(
																<AntTooltip
																	title={moment
																		.unix(
																			row_data.timestamp
																		)
																		.tz(
																			'Asia/Kolkata'
																		)
																		.format(
																			'DD MMM YYYY HH:mm'
																		)}
																>
																	<span className="mar-lt-5">
																		{
																			errorType
																		}
																	</span>
																</AntTooltip>
															);
														} else {
															if (
																index ===
																row_data
																	.device_error_list
																	.length -
																	1
															) {
																extraTypes = extraTypes.concat(
																	errorType +
																		' - ' +
																		moment
																			.unix(
																				row_data.timestamp
																			)
																			.tz(
																				'Asia/Kolkata'
																			)
																			.format(
																				'DD MMM YYYY HH:mm'
																			)
																);
															} else {
																extraTypes = extraTypes.concat(
																	errorType +
																		' - ' +
																		moment
																			.unix(
																				row_data.timestamp
																			)
																			.tz(
																				'Asia/Kolkata'
																			)
																			.format(
																				'DD MMM YYYY HH:mm'
																			) +
																		', '
																);
															}
														}
													}
												);

												if (
													row_data.device_error_list
														.length > 2
												) {
													errors.push(
														<AntTooltip
															title={extraTypes}
														>
															<AntTag
																className="mar-lt-5"
																color="red"
															>
																{'+ ' +
																	(row_data
																		.device_error_list
																		.length -
																		2) +
																	' more'}
															</AntTag>
														</AntTooltip>
													);
												}
											}
											return errors;
										})()}
									</div>
								</div>
							),
						},*/
            {
              title: this.props.t? this.props.t('debug'): "Debug",
              width: "13%",
              align: "center",
              key: "configure",
              render: (config, row_data) => (
                <div className="display-flex aln-cntr just-cntr">
                  <div className="icon-holder mar-right-10">
                    <AntTooltip title={this.props.t? this.props.t('debug'): "Debug"}>
                      <img
                        src={debug}
                        className="cursor-pointer"
                        height={28}
                        width={32}
                        onClick={() => this.openDeviceDebug(row_data)}
                      />
                    </AntTooltip>
                  </div>
                  {this.props.enabled_features?.includes(
                    "DeviceManagement:Rawlog",
                  ) && (
                    <div className="icon-holder mar-right-10">
                      <AntTooltip title="Raw log">
                        <img
                          src={raw_log_icon}
                          className="cursor-pointer"
                          height={28}
                          width={32}
                          onClick={() => this.openDeviceDebug(row_data, true)}
                        />
                      </AntTooltip>
                    </div>
                  )}
                  {(() => {
                    if (/*this.props.application_id !== 17*/ true) {
                      if (true) {
                        /*let routeLink =
													this.iotBasePath +
													'/enterprise/' +
													this.props.client_id +
													this.platform_slug +
													'/devices/assigned/' +
													(config.device_config &&
													config.device_config
														.station_id &&
													config.device_config.station_id
														.length
														? config.device_config
																.station_id[0]
														: row_data.vendor_id
														? row_data.vendor_id
														: 0) +
													'/communication?app_id=' +
													row_data.application_id;*/
                        let routeLink =
                          this.iotBasePath +
                          "/enterprise/" +
                          this.props.client_id +
                          this.platform_slug +
                          "/devices/assigned/" +
                          row_data.id +
                          "/communication?type_id=" +
                          row_data.type_id;
                        if (this.props.is_application_filter) {
                          /*routeLink =
														this.platform_slug +
														'/customer-management/' +
														this.props.customer_id +
														'/applications/' +
														this.state.selected_app +
														'/devices/' +
														(config.device_config &&
														config.device_config
															.station_id &&
														config.device_config
															.station_id.length
															? config.device_config
																	.station_id[0]
															: row_data.vendor_id
															? row_data.vendor_id
															: 0) +
														'/communication?app_id=' +
														row_data.application_id;*/

                          routeLink =
                            this.iotBasePath +
                            "/enterprise/" +
                            this.props.client_id +
                            this.platform_slug +
                            "/customer-management/" +
                            this.props.customer_id +
                            "/applications/" +
                            this.state.selected_app +
                            "/devices/" +
                            row_data.id +
                            "/communication?type_id=" +
                            row_data.type_id;
                        }
                        if (
                          row_data.type_id == 11 ||
                          row_data.type_id == 12 ||
                          row_data.type_id == 51
                        ) {
                          return (
                            <AntTooltip title={this.props.t? this.props.t('configure_device'): "Configure Device"}>
                              <img
                                className="mar-right-10 cursor-pointer"
                                src={configuration}
                                height={28}
                                width={32}
                                onClick={() =>
                                  this.openConfiguration(routeLink)
                                }
                              />
                            </AntTooltip>
                          );
                        }
                      } else {
                        if (
                          row_data.type_id == 11 ||
                          row_data.type_id == 12 ||
                          row_data.type_id == 51
                        ) {
                          return (
                            <AntTooltip title="Configure Device">
                              <img
                                className="mar-right-10 cursor-block"
                                src={configuration}
                                height={28}
                                width={32}
                              />
                            </AntTooltip>
                          );
                        }
                      }
                    }
                  })()}
                  {(() => {
                    if (
                      this.props.application_id == 12 ||
                      (this.props.enabled_features &&
                        this.props.enabled_features.includes(
                          "DeviceManagement:CustomCommand",
                        ))
                    ) {
                      return (
                        <AntTooltip title="Custom Command">
                          <img
                            src={custom_command}
                            className="cursor-pointer"
                            height={28}
                            width={32}
                            onClick={() =>
                              this.openCustomCommand(row_data.id, row_data)
                            }
                          />
                        </AntTooltip>
                      );
                    }
                  })()}
                </div>
              ),
            },
            /*{
							title: 'Device Status',
							width: '10%',
							align: 'center',
							key: 'device_status',
							render: (row_data) => (
								<div>
									<AntTooltip
										title={
											row_data.status_code == 7
												? 'Inactive'
												: 'Active'
										}
									>
										<AntSwitch
											size="medium"
											checkedChildren="Active"
											unCheckedChildren="Inactive"
											checked={
												row_data.status_code == 7
													? false
													: true
											}
											onChange={(checked, event) =>
												this.onUpdateStatus(
													checked,
													event,
													row_data
												)
											}
										></AntSwitch>
									</AntTooltip>
								</div>
							),
						},*/
          ];
        }
      }
    } else {
      if (this.state.client_id == 392) {
        // console.log('this.props.location', 'entered_2');
        columns = [
          {
            title: "Device QR",
            key: "qr",
            width: "25%",
            dataIndex: "qr",
            sorter: (a, b) => a.qr.localeCompare(b.qr),
            render: (qr, row_value) => (
              <div>
                <div className="device-name">
                  <AntTooltip title={qr}>{qr}</AntTooltip>
                </div>
                <div>{row_value.type_name}</div>
              </div>
            ),
          },
          {
            title: this.props.t? this.props.t('customer_name'): "Customer Name",
            // title: "Customer Name",
            key: "customer",
            width: "25%",
            dataIndex: "customer",
            sorter: (a, b) => a.customer.localeCompare(b.customer),
            render: (customer) => (
              <div>
                {(() => {
                  if (customer != "" && customer != null) {
                    return (
                      <span>
                        <span className="">{customer}</span>
                      </span>
                    );
                  } else {
                    return <span>-</span>;
                  }
                })()}
              </div>
            ),
          },
          {
            title: "Status",
            dataIndex: "active",
            width: "10%",
            // align: 'center',
            key: "active",
            sorter: (a, b) => a.active - b.active,
            render: (timestamp) => (
              <AntTooltip title="Last data received">
                {(() => {
                  let current_time = moment().unix(),
                    less_fifteen_online = current_time - timestamp,
                    fifteen_minute = 900,
                    online_icon_class = "",
                    status_text = "";
                  if (less_fifteen_online <= fifteen_minute) {
                    online_icon_class = " online";
                    status_text = "Online";
                  } else {
                    online_icon_class = " offline";
                    status_text = "Offline";
                  }
                  // return <span className={'dot' + online_icon_class}></span>;
                  return (
                    <span className={"table-status-text" + online_icon_class}>
                      {timestamp == "" ||
                      timestamp == null ||
                      timestamp == undefined ||
                      timestamp == 0
                        ? "Never"
                        : status_text}
                    </span>
                  );
                })()}
                <span className="date-time">
                  {timestamp == "" ||
                  timestamp == null ||
                  timestamp == undefined ||
                  timestamp == 0
                    ? "Never"
                    : moment
                        .unix(timestamp)
                        .tz("Asia/Kolkata")
                        .format("DD MMM YYYY, HH:mm")}
                </span>
              </AntTooltip>
            ),
          },
          {
            title: "Device Status",
            width: "15%",
            align: "center",
            key: "device_status",
            render: (row_data) => (
              <div className="device-data-center">
                {(() => {
                  if (row_data.status_code == 7) {
                    return <span>Blocked</span>;
                  } else {
                    return <span>Active</span>;
                  }
                })()}
              </div>
            ),
          },
          {
            title: "Description",
            key: "description",
            width: "25%",
            dataIndex: "description",
          },
        ];
      } else {
        // console.log('this.props.location', 'entered_3');
        columns = [
          {
            title: this.props.t? this.props.t('device_serial_id'): "Device Serial ID",
            width: "16%",
            key: "qr",
            dataIndex: "qr",
            sorter: (a, b) => a.qr.localeCompare(b.qr),
            render: (qr, row_value) => (
              <div>{this.getDeviceDetails(qr, row_value, this.props.t)}</div>
            ),
          },
          {
            title: this.props.t? this.props.t('created_date'): "Created Date",
            width: "9%",
            align: "center",
            key: "created_at",
            dataIndex: "created_at",
            sorter: (a, b) => a.created_at - b.created_at,
            render: (created_at, row_value) => (
              <div>
                {!isNaN(created_at) && created_at > 0
                  ? moment
                      .unix(created_at)
                      .tz("Asia/Kolkata")
                      .format("DD MMM YYYY, HH:mm")
                  : "-"}
              </div>
            ),
          },
          {
            title: this.props.t? this.props.t('firmware'): "Firmware",
            width: "8%",
            align: "center",
            key: "version",
            render: (row_value) => (
              <div>
                {(() => {
                  let firm_ver = _filter(this.state.device_lists.firmwares, {
                    device_type_id: row_value.type_id,
                  });

                  if (firm_ver.length && row_value.firmware_version != "") {
                    if (
                      row_value.firmware_version ===
                      firm_ver[firm_ver.length - 1].version
                    ) {
                      return (
                        <div className="display-flex aln-cntr just-cntr">
                          <span>{row_value.firmware_version + " "}</span>
                          <img
                            src={upto_date}
                            height="15"
                            width="17"
                            className="updated"
                          />
                        </div>
                      );
                    } else {
                      return (
                        <div className="display-flex aln-cntr just-cntr">
                          <span>{row_value.firmware_version + " "}</span>
                          {(() => {
                            if (
                              this.props.application_id &&
                              this.props.application_id !== 17
                            ) {
                              return (
                                <img
                                  style={{
                                    cursor:
                                      row_value.application_id === 17
                                        ? "not-allowed"
                                        : "pointer",
                                  }}
                                  src={update}
                                  height="15"
                                  width="17"
                                  className="not-updated"
                                  onClick={() =>
                                    this.singleDeviceFirmwareUpdate([row_value])
                                  }
                                />
                              );
                            } else {
                              return (
                                <img
                                  src={update}
                                  height="15"
                                  width="17"
                                  className="mar-left-10"
                                />
                              );
                            }
                          })()}
                        </div>
                      );
                    }
                  } else {
                    return "-";
                  }
                })()}
              </div>
            ),
          },
          {
            title: this.props.t? this.props.t('assets'): "Assets",
            width: "9%",
            key: "station",
            dataIndex: "station",
            sorter: (a, b) => a.station_name.localeCompare(b.station_name),
            render: (name, row_value) => (
              <div className="break-word">
                {(() => {
                  if (name && Array.isArray(name) && name.length) {
                    let more_text = "";
                    if (name.length > 1) {
                      let allNames = [name[1]];
                      name.map((thingName, index) => {
                        if (index > 1) {
                          allNames.push(", " + thingName);
                        }
                      });
                      more_text = ", +" + (name.length - 1) + " more";
                      return (
                        <span>
                          <AntTooltip title={allNames}>
                            <span className="">{name[0] + more_text}</span>
                          </AntTooltip>
                        </span>
                      );
                    } else {
                      return (
                        <span>
                          <span className="">{name[0]}</span>
                        </span>
                      );
                    }
                  } else if (name && _isString(name) && name !== "") {
                    return (
                      <span>
                        <span className="">{name}</span>
                      </span>
                    );
                  } else {
                    return <span>-</span>;
                  }
                })()}
              </div>
            ),
          },
          /*{
						title: 'Connectivity Status',
						width: '10%',
						dataIndex: 'active',
						key: 'active',
						sorter: (a, b) => a.active - b.active,
						render: (timestamp, row_value) => (
							<AntTooltip title="Last data received">
								{(() => {
									let status_text = 'Offline',
										online_icon_class = ' offline';
									if (
										row_value.connectivity_status ===
										'online'
									) {
										online_icon_class = ' online';
										status_text = 'Online';
									}
									// return <span className={'dot' + online_icon_class}></span>;
									return (
										<span
											className={
												'table-status-text' +
												online_icon_class
											}
										>
											{timestamp == '' ||
											timestamp == null ||
											timestamp == undefined ||
											timestamp == 0
												? 'Never'
												: status_text}
										</span>
									);
								})()}
								<span className="date-time">
									{timestamp == '' ||
									timestamp == null ||
									timestamp == undefined ||
									timestamp == 0
										? 'Never'
										: moment
												.unix(timestamp)
												.tz('Asia/Kolkata')
												.format(
													'DD MMM YYYY, HH:mm'
												)}
								</span>
							</AntTooltip>
						),
					},*/
          {
            title: "Online Percentage",
            width: "8%",
            key: "percent",
            align: "center",
            // sorter: (a, b) => a.percent - b.percent,
            dataIndex: "percent",
            render: (percent, row_value) => (
              <div className="percentage-holder">
                <AntTooltip title="This month online %">
                  <AntProgress
                    className="percent-icon"
                    type="circle"
                    percent={
                      row_value.percent && row_value.percent != null
                        ? row_value.percent
                        : 0
                    }
                    width={35}
                    strokeColor="#21A1DB"
                  />
                </AntTooltip>
              </div>
            ),
          },
          {
            title: this.props.t? this.props.t('network_status'): "Network Status",
            width: "10%",
            key: "network_percent",
            align: "center",
            dataIndex: "network_percent",
            render: (percent, row_value) => (
              <div className="percentage-holder">
                {/*<AntTooltip title="Data Availability">
										<AntProgress className="percent-icon" type="circle" percent={row_value.availability && row_value.availability != null ? row_value.availability : 0} width={35} strokeColor="#139547"/>
									</AntTooltip>*/}
                <div className="percent-icon">
                  {(() => {
                    if (row_value.device_config) {
                      let simNo = "";
                      if (row_value.device_sim_slot) {
                        simNo = "Sim " + row_value.device_sim_slot;
                      }
                      if (row_value.device_config.device_modem_type == "gprs") {
                        let gprsIcon = GprsSignalRounded;

                        let networkStrengthText = "";
                        if (row_value.connectivity_status === "online") {
                          if (row_value.device_signal_strength > 20) {
                            networkStrengthText =
                              `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                              row_value.device_signal_strength +
                              ` (${this.props.t? this.props.t('Excellent'): "Excellent"})`;
                            gprsIcon = GprsSignalRounded5;
                          } else if (
                            row_value.device_signal_strength >= 13 &&
                            row_value.device_signal_strength <= 20
                          ) {
                            networkStrengthText =
                              `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                              row_value.device_signal_strength +
                              ` (${this.props.t ? this.props.t('good') : "Good"})`;
                            gprsIcon = GprsSignalRounded4;
                          } else if (
                            row_value.device_signal_strength >= 6 &&
                            row_value.device_signal_strength <= 12
                          ) {
                            networkStrengthText =
                              `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                              row_value.device_signal_strength +
                              ` (${this.props.t? this.props.t('poor'): "Poor"})`;
                            gprsIcon = GprsSignalRounded2;
                          } else if (row_value.device_signal_strength < 6) {
                            networkStrengthText = "No Signal";
                          }
                        } else {
                          if (row_value.device_signal_strength > 20) {
                            networkStrengthText =
                              `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                              row_value.device_signal_strength +
                              ` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
                          } else if (
                            row_value.device_signal_strength >= 13 &&
                            row_value.device_signal_strength <= 20
                          ) {
                            networkStrengthText =
                              `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                              row_value.device_signal_strength +
                              ` (${this.props.t ? this.props.t('good') : "Good"})`;
                          } else if (
                            row_value.device_signal_strength >= 6 &&
                            row_value.device_signal_strength <= 12
                          ) {
                            networkStrengthText =
                              `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                              row_value.device_signal_strength +
                              ` (${this.props.t ? this.props.t('poor') : "Poor"})`;
                          } else if (row_value.device_signal_strength < 6) {
                            networkStrengthText =
                              `${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;
                          }
                        }

                        return (
                          <div className="display-flex">
                            <AntTooltip title={networkStrengthText}>
                              <img
                                className="mar-right-10"
                                src={gprsIcon}
                                height={20}
                                width={20}
                              />
                            </AntTooltip>
                            <div>
                              <div>GPRS</div>
                              <div>{simNo}</div>
                            </div>
                          </div>
                        );
                      } else if (
                        row_value.device_config.device_modem_type == "wifi"
                      ) {
                        let wifiIcon = WifiSignalRounded;

                        let networkStrengthText = "";
                        if (row_value.connectivity_status === "online") {
                          if (row_value.device_signal_strength > 20) {
                            networkStrengthText =
                              `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                              row_value.device_signal_strength +
                              ` (${this.props.t? this.props.t('Excellent'): "Excellent"})`;
                            wifiIcon = WifiSignalRounded3;
                          } else if (
                            row_value.device_signal_strength >= 13 &&
                            row_value.device_signal_strength <= 20
                          ) {
                            networkStrengthText =
                              `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                              row_value.device_signal_strength +
                              ` (${this.props.t ? this.props.t('good') : "Good"})`;
                            wifiIcon = WifiSignalRounded2;
                          } else if (
                            row_value.device_signal_strength >= 6 &&
                            row_value.device_signal_strength <= 12
                          ) {
                            networkStrengthText =
                              `${this.props.t? this.props.t('strength') +": ": "Strength: "}` +
                              row_value.device_signal_strength +
                              ` (${this.props.t? this.props.t('poor'): "Poor"})`;
                            wifiIcon = WifiSignalRounded1;
                          } else if (row_value.device_signal_strength < 6) {
                            networkStrengthText = "No Signal";
                          }
                        } else {
                          if (row_value.device_signal_strength > 20) {
                            networkStrengthText =
                              `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                              row_value.device_signal_strength +
                              ` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
                          } else if (
                            row_value.device_signal_strength >= 13 &&
                            row_value.device_signal_strength <= 20
                          ) {
                            networkStrengthText =
                              `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                              row_value.device_signal_strength +
                              ` (${this.props.t ? this.props.t('good') : "Good"})`;
                          } else if (
                            row_value.device_signal_strength >= 6 &&
                            row_value.device_signal_strength <= 12
                          ) {
                            networkStrengthText =
                              `${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}` +
                              row_value.device_signal_strength +
                              ` (${this.props.t ? this.props.t('poor') : "Poor"})`;
                          } else if (row_value.device_signal_strength < 6) {
                            networkStrengthText =
                              `${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;
                          }
                        }
                        return (
                          <div>
                            <AntTooltip title={networkStrengthText}>
                              <img
                                className="mar-right-10"
                                src={wifiIcon}
                                height={20}
                                width={20}
                              />
                            </AntTooltip>
                            <span>Wi-Fi</span>
                          </div>
                        );
                      } else if (
                        row_value.device_config.device_modem_type == "ethernet"
                      ) {
                        if (row_value.connectivity_status === "online") {
                          return (
                            <div>
                              <img
                                className="mar-right-10"
                                src={ethernet}
                                height={20}
                                width={20}
                              />
                              <span>Ethernet</span>
                            </div>
                          );
                        } else {
                          return (
                            <div>
                              <img
                                className="mar-right-10"
                                src={ethernet_inactive}
                                height={20}
                                width={20}
                              />
                              <span>Ethernet</span>
                            </div>
                          );
                        }
                      }
                    }
                  })()}
                </div>
              </div>
            ),
          },
          {
            title: this.props.t? this.props.t('power_status'): "Power Status",
            width: "11%",
            key: "power_status",
            align: "center",
            dataIndex: "power_status",
            render: (power_status, row_value) => (
              <div className="percentage-holder power-container">
                <div className="icon-holder">
                  {(() => {
                    if (
                      row_value.device_power_status !== null &&
                      row_value.device_power_status !== false
                    ) {
                      let icon = power_inactive,
                        text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('On'): "Last Reported Status: On"}`;
                      if (row_value.connectivity_status === "online") {
                        icon = power_on;
                        text = "Power Status: On";
                      }
                      return (
                        <AntTooltip title={text}>
                          <img src={icon} height={22} width={22} />
                        </AntTooltip>
                      );
                    } else if (row_value.device_power_status === false) {
                      let icon = power_inactive,
                        text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('Off'): "Last Reported Status: Off"}`;
                      if (row_value.connectivity_status === "online") {
                        icon = power_off;
                        text = "Power Status: Off";
                      }
                      return (
                        <AntTooltip title={text}>
                          <img src={icon} height={22} width={22} />
                        </AntTooltip>
                      );
                    } else {
                      let text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('unknown'): "Last Reported Status: Unknown"}`;
                      if (row_value.connectivity_status === "online") {
                        text = `${this.props.t? this.props.t('power_status') +": "+ this.props.t('unknown'): "Power Status: Unknown"}`;
                      }
                      return (
                        <AntTooltip title={text}>
                          <img src={power_inactive} height={22} width={22} />
                        </AntTooltip>
                      );
                    }
                  })()}
                </div>
                <div className="battery-container">
                  {(() => {
                    if (row_value.device_battery_percent) {
                      return (
                        <div className="battery mar-left-5">
                          <div className="minus-icon">-</div>
                          <div className="bat-body">
                            <div
                              className={
                                "bar" +
                                (row_value.device_battery_percent !== null &&
                                row_value.device_battery_percent > 0 &&
                                row_value.connectivity_status === "online"
                                  ? " active"
                                  : " deactive")
                              }
                            ></div>
                            <div
                              className={
                                "bar" +
                                (row_value.device_battery_percent !== null &&
                                row_value.device_battery_percent >= 20 &&
                                row_value.connectivity_status === "online"
                                  ? " active"
                                  : " deactive")
                              }
                            ></div>
                            <div
                              className={
                                "bar" +
                                (row_value.device_battery_percent !== null &&
                                row_value.device_battery_percent >= 30 &&
                                row_value.connectivity_status === "online"
                                  ? " active"
                                  : " deactive")
                              }
                            ></div>
                            <div
                              className={
                                "bar" +
                                (row_value.device_battery_percent !== null &&
                                row_value.device_battery_percent >= 40 &&
                                row_value.connectivity_status === "online"
                                  ? " active"
                                  : " deactive")
                              }
                            ></div>
                            <div
                              className={
                                "bar" +
                                (row_value.device_battery_percent !== null &&
                                row_value.device_battery_percent >= 50 &&
                                row_value.connectivity_status === "online"
                                  ? " active"
                                  : " deactive")
                              }
                            ></div>
                            <div
                              className={
                                "bar" +
                                (row_value.device_battery_percent !== null &&
                                row_value.device_battery_percent >= 60 &&
                                row_value.connectivity_status === "online"
                                  ? " active"
                                  : " deactive")
                              }
                            ></div>
                            <div
                              className={
                                "bar" +
                                (row_value.device_battery_percent !== null &&
                                row_value.device_battery_percent >= 70 &&
                                row_value.connectivity_status === "online"
                                  ? " active"
                                  : " deactive")
                              }
                            ></div>
                            <div
                              className={
                                "bar" +
                                (row_value.device_battery_percent !== null &&
                                row_value.device_battery_percent >= 80 &&
                                row_value.connectivity_status === "online"
                                  ? " active"
                                  : " deactive")
                              }
                            ></div>
                            <div
                              className={
                                "bar" +
                                (row_value.device_battery_percent !== null &&
                                row_value.device_battery_percent >= 90 &&
                                row_value.connectivity_status === "online"
                                  ? " active"
                                  : " deactive")
                              }
                            ></div>
                            <div
                              className={
                                "bar" +
                                (row_value.device_battery_percent !== null &&
                                row_value.device_battery_percent >= 100 &&
                                row_value.connectivity_status === "online"
                                  ? " active"
                                  : " deactive")
                              }
                            ></div>
                          </div>
                          <div className="bat-top"></div>
                          <div className="plus-icon">+</div>
                        </div>
                      );
                    }
                  })()}
                  <div className="bat-charge-details">
                    <div className="icon-holder">
                      {(() => {
                        if (
                          row_value.device_battery_percent !== null &&
                          row_value.device_battery_percent !== false &&
                          row_value.connectivity_status === "online"
                        ) {
                          return (
                            <span>
                              {"(" +
                                (row_value.device_battery_percent !== 0
                                  ? row_value.device_battery_percent.toFixed(2)
                                  : 0) +
                                "%)"}
                            </span>
                          );
                        }
                      })()}
                    </div>
                    <div className="icon-holder mar-left-5">
                      {(() => {
                        if (
                          row_value.device_charging_status !== null &&
                          row_value.device_battery_percent !== null
                        ) {
                          if (
                            row_value.device_charging_status &&
                            row_value.connectivity_status === "online"
                          ) {
                            return (
                              <img
                                src={lighting_active}
                                height={15}
                                width={15}
                              />
                            );
                          } else {
                            return (
                              <img
                                src={lighting_inactive}
                                height={15}
                                width={15}
                              />
                            );
                          }
                        }
                      })()}
                    </div>
                  </div>
                </div>
              </div>
            ),
          },
          {
            title: "SIM",
            width: "8%",
            key: "sim_details",
            align: "center",
            dataIndex: "sim_details",
            render: (sim_details, row_value) =>
              this.renderSimDetails(sim_details, row_value),
          },
          /*{
						title: 'Debug',
						width: '10%',
						align: 'center',
						key: 'health',
						render: (config, row_data) => (
							<div>
								<div className="icon-holder">
									<AntTooltip title="Open Debug">
										<img
											src={debug}
											className="cursor-pointer"
											height={28}
											width={32}
											onClick={() =>
												this.openDeviceDebug(
													row_data
												)
											}
										/>
									</AntTooltip>
								</div>
								<div className="device-errors-tags">
									{(() => {
										let errors = [],
											extraTypes = '';
										if (
											row_data.device_error_list &&
											row_data.device_error_list
												.length
										) {
											row_data.device_error_list.map(
												(errorType, index) => {
													if (index === 0) {
														errors.push(
															<AntTooltip
																title={moment
																	.unix(
																		row_data.timestamp
																	)
																	.tz(
																		'Asia/Kolkata'
																	)
																	.format(
																		'DD MMM YYYY HH:mm'
																	)}
															>
																<span className="mar-lt-5">
																	{errorType +
																		', '}
																</span>
															</AntTooltip>
														);
													} else if (
														index === 1
													) {
														errors.push(
															<AntTooltip
																title={moment
																	.unix(
																		row_data.timestamp
																	)
																	.tz(
																		'Asia/Kolkata'
																	)
																	.format(
																		'DD MMM YYYY HH:mm'
																	)}
															>
																<span className="mar-lt-5">
																	{
																		errorType
																	}
																</span>
															</AntTooltip>
														);
													} else {
														if (
															index ===
															row_data
																.device_error_list
																.length -
																1
														) {
															extraTypes = extraTypes.concat(
																errorType +
																	' - ' +
																	moment
																		.unix(
																			row_data.timestamp
																		)
																		.tz(
																			'Asia/Kolkata'
																		)
																		.format(
																			'DD MMM YYYY HH:mm'
																		)
															);
														} else {
															extraTypes = extraTypes.concat(
																errorType +
																	' - ' +
																	moment
																		.unix(
																			row_data.timestamp
																		)
																		.tz(
																			'Asia/Kolkata'
																		)
																		.format(
																			'DD MMM YYYY HH:mm'
																		) +
																	', '
															);
														}
													}
												}
											);

											if (
												row_data.device_error_list
													.length > 2
											) {
												errors.push(
													<AntTooltip
														title={extraTypes}
													>
														<AntTag
															className="mar-lt-5"
															color="red"
														>
															{'+ ' +
																(row_data
																	.device_error_list
																	.length -
																	2) +
																' more'}
														</AntTag>
													</AntTooltip>
												);
											}
										}
										return errors;
									})()}
								</div>
							</div>
						),
					},*/
          {
            title: "Debug & Configure",
            width: "11%",
            align: "center",
            key: "configure",
            render: (config, row_data) => (
              <div className="display-flex aln-cntr just-cntr">
                <div className="icon-holder mar-right-10">
                  <AntTooltip title={this.props.t? this.props.t('debug'): "Debug"}>
                    <img
                      src={debug}
                      className="cursor-pointer"
                      height={28}
                      width={32}
                      onClick={() => this.openDeviceDebug(row_data)}
                    />
                  </AntTooltip>
                </div>
                <div className="icon-holder mar-right-10">
                  <AntTooltip title="Raw log">
                    <img
                      src={raw_log_icon}
                      className="cursor-pointer"
                      height={28}
                      width={32}
                      onClick={() => this.openDeviceDebug(row_data, true)}
                    />
                  </AntTooltip>
                </div>
                {(() => {
                  if (/*this.props.application_id !== 17*/ true) {
                    if (true) {
                      /*let routeLink =
													this.iotBasePath +
													'/enterprise/' +
													this.props.client_id +
													this.platform_slug +
													'/devices/assigned/' +
													(config.device_config &&
													config.device_config
														.station_id &&
													config.device_config.station_id
														.length
														? config.device_config
																.station_id[0]
														: row_data.vendor_id
														? row_data.vendor_id
														: 0) +
													'/communication?app_id=' +
													row_data.application_id;*/
                      let routeLink =
                        this.iotBasePath +
                        "/enterprise/" +
                        this.props.client_id +
                        this.platform_slug +
                        "/devices/assigned/" +
                        row_data.id +
                        "/communication?type_id=" +
                        row_data.type_id;
                      if (this.props.is_application_filter) {
                        /*routeLink =
														this.platform_slug +
														'/customer-management/' +
														this.props.customer_id +
														'/applications/' +
														this.state.selected_app +
														'/devices/' +
														(config.device_config &&
														config.device_config
															.station_id &&
														config.device_config
															.station_id.length
															? config.device_config
																	.station_id[0]
															: row_data.vendor_id
															? row_data.vendor_id
															: 0) +
														'/communication?app_id=' +
														row_data.application_id;*/

                        routeLink =
                          this.iotBasePath +
                          "/enterprise/" +
                          this.props.client_id +
                          this.platform_slug +
                          "/customer-management/" +
                          this.props.customer_id +
                          "/applications/" +
                          this.state.selected_app +
                          "/devices/" +
                          row_data.id +
                          "/communication?type_id=" +
                          row_data.type_id;
                      }
                      if (
                        row_data.type_id == 11 ||
                        row_data.type_id == 12 ||
                        row_data.type_id == 51
                      ) {
                        return (
                          <AntTooltip title={this.props.t? this.props.t('configure_device'): "Configure Device"}>
                            <img
                              className="mar-right-10 cursor-pointer"
                              src={configuration}
                              height={28}
                              width={32}
                              onClick={() => this.openConfiguration(routeLink)}
                            />
                          </AntTooltip>
                        );
                      }
                    } else {
                      if (
                        row_data.type_id == 11 ||
                        row_data.type_id == 12 ||
                        row_data.type_id == 51
                      ) {
                        return (
                          <AntTooltip title="Configure Device">
                            <img
                              className="mar-right-10 cursor-block"
                              src={configuration}
                              height={28}
                              width={32}
                            />
                          </AntTooltip>
                        );
                      }
                    }
                  }
                })()}
                {(() => {
                  if (
                    this.props.application_id == 12 ||
                    (this.props.enabled_features &&
                      this.props.enabled_features.includes(
                        "DeviceManagement:CustomCommand",
                      ))
                  ) {
                    return (
                      <AntTooltip title="Custom Command">
                        <img
                          src={custom_command}
                          className="cursor-pointer"
                          height={28}
                          width={32}
                          onClick={() =>
                            this.openCustomCommand(row_data.id, row_data)
                          }
                        />
                      </AntTooltip>
                    );
                  }
                })()}
              </div>
            ),
          },
          {
            title: "Device Status",
            width: "10%",
            align: "center",
            key: "device_status",
            render: (row_data) => (
              <div>
                <AntTooltip
                  title={row_data.status_code == 7 ? "Inactive" : "Active"}
                >
                  <AntSwitch
                    size="medium"
                    checkedChildren="Active"
                    unCheckedChildren="Inactive"
                    checked={row_data.status_code == 7 ? false : true}
                    onChange={(checked, event) =>
                      this.onUpdateStatus(checked, event, row_data)
                    }
                  ></AntSwitch>
                </AntTooltip>
              </div>
            ),
          },
        ];
      }
    }

    let applicationList = _sortBy(this.state.application_list, [
      function (o) {
        return o.name;
      },
    ]);

    // if (this.state.selected_vendor_id) {
    // 	applicationList = _sortBy(this.state.vendor_application_list, [
    // 		function (o) {
    // 			return o.name;
    // 		},
    // 	]);
    // }

    // if (this.state.selected_client_id) {
    // 	applicationList = _sortBy(this.state.client_application_list, [
    // 		function (o) {
    // 			return o.name;
    // 		},
    // 	]);
    // }

    let customerList = _sortBy(this.state.client_list, [
      function (o) {
        return o.name;
      },
    ]);

    // if (this.state.selected_vendor_id) {
    // 	customerList = _sortBy(this.state.vendor_client_list, [
    // 		function (o) {
    // 			return o.name;
    // 		},
    // 	]);
    // }

    let vendorList = _sortBy(this.state.vendor_list, [
      function (o) {
        return o.name;
      },
    ]);

    let deviceType = _sortBy(this.state.device_type, [
      function (o) {
        return o.name;
      },
    ]);

    let filterConfig = [];

    if (
      this.props.location &&
      Object.keys(this.props.location).length &&
      this.props.location.pathname &&
      this.props.location.pathname.includes("/datoms-x")
    ) {
      filterConfig = [...defaultConfig.datomsxFilterData];
      filterConfig[0].optionData = accountTypeOptions.map((item) => ({
        title: item.label,
        value: item.value,
      }));

      if (this.state.isTerritoryEnabled && this.state.territoryData) {
        filterConfig[1].hideField = false;
        filterConfig[1].component_props.treeData = [this.state.territoryData];
        filterConfig[1].component_props.value =
          this.state.selected_territory_ids;
      }

      filterConfig[2].optionData = [];
      filterConfig[3].optionData = [];
      filterConfig[4].optionData = [];
      filterConfig[5].optionData = [];

      if (vendorList) {
        vendorList.map((vendor, index) => {
          if (
            !vendor.account_type ||
            !this.state.selected_account_type ||
            vendor.account_type === this.state.selected_account_type
          ) {
            const suffix =
              this.props.application_id === 12 &&
              vendor.vendor_id !== 1 &&
              vendor.customer_type?.includes(2)
                ? " [R]"
                : "";
            filterConfig[2].optionData.push({
              title: vendor.name + suffix,
              value: vendor.id,
            });
          }
        });
      }

      if (customerList) {
        customerList.map((customer, index) => {
          if (
            this.state.selected_vendor_id &&
            customer.vendor_id !== this.state.selected_vendor_id
          ) {
            return;
          }
          if (
            !customer.account_type ||
            !this.state.selected_account_type ||
            customer.account_type === this.state.selected_account_type
          ) {
            filterConfig[3].optionData.push({
              title: customer.name,
              value: customer.id,
            });
          }
        });
      }

      if (applicationList) {
        const selectedVendor = this.state.selected_vendor_id
          ? vendorList?.find(
              (vendor) => vendor.id === this.state.selected_vendor_id,
            )
          : "";
        const selectedClient = this.state.selected_client_id
          ? customerList?.find(
              (customer) => customer.id === this.state.selected_client_id,
            )
          : "";
        applicationList.map((application, index) => {
          if (
            this.state.selected_vendor_id &&
            !selectedVendor?.applications?.includes(application.id) &&
            !selectedVendor?.access_applications?.includes(application.id)
          ) {
            return;
          }
          if (
            this.state.selected_client_id &&
            !selectedClient?.applications?.includes(application.id)
          ) {
            return;
          }
          filterConfig[4].optionData.push({
            title: application.name,
            value: application.id,
          });
        });
      }

      if (deviceType) {
        deviceType.map((type, index) => {
          filterConfig[5].optionData.push({
            title: type.name,
            value: type.id,
          });
        });
      }

      filterConfig[0].selectValue = this.state.selected_account_type;
      filterConfig[2].selectValue = this.state.selected_vendor_id;
      filterConfig[3].selectValue = this.state.selected_client_id;
      filterConfig[4].selectValue = this.state.selected_app_id;
      filterConfig[5].selectValue = this.state.selected_type;
      filterConfig[6].selectValue = this.state.selected_connectivity_status;
      filterConfig[7].selectValue = this.state.selected_device_status;
    } else if (
      this.props.location &&
      Object.keys(this.props.location).length &&
      this.props.location.pathname &&
      this.props.location.pathname.includes("/iot-platform")
    ) {
      filterConfig = [...defaultConfig.iotFilterData];

      if (
        this.state.isTerritoryEnabled &&
        this.state.territoryData?.children?.length
      ) {
        filterConfig[0].hideField = false;
        filterConfig[0].component_props.treeData = [this.state.territoryData];
        filterConfig[0].component_props.value =
          this.state.selected_territory_ids;
      }

      filterConfig[1].optionData = [];
      filterConfig[2].optionData = [];
      filterConfig[3].optionData = [];
      filterConfig[4].optionData = [];

      if (vendorList) {
        filterConfig[1].optionRender = (option) =>
          option.data?.customLabel || option.label;
        vendorList.map((vendor, index) => {
          filterConfig[1].optionData.push({
            title: vendor.name,
            value: vendor.id,
            customLabel: (
              <div>
                {vendor.name}{" "}
                {vendor.customerTypeName && (
                  <i
                    style={{
                      display: "inline-block",
                      marginLeft: 4,
                      color: "#808080",
                      fontSize: 12
                    }}
                  >
                    ({vendor.customerTypeName})
                  </i>
                )}
              </div>
            ),
          });
        });
      }

      if (customerList) {
        customerList.map((customer, index) => {
          if (
            this.state.selected_vendor_id &&
            customer.vendor_id !== this.state.selected_vendor_id
          ) {
            return;
          }
          filterConfig[2].optionData.push({
            title: customer.name,
            value: customer.id,
          });
        });
      }

      if (applicationList) {
        applicationList.map((application, index) => {
          if (application.id !== 17) {
            filterConfig[3].optionData.push({
              title: application.name,
              value: application.id,
            });
          }
        });
      }

      if (deviceType) {
        deviceType.map((type, index) => {
          filterConfig[4].optionData.push({
            title: type.name,
            value: type.id,
          });
        });
      }

      filterConfig[1].selectValue = this.state.selected_vendor_id;
      filterConfig[2].selectValue = this.state.selected_client_id;
      filterConfig[3].selectValue = this.state.selected_app_id;
      filterConfig[4].selectValue = this.state.selected_type;
      filterConfig[5].selectValue = this.state.selected_connectivity_status;
      // filterConfig[4].selectValue = this.state.selected_device_status;
    } else {
      filterConfig = [...defaultConfig.applicationFilterData];

      if (deviceType) {
        deviceType.map((type, index) => {
          filterConfig[0].optionData.push({
            title: type.name,
            value: type.id,
          });
        });
      }

      filterConfig[0].selectValue = this.state.selected_type;
      filterConfig[1].selectValue = this.state.selected_connectivity_status;
      filterConfig[2].selectValue = this.state.selected_device_status;
    }

    // console.log('defaultConfig_', filterConfig);

    let applicationSelect = <div />;
    if (
      this.props.is_application_filter &&
      this.props.application_select_list &&
      this.props.application_select_list.length > 1
    ) {
      applicationSelect = (
        <AntRow
          className="search-button-container"
          type="flex"
          justify="space-between"
        >
          <AntCol span={20}>
            <AntSelect
              showSearch
              style={{ width: 300 }}
              value={this.state.selected_app}
              className="application-drop"
              onChange={(e) => this.applicationDrop(e)}
            >
              {(() => {
                return this.props.application_select_list.map((application) => {
                  return (
                    <AntOption
                      key={application.value}
                      value={application.value}
                    >
                      {application.name}
                    </AntOption>
                  );
                });
              })()}
            </AntSelect>
          </AntCol>
        </AntRow>
      );
    }

    let deviceDebug = "";

    // if (this.state.show_device_debug && this.state.selected_row_data) {
    console.log("device-debug-details", this.deviceDetails);
    console.log("parsedddd", this.parsed);
    if (
      this.props.location &&
      Object.keys(this.props.location).length &&
      this.props.location.pathname &&
      this.props.location.pathname.includes("/debug") 
    ) {
      deviceDebug = (
        // <AntDrawer
        // 	title=""
        // 	id="device_debug_drawer"
        // 	className={
        // 		'debug-drawer ' +
        // 		(this.props.collapse ? 'collapsed' : '')
        // 	}
        // 	visible={true}
        // 	mask={true}
        // 	//onClose={() => this.closeDeviceDebug()}
        // 	destroyOnClose={false}
        // 	maskClosable={false}
        // 	getContainer={false}
        // 	closable={false}
        // 	style={{
        // 		overflow: 'auto',
        // 	}}
        // >
        <div className="debug-device-drawer-div">
          <div className="head-section">
            <div className="device-details-container">
              <div className="padding-first">
                <img src={ThingCreated} height={28} width={28} />

                <span>Debug details for </span>
                <span className="device-name">
                  {/* {this.state.selected_row_data.qr} */}
                  {this.deviceDetails.device_qr_code}
                </span>
                <span className="device-category">
                  {/* {this.state.selected_row_data.type_name} */}
                  {this.debug_type_name}
                </span>
              </div>
            </div>
          </div>
          <div className="">
            <DeviceDebug
              t={this.props.t}
              application_id={this.props.application_id}
              clientId={this.props.client_id}
              deviceDetails={this.deviceDetails}
              vendor_id={this.props.vendor_id}
              // selected_device={this.state.selected_device}
              // thingDetails={this.props.thingDetails}
              platform_slug={this.platform_slug}
              {...this.props}
            />
          </div>
        </div>
        // </AntDrawer>
      );
    }
    // }

    let searchConfig = { ...defaultConfig.searchObject };
    searchConfig.value = this.state.table_search;
    if (this.state.selected_rows && this.state.selected_rows.length > 1) {
      searchConfig.disabled = true;
    }

    let filterContainer = <div />,
      searchFilter = <div />;

    if (!this.props.is_application_filter) {
      if (window.innerWidth < 576) {
        filterContainer = (
          <FilterSelectWithSearch
            filterData={[]}
            searchObject={searchConfig}
            isClear={true}
            applyFilterSelect={(value, index) =>
              this.applyFilterSelect(value, index)
            }
            onSearch={(value) => this.searchBoxFilter(value)}
            clearAllFilter={() => this.clearAllFilter()}
          />
        );
      } else {
        filterContainer = (
          <FilterSelectWithSearch
            t={this.props.t}
            filterData={filterConfig}
            searchObject={searchConfig}
            isClear={true}
            applyFilterSelect={(value, index) =>
              this.applyFilterSelect(value, index)
            }
            onSearch={(value) => this.searchBoxFilter(value)}
            clearAllFilter={() => this.clearAllFilter()}
          />
        );
      }
    } else {
      searchFilter = (
        <div className="table-search">
          <SearchInput
            placeholder={this.props.t? this.props.t('search_by_qr_or_customer'): "Search by QR or Customer"}
            className={"filter-search-display "}
            value={decodeURI(this.state.table_search)}
            onSearch={(value) => this.searchBoxFilter(value)}
          />
        </div>
      );
    }

    /*let clearAll = <div />;
		if (
			(this.state.selected_app_id && this.state.selected_app_id.length) ||
			this.state.selected_vendor_id ||
			this.state.selected_client_id ||
			(this.state.selected_type && this.state.selected_type.length) ||
			(this.state.search_value && this.state.search_value !== '') ||
			this.state.selected_connectivity_status ||
			this.state.selected_device_status
		) {
			clearAll = (
				<div
					className="clear-all-container"
					onClick={() => this.clearAllFilter()}
				>
					<img
						src={clear_all}
						className="clear-all"
						height="13"
						width="23"
					/>
					<span>Clear All Filters</span>
				</div>
			);
		}*/
    let simRender = (
      <div className="sub_drawer_content">
        <p className={"sub_drawer_sim_heading"}>SIM 1</p>
        <div className="sub_drawer_each_sim">
          <p className={"sub_drawer_text"}>
            {this.props.t? this.props.t('serial_no'): "Serial No."}
            {/* Serial No. */}
          </p>
          <AntInput
            className={"sub_drawer_sim_details_input"}
            name={"sim_srno_1"}
            value={
              this.state.sim_details && this.state.sim_details[0]?.serial_no
            }
            onChange={(e) =>
              this.onChangeSimDetails(1, "serial_no", e.target.value)
            }
          />
        </div>
        <div className="sub_drawer_each_sim">
          <p className={"sub_drawer_text"}>
            {this.props.t? this.props.t('operator'): "Operator"}
            {/* Operator */}
          </p>
          <AntInput
            className={"sub_drawer_sim_details_input"}
            name={"sim_operator_1"}
            value={
              this.state.sim_details && this.state.sim_details[0]?.operator
            }
            onChange={(e) =>
              this.onChangeSimDetails(1, "operator", e.target.value)
            }
          />
        </div>
        <p className={"sub_drawer_sim_heading"}>SIM 2</p>
        <div className="sub_drawer_each_sim">
          <p className={"sub_drawer_text"}>
            {this.props.t? this.props.t('serial_no'): "Serial No."}
            {/* Serial No. */}
          </p>
          <AntInput
            className={"sub_drawer_sim_details_input"}
            name={"sim_srno_2"}
            value={
              this.state.sim_details && this.state.sim_details[1]?.serial_no
            }
            onChange={(e) =>
              this.onChangeSimDetails(2, "serial_no", e.target.value)
            }
          />
        </div>
        <div className="sub_drawer_each_sim">
          <p className={"sub_drawer_text"}>
            {this.props.t? this.props.t('operator'): "Operator"}
            {/* Operator */}
          </p>
          <AntInput
            className={"sub_drawer_sim_details_input"}
            name={"sim_operator_2"}
            value={
              this.state.sim_details && this.state.sim_details[1]?.operator
            }
            onChange={(e) =>
              this.onChangeSimDetails(2, "operator", e.target.value)
            }
          />
        </div>
      </div>
    );
    let connectionDetails = (
      <AntPasswordModal
        customBody={modalCustomBody || []}
        isVisible={this.state.third_party_modal_visible}
        title="Connection Details"
        okTitle="Close"
        custom_body_className="device-modal-custom-classname"
        onOk={() => this.toggle3rdPartyModal()}
        icon={<img src={DevicePopKey} alt="logo" />}
      />
    );
    let firmwareUpdateNew = (
      <AntPasswordModal
        customBody={firmwareModalBody || []}
        isVisible={this.state.update_firmware}
        title="Firmware update"
        okTitle="Update"
        custom_body_className={`device-modal-custom-classname firmware-update-modal-new ${
          !this.validateFirmwareFields() ? "firmware-update-modal-disabled" : ""
        }`}
        onOk={() => this.updateDeviceFirmware()}
        onCancel={() => this.closeupdateFirmware()}
        icon={<img src={CloudFirmware} alt="logo" />}
      />
    );
    let firmwareUpdateOld = (
      <AntModal
        className="update-firmware-modal"
        title="Update Firmware"
        visible={
          // this.state
          // 	.update_firmware
          false
        }
        onOk={() => this.updateDeviceFirmware()}
        onCancel={() => this.closeupdateFirmware()}
        destroyOnClose={true}
        okText="Update"
      >
        <div className="update-firmware-select">
          <span className="update-firmware-select-label">Firmware</span>
          <AntSelect
            showSearch
            className="firmware-select"
            style={{
              width: 250,
            }}
            placeholder="Select Firmware"
            optionFilterProp="children"
            onChange={(e) => this.selectFirmware(e)}
            onSearch={(e) => this.onSearchFirmware(e)}
            filterOption={(input, option) =>
              option.props.children
                .toLowerCase()
                .indexOf(input.toLowerCase()) >= 0
            }
          >
            {this.firmwareSelectionOptions}
          </AntSelect>
        </div>
      </AntModal>
    );

    if (
      this.props.application_id == 17 &&
      this.props.enabled_features &&
      this.props.enabled_features.includes("DeviceManagement:DeviceDeactivate")
    ) {
      columns.push({
        title: "Device Status",
        width: "9%",
        align: "center",
        key: "device_status",
        render: (row_data) => (
          <div>
            <AntTooltip
              title={row_data.status_code == 7 ? "Inactive" : "Active"}
            >
              <AntSwitch
                size="medium"
                checkedChildren="Active"
                unCheckedChildren="Inactive"
                checked={row_data.status_code == 7 ? false : true}
                onChange={(checked, event) =>
                  this.onUpdateStatus(checked, event, row_data)
                }
              ></AntSwitch>
            </AntTooltip>
          </div>
        ),
      });
    }

    console.log("column__ 1", columns);
    console.log("column__ 2", this.props);

    return (
      <div
        id="datoms_iot_platform_device"
        className={
          "mar-top-70 " +
          (this.props.location &&
          Object.keys(this.props.location).length &&
          this.props.location.pathname &&
          this.props.location.pathname.includes("/iot-platform")
            ? "iot-platform-device-management"
            : this.props.location &&
                Object.keys(this.props.location).length &&
                this.props.location.pathname &&
                this.props.location.pathname.includes("/datoms-x")
              ? "datoms-x-device-management"
              : "application-device-management")
        }
      >
        <Suspense
          fallback={
            <div>
              <AntLayout className={"contains"}>
                <AntSpin className="align-center-loading" />
              </AntLayout>
            </div>
          }
        >
          {deviceDebug ? (
            deviceDebug
          ) : (
            <AntLayout className={"mar-top-52"}>
              <AntContent
                className={
                  "contains" +
                  (this.props.is_application_filter
                    ? " customer-details"
                    : "") +
                  (this.state.mobile_drawer ? " mobile-drawer-open" : "")
                }
              >
                {window.innerWidth < 576 ? (
                  this.state.tableVisible && this.device_details ? (
                    <>
                      <div>
                        <div className="dm_mb_ad_tabs_parent">
                          <TwinTabs
                            onChange={(value) => {
                              let { app_name } = this.props;
                              this.props.history.push(
                                `/${app_name}/devices/${value}`,
                              );
                            }}
                            value={"assigned"}
                            tab_array={[
                              {
                                value: "assigned",
                                name: "Assigned Devices",
                              },
                              {
                                value: "unassigned",
                                name: "Unassigned Devices",
                              },
                            ]}
                          />
                        </div>
                        {this.state.show_no_data ? (
                          <NoDataComponent height="100%" page_name="device" />
                        ) : (
                          <>
                            <div className="filter-select-container">
                              {filterContainer}
                            </div>
                            <MobileDeviceList
                              mobile_drawer_visible={this.state.mobile_drawer}
                              details={"assign"}
                              application_id={this.props.application_id}
                              is_application_filter={
                                this.props.is_application_filter
                              }
                              location={this.props.location}
                              show_no_data={this.state.show_no_data}
                              customer_id={this.props.customer_id}
                              client_id={this.props.client_id}
                              device_lists={this.state.device_lists}
                              renderSimDetails={(a, b) =>
                                this.renderSimDetails(a, b)
                              }
                              deviceListData={deviceListData}
                              singleDeviceFirmwareUpdate={(e) =>
                                this.singleDeviceFirmwareUpdate(e)
                              }
                              mobileDeviceSelected={() =>
                                this.setState({
                                  mobile_device_selected: true,
                                })
                              }
                              getViewAccess={this.props.getViewAccess([
                                "DeviceManagement:View",
                              ])}
                              unassignDevice={(a, b) =>
                                this.unassignDevice(a, b)
                              }
                              mobileDrawerClicked={(e) =>
                                this.mobileDrawerClicked(e)
                              }
                            />
                            <AntPasswordModal
                              customBody={
                                <div className="title">
                                  {"Do you want to Un-assign the device " +
                                    this.state.selected_rows?.[0]?.qr +
                                    " to the customer " +
                                    this.state.selected_rows?.[0]?.customer +
                                    " ?"}
                                </div>
                              }
                              isVisible={
                                this.state.unassign_mobile_devices_modal
                              }
                              title=""
                              okTitle="Yes,Sure"
                              custom_body_className={`device-modal-custom-classname mobile un-assign firmware-update-modal-new`}
                              onOk={() => this.unassignedOnOk()}
                              onCancel={() => this.unassignedOnCancel()}
                              icon={
                                <img
                                  src={unAssignImage2}
                                  alt="logo"
                                  style={{
                                    width: 50,
                                    height: 50,
                                  }}
                                />
                              }
                            />
                            {/* <AntPasswordModal
														customBody={[]}
														isVisible={true}
														title="Do you want to Un-assign the device 2YNRSHKL to the customer Abc Asnad Pvt Ltd ?"
														okTitle="Update"
														custom_body_className="device-modal-custom-classname"
														onOk={() => this.unassignedOnOk()}
														onCancel={() => this.unassignedOnCancel()}
														icon={<img src={unAssignImage} alt="logo" />}
													/> */}
                            {connectionDetails}
                            {firmwareUpdateNew}
                            {firmwareUpdateOld}
                          </>
                        )}
                      </div>
                    </>
                  ) : (
                    <AntLayout className={"contains"}>
                      <AntSpin className="align-center-loading" />
                    </AntLayout>
                  )
                ) : (
                  <>
                    {/* {deviceDebug} */}
                    {(() => {
                      if (
                        !this.props.is_application_filter &&
                        this.props.location &&
                        Object.keys(this.props.location).length &&
                        this.props.location.pathname &&
                        (this.props.location.pathname.includes("/datoms-x") ||
                          this.props.location.pathname.includes(
                            "/iot-platform",
                          ))
                      ) {
                        return (
                          <div role="tablist" className="station-tabs-bar">
                            <div className="station-tabs-nav-scroll">
                              <div className="station-tabs-nav station-tabs-nav-animated">
                                <div>
                                  <div
                                    role="tab"
                                    aria-disabled="false"
                                    aria-selected="true"
                                    className={
                                      "station-tabs station-tabs-tab" +
                                      (this.props.location.pathname &&
                                      this.props.location.pathname.search(
                                        "/devices/assigned",
                                      ) > -1
                                        ? " station-tabs-tab-active"
                                        : "")
                                    }
                                    onClick={() => this.changeGroup("assigned")}
                                  >
                                    {this.props.t? this.props.t('Assigned'): "Assigned"}
                                    {/* Assigned */}
                                  </div>
                                  <div
                                    role="tab"
                                    aria-disabled="false"
                                    aria-selected="true"
                                    className={
                                      "station-tabs station-tabs-tab" +
                                      (this.props.location.pathname &&
                                      this.props.location.pathname.search(
                                        "/devices/unassigned",
                                      ) > -1
                                        ? " station-tabs-tab-active"
                                        : "")
                                    }
                                    onClick={() =>
                                      this.changeGroup("unassigned")
                                    }
                                  >
                                    {this.props.t? this.props.t('Unassigned'): "Unassigned"}
                                    {/* Unassigned */}
                                  </div>
                                </div>
                                <div className="blank-border"></div>
                              </div>
                              <span
                                className="btn-controller-assign-customer hide"
                                onClick={() => {
                                  this.openAssigntoCustomerDrawer();
                                }}
                              >
                                <span className="width-control-assign-customer">
                                  <span className="assign-to-customer-btn">
                                    <UserAddOutlined
                                      width={"10px"}
                                      height={"10px"}
                                    />
                                  </span>
                                  <span className="assign-to-text">
                                    Assign to Customer
                                  </span>
                                </span>
                              </span>
                            </div>
                          </div>
                        );
                      }
                    })()}
                    {applicationSelect}
                    {(() => {
                      if (this.state.tableVisible && this.device_details) {
                        console.log("rerr", deviceListData);
                        return (
                          <div>
                            <div className="filter-select-container">
                              {filterContainer}
                              {/*clearAll*/}
                            </div>
                            {searchFilter}
                            {(() => {
                              if (
                                (this.state.deviceTypeFilterValue &&
                                  this.state.deviceTypeFilterValue.length) ||
                                (this.state.applicationFilterValue &&
                                  this.state.applicationFilterValue.length) ||
                                (this.state.customerFilterValue &&
                                  this.state.customerFilterValue.length)
                              ) {
                                return (
                                  <div className="section-filter-wrapper">
                                    {(() => {
                                      if (
                                        this.state.deviceTypeFilterValue &&
                                        this.state.deviceTypeFilterValue.length
                                      ) {
                                        return (
                                          <AntTreeSelect
                                            default_type={true}
                                            treeNodeFilterProp={"title"}
                                            popupClassName="tree-drop"
                                            treeDefaultExpandAll
                                            onChange={(value, label, extra) =>
                                              this.applyDeviceTypeFilter(
                                                value,
                                                label,
                                                extra,
                                              )
                                            }
                                            // {...deviceTypeFilterPropsView}
                                            className="select-filter online-status"
                                          />
                                        );
                                      }
                                    })()}
                                    {(() => {
                                      if (
                                        this.state.applicationFilterValue &&
                                        this.state.applicationFilterValue.length
                                      ) {
                                        return (
                                          <AntTreeSelect
                                            default_type={true}
                                            treeNodeFilterProp={"title"}
                                            popupClassName="tree-drop"
                                            treeDefaultExpandAll
                                            onChange={(value, label, extra) =>
                                              this.applyApplicationFilter(
                                                value,
                                                label,
                                                extra,
                                              )
                                            }
                                            // {...applicationFilterPropsView}
                                            className="select-filter device-type"
                                          />
                                        );
                                      }
                                    })()}
                                  </div>
                                );
                              }
                            })()}
                            {(() => {
                              if (this.state.add_station) {
                                return (
                                  <AddForm
                                    visible={true}
                                    addNewDevice={(e) => this.addNewDevice(e)}
                                    closeAddStationModal={(e) =>
                                      this.closeAddStationModal(e)
                                    }
                                    device_cities={this.state.device_cities}
                                    unassigned_devices={
                                      this.state.unassigned_devices
                                    }
                                    changeCity={(e) => this.changeCity(e)}
                                    changeStnName={(e) => this.changeStnName(e)}
                                    changeDevice={(e) => this.changeDevice(e)}
                                    changeLat={(e) => this.changeLat(e)}
                                    changeLong={(e) => this.changeLong(e)}
                                    city_id={this.state.city_id}
                                    stations_device={this.state.stations_device}
                                    station_name={this.state.station_name}
                                    station_lat={this.state.station_lat}
                                    station_long={this.state.station_lat}
                                  />
                                );
                              }
                            })()}
                            <AntRow>
                              {(() => {
                                if (
                                  this.state.selected_devices &&
                                  this.state.selected_devices.length
                                ) {
                                  let show_firmware_btn = true;
                                  let first_child = this.state.selected_rows[0];
                                  this.state.selected_rows.map((row_value) => {
                                    if (
                                      first_child.type_id !=
                                        row_value.type_id ||
                                      first_child.application_id !=
                                        row_value.application_id ||
                                      row_value.application_id == 17 ||
                                      first_child.circuit_version !=
                                        row_value.circuit_version ||
                                      (first_child.type_id == 1 &&
                                        first_child.product_model !=
                                          row_value.product_model)
                                    ) {
                                      show_firmware_btn = false;
                                    }
                                  });
                                  return (
                                    <div className="table-options">
                                      <div className="bulk-selection-option">
                                        <div className="device-selected">
                                          <span className="device-selected-value">
                                            {this.state.selected_rows.length}
                                          </span>
                                          {this.props.t? this.props.t('devices_selected'): "Devices Selected"}
                                          {/* Devices Selected */}
                                        </div>
                                        <div className="show-checked-switch-container">
                                          <AntSwitch
                                            className="show-checked-switch"
                                            size="small"
                                            checked={this.state.show_checked}
                                            onChange={(checked, event) =>
                                              this.onShowChecked(checked, event)
                                            }
                                          />
                                          {this.props.t? this.props.t('show_selected_only'): "Show Selected Only"}
                                          {/* Show Selected Only */}
                                        </div>
                                        <div
                                          className="deselect-all-btn"
                                          onClick={() => this.deselectAll()}
                                        >
                                          <StopOutlined className="deselect-icon" />
                                          {this.props.t? this.props.t('deselect_all'): "Deselect All"}
                                          {/* Deselect All */}
                                        </div>
                                      </div>
                                      <div
                                        className={
                                          "assign-to-customer-firmware" +
                                          (this.props.is_application_filter
                                            ? " cust-detls"
                                            : "")
                                        }
                                      >
                                        {(() => {
                                          if (
                                            this.props.location.pathname.includes(
                                              "/datoms-x",
                                            ) ||
                                            this.props.getViewAccess([
                                              "DeviceManagement:Assign",
                                            ], true)
                                          ) {
                                            return (
                                              <div className="assign-to-status-parent">
                                                <div
                                                  className="assign-to-status"
                                                  onClick={(e) =>
                                                    this.unassignDevice()
                                                  }
                                                >
                                                  <img
                                                    src={unassign}
                                                    height="18"
                                                    width="18"
                                                    className="icon"
                                                  />
                                                  <span className="icon-text">
                                                    {this.props.t? this.props.t('unassign'): "Un-assign"}
                                                    {/* Un-assign */}
                                                  </span>
                                                </div>
                                              </div>
                                            );
                                          } else {
                                            return (
                                              <div className="assign-to-status-parent disable-btn">
                                                <div className="assign-to-status" style={{ cursor: 'not-allowed'}}>
                                                  <img
                                                    src={unassign}
                                                    height="18"
                                                    width="18"
                                                    className="icon"
                                                  />
                                                  <span className="icon-text">
                                                    {this.props.t? this.props.t('unassign'): "Un-assign"}
                                                    {/* Un-assign */}
                                                  </span>
                                                </div>
                                              </div>
                                            );
                                          }
                                        })()}
                                        {(() => {
                                          if (
                                            this.props.location.pathname.includes(
                                              "/datoms-x",
                                            ) ||
                                            this.props.getViewAccess([
                                              "DeviceManagement:DeviceDeactivate",
                                            ], true)
                                          ) {
                                            let statusMenu = (
                                              <AntMenu>
                                                <AntMenuItem
                                                  onClick={() =>
                                                    this.updateStatus("unblock")
                                                  }
                                                  key="unblock"
                                                >
                                                  {this.props.t? this.props.t('unblock'): "Unblock"}
                                                  {/* Unblock */}
                                                </AntMenuItem>
                                                <AntMenuItem
                                                  onClick={() =>
                                                    this.updateStatus("block")
                                                  }
                                                  key="block"
                                                >
                                                  {this.props.t? this.props.t('block'): "Block"}
                                                  {/* Block */}
                                                </AntMenuItem>
                                              </AntMenu>
                                            );

                                            if (
                                              this.state.selected_rows &&
                                              this.state.selected_rows.length ==
                                                1
                                            ) {
                                              if (
                                                this.state.selected_rows[0]
                                                  .device_status == "active"
                                              ) {
                                                statusMenu = (
                                                  <AntMenu>
                                                    <AntMenuItem
                                                      onClick={() =>
                                                        this.updateStatus(
                                                          "block",
                                                        )
                                                      }
                                                      key="block"
                                                    >
                                                      {this.props.t? this.props.t('block'): "Block"}
                                                      {/* Block */}
                                                    </AntMenuItem>
                                                  </AntMenu>
                                                );
                                              } else {
                                                statusMenu = (
                                                  <AntMenu>
                                                    <AntMenuItem
                                                      onClick={() =>
                                                        this.updateStatus(
                                                          "unblock",
                                                        )
                                                      }
                                                      key="unblock"
                                                    >
                                                      {this.props.t? this.props.t('unblock'): "Unblock"}
                                                      {/* Unblock */}
                                                    </AntMenuItem>
                                                  </AntMenu>
                                                );
                                              }
                                            }

                                            return (
                                              <div className="assign-to-status-parent">
                                                <AntDropdown
                                                  overlay={statusMenu}
                                                  trigger={["click"]}
                                                  placement="bottomCenter"
                                                >
                                                  <div className="assign-to-status">
                                                    <img
                                                      src={update_status}
                                                      height="18"
                                                      width="22"
                                                      className="icon"
                                                    />
                                                    <span>
                                                      {this.props.t? this.props.t('update_status'): "Update Status"}
                                                      {/* Update Status */}
                                                    </span>
                                                    <DownOutlined className="icon mar-lt-5 mar-rt-20" />
                                                  </div>
                                                </AntDropdown>
                                              </div>
                                            );
                                          } else {
                                            let statusMenu = (
                                              <AntMenu>
                                                <AntMenuItem
                                                  className="blocked-menu"
                                                  key="unblock"
                                                >
                                                  {this.props.t? this.props.t('unblock'): "Unblock"}
                                                  {/* Unblock */}
                                                </AntMenuItem>
                                                <AntMenuItem
                                                  className="blocked-menu"
                                                  key="block"
                                                >
                                                  {this.props.t? this.props.t('block'): "Block"}
                                                  {/* Block */}
                                                </AntMenuItem>
                                              </AntMenu>
                                            );

                                            if (
                                              this.state.selected_rows &&
                                              this.state.selected_rows.length ==
                                                1
                                            ) {
                                              if (
                                                this.state.selected_rows[0]
                                                  .device_status == "active"
                                              ) {
                                                statusMenu = (
                                                  <AntMenu>
                                                    <AntMenuItem
                                                      className="blocked-menu"
                                                      key="block"
                                                    >
                                                      {this.props.t? this.props.t('block'): "Block"}
                                                      {/* Block */}
                                                    </AntMenuItem>
                                                  </AntMenu>
                                                );
                                              } else {
                                                statusMenu = (
                                                  <AntMenu>
                                                    <AntMenuItem
                                                      className="blocked-menu"
                                                      key="unblock"
                                                    >
                                                      {this.props.t? this.props.t('unblock'): "Unblock"}
                                                      {/* Unblock */}
                                                    </AntMenuItem>
                                                  </AntMenu>
                                                );
                                              }
                                            }
                                            return (
                                              <div className="assign-to-status-parent disable-btn">
                                                <AntDropdown
                                                  overlay={statusMenu}
                                                  trigger={["click"]}
                                                  placement="bottomCenter"
                                                >
                                                  <div className="assign-to-status">
                                                    <img
                                                      src={update_status}
                                                      height="18"
                                                      width="23"
                                                      className="icon"
                                                    />
                                                    <span>
                                                    {this.props.t? this.props.t('update_status'): "Update Status"}
                                                      {/* Update Status */}
                                                    </span>
                                                    <DownOutlined className="icon mar-lt-10 mar-rt-20" />
                                                  </div>
                                                </AntDropdown>
                                              </div>
                                            );
                                          }
                                        })()}
                                        {(() => {
                                          if (
                                            this.props.location.pathname.includes(
                                              "/datoms-x",
                                            ) ||
                                            this.props.enabled_features?.includes(
                                              "DeviceManagement:OTA",
                                            )
                                          ) {
                                            if (show_firmware_btn) {
                                              return (
                                                <div className="assign-to-firmware-parent">
                                                  <div
                                                    className="assign-to-firmware"
                                                    onClick={(e) =>
                                                      this.updateFirmware()
                                                    }
                                                  >
                                                    <img
                                                      src={update_firmware}
                                                      height="18"
                                                      width="22"
                                                      className="icon"
                                                    />
                                                    <span className="icon-text">
                                                      Update Firmware
                                                    </span>
                                                  </div>
                                                  {/*<div className={'view-data-firmware' + (this.state.update_firmware ? ' height-add' : '')}>
																			</div>*/}
                                                </div>
                                              );
                                            }
                                          }
                                        })()}
                                      </div>
                                    </div>
                                  );
                                }
                              })()}
                              {connectionDetails}
                              {firmwareUpdateNew}
                              {firmwareUpdateOld}
                            </AntRow>
                            <AntRow>
                              {(() => {
                                console.log(
                                  "column__",
                                  deviceListData,
                                  this.state.remove_loading,
                                );
                              })()}
                              <AntTable
                                className={
                                  "assigned table-width-minus" +
                                  (this.state.table_data &&
                                  this.state.table_data.length
                                    ? " device-table"
                                    : " table-data-view") +
                                  (this.props.is_application_filter
                                    ? " cust-detl"
                                    : "") +
                                  (this.state.table_data &&
                                  this.state.table_data.length == 0
                                    ? " mar-top-40"
                                    : "")
                                }
                                columns={columns}
                                locale={{
                                  emptyText: this.props.t? this.props.t('no_devices_found!'): 'No Devices Found!',
                                }}
                                loading={
                                  this.state.remove_loading ? false : true
                                }
                                scroll={{ x: "max-content" }}
                                sticky={true}
                                pagination={this.device_table_pagination_config}
                                dataSource={deviceListData}
                                onChange={(
                                  pagination,
                                  filters,
                                  sorter,
                                  extra,
                                ) =>
                                  this.handleTableDataChange(
                                    pagination,
                                    filters,
                                    sorter,
                                    extra,
                                  )
                                }
                                rowSelection={tableRowSelectionOptions}
                              />
                            </AntRow>
                          </div>
                        );
                      } else {
                        return (
                          <AntLayout className={"contains"}>
                            <AntSpin className="align-center-loading" />
                          </AntLayout>
                        );
                      }
                    })()}
                    {/*(() => {
								if (this.props.location.pathname && (this.props.location.pathname.search('/debug') > -1)) {
									return <Drawer
										title=''
										id="debug_drawer"
										className={'device-debug-drawer'}
										width={1320}
										visible={true}
										onClose={() => this.closeDebugDrawer()}
										destroyOnClose={true}
										maskClosable={false}
										style={{
											overflow: 'auto',
										}}
									>
										<DeviceDebugPage {...this.props} client_id={this.props.client_id} row_data={this.state.row_data}/>
									</Drawer>;
								}
							})()*/}
                  </>
                )}
              </AntContent>
            </AntLayout>
          )}
        </Suspense>
        {window.innerWidth < 576 ? (
          <AntPasswordModal
            isVisible={this.state.sim_details_drawer}
            icon={<img src={SimMobileIcon} alt="" />}
            onCancel={() => this.setState({ sim_details_drawer: false })}
            onOk={this.updateSimDetails}
            okTitle="Save"
            cancelTitle="Cancel"
            custom_body_className="simdetails-drawer device-modal-custom-classname mobile un-assign firmware-update-modal-new"
            customBody={simRender}
            getContainer={false}
          />
        ) : (
          <AntDrawer
            className="simdetails-drawer"
            visible={this.state.sim_details_drawer}
            title={`SIM ${this.props.t? this.props.t('Details'): "Details"}`}
            onClose={() => this.setState({ sim_details_drawer: false })}
          >
            {simRender}
            <div className="drawer-actions-box">
              <AntButton
                type="primary"
                className="apply-btn"
                onClick={this.updateSimDetails}
                // loading={footProps.loading}
              >
                {this.props.t? this.props.t('save'): "Save"}
                {/* Save */}
              </AntButton>
            </div>
            {/*<div className="sub_drawer_logs">*/}
            {/*	<p>Logs</p>*/}
            {/*	<span>*/}
            {/*		First data received at{' '}*/}
            {/*	</span>*/}
            {/*</div>*/}
          </AntDrawer>
        )}

        {/* {deviceDebug} */}
      </div>
    );
  }
}
