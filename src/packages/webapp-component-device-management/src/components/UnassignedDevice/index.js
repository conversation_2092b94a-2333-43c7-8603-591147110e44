import React, { Suspense, lazy } from 'react';
import AntLayout from '@datoms/react-components/src/components/AntLayout';
import AntContent from '@datoms/react-components/src/components/AntContent';
import AntRow from '@datoms/react-components/src/components/AntRow';
import AntCol from '@datoms/react-components/src/components/AntCol';
import AntTooltip from '@datoms/react-components/src/components/AntTooltip';
import AntSpin from '@datoms/react-components/src/components/AntSpin';
import AntSelect from '@datoms/react-components/src/components/AntSelect';
import AntOption from '@datoms/react-components/src/components/AntOption';
import AntInput from '@datoms/react-components/src/components/AntInput';
import AntModal from '@datoms/react-components/src/components/AntModal';
import AntConfirmModal from '@datoms/react-components/src/components/AntConfirmModal';
import AntMessage from '@datoms/react-components/src/components/AntMessage';
import AntNotification from '@datoms/react-components/src/components/AntNotification';
import AntProgress from '@datoms/react-components/src/components/AntProgress';
import AntTreeSelect from '@datoms/react-components/src/components/AntTreeSelect';
import AntTable from '@datoms/react-components/src/components/AntTable';
import AntDrawer from '@datoms/react-components/src/components/AntDrawer';
import FilterSelectWithSearch from '@datoms/react-components/src/components/FilterSelectWithSearch';
import AntSwitch from '@datoms/react-components/src/components/AntSwitch';
import SearchInput from '@datoms/react-components/src/components/SearchInput';
import AntDragger from '@datoms/react-components/src/components/AntDragger';
import AntPasswordModal from '@datoms/react-components/src/components/AntPasswordModal';
import AntRadio from '@datoms/react-components/src/components/AntRadio';
import AntRadioGroup from '@datoms/react-components/src/components/AntRadioGroup';
import AntButton from '@datoms/react-components/src/components/AntButton';
import TwinTabs from '@datoms/react-components/src/components/TwinTabs';
import NoDataComponent from '@datoms/react-components/src/components/NoDataComponent';
import PlusCircleOutlined from '@ant-design/icons/PlusCircleOutlined';
import MinusCircleOutlined from '@ant-design/icons/MinusCircleOutlined';
import EditOutlined from '@ant-design/icons/EditOutlined';
import PlusOutlined from '@ant-design/icons/PlusOutlined';
import UserAddOutlined from '@ant-design/icons/UserAddOutlined';
import SearchOutlined from '@ant-design/icons/SearchOutlined';
import CloseOutlined from '@ant-design/icons/CloseOutlined';
import CloudUploadOutlined from '@ant-design/icons/CloudUploadOutlined';
import CloudDownloadOutlined from '@ant-design/icons/CloudDownloadOutlined';
import StopOutlined from '@ant-design/icons/StopOutlined';
import CopyOutlined from '@ant-design/icons/CopyOutlined';
import {
	retriveCustomerList,
	addDevice,
	deleteDevices,
	deviceLists,
	assignDevice,
	deviceStatusUpdate,
	updateDeviceSimDetails,
	retriveVendorFirmwareList,
	retriveVendorFirmwareDetails, getSecurityToken,
} from '@datoms/js-sdk';
import { getUniqueArray } from '@datoms/js-utils/src/basic-utility';
import { isCustomerValid } from '@datoms/js-utils/src/access-functions';
import _some from 'lodash/some';
import _find from 'lodash/find';
import _isEqual from 'lodash/isEqual';
import _orderBy from 'lodash/orderBy';
import _map from 'lodash/map';
import _remove from 'lodash/remove';
import _filter from 'lodash/filter';
import _indexOf from 'lodash/indexOf';
import _sortBy from 'lodash/sortBy';
import io from 'socket.io-client';
import moment from 'moment-timezone';
import { utils as xlsxUtils, read as xlsxRead, writeFile } from 'xlsx/xlsx.mjs';
import defaultConfig from './defaultConfig';
import MobileDeviceList from '../Mobile/MobileDeviceList';
import ethernet_inactive from '../../imgs/ethernet_inactive.svg';
import sim from '../../imgs/sim.svg';
import ThingCreated from '../../imgs/ThingCreated.svg';
import third_party_orange from '../../imgs/3rd_party_orange.svg';
import upto_date from '../../imgs/up_to_date.svg';
import update from '../../imgs/update.svg';
import ethernet from '../../imgs/ethernet.svg';
import configuration from '../../imgs/configuration.svg';
import debug from '../../imgs/debug.svg';
import custom_command from '../../imgs/custom_command.svg';
import assign from '../../imgs/assign.svg';
import update_firmware from '../../imgs/update_firmware.svg';
import device_delete from '../../imgs/device_delete.svg';
import ActiveIcon from '../../imgs/active_icon.svg';
import InactiveIcon from '../../imgs/inactive_icon.svg';
import power_on from '../../imgs/power_on.svg';
import power_off from '../../imgs/power.svg';
import power_inactive from '../../imgs/power_inactive.svg';
import lighting_inactive from '../../imgs/lighting_inactive.svg';
import lighting_active from '../../imgs/lighting_active.svg';
import xlsx_icon from '../../imgs/xlsx_icon.svg';
import csv_icon from '../../imgs/csv_icon.svg';
import WifiSignalRounded from '../../imgs/wifi_signal_rounded.svg';
import WifiSignalRounded1 from '../../imgs/wifi-signal/wifi_signal_rounded_1.svg';
import WifiSignalRounded2 from '../../imgs/wifi-signal/wifi_signal_rounded_2.svg';
import WifiSignalRounded3 from '../../imgs/wifi-signal/wifi_signal_rounded_3.svg';
import GprsSignalRounded from '../../imgs/gprs_signal_rounded.svg';
import GprsSignalRounded2 from '../../imgs/gprs-signal/gprs_signal_rounded_2.svg';
import GprsSignalRounded4 from '../../imgs/gprs-signal/gprs_signal_rounded_4.svg';
import GprsSignalRounded5 from '../../imgs/gprs-signal/gprs_signal_rounded_5.svg';
import DevicePopKey from '../../imgs/device_pop_key.svg';
import CloudFirmware from '../../imgs/cloud_firmware.svg';
import raw_log_icon from '../../imgs/raw_log_icon.svg';
import SimMobileIcon from '../../imgs/sim-modal.svg';
import assignDeviceImage from '../../imgs/assign-device-img.svg';
import DeviceDebug from '../../../../webapp-component-thing-management/src/components/DeviceDebug';
import { circuitExceptionDevices } from '../imports/config.js';
import { getDeviceDetails } from '../logic.js'
import '../style.less';

const AddDevice = lazy(() => import('../imports/AddDevice'));
const AddForm = lazy(() => import('../imports/AddForm'));

import queryString from 'query-string';

export default class UnassignedDevice extends React.Component {
	constructor(props) {
		super(props);
		this.parsed = queryString.parse(props.location.search);
		this.search_value = this.parsed.search ? this.parsed.search : '';

		this.deviceDetails = {
			customer_id: this.parsed.customer_id ? this.parsed.customer_id : '',
			application_id: this.parsed.application_id
				? this.parsed.application_id
				: '',
			device_id: this.parsed.device_id ? this.parsed.device_id : '',
			device_qr_code: this.parsed.device_qr_code
				? this.parsed.device_qr_code
				: '',
		};
		this.debug_type_name = this.parsed.type_name
			? this.parsed.type_name
			: '';
		this.type = this.parsed.type ? JSON.parse(this.parsed.type) : [];
		this.project = this.parsed.project
			? JSON.parse(this.parsed.project)
			: [];
		this.type_selected = [];
		this.project_selected = [];
		if (this.type && this.type.length) {
			this.type.map((val, ind) => {
				this.type_selected.push('0-1-' + val);
			});
		}
		if (this.project && this.project.length) {
			this.project.map((val, ind) => {
				this.project_selected.push('0-1-' + val);
			});
		}
		this.getDeviceDetails = getDeviceDetails.bind(this);
		this.state = {
			value: this.selected,
			table_data: [],
			table_search:
				this.parsed_search &&
				Object.values(this.parsed_search).length &&
				this.parsed_search.search
					? this.parsed_search.search
					: '',
			filtered_city_ids: [],
			device_type_filter_ids: this.type,
			deviceTypeFilterValue: this.type_selected,
			project_type_filter_ids: this.project,
			projectTypeFilterValue: this.project_selected,
			sim_details_drawer: false,
			selected_device_sim: null,
			table_view_options: {},
			tableVisible: true,
			drawAddVisible: false,
			mobile_drawer: false,
			status: null,
			id: null,
			adding_status: null,
			show_no_data: false,
			city_id: document.getElementById('city_id')
				? document.getElementById('city_id').value
				: 1, //'all',
			city_id_exclude: ['all', 'unconfigured', 'no_data_received'],
			search_value: this.search_value,
			modal_view: false,
			modal_sync_view: false,
			sync_index: null,
			sorted: false,
			sort_keys: null,
			date_time_online: false,
			filter_keys: {
				city_id: '0',
				connection: '0',
				sync: '0',
				health: '0',
				data_sending: '0',
				unconfigured: '0', //false
				device_archive_status: 'active',
				no_data_received: '0', //false
				publicly_accessible: '0',
			},
			health_status: {
				0: 'Any',
				1: 'OK',
				2: 'Power Failure',
				3: 'Code Restart',
				4: 'Modem Restart',
				5: 'Other Error',
				6: 'Battery Voltage Down',
				7: 'Network Signal Low',
				8: 'Debug Data Not Sent',
			},
			modem_types: {
				1: 'GPRS',
				2: 'WIFI',
				3: 'Ethernet',
			},
			add_station: false,
			city_id: '',
			station_name: '',
			stations_device: '',
			station_lat: '',
			station_long: '',
			current_header_menu:
				this.props.location &&
				Object.keys(this.props.location).length &&
				this.props.location.pathname
					? this.props.location.pathname.includes('sim-management') ||
					  this.props.location.pathname.includes(
							'production-inventory'
					  )
						? 'production'
						: this.props.location.pathname.includes(
								'client-management'
						  )
						? 'client'
						: this.props.location.pathname.includes('/devices/')
						? 'device'
						: ''
					: '',
			assign_to_customer: false,
			update_firmware: false,
			selected_devices: [],
			sim_details: [],
			device_assignment_customer_id: 0,
			device_assignment_application_id: 0,
			device_updated_firmware_id: 0,
			add_device_to_customer: false,
			drawer_search_value: '',
			filtered_drawer_stations_list: [],
			selectedRowKeysChild: [],
			assign_child_data: [],
			assign_child_data_add: [],
			added_filtered_device_list: [],
			selectedRows: [],
			update_tag: false,
			update_status: false,
			deviceTypeFilter: [],
			applicationFilter: [],
			customerFilter: [],
			projectTypeFilter: [],
			data_loading: true,
			convert_to_things: false,
			selected_app_thing_types: [],
			thing_name: '',
			edit_device: false,
			bulk_add_device: false,
			fileList: [],
			download_data: [
				['Device serial no', 'Device type', 'Description'],
				['Sample-Device-1', 'Sample-Trade GPRS', 'Sample-Description'],
				['Sample-Device-2', 'Sample-Trade GPRS', 'Sample-Description'],
			],
			adding_new_device: false,
			access_key: ['DeviceManagement:DeviceManagement'],
			btn_loading: false,
			third_party_row_value: '',
			third_party_modal_visible: false,
			selected_row: [],
			selected_rows: [],
			release_firmware_list: [],
			dev_firmware_list: [],
			selected_radio_value: 'release',
			firmware_value: undefined,
			firm_application_value: undefined,
			firmware_update_modal_loading: false,
			single_update: false,
			accepted_type_ids: [1, 2, 11, 12, 51, 60],
		};
		this.device_details = null;
		this.device_types = null;
		this.projects_list = null;
		this.device_name = '';
		this.device_table_pagination_config = {
			position: ['topRight', 'bottomRight'],
			defaultPageSize: 20,
			showSizeChanger: true,
			size: 'small',
			showTotal: (total, range) =>
				`${range[0]}-${range[1]} of ${total} items`,
		};
		this.currently_viewing_device_ids = [];
		this.customerSelectionOptions = [];
		this.applicationSelectionOptions = [];
		this.vendorSelectionOptions = [];
		this.thingsSelectionOptions = [];
		this.firmwareSelectionOptions = [];
		this.platform_slug =
			this.props.location &&
			this.props.location.pathname &&
			this.props.location.pathname.includes('/datoms-x')
				? '/datoms-x'
				: this.props.location.pathname.includes('/iot-platform')
				? '/iot-platform'
				: '/datoms-x';

		// this.iotBasePath = this.props.location
		// 	? this.props.location.pathname &&
		// 	  this.props.location.pathname.includes('/datoms-x')
		// 		? '/datoms-x'
		// 		: this.props.location.pathname.includes('/iot-platform')
		// 		? '/iot-platform'
		// 		: '/datoms-x'
		// 	: '';

		/*if (import.meta.env.VITE_BUILD_MODE !== 'development') {
			let application_slug = window.location.pathname.split('/')[3];
			if (application_slug && typeof application_slug === 'string') {
				if (application_slug == 'iframe.html') {
					application_slug = 'datoms-x';
				}
				this.iotBasePath = '/' + application_slug;
			}
		} else {
			let application_slug = window.location.pathname.split('/')[1];
			if (application_slug && typeof application_slug === 'string') {
				if (application_slug == 'iframe.html') {
					application_slug = 'datoms-x';
				}
				this.iotBasePath = '/' + application_slug;
			}
		}*/

		if (this.props.app_name) {
			this.platform_slug = '/' + this.props.app_name;
		}

		this.iotBasePath = 'https://app.datoms.io';
		this.updateSimDetails = this.updateSimDetails.bind(this);
		if (
			!import.meta.env.VITE_MOBILE &&
			typeof window !== undefined &&
			!window.location.href.includes('localhost')
		) {
			this.iotBasePath =
				window.location.protocol + '//' + window.location.host;
		}
		//array comparision method
		// Warn if overriding existing method
		if (Array.prototype.equals)
			console.warn(
				"Overriding existing Array.prototype.equals. Possible causes: New API defines the method, there's a framework conflict or you've got double inclusions in your code."
			);
		// attach the .equals method to Array's prototype to call it on any array
		Array.prototype.equals = function (array) {
			// if the other array is a falsy value, return
			if (!array) return false;

			// compare lengths - can save a lot of time
			if (this.length != array.length) return false;

			for (var i = 0, l = this.length; i < l; i++) {
				// Check if we have nested arrays
				if (this[i] instanceof Array && array[i] instanceof Array) {
					// recurse into the nested arrays
					if (!this[i].equals(array[i])) return false;
				} else if (this[i] !== array[i]) {
					// Warning - two different object instances will never be equal: {x:20} != {x:20}
					return false;
				}
			}
			return true;
		};
		// Hide method from for-in loops
		Object.defineProperty(Array.prototype, 'equals', { enumerable: false });
		/*if (this.props.history.location.pathname.includes('/datoms-x')) {
			this.props.setPageType();
		}*/
		console.log('this_props_', this.props);
	}

	readCookie(key) {
		let result;
		return (result = new RegExp(
			'(?:^|; )' + encodeURIComponent(key) + '=([^;]*)'
		).exec(document.cookie))
			? result[1]
			: null;
	}

	getDeviceSyncStatus(deviceDetails) {
		if (
			deviceDetails.device_sync_status === 'not_in_sync' ||
			deviceDetails.pending_messages_found === 'true' ||
			deviceDetails.pending_messages_found === true
		) {
			if (
				deviceDetails.pending_messages_found === 'true' ||
				deviceDetails.pending_messages_found === true
			) {
				if (deviceDetails.connection_status === 'online') {
					return 'syncing';
				} else {
					return 'waiting_for_device';
				}
			} else {
				return 'not_in_sync';
			}
		} else {
			return 'in_sync';
		}
	}

	mobileDrawerClicked(e) {
		this.setState({
			mobile_drawer: e,
		});
	}

	async fetchDeviceList() {
		let that = this;
		that.setState({
			loading: true,
		});
		let response = await deviceLists('unassigned', that.props.client_id);
		if (response.status === 403) {
			that.setState({
				loading: false,
				remove_loading: true,
				unauthorised_access: true,
				unauthorised_access_msg: response.message,
			});
		} else if (response.status === 'success') {
			// console.log('device_list', response);
			let data = response,
				deviceTypeFilter = [],
				applicationFilter = [],
				customerFilter = [],
				projectTypeFilter = [];

			data.devices = _orderBy(response.devices, ['created_at'], ['desc']);

			if (data.device_types && data.device_types.length) {
				data.device_types.map((type) => {
					deviceTypeFilter.push({
						title: type.name,
						value: '0-1' + '-' + type.id,
						key: '0-1' + '-' + type.id,
					});
				});
			}

			projectTypeFilter.push(
				{
					title: 'Project - 1',
					value: '0-1-1',
					key: '0-1-1',
				},
				{
					title: 'Project - 2',
					value: '0-1-2',
					key: '0-1-2',
				},
				{
					title: 'Project - 3',
					value: '0-1-3',
					key: '0-1-3',
				}
			);

			that.firmwareSelectionOptions = data.firmwares
				.map((firmware) => {
					return (
						<AntOption value={firmware.id}>
							{firmware.version}
						</AntOption>
					);
				})
				.filter(Boolean);
			that.device_types = response.device_types;
			that.device_details = data.devices;
			that.projects_list = data.all_projects;

			that.setState(
				{
					deviceTypeFilter: deviceTypeFilter,
					projectTypeFilter: projectTypeFilter,
					device_lists: data,
					show_no_data: data.devices && data.devices.length == 0,
					device_types: response.device_types,
				},
				() => {
					that.filterTableData();
				}
			);
			// that.getCustomerListFunction();
		} else {
			that.openNotification('error', response.message);
			that.setState({
				unauthorised_access: false,
				remove_loading: true,
				error_API: true,
				remove_loading: true,
				error_API_msg: response.message,
			});
		}
	}

	async getCustomerListFunction() {
		console.log('IamCalled-1');
		let that = this;
		let response;
		if (that.platform_slug.includes('datoms-x')) {
			response = await retriveCustomerList(1, '?dealers=false');
		} else {
			response = await retriveCustomerList(that.props.client_id);
		}
		if (response.status === 403) {
			that.setState({
				loading: false,
				unauthorised_access: true,
				unauthorised_access_msg: response.message,
			});
		} else if (response.status === 'success') {
			let vendorList = [],
				customerList = [];
			if (
				that.platform_slug.includes('datoms-x') &&
				response.customers &&
				response.customers.length
			) {
				response.customers.map((customer) => {
					if (customer.is_vendor) {
						vendorList.push(customer);
					} else {
						customerList.push(customer);
					}
				});
			}
			that.setState({
				vendor_list: vendorList,
				client_list: that.platform_slug.includes('datoms-x')
					? customerList
					: response.customers,
				application_list: response.applications,
			});
			that.fetchDeviceList();
		} else {
			that.openNotification('error', response.message);
			that.setState({
				loading: false,
				error_API: true,
				error_API_msg: response.message,
			});
		}
	}

	isCustomerAllowed(customer){
		if(this.props.client_id !== 1){
			return true;
		}
		if(customer.is_vendor && customer.vendor_id !== 1){
			return false;
		}
		if(!customer.is_vendor && customer.parent_vendor_id){
			return false
		}
		return true;
	}

	async getCustomerListFunction() {
		console.log('IamCalled-2');
		let that = this;
		this.setState({
			loading: true,
		});
		let response = await retriveCustomerList(this.props.client_id);
		if (response.status === 403) {
			that.setState({
				loading: false,
				unauthorised_access: true,
				unauthorised_access_msg: response.message,
			});
		} else if (response.status === 'success') {
			// console.log('customers_list', response);

			let customerFilter = [],
				applicationFilter = [],
				custAppAccess = [];
			const vendorList = this.props.enabled_features?.includes(
				'IndustryManagement:Dealers'
			) ? [{id: this.props.client_id, name: this.props.client_name}] : [];

			if (response.customers && response.customers.length) {
				/*this.customerSelectionOptions =*/ response.customers
					.map((client) => {
						console.log('validCus: ',isCustomerValid(client.status))
						if(isCustomerValid(client.status) && this.isCustomerAllowed(client)){
							this.customerSelectionOptions.push(
								<AntOption value={client.id}>
									{client.name}
								</AntOption>
							);
						}
					})
				//	.filter(Boolean);
				response.customers.map((client) => {
					if (client.is_vendor) {
						vendorList.push({
							id: client.id,
							name: client.name,
						});
					}

					custAppAccess.push({
						id: client.id,
						applications: client.customer_type.includes(5) ? getUniqueArray(client.applications, client.access_applications) : client.customer_type.includes(4) ? getUniqueArray(client.access_applications) : getUniqueArray(client.applications)
					});

					customerFilter.push({
						title: client.name,
						value: '0-1' + '-' + client.id,
						key: '0-1' + '-' + client.id,
					});
				});
			}

			if (response.applications && response.applications.length) {
				response.applications.map((app) => {
					applicationFilter.push({
						title: app.name,
						value: '0-1' + '-' + app.id,
						key: '0-1' + '-' + app.id,
					});
				});
			}

			this.setState(
				{
					customerFilter: customerFilter,
					applicationFilter: applicationFilter,
					cust_app_access: custAppAccess,
					app_lists: response.applications,
					vendor_list: vendorList,//response.vendor_list,
					customer_list: response.customers,
					data_loading: false,
					remove_loading: true,
				},
				() => {
					that.getFilteredStations(that.state.search_value);
				}
			);
		} else {
			that.openNotification('error', response.message);
			that.setState({
				unauthorised_access: false,
				loading: true,
				error_API: true,
				error_API_msg: response.message,
			});
		}
	}

	/**
	 * This method is called right after when an instance of a component is being created and inserted into the DOM.
	 */
	componentDidMount() {
		// document.title = 'Device Management - Datoms IOT Platform';
		if (!this.props.location.pathname.includes('/unassigned')) {
			this.props.history.push(this.platform_slug + '/devices/unassigned');
		}
		//fetch devices list from REST API
		this.fetchDeviceList();
		this.getCustomerListFunction();
		/**
		 * Socket is used to sync the data between client and server in real time.
		 */
		this.socket = io(import.meta.env.VITE_IOT_PLATFORM_SOCKET_BASEPATH, {
			query: {
				s_token: getSecurityToken(),
				// s_token: 'ufoiel0jdkm3il44valqj6r1j0'
			},
		});
		this.socket.on('connect', () => {
			this.socket.emit('connect_dashboard_to_socket');
			// console.log('connected to socket');
		});

		this.socket.on('dashboard_successfully_connected', () => {
			/*this.socket.emit('get_list_of_devices', JSON.stringify({
				// city_id: this.state.city_id
				city_id: 5
			}));*/
			if (this.currently_viewing_device_ids.length) {
				this.subscribeForDeviceUpdates(true);
			}
			// console.log('dashboard successfully connected');
		});

		this.socket.on('device_firmware_updated', () => {
			// console.log('device_firmware_updated');
			this.openNotification(
				'success',
				'Devices firmware update queued successfully.'
			);
		});

		/*this.socket.on('update_list_of_devices', (payload) => {
			let data = JSON.parse(payload);
			let child_data = [];
			if (data.cities && Object.keys(data.cities).length) {
				Object.keys(data.cities).map((ct_id) => {
					child_data.push({
						'title': data.cities[ct_id],
						'value': '0-1' + '-' + ct_id,
						'key': '0-1' + '-' + ct_id,
					});
				});
			}
			userFilter.push({
				title: 'City',
				value: '0-1',
				key: '0-1',
				children: child_data
			});
			console.log('unassigned_devices', data);
			this.device_details = data.devices;
			this.setState({
				device_cities: data.cities,
				unassigned_devices: data.unassigned_devices
			}, () => {
				this.getFilteredStations(this.state.search_value)
				console.log('update_list_of_devices', this.device_details);
				console.log('unassigned_devices', this.state.unassigned_devices);
			});
		});*/

		// this.socket.on('update_device_details', (payload) => {
		// 	let device_data = JSON.parse(payload);
		// 	let deviceData = this.device_details;
		// 	if (deviceData && deviceData.length) {
		// 		this.device_details.map((data, index) => {
		// 			if (data.id == parseInt(device_data.device_id)) {
		// 				/*if (device_data.connection_status) {
		// 					deviceData[index].status_code =
		// 						device_data.connection_status == 'online'
		// 							? 1
		// 							: 0;
		// 				}*/
		// 				if (parseInt(device_data.last_data_receive_time)) {
		// 					deviceData[index].last_data_receive_time = parseInt(
		// 						device_data.last_data_receive_time
		// 					);
		// 				}
		// 				// if (data.id == '4445') {
		// 				// 	console.log('update_device_details_ device_data', device_data);
		// 				// 	console.log('update_device_details_ deviceData', deviceData);
		// 				// }
		// 			}
		// 		});
		// 	}
		// 	this.device_details = deviceData;
		// 	this.filterTableData();
		// });

		this.socket.on('update_location_details_in_dashboard', (payload) => {
			let data_status = JSON.parse(payload);
			if (this.device_details) {
				let status_change = this.device_details;
				status_change.map((stat, index) => {
					if (stat && data_status.device_id == stat.id) {
						Object.keys(data_status).map((key) => {
							if (key != 'device_id') {
								status_change[index][key] = data_status[key];
							}
						});
					}
				});
				this.device_details = status_change;
				this.updateFilter();
				// this.setState({device_details: status_change}, () => this.updateFilter());
			}
			// console.log('update_location_details_in_dashboard', this.device_details);
			// console.log('Dashboard successfully connected to socket.');
			// console.log('payload', data_status);
		});

		this.socket.on('new_station_added_successfully', (payload) => {
			let add_new_device = JSON.parse(payload);
			let device_details = this.device_details;
			device_details.push(add_new_device);
			this.device_details = device_details;
			this.filterTableData();
			/*this.setState({
				device_details: device_details
			}, () => this.getFilteredStations(this.state.search_value));*/
			// console.log('new_station_added_successfully', add_new_device);
			// showPopup('success', 'New device added successfully!');
		});
		this.socket.on('unsubscribe_from_devices_successful', (payload) => {
			// console.log('unsubscribe successful');
			this.socket.emit(
				'subscribe_to_devices',
				JSON.stringify({
					device_ids: this.currently_viewing_device_ids,
				})
			);
		});
		this.socket.on('subscribe_to_devices_successful', (payload) => {
			// console.log('subscribe successful');
		});
		// if ((this.city_name === 'admin') || (this.city_name === '127')) {
		// } else {
		// 	document.title = '404 Not Found - Aurassure';
		// }
	}

	componentDidUpdate(prevProps, prevState, screenshots) {
		// console.log('componentDidUpdate prevProps --> ', prevProps);
		// console.log('componentDidUpdate this.props --> ', this.props);
		// console.log('componentDidUpdate this.state --> ', this.state);
		// console.log('componentDidUpdate prevState --> ', prevState);
		// console.log('componentDidUpdate screenshots --> ', screenshots);

		if (
			prevState.current_header_menu == '' &&
			this.props.location &&
			Object.keys(this.props.location).length &&
			this.props.location.pathname &&
			(this.props.location.pathname.includes('sim-management') ||
				this.props.location.pathname.includes('production-inventory') ||
				this.props.location.pathname.includes('client-management') ||
				this.props.location.pathname.includes('/devices/'))
		) {
			this.setState({
				current_header_menu:
					this.props.location &&
					Object.keys(this.props.location).length &&
					this.props.location.pathname
						? this.props.location.pathname.includes(
								'sim-management'
						  ) ||
						  this.props.location.pathname.includes(
								'production-inventory'
						  )
							? 'production'
							: this.props.location.pathname.includes(
									'client-management'
							  )
							? 'client'
							: this.props.location.pathname.includes('/devices/')
							? 'device'
							: ''
						: '',
			});
		}
	}

	changeGroup(key /*, update = false*/) {
		// console.log('in changeGroup', key);
		/*console.log('in changeGroup',update);*/
		this.props.history.push(this.platform_slug + '/devices/' + key);
	}

	toggleShowOnlineDevices() {
		this.setState({ date_time_online: !this.state.date_time_online }, () =>
			this.updateFilter()
		);
	}

	/**
	 * This function calls the notification alert.
	 * @param  {String}	type
	 * @param  {String}	msg
	 * @return {void}
	 */
	openNotification(type, msg) {
		if (window.innerWidth < 576) {
			AntMessage(type, msg);
		} else {
			AntNotification({
				type: type,
				message: msg,
				// description: 'This is success notification',
				placement: 'bottomLeft',
				className: 'alert-' + type,
			});
		}
	}

	/**
	 * This Perform any necessary cleanup in this method, such as invalidating timers, canceling network requests, or cleaning up any DOM elements that were created in componentDidMount.
	 */
	componentWillUnmount() {
		if (this.socket !== null || (this.socket && this.socket.connected)) {
			this.socket.close();
			// console.log('socket disconnected');
		}
	}

	updateFilter(options) {
		if (options) {
			this.setState({ filter_keys: options }, () =>
				this.filterTableData()
			);
		} else {
			this.filterTableData();
		}
		// console.log('options', options);
	}

	getFilteredStations(search_value) {
		// console.log('getFilteredStations');
		if (
			this.state.filtered_table_data &&
			this.state.filtered_table_data.length
		) {
			let device_list = [],
				current_time = moment().unix(),
				filtered_stations_list = [];
			this.state.filtered_table_data.map((config, index) => {
				device_list.push(config);
			});
			// console.log('Device List', device_list);
			// console.log(this.state);
			this.state.filtered_table_data.map((config, index) => {
				// Connection Status
				if (
					_some(device_list, config) &&
					!(
						this.state.filter_keys.connection === '0' ||
						config.status === this.state.filter_keys.connection ||
						(config.status === 'data_off' &&
							this.state.filter_keys.connection === 'online')
					)
				) {
					device_list.splice(device_list.indexOf(config), 1);
				}
				// Only online devices
				if (this.state.date_time_online) {
					let less_fifteen_offline =
							current_time - config.last_data_receive_time,
						fifteen_minute = 900;
					if (
						_some(device_list, config) &&
						less_fifteen_offline >= fifteen_minute
					) {
						device_list.splice(device_list.indexOf(config), 1);
					}
					// console.log('date_time_online_device_list', device_list);
				}
				// Text Search
				let client_det = _find(this.state.device_lists.clients, {
					id: config.client_id,
				});
				// console.log('getFilteredStations client_det_', config);
				let data_string = config.qr_code.toLowerCase(); /* +
					' ' +
					config.name +
					' ' +
					(client_det ? client_det.name : '')*/
				// console.log('Search', data_string.toLowerCase());
				if (
					_some(device_list, config) &&
					!(
						search_value.toLowerCase() == '' ||
						data_string
							.toLowerCase()
							.search(search_value.toLowerCase()) > -1
					)
				) {
					device_list.splice(device_list.indexOf(config), 1);
				}
				//filter by Device Type
				if (
					_some(device_list, config) &&
					!(
						this.state.device_type_filter_ids.length == 0 ||
						this.state.device_type_filter_ids.indexOf(
							config.type_id
						) > -1
					)
				) {
					device_list.splice(device_list.indexOf(config), 1);
				}

				//filter by Project
				// if(
				// 	_some(device_list, config) &&
				// 	!(
				// 		this.state.project_type_filter_ids.length == 0 ||
				// 		this.state.project_type_filter_ids.indexOf(config.type_id) > -1
				// 	)
				// ) {
				// 	device_list.splice(device_list.indexOf(config), 1);
				// }
			});

			filtered_stations_list = device_list.filter(Boolean);

			// console.log('Filtered Devices:', filtered_stations_list);
			let table_data = [];
			if (filtered_stations_list && filtered_stations_list.length) {
				filtered_stations_list.map((st, index) => {
					let device_type_data = _find(this.device_types, {
						id: st.type_id,
					});
					let project_data = _find(this.projects_list, {
						id: st.project_id,
					});
					let type_det = _find(
						this.state.device_lists.device_types,
						{ id: st.type_id }
					);
					let client_det = _find(this.state.device_lists.clients, {
						id: st.client_id,
					});

					let app_det = _find(this.state.application_list, {
						id: st.application_id,
					});

					/*let connectivityStatus = 'offline';
					if (st.last_data_receive_time) {
						let current_time = moment().unix(),
							less_fifteen_online =
								current_time - st.last_data_receive_time,
							fifteen_minute = 900;
						if (less_fifteen_online <= fifteen_minute) {
							connectivityStatus = 'online';
						}
					}*/

					table_data.push({
						key: st.id,
						id: st.id,
						qr: st.qr_code,
						qr_code: st.qr_code,
						created_at: st.created_at,
						station_id: st.station_id,
						station: st.name,
						name: st.name,
						customer_id: st.client_id,
						client_id: st.client_id,
						customer: client_det ? client_det.name : '',
						application_id: st.application_id,
						application: app_det ? app_det.name : '',
						// city: this.state.device_cities[st.city_id],
						active: st.last_data_receive_time,
						sync: this.getDeviceSyncStatus(st),
						connect: st.device_modem_type,
						device_modem_type: st.device_modem_type,
						health: this.state.health_status[
							st.device_error_status
						],
						device_error_status: st.device_error_status,
						date: moment
							.unix(st.last_data_receive_time)
							.tz('Asia/Kolkata')
							.format('HH:mm, DD MMM'),
						timestamp: st.last_data_receive_time,
						network: st.network,
						percent: st.online_percentage,
						online_percentage: st.online_percentage,
						availability: st.data_availability,
						data_availability: st.data_availability,
						tags: st.tags,
						vendor_id: st.vendor_id,
						is_editable: st.is_editable,
						vendor_name: st.vendor_name,
						device_auth_token: st.device_auth_token,
						device_key:
							st.device_auth_token && st.device_auth_token !== ''
								? st.device_auth_token
								: 'NA',
						type_id: st.type_id,
						type_name: type_det ? type_det.name : '',
						device_config: st,
						sim_details: st.sim_details,
						firmware_version: st.firmware_version
							? st.firmware_version
							: '',
						firmware_update_time: st.firmware_update_time
							? st.firmware_update_time
							: 0,
						firmware_update_request_time: st.firmware_update_request_time
							? st.firmware_update_request_time
							: 0,
						description: st.description,
						last_data_receive_time: st.last_data_receive_time,
						status_code: st.status_code,
						device_sim_slot: st.device_sim_slot
							? st.device_sim_slot
							: 0,
						device_error_list: st.device_error_list
							? st.device_error_list
							: [],
						device_charging_status: st.device_charging_status
							? st.device_charging_status
							: null,
						device_battery_percent: st.device_battery_percent
							? st.device_battery_percent
							: null,
						device_power_status: st.device_power_status
							? st.device_power_status
							: null,
						device_battery_status: st.device_battery_status
							? st.device_battery_status
							: null,
						device_signal_strength: st.device_signal_strength
							? st.device_signal_strength
							: null,
						device_status:
							st.status_code && st.status_code !== 7
								? 'active'
								: 'inactive',
						connectivity_status:
							st.status_code && st.status_code === 1
								? 'online'
								: 'offline',
						device_auth_protocols: st.device_auth_protocols
							? st.device_auth_protocols
							: {},
						product_model: st.product_model, //firmware-new-changes
						circuit_version: st.circuit_version, //firmware-new-changes
					});
				});
			}

			let selected_row_data = {};
			if (this.props.location.pathname.includes('/edit')) {
				let found_row = _find(table_data, {
					id: this.props.match.params.device_id,
				});
				if (found_row) {
					selected_row_data = found_row;
				}
			}

			this.filtered_stations = filtered_stations_list;
			this.setState(
				{
					search_value: search_value,
					table_data: table_data,
					selected_devices: table_data.length
						? this.state.selected_devices
						: undefined,
					selected_rows: table_data.length
						? this.state.selected_rows
						: undefined,
					table_search: search_value,
					selected_row_data: selected_row_data,
					remove_loading: true,
					edit_device: this.props.location.pathname.includes('/edit')
						? true
						: false,
				},
				() => {
					// console.log(
					// 	'selected_row_data1',
					// 	this.state.selected_row_data
					// );
					if (this.props.location.pathname.includes('/edit')) {
						// this.openEditDrawer(this.state.selected_row_data);
					}
					this.subscribeForDeviceUpdates();
				}
			);
			// console.log('Filtered Devices:', device_list.filter(Boolean));
			// return device_list.filter(Boolean);
		} else {
			this.setState({
				search_value: search_value,
				table_data: [],
				selected_devices: undefined,
				selected_rows: undefined,
				table_search: search_value,
				remove_loading: true,
			});
		}
	}

	drawerSearchResults(value) {
		// console.log('Search inserted');
		let assign_child_data = [];
		let device_list = [],
			filtered_stations_list = [];
		if (this.device_details && this.device_details.length) {
			this.device_details.map((config, index) => {
				device_list.push(config);
			});

			if (value) {
				value = value.toLowerCase();
			}

			this.device_details.map((config, index) => {
				// Text Search
				let data_string = config.qr;
				// console.log('Search', data_string.toLowerCase());
				if (
					_some(device_list, config) &&
					!(
						value == '' ||
						data_string.toLowerCase().search(value) > -1
					)
				) {
					device_list.splice(device_list.indexOf(config), 1);
				}
			});

			filtered_stations_list = device_list.filter(Boolean);

			if (filtered_stations_list && filtered_stations_list.length) {
				filtered_stations_list.map((device, ind) => {
					// console.log('deviceeeee', device);
					let device_type_data = _find(this.device_types, {
						id: device.type_id,
					});
					let project_data = _find(this.projects_list, {
						id: device.project_id,
					});
					assign_child_data.push({
						key: device.id,
						id: device.id,
						qr_code: device.qr_code,
						device_type: device_type_data
							? device_type_data.name
							: '-',
						project: project_data ? project_data.name : '-',
						device_config: device,
					});
				});
			}
			// console.log('filtered_stations_list', filtered_stations_list);
		}
		this.setState({
			drawer_search_value: value,
			filtered_drawer_stations_list: filtered_stations_list,
			assign_child_data: assign_child_data,
		});
	}

	handleTableDataChange(pagination, filters, sorter, extra) {
		// console.log('pagination -> ', pagination);
		// console.log('filters -> ', filters);
		// console.log('sorter -> ', sorter);
		// console.log('extra -> ', extra);
		this.setState(
			{
				table_view_options: {
					pagination: pagination,
					filters: filters,
					sorter: sorter,
					extra: extra,
				},
			},
			() => this.subscribeForDeviceUpdates()
		);
	}

	subscribeForDeviceUpdates(forceSubscribe) {
		// console.log('socket_ this.filtered_stations', this.filtered_stations);
		// console.log('socket_ subscribeForDeviceUpdates is called');
		let filtered_stations = this.filtered_stations;
		//convert stations names & city to lowercase for better comparision & sorting
		filtered_stations.map((station, i) => {
			filtered_stations[i].station =
				station.station && station.station.length
					? station.station[0].toLowerCase()
					: [];
			filtered_stations[i].qr = station.qr_code.toLowerCase();
		});

		//remove archived stations
		let sorting_config = this.state.table_view_options.sorter,
			pagination_config = this.state.table_view_options.pagination,
			view_start_index,
			view_end_index;
		if (sorting_config !== undefined && !_isEqual(sorting_config, {})) {
			let sort_order = 'asc';
			if (sorting_config.order === 'descend') {
				sort_order = 'desc';
			}
			if (sorting_config.columnKey === 'station') {
				filtered_stations = _orderBy(
					filtered_stations,
					['name'],
					[sort_order]
				);
			}
			if (sorting_config.columnKey === 'qr') {
				filtered_stations = _orderBy(
					filtered_stations,
					['qr_code'],
					[sort_order]
				);
			}
			if (sorting_config.columnKey === 'active') {
				filtered_stations = _orderBy(
					filtered_stations,
					['last_data_receive_time'],
					[sort_order]
				);
			}
			if (sorting_config.columnKey === 'percent') {
				filtered_stations = _orderBy(
					filtered_stations,
					['online_percentage'],
					[sort_order]
				);
			}
			if (sorting_config.columnKey === 'availability') {
				filtered_stations = _orderBy(
					filtered_stations,
					['data_availability'],
					[sort_order]
				);
			}
		}
		if (
			pagination_config !== undefined &&
			!_isEqual(pagination_config, {})
		) {
			view_start_index =
				(pagination_config.current - 1) * pagination_config.pageSize;
			if (view_start_index > filtered_stations.length) {
				view_start_index =
					filtered_stations.length -
					(filtered_stations.length % pagination_config.pageSize);
			}
			view_end_index = view_start_index + pagination_config.pageSize;
		} else {
			//default pagination options
			view_start_index = 0;
			view_end_index =
				view_start_index +
				this.device_table_pagination_config.defaultPageSize;
		}
		// console.log('socket_ filtered_stations', filtered_stations);
		// console.log('socket_ view_start_index', view_start_index);
		// console.log('socket_ view_end_index', view_end_index);
		// console.log('socket_ pagination_config', pagination_config);
		filtered_stations = filtered_stations.slice(
			view_start_index,
			view_end_index
		);
		// console.log('socket_ filtered_stations 1', filtered_stations);

		let currently_viewing_device_ids = _map(
			filtered_stations,
			(n) => n.id
		);

		// console.log(
		// 	'socket_ currently_viewing_device_ids',
		// 	currently_viewing_device_ids
		// );
		// console.log(
		// 	'socket_ this.currently_viewing_device_ids',
		// 	this.currently_viewing_device_ids
		// );
		//subscribe only if any changes in the device list is observed from the last one, otherwise this continuosly emits due to any changes in the state
		if (
			forceSubscribe ||
			!currently_viewing_device_ids.equals(
				this.currently_viewing_device_ids
			)
		) {
			// console.log('socket_ called unsubscribe');

			this.socket.emit(
				'unsubscribe_from_devices',
				JSON.stringify({
					device_ids: this.currently_viewing_device_ids,
				})
			);
			this.currently_viewing_device_ids = currently_viewing_device_ids;
		}
		// console.log('filtered_stations after -> ', filtered_stations);
		// console.log(
		// 	'currently_viewing_device_ids -> ',
		// 	currently_viewing_device_ids
		// );
	}
	searchBoxFilter(value) {
		let that = this;
		if (value && value !== '') {
			that.getFilteredStations(value);
			// let parsed_url = queryString.parse(that.props.location.search);
			that.parseURL('search', value);
		} else {
			that.setState(
				{
					table_search: '',
					search_value: '',
				},
				() => {
					that.parseURL('search', '');
					that.getFilteredStations('');
				}
			);
		}
	}

	parseURL(filter, parsed) {
		// console.log('entered to parse', filter);
		// console.log('entered to parse_2', parsed);
		let parsed_url = queryString.parse(this.props.location.search);
		if (filter == 'type') {
			// console.log('entered to parse_114');
			if (parsed != '') {
				parsed_url['type'] = '[' + parsed + ']';
			} else {
				delete parsed_url['type'];
			}
		}

		if (filter == 'project') {
			// console.log('entered to parse_112');
			if (parsed != '') {
				parsed_url['project'] = '[' + parsed + ']';
			} else {
				delete parsed_url['project'];
			}
		}
		if (filter == 'search') {
			// console.log('entered to parse_111');
			if (parsed != '') {
				parsed_url['search'] = parsed;
			} else {
				delete parsed_url['search'];
			}
		}
		let query_url = '';
		if (
			parsed_url['type'] &&
			parsed_url['project'] &&
			parsed_url['search']
		) {
			// console.log('entered to parse_3');
			query_url =
				'type=' +
				parsed_url['type'] +
				'&' +
				'project=' +
				parsed_url['project'] +
				'&' +
				'search=' +
				parsed_url['search'];
		} else if (parsed_url['type'] && parsed_url['project']) {
			// console.log('entered to parse_4');
			query_url =
				'type=' +
				parsed_url['type'] +
				'&' +
				'project=' +
				parsed_url['project'];
		} else if (parsed_url['project'] && parsed_url['search']) {
			// console.log('entered to parse_5');
			query_url =
				'project=' +
				parsed_url['project'] +
				'&' +
				'search=' +
				parsed_url['search'];
		} else if (parsed_url['type'] && parsed_url['search']) {
			// console.log('entered to parse_6');
			query_url =
				'type=' +
				parsed_url['type'] +
				'&' +
				'search=' +
				parsed_url['search'];
		} else if (parsed_url['type']) {
			// console.log('entered to parse_7');
			query_url = 'type=' + parsed_url['type'];
		} else if (parsed_url['project']) {
			// console.log('entered to parse_8');
			query_url = 'project=' + parsed_url['project'];
		} else if (parsed_url['search']) {
			// console.log('entered to parse_17');
			query_url = 'search=' + parsed_url['search'];
		}
		this.props.history.push(
			this.platform_slug + '/devices/unassigned?' + query_url
		);
	}

	onChangeFilter(value) {
		this.setState({ value });
	}

	closeAddStationModal(e) {
		// console.log(e);
		this.setState({
			add_station: false,
			city_id: '',
			station_name: '',
			stations_device: '',
			station_lat: '',
			station_long: '',
		});
	}

	openAddModal() {
		this.setState({
			add_station: true,
		});
	}

	changeCity(e) {
		this.setState({
			city_id: e,
		});
	}

	changeStnName(e) {
		this.setState({
			station_name: e,
		});
	}

	changeDevice(e) {
		this.setState({
			stations_device: e,
		});
	}

	changeLat(e) {
		this.setState({
			station_lat: e,
		});
	}

	changeLong(e) {
		this.setState({
			station_long: e,
		});
	}

	addNewDevice() {
		this.setState({ add_station: true });
		// console.log(
		// 	'aaavvvccc',
		// 	JSON.stringify({
		// 		city_id: this.state.city_id,
		// 		name: this.state.station_name,
		// 		device_id: this.state.stations_device,
		// 		latitude: this.state.station_lat,
		// 		longitude: this.state.station_long,
		// 	})
		// );
		this.closeAddStationModal();
	}

	applyDeviceTypeFilter(val, label, extra) {
		let device_type_filter_ids = [];
		if (val && val.length) {
			val.map((data_key) => {
				device_type_filter_ids.push(parseInt(data_key.split('-')[2]));
			});
		}
		this.setState(
			{
				deviceTypeFilterValue: val,
				device_type_filter_ids: device_type_filter_ids,
			},
			() => {
				this.parseURL('type', device_type_filter_ids);
				this.filterTableData();
			}
		);
	}

	applyProjectTypeFilter(val, label, extra) {
		let project_type_filter_ids = [];
		if (val && val.length) {
			val.map((data_key) => {
				project_type_filter_ids.push(parseInt(data_key.split('-')[2]));
			});
		}
		this.setState(
			{
				projectTypeFilterValue: val,
				project_type_filter_ids: project_type_filter_ids,
			},
			() => {
				this.parseURL('project', project_type_filter_ids);
				this.filterTableData();
			}
		);
	}

	applyDeviceTypeFilterChild(val, label, extra) {
		let device_type_filter_ids_child = [];
		if (val && val.length) {
			val.map((data_key) => {
				device_type_filter_ids_child.push(
					parseInt(data_key.split('-')[2])
				);
			});
		}
		this.setState(
			{
				deviceTypeFilterValueChild: val,
				device_type_filter_ids_child: device_type_filter_ids_child,
			},
			() => {
				// this.parseURL('type', device_type_filter_ids_child);
				// this.getFilteredStations(this.state.search_value)
			}
		);
	}

	applyProjectTypeFilterChild(val, label, extra) {
		let project_type_filter_ids_child = [];
		if (val && val.length) {
			val.map((data_key) => {
				project_type_filter_ids_child.push(
					parseInt(data_key.split('-')[2])
				);
			});
		}
		this.setState(
			{
				projectTypeFilterValueChild: val,
				project_type_filter_ids_child: project_type_filter_ids_child,
			},
			() => {
				// this.parseURL('project', project_type_filter_ids_child);
				// this.getFilteredStations(this.state.search_value)
			}
		);
	}

	assignToCustomer(mobile_details, device) {
		let selectedDevices = [],
			filteredDevices = [];
		this.setState({
			mobile_details: mobile_details,
			assign_to_customer: true,
		});
		if (mobile_details) {
			selectedDevices.push(parseInt(device.id));
			filteredDevices.push(device);
			this.setState({
				mobile_device_selected: device,
				selected_devices: selectedDevices,
				selected_rows: filteredDevices,
			});
		}
	}

	closeAssignDevicesToCustomer() {
		this.setState({
			assign_to_customer: false,
			selected_vendor: undefined,
			device_assignment_application_id: undefined,
			selected_app_thing_types: [],
			selected_thing_type: undefined,
			thing_name: undefined,
		});
	}

	async handleAssignDevicesToCustomer() {
		let that = this;
		if (this.state.selected_devices && this.state.selected_devices.length) {
			let deviceLists = [];
			// console.log('selectedRows_', this.state.selected_rows);
			this.state.selected_rows.map((row) => {
				deviceLists.push({
					id: row.id,
					serial_no: row.qr,
				});
			});

			this.setState({
				loading: true,
				btn_loading: true,
				remove_loading: false,
			});

			let data = {
				device_ids: deviceLists,
				customer_id: this.state.device_assignment_customer_id,
				application_id: this.state.device_assignment_application_id,
				convert_device_to_things:
					this.props.client_id == 392
						? true
						: this.state.convert_to_things,
			};

			if (this.state.selected_vendor && !this.state.device_assignment_customer_type.includes(4)) {
				data['device_vendor_id'] = this.state.selected_vendor;
			} else {
				data['device_vendor_id'] = this.state.device_assignment_customer_id;
			}
			let response = await assignDevice(data, this.props.client_id);
			if (response.status === 403) {
				that.setState({
					loading: false,
					mobile_drawer: false,
					unauthorised_access: true,
					unauthorised_access_msg: response.message,
				});
			} else if (response.status === 'success') {
				_remove(that.device_details, (device) => {
					return that.state.selected_devices.includes(device.id);
				});

				that.setState(
					{
						selectedRows: undefined,
						selectedRowKeys: undefined,
						btn_loading: false,
						selected_devices: undefined,
						selected_vendor: undefined,
						mobile_drawer: false,
					},
					() => {
						//close modal
						that.closeAssignDevicesToCustomer();
						// close drawer
						that.closeAssigntoCustomerDrawer();
						//show success response
						that.openNotification(
							'success',
							'Devices assigned successfully.'
						);
						that.fetchDeviceList();
						// that.getFilteredStations(that.state.search_value);
						that.drawerSearchResults('');
						//reset selected devices in state
					}
				);
			} else {
				that.openNotification('error', response.message);
				that.setState({
					unauthorised_access: false,
					loading: true,
					mobile_drawer: false,
					btn_loading: false,
					error_API: true,
					remove_loading: true,
					error_API_msg: response.message,
				});
			}
		}
	}

	updateFirmware() {
		this.setState(
			{
				update_firmware: true,
				firmware_update_modal_loading:
					this.state.device_lists.applications &&
					this.state.device_lists.applications.length == 1
						? true
						: false,
			},
			() => {
				if (
					this.state.device_lists.applications &&
					this.state.device_lists.applications.length == 1
				) {
					if (this.state.single_update) {
						if (this.state.selected_row.length) {
							this.getFirmwareList(this.state.selected_row[0]);
						}
					} else {
						if (
							this.state.selected_rows &&
							this.state.selected_rows.length
						) {
							this.getFirmwareList(this.state.selected_rows[0]);
						}
					}
				}
			}
		);
	}

	updateStatus() {
		this.setState({ update_status: true });
	}

	updateTag() {
		this.setState({ update_tag: true });
	}

	closeupdateFirmware() {
		this.setState({
			single_update: false,
			update_firmware: false,
			selected_radio_value: 'release',
			firmware_value: undefined,
			firm_application_value: undefined,
			selected_version_features: [],
		});
	}

	updateDeviceFirmware() {
		if (this.validateFirmwareFields()) {
			console.log('validate_firmware', this.state.firmware_value);
			this.socket.emit(
				'update_device_firmware',
				JSON.stringify({
					firmware_id: this.state.device_updated_firmware_id,
					device_ids: this.state.single_update
						? this.state.selected_device
						: this.state.selected_devices,
				})
			);
			this.setState({
				update_firmware: false,
				single_update: false,
				selected_radio_value: 'release',
				firmware_value: undefined,
				firm_application_value: undefined,
			});
		}
	}

	selectClient(value) {
		let appLists = [],
			foundCust,
			custType = [];

		if (this.state.cust_app_access && this.state.cust_app_access.length) {
			foundCust = _find(this.state.cust_app_access, {
				id: parseInt(value),
			});
			if (foundCust) {
				if (foundCust.applications && foundCust.applications.length) {
					if (this.state.app_lists && this.state.app_lists.length) {
						foundCust.applications.map((app) => {
							let foundApp = _find(this.state.app_lists, {
								id: parseInt(app),
							});
							if (foundApp) {
								appLists.push({
									id: foundApp.id,
									name: foundApp.name,
								});
							}
						});
					}
				}
			}
		}
		let selectedCust = _find(this.state.customer_list, {
			id: parseInt(value),
		});
		if (selectedCust && selectedCust.customer_type) {
			custType = selectedCust.customer_type;
		}

		let vendorSelectionOptions = [],
			vendor_id;
		if (selectedCust && selectedCust.vendor_id) {
			vendor_id = selectedCust.vendor_id;
			this.state.vendor_list.forEach((item) => {
				if (item.id == 1 || item.id == this.props.client_id || item.id == selectedCust.vendor_id) {
					vendorSelectionOptions.push({
						id: item.id,
						name: item.name,
					});
				}
			});
		}
		this.vendorSelectionOptions = vendorSelectionOptions.map((vendor) => {
			return <AntOption value={vendor.id}>{vendor.name}</AntOption>;
		});
		if (appLists.length) {
			this.applicationSelectionOptions = appLists
				.map((application) => {
					return (
						<AntOption value={application.id}>
							{application.name}
						</AntOption>
					);
				})
				.filter(Boolean);
		} else {
			this.applicationSelectionOptions = [];
		}
		let stateUpdate = {
			device_assignment_customer_id: value,
			device_assignment_application_id: null,
			device_assignment_customer_type: custType,
		};
		stateUpdate['selected_vendor'] = vendor_id;
		this.setState(stateUpdate, () => {
			if (this.applicationSelectionOptions.length == 1) {
				this.selectApplication(appLists[0].id);
			}
		});
	}

	onSearchClient(val) {
		// console.log('search:', val);
	}

	selectApplication(value) {
		let selectedApplicationThingTypes = [];
		if (
			this.state.application_lists &&
			this.state.application_lists.length
		) {
			let found_application = _find(this.state.application_lists, {
				id: value,
			});
			if (found_application) {
				if (
					found_application.categories &&
					found_application.categories.length
				) {
					if (
						this.state.things_categories &&
						this.state.things_categories.length
					) {
						this.state.things_categories.map((thing) => {
							if (
								found_application.categories.includes(thing.id)
							) {
								selectedApplicationThingTypes.push({
									id: thing.id,
									name: thing.name,
								});
							}
						});
					}
				}
			}
		}
		this.setState({
			device_assignment_application_id: value,
			selected_app_thing_types: selectedApplicationThingTypes,
			selected_thing_type: undefined,
			thing_name: undefined,
			convert_to_things:
				this.props.client_id == 392
					? true
					: this.state.convert_to_things,
		});
	}

	selectVendor(value) {
		this.setState({ selected_vendor: value });
	}

	onSearchApplication(val) {
		// console.log('search:', val);
	}

	selectFirmware(value) {
		this.setState(
			{
				selected_version_features: ['...'],
				firmware_value: value,
				device_updated_firmware_id: value,
			},
			async () => {
				let response = await retriveVendorFirmwareDetails(this.props.client_id, value);
				if (response.status === 'success') {
					this.setState({
						selected_version_features:
							response.release_log.features,
					});
				} else {
					this.setState({
						selected_version_features: [],
					});
				}
			}
		);
	}

	onSearchFirmware(val) {
		// console.log('search:', val);
	}

	openAssigntoCustomerDrawer() {
		let that = this;
		let assign_child_data = [];
		// console.log('that.device_details', that.device_details);
		if (that.device_details && that.device_details.length) {
			that.device_details.map((device, ind) => {
				// console.log('device_details', device);
				let device_type_data = _find(that.device_types, {
					id: device.type_id,
				});
				let project_data = _find(that.projects_list, {
					id: device.project_id,
				});
				assign_child_data.push({
					key: device.id,
					id: device.id,
					qr_code: device.qr_code,
					device_type: device_type_data ? device_type_data.name : '-',
					project: project_data ? project_data.name : '-',
					device_config: device,
				});
			});
		}
		that.setState(
			{
				assign_child_data: assign_child_data,
				add_device_to_customer: true,
			},
			() => {
				// console.log('assign_child_data', assign_child_data);
				that.drawerSearchResults(that.state.drawer_search_value);
			}
		);
	}

	addDeviceToAssigned(device) {
		// console.log('add inserted', device);
		if (this.device_details && this.device_details.length) {
			let added_filtered_device_list = this.state
					.added_filtered_device_list,
				filtered_drawer_stations_list = this.state
					.filtered_drawer_stations_list;

			if (
				!_find(this.state.added_filtered_device_list, {
					id: device.id,
				})
			) {
				added_filtered_device_list.push(
					_find(this.device_details, { id: device.id })
				);
			}

			if (
				_find(this.state.filtered_drawer_stations_list, {
					id: device.id,
				})
			) {
				filtered_drawer_stations_list.splice(
					this.state.filtered_drawer_stations_list.indexOf(
						_find(this.state.filtered_drawer_stations_list, {
							id: device.id,
						})
					),
					1
				);
			}

			added_filtered_device_list = added_filtered_device_list.filter(
				Boolean
			);
			filtered_drawer_stations_list = filtered_drawer_stations_list.filter(
				Boolean
			);

			let assign_child_data = [],
				assign_child_data_add = [];
			if (
				filtered_drawer_stations_list &&
				filtered_drawer_stations_list.length
			) {
				filtered_drawer_stations_list.map((device, ind) => {
					// console.log('deviceeeee', device);
					let device_type_data = _find(this.device_types, {
						id: device.type_id,
					});
					let project_data = _find(this.projects_list, {
						id: device.project_id,
					});
					assign_child_data.push({
						key: device.id,
						id: device.id,
						qr_code: device.qr_code,
						device_type: device_type_data
							? device_type_data.name
							: '-',
						project: project_data ? project_data.name : '-',
						device_config: device,
					});
				});
			}
			if (
				added_filtered_device_list &&
				added_filtered_device_list.length
			) {
				added_filtered_device_list.map((device, ind) => {
					// console.log('deviceeeee', device);
					let device_type_data = _find(this.device_types, {
						id: device.type_id,
					});
					let project_data = _find(this.projects_list, {
						id: device.project_id,
					});
					assign_child_data_add.push({
						key: device.id,
						id: device.id,
						qr_code: device.qr_code,
						device_type: device_type_data
							? device_type_data.name
							: '-',
						project: project_data ? project_data.name : '-',
						device_config: device,
					});
				});
			}
			// console.log(
			// 	'added_filtered_device_list',
			// 	added_filtered_device_list
			// );
			// console.log(
			// 	'added_filtered_device_list',
			// 	filtered_drawer_stations_list
			// );
			this.setState({
				added_filtered_device_list: added_filtered_device_list,
				filtered_drawer_stations_list: filtered_drawer_stations_list,
				assign_child_data: assign_child_data,
				assign_child_data_add: assign_child_data_add,
			});
		}
	}

	removeDeviceToAssigned(device) {
		// console.log('remove inserted', device);
		if (this.device_details && this.device_details.length) {
			let added_filtered_device_list = this.state
					.added_filtered_device_list,
				filtered_drawer_stations_list = this.state
					.filtered_drawer_stations_list;

			if (
				_find(this.state.added_filtered_device_list, { id: device.id })
			) {
				added_filtered_device_list.splice(
					this.state.added_filtered_device_list.indexOf(
						_find(this.state.added_filtered_device_list, {
							id: device.id,
						})
					),
					1
				);
			}

			if (
				!_find(this.state.filtered_drawer_stations_list, {
					id: device.id,
				})
			) {
				filtered_drawer_stations_list.push(
					_find(this.device_details, { id: device.id })
				);
			}

			added_filtered_device_list = added_filtered_device_list.filter(
				Boolean
			);
			filtered_drawer_stations_list = filtered_drawer_stations_list.filter(
				Boolean
			);

			let assign_child_data = [],
				assign_child_data_add = [];
			if (
				filtered_drawer_stations_list &&
				filtered_drawer_stations_list.length
			) {
				filtered_drawer_stations_list.map((device, ind) => {
					let device_type_data = _find(this.device_types, {
						id: device.type_id,
					});
					let project_data = _find(this.projects_list, {
						id: device.project_id,
					});
					assign_child_data.push({
						key: device.id,
						id: device.id,
						qr_code: device.qr_code,
						device_type: device_type_data
							? device_type_data.name
							: '-',
						project: project_data ? project_data.name : '-',
						device_config: device,
					});
				});
			}

			if (
				added_filtered_device_list &&
				added_filtered_device_list.length
			) {
				added_filtered_device_list.map((device, ind) => {
					// console.log('deviceeeee', device);
					let device_type_data = _find(this.device_types, {
						id: device.type_id,
					});
					let project_data = _find(this.projects_list, {
						id: device.project_id,
					});
					assign_child_data_add.push({
						key: device.id,
						id: device.id,
						qr_code: device.qr_code,
						device_type: device_type_data
							? device_type_data.name
							: '-',
						project: project_data ? project_data.name : '-',
						device_config: device,
					});
				});
			}
			// console.log(
			// 	'added_filtered_device_list',
			// 	added_filtered_device_list
			// );
			this.setState({
				added_filtered_device_list: added_filtered_device_list,
				filtered_drawer_stations_list: filtered_drawer_stations_list,
				assign_child_data: assign_child_data,
				assign_child_data_add: assign_child_data_add,
			});
		}
	}

	bulkaddDeviceToAssigned(devices) {
		// console.log('add bulk inserted', devices);
		if (
			this.device_details &&
			this.device_details.length &&
			devices &&
			devices.length
		) {
			let added_filtered_device_list = this.state
					.added_filtered_device_list,
				filtered_drawer_stations_list = this.state
					.filtered_drawer_stations_list;

			devices.map((device_id) => {
				if (
					!_find(this.state.added_filtered_device_list, {
						id: device_id,
					})
				) {
					added_filtered_device_list.push(
						_find(this.device_details, { id: device_id })
					);
				}

				if (
					_find(this.state.filtered_drawer_stations_list, {
						id: device_id,
					})
				) {
					filtered_drawer_stations_list.splice(
						this.state.filtered_drawer_stations_list.indexOf(
							_find(this.state.filtered_drawer_stations_list, {
								id: device_id,
							})
						),
						1
					);
				}
			});

			added_filtered_device_list = added_filtered_device_list.filter(
				Boolean
			);
			filtered_drawer_stations_list = filtered_drawer_stations_list.filter(
				Boolean
			);

			let assign_child_data = [],
				assign_child_data_add = [];
			if (
				filtered_drawer_stations_list &&
				filtered_drawer_stations_list.length
			) {
				filtered_drawer_stations_list.map((device, ind) => {
					// console.log('deviceeeee', device);
					let device_type_data = _find(this.device_types, {
						id: device.type_id,
					});
					let project_data = _find(this.projects_list, {
						id: device.project_id,
					});
					assign_child_data.push({
						key: device.id,
						id: device.id,
						qr_code: device.qr_code,
						device_type: device_type_data
							? device_type_data.name
							: '-',
						project: project_data ? project_data.name : '-',
						device_config: device,
					});
				});
			}
			if (
				added_filtered_device_list &&
				added_filtered_device_list.length
			) {
				added_filtered_device_list.map((device, ind) => {
					// console.log('deviceeeee', device);
					let device_type_data = _find(this.device_types, {
						id: device.type_id,
					});
					let project_data = _find(this.projects_list, {
						id: device.project_id,
					});
					assign_child_data_add.push({
						key: device.id,
						id: device.id,
						qr_code: device.qr_code,
						device_type: device_type_data
							? device_type_data.name
							: '-',
						project: project_data ? project_data.name : '-',
						device_config: device,
					});
				});
			}
			// console.log(
			// 	'added_filtered_device_list',
			// 	added_filtered_device_list
			// );
			// console.log(
			// 	'added_filtered_device_list',
			// 	filtered_drawer_stations_list
			// );
			this.setState({
				added_filtered_device_list: added_filtered_device_list,
				filtered_drawer_stations_list: filtered_drawer_stations_list,
				assign_child_data: assign_child_data,
				assign_child_data_add: assign_child_data_add,
				selectedRowKeysChild: [],
				selectedRows: [],
			});
		}
	}

	closeAssigntoCustomerDrawer() {
		this.setState({
			add_device_to_customer: false,
			added_filtered_device_list: [],
			filtered_drawer_stations_list: [],
			device_assignment_customer_id: 0,
			device_assignment_application_id: 0,
		});
	}

	convertToThing(value) {
		this.setState({
			convert_to_things: value.target.checked,
			selected_thing_type: undefined,
			thing_name: undefined,
		});
	}

	submitAssignDrawer() {
		let selected_devices = [];
		if (
			this.state.added_filtered_device_list &&
			this.state.added_filtered_device_list.length
		) {
			this.state.added_filtered_device_list.map((dev, ind) => {
				selected_devices.push(dev.id);
			});

			this.setState(
				{
					selected_devices: selected_devices,
				},
				() => {
					this.handleAssignDevicesToCustomer();
				}
			);
		}
	}

	clearSearchValue() {
		this.setState(
			{
				table_search: '',
				search_value: '',
			},
			() => {
				this.parseURL('search', '');
				this.getFilteredStations('');
			}
		);
	}

	onSelectChange(selectedRowKeysChild, selectedRows) {
		// console.log('selectedRowKeys changed: ', selectedRowKeysChild);
		// console.log('selectedRowKeys changed: ', selectedRows);
		this.setState({
			selectedRowKeysChild: selectedRowKeysChild,
			selectedRows: selectedRows,
		});
	}

	async deleteDeviceFunction(device_ids) {
		let that = this;
		let data = {
			device_ids: device_ids,
			client_id: this.props.client_id,
		};
		let response = await deleteDevices(data);
		// console.log('deleteTemplateFunction', response);
		if (response.status === 403) {
			that.setState({
				drawer_unauthorised_access: true,
				drawer_unauthorised_access_msg: response.message,
			});
		} else if (response.status === 'success') {
			that.openNotification('success', 'Device/s deleted successfully');
			that.fetchDeviceList();
			that.setState({
				selectedRows: undefined,
				selectedRowKeys: undefined,
				selected_devices: undefined,
			});
		} else {
			that.openNotification('error', response.message);
			that.setState({
				drawer_unauthorised_access: false,
				error_API: true,
				error_API_msg: response.message,
			});
		}
	}

	openAddDrawer() {
		this.setState(
			{
				show_add_edit_drawer: true,
				edit_device: false,
				selected_row_data: undefined,
			},
			() => {
				this.props.history.push(
					this.platform_slug + '/devices/unassigned/add'
				);
			}
		);
	}

	openEditDrawer(row_data) {
		this.setState(
			{
				show_add_edit_drawer: true,
				edit_device: true,
				selected_row_data: row_data,
			},
			() => {
				this.props.history.push(
					this.platform_slug +
						'/devices/unassigned/' +
						row_data.id +
						'/edit'
				);
			}
		);
	}

	closeAddEditDrawer() {
		this.setState(
			{
				show_add_edit_drawer: false,
				edit_device: false,
			},
			() => {
				this.props.history.push(
					this.platform_slug + '/devices/unassigned'
				);
			}
		);
	}

	deleteDevice() {
		let deviceIds = [],
			nonDelete = false,
			that = this;
		if (that.state.selected_rows && that.state.selected_rows.length) {
			// console.log('deleteDevice', that.state.selected_rows);
			that.state.selected_rows.map((row) => {
				if (row.is_editable) {
					deviceIds.push(row.id);
				} else {
					nonDelete = true;
				}
			});
		}

		if (nonDelete) {
			that.openNotification(
				'error',
				'Only bulk or single added device can be deleted'
			);
		} else {
			that.setState(
				{
					device_ids: deviceIds,
				},
				() => {
					AntConfirmModal({
						title:
							that.state.selected_devices &&
							that.state.selected_devices.length > 1
								? 'Are you sure delete these devices?'
								: 'Are you sure delete this device?',
						content: '',
						okText: 'Yes',
						okType: 'danger',
						cancelText: 'No',
						onOk: () =>
							that.deleteDeviceFunction(that.state.device_ids),
					});
				}
			);
		}
	}

	openBulkUpload() {
		this.setState({
			bulk_add_device: true,
		});
	}

	bulkAddDevice() {
		this.setState({
			adding_new_device: true,
		});
		let data = {
			devices: this.state.bulk_device_data,
			client_id: this.props.client_id,
		};
		this.deviceAddFunction(data);
	}

	closeBulkAdd() {
		this.setState({
			bulk_device_data: [],
			bulk_add_device: false,
			fileList: [],
			uploaded_file_data: [],
			adding_new_device: false,
		});
	}

	handleBulkAdd(info) {
		// console.log('0_handleBulkAdd', info);
		let fileList = [...info.fileList];

		if (fileList && fileList.length) {
			// 1. Limit the number of uploaded files
			// Only to show one recent uploaded files, and old ones will be replaced by the new
			fileList = fileList.slice(-1);

			// 2. Read from response and show file link
			fileList = fileList.map((file) => {
				if (file.response) {
					// Component will show file.url as link
					file.url = file.response.url;
				}
				return file;
			});
			let reader = new FileReader();
			let rABS = !!reader.readAsBinaryString;
			reader.onload = (e) => {
				/* Parse data */
				let bstr = e.target.result;
				let wb = xlsxRead(bstr, { type: rABS ? 'binary' : 'array' });
				/* Get first worksheet */
				let wsname = wb.SheetNames[0];
				let ws = wb.Sheets[wsname];
				/* Convert array of arrays */
				let data = xlsxUtils.sheet_to_json(ws, { header: 0 });
				/* Update state */
				this.setState(
					{
						uploaded_file_data: data,
					},
					() => {
						this.parseFromFileObjectToData();
					}
				);
				// console.log('0_handleBulkAdd_data', data);
			};
			if (rABS) reader.readAsBinaryString(info.file.originFileObj);
			else reader.readAsArrayBuffer(info.file.originFileObj);
			// console.log('0_handleBulkAdd_data', info.file.originFileObj);
		} else {
			this.setState(
				{
					uploaded_file_data: [],
				},
				() => {
					this.parseFromFileObjectToData();
				}
			);
			// console.log('0_handleBulkAdd_data', []);
		}
		this.setState({ fileList });
	}

	downloadDemoFile(type) {
		// console.log('function called');
		let ws = xlsxUtils.aoa_to_sheet(this.state.download_data);
		let wb = xlsxUtils.book_new();
		xlsxUtils.book_append_sheet(wb, ws, 'SheetJS');
		/* generate XLSX file and send to client */
		if (type == 'xlsx') {
			writeFile(wb, 'DemoSheet.xlsx');
		} else {
			writeFile(wb, 'DemoSheet.csv');
		}
	}

	parseFromFileObjectToData() {
		let bulkDeviceData = [];
		if (
			this.state.uploaded_file_data &&
			this.state.uploaded_file_data.length
		) {
			for (let i = 0; i < this.state.uploaded_file_data.length; i++) {
				let dataRow = this.state.uploaded_file_data[i];
				// console.log('0_handleBulkAdd_dataRow', dataRow);
				let mainFileData = JSON.parse(
					JSON.stringify(this.state.uploaded_file_data)
				);
				if (
					dataRow['Device serial no'] &&
					dataRow['Device serial no'] !== '' &&
					dataRow['Device serial no'] !== null &&
					dataRow['Device type'] &&
					dataRow['Device type'] !== '' &&
					dataRow['Device type'] !== null
				) {
					let multipleValue = _filter(
						this.state.uploaded_file_data,
						{ 'Device serial no': dataRow['Device serial no'] }
					);
					if (multipleValue && multipleValue.length > 1) {
						this.openNotification(
							'error',
							'Duplicate Device serial no found at row no - ' +
								(dataRow['__rowNum__'] + 1)
						);
						this.setState({
							bulk_device_data: [],
							fileList: [],
							uploaded_file_data: [],
							adding_new_device: false,
						});
						break;
					} else {
						bulkDeviceData.push({
							serial_no: dataRow['Device serial no'],
							device_type: dataRow['Device type'],
							description: dataRow['Description'],
						});
					}
				} else if (!dataRow['Device serial no']) {
					this.openNotification(
						'error',
						'No Device serial no at row no - ' +
							(dataRow['__rowNum__'] + 1)
					);
					this.setState({
						bulk_device_data: [],
						fileList: [],
						uploaded_file_data: [],
						adding_new_device: false,
					});
					break;
				} else if (!dataRow['Device type']) {
					this.openNotification(
						'error',
						'No Device type at row no - ' +
							(dataRow['__rowNum__'] + 1)
					);
					this.setState({
						bulk_device_data: [],
						fileList: [],
						uploaded_file_data: [],
						adding_new_device: false,
					});
					break;
				}
			}
		}
		this.setState({
			bulk_device_data: bulkDeviceData,
		});
		// console.log('0_handleBulkAdd_bulkDeviceData', bulkDeviceData);
	}

	async deviceAddFunction(data) {
		// console.log('0_handleBulkAdd_data', data);
		let that = this;
		let response = await addDevice(data);
		if (response.status === 403) {
			that.setState({
				unauthorised_access: true,
				unauthorised_access_msg: response.message,
				adding_new_device: false,
			});
		} else if (response.status === 'success') {
			that.openNotification('success', 'Devices added successfully');
			that.fetchDeviceList();
			that.closeBulkAdd();
		} else {
			that.openNotification('error', response.message);
			that.setState({
				unauthorised_access: false,
				error_API: true,
				adding_new_device: false,
				error_API_msg: response.message,
			});
		}
	}

	openCustomCommand(deviceId, row_data) {
		let routeLink =
			this.iotBasePath +
			'/enterprise/' +
			this.props.client_id +
			this.platform_slug +
			'/devices/unassigned/' +
			deviceId +
			'/custom-command?type_id=' +
			row_data.type_id;
		window.open(routeLink, '_blank');

		/*this.props.history.push(
			this.platform_slug +
				'/devices/unassigned/' +
				deviceId +
				'/custom-command'
		);*/
	}

	filterTableData() {
		let tableData = [...this.device_details];

		// console.log('table_data_ 0', tableData);

		/*if (this.state.selected_vendor_id) {
			tableData = _filter(tableData, {vendor_id: this.state.selected_vendor_id});
		}

		if (this.state.selected_client_id) {
			tableData = _filter(tableData, {client_id: this.state.selected_client_id});
		}

		if (this.state.selected_app_id) {
			tableData = _filter(tableData, {application_id: this.state.selected_app_id});
		}*/

		if (this.state.selected_type && this.state.selected_type.length) {
			tableData = _filter(
				tableData,
				(v) => _indexOf(this.state.selected_type, v.type_id) !== -1
			);
		}

		if (this.state.selected_connectivity_status) {
			if (this.state.selected_connectivity_status === 1) {
				tableData = _filter(tableData, {
					status_code: this.state.selected_connectivity_status,
				});
			} else {
				tableData = _filter(
					tableData,
					(v) => _indexOf([2, 3, 4, 5, 6, 7], v.status_code) !== -1
				);
			}
			// console.log('selected_connectivity_status_', this.state.selected_connectivity_status);
		}

		if (this.state.selected_device_status) {
			// console.log('selected_connectivity_status_ selected_device_status', this.state.selected_device_status);
			if (this.state.selected_device_status === 7) {
				tableData = _filter(tableData, {
					status_code: this.state.selected_device_status,
				});
			} else {
				tableData = _filter(
					tableData,
					(v) => _indexOf([1, 2, 3, 4, 5, 6], v.status_code) !== -1
				);
			}
		}

		// console.log('table_data_ 1', tableData);
		this.setState(
			{
				filtered_table_data: tableData,
				remove_loading: true,
			},
			() => {
				this.getFilteredStations(this.state.search_value);
			}
		);
	}

	async getClientCustomerList() {
		let customerListResult = await retriveCustomerList(
			this.state.selected_vendor_id
		);

		if (customerListResult && customerListResult.status == 'success') {
			this.setState({
				vendor_client_list: customerListResult.customers,
				vendor_application_list: customerListResult.applications,
			});
		}

		this.filterTableData();
	}

	openConfiguration(routeLink) {
		// this.props.history.push(routeLink);
		window.open(routeLink, '_blank');
		// this.props.history.push(routeLink);
	}

	getClientApplicationList() {
		let applicationList = [],
			applicationArr = this.state.application_list,
			customerArr = this.state.client_list,
			selectedId = this.state.selected_client_id;
		if (this.state.selected_vendor_id && !this.state.selected_client_id) {
			applicationArr = this.state.vendor_application_list;
			customerArr = this.state.vendor_client_list;
			selectedId = this.state.selected_vendor_id;
		}

		let selectedCustDetails = _find(customerArr, { id: selectedId });
		// console.log('selectedCustDetails_', selectedCustDetails);
		// console.log('selectedCustDetails_ 1', applicationArr);
		if (selectedCustDetails) {
			applicationArr.map((appDet) => {
				if (
					selectedCustDetails.applications.includes(
						appDet.id//.toString()
					) || selectedCustDetails.access_applications.includes(
						appDet.id//.toString()
					)
				) {
					applicationList.push(appDet);
				}
			});
		}

		this.setState({
			client_application_list: applicationList,
		});
		this.filterTableData();
	}

	/*onChangeVendor(e) {
		this.setState({
			selected_vendor_id: e,
			selected_client_id: undefined,
			selected_app_id: undefined,
			remove_loading: false
		}, () => {
			if(e) {
				this.getClientCustomerList();
			} else {
				this.filterTableData();
			}
		});
	}

	onChangeClient(e) {
		this.setState({
			selected_client_id: e,
			selected_app_id: undefined,
			remove_loading: false
		}, () => {
			if (e) {
				this.getClientApplicationList();
			} else {
				this.filterTableData();
			}
		});
	}

	onChangeApplication(e) {
		this.setState({
			selected_app_id: e,
			remove_loading: false
		}, () => {
			this.filterTableData();
		});
	}

	onChangeStatus(e) {
		console.log('onChangeStatus_', e);

		this.setState({
			selected_status: e,
			remove_loading: false
		}, () => {
			this.filterTableData();
		});
	}

	onChangeType(e) {
		console.log('onChangeType_', e);

		this.setState({
			selected_type: e,
			remove_loading: false
		}, () => {
			this.filterTableData();
		});
	}*/

	closeStatusTag(e, status) {
		// console.log('closeTag_', status);
		let selectedType = this.state.selected_type;
		selectedType.remove(status);
		this.setState(
			{
				selected_type: selectedType,
				remove_loading: false,
			},
			() => {
				// console.log('closeTag_ 1', this.state.selected_type);
				this.filterTableData();
			}
		);
	}

	singleDeviceFirmwareUpdate(selectedRow) {
		// console.log('singleDeviceFirmwareUpdate_', selectedRow);
		let that = this;
		that.setState(
			{
				selected_device: [selectedRow[0].id],
				selected_row: selectedRow,
				single_update: true,
			},
			() => {
				// console.log('singleDeviceFirmwareUpdate_ 1', that.state.selected_devices);
				// console.log('singleDeviceFirmwareUpdate_ 2', that.state.selected_row);

				if (
					that.state.device_lists &&
					that.state.selected_row &&
					that.state.selected_row.length
				) {
					// let firmwares = that.state.device_lists.firmwares.reverse();
					let firmwares = _orderBy(
						that.state.device_lists.firmwares,
						['id'],
						['desc']
					);
					that.firmwareSelectionOptions = firmwares
						.map((firmware) => {
							if (
								that.state.selected_row[0].type_id ===
								firmware.device_type_id
							) {
								return (
									<AntOption value={firmware.id}>
										{firmware.version}
									</AntOption>
								);
							}
						})
						.filter(Boolean);
				}
				that.updateFirmware();
			}
		);
	}

	openDeviceDebug(row_data, raw_log = false) {
		this.setState({
			//show_device_debug: true,
			selected_row_data: row_data,
		});
		// let deviceDetails = {
		// 	customer_id: this.state.selected_row_data.customer_id,
		// 	application_id: this.state.selected_row_data.application_id,
		// 	device_id: this.state.selected_row_data.id,
		// 	device_qr_code: this.state.selected_row_data.qr,
		// };
		let routeLink = '',
			query = '';
		if (raw_log) {
			query = '&tab=raw_log';
		}
		if (this.props.is_application_filter) {
			routeLink =
				this.iotBasePath +
				'/enterprise/' +
				this.props.client_id +
				this.platform_slug +
				'/customer-management/' +
				this.props.customer_id +
				'/applications/' +
				this.state.selected_app +
				'/devices/' +
				row_data.id +
				'/debug?' +
				'customer_id=' +
				row_data.customer_id +
				'&application_id=' +
				row_data.application_id +
				'&device_id=' +
				row_data.id +
				'&device_qr_code=' +
				row_data.qr +
				'&type_name=' +
				row_data.type_name +
				query;
		} else {
			routeLink =
				this.iotBasePath +
				'/enterprise/' +
				this.props.client_id +
				this.platform_slug +
				'/devices/unassigned/' +
				row_data.id +
				'/debug?' +
				'customer_id=' +
				row_data.customer_id +
				'&application_id=' +
				row_data.application_id +
				'&device_id=' +
				row_data.id +
				'&device_qr_code=' +
				row_data.qr +
				'&type_name=' +
				row_data.type_name +
				query;
		}
		window.open(routeLink, '_blank');
	}

	closeDeviceDebug() {
		this.setState({
			show_device_debug: false,
			selected_row_data: undefined,
		});
	}

	clearAllFilter() {
		this.setState(
			{
				selected_vendor_id: undefined,
				selected_client_id: undefined,
				selected_app_id: [],
				selected_type: [],
				search_value: '',
				selected_connectivity_status: undefined,
				selected_device_status: undefined,
				remove_loading: false,
			},
			() => {
				this.filterTableData();
			}
		);
	}

	enableCopyToClip(value = false, responseText) {
		if (value) {
			return (
				<div
					className="copy-btn"
					onClick={() => this.copyToClipboard(value, responseText)}
				>
					<CopyOutlined className="mar-rt-5" />
					<span className="copy-text">Copy</span>
				</div>
			);
		} else {
			return (
				<div className="copy-btn disabled">
					<CopyOutlined className="mar-rt-5" />
					<span className="copy-text">Copy</span>
				</div>
			);
		}
	}

	processProtocolData(device_auth_protocols) {
		let protocolOptions = Object.keys(device_auth_protocols),
			renderProtocol = '';
		const tab_option = {
			showSearch: false,
			allowClear: false,
			showArrow: true,
			value: this.state.selectedProtocol
				? this.state.selectedProtocol
				: undefined,
			placeholder: 'Select a protocol',
		};
		if (protocolOptions && protocolOptions.length) {
			renderProtocol = (
				<>
					<div className="data-container">
						<span className="data-value-selector">
							<AntSelect
								{...tab_option}
								onChange={(value) =>
									this.setState({ selectedProtocol: value })
								}
							>
								{protocolOptions.length &&
									protocolOptions.map((e) => (
										<AntOption value={e}>{e}</AntOption>
									))}
							</AntSelect>
						</span>
					</div>
					{(() => {
						let selectedPacket =
							this.state.selectedProtocol &&
							device_auth_protocols &&
							device_auth_protocols[this.state.selectedProtocol]
								? device_auth_protocols[
										this.state.selectedProtocol
								  ]
								: {};
						return (
							<>
								<div className="data-container">
									<span className="data-label">
										Domain :{' '}
									</span>
									<span className="data-value  key-value">
										{selectedPacket.domain || 'NA'}
									</span>
									{this.enableCopyToClip(
										selectedPacket.domain,
										'Domain copied to clipboard'
									)}
								</div>
								<div className="data-container">
									<span className="data-label">IP : </span>
									<span className="data-value  key-value">
										{selectedPacket.ip || 'NA'}
									</span>
									{this.enableCopyToClip(
										selectedPacket.ip,
										'IP copied to clipboard'
									)}
								</div>
								<div className="data-container">
									<span className="data-label">Port : </span>
									<span className="data-value  key-value">
										{selectedPacket.port || 'NA'}
									</span>
									{this.enableCopyToClip(
										selectedPacket.port,
										'Port copied to clipboard'
									)}
								</div>
								<div className="data-packet-title">
									Authentication Packet :
								</div>
								<div className="data-container data-container-packet">
									<div className="packet-val">
										{selectedPacket.auth_packet || 'NA'}
									</div>
									{this.enableCopyToClip(
										selectedPacket.auth_packet,
										'Authentication Packet copied to clipboard'
									)}
								</div>
							</>
						);
					})()}
				</>
			);
		}
		return renderProtocol;
	}

	async getFirmwareList(firmware_row) {
		let app_id = this.state.firm_application_value, //firmware_row.application_id,
			device_type_id = firmware_row.type_id,
			product_model = parseInt(firmware_row.product_model),
			circuit_version = parseInt(firmware_row.circuit_version);

		let application_query = 'application=' + app_id;
		let response = await retriveVendorFirmwareList(this.props.client_id, application_query);

		if (response.status === 403) {
			this.setState({
				unauthorised_access: true,
				unauthorised_access_msg: response.message,
				firmware_update_modal_loading: false,
			});
		} else if (response.status === 'success') {
			let release_firmware_list = [],
				dev_firmware_list = [];
			console.log('firmware list response', response);
			if (response.device_types && response.device_types.length) {
				let total_firmware_list = _find(
					response.device_types,
					function (o) {
						return o.id == device_type_id;
					}
				);
				if (
					total_firmware_list &&
					total_firmware_list.firmwares &&
					total_firmware_list.firmwares.length
				) {
					total_firmware_list.firmwares.map((firm) => {
						if (
							(device_type_id !== 1 ||
								firm.product_models?.includes(product_model)) &&
						 	(!this.state.accepted_type_ids.includes(device_type_id) || firm.circuit_versions?.includes(circuit_version)
						 || circuitExceptionDevices.includes(firmware_row.id))
						) {
							if (firm.is_release == 1) {
								release_firmware_list.push(firm);
							} else if (firm.is_release == 0) {
								dev_firmware_list.push(firm);
							}
						}
					});
				}
			}
			this.setState({
				release_firmware_list: release_firmware_list,
				dev_firmware_list: dev_firmware_list,
				firmware_update_modal_loading: false,
			});
		} else {
			this.openNotification('error', response.message);
			this.setState({
				unauthorised_access: false,
				error_API: true,
				error_API_msg: response.message,
				firmware_update_modal_loading: false,
			});
		}
	}

	onRadioFirmwareChange(value) {
		this.setState({
			selected_version_features: [],
			selected_radio_value: value,
			firmware_value: undefined,
		});
	}

	selectFirmApplication(value) {
		this.setState(
			{
				selected_version_features: [],
				firm_application_value: value,
				selected_radio_value: 'release',
				firmware_value: undefined,
				firmware_update_modal_loading: true,
			},
			() => {
				if (this.state.single_update) {
					if (this.state.selected_row.length) {
						this.getFirmwareList(this.state.selected_row[0]);
					}
				} else {
					if (
						this.state.selected_rows &&
						this.state.selected_rows.length
					) {
						this.getFirmwareList(this.state.selected_rows[0]);
					}
				}
			}
		);
	}

	getfirmwareModalBody(selected_row) {
		let firmwareOptions = [],
			applicationOptions = [];
		if (this.state.selected_radio_value == 'release') {
			if (this.state.release_firmware_list.length) {
				let sorted_release_firmware_list = _orderBy(
					this.state.release_firmware_list,
					['id'],
					['desc']
				);
				firmwareOptions = sorted_release_firmware_list.map((firm) => {
					return (
						<AntOption value={firm.id}>{firm.version}</AntOption>
					);
				});
			}
		} else if (this.state.selected_radio_value == 'dev') {
			if (this.state.dev_firmware_list.length) {
				let sorted_dev_firmware_list = _orderBy(
					this.state.dev_firmware_list,
					['id'],
					['desc']
				);
				firmwareOptions = sorted_dev_firmware_list.map((firm) => {
					return (
						<AntOption value={firm.id}>
							{firm.version}
							{' (Build no: '}
							{firm.build_no}
							{')'}
						</AntOption>
					);
				});
			}
		}
		if (
			this.state.device_lists.applications &&
			this.state.device_lists.applications.length
		) {
			let sorted_applications = _orderBy(
				this.state.device_lists.applications,
				['name'],
				['asc']
			);
			let app_array = null,
				curr_device_type = null;
			if (this.state.device_types && this.state.device_types.length) {
				curr_device_type = _find(this.state.device_types, {
					id: selected_row.type_id,
				});
				app_array = curr_device_type ? curr_device_type.apps : null;
			}
			console.log('app_array', app_array);
			applicationOptions = sorted_applications.map((app) => {
				if (app.id !== 17) {
					if (
						app_array &&
						app_array.length &&
						app_array.includes(app.id)
					) {
						console.log('app_array1', app_array, app.id);
						return <AntOption value={app.id}>{app.name}</AntOption>;
					}
				}
			});
		}
		return (
			<div className="firmware-modal-body">
				<AntRow gutter={[10, 20]}>
					<AntCol xs={24} sm={9} className="firmware-label">
						Application* :{' '}
					</AntCol>
					<AntCol xs={24} sm={15}>
						<AntSelect
							showSearch
							value={
								this.state.firm_application_value
								// this.state.device_lists.applications &&
								// this.state.device_lists.applications.length == 1
								// 	? this.state.device_lists.applications[0].id
								// 	: undefined
							}
							disabled={
								this.state.device_lists.applications &&
								this.state.device_lists.applications.length == 1
									? true
									: false
							}
							placeholder="Select application"
							optionFilterProp="children"
							// value={
							// 	this.state.device_lists.applications &&
							// 	this.state.device_lists.applications.length == 1
							// 		? this.state.device_lists.applications[0].id
							// 		: this.state.firm_application_value
							// }
							onChange={(e) => this.selectFirmApplication(e)}
							filterOption={(input, option) =>
								option.props.children
									.toLowerCase()
									.indexOf(input.toLowerCase()) >= 0
							}
						>
							{applicationOptions}
						</AntSelect>
					</AntCol>
					<AntCol xs={24} sm={9} className="firmware-label">
						Build Type*
					</AntCol>
					<AntCol xs={24} sm={15}>
						<AntRadioGroup
							value={this.state.selected_radio_value}
							disabled={
								(this.state.device_lists.applications &&
									this.state.device_lists.applications
										.length > 1 &&
									this.state.firm_application_value ==
										undefined) ||
								this.state.firmware_update_modal_loading
									? true
									: false
							}
							onChange={(e) =>
								this.onRadioFirmwareChange(e.target.value)
							}
						>
							<AntRadio value={'release'}>Release</AntRadio>
							<AntRadio
								value={'dev'}
								style={{ marginLeft: '20px' }}
							>
								Dev
							</AntRadio>
						</AntRadioGroup>
					</AntCol>
					<AntCol xs={24} sm={9} className="firmware-label">
						Firmware Version* :{' '}
					</AntCol>
					<AntCol xs={24} sm={15}>
						<AntSelect
							showSearch
							placeholder="Select firmware version"
							disabled={
								(this.state.device_lists.applications &&
									this.state.device_lists.applications
										.length > 1 &&
									this.state.firm_application_value ==
										undefined) ||
								this.state.firmware_update_modal_loading
									? true
									: false
							}
							optionFilterProp="children"
							loading={this.state.firmware_update_modal_loading}
							value={this.state.firmware_value}
							onChange={(e) => this.selectFirmware(e)}
							filterOption={(input, option) =>
								option.props.children
									.toLowerCase()
									.indexOf(input.toLowerCase()) >= 0
							}
						>
							{firmwareOptions}
						</AntSelect>
					</AntCol>
					<AntCol xs={24} sm={9}>
						Firmware Features:
					</AntCol>
					<AntCol xs={24} sm={15}>
						<section className="firmware-features">
							{this.state.selected_version_features?.[0]
								? this.state.selected_version_features[0]
								: 'NA'}
						</section>
					</AntCol>
				</AntRow>
			</div>
		);
		//}
	}

	//validate firmware mandatory fields
	validateFirmwareFields() {
		return this.state.firmware_value && this.state.firm_application_value;
	}

	getmodalCustomBody(row_value) {
		return (
			<div className="device-form">
				<div className="data-container">
					<span className="data-label">Device Type : </span>
					<span className="data-value type-name">
						{row_value.type_name}
					</span>
				</div>
				<div className="data-container">
					<span className="data-label">Serial Id : </span>
					<span className="data-value qr-value">{row_value.qr}</span>
				</div>
				<div className="data-container">
					<span className="data-label">Key : </span>
					<span className="data-value key-value">
						{row_value.device_key}
					</span>
					{(() => {
						if (row_value.device_key !== 'NA') {
							return (
								<div
									className="copy-btn"
									onClick={() =>
										this.copyToClipboard(
											row_value.device_key,
											'Device key copied to clipboard'
										)
									}
								>
									<CopyOutlined className="mar-rt-5" />
									<span className="copy-text">Copy</span>
								</div>
							);
						} else {
							return (
								<div className="copy-btn disabled">
									<CopyOutlined className="mar-rt-5" />
									<span className="copy-text">Copy</span>
								</div>
							);
						}
					})()}
				</div>
				{this.processProtocolData(row_value.device_auth_protocols)}
				{/* <div className="data-container data-container-packet">
					<div className="data-packet-title">
						Authentication Packet Field :{' '}
					</div>
					<div className="packet-val"></div>
				</div> */}
			</div>
		);
	}
	showDeviceKey(row_value) {
		// let confirm = {
		// 	title: (
		// 		<div className="display-flex font-bold">
		// 			<img src={third_party_grey} height={20} width={20} />
		// 			<span className="mar-lt-20">3rd Party Device</span>
		// 		</div>
		// 	),
		// 	className: 'device-key-modal',
		// 	maskClosable: false,
		// 	content: (
		// 		<div className="device-form">
		// 			<div className="data-container">
		// 				<span className="data-label">Device Type : </span>
		// 				<span className="data-value type-name">
		// 					{row_value.type_name}
		// 				</span>
		// 			</div>
		// 			<div className="data-container">
		// 				<span className="data-label">Serial Id : </span>
		// 				<span className="data-value qr-value">
		// 					{row_value.qr}
		// 				</span>
		// 			</div>
		// 			<div className="data-container">
		// 				<span className="data-label">Key : </span>
		// 				<span className="data-value key-value">
		// 					{row_value.device_key}
		// 				</span>
		// 				{(() => {
		// 					if (row_value.device_key !== 'NA') {
		// 						return (
		// 							<div
		// 								className="copy-btn"
		// 								onClick={() =>
		// 									this.copyToClipboard(
		// 										row_value.device_key
		// 									)
		// 								}
		// 							>
		// 								<CopyOutlined className="mar-rt-5" />
		// 								<span className="copy-text">Copy</span>
		// 							</div>
		// 						);
		// 					} else {
		// 						return (
		// 							<div className="copy-btn disabled">
		// 								<CopyOutlined className="mar-rt-5" />
		// 								<span className="copy-text">Copy</span>
		// 							</div>
		// 						);
		// 					}
		// 				})()}
		// 			</div>
		// 		</div>
		// 	),
		// 	okText: '',
		// 	cancelText: 'Close',
		// };

		// AntConfirmModal(confirm);
		let defaultProtocol = '';
		Object.keys(row_value.device_auth_protocols).map((protocols) => {
			if (row_value.device_auth_protocols[protocols].is_default === 1) {
				defaultProtocol = protocols;
			}
		});
		this.setState(
			{
				third_party_row_value: row_value,
				selectedProtocol: defaultProtocol,
			},
			() => this.toggle3rdPartyModal()
		);
	}
	toggle3rdPartyModal() {
		this.setState((p) => {
			return {
				...p,
				third_party_modal_visible: !p.third_party_modal_visible,
			};
		});
	}

	copyToClipboard(deviceKey, responseText = 'Copied to clipboard') {
		let el = document.createElement('textarea');
		el.value = deviceKey;
		el.setAttribute('readonly', '');
		el.style.position = 'absolute';
		el.style.left = '-9999px';
		document.body.appendChild(el);
		el.select();
		document.execCommand('copy');
		document.body.removeChild(el);
		this.openNotification('success', responseText);
	}

	applyFilterSelect(value, index) {
		let that = this;

		if (index === 0) {
			that.setState(
				{
					selected_type: value,
					loading: true,
				},
				() => {
					that.filterTableData();
				}
			);
		} else if (index === 1) {
			that.setState(
				{
					selected_connectivity_status: value,
					loading: true,
				},
				() => {
					that.filterTableData();
				}
			);
		} else if (index === 2) {
			that.setState(
				{
					selected_device_status: value,
					loading: true,
				},
				() => {
					that.filterTableData();
				}
			);
		}
	}

	async updateStatusFunction(key, selectedDevice) {
		// console.log('updateStatusFunction_', selectedDevice);
		let that = this;
		let data = {
			is_blocked: key == 'block' ? true : false,
			device_ids: selectedDevice,
			client_id: this.props.client_id,
		};
		let response = await deviceStatusUpdate(data);
		if (response.status === 403) {
			that.setState({
				drawer_unauthorised_access: true,
				drawer_unauthorised_access_msg: response.message,
			});
		} else if (response.status === 'success') {
			this.setState(
				{
					remove_loading: false,
				},
				() => {
					that.fetchDeviceList();
					that.openNotification(
						'success',
						'Device/s status updated successfully'
					);
				}
			);
		} else {
			that.openNotification('error', response.message);
			that.setState({
				drawer_unauthorised_access: false,
				error_API: true,
				error_API_msg: response.message,
			});
		}
	}

	onUpdateStatus(checked, event, row_data) {
		let text = <span className="red">Inactive</span>,
			icon = InactiveIcon,
			key = 'block';
		if (row_data.status_code == 7) {
			text = <span className="green">Active</span>;
			icon = ActiveIcon;
			key = 'active';
		}
		AntConfirmModal({
			title: (
				<div>
					<img
						src={icon}
						width={25}
						height={row_data.status_code == 7 ? 30 : 25}
					/>
					<span>
						{row_data.status_code == 7 ? 'Active' : 'Inactive'}
					</span>
				</div>
			),
			content: (
				<div>
					<span>
						{'Are you sure you want to make ' + row_data.qr + ' '}
					</span>{' '}
					{text} <span>{' ?'}</span>
				</div>
			),
			okText: row_data.status_code == 7 ? 'Active' : 'Inactive',
			cancelText: 'Cancel',
			className: 'device-status-update-modal',
			onOk: () => this.updateStatusFunction(key, [row_data.id]),
		});
	}

	onShowChecked(checked, event) {
		this.setState({
			show_checked: checked,
		});
	}

	deselectAll() {
		this.setState({
			show_checked: false,
			selected_devices: [],
			selected_rows: [],
			selected_row_keys: [],
		});
	}

	async updateSimDetails() {
		let { selected_device_sim, sim_details, table_data } = this.state;
		let updated_table_data = table_data.map((item) => {
			if (item.id == selected_device_sim) {
				return { ...item, sim_details };
			}
			return item;
		});
		let response = await updateDeviceSimDetails(
			this.props.client_id,
			selected_device_sim,
			{ sim_details }
		);
		if (response.status == 'success') {
			this.setState({
				table_data: updated_table_data,
				sim_details_drawer: false,
			});
			this.openNotification(
				'success',
				'Sim Details Updated Successfully'
			);
		} else if (response.status == 'failure' && response.message) {
			this.openNotification('error', response.message);
		}
	}

	onChangeSimDetails(id, name, value) {
		let { sim_details } = this.state;
		let sim_details_obj = {
			serial_no: '',
			operator: '',
			sim_slot: id,
		};
		if (!Array.isArray(sim_details)) {
			sim_details = [];
		}
		let find_sim_details_obj = sim_details.find(
			(item) => item.sim_slot == id
		);
		if (!find_sim_details_obj) {
			if (id == 2) {
				sim_details = [
					{
						serial_no: '',
						operator: '',
						sim_slot: 1,
					},
					{
						...sim_details_obj,
						[name]: value,
					},
				];
			} else {
				sim_details = [
					...sim_details,
					{
						...sim_details_obj,
						[name]: value,
					},
				];
			}
		} else {
			sim_details = sim_details.map((item) => {
				if (item.sim_slot == id) {
					return {
						...find_sim_details_obj,
						[name]: value,
					};
				}
				return item;
			});
		}
		this.setState({ sim_details });
	}

	renderSimDetails(sim_details, row_value) {
		return (
			<div className="sim-icon-holder">
				{window.innerWidth < 576 ? (
					<div className="sim-container">
						<EditOutlined
							onClick={() => {
								this.setState({
									selected_device_sim: row_value.id,
									sim_details: sim_details,
									sim_details_drawer: true,
								});
							}}
						/>
						<div>
							{sim_details &&
							sim_details[0] &&
							sim_details[0].serial_no ? (
								<div className="sim-icon-tooltip-each-line">
									SIM1 : {sim_details[0]?.serial_no} (
									{sim_details[0]?.operator})
								</div>
							) : (
								'NA'
							)}
						</div>
						<div>
							{sim_details &&
							sim_details[1] &&
							sim_details[1].serial_no ? (
								<div className="sim-icon-tooltip-each-line">
									SIM2 : {sim_details[1]?.serial_no} (
									{sim_details[1]?.operator})
								</div>
							) : (
								''
							)}
						</div>
					</div>
				) : (
					<AntTooltip
						title={
							<div className={'sim-icon-tooltip-container'}>
								{sim_details &&
								sim_details[0] &&
								sim_details[0].serial_no ? (
									<div className="sim-icon-tooltip-each-line">
										SIM1 : {sim_details[0]?.serial_no} (
										{sim_details[0]?.operator})
									</div>
								) : (
									'NA'
								)}
								{sim_details &&
								sim_details[1] &&
								sim_details[1].serial_no ? (
									<div className="sim-icon-tooltip-each-line">
										SIM2 : {sim_details[1]?.serial_no} (
										{sim_details[1]?.operator})
									</div>
								) : (
									''
								)}
							</div>
						}
					>
						<div
							className="icon-holder"
							onClick={() => {
								this.setState({
									selected_device_sim: row_value.id,
									sim_details: sim_details,
									sim_details_drawer: true,
								});
							}}
						>
							<img src={sim} alt="" />
						</div>
					</AntTooltip>
				)}
			</div>
		);
	}
	selectDevices(e, deviceListData) {
		let selectedDevices = [],
			filteredDevices = [];
		if (e && e.length) {
			e.map((devices) => {
				selectedDevices.push(parseInt(devices));
				filteredDevices.push(
					_find(deviceListData, { id: parseInt(devices) })
				);
			});
		}

		this.setState({
			selected_devices: selectedDevices,
			selected_rows: filteredDevices,
		});
	}
	mobileSelectedDevices(deviceListData) {
		let options = [];
		if (deviceListData && deviceListData.length) {
			deviceListData.map((devices) => {
				options.push(
					<AntOption key={devices.id}>{devices.qr}</AntOption>
				);
			});
		}
		return (
			<div className="assign-to-customer-label-select">
				<span className="customer-select-label">Select Devices</span>
				<AntSelect
					showSearch
					style={{
						width: 250,
					}}
					mode="tags"
					className="customer-select"
					placeholder="Select Devices"
					maxTagCount={4}
					optionFilterProp="children"
					onChange={(e) => this.selectDevices(e, deviceListData)}
					onSearch={(e) => this.onDeviceClient(e)}
					filterOption={(input, option) =>
						option.props.children
							.toLowerCase()
							.indexOf(input.toLowerCase()) >= 0
					}
				>
					{options}
				</AntSelect>
			</div>
		);
	}

	render() {
		const {
			startValue,
			endValue,
			endOpen,
			formLayout,
			selectedRowKeysChild,
			third_party_row_value,
		} = this.state;
		const rowSelectionTableAssign = {
			selectedRowKeysChild,
			onChange: (selectedRowKeys, selectedRows) => {
				this.onSelectChange(selectedRowKeys, selectedRows);
			},
		};

		const deviceTypeFilterProps = {
			treeData: this.state.deviceTypeFilter,
			value: this.state.deviceTypeFilterValue,
			treeCheckable: true,
			maxTagCount: 0,
			placeholder: 'Device Type',
			showSearch: false,
			maxTagPlaceholder: 'Device Type',
		};

		const deviceTypeFilterPropsView = {
			treeData: this.state.deviceTypeFilter,
			value: this.state.deviceTypeFilterValue,
			treeCheckable: true,
			placeholder: 'Device Type',
		};

		const projectTypeFilterProps = {
			treeData: this.state.projectTypeFilter,
			value: this.state.projectTypeFilterValue,
			treeCheckable: true,
			maxTagCount: 3,
			placeholder: 'Project',
		};

		const deviceTypeFilterPropsChild = {
			treeData: this.state.deviceTypeFilter,
			value: this.state.deviceTypeFilterValueChild,
			treeCheckable: true,
			maxTagCount: 3,
			placeholder: 'Device Type',
		};

		const projectTypeFilterPropsChild = {
			treeData: this.state.projectTypeFilter,
			value: this.state.projectTypeFilterValueChild,
			treeCheckable: true,
			maxTagCount: 3,
			placeholder: 'Project',
		};
		console.log('selected_row_keys', this.state.selected_row_keys);
		const tableRowSelectionOptions = {
			selectedRowKeys: this.state.selected_row_keys,
			onChange: (selectedRowKeys, selectedRows) => {
				this.setState(
					{
						show_checked: selectedRows.length
							? this.state.show_checked
							: false,
						selected_devices: selectedRowKeys,
						selected_rows: selectedRows,
						selected_row_keys: selectedRowKeys,
					},
					() => {
						if (
							this.state.device_lists &&
							this.state.selected_rows &&
							this.state.selected_rows.length
						) {
							// let firmwares = this.state.device_lists.firmwares.reverse();
							let firmwares = _orderBy(
								this.state.device_lists.firmwares,
								['id'],
								['desc']
							);
							this.firmwareSelectionOptions = firmwares
								.map((firmware) => {
									if (
										this.state.selected_rows[0].type_id ===
										firmware.device_type_id
									) {
										return (
											<AntOption value={firmware.id}>
												{firmware.version}
											</AntOption>
										);
									}
								})
								.filter(Boolean);
						}
					}
				);
				// console.log('selectedRowKeys -> ', selectedRowKeys);
				// console.log('selectedRows -> ', selectedRows);
			},
		};

		let modalCustomBody = third_party_row_value
			? this.getmodalCustomBody(third_party_row_value)
			: '';
		let firmwareModalBody = '';
		firmwareModalBody = this.state.single_update
			? this.state.selected_row.length
				? this.getfirmwareModalBody(this.state.selected_row[0])
				: ''
			: this.state.selected_rows && this.state.selected_rows.length
			? this.getfirmwareModalBody(this.state.selected_rows[0])
			: '';
		let columns_child_list = [
			{
				title: 'QR Code',
				width: '30%',
				key: 'qr_code',
				dataIndex: 'qr_code',
			},
			{
				title: 'Project',
				width: '25%',
				key: 'project',
				dataIndex: 'project',
				align: 'center',
			},
			{
				title: 'Device Type',
				width: '25%',
				key: 'device_type',
				dataIndex: 'device_type',
				align: 'center',
			},
			{
				title: 'Action',
				width: '15%',
				key: 'action',
				dataIndex: 'action',
				align: 'center',
				render: (config, row_value) => (
					<AntTooltip title={this.props.t? this.props.t('configure_device'): "Configure Device"}>
						<span className="device-data-action">
							<PlusCircleOutlined
								className="action-btn-icon"
								onClick={() => {
									this.addDeviceToAssigned(row_value);
								}}
							/>
						</span>
					</AntTooltip>
				),
			},
		];

		let columns_child_add = [
			{
				title: 'QR Code',
				width: '30%',
				key: 'qr_code',
				dataIndex: 'qr_code',
			},
			{
				title: 'Project',
				width: '25%',
				key: 'project',
				dataIndex: 'project',
				align: 'center',
			},
			{
				title: 'Device Type',
				width: '25%',
				key: 'device_type',
				dataIndex: 'device_type',
				align: 'center',
			},
			{
				title: 'Action',
				width: '15%',
				key: 'action',
				dataIndex: 'action',
				align: 'center',
				render: (config, row_value) => (
					<AntTooltip title={this.props.t? this.props.t('configure_device'): "Configure Device"}>
						<span className="device-data-action">
							<MinusCircleOutlined
								className="action-btn-icon red-close"
								onClick={() => {
									this.removeDeviceToAssigned(row_value);
								}}
							/>
						</span>
					</AntTooltip>
				),
			},
		];

		let columns = [];

		if (
			this.props.location &&
			Object.keys(this.props.location).length &&
			this.props.location.pathname &&
			this.props.location.pathname.includes('/datoms-x')
		) {
			if (!this.props.is_application_filter) {
				if (this.props.client_id == 392) {
					columns = [
						{
							title: 'Device QR',
							key: 'qr',
							width: '30%',
							dataIndex: 'qr',
							sorter: (a, b) => a.qr.localeCompare(b.qr),
							render: (qr, row_value) => (
								<div>
									<div className="device-name">
										<AntTooltip title={qr}>{qr}</AntTooltip>
									</div>
									<div>{row_value.type_name}</div>
								</div>
							),
						},
						{
							title: 'Status',
							dataIndex: 'active',
							width: '20%',
							// align: 'center',
							key: 'active',
							sorter: (a, b) => a.active - b.active,
							render: (timestamp) => (
								<AntTooltip title="Last data received">
									{(() => {
										let current_time = moment().unix(),
											less_fifteen_online =
												current_time - timestamp,
											fifteen_minute = 900,
											online_icon_class = '',
											status_text = '';
										if (
											less_fifteen_online <=
											fifteen_minute
										) {
											online_icon_class = ' online';
											status_text = 'Online';
										} else {
											online_icon_class = ' offline';
											status_text = 'Offline';
										}
										// return <span className={'dot' + online_icon_class}></span>;
										return (
											<span
												className={
													'table-status-text' +
													online_icon_class
												}
											>
												{timestamp == '' ||
												timestamp == null ||
												timestamp == undefined ||
												timestamp == 0
													? 'Never'
													: status_text}
											</span>
										);
									})()}
									<span className="date-time">
										{timestamp == '' ||
										timestamp == null ||
										timestamp == undefined ||
										timestamp == 0
											? 'Never'
											: moment
													.unix(timestamp)
													.tz('Asia/Kolkata')
													.format(
														'DD MMM YYYY, HH:mm'
													)}
									</span>
								</AntTooltip>
							),
						},
						{
							title: 'Device Status',
							width: '25%',
							align: 'center',
							key: 'device_status',
							render: (row_data) => (
								<div className="device-data-center">
									{(() => {
										if (row_data.status_code == 7) {
											return <span>Blocked</span>;
										} else {
											return <span>Active</span>;
										}
									})()}
								</div>
							),
						},
						{
							title: 'Description',
							key: 'description',
							width: '25%',
							dataIndex: 'description',
						},
					];
				} else {
					columns = [
						{
							title: this.props.t? this.props.t('device_serial_id'): 'Device Serial ID',
							width: '20%',
							key: 'qr',
							dataIndex: 'qr',
							sorter: (a, b) => a.qr.localeCompare(b.qr),
							render: (qr, row_value) => (
								<div>
									{this.getDeviceDetails(qr, row_value, this.props.t)}
								</div>
							),
						},
						{
							title: this.props.t? this.props.t('created_date'): 'Created Date',
							width: '12%',
							align: 'center',
							key: 'created_at',
							dataIndex: 'created_at',
							sorter: (a, b) => a.created_at - b.created_at,
							render: (created_at, row_value) => (
								<div>
									{!isNaN(created_at) && created_at > 0 ? moment
										.unix(
											created_at
										)
										.tz(
											'Asia/Kolkata'
										)
										.format(
											'DD MMM YYYY, HH:mm'
										) : '-'}
								</div>
							),
						},
						{
							title: this.props.t? this.props.t('firmware'): 'Firmware',
							width: '8%',
							align: 'center',
							key: 'version',
							render: (row_value) => (
								<div>
									{(() => {
										let firm_ver = _filter(
											this.state.device_lists.firmwares,
											{
												device_type_id:
													row_value.type_id,
											}
										);

										if (
											firm_ver.length &&
											row_value.firmware_version != ''
										) {
											if (
												row_value.firmware_version ===
												firm_ver[firm_ver.length - 1]
													.version
											) {
												return (
													<div className="display-flex aln-cntr just-cntr">
														<span>
															{row_value.firmware_version +
																' '}
														</span>
														<img
															src={upto_date}
															height="15"
															width="17"
															className="updated"
														/>
													</div>
												);
											} else {
												return (
													<div className="display-flex aln-cntr just-cntr">
														<span>
															{row_value.firmware_version +
																' '}
														</span>
														{(() => {
															if (
																this.props
																	.application_id &&
																this.props
																	.application_id !==
																	17
															) {
																return (
																	<img
																		src={
																			update
																		}
																		height="15"
																		width="17"
																		className="not-updated"
																		onClick={() =>
																			this.singleDeviceFirmwareUpdate(
																				[
																					row_value,
																				]
																			)
																		}
																	/>
																);
															} else {
																return (
																	<img
																		src={
																			update
																		}
																		height="15"
																		width="17"
																		className="mar-left-10"
																	/>
																);
															}
														})()}
													</div>
												);
											}
										} else {
											return (
												<img
													src={update}
													style={{
														cursor: 'pointer',
													}}
													onClick={() => {
														this.singleDeviceFirmwareUpdate(
															[row_value]
														);
													}}
													height="15"
													width="17"
													className="mar-left-10"
												/>
											);
										}
									})()}
								</div>
							),
						},
						/*{
							title: 'Connectivity Status',
							width: '10%',
							dataIndex: 'active',
							key: 'active',
							sorter: (a, b) => a.active - b.active,
							render: (timestamp, row_value) => (
								<AntTooltip title="Last data received">
									{(() => {
										let status_text = 'Offline',
											online_icon_class = ' offline';
										if (
											row_value.connectivity_status ===
											'online'
										) {
											online_icon_class = ' online';
											status_text = 'Online';
										}
										// return <span className={'dot' + online_icon_class}></span>;
										return (
											<span
												className={
													'table-status-text' +
													online_icon_class
												}
											>
												{timestamp == '' ||
												timestamp == null ||
												timestamp == undefined ||
												timestamp == 0
													? 'Never'
													: status_text}
											</span>
										);
									})()}
									<span className="date-time">
										{timestamp == '' ||
										timestamp == null ||
										timestamp == undefined ||
										timestamp == 0
											? 'Never'
											: moment
													.unix(timestamp)
													.tz('Asia/Kolkata')
													.format('DD MMM YYYY, HH:mm')}
									</span>
								</AntTooltip>
							),
						},*/
						{
							title: this.props.t? this.props.t('online_percentage'): "Online Percentage",
							// title: 'Online Percentage',
							width: '14%',
							key: 'percent',
							sorter: (a, b) => a.percent - b.percent,
							dataIndex: 'percent',
							render: (percent, row_value) => (
								<div className="percentage-holder online-padding-left">
									<AntTooltip title={this.props.t? this.props.t("This month online %"): "This month online %"}>
										<AntProgress
											className="percent-icon"
											type="circle"
											percent={
												row_value.percent &&
												row_value.percent != null
													? row_value.percent
													: 0
											}
											width={35}
											strokeColor="#21A1DB"
										/>
									</AntTooltip>
								</div>
							),
						},
						{
							title: this.props.t? this.props.t('network_status'): 'Network Status',
							width: '10%',
							key: 'network_percent',
							align: 'center',
							dataIndex: 'network_percent',
							render: (percent, row_value) => (
								<div className="percentage-holder">
									{/*<AntTooltip title="Data Availability">
										<AntProgress className="percent-icon" type="circle" percent={row_value.availability && row_value.availability != null ? row_value.availability : 0} width={35} strokeColor="#139547"/>
									</AntTooltip>*/}
									<div className="percent-icon">
										{(() => {
											if (row_value.device_config) {
												let simNo = '';
												if (row_value.device_sim_slot) {
													simNo =
														'Sim ' +
														row_value.device_sim_slot;
												}
												if (
													row_value.device_config
														.device_modem_type ==
													'gprs'
												) {
													let gprsIcon = GprsSignalRounded;

													let networkStrengthText =
														'';
													if (
														row_value.connectivity_status ===
														'online'
													) {
														if (
															row_value.device_signal_strength >
															20
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Excellent)';
															gprsIcon = GprsSignalRounded5;
														} else if (
															row_value.device_signal_strength >=
																13 &&
															row_value.device_signal_strength <=
																20
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Good)';
															gprsIcon = GprsSignalRounded4;
														} else if (
															row_value.device_signal_strength >=
																6 &&
															row_value.device_signal_strength <=
																12
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Poor)';
															gprsIcon = GprsSignalRounded2;
														} else if (
															row_value.device_signal_strength <
															6
														) {
															networkStrengthText =
																'No Signal';
														}
													} else {
														if (
															row_value.device_signal_strength >
															20
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
														} else if (
															row_value.device_signal_strength >=
																13 &&
															row_value.device_signal_strength <=
																20
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('good') : "Good"})`;
														} else if (
															row_value.device_signal_strength >=
																6 &&
															row_value.device_signal_strength <=
																12
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('poor') : "Poor"})`;
														} else if (
															row_value.device_signal_strength <
															6
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;
														}
													}

													return (
														<div className="display-flex">
															<AntTooltip
																title={
																	networkStrengthText
																}
															>
																<img
																	className="mar-right-10"
																	src={
																		gprsIcon
																	}
																	height={20}
																	width={20}
																/>
															</AntTooltip>
															<div>
																<div>GPRS</div>
																<div>
																	{simNo}
																</div>
															</div>
														</div>
													);
												} else if (
													row_value.device_config
														.device_modem_type ==
													'wifi'
												) {
													let wifiIcon = WifiSignalRounded;

													let networkStrengthText =
														'';
													if (
														row_value.connectivity_status ===
														'online'
													) {
														if (
															row_value.device_signal_strength >
															20
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Excellent)';
															wifiIcon = WifiSignalRounded3;
														} else if (
															row_value.device_signal_strength >=
																13 &&
															row_value.device_signal_strength <=
																20
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Good)';
															wifiIcon = WifiSignalRounded2;
														} else if (
															row_value.device_signal_strength >=
																6 &&
															row_value.device_signal_strength <=
																12
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Poor)';
															wifiIcon = WifiSignalRounded1;
														} else if (
															row_value.device_signal_strength <
															6
														) {
															networkStrengthText =
																'No Signal';
														}
													} else {
														if (
															row_value.device_signal_strength >
															20
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
														} else if (
															row_value.device_signal_strength >=
																13 &&
															row_value.device_signal_strength <=
																20
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('good') : "Good"})`;
														} else if (
															row_value.device_signal_strength >=
																6 &&
															row_value.device_signal_strength <=
																12
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('poor') : "Poor"})`;
														} else if (
															row_value.device_signal_strength <
															6
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;
														}
													}
													return (
														<div>
															<AntTooltip
																title={
																	networkStrengthText
																}
															>
																<img
																	className="mar-right-10"
																	src={
																		wifiIcon
																	}
																	height={20}
																	width={20}
																/>
															</AntTooltip>
															<span>Wi-Fi</span>
														</div>
													);
												} else if (
													row_value.device_config
														.device_modem_type ==
													'ethernet'
												) {
													if (
														row_value.connectivity_status ===
														'online'
													) {
														return (
															<div>
																<img
																	className="mar-right-10"
																	src={
																		ethernet
																	}
																	height={20}
																	width={20}
																/>
																<span>
																	Ethernet
																</span>
															</div>
														);
													} else {
														return (
															<div>
																<img
																	className="mar-right-10"
																	src={
																		ethernet_inactive
																	}
																	height={20}
																	width={20}
																/>
																<span>
																	Ethernet
																</span>
															</div>
														);
													}
												}
											}
										})()}
									</div>
								</div>
							),
						},
						{
							title: this.props.t? this.props.t('power_status'): 'Power Status',
							width: '10%',
							key: 'power_status',
							align: 'center',
							dataIndex: 'power_status',
							render: (power_status, row_value) => (
								<div className="percentage-holder power-container">
									<div className="icon-holder">
										{(() => {
											if (
												row_value.device_power_status !==
													null &&
												row_value.device_power_status !==
													false
											) {
												let icon = power_inactive,
													text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('On'): "Last Reported Status: On"}`;
												if (
													row_value.connectivity_status ===
													'online'
												) {
													icon = power_on;
													text = 'Power Status: On';
												}
												return (
													<AntTooltip title={text}>
														<img
															src={icon}
															height={22}
															width={22}
														/>
													</AntTooltip>
												);
											} else if (
												row_value.device_power_status ===
												false
											) {
												let icon = power_inactive,
													text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('Off'): "Last Reported Status: Off"}`;
												if (
													row_value.connectivity_status ===
													'online'
												) {
													icon = power_off;
													text = 'Power Status: Off';
												}
												return (
													<AntTooltip title={text}>
														<img
															src={icon}
															height={22}
															width={22}
														/>
													</AntTooltip>
												);
											} else {
												let text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('unknown'): "Last Reported Status: Unknown"}`;
												if (
													row_value.connectivity_status ===
													'online'
												) {
													text =
														'Power Status: Unknown';
												}
												return (
													<AntTooltip title={text}>
														<img
															src={power_inactive}
															height={22}
															width={22}
														/>
													</AntTooltip>
												);
											}
										})()}
									</div>
									<div className="battery-container">
										{(() => {
											if (
												row_value.device_battery_percent
											) {
												return (
													<div className="battery mar-left-5">
														<div className="minus-icon">
															-
														</div>
														<div className="bat-body">
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >
																		0 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		20 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		30 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		40 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		50 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		60 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		70 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		80 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		90 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		100 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
														</div>
														<div className="bat-top"></div>
														<div className="plus-icon">
															+
														</div>
													</div>
												);
											}
										})()}
										<div className="bat-charge-details">
											<div className="icon-holder">
												{(() => {
													if (
														row_value.device_battery_percent !==
															null &&
														row_value.device_battery_percent !==
															false &&
														row_value.connectivity_status ===
															'online'
													) {
														return (
															<span>
																{'(' +
																	(row_value.device_battery_percent !==
																	0
																		? row_value.device_battery_percent.toFixed(
																				2
																		  )
																		: 0) +
																	'%)'}
															</span>
														);
													}
												})()}
											</div>
											<div className="icon-holder mar-left-5">
												{(() => {
													if (
														row_value.device_charging_status !==
															null &&
														row_value.device_battery_percent !==
															null
													) {
														if (
															row_value.device_charging_status &&
															row_value.connectivity_status ===
																'online'
														) {
															return (
																<img
																	src={
																		lighting_active
																	}
																	height={15}
																	width={15}
																/>
															);
														} else {
															return (
																<img
																	src={
																		lighting_inactive
																	}
																	height={15}
																	width={15}
																/>
															);
														}
													}
												})()}
											</div>
										</div>
									</div>
								</div>
							),
						},
						{
							title: 'SIM',
							width: '6%',
							key: 'sim_details',
							align: 'center',
							dataIndex: 'sim_details',
							render: (sim_details, row_value) =>
								this.renderSimDetails(sim_details, row_value),
						},
						/*{
							title: 'Debug',
							width: '10%',
							align: 'center',
							key: 'health',
							render: (config, row_data) => (
								<div>
									<div className="icon-holder">
										<AntTooltip title="Open Debug">
											<img
												src={debug}
												className="cursor-pointer"
												height={28}
												width={32}
												onClick={() =>
													this.openDeviceDebug(row_data)
												}
											/>
										</AntTooltip>
									</div>
									<div className="device-errors-tags">
										{(() => {
											let errors = [],
												extraTypes = '';
											if (
												row_data.device_error_list &&
												row_data.device_error_list.length
											) {
												row_data.device_error_list.map(
													(errorType, index) => {
														if (index === 0) {
															errors.push(
																<AntTooltip
																	title={moment
																		.unix(
																			row_data.timestamp
																		)
																		.tz(
																			'Asia/Kolkata'
																		)
																		.format(
																			'DD MMM YYYY HH:mm'
																		)}
																>
																	<span className="mar-lt-5">
																		{errorType +
																			', '}
																	</span>
																</AntTooltip>
															);
														} else if (index === 1) {
															errors.push(
																<AntTooltip
																	title={moment
																		.unix(
																			row_data.timestamp
																		)
																		.tz(
																			'Asia/Kolkata'
																		)
																		.format(
																			'DD MMM YYYY HH:mm'
																		)}
																>
																	<span className="mar-lt-5">
																		{errorType}
																	</span>
																</AntTooltip>
															);
														} else {
															if (
																index ===
																row_data
																	.device_error_list
																	.length -
																	1
															) {
																extraTypes = extraTypes.concat(
																	errorType +
																		' - ' +
																		moment
																			.unix(
																				row_data.timestamp
																			)
																			.tz(
																				'Asia/Kolkata'
																			)
																			.format(
																				'DD MMM YYYY HH:mm'
																			)
																);
															} else {
																extraTypes = extraTypes.concat(
																	errorType +
																		' - ' +
																		moment
																			.unix(
																				row_data.timestamp
																			)
																			.tz(
																				'Asia/Kolkata'
																			)
																			.format(
																				'DD MMM YYYY HH:mm'
																			) +
																		', '
																);
															}
														}
													}
												);

												if (
													row_data.device_error_list
														.length > 2
												) {
													errors.push(
														<AntTooltip
															title={extraTypes}
														>
															<AntTag
																className="mar-lt-5"
																color="red"
															>
																{'+ ' +
																	(row_data
																		.device_error_list
																		.length -
																		2) +
																	' more'}
															</AntTag>
														</AntTooltip>
													);
												}
											}
											return errors;
										})()}
									</div>
								</div>
							),
						},*/
						{
							title: 'Debug & Configure',
							width: '10%',
							align: 'center',
							key: 'configure',
							render: (config, row_data) => (
								<div className="display-flex just-cntr aln-cntr">
									<div className="icon-holder mar-right-10">
										<AntTooltip title={this.props.t? this.props.t('debug'): "Debug"}>
											<img
												src={debug}
												className="cursor-pointer"
												height={28}
												width={32}
												onClick={() =>
													this.openDeviceDebug(
														row_data
													)
												}
											/>
										</AntTooltip>
									</div>
									<div className="icon-holder mar-right-10">
										<AntTooltip title="Raw log">
											<img
												src={raw_log_icon}
												className="cursor-pointer"
												height={28}
												width={32}
												onClick={() =>
													this.openDeviceDebug(
														row_data,
														true
													)
												}
											/>
										</AntTooltip>
									</div>
									{(() => {
										if (
											/*this.props.application_id !== 17*/ true
										) {
											if (
												/*row_data.application_id == 6*/ true
											) {
												/*let routeLink =
											this.iotBasePath +
											'/enterprise/' +
											this.props.client_id +
											this.platform_slug +
											'/devices/unassigned/' +
											(config.device_config &&
											config.device_config.station_id &&
											config.device_config.station_id
												.length
												? config.device_config
														.station_id[0]
												: row_data.vendor_id
												? row_data.vendor_id
												: 0) +
											'/configuration?app_id=' +
											row_data.application_id;*/
												let routeLink =
													this.iotBasePath +
													'/enterprise/' +
													this.props.client_id +
													this.platform_slug +
													'/devices/unassigned/' +
													row_data.id +
													'/communication?type_id=' +
													row_data.type_id;
												if (
													this.props
														.is_application_filter
												) {
													/*routeLink =
												this.platform_slug +
												'/customer-management/' +
												this.props.customer_id +
												'/applications/' +
												this.state.selected_app +
												'/devices/' +
												(config.device_config &&
												config.device_config
													.station_id &&
												config.device_config.station_id
													.length
													? config.device_config
															.station_id[0]
													: row_data.vendor_id
													? row_data.vendor_id
													: 0) +
												'/configuration?app_id=' +
												row_data.application_id;*/
													routeLink =
														this.iotBasePath +
														'/enterprise/' +
														this.props.client_id +
														this.platform_slug +
														'/customer-management/' +
														this.props.customer_id +
														'/applications/' +
														this.state
															.selected_app +
														'/devices/' +
														row_data.id +
														'/communication?type_id=' +
														row_data.type_id;
												}
												if (
													row_data.type_id == 11 ||
													row_data.type_id == 12 ||
													row_data.type_id == 51
												) {
													return (
														<AntTooltip title={this.props.t? this.props.t('configure_device'): "Configure Device"}>
															<img
																className="mar-right-10 cursor-pointer"
																src={
																	configuration
																}
																height={28}
																width={32}
																onClick={() =>
																	this.openConfiguration(
																		routeLink
																	)
																}
															/>
														</AntTooltip>
													);
												}
											} else {
												if (
													row_data.type_id == 11 ||
													row_data.type_id == 12 ||
													row_data.type_id == 51
												) {
													return (
														<AntTooltip title="Configure Device">
															<img
																className="mar-right-10 cursor-block"
																src={
																	configuration
																}
																height={28}
																width={32}
															/>
														</AntTooltip>
													);
												}
											}
										}
									})()}
									{(() => {
										if (this.props.application_id == 12 || (this.props.enabled_features && this.props.enabled_features.includes('DeviceManagement:CustomCommand'))) {
											return (
												<AntTooltip title="Custom Command">
													<img
														src={custom_command}
														className="cursor-pointer"
														height={28}
														width={32}
														onClick={() =>
															this.openCustomCommand(
																row_data.id,
																row_data
															)
														}
													/>
												</AntTooltip>
											);
										}
									})()}
								</div>
							),
						},
						{
							title: 'Device Status',
							width: '10%',
							align: 'center',
							key: 'device_status',
							render: (row_data) => (
								<div>
									<AntTooltip
										title={
											row_data.status_code == 7
												? 'Inactive'
												: 'Active'
										}
									>
										<AntSwitch
											size="medium"
											checkedChildren="Active"
											unCheckedChildren="Inactive"
											checked={
												row_data.status_code == 7
													? false
													: true
											}
											onChange={(checked, event) =>
												this.onUpdateStatus(
													checked,
													event,
													row_data
												)
											}
										></AntSwitch>
									</AntTooltip>
								</div>
							),
						},
						/*{
							title: 'Edit',
							width: '5%',
							align: 'center',
							key: 'device_status',
							render: (row_data) => (
								<span>
									{(() => {
										if (row_data.is_editable) {
											return (
												<EditOutlined
													className="action-icon"
													onClick={() =>
														this.openEditDrawer(
															row_data
														)
													}
												/>
											);
										} else {
											return (
												<EditOutlined className="block-icon" />
											);
										}
									})()}
								</span>
							),
						},*/
					];
				}
			} else {
				if (this.props.customer_id == 392) {
					columns = [
						{
							title: 'Device QR',
							key: 'qr',
							width: '30%',
							dataIndex: 'qr',
							sorter: (a, b) => a.qr.localeCompare(b.qr),
							render: (qr, row_value) => (
								<div>
									<div className="device-name">
										<AntTooltip title={qr}>{qr}</AntTooltip>
									</div>
									<div>{row_value.type_name}</div>
								</div>
							),
						},
						{
							title: 'Status',
							dataIndex: 'active',
							width: '20%',
							// align: 'center',
							key: 'active',
							sorter: (a, b) => a.active - b.active,
							render: (timestamp) => (
								<AntTooltip title="Last data received">
									{(() => {
										let current_time = moment().unix(),
											less_fifteen_online =
												current_time - timestamp,
											fifteen_minute = 900,
											online_icon_class = '',
											status_text = '';
										if (
											less_fifteen_online <=
											fifteen_minute
										) {
											online_icon_class = ' online';
											status_text = 'Online';
										} else {
											online_icon_class = ' offline';
											status_text = 'Offline';
										}
										// return <span className={'dot' + online_icon_class}></span>;
										return (
											<span
												className={
													'table-status-text' +
													online_icon_class
												}
											>
												{timestamp == '' ||
												timestamp == null ||
												timestamp == undefined ||
												timestamp == 0
													? 'Never'
													: status_text}
											</span>
										);
									})()}
									<span className="date-time">
										{timestamp == '' ||
										timestamp == null ||
										timestamp == undefined ||
										timestamp == 0
											? 'Never'
											: moment
													.unix(timestamp)
													.tz('Asia/Kolkata')
													.format(
														'DD MMM YYYY, HH:mm'
													)}
									</span>
								</AntTooltip>
							),
						},
						{
							title: 'Device Status',
							width: '25%',
							align: 'center',
							key: 'device_status',
							render: (row_data) => (
								<div className="device-data-center">
									{(() => {
										if (row_data.status_code == 7) {
											return <span>Blocked</span>;
										} else {
											return <span>Active</span>;
										}
									})()}
								</div>
							),
						},
						{
							title: 'Description',
							key: 'description',
							width: '25%',
							dataIndex: 'description',
						},
					];
				} else {
					columns = [
						{
							title: this.props.t? this.props.t('device_serial_id'): 'Device Serial ID',
							width: '20%',
							key: 'qr',
							dataIndex: 'qr',
							sorter: (a, b) => a.qr.localeCompare(b.qr),
							render: (qr, row_value) => (
								<div>
									{this.getDeviceDetails(qr, row_value, this.props.t)}
								</div>
							),
						},
						{
							title: this.props.t? this.props.t('created_date'): 'Created Date',
							width: '12%',
							align: 'center',
							key: 'created_at',
							dataIndex: 'created_at',
							sorter: (a, b) => a.created_at - b.created_at,
							render: (created_at, row_value) => (
								<div>
									{!isNaN(created_at) && created_at > 0 ? moment
										.unix(
											created_at
										)
										.tz(
											'Asia/Kolkata'
										)
										.format(
											'DD MMM YYYY, HH:mm'
										) : '-'}
								</div>
							),
						},
						{
							title: this.props.t? this.props.t('firmware'): 'Firmware',
							width: '9%',
							align: 'center',
							key: 'version',
							render: (row_value) => (
								<div>
									{(() => {
										let firm_ver = _filter(
											this.state.device_lists.firmwares,
											{
												device_type_id:
													row_value.type_id,
											}
										);

										if (
											firm_ver.length &&
											row_value.firmware_version != ''
										) {
											if (
												row_value.firmware_version ===
												firm_ver[firm_ver.length - 1]
													.version
											) {
												return (
													<div className="display-flex aln-cntr just-cntr">
														<span>
															{row_value.firmware_version +
																' '}
														</span>
														<img
															src={upto_date}
															height="15"
															width="17"
															className="updated"
														/>
													</div>
												);
											} else {
												return (
													<div className="display-flex aln-cntr just-cntr">
														<span>
															{row_value.firmware_version +
																' '}
														</span>
														{(() => {
															if (
																this.props
																	.application_id &&
																this.props
																	.application_id !==
																	17
															) {
																return (
																	<img
																		src={
																			update
																		}
																		height="15"
																		width="17"
																		className="not-updated"
																		onClick={() =>
																			this.singleDeviceFirmwareUpdate(
																				[
																					row_value,
																				]
																			)
																		}
																	/>
																);
															} else {
																return (
																	<img
																		src={
																			update
																		}
																		height="15"
																		width="17"
																		className="mar-left-10"
																	/>
																);
															}
														})()}
													</div>
												);
											}
										} else {
											return '-';
										}
									})()}
								</div>
							),
						},
						/*{
							title: 'Connectivity Status',
							width: '15%',
							dataIndex: 'active',
							key: 'active',
							sorter: (a, b) => a.active - b.active,
							render: (timestamp, row_value) => (
								<AntTooltip title="Last data received">
									{(() => {
										let status_text = 'Offline',
											online_icon_class = ' offline';
										if (
											row_value.connectivity_status ===
											'online'
										) {
											online_icon_class = ' online';
											status_text = 'Online';
										}
										// return <span className={'dot' + online_icon_class}></span>;
										return (
											<span
												className={
													'table-status-text' +
													online_icon_class
												}
											>
												{timestamp == '' ||
												timestamp == null ||
												timestamp == undefined ||
												timestamp == 0
													? 'Never'
													: status_text}
											</span>
										);
									})()}
									<span className="date-time">
										{timestamp == '' ||
										timestamp == null ||
										timestamp == undefined ||
										timestamp == 0
											? 'Never'
											: moment
													.unix(timestamp)
													.tz('Asia/Kolkata')
													.format(
														'DD MMM YYYY, HH:mm'
													)}
									</span>
								</AntTooltip>
							),
						},*/
						{
							// title: 'Online Percentage',
							title: this.props.t? this.props.t('online_percentage'): "Online Percentage",
							width: '13%',
							key: 'percent',
							sorter: (a, b) => a.percent - b.percent,
							dataIndex: 'percent',
							render: (percent, row_value) => (
								<div className="percentage-holder online-padding-left">
									<AntTooltip title={this.props.t? this.props.t("This month online %"): "This month online %"}>
										<AntProgress
											className="percent-icon"
											type="circle"
											percent={
												row_value.percent &&
												row_value.percent != null
													? row_value.percent
													: 0
											}
											width={35}
											strokeColor="#21A1DB"
										/>
									</AntTooltip>
								</div>
							),
						},
						{
							title: this.props.t? this.props.t('network_status'): 'Network Status',
							width: '10%',
							key: 'network_percent',
							align: 'center',
							dataIndex: 'network_percent',
							render: (percent, row_value) => (
								<div className="percentage-holder">
									{/*<AntTooltip title="Data Availability">
										<AntProgress className="percent-icon" type="circle" percent={row_value.availability && row_value.availability != null ? row_value.availability : 0} width={35} strokeColor="#139547"/>
									</AntTooltip>*/}
									<div className="percent-icon">
										{(() => {
											if (row_value.device_config) {
												let simNo = '';
												if (row_value.device_sim_slot) {
													simNo =
														'Sim ' +
														row_value.device_sim_slot;
												}
												if (
													row_value.device_config
														.device_modem_type ==
													'gprs'
												) {
													let gprsIcon = GprsSignalRounded;

													let networkStrengthText =
														'';
													if (
														row_value.connectivity_status ===
														'online'
													) {
														if (
															row_value.device_signal_strength >
															20
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Excellent)';
															gprsIcon = GprsSignalRounded5;
														} else if (
															row_value.device_signal_strength >=
																13 &&
															row_value.device_signal_strength <=
																20
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Good)';
															gprsIcon = GprsSignalRounded4;
														} else if (
															row_value.device_signal_strength >=
																6 &&
															row_value.device_signal_strength <=
																12
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Poor)';
															gprsIcon = GprsSignalRounded2;
														} else if (
															row_value.device_signal_strength <
															6
														) {
															networkStrengthText =
																'No Signal';
														}
													} else {
														if (
															row_value.device_signal_strength >
															20
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
														} else if (
															row_value.device_signal_strength >=
																13 &&
															row_value.device_signal_strength <=
																20
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('good') : "Good"})`;
														} else if (
															row_value.device_signal_strength >=
																6 &&
															row_value.device_signal_strength <=
																12
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('poor') : "Poor"})`;
														} else if (
															row_value.device_signal_strength <
															6
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;
														}
													}

													return (
														<div className="display-flex">
															<AntTooltip
																title={
																	networkStrengthText
																}
															>
																<img
																	className="mar-right-10"
																	src={
																		gprsIcon
																	}
																	height={20}
																	width={20}
																/>
															</AntTooltip>
															<div>
																<div>GPRS</div>
																<div>
																	{simNo}
																</div>
															</div>
														</div>
													);
												} else if (
													row_value.device_config
														.device_modem_type ==
													'wifi'
												) {
													let wifiIcon = WifiSignalRounded;

													let networkStrengthText =
														'';
													if (
														row_value.connectivity_status ===
														'online'
													) {
														if (
															row_value.device_signal_strength >
															20
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Excellent)';
															wifiIcon = WifiSignalRounded3;
														} else if (
															row_value.device_signal_strength >=
																13 &&
															row_value.device_signal_strength <=
																20
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Good)';
															wifiIcon = WifiSignalRounded2;
														} else if (
															row_value.device_signal_strength >=
																6 &&
															row_value.device_signal_strength <=
																12
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Poor)';
															wifiIcon = WifiSignalRounded1;
														} else if (
															row_value.device_signal_strength <
															6
														) {
															networkStrengthText =
																'No Signal';
														}
													} else {
														if (
															row_value.device_signal_strength >
															20
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
														} else if (
															row_value.device_signal_strength >=
																13 &&
															row_value.device_signal_strength <=
																20
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('good') : "Good"})`;
														} else if (
															row_value.device_signal_strength >=
																6 &&
															row_value.device_signal_strength <=
																12
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('poor') : "Poor"})`;
														} else if (
															row_value.device_signal_strength <
															6
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;
														}
													}
													return (
														<div>
															<AntTooltip
																title={
																	networkStrengthText
																}
															>
																<img
																	className="mar-right-10"
																	src={
																		wifiIcon
																	}
																	height={20}
																	width={20}
																/>
															</AntTooltip>
															<span>Wi-Fi</span>
														</div>
													);
												} else if (
													row_value.device_config
														.device_modem_type ==
													'ethernet'
												) {
													if (
														row_value.connectivity_status ===
														'online'
													) {
														return (
															<div>
																<img
																	className="mar-right-10"
																	src={
																		ethernet
																	}
																	height={20}
																	width={20}
																/>
																<span>
																	Ethernet
																</span>
															</div>
														);
													} else {
														return (
															<div>
																<img
																	className="mar-right-10"
																	src={
																		ethernet_inactive
																	}
																	height={20}
																	width={20}
																/>
																<span>
																	Ethernet
																</span>
															</div>
														);
													}
												}
											}
										})()}
									</div>
								</div>
							),
						},
						{
							title: this.props.t? this.props.t('power_status'): 'Power Status',
							width: '10%',
							key: 'power_status',
							align: 'center',
							dataIndex: 'power_status',
							render: (power_status, row_value) => (
								<div className="percentage-holder power-container">
									<div className="icon-holder">
										{(() => {
											if (
												row_value.device_power_status !==
													null &&
												row_value.device_power_status !==
													false
											) {
												let icon = power_inactive,
													text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('On'): "Last Reported Status: On"}`;
												if (
													row_value.connectivity_status ===
													'online'
												) {
													icon = power_on;
													text = 'Power Status: On';
												}
												return (
													<AntTooltip title={text}>
														<img
															src={icon}
															height={22}
															width={22}
														/>
													</AntTooltip>
												);
											} else if (
												row_value.device_power_status ===
												false
											) {
												let icon = power_inactive,
													text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('Off'): "Last Reported Status: Off"}`;
												if (
													row_value.connectivity_status ===
													'online'
												) {
													icon = power_off;
													text = 'Power Status: Off';
												}
												return (
													<AntTooltip title={text}>
														<img
															src={icon}
															height={22}
															width={22}
														/>
													</AntTooltip>
												);
											} else {
												let text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('unknown'): "Last Reported Status: Unknown"}`;
												if (
													row_value.connectivity_status ===
													'online'
												) {
													text =
														'Power Status: Unknown';
												}
												return (
													<AntTooltip title={text}>
														<img
															src={power_inactive}
															height={22}
															width={22}
														/>
													</AntTooltip>
												);
											}
										})()}
									</div>
									<div className="battery-container">
										{(() => {
											if (
												row_value.device_battery_percent
											) {
												return (
													<div className="battery mar-left-5">
														<div className="minus-icon">
															-
														</div>
														<div className="bat-body">
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >
																		0 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		20 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		30 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		40 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		50 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		60 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		70 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		80 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		90 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		100 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
														</div>
														<div className="bat-top"></div>
														<div className="plus-icon">
															+
														</div>
													</div>
												);
											}
										})()}
										<div className="bat-charge-details">
											<div className="icon-holder">
												{(() => {
													if (
														row_value.device_battery_percent !==
															null &&
														row_value.device_battery_percent !==
															false &&
														row_value.connectivity_status ===
															'online'
													) {
														return (
															<span>
																{'(' +
																	(row_value.device_battery_percent !==
																	0
																		? row_value.device_battery_percent.toFixed(
																				2
																		  )
																		: 0) +
																	'%)'}
															</span>
														);
													}
												})()}
											</div>
											<div className="icon-holder mar-left-5">
												{(() => {
													if (
														row_value.device_charging_status !==
															null &&
														row_value.device_battery_percent !==
															null
													) {
														if (
															row_value.device_charging_status &&
															row_value.connectivity_status ===
																'online'
														) {
															return (
																<img
																	src={
																		lighting_active
																	}
																	height={15}
																	width={15}
																/>
															);
														} else {
															return (
																<img
																	src={
																		lighting_inactive
																	}
																	height={15}
																	width={15}
																/>
															);
														}
													}
												})()}
											</div>
										</div>
									</div>
								</div>
							),
						},
						{
							title: 'SIM',
							width: '6%',
							key: 'sim_details',
							align: 'center',
							dataIndex: 'sim_details',
							render: (sim_details, row_value) =>
								this.renderSimDetails(sim_details, row_value),
						},
						/*{
							title: 'Debug',
							width: '10%',
							align: 'center',
							key: 'health',
							render: (config, row_data) => (
								<div>
									<div className="icon-holder">
										<AntTooltip title="Open Debug">
											<img
												src={debug}
												className="cursor-pointer"
												height={28}
												width={32}
												onClick={() =>
													this.openDeviceDebug(row_data)
												}
											/>
										</AntTooltip>
									</div>
									<div className="device-errors-tags">
										{(() => {
											let errors = [],
												extraTypes = '';
											if (
												row_data.device_error_list &&
												row_data.device_error_list.length
											) {
												row_data.device_error_list.map(
													(errorType, index) => {
														if (index === 0) {
															errors.push(
																<AntTooltip
																	title={moment
																		.unix(
																			row_data.timestamp
																		)
																		.tz(
																			'Asia/Kolkata'
																		)
																		.format(
																			'DD MMM YYYY HH:mm'
																		)}
																>
																	<span className="mar-lt-5">
																		{errorType +
																			', '}
																	</span>
																</AntTooltip>
															);
														} else if (index === 1) {
															errors.push(
																<AntTooltip
																	title={moment
																		.unix(
																			row_data.timestamp
																		)
																		.tz(
																			'Asia/Kolkata'
																		)
																		.format(
																			'DD MMM YYYY HH:mm'
																		)}
																>
																	<span className="mar-lt-5">
																		{errorType}
																	</span>
																</AntTooltip>
															);
														} else {
															if (
																index ===
																row_data
																	.device_error_list
																	.length -
																	1
															) {
																extraTypes = extraTypes.concat(
																	errorType +
																		' - ' +
																		moment
																			.unix(
																				row_data.timestamp
																			)
																			.tz(
																				'Asia/Kolkata'
																			)
																			.format(
																				'DD MMM YYYY HH:mm'
																			)
																);
															} else {
																extraTypes = extraTypes.concat(
																	errorType +
																		' - ' +
																		moment
																			.unix(
																				row_data.timestamp
																			)
																			.tz(
																				'Asia/Kolkata'
																			)
																			.format(
																				'DD MMM YYYY HH:mm'
																			) +
																		', '
																);
															}
														}
													}
												);

												if (
													row_data.device_error_list
														.length > 2
												) {
													errors.push(
														<AntTooltip
															title={extraTypes}
														>
															<AntTag
																className="mar-lt-5"
																color="red"
															>
																{'+ ' +
																	(row_data
																		.device_error_list
																		.length -
																		2) +
																	' more'}
															</AntTag>
														</AntTooltip>
													);
												}
											}
											return errors;
										})()}
									</div>
								</div>
							),
						},*/
						{
							title: 'Debug & Configure',
							width: '10%',
							align: 'center',
							key: 'configure',
							render: (config, row_data) => (
								<div className="display-flex just-cntr aln-cntr">
									<div className="icon-holder mar-right-10">
										<AntTooltip title={this.props.t? this.props.t('debug'): "Debug"}>
											<img
												src={debug}
												className="cursor-pointer"
												height={28}
												width={32}
												onClick={() =>
													this.openDeviceDebug(
														row_data
													)
												}
											/>
										</AntTooltip>
									</div>
									<div className="icon-holder mar-right-10">
										<AntTooltip title="Raw log">
											<img
												src={raw_log_icon}
												className="cursor-pointer"
												height={28}
												width={32}
												onClick={() =>
													this.openDeviceDebug(
														row_data,
														true
													)
												}
											/>
										</AntTooltip>
									</div>
									{(() => {
										if (
											/*this.props.application_id !== 17*/ true
										) {
											if (
												/*row_data.application_id == 6*/ true
											) {
												/*let routeLink =
											this.iotBasePath +
											'/enterprise/' +
											this.props.client_id +
											this.platform_slug +
											'/devices/unassigned/' +
											(config.device_config &&
											config.device_config.station_id &&
											config.device_config.station_id
												.length
												? config.device_config
														.station_id[0]
												: row_data.vendor_id
												? row_data.vendor_id
												: 0) +
											'/configuration?app_id=' +
											row_data.application_id;*/
												let routeLink =
													this.iotBasePath +
													'/enterprise/' +
													this.props.client_id +
													this.platform_slug +
													'/devices/unassigned/' +
													row_data.id +
													'/communication?type_id=' +
													row_data.type_id;
												if (
													this.props
														.is_application_filter
												) {
													/*routeLink =
												this.platform_slug +
												'/customer-management/' +
												this.props.customer_id +
												'/applications/' +
												this.state.selected_app +
												'/devices/' +
												(config.device_config &&
												config.device_config
													.station_id &&
												config.device_config.station_id
													.length
													? config.device_config
															.station_id[0]
													: row_data.vendor_id
													? row_data.vendor_id
													: 0) +
												'/configuration?app_id=' +
												row_data.application_id;*/
													routeLink =
														this.iotBasePath +
														'/enterprise/' +
														this.props.client_id +
														this.platform_slug +
														'/customer-management/' +
														this.props.customer_id +
														'/applications/' +
														this.state
															.selected_app +
														'/devices/' +
														row_data.id +
														'/communication?type_id=' +
														row_data.type_id;
												}
												if (
													row_data.type_id == 11 ||
													row_data.type_id == 12 ||
													row_data.type_id == 51
												) {
													return (
														<AntTooltip title={this.props.t? this.props.t('configure_device'): "Configure Device"}>
															<img
																className="mar-right-10 cursor-pointer"
																src={
																	configuration
																}
																height={28}
																width={32}
																onClick={() =>
																	this.openConfiguration(
																		routeLink
																	)
																}
															/>
														</AntTooltip>
													);
												}
											} else {
												if (
													row_data.type_id == 11 ||
													row_data.type_id == 12 ||
													row_data.type_id == 51
												) {
													return (
														<AntTooltip title="Configure Device">
															<img
																className="mar-right-10 cursor-block"
																src={
																	configuration
																}
																height={28}
																width={32}
															/>
														</AntTooltip>
													);
												}
											}
										}
									})()}
									{(() => {
										if (this.props.application_id == 12 || (this.props.enabled_features && this.props.enabled_features.includes('DeviceManagement:CustomCommand'))) {
											return (
												<AntTooltip title="Custom Command">
													<img
														src={custom_command}
														className="cursor-pointer"
														height={28}
														width={32}
														onClick={() =>
															this.openCustomCommand(
																row_data.id,
																row_data
															)
														}
													/>
												</AntTooltip>
											);
										}
									})()}
								</div>
							),
						},
						{
							title: 'Device Status',
							width: '10%',
							align: 'center',
							key: 'device_status',
							render: (row_data) => (
								<div>
									<AntTooltip
										title={
											row_data.status_code == 7
												? 'Inactive'
												: 'Active'
										}
									>
										<AntSwitch
											size="medium"
											checkedChildren="Active"
											unCheckedChildren="Inactive"
											checked={
												row_data.status_code == 7
													? false
													: true
											}
											onChange={(checked, event) =>
												this.onUpdateStatus(
													checked,
													event,
													row_data
												)
											}
										></AntSwitch>
									</AntTooltip>
								</div>
							),
						},
						/*{
							title: 'Edit',
							width: '5%',
							align: 'center',
							key: 'device_status',
							render: (row_data) => (
								<span>
									{(() => {
										if (row_data.is_editable) {
											return (
												<EditOutlined
													className="action-icon"
													onClick={() =>
														this.openEditDrawer(
															row_data
														)
													}
												/>
											);
										} else {
											return (
												<EditOutlined className="block-icon" />
											);
										}
									})()}
								</span>
							),
						},*/
					];
				}
			}
		} else if (
			this.props.location &&
			Object.keys(this.props.location).length &&
			this.props.location.pathname &&
			this.props.location.pathname.includes('/iot-platform')
		) {
			if (!this.props.is_application_filter) {
				if (this.props.client_id == 392) {
					columns = [
						{
							title: 'Device QR',
							key: 'qr',
							width: '30%',
							dataIndex: 'qr',
							sorter: (a, b) => a.qr.localeCompare(b.qr),
							render: (qr, row_value) => (
								<div>
									<div className="device-name">
										<AntTooltip title={qr}>{qr}</AntTooltip>
									</div>
									<div>{row_value.type_name}</div>
								</div>
							),
						},
						{
							title: 'Status',
							dataIndex: 'active',
							width: '20%',
							// align: 'center',
							key: 'active',
							sorter: (a, b) => a.active - b.active,
							render: (timestamp) => (
								<AntTooltip title="Last data received">
									{(() => {
										let current_time = moment().unix(),
											less_fifteen_online =
												current_time - timestamp,
											fifteen_minute = 900,
											online_icon_class = '',
											status_text = '';
										if (
											less_fifteen_online <=
											fifteen_minute
										) {
											online_icon_class = ' online';
											status_text = 'Online';
										} else {
											online_icon_class = ' offline';
											status_text = 'Offline';
										}
										// return <span className={'dot' + online_icon_class}></span>;
										return (
											<span
												className={
													'table-status-text' +
													online_icon_class
												}
											>
												{timestamp == '' ||
												timestamp == null ||
												timestamp == undefined ||
												timestamp == 0
													? 'Never'
													: status_text}
											</span>
										);
									})()}
									<span className="date-time">
										{timestamp == '' ||
										timestamp == null ||
										timestamp == undefined ||
										timestamp == 0
											? 'Never'
											: moment
													.unix(timestamp)
													.tz('Asia/Kolkata')
													.format(
														'DD MMM YYYY, HH:mm'
													)}
									</span>
								</AntTooltip>
							),
						},
						{
							title: 'Device Status',
							width: '25%',
							align: 'center',
							key: 'device_status',
							render: (row_data) => (
								<div className="device-data-center">
									{(() => {
										if (row_data.status_code == 7) {
											return <span>Blocked</span>;
										} else {
											return <span>Active</span>;
										}
									})()}
								</div>
							),
						},
						{
							title: 'Description',
							key: 'description',
							width: '25%',
							dataIndex: 'description',
						},
					];
				} else {
					console.log('----gdai-class-----')
					columns = [/*Actual Col: Iot */
						{
							title: this.props.t? this.props.t('device_serial_id'): 'Device Serial ID',
							width: '20%',
							key: 'qr',
							dataIndex: 'qr',
							sorter: (a, b) => a.qr.localeCompare(b.qr),
							render: (qr, row_value) => (
								<div>
									{this.getDeviceDetails(qr, row_value, this.props.t)}
								</div>
							),
						},
						{
							title: this.props.t? this.props.t('created_date'): 'Created Date',
							width: '12%',
							align: 'center',
							key: 'created_at',
							dataIndex: 'created_at',
							sorter: (a, b) => a.created_at - b.created_at,
							render: (created_at, row_value) => (
								<div>
									{!isNaN(created_at) && created_at > 0 ? moment
										.unix(
											created_at
										)
										.tz(
											'Asia/Kolkata'
										)
										.format(
											'DD MMM YYYY, HH:mm'
										) : '-'}
								</div>
							),
						},
						{
							title: this.props.t? this.props.t('firmware'): 'Firmware',
							width: '13%',
							align: 'center',
							key: 'version',
							render: (row_value) => (
								<div>
									{(() => {
										let firm_ver = _filter(
											this.state.device_lists.firmwares,
											{
												device_type_id:
													row_value.type_id,
											}
										);

										if (
											firm_ver.length &&
											row_value.firmware_version != ''
										) {
											if (
												row_value.firmware_version ===
												firm_ver[firm_ver.length - 1]
													.version
											) {
												return (
													<div className="display-flex aln-cntr just-cntr">
														<span>
															{
																row_value.firmware_version
															}
														</span>
														{this.props.enabled_features?.includes(
															"DeviceManagement:OTA"
														) && (
															<img
																src={upto_date}
																height="15"
																width="17"
																className="updated"
															/>
														)}
													</div>
												);
											} else {
												return (
													<div className="display-flex aln-cntr just-cntr">
														<span>
															{
																row_value.firmware_version
															}
														</span>
														{(() => {
															if (
																// this.props
																// 	.application_id &&
																// (this.props
																// 	.application_id !==
																// 	17 ||
																	this.props.enabled_features?.includes(
																		"DeviceManagement:OTA"
																	)
																	//)
															) {
																return (
																	<img
																		src={
																			update
																		}
																		height="15"
																		width="17"
																		className="not-updated"
																		onClick={() =>
																			this.singleDeviceFirmwareUpdate(
																				[
																					row_value,
																				]
																			)
																		}
																	/>
																);
															} else {
																return '';
																// return (
																// 	<img
																// 		src={
																// 			update
																// 		}
																// 		height="15"
																// 		width="17"
																// 		className="mar-left-10"
																// 	/>
																// );
															}
														})()}
													</div>
												);
											}
										} else {
											if(this.props.enabled_features?.includes(
												"DeviceManagement:OTA"
											)){
												return (
													<img
														src={update}
														style={{
															cursor: 'pointer',
														}}
														onClick={() => {
															this.singleDeviceFirmwareUpdate(
																[row_value]
															);
														}}
														height="15"
														width="17"
														className="mar-left-10"
													/>
												);
											}else{
												return '-';
											}
										}
									})()}
								</div>
							),
						},
						/*{
							title: 'Connectivity Status',
							width: '10%',
							dataIndex: 'active',
							key: 'active',
							sorter: (a, b) => a.active - b.active,
							render: (timestamp, row_value) => (
								<AntTooltip title="Last data received">
									{(() => {
										let status_text = 'Offline',
											online_icon_class = ' offline';
										if (
											row_value.connectivity_status ===
											'online'
										) {
											online_icon_class = ' online';
											status_text = 'Online';
										}
										// return <span className={'dot' + online_icon_class}></span>;
										return (
											<span
												className={
													'table-status-text' +
													online_icon_class
												}
											>
												{timestamp == '' ||
												timestamp == null ||
												timestamp == undefined ||
												timestamp == 0
													? 'Never'
													: status_text}
											</span>
										);
									})()}
									<span className="date-time">
										{timestamp == '' ||
										timestamp == null ||
										timestamp == undefined ||
										timestamp == 0
											? 'Never'
											: moment
													.unix(timestamp)
													.tz('Asia/Kolkata')
													.format('DD MMM YYYY, HH:mm')}
									</span>
								</AntTooltip>
							),
						},*/
						{
							// title: 'Online Percentage',
							title: this.props.t? this.props.t('online_percentage'): "Online Percentage",
							width: '15%',
							key: 'percent',
							sorter: (a, b) => a.percent - b.percent,
							dataIndex: 'percent',
							render: (percent, row_value) => (
								<div className="percentage-holder online-padding-left">
									<AntTooltip title={this.props.t? this.props.t("This month online %"): "This month online %"}>
										<AntProgress
											className="percent-icon"
											type="circle"
											percent={
												row_value.percent &&
												row_value.percent != null
													? row_value.percent
													: 0
											}
											width={35}
											strokeColor="#21A1DB"
										/>
									</AntTooltip>
								</div>
							),
						},
						{
							title: this.props.t? this.props.t('network_status'): 'Network Status',
							width: '13%',
							key: 'network_percent',
							align: 'center',
							dataIndex: 'network_percent',
							render: (percent, row_value) => (
								<div className="percentage-holder">
									{/*<AntTooltip title="Data Availability">
										<AntProgress className="percent-icon" type="circle" percent={row_value.availability && row_value.availability != null ? row_value.availability : 0} width={35} strokeColor="#139547"/>
									</AntTooltip>*/}
									<div className="percent-icon">
										{(() => {
											if (row_value.device_config) {
												let simNo = '';
												if (row_value.device_sim_slot) {
													simNo =
														'Sim ' +
														row_value.device_sim_slot;
												}
												if (
													row_value.device_config
														.device_modem_type ==
													'gprs'
												) {
													let gprsIcon = GprsSignalRounded;

													let networkStrengthText =
														'';
													if (
														row_value.connectivity_status ===
														'online'
													) {
														if (
															row_value.device_signal_strength >
															20
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Excellent)';
															gprsIcon = GprsSignalRounded5;
														} else if (
															row_value.device_signal_strength >=
																13 &&
															row_value.device_signal_strength <=
																20
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Good)';
															gprsIcon = GprsSignalRounded4;
														} else if (
															row_value.device_signal_strength >=
																6 &&
															row_value.device_signal_strength <=
																12
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Poor)';
															gprsIcon = GprsSignalRounded2;
														} else if (
															row_value.device_signal_strength <
															6
														) {
															networkStrengthText =
																'No Signal';
														}
													} else {
														if (
															row_value.device_signal_strength >
															20
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
														} else if (
															row_value.device_signal_strength >=
																13 &&
															row_value.device_signal_strength <=
																20
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('good') : "Good"})`;
														} else if (
															row_value.device_signal_strength >=
																6 &&
															row_value.device_signal_strength <=
																12
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('poor') : "Poor"})`;
														} else if (
															row_value.device_signal_strength <
															6
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;;
														}
													}

													return (
														<div className="display-flex">
															<AntTooltip
																title={
																	networkStrengthText
																}
															>
																<img
																	className="mar-right-10"
																	src={
																		gprsIcon
																	}
																	height={20}
																	width={20}
																/>
															</AntTooltip>
															<div>
																<div>GPRS</div>
																<div>
																	{simNo}
																</div>
															</div>
														</div>
													);
												} else if (
													row_value.device_config
														.device_modem_type ==
													'wifi'
												) {
													let wifiIcon = WifiSignalRounded;

													let networkStrengthText =
														'';
													if (
														row_value.connectivity_status ===
														'online'
													) {
														if (
															row_value.device_signal_strength >
															20
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Excellent)';
															wifiIcon = WifiSignalRounded3;
														} else if (
															row_value.device_signal_strength >=
																13 &&
															row_value.device_signal_strength <=
																20
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Good)';
															wifiIcon = WifiSignalRounded2;
														} else if (
															row_value.device_signal_strength >=
																6 &&
															row_value.device_signal_strength <=
																12
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Poor)';
															wifiIcon = WifiSignalRounded1;
														} else if (
															row_value.device_signal_strength <
															6
														) {
															networkStrengthText =
																'No Signal';
														}
													} else {
														if (
															row_value.device_signal_strength >
															20
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
														} else if (
															row_value.device_signal_strength >=
																13 &&
															row_value.device_signal_strength <=
																20
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('good') : "Good"})`;
														} else if (
															row_value.device_signal_strength >=
																6 &&
															row_value.device_signal_strength <=
																12
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('poor') : "Poor"})`;
														} else if (
															row_value.device_signal_strength <
															6
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;;
														}
													}
													return (
														<div>
															<AntTooltip
																title={
																	networkStrengthText
																}
															>
																<img
																	className="mar-right-10"
																	src={
																		wifiIcon
																	}
																	height={20}
																	width={20}
																/>
															</AntTooltip>
															<span>Wi-Fi</span>
														</div>
													);
												} else if (
													row_value.device_config
														.device_modem_type ==
													'ethernet'
												) {
													if (
														row_value.connectivity_status ===
														'online'
													) {
														return (
															<div>
																<img
																	className="mar-right-10"
																	src={
																		ethernet
																	}
																	height={20}
																	width={20}
																/>
																<span>
																	Ethernet
																</span>
															</div>
														);
													} else {
														return (
															<div>
																<img
																	className="mar-right-10"
																	src={
																		ethernet_inactive
																	}
																	height={20}
																	width={20}
																/>
																<span>
																	Ethernet
																</span>
															</div>
														);
													}
												}
											}
										})()}
									</div>
								</div>
							),
						},
						{
							title: this.props.t? this.props.t('power_status'): 'Power Status',
							width: '10%',
							key: 'power_status',
							align: 'center',
							dataIndex: 'power_status',
							render: (power_status, row_value) => (
								<div className="percentage-holder power-container">
									<div className="icon-holder">
										{(() => {
											if (
												row_value.device_power_status !==
													null &&
												row_value.device_power_status !==
													false
											) {
												let icon = power_inactive,
													text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('On'): "Last Reported Status: On"}`;
												if (
													row_value.connectivity_status ===
													'online'
												) {
													icon = power_on;
													text = 'Power Status: On';
												}
												return (
													<AntTooltip title={text}>
														<img
															src={icon}
															height={22}
															width={22}
														/>
													</AntTooltip>
												);
											} else if (
												row_value.device_power_status ===
												false
											) {
												let icon = power_inactive,
													text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('Off'): "Last Reported Status: Off"}`;
												if (
													row_value.connectivity_status ===
													'online'
												) {
													icon = power_off;
													text = 'Power Status: Off';
												}
												return (
													<AntTooltip title={text}>
														<img
															src={icon}
															height={22}
															width={22}
														/>
													</AntTooltip>
												);
											} else {
												let text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('unknown'): "Last Reported Status: Unknown"}`;
												if (
													row_value.connectivity_status ===
													'online'
												) {
													text =
														'Power Status: Unknown';
												}
												return (
													<AntTooltip title={text}>
														<img
															src={power_inactive}
															height={22}
															width={22}
														/>
													</AntTooltip>
												);
											}
										})()}
									</div>
									<div className="battery-container">
										{(() => {
											if (
												row_value.device_battery_percent
											) {
												return (
													<div className="battery mar-left-5">
														<div className="minus-icon">
															-
														</div>
														<div className="bat-body">
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >
																		0 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		20 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		30 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		40 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		50 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		60 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		70 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		80 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		90 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		100 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
														</div>
														<div className="bat-top"></div>
														<div className="plus-icon">
															+
														</div>
													</div>
												);
											}
										})()}
										<div className="bat-charge-details">
											<div className="icon-holder">
												{(() => {
													if (
														row_value.device_battery_percent !==
															null &&
														row_value.device_battery_percent !==
															false &&
														row_value.connectivity_status ===
															'online'
													) {
														return (
															<span>
																{'(' +
																	(row_value.device_battery_percent !==
																	0
																		? row_value.device_battery_percent.toFixed(
																				2
																		  )
																		: 0) +
																	'%)'}
															</span>
														);
													}
												})()}
											</div>
											<div className="icon-holder mar-left-5">
												{(() => {
													if (
														row_value.device_charging_status !==
															null &&
														row_value.device_battery_percent !==
															null
													) {
														if (
															row_value.device_charging_status &&
															row_value.connectivity_status ===
																'online'
														) {
															return (
																<img
																	src={
																		lighting_active
																	}
																	height={15}
																	width={15}
																/>
															);
														} else {
															return (
																<img
																	src={
																		lighting_inactive
																	}
																	height={15}
																	width={15}
																/>
															);
														}
													}
												})()}
											</div>
										</div>
									</div>
								</div>
							),
						},
						{
							title: 'SIM',
							width: '6%',
							key: 'sim_details',
							align: 'center',
							dataIndex: 'sim_details',
							render: (sim_details, row_value) =>
								this.renderSimDetails(sim_details, row_value),
						},
						/*{
							title: 'Debug',
							width: '10%',
							align: 'center',
							key: 'health',
							render: (config, row_data) => (
								<div>
									<div className="icon-holder">
										<AntTooltip title="Open Debug">
											<img
												src={debug}
												className="cursor-pointer"
												height={28}
												width={32}
												onClick={() =>
													this.openDeviceDebug(row_data)
												}
											/>
										</AntTooltip>
									</div>
									<div className="device-errors-tags">
										{(() => {
											let errors = [],
												extraTypes = '';
											if (
												row_data.device_error_list &&
												row_data.device_error_list.length
											) {
												row_data.device_error_list.map(
													(errorType, index) => {
														if (index === 0) {
															errors.push(
																<AntTooltip
																	title={moment
																		.unix(
																			row_data.timestamp
																		)
																		.tz(
																			'Asia/Kolkata'
																		)
																		.format(
																			'DD MMM YYYY HH:mm'
																		)}
																>
																	<span className="mar-lt-5">
																		{errorType +
																			', '}
																	</span>
																</AntTooltip>
															);
														} else if (index === 1) {
															errors.push(
																<AntTooltip
																	title={moment
																		.unix(
																			row_data.timestamp
																		)
																		.tz(
																			'Asia/Kolkata'
																		)
																		.format(
																			'DD MMM YYYY HH:mm'
																		)}
																>
																	<span className="mar-lt-5">
																		{errorType}
																	</span>
																</AntTooltip>
															);
														} else {
															if (
																index ===
																row_data
																	.device_error_list
																	.length -
																	1
															) {
																extraTypes = extraTypes.concat(
																	errorType +
																		' - ' +
																		moment
																			.unix(
																				row_data.timestamp
																			)
																			.tz(
																				'Asia/Kolkata'
																			)
																			.format(
																				'DD MMM YYYY HH:mm'
																			)
																);
															} else {
																extraTypes = extraTypes.concat(
																	errorType +
																		' - ' +
																		moment
																			.unix(
																				row_data.timestamp
																			)
																			.tz(
																				'Asia/Kolkata'
																			)
																			.format(
																				'DD MMM YYYY HH:mm'
																			) +
																		', '
																);
															}
														}
													}
												);

												if (
													row_data.device_error_list
														.length > 2
												) {
													errors.push(
														<AntTooltip
															title={extraTypes}
														>
															<AntTag
																className="mar-lt-5"
																color="red"
															>
																{'+ ' +
																	(row_data
																		.device_error_list
																		.length -
																		2) +
																	' more'}
															</AntTag>
														</AntTooltip>
													);
												}
											}
											return errors;
										})()}
									</div>
								</div>
							),
						},*/
						{
							// title: 'Debug',
							title: this.props.t? this.props.t('debug'): "Debug",
							width: '10%',
							align: 'center',
							key: 'configure',
							render: (config, row_data) => (
								<div className="display-flex just-cntr aln-cntr">
									<div className="icon-holder mar-right-10">
										<AntTooltip title={this.props.t? this.props.t('debug'): "Debug"}>
											<img
												src={debug}
												className="cursor-pointer"
												height={28}
												width={32}
												onClick={() =>
													this.openDeviceDebug(
														row_data
													)
												}
											/>
										</AntTooltip>
									</div>
									{this.props.enabled_features?.includes(
                          "DeviceManagement:Rawlog",
                        ) && <div className="icon-holder mar-right-10">
										<AntTooltip title="Raw log">
											<img
												src={raw_log_icon}
												className="cursor-pointer"
												height={28}
												width={32}
												onClick={() =>
													this.openDeviceDebug(
														row_data,
														true
													)
												}
											/>
										</AntTooltip>
									</div>}
									{(() => {
										if (
											/*this.props.application_id !== 17*/ true
										) {
											if (
												/*row_data.application_id == 6*/ true
											) {
												/*let routeLink =
											this.iotBasePath +
											'/enterprise/' +
											this.props.client_id +
											this.platform_slug +
											'/devices/unassigned/' +
											(config.device_config &&
											config.device_config.station_id &&
											config.device_config.station_id
												.length
												? config.device_config
														.station_id[0]
												: row_data.vendor_id
												? row_data.vendor_id
												: 0) +
											'/configuration?app_id=' +
											row_data.application_id;*/
												let routeLink =
													this.iotBasePath +
													'/enterprise/' +
													this.props.client_id +
													this.platform_slug +
													'/devices/unassigned/' +
													row_data.id +
													'/communication?type_id=' +
													row_data.type_id;
												if (
													this.props
														.is_application_filter
												) {
													/*routeLink =
												this.platform_slug +
												'/customer-management/' +
												this.props.customer_id +
												'/applications/' +
												this.state.selected_app +
												'/devices/' +
												(config.device_config &&
												config.device_config
													.station_id &&
												config.device_config.station_id
													.length
													? config.device_config
															.station_id[0]
													: row_data.vendor_id
													? row_data.vendor_id
													: 0) +
												'/configuration?app_id=' +
												row_data.application_id;*/
													routeLink =
														this.iotBasePath +
														'/enterprise/' +
														this.props.client_id +
														this.platform_slug +
														'/customer-management/' +
														this.props.customer_id +
														'/applications/' +
														this.state
															.selected_app +
														'/devices/' +
														row_data.id +
														'/communication?type_id=' +
														row_data.type_id;
												}
												if (
													row_data.type_id == 11 ||
													row_data.type_id == 12 ||
													row_data.type_id == 51
												) {
													return (
														<AntTooltip title={this.props.t? this.props.t('configure_device'): "Configure Device"}>
															<img
																className="mar-right-10 cursor-pointer"
																src={
																	configuration
																}
																height={28}
																width={32}
																onClick={() =>
																	this.openConfiguration(
																		routeLink
																	)
																}
															/>
														</AntTooltip>
													);
												}
											} else {
												if (
													row_data.type_id == 11 ||
													row_data.type_id == 12 ||
													row_data.type_id == 51
												) {
													return (
														<AntTooltip title="Configure Device">
															<img
																className="mar-right-10 cursor-block"
																src={
																	configuration
																}
																height={28}
																width={32}
															/>
														</AntTooltip>
													);
												}
											}
										}
									})()}
									{(() => {
										if (this.props.application_id == 12 || (this.props.enabled_features && this.props.enabled_features.includes('DeviceManagement:CustomCommand'))) {
											return (
												<AntTooltip title="Custom Command">
													<img
														src={custom_command}
														className="cursor-pointer"
														height={28}
														width={32}
														onClick={() =>
															this.openCustomCommand(
																row_data.id,
																row_data
															)
														}
													/>
												</AntTooltip>
											);
										}
									})()}
								</div>
							),
						},
						/*{
							title: 'Device Status',
							width: '10%',
							align: 'center',
							key: 'device_status',
							render: (row_data) => (
								<div>
									<AntTooltip
										title={
											row_data.status_code == 7
												? 'Inactive'
												: 'Active'
										}
									>
										<AntSwitch
											size="medium"
											checkedChildren="Active"
											unCheckedChildren="Inactive"
											checked={
												row_data.status_code == 7
													? false
													: true
											}
											onChange={(checked, event) =>
												this.onUpdateStatus(
													checked,
													event,
													row_data
												)
											}
										></AntSwitch>
									</AntTooltip>
								</div>
							),
						},*/
						/*{
							title: 'Edit',
							width: '5%',
							align: 'center',
							key: 'device_status',
							render: (row_data) => (
								<span>
									{(() => {
										if (row_data.is_editable) {
											return (
												<EditOutlined
													className="action-icon"
													onClick={() =>
														this.openEditDrawer(
															row_data
														)
													}
												/>
											);
										} else {
											return (
												<EditOutlined className="block-icon" />
											);
										}
									})()}
								</span>
							),
						},*/
					];
				}
			} else {
				if (this.props.customer_id == 392) {
					columns = [
						{
							title: 'Device QR',
							key: 'qr',
							width: '30%',
							dataIndex: 'qr',
							sorter: (a, b) => a.qr.localeCompare(b.qr),
							render: (qr, row_value) => (
								<div>
									<div className="device-name">
										<AntTooltip title={qr}>{qr}</AntTooltip>
									</div>
									<div>{row_value.type_name}</div>
								</div>
							),
						},
						{
							title: 'Status',
							dataIndex: 'active',
							width: '20%',
							// align: 'center',
							key: 'active',
							sorter: (a, b) => a.active - b.active,
							render: (timestamp) => (
								<AntTooltip title="Last data received">
									{(() => {
										let current_time = moment().unix(),
											less_fifteen_online =
												current_time - timestamp,
											fifteen_minute = 900,
											online_icon_class = '',
											status_text = '';
										if (
											less_fifteen_online <=
											fifteen_minute
										) {
											online_icon_class = ' online';
											status_text = 'Online';
										} else {
											online_icon_class = ' offline';
											status_text = 'Offline';
										}
										// return <span className={'dot' + online_icon_class}></span>;
										return (
											<span
												className={
													'table-status-text' +
													online_icon_class
												}
											>
												{timestamp == '' ||
												timestamp == null ||
												timestamp == undefined ||
												timestamp == 0
													? 'Never'
													: status_text}
											</span>
										);
									})()}
									<span className="date-time">
										{timestamp == '' ||
										timestamp == null ||
										timestamp == undefined ||
										timestamp == 0
											? 'Never'
											: moment
													.unix(timestamp)
													.tz('Asia/Kolkata')
													.format(
														'DD MMM YYYY, HH:mm'
													)}
									</span>
								</AntTooltip>
							),
						},
						{
							title: 'Device Status',
							width: '25%',
							align: 'center',
							key: 'device_status',
							render: (row_data) => (
								<div className="device-data-center">
									{(() => {
										if (row_data.status_code == 7) {
											return <span>Blocked</span>;
										} else {
											return <span>Active</span>;
										}
									})()}
								</div>
							),
						},
						{
							title: 'Description',
							key: 'description',
							width: '25%',
							dataIndex: 'description',
						},
					];
				} else {
					columns = [
						{
							title: this.props.t? this.props.t('device_serial_id'): 'Device Serial ID',
							width: '20%',
							key: 'qr',
							dataIndex: 'qr',
							sorter: (a, b) => a.qr.localeCompare(b.qr),
							render: (qr, row_value) => (
								<div>
									{this.getDeviceDetails(qr, row_value, this.props.t)}
								</div>
							),
						},
						{
							title: this.props.t? this.props.t('created_date'): 'Created Date',
							width: '12%',
							align: 'center',
							key: 'created_at',
							dataIndex: 'created_at',
							sorter: (a, b) => a.created_at - b.created_at,
							render: (created_at, row_value) => (
								<div>
									{!isNaN(created_at) && created_at > 0 ? moment
										.unix(
											created_at
										)
										.tz(
											'Asia/Kolkata'
										)
										.format(
											'DD MMM YYYY, HH:mm'
										) : '-'}
								</div>
							),
						},
						{
							title: this.props.t? this.props.t('firmware'): 'Firmware',
							width: '15%',
							align: 'center',
							key: 'version',
							render: (row_value) => (
								<div>
									{(() => {
										let firm_ver = _filter(
											this.state.device_lists.firmwares,
											{
												device_type_id:
													row_value.type_id,
											}
										);

										if (
											firm_ver.length &&
											row_value.firmware_version != ''
										) {
											if (
												row_value.firmware_version ===
												firm_ver[firm_ver.length - 1]
													.version
											) {
												return (
													<div className="display-flex aln-cntr just-cntr">
														<span>
															{
																row_value.firmware_version
															}
														</span>
														{/* <img
															src={upto_date}
															height="15"
															width="17"
															className="updated"
														/> */}
													</div>
												);
											} else {
												return (
													<div className="display-flex aln-cntr just-cntr">
														<span>
															{
																row_value.firmware_version
															}
														</span>
														{/* <img
															src={update}
															height="15"
															width="17"
															className="mar-left-10"
														/> */}
													</div>
												);
											}
										} else {
											return '-';
										}
									})()}
								</div>
							),
						},
						/*{
							title: 'Connectivity Status',
							width: '15%',
							dataIndex: 'active',
							key: 'active',
							sorter: (a, b) => a.active - b.active,
							render: (timestamp, row_value) => (
								<AntTooltip title="Last data received">
									{(() => {
										let status_text = 'Offline',
											online_icon_class = ' offline';
										if (
											row_value.connectivity_status ===
											'online'
										) {
											online_icon_class = ' online';
											status_text = 'Online';
										}
										// return <span className={'dot' + online_icon_class}></span>;
										return (
											<span
												className={
													'table-status-text' +
													online_icon_class
												}
											>
												{timestamp == '' ||
												timestamp == null ||
												timestamp == undefined ||
												timestamp == 0
													? 'Never'
													: status_text}
											</span>
										);
									})()}
									<span className="date-time">
										{timestamp == '' ||
										timestamp == null ||
										timestamp == undefined ||
										timestamp == 0
											? 'Never'
											: moment
													.unix(timestamp)
													.tz('Asia/Kolkata')
													.format(
														'DD MMM YYYY, HH:mm'
													)}
									</span>
								</AntTooltip>
							),
						},*/
						/*{
							title: 'Online Percentage',
							width: '15%',
							key: 'percent',
							sorter: (a, b) => a.percent - b.percent,
							dataIndex: 'percent',
							render: (percent, row_value) => (
								<div className="percentage-holder">
									<AntTooltip title="This month online %">
										<AntProgress
											className="percent-icon"
											type="circle"
											percent={
												row_value.percent &&
												row_value.percent != null
													? row_value.percent
													: 0
											}
											width={35}
											strokeColor="#21A1DB"
										/>
									</AntTooltip>
								</div>
							),
						},*/
						{
							title: this.props.t? this.props.t('network_status'): 'Network Status',
							width: '15%',
							key: 'network_percent',
							align: 'center',
							dataIndex: 'network_percent',
							render: (percent, row_value) => (
								<div className="percentage-holder">
									{/*<AntTooltip title="Data Availability">
										<AntProgress className="percent-icon" type="circle" percent={row_value.availability && row_value.availability != null ? row_value.availability : 0} width={35} strokeColor="#139547"/>
									</AntTooltip>*/}
									<div className="percent-icon">
										{(() => {
											if (row_value.device_config) {
												let simNo = '';
												if (row_value.device_sim_slot) {
													simNo =
														'Sim ' +
														row_value.device_sim_slot;
												}
												if (
													row_value.device_config
														.device_modem_type ==
													'gprs'
												) {
													let gprsIcon = GprsSignalRounded;

													let networkStrengthText =
														'';
													if (
														row_value.connectivity_status ===
														'online'
													) {
														if (
															row_value.device_signal_strength >
															20
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Excellent)';
															gprsIcon = GprsSignalRounded5;
														} else if (
															row_value.device_signal_strength >=
																13 &&
															row_value.device_signal_strength <=
																20
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Good)';
															gprsIcon = GprsSignalRounded4;
														} else if (
															row_value.device_signal_strength >=
																6 &&
															row_value.device_signal_strength <=
																12
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Poor)';
															gprsIcon = GprsSignalRounded2;
														} else if (
															row_value.device_signal_strength <
															6
														) {
															networkStrengthText =
																'No Signal';
														}
													} else {
														if (
															row_value.device_signal_strength >
															20
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
														} else if (
															row_value.device_signal_strength >=
																13 &&
															row_value.device_signal_strength <=
																20
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('good') : "Good"})`;
														} else if (
															row_value.device_signal_strength >=
																6 &&
															row_value.device_signal_strength <=
																12
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('poor') : "Poor"})`;
														} else if (
															row_value.device_signal_strength <
															6
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;;
														}
													}

													return (
														<div className="display-flex">
															<AntTooltip
																title={
																	networkStrengthText
																}
															>
																<img
																	className="mar-right-10"
																	src={
																		gprsIcon
																	}
																	height={20}
																	width={20}
																/>
															</AntTooltip>
															<div>
																<div>GPRS</div>
																<div>
																	{simNo}
																</div>
															</div>
														</div>
													);
												} else if (
													row_value.device_config
														.device_modem_type ==
													'wifi'
												) {
													let wifiIcon = WifiSignalRounded;

													let networkStrengthText =
														'';
													if (
														row_value.connectivity_status ===
														'online'
													) {
														if (
															row_value.device_signal_strength >
															20
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Excellent)';
															wifiIcon = WifiSignalRounded3;
														} else if (
															row_value.device_signal_strength >=
																13 &&
															row_value.device_signal_strength <=
																20
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Good)';
															wifiIcon = WifiSignalRounded2;
														} else if (
															row_value.device_signal_strength >=
																6 &&
															row_value.device_signal_strength <=
																12
														) {
															networkStrengthText =
																'Strength: ' +
																row_value.device_signal_strength +
																' (Poor)';
															wifiIcon = WifiSignalRounded1;
														} else if (
															row_value.device_signal_strength <
															6
														) {
															networkStrengthText =
																'No Signal';
														}
													} else {
														if (
															row_value.device_signal_strength >
															20
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
														} else if (
															row_value.device_signal_strength >=
																13 &&
															row_value.device_signal_strength <=
																20
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('good') : "Good"})`;
														} else if (
															row_value.device_signal_strength >=
																6 &&
															row_value.device_signal_strength <=
																12
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
																row_value.device_signal_strength +
																` (${this.props.t ? this.props.t('poor') : "Poor"})`;
														} else if (
															row_value.device_signal_strength <
															6
														) {
															networkStrengthText =
																`${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;;
														}
													}
													return (
														<div>
															<AntTooltip
																title={
																	networkStrengthText
																}
															>
																<img
																	className="mar-right-10"
																	src={
																		wifiIcon
																	}
																	height={20}
																	width={20}
																/>
															</AntTooltip>
															<span>Wi-Fi</span>
														</div>
													);
												} else if (
													row_value.device_config
														.device_modem_type ==
													'ethernet'
												) {
													if (
														row_value.connectivity_status ===
														'online'
													) {
														return (
															<div>
																<img
																	className="mar-right-10"
																	src={
																		ethernet
																	}
																	height={20}
																	width={20}
																/>
																<span>
																	Ethernet
																</span>
															</div>
														);
													} else {
														return (
															<div>
																<img
																	className="mar-right-10"
																	src={
																		ethernet_inactive
																	}
																	height={20}
																	width={20}
																/>
																<span>
																	Ethernet
																</span>
															</div>
														);
													}
												}
											}
										})()}
									</div>
								</div>
							),
						},
						{
							title: this.props.t? this.props.t('power_status'): 'Power Status',
							width: '15%',
							key: 'power_status',
							align: 'center',
							dataIndex: 'power_status',
							render: (power_status, row_value) => (
								<div className="percentage-holder power-container">
									<div className="icon-holder">
										{(() => {
											if (
												row_value.device_power_status !==
													null &&
												row_value.device_power_status !==
													false
											) {
												let icon = power_inactive,
													text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('On'): "Last Reported Status: On"}`;
												if (
													row_value.connectivity_status ===
													'online'
												) {
													icon = power_on;
													text = 'Power Status: On';
												}
												return (
													<AntTooltip title={text}>
														<img
															src={icon}
															height={22}
															width={22}
														/>
													</AntTooltip>
												);
											} else if (
												row_value.device_power_status ===
												false
											) {
												let icon = power_inactive,
													text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('Off'): "Last Reported Status: Off"}`;
												if (
													row_value.connectivity_status ===
													'online'
												) {
													icon = power_off;
													text = 'Power Status: Off';
												}
												return (
													<AntTooltip title={text}>
														<img
															src={icon}
															height={22}
															width={22}
														/>
													</AntTooltip>
												);
											} else {
												let text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('unknown'): "Last Reported Status: Unknown"}`;
												if (
													row_value.connectivity_status ===
													'online'
												) {
													text =
														'Power Status: Unknown';
												}
												return (
													<AntTooltip title={text}>
														<img
															src={power_inactive}
															height={22}
															width={22}
														/>
													</AntTooltip>
												);
											}
										})()}
									</div>
									<div className="battery-container">
										{(() => {
											if (
												row_value.device_battery_percent
											) {
												return (
													<div className="battery mar-left-5">
														<div className="minus-icon">
															-
														</div>
														<div className="bat-body">
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >
																		0 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		20 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		30 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		40 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		50 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		60 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		70 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		80 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		90 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
															<div
																className={
																	'bar' +
																	(row_value.device_battery_percent !==
																		null &&
																	row_value.device_battery_percent >=
																		100 &&
																	row_value.connectivity_status ===
																		'online'
																		? ' active'
																		: ' deactive')
																}
															></div>
														</div>
														<div className="bat-top"></div>
														<div className="plus-icon">
															+
														</div>
													</div>
												);
											}
										})()}
										<div className="bat-charge-details">
											<div className="icon-holder">
												{(() => {
													if (
														row_value.device_battery_percent !==
															null &&
														row_value.device_battery_percent !==
															false &&
														row_value.connectivity_status ===
															'online'
													) {
														return (
															<span>
																{'(' +
																	(row_value.device_battery_percent !==
																	0
																		? row_value.device_battery_percent.toFixed(
																				2
																		  )
																		: 0) +
																	'%)'}
															</span>
														);
													}
												})()}
											</div>
											<div className="icon-holder mar-left-5">
												{(() => {
													if (
														row_value.device_charging_status !==
															null &&
														row_value.device_battery_percent !==
															null
													) {
														if (
															row_value.device_charging_status &&
															row_value.connectivity_status ===
																'online'
														) {
															return (
																<img
																	src={
																		lighting_active
																	}
																	height={15}
																	width={15}
																/>
															);
														} else {
															return (
																<img
																	src={
																		lighting_inactive
																	}
																	height={15}
																	width={15}
																/>
															);
														}
													}
												})()}
											</div>
										</div>
									</div>
								</div>
							),
						},
						{
							title: 'SIM',
							width: '8%',
							key: 'sim_details',
							align: 'center',
							dataIndex: 'sim_details',
							render: (sim_details, row_value) =>
								this.renderSimDetails(sim_details, row_value),
						},
						/*{
							title: 'Debug',
							width: '10%',
							align: 'center',
							key: 'health',
							render: (config, row_data) => (
								<div>
									<div className="icon-holder">
										<AntTooltip title="Open Debug">
											<img
												src={debug}
												className="cursor-pointer"
												height={28}
												width={32}
												onClick={() =>
													this.openDeviceDebug(row_data)
												}
											/>
										</AntTooltip>
									</div>
									<div className="device-errors-tags">
										{(() => {
											let errors = [],
												extraTypes = '';
											if (
												row_data.device_error_list &&
												row_data.device_error_list.length
											) {
												row_data.device_error_list.map(
													(errorType, index) => {
														if (index === 0) {
															errors.push(
																<AntTooltip
																	title={moment
																		.unix(
																			row_data.timestamp
																		)
																		.tz(
																			'Asia/Kolkata'
																		)
																		.format(
																			'DD MMM YYYY HH:mm'
																		)}
																>
																	<span className="mar-lt-5">
																		{errorType +
																			', '}
																	</span>
																</AntTooltip>
															);
														} else if (index === 1) {
															errors.push(
																<AntTooltip
																	title={moment
																		.unix(
																			row_data.timestamp
																		)
																		.tz(
																			'Asia/Kolkata'
																		)
																		.format(
																			'DD MMM YYYY HH:mm'
																		)}
																>
																	<span className="mar-lt-5">
																		{errorType}
																	</span>
																</AntTooltip>
															);
														} else {
															if (
																index ===
																row_data
																	.device_error_list
																	.length -
																	1
															) {
																extraTypes = extraTypes.concat(
																	errorType +
																		' - ' +
																		moment
																			.unix(
																				row_data.timestamp
																			)
																			.tz(
																				'Asia/Kolkata'
																			)
																			.format(
																				'DD MMM YYYY HH:mm'
																			)
																);
															} else {
																extraTypes = extraTypes.concat(
																	errorType +
																		' - ' +
																		moment
																			.unix(
																				row_data.timestamp
																			)
																			.tz(
																				'Asia/Kolkata'
																			)
																			.format(
																				'DD MMM YYYY HH:mm'
																			) +
																		', '
																);
															}
														}
													}
												);

												if (
													row_data.device_error_list
														.length > 2
												) {
													errors.push(
														<AntTooltip
															title={extraTypes}
														>
															<AntTag
																className="mar-lt-5"
																color="red"
															>
																{'+ ' +
																	(row_data
																		.device_error_list
																		.length -
																		2) +
																	' more'}
															</AntTag>
														</AntTooltip>
													);
												}
											}
											return errors;
										})()}
									</div>
								</div>
							),
						},*/
						{
							title: 'Debug',
							width: '15%',
							align: 'center',
							key: 'configure',
							render: (config, row_data) => (
								<div className="display-flex just-cntr aln-cntr">
									<div className="icon-holder mar-right-10">
										<AntTooltip title={this.props.t? this.props.t('debug'): "Debug"}>
											<img
												src={debug}
												className="cursor-pointer"
												height={28}
												width={32}
												onClick={() =>
													this.openDeviceDebug(
														row_data
													)
												}
											/>
										</AntTooltip>
									</div>
									{this.props.enabled_features?.includes(
                          "DeviceManagement:Rawlog",
                        ) && <div className="icon-holder mar-right-10">
										<AntTooltip title="Raw log">
											<img
												src={raw_log_icon}
												className="cursor-pointer"
												height={28}
												width={32}
												onClick={() =>
													this.openDeviceDebug(
														row_data,
														true
													)
												}
											/>
										</AntTooltip>
									</div>}
									{(() => {
										if (
											/*this.props.application_id !== 17*/ true
										) {
											if (
												/*row_data.application_id == 6*/ true
											) {
												/*let routeLink =
											this.iotBasePath +
											'/enterprise/' +
											this.props.client_id +
											this.platform_slug +
											'/devices/unassigned/' +
											(config.device_config &&
											config.device_config.station_id &&
											config.device_config.station_id
												.length
												? config.device_config
														.station_id[0]
												: row_data.vendor_id
												? row_data.vendor_id
												: 0) +
											'/configuration?app_id=' +
											row_data.application_id;*/
												let routeLink =
													this.iotBasePath +
													'/enterprise/' +
													this.props.client_id +
													this.platform_slug +
													'/devices/unassigned/' +
													row_data.id +
													'/communication?type_id=' +
													row_data.type_id;
												if (
													this.props
														.is_application_filter
												) {
													/*routeLink =
												this.platform_slug +
												'/customer-management/' +
												this.props.customer_id +
												'/applications/' +
												this.state.selected_app +
												'/devices/' +
												(config.device_config &&
												config.device_config
													.station_id &&
												config.device_config.station_id
													.length
													? config.device_config
															.station_id[0]
													: row_data.vendor_id
													? row_data.vendor_id
													: 0) +
												'/configuration?app_id=' +
												row_data.application_id;*/
													routeLink =
														this.iotBasePath +
														'/enterprise/' +
														this.props.client_id +
														this.platform_slug +
														'/customer-management/' +
														this.props.customer_id +
														'/applications/' +
														this.state
															.selected_app +
														'/devices/' +
														row_data.id +
														'/communication?type_id=' +
														row_data.type_id;
												}
												if (
													row_data.type_id == 11 ||
													row_data.type_id == 12 ||
													row_data.type_id == 51
												) {
													return (
														<AntTooltip title={this.props.t? this.props.t('configure_device'): "Configure Device"}>
															<img
																className="mar-right-10 cursor-pointer"
																src={
																	configuration
																}
																height={28}
																width={32}
																onClick={() =>
																	this.openConfiguration(
																		routeLink
																	)
																}
															/>
														</AntTooltip>
													);
												}
											} else {
												if (
													row_data.type_id == 11 ||
													row_data.type_id == 12 ||
													row_data.type_id == 51
												) {
													return (
														<AntTooltip title="Configure Device">
															<img
																className="mar-right-10 cursor-block"
																src={
																	configuration
																}
																height={28}
																width={32}
															/>
														</AntTooltip>
													);
												}
											}
										}
									})()}
									{(() => {
										if (this.props.application_id == 12 || (this.props.enabled_features && this.props.enabled_features.includes('DeviceManagement:CustomCommand'))) {
											return (
												<AntTooltip title="Custom Command">
													<img
														src={custom_command}
														className="cursor-pointer"
														height={28}
														width={32}
														onClick={() =>
															this.openCustomCommand(
																row_data.id,
																row_data
															)
														}
													/>
												</AntTooltip>
											);
										}
									})()}
								</div>
							),
						},
						/*{
							title: 'Device Status',
							width: '10%',
							align: 'center',
							key: 'device_status',
							render: (row_data) => (
								<div>
									<AntTooltip
										title={
											row_data.status_code == 7
												? 'Inactive'
												: 'Active'
										}
									>
										<AntSwitch
											size="medium"
											checkedChildren="Active"
											unCheckedChildren="Inactive"
											checked={
												row_data.status_code == 7
													? false
													: true
											}
											onChange={(checked, event) =>
												this.onUpdateStatus(
													checked,
													event,
													row_data
												)
											}
										></AntSwitch>
									</AntTooltip>
								</div>
							),
						},*/
						/*{
							title: 'Edit',
							width: '5%',
							align: 'center',
							key: 'device_status',
							render: (row_data) => (
								<span>
									{(() => {
										if (row_data.is_editable) {
											return (
												<EditOutlined
													className="action-icon"
													onClick={() =>
														this.openEditDrawer(
															row_data
														)
													}
												/>
											);
										} else {
											return (
												<EditOutlined className="block-icon" />
											);
										}
									})()}
								</span>
							),
						},*/
					];
				}
			}
		} else {
			columns = [
				{
					title: this.props.t? this.props.t('device_serial_id'): 'Device Serial ID',
					width: '20%',
					key: 'qr',
					dataIndex: 'qr',
					sorter: (a, b) => a.qr.localeCompare(b.qr),
					render: (qr, row_value) => (
						<div>
							{this.getDeviceDetails(qr, row_value, this.props.t)}
						</div>
					),
				},
				{
					title: this.props.t? this.props.t('created_date'): 'Created Date',
					width: '12%',
					align: 'center',
					key: 'created_at',
					dataIndex: 'created_at',
					sorter: (a, b) => a.created_at - b.created_at,
					render: (created_at, row_value) => (
						<div>
							{!isNaN(created_at) && created_at > 0 ? moment
								.unix(
									created_at
								)
								.tz(
									'Asia/Kolkata'
								)
								.format(
									'DD MMM YYYY, HH:mm'
								) : '-'}
						</div>
					),
				},
				{
					title: this.props.t? this.props.t('firmware'): 'Firmware',
					width: '9%',
					align: 'center',
					key: 'version',
					render: (row_value) => (
						<div>
							{(() => {
								let firm_ver = _filter(
									this.state.device_lists.firmwares,
									{ device_type_id: row_value.type_id }
								);

								if (
									firm_ver.length &&
									row_value.firmware_version != ''
								) {
									if (
										row_value.firmware_version ===
										firm_ver[firm_ver.length - 1].version
									) {
										return (
											<div className="display-flex aln-cntr just-cntr">
												<span>
													{row_value.firmware_version +
														' '}
												</span>
												<img
													src={upto_date}
													height="15"
													width="17"
													className="updated"
												/>
											</div>
										);
									} else {
										return (
											<div className="display-flex aln-cntr just-cntr">
												<span>
													{row_value.firmware_version +
														' '}
												</span>
												{(() => {
													if (
														this.props
															.application_id &&
														this.props
															.application_id !==
															17
													) {
														return (
															<img
																src={update}
																height="15"
																width="17"
																className="not-updated"
																onClick={() =>
																	this.singleDeviceFirmwareUpdate(
																		[
																			row_value,
																		]
																	)
																}
															/>
														);
													} else {
														return (
															<img
																src={update}
																height="15"
																width="17"
																className="mar-left-10"
															/>
														);
													}
												})()}
											</div>
										);
									}
								} else {
									return '-';
								}
							})()}
						</div>
					),
				},
				/*{
					title: 'Connectivity Status',
					width: '10%',
					dataIndex: 'active',
					key: 'active',
					sorter: (a, b) => a.active - b.active,
					render: (timestamp, row_value) => (
						<AntTooltip title="Last data received">
							{(() => {
								let status_text = 'Offline',
									online_icon_class = ' offline';
								if (
									row_value.connectivity_status === 'online'
								) {
									online_icon_class = ' online';
									status_text = 'Online';
								}
								// return <span className={'dot' + online_icon_class}></span>;
								return (
									<span
										className={
											'table-status-text' +
											online_icon_class
										}
									>
										{timestamp == '' ||
										timestamp == null ||
										timestamp == undefined ||
										timestamp == 0
											? 'Never'
											: status_text}
									</span>
								);
							})()}
							<span className="date-time">
								{timestamp == '' ||
								timestamp == null ||
								timestamp == undefined ||
								timestamp == 0
									? 'Never'
									: moment
											.unix(timestamp)
											.tz('Asia/Kolkata')
											.format('DD MMM YYYY, HH:mm')}
							</span>
						</AntTooltip>
					),
				},*/
				{
					// title: 'Online Percentage',
					title: this.props.t? this.props.t('online_percentage'): "Online Percentage",
					width: '10%',
					key: 'percent',
					sorter: (a, b) => a.percent - b.percent,
					dataIndex: 'percent',
					render: (percent, row_value) => (
						<div className="percentage-holder online-padding-left">
							<AntTooltip title={this.props.t? this.props.t("This month online %"): "This month online %"}>
								<AntProgress
									className="percent-icon"
									type="circle"
									percent={
										row_value.percent &&
										row_value.percent != null
											? row_value.percent
											: 0
									}
									width={35}
									strokeColor="#21A1DB"
								/>
							</AntTooltip>
						</div>
					),
				},
				{
					title: this.props.t? this.props.t('network_status'): 'Network Status',
					width: '10%',
					key: 'network_percent',
					align: 'center',
					dataIndex: 'network_percent',
					render: (percent, row_value) => (
						<div className="percentage-holder">
							{/*<AntTooltip title="Data Availability">
										<AntProgress className="percent-icon" type="circle" percent={row_value.availability && row_value.availability != null ? row_value.availability : 0} width={35} strokeColor="#139547"/>
									</AntTooltip>*/}
							<div className="percent-icon">
								{(() => {
									if (row_value.device_config) {
										let simNo = '';
										if (row_value.device_sim_slot) {
											simNo =
												'Sim ' +
												row_value.device_sim_slot;
										}
										if (
											row_value.device_config
												.device_modem_type == 'gprs'
										) {
											let gprsIcon = GprsSignalRounded;

											let networkStrengthText = '';
											if (
												row_value.connectivity_status ===
												'online'
											) {
												if (
													row_value.device_signal_strength >
													20
												) {
													networkStrengthText =
														'Strength: ' +
														row_value.device_signal_strength +
														' (Excellent)';
													gprsIcon = GprsSignalRounded5;
												} else if (
													row_value.device_signal_strength >=
														13 &&
													row_value.device_signal_strength <=
														20
												) {
													networkStrengthText =
														'Strength: ' +
														row_value.device_signal_strength +
														' (Good)';
													gprsIcon = GprsSignalRounded4;
												} else if (
													row_value.device_signal_strength >=
														6 &&
													row_value.device_signal_strength <=
														12
												) {
													networkStrengthText =
														'Strength: ' +
														row_value.device_signal_strength +
														' (Poor)';
													gprsIcon = GprsSignalRounded2;
												} else if (
													row_value.device_signal_strength <
													6
												) {
													networkStrengthText =
														'No Signal';
												}
											} else {
												if (
													row_value.device_signal_strength >
													20
												) {
													networkStrengthText =
														`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
														row_value.device_signal_strength +
														` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
												} else if (
													row_value.device_signal_strength >=
														13 &&
													row_value.device_signal_strength <=
														20
												) {
													networkStrengthText =
														`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
														row_value.device_signal_strength +
														` (${this.props.t ? this.props.t('good') : "Good"})`;
												} else if (
													row_value.device_signal_strength >=
														6 &&
													row_value.device_signal_strength <=
														12
												) {
													networkStrengthText =
														`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
														row_value.device_signal_strength +
														` (${this.props.t ? this.props.t('poor') : "Poor"})`;
												} else if (
													row_value.device_signal_strength <
													6
												) {
													networkStrengthText =
														`${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;;
												}
											}

											return (
												<div className="display-flex">
													<AntTooltip
														title={
															networkStrengthText
														}
													>
														<img
															className="mar-right-10"
															src={gprsIcon}
															height={20}
															width={20}
														/>
													</AntTooltip>
													<div>
														<div>GPRS</div>
														<div>{simNo}</div>
													</div>
												</div>
											);
										} else if (
											row_value.device_config
												.device_modem_type == 'wifi'
										) {
											let wifiIcon = WifiSignalRounded;

											let networkStrengthText = '';
											if (
												row_value.connectivity_status ===
												'online'
											) {
												if (
													row_value.device_signal_strength >
													20
												) {
													networkStrengthText =
														'Strength: ' +
														row_value.device_signal_strength +
														' (Excellent)';
													wifiIcon = WifiSignalRounded3;
												} else if (
													row_value.device_signal_strength >=
														13 &&
													row_value.device_signal_strength <=
														20
												) {
													networkStrengthText =
														'Strength: ' +
														row_value.device_signal_strength +
														' (Good)';
													wifiIcon = WifiSignalRounded2;
												} else if (
													row_value.device_signal_strength >=
														6 &&
													row_value.device_signal_strength <=
														12
												) {
													networkStrengthText =
														'Strength: ' +
														row_value.device_signal_strength +
														' (Poor)';
													wifiIcon = WifiSignalRounded1;
												} else if (
													row_value.device_signal_strength <
													6
												) {
													networkStrengthText =
														'No Signal';
												}
											} else {
												if (
													row_value.device_signal_strength >
													20
												) {
													networkStrengthText =
														`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
														row_value.device_signal_strength +
														` (${this.props.t ? this.props.t('excellent') : "Excellent"})`;
												} else if (
													row_value.device_signal_strength >=
														13 &&
													row_value.device_signal_strength <=
														20
												) {
													networkStrengthText =
														`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
														row_value.device_signal_strength +
														` (${this.props.t ? this.props.t('good') : "Good"})`;
												} else if (
													row_value.device_signal_strength >=
														6 &&
													row_value.device_signal_strength <=
														12
												) {
													networkStrengthText =
														`${this.props.t? this.props.t('last_reported_strength') +": ": "Last Reported Strength: "}`+
														row_value.device_signal_strength +
														` (${this.props.t ? this.props.t('poor') : "Poor"})`;
												} else if (
													row_value.device_signal_strength <
													6
												) {
													networkStrengthText =
														`${this.props.t? this.props.t('last_reported_strength_no_signal'): "Last Reported Strength: No Signal"}`;;
												}
											}
											return (
												<div>
													<AntTooltip
														title={
															networkStrengthText
														}
													>
														<img
															className="mar-right-10"
															src={wifiIcon}
															height={20}
															width={20}
														/>
													</AntTooltip>
													<span>Wi-Fi</span>
												</div>
											);
										} else if (
											row_value.device_config
												.device_modem_type == 'ethernet'
										) {
											if (
												row_value.connectivity_status ===
												'online'
											) {
												return (
													<div>
														<img
															className="mar-right-10"
															src={ethernet}
															height={20}
															width={20}
														/>
														<span>Ethernet</span>
													</div>
												);
											} else {
												return (
													<div>
														<img
															className="mar-right-10"
															src={
																ethernet_inactive
															}
															height={20}
															width={20}
														/>
														<span>Ethernet</span>
													</div>
												);
											}
										}
									}
								})()}
							</div>
						</div>
					),
				},
				{
					title: this.props.t? this.props.t('power_status'): 'Power Status',
					width: '10%',
					key: 'power_status',
					align: 'center',
					dataIndex: 'power_status',
					render: (power_status, row_value) => (
						<div className="percentage-holder power-container">
							<div className="icon-holder">
								{(() => {
									if (
										row_value.device_power_status !==
											null &&
										row_value.device_power_status !== false
									) {
										let icon = power_inactive,
											text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('On'): "Last Reported Status: On"}`;
										if (
											row_value.connectivity_status ===
											'online'
										) {
											icon = power_on;
											text = 'Power Status: On';
										}
										return (
											<AntTooltip title={text}>
												<img
													src={icon}
													height={22}
													width={22}
												/>
											</AntTooltip>
										);
									} else if (
										row_value.device_power_status === false
									) {
										let icon = power_inactive,
											text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('Off'): "Last Reported Status: Off"}`;
										if (
											row_value.connectivity_status ===
											'online'
										) {
											icon = power_off;
											text = 'Power Status: Off';
										}
										return (
											<AntTooltip title={text}>
												<img
													src={icon}
													height={22}
													width={22}
												/>
											</AntTooltip>
										);
									} else {
										let text = `${this.props.t? this.props.t('last_reported_status') +": " + this.props.t('unknown'): "Last Reported Status: Unknown"}`;
										if (
											row_value.connectivity_status ===
											'online'
										) {
											text = 'Power Status: Unknown';
										}
										return (
											<AntTooltip title={text}>
												<img
													src={power_inactive}
													height={22}
													width={22}
												/>
											</AntTooltip>
										);
									}
								})()}
							</div>
							<div className="battery-container">
								{(() => {
									if (row_value.device_battery_percent) {
										return (
											<div className="battery mar-left-5">
												<div className="minus-icon">
													-
												</div>
												<div className="bat-body">
													<div
														className={
															'bar' +
															(row_value.device_battery_percent !==
																null &&
															row_value.device_battery_percent >
																0 &&
															row_value.connectivity_status ===
																'online'
																? ' active'
																: ' deactive')
														}
													></div>
													<div
														className={
															'bar' +
															(row_value.device_battery_percent !==
																null &&
															row_value.device_battery_percent >=
																20 &&
															row_value.connectivity_status ===
																'online'
																? ' active'
																: ' deactive')
														}
													></div>
													<div
														className={
															'bar' +
															(row_value.device_battery_percent !==
																null &&
															row_value.device_battery_percent >=
																30 &&
															row_value.connectivity_status ===
																'online'
																? ' active'
																: ' deactive')
														}
													></div>
													<div
														className={
															'bar' +
															(row_value.device_battery_percent !==
																null &&
															row_value.device_battery_percent >=
																40 &&
															row_value.connectivity_status ===
																'online'
																? ' active'
																: ' deactive')
														}
													></div>
													<div
														className={
															'bar' +
															(row_value.device_battery_percent !==
																null &&
															row_value.device_battery_percent >=
																50 &&
															row_value.connectivity_status ===
																'online'
																? ' active'
																: ' deactive')
														}
													></div>
													<div
														className={
															'bar' +
															(row_value.device_battery_percent !==
																null &&
															row_value.device_battery_percent >=
																60 &&
															row_value.connectivity_status ===
																'online'
																? ' active'
																: ' deactive')
														}
													></div>
													<div
														className={
															'bar' +
															(row_value.device_battery_percent !==
																null &&
															row_value.device_battery_percent >=
																70 &&
															row_value.connectivity_status ===
																'online'
																? ' active'
																: ' deactive')
														}
													></div>
													<div
														className={
															'bar' +
															(row_value.device_battery_percent !==
																null &&
															row_value.device_battery_percent >=
																80 &&
															row_value.connectivity_status ===
																'online'
																? ' active'
																: ' deactive')
														}
													></div>
													<div
														className={
															'bar' +
															(row_value.device_battery_percent !==
																null &&
															row_value.device_battery_percent >=
																90 &&
															row_value.connectivity_status ===
																'online'
																? ' active'
																: ' deactive')
														}
													></div>
													<div
														className={
															'bar' +
															(row_value.device_battery_percent !==
																null &&
															row_value.device_battery_percent >=
																100 &&
															row_value.connectivity_status ===
																'online'
																? ' active'
																: ' deactive')
														}
													></div>
												</div>
												<div className="bat-top"></div>
												<div className="plus-icon">
													+
												</div>
											</div>
										);
									}
								})()}
								<div className="bat-charge-details">
									<div className="icon-holder">
										{(() => {
											if (
												row_value.device_battery_percent !==
													null &&
												row_value.device_battery_percent !==
													false &&
												row_value.connectivity_status ===
													'online'
											) {
												return (
													<span>
														{'(' +
															(row_value.device_battery_percent !==
															0
																? row_value.device_battery_percent.toFixed(
																		2
																  )
																: 0) +
															'%)'}
													</span>
												);
											}
										})()}
									</div>
									<div className="icon-holder mar-left-5">
										{(() => {
											if (
												row_value.device_charging_status !==
													null &&
												row_value.device_battery_percent !==
													null
											) {
												if (
													row_value.device_charging_status &&
													row_value.connectivity_status ===
														'online'
												) {
													return (
														<img
															src={
																lighting_active
															}
															height={15}
															width={15}
														/>
													);
												} else {
													return (
														<img
															src={
																lighting_inactive
															}
															height={15}
															width={15}
														/>
													);
												}
											}
										})()}
									</div>
								</div>
							</div>
						</div>
					),
				},
				{
					title: 'SIM',
					width: '6%',
					key: 'sim_details',
					align: 'center',
					dataIndex: 'sim_details',
					render: (sim_details, row_value) =>
						this.renderSimDetails(sim_details, row_value),
				},
				/*{
					title: 'Debug',
					width: '10%',
					align: 'center',
					key: 'health',
					render: (config, row_data) => (
						<div>
							<div className="icon-holder">
								<AntTooltip title="Open Debug">
									<img
										src={debug}
										className="cursor-pointer"
										height={28}
										width={32}
										onClick={() =>
											this.openDeviceDebug(row_data)
										}
									/>
								</AntTooltip>
							</div>
							<div className="device-errors-tags">
								{(() => {
									let errors = [],
										extraTypes = '';
									if (
										row_data.device_error_list &&
										row_data.device_error_list.length
									) {
										row_data.device_error_list.map(
											(errorType, index) => {
												if (index === 0) {
													errors.push(
														<AntTooltip
															title={moment
																.unix(
																	row_data.timestamp
																)
																.tz(
																	'Asia/Kolkata'
																)
																.format(
																	'DD MMM YYYY HH:mm'
																)}
														>
															<span className="mar-lt-5">
																{errorType +
																	', '}
															</span>
														</AntTooltip>
													);
												} else if (index === 1) {
													errors.push(
														<AntTooltip
															title={moment
																.unix(
																	row_data.timestamp
																)
																.tz(
																	'Asia/Kolkata'
																)
																.format(
																	'DD MMM YYYY HH:mm'
																)}
														>
															<span className="mar-lt-5">
																{errorType}
															</span>
														</AntTooltip>
													);
												} else {
													if (
														index ===
														row_data
															.device_error_list
															.length -
															1
													) {
														extraTypes = extraTypes.concat(
															errorType +
																' - ' +
																moment
																	.unix(
																		row_data.timestamp
																	)
																	.tz(
																		'Asia/Kolkata'
																	)
																	.format(
																		'DD MMM YYYY HH:mm'
																	)
														);
													} else {
														extraTypes = extraTypes.concat(
															errorType +
																' - ' +
																moment
																	.unix(
																		row_data.timestamp
																	)
																	.tz(
																		'Asia/Kolkata'
																	)
																	.format(
																		'DD MMM YYYY HH:mm'
																	) +
																', '
														);
													}
												}
											}
										);

										if (
											row_data.device_error_list.length >
											2
										) {
											errors.push(
												<AntTooltip title={extraTypes}>
													<AntTag
														className="mar-lt-5"
														color="red"
													>
														{'+ ' +
															(row_data
																.device_error_list
																.length -
																2) +
															' more'}
													</AntTag>
												</AntTooltip>
											);
										}
									}
									return errors;
								})()}
							</div>
						</div>
					),
				},*/
				{
					title: 'Debug & Configure',
					width: '13%',
					align: 'center',
					key: 'configure',
					render: (config, row_data) => (
						<div className="display-flex just-cntr aln-cntr">
							<div className="icon-holder mar-right-10">
								<AntTooltip title={this.props.t? this.props.t('debug'): "Debug"}>
									<img
										src={debug}
										className="cursor-pointer"
										height={28}
										width={32}
										onClick={() =>
											this.openDeviceDebug(row_data)
										}
									/>
								</AntTooltip>
							</div>
							<div className="icon-holder mar-right-10">
								<AntTooltip title="Raw log">
									<img
										src={raw_log_icon}
										className="cursor-pointer"
										height={28}
										width={32}
										onClick={() =>
											this.openDeviceDebug(row_data, true)
										}
									/>
								</AntTooltip>
							</div>
							{(() => {
								if (/*this.props.application_id !== 17*/ true) {
									if (/*row_data.application_id == 6*/ true) {
										/*let routeLink =
											this.iotBasePath +
											'/enterprise/' +
											this.props.client_id +
											this.platform_slug +
											'/devices/unassigned/' +
											(config.device_config &&
											config.device_config.station_id &&
											config.device_config.station_id
												.length
												? config.device_config
														.station_id[0]
												: row_data.vendor_id
												? row_data.vendor_id
												: 0) +
											'/configuration?app_id=' +
											row_data.application_id;*/
										let routeLink =
											this.iotBasePath +
											'/enterprise/' +
											this.props.client_id +
											this.platform_slug +
											'/devices/unassigned/' +
											row_data.id +
											'/communication?type_id=' +
											row_data.type_id;
										if (this.props.is_application_filter) {
											/*routeLink =
												this.platform_slug +
												'/customer-management/' +
												this.props.customer_id +
												'/applications/' +
												this.state.selected_app +
												'/devices/' +
												(config.device_config &&
												config.device_config
													.station_id &&
												config.device_config.station_id
													.length
													? config.device_config
															.station_id[0]
													: row_data.vendor_id
													? row_data.vendor_id
													: 0) +
												'/configuration?app_id=' +
												row_data.application_id;*/
											routeLink =
												this.iotBasePath +
												'/enterprise/' +
												this.props.client_id +
												this.platform_slug +
												'/customer-management/' +
												this.props.customer_id +
												'/applications/' +
												this.state.selected_app +
												'/devices/' +
												row_data.id +
												'/communication?type_id=' +
												row_data.type_id;
										}
										if (
											row_data.type_id == 11 ||
											row_data.type_id == 12 ||
											row_data.type_id == 51
										) {
											return (
												<AntTooltip title={this.props.t? this.props.t('configure_device'): "Configure Device"}>
													<img
														className="mar-right-10 cursor-pointer"
														src={configuration}
														height={28}
														width={32}
														onClick={() =>
															this.openConfiguration(
																routeLink
															)
														}
													/>
												</AntTooltip>
											);
										}
									} else {
										if (
											row_data.type_id == 11 ||
											row_data.type_id == 12 ||
											row_data.type_id == 51
										) {
											return (
												<AntTooltip title="Configure Device">
													<img
														className="mar-right-10 cursor-block"
														src={configuration}
														height={28}
														width={32}
													/>
												</AntTooltip>
											);
										}
									}
								}
							})()}
							{(() => {
								if (this.props.application_id == 12 || (this.props.enabled_features && this.props.enabled_features.includes('DeviceManagement:CustomCommand'))) {
									return (
										<AntTooltip title="Custom Command">
											<img
												src={custom_command}
												className="cursor-pointer"
												height={28}
												width={32}
												onClick={() =>
													this.openCustomCommand(
														row_data.id,
														row_data
													)
												}
											/>
										</AntTooltip>
									);
								}
							})()}
						</div>
					),
				},
				/*{
					title: 'Device Status',
					width: '10%',
					align: 'center',
					key: 'device_status',
					render: (row_data) => (
						<div>
							<AntTooltip
								title={
									row_data.status_code == 7
										? 'Inactive'
										: 'Active'
								}
							>
								<AntSwitch
									size="medium"
									checkedChildren="Active"
									unCheckedChildren="Inactive"
									checked={
										row_data.status_code == 7 ? false : true
									}
									onChange={(checked, event) =>
										this.onUpdateStatus(
											checked,
											event,
											row_data
										)
									}
								></AntSwitch>
							</AntTooltip>
						</div>
					),
				},*/
				/*{
					title: 'Edit',
					width: '5%',
					align: 'center',
					key: 'device_status',
					render: (row_data) => (
						<span>
							{(() => {
								if (row_data.is_editable) {
									return (
										<EditOutlined
											className="action-icon"
											onClick={() =>
												this.openEditDrawer(row_data)
											}
										/>
									);
								} else {
									return (
										<EditOutlined className="block-icon" />
									);
								}
							})()}
						</span>
					),
				},*/
			];
		}

		let deviceBulkAdd = <div />,
			deviceAdd = <div />;
		if (this.props.getViewAccess(["DeviceManagement:Add"], true)) {
			deviceBulkAdd = (
				<AntButton
					className="bulk-butn"
					type="primary"
					onClick={() => this.openBulkUpload()}
				>
					<PlusOutlined width={'10px'} height={'10px'} /> Bulk Add
				</AntButton>
			);
			deviceAdd = (
				<AntButton
					type={'add'}
					className={
						this.state.application_id === 17 ? 'iot-style' : ''
					}
					onButtonClick={() => this.openAddDrawer()}
					add_button={{
						text: 'Add Device',
						icon: 'plus',
					}}
				/>
			);
			// deviceAdd = (
			// 	<div
			// 		className={
			// 			'btn-controller-new-add ' +
			// 			(this.props.application_id === 17 ? 'iot-style' : '')
			// 		}
			// 		onClick={() => this.openAddDrawer()}
			// 	>
			// 		<span className="width-control-new-add">
			// 			<span className="new-add-btn">
			// 				<PlusOutlined width={'10px'} height={'10px'} />
			// 			</span>
			// 			<span className="new-text">Add Device</span>
			// 		</span>
			// 	</div>
			// );
		}

		let deviceType = _sortBy(this.state.device_types, [
			function (o) {
				return o.name;
			},
		]);

		let filterConfig = [...defaultConfig.datomsXFilterData];

		if (
			this.props.location &&
			Object.keys(this.props.location).length &&
			this.props.location.pathname &&
			this.props.location.pathname.includes('/iot-platform')
		) {
			filterConfig = [...defaultConfig.iotPlatformFilterData];
		}

		filterConfig[0].optionData = [];

		if (deviceType) {
			deviceType.map((type, index) => {
				filterConfig[0].optionData.push({
					title: type.name,
					value: type.id,
				});
			});
		}

		if (
			this.props.location &&
			Object.keys(this.props.location).length &&
			this.props.location.pathname &&
			this.props.location.pathname.includes('/iot-platform')
		) {
			filterConfig[0].selectValue = this.state.selected_type;
			filterConfig[1].selectValue = this.state.selected_connectivity_status;
		} else {
			filterConfig[0].selectValue = this.state.selected_type;
			filterConfig[1].selectValue = this.state.selected_connectivity_status;
			filterConfig[2].selectValue = this.state.selected_device_status;
		}

		let deviceDebug = '';

		// if (this.state.show_device_debug && this.state.selected_row_data) {

		if (
			this.props.location &&
			Object.keys(this.props.location).length &&
			this.props.location.pathname &&
			this.props.location.pathname.includes('/debug')
		) {
			deviceDebug = (
				// <AntDrawer
				// 	title=""
				// 	id="device_debug_drawer"
				// 	className={
				// 		'debug-drawer ' +
				// 		(this.props.collapse ? 'collapsed' : '')
				// 	}
				// 	visible={true}
				// 	mask={true}
				// 	//onClose={() => this.closeDeviceDebug()}
				// 	destroyOnClose={false}
				// 	maskClosable={false}
				// 	closable={false}
				// 	getContainer={false}
				// 	style={{
				// 		overflow: 'auto',
				// 	}}
				// >
				<div className="debug-device-drawer-div">
					<div className="head-section">
						<div className="device-details-container">
							<div className="padding-first">

									<img
										src={ThingCreated}
										height={28}
										width={28}
									/>

								<span>Debug details for </span>
								<span className="device-name">
									{/* {this.state.selected_row_data.qr} */}
									{this.deviceDetails.device_qr_code}
								</span>
								<span className="device-category">
									{/* {this.state.selected_row_data.type_name} */}
									{this.debug_type_name}
								</span>
							</div>
						</div>
					</div>
					<div className="">
						<DeviceDebug
							t={this.props.t}
							application_id={this.props.application_id}
							clientId={this.props.client_id}
							deviceDetails={this.deviceDetails}
							vendor_id={this.props.vendor_id}
							// selected_device={this.state.selected_device}
							// thingDetails={this.props.thingDetails}
							platform_slug={this.platform_slug}
							{...this.props}
						/>
					</div>
				</div>
				// </AntDrawer>
			);
		}
		// }

		let searchConfig = { ...defaultConfig.searchObject };
		searchConfig.value = this.state.table_search;
		if (this.state.selected_rows && this.state.selected_rows.length > 1) {
			searchConfig.disabled = true;
		}

		let filterContainer = <div />,
			searchFilter = <div />;
		if (window.innerWidth < 576) {
			filterContainer = (
				<FilterSelectWithSearch
					filterData={[]}
					searchObject={searchConfig}
					isClear={true}
					applyFilterSelect={(value, index) =>
						this.applyFilterSelect(value, index)
					}
					onSearch={(value) => this.searchBoxFilter(value)}
					clearAllFilter={() => this.clearAllFilter()}
				/>
			);
		} else {
			filterContainer = (
				<FilterSelectWithSearch
					t={this.props.t}
					filterData={filterConfig}
					searchObject={searchConfig}
					isClear={true}
					applyFilterSelect={(value, index) =>
						this.applyFilterSelect(value, index)
					}
					onSearch={(value) => this.searchBoxFilter(value)}
					clearAllFilter={() => this.clearAllFilter()}
				/>
			);
		}

		if (this.props.is_application_filter) {
			searchFilter = (
				<div className="table-search">
					<SearchInput
						placeholder={this.props.t? this.props.t('search_by_qr_or_customer'): "Search by QR or Customer"}
						className={'filter-search-display '}
						value={decodeURI(this.state.table_search)}
						onSearch={(value) => this.searchBoxFilter(value)}
					/>
				</div>
			);
		}

		/*let clearAll = <div />;
		if (
			(this.state.selected_type && this.state.selected_type.length) ||
			(this.state.search_value && this.state.search_value !== '') ||
			this.state.selected_connectivity_status ||
			this.state.selected_device_status
		) {
			clearAll = (
				<div
					className="clear-all-container"
					onClick={() => this.clearAllFilter()}
				>
					<img
						src={clear_all}
						className="clear-all"
						height="13"
						width="23"
					/>
					<span>Clear All Filters</span>
				</div>
			);
		}*/

		let deviceListData = this.state.table_data;

		if (this.state.show_checked) {
			deviceListData = this.state.selected_rows;
		}

		let connectionDetails = (
			<AntPasswordModal
				customBody={modalCustomBody || []}
				isVisible={this.state.third_party_modal_visible}
				title="Connection Details"
				okTitle="Close"
				custom_body_className="device-modal-custom-classname"
				onOk={() => this.toggle3rdPartyModal()}
				icon={<img src={DevicePopKey} alt="logo" />}
			/>
		);
		let assignModalOptions = [
			<div className="assign-to-customer-label-select">
				<span className="customer-select-label">Customer</span>
				<AntSelect
					showSearch
					style={{
						width: 250,
					}}
					className="customer-select"
					placeholder="Select Customer"
					optionFilterProp="children"
					onChange={(e) => this.selectClient(e)}
					onSearch={(e) => this.onSearchClient(e)}
					filterOption={(input, option) =>
						option.props.children
							.toLowerCase()
							.indexOf(input.toLowerCase()) >= 0
					}
				>
					{this.customerSelectionOptions}
				</AntSelect>
			</div>,
			<>
				{(parseInt(this.props.application_id) == 12 || this.props.enabled_features?.includes(
				'IndustryManagement:Dealers'
			)) && this.vendorSelectionOptions.length > 1 && (
						<div className="assign-to-customer-label-select">
							<span className="customer-select-label">
								Partner
							</span>
							<AntSelect
								showSearch
								style={{
									width: 250,
								}}
								className="customer-select"
								placeholder="Select Partner"
								optionFilterProp="children"
								disabled={
									this.vendorSelectionOptions.length == 1 &&
									parseInt(this.state.selected_vendor) == 1
								}
								value={this.state.selected_vendor}
								onChange={(e) => this.selectVendor(e)}
								onSearch={(e) => this.onSearchClient(e)}
								filterOption={(input, option) =>
									option.props.children
										.toLowerCase()
										.indexOf(input.toLowerCase()) >= 0
								}
							>
								{this.vendorSelectionOptions}
							</AntSelect>
						</div>
					)}
			</>,
			<>
				{this.applicationSelectionOptions.length > 1 && (
					<div className="assign-to-application-label-select">
						<span className="application-select-label">
							Application
						</span>
						<AntSelect
							showSearch
							className="application-select"
							style={{
								width: 250,
							}}
							placeholder="Select Application"
							optionFilterProp="children"
							onChange={(e) => this.selectApplication(e)}
							onSearch={(e) => this.onSearchApplication(e)}
							filterOption={(input, option) =>
								option.props.children
									.toLowerCase()
									.indexOf(input.toLowerCase()) >= 0
							}
						>
							{this.applicationSelectionOptions}
						</AntSelect>
					</div>
				)}
			</>,
		];

		let assignToCustomer = (
			<AntModal
				className="assign-to-customer-modal"
				title="Assign To Customer"
				visible={this.state.assign_to_customer}
				onOk={() => this.handleAssignDevicesToCustomer()}
				onCancel={() => this.closeAssignDevicesToCustomer()}
				destroyOnClose={true}
				confirmLoading={this.state.btn_loading}
			>
				{assignModalOptions}
			</AntModal>
		);

		if (window.innerWidth < 576) {
			if (!this.state.mobile_details) {
				assignModalOptions.push(
					this.mobileSelectedDevices(deviceListData)
				);
			}
			assignToCustomer = (
				<AntPasswordModal
					customBody={
						(
							<>
								{this.state.mobile_details ? (
									<div className="title">
										{'Please select a customer below to assign the device ' +
											this.state.mobile_device_selected
												?.qr}
									</div>
								) : (
									''
								)}
								{this.state.assign_to_customer
									? assignModalOptions
									: ''}
							</>
						) || []
					}
					isVisible={this.state.assign_to_customer}
					title={
						!this.state.mobile_details
							? 'Assign Device to Customer'
							: ''
					}
					okTitle="Assign"
					custom_body_className={`assign-to-customer-modal device-modal-custom-classname mobile un-assign firmware-update-modal-new`}
					onOk={() => this.handleAssignDevicesToCustomer()}
					onCancel={() => this.closeAssignDevicesToCustomer()}
					icon={
						<img
							src={assignDeviceImage}
							alt=""
							style={{ width: 50, height: 50 }}
						/>
					}
				/>
			);
		}

		let thirdPartyDeviceModal = (
			<AntModal
				className="upload-device-modal"
				title={
					<div className="display-flex font-bold">
						<img src={third_party_orange} height={20} width={20} />
						<span className="mar-lt-20">3rd Party Device</span>
					</div>
				}
				visible={this.state.bulk_add_device}
				maskClosable={false}
				onOk={() => this.bulkAddDevice()}
				onCancel={() => this.closeBulkAdd()}
				footer={[
					<AntButton
						key="back"
						onClick={() => {
							this.closeBulkAdd();
						}}
					>
						Cancel
					</AntButton>,
					<AntButton
						key="submit"
						type={this.state.fileList.length ? 'primary' : ''}
						disabled={this.state.fileList.length ? false : true}
						loading={this.state.adding_new_device}
						onClick={() => {
							this.bulkAddDevice();
						}}
					>
						Add
					</AntButton>,
				]}
			>
				<div className="upload-device-container">
					<div className="upload-device-label">
						<CloudDownloadOutlined /> Click to download sample file
						in
					</div>
					<div>
						<img
							className="mar-rt-20 download-link"
							onClick={() => {
								this.downloadDemoFile('xlsx');
							}}
							src={xlsx_icon}
							height={50}
							width={40}
						/>
						or
						<img
							className="mar-lt-20 download-link"
							onClick={() => {
								this.downloadDemoFile('csv');
							}}
							src={csv_icon}
							height={50}
							width={40}
						/>
					</div>
				</div>
				<div className="upload-device-container">
					<AntDragger
						accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
						multiple={false}
						name="file"
						onChange={(info) => {
							this.handleBulkAdd(info);
						}}
						fileList={this.state.fileList}
					>
						<p>
							<CloudUploadOutlined className="upload-cloud-icon" />
						</p>
						<p>Click or drag file to this area to upload</p>
						<p>Support for XLSX or CSV file upload.</p>
					</AntDragger>
					{/*<AntUpload
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
        multiple={false}
        name="file"
        onChange={(info) => {
            this.handleBulkAdd(
                info
            );
        }}
        fileList={
            this.state.fileList
        }
        uploadText={
            'Click to upload'
        }
    />*/}
				</div>
			</AntModal>
		);
		let firmwareUpdateNew = (
			<AntPasswordModal
				customBody={firmwareModalBody || []}
				isVisible={this.state.update_firmware}
				title="Firmware update"
				okTitle="Update"
				custom_body_className={`device-modal-custom-classname firmware-update-modal-new ${
					!this.validateFirmwareFields()
						? 'firmware-update-modal-disabled'
						: ''
				}`}
				onOk={() => this.updateDeviceFirmware()}
				onCancel={() => this.closeupdateFirmware()}
				icon={<img src={CloudFirmware} alt="logo" />}
			/>
		);
		let firmwareUpdateOld = (
			<AntModal
				className="update-firmware-modal"
				title="Update Firmware"
				visible={
					// this.state
					// 	.update_firmware
					false
				}
				onOk={() => this.updateDeviceFirmware()}
				onCancel={() => this.closeupdateFirmware()}
				okText="Update"
			>
				<div className="update-firmware-select">
					<span className="update-firmware-select-label">
						Firmware
					</span>
					<AntSelect
						showSearch
						className="firmware-select"
						style={{
							width: 250,
						}}
						placeholder="Select Firmware"
						optionFilterProp="children"
						onChange={(e) => this.selectFirmware(e)}
						onSearch={(e) => this.onSearchFirmware(e)}
						filterOption={(input, option) =>
							option.props.children
								.toLowerCase()
								.indexOf(input.toLowerCase()) >= 0
						}
					>
						{this.firmwareSelectionOptions}
					</AntSelect>
				</div>
			</AntModal>
		);

		if (this.props.application_id == 17 && (this.props.enabled_features && this.props.enabled_features.includes('DeviceManagement:DeviceDeactivate'))) {
			columns.push(
				{
					title: 'Device Status',
					width: '10%',
					align: 'center',
					key: 'device_status',
					render: (row_data) => (
						<div>
							<AntTooltip
								title={
									row_data.status_code == 7
										? 'Inactive'
										: 'Active'
								}
							>
								<AntSwitch
									size="medium"
									checkedChildren="Active"
									unCheckedChildren="Inactive"
									checked={
										row_data.status_code == 7 ? false : true
									}
									onChange={(checked, event) =>
										this.onUpdateStatus(
											checked,
											event,
											row_data
										)
									}
								></AntSwitch>
							</AntTooltip>
						</div>
					),
				},
			);
		}

		return (
			<div
				id="datoms_iot_platform_device"
				className={
					'mar-top-70 unassigned-tab ' +
					(this.props.location &&
					Object.keys(this.props.location).length &&
					this.props.location.pathname &&
					this.props.location.pathname.includes('/iot-platform')
						? 'iot-platform-device-management'
						: this.props.location &&
						  Object.keys(this.props.location).length &&
						  this.props.location.pathname &&
						  this.props.location.pathname.includes('/datoms-x')
						? 'datoms-x-device-management'
						: 'application-device-management')
				}
			>
				<Suspense
					fallback={
						<div>
							<AntLayout className={'contains'}>
								<AntSpin className="align-center-loading" />
							</AntLayout>
						</div>
					}
				>
					{deviceDebug ? (
						deviceDebug
					) : (
						<AntLayout className="mar-top-52">
							<AntContent
								className={
									'contains' +
									(this.state.mobile_drawer
										? ' mobile-drawer-open'
										: '')
								}
							>
								{window.innerWidth < 576 ? (
									this.state.tableVisible &&
									this.device_details ? (
										<>
											<div className="dm_mb_ud_tabs_parent">
												<TwinTabs
													onChange={(value) => {
														let {
															app_name,
														} = this.props;
														this.props.history.push(
															`/${app_name}/devices/${value}`
														);
													}}
													value={'unassigned'}
													tab_array={[
														{
															value: 'Assigned',
															name:
																'Assigned Devices',
														},
														{
															value: 'unassigned',
															name:
																'Unassigned Devices',
														},
													]}
												/>
											</div>
											{false ? (
												<NoDataComponent
													height="100%"
													page_name="device"
												/>
											) : (
												<>
													{!this.state
														.show_no_data ? (
														<div className="filter-select-container">
															{filterContainer}
														</div>
													) : (
														''
													)}
													<MobileDeviceList
														show_no_data={
															this.state
																.show_no_data
														}
														assignToCustomer={(
															mobile_details,
															e
														) =>
															this.assignToCustomer(
																mobile_details,
																e
															)
														}
														openAddDrawer={() =>
															this.openAddDrawer()
														}
														openBulkUpload={() =>
															this.openBulkUpload()
														}
														mobile_drawer_visible={
															this.state
																.mobile_drawer
														}
														details={'unassign'}
														application_id={
															this.props
																.application_id
														}
														is_application_filter={
															this.props
																.is_application_filter
														}
														location={
															this.props.location
														}
														customer_id={
															this.props
																.customer_id
														}
														client_id={
															this.props.client_id
														}
														device_lists={
															this.state
																.device_lists
														}
														renderSimDetails={(
															a,
															b
														) =>
															this.renderSimDetails(
																a,
																b
															)
														}
														deviceListData={
															deviceListData
														}
														singleDeviceFirmwareUpdate={(
															e
														) =>
															this.singleDeviceFirmwareUpdate(
																e
															)
														}
														getViewAccess={this.props.getViewAccess(
															["DeviceManagement:View"]
														)}
														// unassignDevice={(a, b) =>
														// 	this.unassignDevice(a, b)
														// }
														mobileDrawerClicked={(
															e
														) =>
															this.mobileDrawerClicked(
																e
															)
														}
													/>
													{(() => {
														if (
															this.state
																.show_add_edit_drawer ||
															this.props.location.pathname.includes(
																'/add'
															) ||
															this.props.location.pathname.includes(
																'/edit'
															)
														) {
															return (
																<AddDevice
																	fetchDeviceList={() =>
																		this.fetchDeviceList()
																	}
																	edit_device={
																		this
																			.state
																			.edit_device
																	}
																	device_types={
																		this
																			.state
																			.device_types
																	}
																	onClose={() =>
																		this.closeAddEditDrawer()
																	}
																	location={
																		this
																			.props
																			.location
																	}
																	selected_row_data={
																		this
																			.state
																			.selected_row_data
																	}
																	client_id={
																		this
																			.props
																			.client_id
																	}
																/>
															);
														}
													})()}
													{connectionDetails}
													{assignToCustomer}
													{thirdPartyDeviceModal}
													{firmwareUpdateNew}
													{firmwareUpdateOld}
												</>
											)}
										</>
									) : (
										<AntLayout className={'contains'}>
											<AntSpin className="align-center-loading" />
										</AntLayout>
									)
								) : (
									<>
										<div
											role="tablist"
											className="station-tabs-bar"
										>
											<div className="station-tabs-nav-scroll">
												<div className="station-tabs-nav station-tabs-nav-animated">
													<div>
														<div
															role="tab"
															aria-disabled="false"
															aria-selected="true"
															className={
																'station-tabs station-tabs-tab' +
																(this.props
																	.location
																	.pathname &&
																this.props.location.pathname.search(
																	'/devices/assigned'
																) > -1
																	? ' station-tabs-tab-active'
																	: '')
															}
															onClick={() =>
																this.changeGroup(
																	'assigned'
																)
															}
														>
															{this.props.t? this.props.t('Assigned'): "Assigned"}
															{/* Assigned */}
														</div>
														<div
															role="tab"
															aria-disabled="false"
															aria-selected="true"
															className={
																'station-tabs station-tabs-tab' +
																(this.props
																	.location
																	.pathname &&
																this.props.location.pathname.search(
																	'/devices/unassigned'
																) > -1
																	? ' station-tabs-tab-active'
																	: '')
															}
															onClick={() =>
																this.changeGroup(
																	'unassigned'
																)
															}
														>
															{this.props.t? this.props.t('Unassigned'): "Unassigned"}
															{/* Unassigned */}
														</div>
													</div>
													<div className="blank-border"></div>
												</div>
												<span
													className="btn-controller-assign-customer hide"
													onClick={() => {
														this.openAssigntoCustomerDrawer();
													}}
												>
													<span className="width-control-assign-customer">
														<span className="assign-to-customer-btn">
															<UserAddOutlined
																width={'10px'}
																height={'10px'}
															/>
														</span>
														<span className="assign-to-text">
															Assign to Customer1
														</span>
													</span>
												</span>
											</div>
										</div>
										{(() => {
											if (
												this.state.tableVisible &&
												this.device_details
											) {
												return (
													<div>
														<div className="filter-select-container">
															{filterContainer}
														</div>
														{searchFilter}
														<div className="add-butn-container">
															{deviceAdd}
															{deviceBulkAdd}
														</div>
														{(() => {
															if (
																(this.state
																	.deviceTypeFilterValue &&
																	this.state
																		.deviceTypeFilterValue
																		.length) ||
																(this.state
																	.projectTypeFilterValue &&
																	this.state
																		.projectTypeFilterValue
																		.length)
															) {
																return (
																	<div className="section-filter-wrapper display-flex">
																		{(() => {
																			if (
																				this
																					.state
																					.deviceTypeFilterValue &&
																				this
																					.state
																					.deviceTypeFilterValue
																					.length
																			) {
																				return (
																					<AntTreeSelect
																						default_type={
																							true
																						}
																						treeNodeFilterProp={
																							'title'
																						}
																						popupClassName="tree-drop"
																						treeDefaultExpandAll
																						onChange={(
																							value,
																							label,
																							extra
																						) =>
																							this.applyDeviceTypeFilter(
																								value,
																								label,
																								extra
																							)
																						}
																						{...deviceTypeFilterPropsView}
																						className="select-filter device-type"
																					/>
																				);
																			}
																		})()}
																	</div>
																);
															}
														})()}
														{(() => {
															if (
																this.state
																	.add_station
															) {
																return (
																	<AddForm
																		visible={
																			true
																		}
																		addNewDevice={(
																			e
																		) =>
																			this.addNewDevice(
																				e
																			)
																		}
																		closeAddStationModal={(
																			e
																		) =>
																			this.closeAddStationModal(
																				e
																			)
																		}
																		device_cities={
																			this
																				.state
																				.device_cities
																		}
																		unassigned_devices={
																			this
																				.state
																				.unassigned_devices
																		}
																		changeCity={(
																			e
																		) =>
																			this.changeCity(
																				e
																			)
																		}
																		changeStnName={(
																			e
																		) =>
																			this.changeStnName(
																				e
																			)
																		}
																		changeDevice={(
																			e
																		) =>
																			this.changeDevice(
																				e
																			)
																		}
																		changeLat={(
																			e
																		) =>
																			this.changeLat(
																				e
																			)
																		}
																		changeLong={(
																			e
																		) =>
																			this.changeLong(
																				e
																			)
																		}
																		city_id={
																			this
																				.state
																				.city_id
																		}
																		stations_device={
																			this
																				.state
																				.stations_device
																		}
																		station_name={
																			this
																				.state
																				.station_name
																		}
																		station_lat={
																			this
																				.state
																				.station_lat
																		}
																		station_long={
																			this
																				.state
																				.station_lat
																		}
																	/>
																);
															}
														})()}
														<AntRow>
															{(() => {
																if (
																	this.state
																		.selected_devices &&
																	this.state
																		.selected_devices
																		.length &&
																	this.state
																		.added_filtered_device_list &&
																	!this.state
																		.added_filtered_device_list
																		.length
																) {
																	let show_firmware_btn = true;
																	let first_child = this
																		.state
																		.selected_rows[0];
																	this.state.selected_rows.map(
																		(
																			row_value
																		) => {
																			if (
																				first_child.type_id !=
																					row_value.type_id ||
																				first_child.circuit_version !=
																					row_value.circuit_version ||
																				(first_child.type_id ==
																					1 &&
																					first_child.product_model !=
																						row_value.product_model)
																			) {
																				show_firmware_btn = false;
																			}
																		}
																	);
																	return (
																		<div className="table-options">
																			<div className="bulk-selection-option">
																				<div className="device-selected">
																					<span className="device-selected-value">
																						{
																							this
																								.state
																								.selected_rows
																								.length
																						}
																					</span>
																					{this.props.t? this.props.t('devices_selected'): "Devices Selected"}
																					{/* Devices
																					Selected */}
																				</div>
																				<div className="show-checked-switch-container">
																					<AntSwitch
																						className="show-checked-switch"
																						size="small"
																						checked={
																							this
																								.state
																								.show_checked
																						}
																						onChange={(
																							checked,
																							event
																						) =>
																							this.onShowChecked(
																								checked,
																								event
																							)
																						}
																					/>
																					{this.props.t? this.props.t('show_selected_only'): "Show Selected Only"}
																					{/* Show
																					Selected
																					Only */}
																				</div>
																				<div
																					className="deselect-all-btn"
																					onClick={() =>
																						this.deselectAll()
																					}
																				>
																					<StopOutlined className="deselect-icon" />
																					{this.props.t? this.props.t('deselect_all'): "Deselect All"}
																					{/* Deselect
																					All */}
																				</div>
																			</div>
																			<div className="assign-to-customer-firmware mar-top-20">
																				{(() => {
																					if (
																						this.props.location.pathname.includes(
																							'/datoms-x'
																						) ||
																						this.props.getViewAccess(
																							["DeviceManagement:Assign"], true
																						)
																					) {
																						return (
																							<div className="assign-to-customer-parent">
																								<div
																									className="assign-to-customer"
																									onClick={(
																										e
																									) =>
																										this.assignToCustomer()
																									}
																								>
																									<img
																										src={
																											assign
																										}
																										height="18"
																										width="18"
																										className="icon"
																									/>
																									<span className="icon-text">
																										{this.props.t ? this.props.t('assign_to_customer'): "Assign To Customer"}
																										{/* Assign
																										To
																										Customer */}
																									</span>
																								</div>
																								{/*<div className={'view-data-customer' + (this.state.assign_to_customer ? ' height-add' : '')}>
																	</div>*/}
																							</div>
																						);
																					} else {
																						return (
																							<div className="assign-to-customer-parent disable-btn">
																								<div className="assign-to-customer">
																									<img
																										src={
																											assign
																										}
																										height="18"
																										width="18"
																										className="icon"
																									/>
																									<span className="icon-text">
																										{this.props.t? this.props.t('assign_to_customer'): "Assign To Customer"}
																										{/* Assign
																										To
																										Customer */}
																									</span>
																								</div>
																								{/*<div className={'view-data-customer' + (this.state.assign_to_customer ? ' height-add' : '')}>
																	</div>*/}
																							</div>
																						);
																					}
																				})()}
																				{(() => {
																					if (
																						this.props.location.pathname.includes(
																							'/datoms-x'
																						) || this.props.enabled_features?.includes(
																							"DeviceManagement:OTA"
																						)
																					) {
																						if (
																							show_firmware_btn
																						) {
																							return (
																								<div className="assign-to-firmware-parent">
																									<div
																										className="assign-to-firmware"
																										onClick={(
																											e
																										) =>
																											this.updateFirmware()
																										}
																									>
																										<img
																											src={
																												update_firmware
																											}
																											height="18"
																											width="22"
																											className="icon"
																										/>
																										<span className="icon-text">
																											Update
																											Firmware
																										</span>
																									</div>
																									{/*<div className={'view-data-firmware' + (this.state.update_firmware ? ' height-add' : '')}>
																			</div>*/}
																								</div>
																							);
																						}
																					}
																				})()}
																				{(() => {
																					if (
																						// this.props.location.pathname.includes(
																						// 	'/datoms-x'
																						// ) ||
																						(parseInt(this.props.client_id) === 392 || parseInt(this.props.client_id) === 2696) &&
																						this.props.getViewAccess(
																							["DeviceManagement:Delete"]
																						)
																					) {
																						return (
																							<div className="assign-to-tag-parent">
																								<div
																									className="assign-to-tag"
																									onClick={() =>
																										this.deleteDevice()
																									}
																								>
																									<img
																										src={
																											device_delete
																										}
																										height="15"
																										width="15"
																										className="icon"
																									/>
																									<span className="icon-text">
																										Delete
																									</span>
																								</div>
																							</div>
																						);
																					} else {
																						return '';
																					}
																				})()}
																				{(() => {
																					if (
																						this
																							.state
																							.selected_rows &&
																						this
																							.state
																							.selected_rows
																							.length ===
																							1 &&
																						this
																							.state
																							.selected_rows[0]
																							.type_id ===
																							17
																					) {
																						return (
																							<div className="assign-to-tag-parent">
																								<div
																									className="assign-to-tag"
																									onClick={() =>
																										this.openEditDrawer(
																											this
																												.state
																												.selected_rows[0]
																										)
																									}
																								>
																									<EditOutlined />
																									<span className="icon-text">
																										Edit
																									</span>
																								</div>
																							</div>
																						);
																					}
																				})()}
																				{/*<div className="show-container selected-device-count">
																({this.state.selected_devices.length} devices selected)
															</div>*/}
																			</div>
																		</div>
																	);
																}
															})()}
															{connectionDetails}
															{assignToCustomer}
															{
																thirdPartyDeviceModal
															}
															{firmwareUpdateNew}
															{firmwareUpdateOld}
															{/*<div className="online-toggle-container">
												<span className="text-online-device">Show Online Devices Only</span>
												<Switch size="small" className="online-toggle" defaultChecked={(this.state.date_time_online == true) ? true : false} onChange={() => this.toggleShowOnlineDevices()} />
											</div>*/}
														</AntRow>
														<AntRow>
															{/*(() => {
												if (this.state.table_data && this.state.table_data.length) {
													return <div className="show-container absolute-position-count">
														{(() => {
															if (this.filtered_stations.length == this.device_details.length) {
																return <span> <span>Total Devices</span><span className="show-txt">{this.device_details.length}</span></span>;
															} else {
																return <span> Showing <span className="show-txt">{this.filtered_stations.length}</span> out of <span className="show-txt">{this.device_details.length}</span></span>;
															}
														})()}
														{(() => {
															if (this.state.selected_devices && this.state.selected_devices.length) {
																return <div className="show-container selected-device-count">
																	({this.state.selected_devices.length} devices selected)
																</div>;
															}
														})()}
													</div>;
												}
											})()*/}
															<AntTable
																className={
																	'table-width-minus unassigned' +
																	(this.state
																		.table_data &&
																	this.state
																		.table_data
																		.length
																		? ' device-table'
																		: ' table-data-view') +
																	(this.state
																		.table_data &&
																	this.state
																		.table_data
																		.length ==
																		0
																		? ' mar-top-40'
																		: '')
																}
																columns={
																	columns
																}
																locale={{
																	emptyText:
																		this.props.t? this.props.t('no_devices_found!'): 'No Devices Found!',
																}}
																loading={
																	this.state
																		.remove_loading
																		? false
																		: true
																}
																/*scroll={{ x: 900, y: 800 }}*/
																sticky={true}
																pagination={
																	this
																		.device_table_pagination_config
																}
																dataSource={
																	deviceListData
																}
																onChange={(
																	pagination,
																	filters,
																	sorter,
																	extra
																) =>
																	this.handleTableDataChange(
																		pagination,
																		filters,
																		sorter,
																		extra
																	)
																}
																rowSelection={
																	tableRowSelectionOptions
																}
															/>
														</AntRow>
													</div>
												);
											} else {
												return (
													<AntLayout
														className={'contains'}
													>
														<AntSpin className="align-center-loading" />
													</AntLayout>
												);
											}
										})()}
										{(() => {
											if (
												this.state
													.show_add_edit_drawer ||
												this.props.location.pathname.includes(
													'/add'
												) ||
												this.props.location.pathname.includes(
													'/edit'
												)
											) {
												return (
													<AddDevice
														fetchDeviceList={() =>
															this.fetchDeviceList()
														}
														edit_device={
															this.state
																.edit_device
														}
														device_types={
															this.state
																.device_types
														}
														onClose={() =>
															this.closeAddEditDrawer()
														}
														location={
															this.props.location
														}
														selected_row_data={
															this.state
																.selected_row_data
														}
														client_id={
															this.props.client_id
														}
													/>
												);
											}
										})()}
										<AntDrawer
											className="add-device-to-customer"
											maskClosable={false}
											title={
												<span className="add-customer-title-container">
													<span className="add-to-customer-title">
														Add device to Customer
													</span>
													<span className="add-select-customers">
														<div className="filter-container">
															<div className="select-client-for-device">
																<AntSelect
																	showSearch
																	// style={{ width: 350 }}
																	className="customer-select"
																	placeholder="Select Customer"
																	optionFilterProp="children"
																	onChange={(
																		e
																	) =>
																		this.selectClient(
																			e
																		)
																	}
																	onSearch={(
																		e
																	) =>
																		this.onSearchClient(
																			e
																		)
																	}
																	filterOption={(
																		input,
																		option
																	) =>
																		option.props.children
																			.toLowerCase()
																			.indexOf(
																				input.toLowerCase()
																			) >=
																		0
																	}
																>
																	{
																		this
																			.customerSelectionOptions
																	}
																</AntSelect>
																<AntSelect
																	showSearch
																	className="application-select"
																	// style={{ width: 250 }}
																	placeholder="Select Application"
																	optionFilterProp="children"
																	onChange={(
																		e
																	) =>
																		this.selectApplication(
																			e
																		)
																	}
																	onSearch={(
																		e
																	) =>
																		this.onSearchApplication(
																			e
																		)
																	}
																	filterOption={(
																		input,
																		option
																	) =>
																		option.props.children
																			.toLowerCase()
																			.indexOf(
																				input.toLowerCase()
																			) >=
																		0
																	}
																>
																	{
																		this
																			.applicationSelectionOptions
																	}
																</AntSelect>
															</div>
														</div>
													</span>
												</span>
											}
											onClose={() =>
												this.closeAssigntoCustomerDrawer()
											}
											visible={
												this.state
													.add_device_to_customer
											}
										>
											<AntRow className="display-flex-device-container">
												<AntCol
													span={12}
													className="left-assign-device-section"
												>
													<AntRow className="table-filter-search-row">
														<div className="table-search">
															<AntInput
																placeholder="Search Devices"
																className={
																	'filter-search-display' +
																	(this.state
																		.table_search
																		? ' active-search'
																		: '')
																}
																prefix={
																	<SearchOutlined />
																}
																value={decodeURI(
																	this.state
																		.drawer_search_value
																)}
																onChange={(e) =>
																	this.drawerSearchResults(
																		e.target
																			.value
																	)
																}
																suffix={
																	this.state
																		.table_search ? (
																		<CloseOutlined
																			className="close-icon"
																			onClick={() => {
																				this.clearSearchValue();
																			}}
																		/>
																	) : (
																		''
																	)
																}
															/>
														</div>
														<div className="table-filter">
															<AntTreeSelect
																default_type={
																	true
																}
																treeNodeFilterProp={
																	'title'
																}
																treeDefaultExpandAll
																onChange={(
																	value,
																	label,
																	extra
																) =>
																	this.applyProjectTypeFilterChild(
																		value,
																		label,
																		extra
																	)
																}
																{...projectTypeFilterPropsChild}
																popupClassName="small-font-size"
																className="select-filter1 filter-icon filter-display"
															/>
														</div>
														<div className="table-filter">
															<AntTreeSelect
																default_type={
																	true
																}
																treeNodeFilterProp={
																	'title'
																}
																treeDefaultExpandAll
																onChange={(
																	value,
																	label,
																	extra
																) =>
																	this.applyDeviceTypeFilterChild(
																		value,
																		label,
																		extra
																	)
																}
																{...deviceTypeFilterPropsChild}
																popupClassName="small-font-size"
																className="select-filter1 filter-icon filter-display"
															/>
														</div>
													</AntRow>
													{(() => {
														if (
															(this.state
																.deviceTypeFilterValueChild &&
																this.state
																	.deviceTypeFilterValueChild
																	.length) ||
															(this.state
																.projectTypeFilterValueChild &&
																this.state
																	.projectTypeFilterValueChild
																	.length)
														) {
															return (
																<div className="section-filter-wrapper display-flex">
																	{(() => {
																		if (
																			this
																				.state
																				.projectTypeFilterValueChild &&
																			this
																				.state
																				.projectTypeFilterValueChild
																				.length
																		) {
																			return (
																				<AntTreeSelect
																					default_type={
																						true
																					}
																					treeNodeFilterProp={
																						'title'
																					}
																					popupClassName="tree-drop"
																					treeDefaultExpandAll
																					onChange={(
																						value,
																						label,
																						extra
																					) =>
																						this.applyProjectTypeFilterChild(
																							value,
																							label,
																							extra
																						)
																					}
																					{...projectTypeFilterPropsChild}
																					className="select-filter online-status"
																				/>
																			);
																		}
																	})()}
																	{(() => {
																		if (
																			this
																				.state
																				.deviceTypeFilterValueChild &&
																			this
																				.state
																				.deviceTypeFilterValueChild
																				.length
																		) {
																			return (
																				<AntTreeSelect
																					default_type={
																						true
																					}
																					treeNodeFilterProp={
																						'title'
																					}
																					popupClassName="tree-drop"
																					treeDefaultExpandAll
																					onChange={(
																						value,
																						label,
																						extra
																					) =>
																						this.applyDeviceTypeFilterChild(
																							value,
																							label,
																							extra
																						)
																					}
																					{...deviceTypeFilterPropsChild}
																					className="select-filter device-type"
																				/>
																			);
																		}
																	})()}
																</div>
															);
														}
													})()}
													{(() => {
														if (
															this.state
																.filtered_drawer_stations_list &&
															this.state
																.filtered_drawer_stations_list
																.length
														) {
															return (
																<AntRow className="selection-btn-with-count">
																	<AntCol className="selected-count">
																		{this
																			.state
																			.filtered_drawer_stations_list
																			.length +
																			' devices' +
																			(this
																				.state
																				.selectedRows &&
																			this
																				.state
																				.selectedRows
																				.length
																				? ' (' +
																				  this
																						.state
																						.selectedRows
																						.length +
																				  ' device' +
																				  (this
																						.state
																						.selectedRows
																						.length >
																				  1
																						? 's'
																						: '') +
																				  ' selected)'
																				: '')}
																	</AntCol>
																	<AntCol className="selected-action">
																		<AntButton
																			type="primary"
																			onClick={() => {
																				this.bulkaddDeviceToAssigned(
																					this
																						.state
																						.selectedRowKeysChild
																				);
																			}}
																		>
																			Add
																		</AntButton>
																	</AntCol>
																</AntRow>
															);
														}
													})()}
													<AntTable
														className="table-unassigned-list"
														rowSelection={
															rowSelectionTableAssign
														}
														columns={
															columns_child_list
														}
														dataSource={
															this.state
																.assign_child_data
														}
														pagination={false}
														scroll={{
															y:
																'calc(100vh - 355px)',
														}}
													/>
													{/*<div className="device-menu">
											<div className="qr-code-column">QR Code</div>
											<div className="project-code-column">Project</div>
											<div className="type-code-column">Device Type</div>
											<div className="action-column">Action</div>
										</div>
										<div className="unassigned-device-container">
											{(() => {
												if (this.state.filtered_drawer_stations_list && this.state.filtered_drawer_stations_list.length) {
													return this.state.filtered_drawer_stations_list.map((device, ind) => {
														console.log('deviceeeee', device);
														return <div className="device-data-row">
															<div className="device-data-qr">{device.qr_code ? device.qr_code : ' - '}</div>
															<div className="device-data-project">{' - '}</div>
															<div className="device-data-type">{' - '}</div>
															<div className="device-data-action"><PlusCircleOutlined className="action-btn-icon" onClick={() => {this.addDeviceToAssigned(device)}} /></div>
														</div>
													});
												} else {
													return <div className="no-unassigned-device-found">No Unassigned Devices Found !</div>
												}
												console.log('state.filtered_drawer_stations_list', this.state.filtered_drawer_stations_list);
											})()}
										</div>*/}
												</AntCol>
												<AntCol
													span={12}
													className="rignt-assign-device-section"
												>
													{(() => {
														if (
															this.state
																.added_filtered_device_list &&
															this.state
																.added_filtered_device_list
																.length
														) {
															return (
																<div>
																	<AntRow className="">
																		<div className="device-added">
																			Device
																			Added
																		</div>
																	</AntRow>
																	<AntTable
																		columns={
																			columns_child_add
																		}
																		dataSource={
																			this
																				.state
																				.assign_child_data_add
																		}
																		pagination={
																			false
																		}
																		scroll={{
																			y:
																				'calc(100vh - 285px)',
																		}}
																	/>
																	{/*<div className="unassigned-device-container">
														{(() => {
															if (this.state.added_filtered_device_list && this.state.added_filtered_device_list.length) {
																return this.state.added_filtered_device_list.map((device, ind) => {
																	return <div className="device-data-row assigned-row">
																		<div className="device-data-qr">{device.qr_code ? device.qr_code : ' - '}</div>
																		<div className="device-data-type">{' - '}</div>
																		<div className="device-data-action"><MinusCircleOutlined className="action-btn-icon red-close" onClick={() => {this.removeDeviceToAssigned(device)}}/></div>
																	</div>
																});
															}
														})()}
													</div>*/}
																</div>
															);
														} else {
															return (
																<div className="no-device-added-msg">
																	No devices
																	added yet !
																</div>
															);
														}
													})()}
												</AntCol>
											</AntRow>
											<div
												style={{
													position: 'absolute',
													left: 0,
													bottom: 0,
													width: '100%',
													borderTop:
														'1px solid #e9e9e9',
													padding: '10px 16px',
													background: '#fff',
													textAlign: 'right',
												}}
											>
												<AntButton
													onClick={() => {
														this.closeAssigntoCustomerDrawer();
													}}
													style={{ marginRight: 8 }}
												>
													Cancel
												</AntButton>
												<AntButton
													disabled={
														this.state
															.device_assignment_customer_id >
															0 &&
														this.state
															.device_assignment_application_id >
															0 &&
														this.state
															.added_filtered_device_list &&
														this.state
															.added_filtered_device_list
															.length
															? false
															: true
													}
													style={{ color: '#fff' }}
													onClick={(e) =>
														this.submitAssignDrawer(
															e
														)
													}
													type="primary"
												>
													Assign
												</AntButton>
											</div>
										</AntDrawer>
									</>
								)}
							</AntContent>
						</AntLayout>
					)}
				</Suspense>
				{/* {deviceDebug} */}
				{window.innerWidth < 576 ? (
					<AntPasswordModal
						isVisible={this.state.sim_details_drawer}
						icon={<img src={SimMobileIcon} alt="" />}
						onCancel={() =>
							this.setState({ sim_details_drawer: false })
						}
						onOk={this.updateSimDetails}
						okTitle="Save"
						cancelTitle="Cancel"
						custom_body_className="simdetails-drawer device-modal-custom-classname mobile un-assign firmware-update-modal-new"
						customBody={
							<div className="sub_drawer_content">
								<p className={'sub_drawer_sim_heading'}>
									SIM 1
								</p>
								<div className="sub_drawer_each_sim">
									<p className={'sub_drawer_text'}>
										Serial No.
									</p>
									<AntInput
										className={
											'sub_drawer_sim_details_input'
										}
										name={'sim_srno_1'}
										value={
											this.state.sim_details &&
											this.state.sim_details[0]?.serial_no
										}
										onChange={(e) =>
											this.onChangeSimDetails(
												1,
												'serial_no',
												e.target.value
											)
										}
									/>
								</div>
								<div className="sub_drawer_each_sim">
									<p className={'sub_drawer_text'}>
										Operator
									</p>
									<AntInput
										className={
											'sub_drawer_sim_details_input'
										}
										name={'sim_operator_1'}
										value={
											this.state.sim_details &&
											this.state.sim_details[0]?.operator
										}
										onChange={(e) =>
											this.onChangeSimDetails(
												1,
												'operator',
												e.target.value
											)
										}
									/>
								</div>
								<p className={'sub_drawer_sim_heading'}>
									SIM 2
								</p>
								<div className="sub_drawer_each_sim">
									<p className={'sub_drawer_text'}>
										Serial No.
									</p>
									<AntInput
										className={
											'sub_drawer_sim_details_input'
										}
										name={'sim_srno_2'}
										value={
											this.state.sim_details &&
											this.state.sim_details[1]?.serial_no
										}
										onChange={(e) =>
											this.onChangeSimDetails(
												2,
												'serial_no',
												e.target.value
											)
										}
									/>
								</div>
								<div className="sub_drawer_each_sim">
									<p className={'sub_drawer_text'}>
										Operator
									</p>
									<AntInput
										className={
											'sub_drawer_sim_details_input'
										}
										name={'sim_operator_2'}
										value={
											this.state.sim_details &&
											this.state.sim_details[1]?.operator
										}
										onChange={(e) =>
											this.onChangeSimDetails(
												2,
												'operator',
												e.target.value
											)
										}
									/>
								</div>
							</div>
						}
						getContainer={false}
					/>
				) : (
					<AntDrawer
						className="simdetails-drawer"
						visible={this.state.sim_details_drawer}
						title={'SIM Details'}
						onClose={() =>
							this.setState({ sim_details_drawer: false })
						}
					>
						<div className="sub_drawer_content">
							<p className={'sub_drawer_sim_heading'}>SIM 1</p>
							<div className="sub_drawer_each_sim">
								<p className={'sub_drawer_text'}>Serial No.</p>
								<AntInput
									className={'sub_drawer_sim_details_input'}
									name={'sim_srno_1'}
									value={
										this.state.sim_details &&
										this.state.sim_details[0]?.serial_no
									}
									onChange={(e) =>
										this.onChangeSimDetails(
											1,
											'serial_no',
											e.target.value
										)
									}
								/>
							</div>
							<div className="sub_drawer_each_sim">
								<p className={'sub_drawer_text'}>Operator</p>
								<AntInput
									className={'sub_drawer_sim_details_input'}
									name={'sim_operator_1'}
									value={
										this.state.sim_details &&
										this.state.sim_details[0]?.operator
									}
									onChange={(e) =>
										this.onChangeSimDetails(
											1,
											'operator',
											e.target.value
										)
									}
								/>
							</div>
							<p className={'sub_drawer_sim_heading'}>SIM 2</p>
							<div className="sub_drawer_each_sim">
								<p className={'sub_drawer_text'}>Serial No.</p>
								<AntInput
									className={'sub_drawer_sim_details_input'}
									name={'sim_srno_2'}
									value={
										this.state.sim_details &&
										this.state.sim_details[1]?.serial_no
									}
									onChange={(e) =>
										this.onChangeSimDetails(
											2,
											'serial_no',
											e.target.value
										)
									}
								/>
							</div>
							<div className="sub_drawer_each_sim">
								<p className={'sub_drawer_text'}>Operator</p>
								<AntInput
									className={'sub_drawer_sim_details_input'}
									name={'sim_operator_2'}
									value={
										this.state.sim_details &&
										this.state.sim_details[1]?.operator
									}
									onChange={(e) =>
										this.onChangeSimDetails(
											2,
											'operator',
											e.target.value
										)
									}
								/>
							</div>
						</div>

						<div className="drawer-actions-box">
							<AntButton
								type="primary"
								className="apply-btn"
								onClick={this.updateSimDetails}
								// loading={footProps.loading}
							>
								Save
							</AntButton>
						</div>
						{/*<div className="sub_drawer_logs">*/}
						{/*	<p>Logs</p>*/}
						{/*	<span>*/}
						{/*		First data received at{' '}*/}
						{/*	</span>*/}
						{/*</div>*/}
					</AntDrawer>
				)}
			</div>
		);
	}
}
