// @import 'device-debug.less'

.draw-btn-container {
	position: absolute;
	bottom: 0;
	width: 100%;
	border-top: 1px solid #e8e8e8;
	padding: 10px 16px;
	text-align: right;
	left: 0;
	background: #fff;
	border-radius: 0 0 4px 4px;
}

.blocked-menu {
	cursor: not-allowed !important;
}

.ant-form {
	width: 100%;
}

.add-items {
	display: inline-flex;
	// align-items: center;
	// width: 50%;
}

.add-city-field {
	display: inline-flex;
	// align-items: center;
	// width: 100%;
}

.add-device-to-customer {
	.ant-drawer-content-wrapper {
		width: 65% !important;

		.ant-drawer-header {
			padding-left: 45px;

			.add-customer-title-container {
				.add-select-customers {
					.filter-container {
						display: inline-block;
						width: calc(100% - 220px);
						font-weight: normal;

						.select-client-for-device {
							margin-bottom: 0px;
						}
					}
				}
			}
		}
	}

	.select-client-for-device {
		margin-bottom: 10px;
		padding: 0px 40px;

		.filter-container {
			// display: flex;
			align-items: center;
		}

		.customer-select {
			width: 40%;
			margin-right: 15px;
			display: inline-block;
		}

		.application-select {
			width: 40%;
			display: inline-block;
		}
	}

	.display-flex-device-container {
		display: flex;
		height: calc(100vh - 175px);
		transition: 0.5s all;
		// align-items: baseline;

		.unassigned-device-container {
			overflow: auto;
			max-height: calc(100vh - 300px);
		}

		.no-device-added-msg {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			font-size: 16px;
			font-weight: 600;
			color: #c4c2c2;
		}

		.no-unassigned-device-found {
			font-size: 16px;
			font-weight: 600;
			color: #c4c2c2;
			text-align: center;
		}

		.left-assign-device-section {
			margin-top: 10px;
			padding-right: 20px;
			border-right: 1px solid #ddd;
			margin-right: 20px;
			padding: 0px 30px 0 20px;
			transition: 0.5s all;

			.table-filter-search-row {
				margin-bottom: 5px;
			}

			.table-search {
				width: auto;
				display: inline-block;
				padding-bottom: 0px;
				// margin-right: 10px;
				// margin-bottom: 15px;
				transition: 0.75s all;

				.filter-search-display {
					border-radius: 8px;
					border: 1px solid transparent;
					transition: 0.5s all;

					input {
						height: 28px;
						font-size: 12px;
						background-color: #edf1f2;
						border: 1px solid transparent;
						border-radius: 8px;
						transition: 0.5s all;
						width: 8vw;

						&:hover {
							background-color: #edf1f2b3;
							border-color: #1da57ae3;
						}

						// &:focus, &:active {
						// 	background-color: rgba(237, 241, 242, 0.2);
						// 	border-color: #1DA57A;
						// 	box-shadow: unset;
						// 	width: 35vw;
						// }
					}

					i {
						font-size: 12px;
					}
				}

				.active-search {
					input {
						background-color: rgba(237, 241, 242, 0.2);
						border-color: #1da57a;
						font-weight: 600;
					}
				}

				.ant-input-group-addon {
					height: 28px;
					border: 1px solid transparent;
					border-radius: 8px;
					outline: none;
					padding: 0;
					border: none;

					.addon-filter-type-class {
						border: 1px solid transparent;
						height: 28px;
						border-radius: 8px;
						font-size: 12px;
						width: 0 !important;
						overflow: hidden;
						padding: 0;
						margin: 0;
						border: none;
					}
				}
			}

			/*.table-filter {
				width: 25%;
				margin-right: 16px;
				display: block;
				float: left;
				transition: 0.5s all;

				.filter-icon:before {
					content: '';
					position: absolute;
					display: inline-table;
					width: 11px;
					height: 11px;
					float: left;
					background: transparent
						url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 44 44'><path d='M43.7.3A1 1 0 0 0 43 0H1.1a1 1 0 0 0-.8.3L.1.6 0 1c0 .*******.7L15 22.3v21.1c.******* 1 .6.3 0 .6-.1.7-.3l12-12c.3-.2.3-.5.3-.8v-8.6L43.7 1.7c.4-.4.4-1 0-1.4zm-16.4 21a1 1 0 0 0-.3.7v8.6l-10 10V22c0-.2 0-.5-.3-.7L3 2h38.2L27.3 21.3z'></path></svg>")
						no-repeat 100%;
					top: 9px;
					left: 12px;
					z-index: 9;
					opacity: 0.75;
				}

				.ant-select-selector {
					cursor: pointer;
					padding-left: 20px;
					border: 1px solid transparent;
					background-color: #edf1f2 !important;
					border-radius: 8px !important;
					box-shadow: unset;
					min-height: 28px;

					.ant-select-selection-placeholder {
						left: 32px;
						display: block !important;
						color: #808080ab;
						font-size: 12px;
						line-height: 20px;
					}
				}
			}

			.filter-display {
				border: 1px solid transparent;
				border-radius: 8px;
				transition: 0.5s all;
				width: 100%;

				&:hover {
					.ant-select-selector {
						background-color: #edf1f2b3;
					}
					border: 1px solid #1da57ae3;
				}

				&:focus,
				&:active {
					.ant-select-selector {
						background-color: rgba(237, 241, 242, 0.2);
					}
					border: 1px solid #1da57a;
				}

				.ant-select-selection-item {
					display: none;
				}
			}*/

			.table-unassigned-list {
				margin-top: 5px;
			}

			.section-filter-wrapper {
				display: flex;
				// justify-content: center;
				align-items: center;
				margin-bottom: 12px;

				.filter-icon {
					font-size: 20px;
					margin-right: 10px;
				}

				.select-filter {
					&::before {
						display: none !important;
					}

					&:focus,
					&:hover,
					&:active {
						box-shadow: none !important;
						outline: none !important;

						.ant-select-selector {
							box-shadow: none !important;
							outline: none !important;
						}
					}

					.ant-select-selector {
						background: transparent;
						border: none;
						cursor: initial !important;

						&:focus,
						&:hover,
						&:active {
							box-shadow: none !important;
							outline: none !important;
						}
					}

					.ant-select-selection-search {
						display: none !important;
					}

					.ant-select-selection-item {
						color: #fff !important;
						border-radius: 10px !important;
						font-size: 12px !important;

						.ant-select-selection-item-remove {
							color: rgba(255, 255, 255, 0.5);
							transition: all 0.3s;

							&:hover {
								color: rgba(255, 255, 255, 1);
							}
						}
					}
				}

				.online-status {
					&::before {
						display: none !important;
					}

					.ant-select-selection__choice {
						background: #07adb1 !important;
						margin-right: 10px;
					}
				}

				.device-type {
					&::before {
						display: none !important;
					}

					.ant-select-selection__choice {
						background: #43429a !important;
						margin-right: 10px;
					}
				}

				.application-type {
					.ant-select-selection__choice {
						background: #56bc7b !important;
						margin-right: 10px;
					}
				}

				.customer-name {
					.ant-select-selection__choice {
						background: #000000 !important;
						margin-right: 10px;
					}
				}
			}

			.selection-btn-with-count {
				margin-bottom: 15px;
				margin-top: 10px;

				.selected-count {
					display: inline-block;
					line-height: 32px;
					font-size: 12px;
					color: #808080;
				}

				.selected-action {
					display: block;
					float: right;
				}
			}
		}

		.rignt-assign-device-section {
			margin-top: 20px;
			padding-right: 15px;
			padding: 0px 35px 0 10px;

			.device-added {
				/* background-color: #f1f1f1; */
				padding: 0px 10px;
				/* border-radius: 5px; */
				font-weight: 600;
				padding-bottom: 30px;
				line-height: 0px;
			}
		}

		.device-data-row {
			display: flex;
			align-items: center;
			background-color: rgba(234, 244, 255, 0.78);
			padding: 10px 20px;
			border-radius: 30px;
			margin-bottom: 12px;
			/* box-shadow: 4px 4px 10px 0px rgba(221,221,221,0.58); */

			.device-data-qr {
				width: 30%;
				display: inline-block;
			}

			.device-data-project {
				width: 25%;
				display: inline-block;
				text-align: center;
			}

			.device-data-type {
				width: 25%;
				display: inline-block;
				text-align: center;
			}

			.device-data-action {
				width: 20%;
				text-align: center;

				.action-btn-icon {
					cursor: pointer;
					font-size: 16px;
					color: #008000a6;
				}

				.red-close {
					color: #ff0000eb;
				}
			}
		}

		.assigned-row {
			background-color: rgba(0, 255, 231, 0.102);
		}
	}

	.device-menu {
		background-color: #fafafa;
		padding: 10px 20px;
		font-weight: 600;
		margin: 15px 0;
		width: 100%;
		border-bottom: 1px solid #ddd;
		border-radius: 4px;

		.qr-code-column {
			width: 30%;
			display: inline-block;
		}

		.project-code-column {
			width: 25%;
			display: inline-block;
			text-align: center;
		}

		.type-code-column {
			width: 25%;
			display: inline-block;
			text-align: center;
		}

		.action-column {
			width: 20%;
			display: inline-block;
			text-align: center;
		}
	}

	.device-data-action {
		width: 20%;
		text-align: center;

		.action-btn-icon {
			cursor: pointer;
			font-size: 16px;
			color: #008000a6;
		}

		.red-close {
			color: #ff0000eb;
		}
	}
}

.device-status-update-modal {
	.ant-modal-content {
		border-radius: 12px;
		font-size: 16px !important;

		.ant-modal-confirm-body {
			.ant-modal-confirm-title {
				border-top-left-radius: 4px !important;
				border-top-right-radius: 4px !important;
				border-bottom: 1px solid #e3e3e3 !important;
				display: flex;
				align-items: center;
				padding-bottom: 15px;
				font-size: 18px !important;

				img {
					margin-right: 15px;
				}
			}
		}

		.ant-btn {
			border-radius: 8px !important;
		}
	}
}

#datoms_iot_platform_device {
	.not-recognised {
		text-align: center;
		margin-left: -150px;
		font-size: 24px;
		color: #808080;
	}

	.break-word {
		overflow-wrap: anywhere !important;
	}

	.vendor-tag {
		font-size: 10px;
		border-radius: 5px;
		line-height: 15px !important;
		overflow: hidden;
		text-overflow: ellipsis;
		width: fit-content;
		color: #2f54eb;
		background: #f0f5ff;
		border: 1px solid #adc6ff;
		text-align: center;
		padding: 1px 5px;
	}

	.mar-right-10 {
		margin-right: 10px;
	}

	.font-bold {
		font-weight: bold !important;
	}

	.mar-lt-5 {
		margin-left: 5px;
	}

	.mar-rt-5 {
		margin-right: 5px;
	}

	.mar-top-40 {
		margin-top: 40px !important;
	}

	.mar-top-20 {
		margin-top: 20px !important;
	}

	.cursor-pointer {
		cursor: pointer;
	}

	.aln-cntr {
		align-items: center !important;
	}

	.cursor-block {
		cursor: not-allowed;
	}

	.font-12 {
		font-size: 12px !important;
	}

	.col-808080 {
		color: #808080 !important;
	}

	.top-minus-10 {
		margin-top: -10px !important;
	}

	.just-cntr {
		justify-content: center !important;
	}

	.contains {
		margin: 0px;
		padding: 5px 10px;
		padding-bottom: 10px;
		background-color: #fff;
		position: relative;
		transition: all 0.5s;
		height: calc(100vh - 52px);
		overflow: hidden;
		overflow-y: auto;

		.add-edit-device-drawer {
			position: fixed;
		}

		&.customer-details {
			padding-top: 0 !important;
			min-height: calc(100vh - 170px) !important;
			height: calc(100vh - 170px) !important;
		}

		&.collapsed-side {
			margin-left: 80px;
		}

		.table-width-minus {
			width: 100%;
			.ant-table {
				width: 100%;
			}
		}

		.unassigned {
			.ant-pagination-mini .ant-pagination-item {
				margin: 0 3px !important;
			}
			.ant-table {
				width: 100%;

				.ant-table-content {
					width: 100%;
					max-height: calc(100vh - 330px) !important;
				}
			}
			.dashboard-icon {
				.anticon-line-chart {
					font-size: 20px;
				}
			}
		}

		.assigned {
			.ant-pagination-mini .ant-pagination-item {
				margin: 0 3px !important;
			}
		}

		.location-name {
			color: #c4c2c2;
			font-size: 15px;
			font-weight: 600;
		}

		/*.device-type {
			margin-right: 15px;
			&::before {
				display: none !important;
			}
		}*/

		.ant-select-multiple {
			.ant-select-selection-item-content {
				font-size: 12px !important;
			}
		}

		.online-status {
			&::before {
				display: none !important;
			}
		}

		.device-qr-code {
			margin-right: 15px;
		}

		.device-name {
			color: #872626;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			width: 100%;
		}

		.qr-code-title {
			margin-left: 20px;
			font-size: 12px;
			font-weight: 500;
		}

		.qr-code-value {
			margin-left: 5px;
			font-weight: 500;
		}

		.device-sync-icon {
			position: absolute;
			display: inline-block;
			right: 145px;
			z-index: 4;
			top: 70px;

			&.cust-det {
				top: 50px !important;
			}
		}

		.configure-icon {
			color: #cacaca;
			cursor: pointer;
			transition: all 0.3s;
		}

		.configure-icon:hover {
			color: #ff7d3f;
		}

		.blocked {
			color: #cacaca;
			cursor: not-allowed;
		}

		.device-data-center {
			text-align: center;
		}

		.display-flex {
			display: flex !important;
			justify-content: unset;
		}

		.table-data-view {
			margin-top: 25px;
		}
	}

	.custom-configure {
		height: calc(100vh - 52px);
	}

	.custom-configure-device {
		.clear {
			clear: both;
		}

		.action-delete {
			cursor: pointer;
		}

		.icon-red {
			color: red;
		}

		.icon-green {
			color: green;
		}

		.delete-all {
			top: -30px;
			font-size: 12px !important;
		}

		.main-container-data {
			.custom-table-data {
				width: 35%;
				float: left;
				padding-right: 20px;
				border-right: 1px solid #ddd;

				.custom-table {
					.ant-table-content {
						width: 100%;

						.ant-table-scroll {
							max-height: calc(100vh - 200px);
						}
					}
				}
			}

			.form-data-main {
				width: 65%;
				float: right;
				padding-left: 20px;

				.form-row-data {
					margin-top: 5px;
					margin-bottom: 25px;

					.form-col-data {
						display: flex;
						align-items: end;

						.form-item-label {
							text-align: left;
							vertical-align: middle;
							display: inline-block;
							color: rgba(0, 0, 0, 0.85);
							margin-right: 10px;
							font-weight: 500;
							width: 60px;
						}

						.form-item-select {
							width: 80%;
						}

						.form-item-text-area {
							width: 95%;
						}

						.form-item-preview {
							width: 100%;
							height: 60px;
							background-color: #fafafa;
							border-radius: 4px;
							padding: 5px 15px;
							font-size: 14px;
						}
					}

					.error-message-packet {
						text-align: center;
						margin-top: 10px;
						height: 25px;

						.message {
							margin-left: 8px;
						}

						.message-with-icon-success {
							color: green;
						}

						.message-with-icon-failure {
							color: red;
						}
					}
				}

				.ant-btn-primary[disabled],
				.ant-btn-primary.disabled:hover {
					color: #ffffffab;
				}

				.form-data-item-btn-content {
					float: right;
				}
			}
		}
	}

	.configure-device {
		.station-view {
			overflow: hidden;
			overflow-y: auto;
			height: calc(100vh - 185px);

			&.customer-det {
				height: calc(100vh - 275px);
			}

			.heading-data {
				font-size: 18px;
				font-weight: bold;
				color: #ff880e;
				padding: 5px 10px;
				width: 100%;
				margin-bottom: 10px;
				border-bottom: 1px solid #ddd;
			}

			.modem-type-error-msg {
				color: red;
				margin-top: 48px;

				.err-msg {
					font-size: 14px;
					margin-left: 10px;
				}
			}
		}
	}

	.assign-container {
		height: 54px;
		width: 500px;
		position: absolute;
		right: 255px;
		display: flex;
		align-items: center;
	}

	.status-update {
		width: 150px;

		.status-update-select {
			font-size: 12px;

			.ant-select-selection__rendered {
				margin-left: 4px !important;
			}
		}
	}

	.tags-update {
		width: 150px;

		.device-tags-update {
			font-size: 12px;

			.ant-select-selection__rendered {
				margin-left: 4px !important;
			}
		}
	}

	.danger-message {
		font-size: 14px;
		color: #f00;
		font-weight: 600;
		background-color: #dddddd8c;
		padding: 10px 20px;
		border-radius: 20px;
	}

	.health-tab {
		.ant-list-item {
			font-size: 13px;
			padding: 10px 8px;
			font-weight: 600;
		}
	}

	.assign-to-customer-status {
		display: flex;
		align-items: center;
		margin-top: 10px;
		margin-bottom: 0px;
		top: 2px;
		// position: absolute;
		// right: 75px;
		z-index: 1;
		margin-right: 20px;
	}

	.assign-to-customer-tags {
		display: flex;
		align-items: center;
		margin-top: 10px;
		margin-bottom: 0px;
		top: 2px;
		// position: absolute;
		// right: 75px;
		z-index: 1;
		margin-right: 20px;
	}

	.assign-to-customer-firmware {
		display: flex;
		align-items: center;
		margin-top: 20px;
		z-index: 1;
		position: absolute;
		left: 20px;

		&.cust-detls {
			//top: 110px !important;
			top: 88px !important;
		}

		.assign-to-customer-parent,
		.assign-to-firmware-parent,
		.assign-to-status-parent,
		.assign-to-tag-parent {
			.assign-to-customer,
			.assign-to-firmware,
			.assign-to-status,
			.assign-to-tag {
				display: flex;
				align-items: center;
				font-size: 13px;
				cursor: pointer;
				color: #1818c9;

				&.delete {
					color: #f25a59 !important;
				}

				.icon {
					margin-right: 5px;
				}

				.icon-text {
					color: #7686a1;
					margin-right: 20px;
				}
			}

			.assign-to-status {
				color: #7686a1;
			}

			.view-data-customer,
			.view-data-firmware,
			.view-data-status,
			.view-data-tag {
				height: 0;
				transition: 0.5s height;
				background-color: #fff;

				&.height-add {
					height: 200px;
					position: absolute;
					width: 140px;
					background-color: #fff;
					z-index: 1;
					overflow: hidden;
					overflow-y: auto;
				}
			}
		}

		.disable-btn {
			.assign-to-customer,
			.assign-to-firmware,
			.assign-to-tag {
				cursor: not-allowed !important;
				color: #6b6b6b !important;

				&.delete {
					color: #6b6b6b !important;
				}
			}
			.assign-to-status {
				color: #6b6b6b !important;
			}
		}
	}

	.assign-unassigned-devices {
		border-radius: 20px;
		font-size: 12px !important;
		background: #c4c2c2 !important;
		border: 1px solid #c4c2c2 !important;
	}

	.assign-unassigned-devices:hover {
		background: #9f9c9c !important;
		border: 1px solid #9f9c9c !important;
	}

	.search-filter-component {
		width: auto !important;

		.filter-with-search-container {
			.select-filter-wrapper {
				.multiple-select-drop {
					width: 230px !important;
				}
			}
		}
	}

	.table-options {
		margin-left: 10px;

		.bulk-selection-option {
			display: flex;
			align-items: center;
			font-size: 12px !important;

			.device-selected {
				.device-selected-value {
					border: 1px solid;
					padding: 0px 6px;
					border-radius: 50%;
					margin-right: 10px;
					color: #f58740;
				}
			}

			.show-checked-switch-container {
				display: flex;
				align-items: center;
				color: #f58740;
				margin-left: 20px;

				.show-checked-switch {
					margin-right: 5px;
				}
			}

			.deselect-all-btn {
				cursor: pointer;
				color: #f58740;
				margin-left: 20px;
				display: flex;
				align-items: center;

				.deselect-icon {
					margin-right: 5px;
					color: #f58740;
					font-size: 16px;
				}
			}
		}
	}

	.device-table {
		width: 100%;

		.mar-left-5 {
			margin-left: 5px !important;
		}

		.ant-table-thead {
			background: #f2eeee !important;
		}

		.ant-table-thead > tr > th {
			padding: 5px 10px !important;
		}

		.ant-table-tbody > tr > td {
			padding: 5px 10px !important;
		}

		.ant-table-column-sorters {
			padding: 7px 10px !important;
			padding-left: 0 !important;
		}

		.ant-table-tbody > tr > td:first-child,
		.ant-table-tbody > tr > td:nth-child(2) {
			background: #e7ecf4 !important;
			vertical-align: top;
		}

		&.cust-detl {
			.ant-table-content {
				height: calc(100vh - 325px);
			}
		}

		.device-errors-tags {
			font-size: 11px !important;
			color: #f5222d;

			.ant-tag {
				font-size: 11px !important;
				padding: 0px 4px;
				line-height: 14px;
			}
		}

		.percentage-holder {
			display: flex;
			align-items: center;
			justify-content: center;
			&.online-padding-left {
				justify-content: flex-start;
				padding-left: 20%;
			}
			.opacity-3 {
				opacity: 0.3 !important;
			}

			.percent-icon {
				margin: 0 5px;
			}

			.icon-holder {
				margin: 0 10px;
			}
		}

		.power-container {
			align-items: flex-start !important;

			.icon-holder {
				margin-right: 0 !important;
			}

			.battery-container {
				.battery {
					display: flex;
					align-items: center;
					justify-content: center;
					margin-bottom: 10px;

					.minus-icon {
						margin-right: 5px;
					}

					.bat-body {
						width: 38px;
						padding: 0 2px;
						height: 18px;
						border-radius: 4px;
						border: 1px solid #707070;
						display: flex;
						align-items: center;
						justify-content: center;

						.bar {
							width: 5px;
							height: 14px;
							margin-right: 1px;
							background: #c4c2c2;

							&.active {
								background: #21d99a !important;
							}

							&.deactive {
								background: #c4c2c2 !important;
							}
						}
					}

					.bat-top {
						height: 10px;
						width: 5px;
						border: 1px solid #707070;
						border-radius: 15px;
						border-left: none;
					}

					.plus-icon {
						margin-left: 5px;
					}
				}

				.bat-charge-details {
					display: flex;
					align-items: center;
				}
			}
		}

		.waiting {
			color: #f58740;
		}

		.third-party-icon {
			background: #7687a0;
			border-radius: 20px;
			padding: 2px 5px;
			width: 80px;
			display: flex;
			align-items: center;
			cursor: pointer;
			margin-left: 6px;

			span {
				display: flex;
				align-items: center;
			}

			img {
				margin-right: 5px;
			}

			.third-party-text {
				width: 50px;
				font-size: 10px;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				color: #fff;
			}
		}

		.updated {
			color: #1da57a;
			margin-left: 10px;
		}

		.not-updated {
			cursor: pointer;
			margin-left: 10px;
		}

		.mar-left-10 {
			margin-left: 10px;
		}

		.proceed {
			svg {
				fill: #9e9494;
				stroke: #9e9494;
				transition: all 0.5s;

				&:hover {
					fill: #ff830d;
					stroke: #ff830d;
				}
			}
		}
	}

	.add-btn {
		display: inline-block;
		margin-left: 10px;
	}

	.device-modem-type.match {
		svg {
			fill: green;
		}
	}

	.device-modem-type.no-match {
		svg {
			fill: #ff830d;
		}
	}

	.station-tabs-tab {
		text-align: center;
	}

	.station-tabs-bar {
		//border-bottom: 1px solid #e8e8e8;
		margin: 0 0 16px 0;
		outline: none;
		transition: padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
		margin-bottom: 20px;
		font-size: 14px;
	}

	.table-txt {
		width: 160px;
		display: inline-block;
	}

	.station-tabs-tab {
		cursor: pointer;
		display: inline-block;
		// height: 100%;
		margin: 0;
		border: 1px solid #e8e8e8;
		border-bottom: 0;
		border-radius: 4px 4px 0 0;
		background: #fafafa;
		margin-right: 2px;
		padding: 0 16px;
		transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
		line-height: 38px;
	}

	.station-tabs-tab-active {
		background: #fff;
		border-color: #e8e8e8;
		// color: #1DA57A;
		color: #f58740;
		font-weight: 600;
		border-bottom: 1px solid white;
		z-index: 20;
	}

	.blank-border {
		border-bottom: 1px solid #ddd;
		z-index: 10;
		margin-top: -1px;
	}

	.primary {
		color: #f00;
	}

	.success {
		color: #45c27e;
	}

	.warning {
		color: #faad14;
	}

	.danger {
		color: #f5222d;
	}

	.white {
		background-color: #fff;
	}

	.mobile-hide {
		display: block;
	}

	.mobile-show {
		display: none;
	}

	.mar-top-72 {
		margin-top: 72px;
	}

	.mar-top-52 {
		margin-top: 0px;
	}

	.back-grey {
		background-color: #fafafa;
	}

	.ant-layout {
		background-color: #f2f8f9;
	}

	.action-img {
		width: 9px;
		cursor: pointer;
	}

	.contain {
		margin: 30px 30px;
		margin-left: 230px;
		text-align: center;
	}

	.head {
		color: #808080;
		width: 100%;
		font-size: 16px;
		margin-bottom: 20px;
		font-weight: bold;
	}

	.device-device {
		.head {
			margin-left: 7px;
		}
	}

	.ant-form-item-label {
		font-weight: normal;
	}

	.dot1 {
		display: inline-block;
		height: 12px;
		width: 12px;
		margin-left: -7px;
		margin-right: 10px;
		border-radius: 60px;
		margin-bottom: 0px;
	}

	.dot1.online {
		border: 2px solid #45c27e;
	}

	.dot1.offline {
		border: 2px solid #f25a59;
	}

	.rows {
		margin-top: 20px;
	}

	.cols {
		text-align: center;
		margin: 5px 11px;
		margin-bottom: 0;
	}

	.label {
		text-align: left;
		font-weight: bold;
	}

	.option {
		text-align: left;
	}

	// .device-details {
	// 	margin-bottom: 50px;
	// }
	.upper-section {
		margin-bottom: 40px;
	}

	.pie-text {
		text-align: center;
	}

	.highcharts-container {
		margin: 0 auto;
	}

	.online-device {
		font-size: 18px;
	}

	.activity-details {
		background: #f7f7f7;
	}

	.activity-container {
		overflow-y: scroll;
		max-height: 290px;
	}

	.activities {
		padding: 5px 0;
		border-bottom: 1px solid #e8e8e8;

		&:last-child {
			border-bottom: none;
		}
	}

	.table-filter {
		width: 15%;
		margin-right: 10px;
		display: block;
		float: left;

		.ant-select {
			width: 100%;
		}

		.ant-select-search__field__placeholder {
			left: 25px;
		}
	}

	.ant-form-vertical .ant-form-item-control {
		text-align: left;
	}

	.table-search {
		width: auto;
		margin-top: 20px;

		.filter-search-display {
			width: auto;
			.ant-input-group-addon {
				display: none;
			}
		}

		.ant-input-affix-wrapper {
			width: auto !important;
			border: none;
			box-shadow: none;
			position: relative;

			.ant-input-prefix {
				position: absolute;
				z-index: 2;
				top: 11px;
				left: 20px;
			}

			.ant-input-suffix {
				position: absolute;
				right: 20px;
				top: 11px;
				z-index: 2;
			}

			.ant-input {
				background-color: #fff !important;
				width: 200px !important;
				transition: all 0.5s;
				padding: 0;
				padding-left: 30px;
				font-size: 12px;
				line-height: 10px;
				height: 28px;
				border-radius: 10px;
				border: 1px solid transparent;
				box-shadow: 9px 9px 16px rgb(230, 230, 230);

				&:focus,
				&:active {
					width: 400px !important;
					background-color: #fff !important;
					border: 1px solid #f58740;
					box-shadow: 9px 9px 16px rgb(230, 230, 230);
				}

				&:hover {
					border: 1px solid #f58740;
					box-shadow: 9px 9px 16px rgb(230, 230, 230);
				}
			}
		}
	}

	.width-change-filter {
		width: 100px;
	}

	.table-filter {
		width: 10%;
		margin: 0 10px;
		position: relative;

		&:before {
			content: '';
			position: absolute;
			display: inline-table;
			width: 11px;
			height: 11px;
			float: left;
			background: transparent
				url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 44 44'><path d='M43.7.3A1 1 0 0 0 43 0H1.1a1 1 0 0 0-.8.3L.1.6 0 1c0 .*******.7L15 22.3v21.1c.******* 1 .6.3 0 .6-.1.7-.3l12-12c.3-.2.3-.5.3-.8v-8.6L43.7 1.7c.4-.4.4-1 0-1.4zm-16.4 21a1 1 0 0 0-.3.7v8.6l-10 10V22c0-.2 0-.5-.3-.7L3 2h38.2L27.3 21.3z'></path></svg>")
				no-repeat 100%;
			top: 10px;
			left: 12px;
			z-index: 9;
			opacity: 0.75;
		}

		&:hover,
		&:focus,
		&:active {
			.ant-select-selector {
				border: 1px solid #ffab6b !important;
			}
		}

		/*.ant-select-selection-item, .ant-select-selection-search {
			display: none !important;
		}*/

		.ant-select-selection--multiple {
			min-height: 28px;
		}

		.ant-select-selector {
			background-color: #edf1f2 !important;
			border: 1px solid transparent !important;
			border-radius: 8px !important;
			box-shadow: none;
			outline: none;

			&:focus,
			&:active,
			&hover {
				border: 1px solid #ffab6b !important;
			}
		}

		.ant-select-selection-placeholder {
			display: block !important;
			padding-left: 15px;
			font-size: 12px;
			line-height: 20px;
		}

		.ant-select-selection-item {
			color: #bfbfbf !important;
			border-radius: 10px !important;
			font-size: 12px !important;
			margin-left: 15px !important;
			background: none !important;

			.ant-select-selection-item-remove {
				color: rgba(255, 255, 255, 0.5);
				transition: all 0.3s;

				&:hover {
					color: rgba(255, 255, 255, 1);
				}
			}
		}
	}

	.filter-select-container {
		display: flex;
		flex-wrap: wrap;
		position: relative;

		/*.clear-all-container {
			margin-top: 8px;
			margin-left: 15px;
			display: flex;
			align-items: center;
			height: fit-content;
			cursor: pointer;

			img {
				margin-right: 10px;
			}

			span {
				color: #f58740;
			}
		}*/

		.filter-search {
			margin-top: 0px !important;

			.search-container {
				margin-top: 0 !important;

				.ant-input-affix-wrapper {
					padding-top: 2px !important;

					.ant-input-prefix {
						top: 12px !important;
					}

					.ant-input {
						box-shadow: none !important;
						background: #f5f6f8 !important;
						height: 33px !important;

						&::placeholder {
							color: #7686a1 !important;
						}
					}
				}
			}
		}
	}

	.close-icon {
		cursor: pointer;
		opacity: 0.65;
	}

	.close-icon:hover {
		opacity: 1;
		font-weight: 700;
	}

	.filter-display {
		border: 1px solid transparent;
		border-radius: 8px;
		transition: 0.5s all;

		&:hover {
			.ant-select-selection {
				background-color: #edf1f2b3;
			}
		}

		&:focus,
		&:active {
			.ant-select-selection {
				background-color: rgba(237, 241, 242, 0.2);
			}
			border: 1px solid #1da57a;
		}

		.ant-select-selection__rendered {
			display: none;
		}

		.ant-select-selection__choice {
			display: none;
		}
	}

	.table-customer-select {
		.ant-select-open {
			.ant-select-search__field__placeholder {
				display: none !important;
			}
		}

		.ant-select-selection__rendered {
			display: inline-block;
			line-height: 22px;
			margin-left: 10px !important;

			.ant-select-search__field__wrap {
				input {
					height: 20px;
				}
			}
		}
	}

	.table-filter-unassigned {
		width: 20%;
		margin-right: 10px;
		display: block;
		float: left;

		.ant-select-search__field__placeholder {
			left: 25px;
		}
	}

	.table-search-unassigned {
		width: 45%;
		display: inline-block;
		padding-bottom: 20px;
	}

	.btn-controller-assign-customer {
		width: 45px;
		/* position: absolute; */
		/* width: 100px; */
		float: right;
		margin-top: -45px;
		display: flex;
		align-items: center;
		position: relative;
		overflow: hidden;
		transition: 0.5s all;

		.width-control-assign-customer {
			/*position: absolute;
			top: 28px;
			right: 30px;*/
			cursor: pointer;
			border: 1px solid transparent;
			padding: 0 5px;
			border-radius: 30px;
			width: auto;
			font-size: 12px;
			padding: 4.5px 0px;
			text-align: left;
			/* width: 230px; */
			margin-left: 0px;
			transition: 0.5s all;
			// position: relative;
			margin-left: 5px;
			height: 40px;
			width: 45px;

			.assign-to-customer-btn {
				/* position: absolute; */
				cursor: pointer;
				font-size: 20px;
				border: 1px solid;
				padding: 0 5px;
				border-radius: 30px;
				color: #808080;
				transition: 0.5s all;
				/* float: left; */
				padding: 4.8px 8px;
				margin-left: -1px;
				/*width: 30px;
				height: 30px;*/
			}

			.assign-to-text {
				right: -137px;
				transition: 0.5s all;
				position: absolute;
				/* right: 10px; */
				padding: 6px 0;
			}
		}
	}

	.assign-to-customer-btn:hover {
		color: #666464;
		// right: 130px;
	}

	.btn-controller-assign-customer:hover {
		width: 175px;
	}

	.btn-controller-assign-customer:hover > .width-control-assign-customer {
		border: 1px solid #808080;
		/* transition: 0.1s all !important; */
		margin-left: 4px;
		width: 200px;
	}

	.btn-controller-assign-customer:hover
		> .width-control-assign-customer
		> .assign-to-text {
		// transform: scale(1);
		// font-size: 12px;
		right: 15px;
		/* font-size: 12px; */
	}

	.unassigned-filter-icon:before {
		content: '';
		position: absolute;
		display: inline-table;
		width: 12px;
		height: 16px;
		float: left;
		background: transparent
			url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 44 44'><path d='M43.7.3A1 1 0 0 0 43 0H1.1a1 1 0 0 0-.8.3L.1.6 0 1c0 .*******.7L15 22.3v21.1c.******* 1 .6.3 0 .6-.1.7-.3l12-12c.3-.2.3-.5.3-.8v-8.6L43.7 1.7c.4-.4.4-1 0-1.4zm-16.4 21a1 1 0 0 0-.3.7v8.6l-10 10V22c0-.2 0-.5-.3-.7L3 2h38.2L27.3 21.3z'></path></svg>")
			no-repeat 100%;
		top: 8px;
		left: 8px;
		z-index: 9;
	}

	.ant-select-selection__placeholder,
	.ant-select-search__field__placeholder {
		font-weight: normal;
	}

	.ant-select-selection--multiple .ant-select-selection__rendered {
		margin-left: 25px;
	}

	.select-icon:before {
		content: '';
		position: absolute;
		display: inline-table;
		width: 18px;
		height: 18px;
		float: left;
		background: transparent
			url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path fill='#666' d='M15.5 14h-.8l-.3-.3c1-1 1.6-2.6 1.6-4.2C16 6 13 3 9.5 3S3 6 3 9.5 6 16 9.5 16c1.6 0 3-.6 4.2-1.6l.3.3v.8l5 5 1.5-1.5-5-5zm-6 0C7 14 5 12 5 9.5S7 5 9.5 5 14 7 14 9.5 12 14 9.5 14z'></path></svg>")
			no-repeat 100%;
		top: 8px;
		left: 8px;
		z-index: 9;
	}

	.ant-tabs:not(.ant-tabs-vertical) > .ant-tabs-content > .ant-tabs-tabpane {
		font-size: 18px;
		font-weight: bold;
		text-align: center;
	}

	.ant-tabs.ant-tabs-card > .ant-tabs-bar .ant-tabs-tab {
		color: #808080;
	}

	.ant-tabs.ant-tabs-card > .ant-tabs-bar .ant-tabs-tab-active {
		color: #1da57a;
	}

	.ant-tabs-nav .ant-tabs-tab:hover {
		color: #1da57a !important;
	}

	.analytics-image,
	.device-image,
	.sync-img {
		cursor: pointer;
	}

	.analytics-image path,
	.device-image path {
		transition: 0.3s;
	}

	.analytics-image:hover path {
		fill: #1da57a;
	}

	.device-image:hover path {
		stroke: #1da57a;
	}

	.ant-table {
		.ant-table-content {
			width: 100%;
		}
	}

	.ant-table::-webkit-scrollbar {
		height: 7px;
	}

	.ant-table-header {
		font-size: 14px;
		top: -5px !important;
	}

	.ant-table-tbody {
		font-size: 13px;
	}

	.ant-table-selection-column {
		min-width: 10px !important;
		width: 10px !important;
		max-width: 30px !important;
		text-align: left;

		.ant-table-selection {
			width: 22px;
			display: inline-block;
		}
	}

	.butn-contain {
		width: 100%;
		border-top: 1px solid #e8e8e8;
		padding: 10px 0px;
		text-align: right;
		border-radius: 0 0 4px 4px;
	}

	.dot {
		display: inline-block;
		border: 1px solid #45c27e;
		height: 9px;
		width: 9px;
		margin-left: -7px;
		margin-right: 3px;
		border-radius: 60px;
		margin-bottom: 0;
	}

	.dot.online {
		border-color: #45c27e;
	}

	.dot.offline {
		border-color: #f25a59;
	}

	.table-status-text {
		font-size: 13px;
		color: #45c27e;
	}

	.table-status-text.online {
		color: #45c27e;
	}

	.table-status-text.offline {
		color: #f25a59;
	}

	.sync-setting {
		cursor: pointer;
	}

	.date-time {
		font-size: 11px;
		display: block;
	}

	.show-container {
		padding-bottom: 10px;
		font-size: 13px;
		color: #808080;
		display: inline-block;
	}

	.show-container.selected-device-count {
		padding-top: 0px;
		padding-left: 15px;
	}

	.absolute-position-count {
		margin-bottom: -60px;
		margin-top: 30px;
	}

	.online-toggle-container {
		float: right;
		margin-left: 15px;
		margin-bottom: 10px;
		margin-top: 5px;
		margin-right: 10px;

		.text-online-device {
			font-size: 13px;
			font-weight: 500;
			margin-right: 10px;
			color: #808080;
		}
	}

	.action-icon {
		cursor: pointer;
		transition: 0.5s all;
		font-size: 18px;

		&:hover {
			color: #f58740;
		}
	}

	.online-toggle {
		background-color: rgb(231, 231, 231);
		min-width: 25px;
	}

	.online-toggle:before,
	.online-toggle:after {
		top: 0.5px;
	}

	.add-butn-container {
		float: right;
		clear: both;
		position: relative;
		margin-right: 10px;
		margin-top: -78px;

		.bulk-butn {
			font-size: 13px !important;
			cursor: pointer;
			z-index: 10;
			margin-top: 1px;
		}

		.blocked-butn {
			cursor: not-allowed !important;
			background: #ddd !important;
			border-color: #ddd;
			color: #808080 !important;
		}

		.btn-block {
			right: 140px;
			cursor: not-allowed !important;
			z-index: 10;

			.width-control-blocked-add {
				.blocked-add-btn {
					padding: 6px 9px;
				}
			}
		}

		.btn-controller-new-add {
			width: 45px;
			right: 140px;
			top: 0px;
			display: flex;
			align-items: center;
			position: absolute;
			overflow: hidden;
			transition: 0.5s all;
			z-index: 10;

			.width-control-new-add {
				cursor: pointer;
				border: 1px solid transparent;
				padding: 0 5px;
				border-radius: 30px;
				width: auto;
				font-size: 12px;
				padding: 4.5px 0px;
				text-align: left;
				margin-left: 0px;
				transition: 0.5s all;
				margin-left: 5px;
				height: 40px;
				width: 45px;
				height: 39px;

				.new-add-btn {
					cursor: pointer;
					font-size: 20px;
					border: 1px solid;
					padding: 0 5px;
					border-radius: 30px;
					color: #808080;
					transition: 0.5s all;
					padding: 4.8px 8px;
					margin-left: -1px;

					padding: 6.8px 8px;
					color: #fff;
					border: 1px solid #f58740;
					background: #f58740;
					font-size: 18px;

					.anticon {
						vertical-align: -2px !important;
					}
				}

				.new-text {
					left: 137px;
					width: 150px;
					transition: 0.5s all;
					position: absolute;
					padding: 6px 0;
					height: 40px;
				}
			}

			&.iot-style {
				.width-control-new-add {
					padding: 4.5px 0px !important;
					margin-left: 5px !important;

					.new-add-btn {
						padding: 5px 9px !important;
						margin-left: -2px !important;
					}
				}
			}
		}

		.new-add-btn:hover {
			color: #666464;
		}

		.btn-controller-new-add:hover {
			width: 145px;
		}

		.btn-controller-new-add:hover > .width-control-new-add {
			border: 1px solid #808080;
			margin-left: 4px;
			width: 200px;
		}

		.btn-controller-new-add:hover > .width-control-new-add > .new-text {
			left: 50px;
		}
	}

	.ant-switch-checked {
		background-color: #f58740;
	}

	.show-txt {
		padding: 0 10px;
	}

	.form-txt {
		padding-right: 5px;
	}

	.border-top {
		border-top: 1px solid #d9d9d9;
		padding-top: 10px;
	}

	.btn-contain {
		display: block;
		padding: 10px 0;
		padding-bottom: 20px;
		text-align: left;
	}

	.calibration-form {
		.ant-form-item-label {
			font-weight: normal;
		}
	}

	.ant-row .ant-form-item {
		padding-top: 10px;
	}

	.right {
		float: right;
		display: flex;

		.ant-form-vertical .ant-form-item {
			margin-bottom: 20px;
			padding-bottom: 0px;
		}

		.ant-form-item-control {
			text-align: right;
			width: 12px;
			padding-top: 20px;
			cursor: pointer;

			.anticon-close {
				font-size: 20px;
				cursor: pointer;
				transition: 0.5s;
			}

			.anticon-close:hover {
				color: #1da57a;
			}
		}
	}

	.device-device {
		position: relative;
	}

	.back-btn {
		position: absolute;
		display: inline-block;
		right: 26px;
		z-index: 3;

		.ant-btn {
			display: flex;
			align-items: flex-end;
		}
	}

	.input-local-class-out {
		font-family: 'Monospaced Number', 'Chinese Quote', -apple-system,
			BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC',
			'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica,
			Arial, sans-serif;
		font-size: 14px;
		line-height: 1.5;
		color: rgba(0, 0, 0, 0.65);
		margin: 0;
		padding: 0;
		list-style: none;
		height: 32px;
		position: relative;
		outline: none;
		user-select: none;
		box-sizing: border-box;
		display: block;
		background-color: #fff;
		border-radius: 4px;
		border: 1px solid #d9d9d9;
		border-top-width: 1.02px;
		transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);

		.input-local-class-in {
			display: block;
			margin-left: 11px;
			margin-right: 11px;
			position: relative;
			line-height: 30px;
		}
	}
}

.device-tags-out-hide {
	display: none !important;
}

.device-debug-drawer {
	top: 53px !important;
	height: calc(100% - 53px) !important;
	z-index: 100 !important;

	.ant-drawer-close-x {
		line-height: 40px !important;
	}

	.ant-drawer-mask {
		display: none !important;
	}

	.ant-drawer-content-wrapper {
		width: calc(100% - 252px) !important;
		transition: all 0.5s;
	}

	.ant-drawer-body {
		padding: 0 !important;
	}

	&.collapsed-side {
		.ant-drawer-content-wrapper {
			width: calc(100% - 82px) !important;
		}
	}
}

.assign-to-customer-modal {
	.assign-to-customer-label-select,
	.assign-to-application-label-select {
		margin: 0px 0 20px;

		.customer-select-label,
		.application-select-label {
			font-size: 14px;
			font-weight: 600;
			width: 100px;
			display: inline-block;
		}
	}
}

.update-firmware-modal {
	.update-firmware-select {
		margin: 0px 0 20px;

		.update-firmware-select-label {
			font-size: 14px;
			font-weight: 500;
			width: 100px;
			display: inline-block;
		}
	}
}

.upload-device-modal {
	.upload-device-container {
		margin: 0px 0 20px;

		.upload-device-label {
			font-size: 14px;
			font-weight: 600;
			display: flex;
			align-items: center;

			.anticon {
				font-size: 20px;
				margin-right: 10px;
			}
		}

		.ant-upload-list-text {
			margin-top: 10px;
		}

		.ant-upload-list-item-error,
		.ant-upload-list-item-error .anticon-paper-clip,
		.ant-upload-list-item-error .ant-upload-list-item-name {
			color: rgb(55, 67, 117) !important;
			display: inline-block;
			padding-right: 20px;
		}

		.ant-upload-list-item-error .anticon-cross {
			color: #232323 !important;

			&:hover {
				color: #f5222d !important;
			}
		}

		p {
			color: #8c8c8c !important;
			font-weight: 700 !important;
			margin: 0;
		}

		.upload-cloud-icon {
			font-size: 60px;
		}

		.download-link {
			cursor: pointer;
			padding: 5px;
			color: #7686a1;
			font-style: italic;
		}
	}
}

.device-loader {
	.list-load {
		.ant-skeleton-title {
			width: 90% !important;
		}

		.ant-skeleton-paragraph {
			display: none !important;
		}
	}

	.select-load {
		.ant-skeleton-title {
			width: 100% !important;
			height: 25px !important;
		}

		.ant-skeleton-paragraph {
			display: none !important;
		}
	}

	.hed-load {
		.ant-skeleton-title {
			width: 200px !important;
			height: 10px !important;
		}

		.ant-skeleton-paragraph {
			display: none !important;
		}
	}

	.min-height {
		min-height: 250px;
		text-align: center;

		.ant-spin {
			position: absolute;
			top: 45%;
		}
	}
}

.small-font-size {
	div {
		ul {
			font-size: 12px;
		}
	}
}

/*.datoms-x-device-management {
	&.unassigned-tab {
		.filter-select-container {
			.clear-all-container {
				position: relative !important;
				right: unset !important;
				top: unset !important;
			}
		}
	}

	.assigned {
		.ant-table-body {
			width: 100% !important;
			overflow: hidden !important;
			overflow-y: auto !important;
			max-height: calc(100vh - 360px) !important;
		}
	}

	.unassigned {
		.ant-table-body {
			width: 100% !important;
			overflow: hidden !important;
			overflow-y: auto !important;
			max-height: calc(100vh - 340px) !important;
		}
	}
}*/

/*.iot-platform-device-management {
	.assigned {
		.ant-table-body {
			width: 100% !important;
			overflow: hidden !important;
			overflow-y: auto !important;
			max-height: calc(100vh - 350px) !important;
		}
	}

	.unassigned {
		.ant-table-body {
			width: 100% !important;
			overflow: hidden !important;
			overflow-y: auto !important;
			max-height: calc(100vh - 335px) !important;
		}
	}
}*/

/*.application-device-management {
	.assigned {
		.ant-table-body {
			width: 100% !important;
			overflow: hidden !important;
			overflow-y: auto !important;
			max-height: calc(100vh - 440px) !important;
		}
	}

	.unassigned {
		.ant-table-body {
			width: 100% !important;
			overflow: hidden !important;
			overflow-y: auto !important;
			max-height: calc(100vh - 380px) !important;
		}
	}
}*/

@media screen and (max-width: 1700px) {
	#datoms_iot_platform_device {
		.ant-btn {
			font-size: 16px !important;
		}

		.assign-unassigned-devices {
			font-size: 12px !important;
		}
	}

	/*.datoms-x-device-management {
		.filter-select-container {
			.clear-all-container {
				position: absolute;
				right: 10px;
				top: 50px;
			}
		}
	}*/
}

/*@media screen and (max-width: 1600px) {
	.datoms-x-device-management {
		.assigned {
			.ant-table-body {
				max-height: calc(100vh - 390px) !important;
			}
		}
	}
}*/

/*@media (max-width: 1500px) {
	.iot-platform-device-management {
		.filter-select-container {
			.clear-all-container {
				position: absolute;
				right: 10px;
				top: 50px;
			}
		}

		&.unassigned-tab {
			.filter-select-container {
				.clear-all-container {
					position: relative !important;
					right: unset !important;
					top: unset !important;
				}
			}
		}
	}
}*/

@media screen and (max-width: 1440px) {
	#datoms_iot_platform_device {
		.add-butn-container {
			margin-top: -45px;
		}

		.ant-btn {
			font-size: 16px !important;
		}

		.assign-unassigned-devices {
			font-size: 12px !important;
		}
	}
}

@media screen and (min-width: 1441px) {
	#datoms_iot_platform_device {
		.ant-btn {
			font-size: 16px !important;
		}

		.assign-unassigned-devices {
			font-size: 12px !important;
		}
	}
}

/*@media (max-width: 1350px) {
	.datoms-x-device-management {
		.assigned {
			.ant-table-body {
				max-height: calc(100vh - 430px) !important;
			}
		}
	}

	.iot-platform-device-management {
		.assigned {
			.ant-table-body {
				max-height: calc(100vh - 390px) !important;
			}
		}
	}
}*/

@media (max-width: 1200px) {
	.datoms-x-device-management {
		.assigned {
			.ant-table-body {
				overflow-x: auto !important;

				table {
					width: 1400px !important;
				}
			}
		}

		.unassigned {
			.ant-table-body {
				overflow-x: auto !important;

				table {
					width: 1200px !important;
				}
			}
		}
	}

	.iot-platform-device-management {
		.unassigned {
			.ant-table-body {
				// max-height: calc(100vh - 335px) !important;
				overflow-x: auto !important;

				table {
					width: 1200px !important;
				}
			}
		}
	}
}

/*@media (max-width: 1150px) {
	.iot-platform-device-management {
		.assigned {
			.ant-table-body {
				max-height: calc(100vh - 430px) !important;
			}
		}
	}
}*/

@media screen and (max-width: 1024px) {
	#datoms_iot_platform_device {
		.contains {
			padding-left: 30px;
			padding-right: 30px;
			margin-left: 0px !important;
		}

		.mobile-hide {
			display: none;
		}

		.mobile-show {
			display: block;
		}

		.width-100 {
			width: 100%;
		}

		.activity-details {
			width: 60%;
			margin-top: 30px;
		}

		.assign-to-customer-firmware {
			margin-left: 15px;
		}

		.add-butn-container {
			margin-top: -35px;
		}

		.ant-table-scroll {
			overflow: initial !important;
			overflow-x: unset !important;
		}

		.ant-table-content::-webkit-scrollbar {
			height: 5px;
		}

		.ant-table-content::-webkit-scrollbar-track {
			background: transparent;
		}

		.ant-table-content::-webkit-scrollbar-thumb {
			background: #fbfbfb;
		}
	}

	/*.datoms-x-device-management {
		.assigned {
			.ant-table-body {
				width: 160%;
			}
		}

		.unassigned {
			.ant-table-body {
				// max-height: calc(100vh - 365px) !important;
				width: 160%;
			}
		}
	}*/

	/*.iot-platform-device-management {
		.assigned {
			.ant-table-body {
				width: 160%;
			}
		}

		.unassigned {
			.ant-table-body {
				width: 160%;
			}
		}
	}

	.application-device-management {
		.assigned {
			.ant-table-body {
				width: 160%;
			}
		}

		.unassigned {
			.ant-table-body {
				width: 160%;
			}
		}
	}*/
}

/*@media (max-width: 900px) {
	.datoms-x-device-management {
		.unassigned {
			.ant-table-body {
				max-height: calc(100vh - 375px) !important;
			}
		}

		&.unassigned-tab {
			.filter-select-container {
				.clear-all-container {
					position: absolute !important;
					right: 10px !important;
					top: 37px !important;
				}
			}
		}
	}
}*/

/*@media (max-width: 880px) {
	.iot-platform-device-management {
		.unassigned {
			.ant-table-body {
				max-height: calc(100vh - 495px) !important;
			}
		}
	}
}*/

@media (max-width: 800px) {
	#datoms_iot_platform_device {
		.assign-to-customer-firmware {
			margin-top: 15px;
		}
		.device-table {
			margin-top: 35px;
		}
	}
}

@media (max-width: 768px) {
	/*.ant-drawer-right.ant-drawer-open .ant-drawer-content-wrapper {
		width: 100% !important;
	}*/

	#datoms_iot_platform_device {
		.contain {
			margin: 20px auto;
			margin-left: 0;
			text-align: center;
			width: 100%;
		}

		.contains {
			.section-filter-wrapper {
				.select-filter:first-child {
					margin-left: 20px !important;
				}
			}

			.add-edit-device-drawer {
				.ant-drawer-content-wrapper {
					width: 100% !important;
				}
			}
		}

		.activity-details {
			width: 60%;
		}
	}
}

/*@media (max-width: 680px) {
	.datoms-x-device-management {
		.assigned {
			.ant-table-body {
				max-height: calc(100vh - 475px) !important;
			}
		}

		.unassigned {
			.ant-table-body {
				max-height: calc(100vh - 415px) !important;
			}
		}
	}

	.iot-platform-device-management {
		.assigned {
			.ant-table-body {
				max-height: calc(100vh - 470px) !important;
			}
		}
	}
}*/

@media (max-width: 600px) {
	#datoms_iot_platform_device {
		.activity-details {
			width: 70%;
		}

		.contains {
			padding-bottom: 0;
			margin-bottom: 30px;

			.section-filter-wrapper {
				justify-content: flex-start;
				flex-wrap: wrap;

				.select-filter {
					margin-left: 20px !important;
					margin-bottom: 10px;
				}
			}
		}

		.add-butn-container {
			margin-top: -45px;
		}

		.wid-100 {
			width: 100% !important;
		}

		.right {
			display: none;
		}

		.calibration-form {
			.calib {
				border-bottom: 1px solid #d9d9d9;
				padding: 10px 0;

				&:last-child {
					border: none;
				}
			}

			.ant-col-5 {
				width: 52% !important;
			}

			.ant-col-3 {
				width: 50% !important;
			}
		}
	}

	/*.iot-platform-device-management {
		.assigned {
			.ant-table-body {
				height: calc(100vh - 600px) !important;
			}
		}

		.unassigned {
			.ant-table-body {
				height: calc(100vh - 605px) !important;
			}
		}
	}*/
}

/*@media (max-width: 460px) {
	.datoms-x-device-management {
		.unassigned {
			.ant-table-body {
				max-height: calc(100vh - 505px) !important;
			}
		}
	}
}*/

@media (max-width: 576px) {
	#datoms_iot_platform_device {
		height: calc(100vh - 52px - 55px);
		.contains {
			padding-left: 10px;
			padding-right: 10px;
			margin-bottom: 0px;
			.search-filter-component {
				width: 100% !important;
			}
				.filter-with-search-container
				.search-wrapper {
					width: 100%;
				}
				.search-box
				.ant-input-affix-wrapper {
					width: 100% !important;
				}
				.ant-input {
				width: 100% !important;
			}
		}
		.mobile-drawer-open {
			overflow: hidden;
		}
	}
	.assign-to-customer-modal {
		.assign-to-customer-label-select,
		.assign-to-application-label-select {
			margin: 0px 0 20px;
			width: 100%;
	
			.customer-select-label,
			.application-select-label {
				font-size: 13px;
				font-weight: 600;
				margin-bottom: 10px;
				width: fit-content;
				display: inline-block;
			}
			.customer-select, .application-select {
				width: 100% !important;
			}
		}
	}
}

@media (max-width: 425px) {
	#datoms_iot_platform_device {
		/*.ant-table-body,
		.ant-table-header {
			width: 400%;
		}*/
		.assign-to-customer-firmware {
			margin-top: 20px;
		}

		.add-butn-container {
			//margin-top: -40px;
			float: none;
			margin-top: 0px;
			padding-left: 7px;
			.bulk-butn {
				margin-top: 2px;
			}
		}

		.device-table {
			margin-top: 60px;
		}

		.activity-details {
			width: 100%;
		}

		.table-filter,
		.table-search {
			width: 100%;
		}

		.table-filter {
			margin-bottom: 20px;
		}

		.back-btn {
			position: relative;
			margin-bottom: 20px;
		}
	}
}

@media (max-width: 370px) {
	.device-table {
		.ant-table {
			margin-top: 30px !important;
		}
	}
}

/*@media (max-width: 360px) {
	.datoms-x-device-management {
		.assigned {
			.ant-table-body,
			.ant-table-header {
				width: 420%;
			}
		}

		.unassigned {
			.ant-table-body,
			.ant-table-header {
				width: 420%;
			}
		}
	}
}*/
.simdetails-drawer {
	.ant-drawer-content-wrapper {
		width: 30% !important;
		min-width: 350px;
	}
	.ant-drawer-header {
		border: none;
		padding: 0 32px;
		.ant-drawer-title {
			font-size: 16px;
			font-weight: 600;
			color: #232323;
			padding: 20px 0;
			border-bottom: 1px solid #c4c2c250;
		}
	}
	.sub_drawer_content {
		.sub_drawer_sim_heading {
			font-weight: bold;
			text-align: left;
			width: 100%;
			margin-right: 0;
			margin-top: 15px;
		}
		display: flex;
		width: 100%;
		align-items: center;
		flex-direction: column;
		margin: 15px 0;
		.sub_drawer_each_sim {
			width: 100%;
			margin: 7px auto;
			display: flex;
			align-items: center;
			//justify-content: center;
		}
		p {
			margin-right: 30px;
			margin-bottom: 0;
			width: 20%;
		}
		.sub_drawer_sim_details_input {
			width: 50%;
			min-width: 200px;
		}
	}

	.sub_drawer_logs {
		position: relative;
		top: 40%;
		//margin-top: 30%;
		p {
			font-weight: bold;
			margin-bottom: 0;
		}
	}

	.sub_drawer_datepicker {
		width: 250px;
	}

	.ant-drawer-body {
		padding: 6px 32px;
	}

	.drawer-actions-box {
		position: relative;
		//bottom: 30px;
		//right: 20px;
		.apply-btn {
			width: 60px;
			height: 30px;
			display: flex;
			justify-content: center;
			align-items: center;
			float: right;
			background: #ff8500 !important;
			box-shadow: 0px 8px 9px #ff850029;
			border-radius: 8px !important;
			// &.ant-btn-loading{
			// 	width: 108px;
			// }
		}
	}
}
.sim-icon-holder {
	cursor: pointer;
	.sim-icon-tooltip-container {
		width: 100%;
		display: flex;
		min-width: 200px;
		font-size: 16px;

		.sim-icon-tooltip-each-line {
			display: flex;
			span {
				margin-left: 15px;
			}
		}
	}
}
.dm_mb_ud_tabs_parent .datoms-twin-tabs{
	margin: 20px auto 15px;
}