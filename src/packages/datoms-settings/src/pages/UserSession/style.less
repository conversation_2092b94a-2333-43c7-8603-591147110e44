#user_session {
  height: 100%;
  overflow: auto;
  padding: 15px 20px 20px 20px;
  .user-session-normal {
    color: rgb(101, 109, 118);
  }
  .user-session-bold {
    font-weight: 600;
    color: rgb(101, 109, 118);
  }
  .user-session-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .user-session-pagination {
    display: flex;
    justify-content: flex-end;
    margin: 12px 0;
  }
  .user-sessions-list {
    list-style: none;
    padding-left: 0;
    // margin-top: 20px;
    border-radius: 6px;
    background-color: rgb(246, 248, 250);
    border: 0.6666667px solid rgb(208, 215, 222);
    > li {
      padding: 20px;
      border-bottom: 0.6666667px solid rgb(208, 215, 222);
      display: flex;
      justify-content: space-between;
      &:last-child {
        border-bottom: none;
      }
      .user-session-top {
        display: flex;
        gap: 12px;
        margin-bottom: 4px;
      }
      .user-session-btm {
        display: flex;
        gap: 30px;
      }
      p {
        margin-bottom: 0;
      }
    }
  }
  .current-session-btn {
    color: rgb(31, 136, 61) !important;
    border: 0.666667px solid rgba(31, 35, 40, 0.15);
    pointer-events: none;
    font-weight: 600;
    // margin-right: 10px;
  }
  .current-session-btn-dot {
    display: inline-block;
    height: 5px;
    width: 5px;
    border-radius: 50%;
    color: rgb(31, 136, 61);
  }
  .session-out-btn {
    color: rgb(207, 34, 46) !important;
    border: 0.666667px solid rgba(31, 35, 40, 0.15);
    font-weight: 600;
    margin-left: 18px;
    &:hover {
      background-color: rgb(207, 34, 46) !important;
      color: #fff !important;
    }
  }
  .session-out-btn-single {
    color: #808080 !important;
    border: 0.666667px solid rgba(31, 35, 40, 0.15);
    font-weight: 600;
    &:hover {
      background-color: #ffab6b !important;
      color: #fff !important;
    }
  }
}

@media (max-width: 576px) {
  #user_session {
    .user-session-pagination {
      .ant-pagination-mini {
        display: flex;
        justify-content: end;
        align-items: center;

        .ant-pagination-next {
          height: auto;
        }
      }

      .ant-pagination-item {
        display: none;
      }

      .ant-pagination-item-active {
        display: block;
        width: fit-content;
      }

      .ant-pagination-jump-next {
        display: none;
      }

      .ant-pagination-prev {
        height: auto;
      }

      .ant-pagination-next {
        height: auto;
      }
    }
  }
}
