import React from 'react';
import { flushSync } from 'react-dom';
import { Link } from 'react-router-dom';
import queryString from 'query-string';
import moment from 'moment-timezone';
import _sortBy from 'lodash/sortBy';
import _orderBy from 'lodash/orderBy';
import AntTabs from '@datoms/react-components/src/components/AntTabs';
import AntTabPane from '@datoms/react-components/src/components/AntTabPane';
import FilterSelectWithSearch from '@datoms/react-components/src/components/FilterSelectWithSearch';
import SelectWithRangepicker from '@datoms/react-components/src/components/SelectWithRangepicker';
import AntSpin from '@datoms/react-components/src/components/AntSpin';
import AntNotification from '@datoms/react-components/src/components/AntNotification';
import AntTooltip from '@datoms/react-components/src/components/AntTooltip';
import AntPagination from '@datoms/react-components/src/components/AntPagination';
import { useGlobalContext } from '../../../../../../store/globalStore';
// import NoDataComponent from './NoDataComponent';

import CalendarOutlined from '@ant-design/icons/CalendarOutlined';

import NotificationReportObjectData from './UserNotificationReportObjectData';
import {
	retriveEventsData,
	retriveVendorEventsData,
	retriveCustomerList,
	retriveThingsList,
	subscribeForEventsUpdates,
	disconnectSocketConnection,
	establishSocketConnection,
} from '@datoms/js-sdk';
import { TimeFormatter } from '@datoms/js-utils/src/TimeFormatting.js'

import './user-notification-page.less';

import NoNotifications from './NoNotifications';
// import EmptyNotificationImage from './../../../imgs/Group_5498.svg';
import ReadTickIcon from './../../../imgs/read_tick.svg';
import BellIcon from './../../../imgs/bell.svg';
import ActivityIcon from './../../../imgs/activity_noti.svg';
import FaultIcon from './../../../imgs/fault_new.svg';
import MaintenanceIcon from './../../../imgs/maintenance_noti.svg';
import ViolationIcon from './../../../imgs/violation_new.svg';
import NoDataComponent from './NoDataComponent';
import NoActivityFaultIcon from './../../../imgs/Group_4754.svg';
import NoViolationIcon from './../../../imgs/Group_4762.svg';
import NoMaintenanceIcon from './../../../imgs/Group_5528.svg';
//import UserNotificationDetailSection from './UserNotificationDetailSection';


function disabledDate(current) {
	// Can not select future dates
	return current && current >= moment().endOf('day');
}

const datePickerConfig = {
	placeholder: ['From', 'To'],
	size: 'default',
	disabledDate: disabledDate,
	showTime: false,
	separator: ' - ',
	format: 'DD-MMM-YYYY',
};

function getRanges(upto_time) {
	let ranges = {
		'last_7_days': [
			moment().subtract(7, 'days').startOf('day'),
			moment().endOf('day'),
		],
		'last_15_days': [
			moment((upto_time - 15 * 86400) * 1000).startOf('day'),
			moment(upto_time * 1000).endOf('day'),
		],
		'this_month': [
			moment.unix(upto_time).startOf('month'),
			moment(upto_time * 1000).endOf('day'),
		],
		'last_3_months': [
			moment.unix(upto_time).startOf('month').subtract(2, 'M'),
			moment(upto_time * 1000).endOf('day'),
		],
	};

	return ranges;
}

export default class UserNotificationPage extends React.Component {

	constructor(props) {
		super(props);

		this.parsed_search = queryString.parse(window.location.search);

		this.state = {
			screenWidth: null,
			user_all_notifications: [],
			user_all_notifications_filtered: [],
			//user_all_notifications_unfiltered: [],
			//user_read_notifications_filtered: [],
			//user_unread_notifications_filtered: [],
			tab_selected: 'all_notifications',
			//selected_notification_details: {},
			//current_notification_drawer_status: true,
			category_tree: [],
			selected_category: undefined,
			initial_all_customer_details: [],
			all_customer_details: [],
			partner_tree: [],
			selected_partner: [],
			customer_tree: [],
			selected_customer: undefined,
			application_tree: [],
			vendor_application_tree: [],
			selected_application:
				this.parsed_search &&
				Object.values(this.parsed_search).length &&
				this.parsed_search.selected_application
					? parseInt(this.parsed_search.selected_application)
					: undefined,
			things_tree: [],
			selected_things: [],
			selected_from_time: import.meta.env.VITE_DESKTOP ? 			
			moment().subtract(7, 'days').startOf('day').unix()
			: moment()
				.startOf('month')
				.subtract(2, 'M')
				.unix(),
			selected_upto_time:import.meta.env.VITE_DESKTOP ? 
			moment().endOf('day').unix()
			:  moment().endOf('day').unix(),
			notification_list_report_object: {},
			notification_pdf_button_loading: false,
			notification_pdf_report_download: false,
			socket_flag: true,
			last_24_hr_count: 0,
			custom_open_change_status: false,
			notification_full_loading: true,
			page: 1,
			pageSize: 30,
			total: 0,
		};

		this.onPaginationChange = this.onPaginationChange.bind(this);
	}

	windowResize() {
		this.setState({ screenWidth: window.innerWidth });
	}

	async getAllCustomers(initial_flag, client_id, url_text = '') {
		let response = await retriveCustomerList(client_id, url_text);
		if (response.status === 403) {
			this.setState({
				unauthorized_access: true,
				unauthorized_access_msg: response.message,
			});
		} else if (response.status === 'success') {
			let customer_tree = [],
				application_tree = [],
				partner_tree = [],
				selected_application = undefined,
				dg_status = false;

			// if(parseInt(client_id) === 1 && url_text == '') {
			// 	if(response.vendor_list && response.vendor_list.length) {
			// 		response.vendor_list.map((v) => {
			// 			if(parseInt(v.id) !== 1) {
			// 				partner_tree.push({
			// 					title: v.name,
			// 					value: v.id,
			// 				});
			// 			}
			// 		});
			// 	}
			// }

			// if(response.client_list && response.client_list.length) {
			// 	response.client_list.map((cust) => {
			// 		customer_tree.push({
			// 			title: cust.name,
			// 			value: cust.id,
			// 		});
			// 	});
			// }
			if (initial_flag) {
				if (response.applications && response.applications.length) {
					response.applications.map((app) => {
						if (parseInt(app.id) !== 17) {
							if (parseInt(app.id) === 16) {
								dg_status = true;
							}
							application_tree.push({
								title: app.name,
								value: app.id,
							});
						}
					});
				}

				if (application_tree.length) {
					application_tree = _sortBy(application_tree, function (o) {
						return o.title;
					});
					selected_application =
						this.state.selected_application == undefined
							? dg_status
								? 16
								: application_tree[0].value
							: this.state.selected_application;
				}
			} else {
				if (this.state.application_tree.length) {
					application_tree = this.state.application_tree;
					selected_application = this.state.selected_application;
				}
			}

			if (response.customers && response.customers.length) {
				response.customers.map((cust) => {
					if (parseInt(cust.is_vendor) === 1) {
						if (
							cust.access_applications.includes(
								selected_application
							) &&
							parseInt(cust.id) !== 1
						) {
							partner_tree.push({
								title: cust.name,
								value: cust.id,
							});
						}
					} else {
						if (cust.applications.includes(selected_application)) {
							customer_tree.push({
								title: cust.name,
								value: cust.id,
								vendor_id: cust.vendor_id
							});
						}
					}
				});
			}
			if(parseInt(this.props.applicationId1) === 12 && initial_flag && partner_tree[0]){
				customer_tree = customer_tree.filter(cust => cust.vendor_id === partner_tree[0].value);
			}
			const stateToUpdate = {
				all_customer_details:
					response.customers && response.customers.length
						? response.customers
						: [],
				initial_all_customer_details:
					initial_flag &&
					response.customers &&
					response.customers.length
						? response.customers
						: this.state.initial_all_customer_details,
				vendor_application_tree: application_tree,
				partner_tree:
					parseInt(client_id) === 1 && url_text == ''
						? partner_tree
						: this.state.partner_tree,
				customer_tree: customer_tree,
				application_tree: application_tree,
				selected_application: selected_application,
			};
			if(parseInt(this.props.applicationId1) === 12 && partner_tree[0]){
				stateToUpdate['selected_partner'] = partner_tree[0].value;
				console.log('called_gdai', partner_tree)
			}
			flushSync(() => {
				this.setState(
					stateToUpdate,
					() => {
						if (!window.location.href.includes('?')) {
							let newurl =
								window.location.href +
								'?selected_application=' +
								this.state.selected_application;
							window.history.pushState({ path: newurl }, '', newurl);
						}
					}
				);
			})
		} else {
			this.openNotification('error', response.message);
			this.setState({
				unauthorized_access: true,
				error_API: true,
				error_API_msg: response.message,
			});
		}
	}

	async getAllThings(client_id, app_id) {
		const assetsArray = [];
		let response = await retriveThingsList({
			client_id: client_id,
			application_id: app_id,
		});
		if (response.status === 403) {
			this.setState({
				unauthorized_access: true,
				unauthorized_access_msg: response.message,
			});
		} else if (response.status === 'success') {
			let things_list = [];
			if (response.things && response.things.length) {
				response.things.forEach((t) => {
					things_list.push({
						title: t.name,
						value: t.id,
					});
					assetsArray.push(t.id);
				});
			}

			this.setState({
				things_tree: things_list,
			});
		} else {
			this.openNotification('error', response.message);
			this.setState({
				unauthorized_access: true,
				error_API: true,
				error_API_msg: response.message,
			});
		}
		return assetsArray;
	}

	notificationPolling (){
		if(import.meta.env.VITE_DESKTOP) {
		  this.notificationInterval = setInterval(() => {
			this.fetchNotifications(
				this.props.clientId1,
				this.props.applicationId1,
				false,
			);
		  }, 5000);
		}
	  }

	async fetchNotifications(
		client_id,
		app_id,
		last_24_hr_status,
		vendor_id = 0,
		exclude_count=false
	) {
		let data = {};
		if (
			parseInt(this.props.applicationId1) === 12 ||
			parseInt(this.props.applicationId1) === 17
		) {
			data = {
				client_id: this.props.isRentalStore ? vendor_id : client_id,
				application_id:  this.props.isRentalStore ? 'all' : app_id,
				vendor_id: vendor_id,
			};
		} else {
			data = {
				client_id: client_id,
				application_id: app_id,
			};
		}
		/*if (window.localStorage.getItem('Client-Id')) {
			data.client_id = window.localStorage.getItem('Client-Id');
		}*/
		let url_string = '';
		let e_data;
		let paginationString =
			'&page_no=' +
			this.state.page +
			'&results_per_page=' +
			this.state.pageSize +
			(this.state.selected_category
				? '&tag=' + this.state.selected_category
				: '') + 
				(this.state.selected_things.length
					? '&entity_type=thing&entity_id=' + this.state.selected_things[0]
					: '');
		if (last_24_hr_status) {
			url_string =
				url_string +
				'?get_details=true' +
				'&generated_after=' +
				(moment().unix() - 86400) +
				'&generated_before=' +
				moment().unix() +
				paginationString;
		} else {
			url_string =
				url_string +
				'?get_details=true' +
				'&generated_after=' +
				this.state.selected_from_time +
				'&generated_before=' +
				this.state.selected_upto_time +
				paginationString;
		}
		if(exclude_count){
			url_string += '&exclude_count=true'
		}
		if (
			parseInt(this.props.applicationId1) === 12 ||
			parseInt(this.props.applicationId1) === 17
		) {
			e_data = await retriveVendorEventsData(data, url_string);
		} else {
			e_data = await retriveEventsData(data, url_string);
		}

		if (e_data.response.status === 403) {
			this.setState({
				unauthorized_access: true,
				unauthorized_access_msg: e_data.response.message,
				//custom_open_change_status: true,
				notification_full_loading: false,
				user_all_notifications: [],
			});
		} else if (e_data.response.status === 'success') {
			console.log('Noti Response All', e_data.response);
			let total_events = [],
				notification_count = 0;
			if (e_data.response.events && e_data.response.events.length) {
				e_data.response.events.map((ne) => {
					if (
						(ne.entity_type == 'thing' || this.props.isRentalStore) &&
						(ne.tags.includes('Fault') ||
							ne.tags.includes('Fault OK') ||
							ne.tags.includes('Activity') ||
							ne.tags.includes('Maintenance') ||
							ne.tags.includes('Violation'))
					) {
						total_events.push({
							message: ne.message,
							thing_id: ne.entity_id,
							timestamp: ne.generated_at,
							event_type: ne.type,
							category:
								ne.tags.includes('Fault') ||
								ne.tags.includes('Fault OK')
									? 'Fault'
									: ne.tags.includes('Violation')
									? 'Violation'
									: ne.tags.includes('Activity')
									? 'Activity'
									: 'Maintenance',
							read_status: false,
							service_id: ne.details?.service_id,
							//event_id: ne.event_id,
						});
						notification_count = notification_count + 1;
					}
				});
			}
			let total = exclude_count ? this.state.total : e_data.response.total_results;
			if (last_24_hr_status) {
				this.setState({
					last_24_hr_count: notification_count,
					total: total,
				});
			} else {
				this.setState(
					{
						user_all_notifications: total_events,
						//custom_open_change_status: true,
						notification_full_loading: false,
						total: total,
					},
					() => {
						this.setNotificationList();
					}
				);
			}
		} else {
			this.openNotification('error', e_data.response.message);
			this.setState({
				unauthorized_access: true,
				error_API: true,
				error_API_msg: e_data.response.message,
				//custom_open_change_status: true,
				notification_full_loading: false,
				user_all_notifications: [],
			});
		}
	}

	// componentDidUpdate() {
	// 	if(this.parsed_search && parseInt(this.parsed_search.selected_application) != this.state.selected_application) {
	// 		this.setState({
	// 			selected_application: parseInt(this.parsed_search.selected_application),
	// 		});
	// 	}
	// }

	async componentDidMount() {
		window.addEventListener('resize', this.windowResize.bind(this));
		let category_tree = [
			{
				title: this.props.t('activity'),
				// title: 'Activity',
				value: 'Activity',
			},
			{
				title: this.props.t('faults'),
				// title: 'Faults',
				value: 'Fault',
			},
			{
				title: this.props.t('maintenance'),
				// title: 'Maintenance',
				value: 'Maintenance',
			},
			{
				title: this.props.t('violation'),
				// title: 'Violation',
				value: 'Violation',
			},
		];
		this.setState({
			category_tree: category_tree,
		});

		let accessibleAssets = [];
		if (parseInt(this.props.applicationId1) === 12) {
			await this.getAllCustomers(true, this.props.clientId1);
			await this.getAllThings(
				this.props.clientId1,
				this.state.selected_application
			);
			//await this.fetchNotifications('all', this.state.selected_application, true, 'all');
			await this.fetchNotifications(
				'all',
				this.state.selected_application,
				false,
				this.state.selected_partner//'all'
			);
			console.log('selectedPartner: ', this.state.selected_partner)
		} else if (parseInt(this.props.applicationId1) === 17) {
			await this.getAllCustomers(true, this.props.clientId1);
			await this.getAllThings(
				this.props.clientId1,
				this.state.selected_application
			);
			//await this.fetchNotifications('all', this.state.selected_application, true, this.props.clientId1);
			await this.fetchNotifications(
				'all',
				this.state.selected_application,
				false,
				this.props.clientId1
			);
		} else {
			accessibleAssets = await this.getAllThings(
				this.props.clientId1,
				this.props.applicationId1
			);
			await this.fetchNotifications(
				this.props.clientId1,
				this.props.applicationId1,
				true
			);
			await this.fetchNotifications(
				this.props.clientId1,
				this.props.applicationId1,
				false
			);
		}

		if (
			parseInt(this.props.applicationId1) !== 12 &&
			parseInt(this.props.applicationId1) !== 17
		) {
			this.notificationPolling();
			this.socket = establishSocketConnection();
			this.socket.on('connect', () => {
				subscribeForEventsUpdates(
					this.socket,
					this.props.clientId1,
					parseInt(this.props.applicationId1) === 12 ||
						parseInt(this.props.applicationId1) === 17
						? this.state.selected_application
						: this.props.applicationId1
				);
			});
			this.socket.on('new_event_generated', (payload) => {
				if (
					payload &&
					this.state.selected_upto_time ==
						moment().endOf('day').unix() &&
					this.state.socket_flag
				) {
					let realtimeNotificationData = this.state
						.user_all_notifications;
					let current_notification_data = {};
					if (
						payload.entity_type == 'thing' &&
						(payload.tags.includes('Fault') ||
							payload.tags.includes('Fault OK') ||
							payload.tags.includes('Activity') ||
							payload.tags.includes('Maintenance') ||
							payload.tags.includes('Violation'))
						&& accessibleAssets.includes(parseInt(payload.entity_id))
					) {
						current_notification_data = {
							message: payload.message,
							thing_id: parseInt(payload.entity_id),
							timestamp: payload.generated_at,
							event_type: payload.type,
							category:
								payload.tags.includes('Fault') ||
								payload.tags.includes('Fault OK')
									? 'Fault'
									: payload.tags.includes('Violation')
									? 'Violation'
									: payload.tags.includes('Activity')
									? 'Activity'
									: 'Maintenance',
							read_status: false,
							service_id: payload.details?.service_id,
							//event_id: payload.event_id,
						};
						if (parseInt(this.props.applicationId1) === 12) {
							if (this.state.selected_customer != undefined) {
								let thing_index = this.state.things_tree.findIndex(
									(thing) =>
										thing.value ===
										parseInt(payload.entity_id)
								);
								if (thing_index > -1) {
									realtimeNotificationData.unshift(
										current_notification_data
									);
								}
							} else {
								realtimeNotificationData.unshift(
									current_notification_data
								);
							}
						} else if (parseInt(this.props.applicationId1) === 17) {
							if (this.state.selected_customer != undefined) {
								let thing_index = this.state.things_tree.findIndex(
									(thing) =>
										thing.value ===
										parseInt(payload.entity_id)
								);
								if (thing_index > -1) {
									realtimeNotificationData.unshift(
										current_notification_data
									);
								}
							}
						} else {
							realtimeNotificationData.unshift(
								current_notification_data
							);
						}
					}
					this.setState(
						{
							user_all_notifications: realtimeNotificationData,
							socket_flag: true,
						},
						() => {
							this.setNotificationList();
						}
					);
				}
				if (
					payload &&
					payload.entity_type == 'thing' &&
					(payload.tags.includes('Fault') ||
						payload.tags.includes('Fault OK') ||
						payload.tags.includes('Activity') ||
						payload.tags.includes('Maintenance') ||
						payload.tags.includes('Violation')) 
					&& accessibleAssets.includes(parseInt(payload.entity_id))
				) {
					this.setState({
						last_24_hr_count: this.state.last_24_hr_count + 1,
					});
				}
			});
		}
	}

	componentWillUnmount() {
		window.removeEventListener('resize', this.windowResize.bind(this));
		if (
			parseInt(this.props.applicationId1) !== 12 &&
			parseInt(this.props.applicationId1) !== 17
		) {
			disconnectSocketConnection(this.socket);
		}
		clearInterval(this.notificationInterval);
	}

	openNotification(type, msg) {
		AntNotification({
			type: type,
			message: msg,
			// description: 'This is success notification',
			placement: 'bottomLeft',
			className: 'alert-' + type,
		});
	}

	getNotificationList(notification_array, type) {
		console.log('notification-array', type, notification_array);
		let sorted_notification_array = _orderBy(
			notification_array,
			['timestamp'],
			['desc']
		);
		let current_notification_array = [],
			total_notification_array = [],
			time_string = moment
				.unix(sorted_notification_array[0].timestamp)
				.format('dddd, MMMM DD, YYYY');

		sorted_notification_array.map((notification) => {
			if (
				moment
					.unix(notification.timestamp)
					.format('dddd, MMMM DD, YYYY') == time_string
			) {
				current_notification_array.push(
					//<div className={'user-full-notification-date-list-item-container' + (this.state.selected_notification_details.timestamp == notification.timestamp ? ' clicked-full-notification-list-item-container' : '')} onClick={() => this.onNotificationClick(notification.timestamp, type)}>
					<div
						className={
							'user-full-notification-date-list-item-container'
						}
					>
						<div className="user-full-list-item-category-message-container">
							<div>
								{notification.category == 'Fault' ? (
									<AntTooltip title={'Fault'}>
										<img src={FaultIcon} />
									</AntTooltip>
								) : notification.category == 'Violation' ? (
									<AntTooltip title={'Violation'}>
										<img src={ViolationIcon} />
									</AntTooltip>
								) : notification.category == 'Activity' ? (
									<AntTooltip title={'Activity'}>
										<img src={ActivityIcon} />
									</AntTooltip>
								) : (
									<AntTooltip title={'Maintenance'}>
										<img src={MaintenanceIcon} />
									</AntTooltip>
								)}
							</div>
							<div className="user-full-list-item-category-message-inner-container">
								{/* <span className="user-full-list-item-category">
									{notification.category} :{' '}
								</span> */}
								<span className="user-full-list-item-message">
									{notification.message}
								</span>
								{notification.service_id ? (
									<Link
										to={
											(this.props.applicationId1 === 17
												? '/iot-platform/work-orders'
												: '/dg-monitoring/work-orders') +
											'?id=' +
											notification.service_id
										}
										target={'_blank'}
										className="link-to-service-module"
									>
										View
									</Link>
								) : (
									''
								)}
							</div>
						</div>
						<div className="user-full-list-item-time-status-container">
							<span
								className={
									'user-full-list-item-time ' +
									(notification.read_status
										? 'full-time-mar-12'
										: 'full-time-mar-28')
								}
							>
								{TimeFormatter(
									this.props.user_preferences
										?.time_format,
										notification.timestamp,
									'HH:mm'
							  )}
							</span>
							<span className="user-full-list-item-read-status">
								{notification.read_status ? (
									<img
										src={ReadTickIcon}
										width={18}
										height={18}
									/>
								) : (
									<img
										src={ReadTickIcon}
										width={18}
										height={18}
										style={{ display: 'none' }}
									/>
								)}
							</span>
						</div>
					</div>
				);
			} else {
				total_notification_array.push(
					<div className="user-full-notification-date-container">
						<div className="user-full-date">{time_string}</div>
						<div className="user-full-notification-date-list-container">
							{current_notification_array}
						</div>
					</div>
				);
				time_string = moment
					.unix(notification.timestamp)
					.format('dddd, MMMM DD, YYYY');
				current_notification_array = [];
				current_notification_array.push(
					//<div className={'user-full-notification-date-list-item-container' + (this.state.selected_notification_details.timestamp == notification.timestamp ? ' clicked-full-notification-list-item-container' : '')} onClick={() => this.onNotificationClick(notification.timestamp, type)}>
					<div
						className={
							'user-full-notification-date-list-item-container'
						}
					>
						<div className="user-full-list-item-category-message-container">
							<div>
								{notification.category == 'Fault' ? (
									<AntTooltip title={'Fault'}>
										<img src={FaultIcon} />
									</AntTooltip>
								) : notification.category == 'Violation' ? (
									<AntTooltip title={'Violation'}>
										<img src={ViolationIcon} />
									</AntTooltip>
								) : notification.category == 'Activity' ? (
									<AntTooltip title={'Activity'}>
										<img src={ActivityIcon} />
									</AntTooltip>
								) : (
									<AntTooltip title={'Maintenance'}>
										<img src={MaintenanceIcon} />
									</AntTooltip>
								)}
							</div>
							<div className="user-full-list-item-category-message-inner-container">
								{/* <span className="user-full-list-item-category">
									{notification.category} :{' '}
								</span> */}
								<span className="user-full-list-item-message">
									{notification.message}
								</span>
							</div>
						</div>
						<div className="user-full-list-item-time-status-container">
							<span
								className={
									'user-full-list-item-time ' +
									(notification.read_status
										? 'full-time-mar-12'
										: 'full-time-mar-28')
								}
							>
								{TimeFormatter(
									this.props.user_preferences
										?.time_format,
										notification.timestamp,
									'HH:mm'
							  )}
							</span>
							<span className="user-full-list-item-read-status">
								{notification.read_status ? (
									<img
										src={ReadTickIcon}
										width={18}
										height={18}
									/>
								) : (
									<img
										src={ReadTickIcon}
										width={18}
										height={18}
										style={{ display: 'none' }}
									/>
								)}
							</span>
						</div>
					</div>
				);
			}
		});

		if (current_notification_array.length) {
			total_notification_array.push(
				<div className="user-full-notification-date-container">
					<div className="user-full-date">{time_string}</div>
					<div className="user-full-notification-date-list-container">
						{current_notification_array}
					</div>
				</div>
			);
		}

		console.log('total_notification_array', type, total_notification_array);

		return total_notification_array;
	}

	onNotificationTabChange(key) {
		this.setState({
			tab_selected: key,
		});
	}

	async onCategoryThingFilterChange(value, index) {
		console.log('FILTER', index, value);
		if (index == 0) {
			this.setState(
				{
					selected_category: value,
					notification_full_loading: true,
			page: 1,
			total: 0,
				},
				() => {
					// this.setNotificationList();
					if (
						parseInt(this.props.applicationId1) ===
							12 ||
						parseInt(this.props.applicationId1) === 17
					) {
						this.onDateRangeFunctionCall();
					} else {
						this.fetchNotifications(
							this.props.clientId1,
							this.props.applicationId1,
							false
						);
					}
				}
			);
		}
		if (
			parseInt(this.props.applicationId1) === 12 ||
			parseInt(this.props.applicationId1) === 17
		) {
			if (index == 1) {
				this.setState(
					{
						selected_application: value,
						selected_partner: undefined,
						selected_customer: undefined,
						selected_things: [],
						notification_full_loading: true,
			page: 1,
			total: 0,
					},
					async () => {
						if (window.location.href.includes('?')) {
							let newurl =
								window.location.href.split('?')[0] +
								'?selected_application=' +
								this.state.selected_application;
							window.history.pushState(
								{ path: newurl },
								'',
								newurl
							);
						}
						let partner_tree = [],
							customer_tree = [];
						if (this.state.initial_all_customer_details.length) {
							this.state.initial_all_customer_details.map(
								(cust) => {
									if (parseInt(cust.is_vendor) === 1) {
										if (
											cust.access_applications.includes(
												this.state.selected_application
											) &&
											parseInt(cust.id) !== 1
										) {
											partner_tree.push({
												title: cust.name,
												value: cust.id,
											});
										}
									} else {
										if (
											cust.applications.includes(
												this.state.selected_application
											)
										) {
											customer_tree.push({
												title: cust.name,
												value: cust.id,
											});
										}
									}
								}
							);
						}
						this.setState(
							{
								partner_tree: partner_tree,
								customer_tree: customer_tree,
								selected_partner: partner_tree[0]?.value
							},
							async () => {
								await this.getAllThings(
									this.props.clientId1,
									this.state.selected_application
								);
								let partnerId = parseInt(this.props.applicationId1) === 17 ? this.props.clientId1 : this.state.selected_partner;
								await this.fetchNotifications(
									'all',
									this.state.selected_application,
									false,
									partnerId//'all'
								);
							}
						);
					}
				);
			}
		}
		if (parseInt(this.props.applicationId1) === 12) {
			if (index == 2) {
				this.setState(
					{
						selected_partner: value,
						selected_customer: undefined,
						selected_things: [],
						notification_full_loading: true,
			page: 1,
			total: 0,
					},
					async () => {
						if (value == undefined) {
							let customer_tree = [];
							if (
								this.state.initial_all_customer_details.length
							) {
								this.state.initial_all_customer_details.map(
									(cust) => {
										if (parseInt(cust.is_vendor) !== 1) {
											if (
												cust.applications.includes(
													this.state
														.selected_application
												)
											) {
												customer_tree.push({
													title: cust.name,
													value: cust.id,
												});
											}
										}
									}
								);
							}
							this.setState(
								{
									customer_tree: customer_tree,
								},
								async () => {
									await this.getAllThings(
										this.props.clientId1,
										this.state.selected_application
									);
									await this.fetchNotifications(
										'all',
										this.state.selected_application,
										false,
										this.state.selected_partner//'all'
									);
								}
							);
						} else {
							let url_string =
								'?vendors=' + this.state.selected_partner;
							await this.getAllCustomers(
								false,
								this.props.clientId1,
								url_string
							);
							await this.getAllThings(
								this.state.selected_partner,
								this.state.selected_application
							);
							await this.fetchNotifications(
								'all',
								this.state.selected_application,
								false,
								this.state.selected_partner
							);
						}
					}
				);
			}
			if (index == 3) {
				this.setState(
					{
						selected_customer: value,
						selected_things: [],
						notification_full_loading: true,
			page: 1,
			total: 0,
					},
					async () => {
						if (
							value == undefined &&
							this.state.selected_partner == undefined
						) {
							await this.getAllThings(
								this.props.clientId1,
								this.state.selected_application
							);
							await this.fetchNotifications(
								'all',
								this.state.selected_application,
								false,
								this.state.selected_partner//'all'
							);
						} else if (
							value != undefined &&
							this.state.selected_partner == undefined
						) {
							await this.getAllThings(
								value,
								this.state.selected_application
							);
							await this.fetchNotifications(
								value,
								this.state.selected_application,
								false,
								this.props.clientId1
							);
						} else if (
							value == undefined &&
							this.state.selected_partner != undefined
						) {
							await this.getAllThings(
								this.state.selected_partner,
								this.state.selected_application
							);
							await this.fetchNotifications(
								'all',
								this.state.selected_application,
								false,
								this.state.selected_partner
							);
						} else if (
							value != undefined &&
							this.state.selected_partner != undefined
						) {
							await this.getAllThings(
								this.state.selected_customer,
								this.state.selected_application
							);
							await this.fetchNotifications(
								value,
								this.state.selected_application,
								false,
								this.state.selected_partner
							);
						}
					}
				);
			}
			if (index == 4) {
				this.setState(
					{
						selected_things: value ? [value] : [],
						notification_full_loading: true,
			page: 1,
			total: 0,
					},
					() => {
					//	this.setNotificationList();
					if (
						parseInt(this.props.applicationId1) ===
							12 ||
						parseInt(this.props.applicationId1) === 17
					) {
						this.onDateRangeFunctionCall();
					} else {
						this.fetchNotifications(
							this.props.clientId1,
							this.props.applicationId1,
							false
						);
					}
					}
				);
			}
		} else if (parseInt(this.props.applicationId1) === 17) {
			if (index == 2) {
				this.setState(
					{
						selected_customer: value,
						selected_things: [],
						notification_full_loading: true,
			page: 1,
			total: 0,
					},
					async () => {
						if (value == undefined) {
							await this.getAllThings(
								this.props.clientId1,
								this.state.selected_application
							);
							await this.fetchNotifications(
								'all',
								this.state.selected_application,
								false,
								this.props.clientId1
							);
						} else {
							await this.getAllThings(
								value,
								this.state.selected_application
							);
							await this.fetchNotifications(
								value,
								this.state.selected_application,
								false,
								this.props.clientId1
							);
						}
					}
				);
			}
			if (index == 3) {
				this.setState(
					{
						selected_things: value ? [value] : [],
						notification_full_loading: true,
			page: 1,
			total: 0,
					},
					() => {
					//	this.setNotificationList();
					if (
						parseInt(this.props.applicationId1) ===
							12 ||
						parseInt(this.props.applicationId1) === 17
					) {
						this.onDateRangeFunctionCall();
					} else {
						this.fetchNotifications(
							this.props.clientId1,
							this.props.applicationId1,
							false
						);
					}
					}
				);
			}
		} else {
			if (index == 1) {
				this.setState(
					{
						selected_things: value ? [value] : [],
						notification_full_loading: true,
			page: 1,
			total: 0,
					},
					() => {
					//	this.setNotificationList();
					if (
						parseInt(this.props.applicationId1) ===
							12 ||
						parseInt(this.props.applicationId1) === 17
					) {
						this.onDateRangeFunctionCall();
					} else {
						this.fetchNotifications(
							this.props.clientId1,
							this.props.applicationId1,
							false
						);
					}
					}
				);
			}
		}
	}

	setNotificationList() {
		console.log('FILTER-1', this.state.selected_things);
		let filter_all = [];
		//unfilter_all = [],
		//filter_read = [],
		//filter_unread  = [];

		if (
			this.state.selected_category == undefined &&
			this.state.selected_things.length == 0
		) {
			this.state.user_all_notifications.map((n) => {
				filter_all.push(n);
				// if(n.read_status) {
				//     filter_read.push(n);
				// } else {
				//     filter_unread.push(n);
				// }
			});
		} else if (
			this.state.selected_category != undefined &&
			this.state.selected_things.length == 0
		) {
			this.state.user_all_notifications.map((n) => {
				if (n.category == this.state.selected_category) {
					filter_all.push(n);

					// if(n.read_status) {
					//     filter_read.push(n);
					// } else {
					//     filter_unread.push(n);
					// }
				}
				// else {
				//     unfilter_all.push(n);
				// }
			});
		} else if (
			this.state.selected_category == undefined &&
			this.state.selected_things.length != 0
		) {
			this.state.user_all_notifications.map((n) => {
				if (this.state.selected_things.includes(n.thing_id)) {
					filter_all.push(n);

					// if(n.read_status) {
					//     filter_read.push(n);
					// } else {
					//     filter_unread.push(n);
					// }
				}
				// else {
				//     unfilter_all.push(n);
				// }
			});
		} else if (
			this.state.selected_category != undefined &&
			this.state.selected_things.length != 0
		) {
			this.state.user_all_notifications.map((n) => {
				if (
					n.category == this.state.selected_category &&
					this.state.selected_things.includes(n.thing_id)
				) {
					filter_all.push(n);

					// if(n.read_status) {
					//     filter_read.push(n);
					// } else {
					//     filter_unread.push(n);
					// }
				}
				// else {
				//     unfilter_all.push(n);
				// }
			});
		}

		this.setState(
			{
				user_all_notifications_filtered: filter_all,
				//user_all_notifications_unfiltered: unfilter_all,
				//user_read_notifications_filtered: filter_read,
				//user_unread_notifications_filtered: filter_unread,
				socket_flag: true,
			}
			// () => {
			//     this.setNotificationReadViewStatus();
			// }
		);
	}

	// setNotificationReadViewStatus() {
	//     let all_notifications = [...this.state.user_all_notifications],
	//         all_notifications_filtered = [...this.state.user_all_notifications_filtered],
	//         read_notifications_filtered = [],
	//         unread_notifications_filtered = [],
	//         notification_detail = {};

	//     if(this.state.tab_selected == 'all_notifications' && all_notifications_filtered.length != 0) {
	//         if(!all_notifications_filtered[0].read_status) {
	//             all_notifications_filtered[0].read_status = true;

	//             let index_1 = all_notifications.findIndex((u) => u.timestamp == all_notifications_filtered[0].timestamp);
	//             if(index_1 != -1) {
	//                 all_notifications[index_1].read_status = true;
	//             }

	//             all_notifications_filtered.map((n) => {
	//                 if(n.read_status) {
	//                     read_notifications_filtered.push(n);
	//                 } else {
	//                     unread_notifications_filtered.push(n);
	//                 }
	//             });
	//         }
	//         notification_detail = all_notifications_filtered[0];
	//     } else if(this.state.tab_selected == 'read_notifications' && this.state.user_read_notifications_filtered.length != 0) {
	//         notification_detail = this.state.user_read_notifications_filtered[0];
	//     }

	//     this.setState({
	//         user_all_notifications: all_notifications,
	//         user_all_notifications_filtered: all_notifications_filtered,
	//         user_read_notifications_filtered: read_notifications_filtered.length ? read_notifications_filtered : this.state.user_read_notifications_filtered,
	//         user_unread_notifications_filtered: unread_notifications_filtered.length ? unread_notifications_filtered : this.state.user_unread_notifications_filtered,
	//         selected_notification_details: notification_detail,
	//         socket_flag: true,
	//     });
	// }

	onDateRangeFunctionCall(exclude_count=false) {
		if (parseInt(this.props.applicationId1) === 12) {
			if (
				this.state.selected_partner == undefined &&
				this.state.selected_customer == undefined
			) {
				this.fetchNotifications(
					'all',
					this.state.selected_application,
					false,
					this.state.selected_partner,//'all',
					exclude_count
				);
			} else if (
				this.state.selected_partner != undefined &&
				this.state.selected_customer == undefined
			) {
				this.fetchNotifications(
					'all',
					this.state.selected_application,
					false,
					this.state.selected_partner,
					exclude_count
				);
			} else if (
				this.state.selected_partner == undefined &&
				this.state.selected_customer != undefined
			) {
				this.fetchNotifications(
					this.state.selected_customer,
					this.state.selected_application,
					false,
					this.props.clientId1,
					exclude_count
				);
			} else if (
				this.state.selected_partner != undefined &&
				this.state.selected_customer != undefined
			) {
				this.fetchNotifications(
					this.state.selected_customer,
					this.state.selected_application,
					false,
					this.state.selected_partner,
					exclude_count
				);
			}
		} else if (parseInt(this.props.applicationId1) === 17) {
			if (this.state.selected_customer == undefined) {
				this.fetchNotifications(
					'all',
					this.state.selected_application,
					false,
					this.props.clientId1,
					exclude_count
				);
			} else {
				this.fetchNotifications(
					this.state.selected_customer,
					this.state.selected_application,
					false,
					this.props.clientId1,
					exclude_count
				);
			}
		}
	}

	onDateRangeChange(rangeArr, isCustom) {
		console.log('Date Select Range', rangeArr, isCustom);

		if (isCustom) {
			this.setState({
				selected_from_time: moment(rangeArr[0] * 1000)
					.startOf('day')
					.unix(),
				selected_upto_time: moment(rangeArr[1] * 1000)
					.endOf('day')
					.unix(),
				socket_flag: false,
			});
		} else {
			this.setState(
				{
					prev_selected_from_time: this.state.selected_from_time,
					prev_selected_upto_time: this.state.selected_upto_time,
					selected_from_time: rangeArr[0],
					selected_upto_time: rangeArr[1],
					socket_flag: false,
				},
				() => {
					if (
						this.state.prev_selected_from_time !=
							this.state.selected_from_time ||
						this.state.prev_selected_upto_time !=
							this.state.selected_upto_time
					) {
						this.setState(
							{
								notification_full_loading: true,
			page: 1,
			total: 0,
							},
							() => {
								if (
									parseInt(this.props.applicationId1) ===
										12 ||
									parseInt(this.props.applicationId1) === 17
								) {
									this.onDateRangeFunctionCall();
								} else {
									this.fetchNotifications(
										this.props.clientId1,
										this.props.applicationId1,
										false
									);
								}
							}
						);
					}
				}
			);
		}
	}

	onDateOpenChange(status) {
		console.log('Date Select Custom Status', status);
		if (status) {
			this.setState({
				custom_open_change_status: true,
				prev_selected_from_time: this.state.selected_from_time,
				prev_selected_upto_time: this.state.selected_upto_time,
			});
		}
	}

	onCustomOkButtonClick() {
		this.setState(
			{
				custom_open_change_status: false,
			},
			() => {
				if (
					this.state.prev_selected_from_time !=
						this.state.selected_from_time ||
					this.state.prev_selected_upto_time !=
						this.state.selected_upto_time
				) {
					this.setState(
						{
							notification_full_loading: true,
			page: 1,
			total: 0,
						},
						() => {
							if (
								parseInt(this.props.applicationId1) === 12 ||
								parseInt(this.props.applicationId1) === 17
							) {
								this.onDateRangeFunctionCall();
							} else {
								this.fetchNotifications(
									this.props.clientId1,
									this.props.applicationId1,
									false
								);
							}
						}
					);
				}
			}
		);
	}

	getNotificationListPdfStructure(isReportDownload = false) {
		let from_date = this.state.selected_from_time,
			upto_date = this.state.selected_upto_time;

		let thing_list = '';
		if (this.state.selected_things.length != 0) {
			for (let t = 0; t < this.state.selected_things.length; t++) {
				if (t == 0) {
					thing_list = thing_list + this.state.selected_things[t];
				} else {
					thing_list =
						thing_list + ' ,' + this.state.selected_things[t];
				}
			}
		}

		let filter_applied = [
			{
				type:
					this.state.selected_category != undefined
						? this.state.selected_category
						: '-',
				things:
					this.state.selected_things.length != 0 ? thing_list : '-',
			},
		];

		let filtered_notifications = [],
			sorted_notifications = [];

		if (
			this.state.user_all_notifications_filtered &&
			this.state.user_all_notifications_filtered.length
		) {
			sorted_notifications = _orderBy(
				this.state.user_all_notifications_filtered,
				['timestamp'],
				['desc']
			);

			sorted_notifications.map((n) => {
				filtered_notifications.push({
					category: n.category,
					received_date: moment
						.unix(n.timestamp)
						.format('DD-MM-YYYY, HH:mm'),
					description: n.message,
					thing_name: n.thing_name,
					parameter: n.parameter_name,
					value: n.parameter_value,
					allowed_value: n.allowed_value,
				});
			});
		}

		let notificationListReportObject = {
			...NotificationReportObjectData.report_data,
		};

		notificationListReportObject.body_section.body_data.map((ob) => {
			if (ob.id == 'notification_filter') {
				ob.table_data = filter_applied;
			}
			if (ob.id == 'notifications') {
				ob.table_data = filtered_notifications;
			}
		});

		notificationListReportObject.file_name =
			notificationListReportObject.file_name +
			' ' +
			moment.unix(from_date).format('DD-MM-YYYY_HH-mm') +
			'_' +
			moment.unix(upto_date).format('DD-MM-YYYY_HH-mm');

		notificationListReportObject.time_interval = [from_date, upto_date];

		let downloadReport = false;
		if (isReportDownload) {
			downloadReport = true;
		}

		console.log(
			'notification_list_report_object',
			notificationListReportObject,
			downloadReport
		);

		this.setState(
			{
				notification_list_report_object: notificationListReportObject,
				notification_pdf_report_download: downloadReport,
			},
			() =>
				console.log(
					'notification_list_report_object_1',
					this.state.notification_list_report_object,
					this.state.notification_pdf_report_download
				)
		);
	}

	downloadNotificationReport() {
		this.setState(
			{
				notification_pdf_button_loading: false,
				notification_pdf_report_download: false,
			},
			() => {
				this.getNotificationListPdfStructure(true);
			}
		);
	}

	downloadNotificationReportClose() {
		this.setState(
			{
				notification_pdf_button_loading: false,
			},
			() => {
				this.openNotification(
					'success',
					'PDF Report Downloaded for User Notification List Data.'
				);
			}
		);
	}

	displayNoResultIcons() {
		let icon_display;
		if (
			this.state.selected_category != undefined ||
			this.state.selected_things.length != 0
		) {
			if (this.state.selected_category == undefined) {
				icon_display = (
					<NoDataComponent t={this.props.t}/>
					// <img src={NoResultIcon} width={372} height={263} />
				);
			} else {
				if (this.state.selected_category == 'Fault') {
					icon_display = (
						<div>
							<div>
								<img src={NoActivityFaultIcon} />
							</div>
							<div className="result-message fault-message">
								{this.props.t? this.props.t('your_machine_is_fine_no_faults'): "Your machine is absolutely fine! No faults."}
								{/* Your machine is absolutely fine! No faults. */}
							</div>
						</div>
					);
				} else if (this.state.selected_category == 'Activity') {
					icon_display = (
						<div>
							<div>
								<img src={NoActivityFaultIcon} />
							</div>
							<div className="result-message activity-message">
								{this.props.t? this.props.t('there_are_no_activity_here'): "There are no activity! so, let me have a nap here."}
								{/* There are no activity! so, let me have a nap
								here. */}
							</div>
						</div>
					);
				} else if (this.state.selected_category == 'Violation') {
					icon_display = (
						<div>
							<div>
								<img src={NoViolationIcon} />
							</div>
							<div className="result-message violation-message">
								{this.props.t? this.props.t('machine_on_right_track'): "Your machine is on the right track. no violations! no worries!"}
								{/* Your machine is on the right track. no
								violations! no worries! */}
							</div>
						</div>
					);
				} else if (this.state.selected_category == 'Maintenance') {
					icon_display = (
						<div>
							<div>
								<img src={NoMaintenanceIcon} />
							</div>
							<div className="result-message violation-message">
								{this.props.t? this.props.t('there_are_no_maintenance_alerts') + "! ": "There are no maintenance alerts!"}
								{/* There are no maintenance alerts! */}
							</div>
						</div>
					);
				}
			}
		} else {
			icon_display= <NoNotifications t={this.props.t}/>
			// icon_display = <img src={EmptyNotificationImage} />;
		}

		return icon_display;
	}
	onPaginationChange(newpage, newpageSize) {
		this.setState({ page: newpage, pageSize: newpageSize, notification_full_loading: true }, () => {
			if (
				parseInt(this.props.applicationId1) === 12 ||
				parseInt(this.props.applicationId1) === 17
			) {
				this.onDateRangeFunctionCall(true);
			} else {
				this.fetchNotifications(
					this.props.clientId1,
					this.props.applicationId1,
					false,
					undefined,
					true
				);
			}
		});
	}
	render() {
		const { page, pageSize, total } = this.state;
		
		let filterData1 = [
			{
				optionData: this.state.category_tree,
				selectValue: this.state.selected_category,
				allowClear: true,
				sorted: true,
				showSearch: false,
				filterType: 'category',
				placeholder: this.props.t("all_categories")
				// placeholder: 'All Categories',
				//className: '',
			},
		];

		const paginationConfig = {
			current: page,
			pageSize,
			total,
			pageSizeOptions: ['10', '20', '30'],
			showSizeChanger: true,
			size: 'small',
			showTotal: (total, range) =>
				`${this.props.t('showing')} ${range[0]}-${range[1]} ${this.props.t('of')} ${total}`,
				// `Showing ${range[0]}-${range[1]} of ${total}`,
			onChange: this.onPaginationChange,
		};

		if (
			parseInt(this.props.applicationId1) === 12 ||
			parseInt(this.props.applicationId1) === 17
		) {
			filterData1.push({
				optionData: this.state.application_tree,
				selectValue: this.state.selected_application,
				allowClear:
					parseInt(this.props.applicationId1) === 12 ||
					parseInt(this.props.applicationId1) === 17
						? false
						: true,
				sorted: true,
				showSearch: false,
				filterType: 'application',
				placeholder: 'All Applications',
				//className: '',
			});
		}

		if (parseInt(this.props.applicationId1) === 12) {
			filterData1.push({
				optionData: this.state.partner_tree,
				selectValue: this.state.selected_partner,
				allowClear: false,
				sorted: true,
				showSearch: true,
				filterType: 'partner',
				placeholder: 'All Partners',
				//className: '',
			});
		}

		if (
			parseInt(this.props.applicationId1) === 12 ||
			parseInt(this.props.applicationId1) === 17
		) {
			filterData1.push({
				optionData: this.state.customer_tree,
				selectValue: this.state.selected_customer,
				allowClear: true,
				sorted: true,
				showSearch: true,
				filterType: 'customer',
				placeholder: this.props.t? this.props.t('all_customers'): 'All Customers'
				// placeholder: 'All Customers',
				//className: '',
			});
		}

		filterData1.push({
			optionData: this.state.things_tree,
			selectValue: this.state.selected_things[0],
			allowClear: true,
			sorted: true,
			disabled:
				parseInt(this.props.applicationId1) === 12 ||
				parseInt(this.props.applicationId1) === 17
					? this.state.selected_customer !== undefined
						? false
						: true
					: false,
		//	multiSelect: true,
			showSearch: true,
			filterType: 'thing-type',
			placeholder: this.props.t("all_assets")
			// placeholder: 'All Assets',
			//className: '',
		});

		let all_notifications_display;
		if (this.state.notification_full_loading) {
			all_notifications_display = (
				<AntSpin className={'notification-full-loading-align-center'} />
			);
		} else {
			if (this.state.user_all_notifications_filtered.length) {
				all_notifications_display = (
					<div className="user-full-notification-list-container">
						{this.getNotificationList(
							this.state.user_all_notifications_filtered,
							'all_notifications'
						)}
					</div>
				);
			} else {
				all_notifications_display = (
					<div className="user-full-empty-notification-list-image">
						{this.displayNoResultIcons()}
					</div>
				);
			}
		}

		return (
			<div id="user-full-notification-view">
				<div className="user-full-notification-page-view">
					<div className="user-full-notification-outer-container">
						<div className="user-full-notification-container">
							<div className="user-full-notification-heading-container">
								<span className="user-full-notification-bell-icon">
									<img src={BellIcon} />
								</span>
								<span className="user-full-notification-heading-text-1">
									{this.props.t("all_notifications")}
									{/* All Notifications */}
								</span>
								{(() => {
									if (this.state.last_24_hr_count > 0) {
										return (
											<span className="user-full-notification-heading-text-2-container">
												<div
													className={
														'user-full-notification-heading-text-2' +
														(this.state
															.last_24_hr_count <
														10
															? ' single-digit-full-count'
															: this.state
																	.last_24_hr_count <
															  100
															? ' two-digit-full-count'
															: '')
													}
												>
													{this.state
														.last_24_hr_count > 99
														? '99+'
														: this.state
																.last_24_hr_count}
												</div>
											</span>
										);
									}
								})()}
							</div>
							{this.props.isRentalStore ?'':<div className="user-full-notification-filter-text">
								{this.props.t("filter_by")}
								{/* Filter by */}
							</div>}
							<div className="user-full-notification-filter-datepicker-container">
								{this.props.isRentalStore ?<div style={{height: window.innerWidth <= 576 ? 0 : 46}}/> :<div
									className={
										'user-full-notification-filter-container' +
										(parseInt(this.props.applicationId1) ===
											12 ||
										parseInt(this.props.applicationId1) ===
											17
											? ' datoms-iot-filter-container'
											: '')
									}
								>
									<FilterSelectWithSearch
										filterData={filterData1}
										applyFilterSelect={(value, index) =>
											this.onCategoryThingFilterChange(
												value,
												index
											)
										}
										t={this.props.t}
										isClear={false}
										noSpaceForFilterTag={true}
									/>
								</div>}
								<div
									className={
										parseInt(this.props.applicationId1) ===
											12 ||
										parseInt(this.props.applicationId1) ===
											17
											? parseInt(
													this.props.applicationId1
											  ) === 12
												? 'datoms-x-datepicker-container' +
												  (this.state.selected_things
														.length == 0
														? ' no-tag-datepicker-datoms-x-container'
														: '')
												: 'iot-platform-datepicker-container' +
												  (this.state.selected_things
														.length == 0
														? ' no-tag-datepicker-iot-platform-container'
														: '')
											: 'user-full-notification-datepicker-container' +
											  (this.state.selected_things
													.length == 0
													? ' no-tag-datepicker-container'
													: '')
									}
								>
									<SelectWithRangepicker
										t={this.props.t}
										fromTime={this.state.selected_from_time}
										uptoTime={this.state.selected_upto_time}
										style_type="dotted"
										config={datePickerConfig}
										ranges={getRanges(moment().unix())}
										prefix={true}
										prefixIcon={<CalendarOutlined />}
										onRangeChange={(rangeArr, isCustom) =>
											this.onDateRangeChange(
												rangeArr,
												isCustom
											)
										}
										onOpenChange={(status) =>
											this.onDateOpenChange(status)
										}
										footer_flag={true}
										open={
											this.state.custom_open_change_status
										}
										onOkButtonClick={() =>
											this.onCustomOkButtonClick()
										}
									/>
								</div>
							</div>
							{/* <div className="user-full-notification-button-container">
								<div className="user-full-mark-all-read-button-container">
                                    <AntButton
                                        value="mark_as_read"
                                        onClick={() =>
                                            this.onClickMarkAllAsReadButton()
                                        }
                                        className={'user-full-mark-all-read-button'}
                                    >
                                        Mark All As Read
                                    </AntButton>
                                </div>
                                <AntDivider type={'vertical'} className="user-full-button-divider" />
								<div className="user-full-download-report-button-container">
									{(() => {
										if (
											this.state
												.notification_pdf_button_loading
										) {
											return (
												<div
													onClick={() =>
														this.doNothing()
													}
												>
													<span>
														<AntSpin />
													</span>
												</div>
											);
										} else {
											return (
												<AntButton
													value="download_all_notifications"
													onClick={() =>
														this.downloadNotificationReport()
													}
													className={
														'user-full-download-report-button'
													}
												>
													<div>
														<span>
															<img
																src={
																	DownloadReportIcon
																}
															/>
														</span>
														<span
															style={{
																marginLeft:
																	'6px',
															}}
														>
															Download Report
														</span>
													</div>
												</AntButton>
											);
										}
									})()}
								</div>
							</div> */}
							<section className="notifications-pagi-sec">
								<AntPagination {...paginationConfig} />
							</section>
							<div
								className={
									parseInt(this.props.applicationId1) ===
										12 ||
									parseInt(this.props.applicationId1) === 17
										? 'datoms-iot-tab-container' +
										  (this.state.selected_things.length ==
										  0
												? ' no-tag-datoms-iot-tab-container'
												: '')
										: 'user-full-notification-tab-container' +
										  (this.state.selected_things.length ==
										  0
												? ' no-tag-tab-container'
												: '')
								}
							>
								<AntTabs
									//defaultActiveKey="all_notifications"
									activeKey={this.state.tab_selected}
									onChange={(key) =>
										this.onNotificationTabChange(key)
									}
								>
									<AntTabPane
										key="all_notifications"
									>
										{all_notifications_display}
									</AntTabPane>
									{/* <AntTabPane
                                        tab="Read"
                                        key="read_notifications"
                                    >
                                        {read_notifications_display}
                                    </AntTabPane>
                                    <AntTabPane
                                        tab="Unread"
                                        key="unread_notifications"
                                    >
                                        {unread_notifications_display}
                                    </AntTabPane> */}
								</AntTabs>
							</div>
						</div>
						{/* {(() => {
                            if(window.screen.width >= 1024) {
                                return (
                                    <div className="user-full-notification-detail-container">{notification_detail_display}</div>
                                );
                            }
                        })()} */}
					</div>
				</div>
				{/* {(() => {
                    if(window.screen.width < 1024) {
                        return (
                            <AntDrawer
                                title=""
                                placement="right"
                                width={'100%'}
                                visible={this.state.current_notification_drawer_status}
                                onClose={() => this.onNotificationDetailDrawerClose()}
                                destroyOnClose={false}
                                maskClosable={false}
                                className={'user-notification-detail-drawer-view'}
                            >
                                {notification_detail_display}
                            </AntDrawer>
                        );
                    }
                })()} */}
				{/* {NotificationListReportSection} */}
			</div>
		);
	}
}
