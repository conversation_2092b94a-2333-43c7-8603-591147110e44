import React, { Suspense } from 'react';
import AntLayout from '@datoms/react-components/src/components/AntLayout';
import AntSpin from '@datoms/react-components/src/components/AntSpin';
import './style.less';
import UserAppNotificationPage from './imports/UserAppNotificationPage';
//import UserNotificationPage from './imports/UserNotificationPage';

export default class UserNotification extends React.Component {
	constructor(props) {
		super(props);

		this.state = {};
	}

	render() {
		return (
			<Suspense
				fallback={
					<div>
						<AntLayout className={'contains'}>
							<AntSpin className="align-center-loading" />
						</AntLayout>
					</div>
				}
			>
				<UserAppNotificationPage
					t={this.props.t}
					clientId={this.props.client_id}
					applicationId={this.props.application_id}
					appName={this.props.app_name}
					handleAppNotificationClose={() =>
						this.props.handleUserNotificationAppDrawerClose()
					}
					user_preferences={this.props.user_preferences}
					isRentalStore={this.props.globalConfig('vehicle_rental_vendors').includes(this.props.client_id)}
					logged_in_user_role_type={this.props.logged_in_user_role_type}
					// onUserNotificationMenuClick={() =>
					// 	this.props.menuClickFunc()
					// }
				/>
			</Suspense>
		);
	}
}
