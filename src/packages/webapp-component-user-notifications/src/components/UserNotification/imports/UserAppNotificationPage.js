import React from 'react';

import { Link } from 'react-router-dom';

import moment from 'moment-timezone';
import _sortBy from 'lodash/sortBy';
import _orderBy from 'lodash/orderBy';
import AntDrawer from '@datoms/react-components/src/components/AntDrawer';
import AntButton from '@datoms/react-components/src/components/AntButton';
import AntTabs from '@datoms/react-components/src/components/AntTabs';
import AntTabPane from '@datoms/react-components/src/components/AntTabPane';
import AntNotification from '@datoms/react-components/src/components/AntNotification';
import AntTooltip from '@datoms/react-components/src/components/AntTooltip';
import AntSpin from '@datoms/react-components/src/components/AntSpin';
import FilterSelectWithSearch from '@datoms/react-components/src/components/FilterSelectWithSearch';

//import UserNotificationPage from './UserNotificationPage';
import {
	retriveEventsData,
	retriveVendorEventsData,
	//retriveCustomerDetails,
	retriveThingsList,
	retriveCustomerList,
	subscribeForEventsUpdates,
	disconnectSocketConnection,
	establishSocketConnection,
} from '@datoms/js-sdk';
import { TimeFormatter } from '@datoms/js-utils/src/TimeFormatting.js'

import EmptyNotificationImage from './../../../imgs/Group_5498.svg';
import ReadTickIcon from './../../../imgs/read_tick.svg';
import BellIcon from './../../../imgs/bell.svg';
import ActivityIcon from './../../../imgs/activity_noti.svg';
import FaultIcon from './../../../imgs/fault_new.svg';
import MaintenanceIcon from './../../../imgs/maintenance_noti.svg';
import ViolationIcon from './../../../imgs/violation_new.svg';

//import drawer_close_icon from '../../../../images/CustomizationIcons/Icon_ionic_md_close_circle_outline.svg';

import './user-app-notification-page.less';

// let iotBasePath ='https://app.datoms.io';

// if (!import.meta.env.VITE_MOBILE && typeof window !== undefined && !window.location.href.includes('localhost')) {
// 	iotBasePath = window.location.protocol + '//' + window.location.host;
// }

export default class UserAppNotificationPage extends React.Component {
	constructor(props) {
		super(props);
		this.state = {
			screenWidth: null,
			user_all_notifications: [],
			//user_read_notifications: [],
			//user_unread_notifications: [],
			tab_selected: 'all_notifications',
			//selected_notification_details: {},
			last_24_hr_count: 0,
			//view_all_flag: false,
			notification_app_loading: true,
			application_tree: [],
			partner_tree: [],
			all_partners: [],
			selected_application: undefined,
		};
	}

	windowResize() {
		this.setState({ screenWidth: window.innerWidth });
	}

	async getAllCustomers(client_id) {
		let response = await retriveCustomerList(client_id);
		if (response.status === 403) {
			this.setState({
				unauthorized_access: true,
				unauthorized_access_msg: response.message,
			});
		} else if (response.status === 'success') {
			let application_tree = [],
				selected_application = undefined,
				dg_status = false,
				partner_tree = [], 
				all_partners = [];

			if (response.applications && response.applications.length) {
				response.applications.map((app) => {
					if (parseInt(app.id) !== 17) {
						if (parseInt(app.id) === 16) {
							dg_status = true;
						}
						application_tree.push({
							title: app.name,
							value: app.id,
						});
					}
				});
			}

			if (application_tree.length) {
				application_tree = _sortBy(application_tree, function (o) {
					return o.title;
				});
				selected_application = application_tree.length
					? dg_status
						? 16
						: application_tree[0].value
					: undefined;
				
			if (response.customers?.length) {
				response.customers.map((cus) => {
					if (
						cus.is_vendor &&
						cus.access_applications.includes(
							selected_application
						) &&
						parseInt(cus.id) !== 1
					) {
						partner_tree.push({
							title: cus.name,
							value: cus.id,
						});
					}
					if (
						cus.is_vendor &&
						parseInt(cus.id) !== 1
					) {
						all_partners.push({
							title: cus.name,
							value: cus.id,
							access_applications: cus.access_applications
						});
					}
				});
			}
				this.setState({
					application_tree: application_tree,
					selected_application: selected_application,
					partner_tree: partner_tree,
					selected_partner: partner_tree[0]?.value,
					all_partners: all_partners
				});
			}
		} else {
			this.openNotification('error', response.message);
			this.setState({
				unauthorized_access: true,
				error_API: true,
				error_API_msg: response.message,
			});
		}
	}

	async fetchNotifications(
		client_id,
		app_id,
		last_24_hr_status,
		vendor_id = 0
	) {
		let data = {};
		if (
			parseInt(this.props.applicationId) === 12 ||
			parseInt(this.props.applicationId) === 17
		) {
			data = {
				client_id: this.props.isRentalStore ? vendor_id : client_id,
				application_id:  this.props.isRentalStore ? 'all' : app_id,
				vendor_id: vendor_id,
			};
			// data = {
			// 	client_id: 326,
			// 	application_id: 16,
			// 	vendor_id: 1,
			// };
		} else {
			data = {
				client_id: client_id,
				application_id: app_id,
			};
		}
		let url_string = '';
		let e_data;
		if (last_24_hr_status) {
			url_string =
				url_string +
				'?get_details=true' +
				'&generated_after=' +
				(moment().unix() - 86400) +
				'&generated_before=' +
				moment().unix();
		} else {
			url_string = url_string + '?get_details=true';
		}
		url_string += '&exclude_count=true';
		if (
			parseInt(this.props.applicationId) === 12 ||
			parseInt(this.props.applicationId) === 17
		) {
			e_data = await retriveVendorEventsData(data, url_string);
		} else {
			e_data = await retriveEventsData(data, url_string);
		}

		if (e_data.response.status === 403) {
			this.setState({
				unauthorized_access: true,
				unauthorized_access_msg: e_data.response.message,
				notification_app_loading: false,
			});
		} else if (e_data.response.status === 'success') {
			console.log('Noti Response', e_data.response);
			let total_events = [],
				notification_count = 0;
			if (e_data.response.events && e_data.response.events.length) {
				e_data.response.events.map((ne) => {
					if (
						(ne.entity_type == 'thing' || this.props.isRentalStore) &&
						(ne.tags.includes('Fault') ||
							ne.tags.includes('Fault OK') ||
							ne.tags.includes('Activity') ||
							ne.tags.includes('Maintenance') ||
							ne.tags.includes('Violation'))
					) {
						total_events.push({
							message: ne.message,
							thing_id: ne.entity_id,
							timestamp: ne.generated_at,
							event_type: ne.type,
							category:
								ne.tags.includes('Fault') ||
								ne.tags.includes('Fault OK')
									? 'Fault'
									: ne.tags.includes('Violation')
									? 'Violation'
									: ne.tags.includes('Activity')
									? 'Activity'
									: 'Maintenance',
							read_status: false,
							service_id: ne.details?.service_id,
							//event_id: ne.event_id,
						});
						notification_count = notification_count + 1;
					}
				});
			}
			if (last_24_hr_status) {
				this.setState({
					last_24_hr_count: notification_count,
				});
			} else {
				this.setState({
					user_all_notifications: total_events,
					notification_app_loading: false,
					//user_unread_notifications: total_events,
				});
			}
		} else {
			this.openNotification('error', e_data.response.message);
			this.setState({
				unauthorized_access: true,
				error_API: true,
				error_API_msg: e_data.response.message,
				notification_app_loading: false,
			});
		}
	}

	notificationPolling(){
		if(import.meta.env.VITE_DESKTOP) {
			console.log("Inside loop")
			this.notificationInterval= setInterval(() => {
				console.log("inside interval")
				this.fetchNotifications(
					this.props.clientId,
					this.props.applicationId,
					true
				);
			},5000)
		}
	}

	async getAllThings () {
		if(parseInt(this.props.applicationId) !== 16 || this.props.logged_in_user_role_type === 1) {
			return "*";
		}
		try {
			const response = await retriveThingsList({
				client_id: this.props.clientId,
				application_id: this.props.applicationId,
			}, "?lite=true");
			return response.things.map((thing) => thing.id);
		} catch (error) {
			return [];
		}
	}

	async componentDidMount() {
		window.addEventListener('resize', this.windowResize.bind(this));
		let accessibleAssets = "*"
		if (
			parseInt(this.props.applicationId) === 12 ||
			parseInt(this.props.applicationId) === 17
		) {
			//await this.getCustomerDetails(this.props.clientId);
			await this.getAllCustomers(this.props.clientId);
			if (parseInt(this.props.applicationId) === 12) {
				//await this.fetchNotifications('all', this.state.selected_application, true, 'all');
				await this.fetchNotifications(
					'all',
					this.state.selected_application,
					false,
					this.state.selected_partner//'all'
				);
			} else {
				//await this.fetchNotifications('all', this.state.selected_application, true, this.props.clientId);
				await this.fetchNotifications(
					'all',
					this.state.selected_application,
					false,
					this.props.clientId
				);
			}
		} else {
			const [assets] = await Promise.all([
				this.getAllThings(),
				this.fetchNotifications(
					this.props.clientId,
					this.props.applicationId,
					true
				),
				this.fetchNotifications(
					this.props.clientId,
					this.props.applicationId,
					false
				),
			]);
			accessibleAssets = assets;
		}
		if (
			parseInt(this.props.applicationId) !== 12 &&
			parseInt(this.props.applicationId) !== 17
		) {
			this.notificationPolling();
			this.socket = establishSocketConnection();
			this.socket.on('connect', () => {
				subscribeForEventsUpdates(
					this.socket,
					this.props.clientId,
					this.props.applicationId
				);
			});
			this.socket.on('new_event_generated', (payload) => {
				if (payload && (accessibleAssets === "*" || accessibleAssets.includes(parseInt(payload.entity_id)))) {
					let realtimeAllNotificationData = this.state
						.user_all_notifications;
					//let realtimeUnreadNotificationData = this.state.user_unread_notifications;
					let notificationObject = {
						message: payload.message,
						thing_id: parseInt(payload.entity_id),
						timestamp: payload.generated_at,
						event_type: payload.type,
						category:
							payload.tags.includes('Fault') ||
							payload.tags.includes('Fault OK')
								? 'Fault'
								: payload.tags.includes('Violation')
								? 'Violation'
								: payload.tags.includes('Activity')
								? 'Activity'
								: 'Maintenance',
						read_status: false,
						service_id: payload.details?.service_id,
						//event_id: payload.event_id,
					};
					if (
						payload.entity_type == 'thing' &&
						(payload.tags.includes('Fault') ||
							payload.tags.includes('Fault OK') ||
							payload.tags.includes('Activity') ||
							payload.tags.includes('Maintenance') ||
							payload.tags.includes('Violation'))
					) {
						realtimeAllNotificationData.unshift(notificationObject);
						//realtimeUnreadNotificationData.unshift(notificationObject);
						this.setState({
							user_all_notifications: realtimeAllNotificationData,
							//user_unread_notifications: realtimeUnreadNotificationData,
							last_24_hr_count: this.state.last_24_hr_count + 1,
						});
					}
				}
			});
		}
	}

	componentWillUnmount() {
		window.removeEventListener('resize', this.windowResize.bind(this));
		if (
			parseInt(this.props.applicationId) !== 12 ||
			parseInt(this.props.applicationId) !== 17
		) {
			disconnectSocketConnection(this.socket);
		}
		clearInterval(this.notificationInterval);
	}

	// componentDidMount() {
	//     let r_notifications = [],
	//         ur_notifications = [];
	//     let total_notifications = [
	//         {
	//             message: 'Fuel level low',
	//             category: 'Faults',
	//             thing_name: 'ABC',
	//             timestamp: '1635159476',
	//             parameter_name: 'PM',
	//             parameter_value: '150.0000',
	//             allowed_value: '1100',
	//             read_status: false,
	//         },
	//         {
	//             message: 'Parameter below threshold',
	//             category: 'Faults',
	//             thing_name: 'BCD',
	//             timestamp: '1634959108',
	//             parameter_name: 'PM',
	//             parameter_value: '230.0000',
	//             allowed_value: '1150',
	//             read_status: true,
	//         },
	//         {
	//             message: 'Fuel level low',
	//             category: 'Violation',
	//             thing_name: 'CDE',
	//             timestamp: '1635159405',
	//             parameter_name: 'SO2',
	//             parameter_value: '201.0000',
	//             allowed_value: '1200',
	//             read_status: false,
	//         },
	//         {
	//             message: 'Fuel level low',
	//             category: 'Violation',
	//             thing_name: 'DEF',
	//             timestamp: '1635059401',
	//             parameter_name: 'SO2',
	//             parameter_value: '175.0000',
	//             allowed_value: '950',
	//             read_status: true,
	//         },
	//         {
	//             message: 'Parameter below threshold',
	//             category: 'Violation',
	//             thing_name: 'PQR',
	//             timestamp: '1635059375',
	//             parameter_name: 'RPM',
	//             parameter_value: '215.0000',
	//             allowed_value: '1120',
	//             read_status: true,
	//         },
	//         {
	//             message: 'Fuel level low',
	//             category: 'Faults',
	//             thing_name: 'XYZ    ',
	//             timestamp: '1634959207',
	//             parameter_name: 'RPM',
	//             parameter_value: '240.0000',
	//             allowed_value: '1175',
	//             read_status: false,
	//         },
	//         {
	//             message: 'Fuel level low',
	//             category: 'Faults',
	//             thing_name: 'ABC',
	//             timestamp: '1535159476',
	//             parameter_name: 'PM',
	//             parameter_value: '150.0000',
	//             allowed_value: '1100',
	//             read_status: false,
	//         },
	//         {
	//             message: 'Parameter below threshold',
	//             category: 'Faults',
	//             thing_name: 'BCD',
	//             timestamp: '1534959108',
	//             parameter_name: 'PM',
	//             parameter_value: '230.0000',
	//             allowed_value: '1150',
	//             read_status: true,
	//         },
	//         {
	//             message: 'Fuel level low',
	//             category: 'Violation',
	//             thing_name: 'CDE',
	//             timestamp: '1535159405',
	//             parameter_name: 'SO2',
	//             parameter_value: '201.0000',
	//             allowed_value: '1200',
	//             read_status: false,
	//         },
	//         {
	//             message: 'Fuel level low',
	//             category: 'Violation',
	//             thing_name: 'DEF',
	//             timestamp: '1535059401',
	//             parameter_name: 'SO2',
	//             parameter_value: '175.0000',
	//             allowed_value: '950',
	//             read_status: true,
	//         },
	//         {
	//             message: 'Parameter below threshold',
	//             category: 'Violation',
	//             thing_name: 'PQR',
	//             timestamp: '1535059375',
	//             parameter_name: 'RPM',
	//             parameter_value: '215.0000',
	//             allowed_value: '1120',
	//             read_status: true,
	//         },
	//         {
	//             message: 'Fuel level low',
	//             category: 'Faults',
	//             thing_name: 'XYZ    ',
	//             timestamp: '1534959207',
	//             parameter_name: 'RPM',
	//             parameter_value: '240.0000',
	//             allowed_value: '1175',
	//             read_status: false,
	//         },
	//         {
	//             message: 'Fuel level low',
	//             category: 'Faults',
	//             thing_name: 'ABC',
	//             timestamp: '1435159476',
	//             parameter_name: 'PM',
	//             parameter_value: '150.0000',
	//             allowed_value: '1100',
	//             read_status: false,
	//         },
	//         {
	//             message: 'Parameter below threshold',
	//             category: 'Faults',
	//             thing_name: 'BCD',
	//             timestamp: '1434959108',
	//             parameter_name: 'PM',
	//             parameter_value: '230.0000',
	//             allowed_value: '1150',
	//             read_status: true,
	//         },
	//         {
	//             message: 'Fuel level low',
	//             category: 'Violation',
	//             thing_name: 'CDE',
	//             timestamp: '1435159405',
	//             parameter_name: 'SO2',
	//             parameter_value: '201.0000',
	//             allowed_value: '1200',
	//             read_status: false,
	//         },
	//         {
	//             message: 'Fuel level low',
	//             category: 'Violation',
	//             thing_name: 'DEF',
	//             timestamp: '1435059401',
	//             parameter_name: 'SO2',
	//             parameter_value: '175.0000',
	//             allowed_value: '950',
	//             read_status: true,
	//         },
	//         {
	//             message: 'Parameter below threshold',
	//             category: 'Violation',
	//             thing_name: 'PQR',
	//             timestamp: '1435059375',
	//             parameter_name: 'RPM',
	//             parameter_value: '215.0000',
	//             allowed_value: '1120',
	//             read_status: true,
	//         },
	//         {
	//             message: 'Fuel level low',
	//             category: 'Faults',
	//             thing_name: 'XYZ    ',
	//             timestamp: '1434959207',
	//             parameter_name: 'RPM',
	//             parameter_value: '240.0000',
	//             allowed_value: '1175',
	//             read_status: false,
	//         },
	//     ];

	//     total_notifications.map((n) => {
	//         if(n.read_status) {
	//             r_notifications.push(n);
	//         } else {
	//             ur_notifications.push(n);
	//         }
	//     });

	//     this.setState({
	//         user_all_notifications: total_notifications,
	//         user_read_notifications: r_notifications,
	//         user_unread_notifications: ur_notifications,
	//     });
	// }

	openNotification(type, msg) {
		AntNotification({
			type: type,
			message: msg,
			// description: 'This is success notification',
			placement: 'bottomLeft',
			className: 'alert-' + type,
		});
	}

	// onNotificationClick(timestamp, type) {
	//     let current_all_notifications = [...this.state.user_all_notifications];
	//     let current_read_notifications = [...this.state.user_read_notifications];
	//     let current_unread_notifications = [...this.state.user_unread_notifications];

	//     let clicked_notification_index = current_all_notifications.findIndex((n) => n.timestamp == timestamp);

	//     if(clicked_notification_index != -1) {
	//         if(!current_all_notifications[clicked_notification_index].read_status) {
	//             current_all_notifications[clicked_notification_index].read_status = true;
	//             current_read_notifications.splice(0, 0, current_all_notifications[clicked_notification_index]);

	//             let unread_notification_index = current_unread_notifications.findIndex((u) => u.timestamp == timestamp);
	//             if(unread_notification_index != -1) {
	//                 current_unread_notifications.splice(unread_notification_index, 1);
	//             }
	//         }
	//     }

	//     this.setState({
	//         user_all_notifications: current_all_notifications,
	//         user_read_notifications: current_read_notifications,
	//         user_unread_notifications: current_unread_notifications,
	//         selected_notification_details: current_all_notifications[clicked_notification_index],
	//         tab_selected: type == 'unread_notifications' ? 'all_notifications' : type,
	//     });
	// }

	getNotificationList(notification_array, type) {
		console.log('notification-array', type, notification_array);
		let sorted_notification_array = _orderBy(
			notification_array,
			['timestamp'],
			['desc']
		);
		let current_notification_array = [],
			total_notification_array = [],
			time_string = moment
				.unix(sorted_notification_array[0].timestamp)
				.format('dddd, MMMM DD, YYYY'),
			updated_notification_array = [];

		updated_notification_array = sorted_notification_array.slice(0, 50);

		updated_notification_array.map((notification) => {
			if (
				moment
					.unix(notification.timestamp)
					.format('dddd, MMMM DD, YYYY') == time_string
			) {
				current_notification_array.push(
					//<div className={'user-app-notification-date-list-item-container' + (this.state.selected_notification_details.timestamp == notification.timestamp ? ' clicked-notification-list-item-container' : '')} onClick={() => this.onNotificationClick(notification.timestamp, type)}>
					<div
						className={
							'user-app-notification-date-list-item-container'
						}
					>
						<div className="user-app-list-item-category-message-container">
							<div>
								{notification.category == 'Fault' ? (
									<AntTooltip title={'Fault'}>
										<img src={FaultIcon} />
									</AntTooltip>
								) : notification.category == 'Violation' ? (
									<AntTooltip title={'Violation'}>
										<img src={ViolationIcon} />
									</AntTooltip>
								) : notification.category == 'Activity' ? (
									<AntTooltip title={'Activity'}>
										<img src={ActivityIcon} />
									</AntTooltip>
								) : (
									<AntTooltip title={'Maintenance'}>
										<img src={MaintenanceIcon} />
									</AntTooltip>
								)}
							</div>
							<div className="user-app-list-item-category-message-inner-container">
								{/* <span className="user-app-list-item-category">
									{notification.category} :{' '}
								</span> */}
								<span className="user-app-list-item-message">
									{notification.message}
								</span>
								{notification.service_id ? (
									<Link
										to={
											(this.props.application_id === 17
												? '/iot-platform/work-orders'
												: '/dg-monitoring/work-orders') +
											'?id=' +
											notification.service_id
										}
										target={'_blank'}
										className="link-to-service-module"
									>
										View
									</Link>
								) : (
									''
								)}
							</div>
						</div>
						<div className="user-app-list-item-time-status-container">
							<span
								className={
									'user-app-list-item-time ' +
									(notification.read_status
										? 'time-mar-12'
										: 'time-mar-28')
								}
							>
								{TimeFormatter(
									this.props.user_preferences
										?.time_format,
										notification.timestamp,
									'HH:mm'
							  )}
							</span>
							<span className="user-app-list-item-read-status">
								{notification.read_status ? (
									<img
										src={ReadTickIcon}
										width={18}
										height={18}
									/>
								) : (
									<img
										src={ReadTickIcon}
										width={18}
										height={18}
										style={{ display: 'none' }}
									/>
								)}
							</span>
						</div>
					</div>
				);
			} else {
				total_notification_array.push(
					<div className="user-app-notification-date-container">
						<div className="user-app-date">{time_string}</div>
						<div className="user-app-notification-date-list-container">
							{current_notification_array}
						</div>
					</div>
				);
				time_string = moment
					.unix(notification.timestamp)
					.format('dddd, MMMM DD, YYYY');
				current_notification_array = [];
				current_notification_array.push(
					// <div className={'user-app-notification-date-list-item-container' + (this.state.selected_notification_details.timestamp == notification.timestamp ? ' clicked-notification-list-item-container' : '')} onClick={() => this.onNotificationClick(notification.timestamp, type)}>
					<div
						className={
							'user-app-notification-date-list-item-container'
						}
					>
						<div className="user-app-list-item-category-message-container">
							<div>
								{notification.category == 'Fault' ? (
									<AntTooltip title={'Fault'}>
										<img src={FaultIcon} />
									</AntTooltip>
								) : notification.category == 'Violation' ? (
									<AntTooltip title={'Violation'}>
										<img src={ViolationIcon} />
									</AntTooltip>
								) : notification.category == 'Activity' ? (
									<AntTooltip title={'Activity'}>
										<img src={ActivityIcon} />
									</AntTooltip>
								) : (
									<AntTooltip title={'Maintenance'}>
										<img src={MaintenanceIcon} />
									</AntTooltip>
								)}
							</div>
							<div className="user-app-list-item-category-message-inner-container">
								{/* <span className="user-app-list-item-category">
									{notification.category} :{' '}
								</span> */}
								<span className="user-app-list-item-message">
									{notification.message}
								</span>
								{notification.service_id ? (
									<Link
										to={
											(this.props.applicationId === 17
												? '/iot-platform/work-orders'
												: '/dg-monitoring/work-orders') +
											'?id=' +
											notification.service_id
										}
										target={'_blank'}
										className="link-to-service-module"
									>
										View
									</Link>
								) : (
									''
								)}
							</div>
						</div>
						<div className="user-app-list-item-time-status-container">
							<span
								className={
									'user-app-list-item-time ' +
									(notification.read_status
										? 'time-mar-12'
										: 'time-mar-28')
								}
							>
								{TimeFormatter(
									this.props.user_preferences
										?.time_format,
										notification.timestamp,
									'HH:mm'
							  )}
							</span>
							<span className="user-app-list-item-read-status">
								{notification.read_status ? (
									<img
										src={ReadTickIcon}
										width={18}
										height={18}
									/>
								) : (
									<img
										src={ReadTickIcon}
										width={18}
										height={18}
										style={{ display: 'none' }}
									/>
								)}
							</span>
						</div>
					</div>
				);
			}
		});

		if (current_notification_array.length) {
			total_notification_array.push(
				<div className="user-app-notification-date-container">
					<div className="user-app-date">{time_string}</div>
					<div className="user-app-notification-date-list-container">
						{current_notification_array}
					</div>
				</div>
			);
		}

		console.log('total_notification_array', type, total_notification_array);

		return total_notification_array;
	}

	onNotificationTabChange(key) {
		this.setState({
			tab_selected: key,
		});
	}

	// onClickMarkAllAsReadButton() {
	//     let current_notifications = [...this.state.user_all_notifications];
	//     if(current_notifications.length) {
	//         current_notifications.map((n) => {
	//             n.read_status = true;
	//         });

	//         this.setState({
	//             user_all_notifications: current_notifications,
	//             user_read_notifications: current_notifications,
	//             user_unread_notifications: [],
	//         });
	//     }
	// }

	onApplicationFilterChange(value, index) {
		if (index == 0) {
			let partner_tree = [];
			if(parseInt(this.props.applicationId) === 12){
				this.state.all_partners.map(partner => {
					if(partner.access_applications?.includes(value)){
						partner_tree.push({
							title: partner.title,
							value: partner.value,
						});
					}
				})
			}
			this.setState(
				{
					selected_application: value,
					partner_tree: partner_tree,
					selected_partner: partner_tree[0]?.value,
					notification_app_loading: true,
				},
				() => {
					if (parseInt(this.props.applicationId) === 12) {
						this.fetchNotifications(
							'all',
							this.state.selected_application,
							false,
							this.state.selected_partner,//'all'
						);
					} else if (parseInt(this.props.applicationId) === 17) {
						this.fetchNotifications(
							'all',
							this.state.selected_application,
							false,
							this.props.clientId
						);
					}
				}
			);
		}
		if(index === 1){
			this.setState(
				{
					selected_partner: value,
					notification_app_loading: true,
				},
				() => {
					if (parseInt(this.props.applicationId) === 12) {
						this.fetchNotifications(
							'all',
							this.state.selected_application,
							false,
							this.state.selected_partner
						);
					}
				}
			);
		}
	}

	onClickViewAllNotificationsButton() {
		console.log('Notification page opens 1');
		//let accessPath = iotBasePath + '/enterprise/' + this.props.clientId + '/' + this.props.appName + '/user-notifications/';
		this.props.handleAppNotificationClose();
		//this.props.onUserNotificationMenuClick();
	}

	// onViewAllNotificationDrawerClose() {
	// 	this.setState(
	// 		{
	// 			view_all_flag: false,
	// 		},
	// 		() => {
	// 			this.props.handleAppNotificationClose();
	// 		}
	// 	);
	// }

	// onClickViewDetailsButton() {
	//     console.log('Notification page opens 2');
	// }

	// onNotificationModalClose() {
	//     this.setState({
	//         selected_notification_details: {},
	//     });
	// }

	render() {
		let filterData1 = [];
		if (
			parseInt(this.props.applicationId) === 12 ||
			parseInt(this.props.applicationId) === 17
		) {
			filterData1.push({
				optionData: this.state.application_tree,
				selectValue: this.state.selected_application,
				allowClear: false,
				sorted: true,
				showSearch: false,
				filterType: 'application',
				placeholder: 'All Applications',
				//className: '',
			});
		}

		if (parseInt(this.props.applicationId) === 12) {
			filterData1.push({
				optionData: this.state.partner_tree,
				selectValue: this.state.selected_partner,
				allowClear: false,
				sorted: true,
				showSearch: true,
				filterType: 'partner',
				placeholder: 'All Partners',
				//className: '',
			});
		}

		let all_notifications_display;
		if (this.state.notification_app_loading) {
			all_notifications_display = (
				<AntSpin className={'notification-app-loading-align-center'} />
			);
		} else {
			if (this.state.user_all_notifications.length) {
				all_notifications_display = (
					<div className="user-app-all-notifications-container">
						{(() => {
							if (
								this.state.last_24_hr_count > 0 &&
								(parseInt(this.props.applicationId) !== 12 ||
									parseInt(this.props.applicationId) !== 17)
							) {
								return (
									<div className="user-app-all-notifications-text">
										Received{' '}
										<span>
											{this.state.last_24_hr_count}
										</span>
										<span>
											{this.state.last_24_hr_count > 1
												? ' notifications '
												: ' notification '}
										</span>
										in last 24 hour
									</div>
								);
							}
						})()}
						<div className="user-app-all-notifications-list-container">
							{this.getNotificationList(
								this.state.user_all_notifications,
								'all_notifications'
							)}
						</div>
					</div>
				);
			} else {
				all_notifications_display = (
					<div className="user-app-empty-notification-list-image">
						<img src={EmptyNotificationImage} />
					</div>
				);
			}
		}

		// let read_notifications_display = (
		//     <div className="user-app-empty-notification-list-image"><img src={EmptyNotificationImage} /></div>
		// );
		// if(this.state.user_read_notifications.length) {
		//     read_notifications_display = (
		//         <div className="user-app-notification-list-container">{this.getNotificationList(this.state.user_read_notifications, 'read_notifications')}</div>
		//     );
		// }

		// let unread_notifications_display = (
		//     <div className="user-app-empty-notification-list-image"><img src={EmptyNotificationImage} /></div>
		// );
		// if(this.state.user_unread_notifications.length) {
		//     unread_notifications_display = (
		//         <div className="user-app-notification-list-container">{this.getNotificationList(this.state.user_unread_notifications, 'unread_notifications')}</div>
		//     );
		// }

		return (
			<div id="user-app-notification-view">
				<AntDrawer
					title={
						<>
						<div className="user-app-notification-heading-container">
							<span className="user-app-notification-bell-icon">
								<img src={BellIcon} />
							</span>
							<span className="user-app-notification-heading-text-1">
								{this.props.t("all_notifications")}
								{/* All Notifications */}
							</span>
							{(() => {
								if (this.state.last_24_hr_count > 0) {
									return (
										<span className="user-app-notification-heading-text-2-container">
											<div
												className={
													'user-app-notification-heading-text-2' +
													(this.state
														.last_24_hr_count < 10
														? ' single-digit-app-count'
														: this.state
																.last_24_hr_count <
														  100
														? ' two-digit-app-count'
														: '')
												}
											>
												{this.state.last_24_hr_count >
												99
													? '99+'
													: this.state
															.last_24_hr_count}
											</div>
										</span>
									);
								}
							})()}
						</div>
						{(() => {
							if (
								parseInt(this.props.applicationId) === 12 ||
								parseInt(this.props.applicationId) === 17
							) {
								return (
									<div className="user-app-notification-filter-container">
										<FilterSelectWithSearch
											filterData={filterData1}
											applyFilterSelect={(value, index) =>
												this.onApplicationFilterChange(
													value,
													index
												)
											}
											isClear={false}
										/>
									</div>
								);
							}
						})()}
						<div
							className={
								'user-app-notification-button-container' +
								((parseInt(this.props.applicationId) === 12 ||
									parseInt(this.props.applicationId) ===
										17) &&
								this.state.application_tree.length > 1
									? ' datoms-iot-notification-button-container'
									: '')
							}
						>
							{/* <div className="user-app-mark-all-read-button-container">
                                <AntButton
                                    value="mark_as_read"
                                    onClick={() =>
                                        this.onClickMarkAllAsReadButton()
                                    }
                                    className={'user-app-mark-all-read-button'}
                                >
                                    Mark All As Read
                                </AntButton>
                            </div>
                            <AntDivider type={'vertical'} className="user-app-button-divider" /> */}
							<div className="user-app-view-all-notifications-button-container">
								<AntButton
									value="view_all_notifications"
									onClick={() =>
										this.onClickViewAllNotificationsButton()
									}
									className={
										'user-app-view-all-notifications-button'
									}
								>
									<Link
										to={
											//'/' + this.props.appName + '/user-notifications/'
											(parseInt(
												this.props.applicationId
											) === 12 ||
												parseInt(
													this.props.applicationId
												) === 17) &&
											this.state.selected_application !=
												undefined
												? '/' +
												  this.props.appName +
												  '/user-notifications?selected_application=' +
												  this.state
														.selected_application
												: '/' +
												  this.props.appName +
												  '/user-notifications/'
										}
										target={'_self'}
									>
										{this.props.t("view_all_notifications")}
										{/* View All Notifications */}
									</Link>
								</AntButton>
							</div>
						</div>
						</>
					}
					placement="right"
					//width={'100%'}
					visible={true}
					//closeIcon={<img src={drawer_close_icon} />}
					onClose={() => this.props.handleAppNotificationClose()}
					destroyOnClose={false}
					maskClosable={true}
					className={'user-app-notification-drawer-view'}
				>
					<div className="user-app-notification-container">
						<div
							className={
								'user-app-notification-tab-container' +
								((parseInt(this.props.applicationId) === 12 ||
									parseInt(this.props.applicationId) ===
										17) &&
								this.state.application_tree.length > 1
									? ' datoms-iot-notification-tab-container'
									: '')
							}
						>
							<AntTabs
								activeKey={this.state.tab_selected}
								onChange={(key) =>
									this.onNotificationTabChange(key)
								}
							>
								<AntTabPane key="all_notifications">
									{all_notifications_display}
								</AntTabPane>
								{/* <AntTabPane
                                    tab="Read"
                                    key="read_notifications"
                                >
                                    {read_notifications_display}
                                </AntTabPane>
                                <AntTabPane
                                    tab="Unread"
                                    key="unread_notifications"
                                >
                                    {unread_notifications_display}
                                </AntTabPane> */}
							</AntTabs>
						</div>
					</div>
				</AntDrawer>
			</div>
		);
	}
}
