<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="shortcut icon" id="favicon" href="/favicon.ico" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=no"
    />
    <!-- ##PR_REPLACE_BY_HEAP_SCRIPT## -->
    <meta name="theme-color" content="#000000" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="/manifest.json" />
    <!--
      Notice the use of  in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <style>
      .loading-parent {
        position: relative;
        height: calc(100vh - 20px);
        /* filter: grayscale(100%); */
      }

      .html-align-center-loading {
        transform: translate(-50%, -50%) !important;
        top: 50% !important;
        left: 50% !important;
        position: absolute !important;
      }
      .svg-loader {
        height: 40px;
        display: table;
        margin: auto;
        margin-bottom: 20px;
      }
      .html-load-msg {
        height: 18px;
        margin-bottom: 9px;
        font-family:
          -apple-system,
          BlinkMacSystemFont,
          Segoe UI,
          Roboto,
          Helvetica,
          Arial,
          sans-serif;
        font-size: 13px;
        color: #646f79;
        opacity: 0.75;
        filter: grayscale(100%);
        margin: center;
      }
      .bottom-logo {
        position: absolute;
        bottom: 0;
        height: 15px;
        width: 100px;
        transform: translate(-50%, -50%) !important;
        left: 50% !important;
        filter: grayscale(100%);
      }
    </style>
    <title id="page_title">&#65279;</title>
    <!-- ##PR_REPLACE_BY_MIXPANEL_SCRIPT## -->
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root">
      <div class="loading-parent">
        <div class="html-align-center-loading">
          <img
            class="svg-loader"
            src="https://prstatic.phoenixrobotix.com/imgs/index/index-page-loader.svg"
            alt=""
          />
          <div class="html-load-msg">Fetching your application...</div>
        </div>
        <div class="bottom-logo"><img id="myImageId" alt="" class="" /></div>
      </div>
    </div>
    <script>
      let getUrl = window.location.href.includes("app.datoms.io");
      let datomsImage = "";
      if (getUrl) {
        datomsImage = document.getElementById("myImageId").src =
          "https://prstatic.phoenixrobotix.com/imgs/datoms/iot-platform/datoms_logo.svg";
        document.getElementById("favicon").href = "/favicon.ico";
        document.getElementById("page_title").innerHTML = "DATOMS";
      } else {
        document.getElementById("favicon").href = "/Icon Android32.png";
        document.getElementById("page_title").innerHTML = "&#65279;";
      }
    </script>

    <!--    For IP camera streaming in pollution monitoring dashboard-->
    <script src="https://prstatic.phoenixrobotix.com/3rd-party/streamedian/streamedian-1.8.5.min.js"></script>
    <script src="https://prstatic.phoenixrobotix.com/3rd-party/google-maps/markerclusterer.min.js"></script>

    <!--    DG Summary report(Backend excel report generation) is not used now-->
    <!--    <script src="https://cdnjs.cloudflare.com/ajax/libs/exceljs/4.2.1/exceljs.min.js"></script>-->

    <!-- ##PR_REPLACE_BY_CORDOVA_FRAMEWORK_FILES## -->
    <!-- <script type="text/javascript">
      function googleTranslateElementInit() {
        new google.translate.TranslateElement(
          {pageLanguage: ''},
          'google_translate_element'
          )
        }
    </script> -->
    <!-- ##PR_REPLACE_BY_BACKEND_CONTENT_BEFORE_BODY_END## -->
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
	-->
    <input type="hidden" id="user_name" value="User" />
    <script type="module" src="/src/index.tsx"></script>
  </body>
</html>
