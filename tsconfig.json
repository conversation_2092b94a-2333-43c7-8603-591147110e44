{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "allowJs": true,
    "esModuleInterop": true,  // helps in making default imports work seamlessly with CommonJS modules.

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* Types */
    // "baseUrl": "./",
    "typeRoots": ["./node_modules/@types", "./src/types/*"],
    
    // /* Paths */
    // "baseUrl": "./",
    "types": ["node", "vite/client", "jest", "@testing-library/jest-dom"],
    "paths": {
      "@datoms/react-components/*": ["./src/packages/react-components/*"],
      "@components/*": ["./src/components/*"],
      "@datoms/js-utils/*": ["./src/packages/js-utils/*"],
    }
  },
  "include": [
    "src",
    "src/types/global.d.ts",
    "cypress/**/*.ts",
    "./jest.setup.ts"
  ],
  "references": [{ "path": "./tsconfig.node.json" }]
}
